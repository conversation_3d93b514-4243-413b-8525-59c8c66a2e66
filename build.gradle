buildscript {
    repositories {
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
    }

    dependencies {
        classpath('se.transmode.gradle:gradle-docker:1.2')
    }
}

plugins {
    id 'org.asciidoctor.convert' version '1.5.3'
    id 'org.springframework.boot' version '2.3.3.RELEASE'
    id 'io.spring.dependency-management' version '1.0.10.RELEASE'
    id 'java'
}

apply plugin: 'io.spring.dependency-management'
apply plugin: 'docker'

group = 'cn.abcyun.cis'
version = '0.0.1-SNAPSHOT'
sourceCompatibility = '1.8'


ext {
    set('springCloudVersion', 'Hoxton.SR7')
    set('dockerApplication', 'abc-cis-charge-service')
    set('dockerRegistry', 'registry.cn-shanghai.aliyuncs.com')
    set('dockerGroup', 'byteflow')
    set('dockerVersion', 'latest')
    set('nexusSnapShotUrl', "https://packages.aliyun.com/maven/repository/105566-snapshot-k87VEs/")
    set('nexusReleaseUrl', 'https://packages.aliyun.com/maven/repository/105566-release-Sy2Ug0/')
    set('nexusUsername', 'ZLmZuu')
    set('nexusPassword', 'Nl4rmLzuy7')
}

repositories {
//    mavenCentral()

    maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
    maven {
        credentials {
            username "${nexusUsername}"
            password "${nexusPassword}"
        }
        url "${nexusReleaseUrl}"
    }
    maven {
        credentials {
            username "${nexusUsername}"
            password "${nexusPassword}"
        }
        url "${nexusSnapShotUrl}"
    }
}



dependencies {
    implementation 'cn.abcyun.bis:abc-bis-rpc-sdk:2.90.49'
    implementation 'cn.abcyun.cis:abc-cis-commons:2.1.7.14'
    implementation 'cn.abcyun.cis:abc-cis-core:0.2.11'
    implementation 'cn.abcyun.cis:abc-cis-id-generator:0.0.8'
    implementation 'cn.abcyun.common:abc-common-model:1.0.10'
    implementation 'cn.abcyun.common:abc-common-log:0.0.9'
    implementation 'io.springfox:springfox-boot-starter:3.0.0'
    implementation 'com.aliyun.openservices:aliyun-log-logback-appender:0.1.19'
    implementation 'org.apache.logging.log4j:log4j-api:2.15.0'
    implementation 'org.apache.logging.log4j:log4j-to-slf4j:2.15.0'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-data-rest'
    implementation 'org.springframework.cloud:spring-cloud-starter-sleuth'
    implementation 'org.springframework.cloud:spring-cloud-starter-config'
    implementation 'org.springframework.cloud:spring-cloud-starter-netflix-hystrix'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    implementation 'io.micrometer:micrometer-registry-prometheus'
    implementation 'commons-lang:commons-lang:2.6'
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
    implementation 'io.github.openfeign:feign-httpclient'
    implementation 'org.mybatis.spring.boot:mybatis-spring-boot-starter:2.0.0'
    implementation 'com.alibaba.mq-amqp:mq-amqp-client:1.0.3'
    implementation 'org.springframework.boot:spring-boot-starter-amqp'
    implementation 'com.alibaba:fastjson:1.2.59'
    implementation 'org.apache.commons:commons-lang3:3.4'
    implementation 'org.redisson:redisson:3.17.7'
    implementation 'org.apache.commons:commons-pool2:2.8.0'
    implementation 'com.github.ben-manes.caffeine:caffeine:2.8.0'
    implementation ('org.apache.rocketmq:rocketmq-spring-boot-starter:2.2.3') {
        exclude group: 'org.apache.tomcat', module: 'annotations-api'
    }
    compileOnly 'org.projectlombok:lombok:1.18.8'

    annotationProcessor "org.springframework.boot:spring-boot-configuration-processor"
    annotationProcessor 'org.projectlombok:lombok:1.18.8'
    compile 'com.google.protobuf:protobuf-java:2.5.0'
    compile 'com.aliyun.openservices:aliyun-log-logback-appender:0.1.15'
    compile 'com.vladmihalcea:hibernate-types-5:2.4.2'

    runtimeOnly 'mysql:mysql-connector-java'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
    }
}

bootJar {
    mainClassName = 'cn.abcyun.cis.charge.AbcCisChargeServiceApplication'
}

dependencyManagement {
    resolutionStrategy {
        cacheChangingModulesFor 0, 'seconds'
    }
}

task buildUnpackJar(type: Copy) {
    dependsOn clean
    dependsOn bootJar
    tasks.findByName('bootJar').mustRunAfter('clean')

    from(zipTree(tasks.bootJar.outputs.files.singleFile))
    into("build/dependency")
}

task buildDocker(type: Docker) {
    dependsOn buildUnpackJar
    tag = "${dockerRegistry}/${dockerGroup}/${dockerApplication}"
    tagVersion = "${dockerVersion}"
    dockerfile = file('Dockerfile')

    doFirst {
        copy {
            from "build/dependency"
            into "${stageDir}/build/dependency"
        }
    }
}

task deployDocker(type: Exec) {
    dependsOn buildDocker
    commandLine "docker", "push", "${dockerRegistry}/${dockerGroup}/${dockerApplication}:${dockerVersion}"
}