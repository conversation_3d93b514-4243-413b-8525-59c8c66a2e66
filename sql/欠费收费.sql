-- 增加欠费收费开关
alter table v2_charge_calculate_config
	add owe_sheet_switch tinyint default 0 not null comment '欠费收费开关';

update v2_charge_pay_mode_config set enable_icon = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAABCNJREFUWEfFlw1olVUYx39nc7blspp3l8gYw48kI3XL2QdLdlmmZA5TkXAh4VBTsZFaUrZlK2MoMxVy3fDrEpt90AJTEIm2osCK0vmx0U3R1ly6e526OaWie+K859699+N973sXxQ688L7nPM//+b/P85znPEeQ4pATGU4QD5IyJBMR3A3Go0YXki4EbQgO4KJZtPFnKtDCSUjmcheSaqAcGOkkH17vBRoQ1IgAF5Pp2BKQ47iFXjYgWYNkRIqGY8UE/Qi2MpJN4gx/WGFYEjD+Gj5D8vC/MhyvJDgKPG3ljQQC0s0kQhwC7vlPjJsgnaTzpLjEyWjcGALheP9ga/zREqjzQtN+aGqEs/7BcuxEUBTtiQECRsyv0ZLU7Vvq4bnntdHGvVC5ZLAEQIXjdkoiOWEScFNDiCpbxIzhcPICjHJpkfkz4OsvBk9AaaTxpug2dhYGgXDSnUma7YuXafdHRvspCIWcCVy9AnNLEncHjFOh0ARc7ARW2KINy4Dv/JCX72wwXiIYgPvcVnr1IshKYVS4bgJJi8yKNVBTN3jjSuNiFzww2kq3Fze5QrqZSYjDtuhjJ0DLccjM1CKVFdC4B0bnwUPFcDkAwW443WpCVNXCC+v1t88L68KJG28kjVlC5vIukpWWBDKz4MBXUFCkl49+A3Omq6DB7Hmw71M933MZJoSTM+tWaP0N7szRa49PhdYfrf9PsFNIF81AXJaoTE0HXxPMKtPKN29qMH+b/p63CLwN+v3XczB1jH5fvBzq3tPvJ45BaaG1cT3bojzwM5J7E6QemQ479kJ+GHh5uS4+kVFeAdt26a9TreCZot+9+6HYA65ceHkV+MJkrGgI/IpAH5JsW5pPzYcx42FHbazIixvg1bdM70zJg56gKZM+DKSE0N/2HhBcdyZgp777EyhbYK5ufgO2bEzmbqu1PvsQJIO6IwdOdEJWlil1pQc8BXChI3USRgjskjAZzDu74NmKRAl/O8wuhqs9qZIwktB+G1rBrF4P1VH5UFsNla+Y3vjpe1j6DHSccyZhbEOnQhSByXHBpm2wQHVm4fFtC8z1wKqXYONmc/7GDaitAu/25EloFCKnUpw/FhYtgaWrIfs204jaegtnQuCSPtPe3q5lokfHefj4A/jIB+fPxntEl2I1m3AYiTRY+5ouQpMKQcQ1Ts1HoGIh9F2LBV1bBerJyIidD3RD6YPwe2f0vD6MDAK6B4w9jgumQeNBXVAiQ1W819fBoSb7+N4/Gba+D4XTtEx/P8wogl/aTR3VrEYfxwYJq4ak5Alo+By+PAwf+uDIQfgrpXYfHisFdYoqnX31sYTjGxKDgF1LNiIb+q87Z3SqEnYt2UAoJPZNaapG7OXsm9KIzpC25QMkhvJiMkBiKK9m0SEcsstpfB79X9fzfwDrqmsiTotSOQAAAABJRU5ErkJggg==',
                                     disable_icon = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAABFNJREFUWEfFV0lIc1cUPieJGrQKookDdcIqzoJaccIZWl0IdZFNBXXZf9ntz9/FX7rtrnaZRChIRApq47D5FRQFQQWhYp3AoVaDs8Y4JKeci+81w3svsf2LBy7cd++553z3jPchREgOhyPa4/G0eL3eLkQsAoD058ES/uRBRL/r9fpRo9H4wWKxPEQiGsMxWa3WVET8DgC+JqKEcPy8j4hXAPALEb3v7+//S+uMKgCn0xnjcrneEtG3RBQXieJgHkS8RcQfTSbTD52dnfdKMhQBPN/6VyKq+TeKFYAsEtFXStYIAWC328uI6Dci+vRjKJdkIOKBwWDo7OnpWfOXGwDg+eZLaspTU1OhtrYWdnd3YWdnB66u2NWRE4Mgos/9LSEDYJ+fnJzMaJm9pqYGCgoKhMbNzU2Yn5+PXPszJyIums3mZikmZAB2u/29z+d7pyZRp9OBxWIBo9EoWKampuDo6OjFAPiATqf7vre3lzMLBIBn029pRXt+fj7U1dXJCs/Pz4GIwgJ4eHiAycnJAD7ODiL6jF0hANhstgEi+kZNGiJCd3c3xMfHh1UYzODxeGBoaCjkHCL+*****************************+cDbrcbHA6HEoCr2NhYEw4ODn7h9XoDbeTHnpCQAF1dXWAwGMTq3NwcbG1tQVxcHJjNZuAb8mCXSFRZWQmlpaXic2NjAxYWFhTB6/X6L9Fqtf4EAG+UOPR6PXR0dEBycrLYPj4+homJCTHPzMyE1tZWMfc3M5/hYI2JiRF7Y2NjcHp6qma9AbTZbB+IqDmYg/3e0tIiFDE9PT0JYZeXl+I7JycHmpqaxPz6+hpGRkbE3D9YWTGfUSNEnGEAG0SUH8yUkpICDQ0NcuDNzs6KAiRRXl4e1NfXi8+zszMYHR0V88bGRkhLSxPpuri4KFygAeAPBnBNRJ+oMWVlZQHHwdpaQAWFsrIyqKiokK0zPDwM9/f/9Bu2IJNWqiLiTVgAasCam5shOztb3l5dXQUeL6RrVRdoCYqOjhaBJmUG8/Lt2Q23t7cRY0BE4QLFINSSwhWRgy2YLi4uwOl0Ale/SEgEoVYaKgkpKSmBqqoqeWtlZUXkvGQNl8sFHLA3NzeRYBgIW4gkKZzXXA1zc3NlwdyMuCkFg+KUXV5ehvX1dc0gFIUoXCnm+s8pV1hYCFFRUbJyTr3p6WlRhJgYHJdsf+L6sL29LQbP/YnfjaIU86JSMyovL4eMjAxISkriR2bA4cPDQ5iZmYHHx8eAdT7Dg1u3P93d3YmCxH1BIrkZ8YJSO+by297eLvd/5uNbLC0twd7enqp/ExMTRds2mUyCh0GOj4/LFZTXQtoxLyo9SNLT06GtrQ34xtyADg4OwOfzRRJcohoWFxfD/v5+SDUMeZCwRLUnGUc3B9XHItUnmZ8rVB+l/xWE5qNUEv6qz3IJxKv+mEggXvXXzN/Xr/ZzGhxw/9fv+d9Zv1PXbDDjqQAAAABJRU5ErkJggg==',
                                     shortcut_key = 'Q'
where id = 20;

-- 刷数据sql
INSERT INTO v2_charge_pay_mode_relation (id, clinic_id, chain_id, pay_mode_config_id, sort, is_enable, is_deleted, created, created_by, last_modified_by, last_modified)
select substr(uuid_short(), 4), chain_id, chain_id, 20, max(sort) + 1, 1, 0, now(), '00000000000000000000000000000000', '00000000000000000000000000000000', now() from v2_charge_pay_mode_relation where chain_id != '' and is_deleted = 0 group by chain_id;


-- 欠费需求相关表
-- 欠费单
create table v2_charge_owe_sheet
(
    id               bigint                                    not null
        primary key,
    charge_sheet_id  varchar(32)                               not null comment '收费单id',
    chain_id         varchar(32)                               not null comment '连锁id',
    clinic_id        varchar(32)                               not null comment '诊所id',
    patient_id       varchar(32)                               not null comment '患者id',
    received_price   decimal(15, 4)  default 0.0000            not null comment '已收金额',
    refunded_price   decimal(15, 4)  default 0.0000            not null comment '已退金额（负值）',
    total_price      decimal(15, 4)  default 0.0000            not null comment '欠的总金额',
    is_old_record    tinyint         default 0                 not null comment '是否为历史单据，退费重收时会把该字段置为1',
    status           tinyint         default 0                 not null comment '支付状态：0：待收，10：部分收，20：已收，30：部分退费，40：已退费',
    is_deleted       tinyint         default 0                 not null comment '删除状态：0：未删除，1：已删除',
    obsolete_millis  bigint unsigned default 0                 not null comment '作废的毫秒值，删除和修改isOldRecord为1时都会对该字段进行赋值',
    created          timestamp       default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by       varchar(32)                               not null comment '创建人id',
    last_modified    timestamp       default CURRENT_TIMESTAMP not null comment '最后修改时间',
    last_modified_by varchar(32)                               not null comment '最后修改人',
    constraint ux_charge_sheet_id
        unique (charge_sheet_id, obsolete_millis)
)
    comment '欠费单';

create index ix_chain_id
    on v2_charge_owe_sheet (chain_id);

create index ix_clinic_id
    on v2_charge_owe_sheet (clinic_id);

create index ix_patient_id
    on v2_charge_owe_sheet (patient_id);





-- 欠费单欠费金额流水日志
create table v2_charge_owe_sheet_log
(
    id                    bigint                              not null comment '主键id'
        primary key,
    chain_id              varchar(32)                         not null comment '连锁id',
    clinic_id             varchar(32)                         not null comment '诊所id',
    owe_sheet_id          bigint                              not null comment '欠费单id',
    charge_transaction_id varchar(32)                         not null comment '收费流水id',
    charge_sheet_id       varchar(32)                         not null comment '收费流水id',
    amount                decimal(15, 4)                      not null comment '金额',
    created               timestamp default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by            varchar(32)                         not null,
    last_modified         timestamp default CURRENT_TIMESTAMP not null,
    last_modified_by      varchar(32)                         not null,
    constraint ux_owe_sheet_id_charge_transaction_id
        unique (owe_sheet_id, charge_transaction_id)
)
    comment '欠费单欠费金额流水日志';

create index ix_chain_id
    on v2_charge_owe_sheet_log (chain_id);

create index ix_charge_sheet_id
    on v2_charge_owe_sheet_log (charge_sheet_id);

create index ix_clinic_id
    on v2_charge_owe_sheet_log (clinic_id);

create index ix_owe_sheet_id
    on v2_charge_owe_sheet_log (owe_sheet_id);



-- 组合还款流水表
create table v2_charge_owe_combine_transaction
(
    id                             bigint                                   not null comment '主键id'
        primary key,
    chain_id                       varchar(32)                              not null comment '连锁id',
    clinic_id                      varchar(32)                              not null comment '诊所id',
    combine_order_id               bigint                                   not null comment '组合支付订单id',
    combine_order_transaction_id   bigint                                   not null comment '组合支付订单流水id',
    business_id                    bigint                                   null comment '业务id',
    source                         tinyint                                  not null comment '订单来源：0未知，1：住院结算单，2：欠费单',
    associate_transaction_id       bigint                                   null comment '退款时关联的对应支付时的id',
    third_party_pay_transaction_id varchar(32)                              null comment '第三方支付流水id',
    third_party_pay_card_id        varchar(32)                              null comment '第三方支付卡id',
    third_party_pay_card_balance   decimal(15, 4)                           null comment '第三方支付卡余额',
    third_party_pay_info           json                                     null comment '第三方支付信息',
    third_party_pay_order_id       varchar(32)                              null comment '第三方支付订单号',
    is_paid_back                   tinyint(1)     default 0                 not null comment '部分支付退回标记',
    pay_mode                       int                                      not null comment '支付方式',
    pay_sub_mode                   int            default 0                 not null comment '支付方式子类型',
    pay_mode_display_name          varchar(128)                             not null comment '支付方式名称',
    type                           tinyint(1)     default 0                 not null comment '类型：1：支付，2：退费',
    amount                         decimal(15, 4) default 0.0000            not null comment '金额',
    refunded_amount                decimal(15, 4) default 0.0000            not null comment '退款金额',
    present_amount                 decimal(15, 4) default 0.0000            not null comment '赠金支付金额',
    principal_amount               decimal(15, 4) default 0.0000            not null comment '本金支付金额',
    is_deleted                     tinyint(1)     default 0                 not null comment '删除状态：0：未删除，1：已删除',
    created                        timestamp      default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by                     varchar(32)                              not null comment '创建人',
    last_modified                  timestamp      default CURRENT_TIMESTAMP not null comment '最后修改人',
    last_modified_by               varchar(32)                              not null comment '最后修改时间'
)
    comment '组合还款流水表';

create index ix_business_id
    on v2_charge_owe_combine_transaction (business_id);

create index ix_chain_id
    on v2_charge_owe_combine_transaction (chain_id);

create index ix_clinic_id
    on v2_charge_owe_combine_transaction (clinic_id);


alter table v2_charge_owe_combine_transaction
	add charge_comment varchar(1024) null comment '交易备注' after principal_amount;



-- 欠费单收费退费记录
create table v2_charge_owe_combine_transaction_record
(
    id                              bigint                                   not null comment '主键id'
        primary key,
    chain_id                        varchar(32)                              not null comment '连锁id',
    clinic_id                       varchar(32)                              not null comment '诊所id',
    owe_sheet_id                    bigint                                   not null comment '欠费单id',
    owe_combine_transaction_id      bigint(32)                               not null comment '组合还款流水id',
    combine_order_id                bigint                                   not null comment '支付订单id',
    combine_order_transaction_id    bigint                                   not null comment '组合支付订单流水id',
    combine_order_item_id           bigint                                   not null comment '支付订单子项id',
    associate_transaction_record_id bigint                                   null,
    pay_mode                        int                                      not null comment '支付方式',
    pay_sub_mode                    int            default 0                 not null comment '支付方式子类型',
    pay_mode_display_name           varchar(128)                             not null comment '支付方式名称',
    type                            tinyint(1)     default 0                 not null comment '类型：1：支付，2：退费',
    amount                          decimal(15, 4) default 0.0000            not null comment '金额',
    refunded_amount                 decimal(15, 4) default 0.0000            not null comment '退款金额',
    present_amount                  decimal(15, 4) default 0.0000            not null comment '赠金支付金额',
    principal_amount                decimal(15, 4) default 0.0000            not null comment '本金支付金额',
    is_deleted                      tinyint(1)     default 0                 not null comment '删除状态：0：未删除，1：已删除',
    created                         timestamp      default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by                      varchar(32)                              not null comment '创建人',
    last_modified                   timestamp      default CURRENT_TIMESTAMP not null comment '最后修改人',
    last_modified_by                varchar(32)                              not null comment '最后修改时间'
)
    comment '欠费单收费退费记录';

create index ix_chain_id
    on v2_charge_owe_combine_transaction_record (chain_id);

create index ix_clinic_id
    on v2_charge_owe_combine_transaction_record (clinic_id);

create index ix_combine_order_id
    on v2_charge_owe_combine_transaction_record (combine_order_id);

create index ix_combine_order_item_id
    on v2_charge_owe_combine_transaction_record (combine_order_item_id);

create index ix_owe_combine_transaction_id
    on v2_charge_owe_combine_transaction_record (owe_combine_transaction_id);

create index ix_owe_sheet_id
    on v2_charge_owe_combine_transaction_record (owe_sheet_id);


-- 组合支付订单表
create table v2_charge_combine_order
(
    id               bigint                              not null comment '主键id'
        primary key,
    chain_id         varchar(32)                         not null comment '连锁id',
    clinic_id        varchar(32)                         not null comment '诊所id',
    source           int                                 not null comment '订单来源：0未知，1：住院结算单，2：欠费单',
    status           int                                 not null comment '订单状态：0：待支付，10：部分支付，20：已支付，30：部分退费，40：已退费',
    total_price      decimal(15, 4)                      not null comment '订单总金额',
    paid_price       decimal(15, 4)                      not null comment '已支付金额',
    refunded_price   decimal(15, 4)                      not null comment '已退款金额',
    is_deleted       tinyint(1)                          not null comment '是否删除：0：未删除，1：已删除',
    created          timestamp default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by       varchar(32)                         not null comment '创建人',
    last_modified    timestamp default CURRENT_TIMESTAMP not null comment '最后修改时间',
    last_modified_by varchar(32)                         not null comment '最后修改人'
)
    comment '组合支付订单表';

create index ix_chain_id
    on v2_charge_combine_order (chain_id);

create index ix_clinic_id
    on v2_charge_combine_order (clinic_id);


-- 组合支付订单子项
create table v2_charge_combine_order_item
(
    id               bigint                                   not null comment '主键id'
        primary key,
    chain_id         varchar(32)                              not null comment '连锁id',
    clinic_id        varchar(32)                              not null comment '诊所id',
    order_id         bigint                                   not null comment '第三方支付订单id',
    business_id      varchar(32)                              not null comment '业务id',
    price            decimal(15, 4) default 0.0000            not null comment '金额',
    paid_price       decimal(15, 4) default 0.0000            not null comment '已支付金额',
    refunded_price   decimal(15, 4) default 0.0000            not null comment '已退金额',
    pay_status       int            default 0                 not null comment '支付状态：0：待支付，10：部分支付，20：已支付，30：部分退费，40：已退费',
    is_deleted       tinyint(1)                               not null comment '是否删除：0：未删除，1：已删除',
    created          timestamp      default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by       varchar(32)                              not null comment '创建人',
    last_modified    timestamp      default CURRENT_TIMESTAMP not null comment '最后修改时间',
    last_modified_by varchar(32)                              not null comment '最后修改人'
)
    comment '组合支付订单子项';

create index ix_chain_id
    on v2_charge_combine_order_item (chain_id);

create index ix_clinic_id
    on v2_charge_combine_order_item (clinic_id);

create index ix_order_id
    on v2_charge_combine_order_item (order_id);

create index ix_pay_transaction_id
    on v2_charge_combine_order_item (business_id);

-- 组合支付订单流水
create table v2_charge_combine_order_transaction
(
    id                             bigint                                   not null comment '主键id'
        primary key,
    chain_id                       varchar(32)                              not null comment '连锁id',
    clinic_id                      varchar(32)                              not null comment '诊所id',
    order_id                       bigint                                   not null comment '订单id',
    associate_transaction_id       bigint                                   null comment '退款关联的支付id',
    third_party_pay_transaction_id varchar(32)                              null comment '第三方支付流水id',
    third_party_pay_card_id        varchar(32)                              null comment '第三方支付卡id',
    third_party_pay_card_balance   decimal(15, 4)                           null comment '第三方支付卡余额',
    third_party_pay_info           json                                     null comment '第三方支付信息',
    third_party_pay_order_id       varchar(32)                              null comment '第三方支付订单号',
    pay_mode                       int                                      not null comment '支付方式',
    pay_sub_mode                   int            default 0                 not null comment '支付方式子类型',
    pay_mode_display_name          varchar(128)                             not null comment '支付方式名称',
    type                           tinyint(1)     default 0                 not null comment '类型：1：支付，2：退费',
    amount                         decimal(15, 4) default 0.0000            not null comment '金额',
    refunded_amount                decimal(15, 4) default 0.0000            not null comment '退款金额',
    present_amount                 decimal(15, 4) default 0.0000            not null comment '赠金支付金额',
    principal_amount               decimal(15, 4) default 0.0000            not null comment '本金支付金额',
    is_deleted                     tinyint(1)                               not null comment '是否删除：0：未删除，1：已删除',
    created                        timestamp      default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by                     varchar(32)                              not null comment '创建人',
    last_modified                  timestamp      default CURRENT_TIMESTAMP not null comment '最后修改时间',
    last_modified_by               varchar(32)                              not null comment '最后修改人'
)
    comment '组合支付订单流水';

create index ix_chain_id
    on v2_charge_combine_order_transaction (chain_id);

create index ix_clinic_id
    on v2_charge_combine_order_transaction (clinic_id);

create index ix_order_id
    on v2_charge_combine_order_transaction (order_id);

alter table v2_charge_combine_order_transaction
	add extra json null comment '业务补充信息' after principal_amount;



-- 组合支付订单流水明细
create table v2_charge_combine_order_transaction_record
(
    id                   bigint                                   not null comment '主键id'
        primary key,
    chain_id             varchar(32)                              not null comment '连锁id',
    clinic_id            varchar(32)                              not null comment '诊所id',
    order_id             bigint                                   not null comment '订单id',
    order_transaction_id bigint                                   not null comment '订单流水id',
    order_item_id        bigint                                   not null comment '订单子项id',
    amount               decimal(15, 4) default 0.0000            not null comment '金额',
    is_deleted           tinyint(1)                               not null comment '是否删除：0：未删除，1：已删除',
    created              timestamp      default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by           varchar(32)                              not null comment '创建人',
    last_modified        timestamp      default CURRENT_TIMESTAMP not null comment '最后修改时间',
    last_modified_by     varchar(32)                              not null comment '最后修改人'
)
    comment '组合支付订单流水明细';

create index ix_chain_id
    on v2_charge_combine_order_transaction_record (chain_id);

create index ix_clinic_id
    on v2_charge_combine_order_transaction_record (clinic_id);

create index ix_order_id
    on v2_charge_combine_order_transaction_record (order_id);

create index ix_order_item_id
    on v2_charge_combine_order_transaction_record (order_item_id);

create index ix_order_transaction_id
    on v2_charge_combine_order_transaction_record (order_transaction_id);




-- 订单发起第三方支付记录表
create table v2_charge_combine_order_pay_transaction
(
    id                           bigint                                   not null comment '主键id'
        primary key,
    chain_id                     varchar(32)                              not null comment '连锁id',
    clinic_id                    varchar(32)                              not null comment '诊所id',
    order_id                     bigint                                   not null comment '组合订单id',
    associate_pay_transaction_id bigint                                   null comment '退款关联的支付id',
    pay_transaction_id           varchar(32)                              not null comment '支付流水id',
    pay_info                     json                                     null comment '三方支付系统流水id',
    pay_type                     tinyint        default 0                 not null comment '支付类型（1：付款，2：退款）',
    pay_status                   tinyint        default 0                 not null comment '支付状态（1：支付中；2：支付成功；3：支付失败；4：支付取消）',
    pay_mode                     int                                      not null comment '支付方式',
    pay_sub_mode                 int            default 0                 not null comment '支付方式子类型',
    amount                       decimal(15, 4) default 0.0000            not null comment '金额',
    business_pay_transaction_id  varchar(32)                              null comment '业务支付单id',
    order_item_info              json                                     not null comment '订单子项明细',
    pay_req                      json                                     not null comment '第三方支付请求参数',
    is_deleted                   tinyint                                  not null comment '是否删除：0：未删除，1：已删除',
    created                      timestamp      default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by                   varchar(32)                              not null comment '创建人',
    last_modified                timestamp      default CURRENT_TIMESTAMP not null comment '最后修改时间',
    last_modified_by             varchar(32)                              not null comment '最后修改人'
)
    comment '订单发起第三方支付记录表';

create index ix_business_pay_transaction_id
    on v2_charge_combine_order_pay_transaction (business_pay_transaction_id);

create index ix_chain_id
    on v2_charge_combine_order_pay_transaction (chain_id);

create index ix_clinic_id
    on v2_charge_combine_order_pay_transaction (clinic_id);

create index ix_order_id
    on v2_charge_combine_order_pay_transaction (order_id);

create index ix_pay_transaction_id
    on v2_charge_combine_order_pay_transaction (pay_transaction_id);


