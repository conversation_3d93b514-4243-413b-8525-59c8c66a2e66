alter table v2_charge_sheet
	add transaction_record_handle_mode tinyint default 0 not null
	 comment '对于退费记录charge_transaction_record的处理方式，默认为0，表示使用新的退费记录方式，选择多少数量就记录多少数量，数量不会有小数，单独标识待退金额，1表示根据退的金额反算数量，数量会有小数';


alter table v2_charge_transaction_record
	add scene_type int default 0 not null comment '场景类型：0：普通收退费，1：欠收，2：欠退';

-- 刷数据sql
update v2_charge_sheet set transaction_record_handle_mode = 1 where status in (1,3) and chain_id in (:chainId);