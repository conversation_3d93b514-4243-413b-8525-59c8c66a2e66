alter table abc_cis_charge.v2_charge_sheet_register_identity
    modify id_card varchar(64) null comment '身份证号';

alter table abc_cis_charge.v2_charge_sheet_register_identity
    modify mobile varchar(64) null comment '手机号';


alter table abc_cis_charge.v2_charge_sheet_additional
    add extended_info json default null comment '其他信息';


create table abc_cis_charge.v2_charge_sheet_register_identity
(
    id               bigint unsigned                     not null
        primary key,
    chain_id         varchar(32)                         not null comment '连锁id',
    clinic_id        varchar(32)                         not null comment '诊所id',
    name             varchar(200)                        not null comment '姓名',
    birthday         varchar(20)                         null comment '生日，当实名登记时，此处为生日，age填空',
    age              json                                null comment '年龄，当处理登记时，此处为登记时的年龄，birthday填空',
    sex              varchar(4)                          null comment '性别',
    id_card          varchar(20)                         null comment '身份证号',
    mobile           varchar(20)                         null comment '手机号码',
    mobile_last4     varchar(4)                          null comment '手机号后4位',
    version          varchar(32)                         not null comment '当前版本',
    is_deleted       int       default 0                 not null comment '是否删除状态（0：正常；1：被删除）',
    last_modified_by varchar(32)                         not null comment '最后修改人',
    last_modified    timestamp default CURRENT_TIMESTAMP not null comment '最后修改时间',
    created          timestamp default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by       varchar(32)                         not null comment '创建人'
)
    comment '实名登记身份信息';

create index ix_chain_id_version
    on abc_cis_charge.v2_charge_sheet_register_identity (chain_id, version);

create table abc_cis_charge.v2_charge_sheet_register_info
(
    id               bigint unsigned                               not null
        primary key,
    chain_id         varchar(32)                                   not null comment '连锁id',
    clinic_id        varchar(32)                                   not null comment '诊所id',
    charge_sheet_id  varchar(32)                                   null comment '收费单id，可以为空，有可能是先生成登记信息，再更新ChargeSheetId',
    identity_id      bigint unsigned                               null comment '登记人信息id',
    prescription_id  bigint unsigned                               null comment '处方id',
    is_deleted       tinyint(1) unsigned default 0                 not null comment '是否删除状态（0：正常；1：被删除）',
    last_modified    timestamp           default CURRENT_TIMESTAMP not null comment '最后修改时间',
    last_modified_by varchar(32)                                   not null comment '最后修改人',
    created          timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by       varchar(32)                                   not null comment '创建人'
)
    comment '收费登记信息';

create table abc_cis_charge.v2_charge_sheet_register_prescription
(
    id                     bigint unsigned                               not null
        primary key,
    chain_id               varchar(32)                                   not null comment '连锁id',
    clinic_id              varchar(32)                                   not null comment '诊所id',
    no                     varchar(64)                                   null comment '处方编号',
    register_identity_id    bigint unsigned                              not null comment '实名身份id',
    prescription_physician varchar(32)                                   null comment '处方医师',
    reviewer_physician     varchar(32)                                   not null comment '审方医师',
    reviewer               varchar(32)                                   not null comment '复核人',
    dispatcher             varchar(32)                                   not null comment '调配人',
    remark                 varchar(128)                                  null comment '备注',
    prescription_urls      json                                          null comment '处方图片url',
    version                varchar(32)                                   not null comment '版本',
    is_deleted             tinyint(1) unsigned default 0                 not null comment '是否删除状态（0：正常；1：被删除）',
    created                timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by             varchar(32)                                   not null comment '创建人'
)
    comment '处方信息';

create index ix_chain_id_version
    on abc_cis_charge.v2_charge_sheet_register_prescription (chain_id, version);

