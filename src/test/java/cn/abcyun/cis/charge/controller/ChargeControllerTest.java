package cn.abcyun.cis.charge.controller;

import cn.abcyun.cis.charge.AbcCisChargeServiceApplication;
import cn.abcyun.cis.charge.controller.api.ChargeController;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

/**
 * chargeController 测试
 */
@RunWith(SpringRunner.class)
@ActiveProfiles("local")
@SpringBootTest(classes = AbcCisChargeServiceApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class ChargeControllerTest {
    private static final Logger sLogger = LoggerFactory.getLogger(ChargeControllerTest.class);
    private MockMvc          mockMvc;
    @Autowired
    private ChargeController chargeController;

    @Before
    public void setup() {
        mockMvc = MockMvcBuilders.standaloneSetup(chargeController).build();
    }

    @Test
    public void testGivenSelfPayCharge_test() throws Exception {
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders.post("/api/v2/charges/calculate")
                .header("cis-clinic-id", "46a3e35d71d84013ae85141514db8c79")
                .header("cis-chain-id", "19e12c3a4a094e9f8dfbfc176378802a")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"chargeSheetId\":\"ffffffff0000000009a1c20004fde003\",\"chargeForms\":[{\"id\":\"ffffffff0000000009a1c20004fde004\",\"keyId\":null,\"sourceFormType\":4,\"sort\":0,\"specification\":null,\"usageScopeId\":null,\"medicineStateScopeId\":null,\"vendorId\":null,\"vendorName\":null,\"totalPrice\":12,\"expectedTotalPrice\":null,\"expectedPriceFlag\":0,\"usageInfo\":null,\"chargeFormItems\":[{\"id\":\"ffffffff0000000009a1c20004fde005\",\"keyId\":null,\"name\":\"西药1\",\"unit\":\"盒\",\"unitCount\":6,\"doseCount\":1,\"executedUnitCount\":null,\"unitPrice\":2,\"discountPrice\":null,\"expectedUnitPrice\":null,\"expectedTotalPrice\":null,\"sourceUnitPrice\":null,\"productId\":\"ffffffff0000000002094dc0002aa000\",\"productType\":1,\"productSubType\":1,\"isAirPharmacy\":0,\"useDismounting\":0,\"sort\":0,\"composeChildren\":null}],\"deliveryInfo\":null,\"medicalRecord\":null,\"deliveryRule\":null,\"processRule\":null},{\"id\":null,\"keyId\":null,\"sourceFormType\":13,\"sort\":0,\"specification\":null,\"usageScopeId\":null,\"medicineStateScopeId\":null,\"vendorId\":null,\"vendorName\":null,\"totalPrice\":null,\"expectedTotalPrice\":null,\"expectedPriceFlag\":0,\"usageInfo\":null,\"chargeFormItems\":[{\"id\":null,\"keyId\":null,\"name\":\"快递费\",\"unit\":null,\"unitCount\":1,\"doseCount\":1,\"executedUnitCount\":null,\"unitPrice\":null,\"discountPrice\":null,\"expectedUnitPrice\":null,\"expectedTotalPrice\":null,\"sourceUnitPrice\":null,\"productId\":null,\"productType\":13,\"productSubType\":null,\"isAirPharmacy\":0,\"useDismounting\":0,\"sort\":0,\"composeChildren\":null}],\"deliveryInfo\":null,\"medicalRecord\":null,\"deliveryRule\":null,\"processRule\":null}],\"promotions\":[{\"id\":\"c9964e7d62044f85b1147c1c893a7901\",\"checked\":true}],\"couponPromotions\":[{\"id\":\"640309461322383360\",\"checked\":false,\"expectedChecked\":null,\"currentCount\":1},{\"id\":\"640321727748980736\",\"checked\":false,\"expectedChecked\":null,\"currentCount\":1}],\"giftRulePromotions\":[],\"memberId\":\"\",\"adjustmentFee\":0,\"payType\":0,\"payMode\":0,\"deliveryType\":1,\"deliveryInfo\":{\"addressProvinceId\":null,\"addressProvinceName\":\"四川省\",\"addressCityId\":null,\"addressCityName\":\"成都市\",\"addressDistrictId\":\"510107\",\"addressDistrictName\":\"武侯区\",\"addressDetail\":\"高新区金桂路233号东苑c区\",\"deliveryName\":\"李龙彬\",\"deliveryMobile\":\"18628397711\",\"deliveryCompany\":null,\"deliveryFee\":null,\"deliveryOrderNo\":null,\"deliveryPayType\":1},\"patientId\":null,\"isDecoction\":0,\"decoctionInfo\":null,\"processInfos\":null}")
        )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn();

        sLogger.info(mvcResult.getResponse().getContentAsString());
    }

}