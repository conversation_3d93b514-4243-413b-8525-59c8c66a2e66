package cn.abcyun.cis.charge.service;

import cn.abcyun.cis.commons.util.JsonUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest                 //Spring Boot 单测的注解
@ActiveProfiles("local")
public class ChargePrintServiceTest {

    @Autowired
    private ChargePrintService chargePrintService;

    @Test
    public void findChargeSheetPrintViewById() {
        System.out.println(
                JsonUtils.dump(
                        chargePrintService.findChargeSheetPrintViewById("ffffffff0000000034f7683ca92fc004")
                )
        );
    }

    @Test
    public void findRefundChargeSheetPrintViewById() {
        System.out.println(
                JsonUtils.dump(
                        chargePrintService.findRefundChargeSheetPrintViewById("ffffffff0000000034d5fb25a7dc0004")
                )
        );
    }
}