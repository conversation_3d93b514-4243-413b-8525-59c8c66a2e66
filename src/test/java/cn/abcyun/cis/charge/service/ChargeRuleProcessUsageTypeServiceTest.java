package cn.abcyun.cis.charge.service;

import cn.abcyun.cis.charge.service.dto.UsageTypeNode;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import org.hamcrest.MatcherAssert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.transaction.Transactional;
import java.util.List;

import static org.hamcrest.Matchers.*;

@RunWith(SpringRunner.class)    //Spring Boot 单测的注解
@SpringBootTest                 //Spring Boot 单测的注解
@ActiveProfiles("local")        //配置文件用本地 --- 这里需要执行junyong的sh脚本映射
@Configuration
@Transactional //事务
@Rollback(true) //单测跑完，需要把全部数据回滚掉
//作为一个配置类，需要提供bean，注入到
public class ChargeRuleProcessUsageTypeServiceTest {
    @Autowired
    private ChargeRuleProcessUsageTypeService chargeRuleProcessUsageTypeService;


    //拉取可用新
    @Test
    public void testGivenClinicId_WhenGetAvailableUsages_ThenReturnAvaliableUsages() throws ParamRequiredException {
        String clinicId = "46a3e35d71d84013ae85141514db8c79"; //我是乙店
        List<UsageTypeNode> rsp = chargeRuleProcessUsageTypeService.getAvailableUsages(clinicId, "");
        MatcherAssert.assertThat(rsp, notNullValue());
        MatcherAssert.assertThat(rsp, hasSize(4));//支持煎药，制膏，打粉 1，2，3
        MatcherAssert.assertThat(rsp.get(0).getType(), equalTo(1));//煎药
        MatcherAssert.assertThat(rsp.get(1).getType(), equalTo(2));//制膏
        MatcherAssert.assertThat(rsp.get(2).getType(), equalTo(3));//打粉
        MatcherAssert.assertThat(rsp.get(0).getChildren(), hasSize(2));//煎药
        MatcherAssert.assertThat(rsp.get(0).getChildren().get(0).getId(), equalTo(3));//人工煎药
        MatcherAssert.assertThat(rsp.get(0).getChildren().get(1).getId(), equalTo(4));//机器煎药
        MatcherAssert.assertThat(rsp.get(2).getChildren(), hasSize(2));//打粉
        MatcherAssert.assertThat(rsp.get(2).getChildren().get(0).getId(), equalTo(12));//细粉
        MatcherAssert.assertThat(rsp.get(2).getChildren().get(1).getId(), equalTo(13));//超细粉
    }
}