package cn.abcyun.cis.charge.service;

import cn.abcyun.cis.charge.amqp.HAMQConsumer;
import cn.abcyun.cis.charge.util.JsonUtils;
import cn.abcyun.cis.commons.amqp.message.ChargeMessage;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 类说明
 *
 * <AUTHOR>
 * @version ChargeExecuteServiceTest.java, 2022/4/8 上午9:51
 */
@RunWith(SpringRunner.class)
@SpringBootTest                 //Spring Boot 单测的注解
@ActiveProfiles("local")
public class ChargeExecuteServiceTest {
    @Autowired
    private HAMQConsumer          hamqConsumer;

    @Test
    public void testCreateOrUpdateChargeExecuteItems(){
        String message = "{\"patientOrder\":{\"id\":\"ffffffff000000003486d71de1f34000\",\"patientName\":\"陈磊-dev\",\"patientId\":\"ffffffff000000003485c010e0038000\",\"patientSex\":\"男\",\"patientMobile\":\"13333333333\",\"patientNamePy\":\"chenlei-dev\",\"patientNamePyFirst\":\"CL-dev\",\"patientAge\":null,\"patientBirthday\":\"1992-02-17\",\"isMember\":0,\"no\":2659,\"chainId\":\"ffffffff00000000146808c695534000\",\"clinicId\":\"ffffffff00000000146808c695534004\",\"source\":2,\"sourceClientType\":0,\"reserveTime\":null,\"visitSourceId\":null,\"visitSourceFrom\":null,\"visitSourceName\":null,\"visitSourceFromName\":null,\"visitSourceRemark\":null,\"revisitStatus\":2,\"hospitalPatientOrderId\":null,\"shebaoCardInfo\":null,\"shebaoChargeType\":1,\"referralFlag\":0,\"referralPatientOrder\":null},\"type\":10001,\"operatorId\":\"6e45706922a74966ab51e4ed1e604641\",\"isOutpatientDeleted\":0}";

        hamqConsumer.receive(JsonUtils.readValue(message, ChargeMessage.class));
    }
}
