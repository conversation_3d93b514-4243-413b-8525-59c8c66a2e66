package cn.abcyun.cis.charge.service;

import cn.abcyun.cis.commons.util.DateUtils;
import cn.abcyun.cis.commons.util.JsonUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDate;
import java.time.LocalTime;

@RunWith(SpringRunner.class)
@SpringBootTest                 //Spring Boot 单测的注解
@ActiveProfiles("local")
public class ChargeNurseServiceTest {

    @Autowired
    private ChargeNurseService chargeNurseService;

    @Test
    public void findChargeSheetAbstractList() {
        System.out.println(
                JsonUtils.dump(
                        chargeNurseService.findChargeSheetAbstractList(0,
                                10,
                                "",
                                "6a869c22abee4ffbaef3e527bbb70aeb",
                                "fff730ccc5ee45d783d82a85b8a0e52d",
                                "bbe59dd98d4d4f39b90243e0844a911f",
                                null,
                                DateUtils.toInstant(LocalDate.now().atTime(LocalTime.MIN)),
                                DateUtils.toInstant(LocalDate.now().atTime(LocalTime.MAX)),
                                0,
                                "",
                                null
                        )
                )
        );
    }
}