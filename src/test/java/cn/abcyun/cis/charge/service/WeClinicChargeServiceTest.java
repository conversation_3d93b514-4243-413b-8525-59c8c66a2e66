package cn.abcyun.cis.charge.service;

import cn.abcyun.cis.charge.api.model.*;
import cn.abcyun.cis.charge.base.ChargeServiceException;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.base.ProductInfoChangedException;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.service.dto.UsageTypeNode;
import cn.abcyun.cis.charge.util.JsonUtils;
import cn.abcyun.cis.commons.CisServiceResponse;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.exception.ServiceInternalException;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrder;
import org.hamcrest.MatcherAssert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

import static org.hamcrest.Matchers.*;

@RunWith(SpringRunner.class)
@SpringBootTest                 //Spring Boot 单测的注解
@ActiveProfiles("local")
public class WeClinicChargeServiceTest {

    @Autowired
    private WeClinicChargeService weClinicChargeService;

    @Test
    public void testGiveReq_WhenCallCreateChargeSheetByClone_ThenSuccess() {
        WeClinicCreateChargeSheetByCloneReq req = JsonUtils.readValue("{\"chargeSheetId\":\"ffffffff0000000009ca01a80511e000\",\"chainId\":\"19e12c3a4a094e9f8dfbfc176378802a\",\"clinicId\":\"46a3e35d71d84013ae85141514db8c79\",\"patientId\":\"e3e1b3da02e0432b805d437ac4a7cf72\",\"remarks\":\"\",\"forms\":[{\"chargeFormId\":\"ffffffff0000000009ca01a80511e001\",\"doseCount\":null}]}", WeClinicCreateChargeSheetByCloneReq.class);
        WeClinicCreateChargeSheetByCloneRsp rsp = weClinicChargeService.createChargeSheetByClone(req,null, Constants.ANONYMOUS_PATIENT_ID);
    }
}