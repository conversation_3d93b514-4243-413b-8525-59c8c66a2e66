package cn.abcyun.cis.charge.service;

import cn.abcyun.cis.charge.api.model.*;
import cn.abcyun.cis.charge.api.model.orderlist.ChargeOrderListReq;
import cn.abcyun.cis.charge.api.model.orderlist.ChargeOrderView;
import cn.abcyun.cis.charge.api.model.orderlist.ChargeOrderViewRsp;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import org.hamcrest.MatcherAssert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

import static org.hamcrest.Matchers.*;

@RunWith(SpringRunner.class)
@SpringBootTest                 //Spring Boot 单测的注解
@ActiveProfiles("local")
public class ChargeSheetServiceWeClinicOrderListTest {
    @Autowired
    private ChargeSheetService chargeSheetService;

    @Autowired
    private ChargeRuleProcessUsageTypeService chargeRuleProcessUsageTypeService;

    public static class TEST_SHEET_ITEM {
        public String chargeSheetId;
        public int status;
        public int checkStatus;
        public int dispensingStatus;
        public int selfPayStatus;

        public TEST_SHEET_ITEM(String sheetId, int status, int checkStatus, int dispensingStatus, int selfPayStatus) {
            this.chargeSheetId = sheetId;
            this.status = status;
            this.checkStatus = checkStatus;
            this.dispensingStatus = dispensingStatus;
            this.selfPayStatus = selfPayStatus;
        }
    }

    ;
    private List<TEST_SHEET_ITEM> testArray = new ArrayList<>();
    private List<TEST_SHEET_ITEM> testWaitingPayArray = new ArrayList<>();
    private List<TEST_SHEET_ITEM> testWaitingDispenseArray = new ArrayList<>();

    @Before
    public void beforeTest() {

        //这条测试数据：门诊开单 + 微诊所自助支付打开+自动推送+微信支付
        testArray.add(new TEST_SHEET_ITEM("ffffffff0000000009c7f5c0050ec003", Constants.ChargeOrderStatus.WAITING_DISPENSE, ChargeSheet.CheckStatus.NOT_NEED_CHECK, Constants.DispensingStatus.WAITING, Constants.ChargeSheetSelfPayStatus.SELF_PAY_USER_SELF_PAID));
        //这条测试数据：门诊开单 + 微诊所自助支付打开+自动推送 待支付
        testArray.add(new TEST_SHEET_ITEM("ffffffff0000000009c81e70050f4003", Constants.ChargeOrderStatus.WAITING_PAY, ChargeSheet.CheckStatus.NOT_NEED_CHECK, Constants.DispensingStatus.NONE, Constants.ChargeSheetSelfPayStatus.SELF_PAY_WAITING_PAY));
        //这条测试数据：收费直接开单+开启自助支付+收费处推送 待支付
        testArray.add(new TEST_SHEET_ITEM("ffffffff0000000009c8b96005116000", Constants.ChargeOrderStatus.WAITING_PAY, ChargeSheet.CheckStatus.NOT_NEED_CHECK, Constants.DispensingStatus.NONE, Constants.ChargeSheetSelfPayStatus.SELF_PAY_WAITING_PAY));
        //这条测试数据：收费直接开单+开启自助支付+挂单+推送 待支付
        testArray.add(new TEST_SHEET_ITEM("ffffffff0000000009c8c85005116000", Constants.ChargeOrderStatus.WAITING_PAY, ChargeSheet.CheckStatus.NOT_NEED_CHECK, Constants.DispensingStatus.NONE, Constants.ChargeSheetSelfPayStatus.SELF_PAY_WAITING_PAY));

        //这条测试数据：门诊挂号+门诊+开启自助支付+自动推送 待支付
        testArray.add(new TEST_SHEET_ITEM("ffffffff0000000009c8cfa0050e8000", Constants.ChargeOrderStatus.WAITING_PAY, ChargeSheet.CheckStatus.NOT_NEED_CHECK, Constants.DispensingStatus.NONE, Constants.ChargeSheetSelfPayStatus.SELF_PAY_WAITING_PAY));
        //这条测试数据：微诊所在线问诊+聊天窗口开单+接诊+自动推送+微信支付 已支付
        testArray.add(new TEST_SHEET_ITEM("ffffffff0000000009ca01a80511e000", Constants.ChargeOrderStatus.WAITING_DISPENSE, ChargeSheet.CheckStatus.NOT_NEED_CHECK, Constants.DispensingStatus.WAITING, Constants.ChargeSheetSelfPayStatus.SELF_PAY_USER_SELF_PAID));


        //这条测试数据：收费直接开单+开启自助支付+收费处推送 待支付
//        testArray.add(new TEST_SHEET_ITEM("ffffffff000000000982042004dc4000", Constants.ChargeOrderStatus.WAITING_PAY, 3, -1, 1));
//        //这条测试数据：门诊开单 + 微诊所自助支付打开 +自动推送 +  门诊收费 +药房发药 - 完成
//        testArray.add(new TEST_SHEET_ITEM("ffffffff00000000098209d004dc4003", Constants.ChargeOrderStatus.COMPLETE, 0, 1, 1));
//        //这条测试数据：门诊开单 + 微诊所自助支付打开 +自动推送 +  门诊收费 - 待发药
//        testArray.add(new TEST_SHEET_ITEM("ffffffff0000000009826a0804dda000", Constants.ChargeOrderStatus.WAITING_DISPENSE, 0, 0, 1));

        //这条测试数据：门诊开单 + 微诊所自助支付打开+自动推送  待支付
        testWaitingPayArray.add(new TEST_SHEET_ITEM("ffffffff0000000009c81e70050f4003", Constants.ChargeOrderStatus.WAITING_PAY, ChargeSheet.CheckStatus.NOT_NEED_CHECK, Constants.DispensingStatus.NONE, Constants.ChargeSheetSelfPayStatus.SELF_PAY_WAITING_PAY));
        //这条测试数据：收费直接开单+开启自助支付+收费处推送 待支付
        testWaitingPayArray.add(new TEST_SHEET_ITEM("ffffffff0000000009c8b96005116000", Constants.ChargeOrderStatus.WAITING_PAY, ChargeSheet.CheckStatus.NOT_NEED_CHECK, Constants.DispensingStatus.NONE, Constants.ChargeSheetSelfPayStatus.SELF_PAY_WAITING_PAY));
        //这条测试数据：收费直接开单+开启自助支付+挂单+推送 待支付
        testWaitingPayArray.add(new TEST_SHEET_ITEM("ffffffff0000000009c8c85005116000", Constants.ChargeOrderStatus.WAITING_PAY, ChargeSheet.CheckStatus.NOT_NEED_CHECK, Constants.DispensingStatus.NONE, Constants.ChargeSheetSelfPayStatus.SELF_PAY_WAITING_PAY));
        //这条测试数据：门诊挂号+门诊+开启自助支付+自动推送 待支付
        testWaitingPayArray.add(new TEST_SHEET_ITEM("ffffffff0000000009c8cfa0050e8000", Constants.ChargeOrderStatus.WAITING_PAY, ChargeSheet.CheckStatus.NOT_NEED_CHECK, Constants.DispensingStatus.NONE, Constants.ChargeSheetSelfPayStatus.SELF_PAY_WAITING_PAY));

        //这条测试数据：微诊所在线问诊+聊天窗口开单+接诊+自动推送+微信支付 已支付
        testWaitingDispenseArray.add(new TEST_SHEET_ITEM("ffffffff0000000009ca01a80511e000", Constants.ChargeOrderStatus.WAITING_DISPENSE, ChargeSheet.CheckStatus.NOT_NEED_CHECK, Constants.DispensingStatus.WAITING, Constants.ChargeSheetSelfPayStatus.SELF_PAY_USER_SELF_PAID));

        //这条测试数据：门诊开单 + 微诊所自助支付打开+自动推送+微信支付
        testWaitingDispenseArray.add(new TEST_SHEET_ITEM("ffffffff0000000009c7f5c0050ec003", Constants.ChargeOrderStatus.WAITING_DISPENSE, ChargeSheet.CheckStatus.NOT_NEED_CHECK, Constants.DispensingStatus.WAITING, Constants.ChargeSheetSelfPayStatus.SELF_PAY_USER_SELF_PAID));

    }

    @Test
    public void testGivenQueryTypeAll_WhenCallWeClinicList_ThenReturnALLList() throws ParamRequiredException {
        ChargeOrderListReq chargeOrderListReq = new ChargeOrderListReq();
        chargeOrderListReq.setPatientIds(new ArrayList<>());
        chargeOrderListReq.getPatientIds().add("e3e1b3da02e0432b805d437ac4a7cf72");
        chargeOrderListReq.setChainId("19e12c3a4a094e9f8dfbfc176378802a");
        chargeOrderListReq.setQueryType(ChargeOrderListReq.QueryType.ALL);
        chargeOrderListReq.setLimit(20);

        int size = 0;
        boolean over = false;
        do {
            ChargeOrderViewRsp rsp = chargeSheetService.getChargeOrderListByPatientId(chargeOrderListReq);
            MatcherAssert.assertThat(rsp, notNullValue());

            for (TEST_SHEET_ITEM test_sheet_item : testArray) {
                ChargeOrderView findView = rsp.getResult().stream().filter(chargeOrderView -> chargeOrderView.getChargeSheetId().equals(test_sheet_item.chargeSheetId)).findFirst().orElse(null);
                if (findView == null) {
                    continue;
                }
                size++;
                MatcherAssert.assertThat(findView.getStatus(), equalTo(test_sheet_item.status));
                MatcherAssert.assertThat(findView.getCheckStatus(), equalTo(test_sheet_item.checkStatus));
                MatcherAssert.assertThat(findView.getDispensingStatus(), equalTo(test_sheet_item.dispensingStatus));
                MatcherAssert.assertThat(findView.getSelfPayStatus(), equalTo(test_sheet_item.selfPayStatus));
            }

            if (rsp.getOffset() + rsp.getLimit() < rsp.getTotalCount()) {
                chargeOrderListReq.setOffset(rsp.getOffset() + rsp.getLimit());
            }
        } while (size < testArray.size() && !over);


    }

    @Test
    public void testGivenQueryTypePaying_WhenCallWeClinicList_ThenReturnOnlyThePayingList() throws ParamRequiredException {
        ChargeOrderListReq chargeOrderListReq = new ChargeOrderListReq();
        chargeOrderListReq.setPatientIds(new ArrayList<>());
        chargeOrderListReq.getPatientIds().add("e3e1b3da02e0432b805d437ac4a7cf72");
        chargeOrderListReq.setChainId("19e12c3a4a094e9f8dfbfc176378802a");
        chargeOrderListReq.setQueryType(ChargeOrderListReq.QueryType.WAITING_PAYING);
        int size = 0;
        do {
            ChargeOrderViewRsp rsp = chargeSheetService.getChargeOrderListByPatientId(chargeOrderListReq);

            size = checkListResult(rsp, size, chargeOrderListReq, testWaitingPayArray, Constants.ChargeOrderStatus.WAITING_PAY);
            if (size == -1)
                break;

        } while (size < testWaitingPayArray.size());


        //单测，一定会拉到这么多条数据
        MatcherAssert.assertThat(size, equalTo(testWaitingPayArray.size()));
    }

    @Test
    public void testGivenQueryTypeDispensing_WhenCallWeClinicList_ThenReturnDispensingList() throws ParamRequiredException {
        ChargeOrderListReq chargeOrderListReq = new ChargeOrderListReq();
        chargeOrderListReq.setPatientIds(new ArrayList<>());
        chargeOrderListReq.getPatientIds().add("e3e1b3da02e0432b805d437ac4a7cf72");
        chargeOrderListReq.setChainId("19e12c3a4a094e9f8dfbfc176378802a");
        chargeOrderListReq.setQueryType(ChargeOrderListReq.QueryType.WAITING_DISPENSING);
        chargeOrderListReq.setLimit(20);
        int size = 0;
        do {
            ChargeOrderViewRsp rsp = chargeSheetService.getChargeOrderListByPatientId(chargeOrderListReq);

            size = checkListResult(rsp, size, chargeOrderListReq, testWaitingDispenseArray, Constants.ChargeOrderStatus.WAITING_DISPENSE);
            if (size == -1)
                break;

        } while (size < testWaitingDispenseArray.size());


        //单测，一定会拉到这么多条数据
        MatcherAssert.assertThat(size, equalTo(testWaitingDispenseArray.size()));
    }

    @Test
    public void testGivenQueryTypeDelivery_WhenCallWeClinicList_ThenReturnDeliveryList() throws ParamRequiredException {
        ChargeOrderListReq chargeOrderListReq = new ChargeOrderListReq();
        chargeOrderListReq.setPatientIds(new ArrayList<>());
        chargeOrderListReq.getPatientIds().add("e3e1b3da02e0432b805d437ac4a7cf72");
        chargeOrderListReq.setChainId("19e12c3a4a094e9f8dfbfc176378802a");
        chargeOrderListReq.setQueryType(ChargeOrderListReq.QueryType.WAITING_DELIVERY);
        chargeOrderListReq.setLimit(20);
        ChargeOrderViewRsp rsp = chargeSheetService.getChargeOrderListByPatientId(chargeOrderListReq);
        MatcherAssert.assertThat(rsp, notNullValue());

        MatcherAssert.assertThat(rsp, notNullValue());
        MatcherAssert.assertThat(rsp.getResult().size(), greaterThan(1));
    }

    @Test
    public void testGivenQueryTypeComplete_WhenCallWeClinicList_ThenReturnCompleteList() throws ParamRequiredException {
        ChargeOrderListReq chargeOrderListReq = new ChargeOrderListReq();
        chargeOrderListReq.setPatientIds(new ArrayList<>());
        chargeOrderListReq.getPatientIds().add("e3e1b3da02e0432b805d437ac4a7cf72");
        chargeOrderListReq.setChainId("19e12c3a4a094e9f8dfbfc176378802a");
        chargeOrderListReq.setQueryType(ChargeOrderListReq.QueryType.FINISHED);
        chargeOrderListReq.setLimit(20);
        ChargeOrderViewRsp rsp = chargeSheetService.getChargeOrderListByPatientId(chargeOrderListReq);
        MatcherAssert.assertThat(rsp, notNullValue());

        MatcherAssert.assertThat(rsp, notNullValue());
        MatcherAssert.assertThat(rsp.getResult().size(), greaterThan(1));
    }


    private int checkListResult(ChargeOrderViewRsp rsp, int size, ChargeOrderListReq chargeOrderListReq, List<TEST_SHEET_ITEM> testArr, int checkStatus) {
        MatcherAssert.assertThat(rsp, notNullValue());
        //检查所有返回的状态
        rsp.getResult().forEach(chargeOrderView -> {
            MatcherAssert.assertThat(chargeOrderView.getStatus(), equalTo(checkStatus));
        });
        for (TEST_SHEET_ITEM test_sheet_item : testArr) {
            ChargeOrderView findView = rsp.getResult().stream().filter(chargeOrderView -> chargeOrderView.getChargeSheetId().equals(test_sheet_item.chargeSheetId)).findFirst().orElse(null);
            if (findView == null) {
                continue;
            }
            size++;
            MatcherAssert.assertThat(findView.getStatus(), equalTo(test_sheet_item.status));
            MatcherAssert.assertThat(findView.getCheckStatus(), equalTo(test_sheet_item.checkStatus));
            MatcherAssert.assertThat(findView.getDispensingStatus(), equalTo(test_sheet_item.dispensingStatus));
            MatcherAssert.assertThat(findView.getSelfPayStatus(), equalTo(test_sheet_item.selfPayStatus));
        }

        if (rsp.getOffset() + rsp.getLimit() < rsp.getTotalCount()) {
            chargeOrderListReq.setOffset(rsp.getOffset() + rsp.getLimit());
        }
        return size;
    }

    @Test
    public void testQueryCount() throws ParamRequiredException {
        ChargeOrderCountViewReq chargeOrderListReq = new ChargeOrderCountViewReq();
        chargeOrderListReq.setPatientIds(new ArrayList<>());
        chargeOrderListReq.getPatientIds().add("e3e1b3da02e0432b805d437ac4a7cf72");
        chargeOrderListReq.setChainId("19e12c3a4a094e9f8dfbfc176378802a");

        ChargeOrderCountViewRsp rsp = chargeSheetService.getChargeOrderCountByPatientId(chargeOrderListReq);
    }
}