package cn.abcyun.cis.charge.service;

import cn.abcyun.bis.rpc.sdk.cis.message.patientorder.HospitalPatientOrder;
import cn.abcyun.bis.rpc.sdk.cis.message.patientorder.HospitalPatientOrderMessage;
import cn.abcyun.cis.charge.amqp.HAMQConsumer;
import cn.abcyun.cis.charge.amqp.MQConsumer;
import cn.abcyun.cis.charge.amqp.RocketMqConsumer;
import cn.abcyun.cis.charge.amqp.model.ChargePayTransactionAutoCancelMessage;
import cn.abcyun.cis.charge.amqp.model.ChargeRefundTaskStartMessage;
import cn.abcyun.cis.charge.hospital.service.MQHospitalMessageHandleService;
import cn.abcyun.cis.charge.util.DateUtils;
import cn.abcyun.cis.charge.util.JsonUtils;
import cn.abcyun.cis.commons.amqp.message.ChargeMessage;
import cn.abcyun.cis.commons.amqp.message.OrganMessage;
import cn.abcyun.cis.commons.amqp.message.WeChatPayMessage;
import cn.abcyun.cis.core.broadcast.message.BroadcastMessage;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest                 //Spring Boot 单测的注解
@ActiveProfiles("local")
public class MQConsumerTest {

    @Autowired
    private MQConsumer mqConsumer;

    @Autowired
    private HAMQConsumer hamqConsumer;

    @Autowired
    private MQHospitalMessageHandleService mqHospitalMessageHandleService;

    @Autowired
    private MQMessageHandleService mqMessageHandleService;

    @Autowired
    private RocketMqConsumer.ReceiveOrganMessage receiveOrganMessage;


    @Test
    public void testReceiveOrganMessage()  {
        OrganMessage message = JsonUtils.readValue("{\"operatorId\":\"ffffffff00000000346d52a9dd980000\",\"clinicId\":\"ffffffff0000000034f98da862260000\",\"type\":11001,\"newVal\":{\"id\":\"ffffffff0000000034f98da862260000\",\"shortId\":\"3817237913513885696\",\"parentId\":\"ffffffff0000000034f98da862260000\",\"parentShortId\":\"3817237913513885696\",\"nodeType\":1,\"status\":1,\"viewMode\":1,\"hisType\":0,\"shortName\":null,\"shortNamePyFirst\":null,\"name\":\"南宁良庆区韦冬艳诊所\",\"namePy\":\"nanningliangqingquweidongyanzhensuo|naningliangqingquweidongyanzhensuo|nanningliangqingouweidongyanzhensuo\",\"namePyFirst\":\"NNLQQWDYZS|NNLQOWDYZS\",\"addressDetail\":\"\",\"contactPhone\":\"\",\"addressGeo\":\"108.393889,22.753613\",\"addressProvinceId\":\"450000\",\"addressProvinceName\":\"广西壮族自治区\",\"addressCityId\":\"450100\",\"addressCityName\":\"南宁市\",\"addressDistrictId\":\"450108\",\"addressDistrictName\":\"良庆区\",\"busSupportFlag\":1},\"oldVal\":{\"id\":null,\"shortId\":null,\"parentId\":null,\"parentShortId\":null,\"nodeType\":0,\"status\":0,\"viewMode\":0,\"hisType\":0,\"shortName\":null,\"shortNamePyFirst\":null,\"name\":null,\"namePy\":null,\"namePyFirst\":null,\"addressDetail\":null,\"contactPhone\":null,\"addressGeo\":null,\"addressProvinceId\":null,\"addressProvinceName\":null,\"addressCityId\":null,\"addressCityName\":null,\"addressDistrictId\":null,\"addressDistrictName\":null,\"busSupportFlag\":0}}", OrganMessage.class);
        receiveOrganMessage.onMessage(message);
    }

    @Test
    public void testReceiveWeChatPayMessage()  {
        WeChatPayMessage message = JsonUtils.readValue("{\"orderPayMessage\":{\"clinicId\":\"fff730ccc5ee45d783d82a85b8a0e52d\",\"chainId\":\"6a869c22abee4ffbaef3e527bbb70aeb\",\"businessTradeNo\":\"ffffffff000000001a26a920104a8006\",\"outTradeNo\":\"ffffffff000000001a26a9200c578001\",\"transactionId\":\"210926127504891012\",\"totalFee\":5,\"payTime\":\"2021-09-26T10:04:23.110+00:00\",\"extra\":\"{\\\"appid\\\":\\\"00218184\\\",\\\"outtrxid\\\":null,\\\"trxcode\\\":null,\\\"trxid\\\":\\\"210926127504891012\\\",\\\"trxamt\\\":\\\"5\\\",\\\"trxdate\\\":null,\\\"paytime\\\":null,\\\"chnltrxid\\\":\\\"4200001214202109261667502054\\\",\\\"trxstatus\\\":\\\"0000\\\",\\\"cusid\\\":\\\"56165104816R1ZX\\\",\\\"termno\\\":null,\\\"termbatchid\\\":null,\\\"termtraceno\\\":null,\\\"termauthno\\\":null,\\\"termrefnum\\\":null,\\\"trxreserved\\\":null,\\\"srctrxid\\\":null,\\\"cusorderid\\\":\\\"ffffffff000000001a26a9200c578001\\\",\\\"acct\\\":null,\\\"fee\\\":null,\\\"signtype\\\":null,\\\"cmid\\\":null,\\\"chnlid\\\":null,\\\"chnldata\\\":null,\\\"sign\\\":\\\"CF4C985C9B7261A22D4A4EAF79653D07\\\"}\",\"paySubMode\":10},\"orderRefundMessage\":null,\"type\":1,\"payMode\":3,\"orderType\":1,\"source\":1}", WeChatPayMessage.class);
        mqConsumer.receiveWeChatPayMessage(message);
    }

    @Test
    public void testInsertOrUpdateChargeSheetByPatientOrder()  {
        ChargeMessage message = JsonUtils.readValue("{\"patientOrder\":{\"id\":\"ffffffff0000000034e059918227c000\",\"patientName\":\"倾城\",\"patientId\":\"ffffffff000000003490456be01fc000\",\"patientSex\":\"女\",\"patientMobile\":\"\",\"patientNamePy\":\"qingcheng\",\"patientNamePyFirst\":\"QC\",\"patientAge\":null,\"patientBirthday\":\"2000-08-22\",\"isMember\":0,\"no\":\"29046\",\"chainId\":\"6a869c22abee4ffbaef3e527bbb70aeb\",\"clinicId\":\"fff730ccc5ee45d783d82a85b8a0e52d\",\"source\":2,\"sourceClientType\":0,\"reserveTime\":null,\"visitSourceId\":null,\"visitSourceFrom\":null,\"visitSourceName\":null,\"visitSourceFromName\":null,\"visitSourceRemark\":null,\"visitSourceParentId\":null,\"visitSourceParentName\":null,\"revisitStatus\":2,\"hospitalPatientOrderId\":null,\"shebaoCardInfo\":null,\"shebaoChargeType\":1,\"referralFlag\":0,\"referralPatientOrder\":null,\"airPharmacyOrderId\":null,\"type\":0,\"created\":\"2024-11-22T05:50:05Z\"},\"type\":10001,\"operatorId\":\"6e45706922a74966ab51e4ed1e604641\",\"isOutpatientDeleted\":0,\"source\":0,\"businessInfo\":{\"id\":\"ffffffff0000000034e0598fc4444000\",\"hospitalPatientOrderId\":null,\"patientOrderId\":\"ffffffff0000000034e059918227c000\",\"patientId\":\"ffffffff000000003490456be01fc000\",\"clinicId\":\"fff730ccc5ee45d783d82a85b8a0e52d\",\"chainId\":\"6a869c22abee4ffbaef3e527bbb70aeb\",\"departmentId\":\"59140f8ecdeb4553ab570f710274e0ab\",\"doctorId\":\"6e45706922a74966ab51e4ed1e604641\",\"copywriterId\":\"\",\"status\":1,\"subStatus\":0,\"executeStatus\":0,\"outpatientSource\":0,\"isMedicalRecordFilled\":0,\"prescriptionForms\":[],\"productForms\":[{\"id\":\"ffffffff0000000034e05991a4444000\",\"patientOrderId\":\"ffffffff0000000034e059918227c000\",\"outpatientSheetId\":\"ffffffff0000000034e0598fc4444000\",\"patientId\":\"ffffffff000000003490456be01fc000\",\"clinicId\":\"fff730ccc5ee45d783d82a85b8a0e52d\",\"chainId\":\"6a869c22abee4ffbaef3e527bbb70aeb\",\"departmentId\":\"59140f8ecdeb4553ab570f710274e0ab\",\"doctorId\":\"6e45706922a74966ab51e4ed1e604641\",\"sort\":0,\"source\":0,\"totalPrice\":3,\"isTotalPriceChanged\":0,\"productFormItems\":[{\"id\":\"ffffffff0000000034e05991a4444001\",\"patientOrderId\":\"ffffffff0000000034e059918227c000\",\"clinicId\":\"fff730ccc5ee45d783d82a85b8a0e52d\",\"outpatientSheetId\":\"ffffffff0000000034e0598fc4444000\",\"productFormId\":\"ffffffff0000000034e05991a4444000\",\"productId\":\"ffffffff0000000034cb6965f199804a\",\"name\":\"云2\",\"unitCount\":1,\"unit\":\"次\",\"unitPrice\":3,\"currentUnitPrice\":null,\"costUnitPrice\":2,\"days\":null,\"dailyDosage\":null,\"sort\":0,\"type\":3,\"subType\":1,\"composeType\":0,\"composeParentFormItemId\":null,\"expectedTotalPrice\":null,\"expectedTotalPriceRatio\":null,\"expectedUnitPrice\":null,\"sourceUnitPrice\":3,\"fractionPrice\":0,\"formFlatPrice\":null,\"sheetFlatPrice\":null,\"isUnitPriceChanged\":0,\"isTotalPriceChanged\":0,\"totalPrice\":3,\"totalPriceRatio\":1,\"unitAdjustmentFee\":0,\"useDismounting\":0,\"pharmacyType\":0,\"pharmacyNo\":0,\"feeComposeType\":0,\"feeTypeId\":\"20\",\"goodsFeeType\":0,\"keyId\":\"5a82921ca18341deb4994f620e167477\",\"chargeStatus\":0,\"executedUnitCount\":null,\"executeStatus\":0,\"executeStatusName\":null,\"examinationResult\":null,\"productInfo\":{\"goodsVersion\":0,\"sourceFlag\":1,\"id\":\"ffffffff0000000034cb6965f199804a\",\"goodsId\":\"ffffffff0000000034cb6965f199804a\",\"status\":1,\"name\":\"云2\",\"displayName\":\"云2\",\"displaySpec\":\"次\",\"organId\":\"6a869c22abee4ffbaef3e527bbb70aeb\",\"typeId\":20,\"type\":3,\"subType\":1,\"pieceNum\":1,\"pieceUnit\":null,\"packageUnit\":\"次\",\"dismounting\":0,\"medicineCadn\":\"\",\"position\":null,\"chainPackagePrice\":3,\"piecePrice\":null,\"packagePrice\":3,\"packageCostPrice\":2,\"fixedPackagePrice\":3,\"priceType\":1,\"inTaxRat\":0,\"outTaxRat\":0,\"needExecutive\":0,\"hospitalNeedExecutive\":0,\"shortId\":\"300000905086447\",\"composeUseDismounting\":0,\"composeSort\":0,\"disableComposePrint\":0,\"createdUserId\":\"566fdc8b20c64d6d953e9c36991c4830\",\"lastModifiedUserId\":\"566fdc8b20c64d6d953e9c36991c4830\",\"lastModifiedDate\":\"2024-07-18T04:29:37Z\",\"combineType\":0,\"bizExtensions\":{\"bizRefId\":\"ffffffff0000000034cb463091954000\",\"providerId\":1,\"itemCategory\":\"1\"},\"bizRelevantId\":\"3804200052400832512\",\"extendSpec\":\"\",\"deviceInfo\":{\"model\":\"kfyj-111\",\"name\":\"开发用云检仪器\",\"deviceModeId\":\"3804200052400832512\",\"deviceUuid\":\"云检.ABC\",\"manufacture\":\"ABC\",\"iconUrl\":\"https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/oa/lis-device/image/7s2LQtKHAiIwnjlwVcBlHRnxx9RG6e8g_1721188463473.jpg\",\"deviceType\":1,\"deviceTypeName\":\"临床检验\",\"usageType\":1,\"usageTypeName\":\"血液分析\",\"innerFlag\":0},\"deviceType\":1,\"medicalFeeGrade\":0,\"disable\":0,\"chainDisable\":0,\"v2DisableStatus\":0,\"chainV2DisableStatus\":0,\"disableSell\":0,\"isSell\":1,\"customTypeId\":\"0\",\"chainPackageCostPrice\":2,\"chainId\":\"6a869c22abee4ffbaef3e527bbb70aeb\",\"shebao\":{\"goodsId\":\"ffffffff0000000034cb6965f199804a\",\"goodsType\":3,\"isDummy\":0,\"medicineNum\":\"300000905086447\",\"medicalFeeGrade\":0},\"pharmacyType\":0,\"pharmacyNo\":0,\"defaultInOutTax\":0,\"dispenseAveragePackageCostPrice\":2,\"innerFlag\":0,\"deviceInnerFlag\":0,\"feeComposeType\":0,\"feeTypeId\":\"20\",\"usePieceUnitFlag\":0,\"copiedFlag\":0,\"coopFlag\":1,\"cloudSupplierFlag\":1,\"expiredWarnMonths\":30,\"dangerIngredient\":0,\"keyId\":\"5a82921ca18341deb4994f620e167477\",\"isPreciousDevice\":0,\"cMSpec\":\"\"},\"stockPieceCount\":null,\"stockPackageCount\":null,\"refundUnitCount\":null,\"batchInfos\":null,\"frontSourceUnitPrice\":3,\"toothNos\":[],\"extendData\":{\"goodsVersion\":0},\"lockId\":null,\"remark\":\"\",\"freq\":null,\"payType\":null,\"sourceTotalPrice\":3,\"printTotalPrice\":null,\"printUnitPrice\":null,\"doctorId\":null,\"departmentId\":null,\"nurseId\":null,\"purposeOfExamination\":null,\"unitAdjustmentFeeLastModifiedBy\":null}],\"sourceFormType\":2,\"created\":\"2024-11-22T05:50:05.010Z\",\"lastModified\":\"2024-11-22T05:50:05.010Z\",\"keyId\":\"07b2d07ef45243ee93c41b0af29c2849\",\"chargeStatus\":0,\"chargeStatusName\":null,\"expectedTotalPrice\":null,\"chargeSheetId\":null,\"sourceTotalPrice\":3,\"printTotalPrice\":null}],\"prescriptionAttachments\":[],\"reserveDate\":\"2024-11-22T05:50:00Z\",\"isReserved\":0,\"registrationCategory\":0,\"orderNo\":1,\"diagnosedDate\":\"2024-11-22T05:50:04.782Z\",\"historyDiagnoseCount\":14,\"orderByDate\":\"2024-11-22T05:50:00Z\",\"isOnline\":0,\"outpatientFrom\":0,\"type\":0,\"isDraft\":0,\"sceneType\":0,\"totalPrice\":9,\"isTotalPriceChanged\":0,\"adjustmentFee\":0,\"statusLastModified\":\"2024-11-22T05:50:05.020Z\",\"extendData\":{\"patientChronicArchivesId\":null,\"psychotropicNarcoticEmployee\":{\"name\":\"\",\"sex\":\"\",\"age\":{\"year\":null,\"month\":null,\"day\":null},\"idCard\":\"\"}},\"version\":0,\"created\":\"2024-11-22T05:50:05.007Z\",\"lastModified\":\"2024-11-22T05:50:05.007Z\",\"chargeStatus\":0,\"registrationFee\":6,\"registrationSheetId\":\"ffffffff0000000034e0599182870000\",\"registrationFeeStatus\":0,\"registrationSheet\":{\"id\":\"ffffffff0000000034e0599182870000\",\"patientOrderId\":\"ffffffff0000000034e059918227c000\",\"patientId\":\"ffffffff000000003490456be01fc000\",\"clinicId\":\"fff730ccc5ee45d783d82a85b8a0e52d\",\"chainId\":\"6a869c22abee4ffbaef3e527bbb70aeb\",\"chiefComplaint\":\"\",\"presentHistory\":\"\",\"pastHistory\":\"\",\"allergicHistory\":\"\",\"familyHistory\":\"\",\"personalHistory\":\"\",\"physicalExamination\":\"\",\"epidemiologicalHistory\":\"\",\"dentistryExaminations\":null,\"preDiagnosisAttachments\":null,\"registrationForms\":[{\"id\":\"ffffffff0000000034e0599182870001\",\"patientOrderId\":\"ffffffff0000000034e059918227c000\",\"patientId\":\"ffffffff000000003490456be01fc000\",\"clinicId\":\"fff730ccc5ee45d783d82a85b8a0e52d\",\"chainId\":\"6a869c22abee4ffbaef3e527bbb70aeb\",\"registrationSheetId\":\"ffffffff0000000034e0599182870000\",\"registrationFormItems\":[{\"id\":\"ffffffff0000000034e0599182870002\",\"patientOrderId\":\"ffffffff0000000034e059918227c000\",\"registrationSheetId\":\"ffffffff0000000034e0599182870000\",\"registrationFormId\":\"ffffffff0000000034e0599182870001\",\"chainId\":\"6a869c22abee4ffbaef3e527bbb70aeb\",\"clinicId\":\"fff730ccc5ee45d783d82a85b8a0e52d\",\"msgId\":null,\"departmentId\":\"59140f8ecdeb4553ab570f710274e0ab\",\"departmentName\":\"小儿外科诊室\",\"doctorId\":\"6e45706922a74966ab51e4ed1e604641\",\"doctorName\":\"丁柱11\",\"examinationDeviceId\":null,\"examinationDeviceName\":null,\"examinationDeviceRoomId\":null,\"examinationDeviceRoomName\":null,\"patientId\":\"ffffffff000000003490456be01fc000\",\"consultingRoomId\":null,\"consultingRoomName\":null,\"orderNo\":1,\"orderNoStr\":\"下午01\",\"reserveOrderNo\":1,\"orderNoType\":0,\"reserveDate\":\"2024-11-22\",\"reserveStart\":\"13:50\",\"reserveEnd\":\"18:00\",\"scheduleId\":null,\"signIn\":2,\"signInTime\":\"2024-11-22 13:50:04\",\"isReserved\":0,\"type\":2,\"payType\":0,\"payStatus\":0,\"payRestTime\":null,\"expire\":0,\"mustPay\":0,\"code\":null,\"codeStr\":null,\"statusV2\":40,\"payStatusV2\":0,\"isRevisited\":1,\"printCount\":0,\"payRestSecond\":0,\"canBeRefundCode\":0,\"fee\":6,\"costUnitPrice\":1,\"oldReserveInfo\":null,\"created\":\"2024-11-22T05:50:04Z\",\"createdBy\":null,\"createdByName\":null,\"isAdditional\":0,\"feeProductId\":null,\"feeTypeId\":\"5\",\"registrationProducts\":null,\"feeDetails\":[{\"id\":\"3810143766250455043\",\"chainId\":\"6a869c22abee4ffbaef3e527bbb70aeb\",\"clinicId\":\"fff730ccc5ee45d783d82a85b8a0e52d\",\"registrationType\":0,\"registrationFormItemId\":\"ffffffff0000000034e0599182870002\",\"goodsId\":\"ffffffff0000000034d10b2bb2a58000\",\"goodsName\":\"挂号费\",\"goodsSpec\":\"次\",\"unitCount\":1,\"unit\":\"次\",\"unitPrice\":6,\"costPrice\":1,\"goodsType\":19,\"goodsSubType\":0,\"goodsTypeId\":33,\"isDismounting\":0,\"feeComposeType\":0,\"feeTypeId\":\"1003\",\"goodsSnap\":{\"goodsVersion\":0,\"disable\":0,\"packagePrice\":0,\"packageCostPrice\":0,\"typeId\":33,\"customTypeId\":\"0\",\"shebao\":{\"goodsId\":\"ffffffff0000000034d10b2bb2a58000\",\"goodsType\":19,\"payMode\":2,\"isDummy\":0,\"medicineNum\":\"63a31bb40d694deabfc5acc6318a2560\",\"medicalFeeGrade\":0,\"nationalCode\":\"001101000010000-110100001\",\"nationalCodeId\":\"3787993836581634048\",\"shebaoPieceNum\":1,\"shebaoPieceUnit\":\"次\",\"shebaoPackageUnit\":\"次\"},\"medicalFeeGrade\":0,\"priceType\":1,\"isPreciousDevice\":0,\"cmspec\":\"\"}}],\"registrationCategory\":0,\"sourceFee\":6,\"consultantId\":null,\"version\":0,\"orderNoReserveStart\":\"\",\"orderNoReserveEnd\":\"\"}]}]},\"totalFee\":null,\"sourceTotalPrice\":9,\"doctor\":null,\"nationalDoctorCode\":null,\"copywriter\":null,\"patientOrder\":{\"id\":\"ffffffff0000000034e059918227c000\",\"patientName\":\"倾城\",\"patientId\":\"ffffffff000000003490456be01fc000\",\"patientSex\":\"女\",\"patientMobile\":\"\",\"patientNamePy\":\"qingcheng\",\"patientNamePyFirst\":\"QC\",\"patientAge\":{\"year\":24,\"month\":3,\"day\":0},\"patientBirthday\":\"2000-08-22\",\"isMember\":0,\"no\":\"29046\",\"chainId\":\"6a869c22abee4ffbaef3e527bbb70aeb\",\"clinicId\":\"fff730ccc5ee45d783d82a85b8a0e52d\",\"source\":2,\"sourceClientType\":0,\"reserveTime\":null,\"visitSourceId\":null,\"visitSourceFrom\":null,\"visitSourceName\":null,\"visitSourceFromName\":null,\"visitSourceRemark\":null,\"visitSourceParentId\":null,\"visitSourceParentName\":null,\"revisitStatus\":2,\"hospitalPatientOrderId\":null,\"shebaoCardInfo\":null,\"shebaoChargeType\":1,\"referralFlag\":0,\"referralPatientOrder\":null,\"airPharmacyOrderId\":null,\"type\":0,\"patientInfo\":{\"id\":\"ffffffff000000003490456be01fc000\",\"chainId\":null,\"name\":\"倾城\",\"namePy\":\"qingcheng\",\"namePyFirst\":\"QC\",\"mobile\":\"\",\"countryCode\":\"86\",\"sex\":\"女\",\"birthday\":\"2000-08-22\",\"idCard\":\"610101200008226648\",\"isMember\":0,\"age\":{\"year\":24,\"month\":3,\"day\":0},\"address\":{\"addressCityId\":\"\",\"addressCityName\":\"\",\"addressDetail\":\"\",\"addressDistrictId\":\"\",\"addressDistrictName\":\"\",\"addressGeo\":null,\"addressProvinceId\":\"\",\"addressProvinceName\":\"\",\"addressPostcode\":null},\"sn\":\"001582\",\"remark\":\"\",\"profession\":\"\",\"company\":\"\",\"memberInfo\":null,\"patientSource\":null,\"tags\":[{\"tagId\":\"732122144157657\",\"tagName\":\"高血压\",\"genMode\":0,\"viewMode\":1,\"style\":{\"viewMode\":1,\"text\":\"血\",\"color\":\"#6e23ed\",\"shape\":\"rect\",\"iconUrl\":\"//static-common-cdn.abcyun.cn/img/patient-label/v2/money.png\"}},{\"tagId\":\"732122144163051\",\"tagName\":\"骨质疏松\",\"genMode\":1,\"viewMode\":1,\"style\":{\"viewMode\":0,\"text\":\"骨\",\"color\":\"#1489ff\",\"shape\":\"rect\",\"iconUrl\":\"\"}},{\"tagId\":\"732122144163053\",\"tagName\":\"胃肠asdasds炎\",\"genMode\":1,\"viewMode\":1,\"style\":{\"viewMode\":0,\"text\":\"胃\",\"color\":\"#1489ff\",\"shape\":\"rect\",\"iconUrl\":null}},{\"tagId\":\"732122144163054\",\"tagName\":\"便秘\",\"genMode\":0,\"viewMode\":1,\"style\":{\"viewMode\":0,\"text\":\"便\",\"color\":\"#1489ff\",\"shape\":\"rect\",\"iconUrl\":null}},{\"tagId\":\"732122144163055\",\"tagName\":\"鼻咽炎\",\"genMode\":1,\"viewMode\":1,\"style\":{\"viewMode\":0,\"text\":\"鼻\",\"color\":\"#1489ff\",\"shape\":\"polygon\",\"iconUrl\":\"https://static-common-cdn.abcyun.cn/img/patient-label/heart.png\"}},{\"tagId\":\"732122144163056\",\"tagName\":\"慢性软组织损伤\",\"genMode\":1,\"viewMode\":1,\"style\":{\"viewMode\":0,\"text\":\"慢\",\"color\":\"#1489ff\",\"shape\":\"rect\",\"iconUrl\":null}},{\"tagId\":\"732122144163060\",\"tagName\":\"头痛\",\"genMode\":1,\"viewMode\":1,\"style\":{\"viewMode\":0,\"text\":\"头\",\"color\":\"#1489ff\",\"shape\":\"polygon\",\"iconUrl\":\"https://static-common-cdn.abcyun.cn/img/patient-label/heart.png\"}},{\"tagId\":\"732122144163064\",\"tagName\":\"咽喉炎\",\"genMode\":0,\"viewMode\":1,\"style\":{\"viewMode\":1,\"text\":\"咽\",\"color\":\"#ff1111\",\"shape\":\"rect\",\"iconUrl\":\"https://static-common-cdn.abcyun.cn/img/patient-label/v2/active.png\"}},{\"tagId\":\"732122144157658\",\"tagName\":\"哮喘\",\"genMode\":1,\"viewMode\":0,\"style\":{\"viewMode\":1,\"text\":\"哮\",\"color\":\"#1489ff\",\"shape\":\"rect\",\"iconUrl\":\"https://static-common-cdn.abcyun.cn/img/patient-label/v2/cheerful.png\"}},{\"tagId\":\"732122144157673\",\"tagName\":\"鼻炎\",\"genMode\":0,\"viewMode\":0,\"style\":{\"viewMode\":0,\"text\":\"鼻\",\"color\":\"#1489ff\",\"shape\":\"rect\",\"iconUrl\":null}},{\"tagId\":\"732122144163047\",\"tagName\":\"哮喘\",\"genMode\":1,\"viewMode\":0,\"style\":{\"viewMode\":0,\"text\":\"哮\",\"color\":\"#1489ff\",\"shape\":\"rect\",\"iconUrl\":null}},{\"tagId\":\"732122144163048\",\"tagName\":\"支气管炎\",\"genMode\":1,\"viewMode\":0,\"style\":{\"viewMode\":0,\"text\":\"支\",\"color\":\"#1489ff\",\"shape\":\"rect\",\"iconUrl\":\"\"}},{\"tagId\":\"732122144163049\",\"tagName\":\"关节炎\",\"genMode\":1,\"viewMode\":0,\"style\":{\"viewMode\":0,\"text\":\"关\",\"color\":\"#1489ff\",\"shape\":\"rect\",\"iconUrl\":null}},{\"tagId\":\"732122144163050\",\"tagName\":\"颈椎病\",\"genMode\":1,\"viewMode\":0,\"style\":{\"viewMode\":0,\"text\":\"颈\",\"color\":\"#1489ff\",\"shape\":\"rect\",\"iconUrl\":null}},{\"tagId\":\"732122144163052\",\"tagName\":\"胃sadsadsa炎\",\"genMode\":1,\"viewMode\":0,\"style\":{\"viewMode\":0,\"text\":\"胃\",\"color\":\"#1489ff\",\"shape\":\"rect\",\"iconUrl\":null}},{\"tagId\":\"732122144163057\",\"tagName\":\"高血脂\",\"genMode\":0,\"viewMode\":0,\"style\":{\"viewMode\":0,\"text\":\"高\",\"color\":\"#1489ff\",\"shape\":\"rect\",\"iconUrl\":null}},{\"tagId\":\"732122144163058\",\"tagName\":\"糖尿病\",\"genMode\":0,\"viewMode\":0,\"style\":{\"viewMode\":1,\"text\":\"糖\",\"color\":\"#1489ff\",\"shape\":\"rect\",\"iconUrl\":\"https://static-common-cdn.abcyun.cn/img/patient-label/v2/virus.png\"}},{\"tagId\":\"732122144163059\",\"tagName\":\"痛风\",\"genMode\":0,\"viewMode\":0,\"style\":{\"viewMode\":0,\"text\":\"痛\",\"color\":\"#1489ff\",\"shape\":\"rect\",\"iconUrl\":null}},{\"tagId\":\"732122144163061\",\"tagName\":\"偏头痛\",\"genMode\":0,\"viewMode\":0,\"style\":{\"viewMode\":0,\"text\":\"偏\",\"color\":\"#1489ff\",\"shape\":\"rect\",\"iconUrl\":null}},{\"tagId\":\"732122144163062\",\"tagName\":\"鼻炎132\",\"genMode\":0,\"viewMode\":0,\"style\":{\"viewMode\":0,\"text\":\"鼻\",\"color\":\"#1489ff\",\"shape\":\"rect\",\"iconUrl\":null}},{\"tagId\":\"732122144163063\",\"tagName\":\"鼻窦炎\",\"genMode\":1,\"viewMode\":0,\"style\":{\"viewMode\":0,\"text\":\"鼻\",\"color\":\"#1489ff\",\"shape\":\"rect\",\"iconUrl\":null}},{\"tagId\":\"ffffffff00000000347adfe31fde4000\",\"tagName\":\"便秘\",\"genMode\":0,\"viewMode\":0,\"style\":{\"viewMode\":0,\"text\":\"便\",\"color\":\"#1489ff\",\"shape\":\"rect\",\"iconUrl\":null}},{\"tagId\":\"ffffffff00000000348c2aef000d4000\",\"tagName\":\"12\",\"genMode\":0,\"viewMode\":0,\"style\":null}],\"marital\":null,\"weight\":null,\"ethnicity\":\"\",\"isAttention\":null,\"appFlag\":null,\"arrearsFlag\":null,\"externalCodeId\":null,\"externalCodeRemark\":null,\"pastHistory\":\"1\",\"shebaoCardInfo\":null,\"chronicArchivesInfo\":null,\"childCareInfo\":null,\"patientPoints\":null},\"created\":\"2024-11-22T05:50:04.754Z\"},\"medicalRecord\":{\"id\":\"ffffffff0000000034e0598fc4444001\",\"qrid\":null,\"patientOrderId\":\"ffffffff0000000034e059918227c000\",\"outpatientSheetId\":\"ffffffff0000000034e0598fc4444000\",\"patientId\":\"ffffffff000000003490456be01fc000\",\"clinicId\":\"fff730ccc5ee45d783d82a85b8a0e52d\",\"chainId\":\"6a869c22abee4ffbaef3e527bbb70aeb\",\"departmentId\":\"\",\"doctorId\":\"\",\"type\":0,\"chiefComplaint\":\"咳嗽\",\"pastHistory\":null,\"allergicHistory\":null,\"familyHistory\":null,\"personalHistory\":null,\"presentHistory\":null,\"physicalExamination\":null,\"diagnosis\":\"急性上呼吸道感染\",\"extendDiagnosisInfos\":[{\"value\":[{\"code\":\"J06.900\",\"name\":\"急性上呼吸道感染\",\"diseaseType\":null,\"hint\":null}]}],\"doctorAdvice\":null,\"syndrome\":null,\"syndromeTreatment\":null,\"therapy\":null,\"chineseExamination\":null,\"birthHistory\":null,\"oralExamination\":null,\"epidemiologicalHistory\":null,\"obstetricalHistory\":null,\"auxiliaryExaminations\":null,\"dentistryExtend\":{},\"extendData\":{},\"wearGlassesHistory\":null,\"eyeExamination\":null,\"target\":null,\"prognosis\":null,\"symptomTime\":null,\"attachments\":[],\"diagnosisInfos\":[{\"code\":\"J06.900\",\"name\":\"急性上呼吸道感染\",\"diseaseType\":null,\"hint\":null}],\"auxiliaryExamination\":null,\"chinesePrescription\":null},\"examSheetStatusItems\":null,\"consultantId\":null,\"firstChargedTime\":null,\"questionSheets\":[],\"prescriptionChineseForms\":[],\"prescriptionInfusionForms\":[],\"prescriptionWesternForms\":[],\"prescriptionExternalForms\":[],\"needUpdateOtherRecords\":true,\"prescriptionChineseFormDeliveryList\":[]}}", ChargeMessage.class);
        hamqConsumer.receive(message);
    }


    @Test
    public void testHospitalOrderCreatedMessage()  {
        HospitalPatientOrderMessage patientOrderCreatedMessage = new HospitalPatientOrderMessage();
        HospitalPatientOrder hospitalPatientOrder = new HospitalPatientOrder();
        hospitalPatientOrder.setId("2288059199188910081");
        hospitalPatientOrder.setPatientId("ffffffff0000000014974b680ca02000");
        hospitalPatientOrder.setChainId("6a869c22abee4ffbaef3e527bbb70aeb");
        hospitalPatientOrder.setClinicId("fff730ccc5ee45d783d82a85b8a0e52d");
        patientOrderCreatedMessage.setHospitalPatientOrder(hospitalPatientOrder);


        mqHospitalMessageHandleService.handleHospitalPatientOrderCreatedMessage(patientOrderCreatedMessage, "6e45706922a74966ab51e4ed1e604641");
    }

    @Test
    public void testReceiveBroadcastMessage()  {
        String str = "{\"type\":20006,\"sender\":\"abc-cis-charge-service\",\"body\":{\"type\":3,\"chainId\":\"6a869c22abee4ffbaef3e527bbb70aeb\",\"clinicId\":\"fff730ccc5ee45d783d82a85b8a0e52d\",\"hospitalOrderId\":\"2288059199188910081\",\"chargeSheetId\":\"ffffffff0000000020150768113ee000\",\"chargeTransactionId\":\"ffffffff0000000020150768113ee004\",\"amount\":100.0,\"status\":2},\"operatorId\":\"6e45706922a74966ab51e4ed1e604641\"}";
        BroadcastMessage message = JsonUtils.readValue(str, BroadcastMessage.class);
        mqConsumer.receiveBroadcastMessage(message);
    }


    @Test
    public void testHandleChargeRefundTaskStartMessage()  {
        String str = "{\"id\":\"3789599045788188672\",\"taskItemId\":\"3789599045788188673\",\"chainId\":\"6a869c22abee4ffbaef3e527bbb70aeb\",\"clinicId\":\"fff730ccc5ee45d783d82a85b8a0e52d\",\"chargeSheetId\":\"ffffffff00000000349737e065588000\",\"amount\":75.0000,\"status\":0,\"operatorId\":\"00000000000000000000000000000000\"}";
        ChargeRefundTaskStartMessage message = JsonUtils.readValue(str, ChargeRefundTaskStartMessage.class);
        mqMessageHandleService.handleChargeRefundTaskStartMessage(message);
    }


    @Test
    public void testHandleChargePayTransactionAutoCancelMessage()  {
        ChargePayTransactionAutoCancelMessage message = new ChargePayTransactionAutoCancelMessage();
        message.setChargeSheetId("ffffffff0000000034f2a58828ec800a")
                .setExpireTime(DateUtils.parseToDate("2025-03-13 15:25:37", "yyyy-MM-dd HH:mm:ss").toInstant())
                .setChargePayTransactionId("ffffffff0000000034f2a708a8ec8002")
                .setOperatorId("1")
                .setIsReleaseSheet(0);

        mqMessageHandleService.handleChargePayTransactionAutoCancelMessage(message);
    }


}
