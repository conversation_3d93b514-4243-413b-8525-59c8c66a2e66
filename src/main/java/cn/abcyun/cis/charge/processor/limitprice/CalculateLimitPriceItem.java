package cn.abcyun.cis.charge.processor.limitprice;

import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.ShebaoPayLimitPriceInfo;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.cis.commons.util.ListUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.http.util.Asserts;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class CalculateLimitPriceItem extends BaseLimitPriceInfo {

    private String id;

    @Deprecated
    private BigDecimal receivablePrice;

    private String productId;

    private int productType;

    private int productSubType;

    private int useDismounting;

    private BigDecimal unitCount;

    private BigDecimal doseCount;

    private int isCompose;

    private int isFeeParent;

    /**
     * goods本身的拆零价格
     */
    private BigDecimal goodsPiecePrice;

    /**
     * goods本身的打包价格
     */
    private BigDecimal goodsPackagePrice;

    /**
     * goods本身的成本价
     */
    private BigDecimal unitCostPrice;

    /**
     * 限价的来源方式
     * {@link LimitPriceFromWay}
     */
    private int limitPriceFromWay;

    private List<CalculateLimitPriceItem> composeChildren;

    private List<CalculateLimitPriceItemBatchInfo> batchInfos;

    private boolean canPayByHealthCard;


    @Override
    public BigDecimal getTotalCount() {
        return MathUtils.wrapBigDecimalMultiply(unitCount, doseCount);
    }

    public void setSheBaoReceivablePrice(BigDecimal sheBaoReceivablePrice) {

        if (!getCanPayByHealthCard()) {
            super.setSheBaoReceivablePrice(BigDecimal.ZERO);
        } else {
            super.setSheBaoReceivablePrice(sheBaoReceivablePrice);
        }

    }

    public static class LimitPriceFromWay {
        public static final int ITEM = 0;

        public static final int BATCH = 1;
    }

    @Data
    @Accessors(chain = true)
    @EqualsAndHashCode(callSuper = true)
    public static class CalculateLimitPriceItemBatchInfo extends BaseLimitPriceInfo {

        private String id;

        private String stockId;

        private String batchId;

        private BigDecimal unitCostPrice;


        private BigDecimal totalCount;




        @Override
        public void markIsLimited(BigDecimal limitUnitPrice, int exceedLimitPriceRule, BaseLimitPriceInfo.LimitInfo limitInfo) {
            setLimited(true);
            setLimitUnitPrice(limitUnitPrice);
            setExceedLimitPriceRule(exceedLimitPriceRule);
            setLimitTotalPrice(limitInfo.getLimitTotalPrice());
            setLimitInfo(limitInfo);
        }

        @Override
        public BigDecimal getTotalCount() {
            return totalCount;
        }

        /**
         * 计算社保应收
         *
         * @return
         */
        public BigDecimal calculateShebaoReceivableTotalPrice() {

            if (isLimited()) {
                return getLimitTotalPrice();
            } else {
                return getReceivableTotalPrice();
            }
        }

//        public BigDecimal calculateShebaoReceivableTotalPrice(CalculateLimitPriceItem calculateLimitPriceItem) {
//            if(!calculateLimitPriceItem.canPayByHealthCard()) {
//                return BigDecimal.ZERO;
//            }
//
//            if (isLimited()) {
//                return getLimitTotalPrice();
//            } else {
//                return getReceivableTotalPrice();
//            }
//        }

        public BigDecimal calculateReceivableTotalPrice() {
            if (!isLimited()) {
                return getReceivableTotalPrice();
            }

            if (getExceedLimitPriceRule() == Constants.ExceedLimitPriceRule.NO_PAY) {
                //限价不转自费
                return getLimitTotalPrice();
            } else {
                //限价转自费
                return getReceivableTotalPrice();
            }
        }

        public boolean hasGoodsLimitInfo() {
            return Objects.nonNull(getGoodsLimitPriceInfo());
        }

        public void resetReceivablePrice(CalculateLimitPriceItemBatchInfo newLimitPriceItemBatchInfo) {
            Asserts.notNull(newLimitPriceItemBatchInfo, "newLimitPriceItemBatchInfo can not be null");

            setReceivableTotalPrice(newLimitPriceItemBatchInfo.getReceivableTotalPrice());
            setReceivableUnitPrice(newLimitPriceItemBatchInfo.getReceivableUnitPrice());
            super.reset();
        }

        public void reCalculateLimitPrice() {
            if (Objects.nonNull(getUpperLimitUnitPrice())
                    && MathUtils.wrapBigDecimalCompare(getReceivableUnitPrice(), getUpperLimitUnitPrice()) > 0
                    && Objects.nonNull(getLimitInfo())) {
                markIsLimited(getUpperLimitUnitPrice(), getLimitInfo().getExceedLimitPriceRule(), getLimitInfo());
                setSheBaoReceivablePrice(getLimitTotalPrice());
                getLimitInfo().calculateLimitFee(getReceivableTotalPrice());
            }
        }
    }

    /**
     * 标记item为限价
     *
     * @param limitUnitPrice
     * @param exceedLimitPriceRule
     */
    @Override
    public void markIsLimited(BigDecimal limitUnitPrice, int exceedLimitPriceRule, BaseLimitPriceInfo.LimitInfo limitInfo) {
        setLimited(true);
        setLimitUnitPrice(limitUnitPrice);
        setExceedLimitPriceRule(exceedLimitPriceRule);
        setLimitPriceFromWay(LimitPriceFromWay.ITEM);
        setLimitTotalPrice(limitInfo.getLimitTotalPrice());
        setLimitInfo(limitInfo);
    }

    /**
     * 计算社保应收
     * <p>
     * 如果项目不能刷社保则社保应收等于0 否则如果被限价则为限价价格没有被限价则为应收价格
     * 如果项目含有子项则社保应收应从子项汇总
     *
     * @return
     */
    public BigDecimal calculateShebaoReceivableTotalPrice() {

        if (!getCanPayByHealthCard()) {
            return BigDecimal.ZERO;
        }

        if (isLimited()) {
            return getLimitTotalPrice();
        } else {
            return getReceivableTotalPrice();
        }
    }

    /**
     * 计算应收价格，由于有多个批次存在，每个批次限价的方式不一样，比如批次1超限价不转自费，批次2超限价转自费，所有这个item的应收需要根据批次的应收来计算
     *
     * @return
     */
    public BigDecimal calculateReceivableTotalPrice() {

        if (!isLimited()) {
            return getReceivableTotalPrice();
        }

        if (getLimitPriceFromWay() == LimitPriceFromWay.ITEM) {
            if (getExceedLimitPriceRule() == Constants.ExceedLimitPriceRule.NO_PAY) {
                //限价不转自费
                return getLimitTotalPrice();
            } else {
                //限价转自费
                return getReceivableTotalPrice();
            }
        }

        if (getLimitPriceFromWay() == LimitPriceFromWay.BATCH) {

            return Optional.ofNullable(getBatchInfos())
                    .orElse(new ArrayList<>())
                    .stream()
                    .map(CalculateLimitPriceItemBatchInfo::calculateReceivableTotalPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

        }
        return getReceivableTotalPrice();
    }

    /**
     * 子项或者批次存在限价失败的标识 则母项返回false
     * @return
     */
    public boolean hasGoodsLimitInfo() {

        if (CollectionUtils.isEmpty(composeChildren)) {

            if (CollectionUtils.isEmpty(getBatchInfos())) {
                return Objects.nonNull(getGoodsLimitPriceInfo());
            } else {
                return Objects.nonNull(getGoodsLimitPriceInfo())
                        && (getGoodsLimitPriceInfo().getType() != ShebaoPayLimitPriceInfo.LimitInfoType.TYPES || getBatchInfos().stream().anyMatch(batchInfo -> Objects.nonNull(batchInfo.getGoodsLimitPriceInfo())));
            }

        } else {
            return composeChildren.stream().filter(Objects::nonNull).anyMatch(CalculateLimitPriceItem::hasGoodsLimitInfo);
        }
    }

    /**
     * 应收金额发生变化，如果发生了变化，表示限价触发，并且直接改了应收金额
     *
     * @return
     */
    public boolean receivableTotalPriceChanged() {

        if (!isLimited()) {
            return false;
        }

        return MathUtils.wrapBigDecimalCompare(getReceivableTotalPrice(), calculateReceivableTotalPrice()) != 0;
    }


    public boolean getCanPayByHealthCard() {

        if (!CollectionUtils.isEmpty(composeChildren)) {
            return composeChildren.stream().filter(Objects::nonNull).anyMatch(CalculateLimitPriceItem::getCanPayByHealthCard);
        }

        return canPayByHealthCard;
    }


    public void initShebaoReceivablePrice() {
        if (!getCanPayByHealthCard()) {
            return;
        }

        if (!CollectionUtils.isEmpty(this.getComposeChildren())) {
            this.getComposeChildren().forEach(CalculateLimitPriceItem::initShebaoReceivablePrice);

            this.setSheBaoReceivablePrice(this.getComposeChildren().stream().map(CalculateLimitPriceItem::getSheBaoReceivablePrice).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));

        } else {
            this.setSheBaoReceivablePrice(MathUtils.wrapBigDecimalOrZero(this.getReceivableTotalPrice()));

            if (!CollectionUtils.isEmpty(this.getBatchInfos())) {
                this.getBatchInfos().forEach(batchInfo -> batchInfo.setSheBaoReceivablePrice(batchInfo.getReceivableTotalPrice()));
            }
        }
    }

    public void resetReceivablePrice(CalculateLimitPriceItem newLimitPriceItem) {
        Asserts.notNull(newLimitPriceItem, "newLimitPriceItem cannot be null");

        setReceivableTotalPrice(newLimitPriceItem.getReceivableTotalPrice());
        setReceivableUnitPrice(newLimitPriceItem.getReceivableUnitPrice());
        super.reset();

        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(composeChildren)) {
            Map<String, CalculateLimitPriceItem> newLimitPriceItemIdMap = ListUtils.toMap(Optional.ofNullable(newLimitPriceItem.getComposeChildren()).orElse(new ArrayList<>()), CalculateLimitPriceItem::getId);
            composeChildren.forEach(child -> child.resetReceivablePrice(newLimitPriceItemIdMap.get(child.getId())));
        }

        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(batchInfos)) {
            Map<String, CalculateLimitPriceItemBatchInfo> newLimitPriceItemBatchInfoIdMap = ListUtils.toMap(Optional.ofNullable(newLimitPriceItem.getBatchInfos()).orElse(new ArrayList<>()), CalculateLimitPriceItemBatchInfo::getId);
            batchInfos.forEach(childBatchInfo -> childBatchInfo.resetReceivablePrice(newLimitPriceItemBatchInfoIdMap.get(childBatchInfo.getId())));
        }
    }
}
