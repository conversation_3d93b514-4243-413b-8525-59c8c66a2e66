package cn.abcyun.cis.charge.processor;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.*;
import cn.abcyun.cis.charge.model.ChargeFormItem;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.service.BatchExtractChargeFormItemDto;
import cn.abcyun.cis.commons.util.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 药店批量提单操作类
 */
@Slf4j
public class PharmacySheetBatchExtractProcessor extends PharmacySheetProcessor {

    public PharmacySheetBatchExtractProcessor(ChargeSheet chargeSheet) {
        super(chargeSheet);
    }

    protected Map<String, BatchExtractChargeFormItemDto> chargeFormItemIdDtoMap;

    public void build(Map<String, BatchExtractChargeFormItemDto> chargeFormItemIdDtoMap) {
        super.build();
        this.chargeFormItemIdDtoMap = Optional.ofNullable(chargeFormItemIdDtoMap).orElse(new HashMap<>());
    }

    /**
     * 重写构造
     *
     * @param isCopyByOutpatient
     * @return
     */
    @Override
    public List<QueryGoodsInPharmacyByIdsAndStockCountReq.QueryPharmacyGoodsReq> generateQueryPharmacyGoodsReqs(boolean isCopyByOutpatient) {
        List<QueryGoodsInPharmacyByIdsAndStockCountReq.QueryPharmacyGoodsReq> queryPharmacyGoodsReqs = super.generateQueryPharmacyGoodsReqs(isCopyByOutpatient);

        if (CollectionUtils.isEmpty(queryPharmacyGoodsReqs)) {
            return queryPharmacyGoodsReqs;
        }

        //修改req的批次信息及lockId
        queryPharmacyGoodsReqs.stream()
                .flatMap(queryPharmacyGoodsReq -> ListUtils.alwaysList(queryPharmacyGoodsReq.getList()).stream())
                .forEach(queryGoodsWithStock -> {
                    String chargeFormItemId = queryGoodsWithStock.getKeyId();
                    BatchExtractChargeFormItemDto batchExtractChargeFormItemDto = chargeFormItemIdDtoMap.get(chargeFormItemId);
                    if (batchExtractChargeFormItemDto == null) {
                        return;
                    }
                    List<ChargeFormItem> draftChargeFormItems = batchExtractChargeFormItemDto.getDraftChargeFormItems();

                    if (CollectionUtils.isEmpty(draftChargeFormItems)) {
                        return;
                    }

                    //这里只能顺位去匹配
                    List<Long> lockIds = draftChargeFormItems.stream()
                            .map(ChargeFormItem::getLockId)
                            .filter(StringUtils::isNotBlank)
                            .map(Long::parseLong)
                            .collect(Collectors.toList());

                    queryGoodsWithStock.setLockIds(lockIds);

                });

        return queryPharmacyGoodsReqs;
    }
}
