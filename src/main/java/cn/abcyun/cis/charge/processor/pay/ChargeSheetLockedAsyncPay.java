package cn.abcyun.cis.charge.processor.pay;

import cn.abcyun.cis.charge.amqp.RocketMqProducer;
import cn.abcyun.cis.charge.base.ChargeServiceError;
import cn.abcyun.cis.charge.base.ChargeServiceException;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.base.ProductInfoChangedException;
import cn.abcyun.cis.charge.base.pay.CombinedPayItem;
import cn.abcyun.cis.charge.model.ChargeForm;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.model.SheetPromotionInfo;
import cn.abcyun.cis.charge.processor.PayInfo;
import cn.abcyun.cis.charge.repository.ChargePayTransactionRepository;
import cn.abcyun.cis.charge.service.ChargeService;
import cn.abcyun.cis.charge.service.ChargeTransactionRecordService;
import cn.abcyun.cis.charge.service.dto.PayChargeSheetInfo;
import cn.abcyun.cis.charge.service.dto.PromotionView;
import cn.abcyun.cis.charge.service.rpc.CisPatientOrderService;
import cn.abcyun.cis.charge.util.ChargeFormUtils;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrder;
import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.common.log.marker.AbcLogMarker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 已锁单异步支付收费单
 */
@Slf4j
public class ChargeSheetLockedAsyncPay extends BaseChargeSheetPay {

    protected ChargeService chargeService;
    protected PatientOrder patientOrder;
    protected ChargeTransactionRecordService chargeTransactionRecordService;
    protected RocketMqProducer rocketMqProducer;

    public ChargeSheetLockedAsyncPay(ChargeService chargeService,
                                     ChargeSheet chargeSheet,
                                     PayChargeSheetInfo payChargeSheetInfo,
                                     PatientOrder patientOrder,
                                     ChargeTransactionRecordService chargeTransactionRecordService,
                                     int paySource,
                                     CombinedPayItem payItem,
                                     RocketMqProducer rocketMqProducer,
                                     CisPatientOrderService cisPatientOrderService,
                                     ChargePayTransactionRepository chargePayTransactionRepository,
                                     String operatorId) {
        super(chargeSheet, payChargeSheetInfo, payItem, paySource, cisPatientOrderService, chargePayTransactionRepository, operatorId);
        this.chargeService = chargeService;
        this.patientOrder = patientOrder;
        this.chargeTransactionRecordService = chargeTransactionRecordService;
        this.rocketMqProducer = rocketMqProducer;
    }

    @Override
    protected void buildChargeSheet() {
    }

    @Override
    protected void saveChargeSheet() {
        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "saveChargeSheet, chargeSheetId: {}", chargeSheet.getId());
        List<PromotionView> availablePromotionViews = sheetProcessor.getAvailablePromotionViews();

        if (!CollectionUtils.isEmpty(availablePromotionViews)) {
            SheetPromotionInfo sheetPromotionInfo = new SheetPromotionInfo();
            sheetPromotionInfo.setPromotions(availablePromotionViews);
            chargeSheet.setPromotionInfo(sheetPromotionInfo);
            chargeSheet.setPromotionInfoJson(JsonUtils.dump(sheetPromotionInfo));
        }

    }

    @Override
    protected void doPay() throws ProductInfoChangedException {
        lockPay();
    }

    protected void lockPay() throws ProductInfoChangedException {
        sheetProcessor = createSheetProcessor(chargeService, patientOrder, chargeTransactionRecordService, payItem);
        PayInfo payInfo = createPayInfo();
        payResult = sheetProcessor.lockPay(payInfo, payChargeSheetInfo.getPayType() == PayChargeSheetInfo.PayType.PAY_FOR_EXISTED_PARTCHARGE_SHEET);
        chargeSheet = sheetProcessor.generateToSaveChargeSheet();
    }

    @Override
    protected void checkCanPaidAndThrowException() {

        int serverLockStatus = chargeSheet.getLockStatus();

        Integer clientLockStatus = payChargeSheetInfo.getLockStatus();

        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "校验是否可以支付, serverLockStatus: {}, clientLockStatus: {}", serverLockStatus, clientLockStatus);

        if (clientLockStatus == null) {
            return;
        }

        if (clientLockStatus == serverLockStatus) {
            return;
        }

//        if (paySource == Constants.ChargeSource.WE_CLINIC) {
//            if (serverLockStatus == Constants.ChargeSheetLockStatus.LOCK_STATUS_PATIENT_DEVICE) {
//                log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "患者在自助服务机锁单了，不能在微诊所上支付");
//                throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_IS_LOCK_BY_DEVICE);
//            }
//            if (serverLockStatus == Constants.ChargeSheetLockStatus.LOCK_STATUS_PATIENT_WEAPP) {
//                log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "患者在小程序锁单了，不能在自助服务机支付");
//                throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_IS_LOCK_BY_WEAPP);
//            }
//        }else if (paySource == Constants.ChargeSource.DEVICE) {
//            if (serverLockStatus == Constants.ChargeSheetLockStatus.LOCK_STATUS_PATIENT_WECLINIC) {
//                log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "患者在微诊所锁单了，不能在设备机上支付");
//                throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_IS_LOCK_BY_WECLINIC);
//            }
//            if (serverLockStatus == Constants.ChargeSheetLockStatus.LOCK_STATUS_PATIENT_WEAPP) {
//                log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "患者在小程序锁单了，不能在微诊所上支付");
//                throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_IS_LOCK_BY_WEAPP);
//            }
//        } else if (paySource == Constants.ChargeSource.WE_APP) {
//            if (serverLockStatus == Constants.ChargeSheetLockStatus.LOCK_STATUS_PATIENT_WECLINIC) {
//                throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_IS_LOCK_BY_WECLINIC);
//            }
//            if (serverLockStatus == Constants.ChargeSheetLockStatus.LOCK_STATUS_PATIENT_DEVICE) {
//                throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_IS_LOCK_BY_DEVICE);
//            }
//        } else if (serverLockStatus == Constants.ChargeSheetLockStatus.LOCK_STATUS_CHARGE_STATION && Constants.ChargeSource.patientPaySources().contains(paySource)) {
//            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "收费单在收费台锁单了，不能支付");
//            throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_IS_LOCK_BY_CHARGE);
//        }


        for (ChargeForm chargeForm : chargeSheet.getChargeForms()) {

            if (chargeForm.getIsDeleted() != 0) {
                continue;
            }

            if (chargeForm.getSourceFormType() != Constants.SourceFormType.AIR_PHARMACY) {
                continue;
            }
            //校验空中药房的地址是否选择
            ChargeFormUtils.checkAirPharmacyLogisticsAndThrowException(chargeForm);
        }

    }


}
