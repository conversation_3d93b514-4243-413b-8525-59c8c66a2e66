package cn.abcyun.cis.charge.processor.provider;

import cn.abcyun.cis.charge.model.ChargeMedicareLimitPriceProduct;
import cn.abcyun.cis.charge.model.ChargeMedicareLimitPriceType;

import java.util.List;

/**
 * 医保限价接口
 */
public interface LimitPriceProvider {

    List<ChargeMedicareLimitPriceProduct> getLimitPriceProducts(String chainId, String clinicId);


    List<ChargeMedicareLimitPriceType> getLimitPriceTypes(String chainId, String clinicId);
}
