package cn.abcyun.cis.charge.processor.discount;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsItem;
import lombok.Data;

import java.util.Objects;

@Data
public class GiftGoodsView{

    private String goodsId;

    private GoodsItem goodsItem;
    //单次满减送额count
    private int unitCount;
    //一共赠送的代金券的张数
    private int count;

    public String getGoodsId() {
        if (Objects.isNull(goodsItem)) {
            return null;
        }

        return goodsItem.getId();
    }
}
