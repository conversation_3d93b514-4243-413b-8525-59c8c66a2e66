package cn.abcyun.cis.charge.processor.discount;

import cn.abcyun.bis.rpc.sdk.cis.model.promotion.Promotion;
import cn.abcyun.cis.charge.service.dto.SinglePromotionView;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
public class PromotionSimple {

    private String promotionParentId;

    private String promotionId;

    private String promotionName;

    private String promotionRuleName;

    @Deprecated
    private List<String> couponIds;

    /**
     * 营销父类的类型
     */
    private Integer parentType;

    /**
     * {@link Type}
     */
    private Integer type;

    /**
     * {@link SubType}
     */
    private Integer subType;

    /**
     * 0折扣 1特价 2满赠
     * {@link Promotion.PromotionDetail.DiscountWay}
     */
    private Integer discountWay;

    /**
     * 优惠方式
     * {@link Promotion.PromotionDetail.RuleType}
     */
    private Integer ruleType;

    /**
     * 是否为特价
     */
    private int isFixedPrice;

    //负数
    private BigDecimal discountPrice;

    /**
     * 本次抵扣的数量
     */
    private int deductedCount;
    /**
     * 赠送表的id
     */
    private String presentId;

    /**
     * 可用的抵扣总次数
     */
    private Integer availableDeductTotalCount;

    /**
     * 使用次数 0 无限次 1 固定次
     */
    private int isGivingLimit;

    private List<ItemDeductedDetail.BatchInfoDeductedDetail> batchInfos;

    public static class SubType extends Promotion.PromotionDetail.PromotionGoods.DiscountType {
        /**
         * 抵扣
         */
        public static final int DISCOUNT_FOR_DEDUCT = 3;
    }

    public static class Type extends Promotion.Type {

        /**
         * 核销抵扣
         */
        public static final int VERIFY_DEDUCT = 996;
        /**
         * 手动标记为赠品的优惠类型
         */
        public static final int MARKED_GIFT_DISCOUNT = 997;

        /**
         * 满减满赠的赠品优惠
         */
        public static final int GIFT_RULE_GIFT_DISCOUNT = 998;

        //患者积分抵扣
        public static final int PATIENT_POINT_DEDUCT = 999;
    }

    public static PromotionSimple ofPromotionSimpleFromSinglePromotionView(SinglePromotionView singlePromotionView) {
        PromotionSimple promotionSimple = new PromotionSimple();
        promotionSimple.setPromotionId(singlePromotionView.getId());
        promotionSimple.setPromotionName(singlePromotionView.getName());
        promotionSimple.setPromotionParentId(singlePromotionView.getParentId());
//        promotionSimple.setPromotionRuleName(singlePromotionView.getRuleName());
        promotionSimple.setType(singlePromotionView.getType());
        promotionSimple.setParentType(singlePromotionView.getParentType());
        promotionSimple.setSubType(singlePromotionView.getPromotionGoods().getDiscountType());
        promotionSimple.setIsFixedPrice(singlePromotionView.getPromotionGoods().getIsFixedPrice());
        promotionSimple.setDiscountWay(singlePromotionView.getPromotionGoods().getDiscountWay());
        promotionSimple.setRuleType(singlePromotionView.getPromotionGoods().getRuleType());
        promotionSimple.setDiscountPrice(singlePromotionView.getDiscountPrice());
        return promotionSimple;
    }

}
