package cn.abcyun.cis.charge.processor;


import cn.abcyun.bis.rpc.sdk.bis.model.order.CreateOrderView;
import cn.abcyun.bis.rpc.sdk.cis.model.charge.GoodsFeeType;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.cis.charge.base.ChargeVersionConstants;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.helper.ChargeTransactionCell;
import cn.abcyun.cis.charge.helper.ChargeTransactionRecordCell;
import cn.abcyun.cis.charge.helper.ChargeTransactionRecordDiscountInfoHelper;
import cn.abcyun.cis.charge.helper.PresentPrincipalAmountFlatTool;
import cn.abcyun.cis.charge.model.*;
import cn.abcyun.cis.charge.processor.stat.StatRecordBatchInfoCalculateCell;
import cn.abcyun.cis.charge.processor.stat.StatRecordByChooseCalculateCell;
import cn.abcyun.cis.charge.processor.stat.StatRecordByChooseCalculateHelper;
import cn.abcyun.cis.charge.processor.stat.StatRecordResult;
import cn.abcyun.cis.charge.service.ChargeTransactionRecordService;
import cn.abcyun.cis.charge.util.AbcIdUtils;
import cn.abcyun.cis.charge.util.FillUtils;
import cn.abcyun.cis.charge.util.ListUtils;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.cis.commons.model.ComposeType;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.common.log.marker.AbcLogMarker;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class StatRecordProcessor {
    private static final Logger sLogger = LoggerFactory.getLogger(StatRecordProcessor.class);


    private String chargeSheetId;
    private String operatorId;

    private int chargeVersion;
    private int payType;
    /**
     * 退费平摊策略
     * {@link RefundFlatType}
     */
    private int refundFlatType;
    /**
     * 对于退费记录charge_transaction_record的处理方式，默认为0，表示使用新的退费记录方式，选择多少数量就记录多少数量，数量不会有小数，单独标识待退金额，1表示根据退的金额反算数量，数量会有小数
     */
    private int transactionRecordHandleMode;

    private boolean isHospital;

    private List<ChargeTransaction> chargeTransactions;
    private List<ChargeFormItem> chargeFormItems;
    private List<ChargeForm> airPharmacyChargeForms;
    private List<ChargeFormItem> refundedChargeFormItems;
    private List<ChargeForm> airPharmacyRefundedChargeForms;

    /**
     * 费用母项
     */
    private List<ChargeFormItem> feeParentChargeFormItems;

    private List<ChargeFormItem> feeRefundedParentChargeFormItems;

    /**
     * 套餐母项
     */
    private List<ChargeFormItem> composeParentChargeFormItems;

    private List<StatRecordAffectedDeductedItem> deductedItems;
    private Map<String, List<ChargeFormItem>> composeChildrenChargeFormItemsMap;

    private ChargeTransactionRecordService chargeTransactionRecordService;

    private List<ChargeTransactionRecord> toSaveChargeTransactionRecords = new ArrayList<>();

    private void addToSaveChargeTransactionRecords(List<ChargeTransactionRecord> toAddRecords) {
        if (toSaveChargeTransactionRecords == null) {
            toSaveChargeTransactionRecords = new ArrayList<>();
        }

        if (CollectionUtils.isEmpty(toAddRecords)) {
            return;
        }

        List<String> toSaveRecordIds = toSaveChargeTransactionRecords.stream()
                .map(ChargeTransactionRecord::getId)
                .collect(Collectors.toList());

        toAddRecords
                .stream()
                .filter(addedToSaveRecord -> !toSaveRecordIds.contains(addedToSaveRecord.getId()))
                .forEach(toSaveChargeTransactionRecords::add);
    }

    public StatRecordProcessor(String chargeSheetId, int chargeVersion, String operatorId) {
        this.chargeSheetId = chargeSheetId;
        this.chargeVersion = chargeVersion;
        this.operatorId = operatorId;
    }

    public void setChargeTransactionRecordService(ChargeTransactionRecordService chargeTransactionRecordService) {
        this.chargeTransactionRecordService = chargeTransactionRecordService;
    }

    public void setChargeInfo(int payType,
                              int transactionRecordHandleMode,
                              int refundFlatType,
                              boolean isHospital,
                              List<ChargeTransaction> chargeTransactions,
                              List<ChargeFormItem> chargeFormItems,
                              List<ChargeForm> airPharmacyChargeForms,
                              List<StatRecordAffectedDeductedItem> deductedItems,
                              List<ChargeFormItem> refundedChargeFormItems,
                              List<ChargeForm> airPharmacyRefundedChargeForms,
                              List<ChargeFormItem> parentChargeFormItems,
                              Map<String, List<ChargeFormItem>> composeChildrenChargeFormItemsMap) {
        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "setChargeInfo payType:{}, transactionRecordHandleMode: {}, refundFlatType: {}", payType, transactionRecordHandleMode, refundFlatType);
        this.payType = payType;
        this.transactionRecordHandleMode = transactionRecordHandleMode;
        this.refundFlatType = refundFlatType;
        this.isHospital = isHospital;
        this.chargeFormItems = Optional.ofNullable(chargeFormItems).orElse(new ArrayList<>())
                .stream()
                .filter(chargeFormItem -> chargeFormItem.getSourceItemType() != Constants.SourceItemType.SELF_PROVIDED)
                .collect(Collectors.toList());
        this.airPharmacyChargeForms = airPharmacyChargeForms;
        this.refundedChargeFormItems = Optional.ofNullable(refundedChargeFormItems).orElse(new ArrayList<>())
                .stream()
                .filter(chargeFormItem -> chargeFormItem.getSourceItemType() != Constants.SourceItemType.SELF_PROVIDED)
                .collect(Collectors.toList());
        this.feeRefundedParentChargeFormItems = Optional.ofNullable(refundedChargeFormItems).orElse(new ArrayList<>())
                .stream()
                .filter(chargeFormItem -> chargeFormItem.getSourceItemType() != Constants.SourceItemType.SELF_PROVIDED)
                .filter(chargeFormItem -> chargeFormItem.getGoodsFeeType() == GoodsFeeType.FEE_PARENT)
                .collect(Collectors.toList());
        this.airPharmacyRefundedChargeForms = airPharmacyRefundedChargeForms;
        this.chargeTransactions = chargeTransactions;
        this.feeParentChargeFormItems = Optional.ofNullable(parentChargeFormItems).orElse(new ArrayList<>())
                .stream()
                .filter(chargeFormItem -> chargeFormItem.getSourceItemType() != Constants.SourceItemType.SELF_PROVIDED)
                .filter(chargeFormItem -> chargeFormItem.getGoodsFeeType() == GoodsFeeType.FEE_PARENT)
                .collect(Collectors.toList());
        this.composeParentChargeFormItems = Optional.ofNullable(parentChargeFormItems).orElse(new ArrayList<>())
                .stream()
                .filter(chargeFormItem -> chargeFormItem.getSourceItemType() != Constants.SourceItemType.SELF_PROVIDED)
                .filter(chargeFormItem -> chargeFormItem.getComposeType() == ComposeType.COMPOSE)
                .collect(Collectors.toList());
        this.deductedItems = deductedItems;
        this.composeChildrenChargeFormItemsMap = composeChildrenChargeFormItemsMap;
    }

    public List<ChargeTransactionRecord> getToSaveChargeTransactionRecords() {
        return Optional.ofNullable(toSaveChargeTransactionRecords).orElse(new ArrayList<>());
    }

    public StatRecordResult generateChargeTransactionRecords(List<ChargeTransactionRecord> addedRecords, List<CreateOrderView> airPharmacyOrders) {
        if (chargeTransactions == null || chargeTransactions.size() == 0) {
            return new StatRecordResult();
        }

        List<ChargeTransactionRecord> historyTransactionRecords = chargeTransactionRecordService.listByChargeSheetIdAndIsOldRecord(chargeSheetId, 0);

        if (CollectionUtils.isNotEmpty(addedRecords)) {
            historyTransactionRecords.addAll(addedRecords);
        }

        List<ChargeTransactionRecord> chargeTransactionRecords;
        if (transactionRecordHandleMode == Constants.TransactionRecordHandleMode.RECORD_COUNT_BY_PRICE_RATE) {
            //兼容老的部分退费的数据，进行退费摊费是要走老的摊费逻辑
            chargeTransactionRecords = generateChargeTransactionRecordsByPriceRate(historyTransactionRecords, airPharmacyOrders);
            addToSaveChargeTransactionRecords(chargeTransactionRecords);
        } else {
            chargeTransactionRecords = generateChargeTransactionRecordsByChoose(historyTransactionRecords, airPharmacyOrders);
        }

        //如果是部分退费且全部退完，将record全部标记为历史数据
        if (payType == PayType.PAID_BACK) {
            historyTransactionRecords.forEach(record -> record.setIsOldRecord(1));
            addToSaveChargeTransactionRecords(historyTransactionRecords);
        }
        StatRecordResult statRecordResult = new StatRecordResult();
        statRecordResult.setAddedRecords(chargeTransactionRecords);
        statRecordResult.setToSaveRecords(toSaveChargeTransactionRecords);
        return statRecordResult;
    }

    private void dealMemberCardAmountFlat(ChargeTransaction chargeTransaction, List<ChargeTransactionRecord> chargeTransactionRecords) {
        //赠金金额
        BigDecimal present = chargeTransaction.getPresentAmount();
        BigDecimal principal = chargeTransaction.getPrincipalAmount();

        FlatReceivedPriceHelper principalHelper = new FlatReceivedPriceHelper(principal.abs());
        List<FlatReceivedPriceHelper.FlatCell> principalCellList = chargeTransactionRecords.stream().map(record -> {
            FlatReceivedPriceHelper.FlatCell flatCell = new FlatReceivedPriceHelper.FlatCell();
            BigDecimal totalPrice = MathUtils.wrapBigDecimalAdd(record.getTotalPrice(), record.getDiscountPrice());
            flatCell.setId(record.getId())
                    .setTotalPrice(totalPrice)
                    .setMaxFlatPrice(totalPrice);

            return flatCell;
        }).collect(Collectors.toList());
        principalHelper.flat(principalCellList);
        Map<String, FlatReceivedPriceHelper.FlatCell> presentCellMap = ListUtils.toMap(principalCellList, FlatReceivedPriceHelper.FlatCell::getId);

        chargeTransactionRecords.forEach(record -> {
            BigDecimal totalPrice = MathUtils.wrapBigDecimalAdd(record.getTotalPrice(), record.getDiscountPrice());
            FlatReceivedPriceHelper.FlatCell flatCell = presentCellMap.get(record.getId());
            BigDecimal recordPrincipalAmount = flatCell.getFlatPrice();
            record.getAdditional().setPrincipalAmount(recordPrincipalAmount);
            record.getAdditional().setPresentAmount(MathUtils.wrapBigDecimalSubtract(totalPrice, recordPrincipalAmount));
        });

        //检查平摊后赠金总金额是否正确
        BigDecimal flatPresent = chargeTransactionRecords.stream().map(record -> record.getAdditional().getPresentAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (MathUtils.wrapBigDecimalCompare(flatPresent, present.abs()) != 0) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "会员支付平摊时计算错误,赠金金额 presentAmount {} 不等于 record赠金金额总和 flatPresent {}", present, flatPresent);
            throw new IllegalStateException("摊费后presentAmount不等于record赠金金额总和");
        }
    }

    private List<ChargeTransactionRecord> generateChargeTransactionRecordsByChoose(List<ChargeTransactionRecord> historyTransactionRecords, List<CreateOrderView> airPharmacyOrders) {
        List<ChargeTransactionRecord> chargeTransactionRecords = new ArrayList<>();
        ChargeTransaction chargeTransaction = CollectionUtils.isNotEmpty(chargeTransactions) ? chargeTransactions.get(0) : null;

        if (Objects.isNull(chargeTransaction)) {
            return new ArrayList<>();
        }

        BigDecimal leftToRecordPrice = BigDecimal.ZERO;

        CalculateResult calculateResult = generateRecordsPerTransactionByChoose(chargeTransaction, chargeTransaction.getAmount().abs(), chargeFormItems, airPharmacyChargeForms, deductedItems, historyTransactionRecords, payType, refundFlatType, airPharmacyOrders, false, chargeVersion, operatorId);
        chargeTransactionRecords.addAll(calculateResult.getAddedRecords());
        historyTransactionRecords.addAll(calculateResult.getAddedRecords());
        addToSaveChargeTransactionRecords(calculateResult.getToSaveRecords());
        leftToRecordPrice = MathUtils.wrapBigDecimalAdd(leftToRecordPrice, calculateResult.leftToRecordPrice);


        //处理欠退金额
        if (leftToRecordPrice.compareTo(BigDecimal.ZERO) > 0
                && !StatRecordProcessor.PayType.isPaid(payType)) {
            CalculateResult oweRefundCalculateResult = generateRecordsPerTransactionByChoose(chargeTransaction, leftToRecordPrice, refundedChargeFormItems, airPharmacyRefundedChargeForms, new ArrayList<>(), historyTransactionRecords, payType, RefundFlatType.ALL_FLAT, new ArrayList<>(), true, chargeVersion, operatorId);
            chargeTransactionRecords.addAll(oweRefundCalculateResult.getAddedRecords());
            historyTransactionRecords.addAll(oweRefundCalculateResult.getAddedRecords());
            addToSaveChargeTransactionRecords(oweRefundCalculateResult.getToSaveRecords());
        }

        //单独给个标记，诊所管家中治疗理疗检查检验其他费用有多个对码时，由于使用了医嘱费用项的结构，但是又没有遵循子项的成本和 = 母项的成本，这种母项数据需要单独对成本金额处理
        boolean needDealClinicShebaoCodeMatchCostPrice = !isHospital;

        //处理费用母项和费用子项的记录
        List<ChargeTransactionRecord> feeParentRecords = generateComposeChargeFormItemRecords(chargeTransaction,
                feeParentChargeFormItems,
                composeChildrenChargeFormItemsMap,
                payType,
                deductedItems,
                historyTransactionRecords,
                chargeTransactionRecords,
                needDealClinicShebaoCodeMatchCostPrice,
                chargeVersion,
                operatorId);
        chargeTransactionRecords.addAll(feeParentRecords);
        historyTransactionRecords.addAll(feeParentRecords);
        addToSaveChargeTransactionRecords(feeParentRecords);

        //处理套餐母项和费用母项的记录
        List<ChargeTransactionRecord> composeRecords = generateComposeChargeFormItemRecords(chargeTransaction,
                composeParentChargeFormItems,
                composeChildrenChargeFormItemsMap,
                payType,
                deductedItems,
                historyTransactionRecords,
                chargeTransactionRecords,
                false,
                chargeVersion,
                operatorId);

        chargeTransactionRecords.addAll(composeRecords);
        historyTransactionRecords.addAll(composeRecords);
        addToSaveChargeTransactionRecords(composeRecords);

        //处理会员支付或卡项支付摊费
        PresentPrincipalAmountFlatTool.flatPresentPrincipalAmountToRecordByChoose(chargeTransactionRecords
                        .stream()
                        .map(ChargeTransactionRecordCell::new)
                        .collect(Collectors.toList()),
                new ChargeTransactionCell(chargeTransactions.get(0))
        );
//        flatMemberCardPayToRecordByChoose(chargeTransactions, chargeTransactionRecords);

        return chargeTransactionRecords;
    }

    private List<ChargeTransactionRecord> generateChargeTransactionRecordsByPriceRate(List<ChargeTransactionRecord> historyTransactionRecords, List<CreateOrderView> airPharmacyOrders) {
        List<ChargeTransactionRecord> chargeTransactionRecords = new ArrayList<>();

        BigDecimal leftToRecordPrice = BigDecimal.ZERO;
        if (payType == StatRecordProcessor.PayType.PARTED_PAID || payType == StatRecordProcessor.PayType.PARTED_PAID_BACK || payType == StatRecordProcessor.PayType.PARTED_REFUND) {
            for (ChargeTransaction chargeTransaction : chargeTransactions) {
                CalculateResult calculateResult = generateRecordsPerTransaction(chargeTransaction, chargeTransaction.getAmount().abs(), chargeFormItems, airPharmacyChargeForms, deductedItems, composeParentChargeFormItems, historyTransactionRecords, payType, new ArrayList<>(), false, false, operatorId);
                chargeTransactionRecords.addAll(calculateResult.getAddedRecords());
                historyTransactionRecords.addAll(calculateResult.getAddedRecords());
                leftToRecordPrice = MathUtils.wrapBigDecimalAdd(leftToRecordPrice, calculateResult.leftToRecordPrice);
            }
        } else if (payType == StatRecordProcessor.PayType.PAID || payType == StatRecordProcessor.PayType.PAID_BACK || payType == StatRecordProcessor.PayType.REFUND) {
            for (int i = 0; i < chargeTransactions.size(); i++) {
                ChargeTransaction chargeTransaction = chargeTransactions.get(i);
                boolean isLastRecord = i == chargeTransactions.size() - 1;
                CalculateResult calculateResult = generateRecordsPerTransaction(chargeTransaction, chargeTransaction.getAmount().abs(), chargeFormItems, airPharmacyChargeForms, deductedItems, composeParentChargeFormItems, historyTransactionRecords, payType, airPharmacyOrders, isLastRecord, false, operatorId);
                chargeTransactionRecords.addAll(calculateResult.getAddedRecords());
                historyTransactionRecords.addAll(calculateResult.getAddedRecords());
                leftToRecordPrice = MathUtils.wrapBigDecimalAdd(leftToRecordPrice, calculateResult.leftToRecordPrice);
            }
        }

        //处理欠退金额
        if (payType == StatRecordProcessor.PayType.REFUND && leftToRecordPrice.compareTo(BigDecimal.ZERO) > 0) {
            ChargeTransaction lastChargeTransaction = chargeTransactions.get(chargeTransactions.size() - 1);
            CalculateResult calculateResult = generateRecordsPerTransaction(lastChargeTransaction, leftToRecordPrice, refundedChargeFormItems, airPharmacyRefundedChargeForms, new ArrayList<>(), composeParentChargeFormItems, historyTransactionRecords, payType, new ArrayList<>(), false, true, operatorId);
            chargeTransactionRecords.addAll(calculateResult.getAddedRecords());
            historyTransactionRecords.addAll(calculateResult.getAddedRecords());
        }

        //处理会员支付摊费
        if (chargeTransactions.get(0) != null && chargeTransactions.get(0).getPayMode() == Constants.ChargePayMode.MEMBER_CARD) {
            Map<Integer, List<ChargeTransactionRecord>> recordGroupByComposeType = ListUtils.groupByKey(chargeTransactionRecords, ChargeTransactionRecord::getComposeType);

            List<ChargeTransactionRecord> flatList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(recordGroupByComposeType.get(ComposeType.COMPOSE_SUB_ITEM))) {
                flatList.addAll(recordGroupByComposeType.get(ComposeType.COMPOSE_SUB_ITEM));
            }
            if (CollectionUtils.isNotEmpty(recordGroupByComposeType.get(ComposeType.NOT_COMPOSE))) {
                flatList.addAll(recordGroupByComposeType.get(ComposeType.NOT_COMPOSE));
            }
            dealMemberCardAmountFlat(chargeTransactions.get(0), flatList);

            if (CollectionUtils.isNotEmpty(recordGroupByComposeType.get(ComposeType.COMPOSE))) {
                Map<String, ChargeTransactionRecord> composeItemRecordMap = ListUtils.toMap(recordGroupByComposeType.get(ComposeType.COMPOSE_SUB_ITEM), ChargeTransactionRecord::getChargeFormItemId);
                recordGroupByComposeType.get(ComposeType.COMPOSE).forEach(composeRecord -> {
                    List<ChargeFormItem> chargeFormItems = composeChildrenChargeFormItemsMap.get(composeRecord.getChargeFormItemId());
                    for (ChargeFormItem chargeFormItem : chargeFormItems) {
                        ChargeTransactionRecord itemRecord = composeItemRecordMap.get(chargeFormItem.getId());
                        if (itemRecord != null) {

                            composeRecord.getAdditional().setPresentAmount(itemRecord.getAdditional().getPresentAmount());
                            composeRecord.getAdditional().setPrincipalAmount(itemRecord.getAdditional().getPrincipalAmount());
                            composeItemRecordMap.remove(chargeFormItem.getId());
                            break;
                        }

                    }
                });
            }
        }


        return chargeTransactionRecords;
    }

    private static CalculateResult generateRecordsPerTransaction(ChargeTransaction chargeTransaction,
                                                                 BigDecimal toRecordPrice,
                                                                 List<ChargeFormItem> chargeFormItems,
                                                                 List<ChargeForm> airPharmacyChargeForms,
                                                                 List<StatRecordAffectedDeductedItem> deductedItems,
                                                                 List<ChargeFormItem> composeChargeFormItems,
                                                                 List<ChargeTransactionRecord> historyRecords,
                                                                 int payType,
                                                                 List<CreateOrderView> airPharmacyOrders,
                                                                 boolean isLastRecord,
                                                                 boolean isOwedRefund,
                                                                 String operatorId) {
        if (chargeFormItems == null) {
            chargeFormItems = new ArrayList<>();
        }

        if (airPharmacyChargeForms == null) {
            airPharmacyChargeForms = new ArrayList<>();
        }

        if (airPharmacyOrders == null) {
            airPharmacyOrders = new ArrayList<>();
        }

        Map<String, List<ChargeTransactionRecord>> historyRecordsIdMap = ListUtils.groupByKey(
                historyRecords.stream().filter(record -> record.getType() != ChargeTransactionRecord.RecordType.AIR_PHARMACY).collect(Collectors.toList()),
                ChargeTransactionRecord::getChargeFormItemId
        );

        Map<String, StatRecordAffectedDeductedItem> deductedItemMap = Optional.ofNullable(deductedItems).orElse(new ArrayList<>())
                .stream().collect(Collectors.toMap(StatRecordAffectedDeductedItem::getId, Function.identity()));

        Map<String, List<ChargeTransactionRecord>> airPharmacyHistoryRecordsIdMap = ListUtils.groupByKey(
                historyRecords.stream()
                        .filter(record -> record.getType() == ChargeTransactionRecord.RecordType.AIR_PHARMACY)
                        .filter(record -> record.getAdditional() != null && !StringUtils.isEmpty(record.getAdditional().getChargeFormId()))
                        .collect(Collectors.toList()),
                record -> record.getAdditional().getChargeFormId()
        );

        Map<String, CreateOrderView> createOrderViewMap = ListUtils.toMap(airPharmacyOrders, CreateOrderView::getSourceOrderId);

        int chargeType;
        boolean isPaidback = false;
        boolean isPaid = false;
        switch (payType) {
            case StatRecordProcessor.PayType.PAID:
                chargeType = ChargeTransactionRecord.ChargeType.CHARGED;
                isPaid = true;
                break;
            case StatRecordProcessor.PayType.PARTED_PAID:
                chargeType = ChargeTransactionRecord.ChargeType.PART_CHARGED;
                isPaid = true;
                break;
            case StatRecordProcessor.PayType.PAID_BACK:
            case StatRecordProcessor.PayType.PARTED_PAID_BACK:
                isPaidback = true;
                chargeType = ChargeTransactionRecord.ChargeType.REFUND;
                break;
            case StatRecordProcessor.PayType.REFUND:
            case StatRecordProcessor.PayType.PARTED_REFUND:
                chargeType = ChargeTransactionRecord.ChargeType.REFUND;
                break;
            default:
                chargeType = 0;
        }

        boolean finalIsPaidback = isPaidback;
        List<cn.abcyun.cis.charge.processor.StatRecordCalculateCell> calculateCells = chargeFormItems.stream().map(item -> {
            cn.abcyun.cis.charge.processor.StatRecordCalculateCell cell = new cn.abcyun.cis.charge.processor.StatRecordCalculateCell();
            cell.setId(item.getId());
            cell.setChargeFormItem(item);
            cell.setDeductedItem(deductedItemMap.getOrDefault(item.getId(), null));
            cell.setPaidback(finalIsPaidback);
            List<ChargeTransactionRecord> itemHistoryRecords = historyRecordsIdMap.getOrDefault(item.getId(), new ArrayList<>());
            cell.setHistoryRecords(itemHistoryRecords);
            return cell;
        }).collect(Collectors.toList());

        calculateCells.addAll(airPharmacyChargeForms.stream().map(form -> {
            cn.abcyun.cis.charge.processor.StatRecordCalculateCell cell = new cn.abcyun.cis.charge.processor.StatRecordCalculateCell();
            cell.setId(form.getId());
            cell.setChargeForm(form);
            cell.setPaidback(finalIsPaidback);
            if (isOwedRefund || (payType != StatRecordProcessor.PayType.PARTED_REFUND && payType != PayType.REFUND)) {
                List<ChargeTransactionRecord> formHistoryRecords = airPharmacyHistoryRecordsIdMap.getOrDefault(form.getId(), new ArrayList<>());

                if (isOwedRefund) {
                    cell.setHistoryRecords(formHistoryRecords.stream().filter(record -> record.getChargeType() == ChargeTransactionRecord.ChargeType.REFUND).collect(Collectors.toList()));
                } else {
                    cell.setHistoryRecords(formHistoryRecords);
                }
            } else {
                cell.setHistoryRecords(new ArrayList<>());
            }

            cell.setIsAirPharmacy(1);
            return cell;
        }).collect(Collectors.toList()));


        BigDecimal leftToRecordPrice = calculate(calculateCells, toRecordPrice, isLastRecord, isPaidback, isPaid);

        Map<String, ChargeFormItem> chargeFormItemIdMap = ListUtils.toMap(chargeFormItems, ChargeFormItem::getId);

        Map<String, ChargeForm> airPharmacyChargeFormMap = ListUtils.toMap(airPharmacyChargeForms, ChargeForm::getId);

        Map<String, ChargeFormItem> composeChargeFormItemIdMap = ListUtils.toMap(composeChargeFormItems, ChargeFormItem::getId);

        List<ChargeTransactionRecord> resultRecords = calculateCells.stream()
                .filter(cell -> cell.getIsAirPharmacy() == 0)
                .map(cell -> {
                    ChargeFormItem chargeFormItem = chargeFormItemIdMap.getOrDefault(cell.getId(), null);
                    if (chargeFormItem == null) {
                        return null;
                    }

                    if (chargeFormItem.getComposeType() == ComposeType.COMPOSE && !isLastRecord) {
                        return null;
                    }

                    ChargeTransactionRecord record = new ChargeTransactionRecord();
                    record.setId(AbcIdUtils.getUUID());
                    record.setPatientOrderId(chargeTransaction.getPatientOrderId());
                    record.setChainId(chargeTransaction.getChainId());
                    record.setClinicId(chargeTransaction.getClinicId());
                    record.setChargeSheetId(chargeTransaction.getChargeSheetId());
                    record.setPatientId(chargeTransaction.getPatientId());
                    record.setTransactionId(chargeTransaction.getId());
                    record.setChargeType(chargeType);
                    //由于历史原因，会员充值最早版本是没有chargeFormItem的，记录的type = 2，在2021-11-10加上了chargeFormItem，为了兼容老数据，这个地方type还是写2，其他数据都写0
                    record.setType(chargeFormItem.getProductType() == Constants.ProductType.MEMBER_CARD_RECHARGE ? ChargeTransactionRecord.RecordType.MEMBER_CARD_RECHARGE : ChargeTransactionRecord.RecordType.PRODUCT);
                    record.setProductId(chargeFormItem.getProductId());
                    record.setChargeFormItemId(chargeFormItem.getId());
                    record.setProductType(chargeFormItem.getProductType());
                    record.setProductSubType(chargeFormItem.getProductSubType());
                    record.setComposeType(chargeFormItem.getComposeType());
                    record.setProductUnit(chargeFormItem.getUnit());
                    record.setUseDismounting(chargeFormItem.getUseDismounting());
                    record.setDoseCount(chargeFormItem.getDoseCount());
                    record.setTotalPrice(cell.getToRecordTotalPriceResult());
                    record.setDiscountPrice(cell.getToRecordDiscountPriceResult());
                    record.setProductUnitCount(cell.getToRecordUnitCountResult());
                    record.setTotalCostPrice(cell.getToRecordTotalCostPriceResult());
                    FillUtils.fillCreatedBy(record, operatorId);

                    ChargeTransactionRecordAdditional additional = new ChargeTransactionRecordAdditional();
                    additional.setId(record.getId());
                    additional.setPatientOrderId(record.getPatientOrderId());
                    additional.setChainId(record.getChainId());
                    additional.setClinicId(record.getClinicId());
                    additional.setChargeSheetId(record.getChargeSheetId());
                    additional.setDiscountInfo(cell.getToRecordDiscountInfoResult());
                    additional.setDeductTotalPrice(cell.getToRecordDeductPrice().negate());
                    additional.setDeductCount(cell.getToRecordDeductUnitCount());
                    additional.setDeductTotalCostPrice(cell.getToRecordDeductTotalCostPrice());
                    additional.setPharmacyType(chargeFormItem.getPharmacyType());

                    FillUtils.fillCreatedBy(additional, operatorId);
                    record.setAdditional(additional);


                    List<ChargeTransactionRecord> records = new ArrayList<>();
                    records.add(record);
                    //套餐母项不算自己的价格只在最后完成支付时存数量
                    if (chargeFormItem.getComposeType() == ComposeType.COMPOSE) {
                        record.setTotalCostPrice(BigDecimal.ZERO);
                        record.setTotalPrice(BigDecimal.ZERO);
                        record.setDiscountPrice(BigDecimal.ZERO);
                    } else if (chargeFormItem.getComposeType() == ComposeType.COMPOSE_SUB_ITEM && !TextUtils.isEmpty(chargeFormItem.getComposeParentFormItemId())) {
                        ChargeFormItem parentChargeFormItem = composeChargeFormItemIdMap.getOrDefault(chargeFormItem.getComposeParentFormItemId(), null);
                        if (parentChargeFormItem != null) {
                            ChargeTransactionRecord parentRecord = new ChargeTransactionRecord();
                            BeanUtils.copyProperties(record, parentRecord);
                            parentRecord.setId(AbcIdUtils.getUUID());
                            parentRecord.setChargeFormItemId(parentChargeFormItem.getId());
                            parentRecord.setProductUnitCount(BigDecimal.ZERO);
                            parentRecord.setProductUnit(parentChargeFormItem.getUnit());
                            parentRecord.setProductType(parentChargeFormItem.getProductType());
                            parentRecord.setProductSubType(parentChargeFormItem.getProductSubType());
                            parentRecord.setProductId(parentChargeFormItem.getProductId());
                            parentRecord.setComposeType(ComposeType.COMPOSE);
                            parentRecord.setUseDismounting(0);

                            ChargeTransactionRecordAdditional parentAdditional = new ChargeTransactionRecordAdditional();
                            parentAdditional.setId(parentRecord.getId());
                            parentAdditional.setPatientOrderId(parentRecord.getPatientOrderId());
                            parentAdditional.setChainId(parentRecord.getChainId());
                            parentAdditional.setClinicId(parentRecord.getClinicId());
                            parentAdditional.setChargeSheetId(parentRecord.getChargeSheetId());
                            parentAdditional.setDiscountInfo(additional.getDiscountInfo());
                            parentAdditional.setDeductTotalPrice(additional.getDeductTotalPrice());
                            parentAdditional.setDeductCount(additional.getDeductCount());
                            parentAdditional.setDeductTotalCostPrice(additional.getDeductTotalCostPrice());
                            parentAdditional.setPharmacyType(parentChargeFormItem.getPharmacyType());
                            FillUtils.fillCreatedBy(parentAdditional, operatorId);
                            parentRecord.setAdditional(parentAdditional);
                            records.add(parentRecord);

                        }
                    }
                    return records;
                })
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());

        List<ChargeTransactionRecord> airPharmacyRecords = calculateCells.stream()
                .filter(cell -> cell.getIsAirPharmacy() == 1)
                .map(cell -> {
                    ChargeForm chargeForm = airPharmacyChargeFormMap.get(cell.getId());

                    if (chargeForm == null) {
                        return null;
                    }
                    ChargeTransactionRecord record = new ChargeTransactionRecord();
                    record.setId(AbcIdUtils.getUUID());
                    record.setPatientOrderId(chargeTransaction.getPatientOrderId());
                    record.setChainId(chargeTransaction.getChainId());
                    record.setClinicId(chargeTransaction.getClinicId());
                    record.setChargeSheetId(chargeTransaction.getChargeSheetId());
                    record.setPatientId(chargeTransaction.getPatientId());
                    record.setTransactionId(chargeTransaction.getId());
                    record.setChargeType(chargeType);
                    record.setType(ChargeTransactionRecord.RecordType.AIR_PHARMACY);
                    record.setProductUnit("笔");
                    record.setTotalPrice(cell.getToRecordTotalPriceResult());
                    record.setDiscountPrice(cell.getToRecordDiscountPriceResult());
                    record.setProductUnitCount(cell.getToRecordUnitCountResult());

                    //处理空中药房的成本价
                    CreateOrderView createOrderView = createOrderViewMap.get(chargeForm.getId());
                    if (chargeType == ChargeTransactionRecord.ChargeType.CHARGED && createOrderView != null) {
                        record.setTotalCostPrice(createOrderView.getClinicTotalPrice());
                    } else if (chargeType == ChargeTransactionRecord.ChargeType.REFUND && CollectionUtils.isEmpty(cell.getHistoryRecords())) {
                        ChargeTransactionRecord chargeTransactionRecord = airPharmacyHistoryRecordsIdMap.getOrDefault(cell.getId(), new ArrayList<>()).stream().filter(r -> r.getChargeType() == ChargeTransactionRecord.ChargeType.CHARGED && r.getTotalCostPrice().compareTo(BigDecimal.ZERO) != 0).findFirst().orElse(null);
                        BigDecimal totalCostPrice = chargeTransactionRecord != null ? chargeTransactionRecord.getTotalCostPrice() : BigDecimal.ZERO;
                        record.setTotalCostPrice(totalCostPrice);
                    } else {
                        record.setTotalCostPrice(BigDecimal.ZERO);
                    }

                    FillUtils.fillCreatedBy(record, operatorId);

                    ChargeTransactionRecordAdditional additional = new ChargeTransactionRecordAdditional();
                    additional.setId(record.getId());
                    additional.setPatientOrderId(record.getPatientOrderId());
                    additional.setChainId(record.getChainId());
                    additional.setClinicId(record.getClinicId());
                    additional.setChargeSheetId(record.getChargeSheetId());
                    additional.setChargeFormId(chargeForm.getId());
                    additional.setDiscountInfo(cell.getToRecordDiscountInfoResult());
                    additional.setDeductTotalPrice(cell.getToRecordDeductPrice().negate());
                    additional.setDeductCount(cell.getToRecordDeductUnitCount());
                    additional.setDeductTotalCostPrice(cell.getToRecordDeductTotalCostPrice());
                    additional.setPharmacyType(chargeForm.getPharmacyType());

                    FillUtils.fillCreatedBy(additional, operatorId);
                    record.setAdditional(additional);

                    return record;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (chargeType == ChargeTransactionRecord.ChargeType.CHARGED) {
            airPharmacyRecords.stream()
                    .forEach(record -> {
                        CreateOrderView createOrderView = createOrderViewMap.getOrDefault(record.getAdditional().getChargeFormId(), null);
                        record.getAdditional().setBisOrderId(Optional.ofNullable(createOrderView).map(CreateOrderView::getOrderId).orElse(null));
                    });

            //找到最后一次退费的时候，在写bisOrderId时只对之后的数据生效
            Instant lastRefundRecordCreated = historyRecords.stream()
                    .filter(record -> record.getType() == ChargeTransactionRecord.RecordType.AIR_PHARMACY && record.getAdditional() != null)
                    .filter(record -> org.apache.commons.lang3.StringUtils.isEmpty(record.getAdditional().getBisOrderId())
                            && record.getChargeType() == ChargeTransactionRecord.ChargeType.REFUND)
                    .sorted(Comparator.comparing(ChargeTransactionRecord::getCreated).reversed())
                    .findFirst()
                    .map(ChargeTransactionRecord::getCreated)
                    .orElse(null);

            historyRecords.stream()
                    .filter(record -> record.getType() == ChargeTransactionRecord.RecordType.AIR_PHARMACY && record.getAdditional() != null)
                    .filter(record -> org.apache.commons.lang3.StringUtils.isEmpty(record.getAdditional().getBisOrderId())
                            && record.getChargeType() == ChargeTransactionRecord.ChargeType.PART_CHARGED)
                    .filter(record -> {
                        if (lastRefundRecordCreated == null) {
                            return true;
                        }
                        return record.getCreated().isAfter(lastRefundRecordCreated);
                    })
                    .forEach(record -> {
                        CreateOrderView createOrderView = createOrderViewMap.getOrDefault(record.getAdditional().getChargeFormId(), null);
                        record.getAdditional().setBisOrderId(Optional.ofNullable(createOrderView).map(CreateOrderView::getOrderId).orElse(null));
                    });
        } else if (chargeType == ChargeTransactionRecord.ChargeType.REFUND) {
            airPharmacyRecords.stream()
                    .forEach(record -> {
                        List<ChargeTransactionRecord> historyAirPharmacyRecords = airPharmacyHistoryRecordsIdMap.getOrDefault(record.getAdditional().getChargeFormId(), new ArrayList<>());
                        ChargeTransactionRecord lastAirPharmacyRecord = historyAirPharmacyRecords.stream()
                                .sorted(Comparator.comparing(ChargeTransactionRecord::getCreated).reversed())
                                .findFirst().orElse(null);

                        record.getAdditional().setBisOrderId(Optional.ofNullable(lastAirPharmacyRecord)
                                .map(ChargeTransactionRecord::getAdditional)
                                .map(ChargeTransactionRecordAdditional::getBisOrderId).orElse(null));
                    });
        }

        resultRecords.addAll(airPharmacyRecords);
        CalculateResult calculateResult = new CalculateResult();
        calculateResult.setLeftToRecordPrice(leftToRecordPrice);
        calculateResult.setAddedRecords(resultRecords);
        return calculateResult;
    }


    private static CalculateResult generateRecordsPerTransactionByChoose(ChargeTransaction chargeTransaction,
                                                                         BigDecimal toRecordPrice,
                                                                         List<ChargeFormItem> chargeFormItems,
                                                                         List<ChargeForm> airPharmacyChargeForms,
                                                                         List<StatRecordAffectedDeductedItem> deductedItems,
                                                                         List<ChargeTransactionRecord> historyRecords,
                                                                         int payType,
                                                                         int refundFlatType,
                                                                         List<CreateOrderView> airPharmacyOrders,
                                                                         boolean isOwedRefund,
                                                                         int chargeVersion,
                                                                         String operatorId) {
        chargeFormItems = chargeFormItems != null ? chargeFormItems : new ArrayList<>();
        airPharmacyChargeForms = airPharmacyChargeForms != null ? airPharmacyChargeForms : new ArrayList<>();
        airPharmacyOrders = airPharmacyOrders != null ? airPharmacyOrders : new ArrayList<>();

        Map<String, List<ChargeTransactionRecord>> historyRecordsIdMap = ListUtils.groupByKey(
                historyRecords.stream().filter(record -> record.getType() != ChargeTransactionRecord.RecordType.AIR_PHARMACY).collect(Collectors.toList()),
                ChargeTransactionRecord::getChargeFormItemId
        );

        Map<String, List<ChargeTransactionRecordBatchInfo>> historyRecordBatchInfosIdMap = historyRecords.stream()
                .flatMap(record -> Optional.ofNullable(record.getBatchInfos()).orElse(new ArrayList<>()).stream())
                .collect(Collectors.groupingBy(ChargeTransactionRecordBatchInfo::getChargeFormItemBatchInfoId));

        Map<String, StatRecordAffectedDeductedItem> deductedItemMap = Optional.ofNullable(deductedItems).orElse(new ArrayList<>())
                .stream().collect(Collectors.toMap(StatRecordAffectedDeductedItem::getId, Function.identity()));

        Map<String, List<ChargeTransactionRecord>> airPharmacyHistoryRecordsIdMap = ListUtils.groupByKey(
                historyRecords.stream()
                        .filter(record -> record.getType() == ChargeTransactionRecord.RecordType.AIR_PHARMACY)
                        .filter(record -> record.getAdditional() != null && !StringUtils.isEmpty(record.getAdditional().getChargeFormId()))
                        .collect(Collectors.toList()),
                record -> record.getAdditional().getChargeFormId()
        );

        Map<String, CreateOrderView> createOrderViewMap = ListUtils.toMap(airPharmacyOrders, CreateOrderView::getSourceOrderId);

        int chargeType = convertToChargeType(payType);

        boolean isPaidback = StatRecordProcessor.PayType.isPaidBack(payType);

        //优先处理非套餐和费用母项和费用项本身
        List<StatRecordByChooseCalculateCell> calculateCells = chargeFormItems.stream()
                .filter(chargeFormItem -> chargeFormItem.getComposeType() != ComposeType.COMPOSE && chargeFormItem.getGoodsFeeType() != GoodsFeeType.FEE_PARENT)
                .map(item -> {
                    StatRecordByChooseCalculateCell cell = StatRecordByChooseCalculateHelper.createCalculateCell(chargeVersion);
                    cell.setId(item.getId());
                    cell.setChargeFormItem(item);
                    cell.setDeductedItem(deductedItemMap.getOrDefault(item.getId(), null));
                    cell.setPaidback(isPaidback);
                    List<ChargeTransactionRecord> itemHistoryRecords = historyRecordsIdMap.getOrDefault(item.getId(), new ArrayList<>());
                    if (org.apache.commons.lang3.StringUtils.isNotEmpty(item.getAssociateFormItemId())) {
                        cell.setAssociateHistoryRecords(historyRecordsIdMap.getOrDefault(item.getAssociateFormItemId(), new ArrayList<>()));
                    }
                    cell.setHistoryRecords(itemHistoryRecords);
                    if (CollectionUtils.isNotEmpty(item.getChargeFormItemBatchInfos())) {
                        cell.setBatchInfoCalculateCells(item.getChargeFormItemBatchInfos().stream()
                                .filter(chargeFormItemBatchInfo -> chargeFormItemBatchInfo.getIsDeleted() == 0)
                                .map(batchInfo -> new StatRecordBatchInfoCalculateCell()
                                        .setId(batchInfo.getId())
                                        .setChargeFormItemBatchInfo(batchInfo)
                                        .setHistoryBatchInfos(historyRecordBatchInfosIdMap.getOrDefault(batchInfo.getId(), new ArrayList<>()))).collect(Collectors.toList())
                        );
                    }

                    //如果是部分退费，只记录在record表中有数据的item
                    if (isPaidback && CollectionUtils.isEmpty(itemHistoryRecords)) {
                        return null;
                    }

                    return cell;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        calculateCells.addAll(airPharmacyChargeForms.stream().map(form -> {
            StatRecordByChooseCalculateCell cell = StatRecordByChooseCalculateHelper.createCalculateCell(chargeVersion);
            cell.setId(form.getId());
            cell.setChargeForm(form);
            cell.setPaidback(isPaidback);
            List<ChargeTransactionRecord> formHistoryRecords = airPharmacyHistoryRecordsIdMap.getOrDefault(form.getId(), new ArrayList<>());

            if (isOwedRefund || (payType != StatRecordProcessor.PayType.PARTED_REFUND && payType != PayType.REFUND)) {
                if (isOwedRefund) {
                    cell.setHistoryRecords(formHistoryRecords.stream().filter(record -> record.getChargeType() == ChargeTransactionRecord.ChargeType.REFUND).collect(Collectors.toList()));
                } else {
                    cell.setHistoryRecords(formHistoryRecords);
                }
            } else {
                cell.setHistoryRecords(new ArrayList<>());
            }
            //退费时，绑定associateHistoryRecords，直接用空中药房的收费数据就行
            cell.setAssociateHistoryRecords(formHistoryRecords);

            cell.setIsAirPharmacy(1);
            return cell;
        }).collect(Collectors.toList()));

        /**
         * 开始计算费用
         */
        BigDecimal leftToRecordPrice = StatRecordByChooseCalculateHelper.calculate(chargeTransaction, calculateCells, toRecordPrice, payType, refundFlatType);

        Map<String, ChargeFormItem> chargeFormItemIdMap = ListUtils.toMap(chargeFormItems, ChargeFormItem::getId);

        Map<String, ChargeForm> airPharmacyChargeFormMap = ListUtils.toMap(airPharmacyChargeForms, ChargeForm::getId);
        CalculateResult calculateResult = new CalculateResult();

        List<ChargeTransactionRecord> resultRecords = calculateCells.stream()
                .filter(cell -> cell.getIsAirPharmacy() == 0)
                .map(cell -> {
                    ChargeFormItem chargeFormItem = chargeFormItemIdMap.getOrDefault(cell.getId(), null);
                    if (chargeFormItem == null) {
                        return null;
                    }

                    //这里不处理套餐的记录，在外层单独处理套餐
                    if (chargeFormItem.getComposeType() == ComposeType.COMPOSE) {
                        return null;
                    }

                    //欠退的情况是不需要记录金额为0的数据的，因为欠退就一定要记录到item上且金额要大于0
                    if (isOwedRefund && MathUtils.wrapBigDecimalCompare(cell.getToRecordTotalPriceResult(), BigDecimal.ZERO) == 0) {
                        return null;
                    }

                    ChargeTransactionRecord record = new ChargeTransactionRecord();
                    record.setId(AbcIdUtils.getUUID());
                    record.setPatientOrderId(chargeTransaction.getPatientOrderId());
                    record.setChainId(chargeTransaction.getChainId());
                    record.setClinicId(chargeTransaction.getClinicId());
                    record.setChargeSheetId(chargeTransaction.getChargeSheetId());
                    record.setPatientId(chargeTransaction.getPatientId());
                    record.setTransactionId(chargeTransaction.getId());
                    record.setChargeType(chargeType);
                    //由于历史原因，会员充值最早版本是没有chargeFormItem的，记录的type = 2，在2021-11-10加上了chargeFormItem，为了兼容老数据，这个地方type还是写2，其他数据都写0
                    record.setType(chargeFormItem.getProductType() == Constants.ProductType.MEMBER_CARD_RECHARGE ? ChargeTransactionRecord.RecordType.MEMBER_CARD_RECHARGE : ChargeTransactionRecord.RecordType.PRODUCT);
                    record.setProductId(chargeFormItem.getProductId());
                    record.setChargeFormItemId(chargeFormItem.getId());
                    record.setProductType(chargeFormItem.getProductType());
                    record.setProductSubType(chargeFormItem.getProductSubType());
                    record.setComposeType(chargeFormItem.getComposeType());
                    record.setProductUnit(chargeFormItem.getUnit());
                    record.setUseDismounting(chargeFormItem.getUseDismounting());
                    record.setDoseCount(chargeFormItem.getDoseCount());
                    record.setTotalPrice(cell.getToRecordTotalPriceResult());
                    record.setDiscountPrice(cell.getToRecordDiscountPriceResult());
                    record.setProductUnitCount(cell.getToRecordUnitCountResult());
                    record.setTotalCostPrice(cell.getToRecordTotalCostPriceResult());
                    record.setSceneType(cell.getToRecordSceneType());
                    record.setFeeTypeId(chargeFormItem.getFeeTypeId());
                    record.setGoodsFeeType(chargeFormItem.getGoodsFeeType());
                    if (chargeVersion == ChargeVersionConstants.V1) {
                        record.setReceivedPrice(cell.getToRecordDiscountedPrice());
                        //反算sourceTotalPrice
                        record.setSourceTotalPrice(cell.getToRecordSourceTotalPriceResult());
                    }
                    record.setCreated(chargeTransaction.getCreated());
                    record.setCreatedBy(chargeTransaction.getCreatedBy());
                    record.setLastModified(chargeTransaction.getLastModified());
                    record.setLastModifiedBy(chargeTransaction.getLastModifiedBy());

                    ChargeTransactionRecordAdditional additional = new ChargeTransactionRecordAdditional();
                    additional.setId(record.getId());
                    additional.setPatientOrderId(record.getPatientOrderId());
                    additional.setChainId(record.getChainId());
                    additional.setClinicId(record.getClinicId());
                    additional.setChargeSheetId(record.getChargeSheetId());
                    additional.setDiscountInfo(cell.getToRecordDiscountInfoResult());
                    additional.setDeductTotalPrice(cell.getToRecordDeductPrice().negate());
                    additional.setDeductCount(cell.getToRecordDeductUnitCount());
                    additional.setDeductTotalCostPrice(cell.getToRecordDeductTotalCostPrice());
                    additional.setPharmacyType(chargeFormItem.getPharmacyType());
                    additional.setDeductInfo(cell.getDeductInfo());


                    additional.setCreated(chargeTransaction.getCreated());
                    additional.setCreatedBy(chargeTransaction.getCreatedBy());
                    additional.setLastModified(chargeTransaction.getLastModified());
                    additional.setLastModifiedBy(chargeTransaction.getLastModifiedBy());
                    record.setAdditional(additional);

                    if (CollectionUtils.isNotEmpty(cell.getBatchInfoCalculateCells()) && cell.canDealBatchInfo()) {
                        record.setBatchInfos(cell.getBatchInfoCalculateCells()
                                .stream()
                                .map(batchInfoCalculateCell -> batchInfoCalculateCell.ofChargeTransactionRecordBatchInfo(record))
                                .collect(Collectors.toList())
                        );
                    }

                    return record;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        List<ChargeTransactionRecord> airPharmacyRecords = calculateCells.stream()
                .filter(cell -> cell.getIsAirPharmacy() == 1)
                .map(cell -> {
                    ChargeForm chargeForm = airPharmacyChargeFormMap.get(cell.getId());

                    if (chargeForm == null) {
                        return null;
                    }
                    ChargeTransactionRecord record = new ChargeTransactionRecord();
                    record.setId(AbcIdUtils.getUUID());
                    record.setPatientOrderId(chargeTransaction.getPatientOrderId());
                    record.setChainId(chargeTransaction.getChainId());
                    record.setClinicId(chargeTransaction.getClinicId());
                    record.setChargeSheetId(chargeTransaction.getChargeSheetId());
                    record.setPatientId(chargeTransaction.getPatientId());
                    record.setTransactionId(chargeTransaction.getId());
                    record.setChargeType(chargeType);
                    record.setType(ChargeTransactionRecord.RecordType.AIR_PHARMACY);
                    record.setProductUnit("笔");
                    record.setTotalPrice(cell.getToRecordTotalPriceResult());
                    record.setDiscountPrice(cell.getToRecordDiscountPriceResult());
                    record.setProductUnitCount(cell.getToRecordUnitCountResult());
                    record.setFeeComposeType(GoodsConst.FeeComposeType.FEE_TYPE_NORMAL);
                    record.setFeeTypeId(GoodsConst.FeeTypeId.FEE_TYPE_ID_AIR_PHARMACY);

                    //处理空中药房的成本价
                    CreateOrderView createOrderView = createOrderViewMap.get(chargeForm.getId());
                    if (chargeType == ChargeTransactionRecord.ChargeType.CHARGED && createOrderView != null) {
                        record.setTotalCostPrice(createOrderView.getClinicTotalPrice());
                    } else if (chargeType == ChargeTransactionRecord.ChargeType.REFUND && CollectionUtils.isEmpty(cell.getHistoryRecords())) {
                        ChargeTransactionRecord chargeTransactionRecord = airPharmacyHistoryRecordsIdMap.getOrDefault(cell.getId(), new ArrayList<>()).stream().filter(r -> r.getChargeType() == ChargeTransactionRecord.ChargeType.CHARGED && r.getTotalCostPrice().compareTo(BigDecimal.ZERO) != 0).findFirst().orElse(null);
                        BigDecimal totalCostPrice = chargeTransactionRecord != null ? chargeTransactionRecord.getTotalCostPrice() : BigDecimal.ZERO;
                        record.setTotalCostPrice(totalCostPrice);
                    } else {
                        record.setTotalCostPrice(BigDecimal.ZERO);
                    }

                    record.setCreated(chargeTransaction.getCreated());
                    record.setCreatedBy(chargeTransaction.getCreatedBy());
                    record.setLastModified(chargeTransaction.getLastModified());
                    record.setLastModifiedBy(chargeTransaction.getLastModifiedBy());

                    ChargeTransactionRecordAdditional additional = new ChargeTransactionRecordAdditional();
                    additional.setId(record.getId());
                    additional.setPatientOrderId(record.getPatientOrderId());
                    additional.setChainId(record.getChainId());
                    additional.setClinicId(record.getClinicId());
                    additional.setChargeSheetId(record.getChargeSheetId());
                    additional.setChargeFormId(chargeForm.getId());
                    additional.setDiscountInfo(cell.getToRecordDiscountInfoResult());
                    additional.setDeductTotalPrice(cell.getToRecordDeductPrice().negate());
                    additional.setDeductCount(cell.getToRecordDeductUnitCount());
                    additional.setDeductTotalCostPrice(cell.getToRecordDeductTotalCostPrice());
                    additional.setPharmacyType(chargeForm.getPharmacyType());
                    additional.setDeductInfo(cell.getDeductInfo());
                    additional.setCreated(chargeTransaction.getCreated());
                    additional.setCreatedBy(chargeTransaction.getCreatedBy());
                    additional.setLastModified(chargeTransaction.getLastModified());
                    additional.setLastModifiedBy(chargeTransaction.getLastModifiedBy());

                    record.setAdditional(additional);

                    return record;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (chargeType == ChargeTransactionRecord.ChargeType.CHARGED) {
            airPharmacyRecords.forEach(record -> {
                CreateOrderView createOrderView = createOrderViewMap.getOrDefault(record.getAdditional().getChargeFormId(), null);
                record.getAdditional().setBisOrderId(Optional.ofNullable(createOrderView).map(CreateOrderView::getOrderId).orElse(null));
            });

            //找到最后一次退费的时候，在写bisOrderId时只对之后的数据生效
            Instant lastRefundRecordCreated = historyRecords.stream()
                    .filter(record -> record.getType() == ChargeTransactionRecord.RecordType.AIR_PHARMACY && record.getAdditional() != null)
                    .filter(record -> org.apache.commons.lang3.StringUtils.isEmpty(record.getAdditional().getBisOrderId())
                            && record.getChargeType() == ChargeTransactionRecord.ChargeType.REFUND)
                    .sorted(Comparator.comparing(ChargeTransactionRecord::getCreated).reversed())
                    .findFirst()
                    .map(ChargeTransactionRecord::getCreated)
                    .orElse(null);

            historyRecords.stream()
                    .filter(record -> record.getType() == ChargeTransactionRecord.RecordType.AIR_PHARMACY && record.getAdditional() != null)
                    .filter(record -> org.apache.commons.lang3.StringUtils.isEmpty(record.getAdditional().getBisOrderId())
                            && record.getChargeType() == ChargeTransactionRecord.ChargeType.PART_CHARGED)
                    .filter(record -> {
                        if (lastRefundRecordCreated == null) {
                            return true;
                        }
                        return record.getCreated().isAfter(lastRefundRecordCreated);
                    })
                    .forEach(record -> {
                        CreateOrderView createOrderView = createOrderViewMap.getOrDefault(record.getAdditional().getChargeFormId(), null);
                        record.getAdditional().setBisOrderId(Optional.ofNullable(createOrderView).map(CreateOrderView::getOrderId).orElse(null));
                        calculateResult.addToSaveRecords(Collections.singletonList(record));
                    });
        } else if (chargeType == ChargeTransactionRecord.ChargeType.REFUND) {
            airPharmacyRecords.stream()
                    .forEach(record -> {
                        List<ChargeTransactionRecord> historyAirPharmacyRecords = airPharmacyHistoryRecordsIdMap.getOrDefault(record.getAdditional().getChargeFormId(), new ArrayList<>());
                        ChargeTransactionRecord lastAirPharmacyRecord = historyAirPharmacyRecords.stream()
                                .sorted(Comparator.comparing(ChargeTransactionRecord::getCreated).reversed())
                                .findFirst().orElse(null);

                        record.getAdditional().setBisOrderId(Optional.ofNullable(lastAirPharmacyRecord)
                                .map(ChargeTransactionRecord::getAdditional)
                                .map(ChargeTransactionRecordAdditional::getBisOrderId).orElse(null));
                    });
        }

        resultRecords.addAll(airPharmacyRecords);
        calculateResult.addToSaveRecords(resultRecords);
        calculateResult.setLeftToRecordPrice(leftToRecordPrice);
        calculateResult.setAddedRecords(resultRecords);
        return calculateResult;
    }

    private static int convertToChargeType(int payType) {
        int chargeType;
        switch (payType) {
            case StatRecordProcessor.PayType.PAID:
                chargeType = ChargeTransactionRecord.ChargeType.CHARGED;
                break;
            case StatRecordProcessor.PayType.PARTED_PAID:
                chargeType = ChargeTransactionRecord.ChargeType.PART_CHARGED;
                break;
            case StatRecordProcessor.PayType.PAID_BACK:
            case StatRecordProcessor.PayType.PARTED_PAID_BACK:
                chargeType = ChargeTransactionRecord.ChargeType.REFUND;
                break;
            case StatRecordProcessor.PayType.REFUND:
            case StatRecordProcessor.PayType.PARTED_REFUND:
                chargeType = ChargeTransactionRecord.ChargeType.REFUND;
                break;
            default:
                chargeType = 0;
        }

        return chargeType;
    }

    /**
     * if (收费) {
     * //是否要保护套餐总的实收不能小于记录的金额
     * if (不是欠收) {
     * 数量 = 母项的数量;
     * 金额 = 子项的本次要记录的金额总和;
     * } else {
     * 数量 = 0;
     * 金额 = 子项的本次要记录的金额总和;
     * }
     * } else {
     * //判断金额是否未退完，historyRecords和本次要记录的所有子项，比较金额和数量是否全部退完
     * if (未退完) {
     * 记录为欠退
     * 数量 = 0;
     * 金额 = 子项的本次要记录的金额总和;
     * } else {
     * 数量 = 母项的数量;
     * 金额 = 子项的本次要记录的金额总和;
     * }
     * }
     *
     * @param chargeTransaction                 本次支付流水
     * @param parentChargeFormItems             所有套餐母项
     * @param composeChildrenChargeFormItemsMap 所有套餐母项对应的子项，包含子项退费的chargeFormItem
     * @param historyRecords                    历史的records，包含本次生成的records
     * @param thisTimeRecords                   本次生成的records
     * @param operatorId                        操作人id
     * @return
     */
    public static List<ChargeTransactionRecord> generateComposeChargeFormItemRecords(ChargeTransaction chargeTransaction,
                                                                                     List<ChargeFormItem> parentChargeFormItems,
                                                                                     Map<String, List<ChargeFormItem>> composeChildrenChargeFormItemsMap,
                                                                                     int payType, List<StatRecordAffectedDeductedItem> deductedItems,
                                                                                     List<ChargeTransactionRecord> historyRecords,
                                                                                     List<ChargeTransactionRecord> thisTimeRecords,
                                                                                     boolean needDealClinicShebaoCodeMatchCostPrice,
                                                                                     int chargeVersion,
                                                                                     String operatorId) {

        List<ChargeTransactionRecord> composeRecords = new ArrayList<>();

        if (CollectionUtils.isEmpty(parentChargeFormItems) || MapUtils.isEmpty(composeChildrenChargeFormItemsMap) || CollectionUtils.isEmpty(thisTimeRecords)) {
            return composeRecords;
        }

        //生成套餐的composeCells
        List<StatRecordByChooseCalculateCell> composeCells = generateComposeCalculateCells(parentChargeFormItems, composeChildrenChargeFormItemsMap, deductedItems, historyRecords, thisTimeRecords, chargeVersion);

        if (CollectionUtils.isEmpty(composeCells)) {
            return new ArrayList<>();
        }

        StatRecordByChooseCalculateHelper.calculateComposeCell(composeCells, needDealClinicShebaoCodeMatchCostPrice, payType, chargeVersion);

        //根据composeCells生成record
        List<ChargeTransactionRecord> resultRecords = composeCells.stream()
                .map(cell -> {
                    ChargeFormItem chargeFormItem = cell.getChargeFormItem();
                    if (chargeFormItem == null) {
                        return null;
                    }

                    ChargeTransactionRecord record = new ChargeTransactionRecord();
                    record.setId(AbcIdUtils.getUUID());
                    record.setPatientOrderId(chargeTransaction.getPatientOrderId());
                    record.setChainId(chargeTransaction.getChainId());
                    record.setClinicId(chargeTransaction.getClinicId());
                    record.setChargeSheetId(chargeTransaction.getChargeSheetId());
                    record.setPatientId(chargeTransaction.getPatientId());
                    record.setTransactionId(chargeTransaction.getId());
                    record.setChargeType(convertToChargeType(payType));
                    //由于历史原因，会员充值最早版本是没有chargeFormItem的，记录的type = 2，在2021-11-10加上了chargeFormItem，为了兼容老数据，这个地方type还是写2，其他数据都写0
                    record.setType(chargeFormItem.getProductType() == Constants.ProductType.MEMBER_CARD_RECHARGE ? ChargeTransactionRecord.RecordType.MEMBER_CARD_RECHARGE : ChargeTransactionRecord.RecordType.PRODUCT);
                    record.setProductId(chargeFormItem.getProductId());
                    record.setChargeFormItemId(chargeFormItem.getId());
                    record.setProductType(chargeFormItem.getProductType());
                    record.setProductSubType(chargeFormItem.getProductSubType());
                    record.setComposeType(chargeFormItem.getComposeType());
                    record.setFeeComposeType(chargeFormItem.getFeeComposeType());
                    record.setFeeTypeId(chargeFormItem.getFeeTypeId());
                    record.setGoodsFeeType(chargeFormItem.getGoodsFeeType());
                    record.setProductUnit(chargeFormItem.getUnit());
                    record.setUseDismounting(chargeFormItem.getUseDismounting());
                    record.setDoseCount(chargeFormItem.getDoseCount());
                    record.setTotalPrice(cell.getToRecordTotalPriceResult());
                    record.setDiscountPrice(cell.getToRecordDiscountPriceResult());
                    record.setProductUnitCount(cell.getToRecordUnitCountResult());
                    record.setTotalCostPrice(cell.getToRecordTotalCostPriceResult());
                    record.setSceneType(cell.getToRecordSceneType());
                    if (chargeVersion == ChargeVersionConstants.V1) {
                        record.setReceivedPrice(cell.getToRecordDiscountedPrice());
                        record.setSourceTotalPrice(cell.getToRecordSourceTotalPriceResult());
                    }
                    record.setCreated(chargeTransaction.getCreated());
                    record.setCreatedBy(chargeTransaction.getCreatedBy());
                    record.setLastModified(chargeTransaction.getLastModified());
                    record.setLastModifiedBy(chargeTransaction.getLastModifiedBy());

                    ChargeTransactionRecordAdditional additional = new ChargeTransactionRecordAdditional();
                    additional.setId(record.getId());
                    additional.setPatientOrderId(record.getPatientOrderId());
                    additional.setChainId(record.getChainId());
                    additional.setClinicId(record.getClinicId());
                    additional.setChargeSheetId(record.getChargeSheetId());
                    additional.setDiscountInfo(cell.getToRecordDiscountInfoResult());
                    additional.setDeductTotalPrice(cell.getToRecordDeductPrice().negate());
                    additional.setDeductCount(cell.getToRecordDeductUnitCount());
                    additional.setDeductTotalCostPrice(cell.getToRecordDeductTotalCostPrice());
                    additional.setPharmacyType(chargeFormItem.getPharmacyType());
                    additional.setDeductInfo(cell.getDeductInfo());
                    additional.setCreated(chargeTransaction.getCreated());
                    additional.setCreatedBy(chargeTransaction.getCreatedBy());
                    additional.setLastModified(chargeTransaction.getLastModified());
                    additional.setLastModifiedBy(chargeTransaction.getLastModifiedBy());

                    record.setAdditional(additional);

                    //将套餐子项的母项id设置为套餐的id
                    cell.getComposeChildrenCells()
                            .stream()
                            .flatMap(childCell -> childCell.getThisTimeRecords().stream())
                            .forEach(childRecord -> childRecord.setComposeParentRecordId(record.getId()));

                    return record;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return resultRecords;
    }

    private static List<StatRecordByChooseCalculateCell> generateComposeCalculateCells(List<ChargeFormItem> parentChargeFormItems,
                                                                                       Map<String, List<ChargeFormItem>> childChargeFormItemsMap,
                                                                                       List<StatRecordAffectedDeductedItem> deductedItems,
                                                                                       List<ChargeTransactionRecord> historyRecords,
                                                                                       List<ChargeTransactionRecord> thisTimeRecords,
                                                                                       int chargeVersion) {

        Map<String, StatRecordAffectedDeductedItem> deductedItemMap = Optional.ofNullable(deductedItems).orElse(new ArrayList<>())
                .stream().collect(Collectors.toMap(StatRecordAffectedDeductedItem::getId, Function.identity()));

        //本次要记录的套餐子项的recordMap
        Map<String, List<ChargeTransactionRecord>> thisTimeRecordChargeFormItemIdMap = thisTimeRecords.stream()
                .filter(record -> record.getType() != ChargeTransactionRecord.RecordType.AIR_PHARMACY)
                .filter(record -> org.apache.commons.lang3.StringUtils.isNotEmpty(record.getChargeFormItemId()))
                .collect(Collectors.groupingBy(ChargeTransactionRecord::getChargeFormItemId));

        Map<String, List<ChargeTransactionRecord>> historyRecordsIdMap = ListUtils.groupByKey(
                historyRecords.stream().filter(record -> record.getType() != ChargeTransactionRecord.RecordType.AIR_PHARMACY).collect(Collectors.toList()),
                ChargeTransactionRecord::getChargeFormItemId
        );
        Map<String, Map<String, ChargeFormItem>> composeChildrenChargeFormItemIdMap = childChargeFormItemsMap.entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue()
                        .stream()
                        .collect(Collectors.toMap(ChargeFormItem::getId, Function.identity(), (a, b) -> a))));

        return parentChargeFormItems.stream()
                .map(item -> {

                    //这个是套餐所有的子项，包含退费的
                    Map<String, ChargeFormItem> childrenChargeFormItemIdMap = composeChildrenChargeFormItemIdMap.getOrDefault(item.getId(), new HashMap<>());

                    if (MapUtils.isEmpty(childrenChargeFormItemIdMap)) {
                        return null;
                    }

                    Map<String, List<ChargeTransactionRecord>> thisTimeChildrenRecordMap = childrenChargeFormItemIdMap.keySet()
                            .stream()
                            .flatMap(childItemId -> thisTimeRecordChargeFormItemIdMap.getOrDefault(childItemId, new ArrayList<>()).stream())
                            .collect(Collectors.groupingBy(ChargeTransactionRecord::getChargeFormItemId));

                    //如果这个为空，表示本次收费或退费没有对这个套餐子项记录，那套餐母项也不记录数据
                    if (MapUtils.isEmpty(thisTimeChildrenRecordMap)) {
                        return null;
                    }

                    //将子项转换为key为已收的itemId， value为本身id或所有退费的itemId，是为了计算是否所有子项都退完了
                    Map<String, Set<String>> childrenItemIdMap = new HashMap<>();
                    childrenChargeFormItemIdMap.values()
                            .forEach(chargeFormItem -> {
                                String paidItemId = chargeFormItem.getId();
                                if (chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.REFUNDED && org.apache.commons.lang3.StringUtils.isNotEmpty(chargeFormItem.getAssociateFormItemId())) {
                                    paidItemId = chargeFormItem.getAssociateFormItemId();
                                }
                                childrenItemIdMap.computeIfAbsent(paidItemId, key -> new HashSet<>()).add(chargeFormItem.getId());
                            });


                    StatRecordByChooseCalculateCell composeCell = StatRecordByChooseCalculateHelper.generateStatRecordByChooseCalculateCell(item, historyRecordsIdMap.getOrDefault(item.getId(), new ArrayList<>()), chargeVersion);
                    composeCell.setComposeChildrenCells(new ArrayList<>());
                    composeCell.setDeductedItem(deductedItemMap.get(composeCell.getId()));
                    composeCell.setComposeChildrenItemPaidCells(childrenChargeFormItemIdMap.values()
                            .stream()
                            .filter(childItem -> childrenItemIdMap.keySet().contains(childItem.getId()))
                            .map(childItem -> {
                                Set<String> childItemIds = childrenItemIdMap.get(childItem.getId());
                                List<ChargeTransactionRecord> records = new ArrayList<>();
                                childItemIds.forEach(id -> records.addAll(historyRecordsIdMap.getOrDefault(id, new ArrayList<>())));
                                return StatRecordByChooseCalculateHelper.generateStatRecordByChooseCalculateCell(childItem, records, composeCell.getChargeVersion());
                            })
                            .collect(Collectors.toList()));

                    return composeCell.setComposeChildrenCells(childrenChargeFormItemIdMap.values()
                            .stream()
                            .map(childItem -> StatRecordByChooseCalculateHelper.generateStatRecordByChooseCalculateCell(childItem, historyRecordsIdMap.getOrDefault(childItem.getId(), new ArrayList<>()), composeCell.getChargeVersion()))
                            //将本次要记录的records绑定到套餐子项cell上，用于计算套餐母项的金额
                            .peek(childCell -> childCell.setThisTimeRecords(thisTimeChildrenRecordMap.getOrDefault(childCell.getId(), new ArrayList<>())))
                            .collect(Collectors.toList()));
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Data
    private static class CalculateResult {
        /**
         * 本次新增的records
         */
        private List<ChargeTransactionRecord> addedRecords;
        /**
         * 需要保存的records，这里包含addedRecords，还有可能有历史已存在的records
         */
        private List<ChargeTransactionRecord> toSaveRecords;
        private BigDecimal leftToRecordPrice;

        public void addToSaveRecords(List<ChargeTransactionRecord> addedToSaveRecords) {

            if (toSaveRecords == null) {
                toSaveRecords = new ArrayList<>();
            }

            if (CollectionUtils.isEmpty(addedToSaveRecords)) {
                return;
            }

            List<String> toSaveRecordIds = toSaveRecords.stream()
                    .map(ChargeTransactionRecord::getId)
                    .collect(Collectors.toList());

            addedToSaveRecords
                    .stream()
                    .filter(addedToSaveRecord -> !toSaveRecordIds.contains(addedToSaveRecord.getId()))
                    .forEach(addedToSaveRecord -> toSaveRecords.add(addedToSaveRecord));
        }
    }

    /**
     * 计算每个cell应该分配的金额
     *
     * @param calculateCellList
     * @param toRecordPrice
     * @param isLastRecord
     * @param isPaidback
     * @return 分不完了的情况，剩下的金额
     */
    public static BigDecimal calculate(List<cn.abcyun.cis.charge.processor.StatRecordCalculateCell> calculateCellList, BigDecimal toRecordPrice, boolean isLastRecord, boolean isPaidback, boolean isPaid) {
        if (calculateCellList == null || calculateCellList.size() == 0) {
            return toRecordPrice;
        }

        //优先处理抵扣
        calculateCellList.forEach(cell -> cell.dealDeductItem());

        //本次受影响的收费项的折后总金额
        BigDecimal discountedPriceSum = calculateCellList.stream().map(cn.abcyun.cis.charge.processor.StatRecordCalculateCell::getDiscountedPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        //本次已经记录了record表的折后总金额
        BigDecimal recordDiscountedPriceSum = calculateCellList.stream().map(cn.abcyun.cis.charge.processor.StatRecordCalculateCell::getRecordedDiscountedPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        //剩余需要记录到record表的折后总金额
        BigDecimal leftToRecordDiscountedPriceSum = discountedPriceSum.subtract(recordDiscountedPriceSum);

        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "discountedPriceSum: {}, recordDiscountedPriceSum: {}, leftToRecordDiscountedPriceSum: {}, isLastRecord: {}", discountedPriceSum, recordDiscountedPriceSum, leftToRecordDiscountedPriceSum, isLastRecord);

        //这是最后一发
        if ((MathUtils.wrapBigDecimalCompare(leftToRecordDiscountedPriceSum, toRecordPrice) <= 0 && !isPaidback) || isLastRecord) {
            calculateCellList.forEach(cell -> {
                if (isPaidback) {
                    cell.setToRecordDiscountedPrice(cell.getRecordedDiscountedPrice());
                    cell.setToRecordTotalCostPrice(cell.getRecordedTotalCostPrice());
                    cell.setToRecordUnitCount(cell.getRecordedUnitCount());
                    cell.setToRecordDiscountPrice(cell.getRecordedDiscountPrice());
                    cell.setToRecordPromotionDiscountInfo(cell.getRecordedChargeTransactionRecordDiscountInfo());
                } else {
                    cell.setToRecordDiscountedPrice(cell.getLeftToRecordDiscountedPrice());
                    cell.setToRecordTotalCostPrice(cell.getTotalCostPrice().subtract(cell.getRecordedTotalCostPrice()));
                    cell.setToRecordUnitCount(cell.getUnitCount().subtract(cell.getRecordedUnitCount()));
                    cell.setToRecordDiscountPrice(cell.getDiscountPrice().subtract(cell.getRecordedDiscountPrice()));
                    cell.setToRecordPromotionDiscountInfo(ChargeTransactionRecordDiscountInfoHelper.generateToRecordChargeTransactionRecordDiscountInfoForLastRecord(cell.getItemPromotionInfo(), cell.getRecordedChargeTransactionRecordDiscountInfo()));
                }
            });
            return isPaidback ? BigDecimal.ZERO : toRecordPrice.subtract(leftToRecordDiscountedPriceSum);
        }

        if (discountedPriceSum.compareTo(BigDecimal.ZERO) == 0) {
            return toRecordPrice;
        }

        //先按比例分一次
        BigDecimal leftToRecordPrice = toRecordPrice;
        for (cn.abcyun.cis.charge.processor.StatRecordCalculateCell cell : calculateCellList) {
            if (isPaid) {
                //收费的时候已经摊好了，直接用，退费由于涉及到部分退，欠退，情况很复杂，就在这儿做摊费逻辑，最后再把摊好的值回写到从每个收费项的receivedPrice中减去
                BigDecimal thisTimeReceivableFee = cell.getThisTimeReceivableFee();
                cell.setToRecordDiscountedPrice(thisTimeReceivableFee);
                leftToRecordPrice = leftToRecordPrice.subtract(thisTimeReceivableFee);
            } else {
                BigDecimal toRecordDiscountedPrice = toRecordPrice.multiply(cell.getDiscountedPrice()).divide(discountedPriceSum, 1, RoundingMode.DOWN);
                if (!isPaidback) {
                    toRecordDiscountedPrice = MathUtils.min(toRecordDiscountedPrice, cell.getLeftToRecordDiscountedPrice());
                } else {
                    toRecordDiscountedPrice = MathUtils.min(cell.getRecordedDiscountedPrice(), toRecordDiscountedPrice);
                }
                cell.setToRecordDiscountedPrice(toRecordDiscountedPrice);
                leftToRecordPrice = leftToRecordPrice.subtract(toRecordDiscountedPrice);
            }
        }


        //如果还没分配完，就从第一个开始安排
        for (cn.abcyun.cis.charge.processor.StatRecordCalculateCell cell : calculateCellList) {
            if (leftToRecordPrice.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }
            BigDecimal addToRecordDiscountedPrice = BigDecimal.ZERO;
            if (!isPaidback) {
                addToRecordDiscountedPrice = MathUtils.min(cell.getLeftToRecordDiscountedPrice(), leftToRecordPrice);
            } else {
                addToRecordDiscountedPrice = MathUtils.min(cell.getRecordedDiscountedPrice().subtract(cell.getToRecordDiscountedPrice()).abs(), leftToRecordPrice);
            }
            cell.setToRecordDiscountedPrice(cell.getToRecordDiscountedPrice().add(addToRecordDiscountedPrice));
            leftToRecordPrice = leftToRecordPrice.subtract(addToRecordDiscountedPrice);
        }

        //按比例处理其他项目
        for (cn.abcyun.cis.charge.processor.StatRecordCalculateCell cell : calculateCellList) {
            if (cell.getDiscountedPrice().compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }

            //这里不直接使用rate，是为了避免两次向下舍去，尽量减少精度的损失
//            BigDecimal rate = cell.getToRecordDiscountedPrice().divide(cell.getDiscountedPrice(), 2, RoundingMode.DOWN);

            cell.setToRecordUnitCount(cell.getUnitCount().multiply(cell.getToRecordDiscountedPrice()).divide(cell.getDiscountedPrice(), 2, RoundingMode.DOWN));
            cell.setToRecordTotalCostPrice(cell.getTotalCostPrice().multiply(cell.getToRecordDiscountedPrice()).divide(cell.getDiscountedPrice(), 2, RoundingMode.DOWN));
            cell.setToRecordDiscountPrice(cell.getDiscountPrice().multiply(cell.getToRecordDiscountedPrice()).divide(cell.getDiscountedPrice(), 2, RoundingMode.DOWN));
            cell.setToRecordPromotionDiscountInfo(ChargeTransactionRecordDiscountInfoHelper.generateToRecordChargeTransactionRecordDiscountInfo(cell.getItemPromotionInfo(), cell.getToRecordDiscountPrice(), cell.getToRecordDiscountedPrice(), cell.getDiscountedPrice(), false));
        }
        return leftToRecordPrice;
    }

    public static final class PayType {
        public static final int PAID = 0;
        public static final int PARTED_PAID = 1;
        public static final int PAID_BACK = 2;
        public static final int PARTED_PAID_BACK = 3;
        public static final int REFUND = 4;
        public static final int PARTED_REFUND = 5;

        public static boolean isPaid(int payType) {
            return payType == PAID || payType == PARTED_PAID;
        }

        public static boolean isPaidBack(int payType) {
            return payType == PAID_BACK || payType == PARTED_PAID_BACK;
        }
    }

    public static final class RefundFlatType {
        //所有退费项平摊
        public static final int ALL_FLAT = 0;

        //原始支付时的item优先摊，如果不够，再往剩下的item上面摊
        public static final int ORIGINAL_PAID_ITEM_FLAT = 1;
    }

    public List<ChargeTransactionRecord> generateImportChargeTransactionRecords() {
        if (chargeTransactions == null || chargeTransactions.size() == 0) {
            return new ArrayList<>();
        }

        List<ChargeTransactionRecord> historyTransactionRecords = chargeTransactionRecordService.listByChargeSheetIdAndIsOldRecord(chargeSheetId, 0);

        List<ChargeTransactionRecord> chargeTransactionRecords = generateChargeTransactionRecordsByChoose(historyTransactionRecords, new ArrayList<>());

        List<String> historyRecordIds = Optional.ofNullable(historyTransactionRecords).orElse(new ArrayList<>())
                .stream()
                .map(ChargeTransactionRecord::getId)
                .collect(Collectors.toList());

        ChargeTransaction chargeTransaction = chargeTransactions.get(0);

        //过滤掉历史recordIds，将剩下的recordIds设置为created和lastModified设置为transaction的创建时间
        Optional.ofNullable(chargeTransactionRecords).orElse(new ArrayList<>())
                .stream()
                .filter(chargeTransactionRecord -> !historyRecordIds.contains(chargeTransactionRecord.getId()))
                .forEach(chargeTransactionRecord -> {
                    chargeTransactionRecord.setCreated(chargeTransaction.getCreated());
                    chargeTransactionRecord.setLastModified(chargeTransaction.getCreated());
                });

        return Optional.ofNullable(chargeTransactionRecords).orElse(new ArrayList<>());
    }

}
