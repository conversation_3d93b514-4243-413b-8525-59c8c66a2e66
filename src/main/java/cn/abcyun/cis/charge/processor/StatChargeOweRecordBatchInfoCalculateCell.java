package cn.abcyun.cis.charge.processor;

import cn.abcyun.cis.charge.model.ChargeOweCombineTransactionRecordDetail;
import cn.abcyun.cis.charge.model.ChargeOweCombineTransactionRecordDetailBatchInfo;
import cn.abcyun.cis.charge.model.ChargeTransactionRecordBatchInfo;
import cn.abcyun.cis.charge.service.dto.ChargeDiscountInfo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@Accessors(chain = true)
public class StatChargeOweRecordBatchInfoCalculateCell {
    private String id;

    /**
     * 收当前record限制，所有的摊费的金额，都不可操作该对象的金额
     */
    private ChargeTransactionRecordBatchInfo limitedBatchInfo;

    private List<ChargeOweCombineTransactionRecordDetailBatchInfo> historyBatchInfos = new ArrayList<>();

    /**
     * 本次要记录进数据库的recordDetail，在计算套餐母项的时候才用到了
     */
    private List<ChargeOweCombineTransactionRecordDetail> thisTimeRecordDetails;

    //记录到record表的折扣后的金额
    private BigDecimal toRecordDiscountedPrice = BigDecimal.ZERO;
    //记录到record表的折扣金额
    private BigDecimal toRecordDiscountPrice = BigDecimal.ZERO;
    //记录到record表的数量
    private BigDecimal toRecordUnitCount = BigDecimal.ZERO;
    //记录到record表的成本价
    private BigDecimal toRecordTotalCostPrice = BigDecimal.ZERO;

    private BigDecimal toRecordDeductCount = BigDecimal.ZERO;

    private BigDecimal toRecordDeductedUnitAdjustmentFee = BigDecimal.ZERO;

    private BigDecimal toRecordDeductTotalCostPrice = BigDecimal.ZERO;

    private BigDecimal toRecordDeductTotalPrice = BigDecimal.ZERO;

    //服务抵扣
    private List<ChargeDiscountInfo.DeductDiscountInfo> toRecordDeductDiscountInfos;

    //优惠&议价
    private ChargeDiscountInfo toRecordPromotionDiscountInfo;

    private int toRecordSceneType;

//
//    //最终落地到record表的对象，是将toRecordPromotionDiscountInfo和toRecordDeductDiscountInfo组合出来的对象
//    public ChargeDiscountInfo getToRecordDiscountInfoResult() {
//
//        if (toRecordPromotionDiscountInfo == null && CollectionUtils.isEmpty(toRecordDeductDiscountInfos)) {
//            return null;
//        }
//
//        ChargeDiscountInfo result = new ChargeDiscountInfo();
//        Optional.ofNullable(toRecordPromotionDiscountInfo)
//                .ifPresent(c -> {
//                    BeanUtils.copyProperties(c, result);
//                    result.setDeductDiscountInfos(new ArrayList<>());
//                    result.setDeductedUnitAdjustmentFee(toRecordDeductedUnitAdjustmentFee);
//                    result.setUnitAdjustmentFee(MathUtils.wrapBigDecimalAdd(result.getDeductedUnitAdjustmentFee(), result.getUnitAdjustmentFeeIgnoreDeduct()));
//                });
//
//        Optional.ofNullable(toRecordDeductDiscountInfos).ifPresent(result::setDeductDiscountInfos);
//        return result;
//    }
//
//    public BigDecimal getToRecordDiscountPriceResult() {
//        return MathUtils.wrapBigDecimalAdd(getToRecordDiscountPrice(), getToRecordDeductTotalPrice().negate());
//    }
//
//    public BigDecimal getToRecordTotalCostPriceResult() {
//        return MathUtils.wrapBigDecimalAdd(toRecordTotalCostPrice, toRecordDeductTotalCostPrice);
//    }
//
//    public BigDecimal getRecordedDiscountedPrice() {
//        return historyRecordDetails.stream()
//                .map(recordDetail -> MathUtils.wrapBigDecimalAdd(recordDetail.getTotalPrice(), recordDetail.getDiscountPrice()))
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
//    }
//
//    public BigDecimal getLeftToRecordDiscountedPrice() {
//        return getLimitedDiscountedPrice().subtract(getRecordedDiscountedPrice());
//    }
//
//    public BigDecimal getLeftToRecordTotalCostPrice() {
//        return limitedRecord.getTotalCostPrice().subtract(getLimitedDeductTotalCostPrice())
//                .subtract(getRecordedTotalCostPrice().subtract(getRecordedDeductTotalCostPrice()));
//    }
//
//    public BigDecimal getRecordedTotalCostPrice() {
//        return historyRecordDetails.stream()
//                .map(recordDetail -> MathUtils.wrapBigDecimalOrZero(recordDetail.getTotalCostPrice()))
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
//    }
//
//    public BigDecimal getLeftToRecordDiscountPrice() {
//        return getLimitedDiscountPrice().subtract(getLimitedDeductTotalPrice().negate())
//                .subtract(getRecordedDiscountPrice().subtract(getRecordedDeductTotalPrice().negate()));
//    }
//
//    private BigDecimal getRecordedDiscountPrice() {
//        return historyRecordDetails.stream()
//                .map(recordDetail -> MathUtils.wrapBigDecimalOrZero(recordDetail.getDiscountPrice()))
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
//    }
//
//    public ChargeDiscountInfo getLimitedRecordDiscountInfo() {
//        return Optional.ofNullable(limitedRecord.getAdditional())
//                .map(ChargeTransactionRecordAdditional::getDiscountInfo)
//                .map(recordDiscountInfo -> {
//                    ChargeDiscountInfo itemPromotionInfo = new ChargeDiscountInfo();
//                    BeanUtils.copyProperties(recordDiscountInfo, itemPromotionInfo);
//                    return itemPromotionInfo;
//                })
//                .orElse(null);
//    }
//
//    public ChargeDiscountInfo getRecordedDiscountInfo() {
//
//        List<ChargeDiscountInfo> recordDiscountInfos = historyRecordDetails.stream()
//                .filter(recordDetail -> Objects.nonNull(recordDetail.getDiscountInfo()))
//                .map(ChargeOweCombineTransactionRecordDetail::getDiscountInfo)
//                .collect(Collectors.toList());
//
//        return ChargeTransactionRecordDiscountInfoHelper.mergeChargeTransactionRecordDiscountInfo(recordDiscountInfos);
//    }
//
//    public BigDecimal getLimitedDiscountedPrice() {
//        return MathUtils.wrapBigDecimalAdd(limitedRecord.getTotalPrice(), limitedRecord.getDiscountPrice());
//    }
//
//    public BigDecimal getLimitedTotalCostPrice() {
//        return MathUtils.wrapBigDecimalOrZero(limitedRecord.getTotalCostPrice());
//    }
//
//    public BigDecimal getLimitedDiscountPrice() {
//        return MathUtils.wrapBigDecimalOrZero(limitedRecord.getDiscountPrice());
//    }
//
//    public BigDecimal getLimitedDeductTotalPrice() {
//        return Optional.ofNullable(limitedRecord.getAdditional())
//                .map(ChargeTransactionRecordAdditional::getDeductTotalPrice)
//                .orElse(BigDecimal.ZERO);
//    }
//
//    private BigDecimal getRecordedDeductTotalPrice() {
//        return historyRecordDetails.stream()
//                .map(recordDetail -> MathUtils.wrapBigDecimalOrZero(recordDetail.getDeductTotalPrice()))
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
//    }
//
//    public BigDecimal getLeftToRecordDeductTotalPrice() {
//        return getLimitedDeductTotalPrice().subtract(getRecordedDeductTotalPrice());
//    }
//
//
//    public BigDecimal getLeftToRecordDeductCount() {
//        return getLimitedDeductCount().subtract(getRecordedDeductCount());
//    }
//
//    public BigDecimal getLeftToRecordDeductTotalCostPrice() {
//        return getLimitedDeductTotalCostPrice().subtract(getRecordedDeductTotalCostPrice());
//    }
//
//    private BigDecimal getRecordedDeductTotalCostPrice() {
//        return historyRecordDetails.stream()
//                .map(recordDetail -> MathUtils.wrapBigDecimalOrZero(recordDetail.getDeductTotalCostPrice()))
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
//    }
//
//    public BigDecimal getLimitedDeductTotalCostPrice() {
//        return Optional.ofNullable(limitedRecord.getAdditional())
//                .map(ChargeTransactionRecordAdditional::getDeductTotalCostPrice)
//                .orElse(BigDecimal.ZERO);
//    }
//
//    private BigDecimal getRecordedDeductCount() {
//        return historyRecordDetails.stream()
//                .map(recordDetail -> MathUtils.wrapBigDecimalOrZero(recordDetail.getDeductCount()))
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
//    }
//
//    public BigDecimal getLimitedDeductCount() {
//        return Optional.ofNullable(limitedRecord.getAdditional())
//                .map(ChargeTransactionRecordAdditional::getDeductCount)
//                .orElse(BigDecimal.ZERO);
//    }
//
//    public List<ChargeDiscountInfo.DeductDiscountInfo> getLimitedDeductDiscountInfo() {
//        return Optional.ofNullable(limitedRecord.getAdditional())
//                .map(ChargeTransactionRecordAdditional::getDiscountInfo)
//                .map(ChargeDiscountInfo::getDeductDiscountInfos)
//                .orElse(new ArrayList<>());
//    }
//
//    public BigDecimal getLeftToRecordUnitCount() {
//        return getLimitedUnitCount().subtract(getRecordedUnitCount());
//    }
//
//    public BigDecimal getRecordedUnitCount() {
//        return historyRecordDetails.stream()
//                .map(recordDetail -> MathUtils.wrapBigDecimalOrZero(recordDetail.getProductUnitCount()))
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
//    }
//
//    public BigDecimal getLimitedUnitCount() {
//        return MathUtils.wrapBigDecimalOrZero(limitedRecord.getProductUnitCount());
//    }
//
//
//    public ChargeOweCombineTransactionRecordDetail ofChargeOweCombineTransactionRecordDetail(ChargeOweCombineTransactionRecord record, boolean isFirstPay, int chargeType, String operatorId) {
//        ChargeOweCombineTransactionRecordDetail recordDetail = new ChargeOweCombineTransactionRecordDetail();
//        recordDetail.setId(AbcIdUtils.getUID())
//                .setPatientOrderId(limitedRecord.getPatientOrderId())
//                .setChainId(record.getChainId())
//                .setClinicId(record.getClinicId())
//                .setChargeSheetId(limitedRecord.getChargeSheetId())
//                .setOweSheetId(String.valueOf(record.getOweSheetId()))
//                .setPatientId(limitedRecord.getPatientId())
//                .setTransactionId(String.valueOf(record.getOweCombineTransactionId()))
//                .setTransactionRecordId(String.valueOf(record.getId()))
//                .setChargeType(chargeType)
//                .setProductId(limitedRecord.getProductId())
//                .setExpenseItemId(id)
//                .setType(limitedRecord.getType() != ChargeTransactionRecord.RecordType.AIR_PHARMACY ? ChargeTransactionRecord.RecordType.PRODUCT : ChargeTransactionRecord.RecordType.AIR_PHARMACY)
//                .setProductType(limitedRecord.getProductType())
//                .setProductSubType(limitedRecord.getProductSubType())
//                .setComposeType(limitedRecord.getComposeType())
//                .setProductUnit(limitedRecord.getProductUnit())
//                .setSceneType(isFirstPay ? limitedRecord.getSceneType() : ChargeTransactionRecord.SceneType.OWE_PAY)
//                .setProductUnitCount(recordDetail.getSceneType() == ChargeTransactionRecord.SceneType.NORMAL ? limitedRecord.getProductUnitCount() : BigDecimal.ZERO)
//                .setUsage(limitedRecord.getUsage())
//                .setDoseCount(limitedRecord.getDoseCount())
//                .setDiscountPrice(getToRecordDiscountPriceResult())
//                .setTotalPrice(toRecordDiscountedPrice.subtract(getToRecordDiscountPriceResult()))
//                .setTotalCostPrice(getToRecordTotalCostPriceResult())
//                .setIsDismounting(limitedRecord.getUseDismounting())
//                .setIsOldRecord(0)
//                .setDiscountInfo(getToRecordDiscountInfoResult())
//                .setDeductTotalPrice(toRecordDeductTotalPrice)
//                .setDeductCount(toRecordDeductCount)
//                .setDeductTotalCostPrice(toRecordDeductTotalCostPrice)
//                .setFeeComposeType(limitedRecord.getFeeComposeType())
//                .setFeeTypeId(limitedRecord.getFeeTypeId())
//                .setGoodsFeeType(limitedRecord.getGoodsFeeType())
//                .setPharmacyType(Optional.ofNullable(limitedRecord.getAdditional()).map(ChargeTransactionRecordAdditional::getPharmacyType).orElse(0))
//                .setBisOrderId(Optional.ofNullable(limitedRecord.getAdditional()).map(ChargeTransactionRecordAdditional::getBisOrderId).orElse(null));
//
//        FillUtils.fillCreatedBy(recordDetail, operatorId);
//        return recordDetail;
//    }
//
//    public ChargeOweCombineTransactionRecordDetail ofComposeChargeOweCombineTransactionRecordDetail(ChargeOweCombineTransactionRecord record, boolean isFirstPay, int chargeType, String operatorId) {
//        ChargeOweCombineTransactionRecordDetail recordDetail = new ChargeOweCombineTransactionRecordDetail();
//        String composeRecordDetailId = AbcIdUtils.getUID();
//        recordDetail.setId(composeRecordDetailId)
//                .setPatientOrderId(limitedRecord.getPatientOrderId())
//                .setChainId(record.getChainId())
//                .setClinicId(record.getClinicId())
//                .setChargeSheetId(limitedRecord.getChargeSheetId())
//                .setOweSheetId(String.valueOf(record.getOweSheetId()))
//                .setPatientId(limitedRecord.getPatientId())
//                .setTransactionId(String.valueOf(record.getOweCombineTransactionId()))
//                .setTransactionRecordId(String.valueOf(record.getId()))
//                .setChargeType(chargeType)
//                .setProductId(limitedRecord.getProductId())
//                .setExpenseItemId(id)
//                .setType(limitedRecord.getType() != ChargeTransactionRecord.RecordType.AIR_PHARMACY ? ChargeTransactionRecord.RecordType.PRODUCT : ChargeTransactionRecord.RecordType.AIR_PHARMACY)
//                .setProductType(limitedRecord.getProductType())
//                .setProductSubType(limitedRecord.getProductSubType())
//                .setComposeType(limitedRecord.getComposeType())
//                .setProductUnit(limitedRecord.getProductUnit())
//                .setSceneType(isFirstPay ? limitedRecord.getSceneType() : ChargeTransactionRecord.SceneType.OWE_PAY)
//                .setProductUnitCount(recordDetail.getSceneType() == ChargeTransactionRecord.SceneType.NORMAL ? limitedRecord.getProductUnitCount() : BigDecimal.ZERO)
//                .setUsage(limitedRecord.getUsage())
//                .setDoseCount(limitedRecord.getDoseCount())
//                .setDiscountPrice(toRecordDiscountPrice)
//                .setTotalPrice(toRecordDiscountedPrice.subtract(toRecordDiscountPrice))
//                .setTotalCostPrice(toRecordTotalCostPrice)
//                .setIsDismounting(limitedRecord.getUseDismounting())
//                .setIsOldRecord(0)
//                .setDiscountInfo(toRecordPromotionDiscountInfo)
//                .setDeductTotalPrice(toRecordDeductTotalPrice)
//                .setDeductCount(toRecordDeductCount)
//                .setDeductTotalCostPrice(toRecordDeductTotalCostPrice)
//                .setFeeTypeId(limitedRecord.getFeeTypeId())
//                .setFeeComposeType(limitedRecord.getFeeComposeType())
//                .setGoodsFeeType(limitedRecord.getGoodsFeeType())
//                .setPharmacyType(Optional.ofNullable(limitedRecord.getAdditional()).map(ChargeTransactionRecordAdditional::getPharmacyType).orElse(0));
//
//        FillUtils.fillCreatedBy(recordDetail, operatorId);
//
//        //套餐子项的recordDetail设置套餐母项的id
//        thisTimeRecordDetails.forEach(child -> child.setComposeParentTransactionRecordDetailId(composeRecordDetailId));
//        return recordDetail;
//    }
//
//    public int getProductType() {
//        return limitedRecord.getProductType();
//    }
//
//    public BigDecimal getLimitedDeductedUnitAdjustmentFee() {
//        return Optional.ofNullable(limitedRecord.getAdditional())
//                .map(ChargeTransactionRecordAdditional::getDiscountInfo)
//                .map(ChargeDiscountInfo::getDeductedUnitAdjustmentFee)
//                .orElse(BigDecimal.ZERO);
//    }
}
