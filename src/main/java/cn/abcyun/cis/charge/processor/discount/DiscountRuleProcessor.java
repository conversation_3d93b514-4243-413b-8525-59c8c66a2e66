package cn.abcyun.cis.charge.processor.discount;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsItem;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsTypeTuple;
import cn.abcyun.bis.rpc.sdk.cis.model.promotion.Promotion;
import cn.abcyun.cis.charge.processor.PromotionQueryGiftGoodsDto;
import cn.abcyun.cis.charge.service.dto.PromotionProductItem;
import cn.abcyun.cis.charge.service.dto.PromotionView;
import cn.abcyun.cis.charge.service.dto.SinglePromotionView;
import cn.abcyun.cis.charge.util.MathUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.http.util.Asserts;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

public abstract class DiscountRuleProcessor {
    protected Promotion promotion;

    public DiscountRuleProcessor(Promotion promotion) {
        this.promotion = promotion;
    }

    public abstract boolean isAvailableRuleForItem(CalculatePromotionItem item);

    public abstract boolean isAvailableRuleForSingleItem(CalculateSinglePromotionItem item);

    public abstract boolean isAvailableRuleForAirPharmacyForm(CalculatePromotionAirPharmacyForm item);

    public abstract void applyBestDiscount(List<CalculatePromotionItem> items, List<CalculatePromotionAirPharmacyForm> airPharmacyForms);

    public abstract PromotionView generatePromotionView(List<CalculatePromotionItem> items, List<CalculatePromotionAirPharmacyForm> airPharmacyForms);

    public abstract SinglePromotionView generateSinglePromotionView(CalculateSinglePromotionItem item, Map<String, GoodsItem> singlePromotionGiftGoodsItemMap);

    public abstract PromotionQueryGiftGoodsDto generatePromotionQueryGiftGoodsDto(CalculateSinglePromotionItem item);

    public abstract PromotionQueryGiftGoodsDto generateOnComingPromotionQueryGiftGoodsDto(CalculateSinglePromotionItem item);

    /**
     * 更新规则的限购数量
     * @param singlePromotionItem
     * @param checkedSinglePromotion
     */
    public abstract void updateLeftSaleCount(CalculateSinglePromotionItem singlePromotionItem, SinglePromotionView checkedSinglePromotion);

    public String getPromotionId() {
        if (promotion == null) {
            return null;
        }
        return promotion.getId();
    }

    protected void applyDiscountCore(CalculatePromotionItem item, Map<String, Promotion.PromotionDetail.PromotionGoods> promotionGoodItemMap) {
        if (promotionGoodItemMap == null || item == null) {
            return;
        }

        PromotionSimple existedMaxDiscountPromotionSimple = null;
        if (!CollectionUtils.isEmpty(item.getPromotions())) {
            existedMaxDiscountPromotionSimple = item.getPromotions().stream()
                    .filter(promotion -> Promotion.Type.NORMAL_DISCOUNT == promotion.getType()
                            || Promotion.Type.MEMBER_DISCOUNT == promotion.getType()
                            || (Promotion.Type.CARD == promotion.getType() && PromotionSimple.SubType.DISCOUNT_FOR_DEDUCT != promotion.getSubType()))
                    .findFirst().orElse(null);
        }

        Promotion.PromotionDetail.PromotionGoods promotionGoodItem = promotionGoodItemMap.getOrDefault(item.getPharmacyTypeGoodsIdKey(), null);

        if (promotionGoodItem == null) {
            return;
        }

        Pair<Boolean, BigDecimal> needAddPromotionPair = checkNeedAddPromotion(existedMaxDiscountPromotionSimple, promotionGoodItem, item);

        if (needAddPromotionPair.getLeft()) {
            PromotionSimple promotionSimple = new PromotionSimple();
            promotionSimple.setPromotionId(promotion.getId());
            promotionSimple.setPromotionName(promotion.getName());
            promotionSimple.setType(promotion.getType());
            promotionSimple.setSubType(promotionGoodItem.getDiscountType());
            promotionSimple.setIsFixedPrice(promotionGoodItem.getIsFixedPrice());
            promotionSimple.setDiscountPrice(needAddPromotionPair.getRight());
            Optional.ofNullable(existedMaxDiscountPromotionSimple).ifPresent(p -> {
                item.getPromotions().remove(p);
                item.setDiscountPrice(MathUtils.wrapBigDecimalSubtract(item.getDiscountPrice(), p.getDiscountPrice()));

            });
            item.addPromotion(promotionSimple);
            item.setDiscountPrice(MathUtils.wrapBigDecimalAdd(item.getDiscountPrice(), needAddPromotionPair.getRight()));
        }
    }

    private Pair<Boolean, BigDecimal> checkNeedAddPromotion(PromotionSimple existedMaxDiscountPromotionSimple, Promotion.PromotionDetail.PromotionGoods promotionGoodItem, CalculatePromotionItem item) {

        if (promotionGoodItem == null) {
            return Pair.of(false, null);
        }

        //计算折扣值
        BigDecimal discount = BigDecimal.ZERO;
        if (promotionGoodItem.getIsFixedPrice() == 1) {
            BigDecimal fixedPrice = promotionGoodItem.getFixedPrice() != null ? promotionGoodItem.getFixedPrice() : item.getCanDiscountTotalPrice();
            BigDecimal fixedTotalPrice = MathUtils.calculateTotalPrice(fixedPrice, item.getCanDiscountTotalCount(), 2);

            //保护一下特价值异常的情况
            fixedTotalPrice = fixedTotalPrice.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : fixedTotalPrice;

            //特价值大于等于了需要金额，不需要应用特价
            if (fixedTotalPrice.compareTo(item.getCanDiscountTotalPrice()) < 0) {
                discount = fixedTotalPrice.subtract(item.getCanDiscountTotalPrice());
            }
        } else {
            discount = calculateDiscountPrice(promotionGoodItem.getDiscount(), item.getCanDiscountTotalPrice());
        }

        if (existedMaxDiscountPromotionSimple == null) {
            return Pair.of(true, discount);
        }

        //如果类型一致就比较金额，谁小返回谁
        if (existedMaxDiscountPromotionSimple.getSubType() == promotionGoodItem.getDiscountType()) {

            if (discount.compareTo(MathUtils.wrapBigDecimalOrZero(existedMaxDiscountPromotionSimple.getDiscountPrice())) < 0) {
                return Pair.of(true, discount);
            }
            return Pair.of(false, null);
        }

        //如果本次折扣是单品，则返回更新折扣
        if (promotionGoodItem.getDiscountType() == PromotionSimple.SubType.DISCOUNT_FOR_GOODS && discount.compareTo(BigDecimal.ZERO) < 0) {

            return Pair.of(true, discount);
        }

        //如果已经应用的折扣是单品，则不需要更新折扣
        return Pair.of(false, null);
    }

    //promotion 优惠打折信息，会员优惠&普通优惠，items非空中药房计算的待要计算优惠的列表
    public boolean isAvailableRuleItem(List<CalculatePromotionItem> items) {
        if (promotion == null || items == null || items.size() == 0) {
            return false;
        }

        return items.stream().anyMatch(this::isAvailableRuleForItem);
    }


    public boolean isAvailableRuleAirPharmacyForm(List<CalculatePromotionAirPharmacyForm> airPharmacyForms) {
        if (promotion == null || airPharmacyForms == null || airPharmacyForms.size() == 0) {
            return false;
        }

        return airPharmacyForms.stream().filter(form -> isAvailableRuleForAirPharmacyForm(form)).count() > 0;
    }

    public boolean isAvailableRule(List<CalculatePromotionItem> items, List<CalculatePromotionAirPharmacyForm> airPharmacyForms) {

        return isAvailableRuleItem(items) || isAvailableRuleAirPharmacyForm(airPharmacyForms);
    }

    public static BigDecimal calculateDiscountPrice(BigDecimal discount, BigDecimal totalPrice) {
        BigDecimal discountRate = BigDecimal.ONE.subtract(MathUtils.wrapBigDecimal(discount, BigDecimal.ONE)).setScale(4, RoundingMode.HALF_UP);

        //这里保护一下，其它服务可能把大于1和小于0的折扣传过来
        if (discountRate.compareTo(BigDecimal.ZERO) < 0) {
            discountRate = BigDecimal.ZERO;
        } else if (discountRate.compareTo(BigDecimal.ONE) > 0) {
            discountRate = BigDecimal.ONE;
        }
        //折扣取地板数
        return MathUtils.wrapBigDecimalOrZero(totalPrice).multiply(discountRate).setScale(2, RoundingMode.FLOOR).negate();
    }

    /**
     * 计算需要循环的数量
     * @param isCycle 是否需要循环
     * @param totalCount 总数量
     * @param thresholdCount 循环的单位数量
     * @return 总的循环次数
     */
    public static BigDecimal calculateCycleCount(boolean isCycle, BigDecimal totalCount, BigDecimal thresholdCount) {

        Asserts.notNull(totalCount, "totalCount is null");
        Asserts.notNull(thresholdCount, "thresholdCount is null");
        Asserts.check(thresholdCount.compareTo(BigDecimal.ZERO) > 0, "thresholdCount is less than 0");

        if (MathUtils.wrapBigDecimalCompare(totalCount, thresholdCount) < 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal cycleCount = BigDecimal.ONE;
        if (isCycle) {
            cycleCount = totalCount.divide(thresholdCount, 0, RoundingMode.DOWN);
        }

        cycleCount = MathUtils.min(totalCount, cycleCount);
        return cycleCount;

    }

    protected void applyDiscountForAirPharmacyForm(Map<String, GoodsTypeTuple> goodsTypeTupleMap, CalculatePromotionAirPharmacyForm form) {

        if (CollectionUtils.isEmpty(goodsTypeTupleMap) || form == null) {
            return;
        }
        BigDecimal discount = BigDecimal.ZERO;

        GoodsTypeTuple goodsTypeTuple = goodsTypeTupleMap.getOrDefault(CalculatePromotionAirPharmacyForm.convertToKey(form), null);
        if (goodsTypeTuple != null) {
            discount = calculateDiscountPrice(goodsTypeTuple.getDiscount(), form.getCanDiscountTotalPrice());
        }

        if (discount.compareTo(BigDecimal.ZERO) < 0
                && (form.getDiscountPrice() == null  //第一次
                || discount.compareTo(form.getDiscountPrice()) < 0)) { //本次优惠粒度更大

            PromotionSimple promotionSimple = new PromotionSimple();
            promotionSimple.setPromotionId(promotion.getId());
            promotionSimple.setPromotionName(promotion.getName());
            promotionSimple.setType(promotion.getType());
            promotionSimple.setSubType(PromotionSimple.SubType.DISCOUNT_FOR_TYPE);
            promotionSimple.setDiscountPrice(discount);
            List<PromotionSimple> promotions = new ArrayList<>();
            promotions.add(promotionSimple);

            form.setPromotions(promotions);
            form.setDiscountPrice(discount);
        }
    }

    protected List<PromotionProductItem> getUsedPromotionProductItems(List<CalculatePromotionItem> items) {

        if (items == null) {
            items = new ArrayList<>();
        }

        List<PromotionProductItem> usedPromotionProductItems = items.stream()
                .filter(item -> !CollectionUtils.isEmpty(item.getPromotions()))
                .filter(item -> {
                    List<String> promotionIds = item.getPromotions().stream().map(PromotionSimple::getPromotionId).collect(Collectors.toList());
                    return promotionIds.contains(promotion.getId());
                })
                .map(item -> generatePromotionProductItem(item.getPromotions(), item.getId(), item.getDisplayName(), promotion.getId()))
                .filter(promotionProductItem -> MathUtils.wrapBigDecimalCompare(promotionProductItem.getDiscountPrice(), BigDecimal.ZERO) != 0)
                .collect(Collectors.toList());

        return usedPromotionProductItems;
    }

    protected List<PromotionProductItem> getUsedPromotionProductItemsForAirPharmacy(List<CalculatePromotionAirPharmacyForm> airPharmacyForms) {

        if (airPharmacyForms == null) {
            airPharmacyForms = new ArrayList<>();
        }

        List<PromotionProductItem> promotionProductItems = airPharmacyForms.stream()
                .filter(form -> !CollectionUtils.isEmpty(form.getPromotions()))
                .filter(form -> {
                    List<String> promotionIds = form.getPromotions().stream().map(PromotionSimple::getPromotionId).collect(Collectors.toList());
                    return promotionIds.contains(promotion.getId());
                })
                .map(form -> generatePromotionProductItem(form.getPromotions(), form.getId(), form.getName(), promotion.getId()))
                .collect(Collectors.toList());

        return promotionProductItems;
    }

    private static PromotionProductItem generatePromotionProductItem(List<PromotionSimple> promotionSimples, String id, String name, String promotionId) {
        Map<String, PromotionSimple> promotionSimpleMap = promotionSimples.stream()
                .filter(promotionSimple -> (promotionSimple.getType() == PromotionSimple.Type.MEMBER_DISCOUNT
                        || promotionSimple.getType() == PromotionSimple.Type.NORMAL_DISCOUNT
                        || promotionSimple.getType() == PromotionSimple.Type.CARD)
                        && promotionSimple.getSubType() != null
                        && promotionSimple.getSubType() != PromotionSimple.SubType.DISCOUNT_FOR_DEDUCT)
                .collect(Collectors.toMap(PromotionSimple::getPromotionId, Function.identity(), (a, b) -> a));

        PromotionSimple promotionSimple = promotionSimpleMap.get(promotionId);

        PromotionProductItem promotionProductItem = new PromotionProductItem();
        promotionProductItem.setId(id);
        promotionProductItem.setName(name);

        if (promotionSimple != null) {
            promotionProductItem.setDiscountPrice(promotionSimple.getDiscountPrice());
        }

        return promotionProductItem;
    }
}
