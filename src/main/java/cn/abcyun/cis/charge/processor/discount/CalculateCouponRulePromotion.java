package cn.abcyun.cis.charge.processor.discount;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class CalculateCouponRulePromotion {

    //活动id
    private String promotionId;

    private String promotionName;


    @Deprecated
    private List<PromotionCouponView> couponViews;

    //最多可使用的数量
    private int availableCount;

    //当前使用的数量
    private Integer currentCount;

    //负数
    private BigDecimal discountPrice;

    private int isResetExpectedChecked;

    //是否为原价使用的优惠券
    private int onlyOriginalPrice;

    private List<String> chargeFormItemIds;

    private List<String> airPharmacyChargeFormIds;

    @Data
    public static class PromotionCouponView{
        //优惠券id
        private String couponId;

        //优惠金额，负数
        private BigDecimal discountPrice = BigDecimal.ZERO;

        //是否用完
        private int isFullUsed = 1;
    }
}
