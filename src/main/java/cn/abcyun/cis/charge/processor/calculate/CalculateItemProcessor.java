package cn.abcyun.cis.charge.processor.calculate;

import cn.abcyun.bis.rpc.sdk.cis.model.charge.GoodsFeeType;
import cn.abcyun.cis.charge.api.model.BasicCalculateItemBatchInfoReq;
import cn.abcyun.cis.charge.api.model.BasicCalculateItemReq;
import cn.abcyun.cis.charge.api.model.BasicCalculateItemRsp;
import cn.abcyun.cis.charge.processor.ExpectedPriceHelper;
import cn.abcyun.cis.charge.processor.FlatPriceHelper;
import cn.abcyun.cis.charge.util.ChargeFormItemUtils;
import cn.abcyun.cis.charge.util.FlatPriceTool;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.cis.commons.model.ComposeType;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class CalculateItemProcessor {

    private final BasicCalculateItemReq itemReq;

    private final CalculateModel calculateModel;

    private List<CalculateItemBatchInfoProcessor> itemBatchInfoProcessors = new ArrayList<>();

    private List<CalculateItemProcessor> composeChildrenProcessors = new ArrayList<>();

    private int isExpectedPriceCleared = 0;

    public BasicCalculateItemReq getItemReq() {
        return itemReq;
    }

    public boolean isComposeOrFeeParent() {
        return (itemReq.getComposeType() == ComposeType.COMPOSE || itemReq.getGoodsFeeType() == GoodsFeeType.FEE_PARENT)
                && CollectionUtils.isNotEmpty(itemReq.getComposeChildren());
    }

    public CalculateItemProcessor(BasicCalculateItemReq itemReq, CalculateModel calculateModel) {
        this.itemReq = itemReq;
        this.calculateModel = calculateModel;
        if (isComposeOrFeeParent()) {
            composeChildrenProcessors = itemReq.getComposeChildren().stream()
                    .map(composeChildrenItemReq -> {
                        if (itemReq.getComposeType() == ComposeType.COMPOSE) {
                            composeChildrenItemReq.setComposeType(ComposeType.COMPOSE_SUB_ITEM);
                        }
                        if (itemReq.getGoodsFeeType() == GoodsFeeType.FEE_PARENT) {
                            composeChildrenItemReq.setGoodsFeeType(GoodsFeeType.FEE_CHILD);
                        }
                        return new CalculateItemProcessor(composeChildrenItemReq, calculateModel);
                    })
                    .collect(Collectors.toList());
        }

        if (CollectionUtils.isNotEmpty(itemReq.getBatchInfos())) {

            //校验批次的数量与item的数量是否一致
            BigDecimal batchTotalCount = itemReq.getBatchInfos()
                    .stream()
                    .map(BasicCalculateItemBatchInfoReq::getUnitCount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            if (MathUtils.wrapBigDecimalCompare(batchTotalCount, MathUtils.calculateTotalCount(itemReq.getUnitCount(), itemReq.getDoseCount())) == 0) {
                itemBatchInfoProcessors = itemReq.getBatchInfos()
                        .stream()
                        .map(batchInfoReq -> new CalculateItemBatchInfoProcessor(batchInfoReq, calculateModel))
                        .collect(Collectors.toList());
            } else {
                itemReq.setBatchInfos(new ArrayList<>());
            }
        }
    }

    //计算item本身的议价值，得到当前item的价格
    public void calculateItemExpectedPrice() {

        if (itemReq.sourceUnitPriceChanged()) {
            itemReq.clearExpectedPrice();
            isExpectedPriceCleared = 1;
        }
        calculatePriceCore();
    }

    private void calculatePriceCore () {
        if (isComposeOrFeeParent()) {
            ExpectedPriceHelper.processCompose(itemReq, composeChildrenProcessors.stream().map(CalculateItemProcessor::getItemReq).collect(Collectors.toList()),
                    itemReq.getExpectedUnitPrice(), itemReq.getExpectedTotalPrice(), itemReq.getExpectedTotalPriceRatio(), calculateModel);
        } else {
            BigDecimal expectedTotalPrice = itemReq.getExpectedTotalPrice();
            //保护一下，如果议价值与原价一致，则把议价值清空
            if (expectedTotalPrice != null && itemReq.calculateSourceTotalPrice().compareTo(expectedTotalPrice) == 0) {
                itemReq.setExpectedTotalPrice(null);
                itemReq.setFractionPrice(BigDecimal.ZERO);
                itemReq.setUnitPrice(itemReq.getSourceUnitPrice());
            }

            ExpectedPriceHelper.process(itemReq, itemReq.getExpectedUnitPrice(), itemReq.getExpectedTotalPrice(), itemReq.getExpectedTotalPriceRatio(), calculateModel);
        }

        //计算批次的价格
        calculateItemPriceFromBatch();
    }

    private void calculateItemPriceFromBatch() {

        if (CollectionUtils.isEmpty(itemBatchInfoProcessors)) {
            return;
        }

        BigDecimal totalCount = MathUtils.calculateTotalCount(itemReq.getUnitCount(), itemReq.getDoseCount());
        //计算批次的总数量
        BigDecimal batchTotalCount = itemBatchInfoProcessors.stream()
                .map(itemBatchInfoProcessor -> itemBatchInfoProcessor.getBatchInfoReq().getUnitCount())
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (MathUtils.wrapBigDecimalCompare(totalCount, BigDecimal.ZERO) == 0 || MathUtils.wrapBigDecimalCompare(totalCount, batchTotalCount) != 0) {
            return;
        }

        //先修正批次的原价与item的原价保持一致
        resetBatchSourceTotalPrice();

        //如果有批次，则价格应该由批次决定
        if (existExpectedPrice()) {
            //如果存在议价，则将议价平摊到批次上
            BigDecimal totalPrice = itemReq.getTotalPrice();
            FlatPriceTool.flatPriceAndApply(totalPrice, itemBatchInfoProcessors.stream()
                    .map(itemBatchInfoProcessor -> new FlatPriceTool.IFlatCell<FlatPriceHelper.FlatPriceCell>() {
                        @Override
                        protected FlatPriceHelper.FlatPriceCell genFlatCell() {
                            BasicCalculateItemBatchInfoReq batchInfoReq = itemBatchInfoProcessor.getBatchInfoReq();
                            return new FlatPriceHelper.FlatPriceCell()
                                    .setId(batchInfoReq.getId())
                                    .setName(String.format("%s-%s", itemReq.getName(), batchInfoReq.getBatchId()))
                                    .setPriority(FlatPriceHelper.PRIORITY_HIGH)
                                    .setTotalPrice(batchInfoReq.getSourceTotalPrice());
                        }

                        @Override
                        protected void apply(BigDecimal flatPrice) {
                            itemBatchInfoProcessor.updateBatchExpectedTotalPrice(flatPrice);
                        }
                    }).collect(Collectors.toList())
            );

        } else {

            if (calculateModel == CalculateModel.NORMAL_RESET) {
                //如果没有议价，则item的价格需要通过批次的价格去重置
                BigDecimal batchSourceTotalPrice = itemBatchInfoProcessors.stream()
                        .map(itemBatchInfoProcessor -> itemBatchInfoProcessor.getBatchInfoReq().getSourceTotalPrice())
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                itemReq.setTotalPrice(batchSourceTotalPrice);
                int unitPriceScale = ChargeFormItemUtils.genItemCalculateUnitPriceScale(itemReq.getProductType(), itemReq.getProductSubType());
                MathUtils.CalculateExpectedUnitPriceResult calculateExpectedUnitPriceResult = MathUtils.calculateExpectedUnitPriceBase(itemReq.getUnitCount(), itemReq.getDoseCount(), batchSourceTotalPrice, unitPriceScale, 2);
                itemReq.setUnitPrice(calculateExpectedUnitPriceResult.expectedUnitPrice);
                itemReq.setFractionPrice(calculateExpectedUnitPriceResult.fractionPrice);
            } else if (calculateModel == CalculateModel.KEEP_CURRENT) {
                BigDecimal totalPrice = itemReq.getTotalPrice();

                FlatPriceTool.flatPriceAndApply(totalPrice, itemBatchInfoProcessors.stream()
                        .map(itemBatchInfoProcessor -> new FlatPriceTool.IFlatCell<FlatPriceHelper.FlatPriceCell>() {
                            @Override
                            protected FlatPriceHelper.FlatPriceCell genFlatCell() {
                                BasicCalculateItemBatchInfoReq batchInfoReq = itemBatchInfoProcessor.getBatchInfoReq();
                                return new FlatPriceHelper.FlatPriceCell()
                                        .setId(batchInfoReq.getId())
                                        .setName(String.format("%s-%s", itemReq.getName(), batchInfoReq.getBatchId()))
                                        .setPriority(FlatPriceHelper.PRIORITY_HIGH)
                                        .setTotalPrice(batchInfoReq.getSourceTotalPrice());
                            }

                            @Override
                            protected void apply(BigDecimal flatPrice) {
                                itemBatchInfoProcessor.updateBatchExpectedTotalPrice(flatPrice);
                            }
                        }).collect(Collectors.toList())
                );
            }

        }
    }

    private void resetBatchSourceTotalPrice() {

        BigDecimal sourceTotalPrice = itemReq.calculateSourceTotalPrice();
        //计算批次的总原价
        BigDecimal batchSourceTotalPrice = itemBatchInfoProcessors.stream()
                .map(itemBatchInfoProcessor -> itemBatchInfoProcessor.getBatchInfoReq().getSourceTotalPrice())
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (MathUtils.wrapBigDecimalCompare(sourceTotalPrice, batchSourceTotalPrice) == 0) {
            return;
        }

        FlatPriceTool.flatPriceAndApply(sourceTotalPrice, itemBatchInfoProcessors.stream()
                .map(itemBatchInfoProcessor -> new FlatPriceTool.IFlatCell<FlatPriceHelper.FlatPriceCell>() {
                    @Override
                    public FlatPriceHelper.FlatPriceCell genFlatCell() {
                        BasicCalculateItemBatchInfoReq batchInfoReq = itemBatchInfoProcessor.getBatchInfoReq();
                        return new FlatPriceHelper.FlatPriceCell()
                                .setId(batchInfoReq.getId())
                                .setName(String.format("平摊item原价到批次上，batchId:%s", batchInfoReq.getBatchId()))
                                .setTotalPrice(batchInfoReq.getSourceTotalPrice())
                                .setPriority(FlatPriceHelper.PRIORITY_HIGH);
                    }

                    @Override
                    protected void apply(BigDecimal flatPrice) {
                        BasicCalculateItemBatchInfoReq batchInfoReq = itemBatchInfoProcessor.getBatchInfoReq();

                        if (MathUtils.wrapBigDecimalCompare(batchInfoReq.getSourceTotalPrice(), flatPrice) == 0) {
                            return;
                        }

                        batchInfoReq.setSourceTotalPrice(flatPrice);
                    }

                }).collect(Collectors.toList())
        );

    }

    public boolean existExpectedPrice() {
        return Objects.nonNull(itemReq.getExpectedUnitPrice()) || Objects.nonNull(itemReq.getExpectedTotalPrice()) || Objects.nonNull(itemReq.getExpectedTotalPriceRatio());
    }

    public BigDecimal getSourceTotalPrice() {
        return itemReq.calculateSourceTotalPrice();
    }


    public void updateItemExpectedTotalPrice(BigDecimal expectedTotalPrice) {
        itemReq.clearExpectedPrice();
        if (MathUtils.wrapBigDecimalCompare(expectedTotalPrice, itemReq.calculateSourceTotalPrice()) == 0) {
            itemReq.setFractionPrice(BigDecimal.ZERO);
            itemReq.setUnitPrice(itemReq.getSourceUnitPrice());
        } else {
            itemReq.setExpectedTotalPrice(expectedTotalPrice);
        }
        calculatePriceCore();
    }

    public BasicCalculateItemRsp generateBasicCalculateItemRsp() {
        BasicCalculateItemRsp itemRsp = new BasicCalculateItemRsp();
        itemRsp.setId(itemReq.getId())
                .setUnit(itemReq.getUnit())
                .setName(itemReq.getName())
                .setUnitCount(itemReq.getUnitCount())
                .setDoseCount(itemReq.getDoseCount())
                .setProductType(itemReq.getProductType())
                .setProductSubType(itemReq.getProductSubType())
                .setUnitPrice(MathUtils.wrapBigDecimalOrZero(itemReq.getUnitPrice()))
                .setSourceUnitPrice(itemReq.getSourceUnitPrice())
                .setTotalPrice(itemReq.getTotalPrice())
                .setTotalPriceRatio(itemReq.getTotalPriceRatio())
                .setSourceTotalPrice(itemReq.calculateSourceTotalPrice())
                .setFractionPrice(MathUtils.wrapBigDecimalOrZero(itemReq.getFractionPrice()))
                .setIsUnitPriceChanged(itemReq.getIsUnitPriceChanged())
                .setIsTotalPriceChanged(itemReq.getIsTotalPriceChanged())
                .setComposeType(itemReq.getComposeType())
                .setCurrentUnitPrice(getCurrentUnitPrice(itemRsp.getUnitPrice(), itemRsp.getTotalPrice(), itemRsp.getSourceTotalPrice()))
                .setFeeComposeType(itemReq.getFeeComposeType())
                .setFeeTypeId(itemRsp.getFeeTypeId())
                .setGoodsFeeType(itemReq.getGoodsFeeType())
                .setExpectedUnitPrice(itemReq.getExpectedUnitPrice())
                .setExpectedTotalPrice(itemReq.getExpectedTotalPrice())
                .setExpectedTotalPriceRatio(itemReq.getExpectedTotalPriceRatio())
                .setUnitAdjustmentFee(calculateUnitAdjustmentFee(itemRsp.getTotalPrice(), itemRsp.getSourceTotalPrice()));
        if (CollectionUtils.isNotEmpty(itemBatchInfoProcessors)) {
            itemRsp.setBatchInfos(itemBatchInfoProcessors.stream()
                    .map(CalculateItemBatchInfoProcessor::generateBasicCalculateItemBatchInfoRsp)
                    .collect(Collectors.toList())
            );
        }

        if (isComposeOrFeeParent()) {
            itemRsp.setComposeChildren(composeChildrenProcessors.stream().map(CalculateItemProcessor::generateBasicCalculateItemRsp).collect(Collectors.toList()));
        }
        return itemRsp;
    }

    private BigDecimal calculateUnitAdjustmentFee(BigDecimal totalPrice, BigDecimal sourceTotalPrice) {

        //todo 门诊处方议价怎么处理
        if (itemReq.getExpectedUnitPrice() == null
                || itemReq.getExpectedTotalPrice() == null
                || itemReq.getExpectedTotalPriceRatio() == null
        ) {
            return BigDecimal.ZERO;
        }

        return MathUtils.wrapBigDecimalSubtract(sourceTotalPrice, totalPrice);
    }

    private BigDecimal getCurrentUnitPrice(BigDecimal unitPrice, BigDecimal totalPrice, BigDecimal sourceTotalPrice) {
        if (MathUtils.wrapBigDecimalCompare(totalPrice, sourceTotalPrice) != 0) {
            return unitPrice;
        }
        return null;
    }

    public void resetItemPrice() {
        if (isComposeOrFeeParent()) {
            composeChildrenProcessors.forEach(CalculateItemProcessor::resetItemPrice);
        }

        itemReq.clearExpectedPrice();
        itemReq.setUnitPrice(itemReq.getSourceUnitPrice());
        itemReq.setFractionPrice(BigDecimal.ZERO);
        itemReq.calculateTotalPrice();
    }
}
