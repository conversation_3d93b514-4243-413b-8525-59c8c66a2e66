package cn.abcyun.cis.charge.processor;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 营销查询赠品goods信息的req
 */
@Data
@Accessors(chain = true)
public class PromotionQueryGiftGoodsDto {

    /**
     * 优惠营销id
     */
    private String promotionId;

    /**
     * 收费项id
     */
    private String chargeFormItemId;

    /**
     * 买的goodsId
     */
    private String goodsId;

    /**
     * 赠送的goodsId
     */
    private String giftGoodsId;

    /**
     * 赠送的goodsCount
     */
    private BigDecimal giftGoodsCount;

    /**
     * 是否拆零
     */
    private int isDismounting;


    public String generateKeyId() {
        return generateKeyId(promotionId, chargeFormItemId, giftGoodsId);
    }

    public static String generateKeyId(String promotionId, String chargeFormItemId, String giftGoodsId) {
        return String.format("%s-%s-%s", promotionId, chargeFormItemId, giftGoodsId);
    }

}
