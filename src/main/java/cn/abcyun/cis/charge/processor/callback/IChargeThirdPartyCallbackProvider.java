package cn.abcyun.cis.charge.processor.callback;

import cn.abcyun.bis.rpc.sdk.cis.model.pay.PayCallbackRsp;
import cn.abcyun.cis.charge.api.model.PayCallbackContainPayModeReq;
import cn.abcyun.cis.charge.model.ChargePayTransaction;
import cn.abcyun.cis.charge.model.ChargeSheet;

public interface IChargeThirdPartyCallbackProvider {

    int getPayType();

    /**
     * 回调处理：
     * 分为支付回调，退款回调，部分退费退款回调
     * @param payCallbackReq
     * @param chargePayTransaction
     * @return
     */
    PayCallbackRsp callback(PayCallbackContainPayModeReq payCallbackReq, ChargeSheet chargeSheet, ChargePayTransaction chargePayTransaction);


    /**
     * 回调失败的弥补方法
     */
    void recover(PayCallbackContainPayModeReq payCallbackReq, ChargePayTransaction chargePayTransaction);

}
