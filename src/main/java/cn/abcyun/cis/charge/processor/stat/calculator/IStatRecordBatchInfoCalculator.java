package cn.abcyun.cis.charge.processor.stat.calculator;

import cn.abcyun.cis.charge.processor.stat.StatRecordByChooseCalculateCell;

public interface IStatRecordBatchInfoCalculator {

    void flatCellBatchInfoDiscountedPrice(StatRecordByChooseCalculateCell calculateCell);

    void flatCellBatchInfoDiscountInfo(StatRecordByChooseCalculateCell calculateCell);

    void fillOtherFields(StatRecordByChooseCalculateCell calculateCell);

}
