package cn.abcyun.cis.charge.processor;

import cn.abcyun.cis.charge.base.pay.CombinedPayItem;
import cn.abcyun.cis.charge.model.ChargeForm;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class RefundInfo {
    private BigDecimal adjustmentFee;
    private BigDecimal needRefundFee;
    private int paySource;
    private String operatorId;
    private String chargeComment;

    private List<ChargeForm> chargeForms;
    private CombinedPayItem payItem;
    private String checkerId;// 审核人
}
