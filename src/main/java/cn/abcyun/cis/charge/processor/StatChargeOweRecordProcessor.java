package cn.abcyun.cis.charge.processor;


import cn.abcyun.bis.rpc.sdk.cis.model.charge.GoodsFeeType;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.cold.ChargeFormItemDaoProxy;
import cn.abcyun.cis.charge.helper.*;
import cn.abcyun.cis.charge.model.*;
import cn.abcyun.cis.charge.service.ChargeOweCombineTransactionRecordDetailService;
import cn.abcyun.cis.charge.service.dto.ChargeDiscountInfo;
import cn.abcyun.cis.charge.util.JsonUtils;
import cn.abcyun.cis.charge.util.ListUtils;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.cis.commons.model.ComposeType;
import cn.abcyun.common.log.marker.AbcLogMarker;
import com.google.common.collect.Lists;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.BiPredicate;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class StatChargeOweRecordProcessor {

    public static final class PayType {
        public static final int PAID = 0;
        public static final int PARTED_PAID = 1;
        public static final int PAID_BACK = 2;
        public static final int PARTED_PAID_BACK = 3;
        public static final int REFUND = 4;
        public static final int PARTED_REFUND = 5;

        public static boolean isPaid(int payType) {
            return payType == PAID || payType == PARTED_PAID;
        }

        public static boolean isPaidBack(int payType) {
            return payType == PAID_BACK || payType == PARTED_PAID_BACK;
        }
    }

    /**
     * 母项类型
     */
    public enum ParentType {
        /**
         * 套餐母项
         */
        COMPOSE_PARENT((composeType, goodsFeeType) -> composeType == ComposeType.COMPOSE_SUB_ITEM),
        /**
         * 费用母项
         */
        FEE_PARENT((composeType, goodsFeeType) -> goodsFeeType == GoodsFeeType.FEE_CHILD);

        private BiPredicate<Integer, Integer> filterPredicate;

        ParentType(BiPredicate<Integer, Integer> filterPredicate) {
            this.filterPredicate = filterPredicate;
        }

        public BiPredicate<Integer, Integer> getFilterPredicate() {
            return filterPredicate;
        }
    }


    private int payType;
    private boolean isFirstPay;
    private int transactionRecordHandleMode;
    private boolean isHospital;
    private ChargeOweSheet chargeOweSheet;
    private String operatorId;
    private ChargeOweCombineTransactionRecord oweCombineTransactionRecord;
    private List<ChargeTransactionRecord> chargeTransactionRecords;
    @Setter
    private ChargeOweCombineTransactionRecordDetailService oweCombineTransactionRecordDetailService;
    private ChargeFormItemDaoProxy chargeFormItemRepository;

    public void setChargeFormItemRepository(ChargeFormItemDaoProxy chargeFormItemRepository) {
        this.chargeFormItemRepository = chargeFormItemRepository;
    }

    public void setChargeInfo(ChargeOweSheet chargeOweSheet,
                              boolean isFirstPay,
                              int payType,
                              int transactionRecordHandleMode,
                              boolean isHospital,
                              ChargeOweCombineTransactionRecord oweCombineTransactionRecord,
                              List<ChargeTransactionRecord> chargeTransactionRecords,
                              String operatorId
    ) {
        Assert.notNull(chargeOweSheet, "chargeOweSheet cannot be null");
        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "setChargeInfo payType:{}, transactionRecordHandleMode: {}", payType, transactionRecordHandleMode);
        this.chargeOweSheet = chargeOweSheet;
        this.isFirstPay = isFirstPay;
        this.payType = payType;
        this.isHospital = isHospital;
        this.transactionRecordHandleMode = transactionRecordHandleMode;
        this.oweCombineTransactionRecord = oweCombineTransactionRecord;
        this.chargeTransactionRecords = chargeTransactionRecords;
        this.operatorId = operatorId;
    }


    public List<ChargeOweCombineTransactionRecordDetail> generateOweCombineTransactionRecordDetails() {
        if (Objects.isNull(oweCombineTransactionRecord)) {
            return new ArrayList<>();
        }

        List<ChargeOweCombineTransactionRecordDetail> historyRecordDetails = Optional.ofNullable(oweCombineTransactionRecordDetailService.findAllByClinicIdAndOweSheetIdAndIsOldRecord(oweCombineTransactionRecord.getClinicId(), String.valueOf(oweCombineTransactionRecord.getOweSheetId()), 0)).orElse(new ArrayList<>());

        return generateOweCombineTransactionRecordDetails(historyRecordDetails);
    }

    public List<ChargeOweCombineTransactionRecordDetail> generateOweCombineTransactionRecordDetails(List<ChargeOweCombineTransactionRecordDetail> historyRecordDetails) {

        if (CollectionUtils.isEmpty(chargeTransactionRecords)) {
            return new ArrayList<>();
        }

        List<String> recordedItemIds = chargeTransactionRecords.stream()
                .filter(record -> StringUtils.isNotEmpty(record.getChargeFormItemId()))
                .map(ChargeTransactionRecord::getChargeFormItemId)
                .collect(Collectors.toList());

        List<ChargeFormItem> chargeFormItems = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(recordedItemIds)) {
            chargeFormItems = Optional.ofNullable(chargeFormItemRepository.findAllByClinicIdAndChargeSheetIdAndIdInAndIsDeleted(chargeOweSheet.getClinicId(), chargeOweSheet.getChargeSheetId(), recordedItemIds, 0)).orElse(new ArrayList<>());
        }

        List<ChargeOweCombineTransactionRecordDetail> oweCombineTransactionRecordDetails;
        Map<String, ChargeTransactionRecord> originalItemIdRecordMap = generateItemOwePayOriginalDataMap(chargeTransactionRecords, chargeFormItems, transactionRecordHandleMode);

        if (transactionRecordHandleMode == Constants.TransactionRecordHandleMode.RECORD_COUNT_BY_PRICE_RATE) {
            //兼容老的部分退费的数据，进行退费摊费是要走老的摊费逻辑
            oweCombineTransactionRecordDetails = generateOweCombineTransactionRecordDetailsByPriceRate(historyRecordDetails, chargeFormItems, originalItemIdRecordMap);
        } else {
            oweCombineTransactionRecordDetails = generateOweCombineTransactionRecordDetailsByChoose(historyRecordDetails, chargeFormItems, originalItemIdRecordMap);
        }

        return Optional.ofNullable(oweCombineTransactionRecordDetails).orElse(new ArrayList<>());
    }

    private List<ChargeOweCombineTransactionRecordDetail> generateOweCombineTransactionRecordDetailsByPriceRate(List<ChargeOweCombineTransactionRecordDetail> historyRecordDetails,
                                                                                                                List<ChargeFormItem> chargeFormItems,
                                                                                                                Map<String, ChargeTransactionRecord> originalItemIdRecordMap) {

        Map<String, List<ChargeOweCombineTransactionRecordDetail>> historyExpenseItemIdRecordDetailIdMap = ListUtils.groupByKey(historyRecordDetails.stream()
                        .filter(recordDetail -> StringUtils.isNotEmpty(recordDetail.getExpenseItemId()))
                        .collect(Collectors.toList()),
                ChargeOweCombineTransactionRecordDetail::getExpenseItemId
        );

        int chargeType = convertToChargeType(payType);

        List<ChargeOweCombineTransactionRecordDetail> thisTimeWriteRecordDetails = new ArrayList<>();

        //非套餐和套餐子项的所有项目
        List<ChargeOweCombineTransactionRecordDetail> oweCombineTransactionRecordDetails = generateOweCombineTransactionRecordDetailsByPriceRateCore(oweCombineTransactionRecord,
                chargeOweSheet.getTotalPrice(),
                originalItemIdRecordMap,
                historyExpenseItemIdRecordDetailIdMap,
                isFirstPay,
                payType,
                chargeType,
                operatorId);
        thisTimeWriteRecordDetails.addAll(oweCombineTransactionRecordDetails);

        //处理套餐母项的项目
        List<ChargeOweCombineTransactionRecordDetail> composeRecordDetails = generateComposeOweCombineTransactionRecordDetailsByPriceRateCore(oweCombineTransactionRecord,
                oweCombineTransactionRecordDetails,
                chargeFormItems,
                originalItemIdRecordMap,
                historyExpenseItemIdRecordDetailIdMap,
                isFirstPay,
                chargeType,
                payType,
                operatorId);
        thisTimeWriteRecordDetails.addAll(composeRecordDetails);


        //处理会员支付摊费
        PresentPrincipalAmountFlatTool.flatPresentPrincipalAmountToRecordByChoose(thisTimeWriteRecordDetails
                        .stream()
                        .map(ChargeOweTransactionRecordCell::new)
                        .collect(Collectors.toList()),
                new ChargeOweTransactionCell(oweCombineTransactionRecord));
//        flatMemberCardPayToRecordByChoose(thisTimeWriteRecordDetails, oweCombineTransactionRecord);

        return thisTimeWriteRecordDetails;
    }

    private List<ChargeOweCombineTransactionRecordDetail> generateComposeOweCombineTransactionRecordDetailsByPriceRateCore(ChargeOweCombineTransactionRecord oweCombineTransactionRecord,
                                                                                                                           List<ChargeOweCombineTransactionRecordDetail> recordDetails,
                                                                                                                           List<ChargeFormItem> chargeFormItems,
                                                                                                                           Map<String, ChargeTransactionRecord> itemIdRecordMap,
                                                                                                                           Map<String, List<ChargeOweCombineTransactionRecordDetail>> historyExpenseItemIdRecordDetailIdMap,
                                                                                                                           boolean isFirstPay,
                                                                                                                           int chargeType,
                                                                                                                           int payType,
                                                                                                                           String operatorId) {

        List<StatChargeOweRecordByChooseCalculateCell> composeCells = calculateComposeCells(recordDetails,
                chargeFormItems,
                itemIdRecordMap,
                historyExpenseItemIdRecordDetailIdMap, ParentType.COMPOSE_PARENT,
                payType,
                false);

        Map<String, ChargeFormItem> composeChargeFormItemIdMap = chargeFormItems.stream()
                .filter(chargeFormItem -> chargeFormItem.getComposeType() == ComposeType.COMPOSE)
                .collect(Collectors.toMap(ChargeFormItem::getId, Function.identity(), (a, b) -> a));

        //计算套餐母项的数量
        composeCells.forEach(composeCell -> {
            String chargeFormItemId = composeCell.getLimitedRecord().getChargeFormItemId();
            ChargeFormItem composeChargeFormItem = composeChargeFormItemIdMap.get(chargeFormItemId);

            if (Objects.isNull(composeChargeFormItem)) {
                return;
            }

            //还款成功，直接使用总数量减已记录的数量
            if (payType == PayType.PAID) {
                composeCell.setToRecordUnitCount(composeChargeFormItem.getUnitCount().subtract(composeCell.getRecordedUnitCount()));
                return;
            }

            if (payType == PayType.PARTED_PAID) {
                Function<BigDecimal, BigDecimal> calculateCountFunction = count -> {
                    if (MathUtils.wrapBigDecimalCompare(composeChargeFormItem.getReceivedPrice(), BigDecimal.ZERO) == 0) {
                        return BigDecimal.ZERO;
                    }
                    return MathUtils.min(count, count.multiply(composeCell.getToRecordDiscountedPrice()).divide(composeChargeFormItem.getReceivedPrice(), 2, RoundingMode.UP));
                };

                composeCell.setToRecordUnitCount(calculateCountFunction.apply(composeChargeFormItem.getUnitCount()));
            }

        });

        return composeCells.stream()
                .map(composeCell -> {
                    ChargeOweCombineTransactionRecordDetail recordDetail = composeCell.ofComposeChargeOweCombineTransactionRecordDetail(oweCombineTransactionRecord, isFirstPay, chargeType, operatorId);
                    recordDetail.setProductUnitCount(composeCell.getToRecordUnitCount())
                            .setSceneType(composeCell.getLimitedRecord().getSceneType())
                            .setDeductCount(BigDecimal.ZERO)
                            .setDeductTotalPrice(BigDecimal.ZERO)
                            .setDeductTotalCostPrice(BigDecimal.ZERO)
                            .setDiscountPrice(composeCell.getToRecordDiscountPrice())
                            .setTotalCostPrice(composeCell.getToRecordTotalCostPrice())
                            .setTotalPrice(composeCell.getToRecordDiscountedPrice().subtract(composeCell.getToRecordDiscountPrice()))
                            .setDiscountInfo(composeCell.getToRecordPromotionDiscountInfo());
                    return recordDetail;
                })
                .collect(Collectors.toList());
    }

    private List<StatChargeOweRecordByChooseCalculateCell> calculateComposeCells(List<ChargeOweCombineTransactionRecordDetail> recordDetails,
                                                                                 List<ChargeFormItem> chargeFormItems,
                                                                                 Map<String, ChargeTransactionRecord> itemIdRecordMap,
                                                                                 Map<String, List<ChargeOweCombineTransactionRecordDetail>> historyExpenseItemIdRecordDetailIdMap,
                                                                                 ParentType parentType,
                                                                                 int payType,
                                                                                 boolean needDealClinicShebaoCodeMatchCostPrice) {

        Assert.notNull(parentType, "parentType is null");

        if (CollectionUtils.isEmpty(recordDetails) || CollectionUtils.isEmpty(chargeFormItems)) {
            return new ArrayList<>();
        }

        //找到本次记录明细的所有子项
        List<ChargeOweCombineTransactionRecordDetail> composeChildrenRecordDetails = recordDetails.stream()
                .filter(recordDetail -> parentType.getFilterPredicate().test(recordDetail.getComposeType(), recordDetail.getGoodsFeeType()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(composeChildrenRecordDetails)) {
            return new ArrayList<>();
        }

        Map<String, String> composeItemIdMap = chargeFormItems.stream()
                .filter(chargeFormItem -> parentType.getFilterPredicate().test(chargeFormItem.getComposeType(), chargeFormItem.getGoodsFeeType())
                        && StringUtils.isNotEmpty(chargeFormItem.getComposeParentFormItemId())
                )
                .collect(Collectors.toMap(ChargeFormItem::getId, ChargeFormItem::getComposeParentFormItemId, (a, b) -> a));

        Map<String, List<ChargeOweCombineTransactionRecordDetail>> parentRecordDetailMap = new HashMap<>();

        //按母项id分组，分成多个list，转换成map，key为套餐母项itemId，value为子项
        composeChildrenRecordDetails.forEach(child -> {
            String composeParentChargeFormItemId = composeItemIdMap.getOrDefault(child.getExpenseItemId(), null);
            if (StringUtils.isEmpty(composeParentChargeFormItemId)) {
                return;
            }
            parentRecordDetailMap.computeIfAbsent(composeParentChargeFormItemId, key -> Lists.newArrayList()).add(child);
        });

        //构造套餐母项摊费对象
        List<StatChargeOweRecordByChooseCalculateCell> parentCells = parentRecordDetailMap
                .entrySet()
                .stream()
                .map(entry -> {
                    ChargeTransactionRecord record = itemIdRecordMap.get(entry.getKey());
                    if (Objects.isNull(record)) {
                        return null;
                    }

                    List<ChargeOweCombineTransactionRecordDetail> childrenRecordDetails = entry.getValue();
                    if (CollectionUtils.isEmpty(childrenRecordDetails)) {
                        return null;
                    }

                    StatChargeOweRecordByChooseCalculateCell cell = new StatChargeOweRecordByChooseCalculateCell();
                    cell.setId(entry.getKey())
                            .setLimitedRecord(record)
                            .setHistoryRecordDetails(historyExpenseItemIdRecordDetailIdMap.getOrDefault(record.getChargeFormItemId(), new ArrayList<>()))
                            .setThisTimeRecordDetails(childrenRecordDetails);
                    return cell;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(parentCells)) {
            return new ArrayList<>();
        }

        StatChargeOweRecordByChooseCalculateCellHelper.calculateParentCell(parentCells, payType, needDealClinicShebaoCodeMatchCostPrice);

        return parentCells;
    }

    private List<ChargeOweCombineTransactionRecordDetail> generateOweCombineTransactionRecordDetailsByPriceRateCore(ChargeOweCombineTransactionRecord oweCombineTransactionRecord,
                                                                                                                    BigDecimal totalOwePrice,
                                                                                                                    Map<String, ChargeTransactionRecord> itemIdRecordMap,
                                                                                                                    Map<String, List<ChargeOweCombineTransactionRecordDetail>> historyExpenseItemIdRecordDetailIdMap,
                                                                                                                    boolean isFirstPay,
                                                                                                                    int payType,
                                                                                                                    int chargeType,
                                                                                                                    String operatorId) {

        BigDecimal transactionRecordPrice = oweCombineTransactionRecord.getAmount();

        //构造摊费对象
        List<StatChargeOweRecordByChooseCalculateCell> cells = itemIdRecordMap
                .values()
                .stream()
                .filter(record -> record.getComposeType() != ComposeType.COMPOSE)
                .map(record -> {
                    StatChargeOweRecordByChooseCalculateCell cell = new StatChargeOweRecordByChooseCalculateCell();
                    cell.setId(record.getGroupByItemId())
                            .setLimitedRecord(record)
                            .setHistoryRecordDetails(historyExpenseItemIdRecordDetailIdMap.getOrDefault(record.getChargeFormItemId(), new ArrayList<>()));
                    return cell;
                })
                .collect(Collectors.toList());

        BigDecimal leftToRecordPrice = transactionRecordPrice;

        //判断是否收完
        if (payType == StatChargeOweRecordProcessor.PayType.PAID) {
            for (StatChargeOweRecordByChooseCalculateCell cell : cells) {
                cell.setToRecordDiscountedPrice(cell.getLeftToRecordDiscountedPrice())
                        .setToRecordUnitCount(cell.getLeftToRecordUnitCount())
                        .setToRecordTotalCostPrice(cell.getLeftToRecordTotalCostPrice())
                        .setToRecordDiscountPrice(cell.getLeftToRecordDiscountPrice())
                        .setToRecordPromotionDiscountInfo(ChargeTransactionRecordDiscountInfoHelper.generateToRecordChargeTransactionRecordDiscountInfoForLastRecord(cell.getLimitedRecordDiscountInfo(), cell.getRecordedDiscountInfo()));
            }
        } else {
            Function<BigDecimal, BigDecimal> calculatePriceFunction = price -> MathUtils.min(price, price.multiply(transactionRecordPrice).divide(totalOwePrice, 2, RoundingMode.DOWN));
            for (StatChargeOweRecordByChooseCalculateCell cell : cells) {
                BigDecimal limitedDiscountedPrice = cell.getLimitedDiscountedPrice();
                cell.setToRecordDiscountedPrice(calculatePriceFunction.apply(limitedDiscountedPrice))
                        .setToRecordTotalCostPrice(calculatePriceFunction.apply(cell.getLimitedTotalCostPrice()))
                        .setToRecordDiscountPrice(calculatePriceFunction.apply((cell.getLimitedDiscountPrice())))
                        .setToRecordUnitCount(calculatePriceFunction.apply(cell.getLimitedUnitCount()))
                        .setToRecordPromotionDiscountInfo(ChargeTransactionRecordDiscountInfoHelper.generateToRecordChargeTransactionRecordDiscountInfo(cell.getLimitedRecordDiscountInfo(), cell.getToRecordDiscountPrice(), cell.getToRecordDiscountedPrice(), cell.getLimitedDiscountedPrice(), false));
                leftToRecordPrice = leftToRecordPrice.subtract(cell.getToRecordDiscountedPrice());
            }

            //精度问题处理
            if (leftToRecordPrice.compareTo(BigDecimal.ZERO) > 0) {
                for (StatChargeOweRecordByChooseCalculateCell cell : cells) {

                    if (leftToRecordPrice.compareTo(BigDecimal.ZERO) <= 0) {
                        break;
                    }

                    BigDecimal fractionPrice = MathUtils.min(leftToRecordPrice, cell.getLeftToRecordDiscountedPrice().subtract(cell.getToRecordDiscountedPrice()));
                    cell.setToRecordDiscountedPrice(cell.getToRecordDiscountedPrice().add(fractionPrice));
                    leftToRecordPrice = leftToRecordPrice.subtract(cell.getToRecordDiscountedPrice());
                }
            }
        }

        return cells.stream()
                .map(cell -> {
                    ChargeOweCombineTransactionRecordDetail recordDetail = cell.ofChargeOweCombineTransactionRecordDetail(oweCombineTransactionRecord, isFirstPay, chargeType, operatorId);
                    recordDetail.setProductUnitCount(cell.getToRecordUnitCount())
                            .setSceneType(cell.getLimitedRecord().getSceneType())
                            .setDeductCount(BigDecimal.ZERO)
                            .setDeductTotalPrice(BigDecimal.ZERO)
                            .setDeductTotalCostPrice(BigDecimal.ZERO)
                            .setDiscountPrice(cell.getToRecordDiscountPrice())
                            .setTotalCostPrice(cell.getToRecordTotalCostPrice())
                            .setTotalPrice(cell.getToRecordDiscountedPrice().subtract(cell.getToRecordDiscountPrice()))
                            .setDiscountInfo(cell.getToRecordPromotionDiscountInfo());

                    return recordDetail;
                })
                .collect(Collectors.toList());
    }

    private List<ChargeOweCombineTransactionRecordDetail> generateOweCombineTransactionRecordDetailsByChoose(List<ChargeOweCombineTransactionRecordDetail> historyRecordDetails,
                                                                                                             List<ChargeFormItem> chargeFormItems,
                                                                                                             Map<String, ChargeTransactionRecord> originalItemIdRecordMap) {

        Map<String, List<ChargeOweCombineTransactionRecordDetail>> historyExpenseItemIdRecordDetailIdMap = ListUtils.groupByKey(historyRecordDetails.stream()
                        .filter(recordDetail -> StringUtils.isNotEmpty(recordDetail.getExpenseItemId()))
                        .collect(Collectors.toList()),
                ChargeOweCombineTransactionRecordDetail::getExpenseItemId
        );

        Map<String, List<ChargeOweCombineTransactionRecordDetailBatchInfo>> historyItemBatchInfoIdDetailBatchInfoMap = Optional.ofNullable(historyRecordDetails).orElse(new ArrayList<>())
                .stream()
                .flatMap(record -> Optional.ofNullable(record.getBatchInfos()).orElse(new ArrayList<>()).stream())
                .collect(Collectors.groupingBy(ChargeOweCombineTransactionRecordDetailBatchInfo::getItemBatchInfoId));

        int chargeType = convertToChargeType(payType);

        List<ChargeOweCombineTransactionRecordDetail> thisTimeWriteRecordDetails = new ArrayList<>();

        //非套餐和套餐子项的所有项目
        List<ChargeOweCombineTransactionRecordDetail> oweCombineTransactionRecordDetails = generateOweCombineTransactionRecordDetailsByChooseCore(oweCombineTransactionRecord,
                chargeOweSheet.getTotalPrice(),
                originalItemIdRecordMap,
                historyExpenseItemIdRecordDetailIdMap,
                historyItemBatchInfoIdDetailBatchInfoMap,
                isFirstPay,
                payType,
                chargeType,
                operatorId);
        thisTimeWriteRecordDetails.addAll(oweCombineTransactionRecordDetails);


        //单独给个标记，诊所管家中治疗理疗检查检验其他费用有多个对码时，由于使用了医嘱费用项的结构，但是又没有遵循子项的成本和 = 母项的成本，这种母项数据需要单独对成本金额处理
        boolean needDealClinicShebaoCodeMatchCostPrice = !isHospital;

        //处理费用母项和费用子项的记录
        List<ChargeOweCombineTransactionRecordDetail> feeParentRecordDetails = generateComposeOweCombineTransactionRecordDetailsByChooseCore(oweCombineTransactionRecord,
                thisTimeWriteRecordDetails,
                chargeFormItems,
                originalItemIdRecordMap,
                historyExpenseItemIdRecordDetailIdMap,
                isFirstPay,
                chargeType,
                ParentType.FEE_PARENT,
                needDealClinicShebaoCodeMatchCostPrice,
                payType,
                operatorId);
        thisTimeWriteRecordDetails.addAll(feeParentRecordDetails);

        //处理套餐母项的项目
        List<ChargeOweCombineTransactionRecordDetail> composeRecordDetails = generateComposeOweCombineTransactionRecordDetailsByChooseCore(oweCombineTransactionRecord,
                thisTimeWriteRecordDetails,
                chargeFormItems,
                originalItemIdRecordMap,
                historyExpenseItemIdRecordDetailIdMap,
                isFirstPay,
                chargeType,
                ParentType.COMPOSE_PARENT,
                false,
                payType,
                operatorId);
        thisTimeWriteRecordDetails.addAll(composeRecordDetails);


        //处理会员支付摊费
        PresentPrincipalAmountFlatTool.flatPresentPrincipalAmountToRecordByChoose(thisTimeWriteRecordDetails
                        .stream()
                        .map(ChargeOweTransactionRecordCell::new)
                        .collect(Collectors.toList()),
                new ChargeOweTransactionCell(oweCombineTransactionRecord));
//        flatMemberCardPayToRecordByChoose(thisTimeWriteRecordDetails, oweCombineTransactionRecord);

        return thisTimeWriteRecordDetails;
    }

    public static Map<String, ChargeTransactionRecord> generateItemOwePayOriginalDataMap(List<ChargeTransactionRecord> chargeTransactionRecords,
                                                                                         List<ChargeFormItem> chargeFormItems,
                                                                                         int transactionRecordHandleMode) {
        if (CollectionUtils.isEmpty(chargeTransactionRecords)) {
            return new HashMap<>();
        }

        Map<String, String> mergedChargeItemIdMap = chargeFormItems.stream()
                .filter(chargeFormItem -> chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED || chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.REFUNDED)
                .collect(Collectors.toMap(ChargeFormItem::getId, chargeFormItem -> {
                    if (StringUtils.isNotEmpty(chargeFormItem.getAssociateFormItemId())) {
                        return chargeFormItem.getAssociateFormItemId();
                    }
                    return chargeFormItem.getId();
                }, (a, b) -> a));

        //找出数量已经退完的itemIds
        Set<String> allRefundedItemIds = chargeFormItems.stream()
                .filter(chargeFormItem -> chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED)
                .filter(chargeFormItem -> {
                    BigDecimal totalCount = MathUtils.calculateTotalCount(chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount());
                    BigDecimal totalRefundCount = MathUtils.calculateTotalCount(chargeFormItem.getRefundUnitCount(), chargeFormItem.getRefundDoseCount());
                    return MathUtils.wrapBigDecimalCompare(totalCount, totalRefundCount) <= 0 && MathUtils.wrapBigDecimalCompare(chargeFormItem.getReceivedPrice(), BigDecimal.ZERO) <= 0;
                })
                .map(ChargeFormItem::getId)
                .collect(Collectors.toSet());

        if (transactionRecordHandleMode == Constants.TransactionRecordHandleMode.RECORD_COUNT_BY_PRICE_RATE) {
            return chargeTransactionRecords
                    .stream()
                    .filter(ChargeTransactionRecord::checkGroupByItemIdIsNotEmpty)
                    .collect(Collectors.toMap(record -> mergedChargeItemIdMap.getOrDefault(record.getGroupByItemId(), record.getGroupByItemId()),
                            //将数据库对象转换成内存对象
                            record -> {
                                ChargeTransactionRecord chargeTransactionRecord = JsonUtils.readValue(JsonUtils.dump(record), ChargeTransactionRecord.class);

                                if (chargeTransactionRecord.getType() != ChargeTransactionRecord.RecordType.AIR_PHARMACY) {
                                    chargeTransactionRecord.setChargeFormItemId(mergedChargeItemIdMap.getOrDefault(record.getGroupByItemId(), record.getGroupByItemId()));
                                }
                                return chargeTransactionRecord;
                            },
                            (a, b) -> {
                                BigDecimal flag = BigDecimal.ONE;
                                if (b.getChargeType() == ChargeTransactionRecord.ChargeType.REFUND) {
                                    flag = flag.negate();
                                }

                                a.setTotalCostPrice(MathUtils.wrapBigDecimalAdd(a.getTotalCostPrice(), MathUtils.wrapBigDecimalMultiply(b.getTotalCostPrice(), flag)));
                                a.setTotalPrice(MathUtils.wrapBigDecimalAdd(a.getTotalPrice(), MathUtils.wrapBigDecimalMultiply(b.getTotalPrice(), flag)));
                                a.setDiscountPrice(MathUtils.wrapBigDecimalAdd(a.getDiscountPrice(), MathUtils.wrapBigDecimalMultiply(b.getDiscountPrice(), flag)));
                                a.setProductUnitCount(MathUtils.wrapBigDecimalAdd(a.getProductUnitCount(), MathUtils.wrapBigDecimalMultiply(b.getProductUnitCount(), flag)));

                                ChargeTransactionRecordAdditional aAdditional = a.getAdditional();
                                ChargeTransactionRecordAdditional bAdditional = b.getAdditional();

                                if (Objects.nonNull(aAdditional) && Objects.nonNull(bAdditional)) {
                                    aAdditional.setDeductCount(MathUtils.wrapBigDecimalAdd(aAdditional.getDeductCount(), MathUtils.wrapBigDecimalMultiply(bAdditional.getDeductCount(), flag)));
                                    aAdditional.setDeductTotalPrice(MathUtils.wrapBigDecimalAdd(aAdditional.getDeductTotalPrice(), MathUtils.wrapBigDecimalMultiply(bAdditional.getDeductTotalCostPrice(), flag)));
                                    aAdditional.setDeductTotalCostPrice(MathUtils.wrapBigDecimalAdd(aAdditional.getDeductTotalCostPrice(), MathUtils.wrapBigDecimalMultiply(bAdditional.getDeductTotalCostPrice(), flag)));
                                    aAdditional.setPresentAmount(MathUtils.wrapBigDecimalAdd(aAdditional.getPresentAmount(), MathUtils.wrapBigDecimalMultiply(bAdditional.getPresentAmount(), flag)));
                                    aAdditional.setPrincipalAmount(MathUtils.wrapBigDecimalAdd(aAdditional.getPrincipalAmount(), MathUtils.wrapBigDecimalMultiply(bAdditional.getPrincipalAmount(), flag)));
                                    List<ChargeDiscountInfo> discountInfos = new ArrayList<>();
                                    Optional.ofNullable(aAdditional.getDiscountInfo())
                                            .ifPresent(discountInfos::add);
                                    Optional.ofNullable(bAdditional.getDiscountInfo())
                                            .ifPresent(recordedDiscountInfo -> discountInfos.add(b.getChargeType() == ChargeTransactionRecord.ChargeType.REFUND ? ChargeTransactionRecordDiscountInfoHelper.negateChargeTransactionRecordDiscountInfo(recordedDiscountInfo) : recordedDiscountInfo));
                                    aAdditional.setDiscountInfo(ChargeTransactionRecordDiscountInfoHelper.mergeChargeTransactionRecordDiscountInfo(discountInfos));
                                    a.setAdditional(aAdditional);

                                    aAdditional.setDeductInfo(Stream.concat(Optional.ofNullable(aAdditional.getDeductInfo()).orElse(new ArrayList<>()).stream(), Optional.ofNullable(bAdditional.getDeductInfo()).orElse(new ArrayList<>()).stream()).collect(Collectors.toList()));
                                }

                                return a;
                            })
                    ).entrySet()
                    .stream()
                    .filter(entry -> {
                        ChargeTransactionRecord record = entry.getValue();

                        if (MathUtils.wrapBigDecimalCompare(record.getProductUnitCount(), BigDecimal.ZERO) <= 0 && MathUtils.wrapBigDecimalCompare(record.getTotalPrice(), BigDecimal.ZERO) <= 0) {
                            return false;
                        }

                        //数量退完了，金额没退完的情况，重置sceneType
                        if (allRefundedItemIds.contains(entry.getKey()) && MathUtils.wrapBigDecimalCompare(record.getProductUnitCount(), BigDecimal.ZERO) == 0) {
                            record.setSceneType(ChargeTransactionRecord.SceneType.OWE_PAY);
                        }

                        return true;
                    })
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        } else {
            return chargeTransactionRecords
                    .stream()
                    .filter(ChargeTransactionRecord::checkGroupByItemIdIsNotEmpty)
                    .collect(Collectors.toMap(record -> mergedChargeItemIdMap.getOrDefault(record.getGroupByItemId(), record.getGroupByItemId()),
                            //将数据库对象转换成内存对象
                            record -> {
                                ChargeTransactionRecord chargeTransactionRecord = JsonUtils.readValue(JsonUtils.dump(record), ChargeTransactionRecord.class);

                                if (chargeTransactionRecord.getType() != ChargeTransactionRecord.RecordType.AIR_PHARMACY) {
                                    chargeTransactionRecord.setChargeFormItemId(mergedChargeItemIdMap.getOrDefault(record.getGroupByItemId(), record.getGroupByItemId()));
                                }
                                return chargeTransactionRecord;
                            },
                            (a, b) -> {

                                BigDecimal flag = BigDecimal.ONE;
                                if (b.getChargeType() == ChargeTransactionRecord.ChargeType.REFUND) {
                                    flag = flag.negate();
                                }

                                a.setTotalCostPrice(MathUtils.wrapBigDecimalAdd(a.getTotalCostPrice(), MathUtils.wrapBigDecimalMultiply(b.getTotalCostPrice(), flag)));
                                a.setTotalPrice(MathUtils.wrapBigDecimalAdd(a.getTotalPrice(), MathUtils.wrapBigDecimalMultiply(b.getTotalPrice(), flag)));
                                a.setDiscountPrice(MathUtils.wrapBigDecimalAdd(a.getDiscountPrice(), MathUtils.wrapBigDecimalMultiply(b.getDiscountPrice(), flag)));
                                if (a.getSceneType() != ChargeTransactionRecord.SceneType.NORMAL && b.getSceneType() == ChargeTransactionRecord.SceneType.NORMAL) {
                                    a.setProductUnitCount(b.getProductUnitCount());
                                }

                                a.setSceneType(a.getSceneType() == ChargeTransactionRecord.SceneType.NORMAL || b.getSceneType() == ChargeTransactionRecord.SceneType.NORMAL
                                        ? ChargeTransactionRecord.SceneType.NORMAL
                                        : a.getSceneType()
                                );

                                if (CollectionUtils.isNotEmpty(a.getBatchInfos()) && CollectionUtils.isNotEmpty(b.getBatchInfos())) {
                                    a.setBatchInfos(mergeRecordBatchInfos(a.getBatchInfos(), a.getSceneType(), b.getBatchInfos(), b.getSceneType()));
                                }

                                ChargeTransactionRecordAdditional aAdditional = a.getAdditional();
                                ChargeTransactionRecordAdditional bAdditional = b.getAdditional();

                                if (Objects.nonNull(aAdditional) && Objects.nonNull(bAdditional)) {
                                    aAdditional.setDeductCount(MathUtils.wrapBigDecimalAdd(aAdditional.getDeductCount(), MathUtils.wrapBigDecimalMultiply(bAdditional.getDeductCount(), flag)));
                                    aAdditional.setDeductTotalPrice(MathUtils.wrapBigDecimalAdd(aAdditional.getDeductTotalPrice(), MathUtils.wrapBigDecimalMultiply(bAdditional.getDeductTotalCostPrice(), flag)));
                                    aAdditional.setDeductTotalCostPrice(MathUtils.wrapBigDecimalAdd(aAdditional.getDeductTotalCostPrice(), MathUtils.wrapBigDecimalMultiply(bAdditional.getDeductTotalCostPrice(), flag)));
                                    aAdditional.setPresentAmount(MathUtils.wrapBigDecimalAdd(aAdditional.getPresentAmount(), MathUtils.wrapBigDecimalMultiply(bAdditional.getPresentAmount(), flag)));
                                    aAdditional.setPrincipalAmount(MathUtils.wrapBigDecimalAdd(aAdditional.getPrincipalAmount(), MathUtils.wrapBigDecimalMultiply(bAdditional.getPrincipalAmount(), flag)));
                                    List<ChargeDiscountInfo> discountInfos = new ArrayList<>();
                                    Optional.ofNullable(aAdditional.getDiscountInfo())
                                            .ifPresent(discountInfos::add);
                                    Optional.ofNullable(bAdditional.getDiscountInfo())
                                            .ifPresent(recordedDiscountInfo -> discountInfos.add(b.getChargeType() == ChargeTransactionRecord.ChargeType.REFUND ? ChargeTransactionRecordDiscountInfoHelper.negateChargeTransactionRecordDiscountInfo(recordedDiscountInfo) : recordedDiscountInfo));
                                    aAdditional.setDiscountInfo(ChargeTransactionRecordDiscountInfoHelper.mergeChargeTransactionRecordDiscountInfo(discountInfos));
                                    a.setAdditional(aAdditional);
                                }
                                return a;
                            })
                    ).entrySet()
                    .stream()
                    .filter(entry -> {
                        ChargeTransactionRecord record = entry.getValue();

                        if (MathUtils.wrapBigDecimalCompare(record.getProductUnitCount(), BigDecimal.ZERO) <= 0 && MathUtils.wrapBigDecimalCompare(record.getTotalPrice(), BigDecimal.ZERO) <= 0) {
                            return false;
                        }

                        //数量退完了，金额没退完的情况，重置sceneType
                        if (allRefundedItemIds.contains(entry.getKey()) && MathUtils.wrapBigDecimalCompare(record.getProductUnitCount(), BigDecimal.ZERO) == 0) {
                            record.setSceneType(ChargeTransactionRecord.SceneType.OWE_PAY);
                        }

                        return true;
                    })
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        }
    }

    private static List<ChargeTransactionRecordBatchInfo> mergeRecordBatchInfos(List<ChargeTransactionRecordBatchInfo> aBatchInfos,
                                                                                int aSceneType,
                                                                                List<ChargeTransactionRecordBatchInfo> bBatchInfos,
                                                                                int bSceneType) {

        if (CollectionUtils.isEmpty(bBatchInfos)) {
            return aBatchInfos;
        }

        if (CollectionUtils.isEmpty(aBatchInfos)) {
            return bBatchInfos;
        }

        aBatchInfos.addAll(bBatchInfos);

        return new ArrayList<>(aBatchInfos.stream()
                .collect(Collectors.toMap(ChargeTransactionRecordBatchInfo::getChargeFormItemBatchInfoId, Function.identity(), (a, b) -> {
                    BigDecimal flag = BigDecimal.ONE;

                    if (b.getChargeType() == ChargeTransactionRecord.ChargeType.REFUND) {
                        flag = flag.negate();
                    }

                    if (aSceneType != ChargeTransactionRecord.SceneType.NORMAL && bSceneType == ChargeTransactionRecord.SceneType.NORMAL) {
                        a.setUnitCount(b.getUnitCount());
                    }



                    a.setReceivedPrice(MathUtils.wrapBigDecimalAdd(a.getReceivedPrice(), MathUtils.wrapBigDecimalMultiply(b.getReceivedPrice(), flag)));
                    a.setTotalCostPrice(MathUtils.wrapBigDecimalAdd(a.getTotalCostPrice(), MathUtils.wrapBigDecimalMultiply(b.getTotalCostPrice(), flag)));
                    a.setPromotionPrice(MathUtils.wrapBigDecimalAdd(a.getPromotionPrice(), MathUtils.wrapBigDecimalMultiply(b.getPromotionPrice(), flag)));
                    a.setSourceTotalPrice(MathUtils.wrapBigDecimalAdd(a.getSourceTotalPrice(), MathUtils.wrapBigDecimalMultiply(b.getSourceTotalPrice(), flag)));
                    List<ChargeDiscountInfo> discountInfos = new ArrayList<>();
                    Optional.ofNullable(a.getDiscountInfo())
                            .ifPresent(discountInfos::add);
                    Optional.ofNullable(b.getDiscountInfo())
                            .ifPresent(recordedDiscountInfo -> discountInfos.add(b.getChargeType() == ChargeTransactionRecord.ChargeType.REFUND ? ChargeTransactionRecordDiscountInfoHelper.negateChargeTransactionRecordDiscountInfo(recordedDiscountInfo) : recordedDiscountInfo));
                    a.setDiscountInfo(ChargeTransactionRecordDiscountInfoHelper.mergeChargeTransactionRecordDiscountInfo(discountInfos));

                    return a;
                }))
                .values());
    }

    /**
     * 处理母项的明细记录
     *
     * @param oweCombineTransactionRecord
     * @param recordDetails
     * @param chargeFormItems
     * @param itemIdRecordMap
     * @param historyExpenseItemIdRecordDetailIdMap
     * @param isFirstPay
     * @param chargeType
     * @param operatorId
     * @return
     */
    private List<ChargeOweCombineTransactionRecordDetail> generateComposeOweCombineTransactionRecordDetailsByChooseCore(ChargeOweCombineTransactionRecord oweCombineTransactionRecord,
                                                                                                                        List<ChargeOweCombineTransactionRecordDetail> recordDetails,
                                                                                                                        List<ChargeFormItem> chargeFormItems,
                                                                                                                        Map<String, ChargeTransactionRecord> itemIdRecordMap,
                                                                                                                        Map<String, List<ChargeOweCombineTransactionRecordDetail>> historyExpenseItemIdRecordDetailIdMap,
                                                                                                                        boolean isFirstPay,
                                                                                                                        int chargeType,
                                                                                                                        ParentType parentType,
                                                                                                                        boolean needDealClinicShebaoCodeMatchCostPrice,
                                                                                                                        int payType,
                                                                                                                        String operatorId) {
        Assert.notNull(parentType, "parentType can not be null");

        List<StatChargeOweRecordByChooseCalculateCell> composeCells = calculateComposeCells(recordDetails,
                chargeFormItems,
                itemIdRecordMap,
                historyExpenseItemIdRecordDetailIdMap,
                parentType,
                payType,
                needDealClinicShebaoCodeMatchCostPrice);

        return composeCells.stream()
                .map(composeCell -> composeCell.ofComposeChargeOweCombineTransactionRecordDetail(oweCombineTransactionRecord, isFirstPay, chargeType, operatorId))
                .collect(Collectors.toList());
    }

    /**
     * @param oweCombineTransactionRecord           本次还款的流水
     * @param totalOwePrice                         总的欠费金额
     * @param itemIdRecordMap                       收费项的id和合并之后的record对象组成的map
     * @param historyExpenseItemIdRecordDetailIdMap 已经记录了还款明细的费用项id分组list
     * @param isFirstPay                            是否为第一次支付
     * @param payType                               支付类型
     * @param operatorId                            操作人id
     * @return
     */
    private List<ChargeOweCombineTransactionRecordDetail> generateOweCombineTransactionRecordDetailsByChooseCore(ChargeOweCombineTransactionRecord oweCombineTransactionRecord,
                                                                                                                 BigDecimal totalOwePrice,
                                                                                                                 Map<String, ChargeTransactionRecord> itemIdRecordMap,
                                                                                                                 Map<String, List<ChargeOweCombineTransactionRecordDetail>> historyExpenseItemIdRecordDetailIdMap,
                                                                                                                 Map<String, List<ChargeOweCombineTransactionRecordDetailBatchInfo>> historyItemBatchInfoIdDetailBatchInfoMap,
                                                                                                                 boolean isFirstPay,
                                                                                                                 int payType,
                                                                                                                 int chargeType,
                                                                                                                 String operatorId) {
        //构造摊费对象
        List<StatChargeOweRecordByChooseCalculateCell> cells = itemIdRecordMap
                .values()
                .stream()
                .filter(record -> record.getComposeType() != ComposeType.COMPOSE && record.getGoodsFeeType() != GoodsFeeType.FEE_PARENT)
                .map(record -> {
                    StatChargeOweRecordByChooseCalculateCell cell = new StatChargeOweRecordByChooseCalculateCell();
                    cell.setId(record.getGroupByItemId())
                            .setLimitedRecord(record)
                            .setHistoryRecordDetails(historyExpenseItemIdRecordDetailIdMap.getOrDefault(record.getGroupByItemId(), new ArrayList<>()));

                    if (CollectionUtils.isNotEmpty(record.getBatchInfos())) {
                        cell.setBatchInfoCalculateCells(record.getBatchInfos().stream()
                                .map(recordBatchInfo -> {
                                    StatChargeOweRecordBatchInfoCalculateCell batchInfoCalculateCell = new StatChargeOweRecordBatchInfoCalculateCell();
                                    batchInfoCalculateCell.setId(recordBatchInfo.getChargeFormItemBatchInfoId())
                                            .setLimitedBatchInfo(recordBatchInfo)
                                            .setHistoryBatchInfos(historyItemBatchInfoIdDetailBatchInfoMap.getOrDefault(recordBatchInfo.getChargeFormItemBatchInfoId(), new ArrayList<>()));
                                    return batchInfoCalculateCell;
                                })
                                .collect(Collectors.toList())
                        );
                    }

                    return cell;
                })
                .collect(Collectors.toList());

        StatChargeOweRecordByChooseCalculateCellHelper.calculateCell(totalOwePrice, oweCombineTransactionRecord.getAmount(), cells, payType, isFirstPay);

        return cells.stream()
                .map(cell -> cell.ofChargeOweCombineTransactionRecordDetail(oweCombineTransactionRecord, isFirstPay, chargeType, operatorId))
                .collect(Collectors.toList());
    }


    private static int convertToChargeType(int payType) {
        int chargeType;
        switch (payType) {
            case PayType.PAID:
                chargeType = ChargeTransactionRecord.ChargeType.CHARGED;
                break;
            case PayType.PARTED_PAID:
                chargeType = ChargeTransactionRecord.ChargeType.PART_CHARGED;
                break;
            case PayType.PAID_BACK:
            case PayType.PARTED_PAID_BACK:
                chargeType = ChargeTransactionRecord.ChargeType.REFUND;
                break;
            case PayType.REFUND:
            case PayType.PARTED_REFUND:
                chargeType = ChargeTransactionRecord.ChargeType.REFUND;
                break;
            default:
                chargeType = 0;
        }

        return chargeType;
    }

}
