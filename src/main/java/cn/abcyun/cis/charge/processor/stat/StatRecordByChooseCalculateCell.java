package cn.abcyun.cis.charge.processor.stat;

import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.helper.ChargeTransactionRecordDiscountInfoHelper;
import cn.abcyun.cis.charge.model.ChargeForm;
import cn.abcyun.cis.charge.model.ChargeFormItem;
import cn.abcyun.cis.charge.model.ChargeTransactionRecord;
import cn.abcyun.cis.charge.model.ChargeTransactionRecordAdditional;
import cn.abcyun.cis.charge.processor.StatRecordAffectedDeductedItem;
import cn.abcyun.cis.charge.processor.StatRecordProcessor;
import cn.abcyun.cis.charge.service.dto.ChargeDiscountInfo;
import cn.abcyun.cis.commons.util.MathUtils;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Data
@Accessors(chain = true)
public abstract class StatRecordByChooseCalculateCell {
    protected String id;
    protected int chargeVersion;
    protected boolean isPaidback;
    protected List<ChargeTransactionRecord> historyRecords = new ArrayList<>();
    /**
     * 退费时关联的chargeFormItem的收费历史records
     */
    protected List<ChargeTransactionRecord> associateHistoryRecords = new ArrayList<>();
    protected ChargeFormItem chargeFormItem;

    protected StatRecordAffectedDeductedItem deductedItem;
    //记录到record表的折扣后的金额
    protected BigDecimal toRecordDiscountedPrice = BigDecimal.ZERO;
    //记录到record表的折扣金额
    protected BigDecimal toRecordDiscountPrice = BigDecimal.ZERO;
    //记录到record表的数量
    protected BigDecimal toRecordUnitCount = BigDecimal.ZERO;
    //记录到record表的成本价
    protected BigDecimal toRecordTotalCostPrice = BigDecimal.ZERO;
    //记录到record表的原价
    protected BigDecimal toRecordSourceTotalPrice = BigDecimal.ZERO;

    protected List<StatRecordBatchInfoCalculateCell> batchInfoCalculateCells;

    protected ChargeForm chargeForm;
    protected int isAirPharmacy;

    protected BigDecimal toRecordDeductPrice = BigDecimal.ZERO;

    protected BigDecimal toRecordDeductedUnitAdjustmentFee = BigDecimal.ZERO;
    protected BigDecimal toRecordVerifyUnitAdjustmentFee = BigDecimal.ZERO;

    protected BigDecimal toRecordDeductUnitCount = BigDecimal.ZERO;

    protected BigDecimal toRecordDeductTotalCostPrice = BigDecimal.ZERO;

    private List<ChargeTransactionRecordAdditional.RecordDeductInfo> deductInfo;

    protected int toRecordSceneType;

    protected List<StatRecordByChooseCalculateCell> composeChildrenCells;

    protected List<StatRecordByChooseCalculateCell> composeChildrenItemPaidCells;

    /**
     * 本次要记录进数据库的record，在计算套餐母项的时候才用到了
     */
    protected List<ChargeTransactionRecord> thisTimeRecords = new ArrayList<>();

    //优惠&议价
    protected ChargeDiscountInfo toRecordPromotionDiscountInfo;

    //服务抵扣
    protected List<ChargeDiscountInfo.DeductDiscountInfo> toRecordDeductDiscountInfos;

    //核销抵扣
    protected List<ChargeDiscountInfo.VerifyDeductInfo> toRecordVerifyDiscountInfos;


    public abstract BigDecimal getDiscountedPrice();

    public abstract BigDecimal getDiscountPrice();

    public abstract BigDecimal getRecordedDiscountedPrice();


    //最终落地到record表的对象，是将toRecordPromotionDiscountInfo和toRecordDeductDiscountInfo组合出来的对象
    public ChargeDiscountInfo getToRecordDiscountInfoResult() {

        if (toRecordPromotionDiscountInfo == null && CollectionUtils.isEmpty(toRecordDeductDiscountInfos) && CollectionUtils.isEmpty(toRecordVerifyDiscountInfos)) {
            return null;
        }

        ChargeDiscountInfo result = new ChargeDiscountInfo();
        Optional.ofNullable(toRecordPromotionDiscountInfo)
                .ifPresent(c -> {
                    BeanUtils.copyProperties(c, result);
                    result.setDeductDiscountInfos(new ArrayList<>());
                    result.setDeductedUnitAdjustmentFee(toRecordDeductedUnitAdjustmentFee);
                    result.setVerifyUnitAdjustmentFee(toRecordVerifyUnitAdjustmentFee);
                    result.setUnitAdjustmentFee(MathUtils.wrapBigDecimalAdd(result.getDeductedUnitAdjustmentFee(), result.getVerifyUnitAdjustmentFee(), result.getUnitAdjustmentFeeIgnoreDeduct()));
                });
        Optional.ofNullable(toRecordDeductDiscountInfos).ifPresent(result::setDeductDiscountInfos);

        Optional.ofNullable(toRecordVerifyDiscountInfos).ifPresent(result::setVerifyDeductInfos);

        return result;
    }

    public BigDecimal getToRecordDiscountedPriceResult() {
        return toRecordDiscountedPrice;
    }

    public BigDecimal getToRecordDiscountPriceResult() {
        return MathUtils.wrapBigDecimalAdd(getToRecordDiscountPrice(), getToRecordDeductPrice());
    }

    public BigDecimal getToRecordUnitCountResult() {
        return MathUtils.wrapBigDecimalAdd(getToRecordUnitCount(), getToRecordDeductUnitCount());
    }

    public BigDecimal getToRecordTotalCostPriceResult() {
        return MathUtils.wrapBigDecimalAdd(getToRecordTotalCostPrice(), getToRecordDeductTotalCostPrice());
    }

    //反算原价
    public BigDecimal getToRecordSourceTotalPriceResult () {
        ChargeDiscountInfo toRecordDiscountInfoResult = getToRecordDiscountInfoResult();

        BigDecimal toRecordSourceTotalPrice = getToRecordDiscountedPrice()
                .subtract(Optional.ofNullable(toRecordDiscountInfoResult)
                        .map(ChargeDiscountInfo::calculateSinglePromotionPrice)
                        .orElse(BigDecimal.ZERO)
                )
                .subtract(Optional.ofNullable(toRecordDiscountInfoResult)
                        .map(ChargeDiscountInfo::calculatePackagePromotionPrice)
                        .orElse(BigDecimal.ZERO))
                .subtract(Optional.ofNullable(toRecordDiscountInfoResult)
                        .map(ChargeDiscountInfo::getUnitAdjustmentFee)
                        .orElse(BigDecimal.ZERO))
                .subtract(Optional.ofNullable(toRecordDiscountInfoResult)
                        .map(ChargeDiscountInfo::getAdjustmentFee)
                        .orElse(BigDecimal.ZERO));

        if (MathUtils.wrapBigDecimalCompare(toRecordSourceTotalPrice, BigDecimal.ZERO) < 0) {
            throw new IllegalStateException("toRecordSourceTotalPrice小于0， toRecordSourceTotalPrice:" + toRecordSourceTotalPrice);
        }

        return toRecordSourceTotalPrice;
    }

    public BigDecimal getToRecordTotalPriceResult() {
        return getToRecordDiscountedPriceResult().subtract(getToRecordDiscountPriceResult());
    }

    public BigDecimal getTotalPrice() {
        if (isAirPharmacy == 0) {
            return MathUtils.wrapBigDecimalOrZero(chargeFormItem.getTotalPrice()).add(chargeFormItem.getAllDeductPromotionPrice());
        } else {
            return MathUtils.wrapBigDecimalOrZero(chargeForm.getTotalPrice());
        }
    }

    private List<ChargeDiscountInfo> getRecordedChargeTransactionRecordDiscountInfos() {

        return historyRecords.stream()
                .filter(record -> record.getAdditional() != null && record.getAdditional().getDiscountInfo() != null)
                .map(record -> record.getAdditional().getDiscountInfo())
                .collect(Collectors.toList());
    }

    public ChargeDiscountInfo getRecordedChargeTransactionRecordDiscountInfo() {
        List<ChargeDiscountInfo> recordedChargeTransactionRecordDiscountInfos = getRecordedChargeTransactionRecordDiscountInfos();

        return ChargeTransactionRecordDiscountInfoHelper.mergeChargeTransactionRecordDiscountInfo(recordedChargeTransactionRecordDiscountInfos);
    }

    public ChargeDiscountInfo getItemPromotionInfo() {
        if (isAirPharmacy == 1) {
            return chargeForm.getPromotionInfo();
        }

        return chargeFormItem.getPromotionInfo();
    }

    //剩余可记录的总金额
    public BigDecimal getLeftCanRecordPrice() {
        return cn.abcyun.cis.charge.util.MathUtils.max(BigDecimal.ZERO, getDiscountedPrice().subtract(getRecordedDiscountedPrice()));
    }

    public BigDecimal getUnitCount() {
        if (isAirPharmacy == 0) {
            return MathUtils.wrapBigDecimalSubtract(chargeFormItem.getUnitCount(), chargeFormItem.getDeductAndVerifyTotalCount());
        } else {
            return BigDecimal.ONE;
        }
    }

    public BigDecimal getTotalCostPrice() {
        if (isAirPharmacy == 0) {
            BigDecimal excludeDeductTotalCount = MathUtils.calculateTotalCount(chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount()).subtract(MathUtils.wrapBigDecimalOrZero(chargeFormItem.getDeductTotalCount()));

            if (excludeDeductTotalCount.compareTo(BigDecimal.ZERO)<= 0) {
                return BigDecimal.ZERO;
            }

            BigDecimal deductedCostPrice = MathUtils.wrapBigDecimalMultiply(chargeFormItem.getDeductAndVerifyTotalCount(), chargeFormItem.getUnitCostPrice()).setScale(2, RoundingMode.DOWN);

            return chargeFormItem.getTotalCostPrice().subtract(deductedCostPrice);
        } else {
            return MathUtils.wrapBigDecimalOrZero(chargeForm.getTotalCostPrice());
        }
    }

    public BigDecimal getDeductTotalCostPrice() {
        if (isAirPharmacy == 0) {
            return MathUtils.wrapBigDecimalOrZero(chargeFormItem.getUnitCostPrice()).multiply(MathUtils.wrapBigDecimalOrZero(chargeFormItem.getDeductTotalCount()));
        } else {
            return BigDecimal.ZERO;
        }
    }

    //根据退费项id找到当时收费的record数据
    public BigDecimal getRecordedDiscountedPriceByPayChargeTransactionId(String payChargeTransactionId) {

        if (StringUtils.isEmpty(payChargeTransactionId)) {
            return BigDecimal.ZERO;
        }

        if (isAirPharmacy == 0) {
            String associateFormItemId = chargeFormItem.getAssociateFormItemId();
            if (StringUtils.isEmpty(associateFormItemId)) {
                return BigDecimal.ZERO;
            }
        }

        return associateHistoryRecords.stream()
                .filter(record -> StringUtils.equals(record.getTransactionId(), payChargeTransactionId))
                .map(record -> MathUtils.wrapBigDecimalAdd(record.getTotalPrice(), record.getDiscountPrice()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    //历史数据
    public BigDecimal getRecordedTotalPrice() {
        return historyRecords.stream()
                .map(record -> {
                    BigDecimal flag = BigDecimal.ONE;
                    if (isAirPharmacy == 0) {
                        //退费项的flag永远=1，收费项才区分正负
                        flag = chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.REFUNDED && record.getChargeType() == ChargeTransactionRecord.ChargeType.REFUND ? BigDecimal.ONE.negate() : BigDecimal.ONE;
                    } else {
                        flag = chargeForm.getStatus() != Constants.ChargeFormStatus.REFUNDED && record.getChargeType() == ChargeTransactionRecord.ChargeType.REFUND ? BigDecimal.ONE.negate() : BigDecimal.ONE;
                    }

                    //计算除了抵扣之外的totalPrice
                    BigDecimal deductTotalPrice = Optional.ofNullable(record.getAdditional()).map(ChargeTransactionRecordAdditional::getDeductTotalPrice).orElse(BigDecimal.ZERO);

                    return flag.multiply(MathUtils.wrapBigDecimalSubtract(record.getTotalPrice(), deductTotalPrice));
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public BigDecimal getRecordedDiscountPrice() {
        return historyRecords.stream()
                .map(record -> {
                    BigDecimal flag = BigDecimal.ONE;
                    if (isAirPharmacy == 0) {
                        //退费项的flag永远=1，收费项才区分正负
                        flag = chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.REFUNDED && record.getChargeType() == ChargeTransactionRecord.ChargeType.REFUND ? BigDecimal.ONE.negate() : BigDecimal.ONE;
                    } else {
                        flag = chargeForm.getStatus() != Constants.ChargeFormStatus.REFUNDED && record.getChargeType() == ChargeTransactionRecord.ChargeType.REFUND ? BigDecimal.ONE.negate() : BigDecimal.ONE;
                    }

                    BigDecimal deductTotalPrice = Optional.ofNullable(record.getAdditional()).map(ChargeTransactionRecordAdditional::getDeductTotalPrice).orElse(BigDecimal.ZERO).negate();

                    return flag.multiply(MathUtils.wrapBigDecimalSubtract(record.getDiscountPrice(), deductTotalPrice));
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public BigDecimal getRecordedUnitCount() {
        return historyRecords.stream()
                .map(record -> {
                    BigDecimal flag = BigDecimal.ONE;
                    if (isAirPharmacy == 0) {
                        flag = chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.REFUNDED && record.getChargeType() == ChargeTransactionRecord.ChargeType.REFUND ? BigDecimal.ONE.negate() : BigDecimal.ONE;
                    } else {
                        flag = chargeForm.getStatus() != Constants.ChargeFormStatus.REFUNDED && record.getChargeType() == ChargeTransactionRecord.ChargeType.REFUND ? BigDecimal.ONE.negate() : BigDecimal.ONE;
                    }

                    BigDecimal deductCount = Optional.ofNullable(record.getAdditional()).map(ChargeTransactionRecordAdditional::getDeductCount).orElse(BigDecimal.ZERO);

                    return flag.multiply(MathUtils.wrapBigDecimalSubtract(record.getProductUnitCount(), deductCount));
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public BigDecimal getRecordedTotalCostPrice() {
        return historyRecords.stream()
                .map(record -> {
                    BigDecimal flag = BigDecimal.ONE;
                    if (isAirPharmacy == 0) {
                        flag = chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.REFUNDED && record.getChargeType() == ChargeTransactionRecord.ChargeType.REFUND ? BigDecimal.ONE.negate() : BigDecimal.ONE;
                    } else {
                        flag = chargeForm.getStatus() != Constants.ChargeFormStatus.REFUNDED && record.getChargeType() == ChargeTransactionRecord.ChargeType.REFUND ? BigDecimal.ONE.negate() : BigDecimal.ONE;
                    }

                    BigDecimal deductTotalCostPrice = Optional.ofNullable(record.getAdditional()).map(ChargeTransactionRecordAdditional::getDeductTotalCostPrice).orElse(BigDecimal.ZERO);

                    return flag.multiply(MathUtils.wrapBigDecimalSubtract(record.getTotalCostPrice(), deductTotalCostPrice));
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

        /**
         * 除了抵扣之外的金额
         * @return
         */
    public BigDecimal getLeftToRecordDiscountedPrice() {
        return getDiscountedPrice().subtract(getRecordedDiscountedPrice());
    }

    public BigDecimal getThisTimeReceivableFee() {

        if (isAirPharmacy == 1) {
            return chargeForm.getThisTimeReceivableFee();
        }
        return chargeFormItem.getThisTimeReceivableFee();
    }

    public void bindSceneTypeAndProductUnitCount(int payType) {
        //计算历史已经记录了的总数量
        BigDecimal historyProductUnitCount = getRecordedUnitCount();

        if (StatRecordProcessor.PayType.isPaid(payType)) {
            //收费场景
            if (getUnitCount().compareTo(BigDecimal.ZERO) == 0 || getUnitCount().compareTo(historyProductUnitCount) > 0 || toRecordDeductUnitCount.compareTo(BigDecimal.ZERO) > 0) {
                toRecordSceneType = ChargeTransactionRecord.SceneType.NORMAL;
            } else {
                toRecordSceneType = ChargeTransactionRecord.SceneType.OWE_PAY;
            }
            toRecordUnitCount = cn.abcyun.cis.charge.util.MathUtils.max(BigDecimal.ZERO, getUnitCount().subtract(historyProductUnitCount));
        } else {
            //退费场景，区分是部分收费状态下的退费场景还是收完再退场景
            if (isPaidback) {
                //部分收费状态下的退费
                boolean containRefundRecord = historyRecords.stream()
                        .anyMatch(record -> record.getChargeType() == ChargeTransactionRecord.ChargeType.REFUND);
                if (!containRefundRecord) {
                    toRecordSceneType = ChargeTransactionRecord.SceneType.NORMAL;
                    toRecordUnitCount = getUnitCount();
                } else {
                    toRecordSceneType = ChargeTransactionRecord.SceneType.OWE_REFUND;
                    toRecordUnitCount = BigDecimal.ZERO;
                }
            } else {
                //收完全退
                if (getUnitCount().compareTo(historyProductUnitCount) > 0 || toRecordDeductUnitCount.compareTo(BigDecimal.ZERO) > 0) {
                    toRecordSceneType = ChargeTransactionRecord.SceneType.NORMAL;
                    toRecordUnitCount = getUnitCount();
                } else {
                    toRecordSceneType = ChargeTransactionRecord.SceneType.OWE_REFUND;
                    toRecordUnitCount = BigDecimal.ZERO;
                }
            }
        }
    }

    public void dealDeductItem() {

        if (deductedItem == null) {
            return;
        }

        if (isAirPharmacy == 1) {
            return;
        }

        toRecordDeductPrice = deductedItem.getTotalPrice();
        toRecordDeductUnitCount = MathUtils.wrapBigDecimalOrZero(deductedItem.getCount());
        toRecordDeductedUnitAdjustmentFee = Optional.ofNullable(deductedItem.getDeductInfo()).orElse(new ArrayList<>()).stream()
                .filter(info -> info.getType() == ChargeTransactionRecordAdditional.DeductType.PATIENT_CARD).map(StatRecordAffectedDeductedItem::getDeductedUnitAdjustmentFee).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        toRecordVerifyUnitAdjustmentFee = Optional.ofNullable(deductedItem.getDeductInfo()).orElse(new ArrayList<>()).stream()
                .filter(info -> info.getType() == ChargeTransactionRecordAdditional.DeductType.VERIFY).map(StatRecordAffectedDeductedItem::getDeductedUnitAdjustmentFee).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        toRecordDeductTotalCostPrice = deductedItem.getTotalCostPrice();


        deductInfo = Optional.ofNullable(deductedItem.getDeductInfo()).orElse(new ArrayList<>()).stream().map(info -> {
            ChargeTransactionRecordAdditional.RecordDeductInfo deductInfo = new ChargeTransactionRecordAdditional.RecordDeductInfo();
            deductInfo.setDeductTotalPrice(info.getTotalPrice());
            deductInfo.setDeductCount(info.getCount());
            deductInfo.setDeductTotalCostPrice(info.getTotalCostPrice());
            deductInfo.setType(info.getType());

            return deductInfo;

        }).collect(Collectors.toList());

        Map<String, ChargeDiscountInfo.DeductDiscountInfo> recordedDeductDiscountInfoMap = Optional.ofNullable(getRecordedChargeTransactionRecordDiscountInfo())
                .map(ChargeDiscountInfo::getDeductDiscountInfos)
                .orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.toMap(ChargeDiscountInfo.DeductDiscountInfo::getPresentId, Function.identity(), (a, b) -> a));


        List<ChargeDiscountInfo.VerifyDeductInfo> verifyDeductInfos = Optional.ofNullable(getRecordedChargeTransactionRecordDiscountInfo())
                .map(ChargeDiscountInfo::getVerifyDeductInfos)
                .orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.toList());

        if (isPaidback) {
            this.toRecordDeductDiscountInfos = new ArrayList<>(recordedDeductDiscountInfoMap.values());
            this.toRecordVerifyDiscountInfos = verifyDeductInfos;
            return;
        }

        List<ChargeDiscountInfo.DeductDiscountInfo> itemDeductDiscountInfos = Optional.ofNullable(chargeFormItem.getPromotionInfo()).map(ChargeDiscountInfo::getDeductDiscountInfos).orElse(new ArrayList<>());
        List<ChargeDiscountInfo.VerifyDeductInfo> itemVerifyDiscountInfos = Optional.ofNullable(chargeFormItem.getPromotionInfo()).map(ChargeDiscountInfo::getVerifyDeductInfos).orElse(new ArrayList<>());

        if (!CollectionUtils.isEmpty(itemDeductDiscountInfos)) {
            this.toRecordDeductDiscountInfos = itemDeductDiscountInfos;
        }

        if (!CollectionUtils.isEmpty(itemVerifyDiscountInfos)) {
            this.toRecordVerifyDiscountInfos = itemVerifyDiscountInfos;
        }
    }

    public boolean isAllRefunded() {

        BigDecimal recordedUnitCount = historyRecords.stream()
                .map(record -> {
                    BigDecimal flag = record.getChargeType() == ChargeTransactionRecord.ChargeType.REFUND ? BigDecimal.ONE.negate() : BigDecimal.ONE;
                    BigDecimal deductCount = Optional.ofNullable(record.getAdditional()).map(ChargeTransactionRecordAdditional::getDeductCount).orElse(BigDecimal.ZERO);
                    return flag.multiply(MathUtils.wrapBigDecimalSubtract(record.getProductUnitCount(), deductCount));
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal recordedTotalPrice = historyRecords.stream()
                .map(record -> {
                    BigDecimal flag = record.getChargeType() == ChargeTransactionRecord.ChargeType.REFUND ? BigDecimal.ONE.negate() : BigDecimal.ONE;

                    //计算除了抵扣之外的totalPrice
                    BigDecimal deductTotalPrice = Optional.ofNullable(record.getAdditional()).map(ChargeTransactionRecordAdditional::getDeductTotalPrice).orElse(BigDecimal.ZERO);

                    return flag.multiply(MathUtils.wrapBigDecimalSubtract(record.getTotalPrice(), deductTotalPrice));
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return recordedUnitCount.compareTo(BigDecimal.ZERO) <= 0 && recordedTotalPrice.compareTo(BigDecimal.ZERO) <= 0;
    }

    public boolean isContainPaidChargeTransactionId(String chargeTransactionId) {
        return historyRecords.stream()
                .anyMatch(record -> StringUtils.equals(record.getTransactionId(), chargeTransactionId));
    }

    public int getProductType() {
        return chargeFormItem.getProductType();
    }

    public boolean canDealBatchInfo() {

        if (CollectionUtils.isEmpty(getBatchInfoCalculateCells())) {
            return false;
        }

        //兼容历史数据，只有有lockId的才走新的逻辑，老的数据里面没有lockId，也没有记录批次明细，老单据统一都不记录批次明细
        if (getChargeFormItem() == null || StringUtils.isEmpty(getChargeFormItem().getLockId())) {
            return false;
        }

        return true;
    }
}
