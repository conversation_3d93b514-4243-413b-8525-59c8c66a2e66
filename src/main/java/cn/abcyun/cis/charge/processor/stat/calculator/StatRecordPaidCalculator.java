package cn.abcyun.cis.charge.processor.stat.calculator;

import cn.abcyun.cis.charge.processor.stat.StatRecordBatchInfoCalculateCell;
import cn.abcyun.cis.charge.processor.stat.StatRecordByChooseCalculateCell;
import cn.abcyun.cis.charge.service.dto.ChargeDiscountInfo;
import cn.abcyun.cis.charge.util.JsonUtils;
import cn.abcyun.cis.charge.util.MathUtils;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Objects;

@Slf4j
public class StatRecordPaidCalculator extends StatRecordPartedPaidCalculator {

    @Override
    public void flatCellBatchInfoDiscountedPrice(StatRecordByChooseCalculateCell calculateCell) {
        for (StatRecordBatchInfoCalculateCell batchInfoCalculateCell : calculateCell.getBatchInfoCalculateCells()) {
            batchInfoCalculateCell.setToRecordDiscountedPrice(batchInfoCalculateCell.getLeftToRecordDiscountedPrice());
        }
    }

    /**
     * 如果是已支付，直接记录剩余的值就行
     * @param calculateCell
     */
    @Override
    public void flatCellBatchInfoDiscountInfo(StatRecordByChooseCalculateCell calculateCell) {

        ChargeDiscountInfo toRecordPromotionDiscountInfo = calculateCell.getToRecordDiscountInfoResult();
        if (Objects.isNull(toRecordPromotionDiscountInfo)) {
            return;
        }
        //顺序，整单议价 -> 折扣 -> 单项议价
        for (StatRecordBatchInfoCalculateCell batchInfoCalculateCell : calculateCell.getBatchInfoCalculateCells()) {
            batchInfoCalculateCell.setToRecordPromotionDiscountInfo(batchInfoCalculateCell.getLeftToRecordPromotionInfo());
            batchInfoCalculateCell.setToRecordSourceTotalPrice(batchInfoCalculateCell.getLeftToRecordSourceTotalPrice());
        }

        //校验平摊的金额是否正确
        BigDecimal itemPromotionAllPrice = toRecordPromotionDiscountInfo.calculateAllPrice();
        BigDecimal batchPromotionAllPrice = calculateCell.getBatchInfoCalculateCells()
                .stream()
                .map(StatRecordBatchInfoCalculateCell::getToRecordPromotionDiscountInfo)
                .filter(Objects::nonNull)
                .map(ChargeDiscountInfo::calculateAllPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (MathUtils.wrapBigDecimalCompare(itemPromotionAllPrice, batchPromotionAllPrice) != 0) {
            log.error("批次平摊优惠信息失败, itemPromotionAllPrice: {}, batchPromotionAllPrice: {}, calculateCell: {}", itemPromotionAllPrice, batchPromotionAllPrice, JsonUtils.dump(calculateCell));
            throw new IllegalStateException("批次平摊优惠信息失败");
        }
    }

    @Override
    public void fillOtherFields(StatRecordByChooseCalculateCell calculateCell) {
        for (StatRecordBatchInfoCalculateCell batchInfoCalculateCell : calculateCell.getBatchInfoCalculateCells()) {
            batchInfoCalculateCell.setToRecordTotalCostPrice(batchInfoCalculateCell.getLeftToRecordTotalCostPrice());
        }

        BigDecimal batchTotalCostPrice = calculateCell.getBatchInfoCalculateCells()
                .stream()
                .map(StatRecordBatchInfoCalculateCell::getToRecordTotalCostPrice)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        //这里直接用批次的成本价总和覆盖item的成本价
        calculateCell.setToRecordTotalCostPrice(batchTotalCostPrice);
    }

}
