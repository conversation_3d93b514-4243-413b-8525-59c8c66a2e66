package cn.abcyun.cis.charge.processor;


import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsBatchInfo;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.model.ChargeFormItemBatchInfo;
import cn.abcyun.cis.charge.processor.calculate.CalculateModel;
import cn.abcyun.cis.charge.processor.discount.CalculatePromotionItemBatchInfo;
import cn.abcyun.cis.charge.processor.discount.ItemDeductedDetail;
import cn.abcyun.cis.charge.processor.discount.PromotionSimple;
import cn.abcyun.cis.charge.processor.limitprice.BaseLimitPriceInfo;
import cn.abcyun.cis.charge.processor.limitprice.CalculateLimitPriceItem;
import cn.abcyun.cis.charge.processor.limitprice.GoodsLimitPriceInfo;
import cn.abcyun.cis.charge.processor.limitprice.LimitPriceConstants;
import cn.abcyun.cis.charge.service.dto.ChargeDiscountInfo;
import cn.abcyun.cis.charge.service.dto.ChargeFormItemBatchInfoView;
import cn.abcyun.cis.charge.util.*;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

public class ItemBatchInfoProcessor {

    private final ChargeFormItemBatchInfo chargeFormItemBatchInfo;

    private GoodsBatchInfo goodsBatchInfo;

    private List<ChargeFormItemBatchInfo> refundChargeFormItemBatchInfos = new ArrayList<>();
    /**
     * 应收金额
     */
    @Setter
    private BigDecimal receivableFee = BigDecimal.ZERO;

    /**
     * 社保应收金额sheBaoReceivableFee
     */
    private BigDecimal sheBaoReceivableFee = BigDecimal.ZERO;

    public String getItemBatchInfoId() {
        return chargeFormItemBatchInfo.getId();
    }

    public void setGoodsBatchInfo(GoodsBatchInfo goodsBatchInfo) {
        this.goodsBatchInfo = goodsBatchInfo;
    }

    public ChargeFormItemBatchInfo getChargeFormItemBatchInfo() {
        return chargeFormItemBatchInfo;
    }

    public ItemBatchInfoProcessor(ChargeFormItemBatchInfo chargeFormItemBatchInfo, boolean updateReceivableFee) {
        this.chargeFormItemBatchInfo = chargeFormItemBatchInfo;

        if (updateReceivableFee) {
            receivableFee = MathUtils.max(BigDecimal.ZERO, MathUtils.wrapBigDecimalSubtract(chargeFormItemBatchInfo.calculateReceivableFee(), chargeFormItemBatchInfo.getReceivedPrice()));
            sheBaoReceivableFee = receivableFee;
        }
    }

    public int getIsNotCharged() {
        return chargeFormItemBatchInfo.getIsNotCharged();
    }

    public int getIsOld() {
        return chargeFormItemBatchInfo.getIsOld();
    }

    public CalculateLimitPriceItem.CalculateLimitPriceItemBatchInfo generateCalculateLimitPriceItemBatchInfo(int dismounting) {

        CalculateLimitPriceItem.CalculateLimitPriceItemBatchInfo itemBatchInfo = new CalculateLimitPriceItem.CalculateLimitPriceItemBatchInfo();
        itemBatchInfo.setId(chargeFormItemBatchInfo.getId())
                .setStockId(chargeFormItemBatchInfo.getStockId())
                .setBatchId(chargeFormItemBatchInfo.getBatchId())
                .setUnitCostPrice(chargeFormItemBatchInfo.getUnitCostPrice())
                .setTotalCount(chargeFormItemBatchInfo.getUnitCount())
                .setReceivableTotalPrice(receivableFee);
        itemBatchInfo.setReceivableUnitPrice(ChargeFormItemUtils.calculateReceivableUnitFee(receivableFee,
                        chargeFormItemBatchInfo.getUnitPrice(),
                        chargeFormItemBatchInfo.getUnitCount(),
                        BigDecimal.ONE,
                        BigDecimal.ZERO)
                );
        itemBatchInfo.setGoodsLimitPriceInfo(GoodsLimitPriceInfo.createGoodsLimitPriceInfo(goodsBatchInfo.getShebaoPayLimitPriceInfo(), dismounting));

        return itemBatchInfo;
    }

    /**
     * 应用限价价格
     *
     * @param limitPriceItemBatchInfo
     */
    public void applyLimitPrice(CalculateLimitPriceItem.CalculateLimitPriceItemBatchInfo limitPriceItemBatchInfo) {

        if (Objects.isNull(limitPriceItemBatchInfo)) {
            return;
        }

        chargeFormItemBatchInfo.setLimitInfo(limitPriceItemBatchInfo.getLimitInfo());
        if (!limitPriceItemBatchInfo.isLimited()) {
            return;
        }

        updateSheBaoReceivablePrice(limitPriceItemBatchInfo.getSheBaoReceivablePrice());
        updateReceivablePrice(limitPriceItemBatchInfo.calculateReceivableTotalPrice());

        if (Objects.nonNull(limitPriceItemBatchInfo.getLimitInfo()) && limitPriceItemBatchInfo.getLimitInfo().getExceedLimitPriceRule() == Constants.ExceedLimitPriceRule.NO_PAY) {
            chargeFormItemBatchInfo.setUnitPrice(limitPriceItemBatchInfo.getLimitUnitPrice());
            chargeFormItemBatchInfo.setTotalPrice(limitPriceItemBatchInfo.getLimitTotalPrice());
        }

        chargeFormItemBatchInfo.setIsUseLimitPrice(1);

        //维护限价金额
        if (Objects.nonNull(limitPriceItemBatchInfo.getLimitInfo()) && limitPriceItemBatchInfo.isLimited()) {
            updateLimitFee(limitPriceItemBatchInfo.getLimitInfo().getLimitFee());
        }

    }


    public void setReceivableFeeAndShebaoReceivableFee(BigDecimal flatPrice) {
        receivableFee = flatPrice;
        if (chargeFormItemBatchInfo.getIsUseLimitPrice() != 1) {
            sheBaoReceivableFee = receivableFee;
        }
    }

    public void updateReceivableFee(BigDecimal flatPrice) {
        receivableFee = chargeFormItemBatchInfo.calculateReceivableFee().add(MathUtils.wrapBigDecimalOrZero(flatPrice));
        if (chargeFormItemBatchInfo.getIsUseLimitPrice() != 1) {
            sheBaoReceivableFee = receivableFee;
        }
    }

    public void setItemBatchInfoLimit(BigDecimal batchInfoSheBaoReceivableTotalPrice) {
        updateSheBaoReceivablePrice(batchInfoSheBaoReceivableTotalPrice);
    }

    public void setIsUseLimitPrice(int isUseLimitPrice) {
        chargeFormItemBatchInfo.setIsUseLimitPrice(isUseLimitPrice);
    }


    public void updateLimitFee(BigDecimal limitFee) {
        ChargeDiscountInfo promotionInfo = Optional.ofNullable(chargeFormItemBatchInfo.getPromotionInfo()).orElse(new ChargeDiscountInfo());
        promotionInfo.setLimitFee(limitFee);
        chargeFormItemBatchInfo.setPromotionInfo(promotionInfo);
        chargeFormItemBatchInfo.setReceivablePrice(chargeFormItemBatchInfo.calculateReceivableFee());
    }

    public void updateSheBaoReceivablePrice(BigDecimal batchInfoSheBaoReceivableTotalPrice) {
        this.sheBaoReceivableFee = batchInfoSheBaoReceivableTotalPrice;
    }

    public void updateReceivablePrice(BigDecimal batchInfoReceivableTotalPrice) {
        this.receivableFee = batchInfoReceivableTotalPrice;
    }

    public ChargeFormItemBatchInfoView generateChargeFormItemBatchInfoView() {
        ChargeFormItemBatchInfoView chargeItemBatchInfoView = ChargeFormItemBatchInfoView.toChargeItemBatchInfoView(chargeFormItemBatchInfo, this.sheBaoReceivableFee);
        Optional.ofNullable(goodsBatchInfo)
                .ifPresent(g -> chargeItemBatchInfoView.setStockPieceCount(g.getStockPieceCount())
                        .setStockPackageCount(g.getStockPackageCount())
                        .setExpiredWarnFlag(g.getExpiredWarnFlag())
                );
        return chargeItemBatchInfoView;
    }

    public ChargeFormItemBatchInfoView generateChargeFormItemBatchInfoView(int itemStatus) {
        ChargeFormItemBatchInfoView chargeItemBatchInfoView = ChargeFormItemBatchInfoView.toChargeItemBatchInfoView(chargeFormItemBatchInfo, this.sheBaoReceivableFee);
        Optional.ofNullable(goodsBatchInfo)
                .ifPresent(g -> chargeItemBatchInfoView.setStockPieceCount(g.getStockPieceCount())
                        .setStockPackageCount(g.getStockPackageCount())
                        .setExpiredWarnFlag(g.getExpiredWarnFlag())
                );
        if (itemStatus == Constants.ChargeFormItemStatus.CHARGED) {
            chargeItemBatchInfoView.setUnitCount(MathUtils.wrapBigDecimalSubtract(chargeItemBatchInfoView.getUnitCount(), chargeItemBatchInfoView.getRefundUnitCount()));
            if (chargeFormItemBatchInfo.getIsOld() == 1) {
                chargeItemBatchInfoView.setCanRefundUnitCount(BigDecimal.ZERO);
            } else {
                chargeItemBatchInfoView.setCanRefundUnitCount(chargeItemBatchInfoView.getUnitCount());
            }
        }

        if (MathUtils.wrapBigDecimalCompare(chargeItemBatchInfoView.getUnitCount(), BigDecimal.ZERO) <= 0) {
            return null;
        }

        return chargeItemBatchInfoView;
    }

    public boolean isLimitNoPay() {
        Integer exceedLimitPriceRule = Optional.ofNullable(chargeFormItemBatchInfo.getLimitInfo())
                .map(BaseLimitPriceInfo.LimitInfo::getExceedLimitPriceRule)
                .orElse(null);

        return exceedLimitPriceRule != null && exceedLimitPriceRule == Constants.ExceedLimitPriceRule.NO_PAY;
    }

    public void clearSinglePromotionInfo() {
        if (chargeFormItemBatchInfo.getPromotionInfo() == null) {
            return;
        }
        ChargeDiscountInfo promotionInfo = chargeFormItemBatchInfo.getPromotionInfo();
        promotionInfo.setDiscountPromotionInfos(null);
        chargeFormItemBatchInfo.setPromotionInfo(promotionInfo);
    }

    public void clearPackagePromotionInfo() {
        if (chargeFormItemBatchInfo.getPromotionInfo() == null) {
            return;
        }
        ChargeDiscountInfo promotionInfo = chargeFormItemBatchInfo.getPromotionInfo();
        promotionInfo.setGiftRulePromotionInfos(null)
                .setCouponInfos(null);
        chargeFormItemBatchInfo.setPromotionInfo(promotionInfo);
    }

    public void flatSinglePromotionInfo(ChargeDiscountInfo parentPromotionInfo, BigDecimal parentSourceTotalPrice) {
        ChargeDiscountInfo childItemPromotionInfo = Optional.ofNullable(chargeFormItemBatchInfo.getPromotionInfo()).orElse(new ChargeDiscountInfo());

        BigDecimal batchSourceTotalPrice = chargeFormItemBatchInfo.getSourceTotalPrice();
        BiConsumer<ChargeDiscountInfo.PromotionInfo, ChargeDiscountInfo.PromotionInfo> flatChildDiscountPriceConsumer = (parent, child) -> {
            BigDecimal childDiscountPrice = BigDecimal.ZERO;
            if (MathUtils.wrapBigDecimalCompare(parentSourceTotalPrice, BigDecimal.ZERO) <= 0) {
                childDiscountPrice = batchSourceTotalPrice;
            } else {
                childDiscountPrice = parent.getDiscountPrice().abs().multiply(batchSourceTotalPrice).divide(parentSourceTotalPrice, 2, RoundingMode.UP);
            }
            //保障不能超过子项的总金额
            childDiscountPrice = MathUtils.min(batchSourceTotalPrice, childDiscountPrice);
            //保障不能超过折扣的剩余金额
            childDiscountPrice = MathUtils.min(childDiscountPrice, MathUtils.wrapBigDecimalSubtract(parent.getDiscountPrice(), parent.getExpendDiscountPrice()).abs()).negate();

            child.setDiscountPrice(childDiscountPrice);
            //单个具体折扣已经使用的折扣金额
            parent.setExpendDiscountPrice(MathUtils.wrapBigDecimalAdd(parent.getExpendDiscountPrice(), childDiscountPrice));
        };

        childItemPromotionInfo
                .setDiscountPromotionInfos(parentPromotionInfo.getDiscountPromotionInfos()
                        .stream()
                        .map(parent -> {
                            ChargeDiscountInfo.PromotionInfo child = new ChargeDiscountInfo.PromotionInfo();
                            BeanUtils.copyProperties(parent, child, "expendDiscountPrice");
                            flatChildDiscountPriceConsumer.accept(parent, child);
                            return child;
                        }).collect(Collectors.toList()));

        setPromotion(childItemPromotionInfo);
    }

    public void flatPackagePromotionInfo(ChargeDiscountInfo parentPromotionInfo, BigDecimal parentPrice) {
        ChargeDiscountInfo childItemPromotionInfo = Optional.ofNullable(chargeFormItemBatchInfo.getPromotionInfo()).orElse(new ChargeDiscountInfo());

        BigDecimal maxFlatPackagePromotionPrice = chargeFormItemBatchInfo.getSourceTotalPrice()
                .add(chargeFormItemBatchInfo.calculateSinglePromotionPrice())
                .add(chargeFormItemBatchInfo.calculateUnitAdjustmentFee());
        BiConsumer<ChargeDiscountInfo.PromotionInfo, ChargeDiscountInfo.PromotionInfo> flatChildDiscountPriceConsumer = (parent, child) -> {
            BigDecimal childDiscountPrice = BigDecimal.ZERO;
            if (MathUtils.wrapBigDecimalCompare(parentPrice, BigDecimal.ZERO) <= 0) {
                childDiscountPrice = maxFlatPackagePromotionPrice;
            } else {
                childDiscountPrice = parent.getDiscountPrice().abs().multiply(maxFlatPackagePromotionPrice).divide(parentPrice, 2, RoundingMode.UP);
            }
            //保障不能超过子项的总金额
            childDiscountPrice = MathUtils.min(maxFlatPackagePromotionPrice, childDiscountPrice);
            //保障不能超过折扣的剩余金额
            childDiscountPrice = MathUtils.min(childDiscountPrice, MathUtils.wrapBigDecimalSubtract(parent.getDiscountPrice(), parent.getExpendDiscountPrice()).abs()).negate();

            child.setDiscountPrice(childDiscountPrice);
            //单个具体折扣已经使用的折扣金额
            parent.setExpendDiscountPrice(MathUtils.wrapBigDecimalAdd(parent.getExpendDiscountPrice(), childDiscountPrice));
        };

        childItemPromotionInfo
                .setGiftRulePromotionInfos(parentPromotionInfo.getGiftRulePromotionInfos()
                        .stream()
                        .map(parent -> {
                            ChargeDiscountInfo.PromotionInfo child = new ChargeDiscountInfo.PromotionInfo();
                            BeanUtils.copyProperties(parent, child, "expendDiscountPrice");
                            flatChildDiscountPriceConsumer.accept(parent, child);
                            return child;
                        }).collect(Collectors.toList()))
                .setCouponInfos(parentPromotionInfo.getCouponInfos()
                        .stream()
                        .map(parent -> {
                            ChargeDiscountInfo.CouponInfo child = new ChargeDiscountInfo.CouponInfo();
                            BeanUtils.copyProperties(parent, child, "expendDiscountPrice");
                            flatChildDiscountPriceConsumer.accept(parent, child);
                            return child;
                        }).collect(Collectors.toList()));

        setPromotion(childItemPromotionInfo);
    }

    private void setPromotion(ChargeDiscountInfo childItemPromotionInfo) {
        chargeFormItemBatchInfo.setPromotionInfo(childItemPromotionInfo);
    }

    /**
     * 追加积分抵扣金额
     *
     * @param promotionPrice
     */
    public void addPatientPointPromotionPrice(BigDecimal promotionPrice) {
        ChargeDiscountInfo promotionInfo = Optional.ofNullable(chargeFormItemBatchInfo.getPromotionInfo()).orElse(new ChargeDiscountInfo());
        promotionInfo.setPatientPointPromotionFee(promotionPrice);
        chargeFormItemBatchInfo.setPromotionInfo(promotionInfo);
    }


    /**
     * 追加单项议价
     *
     * @param unitAdjustmentFee
     */
    public void addUnitAdjustmentFee(BigDecimal unitAdjustmentFee) {
        ChargeDiscountInfo promotionInfo = Optional.ofNullable(chargeFormItemBatchInfo.getPromotionInfo()).orElse(new ChargeDiscountInfo());
        promotionInfo.setUnitAdjustmentFee(unitAdjustmentFee);
        promotionInfo.setUnitAdjustmentFeeIgnoreDeduct(unitAdjustmentFee);
        chargeFormItemBatchInfo.setPromotionInfo(promotionInfo);
    }

    /**
     * 追加整单议价
     *
     * @param adjustmentFee
     */
    public void addAdjustmentFee(BigDecimal adjustmentFee) {
        ChargeDiscountInfo promotionInfo = Optional.ofNullable(chargeFormItemBatchInfo.getPromotionInfo()).orElse(new ChargeDiscountInfo());
        promotionInfo.setAdjustmentFee(adjustmentFee);
        chargeFormItemBatchInfo.setPromotionInfo(promotionInfo);
        receivableFee = chargeFormItemBatchInfo.calculateReceivableFee();
        chargeFormItemBatchInfo.setReceivablePrice(receivableFee);
    }

    public CalculatePromotionItemBatchInfo generateCalculatePromotionItemBatchInfo() {
        return new CalculatePromotionItemBatchInfo()
                .setId(chargeFormItemBatchInfo.getId())
                .setBatchId(chargeFormItemBatchInfo.getBatchId())
                .setTotalCount(chargeFormItemBatchInfo.getUnitCount())
                .setUnitPrice(chargeFormItemBatchInfo.getUnitPrice())
                .setTotalPrice(chargeFormItemBatchInfo.getTotalPrice());
    }

    public BigDecimal flatAndSetPromotion(ChargeDiscountInfo parentPromotionInfo, BigDecimal batchFlatPromotionDenominator, boolean resetWithPromotion) {
        if (Objects.isNull(parentPromotionInfo)) {

            Optional.ofNullable(chargeFormItemBatchInfo.getPromotionInfo())
                    .ifPresent(chargeDiscountInfo -> {
                        chargeDiscountInfo.clearPromotionWithOutPoint();
                        if (resetWithPromotion) {
                            chargeDiscountInfo.setListingDiscountFee(null);
                            chargeDiscountInfo.setLimitFee(null);
                        }
                    });
            chargeFormItemBatchInfo.setReceivablePrice(chargeFormItemBatchInfo.calculateReceivableFee());
            return BigDecimal.ZERO;
        }

        //优先处理抵扣，将抵扣金额取出来
        //积分抵扣
        List<ChargeDiscountInfo.PromotionInfo> pointDeductedPromotionInfos = parentPromotionInfo.getDiscountPromotionInfos()
                .stream()
                .filter(parent -> parent.getType() == PromotionSimple.Type.PATIENT_POINT_DEDUCT)
                .map(parent -> {
                    Map<String, ItemDeductedDetail.BatchInfoDeductedDetail> batchInfoIdMap = Optional.ofNullable(parent.getBatchInfos())
                            .orElse(new ArrayList<>())
                            .stream()
                            .collect(Collectors.toMap(ItemDeductedDetail.BatchInfoDeductedDetail::getId, Function.identity(), (a, b) -> a));
                    ItemDeductedDetail.BatchInfoDeductedDetail batchInfoDeductedDetail = batchInfoIdMap.get(chargeFormItemBatchInfo.getId());
                    if (batchInfoDeductedDetail == null) {
                        return null;
                    }
                    ChargeDiscountInfo.PromotionInfo child = new ChargeDiscountInfo.PromotionInfo();
                    BeanUtils.copyProperties(parent, child);
                    child.setBatchInfos(null);
                    child.setDiscountPrice(batchInfoDeductedDetail.getDeductedPrice());
                    return child;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        //卡项抵扣
        List<ChargeDiscountInfo.DeductDiscountInfo> cardDeductedPromotionInfos = parentPromotionInfo.getDeductDiscountInfos()
                .stream()
                .filter(parent -> CollectionUtils.isNotEmpty(parent.getBatchInfos()))
                .map(parent -> {
                    Map<String, ItemDeductedDetail.BatchInfoDeductedDetail> batchInfoIdMap = parent.getBatchInfos().stream()
                            .collect(Collectors.toMap(ItemDeductedDetail.BatchInfoDeductedDetail::getId, Function.identity(), (a, b) -> a));

                    ItemDeductedDetail.BatchInfoDeductedDetail batchInfoDeductedDetail = batchInfoIdMap.get(chargeFormItemBatchInfo.getId());
                    if (batchInfoDeductedDetail == null) {
                        return null;
                    }
                    ChargeDiscountInfo.DeductDiscountInfo batchInfoDeductDiscountInfo = new ChargeDiscountInfo.DeductDiscountInfo();
                    BeanUtils.copyProperties(parent, batchInfoDeductDiscountInfo, "expendDiscountPrice", "batchInfos");
                    batchInfoDeductDiscountInfo.setDeductedCount(batchInfoDeductedDetail.getDeductedCount());
                    batchInfoDeductDiscountInfo.setDiscountPrice(batchInfoDeductedDetail.getDeductedPrice());
                    batchInfoDeductDiscountInfo.setUnitPrice(chargeFormItemBatchInfo.getUnitPrice());
                    return batchInfoDeductDiscountInfo;
                }).collect(Collectors.toList());


        BigDecimal allDeductedPromotionPrice = pointDeductedPromotionInfos.stream()
                .map(ChargeDiscountInfo.PromotionInfo::getDiscountPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .add(
                        cardDeductedPromotionInfos.stream()
                                .map(ChargeDiscountInfo.DeductDiscountInfo::getDiscountPrice)
                                .reduce(BigDecimal.ZERO, BigDecimal::add)
                );

        //批次计算折扣的分子 = totalPrice - 总的抵扣金额
        BigDecimal batchFlatPromotionMolecule = chargeFormItemBatchInfo.calculateAfterUnitAdjustmentFeeTotalPrice().add(allDeductedPromotionPrice);

        //剩余可用的总金额
        AtomicReference<BigDecimal> leftChildTotalPrice = new AtomicReference<>(batchFlatPromotionMolecule);

        BiConsumer<ChargeDiscountInfo.PromotionInfo, ChargeDiscountInfo.PromotionInfo> flatChildDiscountPriceConsumer = (parent, child) -> {
            BigDecimal childDiscountPrice;
            if (MathUtils.wrapBigDecimalCompare(batchFlatPromotionDenominator, BigDecimal.ZERO) <= 0) {
                childDiscountPrice = batchFlatPromotionMolecule;
            } else {
                childDiscountPrice = parent.getDiscountPrice().abs().multiply(batchFlatPromotionMolecule).divide(batchFlatPromotionDenominator, 2, RoundingMode.UP);
            }
            //保障不能超过批次的剩余金额
            childDiscountPrice = MathUtils.min(leftChildTotalPrice.get(), childDiscountPrice);
            //保障不能超过折扣的剩余金额
            childDiscountPrice = MathUtils.min(childDiscountPrice, MathUtils.wrapBigDecimalSubtract(parent.getDiscountPrice(), parent.getExpendDiscountPrice()).abs()).negate();

            child.setDiscountPrice(childDiscountPrice);

            leftChildTotalPrice.set(leftChildTotalPrice.get().add(childDiscountPrice));

            //单个具体折扣已经使用的折扣金额
            parent.setExpendDiscountPrice(MathUtils.wrapBigDecimalAdd(parent.getExpendDiscountPrice(), childDiscountPrice));
        };

        ChargeDiscountInfo childItemPromotionInfo = new ChargeDiscountInfo();
        childItemPromotionInfo
                .setDiscountPromotionInfos(parentPromotionInfo.getDiscountPromotionInfos()
                        .stream()
                        .filter(parent -> parent.getType() != PromotionSimple.Type.PATIENT_POINT_DEDUCT)
                        .map(parent -> {
                            ChargeDiscountInfo.PromotionInfo child = new ChargeDiscountInfo.PromotionInfo();
                            BeanUtils.copyProperties(parent, child, "expendDiscountPrice");
                            flatChildDiscountPriceConsumer.accept(parent, child);
                            return child;
                        })
                        .collect(Collectors.toList()))
                .setGiftRulePromotionInfos(parentPromotionInfo.getGiftRulePromotionInfos()
                        .stream()
                        .map(parent -> {
                            ChargeDiscountInfo.PromotionInfo child = new ChargeDiscountInfo.PromotionInfo();
                            BeanUtils.copyProperties(parent, child, "expendDiscountPrice");
                            flatChildDiscountPriceConsumer.accept(parent, child);
                            return child;
                        }).collect(Collectors.toList()))
                .setCouponInfos(parentPromotionInfo.getCouponInfos()
                        .stream()
                        .map(parent -> {
                            ChargeDiscountInfo.CouponInfo child = new ChargeDiscountInfo.CouponInfo();
                            BeanUtils.copyProperties(parent, child, "expendDiscountPrice");
                            flatChildDiscountPriceConsumer.accept(parent, child);
                            return child;
                        }).collect(Collectors.toList()));
        //单独添加积分抵扣和卡项抵扣
        childItemPromotionInfo.addDiscountPromotionInfos(pointDeductedPromotionInfos);
        childItemPromotionInfo.setDeductDiscountInfos(cardDeductedPromotionInfos);

        BigDecimal excludeDeductPromotionPrice = childItemPromotionInfo.getExcludeDeductPromotionPrice();
        BigDecimal deductPromotionPrice = childItemPromotionInfo.getDeductPromotionPrice();

        ChargeDiscountInfo existedChargeDiscountInfo = Optional.ofNullable(chargeFormItemBatchInfo.getPromotionInfo())
                .orElse(new ChargeDiscountInfo());

        existedChargeDiscountInfo.setDiscountPromotionInfos(childItemPromotionInfo.getDiscountPromotionInfos())
                .setGiftRulePromotionInfos(childItemPromotionInfo.getGiftRulePromotionInfos())
                .setCouponInfos(childItemPromotionInfo.getCouponInfos())
                .setDeductDiscountInfos(childItemPromotionInfo.getDeductDiscountInfos());

        chargeFormItemBatchInfo.setPromotionInfo(existedChargeDiscountInfo);
        chargeFormItemBatchInfo.setReceivablePrice(chargeFormItemBatchInfo.calculateReceivableFee());

        return excludeDeductPromotionPrice.add(deductPromotionPrice);
    }

    public void updateExpectedTotalPrice(BigDecimal expectedTotalPrice) {

        BigDecimal unitAdjustmentFee = MathUtils.wrapBigDecimalSubtract(expectedTotalPrice, chargeFormItemBatchInfo.getSourceTotalPrice());


        if (unitAdjustmentFee.compareTo(BigDecimal.ZERO) == 0) {
            chargeFormItemBatchInfo.setExpectedTotalPrice(null);
            Optional.ofNullable(chargeFormItemBatchInfo.getPromotionInfo())
                    .ifPresent(chargeDiscountInfo -> chargeDiscountInfo.setUnitAdjustmentFee(null));
            chargeFormItemBatchInfo.setUnitPrice(chargeFormItemBatchInfo.getSourceUnitPrice());
            chargeFormItemBatchInfo.setTotalPrice(chargeFormItemBatchInfo.getSourceTotalPrice());
        } else {
            chargeFormItemBatchInfo.setExpectedTotalPrice(expectedTotalPrice);
            ExpectedPriceHelper.CalculateExpectedPriceResult calculateExpectedPriceResult = ExpectedPriceHelper.calculateExpectedPrice(chargeFormItemBatchInfo.getUnitCount(),
                    BigDecimal.ONE,
                    chargeFormItemBatchInfo.getUnitPrice(),
                    chargeFormItemBatchInfo.getSourceUnitPrice(),
                    chargeFormItemBatchInfo.getSourceTotalPrice(),
                    BigDecimal.ZERO,
                    null,
                    expectedTotalPrice,
                    null,
                    CalculateModel.NORMAL_RESET);
            chargeFormItemBatchInfo.setUnitPrice(calculateExpectedPriceResult.getUnitPrice());
            chargeFormItemBatchInfo.setTotalPrice(calculateExpectedPriceResult.getTotalPrice());
            ChargeDiscountInfo chargeDiscountInfo = Optional.ofNullable(chargeFormItemBatchInfo.getPromotionInfo())
                    .orElse(new ChargeDiscountInfo())
                    .setUnitAdjustmentFee(calculateExpectedPriceResult.getUnitAdjustmentFee());
            chargeFormItemBatchInfo.setPromotionInfo(chargeDiscountInfo);
        }

        receivableFee = chargeFormItemBatchInfo.calculateReceivableFee();
        sheBaoReceivableFee = receivableFee;
        chargeFormItemBatchInfo.setReceivablePrice(receivableFee);
    }


    public ChargePayItemBatchInfo generatePayItemBatchInfo(boolean isShebaoPayMode) {

        BigDecimal receivableFee = isShebaoPayMode ? sheBaoReceivableFee : this.receivableFee;

        ChargePayItemBatchInfo chargePayItemInfo = new ChargePayItemBatchInfo();
        chargePayItemInfo.setId(chargeFormItemBatchInfo.getId());
        chargePayItemInfo.setBatchId(chargeFormItemBatchInfo.getBatchId());
        chargePayItemInfo.setBatchNo(chargeFormItemBatchInfo.getBatchNo());
        chargePayItemInfo.setReceivableFee(receivableFee);
        chargePayItemInfo.setThisTimeReceivableFee(receivableFee);

        return chargePayItemInfo;
    }

    public void updateReceivedPrice(BigDecimal thisTimeReceivableFee) {

        chargeFormItemBatchInfo.setReceivedPrice(MathUtils.wrapBigDecimalAdd(chargeFormItemBatchInfo.getReceivedPrice(), thisTimeReceivableFee));
        chargeFormItemBatchInfo.setThisTimeReceivableFee(thisTimeReceivableFee);

    }


    public void updateReceivedFeeForRefund(RefundUpdateReceivedBatchTemp refundUpdateReceivedItemTemp) {

        if (Objects.isNull(refundUpdateReceivedItemTemp)) {
            return;
        }
        BigDecimal thisRefundFee = refundUpdateReceivedItemTemp.getThisRefundFee();
        if (MathUtils.wrapBigDecimalCompare(thisRefundFee, BigDecimal.ZERO) > 0) {
            chargeFormItemBatchInfo.setReceivedPrice(MathUtils.max(BigDecimal.ZERO, MathUtils.wrapBigDecimalSubtract(chargeFormItemBatchInfo.getReceivedPrice(), thisRefundFee)));
        }
    }

    public void resetReceivedPrice() {
        chargeFormItemBatchInfo.setReceivedPrice(BigDecimal.ZERO);
    }

    public void payBack(String operatorId, boolean payBackFinished) {

        if (!payBackFinished) {
            return;
        }

        chargeFormItemBatchInfo.setReceivedPrice(BigDecimal.ZERO);
        chargeFormItemBatchInfo.setPromotionInfo(null);
        chargeFormItemBatchInfo.setReceivablePrice(chargeFormItemBatchInfo.calculateReceivableFee());
        FillUtils.fillLastModifiedBy(chargeFormItemBatchInfo, operatorId);
    }

    public void markHealthCardPaid() {
        ChargeDiscountInfo chargeDiscountInfo = Optional.ofNullable(chargeFormItemBatchInfo.getPromotionInfo()).orElse(new ChargeDiscountInfo());
        chargeDiscountInfo.setIsMarkedByHealthCardPay(1);
        chargeFormItemBatchInfo.setPromotionInfo(chargeDiscountInfo);
    }

    public void clearLimitInfo() {
        chargeFormItemBatchInfo.setIsUseLimitPrice(0);
        chargeFormItemBatchInfo.setLimitInfo(null);
        Optional.ofNullable(chargeFormItemBatchInfo.getPromotionInfo())
                .ifPresent(promotionInfo -> promotionInfo.setLimitFee(null));
    }

    public void clearListingDiscountFee() {
        Optional.ofNullable(chargeFormItemBatchInfo.getPromotionInfo())
                .ifPresent(promotionInfo -> promotionInfo.setListingDiscountFee(null));
    }

    public void clearPromotionAndAdjustmentFee(int limitType) {
        Optional.ofNullable(chargeFormItemBatchInfo.getPromotionInfo()).ifPresent(promotionInfo -> {
            if (limitType == LimitPriceConstants.LimitPriceType.LISTING_PRICE_LIMIT) {
                promotionInfo.setListingDiscountFee(null);
            }
            promotionInfo.setLimitFee(null);
            promotionInfo.clearPromotionAndAdjustmentFee();
        });
//        chargeFormItemBatchInfo.setUnitPrice(chargeFormItemBatchInfo.getSourceUnitPrice());
//        chargeFormItemBatchInfo.calculateTotalPrice();
        receivableFee = chargeFormItemBatchInfo.calculateReceivableFee();
    }

    /**
     *
     * @param itemBatchInfo
     */
    public void updateListingPrice(CalculateLimitPriceItem.CalculateLimitPriceItemBatchInfo itemBatchInfo) {

        if (Objects.isNull(itemBatchInfo)) {
            return;
        }
        //获取挂网价限价折扣金额
        BigDecimal listingDiscountFee = Optional.ofNullable(itemBatchInfo)
                .map(CalculateLimitPriceItem.CalculateLimitPriceItemBatchInfo::getLimitInfo)
                .map(BaseLimitPriceInfo.LimitInfo::getNotSelfPayPrice)
                .orElse(null);

        ChargeDiscountInfo chargeDiscountInfo = Optional.ofNullable(chargeFormItemBatchInfo.getPromotionInfo()).orElse(new ChargeDiscountInfo());
        chargeDiscountInfo.setListingDiscountFee(listingDiscountFee);
        chargeFormItemBatchInfo.setPromotionInfo(chargeDiscountInfo);
        chargeFormItemBatchInfo.setUnitPrice(itemBatchInfo.getLimitUnitPrice());
        chargeFormItemBatchInfo.setTotalPrice(itemBatchInfo.getLimitTotalPrice());

        receivableFee = chargeFormItemBatchInfo.calculateReceivableFee();
    }
}
