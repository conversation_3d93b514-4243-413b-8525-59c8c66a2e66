package cn.abcyun.cis.charge.processor;

import cn.abcyun.bis.rpc.sdk.bis.model.order.CreateOrderView;
import cn.abcyun.cis.charge.api.model.*;
import cn.abcyun.cis.charge.base.ChargeVersionConstants;
import cn.abcyun.cis.charge.base.pay.CombinedPayItem;
import cn.abcyun.cis.charge.model.*;
import cn.abcyun.cis.charge.processor.stat.StatRecordResult;
import cn.abcyun.cis.charge.rpc.model.ChargeSheetTraceCodesRsp;
import cn.abcyun.cis.charge.service.ChargeTransactionRecordService;
import cn.abcyun.cis.charge.service.dto.*;
import cn.abcyun.cis.charge.service.dto.print.ChargeSheetPrintView;
import cn.abcyun.cis.charge.util.ChargeUtils;
import cn.abcyun.cis.commons.rpc.crm.PatientInfo;
import cn.abcyun.cis.commons.rpc.outpatient.MedicalRecord;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrder;
import cn.abcyun.cis.commons.rpc.pay.PayCallbackReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.util.Asserts;

import java.math.BigDecimal;
import java.util.List;

@Slf4j
public class SheetProcessorDispatcher {


    public interface ISheetProcessorBuild {
        void setSheetProcessorInfoProvider(SheetProcessorInfoProvider sheetProcessorInfoProvider);

        void setPayMode(int payMode);

        void setPatientInfo(PatientInfo patientInfo);

        void setPatientOrder(PatientOrder patientOrder);

        void setOperatorId(String operatorId);

        void setChargeTransactionRecordService(ChargeTransactionRecordService chargeTransactionRecordService);

        List<ChargeFormItem> getAffectedChargeFormItems();

        ChargeRefundSheet getChargeRefundSheet();

        StatRecordResult getRefundChargeStatRecordResult();

        SheetProcessorInfoProvider getSheetProcessorInfoProvider();

        void setMedicalRecord(MedicalRecord medicalRecord);

        List<ChargeOweSheet> getChargeOweSheets();

        void build();

        BigDecimal getRealReceivableFee(boolean needCalculate);
    }

    public static abstract class ISheetProcessor implements ISheetProcessorBuild {
        ChargeSheet chargeSheet;


        public ChargeSheetRelationDto getDeletedDataCollector() {
            return chargeSheet.getDeletedDataCollector();
        }

        @Override
        public void build() {
            ChargeUtils.removeDeleteData(chargeSheet);
        }

        public abstract CalculateChargeResult calculateSheetFee(BigDecimal expectedAdjustmentFee,
                                                                BigDecimal expectedOddFee,
                                                                List<PromotionReq> promotionReqs,
                                                                List<CouponPromotionReq> couponPromotionReqs,
                                                                List<GiftRulePromotionReq> giftRulePromotionReqs,
                                                                List<PatientCardPromotionReq> patientCardPromotionReqs,
                                                                List<PatientPointDeductProductPromotionReq> patientPointDeductProductPromotionReqs,
                                                                List<ItemSinglePromotionReq> itemSinglePromotionReqs,
                                                                List<MallVerificationReq> mallVerificationReqs,
                                                                PatientPointsInfoReq patientPointsInfoReq,
                                                                int calSource,
                                                                boolean payForLeft,
                                                                //是否需要卡项余额信息
                                                                boolean needPatientCardBalance);

        public abstract ChargeSheetView generateSheetDetail(boolean isCopyByOutpatient, boolean isContainGiftGoods, int source, boolean notQuerySheBaoInfo);

        public abstract ChargeSheetView generateSheetDetail(boolean isCopyByOutpatient, boolean isContainGiftGoods, int source, boolean notQuerySheBaoInfo, boolean withAllTransaction, int dispensingQueryCheck);

        public abstract PayResult payForImport(PayInfo payInfo);

        public abstract ChargeSheet generateToSaveChargeSheet();

        public abstract RegistrationChargeSheetView generateRegistrationChargeSheetView();

        public abstract PayResult lockPay(PayInfo payInfo, boolean isPayForLeft);

        public abstract List<PromotionView> getAvailablePromotionViews();

        public abstract void placePayOrderForRegistration(PayInfo payInfo);

        public abstract void placePayOrder(PayInfo payInfo);

        public abstract PayResult pay(PayInfo payInfo, boolean isPayForLeft);

        public abstract PayResult payWithOwePayMode(PayInfo payInfo);

        public abstract PatientInfo getPatientInfo();

        public abstract StatRecordResult generateChargeTransactionRecords();

        public abstract List<ChargeAirPharmacyMedicalRecord> generateToSaveChargeAirPharmacyMedicalRecords();

        public abstract List<ChargeAirPharmacyLogistics> generateToSaveChargeAirPharmacyLogistics();

        public abstract List<ChargeSheetProcessInfo> generateToSaveChargeSheetProcessInfos();

        public abstract StatRecordResult generateChargeTransactionRecords(List<CreateOrderView> airPharmacyOrders);

        public abstract StatRecordResult generateChargeTransactionRecords(List<ChargeTransactionRecord> addedRecords, List<CreateOrderView> airPharmacyOrders);

        public abstract RefundResult refund(RefundInfo refundInfo, boolean keepStatus, boolean isSupportWholeRefundCheck);

        public abstract void lockChargeSheet(PayInfo payInfo);

        public abstract RefundResult paidback(RefundInfo refundInfo);

        public abstract CalculateChargeResult calculateRefundSheetFee(RefundInfo refundInfo);

        public abstract void updatePromotionForRegistration(PayInfo payInfo);

        public abstract ChargeSheet updateProductInfoAndBindMemberId();

        public abstract ChargeSheetPrintView generatePrintView();

        public abstract void placePayOrderForPayCallback(PayInfo payInfo);

        public abstract List<ChargeFormItem> getStockAvailableItems();

        public abstract ChargeTransaction payCallback(CombinedPayItem payItem,
                                                      ThirdPartyPayInfo thirdPartyPayInfo,
                                                      int paySource,
                                                      ChargeAction chargeAction,
                                                      String operatorId,
                                                      String payInfoExtra,
                                                      boolean isFirstPay,
                                                      List<PayCallbackReq.ChargeFormItem> thisTimeReceivedChargeFormItems);

        public abstract void paidbackCallback(ChargePayTransaction chargePayTransaction, ChargeAction chargeAction, String operatorId);

        public abstract ChargeTransaction refundCallback(ChargeRefundSheet chargeRefundSheet,
                                                         int payMode,
                                                         int paySubMode,
                                                         BigDecimal refundFee,
                                                         String associatePayTransactionId,
                                                         int paySource,
                                                         ThirdPartyPayInfo thirdPartyPayInfo,
                                                         ChargeAction chargeAction,
                                                         List<ChargeTransactionRecord> addedRecords,
                                                         String operatorId);

        public abstract ChargeSheetSummary onlyGenerateSummary();

        public abstract ChargeSheet generateToSaveChargeSheetForDraftAdjustmentFee(BigDecimal expectedAdjustmentFee,
                                                                                   BigDecimal outpatientAdjustmentFee,
                                                                                   BigDecimal draftAdjustmentFee,
                                                                                   List<PromotionReq> promotions,
                                                                                   List<CouponPromotionReq> couponPromotions,
                                                                                   List<GiftRulePromotionReq> giftRulePromotions,
                                                                                   List<PatientCardPromotionReq> patientCardPromotions,
                                                                                   List<PatientPointDeductProductPromotionReq> patientPointDeductProductPromotions,
                                                                                   List<ItemSinglePromotionReq> itemSinglePromotionReqs,
                                                                                   PatientPointsInfoReq patientPointsInfo,
                                                                                   List<MallVerificationReq> mallVerifications);

        public abstract ChargeSheet updateProductInfoForDraft();

        public abstract List<PatientCardView> findCanPaidPatientCards();

        public abstract ChargeSheetPrintView generateRefundPrintView();

        public abstract List<ChargeFormItemBatchInfo> getCurrentRefundBatchInfos();

        public abstract CalculateProcessChargeRsp calculateProcessSheetFee(boolean isNeedUpdateProduct);

        public abstract ChargeSheetTraceCodesRsp generateChargeSheetTraceCodesRsp();
    }


    public static ISheetProcessor newSheetProcessor(ChargeSheet chargeSheet) {
        ISheetProcessor iSheetProcessor = null;
        if (chargeSheet.getChargeVersion() == ChargeVersionConstants.V1) {
            iSheetProcessor = new PharmacySheetProcessor(chargeSheet);
        } else if (chargeSheet.getChargeVersion() == ChargeVersionConstants.V0) {
            iSheetProcessor = new SheetProcessor(chargeSheet);
        }

        Asserts.notNull(iSheetProcessor, "iSheetProcessor is null");
        return iSheetProcessor;
    }

    public static PharmacySheetBatchExtractProcessor newPharmacySheetBatchExtractProcessor(ChargeSheet chargeSheet) {
        PharmacySheetBatchExtractProcessor iSheetProcessor = new PharmacySheetBatchExtractProcessor(chargeSheet);
        Asserts.notNull(iSheetProcessor, "iSheetProcessor is null");
        return iSheetProcessor;
    }


}
