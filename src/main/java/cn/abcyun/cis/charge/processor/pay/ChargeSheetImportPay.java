package cn.abcyun.cis.charge.processor.pay;

import cn.abcyun.cis.charge.base.ProductInfoChangedException;
import cn.abcyun.cis.charge.base.pay.CombinedPayItem;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.processor.PayInfo;
import cn.abcyun.cis.charge.processor.SheetProcessorInfoProvider;
import cn.abcyun.cis.charge.repository.ChargePayTransactionRepository;
import cn.abcyun.cis.charge.service.ChargeTransactionRecordService;
import cn.abcyun.cis.charge.service.dto.PayChargeSheetInfo;
import cn.abcyun.cis.charge.service.rpc.CisPatientOrderService;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrder;

/**
 * 导入已收费的收费单，产生transaction
 */
public class ChargeSheetImportPay extends BaseChargeSheetPay{
    private SheetProcessorInfoProvider sheetProcessorInfoProvider;
    private ChargeSheet chargeSheet;
    private PatientOrder patientOrder;
    private ChargeTransactionRecordService chargeTransactionRecordService;

    public ChargeSheetImportPay(SheetProcessorInfoProvider sheetProcessorInfoProvider,
                             ChargeSheet chargeSheet,
                             PayChargeSheetInfo payChargeSheetInfo,
                             PatientOrder patientOrder,
                             ChargeTransactionRecordService chargeTransactionRecordService,
                             int paySource,
                             CombinedPayItem payItem,
                             CisPatientOrderService cisPatientOrderService,
                             ChargePayTransactionRepository chargePayTransactionRepository,
                             String operatorId) {
        super(chargeSheet, payChargeSheetInfo, payItem, paySource, cisPatientOrderService, chargePayTransactionRepository, operatorId);
        this.sheetProcessorInfoProvider = sheetProcessorInfoProvider;
        this.chargeSheet = chargeSheet;
        this.patientOrder = patientOrder;
        this.chargeTransactionRecordService = chargeTransactionRecordService;
    }

    @Override
    protected void saveChargeSheet() {

    }

    @Override
    protected void doPay() throws ProductInfoChangedException {
        sheetProcessor = createSheetProcessor(sheetProcessorInfoProvider, patientOrder, chargeTransactionRecordService, payItem);
        PayInfo payInfo = createPayInfo();
        payResult = sheetProcessor.payForImport(payInfo);
        chargeSheet = sheetProcessor.generateToSaveChargeSheet();
    }

    @Override
    protected void checkCanPaidAndThrowException() {

    }

    @Override
    protected void baseCheckCanPaidAndThrowException () {

    }

    @Override
    protected void buildChargeSheet() {

    }
}
