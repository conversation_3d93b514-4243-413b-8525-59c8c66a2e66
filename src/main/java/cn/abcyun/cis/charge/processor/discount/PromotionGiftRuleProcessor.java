package cn.abcyun.cis.charge.processor.discount;

import cn.abcyun.bis.rpc.sdk.cis.model.promotion.Promotion;
import cn.abcyun.cis.charge.api.model.CouponPromotionReq;
import cn.abcyun.cis.charge.api.model.GiftRulePromotionReq;
import cn.abcyun.cis.charge.processor.FlatPriceHelper;
import cn.abcyun.cis.charge.service.dto.GiftRulePromotionView;
import cn.abcyun.cis.charge.service.dto.OncomingGiftRulePromotionView;
import cn.abcyun.cis.charge.util.ListUtils;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.cis.commons.util.TextUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class PromotionGiftRuleProcessor {

    private final List<PromotionGiftRuleItem> promotionGiftRuleItems;

    private final List<CalculateGiftRulePromotion> discountPromotions = new ArrayList<>();

    private List<GiftRulePromotionView> availableGiftRulePromotionViews = new ArrayList<>();

//    private CalculateGiftRulePromotion bestDiscountPromotion;

    public PromotionGiftRuleProcessor(List<Promotion> promotions) {
        promotionGiftRuleItems = promotions.stream()
                .map(PromotionGiftRuleItem::new)
                .collect(Collectors.toList());
    }


    public <T extends BasicCalculatePromotionItem> List<GiftRulePromotionView> applyBestDiscount(List<T> items, List<CalculatePromotionAirPharmacyForm> airPharmacyForms, List<GiftRulePromotionView> existedGiftRulePromotions, List<GiftRulePromotionReq> giftRulePromotionReqs, String patientId) {

        if (CollectionUtils.isEmpty(promotionGiftRuleItems) || (CollectionUtils.isEmpty(items) && CollectionUtils.isEmpty(airPharmacyForms))) {
            return new ArrayList<>();
        }

        if (giftRulePromotionReqs == null) {
            giftRulePromotionReqs = new ArrayList<>();
        }

        if (existedGiftRulePromotions == null) {
            existedGiftRulePromotions = new ArrayList<>();
        }
        Map<String, GiftRulePromotionView> existedGiftRulePromotionMap = existedGiftRulePromotions.stream().collect(Collectors.toMap(GiftRulePromotionView::getId, Function.identity(), (a, b) -> a));

        Map<String, GiftRulePromotionReq> promotionReqMap = giftRulePromotionReqs.stream().collect(Collectors.toMap(GiftRulePromotionReq::getId, Function.identity(), (a, b) -> a));

        Set<String> unselectedPromotionIds = giftRulePromotionReqs
                .stream()
                .filter(Objects::nonNull)
                .filter(promotionReq -> promotionReq.getExpectedChecked() != null && !promotionReq.getExpectedChecked())
                .map(CouponPromotionReq::getId)
                .filter(id -> !TextUtils.isEmpty(id))
                .collect(Collectors.toSet());

        Set<String> selectedPromotionIds = giftRulePromotionReqs
                .stream()
                .filter(Objects::nonNull)
                .filter(promotionReq -> promotionReq.getExpectedChecked() != null && promotionReq.getExpectedChecked())
                .filter(CouponPromotionReq::getChecked)
                .map(CouponPromotionReq::getId)
                .filter(id -> !TextUtils.isEmpty(id))
                .collect(Collectors.toSet());

        if (!CollectionUtils.isEmpty(existedGiftRulePromotions)) {
            Set<String> existedUnselectedPromotionIds = existedGiftRulePromotions.stream()
                    .filter(promotionView -> promotionView.getExpectedChecked() != null && !promotionView.getExpectedChecked())
                    .filter(promotionView -> !TextUtils.isEmpty(promotionView.getId()))
                    .filter(promotionView -> !selectedPromotionIds.contains(promotionView.getId()))
                    .map(GiftRulePromotionView::getId)
                    .collect(Collectors.toSet());
            unselectedPromotionIds.addAll(existedUnselectedPromotionIds);
        }

        List<PromotionGiftRuleItem> availablePromotionGiftRuleItems = promotionGiftRuleItems.stream()
                .filter(promotionCouponItem -> promotionCouponItem.isAvailable(items, airPharmacyForms))
                .collect(Collectors.toList());

//        availablePromotionGiftRuleItems = availablePromotionGiftRuleItems.stream().filter(promotionGiftRuleItem -> !unselectedPromotionIds.contains(promotionGiftRuleItem.getPromotionId())).collect(Collectors.toList());

        Set<String> notAvailablePromotionGiftRuleItems = promotionGiftRuleItems.stream()
                .filter(promotionCouponItem -> !promotionCouponItem.isAvailable(items, airPharmacyForms))
                .map(PromotionGiftRuleItem::getPromotionId)
                .collect(Collectors.toSet());

        for (PromotionGiftRuleItem promotionGiftRuleItem : availablePromotionGiftRuleItems) {
            CalculateGiftRulePromotion calculateGiftRulePromotion = promotionGiftRuleItem.generateCalculateGiftRuleRulePromotion(items, airPharmacyForms, patientId);
            if (calculateGiftRulePromotion == null) {
                continue;
            }
            if (!unselectedPromotionIds.contains(promotionGiftRuleItem.getPromotionId())) {
                flatPriceCellToCalculatePromotionItem(calculateGiftRulePromotion, items, airPharmacyForms);
            }
            discountPromotions.add(calculateGiftRulePromotion);
        }

        Map<String, CalculateGiftRulePromotion> discountPromotionMap = ListUtils.toMap(discountPromotions, CalculateGiftRulePromotion::getPromotionId);

        availableGiftRulePromotionViews = promotionGiftRuleItems
                .stream()
                .map(discountRule -> discountRule.generateGiftRulePromotionView(items, airPharmacyForms))
                .filter(Objects::nonNull)
                .peek(promotionView -> {

                    GiftRulePromotionReq couponPromotionReq = promotionReqMap.get(promotionView.getId());
                    if (couponPromotionReq != null) {
                        promotionView.setExpectedChecked(couponPromotionReq.getExpectedChecked());
                    } else {
                        GiftRulePromotionView giftRulePromotionView = existedGiftRulePromotionMap.get(promotionView.getId());
                        if (giftRulePromotionView != null) {
                            promotionView.setExpectedChecked(giftRulePromotionView.getExpectedChecked());
                        }
                    }

                    if (unselectedPromotionIds.contains(promotionView.getId())) {
                        promotionView.setChecked(false);
                    }

                    CalculateGiftRulePromotion discountPromotion = discountPromotionMap.getOrDefault(promotionView.getId(), null);

                    if (discountPromotion == null) {
                        promotionView.setChecked(false);
                    } else {
                        Map<String, GiftRulePromotionReq.GiftGoodsItemReq> giftGoodsItemReqMap = Optional.ofNullable(couponPromotionReq)
                                .map(GiftRulePromotionReq::getGiftGoodItems)
                                .orElse(new ArrayList<>())
                                .stream()
                                .collect(Collectors.toMap(GiftRulePromotionReq.GiftGoodsItemReq::getId, Function.identity(), (a, b) -> a));

                        promotionView.setOrderThresholdPrice(discountPromotion.getOrderThresholdPrice());
                        promotionView.setHitGiftRule(discountPromotion.getHitGiftRule());
                        promotionView.setGiftCoupons(discountPromotion.getGiftCoupons());
                        promotionView.setGiftGoodItems(Optional.ofNullable(discountPromotion.getGiftGoodItems())
                                .orElse(new ArrayList<>())
                                .stream()
                                .peek(giftGoodsItemView -> {
                                    GiftRulePromotionReq.GiftGoodsItemReq giftGoodsItemReq = giftGoodsItemReqMap.get(giftGoodsItemView.getId());
                                    if (giftGoodsItemReq != null) {
                                        giftGoodsItemView.setChecked(giftGoodsItemReq.getChecked());
                                        giftGoodsItemView.setExpectedChecked(giftGoodsItemReq.getExpectedChecked());
                                    }
                                })
                                .collect(Collectors.toList())
                        );
                    }

                    if (notAvailablePromotionGiftRuleItems.contains(promotionView.getId())) {
                        promotionView.setIsCanBeUsed(0);
                    }
                })
                .collect(Collectors.toList());
        availableGiftRulePromotionViews.removeIf(promotionView -> promotionView.getIsCanBeUsed() == 0);
        return availableGiftRulePromotionViews;
    }

    public List<GiftRulePromotionView> applySingleBestDiscount(List<CalculateSinglePromotionItem> items, List<GiftRulePromotionView> existedGiftRulePromotions, List<GiftRulePromotionReq> giftRulePromotionReqs, String patientId) {
        return applyBestDiscount(items, new ArrayList<>(), existedGiftRulePromotions, giftRulePromotionReqs, patientId);
    }


    private <T extends BasicCalculatePromotionItem> void flatPriceCellToCalculatePromotionItem(CalculateGiftRulePromotion discountPromotion, List<T> items, List<CalculatePromotionAirPharmacyForm> airPharmacyForms) {

        if (discountPromotion == null) {
            return;
        }

        List<String> chargeFormItemIds = discountPromotion.getChargeFormItemIds();
        List<String> airPharmacyChargeFormIds = discountPromotion.getAirPharmacyChargeFormIds();

        if (CollectionUtils.isEmpty(airPharmacyChargeFormIds) && CollectionUtils.isEmpty(chargeFormItemIds)) {
            return;
        }

        if (chargeFormItemIds == null) {
            chargeFormItemIds = new ArrayList<>();
        }
        List<String> finalChargeFormItemIds = chargeFormItemIds;

        if (airPharmacyChargeFormIds == null) {
            airPharmacyChargeFormIds = new ArrayList<>();
        }
        List<String> finalAirPharmacyChargeFormIds = airPharmacyChargeFormIds;


        List<T> availableItems = items.stream()
                .filter(BasicCalculatePromotionItem::isCanApplyGiftPromotion)
                .filter(item -> finalChargeFormItemIds.contains(item.getId()))
                .collect(Collectors.toList());


        List<CalculatePromotionAirPharmacyForm> availableAirPharmacyForms = airPharmacyForms.stream()
                .filter(item -> finalAirPharmacyChargeFormIds.contains(item.getId()))
                .collect(Collectors.toList());

        List<FlatPriceHelper.FlatPriceCell> flatPriceCells = availableItems.stream().map(item -> {
            FlatPriceHelper.FlatPriceCell flatPriceCell = new FlatPriceHelper.FlatPriceCell();
            flatPriceCell.setId(item.getId());
            flatPriceCell.setName(item.getName());
            flatPriceCell.setPriority(FlatPriceHelper.PRIORITY_LOW);
            flatPriceCell.setTotalPrice(MathUtils.wrapBigDecimalAdd(item.getCanDiscountTotalPrice(), item.getExistedDiscountPrice()));
            return flatPriceCell;
        }).collect(Collectors.toList());

        flatPriceCells.addAll(
                availableAirPharmacyForms.stream().map(form -> {
                    FlatPriceHelper.FlatPriceCell flatPriceCell = new FlatPriceHelper.FlatPriceCell();
                    flatPriceCell.setId(form.getId());
                    flatPriceCell.setName(form.getName());
                    flatPriceCell.setPriority(FlatPriceHelper.PRIORITY_LOW);
                    flatPriceCell.setTotalPrice(MathUtils.wrapBigDecimalAdd(form.getCanDiscountTotalPrice(), form.getDiscountPrice()));
                    return flatPriceCell;
                }).collect(Collectors.toList())
        );

        if (CollectionUtils.isEmpty(flatPriceCells)) {
            return;
        }

        FlatPriceHelper flatPriceHelper = new FlatPriceHelper(discountPromotion.getDiscountPrice());
        flatPriceHelper.flat(flatPriceCells);
        Map<String, FlatPriceHelper.FlatPriceCell> flatPriceCellMap = flatPriceCells.stream().collect(Collectors.toMap(FlatPriceHelper.FlatPriceCell::getId, Function.identity(), (a, b) -> a));
        items.forEach(item -> {
            FlatPriceHelper.FlatPriceCell flatPriceCell = flatPriceCellMap.getOrDefault(item.getId(), null);
            if (flatPriceCell != null) {
                item.addDiscountPrice(flatPriceCell.getFlatPrice());
                PromotionSimple promotionSimple = new PromotionSimple();
                promotionSimple.setPromotionId(discountPromotion.getPromotionId());
                promotionSimple.setPromotionName(discountPromotion.getPromotionName());
                promotionSimple.setParentType(discountPromotion.getPromotionParentType());
                promotionSimple.setType(Promotion.Type.GIFT_RULE);
                promotionSimple.setDiscountPrice(flatPriceCell.getFlatPrice());
                item.addPromotion(promotionSimple);
            }
        });

        airPharmacyForms.forEach(form -> {
            FlatPriceHelper.FlatPriceCell flatPriceCell = flatPriceCellMap.getOrDefault(form.getId(), null);
            if (flatPriceCell != null) {
                form.setDiscountPrice(MathUtils.wrapBigDecimalAdd(form.getDiscountPrice(), flatPriceCell.getFlatPrice()));
                List<PromotionSimple> promotionSimples = form.getPromotions();
                if (promotionSimples == null) {
                    promotionSimples = new ArrayList<>();
                }
                PromotionSimple promotionSimple = new PromotionSimple();
                promotionSimple.setPromotionId(discountPromotion.getPromotionId());
                promotionSimple.setPromotionName(discountPromotion.getPromotionName());
                promotionSimple.setParentType(discountPromotion.getPromotionParentType());
                promotionSimple.setType(Promotion.Type.GIFT_RULE);
                promotionSimple.setDiscountPrice(flatPriceCell.getFlatPrice());
                promotionSimples.add(promotionSimple);
                form.setPromotions(promotionSimples);
            }
        });

    }

    public <T extends BasicCalculatePromotionItem> OncomingGiftRulePromotionView matchOncomingGiftRulePromotion(List<T> items) {

        if (CollectionUtils.isEmpty(promotionGiftRuleItems) || CollectionUtils.isEmpty(items)) {
            return null;
        }

        Map<String, GiftRulePromotionView> availableGiftRulePromotionViewIdMap = availableGiftRulePromotionViews
                .stream()
                .filter(promotionGiftRuleItem -> promotionGiftRuleItem.getOrderThresholdPrice() != null)
                .collect(Collectors.toMap(GiftRulePromotionView::getId, Function.identity(), (a, b) -> a));

        //找到每个promotion的最低门槛的优惠，如果目前已经匹配中，则需要将其排除
        return promotionGiftRuleItems.stream()
                .map(promotionGiftRuleItem -> promotionGiftRuleItem.generateOncomingGiftRulePromotionView(availableGiftRulePromotionViewIdMap.get(promotionGiftRuleItem.getPromotionId())))
                .filter(Objects::nonNull)
                .min(Comparator.comparing(OncomingGiftRulePromotionView::getOrderThresholdPrice)
                        .thenComparing(Comparator.comparing(OncomingGiftRulePromotionView::getDiscountPrice).reversed()))
                .orElse(null);
    }
}
