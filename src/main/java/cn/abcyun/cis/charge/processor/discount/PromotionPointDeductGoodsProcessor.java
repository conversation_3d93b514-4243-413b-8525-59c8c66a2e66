package cn.abcyun.cis.charge.processor.discount;

import cn.abcyun.bis.rpc.sdk.cis.model.patient.PointPromotionRsp;
import cn.abcyun.cis.charge.api.model.PatientPointDeductProductPromotionReq;
import cn.abcyun.cis.charge.api.model.PatientPointDeductProductPromotionView;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 积分抵扣商品处理器
 */
public class PromotionPointDeductGoodsProcessor {
    private List<PromotionPointDeductGoodsRule> deductGoodsRules;
    private int points;


    //构造的时候传入的折扣规则
    public PromotionPointDeductGoodsProcessor(List<PointPromotionRsp.DeductPromotionGoods> deductPromotionGoodsList, int points) {
        this.deductGoodsRules = deductPromotionGoodsList
                .stream()
                .filter(deductPromotionGoods -> deductPromotionGoods.getDeductionUnitCount() > 0)
                .map(PromotionPointDeductGoodsRule::new)
                .collect(Collectors.toList());
        this.points = points;
    }

    /**
     * 获取剩余的积分
     * @return
     */
    public int getLeftPoints() {
        return Math.max(points, 0);
    }

    /**
     * 应用积分商品抵扣规则
     */
    public List<PatientPointDeductProductPromotionView> applyDeduct(List<CalculatePromotionItem> items,
                                                                    List<PatientPointDeductProductPromotionView> existedPatientPointDeductGoodsList,
                                                                    List<PatientPointDeductProductPromotionReq> patientPointDeductProductPromotionReqs) {
        if (CollectionUtils.isEmpty(deductGoodsRules)) {
            return new ArrayList<>();
        }

        items = items != null ? items : new ArrayList<>();
        existedPatientPointDeductGoodsList = existedPatientPointDeductGoodsList != null ? existedPatientPointDeductGoodsList : new ArrayList<>();
        patientPointDeductProductPromotionReqs = patientPointDeductProductPromotionReqs != null ? patientPointDeductProductPromotionReqs : new ArrayList<>();

        mergePatientPointDeductProductPromotionReqs(patientPointDeductProductPromotionReqs, existedPatientPointDeductGoodsList);

        Map<String, PatientPointDeductProductPromotionReq> patientPointDeductPromotionReqMap = patientPointDeductProductPromotionReqs.stream()
                .collect(Collectors.toMap(PatientPointDeductProductPromotionReq::getGoodsId, Function.identity(), (a, b) -> a));

        //这个表示本张卡已经使用的卡项，key：goodsId，value：使用的次数
        Map<String, AtomicInteger> usedDeductGoodsIdMap = new HashMap<>();

        //按顺序抵扣goods
        List<CalculatePromotionItem> finalItems = items;
        deductGoodsRules.forEach(deductGoodsRule -> {
                int usedPoints = deductGoodsRule.fetchDeductGoodsAndReturnUsedPoints(finalItems, patientPointDeductPromotionReqMap.get(deductGoodsRule.getGoodsId()), usedDeductGoodsIdMap, points);
                points -= usedPoints;
        });

        //构造返回view对象
        List<PatientPointDeductProductPromotionView> patientPointDeductProductPromotionViews = deductGoodsRules.stream()
                .map(deductGoodsRule -> deductGoodsRule.generatePromotionView(finalItems, patientPointDeductPromotionReqMap.get(deductGoodsRule.getGoodsId())))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return patientPointDeductProductPromotionViews;
    }

    private void mergePatientPointDeductProductPromotionReqs(List<PatientPointDeductProductPromotionReq> patientPointDeductProductPromotionReqs, List<PatientPointDeductProductPromotionView> existedPatientPointDeductGoodsList) {
        List<String> goodIdReqs = patientPointDeductProductPromotionReqs.stream()
                .map(PatientPointDeductProductPromotionReq::getGoodsId)
                .collect(Collectors.toList());

        patientPointDeductProductPromotionReqs.addAll(existedPatientPointDeductGoodsList.stream()
                .filter(patientPointDeductPromotionView -> !goodIdReqs.contains(patientPointDeductPromotionView.getGoodsId()))
                .map(patientPointDeductPromotionView -> {
                    PatientPointDeductProductPromotionReq existedReq = new PatientPointDeductProductPromotionReq();
                    existedReq.setGoodsId(patientPointDeductPromotionView.getGoodsId())
                            .setChecked(patientPointDeductPromotionView.getChecked())
                            .setCurrentCount(patientPointDeductPromotionView.getCurrentCount());
                    return existedReq;
                }).collect(Collectors.toList()));
    }

}
