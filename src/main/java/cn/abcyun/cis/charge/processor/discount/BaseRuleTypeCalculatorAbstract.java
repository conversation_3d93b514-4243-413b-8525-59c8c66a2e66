package cn.abcyun.cis.charge.processor.discount;

import cn.abcyun.bis.rpc.sdk.cis.model.promotion.Promotion;

/**
 * <AUTHOR>
 * @date 2024/5/11 16:18
 */
public abstract class BaseRuleTypeCalculatorAbstract implements DiscountRulePromotionGoodsCalculator.RuleTypeCalculator {

    protected DiscountRulePromotionGoodsCalculator.PromotionGoodsCalculateResult.HitRuleDetail getHitRuleDetail(Promotion.PromotionDetail.PromotionGoods promotionGood, Promotion.PromotionDetail.BaseRuleDetail ruleDetail) {
        if (ruleDetail == null) {
            return null;
        }
        DiscountRulePromotionGoodsCalculator.PromotionGoodsCalculateResult.HitRuleDetail hitRuleDetail = new DiscountRulePromotionGoodsCalculator.PromotionGoodsCalculateResult.HitRuleDetail();
        hitRuleDetail.setDiscount(promotionGood.getIsFixedPrice() == 1 ? promotionGood.getFixedPrice() : promotionGood.getDiscount());
        hitRuleDetail.setType(promotionGood.getDiscountType());
        hitRuleDetail.setDiscountWay(promotionGood.getDiscountWay());
        hitRuleDetail.setRuleType(promotionGood.getRuleType());
        hitRuleDetail.setGoodsTypeName("");
        if (promotionGood.getDiscountType() == Promotion.PromotionDetail.PromotionGoods.DiscountType.DISCOUNT_FOR_TYPE) {
            hitRuleDetail.setGoodsTypeName(promotionGood.getName());
        }
        this.fillRuleDetail(hitRuleDetail, ruleDetail);
        return hitRuleDetail;
    }
}
