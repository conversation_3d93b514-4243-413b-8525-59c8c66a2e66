package cn.abcyun.cis.charge.processor.discount;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsBatchInfo;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsItem;
import cn.abcyun.bis.rpc.sdk.cis.model.promotion.Promotion;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.processor.PromotionQueryGiftGoodsDto;
import cn.abcyun.cis.charge.util.MathUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 单品优惠买赠算费工具
 */
@Slf4j
public class DiscountRulePromotionGoodsGiftCalculator extends DiscountRulePromotionGoodsAbstractCalculator {

    private final EnoughNGiftGoodsCalculator enoughNGiftGoodsCalculator;

    public DiscountRulePromotionGoodsGiftCalculator () {
        enoughNGiftGoodsCalculator = new EnoughNGiftGoodsCalculator();
    }

    @Override
    public Map<Integer, RuleTypeCalculator> getRuleTypeCalculatorMap() {
        return null;
    }

    /**
     * 买赠只有一个规则，不需要通过ruleType来区分
     * @param item
     * @param promotionGoods
     * @param singlePromotionGiftGoodsItemMap
     * @return
     */
    @Override
    public PromotionGoodsCalculateResult calculateDiscountPrice(CalculateSinglePromotionItem item,
                                                                String promotionId,
                                                                Promotion.PromotionDetail.PromotionGoods promotionGoods,
                                                                Map<String, GoodsItem> singlePromotionGiftGoodsItemMap) {

        return enoughNGiftGoodsCalculator.calculateDiscountPrice(item, promotionId, promotionGoods, singlePromotionGiftGoodsItemMap);
    }


    /**
     * 满N件赠送
     */
    public static class EnoughNGiftGoodsCalculator extends BaseRuleTypeCalculatorAbstract implements GiftGoodsCalculator {

        @Override
        public PromotionGoodsCalculateResult calculateDiscountPrice(CalculateSinglePromotionItem item,
                                                                    String promotionId,
                                                                    Promotion.PromotionDetail.PromotionGoods promotionGoods,
                                                                    Map<String, GoodsItem> singlePromotionGiftGoodsItemMap) {

            if (MapUtils.isEmpty(singlePromotionGiftGoodsItemMap)) {
                return null;
            }

            PromotionGiftGoodsResult promotionGiftGoodsResult = calculateGiftGoods(item, promotionGoods);

            if (Objects.isNull(promotionGiftGoodsResult) || MathUtils.compareZero(promotionGiftGoodsResult.getCount()) <= 0) {
                return null;
            }

            Promotion.PromotionDetail.GiftBySingleGoodsRuleDetail hitGiftBySingleGoodsRuleDetail = promotionGiftGoodsResult.getHitGiftBySingleGoodsRuleDetail();
            BigDecimal needGiftTotalCount = promotionGiftGoodsResult.getCount();


            String keyId = PromotionQueryGiftGoodsDto.generateKeyId(promotionId, item.getId(), promotionGiftGoodsResult.getGoodsId());
            GoodsItem giftGoodsItem = singlePromotionGiftGoodsItemMap.get(keyId);

            if (Objects.isNull(giftGoodsItem)) {
                log.info("买赠赠品不存在，promotionId:{}, chargeFormItemId:{}, itemProductId:{}", promotionId, item.getId(), promotionGiftGoodsResult.getGoodsId());
                return null;
            }

            //修正promotionGiftGoodsResult的isDismounting
            if (GoodsConst.isChineseMedicine(giftGoodsItem.getType(), giftGoodsItem.getSubType())) {
                promotionGiftGoodsResult.setIsDismounting(1);
            }

            //比较goodsItem的批次数量和计算出来赠送的数量是否一致，如果不一致，也返回null，表示没命中
            BigDecimal batchTotalCount = Optional.ofNullable(giftGoodsItem.getGoodsBatchInfoList())
                    .orElse(new ArrayList<>())
                    .stream()
                    .map(goodsBatchInfo -> {
                        if (promotionGiftGoodsResult.getIsDismounting() == 1) {
                            return goodsBatchInfo.getCutPieceCount();
                        }
                        return goodsBatchInfo.getCutPackageCount();
                    })
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            if (MathUtils.wrapBigDecimalCompare(needGiftTotalCount, batchTotalCount) != 0) {
                log.info("买赠赠品数量不匹配，promotionId:{}, chargeFormItemId:{}, giftGoodsId:{}, needGiftTotalCount:{}, batchTotalCount:{}",
                        promotionId, item.getId(), hitGiftBySingleGoodsRuleDetail.getGiftGoodsId(), needGiftTotalCount, batchTotalCount);
                return null;
            }

            //计算赠品的价值
            BigDecimal giftGoodsTotalPrice = giftGoodsItem.getGoodsBatchInfoList()
                    .stream()
                    .map(GoodsBatchInfo::getTotalSalePrice)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            promotionGiftGoodsResult.setDiscountPrice(giftGoodsTotalPrice.negate())
                    .setGoodsItem(giftGoodsItem);

            String ruleName;
            // 买A赠A
            if (hitGiftBySingleGoodsRuleDetail.getGiftGoodsId().equals(item.getId())) {
                // 不循环：买2赠1
                // 循环：每满2件，赠1件
                ruleName = hitGiftBySingleGoodsRuleDetail.getIsCycle() == 1 ? String.format("每满%s件，赠%s件", hitGiftBySingleGoodsRuleDetail.getThresholdCount().stripTrailingZeros().toPlainString(), hitGiftBySingleGoodsRuleDetail.getDiscountValue().stripTrailingZeros().toPlainString()) :
                        String.format("买%s赠%s", hitGiftBySingleGoodsRuleDetail.getThresholdCount().stripTrailingZeros().toPlainString(), hitGiftBySingleGoodsRuleDetail.getDiscountValue().stripTrailingZeros().toPlainString());
            } else {
                // 买A赠B
                // 不循环：买3件赠阿莫西林*1
                // 循环：每满2件，赠阿莫西林*1
                ruleName = hitGiftBySingleGoodsRuleDetail.getIsCycle() == 1 ? String.format("每满%s件，赠%s*%s", hitGiftBySingleGoodsRuleDetail.getThresholdCount().stripTrailingZeros().toPlainString(), giftGoodsItem.getName(), hitGiftBySingleGoodsRuleDetail.getDiscountValue().stripTrailingZeros().toPlainString()) :
                        String.format("买%s件赠%s*%s", hitGiftBySingleGoodsRuleDetail.getThresholdCount().stripTrailingZeros().toPlainString(), giftGoodsItem.getName(), hitGiftBySingleGoodsRuleDetail.getDiscountValue().stripTrailingZeros().toPlainString());
            }

            PromotionGoodsCalculateResult.HitRuleDetail.ChargeGiftBySingleGoodsRuleDetail chargeGiftBySingleGoodsRuleDetail = new PromotionGoodsCalculateResult.HitRuleDetail.ChargeGiftBySingleGoodsRuleDetail();
            BeanUtils.copyProperties(hitGiftBySingleGoodsRuleDetail, chargeGiftBySingleGoodsRuleDetail);
            chargeGiftBySingleGoodsRuleDetail.setGiftGoodsName(giftGoodsItem.getDisplayName());
            String unit = promotionGiftGoodsResult.getIsDismounting() == 0 ? giftGoodsItem.getPackageUnit() : giftGoodsItem.getPieceUnit();
            chargeGiftBySingleGoodsRuleDetail.setGiftGoodsUnit(unit);

            return new PromotionGoodsCalculateResult()
                    .setIsHit(1)
                    .setParticipationDiscountCount(promotionGiftGoodsResult.getOriginalItemCount())
                    .setGiftGoodsList(Arrays.asList(promotionGiftGoodsResult))
                    .setHitRuleDetail(getHitRuleDetail(promotionGoods, chargeGiftBySingleGoodsRuleDetail));
        }

        @Override
        public PromotionGoodsCalculateResult calculateOncomingDiscountPrice(CalculateSinglePromotionItem item, Promotion.PromotionDetail.PromotionGoods promotionGood) {
            //todo
            return null;
        }

        @Override
        public void fillRuleDetail(PromotionGoodsCalculateResult.HitRuleDetail hitRuleDetailResult, Promotion.PromotionDetail.BaseRuleDetail hitRuleDetail) {
            hitRuleDetailResult.setGiftBySingleGoodsRuleDetails((PromotionGoodsCalculateResult.HitRuleDetail.ChargeGiftBySingleGoodsRuleDetail) hitRuleDetail);
        }

        @Override
        public PromotionGiftGoodsResult calculateGiftGoods(CalculateSinglePromotionItem item, Promotion.PromotionDetail.PromotionGoods promotionGoods) {
            Promotion.PromotionDetail.GiftBySingleGoodsRuleDetail hitGiftBySingleGoodsRuleDetail = RuleTypeCalculator.filterHitEnoughNRuleDetail(item, promotionGoods.getGiftBySingleGoodsRuleDetails());

            //如果没有匹配中规则，直接返回null
            if (hitGiftBySingleGoodsRuleDetail == null) {
                return null;
            }

            //判断是否循环
            boolean isCycle = promotionGoods.getGiftBySingleGoodsRuleDetails().size() == 1 && hitGiftBySingleGoodsRuleDetail.getIsCycle() == 1;

            //处理限购
            BigDecimal maxTotalCount = RuleTypeCalculator.filterLeftSaleCount(item.getCanDiscountTotalCount(), promotionGoods);

            //计算需要循环的次数
            BigDecimal cycleCount = DiscountRuleProcessor.calculateCycleCount(isCycle, maxTotalCount, hitGiftBySingleGoodsRuleDetail.getThresholdCount());

            //计算赠送总数量
            BigDecimal needGiftTotalCount = hitGiftBySingleGoodsRuleDetail.getDiscountValue().multiply(cycleCount).setScale(0, RoundingMode.DOWN);
            //计算item原始参与赠送的数量
            BigDecimal originalItemCount = hitGiftBySingleGoodsRuleDetail.getThresholdCount().multiply(cycleCount).setScale(0, RoundingMode.DOWN);

            if (MathUtils.compareZero(needGiftTotalCount) <= 0) {
                return null;
            }

            return new PromotionGiftGoodsResult()
                    .setGoodsId(hitGiftBySingleGoodsRuleDetail.getGiftGoodsId())
                    .setCount(needGiftTotalCount)
                    .setOriginalItemCount(originalItemCount)
                    .setIsDismounting(0)
                    .setHitGiftBySingleGoodsRuleDetail(hitGiftBySingleGoodsRuleDetail);
        }

        @Override
        public PromotionGiftGoodsResult calculateOnComingGiftGoods(CalculateSinglePromotionItem item, Promotion.PromotionDetail.PromotionGoods promotionGoods) {
            return null;
//            //判断是否循环
//            boolean isCycle = promotionGoods.getGiftBySingleGoodsRuleDetails().size() == 1 && promotionGoods.getGiftBySingleGoodsRuleDetails().get(0).getIsCycle() == 1;
//
//            //先处理限购
//
//            return isCycle ? calculateOnComingForCycle(item, promotionGoods) : calculateOncomingForNotCycle(item, promotionGoods);

        }

        /**
         * 非循环累计计算预告
         * @param item
         * @param promotionGoods
         * @return
         */
        private PromotionGiftGoodsResult calculateOncomingForNotCycle(CalculateSinglePromotionItem item, Promotion.PromotionDetail.PromotionGoods promotionGoods) {

            return null;
        }

        /**
         * 循环累计计算预告
         * @param item
         * @param promotionGoods
         * @return
         */
        private PromotionGiftGoodsResult calculateOnComingForCycle(CalculateSinglePromotionItem item, Promotion.PromotionDetail.PromotionGoods promotionGoods) {

            PromotionGiftGoodsResult hitGiftGoodsResult = calculateGiftGoods(item, promotionGoods);

            Promotion.PromotionDetail.GiftBySingleGoodsRuleDetail hitGiftBySingleGoodsRuleDetail = Optional.ofNullable(hitGiftGoodsResult).map(PromotionGiftGoodsResult::getHitGiftBySingleGoodsRuleDetail).orElse(null);

            Promotion.PromotionDetail.GiftBySingleGoodsRuleDetail nextEnoughNRuleDetail = RuleTypeCalculator.nextEnoughNRuleDetail(hitGiftBySingleGoodsRuleDetail, promotionGoods.getGiftBySingleGoodsRuleDetails());

            BigDecimal cycleCount = null;
            //命中的规则为空，表示第一次循环都没有匹配到，那么需要循环的数量就是1
            if (Objects.isNull(hitGiftGoodsResult)) {
                cycleCount = BigDecimal.ONE;



                //判断是否有限购
                //计算item原始参与赠送的数量
                BigDecimal originalItemCount = hitGiftBySingleGoodsRuleDetail.getThresholdCount().multiply(cycleCount).setScale(0, RoundingMode.DOWN);


            }

            return null;

        }
    }

}
