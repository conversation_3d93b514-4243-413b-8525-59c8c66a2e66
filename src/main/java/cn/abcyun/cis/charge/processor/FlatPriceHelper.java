package cn.abcyun.cis.charge.processor;

import cn.abcyun.cis.charge.util.FlatPriceTool;
import cn.abcyun.cis.charge.util.JsonUtils;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.common.log.marker.AbcLogMarker;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

//平摊费用计算器
public class FlatPriceHelper {
    private static final Logger sLogger = LoggerFactory.getLogger(FlatPriceHelper.class);

    public static final int PRIORITY_LOW = 0;
    public static final int PRIORITY_MIDDLE = 1;
    public static final int PRIORITY_HIGH = 2;

    private BigDecimal targetPrice;
    private BigDecimal leftPrice;
    private boolean isNegativeFlat;

    public FlatPriceHelper(BigDecimal targetPrice) {
        this.targetPrice = MathUtils.wrapBigDecimalOrZero(targetPrice);
        this.leftPrice = this.targetPrice;
        isNegativeFlat = this.targetPrice.compareTo(BigDecimal.ZERO) < 0;
    }

    public void flat(List<FlatPriceCell> flatPriceCells) {
        if (flatPriceCells == null || flatPriceCells.size() == 0) {
            return;
        }

        for (FlatPriceCell flatPriceCell : flatPriceCells) {
            if (MathUtils.wrapBigDecimalCompare(flatPriceCell.getTotalPrice(), BigDecimal.ZERO) < 0) {
                sLogger.error("totalPrice必须大于等于0, targetPrice: {}, cells: {}", targetPrice, JsonUtils.dump(flatPriceCells));
                throw new IllegalArgumentException("totalPrice必须大于等于0");
            }
        }

        int priority = PRIORITY_HIGH;
        BigDecimal totalPrice = sumTotalPriceByAbovePriority(flatPriceCells, priority);
        if (MathUtils.wrapBigDecimalCompare(totalPrice, targetPrice.abs()) < 0) {
            priority = PRIORITY_MIDDLE;
            totalPrice = sumTotalPriceByAbovePriority(flatPriceCells, priority);
            if (MathUtils.wrapBigDecimalCompare(totalPrice, targetPrice.abs()) < 0) {
                priority = PRIORITY_LOW;
                totalPrice = sumTotalPriceByAbovePriority(flatPriceCells, priority);
            }
        }

        flatPriceCells.forEach(flatPriceCell -> flatPriceCell.setFlatPrice(BigDecimal.ZERO));

        int finalPriority = priority;
        List<FlatPriceCell> toFlatPriceCells = flatPriceCells.stream()
                .filter(flatPriceCell -> flatPriceCell.getPriority() >= finalPriority).collect(Collectors.toList());

        //总金额为0，需要摊的金额为正值，这种情况，直接按cells的个数进行均分
        if (!isNegativeFlat && totalPrice.compareTo(BigDecimal.ZERO) == 0) {

            BigDecimal averagePrice = this.targetPrice.divide(new BigDecimal(flatPriceCells.size()), 2, RoundingMode.DOWN);

            this.leftPrice = this.targetPrice.subtract(averagePrice.multiply(new BigDecimal(flatPriceCells.size())));

            flatPriceCells.stream().forEach(flatPriceCell -> flatPriceCell.setFlatPrice(averagePrice));

            //如果还有剩余的数，则放到第一个
            if (this.leftPrice.compareTo(BigDecimal.ZERO) != 0) {
                FlatPriceCell firstFlatPriceCell = toFlatPriceCells.get(0);
                firstFlatPriceCell.setFlatPrice(firstFlatPriceCell.getFlatPrice().add(this.leftPrice));
            }

            return;
        }

        if (totalPrice.compareTo(BigDecimal.ZERO) <= 0) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "totalPrice:{}", totalPrice);
            return;
        }

        //如果要摊的值为负值，则0元的cell就不参与平摊了
        if (isNegativeFlat) {
            toFlatPriceCells = toFlatPriceCells.stream().filter(cell -> MathUtils.wrapBigDecimalCompare(cell.getTotalPrice(), BigDecimal.ZERO) != 0).collect(Collectors.toList());
        }

        if (toFlatPriceCells.size() == 0) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "toFlatPriceCells size = 0");
            return;
        }

        for (int i = 0; i <= toFlatPriceCells.size() - 1; i++) {
            FlatPriceCell cell = toFlatPriceCells.get(i);
            BigDecimal flatPrice = MathUtils.wrapBigDecimalOrZero(cell.getTotalPrice()).multiply(this.targetPrice).divide(totalPrice, 2, isNegativeFlat ? RoundingMode.DOWN : RoundingMode.HALF_UP);
            if (!isNegativeFlat) {
                flatPrice = MathUtils.min(flatPrice, this.leftPrice);
            }
            cell.setFlatPrice(flatPrice);
            this.leftPrice = this.leftPrice.subtract(flatPrice);
        }

        if (isNegativeFlat && this.leftPrice.compareTo(BigDecimal.ZERO) < 0) {
            //如果是负值平摊，则平摊下来的值不能超过cell的总金额
            for (int i = 0; i < toFlatPriceCells.size() - 1; i++) {
                FlatPriceCell cell = toFlatPriceCells.get(i);
                BigDecimal cellCanLeftPrice = MathUtils.wrapBigDecimalOrZero(cell.getTotalPrice()).subtract(cell.getFlatPrice().abs());
                if (cellCanLeftPrice.compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }

                cellCanLeftPrice = MathUtils.max(cellCanLeftPrice.negate(), this.leftPrice);

                cell.setFlatPrice(MathUtils.wrapBigDecimalAdd(cell.getFlatPrice(), cellCanLeftPrice));

                this.leftPrice = this.leftPrice.subtract(cellCanLeftPrice);

                if (this.leftPrice.compareTo(BigDecimal.ZERO) >= 0) {
                    break;
                }
            }
        } else {
            FlatPriceCell lastFlatCell = toFlatPriceCells.get(toFlatPriceCells.size() - 1);
            lastFlatCell.setFlatPrice(lastFlatCell.getFlatPrice().add(leftPrice));
        }
    }

    private BigDecimal sumTotalPriceByAbovePriority(List<FlatPriceCell> flatPriceCells, int priority) {
        if (flatPriceCells == null) {
            return BigDecimal.ZERO;
        }

        return flatPriceCells.stream()
                .filter(flatPriceCell -> flatPriceCell.getPriority() >= priority)
                .map(FlatPriceCell::getTotalPrice)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Data
    @Accessors(chain = true)
    @EqualsAndHashCode(callSuper = true)
    public static class FlatPriceCell extends FlatPriceTool.IFlatCell.BasicCell {
        private String id;
        private String name;
        private BigDecimal totalPrice;
        private int priority;
    }
}
