package cn.abcyun.cis.charge.processor.calculate;

import cn.abcyun.cis.charge.api.model.BasicCalculateFormReq;
import cn.abcyun.cis.charge.api.model.BasicCalculateFormRsp;
import cn.abcyun.cis.charge.api.model.BasicCalculateItemReq;
import cn.abcyun.cis.charge.base.ChargeServiceError;
import cn.abcyun.cis.charge.base.ChargeServiceException;
import cn.abcyun.cis.charge.processor.FlatPriceHelper;
import cn.abcyun.cis.charge.processor.SmartFlatPriceHelper;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.cis.commons.util.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class CalculateFormProcessor {

    private BasicCalculateFormReq formReq;

    private List<CalculateItemProcessor> itemProcessors = new ArrayList<>();

    public List<CalculateItemProcessor> getItemProcessors() {
        return itemProcessors;
    }

    public CalculateFormProcessor(BasicCalculateFormReq formReq, CalculateModel calculateModel) {
        this.formReq = formReq;

        build(calculateModel);
    }

    private void build(CalculateModel calculateModel) {

        if (Objects.isNull(formReq)) {
            log.info("formReq is null");
            return;
        }

        if (CollectionUtils.isEmpty(formReq.getItems())) {
            log.info("formReq.items is null");
            return;
        }

        formReq.getItems().forEach(itemReq -> itemProcessors.add(new CalculateItemProcessor(itemReq, calculateModel)));
    }

    public BasicCalculateFormReq getFormReq() {
        return formReq;
    }

    public void calculateItemExpectedPrice() {
        itemProcessors.forEach(CalculateItemProcessor::calculateItemExpectedPrice);
    }

    public void calculateFormExpectedPrice() {
        //看当前form是否有议价
        if (Objects.isNull(formReq.getExpectedTotalPrice())) {
            return;
        }

        resetItemPrice();

        updateItemExpectedTotalPrice(formReq.getExpectedTotalPrice());
    }

    public SmartFlatPriceHelper.SmartFlatPriceCell generateSmartFlatPriceCell() {
        return new SmartFlatPriceHelper.SmartFlatPriceCell(formReq.getId(), "", formReq.getSourceTotalPrice(), formReq.getSourceFormType());
    }


    /**
     * 更新item的当前价格
     * @param expectedTotalPrice
     */
    private void updateItemExpectedTotalPrice(BigDecimal expectedTotalPrice) {

        if (expectedTotalPrice == null) {
            return;
        }

        List<FlatPriceHelper.FlatPriceCell> flatPriceCells = itemProcessors.stream()
                .filter(itemProcessor -> {
                    BasicCalculateItemReq itemReq = itemProcessor.getItemReq();
                    BigDecimal totalCount = MathUtils.calculateTotalCount(itemReq.getUnitCount(), itemReq.getDoseCount());
                    return MathUtils.wrapBigDecimalCompare(totalCount, BigDecimal.ZERO) > 0;
                })
                .map(itemProcessor -> {
                    FlatPriceHelper.FlatPriceCell flatPriceCell = new FlatPriceHelper.FlatPriceCell();
                    flatPriceCell.setId(itemProcessor.getItemReq().getId());
                    flatPriceCell.setName(itemProcessor.getItemReq().getName());
                    flatPriceCell.setPriority(FlatPriceHelper.PRIORITY_HIGH);
                    flatPriceCell.setTotalPrice(itemProcessor.getSourceTotalPrice());
                    return flatPriceCell;
                }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(flatPriceCells)) {
            throw new ChargeServiceException(ChargeServiceError.CALCULATE_ITEM_IS_ALL_ZERO_COUNT);
        }

        FlatPriceHelper flatPriceHelper = new FlatPriceHelper(expectedTotalPrice);
        flatPriceHelper.flat(flatPriceCells);

        Map<String, FlatPriceHelper.FlatPriceCell> flatPriceCellMap = ListUtils.toMap(flatPriceCells, FlatPriceHelper.FlatPriceCell::getId);

        itemProcessors.forEach(itemProcessor -> {
            FlatPriceHelper.FlatPriceCell flatPriceCell = flatPriceCellMap.get(itemProcessor.getItemReq().getId());

            if (Objects.isNull(flatPriceCell)) {
                return;
            }

            itemProcessor.updateItemExpectedTotalPrice(flatPriceCell.getFlatPrice());
        });
    }

    /**
     * 更新form的当前价格
     * @param adjustmentPrice 从整单议价上平摊下来的议价值
     */
    public void flatAdjustmentPrice(BigDecimal adjustmentPrice) {
        BigDecimal expectedTotalPrice = null;
        if (MathUtils.wrapBigDecimalCompare(adjustmentPrice, BigDecimal.ZERO) != 0) {
            expectedTotalPrice = MathUtils.wrapBigDecimalAdd(formReq.getSourceTotalPrice(), adjustmentPrice);
        }
        formReq.setExpectedTotalPrice(expectedTotalPrice);
        updateItemExpectedTotalPrice(expectedTotalPrice);
    }

    public BasicCalculateFormRsp generateBasicCalculateFormRsp() {

        BasicCalculateFormRsp formRsp = new BasicCalculateFormRsp();
        return formRsp.setId(formReq.getId())
                .setTotalPrice(calculateCurrentTotalPrice())
                .setSourceTotalPrice(formReq.getSourceTotalPrice())
                .setIsTotalPriceChanged(formReq.getIsTotalPriceChanged())
                .setSourceFormType(formReq.getSourceFormType())
                .setItems(itemProcessors.stream().map(CalculateItemProcessor::generateBasicCalculateItemRsp).collect(Collectors.toList()));
    }

    public void resetPrice() {
        formReq.clearExpectedPrice();
        resetItemPrice();
    }

    public void resetItemPrice() {
        itemProcessors.forEach(CalculateItemProcessor::resetItemPrice);
    }

    public BigDecimal calculateCurrentTotalPrice() {
        return itemProcessors.stream().map(itemProcessor -> MathUtils.wrapBigDecimalOrZero(itemProcessor.getItemReq().getTotalPrice())).reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}
