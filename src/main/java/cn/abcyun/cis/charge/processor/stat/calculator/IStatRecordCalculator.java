package cn.abcyun.cis.charge.processor.stat.calculator;

import cn.abcyun.cis.charge.base.ChargeVersionConstants;
import cn.abcyun.cis.charge.helper.ChargeTransactionRecordDiscountInfoHelper;
import cn.abcyun.cis.charge.model.ChargeTransaction;
import cn.abcyun.cis.charge.processor.stat.StatRecordByChooseCalculateCell;
import cn.abcyun.cis.charge.util.MathUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

public interface IStatRecordCalculator {
    BigDecimal flatCell(ChargeTransaction chargeTransaction, List<StatRecordByChooseCalculateCell> cells, BigDecimal toRecordPrice, int refundFlatType);

    void flatCellBatchInfo(List<StatRecordByChooseCalculateCell> cells);

    default BigDecimal calculate(ChargeTransaction chargeTransaction, List<StatRecordByChooseCalculateCell> cells, BigDecimal toRecordPrice, int refundFlatType) {
        BigDecimal leftPrice = flatCell(chargeTransaction, cells, toRecordPrice, refundFlatType);
        flatCellBatchInfo(cells);
        return leftPrice;
    }

    static void fillOtherToRecordFields(List<StatRecordByChooseCalculateCell> calculateCellList) {
        //按比例处理其他项目
        for (StatRecordByChooseCalculateCell cell : calculateCellList) {
            if (cell.getDiscountedPrice().compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }

            if (MathUtils.wrapBigDecimalCompare(cell.getToRecordDiscountedPrice(), cell.getLeftToRecordDiscountedPrice()) == 0) {
                cell.setToRecordTotalCostPrice(cell.getTotalCostPrice().subtract(cell.getRecordedTotalCostPrice()));
                cell.setToRecordDiscountPrice(cell.getDiscountPrice().subtract(cell.getRecordedDiscountPrice()));
                cell.setToRecordPromotionDiscountInfo(ChargeTransactionRecordDiscountInfoHelper.generateToRecordChargeTransactionRecordDiscountInfoForLastRecord(cell.getItemPromotionInfo(), cell.getRecordedChargeTransactionRecordDiscountInfo()));
            } else {
                //这里不直接使用rate，是为了避免两次向下舍去，尽量减少精度的损失
                cell.setToRecordTotalCostPrice(cell.getTotalCostPrice().multiply(cell.getToRecordDiscountedPrice()).divide(cell.getDiscountedPrice(), 4, RoundingMode.DOWN));
                cell.setToRecordDiscountPrice(cell.getDiscountPrice().multiply(cell.getToRecordDiscountedPrice()).divide(cell.getDiscountedPrice(), 2, RoundingMode.DOWN));
                cell.setToRecordPromotionDiscountInfo(ChargeTransactionRecordDiscountInfoHelper.generateToRecordChargeTransactionRecordDiscountInfo(cell.getItemPromotionInfo(), cell.getToRecordDiscountPrice(), cell.getToRecordDiscountedPrice(), cell.getDiscountedPrice(), cell.getChargeVersion() == ChargeVersionConstants.V1));
            }

        }
    }
}
