package cn.abcyun.cis.charge.processor.stat;

import cn.abcyun.bis.rpc.sdk.cis.model.charge.GoodsFeeType;
import cn.abcyun.cis.charge.base.ChargeVersionConstants;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.helper.ChargeTransactionRecordDiscountInfoHelper;
import cn.abcyun.cis.charge.model.ChargeFormItem;
import cn.abcyun.cis.charge.model.ChargeTransaction;
import cn.abcyun.cis.charge.model.ChargeTransactionRecord;
import cn.abcyun.cis.charge.model.ChargeTransactionRecordAdditional;
import cn.abcyun.cis.charge.processor.StatRecordProcessor;
import cn.abcyun.cis.charge.processor.stat.calculator.*;
import cn.abcyun.cis.charge.service.dto.ChargeDiscountInfo;
import cn.abcyun.cis.charge.util.JsonUtils;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.cis.commons.model.ComposeType;
import cn.abcyun.common.log.marker.AbcLogMarker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;

@Slf4j
public class StatRecordByChooseCalculateHelper {

    public static StatRecordByChooseCalculateCell createCalculateCell(int chargeVersion) {
        StatRecordByChooseCalculateCell calculateCell = null;
        switch (chargeVersion) {
            case ChargeVersionConstants.V0:
                calculateCell = new StatRecordByChooseCalculateCellV0();
                break;
            case ChargeVersionConstants.V1:
                calculateCell = new StatRecordByChooseCalculateCellV1();
                break;
            default:
                throw new RuntimeException("未知的chargeVersion");
        }

        calculateCell.setChargeVersion(chargeVersion);
        return calculateCell;
    }

    public enum PayTypeCalculateEnum {
        //支付成功
        PAID(StatRecordProcessor.PayType.PAID, new StatRecordPaidCalculator()),
        //部分支付
        PARTED_PAID(StatRecordProcessor.PayType.PARTED_PAID, new StatRecordPartedPaidCalculator()),
        //部分支付状态下全退
        PAID_BACK(StatRecordProcessor.PayType.PAID_BACK, new StatRecordPaidBackCalculator()),
        //部分支付状态下部分退
        PARTED_PAID_BACK(StatRecordProcessor.PayType.PARTED_PAID_BACK, new StatRecordPartedPaidBackCalculator()),
        //全退，全退只是表示本次没有产生新的欠费金额，这个type也有可能是只退欠费金额
        REFUND(StatRecordProcessor.PayType.REFUND, new StatRecordRefundCalculator()),
        //部分退
        PARTED_REFUND(StatRecordProcessor.PayType.PARTED_REFUND, new StatRecordPartedRefundCalculator());

        private int payType;
        private IStatRecordCalculator calculator;

        PayTypeCalculateEnum(int payType, IStatRecordCalculator calculator) {
            this.payType = payType;
            this.calculator = calculator;
        }

        public int getPayType() {
            return payType;
        }

        public void setPayType(int payType) {
            this.payType = payType;
        }

        public IStatRecordCalculator getCalculator() {
            return calculator;
        }

        public void setCalculator(IStatRecordCalculator calculator) {
            this.calculator = calculator;
        }

        public static PayTypeCalculateEnum getPayTypeCalculateEnumByPayType(int payType) {
            return Stream.of(PayTypeCalculateEnum.values())
                    .filter(payTypeCalculateEnum -> payTypeCalculateEnum.getPayType() == payType)
                    .findFirst()
                    .orElse(null);
        }
    }

    /**
     * 计算每个cell应该分配的金额
     *
     * @param calculateCellList
     * @param toRecordPrice
     * @return 分不完了的情况，剩下的金额
     */
    public static BigDecimal calculate(ChargeTransaction chargeTransaction,
                                       List<StatRecordByChooseCalculateCell> calculateCellList,
                                       BigDecimal toRecordPrice,
                                       int payType,
                                       int refundFlatType) {
        if (calculateCellList == null || calculateCellList.size() == 0) {
            return toRecordPrice;
        }

        calculateCellList.forEach(cell -> {
            //优先处理抵扣
            cell.dealDeductItem();
            //处理本次场景类型是普通收退费还是欠收欠退
            cell.bindSceneTypeAndProductUnitCount(payType);

        });

        //找到不同方式的具体实现，用实现方法去算费
        PayTypeCalculateEnum payTypeCalculateEnum = PayTypeCalculateEnum.getPayTypeCalculateEnumByPayType(payType);

        if (Objects.isNull(payTypeCalculateEnum)) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "payTypeCalculateEnum is null, payType: {}", payType);
            throw new IllegalStateException(String.format("payTypeCalculateEnum is null, payType: %d", payType));
        }
        return payTypeCalculateEnum.getCalculator().calculate(chargeTransaction, calculateCellList, toRecordPrice, refundFlatType);
    }

    public static void calculateComposeCell(List<StatRecordByChooseCalculateCell> composeCells, boolean needDealClinicShebaoCodeMatchCostPrice, int payType, int chargeVersion) {

        if (CollectionUtils.isEmpty(composeCells)) {
            return;
        }

        for (StatRecordByChooseCalculateCell composeCell : composeCells) {

            BigDecimal composeTotalPrice = BigDecimal.ZERO;
            BigDecimal composeTotalCostPrice = BigDecimal.ZERO;
            BigDecimal composeDiscountPrice = BigDecimal.ZERO;
            BigDecimal composeDeductedPrice = BigDecimal.ZERO;

            composeCell.dealDeductItem();

            List<ChargeDiscountInfo> childrenDiscountInfos = new ArrayList<>();

            for (StatRecordByChooseCalculateCell composeChildrenCell : composeCell.getComposeChildrenCells()) {
                for (ChargeTransactionRecord record : composeChildrenCell.getThisTimeRecords()) {

                    composeTotalPrice = MathUtils.wrapBigDecimalAdd(composeTotalPrice, MathUtils.wrapBigDecimalSubtract(record.getTotalPrice(), record.getDeductTotalPrice()));

                    composeTotalCostPrice = MathUtils.wrapBigDecimalAdd(composeTotalCostPrice, MathUtils.wrapBigDecimalSubtract(record.getTotalCostPrice(), record.getDeductTotalCostPrice()));
                    //折扣金额
                    composeDiscountPrice = MathUtils.wrapBigDecimalAdd(composeDiscountPrice, MathUtils.wrapBigDecimalSubtract(record.getDiscountPrice(), record.getDeductTotalPrice().negate()));
                    //抵扣金额
                    composeDeductedPrice = MathUtils.wrapBigDecimalAdd(composeDeductedPrice, Optional.ofNullable(record.getAdditional()).map(ChargeTransactionRecordAdditional::getDeductTotalPrice).orElse(BigDecimal.ZERO));

                    Optional.ofNullable(record.getAdditional()).map(ChargeTransactionRecordAdditional::getDiscountInfo)
                            .ifPresent(discountInfo -> childrenDiscountInfos.add(JsonUtils.readValue(JsonUtils.dump(discountInfo), ChargeDiscountInfo.class)));
                }
            }

            //支付逻辑
            if (StatRecordProcessor.PayType.isPaid(payType)) {
                //计算历史已经记录了的总数量
                BigDecimal historyProductUnitCount = composeCell.getRecordedUnitCount();
                BigDecimal unitCount = composeCell.getUnitCount();
                if (unitCount.compareTo(historyProductUnitCount) > 0 || composeCell.getToRecordDeductUnitCount().compareTo(BigDecimal.ZERO) > 0) {
                    composeCell.setToRecordSceneType(ChargeTransactionRecord.SceneType.NORMAL);
                    composeCell.setToRecordUnitCount(cn.abcyun.cis.charge.util.MathUtils.max(BigDecimal.ZERO, unitCount.subtract(historyProductUnitCount)));
                } else {
                    composeCell.setToRecordSceneType(ChargeTransactionRecord.SceneType.OWE_PAY);
                    composeCell.setToRecordUnitCount(BigDecimal.ZERO);
                }

                BigDecimal thisTimeReceivableFee = composeCell.getThisTimeReceivableFee();
                composeCell.setToRecordDiscountedPrice(thisTimeReceivableFee);
            } else {
                //区分是套餐还是费用母项
                if (composeCell.getChargeFormItem().getComposeType() == ComposeType.COMPOSE) {
                    //退费逻辑，判断金额是否未退完，historyRecords和本次要记录的所有子项，比较金额和数量是否全部退完
                    boolean childIsAllRefunded = composeCell.getComposeChildrenItemPaidCells().stream()
                            .allMatch(StatRecordByChooseCalculateCell::isAllRefunded);

                    if (childIsAllRefunded) {
                        composeCell.setToRecordSceneType(ChargeTransactionRecord.SceneType.NORMAL);
                        composeCell.setToRecordUnitCount(composeCell.getUnitCount());
                    } else {
                        composeCell.setToRecordSceneType(ChargeTransactionRecord.SceneType.OWE_REFUND);
                        composeCell.setToRecordUnitCount(BigDecimal.ZERO);
                    }

                } else if (composeCell.getChargeFormItem().getGoodsFeeType() == GoodsFeeType.FEE_PARENT) {

                    boolean childIsAllOweRefund = composeCell.getComposeChildrenCells()
                            .stream()
                            .flatMap(child -> child.getThisTimeRecords().stream())
                            .allMatch(record -> record.getSceneType() == ChargeTransactionRecord.SceneType.OWE_REFUND);
                    //如果子项都是欠退，那么母项也记录欠退
                    if (childIsAllOweRefund) {
                        composeCell.setToRecordSceneType(ChargeTransactionRecord.SceneType.OWE_REFUND);
                        composeCell.setToRecordUnitCount(BigDecimal.ZERO);
                    } else {
                        composeCell.setToRecordSceneType(ChargeTransactionRecord.SceneType.NORMAL);
                        composeCell.setToRecordUnitCount(composeCell.getUnitCount());
                    }
                }

                //套餐的记录金额永远等于本次子项记录的总金额
                composeCell.setToRecordDiscountedPrice(composeTotalPrice.add(composeDiscountPrice));
            }


            //套餐的折扣价
            composeCell.setToRecordDiscountPrice(composeDiscountPrice);

            //诊所管家中治疗理疗检查检验其他费用有多个对码时，由于使用了医嘱费用项的结构，但是又没有遵循子项的成本和 = 母项的成本，这种母项数据需要单独对成本金额处理
            if (canDealClinicShebaoCodeMatchCostPriceItem(needDealClinicShebaoCodeMatchCostPrice, composeCell.getProductType())) {
                composeTotalCostPrice = calculateShebaoCodeMatchCostPrice(composeCell, payType);
            }

            //套餐的成本价
            composeCell.setToRecordTotalCostPrice(composeTotalCostPrice);

            composeCell.setToRecordPromotionDiscountInfo(ChargeTransactionRecordDiscountInfoHelper.mergeChargeTransactionRecordDiscountInfo(childrenDiscountInfos));
        }
    }

    public static boolean canDealClinicShebaoCodeMatchCostPriceItem(boolean needDealClinicShebaoCodeMatchCostPrice, int productType) {
        return needDealClinicShebaoCodeMatchCostPrice
                && (productType == Constants.ProductType.TREATMENT || productType == Constants.ProductType.EXAMINATION || productType == Constants.ProductType.OTHER_FEE);
    }

    private static BigDecimal calculateShebaoCodeMatchCostPrice(StatRecordByChooseCalculateCell composeCell, int payType) {
        BigDecimal toRecordTotalPrice = BigDecimal.ZERO;
        if (payType == StatRecordProcessor.PayType.PARTED_PAID) {
            if (MathUtils.wrapBigDecimalCompare(composeCell.getDiscountedPrice(), BigDecimal.ZERO) > 0) {
                toRecordTotalPrice = composeCell.getTotalCostPrice().multiply(composeCell.getToRecordDiscountedPrice()).divide(composeCell.getDiscountedPrice(), 2, RoundingMode.DOWN);
            }
        } else if (payType == StatRecordProcessor.PayType.PAID) {
            toRecordTotalPrice = composeCell.getTotalCostPrice().subtract(composeCell.getRecordedTotalCostPrice());
        } else if (payType == StatRecordProcessor.PayType.PAID_BACK) {
            toRecordTotalPrice = composeCell.getRecordedTotalCostPrice();
        } else if (payType == StatRecordProcessor.PayType.PARTED_PAID_BACK) {
            if (MathUtils.wrapBigDecimalCompare(composeCell.getDiscountedPrice(), BigDecimal.ZERO) > 0) {
                toRecordTotalPrice = composeCell.getTotalCostPrice().multiply(composeCell.getToRecordDiscountedPrice()).divide(composeCell.getDiscountedPrice(), 2, RoundingMode.DOWN);
            }
        } else if (payType == StatRecordProcessor.PayType.PARTED_REFUND) {
            if (MathUtils.wrapBigDecimalCompare(composeCell.getDiscountedPrice(), BigDecimal.ZERO) > 0) {
                toRecordTotalPrice = composeCell.getTotalCostPrice().multiply(composeCell.getToRecordDiscountedPrice()).divide(composeCell.getDiscountedPrice(), 2, RoundingMode.DOWN);
            }
        } else if (payType == StatRecordProcessor.PayType.REFUND) {
            //判断是否退完了，如果退完了，直接减，没退完，还是用比例
            //退费逻辑，判断金额是否未退完，historyRecords和本次要记录的所有子项，比较金额和数量是否全部退完
            boolean childIsAllRefunded = composeCell.getComposeChildrenItemPaidCells().stream()
                    .allMatch(StatRecordByChooseCalculateCell::isAllRefunded);
            if (childIsAllRefunded) {
                toRecordTotalPrice = composeCell.getTotalCostPrice().subtract(composeCell.getRecordedTotalCostPrice());
            } else if (MathUtils.wrapBigDecimalCompare(composeCell.getDiscountedPrice(), BigDecimal.ZERO) > 0) {
                toRecordTotalPrice = composeCell.getTotalCostPrice().multiply(composeCell.getToRecordDiscountedPrice()).divide(composeCell.getDiscountedPrice(), 2, RoundingMode.DOWN);
            }
        }

        return toRecordTotalPrice;
    }

    public static StatRecordByChooseCalculateCell generateStatRecordByChooseCalculateCell(ChargeFormItem chargeFormItem, List<ChargeTransactionRecord> itemHistoryRecords, int chargeVersion) {
        if (Objects.isNull(chargeFormItem)) {
            return null;
        }
        StatRecordByChooseCalculateCell cell = StatRecordByChooseCalculateHelper.createCalculateCell(chargeVersion);
        cell.setId(chargeFormItem.getId());
        cell.setChargeFormItem(chargeFormItem);
        cell.setHistoryRecords(itemHistoryRecords);
        return cell;
    }

}
