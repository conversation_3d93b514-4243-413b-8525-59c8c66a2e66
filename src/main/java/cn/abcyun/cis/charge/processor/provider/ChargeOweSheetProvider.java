package cn.abcyun.cis.charge.processor.provider;

import cn.abcyun.cis.charge.model.ChargeOweSheet;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.model.ChargeTransaction;

import java.util.List;

public interface ChargeOweSheetProvider {

    /**
     * 创建或修改欠费单
     * @param chargeSheet
     * @param chargeTransaction
     * @param operatorId
     * @return
     */
    void insertOrUpdateChargeOweSheet(ChargeSheet chargeSheet, ChargeTransaction chargeTransaction, String operatorId);

    List<ChargeOweSheet> findChargeOweSheetsByChargeSheetId(String clinicId, String chargeSheetId);

    ChargeOweSheet findCurrentChargeOweSheetByChargeSheetId(String clinicId, String chargeSheetId);
}
