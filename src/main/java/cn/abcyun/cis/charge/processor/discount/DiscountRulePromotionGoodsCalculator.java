package cn.abcyun.cis.charge.processor.discount;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsItem;
import cn.abcyun.bis.rpc.sdk.cis.model.promotion.Promotion;
import cn.abcyun.cis.charge.util.MathUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public interface DiscountRulePromotionGoodsCalculator {

    interface GiftGoodsCalculator extends RuleTypeCalculator {
        PromotionGiftGoodsResult calculateGiftGoods(CalculateSinglePromotionItem item, Promotion.PromotionDetail.PromotionGoods promotionGoods);

        PromotionGiftGoodsResult calculateOnComingGiftGoods(CalculateSinglePromotionItem item, Promotion.PromotionDetail.PromotionGoods promotionGoods);
    }

    /**
     * 优惠规则类型计算器
     */
    interface RuleTypeCalculator {

        /**
         * 计算优惠金额
         *
         * @param item
         * @param promotionGoods
         * @return
         */
        PromotionGoodsCalculateResult calculateDiscountPrice(CalculateSinglePromotionItem item,
                                                             String promotionId,
                                                             Promotion.PromotionDetail.PromotionGoods promotionGoods,
                                                             Map<String, GoodsItem> singlePromotionGiftGoodsItemMap);

        /**
         * 计算第一个达到的预告优惠金额
         *
         * @param item
         * @param promotionGood
         * @return
         */
        PromotionGoodsCalculateResult calculateOncomingDiscountPrice(CalculateSinglePromotionItem item, Promotion.PromotionDetail.PromotionGoods promotionGood);


        void fillRuleDetail(DiscountRulePromotionGoodsCalculator.PromotionGoodsCalculateResult.HitRuleDetail hitRuleDetailResult, Promotion.PromotionDetail.BaseRuleDetail hitRuleDetail);


        /**
         * 过滤出有效的满N规则
         *
         * @param item
         * @param enoughNRuleDetails 所有规则
         * @return
         */
        static <T extends Promotion.PromotionDetail.EnoughNRuleDetail> T filterHitEnoughNRuleDetail(CalculateSinglePromotionItem item, List<T> enoughNRuleDetails) {
            /**
             * 拆零并且不是中药，不走这个匹配规则
             */
            if (item.getUseDismounting() == 1 && !GoodsConst.isChineseMedicine(item.getProductType(), item.getProductSubType())) {
                return null;
            }

            if (CollectionUtils.isEmpty(enoughNRuleDetails)) {
                return null;
            }

            //命中的满N件规则
            return enoughNRuleDetails
                    .stream()
                    .sorted(Comparator.comparing(T::getLevel).reversed())
                    .filter(enoughNRuleDetail -> enoughNRuleDetail.getThresholdCount() != null)
                    .filter(enoughNRuleDetail -> MathUtils.wrapBigDecimalCompare(item.getCanDiscountTotalCount(), enoughNRuleDetail.getThresholdCount()) >= 0)
                    .findFirst()
                    .orElse(null);
        }

        static <T extends Promotion.PromotionDetail.EnoughNRuleDetail> T nextEnoughNRuleDetail(T hitEnoughNRuleDetail, List<T> enoughNRuleDetails) {
            if (CollectionUtils.isEmpty(enoughNRuleDetails)) {
                return null;
            }

            if (Objects.isNull(hitEnoughNRuleDetail)) {
                return enoughNRuleDetails
                        .stream()
                        .min(Comparator.comparing(T::getLevel))
                        .orElse(null);
            }

            //下一个阶梯规则
            return enoughNRuleDetails
                    .stream()
                    .sorted(Comparator.comparing(T::getLevel).reversed())
                    .filter(enoughNRuleDetail -> enoughNRuleDetail.getLevel() > hitEnoughNRuleDetail.getLevel())
                    .findFirst()
                    .orElse(null);
        }

        static BigDecimal filterLeftSaleCount(BigDecimal itemTotalCount, Promotion.PromotionDetail.PromotionGoods promotionGoods) {

            if (promotionGoods == null || promotionGoods.getLeftSaleCount() == null) {
                return itemTotalCount;
            }

            return MathUtils.min(itemTotalCount, promotionGoods.getLeftSaleCount());
        }

        /**
         * 过滤出有效的第N件规则
         */
        static Promotion.PromotionDetail.TheNRuleDetail filterHitTheNRuleDetail(CalculateSinglePromotionItem item, Promotion.PromotionDetail.PromotionGoods promotionGoods) {
            // 拆零并且不是中药
            if (item.getUseDismounting() == 1  && !GoodsConst.isChineseMedicine(item.getProductType(), item.getProductSubType())) {
                return null;
            }

            Promotion.PromotionDetail.TheNRuleDetail theNRuleDetail = promotionGoods.getTheNRuleDetail();

            if (Objects.isNull(theNRuleDetail) || theNRuleDetail.getThresholdCount() == null) {
                return null;
            }

            //判断是否满足条件
            if (MathUtils.wrapBigDecimalCompare(item.getCanDiscountTotalCount(), theNRuleDetail.getThresholdCount()) < 0) {
                return null;
            }

            return theNRuleDetail;
        }
    }

    @Data
    @Accessors(chain = true)
    class PromotionGoodsCalculateResult {

        private int isHit;

        /**
         * 优惠金额
         */
        private BigDecimal discountPrice = BigDecimal.ZERO;

        /**
         * 参与优惠的数量
         */
        private BigDecimal participationDiscountCount;

        /**
         * 赠送的goods
         */
        private List<PromotionGiftGoodsResult> giftGoodsList;


//        private String ruleName;

        /**
         * 规则明细
         */
        private HitRuleDetail hitRuleDetail;

        public BigDecimal calculateDisplayDiscountPrice() {

            if (CollectionUtils.isEmpty(giftGoodsList)) {
                return discountPrice;
            }

            return giftGoodsList.stream()
                    .map(PromotionGiftGoodsResult::getDiscountPrice)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        @Data
        public static class HitRuleDetail {
            @ApiModelProperty("折扣值：特价时为特价值，折扣时为折扣值")
            private BigDecimal discount;

            @ApiModelProperty("折扣类型 1:类型; 2:商品")
            private int type;

            @ApiModelProperty("0折扣率 1特价 2买赠")
            private int discountWay;

            @ApiModelProperty("0固定优惠 1满n件优惠 2第n件优惠（折扣）")
            private int ruleType;

            @ApiModelProperty("满N优惠详情")
            @JsonInclude(JsonInclude.Include.NON_NULL)
            private Promotion.PromotionDetail.EnoughNRuleDetail enoughNRuleDetails;

            @ApiModelProperty("第N优惠详情")
            @JsonInclude(JsonInclude.Include.NON_NULL)
            private Promotion.PromotionDetail.TheNRuleDetail theNRuleDetail;
            @JsonInclude(JsonInclude.Include.NON_NULL)

            @ApiModelProperty("买赠优惠详情")
            private ChargeGiftBySingleGoodsRuleDetail giftBySingleGoodsRuleDetails;

            @ApiModelProperty("商品类型名称，结合type使用")
            private String goodsTypeName;

            @EqualsAndHashCode(callSuper = true)
            @Data
            public static class ChargeGiftBySingleGoodsRuleDetail extends Promotion.PromotionDetail.GiftBySingleGoodsRuleDetail {
                private String giftGoodsName;
                private String giftGoodsUnit;
            }
        }
    }

    @Data
    @Accessors(chain = true)
    class PromotionGiftGoodsResult {

        /**
         * 赠送商品id
         */
        private String goodsId;

        /**
         * 赠送的数量
         */
        private BigDecimal count;

        /**
         * 参与赠送原始item的数量
         */
        private BigDecimal originalItemCount;

        /**
         * 是否拆零
         */
        private int isDismounting;

        private Promotion.PromotionDetail.GiftBySingleGoodsRuleDetail hitGiftBySingleGoodsRuleDetail;

        /**
         * 赠送的优惠金额
         */
        private BigDecimal discountPrice;

        @JsonIgnore
        private GoodsItem goodsItem;
    }

    /**
     * 计算优惠金额
     *
     * @param item
     * @param promotionGoods
     * @return
     */
    PromotionGoodsCalculateResult calculateDiscountPrice(CalculateSinglePromotionItem item,
                                                         String promotionId,
                                                         Promotion.PromotionDetail.PromotionGoods promotionGoods,
                                                         Map<String, GoodsItem> singlePromotionGiftGoodsItemMap);

    /**
     * 计算第一个达到的预告优惠金额
     *
     * @param item
     * @param promotionGood
     * @return
     */
    PromotionGoodsCalculateResult calculateOncomingDiscountPrice(CalculateSinglePromotionItem item, Promotion.PromotionDetail.PromotionGoods promotionGood);


}
