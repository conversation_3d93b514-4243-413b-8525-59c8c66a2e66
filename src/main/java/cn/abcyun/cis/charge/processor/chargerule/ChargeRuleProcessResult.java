package cn.abcyun.cis.charge.processor.chargerule;

import cn.abcyun.cis.charge.api.model.ChargeRuleProcessUsageView;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ChargeRuleProcessResult {

    private List<ChargeRuleProcessRsp> chargeRuleProcesses;

    @Data
    public static class ChargeRuleProcessRsp{

        private ChargeRuleProcessUsageView processUsage;

        private List<String> chargeSheetProcessInfoIds = new ArrayList<>();

        private int subType;

        private int type;

    }

}
