package cn.abcyun.cis.charge.processor.discount;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsItem;
import cn.abcyun.bis.rpc.sdk.cis.model.promotion.Promotion;
import cn.abcyun.cis.charge.util.MathUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;

/**
 * 单品优惠特价算费工具
 */
public class DiscountRulePromotionGoodsFixedPriceCalculator extends DiscountRulePromotionGoodsAbstractCalculator {

    private final static Map<Integer, RuleTypeCalculator> RULE_TYPE_CALCULATOR_MAP = new HashMap<Integer, RuleTypeCalculator>() {{
        put(Promotion.PromotionDetail.RuleType.FIXED, new FixedRuleTypeCalculator());
        put(Promotion.PromotionDetail.RuleType.ENOUGH_N, new EnoughNRuleTypeCalculator());
        put(Promotion.PromotionDetail.RuleType.THE_N, new TheNRuleTypeCalculator());
    }};

    @Override
    public Map<Integer, RuleTypeCalculator> getRuleTypeCalculatorMap() {
        return RULE_TYPE_CALCULATOR_MAP;
    }

    /**
     * 固定特价
     */
    public static class FixedRuleTypeCalculator extends BaseRuleTypeCalculatorAbstract {

        @Override
        public PromotionGoodsCalculateResult calculateDiscountPrice(CalculateSinglePromotionItem item,
                                                                    String promotionId,
                                                                    Promotion.PromotionDetail.PromotionGoods promotionGoods,
                                                                    Map<String, GoodsItem> singlePromotionGiftGoodsItemMap) {

            //如果是拆零，并且不是中药，就不应用规则
            if (item.getUseDismounting() == 1 && !GoodsConst.isChineseMedicine(item.getProductType(), item.getProductSubType())) {
                return null;
            }

            BigDecimal fixedPrice = promotionGoods.getFixedPrice();

            if (fixedPrice == null || fixedPrice.compareTo(BigDecimal.ZERO) < 0) {
                return null;
            }

            if (fixedPrice.compareTo(item.getUnitPrice()) >= 0) {
                return null;
            }

            BigDecimal needFixedTotalCount = RuleTypeCalculator.filterLeftSaleCount(item.getCanDiscountTotalCount(), promotionGoods);

            BigDecimal fixedTotalPrice = MathUtils.calculateTotalPrice(fixedPrice, needFixedTotalCount, 2);

            //保护一下特价值异常的情况
            fixedTotalPrice = fixedTotalPrice.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : fixedTotalPrice;

            //如果特价值大于等于了总金额，不需要应用特价
            if (fixedTotalPrice.compareTo(item.getCanDiscountTotalPrice()) >= 0) {
                return null;
            }

            //计算对应特价数量的原价
            BigDecimal fixedSourceTotalPrice = MathUtils.calculateTotalPrice(item.getUnitPrice(), needFixedTotalCount).setScale(2, RoundingMode.DOWN);

            if (MathUtils.wrapBigDecimalCompare(needFixedTotalCount, item.getCanDiscountTotalCount()) == 0) {
                //全是特价，直接用totalPrice
                fixedSourceTotalPrice = item.getCanDiscountTotalPrice();
            }


            BigDecimal discountPrice = fixedTotalPrice.subtract(fixedSourceTotalPrice);
//            String ruleName = String.format("每件%s元", fixedPrice.stripTrailingZeros().toPlainString());
            return new PromotionGoodsCalculateResult()
                    .setIsHit(1)
                    .setParticipationDiscountCount(needFixedTotalCount)
                    .setDiscountPrice(discountPrice)
                    .setHitRuleDetail(getHitRuleDetail(promotionGoods, new Promotion.PromotionDetail.BaseRuleDetail()));
        }

        @Override
        public PromotionGoodsCalculateResult calculateOncomingDiscountPrice(CalculateSinglePromotionItem item, Promotion.PromotionDetail.PromotionGoods promotionGood) {
            //todo
            return null;
        }

        @Override
        public void fillRuleDetail(PromotionGoodsCalculateResult.HitRuleDetail hitRuleDetailResult, Promotion.PromotionDetail.BaseRuleDetail hitRuleDetail) {

        }

    }

    /**
     * 满N件特价
     */
    public static class EnoughNRuleTypeCalculator extends BaseRuleTypeCalculatorAbstract {

        @Override
        public PromotionGoodsCalculateResult calculateDiscountPrice(CalculateSinglePromotionItem item,
                                                                    String promotionId,
                                                                    Promotion.PromotionDetail.PromotionGoods promotionGoods,
                                                                    Map<String, GoodsItem> singlePromotionGiftGoodsItemMap) {
            Promotion.PromotionDetail.EnoughNRuleDetail hitEnoughNRuleDetail = RuleTypeCalculator.filterHitEnoughNRuleDetail(item, promotionGoods.getEnoughNRuleDetails());

            //如果没有匹配中规则，直接返回null
            if (hitEnoughNRuleDetail == null) {
                return null;
            }


            //判断是否循环
            boolean isCycle = promotionGoods.getEnoughNRuleDetails().size() == 1 && hitEnoughNRuleDetail.getIsCycle() == 1;

            //处理限购
            BigDecimal maxTotalCount = RuleTypeCalculator.filterLeftSaleCount(item.getCanDiscountTotalCount(), promotionGoods);

            //计算需要循环的次数
            BigDecimal cycleCount = DiscountRuleProcessor.calculateCycleCount(isCycle, maxTotalCount, hitEnoughNRuleDetail.getThresholdCount());

            //计算特价总数量
            BigDecimal needFixedTotalCount = hitEnoughNRuleDetail.getThresholdCount().multiply(cycleCount).setScale(0, RoundingMode.DOWN);

            //特价总金额
            BigDecimal fixedTotalPrice = hitEnoughNRuleDetail.getDiscountValue().multiply(cycleCount).setScale(2, RoundingMode.DOWN);

            //计算对应特价数量的原价
            BigDecimal fixedSourceTotalPrice = MathUtils.calculateTotalPrice(item.getUnitPrice(), needFixedTotalCount).setScale(2, RoundingMode.DOWN);

            if (MathUtils.wrapBigDecimalCompare(needFixedTotalCount, item.getCanDiscountTotalCount()) == 0) {
                //全是特价，直接用totalPrice
                fixedSourceTotalPrice = item.getCanDiscountTotalPrice();
            }

            if (fixedTotalPrice.compareTo(fixedSourceTotalPrice) >= 0) {
                return null;
            }

            //满足条件，计算优惠金额
            BigDecimal discountPrice = fixedTotalPrice.subtract(fixedSourceTotalPrice);

//            String ruleName = "";
//            if (isCycle) {
//                // 每N件X元
//                ruleName = String.format("每%s件%s元", hitEnoughNRuleDetail.getThresholdCount().stripTrailingZeros().toPlainString(), hitEnoughNRuleDetail.getDiscountValue().stripTrailingZeros().toPlainString());
//            } else {
//                // N件X元
//                ruleName = String.format("%s件%s元", hitEnoughNRuleDetail.getThresholdCount().stripTrailingZeros().toPlainString(), hitEnoughNRuleDetail.getDiscountValue().stripTrailingZeros().toPlainString());
//            }

            return new PromotionGoodsCalculateResult()
                    .setIsHit(1)
                    .setParticipationDiscountCount(needFixedTotalCount)
                    .setDiscountPrice(discountPrice)
                    .setHitRuleDetail(getHitRuleDetail(promotionGoods, hitEnoughNRuleDetail));
        }

        @Override
        public PromotionGoodsCalculateResult calculateOncomingDiscountPrice(CalculateSinglePromotionItem item, Promotion.PromotionDetail.PromotionGoods promotionGood) {
            //todo
            return null;
        }

        @Override
        public void fillRuleDetail(PromotionGoodsCalculateResult.HitRuleDetail hitRuleDetailResult, Promotion.PromotionDetail.BaseRuleDetail hitRuleDetail) {
            hitRuleDetailResult.setEnoughNRuleDetails((Promotion.PromotionDetail.EnoughNRuleDetail) hitRuleDetail);
        }

    }

    /**
     * 第N件特价
     */
    public static class TheNRuleTypeCalculator extends BaseRuleTypeCalculatorAbstract {

        @Override
        public PromotionGoodsCalculateResult calculateDiscountPrice(CalculateSinglePromotionItem item,
                                                                    String promotionId,
                                                                    Promotion.PromotionDetail.PromotionGoods promotionGoods,
                                                                    Map<String, GoodsItem> singlePromotionGiftGoodsItemMap) {
            Promotion.PromotionDetail.TheNRuleDetail theNRuleDetail = RuleTypeCalculator.filterHitTheNRuleDetail(item, promotionGoods);

            if (theNRuleDetail == null) {
                return null;
            }

            if (MathUtils.wrapBigDecimalCompare(item.getUnitPrice(), theNRuleDetail.getDiscountValue()) < 0) {
                return null;
            }

            BigDecimal maxTotalCount = RuleTypeCalculator.filterLeftSaleCount(item.getCanDiscountTotalCount(), promotionGoods);

            boolean isCycle = theNRuleDetail.getIsCycle() == 1;
            //计算需要特价的数量，循环累计时，累加打折的数量
            BigDecimal cycleCount = DiscountRuleProcessor.calculateCycleCount(isCycle, maxTotalCount, theNRuleDetail.getThresholdCount());

            //计算特价的总金额
            BigDecimal fixedTotalPrice = MathUtils.calculateTotalPrice(theNRuleDetail.getDiscountValue(), cycleCount).setScale(2, RoundingMode.DOWN);
            //计算特价数量的原总金额
            BigDecimal fixedSourceTotalPrice = MathUtils.calculateTotalPrice(item.getUnitPrice(), cycleCount).setScale(2, RoundingMode.DOWN);

            if (MathUtils.wrapBigDecimalCompare(item.getCanDiscountTotalCount(), cycleCount) == 0) {
                fixedSourceTotalPrice = item.getCanDiscountTotalPrice();
            }

            if (fixedTotalPrice.compareTo(fixedSourceTotalPrice) >= 0) {
                return null;
            }

            //满足条件，计算优惠金额
            BigDecimal discountPrice = fixedTotalPrice.subtract(fixedSourceTotalPrice);

//            // 计算规则名称
//            String ruleName = "";
//            if (isCycle) {
//                // 第2件9.9元
//                ruleName = String.format("第%s件%s元", theNRuleDetail.getThresholdCount().stripTrailingZeros().toPlainString(), theNRuleDetail.getDiscountValue().stripTrailingZeros().toPlainString());
//
//            } else {
//                // 仅第2件9.9元
//                ruleName = String.format("仅第%s件%s元", theNRuleDetail.getThresholdCount().stripTrailingZeros().toPlainString(), theNRuleDetail.getDiscountValue().stripTrailingZeros().toPlainString());
//            }

            return new PromotionGoodsCalculateResult()
                    .setIsHit(1)
                    .setParticipationDiscountCount(cycleCount)
                    .setDiscountPrice(discountPrice)
                    .setHitRuleDetail(getHitRuleDetail(promotionGoods, theNRuleDetail));
        }

        @Override
        public PromotionGoodsCalculateResult calculateOncomingDiscountPrice(CalculateSinglePromotionItem item, Promotion.PromotionDetail.PromotionGoods promotionGood) {
            //todo
            return null;
        }

        @Override
        public void fillRuleDetail(PromotionGoodsCalculateResult.HitRuleDetail hitRuleDetailResult, Promotion.PromotionDetail.BaseRuleDetail hitRuleDetail) {
            hitRuleDetailResult.setTheNRuleDetail((Promotion.PromotionDetail.TheNRuleDetail) hitRuleDetail);
        }
    }
}
