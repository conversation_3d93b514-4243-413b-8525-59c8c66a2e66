package cn.abcyun.cis.charge.processor.discount;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsTypeTuple;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class CalculatePromotionAirPharmacyForm {

    private String id;
    private String name;
    private int goodsType;
    private int goodsSubType;
    private String goodsCMSpec;

    private BigDecimal totalPrice;

    private BigDecimal discountPrice;

    private List<PromotionSimple> promotions;

    /**
     * {@link cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst.PharmacyType}
     * 药房类型：1：虚拟药房，2：空中药房
     */
    private int pharmacyType;

    private boolean isOriginalPrice;

    public boolean canApplyOriginalPricePromotion() {
        return isOriginalPrice && (getDiscountPrice() == null || getDiscountPrice().compareTo(BigDecimal.ZERO) == 0);
    }

    public BigDecimal getCanDiscountTotalPrice () {
        return getTotalPrice();
    }


    public static String convertToKey(GoodsTypeTuple goodsTypeTuple) {
        return convertToKey(goodsTypeTuple.getGoodsType(), goodsTypeTuple.getGoodsSubType(), goodsTypeTuple.getGoodsCMSpec(), goodsTypeTuple.getPharmacyType());
    }

    public static String convertToKey(CalculatePromotionAirPharmacyForm airPharmacyForm) {
        return convertToKey(airPharmacyForm.getGoodsType(), airPharmacyForm.getGoodsSubType(), airPharmacyForm.getGoodsCMSpec(), GoodsConst.PharmacyType.AIR_PHARMACY);
    }

    public static String convertToKey(int goodsType, int goodsSubType, String goodsCMSpec, int pharmacyType) {
        return String.format("%s-%s-%s-%s", goodsType, goodsSubType, goodsCMSpec, pharmacyType);
    }
}
