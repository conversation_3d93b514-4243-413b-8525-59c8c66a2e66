package cn.abcyun.cis.charge.processor.discount;

import cn.abcyun.bis.rpc.sdk.cis.model.promotion.GoodsBaseInfo;
import cn.abcyun.cis.commons.util.MathUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class CalculatePromotionItem extends BasicCalculatePromotionItem {

    @JsonIgnore
    private GoodsBaseInfo goodsBaseInfo;

    /**
     * 卡项已抵扣的金额，负值
     */
    private BigDecimal deductedTotalPrice;

    /**
     * 卡项已抵扣的数量
     */
    private int deductedTotalCount;

    /**
     * 积分已抵扣数量
     */
    private int pointDeductedTotalCount;

    /**
     * 积分已抵扣的金额，负值
     */
    private BigDecimal pointDeductedTotalPrice;

    /**
     *  核销抵扣数量
     */
    private int verifyDeductedCount;

    /**
     * 核销抵扣金额，负值
     */
    private BigDecimal verifyDeductedTotalPrice;

    /**
     * 卡项抵扣的收费项明细
     */
    private List<ItemDeductedDetail> itemDeductedDetails;

    /**
     * 积分抵扣的收费项明细
     */
    private List<ItemDeductedDetail> pointItemDeductedDetails;


    private boolean isOriginalPrice = true;

    public boolean canApplyOriginalPricePromotion() {
        return isOriginalPrice && (getDiscountPrice() == null || getDiscountPrice().compareTo(BigDecimal.ZERO) == 0);
    }

    public void addItemDeductedDetail(ItemDeductedDetail itemDeductedDetail) {

        if (itemDeductedDetails == null) {
            itemDeductedDetails = new ArrayList<>();
        }

        if (itemDeductedDetail != null) {
            itemDeductedDetails.add(itemDeductedDetail);
        }
    }

    public void addPointItemDeductedDetail(ItemDeductedDetail itemDeductedDetail) {

        if (pointItemDeductedDetails == null) {
            pointItemDeductedDetails = new ArrayList<>();
        }

        if (itemDeductedDetail != null) {
            pointItemDeductedDetails.add(itemDeductedDetail);
        }
    }


    public BigDecimal getPromotionPrice() {
        return MathUtils.wrapBigDecimalAdd(getDiscountPrice(), getAllDeductedTotalPrice());
    }

    @Override
    public BigDecimal getCanDiscountTotalPrice() {
        return MathUtils.wrapBigDecimalAdd(getTotalPrice(), getAllDeductedTotalPrice());
    }

    @Override
    public BigDecimal getExistedDiscountPrice() {
        return MathUtils.wrapBigDecimalOrZero(getDiscountPrice());
    }

    public boolean canDeducted() {
        return MathUtils.wrapBigDecimalCompare(getTotalCount(), getAllDeductedTotalCount()) > 0;
    }

    public BigDecimal getCanDiscountTotalCount () {
        return MathUtils.wrapBigDecimalSubtract(getTotalCount(), getAllDeductedTotalCount());
    }

    /**
     * 包含卡项抵扣数量和积分抵扣数量
     *
     * @return
     */
    public BigDecimal getAllDeductedTotalCount() {
        return BigDecimal.valueOf(deductedTotalCount + pointDeductedTotalCount + verifyDeductedCount);
    }

    /**
     * 包含卡项抵扣金额和积分抵扣金额
     *
     * @return
     */
    public BigDecimal getAllDeductedTotalPrice() {
        return MathUtils.wrapBigDecimalAdd(deductedTotalPrice, pointDeductedTotalPrice, verifyDeductedTotalPrice);
    }
}
