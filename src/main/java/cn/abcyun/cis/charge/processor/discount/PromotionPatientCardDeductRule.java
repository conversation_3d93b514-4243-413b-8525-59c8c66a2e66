package cn.abcyun.cis.charge.processor.discount;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.promotion.GoodsBaseInfo;
import cn.abcyun.bis.rpc.sdk.cis.model.promotion.Promotion;
import cn.abcyun.cis.charge.api.model.PatientCardPromotionView;
import cn.abcyun.cis.charge.api.model.PatientCardView;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.processor.*;
import cn.abcyun.cis.charge.service.dto.PromotionDeductItem;
import cn.abcyun.cis.charge.util.ListUtils;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.cis.commons.util.TextUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class PromotionPatientCardDeductRule {

    private Promotion promotion;
    private Promotion.PromotionDetail promotionDetail;
    private Promotion.PromotionDetail.PatientCard patientCard;
    /**
     * 这个string的结构：pharmacyType-goodsId
     */
    private List<String> canPaidGoodsList = new ArrayList<>();
    private List<String> canPaidGoodsTypeTupleKeyList = new ArrayList<>();

    public String getPromotionId() {
        return Optional.ofNullable(promotion).map(Promotion::getId).orElse("");
    }

    /**
     * 可以抵扣的goods列表
     */
    private Map<String, Promotion.PromotionDetail.PromotionCardPatientPresent> presentGoodsMap = new HashMap<>();


    public PromotionPatientCardDeductRule(Promotion promotion) {
        this.promotion = promotion;
        promotionDetail = promotion.getDetail();

        if (promotionDetail != null) {
            patientCard = promotionDetail.getPatientCard();
        }

        Optional.ofNullable(patientCard).ifPresent(p -> {
            canPaidGoodsList = Optional.ofNullable(p.getCanPaidGoodsItemList()).orElse(new ArrayList<>())
                    .stream()
                    .map(goodsBaseInfo -> CalculatePromotionItem.convertPharmacyTypeGoodsIdKey(goodsBaseInfo.getPharmacyType(), goodsBaseInfo.getGoodsId()))
                    .collect(Collectors.toList());
            canPaidGoodsTypeTupleKeyList = Optional.ofNullable(p.getCanPaidGoodsTypeTupleList()).orElse(new ArrayList<>()).stream()
                    .map(CalculatePromotionAirPharmacyForm::convertToKey)
                    .collect(Collectors.toList());

            presentGoodsMap = Optional.ofNullable(p.getPresents()).orElse(new ArrayList<>())
                    .stream()
                    .collect(Collectors.toMap(Promotion.PromotionDetail.PromotionCardPatientPresent::getGoodsId, Function.identity(), (a, b) -> a));
        });

    }

    /**
     * 选择最好的抵扣方式
     *
     * @param items
     * @param unselectedGoodsIds
     * @param usedDeductGoodsIdMap
     */
    public void applyBestDeduct(List<CalculatePromotionItem> items, Set<String> unselectedGoodsIds, Map<String, AtomicInteger> usedDeductGoodsIdMap) {

        if (CollectionUtils.isEmpty(items) || MapUtils.isEmpty(presentGoodsMap)) {
            return;
        }

        items.stream()
                .filter(CalculatePromotionItem::canDeducted)
                .filter(item -> MathUtils.wrapBigDecimalCompare(item.getTotalCount(), BigDecimal.ZERO) > 0)
                //抵扣只对本地药房生效
                .filter(item -> item.getPharmacyType() == GoodsConst.PharmacyType.LOCAL_PHARMACY)
                //抵扣只对剂量为1的数据进行抵扣
                .filter(item -> MathUtils.wrapBigDecimalCompare(BigDecimal.ONE, item.getDoseCount()) == 0)
                .filter(item -> !unselectedGoodsIds.contains(item.getProductId()))
                .filter(item -> presentGoodsMap.containsKey(item.getProductId()))
                .forEach(item -> {

                    //西药，中西成药，商品，物资分类下的医用材料，这几类只能在不拆零时使用抵扣
                    if ((item.getProductType() == Constants.ProductType.MEDICINE && (item.getProductSubType() == Constants.ProductType.SubType.MEDICINE_WESTERN || item.getProductSubType() == Constants.ProductType.SubType.MEDICINE_CHINESE_COMPOSE))
                            || item.getProductType() == Constants.ProductType.SALE_PRODUCT
                            || (item.getProductType() == Constants.ProductType.MATERIAL && item.getProductSubType() == Constants.ProductType.SubType.MEDICAL_MATERIAL)

                    ) {
                        if (item.getUseDismounting() == 1) {
                            return;
                        }
                    }

                    Promotion.PromotionDetail.PromotionCardPatientPresent present = presentGoodsMap.get(item.getProductId());
                    //剩余可抵扣次数
                    int leftCanDeductedCount = item.getCanDiscountTotalCount().intValue();
                    Integer availableDeductCount = null;
                    if (leftCanDeductedCount <= 0) {
                        return;
                    }

                    //计算可抵扣的次数
                    if (present.getIsGivingLimit() == 1) {
                        int totalCount = present.getTotalCount() == null ? 0 : present.getTotalCount();
                        availableDeductCount = totalCount - present.getUsedCount() - usedDeductGoodsIdMap.getOrDefault(present.getGoodsId(), new AtomicInteger(0)).get();
                        leftCanDeductedCount = Math.min(leftCanDeductedCount, availableDeductCount);
                    }

                    if (leftCanDeductedCount <= 0) {
                        return;
                    }

                    AtomicInteger usedCount = usedDeductGoodsIdMap.computeIfAbsent(present.getGoodsId(), key -> new AtomicInteger(0));
                    usedCount.getAndAdd(leftCanDeductedCount);

                    updateDeductedTotalCount(item, leftCanDeductedCount, availableDeductCount, present);
                });
    }

    private void updateDeductedTotalCount(CalculatePromotionItem item, int currentDeductedCount, Integer availableDeductCount, Promotion.PromotionDetail.PromotionCardPatientPresent present) {
        item.setDeductedTotalCount(item.getDeductedTotalCount() + currentDeductedCount);

        //如果有批次，抵扣需要落地到批次上
        DeductedPriceAndDeductedBatchInfoDto deductedPriceAndDeductedBatchInfoDto = CalculateDeductUtils.calculateDeductedPriceAndDeductedBatchInfos(item, new BigDecimal(currentDeductedCount), CalculateDeductUtils.DeductType.PATIENT_CARD);
        BigDecimal deductedPrice = deductedPriceAndDeductedBatchInfoDto.getDeductedPrice();
        List<ItemDeductedDetail.BatchInfoDeductedDetail> deductedBatchInfos = deductedPriceAndDeductedBatchInfoDto.getDeductedBatchInfos();


        item.addItemDeductedDetail(new ItemDeductedDetail()
                .setId(item.getId())
                .setPromotionId(promotion.getId())
                .setPromotionName(promotion.getName())
                .setPresentId(present.getId())
                .setDeductedCount(currentDeductedCount)
                .setAvailableDeductCount(availableDeductCount)
                .setDeductedPrice(deductedPrice)
                .setBatchInfos(deductedBatchInfos)
        );
        item.setDeductedTotalPrice(MathUtils.wrapBigDecimalAdd(item.getDeductedTotalPrice(), deductedPrice));
        item.addPromotion(new PromotionSimple()
                .setPromotionId(promotion.getId())
                .setPromotionName(promotion.getName())
                .setDeductedCount(currentDeductedCount)
                .setAvailableDeductTotalCount(availableDeductCount)
                .setPresentId(present.getId())
                .setDiscountPrice(deductedPrice)
                .setType(promotion.getType())
                .setSubType(PromotionSimple.SubType.DISCOUNT_FOR_DEDUCT)
                .setBatchInfos(deductedBatchInfos)
        );
    }


    /**
     * 转换成卡项抵扣view
     *
     * @param items
     * @param unselectedGoodsIds
     * @return
     */
    public PatientCardPromotionView generatePromotionView(List<CalculatePromotionItem> items, Set<String> unselectedGoodsIds, Map<String, Set<String>> allDeductedGoodsIdPresentIdsMap) {
        if (patientCard == null) {
            return null;
        }

        if (unselectedGoodsIds == null) {
            unselectedGoodsIds = new HashSet<>();
        }

        PatientCardPromotionView promotionView = new PatientCardPromotionView();
        promotionView.setId(promotion.getId());
        promotionView.setName(promotion.getName());
        promotionView.setType(promotion.getType());
        promotionView.setPatientId(patientCard.getPatientId());
        promotionView.setPatientName(patientCard.getPatientName());
        promotionView.setPatientMobile(patientCard.getPatientMobile());
        promotionView.setIsOutOfUseRangeCard(patientCard.getIsOutOfUseRangeCard());
        promotionView.setChecked(true);

        promotionView.setDeductItems(generateDeductItems(items, unselectedGoodsIds, allDeductedGoodsIdPresentIdsMap));
        BigDecimal totalDeductPrice = promotionView.getDeductItems().stream()
                .map(promotionDeductItem -> MathUtils.wrapBigDecimalOrZero(promotionDeductItem.getDeductPrice()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        promotionView.setTotalDeductPrice(totalDeductPrice);
        return promotionView;
    }

    private List<PromotionDeductItem> generateDeductItems(List<CalculatePromotionItem> items, Set<String> unselectedGoodsIds, Map<String, Set<String>> allDeductedGoodsIdPresentIdsMap) {

        Map<String, GoodsBaseInfoWithGoodsName> goodsBaseInfoMap = items.stream()
                .filter(item -> item.getGoodsBaseInfo() != null)
                .map(item -> {
                    GoodsBaseInfoWithGoodsName goodsBaseInfoWithGoodsName = new GoodsBaseInfoWithGoodsName();
                    BeanUtils.copyProperties(item.getGoodsBaseInfo(), goodsBaseInfoWithGoodsName);
                    goodsBaseInfoWithGoodsName.setDisplayName(item.getDisplayName());
                    return goodsBaseInfoWithGoodsName;
                })
                .collect(Collectors.toMap(GoodsBaseInfo::getGoodsId, Function.identity(), (a, b) -> a));

        Map<String, List<ItemDeductedDetail>> presentIdItemDeductedDetailMap = items.stream()
                .filter(item -> item.getItemDeductedDetails() != null)
                .flatMap(item -> item.getItemDeductedDetails().stream())
                .filter(itemDeductedDetail -> Objects.equals(promotion.getId(), itemDeductedDetail.getPromotionId()))
                .collect(Collectors.groupingBy(ItemDeductedDetail::getPresentId));


        Map<String, PromotionSimple> presentMap = items.stream()
                .filter(item -> CollectionUtils.isNotEmpty(item.getPromotions()))
                .flatMap(item -> item.getPromotions().stream())
                .filter(promotionSimple -> promotionSimple.getType() == Promotion.Type.CARD && StringUtils.isNotEmpty(promotionSimple.getPresentId()))
                .collect(Collectors.toMap(PromotionSimple::getPresentId, promotionSimple -> {
                    PromotionSimple promotionSimpleView = new PromotionSimple();
                    BeanUtils.copyProperties(promotionSimple, promotionSimpleView);
                    return promotionSimpleView;
                }, (a, b) -> {
                    a.setDiscountPrice(MathUtils.wrapBigDecimalAdd(a.getDiscountPrice(), b.getDiscountPrice()));
                    a.setDeductedCount(a.getDeductedCount() + b.getDeductedCount());
                    return a;
                }));


        return presentGoodsMap.values()
                .stream()
                .map(present -> generatePromotionDeductItem(present, presentMap, presentIdItemDeductedDetailMap, unselectedGoodsIds))
                .filter(Objects::nonNull)
                .peek(deductItem -> updatePromotionDeductItem(deductItem, goodsBaseInfoMap, allDeductedGoodsIdPresentIdsMap))
                .collect(Collectors.toList());
    }

    private static void updatePromotionDeductItem(PromotionDeductItem deductItem, Map<String, GoodsBaseInfoWithGoodsName> goodsBaseInfoMap, Map<String, Set<String>> allDeductedGoodsIdPresentIds) {
        //设置goods不可抵扣属性
        Set<String> allDeductedGoodsIds = allDeductedGoodsIdPresentIds.keySet();

        Set<String> presentIds = allDeductedGoodsIdPresentIds.getOrDefault(deductItem.getGoodsId(), new HashSet<>());
        if (deductItem.getCheckedCount() == 0 && CollectionUtils.isEmpty(deductItem.getItemDeductedDetails()) && allDeductedGoodsIds.contains(deductItem.getGoodsId()) && !presentIds.contains(deductItem.getId())) {
            deductItem.setIsCanDeduct(0);
            deductItem.setChecked(false);
        }

        //如果是无限次，则没有剩余次数的概念
        if (deductItem.getIsGivingLimit() == 0) {
            deductItem.setAvailableDeductTotalCount(null)
                    .setLeftCount(null);
        }

        //绑定goods基础信息
        Optional.ofNullable(goodsBaseInfoMap.getOrDefault(deductItem.getGoodsId(), null))
                .ifPresent(goodsBaseInfo -> deductItem
                        .setGoodsName(goodsBaseInfo.getDisplayName())
                        .setGoodsType(goodsBaseInfo.getGoodsType())
                        .setGoodsSubType(goodsBaseInfo.getGoodsSubType())
                        .setGoodsCMSpec(goodsBaseInfo.getGoodsCMSpec()));
    }

    private static PromotionDeductItem generatePromotionDeductItem(Promotion.PromotionDetail.PromotionCardPatientPresent present, Map<String, PromotionSimple> presentMap, Map<String, List<ItemDeductedDetail>> presentIdItemDeductedDetailMap, Set<String> unselectedGoodsIds) {
        PromotionDeductItem deductItem = new PromotionDeductItem();

        if (unselectedGoodsIds.contains(present.getGoodsId())) {
            deductItem.setId(present.getId())
                    .setGoodsId(present.getGoodsId())
                    .setChecked(false)
                    .setIsCanDeduct(1)
                    .setDeductPrice(BigDecimal.ZERO)
                    .setAvailableDeductTotalCount(present.getTotalCount() != null ? present.getTotalCount() - present.getUsedCount() : null)
                    .setLeftCount(deductItem.getAvailableDeductTotalCount() != null ? deductItem.getAvailableDeductTotalCount() - deductItem.getCheckedCount() : null)
                    .setIsGivingLimit(present.getIsGivingLimit());

            return deductItem;
        }

        PromotionSimple promotionSimple = presentMap.get(present.getId());

        if (Objects.isNull(promotionSimple)) {
            return null;
        }

        List<ItemDeductedDetail> itemDeductedDetails = presentIdItemDeductedDetailMap.getOrDefault(present.getId(), new ArrayList<>());

        deductItem.setId(present.getId())
                .setGoodsId(present.getGoodsId())
                .setChecked(true)
                .setIsCanDeduct(1)
                .setDeductPrice(promotionSimple.getDiscountPrice())
                .setAvailableDeductTotalCount(promotionSimple.getAvailableDeductTotalCount())
                .setCheckedCount(promotionSimple.getDeductedCount())
                .setLeftCount(deductItem.getAvailableDeductTotalCount() != null ? deductItem.getAvailableDeductTotalCount() - deductItem.getCheckedCount() : null)
                .setIsGivingLimit(present.getIsGivingLimit())
                .setItemDeductedDetails(itemDeductedDetails);

        return deductItem;
    }

    public PatientCardView generatePatientCardView(List<ItemProcessor> itemProcessors, List<FormProcessor> airPharmacyFormProcessors) {

        if (patientCard == null) {
            return null;
        }

        itemProcessors = itemProcessors != null ? itemProcessors : new ArrayList<>();
        airPharmacyFormProcessors = airPharmacyFormProcessors != null ? airPharmacyFormProcessors : new ArrayList<>();

        //没有可支付的收费项
        if (CollectionUtils.isEmpty(itemProcessors) && CollectionUtils.isEmpty(airPharmacyFormProcessors)) {
            PatientCardView patientCardView = new PatientCardView();
            patientCardView.setId(promotion.getId())
                    .setName(promotion.getName())
                    .setCardBalance(patientCard.getCardBalance())
                    .setAvailableBalance(BigDecimal.ZERO)
                    .setPrincipal(patientCard.getPrincipal())
                    .setPresent(patientCard.getPresent())
                    .setEndDate(patientCard.getEndDate());
            return patientCardView;
        }

        List<ItemProcessor> canPaidItemProcessors = itemProcessors.stream()
                .filter(itemProcessor -> canPaidGoodsList.contains(CalculatePromotionItem.convertPharmacyTypeGoodsIdKey(itemProcessor.getPharmacyType(), itemProcessor.getProductId())))
                .collect(Collectors.toList());

        List<FormProcessor> canPaidAirPharmacyFormProcessors = airPharmacyFormProcessors.stream()
                .filter(formProcessor -> {
                    GoodsBaseInfo goodsBaseInfo = formProcessor.generateGoodsBaseInfo();
                    if (goodsBaseInfo == null) {
                        return false;
                    }
                    return canPaidGoodsTypeTupleKeyList.contains(CalculatePromotionAirPharmacyForm.convertToKey(goodsBaseInfo.getGoodsType(), goodsBaseInfo.getGoodsSubType(), goodsBaseInfo.getGoodsCMSpec(), goodsBaseInfo.getPharmacyType()));
                })
                .collect(Collectors.toList());

        return generatePatientCardView(patientCard.getCardBalance(),
                promotion.getId(),
                promotion.getName(),
                patientCard.getPatientId(),
                patientCard.getPatientName(),
                patientCard.getPatientMobile(),
                patientCard.getPrincipal(),
                patientCard.getPresent(),
                patientCard.getEndDate(),
                canPaidItemProcessors,
                canPaidAirPharmacyFormProcessors);
    }

    public PatientCardView generatePharmacyPatientCardView(List<PharmacyItemProcessor> itemProcessors, List<PharmacyFormProcessor> airPharmacyFormProcessors) {

        if (patientCard == null) {
            return null;
        }

        itemProcessors = itemProcessors != null ? itemProcessors : new ArrayList<>();
        airPharmacyFormProcessors = airPharmacyFormProcessors != null ? airPharmacyFormProcessors : new ArrayList<>();

        //没有可支付的收费项
        if (CollectionUtils.isEmpty(itemProcessors) && CollectionUtils.isEmpty(airPharmacyFormProcessors)) {
            PatientCardView patientCardView = new PatientCardView();
            patientCardView.setId(promotion.getId())
                    .setName(promotion.getName())
                    .setCardBalance(patientCard.getCardBalance())
                    .setAvailableBalance(BigDecimal.ZERO)
                    .setPrincipal(patientCard.getPrincipal())
                    .setPresent(patientCard.getPresent())
                    .setEndDate(patientCard.getEndDate());
            return patientCardView;
        }

        List<PharmacyItemProcessor> canPaidItemProcessors = itemProcessors.stream()
                .filter(itemProcessor -> canPaidGoodsList.contains(CalculatePromotionItem.convertPharmacyTypeGoodsIdKey(itemProcessor.getPharmacyType(), itemProcessor.getProductId())))
                .collect(Collectors.toList());

        List<PharmacyFormProcessor> canPaidAirPharmacyFormProcessors = airPharmacyFormProcessors.stream()
                .filter(formProcessor -> {
                    GoodsBaseInfo goodsBaseInfo = formProcessor.generateGoodsBaseInfo();
                    if (goodsBaseInfo == null) {
                        return false;
                    }
                    return canPaidGoodsTypeTupleKeyList.contains(CalculatePromotionAirPharmacyForm.convertToKey(goodsBaseInfo.getGoodsType(), goodsBaseInfo.getGoodsSubType(), goodsBaseInfo.getGoodsCMSpec(), goodsBaseInfo.getPharmacyType()));
                })
                .collect(Collectors.toList());

        return generatePharmacyPatientCardView(patientCard.getCardBalance(),
                promotion.getId(),
                promotion.getName(),
                patientCard.getPatientId(),
                patientCard.getPatientName(),
                patientCard.getPatientMobile(),
                patientCard.getPrincipal(),
                patientCard.getPresent(),
                patientCard.getEndDate(),
                canPaidItemProcessors,
                canPaidAirPharmacyFormProcessors);
    }

    public static PatientCardView generatePatientCardView(BigDecimal cardBalance,
                                                          String cardId,
                                                          String name,
                                                          String patientId,
                                                          String patientName,
                                                          String patientMobile,
                                                          BigDecimal principal,
                                                          BigDecimal present,
                                                          Instant endDate,
                                                          List<ItemProcessor> canPaidItemProcessors,
                                                          List<FormProcessor> canPaidAirPharmacyFormProcessors) {
        PatientCardView patientCardView = new PatientCardView();

        List<PatientCardView.PromotionCardGoodsItem> cardGoodsItems = generatePromotionCardGoodsItems(canPaidItemProcessors, canPaidAirPharmacyFormProcessors);

        BigDecimal canPaidTotalPrice = cardGoodsItems.stream().map(PatientCardView.PromotionCardGoodsItem::getTotalPrice)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (MathUtils.wrapBigDecimalCompare(canPaidTotalPrice, cardBalance) > 0) {
            //收费项的金额高于了卡的余额，说明卡支付不够，需要将收费项的金额进行摊费
            List<FlatReceivedPriceHelper.FlatCell> flatPriceCells = cardGoodsItems.stream()
                    .filter(cardGoodsItem -> cardGoodsItem.getTotalPrice() != null)
                    .map(cardGoodsItem -> {
                        FlatReceivedPriceHelper.FlatCell cell = new FlatReceivedPriceHelper.FlatCell();
                        cell.setId(cardGoodsItem.getId());
                        cell.setName(cardGoodsItem.getGoodsName());
                        cell.setTotalPrice(cardGoodsItem.getTotalPrice());
                        cell.setMaxFlatPrice(cardGoodsItem.getTotalPrice());
                        return cell;
                    }).collect(Collectors.toList());
            FlatReceivedPriceHelper flatPriceHelper = new FlatReceivedPriceHelper(cardBalance);
            flatPriceHelper.flat(flatPriceCells);

            Map<String, FlatReceivedPriceHelper.FlatCell> flatPriceCellMap = ListUtils.toMap(flatPriceCells, FlatReceivedPriceHelper.FlatCell::getId);

            cardGoodsItems.forEach(cardGoodsItem -> Optional.ofNullable(flatPriceCellMap.getOrDefault(cardGoodsItem.getId(), null))
                    .ifPresent(flatPriceCell -> cardGoodsItem.setReceivablePrice(flatPriceCell.getFlatPrice())));
        }

        canPaidTotalPrice = MathUtils.min(canPaidTotalPrice, cardBalance);


        patientCardView.setId(cardId)
                .setName(name)
                .setPatientId(patientId)
                .setPatientName(patientName)
                .setPatientMobile(patientMobile)
                .setCardBalance(cardBalance)
                .setAvailableBalance(canPaidTotalPrice)
                .setPrincipal(principal)
                .setPresent(present)
                .setEndDate(endDate)
                .setProductItems(cardGoodsItems);

        return patientCardView;
    }

    public static PatientCardView generatePharmacyPatientCardView(BigDecimal cardBalance,
                                                                  String cardId,
                                                                  String name,
                                                                  String patientId,
                                                                  String patientName,
                                                                  String patientMobile,
                                                                  BigDecimal principal,
                                                                  BigDecimal present,
                                                                  Instant endDate,
                                                                  List<PharmacyItemProcessor> canPaidItemProcessors,
                                                                  List<PharmacyFormProcessor> canPaidAirPharmacyFormProcessors) {
        PatientCardView patientCardView = new PatientCardView();

        List<PatientCardView.PromotionCardGoodsItem> cardGoodsItems = generatePharmacyPromotionCardGoodsItems(canPaidItemProcessors, canPaidAirPharmacyFormProcessors);

        BigDecimal canPaidTotalPrice = cardGoodsItems.stream().map(PatientCardView.PromotionCardGoodsItem::getTotalPrice)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (MathUtils.wrapBigDecimalCompare(canPaidTotalPrice, cardBalance) > 0) {
            //收费项的金额高于了卡的余额，说明卡支付不够，需要将收费项的金额进行摊费
            List<FlatReceivedPriceHelper.FlatCell> flatPriceCells = cardGoodsItems.stream()
                    .filter(cardGoodsItem -> cardGoodsItem.getTotalPrice() != null)
                    .map(cardGoodsItem -> {
                        FlatReceivedPriceHelper.FlatCell cell = new FlatReceivedPriceHelper.FlatCell();
                        cell.setId(cardGoodsItem.getId());
                        cell.setName(cardGoodsItem.getGoodsName());
                        cell.setTotalPrice(cardGoodsItem.getTotalPrice());
                        cell.setMaxFlatPrice(cardGoodsItem.getTotalPrice());
                        return cell;
                    }).collect(Collectors.toList());
            FlatReceivedPriceHelper flatPriceHelper = new FlatReceivedPriceHelper(cardBalance);
            flatPriceHelper.flat(flatPriceCells);

            Map<String, FlatReceivedPriceHelper.FlatCell> flatPriceCellMap = ListUtils.toMap(flatPriceCells, FlatReceivedPriceHelper.FlatCell::getId);

            cardGoodsItems.forEach(cardGoodsItem -> Optional.ofNullable(flatPriceCellMap.getOrDefault(cardGoodsItem.getId(), null))
                    .ifPresent(flatPriceCell -> cardGoodsItem.setReceivablePrice(flatPriceCell.getFlatPrice())));
        }

        canPaidTotalPrice = MathUtils.min(canPaidTotalPrice, cardBalance);


        patientCardView.setId(cardId)
                .setName(name)
                .setPatientId(patientId)
                .setPatientName(patientName)
                .setPatientMobile(patientMobile)
                .setCardBalance(cardBalance)
                .setAvailableBalance(canPaidTotalPrice)
                .setPrincipal(principal)
                .setPresent(present)
                .setEndDate(endDate)
                .setProductItems(cardGoodsItems);

        return patientCardView;
    }

    public static List<PatientCardView.PromotionCardGoodsItem> generatePromotionCardGoodsItems(List<ItemProcessor> canPaidItemProcessors, List<FormProcessor> canPaidAirPharmacyFormProcessors) {
        List<PatientCardView.PromotionCardGoodsItem> cardGoodsItems = new ArrayList<>();
        if (CollectionUtils.isEmpty(canPaidItemProcessors) && CollectionUtils.isEmpty(canPaidAirPharmacyFormProcessors)) {
            return cardGoodsItems;
        }

        cardGoodsItems.addAll(canPaidItemProcessors.stream()
                .map(itemProcessor -> new PatientCardView.PromotionCardGoodsItem()
                        .setId(itemProcessor.getItemId())
                        .setGoodsId(itemProcessor.getProductId())
                        .setGoodsName(getGoodsDisplayName(itemProcessor.getProductType(), itemProcessor.getName()))
                        .setTotalPrice(itemProcessor.getReceivableFee())
                        .setReceivablePrice(itemProcessor.getReceivableFee())
                        .setPharmacyType(itemProcessor.getPharmacyType())
                ).collect(Collectors.toList())
        );

        cardGoodsItems.addAll(canPaidAirPharmacyFormProcessors.stream()
                .map(formProcessor -> new PatientCardView.PromotionCardGoodsItem()
                        .setId(formProcessor.getFormId())
                        .setGoodsName(String.format("空中药房订单：%s", formProcessor.getChargeForm().getVendorName()))
                        .setTotalPrice(formProcessor.getReceivableFee())
                        .setReceivablePrice(formProcessor.getReceivableFee())
                        .setPharmacyType(GoodsConst.PharmacyType.AIR_PHARMACY)
                ).collect(Collectors.toList())
        );

        return cardGoodsItems;
    }

    public static List<PatientCardView.PromotionCardGoodsItem> generatePharmacyPromotionCardGoodsItems(List<PharmacyItemProcessor> canPaidItemProcessors, List<PharmacyFormProcessor> canPaidAirPharmacyFormProcessors) {
        List<PatientCardView.PromotionCardGoodsItem> cardGoodsItems = new ArrayList<>();
        if (CollectionUtils.isEmpty(canPaidItemProcessors) && CollectionUtils.isEmpty(canPaidAirPharmacyFormProcessors)) {
            return cardGoodsItems;
        }

        cardGoodsItems.addAll(canPaidItemProcessors.stream()
                .map(itemProcessor -> new PatientCardView.PromotionCardGoodsItem()
                        .setId(itemProcessor.getItemId())
                        .setGoodsId(itemProcessor.getProductId())
                        .setGoodsName(getGoodsDisplayName(itemProcessor.getProductType(), itemProcessor.getName()))
                        .setTotalPrice(itemProcessor.getReceivableFee())
                        .setReceivablePrice(itemProcessor.getReceivableFee())
                        .setPharmacyType(itemProcessor.getPharmacyType())
                ).collect(Collectors.toList())
        );

        cardGoodsItems.addAll(canPaidAirPharmacyFormProcessors.stream()
                .map(formProcessor -> new PatientCardView.PromotionCardGoodsItem()
                        .setId(formProcessor.getFormId())
                        .setGoodsName(String.format("空中药房订单：%s", formProcessor.getChargeForm().getVendorName()))
                        .setTotalPrice(formProcessor.getReceivableFee())
                        .setReceivablePrice(formProcessor.getReceivableFee())
                        .setPharmacyType(GoodsConst.PharmacyType.AIR_PHARMACY)
                ).collect(Collectors.toList())
        );

        return cardGoodsItems;
    }

    private static String getGoodsDisplayName(int productType, String name) {
        return productType == Constants.ProductType.REGISTRATION && !TextUtils.alwaysString(name).contains(Constants.SystemProductId.REGISTRATION_NAME) ? String.format("%s-%s", Constants.SystemProductId.REGISTRATION_NAME, TextUtils.alwaysString(name)) : name;
    }

    @Data
    @Accessors(chain = true)
    @EqualsAndHashCode(callSuper = true)
    public static class GoodsBaseInfoWithGoodsName extends GoodsBaseInfo {
        private String displayName;
    }
}
