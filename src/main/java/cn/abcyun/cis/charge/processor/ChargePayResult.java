package cn.abcyun.cis.charge.processor;

import cn.abcyun.cis.charge.model.ThirdPartyPayInfo;
import cn.abcyun.cis.charge.util.MathUtils;
import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;

@Data
public class ChargePayResult {
    private int payStatus;
    private String thirdPayTaskId;
    private BigDecimal receivedPrincipal = BigDecimal.ZERO; //收到的本金
    private BigDecimal receivedPresent = BigDecimal.ZERO; //收到的赠金
    private ThirdPartyPayInfo payInfo;
    private String chargePayTransactionId;
    private int payMode;
    private int paySubMode;
    private Long lockPatientOrderExpireTime;
    private Instant expireTime;

    //收到的总费用
    public BigDecimal getReceivedFee() {
        return MathUtils.wrapBigDecimalAdd(receivedPresent, receivedPrincipal);
    }
}
