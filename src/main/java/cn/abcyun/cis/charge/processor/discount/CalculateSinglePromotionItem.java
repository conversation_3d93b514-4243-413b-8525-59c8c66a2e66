package cn.abcyun.cis.charge.processor.discount;

import cn.abcyun.bis.rpc.sdk.cis.model.promotion.GoodsBaseInfo;
import cn.abcyun.cis.charge.service.dto.SinglePromotionView;
import cn.abcyun.cis.commons.util.MathUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.http.util.Asserts;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class CalculateSinglePromotionItem extends BasicCalculatePromotionItem {

    /**
     * 单项议价的值
     */
    private BigDecimal unitAdjustmentFee;

    private List<SinglePromotionView> singlePromotionViews = new ArrayList<>();

    @JsonIgnore
    private GoodsBaseInfo goodsBaseInfo;

    /**
     * 卡项已抵扣的金额，负值
     */
    private BigDecimal deductedTotalPrice;

    /**
     * 卡项已抵扣的数量
     */
    private int deductedTotalCount;

    /**
     * 积分已抵扣数量
     */
    private int pointDeductedTotalCount;

    /**
     * 积分已抵扣的金额，负值
     */
    private BigDecimal pointDeductedTotalPrice;

    /**
     * 卡项抵扣的收费项明细
     */
    private List<ItemDeductedDetail> itemDeductedDetails;

    /**
     * 积分抵扣的收费项明细
     */
    private List<ItemDeductedDetail> pointItemDeductedDetails;

    private boolean isOriginalPrice = true;

    public boolean canApplyOriginalPricePromotion() {
        return isOriginalPrice
                && MathUtils.wrapBigDecimalCompare(getDiscountPrice(), BigDecimal.ZERO) == 0
                && MathUtils.wrapBigDecimalCompare(getUnitAdjustmentFee(), BigDecimal.ZERO) == 0;
    }

    public void addItemDeductedDetail(ItemDeductedDetail itemDeductedDetail) {

        if (itemDeductedDetails == null) {
            itemDeductedDetails = new ArrayList<>();
        }

        if (itemDeductedDetail != null) {
            itemDeductedDetails.add(itemDeductedDetail);
        }
    }

    public void addPointItemDeductedDetail(ItemDeductedDetail itemDeductedDetail) {

        if (pointItemDeductedDetails == null) {
            pointItemDeductedDetails = new ArrayList<>();
        }

        if (itemDeductedDetail != null) {
            pointItemDeductedDetails.add(itemDeductedDetail);
        }
    }

    public BigDecimal getPromotionPrice() {
        return MathUtils.wrapBigDecimalAdd(getDiscountPrice(), getAllDeductedTotalPrice());
    }

    public BigDecimal getCanDiscountTotalPrice() {
        return MathUtils.wrapBigDecimalAdd(getTotalPrice(), getAllDeductedTotalPrice());
    }

    @Override
    public BigDecimal getExistedDiscountPrice() {
        return MathUtils.wrapBigDecimalAdd(getDiscountPrice(), getUnitAdjustmentFee());
    }

    public boolean canDeducted() {
        return MathUtils.wrapBigDecimalCompare(getTotalCount(), getAllDeductedTotalCount()) > 0;
    }

    public BigDecimal getCanDiscountTotalCount () {
        return MathUtils.wrapBigDecimalSubtract(getTotalCount(), getAllDeductedTotalCount());
    }

    /**
     * 包含卡项抵扣数量和积分抵扣数量
     *
     * @return
     */
    public BigDecimal getAllDeductedTotalCount() {
        return BigDecimal.valueOf(deductedTotalCount + pointDeductedTotalCount);
    }
    /**
     * 包含卡项抵扣金额和积分抵扣金额
     *
     * @return
     */
    public BigDecimal getAllDeductedTotalPrice() {
        return MathUtils.wrapBigDecimalAdd(deductedTotalPrice, pointDeductedTotalPrice);
    }

    public void bindCheckedPromotion(SinglePromotionView checkedSinglePromotion) {
        if (checkedSinglePromotion == null) {
            return;
        }

        addPromotionAndDiscountPrice(PromotionSimple.ofPromotionSimpleFromSinglePromotionView(checkedSinglePromotion));
    }

    public void addPromotionAndDiscountPrice(PromotionSimple promotionSimple) {
        Asserts.notNull(promotionSimple, "promotionSimple is null");
        addDiscountPrice(promotionSimple.getDiscountPrice());
        addPromotion(promotionSimple);
    }

    public SinglePromotionView filterGiftGoodsCheckedPromotionView() {
        return getSinglePromotionViews()
                .stream()
                .filter(singlePromotionView -> singlePromotionView.getChecked()
                        && org.apache.commons.collections.CollectionUtils.isNotEmpty(singlePromotionView.getGiftGoodsList()))
                .findFirst()
                .orElse(null);
    }
}
