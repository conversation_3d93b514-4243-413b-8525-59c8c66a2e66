package cn.abcyun.cis.charge.processor.stat.calculator;

import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.cis.charge.helper.ChargeTransactionRecordDiscountInfoHelper;
import cn.abcyun.cis.charge.model.ChargeTransaction;
import cn.abcyun.cis.charge.processor.limitprice.BaseLimitPriceInfo;
import cn.abcyun.cis.charge.processor.stat.StatRecordBatchInfoCalculateCell;
import cn.abcyun.cis.charge.processor.stat.StatRecordByChooseCalculateCell;
import cn.abcyun.cis.charge.processor.stat.StatRecordByChooseCalculateCellV0;
import cn.abcyun.cis.charge.service.dto.ChargeDiscountInfo;
import cn.abcyun.cis.charge.util.JsonUtils;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.common.log.marker.AbcLogMarker;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
public class StatRecordPartedPaidCalculator extends StatRecordAbstractCalculator implements IStatRecordBatchInfoCalculator{

    @Override
    public BigDecimal flatCell(ChargeTransaction chargeTransaction, List<StatRecordByChooseCalculateCell> cells, BigDecimal toRecordPrice, int refundFlatType) {
        BigDecimal leftToRecordPrice = toRecordPrice;

        //本次受影响的收费项的折后总金额
        BigDecimal discountedPriceSum = cells.stream().map(StatRecordByChooseCalculateCell::getDiscountedPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        //本次已经记录了record表的折后总金额
        BigDecimal recordDiscountedPriceSum = cells.stream().map(StatRecordByChooseCalculateCell::getRecordedDiscountedPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        //剩余需要记录到record表的折后总金额
        BigDecimal leftToRecordDiscountedPriceSum = discountedPriceSum.subtract(recordDiscountedPriceSum);

        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "discountedPriceSum: {}, recordDiscountedPriceSum: {}, leftToRecordDiscountedPriceSum: {}", discountedPriceSum, recordDiscountedPriceSum, leftToRecordDiscountedPriceSum);

        //这是最后一发
        if (MathUtils.wrapBigDecimalCompare(leftToRecordDiscountedPriceSum, toRecordPrice) <= 0) {
            for (StatRecordByChooseCalculateCell cell : cells) {
                cell.setToRecordDiscountedPrice(cell.getLeftToRecordDiscountedPrice());
                cell.setToRecordTotalCostPrice(cell.getTotalCostPrice().subtract(cell.getRecordedTotalCostPrice()));
                cell.setToRecordDiscountPrice(cell.getDiscountPrice().subtract(cell.getRecordedDiscountPrice()));
                cell.setToRecordPromotionDiscountInfo(ChargeTransactionRecordDiscountInfoHelper.generateToRecordChargeTransactionRecordDiscountInfoForLastRecord(cell.getItemPromotionInfo(), cell.getRecordedChargeTransactionRecordDiscountInfo()));
            }
            return toRecordPrice.subtract(leftToRecordDiscountedPriceSum);
        }

        for (StatRecordByChooseCalculateCell cell : cells) {
            //收费的时候已经摊好了，直接用，退费由于涉及到部分退，欠退，情况很复杂，就在这儿做摊费逻辑，最后再把摊好的值回写到从每个收费项的receivedPrice中减去
            BigDecimal thisTimeReceivableFee = cell.getThisTimeReceivableFee();
            cell.setToRecordDiscountedPrice(thisTimeReceivableFee);
            leftToRecordPrice = leftToRecordPrice.subtract(thisTimeReceivableFee);
        }
        IStatRecordCalculator.fillOtherToRecordFields(cells);
        return leftToRecordPrice;
    }

    @Override
    public void flatCellBatchInfo(List<StatRecordByChooseCalculateCell> cells) {
        for (StatRecordByChooseCalculateCell cell : cells) {
            super.doCalculateBatchInfo(cell, this);
        }
    }


    public void flatCellBatchInfoDiscountedPrice(StatRecordByChooseCalculateCell calculateCell) {
        calculateCell.getBatchInfoCalculateCells().forEach(statRecordBatchInfoCalculateCell -> {
            statRecordBatchInfoCalculateCell.setToRecordDiscountedPrice(statRecordBatchInfoCalculateCell.getChargeFormItemBatchInfo().getThisTimeReceivableFee());
        });
    }

//    场景demo
//    public static void main(String[] args) {
//        StatRecordPartedPaidCalculator statRecordPartedPaidCalculator = new StatRecordPartedPaidCalculator();
//        String str = "{\"id\":\"ffffffff0000000034fbd89508b5c01d\",\"chargeVersion\":0,\"historyRecords\":[],\"associateHistoryRecords\":[],\"chargeFormItem\":{\"id\":\"ffffffff0000000034fbd89508b5c01d\",\"clinicId\":\"ffffffff0000000034c9ebb7f62c4003\",\"chainId\":\"ffffffff0000000034c9ebb7f62c4001\",\"patientOrderId\":\"ffffffff0000000034fbd7aee0e44005\",\"chargeSheetId\":\"ffffffff0000000034fbd7aee8b50016\",\"chargeFormId\":\"ffffffff0000000034fbd89508b5c002\",\"sourceFormItemId\":\"ffffffff0000000034fbd894e6890094\",\"status\":1,\"payStatus\":1,\"paySource\":0,\"unit\":\"g\",\"name\":\"全蝎（全虫）\",\"unitCostPrice\":2.9180,\"totalCostPrice\":175.08000000,\"unitCount\":10.000,\"doseCount\":6.000,\"expectedDoseCount\":null,\"unitPrice\":3.6000,\"discountPrice\":0,\"totalPrice\":216.0700,\"expectedUnitPrice\":null,\"sourceUnitPrice\":3.6000,\"sourceTotalPrice\":216.00,\"receivablePrice\":null,\"fractionPrice\":0,\"expectedTotalPrice\":null,\"frontEndUnitPrice\":null,\"frontEndTotalPrice\":null,\"deductTotalCount\":0.0000,\"verifyTotalCount\":0.0000,\"adjustmentPrice\":0.0700,\"totalPriceRatio\":1,\"expectedTotalPriceRatio\":null,\"unitAdjustmentFee\":null,\"promotionPrice\":0,\"receivedPrice\":216.0200,\"doctorSourceUnitPrice\":null,\"doctorSourceTotalPrice\":null,\"v2Status\":1,\"refundUnitCount\":0.000,\"refundDoseCount\":null,\"refundDiscountPrice\":0.0000,\"refundTotalPrice\":0.0000,\"useDismounting\":1,\"productType\":1,\"productSubType\":2,\"isAirPharmacy\":0,\"associateFormItemId\":null,\"productId\":\"ffffffff0000000034ca6ac17e42000b\",\"groupId\":null,\"sort\":13,\"composeType\":0,\"composeParentFormItemId\":null,\"isUseLimitPrice\":1,\"sourceItemType\":0,\"keyId\":null,\"sourceFormItemKeyId\":null,\"usageInfo\":{\"ivgtt\":0,\"checked\":true,\"processBagUnitCountDecimal\":0},\"promotionInfo\":{\"isMarkedByHealthCardPay\":1,\"adjustmentFee\":0.0700,\"limitFee\":-0.0500},\"couponPromotionInfoJson\":null,\"couponPromotionInfo\":null,\"giftRulePromotionInfoJson\":null,\"giftRulePromotionInfo\":null,\"isGift\":0,\"pharmacyType\":0,\"composeParentUnitCount\":null,\"pharmacyNo\":0,\"feeComposeType\":0,\"feeTypeId\":13,\"goodsFeeType\":0,\"additional\":{\"doctorId\":\"ffffffff0000000034ca482b162b4000\",\"departmentId\":\"ffffffff0000000034cac5bc57964000\",\"limitInfo\":{\"type\":2,\"limitDetail\":null,\"limitPrice\":3.60,\"limitTotalPrice\":216.02,\"limitFractionPrice\":0.0200,\"exceedLimitPriceRule\":1,\"selfPayPrice\":-0.0500,\"notSelfPayPrice\":null,\"limitPriceFromWay\":1,\"limitFee\":-0.0500},\"goodsVersion\":1},\"goodsTypeId\":null,\"isExpectedBatch\":0,\"isFixedData\":0,\"chargeFormItemBatchInfos\":[{\"id\":\"3817883409090658378\",\"clinicId\":\"ffffffff0000000034c9ebb7f62c4003\",\"chainId\":\"ffffffff0000000034c9ebb7f62c4001\",\"patientOrderId\":\"ffffffff0000000034fbd7aee0e44005\",\"chargeSheetId\":\"ffffffff0000000034fbd7aee8b50016\",\"chargeFormId\":\"ffffffff0000000034fbd89508b5c002\",\"chargeFormItemId\":\"ffffffff0000000034fbd89508b5c01d\",\"associateItemBatchInfoId\":null,\"unitCostPrice\":3.0000,\"totalCostPrice\":57.00000000,\"totalPrice\":68.40,\"sourceTotalPrice\":68.40,\"sourceUnitPrice\":3.6000,\"unitCount\":19.0000,\"unitPrice\":3.6000,\"refundUnitCount\":0.000,\"refundTotalPrice\":0.0000,\"productId\":\"ffffffff0000000034ca6ac17e42000b\",\"isUseLimitPrice\":0,\"stockId\":null,\"batchId\":\"96641631\",\"batchNo\":\"24120101\",\"receivablePrice\":68.4200,\"expiryDate\":null,\"promotionInfo\":{\"isMarkedByHealthCardPay\":1,\"adjustmentFee\":0.0200},\"limitInfo\":{\"type\":2,\"limitDetail\":{\"minPriceLimit\":0,\"maxPriceLimit\":null,\"limitRate\":25},\"limitPrice\":3.7500,\"limitTotalPrice\":71.25,\"limitFractionPrice\":0,\"exceedLimitPriceRule\":1,\"selfPayPrice\":2.8300,\"notSelfPayPrice\":null,\"limitPriceFromWay\":1,\"limitFee\":2.8300},\"expectedTotalPrice\":null,\"traceableCodes\":null,\"receivedPrice\":68.4200,\"isOld\":0,\"isNotCharged\":0,\"isDeleted\":0,\"createdBy\":\"ffffffff0000000034ca477c762c0000\",\"created\":\"2025-05-08T02:20:12Z\",\"lastModifiedBy\":\"ffffffff0000000034ca477c762c0000\",\"lastModified\":\"2025-05-08T02:21:15.848Z\",\"dispensedRefundUnitCount\":null,\"dispensedUnitCount\":null,\"canRefundCount\":19.0000,\"leftCanRefundCount\":19.0000},{\"id\":\"3817883409090658377\",\"clinicId\":\"ffffffff0000000034c9ebb7f62c4003\",\"chainId\":\"ffffffff0000000034c9ebb7f62c4001\",\"patientOrderId\":\"ffffffff0000000034fbd7aee0e44005\",\"chargeSheetId\":\"ffffffff0000000034fbd7aee8b50016\",\"chargeFormId\":\"ffffffff0000000034fbd89508b5c002\",\"chargeFormItemId\":\"ffffffff0000000034fbd89508b5c01d\",\"associateItemBatchInfoId\":null,\"unitCostPrice\":2.8800,\"totalCostPrice\":118.08000000,\"totalPrice\":147.60,\"sourceTotalPrice\":147.60,\"sourceUnitPrice\":3.6000,\"unitCount\":41.0000,\"unitPrice\":3.6000,\"refundUnitCount\":0.000,\"refundTotalPrice\":0.0000,\"productId\":\"ffffffff0000000034ca6ac17e42000b\",\"isUseLimitPrice\":1,\"stockId\":null,\"batchId\":\"98788751\",\"batchNo\":\"20250302\",\"receivablePrice\":147.65,\"expiryDate\":\"2028-03-11\",\"promotionInfo\":{\"isMarkedByHealthCardPay\":1,\"adjustmentFee\":0.05,\"limitFee\":-0.05},\"limitInfo\":{\"type\":2,\"limitDetail\":{\"minPriceLimit\":0,\"maxPriceLimit\":null,\"limitRate\":25},\"limitPrice\":3.6000,\"limitTotalPrice\":147.60,\"limitFractionPrice\":0,\"exceedLimitPriceRule\":1,\"selfPayPrice\":-0.05,\"notSelfPayPrice\":null,\"limitPriceFromWay\":1,\"limitFee\":-0.05},\"expectedTotalPrice\":null,\"traceableCodes\":null,\"receivedPrice\":147.60,\"isOld\":0,\"isNotCharged\":0,\"isDeleted\":0,\"createdBy\":\"ffffffff0000000034ca477c762c0000\",\"created\":\"2025-05-08T02:20:12Z\",\"lastModifiedBy\":\"ffffffff0000000034ca477c762c0000\",\"lastModified\":\"2025-05-08T02:21:15.848Z\",\"dispensedRefundUnitCount\":null,\"dispensedUnitCount\":null,\"canRefundCount\":41.0000,\"leftCanRefundCount\":41.0000}],\"lockId\":\"3817883243143905359\",\"created\":\"2025-05-08T02:15:04Z\",\"productInfo\":{\"goodsVersion\":1,\"id\":\"ffffffff0000000034ca6ac17e42000b\",\"goodsId\":\"ffffffff0000000034ca6ac17e42000b\",\"status\":1,\"name\":null,\"displayName\":\"全蝎（全虫）\",\"displaySpec\":\"饮片\",\"organId\":\"ffffffff0000000034c9ebb7f62c4001\",\"typeId\":14,\"type\":1,\"subType\":2,\"pieceNum\":1.0,\"pieceUnit\":\"g\",\"packageUnit\":\"\",\"dismounting\":1,\"medicineCadn\":\"全蝎（全虫）\",\"materialSpec\":\"中药饮片\",\"position\":null,\"chainPackagePrice\":3.6,\"chainPiecePrice\":3.6,\"piecePrice\":3.6,\"packagePrice\":3.6,\"packageCostPrice\":2.88,\"minPackagePrice\":3.6,\"maxPackagePrice\":3.6,\"minPackageCostPrice\":2.88,\"maxPackageCostPrice\":3.0,\"fixedPackagePrice\":3.6,\"fixedPiecePrice\":3.6,\"totalSalePrice\":216.0,\"priceType\":1,\"subClinicPriceFlag\":1,\"inTaxRat\":0.0,\"outTaxRat\":0.0,\"pieceCount\":1328.0,\"packageCount\":0.0,\"dispGoodsCount\":\"1328g\",\"stockPieceCount\":1328.0,\"stockPackageCount\":0.0,\"dispStockGoodsCount\":\"1328g\",\"availablePackageCount\":0.0,\"availablePieceCount\":1328.0,\"outPieceCount\":1268.0,\"outPackageCount\":0.0,\"dispOutGoodsCount\":\"1268g\",\"prohibitPieceCount\":0.0,\"prohibitPackageCount\":0.0,\"dispProhibitGoodsCount\":\"0g\",\"lockingPieceCount\":60.0,\"lockingPackageCount\":0.0,\"dispLockingGoodsCount\":\"60g\",\"lastPackageCostPrice\":2.88,\"needExecutive\":0,\"hospitalNeedExecutive\":0,\"shortId\":\"000369\",\"composeUseDismounting\":0,\"composeSort\":0,\"disableComposePrint\":0,\"createdUserId\":\"ffffffff0000000034c9ebb7f62c4000\",\"lastModifiedUserId\":\"ffffffff0000000034c9c12336250000\",\"lastModifiedDate\":\"2024-07-12T09:13:11Z\",\"combineType\":0,\"bizRelevantId\":null,\"extendSpec\":\"\",\"medicalFeeGrade\":1,\"shebaoNationalCode\":\"T001500639\",\"disable\":0,\"chainDisable\":0,\"v2DisableStatus\":0,\"chainV2DisableStatus\":0,\"disableSell\":0,\"isSell\":1,\"customTypeId\":0,\"chainPackageCostPrice\":2.88,\"chainId\":\"ffffffff0000000034c9ebb7f62c4001\",\"shebao\":{\"goodsId\":\"ffffffff0000000034ca6ac17e42000b\",\"goodsType\":1,\"payMode\":0,\"isDummy\":0,\"medicineNum\":\"000369\",\"medicalFeeGrade\":1,\"nationalCode\":\"T001500639\",\"nationalCodeId\":\"24203000005358\",\"shebaoPieceNum\":1,\"shebaoPieceUnit\":\"g\",\"shebaoPackageUnit\":\"g\",\"restriction\":\"3～6g；单独使用时统筹基金不予支付，且全部由这些饮片组成的处方统筹基金也不予支付\"},\"recentAvgSell\":15.3,\"turnoverDays\":83,\"profitRat\":20.0,\"lastStockInId\":*********,\"lastStockInOrderSupplier\":\"湖北清大中药饮片有限公司\",\"pharmacyType\":0,\"pharmacyNo\":0,\"pharmacyName\":\"本地药房\",\"pharmacyGoodsStockList\":[{\"pharmacyName\":\"本地药房\",\"pharmacyNo\":0,\"lastPackageCostPrice\":2.88,\"stockPieceCount\":1328.0,\"stockPackageCount\":0.0,\"availablePackageCount\":0.0,\"availablePieceCount\":1328.0,\"esInorder\":1}],\"defaultInOutTax\":1,\"dispenseAveragePackageCostPrice\":2.90594,\"shebaoPayMode\":0,\"restriction\":\"3～6g；单独使用时统筹基金不予支付，且全部由这些饮片组成的处方统筹基金也不予支付\",\"innerFlag\":0,\"deviceInnerFlag\":1,\"feeComposeType\":0,\"feeTypeId\":\"13\",\"usePieceUnitFlag\":0,\"copiedFlag\":0,\"coopFlag\":0,\"cloudSupplierFlag\":0,\"nationalCode\":\"T001500639\",\"nationalCodeId\":\"24203000005358\",\"minExpiryDate\":\"2028-03-11\",\"expiredWarnMonths\":3,\"dangerIngredient\":0,\"keyId\":\"ffffffff0000000034fbd89508b5c01d\",\"goodsBatchInfoList\":[{\"batchId\":98788751,\"pieceNum\":1,\"pharmacyType\":0,\"pharmacyNo\":0,\"expiryDate\":\"2028-03-11\",\"productionDate\":\"2025-03-12\",\"inDate\":\"2025-04-08T01:26:43Z\",\"supplierId\":\"ffffffff0000000034cfbbd61eb28000\",\"supplierName\":\"永州市永靛中药饮片股份有限公司\",\"batchNo\":\"20250302\",\"packageCostPrice\":2.88,\"packagePrice\":3.6,\"piecePrice\":3.6,\"pieceCount\":41.0,\"packageCount\":0.0,\"dispGoodsCount\":\"41g\",\"stockPieceCount\":41.0,\"stockPackageCount\":0.0,\"dispStockGoodsCount\":\"41g\",\"lockingPieceCount\":0.0,\"lockingPackageCount\":0.0,\"dispLockingGoodsCount\":\"0g\",\"cutTotalPieceCount\":41.0,\"cutPieceCount\":41.0,\"totalSalePrice\":147.6,\"lockInfo\":{\"lockId\":3817883243143905359,\"lockingPieceCount\":41.0,\"lockingPackageCount\":0.0,\"lockLeftTotalPieceCount\":0.0,\"pharmacyType\":0,\"pharmacyNo\":0},\"status\":0,\"shebaoPayLimitPriceFailFlag\":0,\"shebaoPayLimitPriceInfo\":{\"exceedLimitPriceRule\":1,\"limitPackagePrice\":3.6,\"limitPiecePrice\":3.6,\"type\":2,\"limitDetail\":{\"minPriceLimit\":0.0,\"limitRate\":25}}},{\"batchId\":96641631,\"pieceNum\":1,\"pharmacyType\":0,\"pharmacyNo\":0,\"inDate\":\"2025-03-04T02:31:02Z\",\"supplierId\":\"ffffffff0000000034caefe63e47c00e\",\"supplierName\":\"湖北清大医药有限公司\",\"batchNo\":\"24120101\",\"packageCostPrice\":3.0,\"packagePrice\":3.6,\"piecePrice\":3.6,\"pieceCount\":287.0,\"packageCount\":0.0,\"dispGoodsCount\":\"287g\",\"stockPieceCount\":287.0,\"stockPackageCount\":0.0,\"dispStockGoodsCount\":\"287g\",\"lockingPieceCount\":0.0,\"lockingPackageCount\":0.0,\"dispLockingGoodsCount\":\"0g\",\"cutTotalPieceCount\":19.0,\"cutPieceCount\":19.0,\"totalSalePrice\":68.4,\"lockInfo\":{\"lockId\":3817883243143905359,\"lockingPieceCount\":19.0,\"lockingPackageCount\":0.0,\"lockLeftTotalPieceCount\":0.0,\"pharmacyType\":0,\"pharmacyNo\":0},\"status\":0,\"shebaoPayLimitPriceFailFlag\":0,\"shebaoPayLimitPriceInfo\":{\"exceedLimitPriceRule\":1,\"limitPackagePrice\":3.75,\"limitPiecePrice\":3.75,\"type\":2,\"limitDetail\":{\"minPriceLimit\":0.0,\"limitRate\":25}}}],\"lastMonthSellCount\":459.0,\"isPreciousDevice\":0,\"shebaoPayLimitPriceFailFlag\":0,\"shebaoPayLimitPriceInfo\":{\"exceedLimitPriceRule\":1,\"type\":2},\"shebaoPayLimitPriceRule\":{\"limitPriceType\":0,\"typeRule\":{\"priceType\":1,\"exceedLimitPriceRule\":1,\"limitDetail\":[{\"minPriceLimit\":0.0,\"limitRate\":25}]}},\"lockBatchOps\":2,\"cMSpec\":\"中药饮片\"},\"executedUnitCount\":null,\"needExecutive\":0,\"containAdjustmentUnitPrice\":null,\"containAdjustmentTotalPrice\":null,\"containAdjustmentFractionPrice\":null,\"composeChildren\":null,\"version\":3,\"originalChargeFormItemIds\":null,\"canDispensing\":true,\"topItem\":true,\"parentItem\":false,\"childItem\":false,\"expectedPriceItem\":false,\"deductAndVerifyTotalCount\":0.0000},\"deductedItem\":null,\"toRecordDiscountedPrice\":216.0200,\"toRecordDiscountPrice\":0.00,\"toRecordUnitCount\":10.0000,\"toRecordTotalCostPrice\":175.0394,\"toRecordSourceTotalPrice\":0,\"batchInfoCalculateCells\":[{\"id\":\"3817883409090658378\",\"chargeFormItemBatchInfo\":{\"id\":\"3817883409090658378\",\"clinicId\":\"ffffffff0000000034c9ebb7f62c4003\",\"chainId\":\"ffffffff0000000034c9ebb7f62c4001\",\"patientOrderId\":\"ffffffff0000000034fbd7aee0e44005\",\"chargeSheetId\":\"ffffffff0000000034fbd7aee8b50016\",\"chargeFormId\":\"ffffffff0000000034fbd89508b5c002\",\"chargeFormItemId\":\"ffffffff0000000034fbd89508b5c01d\",\"associateItemBatchInfoId\":null,\"unitCostPrice\":3.0000,\"totalCostPrice\":57.00000000,\"totalPrice\":68.40,\"sourceTotalPrice\":68.40,\"sourceUnitPrice\":3.6000,\"unitCount\":19.0000,\"unitPrice\":3.6000,\"refundUnitCount\":0.000,\"refundTotalPrice\":0.0000,\"productId\":\"ffffffff0000000034ca6ac17e42000b\",\"isUseLimitPrice\":0,\"stockId\":null,\"batchId\":\"96641631\",\"batchNo\":\"24120101\",\"receivablePrice\":68.4200,\"expiryDate\":null,\"promotionInfo\":{\"isMarkedByHealthCardPay\":1,\"adjustmentFee\":0.0200},\"limitInfo\":{\"type\":2,\"limitDetail\":{\"minPriceLimit\":0,\"maxPriceLimit\":null,\"limitRate\":25},\"limitPrice\":3.7500,\"limitTotalPrice\":71.25,\"limitFractionPrice\":0,\"exceedLimitPriceRule\":1,\"selfPayPrice\":2.8300,\"notSelfPayPrice\":null,\"limitPriceFromWay\":1,\"limitFee\":2.8300},\"expectedTotalPrice\":null,\"traceableCodes\":null,\"receivedPrice\":68.4200,\"isOld\":0,\"isNotCharged\":0,\"isDeleted\":0,\"createdBy\":\"ffffffff0000000034ca477c762c0000\",\"created\":\"2025-05-08T02:20:12Z\",\"lastModifiedBy\":\"ffffffff0000000034ca477c762c0000\",\"lastModified\":\"2025-05-08T02:21:15.848Z\",\"dispensedRefundUnitCount\":null,\"dispensedUnitCount\":null,\"canRefundCount\":19.0000,\"leftCanRefundCount\":19.0000},\"historyBatchInfos\":[],\"toRecordDiscountedPrice\":68.4200,\"toRecordUnitCount\":19.0000,\"toRecordTotalCostPrice\":0,\"toRecordSourceTotalPrice\":68.40,\"toRecordDeductPrice\":0,\"toRecordDeductedUnitAdjustmentFee\":0,\"toRecordDeductUnitCount\":0,\"toRecordDeductTotalCostPrice\":0,\"toRecordSceneType\":0,\"toRecordPromotionDiscountInfo\":{\"isMarkedByHealthCardPay\":1,\"adjustmentFee\":0.0200},\"totalCostPrice\":57.00000000,\"recordedPromotionPrice\":0,\"toRecordPromotionPrice\":0,\"discountedPrice\":68.4200,\"unitCount\":19.0000,\"promotionPrice\":0,\"promotionInfo\":{\"isMarkedByHealthCardPay\":1,\"adjustmentFee\":0.0200},\"leftToRecordPromotionInfo\":{\"isMarkedByHealthCardPay\":1,\"adjustmentFee\":0.0200},\"leftToRecordSourceTotalPrice\":68.40,\"leftToRecordTotalCostPrice\":57.00000000,\"recordedPromotionInfo\":null,\"recordedSourceTotalPrice\":0,\"leftToRecordDiscountedPrice\":68.4200,\"recordedDiscountedPrice\":0,\"recordedTotalCostPrice\":0},{\"id\":\"3817883409090658377\",\"chargeFormItemBatchInfo\":{\"id\":\"3817883409090658377\",\"clinicId\":\"ffffffff0000000034c9ebb7f62c4003\",\"chainId\":\"ffffffff0000000034c9ebb7f62c4001\",\"patientOrderId\":\"ffffffff0000000034fbd7aee0e44005\",\"chargeSheetId\":\"ffffffff0000000034fbd7aee8b50016\",\"chargeFormId\":\"ffffffff0000000034fbd89508b5c002\",\"chargeFormItemId\":\"ffffffff0000000034fbd89508b5c01d\",\"associateItemBatchInfoId\":null,\"unitCostPrice\":2.8800,\"totalCostPrice\":118.08000000,\"totalPrice\":147.60,\"sourceTotalPrice\":147.60,\"sourceUnitPrice\":3.6000,\"unitCount\":41.0000,\"unitPrice\":3.6000,\"refundUnitCount\":0.000,\"refundTotalPrice\":0.0000,\"productId\":\"ffffffff0000000034ca6ac17e42000b\",\"isUseLimitPrice\":1,\"stockId\":null,\"batchId\":\"98788751\",\"batchNo\":\"20250302\",\"receivablePrice\":147.65,\"expiryDate\":\"2028-03-11\",\"promotionInfo\":{\"isMarkedByHealthCardPay\":1,\"adjustmentFee\":0.05,\"limitFee\":-0.05},\"limitInfo\":{\"type\":2,\"limitDetail\":{\"minPriceLimit\":0,\"maxPriceLimit\":null,\"limitRate\":25},\"limitPrice\":3.6000,\"limitTotalPrice\":147.60,\"limitFractionPrice\":0,\"exceedLimitPriceRule\":1,\"selfPayPrice\":-0.05,\"notSelfPayPrice\":null,\"limitPriceFromWay\":1,\"limitFee\":-0.05},\"expectedTotalPrice\":null,\"traceableCodes\":null,\"receivedPrice\":147.60,\"isOld\":0,\"isNotCharged\":0,\"isDeleted\":0,\"createdBy\":\"ffffffff0000000034ca477c762c0000\",\"created\":\"2025-05-08T02:20:12Z\",\"lastModifiedBy\":\"ffffffff0000000034ca477c762c0000\",\"lastModified\":\"2025-05-08T02:21:15.848Z\",\"dispensedRefundUnitCount\":null,\"dispensedUnitCount\":null,\"canRefundCount\":41.0000,\"leftCanRefundCount\":41.0000},\"historyBatchInfos\":[],\"toRecordDiscountedPrice\":147.60,\"toRecordUnitCount\":41.0000,\"toRecordTotalCostPrice\":0,\"toRecordSourceTotalPrice\":147.55,\"toRecordDeductPrice\":0,\"toRecordDeductedUnitAdjustmentFee\":0,\"toRecordDeductUnitCount\":0,\"toRecordDeductTotalCostPrice\":0,\"toRecordSceneType\":0,\"toRecordPromotionDiscountInfo\":{\"adjustmentFee\":0.05},\"totalCostPrice\":118.08000000,\"recordedPromotionPrice\":0,\"toRecordPromotionPrice\":0.00,\"discountedPrice\":147.65,\"unitCount\":41.0000,\"promotionPrice\":0,\"promotionInfo\":{\"isMarkedByHealthCardPay\":1,\"adjustmentFee\":0.05,\"limitFee\":-0.05},\"leftToRecordPromotionInfo\":{\"isMarkedByHealthCardPay\":1,\"adjustmentFee\":0.05,\"limitFee\":-0.05},\"leftToRecordSourceTotalPrice\":147.60,\"leftToRecordTotalCostPrice\":118.08000000,\"recordedPromotionInfo\":null,\"recordedSourceTotalPrice\":0,\"leftToRecordDiscountedPrice\":147.65,\"recordedDiscountedPrice\":0,\"recordedTotalCostPrice\":0}],\"chargeForm\":null,\"isAirPharmacy\":0,\"toRecordDeductPrice\":0,\"toRecordDeductedUnitAdjustmentFee\":0,\"toRecordVerifyUnitAdjustmentFee\":0,\"toRecordDeductUnitCount\":0,\"toRecordDeductTotalCostPrice\":0,\"deductInfo\":null,\"toRecordSceneType\":0,\"composeChildrenCells\":null,\"composeChildrenItemPaidCells\":null,\"thisTimeRecords\":[],\"toRecordPromotionDiscountInfo\":{\"adjustmentFee\":0.06,\"limitFee\":-0.04},\"toRecordDeductDiscountInfos\":null,\"toRecordVerifyDiscountInfos\":null,\"discountedPrice\":216.0700,\"discountPrice\":0,\"recordedDiscountedPrice\":0,\"totalCostPrice\":175.08000000,\"totalPrice\":216.0700,\"unitCount\":10.0000,\"paidback\":false,\"productType\":1,\"deductTotalCostPrice\":0.00000000,\"thisTimeReceivableFee\":216.0200,\"leftCanRecordPrice\":216.0700,\"recordedTotalPrice\":0,\"allRefunded\":true,\"toRecordDiscountedPriceResult\":216.0200,\"leftToRecordDiscountedPrice\":216.0700,\"itemPromotionInfo\":{\"isMarkedByHealthCardPay\":1,\"adjustmentFee\":0.0700,\"limitFee\":-0.0500},\"recordedTotalCostPrice\":0,\"recordedUnitCount\":0,\"recordedDiscountPrice\":0,\"recordedChargeTransactionRecordDiscountInfo\":null,\"toRecordTotalPriceResult\":216.0200,\"toRecordDiscountPriceResult\":0.00,\"toRecordUnitCountResult\":10.0000,\"toRecordTotalCostPriceResult\":175.0394,\"toRecordSourceTotalPriceResult\":215.9600,\"toRecordDiscountInfoResult\":{\"adjustmentFee\":0.06,\"limitFee\":-0.04}}";
//        StatRecordByChooseCalculateCellV0 calculateCell = JsonUtils.readValue(str, StatRecordByChooseCalculateCellV0.class);
//        statRecordPartedPaidCalculator.flatCellBatchInfoDiscountInfo(calculateCell);
//    }

    /**
     * 平摊discountInfo，这里要有几个维度的校验，首先平摊的数据不能超过批次本身的金额范围，其次要保证每次计算下一个值时，前面计算的和不能小于0
     *
     * @param calculateCell
     */
    public void flatCellBatchInfoDiscountInfo(StatRecordByChooseCalculateCell calculateCell) {

        ChargeDiscountInfo toRecordPromotionDiscountInfo = calculateCell.getToRecordDiscountInfoResult();
        if (Objects.isNull(toRecordPromotionDiscountInfo)) {
            return;
        }

        ChargeDiscountInfo upperLimitPromotionInfo = toRecordPromotionDiscountInfo;
        for (StatRecordBatchInfoCalculateCell batchInfoCalculateCell : calculateCell.getBatchInfoCalculateCells()) {

            ChargeDiscountInfo leftToRecordPromotionInfo = batchInfoCalculateCell.getLeftToRecordPromotionInfo();

            if (batchInfoCalculateCell.isCurrentAllRecord()) {
                batchInfoCalculateCell.setToRecordPromotionDiscountInfo(leftToRecordPromotionInfo);
                batchInfoCalculateCell.setToRecordSourceTotalPrice(batchInfoCalculateCell.getLeftToRecordSourceTotalPrice());
            } else {
                ChargeTransactionRecordDiscountInfoHelper.FlatBatchInfoPromotionInfoResult flatBatchInfoPromotionInfoResult = ChargeTransactionRecordDiscountInfoHelper.flatBatchInfoPromotionInfo(
                        upperLimitPromotionInfo,
                        batchInfoCalculateCell.getPromotionInfo(),
                        leftToRecordPromotionInfo,
                        batchInfoCalculateCell.getToRecordDiscountedPrice(),
                        batchInfoCalculateCell.getDiscountedPrice(),
                        Optional.ofNullable(batchInfoCalculateCell.getChargeFormItemBatchInfo().getLimitInfo()).map(BaseLimitPriceInfo.LimitInfo::getExceedLimitPriceRule).orElse(YesOrNo.YES) == YesOrNo.NO);
                batchInfoCalculateCell.setToRecordPromotionDiscountInfo(flatBatchInfoPromotionInfoResult.getBatchPromotionInfo());
                batchInfoCalculateCell.setToRecordSourceTotalPrice(flatBatchInfoPromotionInfoResult.getSourceTotalPrice());
            }

            //merge upperLimitDiscountInfo
            upperLimitPromotionInfo = ChargeTransactionRecordDiscountInfoHelper.mergeLeftPromotionInfo(upperLimitPromotionInfo, batchInfoCalculateCell.getToRecordPromotionDiscountInfo());
        }

        //如果还有剩余，则从第一个批次把零头放进去
        if (upperLimitPromotionInfo != null && !upperLimitPromotionInfo.isDiscountPriceEmpty()) {

            for (StatRecordBatchInfoCalculateCell batchInfoCalculateCell : calculateCell.getBatchInfoCalculateCells()) {

                if (batchInfoCalculateCell.isCurrentAllRecord()) {
                    continue;
                }

                ChargeDiscountInfo leftToRecordPromotionInfo = batchInfoCalculateCell.getLeftToRecordPromotionInfo();

                BigDecimal fractionPrice = ChargeTransactionRecordDiscountInfoHelper.addFractionPromotionInfo(leftToRecordPromotionInfo, batchInfoCalculateCell.getToRecordPromotionDiscountInfo(), upperLimitPromotionInfo, batchInfoCalculateCell.getToRecordSourceTotalPrice());

                batchInfoCalculateCell.setToRecordSourceTotalPrice(MathUtils.wrapBigDecimalSubtract(batchInfoCalculateCell.getToRecordSourceTotalPrice(), fractionPrice));

                if (upperLimitPromotionInfo.isDiscountPriceEmpty()) {
                    break;
                }
            }
        }

        //校验平摊的金额是否正确
        BigDecimal itemPromotionAllPrice = toRecordPromotionDiscountInfo.calculateAllPrice();
        BigDecimal batchPromotionAllPrice = calculateCell.getBatchInfoCalculateCells()
                .stream()
                .map(StatRecordBatchInfoCalculateCell::getToRecordPromotionDiscountInfo)
                .filter(Objects::nonNull)
                .map(ChargeDiscountInfo::calculateAllPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (MathUtils.wrapBigDecimalCompare(itemPromotionAllPrice, batchPromotionAllPrice) != 0) {
            log.error("批次平摊优惠信息失败, itemPromotionAllPrice: {}, batchPromotionAllPrice: {}, calculateCell: {}, upperLimitPromotionInfo: {}", itemPromotionAllPrice, batchPromotionAllPrice, JsonUtils.dump(calculateCell), JsonUtils.dump(upperLimitPromotionInfo));
            throw new IllegalStateException("批次平摊优惠信息失败");
        }
    }

    public void fillOtherFields(StatRecordByChooseCalculateCell calculateCell) {
        BigDecimal upperLimitTotalPrice = calculateCell.getToRecordTotalCostPrice();
        for (StatRecordBatchInfoCalculateCell batchInfoCalculateCell : calculateCell.getBatchInfoCalculateCells()) {
            BigDecimal discountedPrice = batchInfoCalculateCell.getDiscountedPrice();
            BigDecimal totalCostPrice = batchInfoCalculateCell.getTotalCostPrice();
            BigDecimal leftToRecordTotalCostPrice = batchInfoCalculateCell.getLeftToRecordTotalCostPrice();
            BigDecimal toRecordTotalCostPrice = BigDecimal.ZERO;
            if (discountedPrice.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }

            if (MathUtils.wrapBigDecimalCompare(batchInfoCalculateCell.getLeftToRecordDiscountedPrice(), batchInfoCalculateCell.getToRecordDiscountedPrice()) == 0) {
                batchInfoCalculateCell.setToRecordTotalCostPrice(batchInfoCalculateCell.getLeftToRecordTotalCostPrice());
                continue;
            }

            toRecordTotalCostPrice = MathUtils.wrapBigDecimalOrZero(totalCostPrice).multiply(batchInfoCalculateCell.getToRecordDiscountedPrice()).divide(discountedPrice, 2, RoundingMode.UP);
            toRecordTotalCostPrice = MathUtils.min(toRecordTotalCostPrice, upperLimitTotalPrice);
            toRecordTotalCostPrice = MathUtils.min(toRecordTotalCostPrice, leftToRecordTotalCostPrice);

            batchInfoCalculateCell.setToRecordTotalCostPrice(toRecordTotalCostPrice);
            upperLimitTotalPrice = upperLimitTotalPrice.subtract(toRecordTotalCostPrice);
        }

        BigDecimal batchTotalCostPrice = calculateCell.getBatchInfoCalculateCells()
                .stream()
                .map(StatRecordBatchInfoCalculateCell::getToRecordTotalCostPrice)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        //这里直接用批次的成本价总和覆盖item的成本价
        calculateCell.setToRecordTotalCostPrice(batchTotalCostPrice);

    }
}
