package cn.abcyun.cis.charge.processor;

import cn.abcyun.bis.rpc.sdk.bis.model.order.CalculateLogisticsRsp;
import cn.abcyun.bis.rpc.sdk.bis.model.order.CalculatePriceRsp;
import cn.abcyun.bis.rpc.sdk.bis.model.order.OrderDeliveryRuleView;
import cn.abcyun.bis.rpc.sdk.bis.model.order.OrderProcessRuleView;
import cn.abcyun.bis.rpc.sdk.cis.model.charge.GoodsFeeType;
import cn.abcyun.bis.rpc.sdk.cis.model.dispensing.DispenseConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.*;
import cn.abcyun.bis.rpc.sdk.cis.model.goodslocking.GoodsLockBatchItem;
import cn.abcyun.bis.rpc.sdk.cis.model.goodslocking.GoodsLockingForm;
import cn.abcyun.bis.rpc.sdk.cis.model.goodslocking.GoodsLockingFormItem;
import cn.abcyun.bis.rpc.sdk.cis.model.promotion.GoodsBaseInfo;
import cn.abcyun.bis.rpc.sdk.cis.model.promotion.Promotion;
import cn.abcyun.bis.rpc.sdk.cis.model.shebao.RpcGetShebaoMatchedCodesReq;
import cn.abcyun.cis.charge.api.model.AirPharmacyDeliveryRuleReq;
import cn.abcyun.cis.charge.api.model.AirPharmacyProcessRuleReq;
import cn.abcyun.cis.charge.api.model.ChargeAirPharmacyLogisticsReq;
import cn.abcyun.cis.charge.api.model.ChargeConfigDetailView;
import cn.abcyun.cis.charge.base.*;
import cn.abcyun.cis.charge.factory.ChargeFormItemFactory;
import cn.abcyun.cis.charge.model.*;
import cn.abcyun.cis.charge.processor.discount.CalculatePromotionAirPharmacyForm;
import cn.abcyun.cis.charge.processor.discount.CalculatePromotionItem;
import cn.abcyun.cis.charge.processor.discount.CalculatePromotionItemBatchInfo;
import cn.abcyun.cis.charge.processor.discount.PromotionSimple;
import cn.abcyun.cis.charge.service.dto.*;
import cn.abcyun.cis.charge.service.dto.print.ChargeFormItemPrintView;
import cn.abcyun.cis.charge.service.dto.print.ChargeFormPrintView;
import cn.abcyun.cis.charge.util.*;
import cn.abcyun.cis.commons.exception.CisCustomException;
import cn.abcyun.cis.commons.model.ComposeType;
import cn.abcyun.cis.commons.rpc.outpatient.UsageInfo;
import cn.abcyun.cis.commons.rpc.registration.RegistrationForm;
import cn.abcyun.cis.commons.rpc.registration.RegistrationFormItem;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.common.log.marker.AbcLogMarker;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class FormProcessor {

    private static final Logger sLogger = LoggerFactory.getLogger(FormProcessor.class);

    public static class FlatCellKey {

        public static final String DELIVERY = "delivery";

        public static final String PROCESS = "process";

        public static final String MEDICINE = "medicine";
    }

    private final ChargeForm chargeForm;

    private final ChargeSheetRelationDto toDeleteChargeSheetRelationDto;

    //加工费的formProcessor
    @Setter
    @Getter
    private FormProcessor processFormProcessor;

    private Map<String, ItemProcessor> itemProcessorMap = new HashMap<>();

    private ChargeForm affectedForm;

    private ChargeForm refundChargeForm;

    private ChargeForm addedRefundChargeForm;

    private OrderDeliveryRuleView airPharmacyDeliveryRule;

    private OrderProcessRuleView airPharmacyProcessRule;

    public ChargeForm getAddedRefundChargeForm() {
        return addedRefundChargeForm;
    }

    /**
     * 应收：平摊了议价之后的金额
     */
    private BigDecimal receivableFee;

    /**
     * 虚拟药房的发药状态
     */
    private Integer virtualPharmacyDispensingStatus = 0;


    private List<DispensingFormInfo> dispensingFormInfoList;

    public List<DispensingFormInfo> getDispensingFormInfoList() {
        return dispensingFormInfoList;
    }

    public void bindDispensingFormInfoList(List<DispensingFormInfo> dispensingFormInfoList) {
        this.dispensingFormInfoList = dispensingFormInfoList;
    }

    public BigDecimal getReceivableFee() {
        return receivableFee;
    }

    public boolean isAirPharmacy() {
        return getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY;
    }

    public FormProcessor(ChargeForm chargeForm, ChargeSheetRelationDto toDeleteChargeSheetRelationDto) {
        this.chargeForm = chargeForm;
        this.toDeleteChargeSheetRelationDto = toDeleteChargeSheetRelationDto;
        //在状态的已支付时能确认已收的值，在待收时需要在算费时实时计算，不能在这儿赋值
        if (chargeForm.getStatus() == Constants.ChargeFormStatus.CHARGED) {
            receivableFee = MathUtils.max(BigDecimal.ZERO, MathUtils.wrapBigDecimalSubtract(getDiscountedPrice(), chargeForm.getReceivedPrice()));
        }
    }

    public String getChargeFormId() {
        return chargeForm.getId();
    }

    public OrderProcessRuleView getAirPharmacyProcessRule() {
        return airPharmacyProcessRule;
    }

    public int getPharmacyNo() {
        return chargeForm.getPharmacyNo();
    }

    public int getPharmacyType() {
        return chargeForm.getPharmacyType();
    }

    public Integer getVirtualPharmacyDispensingStatus() {
        return virtualPharmacyDispensingStatus;
    }

    public String getAirPharmacyFormName() {
        return String.format("空中药房订单：%s", chargeForm.getVendorName());
    }

    /**
     * ChargeForm算费类，build主要是给chargeForm以及ChargeFormItem生成对应的processor
     */
    public void build() {
        if (chargeForm.getChargeFormItems() != null) {
            //非退费单 ---生成  ItemProcessor
            Map<String, ItemProcessor> tmpItemProcessorMap = new HashMap<>();
            for (ChargeFormItem chargeFormItem : chargeForm.getChargeFormItems()) {

                if (chargeFormItem.getIsDeleted() == 1) {
                    continue;
                }

                if ((chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.REFUNDED && chargeFormItem.getIsAirPharmacy() == 0)
                        || chargeFormItem.getIsAirPharmacy() == 1
                ) {
                    ItemProcessor itemProcessor = new ItemProcessor(chargeFormItem, chargeForm, toDeleteChargeSheetRelationDto);
                    if (!TextUtils.isEmpty(chargeFormItem.getId())) {
                        tmpItemProcessorMap.put(chargeFormItem.getId(), itemProcessor);
                    } else {
                        tmpItemProcessorMap.put(AbcIdUtils.getUUID(), itemProcessor);
                    }
                }
            }
            //退费单 &&非空中药房 --》查找上一步生成的 关联formItemID ItemProcessor，为这个process设置退款chargeFormItem
            for (ChargeFormItem chargeFormItem : chargeForm.getChargeFormItems()) {

                if (chargeFormItem.getIsDeleted() == 1) {
                    continue;
                }

                if (chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.REFUNDED
                        && chargeFormItem.getIsAirPharmacy() == 0) {
                    if (!TextUtils.isEmpty(chargeFormItem.getAssociateFormItemId())
                            && tmpItemProcessorMap.containsKey(chargeFormItem.getAssociateFormItemId())) {
                        ItemProcessor itemProcessor = tmpItemProcessorMap.get(chargeFormItem.getAssociateFormItemId());
                        itemProcessor.addRefundChargeFormItem(chargeFormItem);
                    }
                }
            }

            //保留非套餐和套餐母项并且费用类型为费用本身或费用母项的ItemProcessor
            itemProcessorMap = tmpItemProcessorMap.values()
                    .stream()
                    .filter(itemProcessor -> (itemProcessor.getComposeType() == ComposeType.NOT_COMPOSE || itemProcessor.getComposeType() == ComposeType.COMPOSE)
                            && (itemProcessor.getGoodsFeeType() == GoodsFeeType.FEE_OWN || itemProcessor.getGoodsFeeType() == GoodsFeeType.FEE_PARENT))
                    .collect(Collectors.toMap(ItemProcessor::getItemId, Function.identity(), (a, b) -> a));


            //将费用子项绑定到费用母项上
            tmpItemProcessorMap.values()
                    .stream()
                    .filter(itemProcessor -> itemProcessor.getGoodsFeeType() == GoodsFeeType.FEE_CHILD)
                    .filter(itemProcessor -> !TextUtils.isEmpty(itemProcessor.getComposeParentFormItemId()))
                    .forEach(itemProcessor -> {
                        ItemProcessor parentProcessor = tmpItemProcessorMap.getOrDefault(itemProcessor.getComposeParentFormItemId(), null);
                        if (parentProcessor != null) {
                            itemProcessor.setComposeParentUnitCount(parentProcessor.getUnitCount());
                            parentProcessor.addComposeChild(itemProcessor);
                        }
                    });

            //将套餐子项绑定单套餐母项上
            tmpItemProcessorMap.values()
                    .stream()
                    .filter(itemProcessor -> itemProcessor.getComposeType() == ComposeType.COMPOSE_SUB_ITEM)
                    .filter(itemProcessor -> !TextUtils.isEmpty(itemProcessor.getComposeParentFormItemId()))
                    .forEach(itemProcessor -> {
                        ItemProcessor parentProcessor = itemProcessorMap.getOrDefault(itemProcessor.getComposeParentFormItemId(), null);
                        if (parentProcessor != null) {//TODO robinsli 如果这里是default，那这个parentProcessor有啥用？
                            itemProcessor.setComposeParentUnitCount(parentProcessor.getUnitCount());
                            parentProcessor.addComposeChild(itemProcessor);
                        }
                    });
        }

        //绑定refundChargeForm
        if (isAirPharmacy() && chargeForm.getStatus() == Constants.ChargeFormStatus.REFUNDED) {
            refundChargeForm = new ChargeForm();
            BeanUtils.copyProperties(chargeForm, refundChargeForm);
        }
    }

    public ChargeForm getRefundChargeForm() {
        if (!isAirPharmacy()) {
            return null;
        }
        return refundChargeForm;
    }

    public String getUsageInfoJson() {
        return chargeForm.getUsageInfoJson();
    }

    public void setUsageInfoJson(String usageInfoJson) {
        chargeForm.setUsageInfoJson(usageInfoJson);
    }

    public void pay(int payStatus, int paySource, String operatorId) {
        if (chargeForm.getStatus() == Constants.ChargeFormStatus.UNCHARGED) {
            chargeForm.setStatus(Constants.ChargeFormStatus.CHARGED);
            FillUtils.fillLastModifiedBy(chargeForm, operatorId);
        }
        itemProcessorMap.values().forEach(itemProcessor -> itemProcessor.pay(payStatus, paySource, operatorId));

        if (isAirPharmacy()) {
            //处理如果空中药房的药品全部已退单，则把快递费和煎药费也置为已退单
            long medicalChargedCount = itemProcessorMap
                    .values()
                    .stream()
                    .filter(itemProcessor -> itemProcessor.getProductType() != Constants.ProductType.PROCESS && itemProcessor.getProductType() != Constants.ProductType.EXPRESS_DELIVERY && itemProcessor.getProductType() != Constants.ProductType.INGREDIENT)
                    .filter(itemProcessor -> itemProcessor.getItemStatus() == Constants.ChargeFormItemStatus.CHARGED)
                    .count();

            //所有药品都已退单
            if (medicalChargedCount == 0) {
                itemProcessorMap
                        .values()
                        .stream()
                        .filter(itemProcessor -> itemProcessor.getProductType() == Constants.ProductType.PROCESS
                                || itemProcessor.getProductType() == Constants.ProductType.EXPRESS_DELIVERY
                                || itemProcessor.getProductType() == Constants.ProductType.INGREDIENT
                        )
                        .forEach(ItemProcessor::setItemStatusUnselected);
                chargeForm.setStatus(Constants.ChargeFormStatus.REFUNDED);
            }
//            else {
//                affectedForm = chargeForm;
//            }
        }
    }

    public void addGiftGoodsAndAllDeductGoodsInAffectedItemsAndZeroPriceItem() {
        itemProcessorMap.values().forEach(ItemProcessor::addGiftGoodsAndAllDeductGoodsInAffectedItemsAndZeroPriceItem);
    }

    public void payBack(String operatorId, boolean payBackFinished) {

        if (payBackFinished) {
            chargeForm.setStatus(Constants.ChargeFormStatus.UNCHARGED);
            if (isAirPharmacy()) {
                chargeForm.setReceivedPrice(BigDecimal.ZERO);
                chargeForm.setAdjustmentPrice(BigDecimal.ZERO);
//                chargeForm.setExpectedTotalPrice(null);
                chargeForm.setPromotionPrice(BigDecimal.ZERO);
                chargeForm.setPromotionInfoJson(null);
                chargeForm.setCouponPromotionInfoJson(null);
                chargeForm.setGiftRulePromotionInfoJson(null);
                chargeForm.calculateDiscountedPrice();
                FillUtils.fillLastModifiedBy(chargeForm, operatorId);
                affectedForm = chargeForm;
            }
        }

        itemProcessorMap.values().forEach(itemProcessor -> itemProcessor.payBack(operatorId, payBackFinished, chargeForm.getSourceFormType()));
    }

    public int getStatus() {
        return chargeForm.getStatus();
    }

    public void updateRefundInfo(ChargeForm toRefundChargeForm, boolean checkCanRefundCount, String operatorId) throws CisCustomException {
        if (toRefundChargeForm == null || !TextUtils.equals(toRefundChargeForm.getId(), chargeForm.getId()) || toRefundChargeForm.getChargeFormItems() == null) {
            return;
        }

        if (isAirPharmacy()) {
            addedRefundChargeForm = new ChargeForm();
            BeanUtils.copyProperties(chargeForm, addedRefundChargeForm);
            addedRefundChargeForm.setReceivedPrice(BigDecimal.ZERO);
            refundChargeForm = addedRefundChargeForm;
        } else {
            //判断抵扣套餐是否全退
            checkDeductComposeItemCanRefund(toRefundChargeForm.getChargeFormItems());

            for (ChargeFormItem toRefundChargeFormItem : toRefundChargeForm.getChargeFormItems()) {
                String itemKey = toRefundChargeFormItem.getComposeType() == ComposeType.COMPOSE_SUB_ITEM ? toRefundChargeFormItem.getComposeParentFormItemId() : toRefundChargeFormItem.getId();
                ItemProcessor itemProcessor = itemProcessorMap.getOrDefault(itemKey, null);
                if (itemProcessor == null) {
                    continue;
                }
                itemProcessor.updateRefundInfo(toRefundChargeFormItem, checkCanRefundCount, operatorId);
            }
        }
    }

    private void checkDeductComposeItemCanRefund(List<ChargeFormItem> toRefundChargeFormItems) {

        if (CollectionUtils.isEmpty(toRefundChargeFormItems)) {
            return;
        }

        for (ItemProcessor deductedComposeItemProcessor : itemProcessorMap.values()) {
            deductedComposeItemProcessor.checkDeductComposeItemCanRefund(toRefundChargeFormItems);
        }

    }

    public void updateRefundInfo(ChargeForm toRefundChargeForm) {
        refundChargeForm = new ChargeForm();
        BeanUtils.copyProperties(toRefundChargeForm, refundChargeForm);
        addedRefundChargeForm = new ChargeForm();
        BeanUtils.copyProperties(toRefundChargeForm, addedRefundChargeForm);
    }

    public void refund(String operatorId) {

        if (!isAirPharmacy()) {
            itemProcessorMap.values().forEach(itemProcessor -> itemProcessor.refund(operatorId));

            if (chargeForm.getStatus() == Constants.ChargeFormStatus.CHARGED) {
                BigDecimal totalCount = itemProcessorMap.values()
                        .stream()
                        .filter(itemProcessor -> itemProcessor.getItemStatus() == Constants.ChargeFormItemStatus.CHARGED)
                        .map(itemProcessor -> MathUtils.calculateTotalCount(itemProcessor.getUnitCount(), itemProcessor.getDoseCount()))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                BigDecimal totalRefundCount = itemProcessorMap.values()
                        .stream()
                        .filter(itemProcessor -> itemProcessor.getItemStatus() == Constants.ChargeFormItemStatus.CHARGED)
                        .map(itemProcessor -> MathUtils.wrapBigDecimalOrZero(itemProcessor.getRefundUnitCount()).multiply(MathUtils.wrapBigDecimalOrZero(itemProcessor.getRefundDoseCount())))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                if (totalCount.compareTo(totalRefundCount) <= 0) {
                    chargeForm.setStatus(Constants.ChargeFormStatus.REFUNDED);
                    FillUtils.fillLastModifiedBy(chargeForm, operatorId);
                }

            }
        } else {

            if (addedRefundChargeForm == null) {
                return;
            }
            affectedForm = addedRefundChargeForm;

            itemProcessorMap.values().forEach(itemProcessor -> itemProcessor.refundAirPharmacyItem(operatorId));

            chargeForm.setRefundTotalPrice(addedRefundChargeForm.getTotalPrice());
            chargeForm.setRefundDiscountPrice(addedRefundChargeForm.getDiscountPrice());
            chargeForm.setStatus(Constants.ChargeFormStatus.REFUNDED);
            FillUtils.fillLastModifiedBy(chargeForm, operatorId);
        }
    }

    public void renew() {
        itemProcessorMap.values().forEach(ItemProcessor::renew);
        chargeForm.setStatus(Constants.ChargeFormStatus.UNCHARGED);
    }


    public void updateReceivedPrice(Map<String, ChargePayItemInfo> chargePayItemInfoMap, Set<String> thisTimeHealthCardAffectedIds) {
        if (isAirPharmacy()) {
            ChargePayItemInfo chargePayItemInfo = chargePayItemInfoMap.getOrDefault(getFormId(), null);
            if (chargePayItemInfo == null) {
                return;
            }

            chargeForm.setReceivedPrice(MathUtils.wrapBigDecimalAdd(chargeForm.getReceivedPrice(), chargePayItemInfo.getThisTimeReceivableFee()));
            chargeForm.setThisTimeReceivableFee(chargePayItemInfo.getThisTimeReceivableFee());
            affectedForm = chargeForm;

            updateAirPharmacyItemReceivedPrice(chargePayItemInfo.getThisTimeReceivableFee(), thisTimeHealthCardAffectedIds.contains(chargeForm.getId()));
            return;
        }

        itemProcessorMap.values()
                .stream()
                .filter(itemProcessor -> chargePayItemInfoMap.containsKey(itemProcessor.getItemId()))
                .forEach(itemProcessor -> itemProcessor.updateReceivedPrice(chargePayItemInfoMap.get(itemProcessor.getItemId()), true, thisTimeHealthCardAffectedIds));
    }

    private void updateAirPharmacyItemReceivedPrice(BigDecimal thisTimeReceivableFee, boolean needMarkHealthPaidItem) {
        //将本次的已收平摊到每个子项上
        List<FlatPriceHelper.FlatPriceCell> flatPriceCells = itemProcessorMap.values().stream().map(itemProcessor -> {
            FlatPriceHelper.FlatPriceCell flatPriceCell = new FlatPriceHelper.FlatPriceCell();
            flatPriceCell.setId(itemProcessor.getItemId());
            flatPriceCell.setName(itemProcessor.getName());
            flatPriceCell.setPriority(FlatPriceHelper.PRIORITY_HIGH);
            flatPriceCell.setTotalPrice(itemProcessor.getTotalPrice(false).add(itemProcessor.getDiscountPrice(false)));
            return flatPriceCell;
        }).collect(Collectors.toList());

        FlatPriceHelper flatPriceHelper = new FlatPriceHelper(thisTimeReceivableFee);
        flatPriceHelper.flat(flatPriceCells);

        Map<String, FlatPriceHelper.FlatPriceCell> flatPriceCellMap = flatPriceCells.stream().collect(Collectors.toMap(FlatPriceHelper.FlatPriceCell::getId, Function.identity(), (a, b) -> a));
        itemProcessorMap.values().forEach(itemProcessor -> {
            FlatPriceHelper.FlatPriceCell flatPriceCell = flatPriceCellMap.getOrDefault(itemProcessor.getItemId(), null);
            if (flatPriceCell != null) {
                itemProcessor.updateReceivedPriceCore(flatPriceCell.getFlatPrice(), null, false, needMarkHealthPaidItem);
            }
        });
    }

    public Set<String> collectProductIds(int itemStatus) {
        return itemProcessorMap.values().stream()
                .filter(itemProcessor -> itemProcessor.getItemStatus() == itemStatus)
                .filter(itemProcessor -> !TextUtils.isEmpty(itemProcessor.getProductId()))
                .filter(itemProcessor -> !Constants.ProductType.SYSTEM_FEE_PRODUCT_TYPE.contains(itemProcessor.getProductType()))
                .map(ItemProcessor::getProductId)
                .collect(Collectors.toSet());
    }

    public List<QueryGoodsWithStockReq> collectProductIdsAndCount(List<Integer> itemStatuses) {
        return itemProcessorMap.values().stream()
                .filter(itemProcessor -> itemStatuses.contains(itemProcessor.getItemStatus()))
                .filter(itemProcessor -> !TextUtils.isEmpty(itemProcessor.getProductId()))
                .filter(itemProcessor -> !Constants.ProductType.SYSTEM_FEE_PRODUCT_TYPE.contains(itemProcessor.getProductType()))
                .map(itemProcessor -> itemProcessor.generateQueryGoodsWithStockReq())
                .collect(Collectors.toList());
    }

    //获取这个chargeForm下所有药的药名
    public List<QueryChineseMedicinesInPharmacyByCadnsReq.QueryCadnsGoodsItems> collectQueryCadnsGoodsItems(int productType, int productSubType) {
        return itemProcessorMap.values().stream()
                .filter(itemProcessor -> StringUtils.isEmpty(itemProcessor.getProductId()))
                .filter(itemProcessor -> itemProcessor.getProductType() == productType && itemProcessor.getProductSubType() == productSubType)
                .map(itemProcessor -> {
                    QueryChineseMedicinesInPharmacyByCadnsReq.QueryCadnsGoodsItems queryCadnsGoodsItems = new QueryChineseMedicinesInPharmacyByCadnsReq.QueryCadnsGoodsItems();
                    queryCadnsGoodsItems.setGoodsId(itemProcessor.getProductId());
                    queryCadnsGoodsItems.setMedicineCadn(itemProcessor.getName());
                    queryCadnsGoodsItems.setPackageCount(itemProcessor.getUnitCount());
                    queryCadnsGoodsItems.setKeyId(itemProcessor.getItemId());
                    return queryCadnsGoodsItems;
                })
                .collect(Collectors.toList());
    }

    public boolean isNameInProductNames(Set<String> productNames, int productType, int productSubType) {
        return itemProcessorMap.values().stream()
                .filter(itemProcessor -> StringUtils.isEmpty(itemProcessor.getProductId()))
                .filter(itemProcessor -> itemProcessor.getProductType() == productType && itemProcessor.getProductSubType() == productSubType)
                .anyMatch(itemProcessor -> productNames.contains(itemProcessor.getName()));
    }

    /**
     * 构造支付项
     */
    public List<ChargePayItemInfo> generatePayItems(boolean isShebaoPayMode) {
        if (isAirPharmacy()) {

            if (chargeForm.getStatus() == Constants.ChargeFormStatus.REFUNDED) {
                return null;
            }

            GoodsBaseInfo goodsBaseInfo = generateGoodsBaseInfo();

            if (goodsBaseInfo == null) {
                return null;
            }

            BigDecimal receivableFee = isShebaoPayMode ? this.getSheBaoReceivableFee() : this.receivableFee;

            ChargePayItemInfo chargePayItemInfo = new ChargePayItemInfo();
            chargePayItemInfo.setId(chargeForm.getId())
                    .setName(getAirPharmacyFormName())
                    .setGoodsCMSpec(goodsBaseInfo.getGoodsCMSpec())
                    .setPharmacyType(goodsBaseInfo.getPharmacyType())
                    .setIsAirPharmacyForm(1)
                    .setGoodsType(goodsBaseInfo.getGoodsType())
                    .setGoodsSubType(goodsBaseInfo.getGoodsSubType())
                    .setReceivableFee(receivableFee)
                    .setThisTimeReceivableFee(MathUtils.wrapBigDecimalOrZero(receivableFee))
                    .setChildren(itemProcessorMap.values().stream()
                            .filter(itemProcessor -> itemProcessor.getItemStatus() != Constants.ChargeFormItemStatus.UNSELECTED)
                            .map(itemProcessor -> itemProcessor.generatePayItem(isShebaoPayMode))
                            .collect(Collectors.toList()));

            return Collections.singletonList(chargePayItemInfo);
        }

        return itemProcessorMap.values().stream()
                .filter(itemProcessor -> itemProcessor.getItemStatus() != Constants.ChargeFormItemStatus.UNSELECTED)
                .map(itemProcessor -> itemProcessor.generatePayItem(isShebaoPayMode))
                .collect(Collectors.toList());
    }

    /**
     * @param goodsItemMap        第一个key为药房号，第二个key为goodsId
     * @param goodsItemKeyNameMap key为goodsName
     * @param operatorId
     */
    public void bindGoodsItems(Map<Integer, Map<String, GoodsItem>> goodsItemMap,
                               Map<String, GoodsItem> goodsItemKeyNameMap,
                               boolean isCopyByOutpatient, String operatorId) {
        if (CollectionUtils.isEmpty(goodsItemMap)
                && CollectionUtils.isEmpty(goodsItemKeyNameMap)) {
            return;
        }

        if (!CollectionUtils.isEmpty(goodsItemMap)) {
            if (!isAirPharmacy()) {
                //如果是复制历史处方，pharmacyNo可能和查出来的不一样，直接使用goodsId作为唯一key去找goodsItem
                if (isCopyByOutpatient) {
                    Map<String, GoodsItem> goodsItemIdMap = new HashMap<>();
                    goodsItemMap.values()
                            .forEach(goodsItemIdMap::putAll);
                    itemProcessorMap.values()
                            .forEach(itemProcessor -> Optional.ofNullable(goodsItemIdMap.getOrDefault(itemProcessor.getProductId(), null))
                                    .ifPresent(goodsItem -> {
                                        itemProcessor.setGoodsItem(goodsItem, operatorId);
                                        itemProcessor.updatePharmacyTypeAndPharmacyNo(goodsItem.getPharmacyType(), goodsItem.getPharmacyNo());
                                    })
                            );
                } else {
                    itemProcessorMap.values()
                            .forEach(itemProcessor -> Optional.ofNullable(goodsItemMap.getOrDefault(itemProcessor.getPharmacyNo(), null))
                                    .map(goodsItemIdMap -> goodsItemIdMap.getOrDefault(itemProcessor.getProductId(), null))
                                    .ifPresent(goodsItem -> itemProcessor.setGoodsItem(goodsItem, operatorId))
                            );
                }
            } else {
                itemProcessorMap.values()
                        .forEach(itemProcessor -> Optional.ofNullable(goodsItemMap.getOrDefault(0, null))
                                .map(goodsItemIdMap -> goodsItemIdMap.getOrDefault(itemProcessor.getProductId(), null))
                                .ifPresent(goodsItem -> itemProcessor.setGoodsItem(goodsItem, operatorId))
                        );
            }
        }

        if (!CollectionUtils.isEmpty(goodsItemKeyNameMap)) {
            itemProcessorMap.values().stream()
                    .filter(itemProcessor -> StringUtils.isEmpty(itemProcessor.getProductId()))
                    .forEach(itemProcessor -> {
                        GoodsItem item = goodsItemKeyNameMap.getOrDefault(itemProcessor.getName(), null);
                        if (item != null) {
                            itemProcessor.setGoodsItem(item, operatorId);
                        }
                    });

        }
    }

    /**
     * @param chargeFormItemIdGoodsItemMap   key为chargeFormItemId，value为goodsItem
     * @param operatorId
     */
    public void bindGoodsItems(Map<String, GoodsItem> chargeFormItemIdGoodsItemMap,
                               boolean isCopyByOutpatient,
                               String operatorId) {
        if (CollectionUtils.isEmpty(chargeFormItemIdGoodsItemMap)) {
            return;
        }

        if (isAirPharmacy()) {
            return;
        }

        itemProcessorMap.values().forEach(itemProcessor -> {
            GoodsItem goodsItem = chargeFormItemIdGoodsItemMap.get(itemProcessor.getItemId());
            if (goodsItem == null) {
                return;
            }
            itemProcessor.setGoodsItem(goodsItem, operatorId);
            if (isCopyByOutpatient) {
                itemProcessor.updatePharmacyTypeAndPharmacyNo(goodsItem.getPharmacyType(), goodsItem.getPharmacyNo());
            }
        });
    }

    public void setChargeFormItemProductIdNull(List<String> noGoodsItemProductIds) {
        if (CollectionUtils.isEmpty(noGoodsItemProductIds)) {
            return;
        }
        itemProcessorMap.values().stream()
                .filter(itemProcessor -> org.apache.commons.lang3.StringUtils.isNotEmpty(itemProcessor.getProductId()))
                .filter(itemProcessor -> noGoodsItemProductIds.contains(itemProcessor.getProductId()))
                .forEach(ItemProcessor::setProductIdAndPriceNull);
    }

    public void bindDispensingInfo(Map<Integer, Map<String, DispensingInfo>> pharmacyTypeDispensingInfoMap, Map<String, DispensingSheetInfo> dispensingSheetInfoMap) {
        if (pharmacyTypeDispensingInfoMap == null) {
            return;
        }
        if (getPharmacyType() == GoodsConst.PharmacyType.VIRTUAL_PHARMACY) {
            DispensingSheetInfo dispensingSheetInfo = dispensingSheetInfoMap.getOrDefault(String.format("%d-%d", getPharmacyType(), getPharmacyNo()), null);
            if (dispensingSheetInfo != null) {
                virtualPharmacyDispensingStatus = dispensingSheetInfo.getStatus();
            }
        }
        Map<String, DispensingInfo> dispensingInfoMap = pharmacyTypeDispensingInfoMap.getOrDefault(getPharmacyType(), null);
        if (dispensingInfoMap == null) {
            return;
        }
        itemProcessorMap.values().stream()
                .filter(itemProcessor -> itemProcessor.getItemStatus() == Constants.ChargeFormItemStatus.CHARGED)
                .forEach(itemProcessor -> itemProcessor.setDispensingInfo(dispensingInfoMap));
    }

    public void updateProductInfo(boolean needLatestGoodsName) {
        itemProcessorMap.values().forEach(itemProcessor -> itemProcessor.updateProductInfo(needLatestGoodsName, getSourceFormType()));
        // 处理空中药房的议价信息
        if (isAirPharmacy() && getStatus() == Constants.ChargeFormStatus.UNCHARGED) {
            calculateAirPharmacyTotalPrice();
        }
    }

    public void updateChargeFormItemOldSourceUnitPrice() {
        itemProcessorMap.values().forEach(ItemProcessor::updateChargeFormItemOldSourceUnitPrice);
    }

    public void updateProductStock(boolean isLockByBatch) {
        itemProcessorMap.values().forEach(itemProcessor -> itemProcessor.updateProductStock(getPharmacyType(), isLockByBatch));
    }


    public List<CalculatePromotionItem> getCalculatePromotionItems() {
        return itemProcessorMap.values()
                .stream()
                .filter(itemProcessor -> itemProcessor.getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED)
                //排除医保限价的item
                .filter(itemProcessor -> !itemProcessor.isLimitNoPay())
                //排除挂网价限价的item
                .filter(itemProcessor -> !itemProcessor.containListingPrice())
                .filter(itemProcessor -> itemProcessor.getChargeFormItem().getSourceItemType() == Constants.SourceItemType.NORMAL)
                .filter(itemProcessor -> !Constants.ProductType.notNeedDiscountProductTypes().contains(itemProcessor.getProductType()))
                .map(itemProcessor -> {
                    CalculatePromotionItem discountItem = new CalculatePromotionItem();
                    discountItem.setId(itemProcessor.getItemId());
                    discountItem.setName(itemProcessor.getName());
                    discountItem.setProductId(itemProcessor.getProductId());
                    discountItem.setProductType(itemProcessor.getProductType());
                    discountItem.setProductSubType(itemProcessor.getProductSubType());
                    discountItem.setTotalPrice(itemProcessor.getTotalPrice(false));
                    discountItem.setUnitPrice(itemProcessor.getChargeFormItem().getUnitPrice());
                    discountItem.setDoseCount(itemProcessor.getChargeFormItem().getDoseCount());
                    discountItem.setUseDismounting(itemProcessor.getChargeFormItem().getUseDismounting());
                    discountItem.setTotalCount(MathUtils.calculateTotalCount(itemProcessor.getUnitCount(), itemProcessor.getDoseCount()));
                    discountItem.setPharmacyType(getPharmacyType());
                    discountItem.setOriginalPrice(!itemProcessor.getChargeFormItem().isExpectedPriceItem());
                    discountItem.setGoodsBaseInfo(itemProcessor.generateGoodsBaseInfo());
                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(itemProcessor.getItemBatchInfoProcessors())) {
                        discountItem.setBatchInfos(itemProcessor.getItemBatchInfoProcessors()
                                .stream()
                                .map(ItemBatchInfoProcessor::generateCalculatePromotionItemBatchInfo)
                                .sorted(Comparator.comparing(CalculatePromotionItemBatchInfo::getUnitPrice).reversed())
                                .collect(Collectors.toList())
                        );
                    }
                    return discountItem;
                }).collect(Collectors.toList());
    }


    public List<RpcGetShebaoMatchedCodesReq.GetSmartMatchedReqItem> getSmartMatchedReqItems() {
        return itemProcessorMap.values()
                .stream()
                .filter(itemProcessor -> itemProcessor.getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED)
                .filter(itemProcessor -> itemProcessor.getChargeFormItem().getSourceItemType() == Constants.SourceItemType.NORMAL)
                .filter(itemProcessor -> Objects.nonNull(itemProcessor.getGoodsItem()) || itemProcessor.getProductType() == Constants.ProductType.REGISTRATION)
                .map(ItemProcessor::getSmartMatchedReqItems)
                .flatMap(List::stream).collect(Collectors.toList());
    }

    public Map<String, GoodsBaseInfo> getGoodsBaseInfosForPartedPaid() {
        HashMap<String, GoodsBaseInfo> goodsBaseInfoMap = new HashMap<>();
        if (isAirPharmacy()) {
            GoodsBaseInfo goodsBaseInfo = generateGoodsBaseInfo();
            if (goodsBaseInfo == null) {
                return new HashMap<>();
            }
            goodsBaseInfoMap.put(getChargeFormId(), goodsBaseInfo);
            return goodsBaseInfoMap;
        }
        itemProcessorMap.keySet()
                .stream()
                .filter(id -> itemProcessorMap.get(id).getItemStatus() == Constants.ChargeFormItemStatus.CHARGED)
                .forEach(id -> {
                    ItemProcessor itemProcessor = itemProcessorMap.get(id);
                    if (MathUtils.wrapBigDecimalCompare(itemProcessor.getReceivableFee(), BigDecimal.ZERO) <= 0) {
                        return;
                    }
                    GoodsBaseInfo goodsBaseInfo = itemProcessor.generateGoodsBaseInfo();
                    if (goodsBaseInfo == null) {
                        return;
                    }
                    goodsBaseInfoMap.put(itemProcessor.getItemId(), goodsBaseInfo);
                });
        return goodsBaseInfoMap;
    }

    public List<CalculatePromotionItem> getCalculatePromotionItemsForVirtualPharmacy() {
        if (getPharmacyType() != GoodsConst.PharmacyType.VIRTUAL_PHARMACY) {
            return new ArrayList<>();
        }
        return getCalculatePromotionItems().stream()
                .filter(calculatePromotionItem -> !Constants.ProductType.SYSTEM_FEE_PRODUCT_TYPE.contains(calculatePromotionItem.getProductType()))
                .collect(Collectors.toList());
    }

    public void applyPromotion(Map<String, CalculatePromotionItem> promotionItemMap, Map<String, CalculatePromotionAirPharmacyForm> promotionAirPharmacyFormMap, boolean resetWithPromotion) {

        if (!isAirPharmacy()) {
            applyChargeFormItemPromotion(promotionItemMap, resetWithPromotion);
        } else {
            applyAirPharmacyFormPromotion(promotionAirPharmacyFormMap, resetWithPromotion);
        }
    }

    public void applyPatientPointPromotionPrice(Map<String, SmartFlatPriceHelper.SmartFlatPriceCell> flatPriceCellMap) {
        if (chargeForm.getStatus() != Constants.ChargeFormStatus.UNCHARGED) {
            return;
        }
        if (!isAirPharmacy()) {
            applyItemPatientPointPromotionPrice(flatPriceCellMap);
        } else {
            SmartFlatPriceHelper.SmartFlatPriceCell smartFlatPriceCell = flatPriceCellMap.get(chargeForm.getId());
            if (smartFlatPriceCell == null) {
                return;
            }
            BigDecimal flatPrice = smartFlatPriceCell.getFlatPrice();
            if (MathUtils.wrapBigDecimalCompare(flatPrice, BigDecimal.ZERO) == 0) {
                return;
            }
            chargeForm.setPromotionPrice(MathUtils.wrapBigDecimalAdd(chargeForm.getPromotionPrice(), flatPrice));
            chargeForm.calculateDiscountPrice();
            ChargeDiscountInfo promotionInfo = Optional.ofNullable(chargeForm.getPromotionInfo()).orElse(new ChargeDiscountInfo());
            promotionInfo.setPatientPointPromotionFee(flatPrice);
            chargeForm.setPromotionInfo(promotionInfo);
            chargeForm.setPromotionInfoJson(JsonUtils.dump(promotionInfo));
            applyAirPharmacyItemPatientPointPromotionPrice(flatPrice);
        }
    }

    private void applyAirPharmacyItemPatientPointPromotionPrice(BigDecimal flatPrice) {
        List<SmartFlatPriceHelper.SmartFlatPriceCell> airPharmacyItemCells = getCalculateAdjustmentItemsCore();
        SmartFlatPriceHelper.flat(flatPrice, airPharmacyItemCells);
        Map<String, SmartFlatPriceHelper.SmartFlatPriceCell> airPharmacyFlatPriceCellMap = airPharmacyItemCells.stream().collect(Collectors.toMap(SmartFlatPriceHelper.SmartFlatPriceCell::getId, Function.identity(), (a, b) -> a));
        applyItemPatientPointPromotionPrice(airPharmacyFlatPriceCellMap);
    }

    private void applyItemPatientPointPromotionPrice(Map<String, SmartFlatPriceHelper.SmartFlatPriceCell> flatPriceCellMap) {
        itemProcessorMap.keySet()
                .stream()
                .filter(id -> itemProcessorMap.get(id).getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED)
                .forEach(id -> {
                    SmartFlatPriceHelper.SmartFlatPriceCell smartFlatPriceCell = flatPriceCellMap.getOrDefault(id, null);
                    if (smartFlatPriceCell != null && MathUtils.wrapBigDecimalOrZero(smartFlatPriceCell.getFlatPrice()).compareTo(BigDecimal.ZERO) != 0) {
                        itemProcessorMap.get(id).addPatientPointPromotionPrice(smartFlatPriceCell.getFlatPrice());
                    }
                });
    }

    private void applyAirPharmacyFormPromotion(Map<String, CalculatePromotionAirPharmacyForm> promotionAirPharmacyFormMap, boolean resetWithPromotion) {
        CalculatePromotionAirPharmacyForm promotionAirPharmacyForm = promotionAirPharmacyFormMap.get(chargeForm.getId());
        if (promotionAirPharmacyForm != null && MathUtils.wrapBigDecimalCompare(promotionAirPharmacyForm.getDiscountPrice(), BigDecimal.ZERO) != 0) {
            setFormPromotion(promotionAirPharmacyForm.getPromotions(), promotionAirPharmacyForm.getDiscountPrice(), resetWithPromotion);
        } else {
            setFormPromotion(null, BigDecimal.ZERO, resetWithPromotion);
        }
    }

    private void setFormPromotion(List<PromotionSimple> promotions, BigDecimal promotionPrice, boolean resetWithPromotion) {
        chargeForm.setPromotionPrice(promotionPrice);
        chargeForm.calculateDiscountedPrice();
        // 将空中药房的折扣平摊的item上
        setAirPharmacyItemPromotion(promotions, promotionPrice, resetWithPromotion);
        if (!CollectionUtils.isEmpty(promotions)) {
            ChargeDiscountInfo promotionInfo = new ChargeDiscountInfo();
            List<PromotionSimple> discountPromotions = promotions.stream()
                    .filter(promotionSimple -> promotionSimple.getType() == Promotion.Type.MEMBER_DISCOUNT
                            || promotionSimple.getType() == Promotion.Type.NORMAL_DISCOUNT
                            || (promotionSimple.getType() == Promotion.Type.CARD && (promotionSimple.getSubType() == PromotionSimple.SubType.DISCOUNT_FOR_GOODS || promotionSimple.getSubType() == PromotionSimple.SubType.DISCOUNT_FOR_TYPE))
                    )
                    .collect(Collectors.toList());
            List<PromotionSimple> couponPromotions = promotions.stream()
                    .filter(promotionSimple -> promotionSimple.getType() == Promotion.Type.COUPON)
                    .collect(Collectors.toList());
            List<PromotionSimple> giftRulePromotions = promotions.stream()
                    .filter(promotionSimple -> promotionSimple.getType() == Promotion.Type.GIFT_RULE)
                    .collect(Collectors.toList());
            List<PromotionSimple> cardPromotions = promotions.stream()
                    .filter(promotionSimple -> promotionSimple.getType() == Promotion.Type.CARD && promotionSimple.getSubType() == PromotionSimple.SubType.DISCOUNT_FOR_DEDUCT)
                    .collect(Collectors.toList());
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(discountPromotions)) {
                promotionInfo.setDiscountPromotionInfos(discountPromotions.stream().map(discountPromotion -> new ChargeDiscountInfo.PromotionInfo()
                                .setId(discountPromotion.getPromotionId())
                                .setName(discountPromotion.getPromotionName())
                                .setType(discountPromotion.getType())
                                .setDiscountPrice(discountPromotion.getDiscountPrice()))
                        .collect(Collectors.toList())
                );
            }
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(couponPromotions)) {
                promotionInfo.setCouponInfos(couponPromotions.stream().map(couponPromotion -> {
                            ChargeDiscountInfo.CouponInfo couponInfo = new ChargeDiscountInfo.CouponInfo();
                            couponInfo.setId(couponPromotion.getPromotionId())
                                    .setName(couponPromotion.getPromotionName())
                                    .setType(couponPromotion.getType())
                                    .setDiscountPrice(couponPromotion.getDiscountPrice());
                            couponInfo.setCouponIds(couponPromotion.getCouponIds());
                            return couponInfo;
                        }).collect(Collectors.toList())
                );
            }
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(giftRulePromotions)) {
                promotionInfo.setGiftRulePromotionInfos(giftRulePromotions.stream().map(giftRulePromotion -> new ChargeDiscountInfo.PromotionInfo()
                                .setId(giftRulePromotion.getPromotionId())
                                .setName(giftRulePromotion.getPromotionName())
                                .setType(giftRulePromotion.getType())
                                .setDiscountPrice(giftRulePromotion.getDiscountPrice()))
                        .collect(Collectors.toList())
                );
            }
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(cardPromotions)) {
                promotionInfo.setDeductDiscountInfos(cardPromotions.stream().map(cardPromotion -> {
                            ChargeDiscountInfo.DeductDiscountInfo deductDiscountInfo = new ChargeDiscountInfo.DeductDiscountInfo();
                            deductDiscountInfo.setId(cardPromotion.getPromotionId())
                                    .setName(cardPromotion.getPromotionName())
                                    .setDiscountPrice(cardPromotion.getDiscountPrice())
                                    .setType(cardPromotion.getType());
                            deductDiscountInfo.setDeductedCount(new BigDecimal(cardPromotion.getDeductedCount()))
                                    .setPresentId(cardPromotion.getPresentId());
                            return deductDiscountInfo;
                        }).collect(Collectors.toList())
                );
            }
            chargeForm.setPromotionInfo(promotionInfo);
            chargeForm.setPromotionInfoJson(JsonUtils.dump(promotionInfo));

        } else {
            chargeForm.setPromotionInfo(null);
            chargeForm.setPromotionInfoJson("");
        }
    }

    private void setAirPharmacyItemPromotion(List<PromotionSimple> promotions, BigDecimal promotionPrice, boolean resetWithPromotion) {
        if (MathUtils.wrapBigDecimalOrZero(promotionPrice).compareTo(BigDecimal.ZERO) == 0) {
            return;
        }
        List<FlatPriceHelper.FlatPriceCell> cells = itemProcessorMap.values()
                .stream()
                .filter(itemProcessor -> !Constants.ProductType.SYSTEM_FEE_PRODUCT_TYPE.contains(itemProcessor.getProductType()))
                .filter(itemProcessor -> itemProcessor.getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED)
                .map(itemProcessor -> {
                    FlatPriceHelper.FlatPriceCell cell = new FlatPriceHelper.FlatPriceCell();
                    cell.setId(itemProcessor.getItemId());
                    cell.setName(itemProcessor.getName());
                    cell.setPriority(FlatPriceHelper.PRIORITY_HIGH);
                    cell.setTotalPrice(itemProcessor.getTotalPrice(false));
                    return cell;
                })
                .collect(Collectors.toList());

        FlatPriceHelper flatPriceHelper = new FlatPriceHelper(promotionPrice);
        flatPriceHelper.flat(cells);
        Map<String, FlatPriceHelper.FlatPriceCell> flatPriceCellMap = ListUtils.toMap(cells, FlatPriceHelper.FlatPriceCell::getId);
        List<FlatPriceHelper.FlatPriceCell> promotionCells = Optional.ofNullable(promotions).orElse(new ArrayList<>())
                .stream()
                .filter(promotionSimple -> MathUtils.wrapBigDecimalOrZero(promotionSimple.getDiscountPrice()).compareTo(BigDecimal.ZERO) != 0)
                .map(promotionSimple -> {
                    FlatPriceHelper.FlatPriceCell promotionCell = new FlatPriceHelper.FlatPriceCell();
                    promotionCell.setId(promotionSimple.getPromotionId());
                    promotionCell.setPriority(FlatPriceHelper.PRIORITY_HIGH);
                    promotionCell.setTotalPrice(promotionSimple.getDiscountPrice().abs());
                    return promotionCell;
                })
                .collect(Collectors.toList());

        itemProcessorMap.values()
                .stream()
                .filter(itemProcessor -> !Constants.ProductType.SYSTEM_FEE_PRODUCT_TYPE.contains(itemProcessor.getProductType()))
                .filter(itemProcessor -> itemProcessor.getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED)
                .forEach(itemProcessor -> {
                    FlatPriceHelper.FlatPriceCell cell = flatPriceCellMap.getOrDefault(itemProcessor.getItemId(), null);
                    if (Objects.isNull(cell)) {
                        return;
                    }

                    if (MathUtils.wrapBigDecimalOrZero(cell.getFlatPrice()).compareTo(BigDecimal.ZERO) == 0) {
                        itemProcessor.setPromotion(null, BigDecimal.ZERO, resetWithPromotion);
                        return;
                    }

                    itemProcessor.setPromotion(generateItemPromotions(promotionCells, cell.getFlatPrice(), promotions), cell.getFlatPrice(), resetWithPromotion);
                });

        itemProcessorMap.values()
                .stream()
                .filter(itemProcessor -> !Constants.ProductType.SYSTEM_FEE_PRODUCT_TYPE.contains(itemProcessor.getProductType()))
                .filter(itemProcessor -> itemProcessor.getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED)
                .map(itemProcessor -> {
                    FlatPriceHelper.FlatPriceCell cell = new FlatPriceHelper.FlatPriceCell();
                    cell.setId(itemProcessor.getItemId());
                    cell.setName(itemProcessor.getName());
                    cell.setPriority(FlatPriceHelper.PRIORITY_HIGH);
                    cell.setTotalPrice(itemProcessor.getTotalPrice(false));
                    return cell;
                })
                .collect(Collectors.toList());

    }

    private List<PromotionSimple> generateItemPromotions(List<FlatPriceHelper.FlatPriceCell> promotionCells, BigDecimal flatPrice, List<PromotionSimple> promotions) {

        if (org.apache.commons.collections.CollectionUtils.isEmpty(promotionCells)) {
            return new ArrayList<>();
        }

        FlatPriceHelper promotionFlatPriceHelper = new FlatPriceHelper(flatPrice);
        promotionFlatPriceHelper.flat(promotionCells);

        Map<String, FlatPriceHelper.FlatPriceCell> promotionFlatPriceCellMap = ListUtils.toMap(promotionCells, FlatPriceHelper.FlatPriceCell::getId);

        return promotions.stream()
                .map(source -> {
                    FlatPriceHelper.FlatPriceCell promotionCell = promotionFlatPriceCellMap.getOrDefault(source.getPromotionId(), null);
                    if (promotionCell == null) {
                        return null;
                    }
                    PromotionSimple target = new PromotionSimple();
                    BeanUtils.copyProperties(source, target);
                    target.setDiscountPrice(promotionCell.getFlatPrice());
                    return target;
                }).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private void applyChargeFormItemPromotion(Map<String, CalculatePromotionItem> promotionItemMap, boolean resetWithPromotion) {
        itemProcessorMap.keySet()
                .stream()
                .filter(id -> itemProcessorMap.get(id).getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED)
                .forEach(id -> {
                    CalculatePromotionItem promotionItem = promotionItemMap.getOrDefault(id, null);
                    if (promotionItem != null &&
                            (MathUtils.wrapBigDecimalOrZero(promotionItem.getDiscountPrice()).compareTo(BigDecimal.ZERO) != 0 || MathUtils.wrapBigDecimalCompare(promotionItem.getAllDeductedTotalCount(), BigDecimal.ZERO) > 0)) {
                        itemProcessorMap.get(id).setPromotion(promotionItem.getPromotions(), promotionItem.getPromotionPrice(), resetWithPromotion);
                    } else {
                        itemProcessorMap.get(id).setPromotion(null, BigDecimal.ZERO, resetWithPromotion);
                    }
                });
    }

    public List<SmartFlatPriceHelper.SmartFlatPriceCell> getCalculateAdjustmentItems() {
        if (!isAirPharmacy()) {
            return getCalculateAdjustmentItemsCore();
        } else {
            SmartFlatPriceHelper.SmartFlatPriceCell cell = new SmartFlatPriceHelper.SmartFlatPriceCell(chargeForm.getId(), "空中药房", chargeForm.getTotalPrice().add(chargeForm.getDiscountPrice()), SmartFlatPriceHelper.SmartFlatType.AIR_PHARMACY);
            List<SmartFlatPriceHelper.SmartFlatPriceCell> cells = new ArrayList<>();
            cells.add(cell);
            return cells;
        }

    }

    private List<SmartFlatPriceHelper.SmartFlatPriceCell> getCalculateAdjustmentItemsCore() {
        return itemProcessorMap.keySet()
                .stream()
                .filter(id -> itemProcessorMap.get(id).getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED)
                .filter(id -> itemProcessorMap.get(id).getChargeFormItem().getSourceItemType() == Constants.SourceItemType.NORMAL)
                .map(id -> {
                    ItemProcessor itemProcessor = itemProcessorMap.get(id);
                    if (itemProcessor.isAllDeduct()) {
                        return null;
                    }
                    return new SmartFlatPriceHelper.SmartFlatPriceCell(id, itemProcessor.getName(),
                            itemProcessor.getTotalPrice(false).add(itemProcessor.getDiscountPrice(false)), itemProcessor.getProductType());
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public void updateReceivableFee(Map<String, SmartFlatPriceHelper.SmartFlatPriceCell> flatPriceCellMap) {
        if (!isAirPharmacy()) {
            updateItemReceivableFee(flatPriceCellMap);
        } else {
            SmartFlatPriceHelper.SmartFlatPriceCell cell = flatPriceCellMap.get(chargeForm.getId());
            BigDecimal flatPrice = BigDecimal.ZERO;
            if (cell != null) {
                flatPrice = cell.getFlatPrice();
            }
            if (getStatus() == Constants.ChargeFormStatus.UNCHARGED) {
                receivableFee = MathUtils.wrapBigDecimalAdd(chargeForm.calculateDiscountedPrice(), flatPrice);
                List<SmartFlatPriceHelper.SmartFlatPriceCell> airPharmacyItemCells = getCalculateAdjustmentItemsCore();
                SmartFlatPriceHelper.flat(flatPrice, airPharmacyItemCells);
                Map<String, SmartFlatPriceHelper.SmartFlatPriceCell> airPharmacyFlatPriceCellMap = airPharmacyItemCells.stream().collect(Collectors.toMap(SmartFlatPriceHelper.SmartFlatPriceCell::getId, Function.identity(), (a, b) -> a));
                updateItemReceivableFee(airPharmacyFlatPriceCellMap);
            }
        }

    }

    private void updateItemReceivableFee(Map<String, SmartFlatPriceHelper.SmartFlatPriceCell> flatPriceCellMap) {
        itemProcessorMap.keySet()
                .stream()
                .filter(id -> itemProcessorMap.get(id).getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED)
                .forEach(id -> {
                    SmartFlatPriceHelper.SmartFlatPriceCell adjustmentItem = flatPriceCellMap.getOrDefault(id, null);
                    if (adjustmentItem != null) {
                        itemProcessorMap.get(id).updateReceivableFee(adjustmentItem.getFlatPrice());
                    } else {
                        itemProcessorMap.get(id).updateReceivableFee(BigDecimal.ZERO);
                    }
                });
    }

    public void applyAdjustment(Map<String, SmartFlatPriceHelper.SmartFlatPriceCell> flatPriceCellMap) {
        if (!isAirPharmacy()) {
            applyItemAdjustmentPrice(flatPriceCellMap);
        } else {
            SmartFlatPriceHelper.SmartFlatPriceCell cell = flatPriceCellMap.get(chargeForm.getId());
            if (cell == null || MathUtils.wrapBigDecimalOrZero(cell.getFlatPrice()).compareTo(BigDecimal.ZERO) == 0) {
                return;
            }
            if (getStatus() == Constants.ChargeFormStatus.UNCHARGED) {
                chargeForm.setAdjustmentPrice(cell.getFlatPrice());

                applyAirPharmacyItemAdjustmentPrice(cell.getFlatPrice());
                chargeForm.calculateDiscountedPrice();
                ChargeDiscountInfo promotionInfo = chargeForm.getPromotionInfo();
                if (promotionInfo == null) {
                    promotionInfo = new ChargeDiscountInfo();
                }
                promotionInfo.setAdjustmentFee(cell.getFlatPrice());
                chargeForm.setPromotionInfo(promotionInfo);
                chargeForm.setPromotionInfoJson(JsonUtils.dump(promotionInfo));
            }
        }

    }

    private void applyItemAdjustmentPrice(Map<String, SmartFlatPriceHelper.SmartFlatPriceCell> flatPriceCellMap) {
        itemProcessorMap.keySet()
                .stream()
                .filter(id -> itemProcessorMap.get(id).getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED)
                .forEach(id -> {
                    SmartFlatPriceHelper.SmartFlatPriceCell adjustmentItem = flatPriceCellMap.getOrDefault(id, null);
                    if (adjustmentItem != null) {
                        ItemProcessor itemProcessor = itemProcessorMap.get(id);
                        itemProcessor.setAdjustmentPrice(adjustmentItem.getFlatPrice());
                    }
                });
    }

    private void applyAirPharmacyItemAdjustmentPrice(BigDecimal adjustmentFee) {
        List<SmartFlatPriceHelper.SmartFlatPriceCell> airPharmacyItemCells = getCalculateAdjustmentItemsCore();
        SmartFlatPriceHelper.flat(adjustmentFee, airPharmacyItemCells);
        Map<String, SmartFlatPriceHelper.SmartFlatPriceCell> airPharmacyFlatPriceCellMap = airPharmacyItemCells.stream().collect(Collectors.toMap(SmartFlatPriceHelper.SmartFlatPriceCell::getId, Function.identity(), (a, b) -> a));
        applyItemAdjustmentPrice(airPharmacyFlatPriceCellMap);
    }

    public ChargeForm getAffectedForm() {
        return affectedForm;
    }

    public String getFormId() {
        return chargeForm.getId();
    }

    public String getSpecification() {
        return chargeForm.getSpecification();
    }

    public int getSpecificationTypeIdOrDefault() {

        String specification = chargeForm.getSpecification();

        if (StringUtils.isEmpty(specification)) {
            //获取处方下的第一个药品的规格
            specification = itemProcessorMap.values()
                    .stream()
                    .filter(itemProcessor -> itemProcessor.getProductType() == Constants.ProductType.MEDICINE)
                    .findFirst()
                    .map(ItemProcessor::getGoodsCMSpec)
                    .orElse(null);
        }

        specification = TextUtils.isEmpty(specification) ? Constants.ProductType.MedicineChineseSpecification.DRINKS_PIECE : specification;

        return BisOrderUtils.convertToGoodsTypeId(specification);
    }

    public int getSort() {
        return chargeForm.getSort();
    }

    public int getSourceFormType() {
        return chargeForm.getSourceFormType();
    }

    public ChargeSheetProcessInfo getChargeSheetProcessInfo() {
        return chargeForm.getProcessInfo();
    }

    public String getVendorId() {
        return chargeForm.getVendorId();
    }

    public Collection<ItemProcessor> getItemProcessorList() {
        return itemProcessorMap.values();
    }

    public Map<String, ItemProcessor> getItemProcessorMap() {
        return itemProcessorMap;
    }

    public BigDecimal getTotalPrice(boolean isRefundScene) {
        if (!isAirPharmacy()) {
            return itemProcessorMap.values().stream()
                    .map(itemProcessor -> itemProcessor.getTotalPrice(isRefundScene))
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        } else {
            return chargeForm.getTotalPrice();
        }

    }

    public BigDecimal getSourceTotalPrice() {
        return getItemProcessorList()
                .stream()
                .map(ItemProcessor::getSourceTotalPrice)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public BigDecimal getUnitAdjustmentFee() {
        return getItemProcessorList()
                .stream()
                .map(ItemProcessor::getUnitAdjustmentFee)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public BigDecimal getDiscountPrice(boolean isRefundScene) {
        if (!isAirPharmacy()) {
            return itemProcessorMap.values().stream()
                    .map(itemProcessor -> itemProcessor.getDiscountPrice(isRefundScene))
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        } else {
            BigDecimal discountPrice;
            switch (getStatus()) {
                case Constants.ChargeFormStatus.UNCHARGED:
                    if (isRefundScene) {
                        discountPrice = BigDecimal.ZERO;
                    } else {
                        discountPrice = chargeForm.getDiscountPrice();
                    }
                    break;
                case Constants.ChargeFormStatus.CHARGED:
                case Constants.ChargeFormStatus.REFUNDED:
                    discountPrice = chargeForm.getDiscountPrice();
                    break;
                default:
                    discountPrice = BigDecimal.ZERO;
            }
            return discountPrice;
        }
    }

    public BigDecimal getDiscountedPrice() {
        if (!isAirPharmacy()) {
            return itemProcessorMap.values().stream()
                    .filter(itemProcessor -> itemProcessor.getItemStatus() == Constants.ChargeFormItemStatus.CHARGED || itemProcessor.getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED)
                    .map(ItemProcessor::getDiscountedPrice)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        } else {
            return MathUtils.wrapBigDecimalAdd(chargeForm.getTotalPrice(), chargeForm.getDiscountPrice());
        }
    }

    public BigDecimal getRefundTotalPrice(boolean isRefundScene) {
        if (!isAirPharmacy()) {
            return itemProcessorMap.values().stream()
                    .map(ItemProcessor::getRefundTotalPrice)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        } else {
            if (isRefundScene) {
                if (refundChargeForm == null) {
                    return BigDecimal.ZERO;
                } else {
                    return refundChargeForm.getTotalPrice();
                }
            } else {
                return chargeForm.getRefundTotalPrice();
            }

        }

    }

    public BigDecimal getRefundDiscountPrice(boolean isRefundScene) {
        if (!isAirPharmacy()) {
            return itemProcessorMap.values().stream()
                    .map(ItemProcessor::getRefundDiscountPrice)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        } else {
            if (isRefundScene) {
                if (refundChargeForm == null) {
                    return BigDecimal.ZERO;
                } else {
                    return refundChargeForm.getDiscountPrice();
                }
            } else {
                return chargeForm.getRefundDiscountPrice();
            }
        }
    }

    public BigDecimal getSheBaoReceivableFee() {
        return itemProcessorMap.values().stream()
                .map(ItemProcessor::getSheBaoReceivableFee)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public BigDecimal getCouldDispensingCount() {
        if (isAirPharmacy()) {
            return BigDecimal.ZERO;
        } else {
            return itemProcessorMap.values().stream()
                    .map(ItemProcessor::getCouldDispensingCount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
    }

    private boolean isLocalChinesePrescription() {
        return getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE && getPharmacyType() == GoodsConst.PharmacyType.LOCAL_PHARMACY;
    }

    public BigDecimal getRefundDoseCount(BigDecimal doseCount) {
        if (!isLocalChinesePrescription()) {
            return null;
        }
        if (chargeForm.getStatus() == Constants.ChargeFormStatus.REFUNDED) {
            return doseCount;
        }
        if (CollectionUtils.isEmpty(itemProcessorMap)) {
            return BigDecimal.ZERO;
        }
        return itemProcessorMap.values().stream()
                .filter(itemProcessor -> itemProcessor.getItemV2Status() == Constants.ChargeFormItemStatus.CHARGED)
                .filter(itemProcessor -> itemProcessor.getRefundType() == Constants.ChargeFormItemRefundType.DOSE)
                .findFirst()
                .map(ItemProcessor::getRefundDoseCount)
                .orElse(BigDecimal.ZERO);
    }

    public BigDecimal getCouldExecuteUnitCount() {
        return itemProcessorMap.values().stream()
                .map(ItemProcessor::getCouldExecuteUnitCount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

//    public PriceCell getRegistrationPaidPriceCell() {
//        PriceCell priceCell = new PriceCell();
//        priceCell.totalPrice = itemProcessorMap.values().stream()
//                .filter(itemProcessor -> itemProcessor.getItemStatus() == Constants.ChargeFormItemStatus.CHARGED)
//                .filter(itemProcessor -> itemProcessor.getPaySource() == Constants.ChargeSource.REGISTRATION)
//                .map(itemProcessor -> itemProcessor.getTotalPrice(false))
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
//
//        priceCell.discountPrice = itemProcessorMap.values().stream()
//                .filter(itemProcessor -> itemProcessor.getItemStatus() == Constants.ChargeFormItemStatus.CHARGED)
//                .filter(itemProcessor -> itemProcessor.getPaySource() == Constants.ChargeSource.REGISTRATION)
//                .map(itemProcessor -> itemProcessor.getDiscountPrice(false))
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
//
//        priceCell.refundDiscountPrice = itemProcessorMap.values().stream()
//                .filter(itemProcessor -> itemProcessor.getItemStatus() == Constants.ChargeFormItemStatus.CHARGED)
//                .filter(itemProcessor -> itemProcessor.getPaySource() == Constants.ChargeSource.REGISTRATION)
//                .map(ItemProcessor::getRefundDiscountPrice)
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
//
//        priceCell.refundTotalPrice = itemProcessorMap.values().stream()
//                .filter(itemProcessor -> itemProcessor.getItemStatus() == Constants.ChargeFormItemStatus.CHARGED)
//                .filter(itemProcessor -> itemProcessor.getPaySource() == Constants.ChargeSource.REGISTRATION)
//                .map(ItemProcessor::getRefundTotalPrice)
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
//        return priceCell;
//    }

    public void checkProductInfoOrThrowException(int source, boolean enableNoStockGoods, Pair<Boolean, Integer> lockGoodsPair) throws ProductInfoChangedException {
        //校验中药处方的药品剂量是否都一致，如果不一致，则报错
        if (isAirPharmacy() || chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE) {
            Set<BigDecimal> medicineDoseCountSet = chargeForm.getChargeFormItems()
                    .stream()
                    .filter(chargeFormItem -> chargeFormItem.getIsDeleted() == 0)
                    .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.MEDICINE && chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.UNCHARGED)
                    .map(ChargeFormItem::getDoseCount)
                    .collect(Collectors.toSet());

            if (medicineDoseCountSet.size() > 1) {
                throw new ChargeServiceException(ChargeServiceError.PRESCRIPTION_DOSE_COUNT_ERROR);
            }
        }

        if (isAirPharmacy()) {
            return;
        }

        for (ItemProcessor itemProcessor : itemProcessorMap.values()) {
            itemProcessor.checkProductInfoOrThrowException(source, enableNoStockGoods, lockGoodsPair);
        }
    }

    public void checkPriceChangedAndThrowException() throws ProductInfoChangedException {
        for (ItemProcessor itemProcessor : itemProcessorMap.values()) {
            itemProcessor.checkPriceChangedAndThrowException();
        }
    }


    public ChargeForm generateToSaveChargeForm() {
        chargeForm.setChargeFormItems(itemProcessorMap.values().stream().flatMap(itemProcessor -> itemProcessor.generateToSaveChargeFormItems().stream()).collect(Collectors.toList()));
        return chargeForm;
    }

    public ChargeAirPharmacyLogistics generateToSaveChargeAirPharmacyLogistics() {
        if (chargeForm.getChargeAirPharmacyLogistics() != null) {
            return chargeForm.getChargeAirPharmacyLogistics();
        }
        return null;
    }

    public ChargeAirPharmacyMedicalRecord generateToSaveChargeAirPharmacyMedicalRecord() {
        if (chargeForm.getChargeAirPharmacyMedicalRecord() != null) {
            return chargeForm.getChargeAirPharmacyMedicalRecord();
        }
        return null;
    }

    public List<ChargeFormItem> getAffectedChargeFormItems() {
        return itemProcessorMap.values().stream().flatMap(itemProcessor -> itemProcessor.getAffectedItems().stream()).collect(Collectors.toList());
    }

    public List<ChargeFormItem> getRefundedChargeFormItems() {
        return itemProcessorMap.values().stream().flatMap(itemProcessor -> itemProcessor.getRefundChargeFormItems().stream()).collect(Collectors.toList());
    }

    public List<StatRecordAffectedDeductedItem> getStatRecordAffectedDeductedItems() {
        return itemProcessorMap.values().stream().map(ItemProcessor::getStatRecordAffectedDeductedItem)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    //包含所有子项
    public List<ChargeFormItem> getChargeFormItems() {
        return itemProcessorMap.values().stream().map(ItemProcessor::getAllChargeFormItems)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }


    public BigDecimal summaryTotalFee(SummaryFeeType type) {
        Stream<ItemProcessor> filteredItemProcessors = itemProcessorMap.values().stream();
        switch (type) {
            case REGISTRATION:
                filteredItemProcessors = filteredItemProcessors.filter(itemProcessor -> itemProcessor.getProductType() == Constants.ProductType.REGISTRATION);
                break;
            case CHINESE_MEDICINE:
                filteredItemProcessors = filteredItemProcessors.filter(itemProcessor -> itemProcessor.getProductType() == Constants.ProductType.MEDICINE)
                        .filter(itemProcessor -> itemProcessor.getProductSubType() == Constants.ProductType.SubType.MEDICINE_CHINESE);
                break;
            case WESTERN_MEDICINE:
                filteredItemProcessors = filteredItemProcessors.filter(itemProcessor -> itemProcessor.getProductType() == Constants.ProductType.MEDICINE)
                        .filter(itemProcessor -> itemProcessor.getProductSubType() == Constants.ProductType.SubType.MEDICINE_WESTERN ||
                                itemProcessor.getProductSubType() == Constants.ProductType.SubType.MEDICINE_CHINESE_COMPOSE);
                break;
            case EXAMINATION_TREATMENT:
                filteredItemProcessors = filteredItemProcessors.filter(itemProcessor -> itemProcessor.getProductType() == Constants.ProductType.TREATMENT || itemProcessor.getProductType() == Constants.ProductType.EXAMINATION);
                break;
            case EXAMINATION:
                filteredItemProcessors = filteredItemProcessors.filter(itemProcessor -> itemProcessor.getProductType() == Constants.ProductType.EXAMINATION);
                break;
            case TREATMENT:
                filteredItemProcessors = filteredItemProcessors.filter(itemProcessor -> itemProcessor.getProductType() == Constants.ProductType.TREATMENT);
                break;
            case MATERIAL:
                filteredItemProcessors = filteredItemProcessors.filter(itemProcessor -> itemProcessor.getProductType() == Constants.ProductType.MATERIAL);
                break;

        }
        return filteredItemProcessors.map(itemProcessor -> itemProcessor.getTotalPrice(false).subtract(itemProcessor.getRefundTotalPrice()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public BigDecimal summaryDiscountFee(SummaryFeeType type) {
        Stream<ItemProcessor> filteredItemProcessors = itemProcessorMap.values().stream();
        switch (type) {
            case REGISTRATION:
                filteredItemProcessors = filteredItemProcessors.filter(itemProcessor -> itemProcessor.getProductType() == Constants.ProductType.REGISTRATION);
                break;
            case CHINESE_MEDICINE:
                filteredItemProcessors = filteredItemProcessors.filter(itemProcessor -> itemProcessor.getProductType() == Constants.ProductType.MEDICINE)
                        .filter(itemProcessor -> itemProcessor.getProductSubType() == Constants.ProductType.SubType.MEDICINE_CHINESE);
                break;
            case WESTERN_MEDICINE:
                filteredItemProcessors = filteredItemProcessors.filter(itemProcessor -> itemProcessor.getProductType() == Constants.ProductType.MEDICINE)
                        .filter(itemProcessor -> itemProcessor.getProductSubType() == Constants.ProductType.SubType.MEDICINE_WESTERN ||
                                itemProcessor.getProductSubType() == Constants.ProductType.SubType.MEDICINE_CHINESE_COMPOSE);
                break;
            case EXAMINATION_TREATMENT:
                filteredItemProcessors = filteredItemProcessors.filter(itemProcessor -> itemProcessor.getProductType() == Constants.ProductType.TREATMENT || itemProcessor.getProductType() == Constants.ProductType.EXAMINATION);
                break;
            case TREATMENT:
                filteredItemProcessors = filteredItemProcessors.filter(itemProcessor -> itemProcessor.getProductType() == Constants.ProductType.TREATMENT);
                break;
            case EXAMINATION:
                filteredItemProcessors = filteredItemProcessors.filter(itemProcessor -> itemProcessor.getProductType() == Constants.ProductType.EXAMINATION);
                break;
            case MATERIAL:
                filteredItemProcessors = filteredItemProcessors.filter(itemProcessor -> itemProcessor.getProductType() == Constants.ProductType.MATERIAL);
                break;

        }
        return filteredItemProcessors.map(itemProcessor -> itemProcessor.getDiscountPrice(false).subtract(itemProcessor.getRefundDiscountPrice()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public ChargeFormPrintView generateMedicineMaterialProductPrintView(BiFunction<FormProcessor, ItemProcessor, ChargeFormItemPrintView> generateChargeFormItemPrintViewFunc) {
        ChargeFormPrintView chargeFormPrintView = new ChargeFormPrintView();
        chargeFormPrintView.setSourceFormType(chargeForm.getSourceFormType());
        chargeFormPrintView.setPrintFormType(chargeForm.getSourceFormType());
        //这三种类型，对前端都输出PRESCRIPTION_WESTERN
        if (chargeForm.getSourceFormType() == Constants.SourceFormType.ADDITIONAL_SALE_PRODUCT_FORM ||
                chargeForm.getSourceFormType() == Constants.SourceFormType.ADDITIONAL_FORM ||
                chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_INFUSION) {
            chargeFormPrintView.setSourceFormType(Constants.SourceFormType.PRESCRIPTION_WESTERN);
            chargeFormPrintView.setPrintFormType(Constants.PrintFormType.PRESCRIPTION_WESTERN);
        }

        List<ChargeFormItemPrintView> chargeFormItems = itemProcessorMap.values()
                .stream()
                //这里ADDITIONAL里面只输出药品，处方全部输出
                .filter(itemProcessor -> itemProcessor.enablePrintToClient(chargeForm.getSourceFormType())) //条件太复杂，重构成成员函数
                .sorted((a, b) -> ObjectUtils.compare(a.getSort(), b.getSort()))
                .map(itemProcessor -> generateChargeFormItemPrintViewFunc.apply(this, itemProcessor))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(chargeFormItems) && chargeForm.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_TYPE_EYE_GLASS) {
            return null;
        }

        chargeFormPrintView.setId(chargeForm.getId());
        chargeFormPrintView.setChargeFormItems(chargeFormItems);
        if (chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE || chargeForm.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY || chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_TYPE_EYE_GLASS) {
            UsageInfo usageInfo = JsonUtils.readValue(chargeForm.getUsageInfoJson(), UsageInfo.class);
            if (usageInfo != null) {
                BeanUtils.copyProperties(usageInfo, chargeFormPrintView);
            }
            if (chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE && Objects.nonNull(usageInfo)) {
                chargeFormPrintView.setGlassesParams(usageInfo.getGlassesParams());
            }
            if (chargeForm.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY
                    || (chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE && chargeForm.getPharmacyType() == GoodsConst.PharmacyType.VIRTUAL_PHARMACY)) {
                //空中药房的第一个药品
                int doseCount = 1;
                ChargeFormItemPrintView chargeFormItemPrintView = chargeFormItems.stream()
                        .filter(chargeFormItem -> chargeFormItem.getProductType() != Constants.ProductType.EXPRESS_DELIVERY && chargeFormItem.getProductType() != Constants.ProductType.PROCESS && chargeFormItem.getProductType() != Constants.ProductType.INGREDIENT)
                        .findFirst().orElse(null);
                if (chargeFormItemPrintView != null) {
                    doseCount = MathUtils.wrapBigDecimal(chargeFormItemPrintView.getDoseCount(), BigDecimal.ONE).intValue();
                }
                chargeFormPrintView.setDoseCount(doseCount);
                chargeFormPrintView.setSpecification(chargeForm.getSpecification());
            } else if (chargeForm.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_TYPE_EYE_GLASS) {
                // doseCount以第一个item上的为准
                chargeFormPrintView.setDoseCount(MathUtils.wrapBigDecimal(chargeFormItems.get(0).getDoseCount(), BigDecimal.ONE).intValue());
            }

            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeFormItems)) {
                chargeFormPrintView.setSpecification(chargeFormItems.stream()
                        .map(chargeFormItemPrintView -> {
                            if (chargeFormItemPrintView.getType() == ChargeFormItemPrintView.Type.CHINESE_MEDICINE_DRINKS_PIECE) {
                                return Constants.ProductType.MedicineChineseSpecification.DRINKS_PIECE;
                            }
                            if (chargeFormItemPrintView.getType() == ChargeFormItemPrintView.Type.CHINESE_MEDICINE_PARTICLE) {
                                return Constants.ProductType.MedicineChineseSpecification.PARTICLE;
                            }
                            return null;
                        })
                        .filter(Objects::nonNull)
                        .distinct()
                        .sorted()
                        .collect(Collectors.joining(","))
                );
            }
        }
        chargeFormPrintView.calculateTotalPrice();
        return chargeFormPrintView;
    }

    public void resetReceivedPrice() {
        if (isAirPharmacy()) {
            chargeForm.setReceivedPrice(BigDecimal.ZERO);
            return;
        }
        itemProcessorMap.values().forEach(ItemProcessor::resetReceivedPrice);
    }

    public void updateReceivedFeeForRefund(Map<String, BigDecimal> airPharmacyRecordIdMap) {
        if (!isAirPharmacy()) {
            return;
        }
        BigDecimal needSubtractReceivedPrice = airPharmacyRecordIdMap.getOrDefault(chargeForm.getId(), BigDecimal.ZERO);
        chargeForm.setReceivedPrice(MathUtils.max(BigDecimal.ZERO, MathUtils.wrapBigDecimalSubtract(chargeForm.getReceivedPrice(), needSubtractReceivedPrice)));
    }


    public void refundForGiftRule(String operatorId) {
        itemProcessorMap.values().stream()
                .filter(itemProcessor -> itemProcessor.getIsGift() == 1)
                .forEach(itemProcessor -> itemProcessor.refundForGiftRule(operatorId));
        if (chargeForm.getStatus() == Constants.ChargeFormStatus.CHARGED) {
            chargeForm.setStatus(Constants.ChargeFormStatus.REFUNDED);
            FillUtils.fillLastModifiedBy(chargeForm, operatorId);
        }
    }

    public void updateRefundInfoForGiftRule(String operatorId) {
        itemProcessorMap.values().forEach(itemProcessor -> itemProcessor.updateRefundInfoForGiftRule(operatorId));
    }

    /**
     * 新增或修改或删除空中药房的辅料费
     *
     * @param chargeFormRsp
     */
    public void insertOrUpdateAirPharmacyDeliveryProcessIngredientFormItem(CalculatePriceRsp.ChargeFormRsp chargeFormRsp, String operatorId) {
        if (chargeFormRsp == null) {
            return;
        }
        int isCanProcess = chargeFormRsp.getIsCanProcess();
        int isOpenIngredient = chargeFormRsp.getIsOpenIngredient();
        Supplier<ChargeFormItem> insertIngredientFormItemSupplier = () -> ChargeFormItemFactory.insertIngredientFormItem(chargeForm, BigDecimal.ZERO, operatorId);
        Supplier<ChargeFormItem> insertProcessFormItemSupplier = () -> ChargeFormItemFactory.insertProcessFormItem(chargeForm, BigDecimal.ZERO, operatorId);
        insertOrDeletedItem(isOpenIngredient == 1, Constants.ProductType.INGREDIENT, insertIngredientFormItemSupplier, operatorId);
        insertOrDeletedItem(isCanProcess == 1, Constants.ProductType.PROCESS, insertProcessFormItemSupplier, operatorId);
    }

    private void insertOrDeletedItem(boolean needInsert, int productType, Supplier<ChargeFormItem> insertSupplier, String operatorId) {
        if (productType != Constants.ProductType.INGREDIENT
                && productType != Constants.ProductType.PROCESS
                && productType != Constants.ProductType.EXPRESS_DELIVERY) {
            return;
        }
        ItemProcessor itemProcessor = itemProcessorMap.values()
                .stream()
                .filter(chargeFormItem -> chargeFormItem.getProductType() == productType)
                .findFirst()
                .orElse(null);
        if (needInsert) {
            if (itemProcessor == null) {
                ChargeFormItem chargeFormItem = insertSupplier.get();
                chargeForm.getChargeFormItems().add(chargeFormItem);
                itemProcessorMap.put(chargeFormItem.getId(), new ItemProcessor(chargeFormItem, chargeForm, toDeleteChargeSheetRelationDto));
            }
        } else {
            if (itemProcessor != null) {
                chargeForm.getChargeFormItems().removeIf(chargeFormItem -> {

                    if (!Objects.equals(itemProcessor.getItemId(), chargeFormItem.getId())) {
                        return false;
                    }

                    ChargeFormItem toDeleteItem = itemProcessor.getChargeFormItem();
                    toDeleteItem.deleteModel(operatorId);
                    toDeleteChargeSheetRelationDto.addChargeFormItemAndRelationData(toDeleteItem, isDeleted -> Objects.equals(isDeleted, 1));

                    return true;
                });
                itemProcessorMap.remove(itemProcessor.getItemId());
            }
        }
    }

    public void updateAirPharmacyDeliveryPriceAndProcessPriceAndIngredientPrice(CalculatePriceRsp.ChargeFormRsp chargeFormRsp) {
        if (chargeFormRsp == null) {
            return;
        }
        airPharmacyDeliveryRule = chargeFormRsp.getOrderDeliveryRule();
        airPharmacyProcessRule = chargeFormRsp.getOrderProcessRule();

        BigDecimal processPrice = chargeFormRsp.getProcessPrice();
        BigDecimal deliveryPrice = chargeFormRsp.getDeliveryPrice();
        BigDecimal processCostPrice = chargeFormRsp.getProcessCostPrice();
        BigDecimal deliveryCostPrice = chargeFormRsp.getDeliveryCostPrice();

        //辅料费，如果为null，表示没有辅料费用，如果有值，即使是0也要展示辅料费
        BigDecimal ingredientPrice = chargeFormRsp.getIngredientPrice();
        BigDecimal ingredientCostPrice = chargeFormRsp.getIngredientCostPrice();

        //空中药房如果是局部议价，处理要加价的值
        chargeForm.setExpectedPartialExtraPrice(processPrice.add(deliveryPrice).add(MathUtils.wrapBigDecimalOrZero(ingredientPrice)));
        itemProcessorMap.values()
                .stream()
                .filter(itemProcessor -> itemProcessor.getProductType() == Constants.ProductType.PROCESS
                        || itemProcessor.getProductType() == Constants.ProductType.EXPRESS_DELIVERY
                        || itemProcessor.getProductType() == Constants.ProductType.INGREDIENT)
                .forEach(itemProcessor -> {
                    /**
                     * 收费这里为什么不用判断空中药房是否有加工费？
                     * 因为端上已经保护了。保证会把不需要加工费的chargeFormItem插入进去
                     * */
                    if (itemProcessor.getProductType() == Constants.ProductType.PROCESS) {
                        itemProcessor.updateAirPharmacyProcessPrice(processPrice, airPharmacyProcessRule, processCostPrice);
                    } else if (itemProcessor.getProductType() == Constants.ProductType.EXPRESS_DELIVERY) {
                        itemProcessor.updateAirPharmacyDeliveryPrice(deliveryPrice, deliveryCostPrice);
                    } else if (itemProcessor.getProductType() == Constants.ProductType.INGREDIENT && chargeFormRsp.getIsOpenIngredient() == 1) {
                        itemProcessor.updateAirPharmacyIngredientPrice(ingredientPrice, Optional.ofNullable(airPharmacyProcessRule).map(OrderProcessRuleView::getIngredient).orElse(null), ingredientCostPrice);
                    }
                });
    }

    public GoodsBaseInfo generateGoodsBaseInfo() {
        ItemProcessor medicineItemProcessor = itemProcessorMap
                .values()
                .stream()
                .filter(itemProcessor -> itemProcessor.getProductType() == Constants.ProductType.MEDICINE && itemProcessor.getProductSubType() == Constants.ProductType.SubType.MEDICINE_CHINESE)
                .findFirst()
                .orElse(null);

        String goodsSpec = Optional.ofNullable(medicineItemProcessor).map(ItemProcessor::getGoodsCMSpec).orElse(chargeForm.getSpecification());
        if (medicineItemProcessor == null || org.apache.commons.lang3.StringUtils.isEmpty(goodsSpec)) {
            return null;
        }
        GoodsBaseInfo goodsBaseInfo = new GoodsBaseInfo();

        goodsBaseInfo.setGoodsType(medicineItemProcessor.getProductType());
        goodsBaseInfo.setGoodsSubType(medicineItemProcessor.getProductSubType());
        goodsBaseInfo.setGoodsCMSpec(goodsSpec);
        goodsBaseInfo.setPharmacyType(getPharmacyType());
        return goodsBaseInfo;
    }

    //空中药房chargeForm的处理逻辑

    /**
     * 前置条件：空中药房chargeForm
     * 这个函数的功能是计算这个chargeFrom除了挂号，快递，加工费的所有费用累加。其他描述信息使用的是第一个药品类型的描述信息
     */
    public CalculatePromotionAirPharmacyForm generateCalculatePromotionAirPharmacyForm() {
        CalculatePromotionAirPharmacyForm promotionAirPharmacyForm = new CalculatePromotionAirPharmacyForm();
        GoodsBaseInfo goodsBaseInfo = generateGoodsBaseInfo();
        if (goodsBaseInfo == null) {
            return null;
        }
        promotionAirPharmacyForm.setGoodsType(goodsBaseInfo.getGoodsType());
        promotionAirPharmacyForm.setGoodsSubType(goodsBaseInfo.getGoodsSubType());
        promotionAirPharmacyForm.setGoodsCMSpec(goodsBaseInfo.getGoodsCMSpec());
        //计算优惠的金额，去除快递费和加工费
        //TODO robinsli ？？这里是把所有费挂号费，快递费，加工费刨除的费用汇总，但是上面设置的goodType是第一个找到的？这里不会有数据对应问题
        promotionAirPharmacyForm.setTotalPrice(getPromotionTotalPrice());
        promotionAirPharmacyForm.setId(chargeForm.getId());
        if (isAirPharmacy()) {
            promotionAirPharmacyForm.setName(getAirPharmacyFormName());
            promotionAirPharmacyForm.setPharmacyType(GoodsConst.PharmacyType.AIR_PHARMACY);
        } else {
            promotionAirPharmacyForm.setName(String.format("虚拟药房订单，药房号: %d", getPharmacyNo()));
            promotionAirPharmacyForm.setPharmacyType(GoodsConst.PharmacyType.VIRTUAL_PHARMACY);
        }
        if (promotionAirPharmacyForm.getTotalPrice().compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }
        promotionAirPharmacyForm.setOriginalPrice(chargeForm.getExpectedTotalPrice() == null);
        return promotionAirPharmacyForm;
    }

    private BigDecimal getPromotionTotalPrice() {
        BigDecimal promotionPrice;
        BigDecimal calculatePromotionPrice = itemProcessorMap.values()
                .stream()
                .filter(itemProcessor -> !Constants.ProductType.SYSTEM_FEE_PRODUCT_TYPE.contains(itemProcessor.getProductType()))
                .filter(itemProcessor -> itemProcessor.getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED)
                .map(itemProcessor -> itemProcessor.getTotalPrice(false))//计算这个chargeformItem的总价
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (getPharmacyType() == GoodsConst.PharmacyType.VIRTUAL_PHARMACY) {
            return calculatePromotionPrice;
        }
        /**
         * 分几种情况：
         * 1、如果门诊没有议价，则直接取药品的总值
         * 2、如果有议价，判断议价是否小于加工费换个快递费的和，如果小于0，就用0，在金额不满足参与的折扣时，要把chargeForm上的promotionPrice
         */
        if (chargeForm.getExpectedTotalPrice() == null) {
            promotionPrice = calculatePromotionPrice;
        } else {
            promotionPrice = chargeForm.getExpectedTotalPrice();

            if (promotionPrice.compareTo(BigDecimal.ZERO) < 0) {
                promotionPrice = BigDecimal.ZERO;
            }
        }
        return promotionPrice;
    }

    private BigDecimal calculateFlatMedicinePrice(BigDecimal calculatePromotionPrice, BigDecimal expectedTotalPrice) {
        BigDecimal deliveryPrice = itemProcessorMap.values()
                .stream().filter(itemProcessor -> itemProcessor.getProductType() == Constants.ProductType.EXPRESS_DELIVERY)
                .filter(itemProcessor -> itemProcessor.getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED)
                .map(itemProcessor -> itemProcessor.getTotalPrice(false))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal processPrice = itemProcessorMap.values()
                .stream().filter(itemProcessor -> itemProcessor.getProductType() == Constants.ProductType.PROCESS)
                .filter(itemProcessor -> itemProcessor.getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED)
                .map(itemProcessor -> itemProcessor.getTotalPrice(false))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        List<FlatPriceHelper.FlatPriceCell> flatPriceCells = new ArrayList<>();
        Map<String, String> flatCellKeyMap = new HashMap<>();

        if (deliveryPrice != null && deliveryPrice.compareTo(BigDecimal.ZERO) > 0) {
            FlatPriceHelper.FlatPriceCell flatPriceCell = new FlatPriceHelper.FlatPriceCell();
            flatPriceCell.setId(AbcIdUtils.getUUID());
            flatPriceCell.setName(FlatCellKey.DELIVERY);
            flatPriceCell.setPriority(FlatPriceHelper.PRIORITY_LOW);
            flatPriceCell.setTotalPrice(deliveryPrice);
            flatCellKeyMap.put(flatPriceCell.getName(), flatPriceCell.getId());
            flatPriceCells.add(flatPriceCell);
        }

        if (processPrice != null && processPrice.compareTo(BigDecimal.ZERO) > 0) {
            FlatPriceHelper.FlatPriceCell flatPriceCell = new FlatPriceHelper.FlatPriceCell();
            flatPriceCell.setId(AbcIdUtils.getUUID());
            flatPriceCell.setName(FlatCellKey.PROCESS);
            flatPriceCell.setPriority(FlatPriceHelper.PRIORITY_LOW);
            flatPriceCell.setTotalPrice(processPrice);
            flatCellKeyMap.put(flatPriceCell.getName(), flatPriceCell.getId());
            flatPriceCells.add(flatPriceCell);
        }

        if (calculatePromotionPrice != null && calculatePromotionPrice.compareTo(BigDecimal.ZERO) > 0) {
            FlatPriceHelper.FlatPriceCell flatPriceCell = new FlatPriceHelper.FlatPriceCell();
            flatPriceCell.setId(AbcIdUtils.getUUID());
            flatPriceCell.setName(FlatCellKey.MEDICINE);
            flatPriceCell.setPriority(FlatPriceHelper.PRIORITY_LOW);
            flatPriceCell.setTotalPrice(calculatePromotionPrice);
            flatCellKeyMap.put(flatPriceCell.getName(), flatPriceCell.getId());
            flatPriceCells.add(flatPriceCell);
        }

        // 判断有没有药费，或者是不是只有药费
        if (!flatCellKeyMap.containsKey(FlatCellKey.MEDICINE)) {
            return BigDecimal.ZERO;
        }

        if (flatCellKeyMap.containsKey(FlatCellKey.MEDICINE) && flatPriceCells.size() == 1) {
            return expectedTotalPrice;
        }

        FlatPriceHelper flatPriceHelper = new FlatPriceHelper(expectedTotalPrice);
        flatPriceHelper.flat(flatPriceCells);

        FlatPriceHelper.FlatPriceCell medicineFlatPriceCell = flatPriceCells.stream().filter(flatPriceCell -> flatPriceCell.getName().equals(FlatCellKey.MEDICINE)).findFirst().orElse(null);

        return MathUtils.wrapBigDecimalOrZero(medicineFlatPriceCell.getFlatPrice());
    }

    public ChargeForm getChargeForm() {
        return chargeForm;
    }

    public void setDeliveryPrimaryFormId(String deliveryPrimaryFormId) {
        this.chargeForm.setDeliveryPrimaryFormId(deliveryPrimaryFormId);
    }

    public void updateChargeFormAirPharmacyLogisticsCompany(String companyId, String companyName) {
        ChargeAirPharmacyLogistics chargeAirPharmacyLogistics = this.chargeForm.getChargeAirPharmacyLogistics();

        if (Objects.isNull(chargeAirPharmacyLogistics)) {
            return;
        }
        ChargeAirPharmacyLogisticsReq.LogisticsCompanyReq companyReq = new ChargeAirPharmacyLogisticsReq.LogisticsCompanyReq();
        companyReq.setId(companyId);
        companyReq.setName(companyName);
        chargeAirPharmacyLogistics.setDeliveryCompany(companyReq);
    }

    public void calculateAirPharmacyTotalPrice() {
        // 首先加空中药房的折扣和议价全清空
        itemProcessorMap.values()
                .stream()
                .filter(itemProcessor -> itemProcessor.getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED)
                .filter(itemProcessor -> itemProcessor.getProductType() != Constants.ProductType.EXPRESS_DELIVERY
                        && itemProcessor.getProductType() != Constants.ProductType.PROCESS
                        && itemProcessor.getProductType() != Constants.ProductType.INGREDIENT)
                .forEach(ItemProcessor::clearAirPharmacyItemAdjustmentAndPromotion);
        /**
         * 金额分组，true表示系统费用的和(快递费+加工费+辅料费)，false表示药品金额
         */
        Map<Boolean, BigDecimal> partitioningByMedicalTotalPriceMap = itemProcessorMap.values().stream()
                .filter(itemProcessor -> itemProcessor.getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED)
                .collect(Collectors.partitioningBy(itemProcessor -> itemProcessor.getProductType() == Constants.ProductType.EXPRESS_DELIVERY
                                || itemProcessor.getProductType() == Constants.ProductType.PROCESS
                                || itemProcessor.getProductType() == Constants.ProductType.INGREDIENT,
                        Collectors.mapping(itemProcessor -> {
                            ChargeFormItem chargeFormItem = itemProcessor.getChargeFormItem();
                            return MathUtils.wrapBigDecimalOrZero(chargeFormItem.getUnitPrice())
                                    .multiply(MathUtils.calculateTotalCount(chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount()))
                                    .add(MathUtils.wrapBigDecimalOrZero(chargeFormItem.getFractionPrice()))
                                    .setScale(2, RoundingMode.HALF_UP);
                        }, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

        BigDecimal medicalTotalPrice = partitioningByMedicalTotalPriceMap.getOrDefault(Boolean.FALSE, BigDecimal.ZERO);
        BigDecimal systemProductTotalPrice = partitioningByMedicalTotalPriceMap.getOrDefault(Boolean.TRUE, BigDecimal.ZERO);

        //判断是否所有药品都反选了，如果全部反选，则totalPrice设置为0
        boolean isNotContainMedical = itemProcessorMap.values().stream()
                .filter(itemProcessor -> itemProcessor.getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED)
                .noneMatch(itemProcessor -> itemProcessor.getProductType() != Constants.ProductType.EXPRESS_DELIVERY
                        && itemProcessor.getProductType() != Constants.ProductType.PROCESS
                        && itemProcessor.getProductType() != Constants.ProductType.INGREDIENT);

        BigDecimal totalPrice = MathUtils.wrapBigDecimalAdd(medicalTotalPrice, systemProductTotalPrice);
        chargeForm.setSourceTotalPrice(totalPrice); //原价
        BigDecimal formMedicalAdjustmentFee = BigDecimal.ZERO;
        if (chargeForm.getExpectedTotalPrice() != null) {
            //将议价值平摊到空中药房的item上
            formMedicalAdjustmentFee = chargeForm.getExpectedTotalPrice().subtract(medicalTotalPrice);
            chargeForm.setTotalPrice(chargeForm.getExpectedTotalPrice().add(systemProductTotalPrice));
        } else {
            chargeForm.setTotalPrice(totalPrice);
        }
        updateAirPharmacyItemExpectedTotalPrice(formMedicalAdjustmentFee);
        if (isNotContainMedical) {
            chargeForm.setTotalPrice(BigDecimal.ZERO);
            chargeForm.setPromotionPrice(BigDecimal.ZERO);
            chargeForm.setPromotionInfo(null);
            chargeForm.setPromotionInfoJson("");
            chargeForm.setCouponPromotionInfo(null);
            chargeForm.setCouponPromotionInfoJson("");
            chargeForm.setGiftRulePromotionInfo(null);
            chargeForm.setGiftRulePromotionInfoJson("");
        }
        chargeForm.calculateDiscountedPrice();
    }

    private void updateAirPharmacyItemExpectedTotalPrice(BigDecimal adjustmentFee) {
        if (MathUtils.wrapBigDecimalOrZero(adjustmentFee).compareTo(BigDecimal.ZERO) == 0) {
            return;
        }
        List<SmartFlatPriceHelper.SmartFlatPriceCell> cells = itemProcessorMap.values().stream()
                .filter(itemProcessor -> itemProcessor.getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED)
                .filter(itemProcessor -> itemProcessor.getProductType() != Constants.ProductType.EXPRESS_DELIVERY
                        && itemProcessor.getProductType() != Constants.ProductType.PROCESS
                        && itemProcessor.getProductType() != Constants.ProductType.INGREDIENT)
                .map(itemProcessor -> new SmartFlatPriceHelper.SmartFlatPriceCell(itemProcessor.getItemId(),
                        itemProcessor.getName(),
                        itemProcessor.getTotalPrice(false),
                        itemProcessor.getProductType()))
                .collect(Collectors.toList());

        SmartFlatPriceHelper.flat(adjustmentFee, cells);
        Map<String, SmartFlatPriceHelper.SmartFlatPriceCell> smartFlatPriceCellMap = ListUtils.toMap(cells, SmartFlatPriceHelper.SmartFlatPriceCell::getId);
        itemProcessorMap.values()
                .forEach(itemProcessor -> {
                    SmartFlatPriceHelper.SmartFlatPriceCell cell = smartFlatPriceCellMap.getOrDefault(itemProcessor.getItemId(), null);
                    if (Objects.isNull(cell)) {
                        return;
                    }
                    itemProcessor.updateAirPharmacyItemExpectedTotalPrice(cell.getFlatPrice());
                });
    }

    public void checkMinimumOrThrowException() {
        // 校验空中药房是否满足起做量
        BigDecimal minimum = Optional.ofNullable(airPharmacyProcessRule).map(OrderProcessRuleView::getMinimum).orElse(null);
        if (minimum == null) {
            return;
        }
        boolean isNotContainMedical = itemProcessorMap.values().stream()
                .filter(itemProcessor -> itemProcessor.getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED)
                .noneMatch(itemProcessor -> itemProcessor.getProductType() != Constants.ProductType.EXPRESS_DELIVERY
                        && itemProcessor.getProductType() != Constants.ProductType.PROCESS
                        && itemProcessor.getProductType() != Constants.ProductType.INGREDIENT);
        if (isNotContainMedical) {
            return;
        }
        BigDecimal totalWeight = itemProcessorMap.values().stream()
                .filter(itemProcessor -> itemProcessor.getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED)
                .filter(itemProcessor -> itemProcessor.getProductType() != Constants.ProductType.EXPRESS_DELIVERY && itemProcessor.getProductType() != Constants.ProductType.PROCESS && itemProcessor.getProductType() != Constants.ProductType.INGREDIENT)
                .map(itemProcessor -> MathUtils.wrapBigDecimalOrZero(itemProcessor.getDoseCount()).multiply(MathUtils.wrapBigDecimalOrZero(itemProcessor.getUnitCount())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (MathUtils.wrapBigDecimalCompare(minimum, totalWeight) > 0) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "totalWeight小于minimum， totalWeight: {}, minimum: {}", totalWeight, minimum);
            throw new ChargeServiceException(ChargeServiceError.AIR_PHARMACY_UNDER_MINIMUM);
        }
    }

    public void checkAirPharmacyOrThrowException(int paySource) throws ChargeServiceException {
        // 校验是否缺药
        List<ItemProcessor> itemProcessors = itemProcessorMap.values().stream()
                .filter(itemProcessor -> itemProcessor.getProductType() != Constants.ProductType.INGREDIENT
                        && itemProcessor.getProductType() != Constants.ProductType.EXPRESS_DELIVERY
                        && itemProcessor.getProductType() != Constants.ProductType.PROCESS)
                .filter(itemProcessor -> itemProcessor.getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED)
                .filter(itemProcessor -> itemProcessor.getGoodsItem() == null).collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(itemProcessors)) {
            if (Constants.ChargeSource.patientPaySources().contains(paySource)) {
                throw new ChargeServiceException(ChargeServiceError.AIR_PHARMACY_LACK_GOODS);
            }
            String lockGoodsNameStr = itemProcessors.stream().map(itemProcessor -> String.format("【%s】", itemProcessor.getName())).collect(Collectors.joining(" "));
            throw new CisCustomException(ChargeServiceError.AIR_PHARMACY_LACK_GOODS.getCode(), String.format("空中药房处方中以下药品已下架，请更换药品后收费 %s", lockGoodsNameStr));
        }
    }

    public void checkAirPharmacyExpectedPriceAndUpdateTotalPrice() throws ChargeServiceException {
        boolean isNotContainMedical = itemProcessorMap.values().stream()
                .filter(itemProcessor -> itemProcessor.getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED)
                .noneMatch(itemProcessor -> itemProcessor.getProductType() != Constants.ProductType.EXPRESS_DELIVERY
                        && itemProcessor.getProductType() != Constants.ProductType.PROCESS
                        && itemProcessor.getProductType() != Constants.ProductType.INGREDIENT);
        boolean isAllUnitCountZero = itemProcessorMap.values().stream()
                .filter(itemProcessor -> itemProcessor.getProductType() != Constants.ProductType.PROCESS
                        && itemProcessor.getProductType() != Constants.ProductType.EXPRESS_DELIVERY
                        && itemProcessor.getProductType() != Constants.ProductType.INGREDIENT)
                .allMatch(itemProcessor -> itemProcessor.getUnitCount().compareTo(BigDecimal.ZERO) == 0);
        if ((isAllUnitCountZero || isNotContainMedical) && chargeForm.getExpectedTotalPrice() != null && chargeForm.getExpectedTotalPrice().compareTo(BigDecimal.ZERO) != 0) {
            log.error("form里面没有药品，并且议价值不为0,将议价值清空，totalPrice设置为0");
            chargeForm.setExpectedTotalPrice(null);
            chargeForm.setTotalPrice(BigDecimal.ZERO);
        }

    }

    public void checkAirPharmacyDeliveryAndProcessAndThrowException() {
        AirPharmacyProcessRuleReq clientProcessRule = chargeForm.getProcessRule();
        AirPharmacyDeliveryRuleReq clientDeliveryRule = chargeForm.getDeliveryRule();

        String clientProcessRuleId = clientProcessRule != null ? clientProcessRule.getId() : null;
        String airPharmacyProcessRuleId = airPharmacyProcessRule != null ? airPharmacyProcessRule.getId() : null;
        String clientDeliveryRuleId = clientDeliveryRule != null ? clientDeliveryRule.getId() : null;
        String airPharmacyDeliveryRuleId = airPharmacyDeliveryRule != null ? airPharmacyDeliveryRule.getId() : null;

        if (!Objects.equals(clientDeliveryRuleId, airPharmacyDeliveryRuleId)) {
            log.error("快递规则发生变化,chargeForm={} clientDeliveryRuleId: {}, airPharmacyDeliveryRuleId: {}", JsonUtils.dump(chargeForm), clientDeliveryRuleId, airPharmacyDeliveryRuleId);
            throw new AirPharmacyDeliveryOrProcessChangedException(chargeForm.getVendorName(), AirPharmacyDeliveryOrProcessChangedException.Type.DELIVERY);
        }

        if (!Objects.equals(clientProcessRuleId, airPharmacyProcessRuleId)) {
            log.error("加工规则发生变化, clientProcessRuleId: {}, airPharmacyProcessRuleId: {}", clientProcessRuleId, airPharmacyProcessRuleId);
            throw new AirPharmacyDeliveryOrProcessChangedException(chargeForm.getVendorName(), AirPharmacyDeliveryOrProcessChangedException.Type.PROCESS);
        }
    }

    public void updateTotalCostPrice() {
        if (!isAirPharmacy()) {
            return;
        }
        BigDecimal totalCostPrice = itemProcessorMap.values().stream()
                .filter(itemProcessor -> itemProcessor.getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED)
                .map(ItemProcessor::getTotalCostPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        chargeForm.setTotalCostPrice(totalCostPrice);
    }

    public enum SummaryFeeType {
        REGISTRATION,
        WESTERN_MEDICINE,
        CHINESE_MEDICINE,
        EXAMINATION_TREATMENT,
        TREATMENT,
        EXAMINATION,
        MATERIAL,
    }

    /**
     * 判断这个chargeFrom是否是处方，处方才能打印到终端
     * true 能打印
     */
    public boolean isMedicalToPrintToClient() {
        return this.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE ||
                this.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_WESTERN ||
                this.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_INFUSION ||
                this.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_EXTERNAL ||
                this.getSourceFormType() == Constants.SourceFormType.ADDITIONAL_FORM ||
                this.getSourceFormType() == Constants.SourceFormType.ADDITIONAL_SALE_PRODUCT_FORM ||
                this.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY ||
                this.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_TYPE_EYE_GLASS ||
                this.getSourceFormType() == Constants.SourceFormType.GIFT_PRODUCT;
    }

    /**
     * 处理空中药房开的剂量为0的退款处理
     */
    public boolean zeroUnitCanBeRefund() {
        if (isAirPharmacy()) {
            // 检查选中剂量为0
            return getChargeForm()
                    .getChargeFormItems()
                    .stream()
                    .filter(item -> item.getIsDeleted() == 0)
                    .filter(chargeFormItem -> (chargeFormItem.getProductType() != Constants.ProductType.EXPRESS_DELIVERY
                            && chargeFormItem.getProductType() != Constants.ProductType.PROCESS)
                            && chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED
                    )
                    .allMatch(chargeFormItem -> chargeFormItem.getUnitCount().compareTo(BigDecimal.ZERO) == 0);
        }
        return false;
    }

    public List<FlatPriceHelper.FlatPriceCell> generateFlatPriceCellsForRefreshReceivedFee() {
        if (isAirPharmacy()) {
            FlatPriceHelper.FlatPriceCell flatPriceCell = new FlatPriceHelper.FlatPriceCell();
            flatPriceCell.setId(getFormId());
            flatPriceCell.setPriority(FlatPriceHelper.PRIORITY_HIGH);
            flatPriceCell.setTotalPrice(chargeForm.getTotalPrice().add(chargeForm.getDiscountPrice()));
            return Collections.singletonList(flatPriceCell);
        }
        return itemProcessorMap.values().stream().map(ItemProcessor::generateFlatPriceCellForRefreshReceivedFee)
                .collect(Collectors.toList());
    }

    public boolean checkVirtualDeliveryChanged() {
        if (chargeForm.getChargeAirPharmacyLogistics() == null || chargeForm.getDbChargeAirPharmacyLogistics() == null) {
            return true;
        }
        ChargeAirPharmacyLogistics chargeAirPharmacyLogistics = chargeForm.getChargeAirPharmacyLogistics();
        ChargeAirPharmacyLogistics dbChargeAirPharmacyLogistics = chargeForm.getDbChargeAirPharmacyLogistics();
        return !org.apache.commons.lang3.StringUtils.equals(chargeAirPharmacyLogistics.getAddressProvinceId(), dbChargeAirPharmacyLogistics.getAddressProvinceId())
                || !org.apache.commons.lang3.StringUtils.equals(chargeAirPharmacyLogistics.getAddressCityId(), dbChargeAirPharmacyLogistics.getAddressCityId())
                || !org.apache.commons.lang3.StringUtils.equals(chargeAirPharmacyLogistics.getAddressDistrictId(), dbChargeAirPharmacyLogistics.getAddressDistrictId())
                || !org.apache.commons.lang3.StringUtils.equals(chargeAirPharmacyLogistics.getDeliveryCompanyId(), dbChargeAirPharmacyLogistics.getDeliveryCompanyId());

    }

    public boolean checkFormIsNotRefund() {
        return chargeForm.getStatus() == Constants.ChargeFormStatus.CHARGED && refundChargeForm == null;
    }

    public void updateDefaultPharmacyNoForCopyByOutpatient(GoodsPharmacyBaseView defaultPharmacy) {
        itemProcessorMap.values()
                .stream()
                .filter(itemProcessor -> itemProcessor.getProductType() == Constants.ProductType.MEDICINE)
                .forEach(itemProcessor -> itemProcessor.updateDefaultPharmacyNoForCopyByOutpatient(defaultPharmacy));
    }

    public void updateVirtualPharmacyFormItemPharmacyTypeAndPharmacyNo() {
        itemProcessorMap.values()
                .stream()
                .filter(itemProcessor -> itemProcessor.getProductType() == Constants.ProductType.MEDICINE)
                .forEach(itemProcessor -> itemProcessor.updatePharmacyTypeAndPharmacyNo(chargeForm.getPharmacyType(), chargeForm.getPharmacyNo()));
    }

    public void setGoodsPharmacyView(Map<Integer, GoodsPharmacyView> goodsPharmacyViewMap) {
        itemProcessorMap.values()
                .forEach(itemProcessor -> itemProcessor.setGoodsPharmacyView(goodsPharmacyViewMap));
    }

    public void updatePreBatchInfo(GoodsLockingForm goodsLockingForm) {
        getItemProcessorList()
                .forEach(itemProcessor -> {
                    Map<String, GoodsLockingFormItem> goodsLockingFormItemMap = ListUtils.toMap(goodsLockingForm.getLockingFormItems(), GoodsLockingFormItem::getLockFormItemId);

                    if (goodsLockingFormItemMap.get(itemProcessor.getItemId()) != null) {
                        itemProcessor.updatePreBatchInfo(goodsLockingFormItemMap.get(itemProcessor.getItemId()));
                    }
                });
    }


    public List<GoodsLockBatchItem> getPreLockBatchInfo() {
        return getItemProcessorList().stream().flatMap(itemProcessor -> itemProcessor.getPreLockBatchInfo().stream()).collect(Collectors.toList());
    }

    public void applyChargeFormItemPromotionInfoUnitAdjustmentFee() {
        if (isAirPharmacy()) {
            return;
        }
        getItemProcessorList().forEach(ItemProcessor::applyChargeFormItemPromotionInfoUnitAdjustmentFee);
    }

    public void updateRegistrationForm(RegistrationForm registrationForm, String operatorId) {
        if (Objects.isNull(registrationForm) || CollectionUtils.isEmpty(registrationForm.getRegistrationFormItems())) {
            return;
        }
        List<RegistrationFormItem> formItems = registrationForm.getRegistrationFormItems();
        Map<String, RegistrationFormItem> registrationFormItemMap = ListUtils.toMap(formItems, RegistrationFormItem::getId);
        getItemProcessorList().forEach(item -> {
            RegistrationFormItem registrationFormItem = registrationFormItemMap.get(item.getSourceFormItemId());
            if (Objects.isNull(registrationFormItem)) {
                return;
            }
            item.updateRegistrationFormItem(registrationFormItem, chargeForm, operatorId);
        });
    }

    public void updateGoodsFeeTypeView(Map<Long, GoodsFeeTypeConstants.GoodsFeeTypeSortDto> feeTypeSortDtoMap) {
        if (MapUtils.isEmpty(feeTypeSortDtoMap)) {
            return;
        }
        getItemProcessorList().forEach(itemProcessor -> itemProcessor.updateGoodsFeeTypeView(feeTypeSortDtoMap));
    }

    /*public boolean checkDispensingFormAuditCompound(ChargeConfigDetailView configDetailView, boolean throwException) {
        if (Objects.isNull(configDetailView) || CollectionUtils.isEmpty(getDispensingFormInfoList())) {
            return false;
        }
        boolean auditOrCompound = false;
        if (configDetailView.getRefundRestriction() == ChargeCalculateConfig.RefundRestriction.AUDIT) {
            // 审核
            if (getDispensingFormInfoList().stream().anyMatch(it -> it.getAuditedStatus() == DispenseConst.AuditedStatus.SUCCEED)) {
                auditOrCompound = true;
                if (throwException) {
                    throw new ChargeServiceException(ChargeServiceError.NEED_REQUIRED_PARAMETER.getCode(),
                            "处方已审核，若需退费请先至药房撤销审核");
                }
            }
        } else if (configDetailView.getRefundRestriction() == ChargeCalculateConfig.RefundRestriction.COMPOUND) {
            // 调配
            if (getDispensingFormInfoList().stream().anyMatch(it -> it.getCompoundedStatus() == DispenseConst.CompoundedStatus.SUCCEED)) {
                auditOrCompound = true;
                if (throwException) {
                    throw new ChargeServiceException(ChargeServiceError.NEED_REQUIRED_PARAMETER.getCode(),
                            "处方已调配，若需退费请先至药房撤销调配");
                }
            }
        }
        return auditOrCompound;
    }*/

    public boolean checkDispensingFormAuditCompound(ChargeConfigDetailView configDetailView, boolean isWholeSheetRefundCheck, boolean throwException) {
        if (Objects.isNull(configDetailView) || CollectionUtils.isEmpty(getDispensingFormInfoList())) {
            return false;
        }
        boolean auditOrCompound = false;
        if (configDetailView.getRefundRestriction() == ChargeCalculateConfig.RefundRestriction.AUDIT) {
            // 审核
            if (getDispensingFormInfoList().stream().anyMatch(it -> it.getAuditedStatus() == DispenseConst.AuditedStatus.SUCCEED)) {
                auditOrCompound = true;
                if (throwException) {
                    if (isWholeSheetRefundCheck) {
                       throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_NOT_SUPPORT_REFUND_BY_PART_DISPENSING, "审核", "撤销审核");
                    } else {
                        throw new ChargeServiceException(ChargeServiceError.NEED_REQUIRED_PARAMETER.getCode(), "处方已审核，若需退费请先至药房撤销审核");
                    }
                }
            }
        } else if (configDetailView.getRefundRestriction() == ChargeCalculateConfig.RefundRestriction.COMPOUND) {
            // 调配
            if (getDispensingFormInfoList().stream().anyMatch(it -> it.getCompoundedStatus() == DispenseConst.CompoundedStatus.SUCCEED)) {
                auditOrCompound = true;
                if (throwException) {
                    if (isWholeSheetRefundCheck) {
                        throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_NOT_SUPPORT_REFUND_BY_PART_DISPENSING, "调配", "撤销调配");
                    } else {
                        throw new ChargeServiceException(ChargeServiceError.NEED_REQUIRED_PARAMETER.getCode(), "处方已调配，若需退费请先至药房撤销调配");
                    }
                }
            }
        }
        return auditOrCompound;
    }

    public void clearLimitInfo() {
        itemProcessorMap.values().forEach(ItemProcessor::clearLimitInfo);
    }

    public BigDecimal getPromotionPrice() {
        return getItemProcessorList().stream().map(itemProcessor -> itemProcessor.getPromotionPrice()).reduce(BigDecimal.ZERO, MathUtils::wrapBigDecimalAdd);
    }

    public void clearListingDiscountFee() {
        itemProcessorMap.values().forEach(ItemProcessor::clearListingDiscountFee);
    }

}
