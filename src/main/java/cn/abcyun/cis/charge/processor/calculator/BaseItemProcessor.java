package cn.abcyun.cis.charge.processor.calculator;

import cn.abcyun.cis.charge.base.ChargeVersionConstants;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.model.ChargeFormItem;
import cn.abcyun.cis.charge.processor.PharmacyItemProcessor;
import cn.abcyun.cis.charge.service.dto.ChargeSheetRelationDto;
import cn.abcyun.cis.charge.util.ChargeFormItemUtils;
import cn.abcyun.cis.commons.model.ComposeType;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;

public class BaseItemProcessor {

    protected ChargeFormItem chargeFormItem;

    protected ChargeSheetRelationDto toDeleteChargeSheetRelationDto;

    protected int chargeVersion;

    protected List<PharmacyItemProcessor> composeChildren = new ArrayList<>();

    protected List<ChargeFormItem> refundChargeFormItems = new ArrayList<>();

    public int getItemStatus() {
        return chargeFormItem.getStatus();
    }

    public boolean isParent() {
        return chargeFormItem.isParentItem();
    }

    public boolean isCompose() {
        return chargeFormItem.getComposeType() == ComposeType.COMPOSE;
    }

    public ItemCalculator getItemCalculator() {
        if (chargeVersion == ChargeVersionConstants.V0) {
            //todo 待实现
            return null;
        } else if (chargeVersion == ChargeVersionConstants.V1) {
            return isRefundScene -> doCalculateEachFeeFuncCore(isRefundScene, ChargeFormItemUtils::calculateTotalPriceView);

        }
        throw new RuntimeException("itemCalculator is null");
    }


    public BigDecimal doCalculateEachFeeFuncCore(boolean isRefundScene, Function<ChargeFormItem, BigDecimal> calculatePriceFunction) {
        BigDecimal fee = BigDecimal.ZERO;

        if (getItemStatus() == Constants.ChargeFormItemStatus.UNSELECTED) {
            return fee;
        }

        if (isParent()) {
            return composeChildren.stream().map(child -> child.doCalculateEachFeeFuncCore(isRefundScene, calculatePriceFunction)).reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        switch (getItemStatus()) {
            case Constants.ChargeFormItemStatus.UNCHARGED:
                if (isRefundScene) {
                    fee = BigDecimal.ZERO;
                } else {
                    fee = calculatePriceFunction.apply(chargeFormItem);
                }
                break;
            case Constants.ChargeFormItemStatus.CHARGED:
                fee = calculatePriceFunction.apply(chargeFormItem);
                break;
            default:
                fee = BigDecimal.ZERO;
        }
        return fee;
    }

    public BigDecimal doCalculateEachRefundFeeFuncCore(Function<ChargeFormItem, BigDecimal> calculateRefundPriceFunction) {
        if (isParent()) {
            return composeChildren.stream().map(child -> child.doCalculateEachRefundFeeFuncCore(calculateRefundPriceFunction)).reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        BigDecimal refundFee = BigDecimal.ZERO;
        if (getItemStatus() != Constants.ChargeFormItemStatus.CHARGED) {
            return refundFee;
        }
        refundFee = refundChargeFormItems.stream().map(calculateRefundPriceFunction).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        return refundFee;
    }

}
