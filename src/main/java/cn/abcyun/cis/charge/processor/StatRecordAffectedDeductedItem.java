package cn.abcyun.cis.charge.processor;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
public class StatRecordAffectedDeductedItem {

    private String id;

    private BigDecimal count;

    /**
     * 单价（正值）
     */
    private BigDecimal unitPrice;

    private BigDecimal deductedUnitAdjustmentFee;

    /**
     * 抵扣总金额（负值）
     * 注意：抵扣总金额不一定等于 单价*数量，如果是费用母项，这里的总金额就等于每个费用子项的抵扣总金额汇总之后的值，如果直接用母项的单价*数量会出现精度问题
     */
    private BigDecimal totalPrice;

    private BigDecimal totalCostPrice;

    List<StatRecordAffectedDeductedItemChild> deductInfo;



    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class StatRecordAffectedDeductedItemChild extends StatRecordAffectedDeductedItem {

        /**
         * {@link cn.abcyun.cis.charge.model.ChargeTransactionRecordAdditional.DeductType}
         */
        private int type;
    }

}
