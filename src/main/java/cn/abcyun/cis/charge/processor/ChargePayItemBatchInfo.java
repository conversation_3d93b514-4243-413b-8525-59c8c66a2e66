package cn.abcyun.cis.charge.processor;

import cn.abcyun.cis.charge.util.ChargeFormItemUtils;
import cn.abcyun.cis.commons.util.ListUtils;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Data
@Accessors(chain = true)
public class ChargePayItemBatchInfo {

    private String id;


    private String batchId;


    private String batchNo;

    /**
     * 应收
     */
    private BigDecimal receivableFee;

    /**
     * 本次应收
     */
    private BigDecimal thisTimeReceivableFee;


    public static void flatChargePayItemInfos(List<ChargePayItemBatchInfo> chargePayItemBatchInfos, BigDecimal thisTimeAmount) {

        List<FlatReceivedPriceHelper.FlatCell> flatCells = chargePayItemBatchInfos.stream()
                .map(payItemBatchInfo -> new FlatReceivedPriceHelper.FlatCell()
                        .setId(payItemBatchInfo.getId())
                        .setTotalPrice(payItemBatchInfo.getReceivableFee())
                        .setMaxFlatPrice(payItemBatchInfo.getReceivableFee()))
                .collect(Collectors.toList());

        FlatReceivedPriceHelper flatReceivedPriceHelper = new FlatReceivedPriceHelper(thisTimeAmount);
        flatReceivedPriceHelper.flat(flatCells);
        Map<String, FlatReceivedPriceHelper.FlatCell> flatPriceCellMap = ListUtils.toMap(flatCells, FlatReceivedPriceHelper.FlatCell::getId);

        Set<String> chargePayItemInfoIds = flatPriceCellMap.keySet();
        chargePayItemBatchInfos.stream()
                .filter(chargePayItemBatchInfo -> chargePayItemInfoIds.contains(chargePayItemBatchInfo.getId()))
                .forEach(chargePayItemBatchInfo -> {
                    FlatReceivedPriceHelper.FlatCell flatPriceCell = flatPriceCellMap.get(chargePayItemBatchInfo.getId());

                    if (flatPriceCell == null) {
                        return;
                    }
                    chargePayItemBatchInfo.setThisTimeReceivableFee(flatPriceCell.getFlatPrice());

                });
    }
}
