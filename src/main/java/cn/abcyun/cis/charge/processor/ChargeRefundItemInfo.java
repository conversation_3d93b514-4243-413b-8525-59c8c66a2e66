package cn.abcyun.cis.charge.processor;

import cn.abcyun.cis.charge.util.MathUtils;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class ChargeRefundItemInfo {

    /**
     * 如果是空中药房，则是chargeForm的id，如果不是空中药房，则是item的id
     */
    private String id;

    private int isAirPharmacy;

    private String name;

    private String associateFormItemId;

    private String composeParentFormItemId;

    /**
     * 折后金额
     */
    private BigDecimal discountedPrice;

    /**
     * 已收
     */
    private BigDecimal receivedFee;

    /**
     * 本次退费平摊的最大值，不能超过这个值
     */
    private BigDecimal thisTimeMaxRefundFee;

    /**
     * 本次应退金额
     */
    private BigDecimal thisTimeRefundFee;

    public BigDecimal getThisTimeRefundFee() {
        return MathUtils.wrapBigDecimalOrZero(thisTimeRefundFee);
    }
}
