package cn.abcyun.cis.charge.processor.provider;

import cn.abcyun.cis.charge.service.dto.DispensingSheetInfo;
import cn.abcyun.cis.commons.exception.ServiceInternalException;

import java.util.List;

public interface DispensingInfoProvider {

    DispensingSheetInfo findDispensingInfo(String chargeSheetId) throws ServiceInternalException;

    /**
     * 查询发药信息
     * @param chargeSheetId
     * @return
     */
    List<DispensingSheetInfo> getDispensingSheetInfoListBySourceSheetId(String chargeSheetId);


    List<cn.abcyun.bis.rpc.sdk.cis.model.dispensing.DispensingSheet> getDispensingSheets(String chargeSheetId);
}
