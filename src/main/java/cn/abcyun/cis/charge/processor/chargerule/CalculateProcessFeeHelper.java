package cn.abcyun.cis.charge.processor.chargerule;

import cn.abcyun.cis.charge.api.model.ChargeRuleLadderInfoVo;
import cn.abcyun.cis.charge.api.model.ChargeRuleProcessUsageView;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.model.ChargeRuleLadderInfo;
import cn.abcyun.cis.charge.model.ChargeSheetProcessInfo;
import cn.abcyun.cis.commons.util.MathUtils;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.function.BiFunction;

import static cn.abcyun.cis.charge.processor.chargerule.CalculateExpressDeliveryFeeHelper.G_UNIT;

public class CalculateProcessFeeHelper {

    private ChargeRuleProcessUsageView processUsage;
    private int type;
    private int subType;

    private BigDecimal ladderTotalCount;


    public CalculateProcessFeeHelper(ChargeRuleProcessResult.ChargeRuleProcessRsp chargeRuleProcess) {
        if (chargeRuleProcess != null) {
            processUsage = chargeRuleProcess.getProcessUsage();
            type = chargeRuleProcess.getType();
            subType = chargeRuleProcess.getSubType();
        }
    }

    @Data
    @Accessors(chain = true)
    public static class CalculateProcessFeeCell {
        private String name;

        private String unit;

        private int productType;

        private int productSubType;

        private BigDecimal unitCount;

        private BigDecimal doseCount;
    }


    public CalculateProcessFeeResult calculateProcessFee(List<CalculateProcessFeeCell> cells, ChargeSheetProcessInfo processInfo) {
        CalculateProcessFeeResult result = new CalculateProcessFeeResult();
        BigDecimal fee = BigDecimal.ZERO;
        String ruleInfo = null;
        if (processInfo == null || processUsage == null) {
            return result;
        }

        int ruleType = processUsage.getRuleType();
        switch (ruleType) {
            case Constants.ChargeRuleType.SETTLED_RULE:
                fee = processUsage.getPrice();
                break;
            case Constants.ChargeRuleType.LADDER_RULE:
                fee = calculate(cells, processUsage.getLadderInfo(), processInfo);
                break;
            default:
                fee = BigDecimal.ZERO;
        }


        fee = MathUtils.wrapBigDecimalAdd(fee, calculateOverFulfilFee(cells, processInfo));

        result.setFee(fee);
        result.setRuleInfo(processUsage.getRuleInfo());
        return result;
    }

    //计算超额费用
    private BigDecimal calculateOverFulfilFee(List<CalculateProcessFeeCell> cells, ChargeSheetProcessInfo processInfo) {
        BigDecimal overFulfilFee = BigDecimal.ZERO;
        if (processUsage.getOverFulfilSwitch() == 1) {
            BigDecimal limitTotalBagCount = calculateDoseCount(cells).multiply(new BigDecimal(processUsage.getOverFulfilUnitCount()));
            BigDecimal totalBagCount = ChargeRuleLadderInfoEnum.PROCESS_BAG.getLadderTotalCountFunction().apply(cells, processInfo);
            BigDecimal overTotalBagCount = MathUtils.wrapBigDecimalSubtract(totalBagCount, limitTotalBagCount);
            if (overTotalBagCount.compareTo(BigDecimal.ZERO) > 0) {
                int overFlag = overTotalBagCount.divide(new BigDecimal(processUsage.getOverFulfilAdditionalCount()), 0, BigDecimal.ROUND_UP).intValue();
                overFulfilFee = processUsage.getOverFulfilAdditionalPrice().multiply(new BigDecimal(overFlag));
            }

        }
        return overFulfilFee;
    }

    private BigDecimal calculate(List<CalculateProcessFeeCell> cells, ChargeRuleLadderInfoVo ladderInfo, ChargeSheetProcessInfo processInfo) {
        BigDecimal fee = BigDecimal.ZERO;
        if (ladderInfo == null) {
            return BigDecimal.ZERO;
        }

        ChargeRuleLadderInfoEnum chargeRuleLadderInfoEnum = ChargeRuleLadderInfoEnum.getChargeRuleLadderInfoEnumByType(ladderInfo.getType());

        if (chargeRuleLadderInfoEnum != null) {
            BigDecimal ladderTotalCount = chargeRuleLadderInfoEnum.getLadderTotalCountFunction().apply(cells, processInfo);

            if (chargeRuleLadderInfoEnum == ChargeRuleLadderInfoEnum.MEDICINE_WEIGHT) {
                if (G_UNIT.equals(ladderInfo.getUnit())) {
                    ladderTotalCount = ladderTotalCount != null ? ladderTotalCount.multiply(new BigDecimal(1000)) : null;
                }
            }

            this.ladderTotalCount = ladderTotalCount;
            fee = calculateFeeByLadderRule(ladderTotalCount, ladderInfo);
        }

        return fee;
    }

    public static BigDecimal calculateFeeByLadderRule(BigDecimal ladderTotalCount, ChargeRuleLadderInfoVo ladderInfo) {
        BigDecimal fee = BigDecimal.ZERO;
        if (ladderTotalCount != null && ladderTotalCount.compareTo(ladderInfo.getUnitCount()) <= 0 && ladderTotalCount.compareTo(BigDecimal.ZERO) >= 0) {
            fee = ladderInfo.getPrice();
        } else if (ladderTotalCount != null && ladderTotalCount.compareTo(ladderInfo.getUnitCount()) > 0) {
            BigDecimal flag = MathUtils.wrapBigDecimalSubtract(ladderTotalCount, ladderInfo.getUnitCount());
            // 2021.10.15需求，新增取整规则
            int additionalFlag = flag.divide(ladderInfo.getAdditionalCount(), 0, ladderInfo.getRoundType() == ChargeRuleLadderInfoVo.RoundType.ROUND_UP ? BigDecimal.ROUND_UP : BigDecimal.ROUND_DOWN).intValue();

            fee = MathUtils.wrapBigDecimalAdd(ladderInfo.getPrice(),
                    MathUtils.wrapBigDecimalMultiply(ladderInfo.getAdditionalPrice(), new BigDecimal(additionalFlag)));
        }
        return fee;
    }


    public enum ChargeRuleLadderInfoEnum {

        //1：处方剂数，2：药品数量，3：加工袋数
        PRESCRIPTION_DOSAGE(ChargeRuleLadderInfo.Type.PRESCRIPTION_DOSAGE, (cells, processInfo) -> calculateDoseCount(cells)),
        MEDICINE_WEIGHT(ChargeRuleLadderInfo.Type.MEDICINE_WEIGHT, (cells, processInfo) -> {
            BigDecimal ladderTotalCount = BigDecimal.ZERO;

            if (cells == null) {
                return BigDecimal.ZERO;
            }

            ladderTotalCount = cells.stream()
                    .filter(cell -> cell.getProductType() == Constants.ProductType.MEDICINE)
                    .map(cell -> {
                        if ("g".equals(cell.getUnit())) {

                            BigDecimal unitCount = cell.getUnitCount();
                            BigDecimal doseCount = cell.getDoseCount();
                            if (doseCount == null || doseCount.compareTo(BigDecimal.ONE) < 0) {
                                doseCount = BigDecimal.ONE;
                            }
                            if (unitCount == null) {
                                unitCount = BigDecimal.ZERO;
                            }
                            return unitCount.multiply(doseCount).divide(new BigDecimal(1000));
                        }
                        return BigDecimal.ZERO;
                    })
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            return ladderTotalCount;
        }),
        PROCESS_BAG(ChargeRuleLadderInfo.Type.PROCESS_BAG, (cells, processInfo) -> {

            BigDecimal totalBagCount = BigDecimal.ZERO;

            if (CollectionUtils.isEmpty(cells) || processInfo == null) {
                return BigDecimal.ZERO;
            }

            if (processInfo.getTotalProcessCount() != null) {
                totalBagCount = processInfo.getTotalProcessCount();
            } else {
                totalBagCount = calculateDoseCount(cells).multiply(processInfo.getBagUnitCountDecimal());
            }
            return totalBagCount;
        });


        private int type;
        private BiFunction<List<CalculateProcessFeeCell>, ChargeSheetProcessInfo, BigDecimal> ladderTotalCountFunction;

        ChargeRuleLadderInfoEnum(int type, BiFunction<List<CalculateProcessFeeCell>, ChargeSheetProcessInfo, BigDecimal> ladderTotalCountFunction) {
            this.type = type;
            this.ladderTotalCountFunction = ladderTotalCountFunction;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public BiFunction<List<CalculateProcessFeeCell>, ChargeSheetProcessInfo, BigDecimal> getLadderTotalCountFunction() {
            return ladderTotalCountFunction;
        }

        public void setLadderTotalCountFunction(BiFunction<List<CalculateProcessFeeCell>, ChargeSheetProcessInfo, BigDecimal> ladderTotalCountFunction) {
            this.ladderTotalCountFunction = ladderTotalCountFunction;
        }

        public static ChargeRuleLadderInfoEnum getChargeRuleLadderInfoEnumByType(int type) {

            for (ChargeRuleLadderInfoEnum value : ChargeRuleLadderInfoEnum.values()) {
                if (value.getType() == type) {
                    return value;
                }
            }
            return null;
        }
    }

    public static BigDecimal calculateDoseCount(List<CalculateProcessFeeCell> cells) {
        BigDecimal totalDoseCount = BigDecimal.ZERO;

        if (CollectionUtils.isEmpty(cells)) {
            return BigDecimal.ZERO;
        }

        totalDoseCount = MathUtils.wrapBigDecimalOrZero(cells.get(0).getDoseCount());
        return totalDoseCount;
    }

}
