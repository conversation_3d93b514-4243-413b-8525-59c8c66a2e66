package cn.abcyun.cis.charge.processor.discount;

import cn.abcyun.cis.charge.util.MathUtils;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class ApplyPromotionInfoResult {

    private BigDecimal adjustmentFeeOnCalculate = BigDecimal.ZERO;

    public BigDecimal getAdjustmentFeeOnCalculate() {
        return MathUtils.wrapBigDecimalOrZero(adjustmentFeeOnCalculate);
    }
}
