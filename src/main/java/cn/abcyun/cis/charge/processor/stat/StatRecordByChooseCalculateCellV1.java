package cn.abcyun.cis.charge.processor.stat;

import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.model.ChargeTransactionRecord;
import cn.abcyun.cis.commons.util.MathUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 新的算费规则，对应
 * {@link cn.abcyun.cis.charge.base.ChargeVersionConstants.V1}
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class StatRecordByChooseCalculateCellV1 extends StatRecordByChooseCalculateCell {

    @Override
    public BigDecimal getDiscountedPrice() {
        if (isAirPharmacy == 0) {
            return MathUtils.wrapBigDecimalOrZero(chargeFormItem.calculateDiscountedPriceV2());
        } else {
            throw new RuntimeException("空中药房待考虑");
        }
    }

    /**
     * 除了抵扣之外的金额
     * @return
     */
    @Override
    public BigDecimal getRecordedDiscountedPrice() {
        return historyRecords.stream()
                .map(record -> {
                    BigDecimal flag = BigDecimal.ONE;
                    if (isAirPharmacy == 0) {
                        //退费项的flag永远=1，收费项才区分正负
                        flag = chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.REFUNDED && record.getChargeType() == ChargeTransactionRecord.ChargeType.REFUND ? BigDecimal.ONE.negate() : BigDecimal.ONE;
                    } else {
                        flag = chargeForm.getStatus() != Constants.ChargeFormStatus.REFUNDED && record.getChargeType() == ChargeTransactionRecord.ChargeType.REFUND ? BigDecimal.ONE.negate() : BigDecimal.ONE;
                    }

                    return flag.multiply(record.getReceivedPrice());
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public BigDecimal getDiscountPrice() {

        if (isAirPharmacy == 0) {
            return MathUtils.wrapBigDecimalAdd(chargeFormItem.getPromotionPrice(), chargeFormItem.getUnitAdjustmentFee(), chargeFormItem.getAdjustmentPrice());
        } else {
            throw new RuntimeException("空中药房待考虑");
        }
    }
}
