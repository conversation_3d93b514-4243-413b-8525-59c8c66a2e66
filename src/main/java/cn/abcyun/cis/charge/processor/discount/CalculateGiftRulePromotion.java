package cn.abcyun.cis.charge.processor.discount;

import cn.abcyun.cis.charge.service.dto.GiftRulePromotionView;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class CalculateGiftRulePromotion {

    private String promotionId;

    private Integer promotionParentType;

    private String promotionName;

    //正值
    private BigDecimal orderThresholdPrice;

    //负值，在赋值的时候就转换为了负值
    private BigDecimal discountPrice;

    private List<String> chargeFormItemIds;

    private List<String> airPharmacyChargeFormIds;

    private List<GiftCouponView> giftCoupons = new ArrayList<>();//赠送优惠券

    private List<GiftRulePromotionView.GiftGoodsItemView> giftGoodItems; //赠送服务

    private HitGiftRule hitGiftRule;

    @Data
    @Accessors(chain = true)
    public static class HitGiftRule {
        /**
         * 满
         */
        private BigDecimal orderThresholdPrice;
        /**
         * 减
         */
        private BigDecimal discountedPrice;
        /**
         * 是否循环
         */
        private int isCycle;
    }
}
