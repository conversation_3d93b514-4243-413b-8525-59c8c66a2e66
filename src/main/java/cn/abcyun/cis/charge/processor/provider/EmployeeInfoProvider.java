package cn.abcyun.cis.charge.processor.provider;


import cn.abcyun.bis.rpc.sdk.cis.model.clinic.ChainEmployee;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Employee;

import java.util.List;
import java.util.Map;

public interface EmployeeInfoProvider {
    Employee findById(String chainId, String id);

    @Deprecated
    List<Employee> findEmployeeList(String chainId, List<String> employeeIds);

    Map<String, String> findEmployeeIdNameMap(String chainId, List<String> employeeIds);

    ChainEmployee getChainEmployee(String sellerId, String chainId);
}
