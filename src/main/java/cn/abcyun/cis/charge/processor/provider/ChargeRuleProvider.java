package cn.abcyun.cis.charge.processor.provider;

import cn.abcyun.cis.charge.model.ChargeRuleExpressDelivery;
import cn.abcyun.cis.charge.processor.chargerule.ChargeRuleProcessResult;
import cn.abcyun.cis.charge.processor.chargerule.FindChargeRuleProcessesInfo;
import cn.abcyun.cis.charge.service.dto.BasicUsageTypeInfo;
import cn.abcyun.cis.charge.service.dto.UsageTypeInfo;

import java.util.List;

public interface ChargeRuleProvider {

    List<ChargeRuleExpressDelivery> findChargeRuleExpressDelivery(String chainId, String clinicId, String addressProvinceId, String addressCityId, String addressDistrictId, String deliveryCompanyId, int deliveryPayType);

    ChargeRuleProcessResult findChargeRuleProcesses(String chainId, String clinicId, List<FindChargeRuleProcessesInfo> processesInfos);

    List<UsageTypeInfo> findAvailableUsages(String clinicId);

    //填充加工类型的名字
    List<BasicUsageTypeInfo> getAllUsageTypeInfos();
}
