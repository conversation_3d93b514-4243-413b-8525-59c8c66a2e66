package cn.abcyun.cis.charge.processor.stat.calculator;

import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.cis.charge.helper.ChargeTransactionRecordDiscountInfoHelper;
import cn.abcyun.cis.charge.model.ChargeTransaction;
import cn.abcyun.cis.charge.processor.FlatReceivedPriceHelper;
import cn.abcyun.cis.charge.processor.limitprice.BaseLimitPriceInfo;
import cn.abcyun.cis.charge.processor.stat.StatRecordBatchInfoCalculateCell;
import cn.abcyun.cis.charge.processor.stat.StatRecordByChooseCalculateCell;
import cn.abcyun.cis.charge.service.dto.ChargeDiscountInfo;
import cn.abcyun.cis.charge.util.FlatPriceTool;
import cn.abcyun.cis.charge.util.JsonUtils;
import cn.abcyun.cis.charge.util.ListUtils;
import cn.abcyun.cis.charge.util.MathUtils;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
public class StatRecordPartedPaidBackCalculator extends StatRecordAbstractCalculator implements IStatRecordBatchInfoCalculator {
    @Override
    public BigDecimal flatCell(ChargeTransaction chargeTransaction, List<StatRecordByChooseCalculateCell> cells, BigDecimal toRecordPrice, int refundFlatType) {
        //已经记录了的record的总金额
        BigDecimal recordedDiscountedPriceSum = cells.stream()
                .map(StatRecordByChooseCalculateCell::getRecordedDiscountedPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        //表示本次退完
        if (recordedDiscountedPriceSum.compareTo(toRecordPrice) <= 0) {
            for (StatRecordByChooseCalculateCell cell : cells) {
                cell.setToRecordDiscountedPrice(cell.getRecordedDiscountedPrice());
                cell.setToRecordTotalCostPrice(cell.getRecordedTotalCostPrice());
                cell.setToRecordDiscountPrice(cell.getRecordedDiscountPrice());
                cell.setToRecordPromotionDiscountInfo(ChargeTransactionRecordDiscountInfoHelper.generateToRecordChargeTransactionRecordDiscountInfoForLastRecord(cell.getItemPromotionInfo(), cell.getRecordedChargeTransactionRecordDiscountInfo()));
            }
        } else {

            //本次退费为选择项目之后改小了金额，可能为全部项目的部分退费，也可能为部分项目的部分退费，需要将toRecordPrice进行平摊
            FlatReceivedPriceHelper flatReceivedPriceHelper = new FlatReceivedPriceHelper(toRecordPrice);

            List<FlatReceivedPriceHelper.FlatCell> flatCells = cells
                    .stream()
                    .map(calculateCell -> {
                        //可记录的总金额
                        BigDecimal recordedDiscountedPrice = calculateCell.getRecordedDiscountedPrice();
                        return new FlatReceivedPriceHelper.FlatCell()
                                .setId(calculateCell.getId())
                                .setTotalPrice(recordedDiscountedPrice)
                                .setMaxFlatPrice(recordedDiscountedPrice);
                    }).collect(Collectors.toList());
            flatReceivedPriceHelper.flat(flatCells);

            Map<String, FlatReceivedPriceHelper.FlatCell> flatCellMap = ListUtils.toMap(flatCells, FlatReceivedPriceHelper.FlatCell::getId);

            cells.forEach(cell -> {
                FlatReceivedPriceHelper.FlatCell flatCell = flatCellMap.getOrDefault(cell.getId(), null);
                BigDecimal toRecordDiscountedPrice = Optional.ofNullable(flatCell).map(FlatReceivedPriceHelper.FlatCell::getFlatPrice).orElse(BigDecimal.ZERO);
                cell.setToRecordDiscountedPrice(toRecordDiscountedPrice);
            });

            IStatRecordCalculator.fillOtherToRecordFields(cells);
        }

        return BigDecimal.ZERO;
    }

    @Override
    public void flatCellBatchInfo(List<StatRecordByChooseCalculateCell> cells) {
        for (StatRecordByChooseCalculateCell cell : cells) {
            super.doCalculateBatchInfo(cell, this);
        }
    }

    @Override
    public void flatCellBatchInfoDiscountedPrice(StatRecordByChooseCalculateCell calculateCell) {

        FlatPriceTool.flatReceivedPriceAndApply(calculateCell.getToRecordDiscountedPrice(), calculateCell.getBatchInfoCalculateCells()
                .stream()
                .map(batchInfoCalculateCell -> new FlatPriceTool.IFlatCell<FlatReceivedPriceHelper.FlatCell>() {
                    @Override
                    protected FlatReceivedPriceHelper.FlatCell genFlatCell() {
                        return new FlatReceivedPriceHelper.FlatCell()
                                .setId(batchInfoCalculateCell.getId())
                                .setTotalPrice(batchInfoCalculateCell.getRecordedDiscountedPrice())
                                .setMaxFlatPrice(batchInfoCalculateCell.getRecordedDiscountedPrice());
                    }

                    @Override
                    protected void apply(BigDecimal flatPrice) {
                        batchInfoCalculateCell.setToRecordDiscountedPrice(flatPrice);
                    }
                }).collect(Collectors.toList())
        );
    }

    @Override
    public void flatCellBatchInfoDiscountInfo(StatRecordByChooseCalculateCell calculateCell) {
        ChargeDiscountInfo toRecordPromotionDiscountInfo = calculateCell.getToRecordDiscountInfoResult();
        if (Objects.isNull(toRecordPromotionDiscountInfo)) {
            return;
        }

        ChargeDiscountInfo upperLimitPromotionInfo = toRecordPromotionDiscountInfo;
        //顺序，整单议价 -> 折扣 -> 单项议价
        for (StatRecordBatchInfoCalculateCell batchInfoCalculateCell : calculateCell.getBatchInfoCalculateCells()) {
            ChargeDiscountInfo leftToRecordPromotionInfo = batchInfoCalculateCell.getRecordedPromotionInfo();

            if (MathUtils.wrapBigDecimalCompare(batchInfoCalculateCell.getDiscountedPrice(), BigDecimal.ZERO) == 0) {
                batchInfoCalculateCell.setToRecordPromotionDiscountInfo(leftToRecordPromotionInfo);
                continue;
            }

            ChargeTransactionRecordDiscountInfoHelper.FlatBatchInfoPromotionInfoResult flatBatchInfoPromotionInfoResult = ChargeTransactionRecordDiscountInfoHelper.flatBatchInfoPromotionInfo(
                    upperLimitPromotionInfo,
                    batchInfoCalculateCell.getPromotionInfo(),
                    leftToRecordPromotionInfo,
                    batchInfoCalculateCell.getToRecordDiscountedPrice(),
                    batchInfoCalculateCell.getDiscountedPrice(),
                    Optional.ofNullable(batchInfoCalculateCell.getChargeFormItemBatchInfo().getLimitInfo()).map(BaseLimitPriceInfo.LimitInfo::getExceedLimitPriceRule).orElse(YesOrNo.YES) == YesOrNo.NO);
            batchInfoCalculateCell.setToRecordPromotionDiscountInfo(flatBatchInfoPromotionInfoResult.getBatchPromotionInfo());
            batchInfoCalculateCell.setToRecordSourceTotalPrice(flatBatchInfoPromotionInfoResult.getSourceTotalPrice());

            //merge upperLimitDiscountInfo
            upperLimitPromotionInfo = ChargeTransactionRecordDiscountInfoHelper.mergeLeftPromotionInfo(upperLimitPromotionInfo, flatBatchInfoPromotionInfoResult.getBatchPromotionInfo());
        }

        //如果还有剩余，则从第一个批次把零头放进去
        if (upperLimitPromotionInfo != null && !upperLimitPromotionInfo.isDiscountPriceEmpty()) {

            for (StatRecordBatchInfoCalculateCell batchInfoCalculateCell : calculateCell.getBatchInfoCalculateCells()) {

                ChargeDiscountInfo leftToRecordPromotionInfo = batchInfoCalculateCell.getLeftToRecordPromotionInfo();

                BigDecimal fractionPrice = ChargeTransactionRecordDiscountInfoHelper.addFractionPromotionInfo(leftToRecordPromotionInfo, batchInfoCalculateCell.getToRecordPromotionDiscountInfo(), upperLimitPromotionInfo, batchInfoCalculateCell.getToRecordSourceTotalPrice());

                batchInfoCalculateCell.setToRecordSourceTotalPrice(MathUtils.wrapBigDecimalSubtract(batchInfoCalculateCell.getToRecordSourceTotalPrice(), fractionPrice));

                if (upperLimitPromotionInfo.isDiscountPriceEmpty()) {
                    break;
                }
            }
        }

        //校验平摊的金额是否正确
        BigDecimal itemPromotionAllPrice = toRecordPromotionDiscountInfo.calculateAllPrice();
        BigDecimal batchPromotionAllPrice = calculateCell.getBatchInfoCalculateCells()
                .stream()
                .map(StatRecordBatchInfoCalculateCell::getToRecordPromotionDiscountInfo)
                .filter(Objects::nonNull)
                .map(ChargeDiscountInfo::calculateAllPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (MathUtils.wrapBigDecimalCompare(itemPromotionAllPrice, batchPromotionAllPrice) != 0) {
            log.error("批次平摊优惠信息失败, itemPromotionAllPrice: {}, batchPromotionAllPrice: {}, calculateCell: {}", itemPromotionAllPrice, batchPromotionAllPrice, JsonUtils.dump(calculateCell));
            throw new IllegalStateException("批次平摊优惠信息失败");
        }
    }

    @Override
    public void fillOtherFields(StatRecordByChooseCalculateCell calculateCell) {
        BigDecimal upperLimitTotalPrice = calculateCell.getToRecordTotalCostPrice();
        for (StatRecordBatchInfoCalculateCell batchInfoCalculateCell : calculateCell.getBatchInfoCalculateCells()) {
            BigDecimal discountedPrice = batchInfoCalculateCell.getDiscountedPrice();
            BigDecimal totalCostPrice = batchInfoCalculateCell.getTotalCostPrice();
            BigDecimal recordedTotalCostPrice = batchInfoCalculateCell.getRecordedTotalCostPrice();
            BigDecimal toRecordTotalCostPrice = BigDecimal.ZERO;
            if (discountedPrice.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }

            toRecordTotalCostPrice = MathUtils.wrapBigDecimalOrZero(totalCostPrice).multiply(batchInfoCalculateCell.getToRecordDiscountedPrice()).divide(discountedPrice, 2, RoundingMode.UP);
            toRecordTotalCostPrice = MathUtils.min(toRecordTotalCostPrice, upperLimitTotalPrice);
            toRecordTotalCostPrice = MathUtils.min(toRecordTotalCostPrice, recordedTotalCostPrice);

            batchInfoCalculateCell.setToRecordTotalCostPrice(toRecordTotalCostPrice);
            upperLimitTotalPrice = upperLimitTotalPrice.subtract(toRecordTotalCostPrice);
        }

        BigDecimal batchTotalCostPrice = calculateCell.getBatchInfoCalculateCells()
                .stream()
                .map(StatRecordBatchInfoCalculateCell::getToRecordTotalCostPrice)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        //这里直接用批次的成本价总和覆盖item的成本价
        calculateCell.setToRecordTotalCostPrice(batchTotalCostPrice);
    }



}
