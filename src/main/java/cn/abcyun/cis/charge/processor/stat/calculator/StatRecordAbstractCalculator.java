package cn.abcyun.cis.charge.processor.stat.calculator;

import cn.abcyun.cis.charge.model.ChargeTransactionRecord;
import cn.abcyun.cis.charge.processor.stat.StatRecordBatchInfoCalculateCell;
import cn.abcyun.cis.charge.processor.stat.StatRecordByChooseCalculateCell;

import java.math.BigDecimal;

public abstract class StatRecordAbstractCalculator implements IStatRecordCalculator {

    protected void doCalculateBatchInfo(StatRecordByChooseCalculateCell calculateCell, IStatRecordBatchInfoCalculator batchInfoCalculator) {

        if (!calculateCell.canDealBatchInfo()) {
            return;
        }

        fillBatchInfoCount(calculateCell);
        /**
         * 等式为：原价 + 单项议价 + 营销折扣（包含多个维度的营销信息） + 整单议价 = 实收
         * 平摊顺序
         * 1、先平摊实收，得到每个批次本次记录的实收金额，去计算比例
         * 2、discountInfo，这里面包含单项议价，营销折扣，整单议价
         * 3、计算原价 = 实收 - 整单议价 - 营销折扣 - 单项议价
         * fixme 抵扣要怎么处理？目前未考虑有批次的情况下抵扣怎么平摊
         */

        //1、平摊实收
        batchInfoCalculator.flatCellBatchInfoDiscountedPrice(calculateCell);

        //2、平摊discountInfo，同时计算原价
        batchInfoCalculator.flatCellBatchInfoDiscountInfo(calculateCell);

        //3、填充其他字段，目前只有成本价
        batchInfoCalculator.fillOtherFields(calculateCell);
    }

    protected boolean canDealBatchInfo(StatRecordByChooseCalculateCell calculateCell) {
        return calculateCell.canDealBatchInfo();
    }

    protected void fillBatchInfoCount(StatRecordByChooseCalculateCell calculateCell) {
        int toRecordSceneType = calculateCell.getToRecordSceneType();
        for (StatRecordBatchInfoCalculateCell batchInfoCalculateCell : calculateCell.getBatchInfoCalculateCells()) {
            BigDecimal toRecordUnitCount = BigDecimal.ZERO;
            if (toRecordSceneType == ChargeTransactionRecord.SceneType.NORMAL) {
                toRecordUnitCount = batchInfoCalculateCell.getUnitCount();
            }
            batchInfoCalculateCell.setToRecordUnitCount(toRecordUnitCount);
        }
    }
}