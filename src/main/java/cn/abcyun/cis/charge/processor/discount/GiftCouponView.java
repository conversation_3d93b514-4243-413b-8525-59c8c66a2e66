package cn.abcyun.cis.charge.processor.discount;

import cn.abcyun.bis.rpc.sdk.cis.model.promotion.Promotion;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.time.Instant;

@Data
public class GiftCouponView{
    private String promotionId;
    private String name;
    //单次满减送额count
    private int unitCount;

    private int count;
    //一共赠送的代金券的张数
    private int totalCount;

    private String reason;

    private Integer validDays;  //优惠券有效天数
    private Instant validEnd;   //优惠券有效期结束
    private int validType = 0;  //过期类型：0相对过期时间；1绝对过期时间

    @JsonIgnore
    private int leftTotalCount;

    @JsonIgnore
    private Promotion.PromotionDetail.GiftRule.GiftCoupon giftCoupon;
}
