package cn.abcyun.cis.charge.processor;
/***
 * SheetProcessor输出给终端协议
 * 本类应该放到Protocol目录下的，鉴于要访问SheetProcessor里面太多成员变量，不想把这些成员暴露出来，放到了同SheetProcessor同目录
 * 利用包内可见，解决访问问题
 * 2020。8只是将代码从SheetProcosser移动到本类
 * 主要对外的三个接口：
 *      toRegistrationChargeSheetView
 *      toPrintView
 *      toSheetView
 * <AUTHOR> 2020.08
 * */

import cn.abcyun.bis.rpc.sdk.bis.model.order.Constant;
import cn.abcyun.bis.rpc.sdk.bis.model.order.CreateOrderView;
import cn.abcyun.bis.rpc.sdk.bis.model.vendor.BusinessScopeVO;
import cn.abcyun.bis.rpc.sdk.cis.model.charge.GoodsFeeType;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.*;
import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.bis.rpc.sdk.cis.model.dispensing.DispensingSheet;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GetDispenseGoodsStockInfoReq;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GetDispenseGoodsStockInfoRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsSystemType;
import cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientPointsChargeCalculateRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.promotion.PatientCardView;
import cn.abcyun.bis.rpc.sdk.property.model.MedicalDocumentsTreatmentContent;
import cn.abcyun.bis.rpc.sdk.property.model.PrintMedicalDocumentsInfusion;
import cn.abcyun.cis.charge.api.model.*;
import cn.abcyun.cis.charge.base.ChargeServiceError;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.base.GoodsFeeTypeConstants;
import cn.abcyun.cis.charge.combinorder.model.ChargeCombineOrderTransaction;
import cn.abcyun.cis.charge.controller.StatusNameTranslator;
import cn.abcyun.cis.charge.model.*;
import cn.abcyun.cis.charge.processor.calculator.ChargeSheetPrintPriceInfo;
import cn.abcyun.cis.charge.processor.discount.GiftCouponView;
import cn.abcyun.cis.charge.processor.provider.ChargePayProvider;
import cn.abcyun.cis.charge.processor.provider.PropertyProvider;
import cn.abcyun.cis.charge.protocol.frontend.ChargeFormProtocol;
import cn.abcyun.cis.charge.rpc.model.ChargeSheetTraceCodesRsp;
import cn.abcyun.cis.charge.service.dto.*;
import cn.abcyun.cis.charge.service.dto.print.*;
import cn.abcyun.cis.charge.util.*;
import cn.abcyun.cis.commons.exception.ServiceInternalException;
import cn.abcyun.cis.commons.model.ComposeType;
import cn.abcyun.cis.commons.rpc.crm.PatientInfo;
import cn.abcyun.cis.commons.rpc.outpatient.MedicalRecord;
import cn.abcyun.cis.commons.rpc.outpatient.UsageInfo;
import cn.abcyun.cis.commons.rpc.patient.MemberInfo;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrder;
import cn.abcyun.cis.commons.rpc.pay.PayStatus;
import cn.abcyun.cis.commons.rpc.print.OrganPrintView;
import cn.abcyun.cis.commons.rpc.shebao.QueryChargeSheetShebaoInfoReq;
import cn.abcyun.cis.commons.rpc.shebao.QueryChargeSheetShebaoInfoRsp;
import cn.abcyun.cis.commons.util.TextUtils;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;

public class ChargeSheetFeeProtocol {
    private static final Logger sLogger = LoggerFactory.getLogger(ChargeSheetFeeProtocol.class);

    /**
     * 打印View
     * 前置：chargeSheet已经完成了算费
     */
    public static ChargeSheetPrintView toPrintView(SheetProcessor sheetProcessor) {
        //初始化
        ChargeSheet chargeSheet = sheetProcessor.chargeSheet;
        SheetProcessorInfoProvider sheetProcessorInfoProvider = sheetProcessor.sheetProcessorInfoProvider;
        PatientInfo patientInfo = sheetProcessor.getPatientInfo();
        List<FormProcessor> formProcessors = sheetProcessor.formProcessors;
        BigDecimal adjustmentFee = sheetProcessor.adjustmentFee;
        BigDecimal refundAdjustmentFee = sheetProcessor.refundAdjustmentFee;
        BigDecimal totalFee = sheetProcessor.totalFee;
        BigDecimal refundTotalFee = sheetProcessor.refundTotalFee;
        BigDecimal refundDiscountFee = sheetProcessor.refundDiscountFee;
        BigDecimal discountFee = sheetProcessor.discountFee;
        BigDecimal _refundFee = sheetProcessor._refundFee;
        BigDecimal _receivedFee = sheetProcessor._receivedFee;
        PatientOrder patientOrder = sheetProcessor.patientOrder;
        sheetProcessor.updateGoodsFeeTypeView();
        //查询收费单对应的欠费数据
        List<ChargeOweSheet> chargeOweSheets = new ArrayList<>();
        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED) {
            chargeOweSheets = Optional.ofNullable(sheetProcessorInfoProvider).map(SheetProcessorInfoProvider::getChargeOweSheetProvider)
                    .map(chargeOweSheetProvider -> chargeOweSheetProvider.findChargeOweSheetsByChargeSheetId(chargeSheet.getClinicId(), chargeSheet.getId())).orElse(new ArrayList<>());
        }

        cn.abcyun.bis.rpc.sdk.cis.model.clinic.OrganPrintView organ = sheetProcessorInfoProvider.getProductInfoProvider().getOrganPrintInfo(chargeSheet.getClinicId());


        ChargeSheetPrintView chargeSheetPrintView = new ChargeSheetPrintView();

        chargeSheetPrintView.setId(chargeSheet.getId());
        chargeSheetPrintView.setPatient(patientInfo);
        chargeSheetPrintView.setOrgan(organ != null ? new OrganPrintView(organ.getId(), organ.getName(), organ.getName(), organ.getAddressDetail(), organ.getContactPhone(), organ.getLogo(), organ.getCategory(), organ.getHisType()) : null);
        chargeSheetPrintView.setDiagnosedDate(chargeSheet.getDiagnosedDate());
        chargeSheetPrintView.setDiagnosis(Optional.ofNullable(chargeSheet.getAdditional()).map(ChargeSheetAdditional::getDiagnosis).orElse(null));
        chargeSheetPrintView.setDiagnosisInfos(Optional.ofNullable(chargeSheet.getAdditional()).map(ChargeSheetAdditional::getDiagnosisInfos).orElse(new ArrayList<>()));
        chargeSheetPrintView.setExtendDiagnosisInfos(Optional.ofNullable(chargeSheet.getAdditional()).map(ChargeSheetAdditional::getExtendDiagnosisInfos).orElse(new ArrayList<>()));
        chargeSheetPrintView.setOwedStatus(chargeSheet.getOwedStatus());
        chargeSheetPrintView.addChargeFormPrintViews(generateChargeFormPrintViews(formProcessors, adjustmentFee, refundAdjustmentFee, Collections.singletonList(Constants.ChargeFormItemStatus.CHARGED), Optional.ofNullable(organ).map(cn.abcyun.bis.rpc.sdk.cis.model.clinic.OrganPrintView::getHisType).orElse(0)));
        chargeSheetPrintView.setMemberInfo(generateMemberPrintInfo(chargeSheet, sheetProcessorInfoProvider));

        // 找到挂号处收费已支付金额信息
//        PriceCell registrationPriceCell = getRegistrationPaidPriceCell();
        BigDecimal netAdjustmentFee = adjustmentFee.subtract(refundAdjustmentFee);

        chargeSheetPrintView.setTotalFee(MathUtils.setScaleTwo(totalFee.subtract(refundTotalFee).add(netAdjustmentFee)));
        chargeSheetPrintView.setDiscountFee(MathUtils.setScaleTwo(discountFee.subtract(refundDiscountFee)));
        chargeSheetPrintView.setReceivableFee(MathUtils.setScaleTwo(MathUtils.wrapBigDecimalAdd(chargeSheetPrintView.getTotalFee(), chargeSheetPrintView.getDiscountFee())));
        chargeSheetPrintView.setNetIncomeFee(MathUtils.setScaleTwo(_receivedFee.add(_refundFee)));

        ChargeTransactionAndActionDto transactionAndActionDto = getChargeTransactionAndChargeActionContainOwe(chargeSheet, chargeOweSheets);

        if (transactionAndActionDto.getLastOweSheet() != null && transactionAndActionDto.getLastOweSheet().getStatus() == ChargeOweSheet.Status.CHARGED) {
            chargeSheetPrintView.setLatestOwePaidTime(transactionAndActionDto.getLastOweSheet().getLatestPayTime());
        }


        List<ChargeTransaction> chargeTransactions = transactionAndActionDto.getChargeTransactions();
        List<ChargeAction> chargeActions = transactionAndActionDto.getChargeActions();
        List<String> availableActionIds = chargeTransactions.stream()
                .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0 && org.apache.commons.lang.StringUtils.isNotEmpty(chargeTransaction.getChargeActionId()))
                .map(ChargeTransaction::getChargeActionId)
                .distinct()
                .collect(Collectors.toList());
        chargeSheetPrintView.setLatestChargeComment(chargeActions.stream()
                .filter(chargeAction -> availableActionIds.contains(chargeAction.getId())
                        && (chargeAction.getType() == ChargeAction.Type.PAY || chargeAction.getType() == ChargeAction.Type.REPAYMENT)
                        && org.apache.commons.lang3.StringUtils.isNotEmpty(chargeAction.getChargeComment())
                )
                .max(Comparator.comparing(ChargeAction::getCreated))
                .map(ChargeAction::getChargeComment)
                .orElse(null)
        );

        // 支付方式分类
        if (chargeTransactions != null) {
            Map<Long, Integer> payModeTypes = getPayModeTypes(sheetProcessorInfoProvider, chargeSheet.getChainId());
            Map<String, ChargeAction.PayActionInfo> payModeNameMap = ChargeUtils.generatePayModeNameMap(chargeActions, payModeTypes);
            chargeTransactions.forEach(chargeTransaction -> {
                chargeTransaction.setPayModeType(payModeTypes.getOrDefault(Long.valueOf(chargeTransaction.getPayMode()), Constants.ChargePayModeType.UN_KNOW));
            });
            List<ChargeTransactionPrintView> chargeTransactionPrintViews = chargeTransactions
                    .stream()
                    .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                    .filter(chargeTransaction -> chargeTransaction.getPaySource() == Constants.ChargeSource.CHARGE || chargeTransaction.getPaySource() == Constants.ChargeSource.WE_CLINIC || chargeTransaction.getPaySource() == Constants.ChargeSource.DEVICE || chargeTransaction.getPaySource() == Constants.ChargeSource.WE_APP)
                    .collect(Collectors.toMap(ChargeUtils::generateTransactionUniqueKey, chargeTransaction -> {
                        ChargeTransactionPrintView chargeTransactionPrintView = new ChargeTransactionPrintView();
                        chargeTransactionPrintView.setAmount(chargeTransaction.getAmount());
                        chargeTransactionPrintView.setPayMode(chargeTransaction.getPayMode());
                        chargeTransactionPrintView.setPaySubMode(chargeTransaction.getPaySubMode());
                        chargeTransactionPrintView.setThirdPartyPayCardId(chargeTransaction.getThirdPartyPayCardId());
                        return chargeTransactionPrintView;
                    }, (a, b) -> {
                        a.setAmount(MathUtils.wrapBigDecimalAdd(a.getAmount(), b.getAmount()));
                        return a;
                    }))
                    .entrySet()
                    .stream()
                    .map(entry -> {
                        String payModekey = ChargeUtils.generatePayModeKey(entry.getValue().getPayMode(), entry.getValue().getPaySubMode(), payModeTypes.getOrDefault(Long.valueOf(entry.getValue().getPayMode()), Constants.ChargePayModeType.UN_KNOW), entry.getKey());
                        ChargeAction.PayActionInfo payActionInfo = payModeNameMap.getOrDefault(payModekey, new ChargeAction.PayActionInfo());
                        entry.getValue().setPayModeName(payActionInfo.getPayModeName());
                        entry.getValue().setPaySubModeName(payActionInfo.getPaySubModeName());
                        return entry.getValue();
                    }).collect(Collectors.toList());
            chargeSheetPrintView.setChargeTransactions(chargeTransactionPrintViews);
        }

        chargeSheetPrintView.setLatestOweTransaction(Optional.ofNullable(transactionAndActionDto)
                .map(ChargeTransactionAndActionDto::getLastOweSheet)
                .filter(chargeOweSheet -> chargeOweSheet.getIsOldRecord() == 0)
                .map(ChargeOweSheet::getTransactionRecords)
                .orElse(new ArrayList<>())
                .stream()
                .filter(record -> record.getType() == ChargeCombineOrderTransaction.Type.PAY)
                .max(Comparator.comparing(ChargeOweCombineTransactionRecord::getCreated))
                .map(record -> {
                    ChargeTransactionPrintView chargeTransactionPrintView = new ChargeTransactionPrintView();
                    chargeTransactionPrintView.setAmount(record.getAmount());
                    chargeTransactionPrintView.setPayMode(record.getPayMode());
                    chargeTransactionPrintView.setPaySubMode(record.getPaySubMode());
                    chargeTransactionPrintView.setPayModeName(record.getPayModeDisplayName());
                    return chargeTransactionPrintView;
                })
                .orElse(null)
        );

        chargeSheetPrintView.setOweFee(Optional.ofNullable(chargeOweSheets).orElse(new ArrayList<>())
                .stream()
                .filter(chargeOweSheet -> chargeOweSheet.getIsOldRecord() == 0)
                .max(Comparator.comparing(ChargeOweSheet::getCreated))
                .map(ChargeOweSheet::calculateReceivableFee)
                .orElse(BigDecimal.ZERO));
        // 设置个人现金支付
        chargeSheetPrintView.setPersonalPaymentFee(
                MathUtils.wrapBigDecimalSubtract(chargeSheetPrintView.getNetIncomeFee(), chargeSheetPrintView.getHealthCardPaymentFee())
        );

        // 药品分类汇总
        chargeSheetPrintView.buildSubTotals();
        chargeSheetPrintView.setWholeMedicalBills(generateWholeMedicalBills(chargeSheetPrintView.getChargeForms(), chargeSheet.getChainId(), sheetProcessorInfoProvider, sheetProcessor.getFeeTypeSortDtoMap()));

        // 如果有会员卡支付，找到最后一次会员卡支付的余额
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeTransactions)) {
            ChargeTransaction firstMemberCardChargeTransaction = chargeTransactions
                    .stream()
                    .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.MEMBER_CARD)
                    .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                    .filter(chargeTransaction -> MathUtils.wrapBigDecimalOrZero(chargeTransaction.getAmount()).compareTo(BigDecimal.ZERO) > 0)
                    .filter(chargeTransaction -> chargeTransaction.getPaySource() == Constants.ChargeSource.CHARGE || chargeTransaction.getPaySource() == Constants.ChargeSource.WE_CLINIC || chargeTransaction.getPaySource() == Constants.ChargeSource.DEVICE)
                    .sorted((a, b) -> -ObjectUtils.compare(b.getCreated(), a.getCreated()))
                    .findFirst()
                    .orElse(null);

            ChargeTransaction lastMemberCardChargeTransaction = chargeTransactions
                    .stream()
                    .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.MEMBER_CARD)
                    .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                    .filter(chargeTransaction -> MathUtils.wrapBigDecimalOrZero(chargeTransaction.getAmount()).compareTo(BigDecimal.ZERO) > 0)
                    .filter(chargeTransaction -> chargeTransaction.getPaySource() == Constants.ChargeSource.CHARGE || chargeTransaction.getPaySource() == Constants.ChargeSource.WE_CLINIC || chargeTransaction.getPaySource() == Constants.ChargeSource.DEVICE)
                    .sorted((a, b) -> -ObjectUtils.compare(a.getCreated(), b.getCreated()))
                    .findFirst()
                    .orElse(null);

            ChargeTransaction firstHealthCardChargeTransaction = chargeTransactions
                    .stream()
                    .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.HEALTH_CARD)
                    .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                    .filter(chargeTransaction -> MathUtils.wrapBigDecimalOrZero(chargeTransaction.getAmount()).compareTo(BigDecimal.ZERO) >= 0)
                    .filter(chargeTransaction -> chargeTransaction.getPaySource() == Constants.ChargeSource.CHARGE || chargeTransaction.getPaySource() == Constants.ChargeSource.WE_CLINIC || chargeTransaction.getPaySource() == Constants.ChargeSource.DEVICE)
                    .sorted((a, b) -> -ObjectUtils.compare(b.getCreated(), a.getCreated()))
                    .findFirst()
                    .orElse(null);

            ChargeTransaction lastHealthCardChargeTransaction = chargeTransactions
                    .stream()
                    .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.HEALTH_CARD)
                    .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                    .filter(chargeTransaction -> MathUtils.wrapBigDecimalOrZero(chargeTransaction.getAmount()).compareTo(BigDecimal.ZERO) >= 0)
                    .filter(chargeTransaction -> chargeTransaction.getPaySource() == Constants.ChargeSource.CHARGE || chargeTransaction.getPaySource() == Constants.ChargeSource.WE_CLINIC || chargeTransaction.getPaySource() == Constants.ChargeSource.DEVICE)
                    .sorted((a, b) -> -ObjectUtils.compare(a.getCreated(), b.getCreated()))
                    .findFirst()
                    .orElse(null);

            List<ChargeTransaction> healthCardChargeTransactions = chargeTransactions
                    .stream()
                    .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.HEALTH_CARD)
                    .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                    .filter(chargeTransaction -> MathUtils.wrapBigDecimalOrZero(chargeTransaction.getAmount()).compareTo(BigDecimal.ZERO) >= 0)
                    .filter(chargeTransaction -> chargeTransaction.getPaySource() == Constants.ChargeSource.CHARGE || chargeTransaction.getPaySource() == Constants.ChargeSource.WE_CLINIC || chargeTransaction.getPaySource() == Constants.ChargeSource.DEVICE)
                    .sorted((a, b) -> -ObjectUtils.compare(b.getCreated(), a.getCreated()))
                    .collect(Collectors.toList());

            //TODO 可以树立下这个rpc调用，就取了一个手机号，可以想办法省掉
            PatientInfo cisPatientInfo = sheetProcessorInfoProvider.getPatientInfoProvider().findPatientInfoById(chargeSheet.getChainId(), chargeSheet.getClinicId(), chargeSheet.getMemberId(), false, false);

            if (cisPatientInfo != null && lastMemberCardChargeTransaction != null && firstMemberCardChargeTransaction != null) {
                //FIXME: 临时处理，通过反推计算原始余额。【cokesu】
                chargeSheetPrintView.setMemberCardBeginningBalance(MathUtils.wrapBigDecimalAdd(firstMemberCardChargeTransaction.getThirdPartyPayCardBalance(),
                        firstMemberCardChargeTransaction.getAmount()));

                chargeSheetPrintView.setMemberCardBalance(lastMemberCardChargeTransaction.getThirdPartyPayCardBalance());
                chargeSheetPrintView.setMemberCardMobile(cisPatientInfo.getMobile());
            }


            //这份代码保留，只为了兼容老逻辑，未来会删除
            if (lastHealthCardChargeTransaction != null && firstHealthCardChargeTransaction != null) {
                chargeSheetPrintView.setHealthCardBalance(lastHealthCardChargeTransaction.getThirdPartyPayCardBalance());


                ThirdPartyPayInfo firstThirdPartyPayInfo = JsonUtils.readValue(firstHealthCardChargeTransaction.getThirdPartyPayInfoJson(), ThirdPartyPayInfo.class);
                if (firstThirdPartyPayInfo != null && firstThirdPartyPayInfo.getCqShebaoExtraInfo() != null) {
                    chargeSheetPrintView.setHealthCardBeginningBalance(MathUtils.wrapBigDecimalAdd(firstThirdPartyPayInfo.getCqShebaoExtraInfo().getAccountBalance(),
                            firstThirdPartyPayInfo.getCqShebaoExtraInfo().getAccountPaymentFee()));
                } else {
                    chargeSheetPrintView.setHealthCardBeginningBalance(MathUtils.wrapBigDecimalAdd(firstHealthCardChargeTransaction.getThirdPartyPayCardBalance(),
                            firstHealthCardChargeTransaction.getAmount()));
                }

                chargeSheetPrintView.setHealthCardNo(lastHealthCardChargeTransaction.getThirdPartyPayCardId());
                ThirdPartyPayInfo lastThirdPartyPayInfo = JsonUtils.readValue(lastHealthCardChargeTransaction.getThirdPartyPayInfoJson(), ThirdPartyPayInfo.class);
                if (lastThirdPartyPayInfo != null) {
                    chargeSheetPrintView.setHealthCardOwner(lastThirdPartyPayInfo.getCardOwner());
                    chargeSheetPrintView.setHealthCardOwnerRelationToPatient(lastThirdPartyPayInfo.getRelationToPatient());
                }

                //帐户支付金额
                BigDecimal accountPaymentFee = BigDecimal.ZERO;
                //统筹支付金额
                BigDecimal fundPaymentFee = BigDecimal.ZERO;
                //其它支付金额
                BigDecimal otherPaymentFee = BigDecimal.ZERO;
                //持卡人类型 职工 居民 离休干部 等
                String cardOwnerType = Optional.ofNullable(firstThirdPartyPayInfo).map(ThirdPartyPayInfo::getCardOwnerType).orElse(null);
                String cardId = Optional.ofNullable(firstThirdPartyPayInfo).map(ThirdPartyPayInfo::getCardId).orElse(null);

                //自负金额
                BigDecimal selfConceitFee = BigDecimal.ZERO;
                //自费金额
                BigDecimal selfPayFee = BigDecimal.ZERO;

                for (ChargeTransaction healthCardChargeTransaction : healthCardChargeTransactions) {
                    ThirdPartyPayInfo thirdPartyPayInfo = JsonUtils.readValue(healthCardChargeTransaction.getThirdPartyPayInfoJson(), ThirdPartyPayInfo.class);
                    if (thirdPartyPayInfo == null) {
                        continue;
                    }
                    accountPaymentFee = MathUtils.wrapBigDecimalAdd(accountPaymentFee, thirdPartyPayInfo.getAccountPaymentFee());
                    fundPaymentFee = MathUtils.wrapBigDecimalAdd(fundPaymentFee, thirdPartyPayInfo.getFundPaymentFee());
                    otherPaymentFee = MathUtils.wrapBigDecimalAdd(otherPaymentFee, thirdPartyPayInfo.getOtherPaymentFee());
                    selfConceitFee = MathUtils.wrapBigDecimalAdd(selfConceitFee, thirdPartyPayInfo.getSelfConceitFee());
                    selfPayFee = MathUtils.wrapBigDecimalAdd(selfPayFee, thirdPartyPayInfo.getSelfPayFee());
                }
                chargeSheetPrintView.setHealthCardAccountPaymentFee(accountPaymentFee);
                chargeSheetPrintView.setHealthCardFundPaymentFee(fundPaymentFee);
                chargeSheetPrintView.setHealthCardOtherPaymentFee(otherPaymentFee);
                chargeSheetPrintView.setHealthCardCardOwnerType(cardOwnerType);
                chargeSheetPrintView.setHealthCardSelfConceitFee(selfConceitFee);
                chargeSheetPrintView.setHealthCardSelfPayFee(selfPayFee);
                chargeSheetPrintView.setHealthCardId(cardId);
            }
        }

        //展示卡项余额
        chargeSheetPrintView.setPromotionBalances(buildChargeBalanceViews(sheetProcessor, chargeSheet));

        ChargeTransactionAndActionDto chargeTransactionAndActionDto = getChargeTransactionAndChargeActionContainOwe(sheetProcessor.chargeSheet, chargeOweSheets);

        bindShebaoPrintInfo(chargeSheet, QueryChargeSheetShebaoInfoReq.PayTaskType.PAY, chargeSheetPrintView, sheetProcessorInfoProvider, chargeTransactionAndActionDto);

        bindGoodsDispenseInfo(chargeSheet, chargeSheetPrintView, false, sheetProcessorInfoProvider);

        //返券信息
        chargeSheetPrintView.setGiftCoupons(generateGiftCouponPrintViews(chargeSheet));

        // 收费员信息
        List<String> employeeIds = new ArrayList<>();
        if (!TextUtils.isEmpty(chargeSheet.getChargedBy())) {
            employeeIds.add(chargeSheet.getChargedBy());
        }

        if (!TextUtils.isEmpty(chargeSheet.getSellerId())) {
            employeeIds.add(chargeSheet.getSellerId());
        }

        //如果收费单含有配镜处方需要绑定验光师姓名
        List<String> optometristList = chargeSheetPrintView.getChargeForms().stream()
                .filter(form -> form.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_TYPE_EYE_GLASS && !StringUtils.isEmpty(form.getOptometristId()))
                .map(form -> form.getOptometristId())
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(optometristList)) {
            employeeIds.addAll(optometristList);
        }


        Map<String, String> employeeNameMap = sheetProcessorInfoProvider.getEmployeeInfoProvider().findEmployeeList(chargeSheet.getChainId(), employeeIds).stream().collect(Collectors.toMap(Employee::getId, Employee::getName, (a, b) -> a));

        String chargedByName = "";
        if (!TextUtils.isEmpty(chargeSheet.getChargedBy())) {
            if (Objects.equals(chargeSheet.getChargedBy(), Constants.ANONYMOUS_PATIENT_ID)) {
                chargedByName = "自助支付";
            } else {
                chargedByName = employeeNameMap.getOrDefault(chargeSheet.getChargedBy(), "");
            }
        }
        chargeSheetPrintView.setChargedByName(chargedByName);
        chargeSheetPrintView.setChargedTime(chargeSheet.getChargedTime());

        String sellerName = "";
        if (!TextUtils.isEmpty(chargeSheet.getSellerId())) {
            sellerName = employeeNameMap.getOrDefault(chargeSheet.getSellerId(), "");
        }

        String doctorName = "";
        String nationalDoctorCode = "";
        String doctorId = chargeSheet.getType() == ChargeSheet.Type.DIRECT_SALE
                || chargeSheet.getType() == ChargeSheet.Type.THERAPY
                || chargeSheet.getType() == ChargeSheet.Type.EXAMINATION_INSPECTION
                ? Optional.ofNullable(chargeSheet.getAdditional()).map(ChargeSheetAdditional::getTranscribeDoctorId).orElse(null)
                : chargeSheet.getDoctorId();
        if (!TextUtils.isEmpty(doctorId)) {
            //查询医生的名称快照
            Instant doctorIdSnapTime = Optional.ofNullable(chargeSheet.getDiagnosedDate()).orElse(Instant.now());
            doctorName = Optional.ofNullable(sheetProcessorInfoProvider.getClinicProvider()
                            .queryEmployeeSnap(new BatchQueryEmployeeSnapshotReq()
                                    .setChainId(chargeSheet.getChainId())
                                    .addBusinessTimeEmployeeSnap(doctorId, doctorIdSnapTime)))
                    .map(rsp -> rsp.getEmployeeNameByBusTime(doctorId, doctorIdSnapTime))
                    .orElse("");
            ClinicEmployee clinicEmployee = sheetProcessorInfoProvider.getClinicProvider().queryEmployeeClinicInfoById(doctorId, chargeSheet.getClinicId());
            if (clinicEmployee != null && clinicEmployee.getClinicInfo() != null) {
                nationalDoctorCode = clinicEmployee.getClinicInfo().getNationalDoctorCode();
            }
        }

        String departmentName = "", departmentCaty = "";
        if (chargeSheet.getAdditional() != null && org.apache.commons.lang3.StringUtils.isNotEmpty(chargeSheet.getAdditional().getDepartmentId())) {
            Department department = sheetProcessorInfoProvider.getClinicProvider().queryDepartmentById(chargeSheet.getAdditional().getDepartmentId());
            if (department != null) {
                departmentName = department.getName();
                departmentCaty = org.apache.commons.lang3.StringUtils.isNotEmpty(department.getSecondMedicalCode()) ? department.getSecondMedicalCode() : department.getMainMedicalCode();
            }
        }

        if (patientOrder != null) {
            chargeSheetPrintView.setDepartmentName(departmentName);
            chargeSheetPrintView.setDepartmentCaty(departmentCaty);
            chargeSheetPrintView.setPatientOrderNo(String.format("%08d", patientOrder.getNo()));
        }
        chargeSheetPrintView.setSellerName(sellerName);
        chargeSheetPrintView.setDoctorName(doctorName);
        chargeSheetPrintView.setNationalDoctorCode(nationalDoctorCode);

        /**
         * 绑定验光师姓名
         */
        chargeSheetPrintView.getChargeForms().stream()
                .filter(form -> form.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_TYPE_EYE_GLASS && !StringUtils.isEmpty(form.getOptometristId()))
                .forEach(form -> form.setOptometristName(employeeNameMap.get(form.getOptometristId())));


        //补充chargeFormItem的position字段，原因是收费单上已经没有存储goods的快照了，拿不到柜号信息，需从goods服务查询之后再填充上去
//        fillChargeFormItemPosition(chargeSheet.getClinicId(), chargeSheetPrintView, sheetProcessorInfoProvider);

        //处理检查检验里面的组合项，将子项提出来，这个逻辑放在最后处理
        if (Objects.equals(Constants.RegionId.HANGZHOU, Optional.ofNullable(organ).map(cn.abcyun.bis.rpc.sdk.cis.model.clinic.OrganPrintView::getAddressCityId).orElse(null))) {
            chargeSheetPrintView.getChargeForms()
                    .stream()
                    .filter(chargeFormPrintView -> chargeFormPrintView.getSourceFormType() == Constants.SourceFormType.EXAMINATION || chargeFormPrintView.getSourceFormType() == Constants.SourceFormType.COMPOSE_PRODUCT)
                    .filter(chargeFormPrintView -> chargeFormPrintView.getChargeFormItems() != null)
                    .forEach(chargeFormPrintView -> {
                        if (chargeFormPrintView.getSourceFormType() == Constants.SourceFormType.EXAMINATION) {
                            chargeFormPrintView.setChargeFormItems(chargeFormPrintView.getChargeFormItems().stream()
                                    .map(chargeFormItemPrintView -> {
                                        if (chargeFormItemPrintView.getIsExaminationSplitPrint() == 1 && org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeFormItemPrintView.getComposeChildren())) {
                                            return chargeFormItemPrintView.getComposeChildren();
                                        }
                                        return Collections.singletonList(chargeFormItemPrintView);
                                    })
                                    .flatMap(Collection::stream)
                                    .collect(Collectors.toList())
                            );
                            return;
                        }

                        if (chargeFormPrintView.getSourceFormType() == Constants.SourceFormType.COMPOSE_PRODUCT) {
                            chargeFormPrintView.getChargeFormItems().stream()
                                    .filter(chargeFormItemPrintView -> chargeFormItemPrintView.getProductType() == Constants.ProductType.EXAMINATION)
                                    .filter(chargeFormItemPrintView -> org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeFormItemPrintView.getComposeChildren()))
                                    .forEach(chargeFormItemPrintView -> chargeFormItemPrintView.setComposeChildren(chargeFormItemPrintView.getComposeChildren().stream()
                                            .map(child -> {
                                                if (child.getIsExaminationSplitPrint() == 1 && org.apache.commons.collections.CollectionUtils.isNotEmpty(child.getComposeChildren())) {
                                                    return child.getComposeChildren();
                                                }
                                                return Collections.singletonList(child);
                                            })
                                            .flatMap(Collection::stream)
                                            .collect(Collectors.toList()))
                                    );
                        }

                    });
        }
        doReplaceRegistrationFeeNameDisplay(chargeSheet.getClinicId(), chargeSheetPrintView, sheetProcessorInfoProvider);
        return chargeSheetPrintView;
    }

    public static Map<Long, Integer> getPayModeTypes(SheetProcessorInfoProvider sheetProcessorInfoProvider, String chainId) {
        if (sheetProcessorInfoProvider == null) {
            return new HashMap<>();
        }

        List<ChargeConfigDetailView.ChargePayModeConfigView> chargePayModeConfigViews = sheetProcessorInfoProvider.getChargePayModeProvider().queryChargePayModeConfigViews(chainId);
        if (chargePayModeConfigViews == null) {
            return new HashMap<>();
        }
        return chargePayModeConfigViews.stream().collect(Collectors.toMap(ChargeConfigDetailView.ChargePayModeConfigView::getPayModeId, ChargeConfigDetailView.ChargePayModeConfigView::getType));
    }

    private static MemberPrintInfo generateMemberPrintInfo(ChargeSheet chargeSheet, SheetProcessorInfoProvider sheetProcessorInfoProvider) {

        if (chargeSheet == null || org.apache.commons.lang3.StringUtils.isEmpty(chargeSheet.getMemberId()) || org.apache.commons.lang3.StringUtils.isEmpty(chargeSheet.getMemberInfoJson())) {
            return null;
        }

        MemberPrintInfo memberPrintInfo = JsonUtils.readValue(chargeSheet.getMemberInfoJson(), MemberPrintInfo.class);

        if (memberPrintInfo == null) {
            return null;
        }

        memberPrintInfo.setCardBalance(MathUtils.wrapBigDecimalAdd(memberPrintInfo.getPresent(), memberPrintInfo.getPrincipal()));

        ChargeTransaction lastMemberCardChargeTransaction = cn.abcyun.cis.commons.util.ListUtils.alwaysList(chargeSheet.getChargeTransactions())
                .stream()
                .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.MEMBER_CARD)
                .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                .max(Comparator.comparing(ChargeTransaction::getCreated))
                .orElse(null);

        if (lastMemberCardChargeTransaction != null && lastMemberCardChargeTransaction.getThirdPartyPayCardBalance() != null) {
            memberPrintInfo.setCardBalance(lastMemberCardChargeTransaction.getThirdPartyPayCardBalance());
        }

        //查询积分的快照数据
        if (sheetProcessorInfoProvider != null) {
            PatientPointsChargeCalculateRsp patientPointsChargeCalculateRsp = sheetProcessorInfoProvider.getPatientInfoProvider().calculatePatientPointsForCharge(chargeSheet);
            if (Objects.nonNull(patientPointsChargeCalculateRsp)) {
                memberPrintInfo.setPoints(patientPointsChargeCalculateRsp.getPoints());
                memberPrintInfo.setPointsTotal(patientPointsChargeCalculateRsp.getPointsTotal());
                memberPrintInfo.setChangePoints(patientPointsChargeCalculateRsp.getChangePoints());
            }
        }

        return memberPrintInfo;
    }

    /**
     * 打印View
     * 前置：chargeSheet已经完成了算费
     */
    public static ChargeSheetPrintView toPrintView(PharmacySheetProcessor sheetProcessor) {
        //初始化
        ChargeSheet chargeSheet = sheetProcessor.chargeSheet;
        SheetProcessorInfoProvider sheetProcessorInfoProvider = sheetProcessor.sheetProcessorInfoProvider;
        PatientInfo patientInfo = sheetProcessor.getPatientInfo();
        List<PharmacyFormProcessor> formProcessors = sheetProcessor.formProcessors;
        BigDecimal adjustmentFee = sheetProcessor.adjustmentFee;
        BigDecimal refundAdjustmentFee = sheetProcessor.refundAdjustmentFee;
        BigDecimal totalFee = sheetProcessor.totalFee;
        BigDecimal refundTotalFee = sheetProcessor.refundTotalFee;
        BigDecimal refundDiscountFee = sheetProcessor.refundDiscountFee;
        BigDecimal discountFee = sheetProcessor.discountFee;
        BigDecimal _refundFee = sheetProcessor._refundFee;
        BigDecimal _receivedFee = sheetProcessor._receivedFee;
        PatientOrder patientOrder = sheetProcessor.patientOrder;

        //查询收费单对应的欠费数据
        List<ChargeOweSheet> chargeOweSheets = new ArrayList<>();
        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED) {
            chargeOweSheets = Optional.ofNullable(sheetProcessorInfoProvider).map(SheetProcessorInfoProvider::getChargeOweSheetProvider)
                    .map(chargeOweSheetProvider -> chargeOweSheetProvider.findChargeOweSheetsByChargeSheetId(chargeSheet.getClinicId(), chargeSheet.getId())).orElse(new ArrayList<>());
        }

        cn.abcyun.bis.rpc.sdk.cis.model.clinic.OrganPrintView organ = sheetProcessorInfoProvider.getProductInfoProvider().getOrganPrintInfo(chargeSheet.getClinicId());

        ChargeSheetPrintView chargeSheetPrintView = new ChargeSheetPrintView();

        chargeSheetPrintView.setId(chargeSheet.getId());
        chargeSheetPrintView.setPatient(patientInfo);
        chargeSheetPrintView.setOrgan(organ != null ? new OrganPrintView(organ.getId(), organ.getName(), organ.getName(), organ.getAddressDetail(), organ.getContactPhone(), organ.getLogo(), organ.getCategory(), organ.getHisType()) : null);
        chargeSheetPrintView.setDiagnosedDate(chargeSheet.getDiagnosedDate());
        chargeSheetPrintView.setDiagnosis(Optional.ofNullable(chargeSheet.getAdditional()).map(ChargeSheetAdditional::getDiagnosis).orElse(null));
        chargeSheetPrintView.setDiagnosisInfos(Optional.ofNullable(chargeSheet.getAdditional()).map(ChargeSheetAdditional::getDiagnosisInfos).orElse(new ArrayList<>()));
        chargeSheetPrintView.setExtendDiagnosisInfos(Optional.ofNullable(chargeSheet.getAdditional()).map(ChargeSheetAdditional::getExtendDiagnosisInfos).orElse(new ArrayList<>()));
        chargeSheetPrintView.setOwedStatus(chargeSheet.getOwedStatus());
        chargeSheetPrintView.addChargeFormPrintViews(generatePharmacyChargeFormPrintViews(formProcessors, adjustmentFee, refundAdjustmentFee, Optional.ofNullable(organ).map(cn.abcyun.bis.rpc.sdk.cis.model.clinic.OrganPrintView::getHisType).orElse(0)));
        chargeSheetPrintView.setMemberInfo(generateMemberPrintInfo(chargeSheet, sheetProcessorInfoProvider));

        ChargeSheetPrintPriceInfo chargeSheetPrintPriceInfo = sheetProcessor.sheetCalculator.generateSheetPrintPriceInfo();
        chargeSheetPrintView.setTotalFee(chargeSheetPrintPriceInfo.getTotalFee());
        chargeSheetPrintView.setSinglePromotionFee(chargeSheetPrintPriceInfo.getSinglePromotionFee());
        chargeSheetPrintView.setPackagePromotionFee(chargeSheetPrintPriceInfo.getPackagePromotionFee());
        chargeSheetPrintView.setReceivableFee(chargeSheetPrintPriceInfo.getReceivableFee());
        chargeSheetPrintView.setNetIncomeFee(MathUtils.setScaleTwo(_receivedFee.add(_refundFee)));
        chargeSheetPrintView.setSinglePromotionedTotalFee(chargeSheetPrintPriceInfo.getSinglePromotionedTotalFee());

        ChargeTransactionAndActionDto transactionAndActionDto = getChargeTransactionAndChargeActionContainOwe(chargeSheet, chargeOweSheets);

        if (transactionAndActionDto.getLastOweSheet() != null && transactionAndActionDto.getLastOweSheet().getStatus() == ChargeOweSheet.Status.CHARGED) {
            chargeSheetPrintView.setLatestOwePaidTime(transactionAndActionDto.getLastOweSheet().getLatestPayTime());
        }


        List<ChargeTransaction> chargeTransactions = transactionAndActionDto.getChargeTransactions();
        List<ChargeAction> chargeActions = transactionAndActionDto.getChargeActions();

        List<String> availableActionIds = chargeTransactions.stream()
                .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0 && org.apache.commons.lang.StringUtils.isNotEmpty(chargeTransaction.getChargeActionId()))
                .map(ChargeTransaction::getChargeActionId)
                .distinct()
                .collect(Collectors.toList());
        chargeSheetPrintView.setLatestChargeComment(chargeActions.stream()
                .filter(chargeAction -> availableActionIds.contains(chargeAction.getId())
                        && (chargeAction.getType() == ChargeAction.Type.PAY || chargeAction.getType() == ChargeAction.Type.REPAYMENT)
                        && org.apache.commons.lang3.StringUtils.isNotEmpty(chargeAction.getChargeComment())
                )
                .max(Comparator.comparing(ChargeAction::getCreated))
                .map(ChargeAction::getChargeComment)
                .orElse(null)
        );

        // 支付方式分类
        if (chargeTransactions != null) {
            Map<Long, Integer> payModeTypes = getPayModeTypes(sheetProcessorInfoProvider, chargeSheet.getChainId());
            Map<String, ChargeAction.PayActionInfo> payModeNameMap = ChargeUtils.generatePayModeNameMap(chargeActions, payModeTypes);
            chargeTransactions.forEach(chargeTransaction -> {
                chargeTransaction.setPayModeType(payModeTypes.getOrDefault(Long.valueOf(chargeTransaction.getPayMode()), Constants.ChargePayModeType.UN_KNOW));
            });
            List<ChargeTransactionPrintView> chargeTransactionPrintViews = chargeTransactions
                    .stream()
                    .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                    .filter(chargeTransaction -> chargeTransaction.getPaySource() == Constants.ChargeSource.CHARGE || chargeTransaction.getPaySource() == Constants.ChargeSource.WE_CLINIC || chargeTransaction.getPaySource() == Constants.ChargeSource.DEVICE || chargeTransaction.getPaySource() == Constants.ChargeSource.WE_APP)
                    .collect(Collectors.toMap(ChargeUtils::generateTransactionUniqueKey, chargeTransaction -> {
                        ChargeTransactionPrintView chargeTransactionPrintView = new ChargeTransactionPrintView();
                        chargeTransactionPrintView.setAmount(chargeTransaction.getAmount());
                        chargeTransactionPrintView.setPayMode(chargeTransaction.getPayMode());
                        chargeTransactionPrintView.setPaySubMode(chargeTransaction.getPaySubMode());
                        chargeTransactionPrintView.setThirdPartyPayCardId(chargeTransaction.getThirdPartyPayCardId());
                        return chargeTransactionPrintView;
                    }, (a, b) -> {
                        a.setAmount(MathUtils.wrapBigDecimalAdd(a.getAmount(), b.getAmount()));
                        return a;
                    }))
                    .entrySet()
                    .stream()
                    .map(entry -> {
                        String payModeKey = ChargeUtils.generatePayModeKey(entry.getValue().getPayMode(), entry.getValue().getPaySubMode(), payModeTypes.getOrDefault(Long.valueOf(entry.getValue().getPayMode()), Constants.ChargePayModeType.UN_KNOW), entry.getKey());
                        ChargeAction.PayActionInfo payActionInfo = payModeNameMap.getOrDefault(payModeKey, new ChargeAction.PayActionInfo());
                        entry.getValue().setPayModeName(payActionInfo.getPayModeName());
                        entry.getValue().setPaySubModeName(payActionInfo.getPaySubModeName());
                        return entry.getValue();
                    }).collect(Collectors.toList());
            chargeSheetPrintView.setChargeTransactions(chargeTransactionPrintViews);
        }

        chargeSheetPrintView.setLatestOweTransaction(Optional.ofNullable(transactionAndActionDto)
                .map(ChargeTransactionAndActionDto::getLastOweSheet)
                .filter(chargeOweSheet -> chargeOweSheet.getIsOldRecord() == 0)
                .map(ChargeOweSheet::getTransactionRecords)
                .orElse(new ArrayList<>())
                .stream()
                .filter(record -> record.getType() == ChargeCombineOrderTransaction.Type.PAY)
                .max(Comparator.comparing(ChargeOweCombineTransactionRecord::getCreated))
                .map(record -> {
                    ChargeTransactionPrintView chargeTransactionPrintView = new ChargeTransactionPrintView();
                    chargeTransactionPrintView.setAmount(record.getAmount());
                    chargeTransactionPrintView.setPayMode(record.getPayMode());
                    chargeTransactionPrintView.setPaySubMode(record.getPaySubMode());
                    chargeTransactionPrintView.setPayModeName(record.getPayModeDisplayName());
                    return chargeTransactionPrintView;
                })
                .orElse(null)
        );

        chargeSheetPrintView.setOweFee(Optional.ofNullable(chargeOweSheets).orElse(new ArrayList<>())
                .stream()
                .filter(chargeOweSheet -> chargeOweSheet.getIsOldRecord() == 0)
                .max(Comparator.comparing(ChargeOweSheet::getCreated))
                .map(ChargeOweSheet::calculateReceivableFee)
                .orElse(BigDecimal.ZERO));
        // 设置个人现金支付
        chargeSheetPrintView.setPersonalPaymentFee(
                MathUtils.wrapBigDecimalSubtract(chargeSheetPrintView.getNetIncomeFee(), chargeSheetPrintView.getHealthCardPaymentFee())
        );

        // 药品分类汇总
        chargeSheetPrintView.buildSubTotals();
        chargeSheetPrintView.setWholeMedicalBills(generateWholeMedicalBills(chargeSheetPrintView.getChargeForms(), chargeSheet.getChainId(), sheetProcessorInfoProvider, null));

        // 如果有会员卡支付，找到最后一次会员卡支付的余额
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeTransactions)) {
            ChargeTransaction firstMemberCardChargeTransaction = chargeTransactions
                    .stream()
                    .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.MEMBER_CARD)
                    .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                    .filter(chargeTransaction -> MathUtils.wrapBigDecimalOrZero(chargeTransaction.getAmount()).compareTo(BigDecimal.ZERO) > 0)
                    .filter(chargeTransaction -> chargeTransaction.getPaySource() == Constants.ChargeSource.CHARGE || chargeTransaction.getPaySource() == Constants.ChargeSource.WE_CLINIC || chargeTransaction.getPaySource() == Constants.ChargeSource.DEVICE)
                    .sorted((a, b) -> -ObjectUtils.compare(b.getCreated(), a.getCreated()))
                    .findFirst()
                    .orElse(null);

            ChargeTransaction lastMemberCardChargeTransaction = chargeTransactions
                    .stream()
                    .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.MEMBER_CARD)
                    .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                    .filter(chargeTransaction -> MathUtils.wrapBigDecimalOrZero(chargeTransaction.getAmount()).compareTo(BigDecimal.ZERO) > 0)
                    .filter(chargeTransaction -> chargeTransaction.getPaySource() == Constants.ChargeSource.CHARGE || chargeTransaction.getPaySource() == Constants.ChargeSource.WE_CLINIC || chargeTransaction.getPaySource() == Constants.ChargeSource.DEVICE)
                    .sorted((a, b) -> -ObjectUtils.compare(a.getCreated(), b.getCreated()))
                    .findFirst()
                    .orElse(null);

            ChargeTransaction firstHealthCardChargeTransaction = chargeTransactions
                    .stream()
                    .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.HEALTH_CARD)
                    .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                    .filter(chargeTransaction -> MathUtils.wrapBigDecimalOrZero(chargeTransaction.getAmount()).compareTo(BigDecimal.ZERO) >= 0)
                    .filter(chargeTransaction -> chargeTransaction.getPaySource() == Constants.ChargeSource.CHARGE || chargeTransaction.getPaySource() == Constants.ChargeSource.WE_CLINIC || chargeTransaction.getPaySource() == Constants.ChargeSource.DEVICE)
                    .sorted((a, b) -> -ObjectUtils.compare(b.getCreated(), a.getCreated()))
                    .findFirst()
                    .orElse(null);

            ChargeTransaction lastHealthCardChargeTransaction = chargeTransactions
                    .stream()
                    .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.HEALTH_CARD)
                    .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                    .filter(chargeTransaction -> MathUtils.wrapBigDecimalOrZero(chargeTransaction.getAmount()).compareTo(BigDecimal.ZERO) >= 0)
                    .filter(chargeTransaction -> chargeTransaction.getPaySource() == Constants.ChargeSource.CHARGE || chargeTransaction.getPaySource() == Constants.ChargeSource.WE_CLINIC || chargeTransaction.getPaySource() == Constants.ChargeSource.DEVICE)
                    .sorted((a, b) -> -ObjectUtils.compare(a.getCreated(), b.getCreated()))
                    .findFirst()
                    .orElse(null);

            List<ChargeTransaction> healthCardChargeTransactions = chargeTransactions
                    .stream()
                    .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.HEALTH_CARD)
                    .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                    .filter(chargeTransaction -> MathUtils.wrapBigDecimalOrZero(chargeTransaction.getAmount()).compareTo(BigDecimal.ZERO) >= 0)
                    .filter(chargeTransaction -> chargeTransaction.getPaySource() == Constants.ChargeSource.CHARGE || chargeTransaction.getPaySource() == Constants.ChargeSource.WE_CLINIC || chargeTransaction.getPaySource() == Constants.ChargeSource.DEVICE)
                    .sorted((a, b) -> -ObjectUtils.compare(b.getCreated(), a.getCreated()))
                    .collect(Collectors.toList());

            //TODO 可以树立下这个rpc调用，就取了一个手机号，可以想办法省掉
            PatientInfo cisPatientInfo = sheetProcessorInfoProvider.getPatientInfoProvider().findPatientInfoById(chargeSheet.getChainId(), chargeSheet.getClinicId(), chargeSheet.getMemberId(), false, false);

            if (cisPatientInfo != null && lastMemberCardChargeTransaction != null && firstMemberCardChargeTransaction != null) {
                //FIXME: 临时处理，通过反推计算原始余额。【cokesu】
                chargeSheetPrintView.setMemberCardBeginningBalance(MathUtils.wrapBigDecimalAdd(firstMemberCardChargeTransaction.getThirdPartyPayCardBalance(),
                        firstMemberCardChargeTransaction.getAmount()));

                chargeSheetPrintView.setMemberCardBalance(lastMemberCardChargeTransaction.getThirdPartyPayCardBalance());
                chargeSheetPrintView.setMemberCardMobile(cisPatientInfo.getMobile());
            }


            //这份代码保留，只为了兼容老逻辑，未来会删除
            if (lastHealthCardChargeTransaction != null && firstHealthCardChargeTransaction != null) {
                chargeSheetPrintView.setHealthCardBalance(lastHealthCardChargeTransaction.getThirdPartyPayCardBalance());


                ThirdPartyPayInfo firstThirdPartyPayInfo = JsonUtils.readValue(firstHealthCardChargeTransaction.getThirdPartyPayInfoJson(), ThirdPartyPayInfo.class);
                if (firstThirdPartyPayInfo != null && firstThirdPartyPayInfo.getCqShebaoExtraInfo() != null) {
                    chargeSheetPrintView.setHealthCardBeginningBalance(MathUtils.wrapBigDecimalAdd(firstThirdPartyPayInfo.getCqShebaoExtraInfo().getAccountBalance(),
                            firstThirdPartyPayInfo.getCqShebaoExtraInfo().getAccountPaymentFee()));
                } else {
                    chargeSheetPrintView.setHealthCardBeginningBalance(MathUtils.wrapBigDecimalAdd(firstHealthCardChargeTransaction.getThirdPartyPayCardBalance(),
                            firstHealthCardChargeTransaction.getAmount()));
                }

                chargeSheetPrintView.setHealthCardNo(lastHealthCardChargeTransaction.getThirdPartyPayCardId());
                ThirdPartyPayInfo lastThirdPartyPayInfo = JsonUtils.readValue(lastHealthCardChargeTransaction.getThirdPartyPayInfoJson(), ThirdPartyPayInfo.class);
                if (lastThirdPartyPayInfo != null) {
                    chargeSheetPrintView.setHealthCardOwner(lastThirdPartyPayInfo.getCardOwner());
                    chargeSheetPrintView.setHealthCardOwnerRelationToPatient(lastThirdPartyPayInfo.getRelationToPatient());
                }

                //帐户支付金额
                BigDecimal accountPaymentFee = BigDecimal.ZERO;
                //统筹支付金额
                BigDecimal fundPaymentFee = BigDecimal.ZERO;
                //其它支付金额
                BigDecimal otherPaymentFee = BigDecimal.ZERO;
                //持卡人类型 职工 居民 离休干部 等
                String cardOwnerType = Optional.ofNullable(firstThirdPartyPayInfo).map(ThirdPartyPayInfo::getCardOwnerType).orElse(null);
                String cardId = Optional.ofNullable(firstThirdPartyPayInfo).map(ThirdPartyPayInfo::getCardId).orElse(null);

                //自负金额
                BigDecimal selfConceitFee = BigDecimal.ZERO;
                //自费金额
                BigDecimal selfPayFee = BigDecimal.ZERO;

                for (ChargeTransaction healthCardChargeTransaction : healthCardChargeTransactions) {
                    ThirdPartyPayInfo thirdPartyPayInfo = JsonUtils.readValue(healthCardChargeTransaction.getThirdPartyPayInfoJson(), ThirdPartyPayInfo.class);
                    if (thirdPartyPayInfo == null) {
                        continue;
                    }
                    accountPaymentFee = MathUtils.wrapBigDecimalAdd(accountPaymentFee, thirdPartyPayInfo.getAccountPaymentFee());
                    fundPaymentFee = MathUtils.wrapBigDecimalAdd(fundPaymentFee, thirdPartyPayInfo.getFundPaymentFee());
                    otherPaymentFee = MathUtils.wrapBigDecimalAdd(otherPaymentFee, thirdPartyPayInfo.getOtherPaymentFee());
                    selfConceitFee = MathUtils.wrapBigDecimalAdd(selfConceitFee, thirdPartyPayInfo.getSelfConceitFee());
                    selfPayFee = MathUtils.wrapBigDecimalAdd(selfPayFee, thirdPartyPayInfo.getSelfPayFee());
                }
                chargeSheetPrintView.setHealthCardAccountPaymentFee(accountPaymentFee);
                chargeSheetPrintView.setHealthCardFundPaymentFee(fundPaymentFee);
                chargeSheetPrintView.setHealthCardOtherPaymentFee(otherPaymentFee);
                chargeSheetPrintView.setHealthCardCardOwnerType(cardOwnerType);
                chargeSheetPrintView.setHealthCardSelfConceitFee(selfConceitFee);
                chargeSheetPrintView.setHealthCardSelfPayFee(selfPayFee);
                chargeSheetPrintView.setHealthCardId(cardId);
            }
        }

        //展示卡项余额
        chargeSheetPrintView.setPromotionBalances(buildChargeBalanceViews(sheetProcessor, chargeSheet));

        ChargeTransactionAndActionDto chargeTransactionAndActionDto = getChargeTransactionAndChargeActionContainOwe(sheetProcessor.chargeSheet, chargeOweSheets);

        bindShebaoPrintInfo(chargeSheet, QueryChargeSheetShebaoInfoReq.PayTaskType.PAY, chargeSheetPrintView, sheetProcessorInfoProvider, chargeTransactionAndActionDto);

        bindGoodsDispenseInfo(chargeSheet, chargeSheetPrintView, false, sheetProcessorInfoProvider);

        //返券信息
        chargeSheetPrintView.setGiftCoupons(generateGiftCouponPrintViews(chargeSheet));

        // 收费员信息
        List<String> employeeIds = new ArrayList<>();
        if (!TextUtils.isEmpty(chargeSheet.getChargedBy())) {
            employeeIds.add(chargeSheet.getChargedBy());
        }

        if (!TextUtils.isEmpty(chargeSheet.getSellerId())) {
            employeeIds.add(chargeSheet.getSellerId());
        }
        Set<String> needQueryNationalDoctorCodeIdList = new HashSet<>();
        // 药师
        String pharmacistId = null;
        if (chargeSheet.getAdditional() != null && chargeSheet.getAdditional().getExtendedInfo() != null
                && !StringUtils.isEmpty(chargeSheet.getAdditional().getExtendedInfo().getPharmacistId())) {
            pharmacistId = chargeSheet.getAdditional().getExtendedInfo().getPharmacistId();
            employeeIds.add(pharmacistId);
            needQueryNationalDoctorCodeIdList.add(pharmacistId);
        }
        if (chargeSheet.getAdditional() != null) {
            chargeSheetPrintView.setRemarks(chargeSheet.getAdditional().getRemarks());
        }

        //如果收费单含有配镜处方需要绑定验光师姓名
        List<String> optometristList = chargeSheetPrintView.getChargeForms().stream()
                .filter(form -> form.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_TYPE_EYE_GLASS && !StringUtils.isEmpty(form.getOptometristId()))
                .map(form -> form.getOptometristId())
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(optometristList)) {
            employeeIds.addAll(optometristList);
        }


        Map<String, String> employeeNameMap = sheetProcessorInfoProvider.getEmployeeInfoProvider().findEmployeeList(chargeSheet.getChainId(), employeeIds).stream().collect(Collectors.toMap(Employee::getId, Employee::getName, (a, b) -> a));

        String chargedByName = "";
        if (!TextUtils.isEmpty(chargeSheet.getChargedBy())) {
            if (Objects.equals(chargeSheet.getChargedBy(), Constants.ANONYMOUS_PATIENT_ID)) {
                chargedByName = "自助支付";
            } else {
                chargedByName = employeeNameMap.getOrDefault(chargeSheet.getChargedBy(), "");
            }
        }
        chargeSheetPrintView.setChargedByName(chargedByName);
        chargeSheetPrintView.setChargedTime(chargeSheet.getChargedTime());

        String sellerName = "";
        if (!TextUtils.isEmpty(chargeSheet.getSellerId())) {
            sellerName = employeeNameMap.getOrDefault(chargeSheet.getSellerId(), "");
        }

        if (chargeSheet.getAdditional() != null && chargeSheet.getAdditional().getExtendedInfo() != null
                && !StringUtils.isEmpty(chargeSheet.getAdditional().getExtendedInfo().getPharmacistId())) {
            String pharmacistName = employeeNameMap.getOrDefault(chargeSheet.getAdditional().getExtendedInfo().getPharmacistId(), "");
            chargeSheetPrintView.setPharmacistName(pharmacistName);
        }


        String doctorName = "";
        String nationalDoctorCode = "";
        String doctorId = chargeSheet.getType() == ChargeSheet.Type.DIRECT_SALE
                || chargeSheet.getType() == ChargeSheet.Type.THERAPY
                || chargeSheet.getType() == ChargeSheet.Type.EXAMINATION_INSPECTION
                ? Optional.ofNullable(chargeSheet.getAdditional()).map(ChargeSheetAdditional::getTranscribeDoctorId).orElse(null)
                : chargeSheet.getDoctorId();
        if (!TextUtils.isEmpty(doctorId)) {
            //查询医生的名称快照
            Instant doctorIdSnapTime = Optional.ofNullable(chargeSheet.getDiagnosedDate()).orElse(Instant.now());
            doctorName = Optional.ofNullable(sheetProcessorInfoProvider.getClinicProvider()
                            .queryEmployeeSnap(new BatchQueryEmployeeSnapshotReq()
                                    .setChainId(chargeSheet.getChainId())
                                    .addBusinessTimeEmployeeSnap(doctorId, doctorIdSnapTime)))
                    .map(rsp -> rsp.getEmployeeNameByBusTime(doctorId, doctorIdSnapTime))
                    .orElse("");
            needQueryNationalDoctorCodeIdList.add(doctorId);

            //ClinicEmployee clinicEmployee = sheetProcessorInfoProvider.getClinicProvider().queryEmployeeClinicInfoById(doctorId, chargeSheet.getClinicId());
            //if (clinicEmployee != null && clinicEmployee.getClinicInfo() != null) {
            //    nationalDoctorCode = clinicEmployee.getClinicInfo().getNationalDoctorCode();
            //}
        }
        if (!needQueryNationalDoctorCodeIdList.isEmpty()) {
            List<EmployeeClinicInfo> employeeClinicInfos = sheetProcessorInfoProvider.getClinicProvider().queryClinicEmployeeInfo(chargeSheet.getChainId(),
                    chargeSheet.getClinicId(), new ArrayList<>(needQueryNationalDoctorCodeIdList));
            Map<String, EmployeeClinicInfo> employeeIdToClinicEmployee = ListUtils.toMap(employeeClinicInfos, EmployeeClinicBasicInfo::getEmployeeId);
            EmployeeClinicInfo doctorClinicEmployee = employeeIdToClinicEmployee.get(doctorId);
            EmployeeClinicInfo pharmacistClinicEmployee = employeeIdToClinicEmployee.get(pharmacistId);
            if (doctorClinicEmployee != null) {
                nationalDoctorCode = doctorClinicEmployee.getNationalDoctorCode();
            }
            if (pharmacistClinicEmployee != null) {
                chargeSheetPrintView.setPharmacistNationalDoctorCode(pharmacistClinicEmployee.getNationalDoctorCode());
            }
        }

        String departmentName = "", departmentCaty = "";
        if (chargeSheet.getAdditional() != null && org.apache.commons.lang3.StringUtils.isNotEmpty(chargeSheet.getAdditional().getDepartmentId())) {
            Department department = sheetProcessorInfoProvider.getClinicProvider().queryDepartmentById(chargeSheet.getAdditional().getDepartmentId());
            if (department != null) {
                departmentName = department.getName();
                departmentCaty = org.apache.commons.lang3.StringUtils.isNotEmpty(department.getSecondMedicalCode()) ? department.getSecondMedicalCode() : department.getMainMedicalCode();
            }
        }

        if (patientOrder != null) {
            chargeSheetPrintView.setDepartmentName(departmentName);
            chargeSheetPrintView.setDepartmentCaty(departmentCaty);
            chargeSheetPrintView.setPatientOrderNo(String.format("%08d", patientOrder.getNo()));
        }
        chargeSheetPrintView.setSellerName(sellerName);
        chargeSheetPrintView.setDoctorName(doctorName);
        chargeSheetPrintView.setNationalDoctorCode(nationalDoctorCode);

        /**
         * 绑定验光师姓名
         */
        chargeSheetPrintView.getChargeForms().stream()
                .filter(form -> form.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_TYPE_EYE_GLASS && !StringUtils.isEmpty(form.getOptometristId()))
                .forEach(form -> form.setOptometristName(employeeNameMap.get(form.getOptometristId())));


        //补充chargeFormItem的position字段，原因是收费单上已经没有存储goods的快照了，拿不到柜号信息，需从goods服务查询之后再填充上去
//        fillChargeFormItemPosition(chargeSheet.getClinicId(), chargeSheetPrintView, sheetProcessorInfoProvider);

        //处理检查检验里面的组合项，将子项提出来，这个逻辑放在最后处理
        if (Objects.equals(Constants.RegionId.HANGZHOU, Optional.ofNullable(organ).map(cn.abcyun.bis.rpc.sdk.cis.model.clinic.OrganPrintView::getAddressCityId).orElse(null))) {
            chargeSheetPrintView.getChargeForms()
                    .stream()
                    .filter(chargeFormPrintView -> chargeFormPrintView.getSourceFormType() == Constants.SourceFormType.EXAMINATION || chargeFormPrintView.getSourceFormType() == Constants.SourceFormType.COMPOSE_PRODUCT)
                    .filter(chargeFormPrintView -> chargeFormPrintView.getChargeFormItems() != null)
                    .forEach(chargeFormPrintView -> {
                        if (chargeFormPrintView.getSourceFormType() == Constants.SourceFormType.EXAMINATION) {
                            chargeFormPrintView.setChargeFormItems(chargeFormPrintView.getChargeFormItems().stream()
                                    .map(chargeFormItemPrintView -> {
                                        if (chargeFormItemPrintView.getIsExaminationSplitPrint() == 1 && org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeFormItemPrintView.getComposeChildren())) {
                                            return chargeFormItemPrintView.getComposeChildren();
                                        }
                                        return Collections.singletonList(chargeFormItemPrintView);
                                    })
                                    .flatMap(Collection::stream)
                                    .collect(Collectors.toList())
                            );
                            return;
                        }

                        if (chargeFormPrintView.getSourceFormType() == Constants.SourceFormType.COMPOSE_PRODUCT) {
                            chargeFormPrintView.getChargeFormItems().stream()
                                    .filter(chargeFormItemPrintView -> chargeFormItemPrintView.getProductType() == Constants.ProductType.EXAMINATION)
                                    .filter(chargeFormItemPrintView -> org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeFormItemPrintView.getComposeChildren()))
                                    .forEach(chargeFormItemPrintView -> chargeFormItemPrintView.setComposeChildren(chargeFormItemPrintView.getComposeChildren().stream()
                                            .map(child -> {
                                                if (child.getIsExaminationSplitPrint() == 1 && org.apache.commons.collections.CollectionUtils.isNotEmpty(child.getComposeChildren())) {
                                                    return child.getComposeChildren();
                                                }
                                                return Collections.singletonList(child);
                                            })
                                            .flatMap(Collection::stream)
                                            .collect(Collectors.toList()))
                                    );
                        }

                    });
        }
        doReplaceRegistrationFeeNameDisplay(chargeSheet.getClinicId(), chargeSheetPrintView, sheetProcessorInfoProvider);
        return chargeSheetPrintView;
    }


    public static List<MedicalBillPrintView> generateWholeMedicalBills(List<ChargeFormPrintView> chargeForms, String chainId, SheetProcessorInfoProvider sheetProcessorInfoProvider, Map<Long, GoodsFeeTypeConstants.GoodsFeeTypeSortDto> goodsFeeTypeSortDtoMap) {

        if (org.apache.commons.collections.CollectionUtils.isEmpty(chargeForms)) {
            return new ArrayList<>();
        }

        List<GoodsSystemType> systemTypes = sheetProcessorInfoProvider.getGoodsScProvider().getSystemTypes();
        Map<String, GoodsSystemType> goodsTypeKeyToGoodsTypeMap = Optional.ofNullable(systemTypes).orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.toMap(it -> GoodsFeeTypeConstants.goodsTypeKey(it.getType(), it.getSubType(), it.getCMSpec()), Function.identity(), (a, b) -> a));

        Map<Long, MedicalBillPrintView> medicalBillPrintViewMap = chargeForms.stream()
                .filter(chargeFormPrintView -> org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeFormPrintView.getChargeFormItems()))
                .flatMap(chargeFormPrintView -> chargeFormPrintView.getChargeFormItems().stream())
                .flatMap(chargeFormItemPrintView -> {
                    List<ChargeFormItemPrintView> chargeFormItemPrintViews = new ArrayList<>();
                    //检查检验组合项判断是否拆开打印
                    if (chargeFormItemPrintView.getIsExaminationSplitPrint() == YesOrNo.YES) {
                        chargeFormItemPrintViews.addAll(chargeFormItemPrintView.getComposeChildren().stream()
                                .flatMap(child -> {
                                    List<ChargeFormItemPrintView> children = new ArrayList<>();
                                    children.add(child);
                                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(child.getComposeChildren())) {
                                        children.addAll(child.getComposeChildren());
                                    }
                                    return children.stream();
                                })
                                .collect(Collectors.toList()));
                        return chargeFormItemPrintViews.stream();
                    }

                    chargeFormItemPrintViews.add(chargeFormItemPrintView);
                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeFormItemPrintView.getComposeChildren())) {
                        chargeFormItemPrintViews.addAll(chargeFormItemPrintView.getComposeChildren().stream()
                                .flatMap(child -> {
                                    List<ChargeFormItemPrintView> children = new ArrayList<>();
                                    children.add(child);
                                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(child.getComposeChildren())) {
                                        children.addAll(child.getComposeChildren());
                                    }
                                    return children.stream();
                                })
                                .collect(Collectors.toList())
                        );
                    }
                    return chargeFormItemPrintViews.stream();
                })
                .filter(chargeFormItemPrintView -> (chargeFormItemPrintView.getGoodsFeeType() == GoodsFeeType.FEE_OWN || chargeFormItemPrintView.getGoodsFeeType() == GoodsFeeType.FEE_CHILD) && chargeFormItemPrintView.getComposeType() != ComposeType.COMPOSE)
                .filter(chargeFormItemPrintView -> Objects.nonNull(chargeFormItemPrintView.getFeeTypeIdOrDefaultByGoodsTypeId(goodsTypeKeyToGoodsTypeMap)))
                .collect(Collectors.toMap(chargeFormItemPrintView -> chargeFormItemPrintView.getFeeTypeIdOrDefaultByGoodsTypeId(goodsTypeKeyToGoodsTypeMap),
                        chargeFormItemPrintView -> {
                            MedicalBillPrintView medicalBillPrintView = new MedicalBillPrintView();
                            medicalBillPrintView.setFeeTypeId(chargeFormItemPrintView.getFeeTypeIdOrDefaultByGoodsTypeId(goodsTypeKeyToGoodsTypeMap));
                            medicalBillPrintView.addCountAndFee(chargeFormItemPrintView.getDiscountedPrice(), 1);
                            return medicalBillPrintView;
                        },
                        (a, b) -> {
                            a.addCountAndFee(b.getTotalFee(), b.getTotalCount());
                            return a;
                        }));


        if (MapUtils.isEmpty(goodsFeeTypeSortDtoMap)) {
            goodsFeeTypeSortDtoMap = sheetProcessorInfoProvider.getGoodsScProvider().queryGoodsFeeTypeNames(chainId, new HashSet<>(medicalBillPrintViewMap.keySet()));
        }

        Map<Long, GoodsFeeTypeConstants.GoodsFeeTypeSortDto> finalGoodsFeeTypeSortDtoMap = goodsFeeTypeSortDtoMap;
        return medicalBillPrintViewMap.values()
                .stream()
                .peek(medicalBillPrintView -> {
                    GoodsFeeTypeConstants.GoodsFeeTypeSortDto goodsFeeTypeSortDto = finalGoodsFeeTypeSortDtoMap.get(medicalBillPrintView.getFeeTypeId());
                    if (Objects.nonNull(goodsFeeTypeSortDto)) {
                        medicalBillPrintView.setName(goodsFeeTypeSortDto.getName());
                        medicalBillPrintView.setSort(goodsFeeTypeSortDto.getSort());
                    }
                    medicalBillPrintView.setInnerFlag(medicalBillPrintView.getFeeTypeId() <= GoodsConst.FeeTypeId.FEE_TYPE_ID_ABC_INNER_MAX ? 1 : 0);
                })
                .sorted(Comparator.comparing(MedicalBillPrintView::getSort).thenComparing(MedicalBillPrintView::getFeeTypeId))
                .collect(Collectors.toList());

    }

    public static List<ChargeFormPrintView> generateChargeFormPrintViews(List<FormProcessor> formProcessors, BigDecimal adjustmentFee, BigDecimal refundAdjustmentFee, List<Integer> itemFilterStatus, int hisType) {

        List<ChargeFormPrintView> chargeFormPrintViews = new ArrayList<>();

        //挂号费
        ChargeFormPrintView registrationFormView = new ChargeFormPrintView();
        registrationFormView.setId(AbcIdUtils.getUUID());
        registrationFormView.setSourceFormType(Constants.SourceFormType.REGISTRATION);
        registrationFormView.setPrintFormType(Constants.PrintFormType.REGISTRATION);
        registrationFormView.addChargeFormItemPrintViews(generateNonMedicinePrintView(formProcessors, Constants.ProductType.REGISTRATION, itemFilterStatus));
        registrationFormView.calculateTotalPrice();
        if (registrationFormView.getChargeFormItems().size() > 0) {
            chargeFormPrintViews.add(registrationFormView);
        }

        //快递费
        ChargeFormPrintView expressDeliveryFormView = new ChargeFormPrintView();
        expressDeliveryFormView.setId(AbcIdUtils.getUUID());
        expressDeliveryFormView.setSourceFormType(Constants.SourceFormType.EXPRESS_DELIVERY);
        expressDeliveryFormView.setPrintFormType(Constants.PrintFormType.EXPRESS_DELIVERY);
        expressDeliveryFormView.addChargeFormItemPrintViews(generateNonMedicinePrintView(formProcessors, Constants.ProductType.EXPRESS_DELIVERY, itemFilterStatus));
        expressDeliveryFormView.calculateTotalPrice();
        if (expressDeliveryFormView.getChargeFormItems().size() > 0) {
            chargeFormPrintViews.add(expressDeliveryFormView);
        }

        //签约费
        ChargeFormPrintView familyDoctorSignFormView = new ChargeFormPrintView();
        familyDoctorSignFormView.setId(AbcIdUtils.getUUID());
        familyDoctorSignFormView.setSourceFormType(Constants.SourceFormType.FAMILY_DOCTOR_SIGN);
        familyDoctorSignFormView.setPrintFormType(Constants.PrintFormType.FAMILY_DOCTOR_SIGN);
        familyDoctorSignFormView.addChargeFormItemPrintViews(generateNonMedicinePrintView(formProcessors, Constants.ProductType.FAMILY_DOCTOR_SIGN, itemFilterStatus));
        familyDoctorSignFormView.calculateTotalPrice();
        if (familyDoctorSignFormView.getChargeFormItems().size() > 0) {
            chargeFormPrintViews.add(familyDoctorSignFormView);
        }

        //加工费
        ChargeFormPrintView decoctionFormView = new ChargeFormPrintView();
        decoctionFormView.setId(AbcIdUtils.getUUID());
        decoctionFormView.setSourceFormType(Constants.SourceFormType.PROCESS);
        decoctionFormView.setPrintFormType(Constants.PrintFormType.PROCESS);
        decoctionFormView.addChargeFormItemPrintViews(generateNonMedicinePrintView(formProcessors, Constants.ProductType.PROCESS, itemFilterStatus));
        decoctionFormView.calculateTotalPrice();
        if (decoctionFormView.getChargeFormItems().size() > 0) {
            chargeFormPrintViews.add(decoctionFormView);
        }

        //咨询费
        ChargeFormPrintView onlineConsultationFormView = new ChargeFormPrintView();
        onlineConsultationFormView.setId(AbcIdUtils.getUUID());
        onlineConsultationFormView.setSourceFormType(Constants.SourceFormType.ONLINE_CONSULTATION);
        onlineConsultationFormView.setPrintFormType(Constants.PrintFormType.ONLINE_CONSULTATION);
        onlineConsultationFormView.addChargeFormItemPrintViews(generateNonMedicinePrintView(formProcessors, Constants.ProductType.ONLINE_CONSULTATION, itemFilterStatus));
        onlineConsultationFormView.calculateTotalPrice();
        if (onlineConsultationFormView.getChargeFormItems().size() > 0) {
            chargeFormPrintViews.add(onlineConsultationFormView);
        }

        //检查检验
        ChargeFormPrintView examinationFormView = new ChargeFormPrintView();
        examinationFormView.setId(AbcIdUtils.getUUID());
        examinationFormView.setSourceFormType(Constants.SourceFormType.EXAMINATION);
        examinationFormView.setPrintFormType(Constants.SourceFormType.EXAMINATION);
        examinationFormView.addChargeFormItemPrintViews(generateNonMedicinePrintView(formProcessors, Constants.ProductType.EXAMINATION, itemFilterStatus));
        examinationFormView.calculateTotalPrice();
        if (examinationFormView.getChargeFormItems().size() > 0) {
            chargeFormPrintViews.add(examinationFormView);
        }


        List<ChargeFormItemPrintView> allTreatmentFormItemViews = generateNonMedicinePrintView(formProcessors, Constants.ProductType.TREATMENT, itemFilterStatus);
        Map<Boolean, List<ChargeFormItemPrintView>> treatmentFormItemViewMap = allTreatmentFormItemViews.stream()
                .collect(Collectors.groupingBy(chargeFormItemPrintView -> chargeFormItemPrintView.getProductType() == Constants.ProductType.TREATMENT && chargeFormItemPrintView.getProductSubType() == Constants.ProductType.SubType.TREATMENT_OTHER));

        List<ChargeFormItemPrintView> inTreatmentFormOtherFormItemViews = treatmentFormItemViewMap.getOrDefault(Boolean.TRUE, new ArrayList<>());
        List<ChargeFormItemPrintView> treatmentFormItemViews = treatmentFormItemViewMap.getOrDefault(Boolean.FALSE, new ArrayList<>());

        //治疗理疗
        ChargeFormPrintView treatmentFormView = new ChargeFormPrintView();
        treatmentFormView.setId(AbcIdUtils.getUUID());
        treatmentFormView.setSourceFormType(Constants.SourceFormType.TREATMENT);
        treatmentFormView.setPrintFormType(Constants.PrintFormType.TREATMENT);

        treatmentFormView.addChargeFormItemPrintViews(treatmentFormItemViews);
        ChargeFormItemPrintView adjustmentItemPrintView = generateAdjustmentPrintView(adjustmentFee, refundAdjustmentFee);
        if (adjustmentItemPrintView != null) {
            treatmentFormView.addChargeFormItemPrintView(adjustmentItemPrintView);
        }
        if (treatmentFormView.getChargeFormItems().size() > 0) {
            treatmentFormView.calculateTotalPrice();
            chargeFormPrintViews.add(treatmentFormView);
        }

        //其他费用
        ChargeFormPrintView otherFormView = new ChargeFormPrintView();
        otherFormView.setId(AbcIdUtils.getUUID());
        otherFormView.setSourceFormType(Constants.SourceFormType.OTHER_FEE);
        otherFormView.setPrintFormType(Constants.PrintFormType.OTHER_FEE);

        otherFormView.addChargeFormItemPrintViews(inTreatmentFormOtherFormItemViews);
        otherFormView.addChargeFormItemPrintViews(generateNonMedicinePrintView(formProcessors, Constants.ProductType.OTHER_FEE, itemFilterStatus));
        otherFormView.calculateTotalPrice();
        if (otherFormView.getChargeFormItems().size() > 0) {
            chargeFormPrintViews.add(otherFormView);
        }

        //材料商品，非药店管家，需要把输注、外治处方的耗材从form中移到 耗材的from中
        if (hisType != Organ.HisType.CIS_HIS_TYPE_PHARMACY) {
            ChargeFormPrintView materialFormView = new ChargeFormPrintView();
            materialFormView.setId(AbcIdUtils.getUUID());
            materialFormView.setSourceFormType(Constants.SourceFormType.MATERIAL);
            materialFormView.setPrintFormType(Constants.PrintFormType.MATERIAL);
            materialFormView.addChargeFormItemPrintViews(generateNonMedicinePrintView(formProcessors, Constants.ProductType.MATERIAL, itemFilterStatus));
            materialFormView.addChargeFormItemPrintViews(generateNonMedicinePrintView(formProcessors, Constants.ProductType.SALE_PRODUCT, itemFilterStatus));
            materialFormView.calculateTotalPrice();
            if (materialFormView.getChargeFormItems().size() > 0) {
                chargeFormPrintViews.add(materialFormView);
            }
        } else {
            ChargeFormPrintView materialFormView = new ChargeFormPrintView();
            materialFormView.setId(AbcIdUtils.getUUID());
            materialFormView.setSourceFormType(Constants.SourceFormType.MATERIAL);
            materialFormView.setPrintFormType(Constants.PrintFormType.MATERIAL);
            materialFormView.addChargeFormItemPrintViews(generateNonMedicinePrintView(formProcessors, Constants.ProductType.MATERIAL, itemFilterStatus));
            materialFormView.calculateTotalPrice();
            if (materialFormView.getChargeFormItems().size() > 0) {
                chargeFormPrintViews.add(materialFormView);
            }
            ChargeFormPrintView productFormView = new ChargeFormPrintView();
            productFormView.setId(AbcIdUtils.getUUID());
            productFormView.setSourceFormType(Constants.SourceFormType.ADDITIONAL_SALE_PRODUCT_FORM);
            productFormView.setPrintFormType(Constants.PrintFormType.ADDITIONAL_SALE_PRODUCT_FORM);
            productFormView.addChargeFormItemPrintViews(generateNonMedicinePrintView(formProcessors, Constants.ProductType.SALE_PRODUCT, itemFilterStatus));
            productFormView.calculateTotalPrice();
            if (productFormView.getChargeFormItems().size() > 0) {
                chargeFormPrintViews.add(productFormView);
            }
        }

        //套餐处理
        ChargeFormPrintView composeProductFormView = new ChargeFormPrintView();
        composeProductFormView.setId(AbcIdUtils.getUUID());
        composeProductFormView.setSourceFormType(Constants.SourceFormType.COMPOSE_PRODUCT);
        composeProductFormView.setPrintFormType(Constants.PrintFormType.COMPOSE_PRODUCT);
        composeProductFormView.addChargeFormItemPrintViews(generateNonMedicinePrintView(formProcessors, Constants.ProductType.COMPOSE_PRODUCT, itemFilterStatus));
        composeProductFormView.calculateTotalPrice();
        if (composeProductFormView.getChargeFormItems().size() > 0) {
            chargeFormPrintViews.add(composeProductFormView);
        }

        //配镜处方
        ChargeFormPrintView eyeFormView = new ChargeFormPrintView();
        eyeFormView.setId(AbcIdUtils.getUUID());
        eyeFormView.setSourceFormType(Constants.SourceFormType.PRESCRIPTION_EYE);
        eyeFormView.setPrintFormType(Constants.PrintFormType.PRESCRIPTION_EYE);
        eyeFormView.addChargeFormItemPrintViews(generateNonMedicinePrintView(formProcessors, Constants.ProductType.EYE, itemFilterStatus));
        eyeFormView.calculateTotalPrice();
        if (eyeFormView.getChargeFormItems().size() > 0) {
            chargeFormPrintViews.add(eyeFormView);
        }

        //护理医嘱
        ChargeFormPrintView nursingFormView = new ChargeFormPrintView();
        nursingFormView.setId(AbcIdUtils.getUUID());
        nursingFormView.setSourceFormType(Constants.SourceFormType.NURSING);
        nursingFormView.setPrintFormType(Constants.PrintFormType.NURSING);
        nursingFormView.addChargeFormItemPrintViews(generateNonMedicinePrintView(formProcessors, Constants.ProductType.NURSE, itemFilterStatus));
        nursingFormView.calculateTotalPrice();
        if (nursingFormView.getChargeFormItems().size() > 0) {
            chargeFormPrintViews.add(nursingFormView);
        }

        //手术
        ChargeFormPrintView surgeryFormView = new ChargeFormPrintView();
        surgeryFormView.setId(AbcIdUtils.getUUID());
        surgeryFormView.setSourceFormType(Constants.SourceFormType.SURGERY);
        surgeryFormView.setPrintFormType(Constants.PrintFormType.SURGERY);
        surgeryFormView.addChargeFormItemPrintViews(generateNonMedicinePrintView(formProcessors, Constants.ProductType.SURGERY, itemFilterStatus));
        surgeryFormView.calculateTotalPrice();
        if (surgeryFormView.getChargeFormItems().size() > 0) {
            chargeFormPrintViews.add(surgeryFormView);
        }

        // 中药处方上增加取药时间
        Map<String, ChargeSheetProcessInfo> processMap = formProcessors.stream()
                .filter(form -> form.getSourceFormType() == Constants.SourceFormType.PROCESS && Objects.nonNull(form.getChargeSheetProcessInfo()) && Objects.nonNull(form.getChargeSheetProcessInfo().getChargeFormId()))
                .map(FormProcessor::getChargeSheetProcessInfo)
                .collect(Collectors.toMap(ChargeSheetProcessInfo::getChargeFormId, Function.identity(), (k1, k2) -> k1.getLastModified().isAfter(k2.getLastModified()) ? k1 : k2));
        //处方
        List<ChargeFormPrintView> prescriptionFormPrintViewList = formProcessors
                .stream()
                .sorted((a, b) -> {
                    if (a.getSourceFormType() == b.getSourceFormType()) {
                        return ObjectUtils.compare(a.getSort(), b.getSort());
                    } else {
                        return ObjectUtils.compare(a.getSourceFormType(), b.getSourceFormType());
                    }
                })
                .filter(FormProcessor::isMedicalToPrintToClient)
                .map(formProcessor -> {
                    ChargeFormPrintView view = formProcessor.generateMedicineMaterialProductPrintView((formProcessor1, itemProcessor) -> itemProcessor.generatePrintView(itemFilterStatus));
                    if (Objects.nonNull(view)) {
                        // 只有本地药房 + 中药才处理加工信息
                        if (formProcessor.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE && formProcessor.getPharmacyType() == GoodsConst.PharmacyType.LOCAL_PHARMACY && processMap.containsKey(view.getId())) {
                            ChargeSheetProcessInfo process = processMap.get(view.getId());
                            if (Objects.nonNull(process)) {
                                view.setTakeMedicationTime(process.getTakeMedicationTime());
                            }
                        }
                    }
                    return view;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        chargeFormPrintViews.addAll(prescriptionFormPrintViewList);

        updateGoodsFeeChildProductType(chargeFormPrintViews, hisType);

        return chargeFormPrintViews;
    }

    private static void updateGoodsFeeChildProductType(List<ChargeFormPrintView> chargeFormPrintViews, int hisType) {

        if (hisType != Organ.HisType.CIS_HIS_TYPE_NORMAL
                && hisType != Organ.HisType.CIS_HIS_TYPE_DENTISTRY
                && hisType != Organ.HisType.CIS_HIS_TYPE_EYE) {
            return;
        }

        if (CollectionUtils.isEmpty(chargeFormPrintViews)) {
            return;
        }

        chargeFormPrintViews.stream()
                .filter(chargeFormPrintView -> chargeFormPrintView.getChargeFormItems() != null)
                .flatMap(chargeFormPrintView -> chargeFormPrintView.getChargeFormItems().stream())
                .filter(chargeFormItemPrintView -> ChargeFormItemUtils.isParentItem(chargeFormItemPrintView.getComposeType(), chargeFormItemPrintView.getGoodsFeeType()))
                .forEach(ChargeSheetFeeProtocol::updateItemGoodsFeeChildProductType);
    }

    private static void updateItemGoodsFeeChildProductType(ChargeFormItemPrintView chargeFormItemPrintView) {

        if (CollectionUtils.isEmpty(chargeFormItemPrintView.getComposeChildren())) {
            return;
        }


        if (chargeFormItemPrintView.getGoodsFeeType() == GoodsFeeType.FEE_PARENT) {
            chargeFormItemPrintView.getComposeChildren()
                    .forEach(child -> {
                        child.setProductType(chargeFormItemPrintView.getProductType());
                        child.setProductSubType(chargeFormItemPrintView.getProductSubType());
                        child.setType(chargeFormItemPrintView.getType());
                        child.setFeeTypeId(chargeFormItemPrintView.getFeeTypeId());
                    });
            return;
        }

        if (chargeFormItemPrintView.getComposeType() == ComposeType.COMPOSE) {
            chargeFormItemPrintView.getComposeChildren()
                    .stream()
                    .filter(child -> ChargeFormItemUtils.isParentItem(child.getComposeType(), child.getGoodsFeeType()))
                    .forEach(ChargeSheetFeeProtocol::updateItemGoodsFeeChildProductType);
        }

    }

    public static List<ChargeFormPrintView> generatePharmacyChargeFormPrintViews(List<PharmacyFormProcessor> formProcessors, BigDecimal adjustmentFee, BigDecimal refundAdjustmentFee, int hisType) {

        List<ChargeFormPrintView> chargeFormPrintViews = new ArrayList<>();

        //挂号费
        ChargeFormPrintView registrationFormView = new ChargeFormPrintView();
        registrationFormView.setId(AbcIdUtils.getUUID());
        registrationFormView.setSourceFormType(Constants.SourceFormType.REGISTRATION);
        registrationFormView.setPrintFormType(Constants.PrintFormType.REGISTRATION);
        registrationFormView.addChargeFormItemPrintViews(generatePharmacyNonMedicinePrintView(formProcessors, Constants.ProductType.REGISTRATION));
        registrationFormView.calculateTotalPrice();
        if (registrationFormView.getChargeFormItems().size() > 0) {
            chargeFormPrintViews.add(registrationFormView);
        }

        //快递费
        ChargeFormPrintView expressDeliveryFormView = new ChargeFormPrintView();
        expressDeliveryFormView.setId(AbcIdUtils.getUUID());
        expressDeliveryFormView.setSourceFormType(Constants.SourceFormType.EXPRESS_DELIVERY);
        expressDeliveryFormView.setPrintFormType(Constants.PrintFormType.EXPRESS_DELIVERY);
        expressDeliveryFormView.addChargeFormItemPrintViews(generatePharmacyNonMedicinePrintView(formProcessors, Constants.ProductType.EXPRESS_DELIVERY));
        expressDeliveryFormView.calculateTotalPrice();
        if (expressDeliveryFormView.getChargeFormItems().size() > 0) {
            chargeFormPrintViews.add(expressDeliveryFormView);
        }

        //签约费
        ChargeFormPrintView familyDoctorSignFormView = new ChargeFormPrintView();
        familyDoctorSignFormView.setId(AbcIdUtils.getUUID());
        familyDoctorSignFormView.setSourceFormType(Constants.SourceFormType.FAMILY_DOCTOR_SIGN);
        familyDoctorSignFormView.setPrintFormType(Constants.PrintFormType.FAMILY_DOCTOR_SIGN);
        familyDoctorSignFormView.addChargeFormItemPrintViews(generatePharmacyNonMedicinePrintView(formProcessors, Constants.ProductType.FAMILY_DOCTOR_SIGN));
        familyDoctorSignFormView.calculateTotalPrice();
        if (familyDoctorSignFormView.getChargeFormItems().size() > 0) {
            chargeFormPrintViews.add(familyDoctorSignFormView);
        }

        //加工费
        ChargeFormPrintView decoctionFormView = new ChargeFormPrintView();
        decoctionFormView.setId(AbcIdUtils.getUUID());
        decoctionFormView.setSourceFormType(Constants.SourceFormType.PROCESS);
        decoctionFormView.setPrintFormType(Constants.PrintFormType.PROCESS);
        decoctionFormView.addChargeFormItemPrintViews(generatePharmacyNonMedicinePrintView(formProcessors, Constants.ProductType.PROCESS));
        decoctionFormView.calculateTotalPrice();
        if (decoctionFormView.getChargeFormItems().size() > 0) {
            chargeFormPrintViews.add(decoctionFormView);
        }

        //咨询费
        ChargeFormPrintView onlineConsultationFormView = new ChargeFormPrintView();
        onlineConsultationFormView.setId(AbcIdUtils.getUUID());
        onlineConsultationFormView.setSourceFormType(Constants.SourceFormType.ONLINE_CONSULTATION);
        onlineConsultationFormView.setPrintFormType(Constants.PrintFormType.ONLINE_CONSULTATION);
        onlineConsultationFormView.addChargeFormItemPrintViews(generatePharmacyNonMedicinePrintView(formProcessors, Constants.ProductType.ONLINE_CONSULTATION));
        onlineConsultationFormView.calculateTotalPrice();
        if (onlineConsultationFormView.getChargeFormItems().size() > 0) {
            chargeFormPrintViews.add(onlineConsultationFormView);
        }

        //检查检验
        ChargeFormPrintView examinationFormView = new ChargeFormPrintView();
        examinationFormView.setId(AbcIdUtils.getUUID());
        examinationFormView.setSourceFormType(Constants.SourceFormType.EXAMINATION);
        examinationFormView.setPrintFormType(Constants.SourceFormType.EXAMINATION);
        examinationFormView.addChargeFormItemPrintViews(generatePharmacyNonMedicinePrintView(formProcessors, Constants.ProductType.EXAMINATION));
        examinationFormView.calculateTotalPrice();
        if (examinationFormView.getChargeFormItems().size() > 0) {
            chargeFormPrintViews.add(examinationFormView);
        }


        List<ChargeFormItemPrintView> allTreatmentFormItemViews = generatePharmacyNonMedicinePrintView(formProcessors, Constants.ProductType.TREATMENT);
        Map<Boolean, List<ChargeFormItemPrintView>> treatmentFormItemViewMap = allTreatmentFormItemViews.stream()
                .collect(Collectors.groupingBy(chargeFormItemPrintView -> chargeFormItemPrintView.getProductType() == Constants.ProductType.TREATMENT && chargeFormItemPrintView.getProductSubType() == Constants.ProductType.SubType.TREATMENT_OTHER));

        List<ChargeFormItemPrintView> inTreatmentFormOtherFormItemViews = treatmentFormItemViewMap.getOrDefault(Boolean.TRUE, new ArrayList<>());
        List<ChargeFormItemPrintView> treatmentFormItemViews = treatmentFormItemViewMap.getOrDefault(Boolean.FALSE, new ArrayList<>());

        //治疗理疗
        ChargeFormPrintView treatmentFormView = new ChargeFormPrintView();
        treatmentFormView.setId(AbcIdUtils.getUUID());
        treatmentFormView.setSourceFormType(Constants.SourceFormType.TREATMENT);
        treatmentFormView.setPrintFormType(Constants.PrintFormType.TREATMENT);

        treatmentFormView.addChargeFormItemPrintViews(treatmentFormItemViews);
        ChargeFormItemPrintView adjustmentItemPrintView = generateAdjustmentPrintView(adjustmentFee, refundAdjustmentFee);
        if (adjustmentItemPrintView != null) {
            treatmentFormView.addChargeFormItemPrintView(adjustmentItemPrintView);
        }
        if (treatmentFormView.getChargeFormItems().size() > 0) {
            treatmentFormView.calculateTotalPrice();
            chargeFormPrintViews.add(treatmentFormView);
        }

        //其他费用
        ChargeFormPrintView otherFormView = new ChargeFormPrintView();
        otherFormView.setId(AbcIdUtils.getUUID());
        otherFormView.setSourceFormType(Constants.SourceFormType.OTHER_FEE);
        otherFormView.setPrintFormType(Constants.PrintFormType.OTHER_FEE);

        otherFormView.addChargeFormItemPrintViews(inTreatmentFormOtherFormItemViews);
        otherFormView.addChargeFormItemPrintViews(generatePharmacyNonMedicinePrintView(formProcessors, Constants.ProductType.OTHER_FEE));
        otherFormView.calculateTotalPrice();
        if (otherFormView.getChargeFormItems().size() > 0) {
            chargeFormPrintViews.add(otherFormView);
        }

        //材料商品
        if (hisType != Organ.HisType.CIS_HIS_TYPE_PHARMACY) {
            ChargeFormPrintView materialFormView = new ChargeFormPrintView();
            materialFormView.setId(AbcIdUtils.getUUID());
            materialFormView.setSourceFormType(Constants.SourceFormType.MATERIAL);
            materialFormView.setPrintFormType(Constants.PrintFormType.MATERIAL);
            materialFormView.addChargeFormItemPrintViews(generatePharmacyNonMedicinePrintView(formProcessors, Constants.ProductType.MATERIAL));
            materialFormView.addChargeFormItemPrintViews(generatePharmacyNonMedicinePrintView(formProcessors, Constants.ProductType.SALE_PRODUCT));
            materialFormView.calculateTotalPrice();
            if (materialFormView.getChargeFormItems().size() > 0) {
                chargeFormPrintViews.add(materialFormView);
            }
        } else {
            ChargeFormPrintView materialFormView = new ChargeFormPrintView();
            materialFormView.setId(AbcIdUtils.getUUID());
            materialFormView.setSourceFormType(Constants.SourceFormType.MATERIAL);
            materialFormView.setPrintFormType(Constants.PrintFormType.MATERIAL);
            materialFormView.addChargeFormItemPrintViews(generatePharmacyNonMedicinePrintView(formProcessors, Constants.ProductType.MATERIAL));
            materialFormView.calculateTotalPrice();
            if (materialFormView.getChargeFormItems().size() > 0) {
                chargeFormPrintViews.add(materialFormView);
            }
            ChargeFormPrintView productFormView = new ChargeFormPrintView();
            productFormView.setId(AbcIdUtils.getUUID());
            productFormView.setSourceFormType(Constants.SourceFormType.ADDITIONAL_SALE_PRODUCT_FORM);
            productFormView.setPrintFormType(Constants.PrintFormType.ADDITIONAL_SALE_PRODUCT_FORM);
            productFormView.addChargeFormItemPrintViews(generatePharmacyNonMedicinePrintView(formProcessors, Constants.ProductType.SALE_PRODUCT));
            productFormView.calculateTotalPrice();
            if (productFormView.getChargeFormItems().size() > 0) {
                chargeFormPrintViews.add(productFormView);
            }
        }

        //套餐处理
        ChargeFormPrintView composeProductFormView = new ChargeFormPrintView();
        composeProductFormView.setId(AbcIdUtils.getUUID());
        composeProductFormView.setSourceFormType(Constants.SourceFormType.COMPOSE_PRODUCT);
        composeProductFormView.setPrintFormType(Constants.PrintFormType.COMPOSE_PRODUCT);
        composeProductFormView.addChargeFormItemPrintViews(generatePharmacyNonMedicinePrintView(formProcessors, Constants.ProductType.COMPOSE_PRODUCT));
        composeProductFormView.calculateTotalPrice();
        if (composeProductFormView.getChargeFormItems().size() > 0) {
            chargeFormPrintViews.add(composeProductFormView);
        }

        //配镜处方
        ChargeFormPrintView eyeFormView = new ChargeFormPrintView();
        eyeFormView.setId(AbcIdUtils.getUUID());
        eyeFormView.setSourceFormType(Constants.SourceFormType.PRESCRIPTION_EYE);
        eyeFormView.setPrintFormType(Constants.PrintFormType.PRESCRIPTION_EYE);
        eyeFormView.addChargeFormItemPrintViews(generatePharmacyNonMedicinePrintView(formProcessors, Constants.ProductType.EYE));
        eyeFormView.calculateTotalPrice();
        if (eyeFormView.getChargeFormItems().size() > 0) {
            chargeFormPrintViews.add(eyeFormView);
        }

        //护理医嘱
        ChargeFormPrintView nursingFormView = new ChargeFormPrintView();
        nursingFormView.setId(AbcIdUtils.getUUID());
        nursingFormView.setSourceFormType(Constants.SourceFormType.NURSING);
        nursingFormView.setPrintFormType(Constants.PrintFormType.NURSING);
        nursingFormView.addChargeFormItemPrintViews(generatePharmacyNonMedicinePrintView(formProcessors, Constants.ProductType.NURSE));
        nursingFormView.calculateTotalPrice();
        if (nursingFormView.getChargeFormItems().size() > 0) {
            chargeFormPrintViews.add(nursingFormView);
        }

        //手术
        ChargeFormPrintView surgeryFormView = new ChargeFormPrintView();
        surgeryFormView.setId(AbcIdUtils.getUUID());
        surgeryFormView.setSourceFormType(Constants.SourceFormType.SURGERY);
        surgeryFormView.setPrintFormType(Constants.PrintFormType.SURGERY);
        surgeryFormView.addChargeFormItemPrintViews(generatePharmacyNonMedicinePrintView(formProcessors, Constants.ProductType.SURGERY));
        surgeryFormView.calculateTotalPrice();
        if (surgeryFormView.getChargeFormItems().size() > 0) {
            chargeFormPrintViews.add(surgeryFormView);
        }

        //处方
        List<ChargeFormPrintView> prescriptionFormPrintViewList = formProcessors
                .stream()
                .sorted((a, b) -> {
                    if (a.getSourceFormType() == b.getSourceFormType()) {
                        return ObjectUtils.compare(a.getSort(), b.getSort());
                    } else {
                        return ObjectUtils.compare(a.getSourceFormType(), b.getSourceFormType());
                    }
                })
                .filter(PharmacyFormProcessor::isMedicalToPrintToClient)
                .map(formProcessor -> formProcessor.generateMedicineMaterialProductPrintView((formProcessor1, itemProcessor) -> itemProcessor.generatePrintView()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        chargeFormPrintViews.addAll(prescriptionFormPrintViewList);

        // 药店赠品
        ChargeFormPrintView giftFormView = new ChargeFormPrintView();
        giftFormView.setId(AbcIdUtils.getUUID());
        giftFormView.setSourceFormType(Constants.SourceFormType.GIFT_PRODUCT);
        giftFormView.setPrintFormType(Constants.PrintFormType.GIFT_PRODUCT);
        giftFormView.addChargeFormItemPrintViews(generatePharmacyGiftMedicinePrintView(formProcessors));
        giftFormView.calculateTotalPrice();
        if (!CollectionUtils.isEmpty(giftFormView.getChargeFormItems())) {
            chargeFormPrintViews.add(giftFormView);
        }

        updateGoodsFeeChildProductType(chargeFormPrintViews, hisType);
        return chargeFormPrintViews;
    }

    private static List<PromotionCardBalanceView> buildChargeBalanceViews(SheetProcessorDispatcher.ISheetProcessor sheetProcessor, ChargeSheet chargeSheet) {

        List<PromotionCardBalanceView> chargeBalanceViews = new ArrayList<>();
        if (chargeSheet == null || CollectionUtils.isEmpty(chargeSheet.getChargeTransactions())) {
            return chargeBalanceViews;
        }

        List<ChargeTransaction> chargeTransactionCards = chargeSheet.getChargeTransactions()
                .stream()
                .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.PROMOTION_CARD)
                .filter(chargeTransaction -> org.apache.commons.lang3.StringUtils.isNotEmpty(chargeTransaction.getThirdPartyPayCardId()))
                .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                .sorted(Comparator.comparing(ChargeTransaction::getCreated).reversed())
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(chargeTransactionCards)) {

            Map<String, List<ChargeTransaction>> transactionMap = chargeTransactionCards.stream()
                    .collect(Collectors.groupingBy(ChargeTransaction::getThirdPartyPayCardId));


            buildChargeBalance(sheetProcessor.getSheetProcessorInfoProvider(), chargeSheet, chargeBalanceViews, chargeTransactionCards, transactionMap);
        }

        return chargeBalanceViews;
    }

    private static void buildChargeBalance(SheetProcessorInfoProvider infoProvider, ChargeSheet chargeSheet, List<PromotionCardBalanceView> chargeBalanceViews, List<ChargeTransaction> chargeTransactionCards, Map<String, List<ChargeTransaction>> transactionMap) {
        Map<Long, Integer> payModeMaps = getPayModeTypes(infoProvider, chargeSheet.getChainId());
        Map<String, ChargeAction.PayActionInfo> actionInfoMap = ChargeUtils.generatePayModeNameMap(chargeSheet.getChargeActions(), payModeMaps);

        for (Map.Entry<String, List<ChargeTransaction>> entry : transactionMap.entrySet()) {

            List<ChargeTransaction> chargeTransactions = entry.getValue();
            PromotionCardBalanceView promotionCardBalanceView = new PromotionCardBalanceView();
            ChargeAction.PayActionInfo payActionInfo;
            BigDecimal beginBalance = BigDecimal.ZERO;
            BigDecimal remainingBalance;

            ChargeTransaction chargeTransactionOne = chargeTransactions.get(0);
            chargeTransactionOne.setPayModeType(payModeMaps.getOrDefault(Long.valueOf(chargeTransactionOne.getPayMode()), 0));
            payActionInfo = actionInfoMap.get(ChargeUtils.generateTransactionUniqueKey(chargeTransactionOne));
            if (payActionInfo != null) {
                promotionCardBalanceView.setName(payActionInfo.getPayModeDisplayName());
            }

            remainingBalance = chargeTransactionOne.getThirdPartyPayCardBalance();

            for (ChargeTransaction chargeTransaction : chargeTransactions) {

                if (beginBalance.compareTo(BigDecimal.ZERO) == 0) {
                    beginBalance = remainingBalance.add(chargeTransaction.getAmount());
                } else {
                    beginBalance = beginBalance.add(chargeTransaction.getAmount());
                }
            }
            promotionCardBalanceView.setBeginBalance(beginBalance);
            promotionCardBalanceView.setRemainingBalance(remainingBalance);
            chargeBalanceViews.add(promotionCardBalanceView);
        }
    }

    /**
     * 绑定药品的厂家，批号，效期
     *
     * @param chargeSheet
     * @param chargeSheetPrintView
     * @param sheetProcessorInfoProvider
     */
    private static void bindGoodsDispenseInfo(ChargeSheet chargeSheet, ChargeSheetPrintView chargeSheetPrintView, boolean isUnDispense, SheetProcessorInfoProvider sheetProcessorInfoProvider) {

        if (chargeSheetPrintView == null || org.apache.commons.collections.CollectionUtils.isEmpty(chargeSheetPrintView.getChargeForms())) {
            return;
        }

        List<DispensingSheet> dispensingSheets = sheetProcessorInfoProvider.getDispensingInfoProvider().getDispensingSheets(chargeSheet.getId());

        if (org.apache.commons.collections.CollectionUtils.isEmpty(dispensingSheets)) {
            return;
        }

        Map<String, String> dispensingFormItemIdMap = new HashMap<>();

        List<GetDispenseGoodsStockInfoReq.DispensingFormItem> items = dispensingSheets.stream()
                .filter(dispensingSheet -> org.apache.commons.collections.CollectionUtils.isNotEmpty(dispensingSheet.getDispensingForms()))
                .flatMap(dispensingSheet -> dispensingSheet.getDispensingForms().stream())
                .filter(dispensingForm -> org.apache.commons.collections.CollectionUtils.isNotEmpty(dispensingForm.getDispensingFormItems()))
                .flatMap(dispensingForm -> dispensingForm.getDispensingFormItems().stream())
                .filter(dispensingFormItem -> dispensingFormItem.getStatus() == Constants.DispensingFormItemStatus.DISPENSED || dispensingFormItem.getStatus() == Constants.DispensingFormItemStatus.UNDISPENSED)
                .map(dispensingFormItem -> {

                    String dispensingFormItemId = dispensingFormItem.getId();
                    if (dispensingFormItem.getStatus() == Constants.DispensingFormItemStatus.UNDISPENSED) {
                        dispensingFormItemId = dispensingFormItem.getAssociateFormItemId();
                    }

                    if (org.apache.commons.lang3.StringUtils.isNotEmpty(dispensingFormItem.getSourceFormItemId())) {
                        dispensingFormItemIdMap.put(dispensingFormItem.getSourceFormItemId(), dispensingFormItemId);
                    }
                    GetDispenseGoodsStockInfoReq.DispensingFormItem item = new GetDispenseGoodsStockInfoReq.DispensingFormItem();
                    item.setDispenseSheetId(dispensingFormItem.getDispensingSheetId());
                    item.setDispenseItemId(dispensingFormItemId);
                    item.setGoodsId(dispensingFormItem.getProductId());
                    return item;
                }).collect(Collectors.toList());

        if (org.apache.commons.collections.CollectionUtils.isEmpty(items)) {
            return;
        }

        GetDispenseGoodsStockInfoReq req = new GetDispenseGoodsStockInfoReq();
        req.setPatientOrderId(chargeSheet.getPatientOrderId());
        req.setChainId(chargeSheet.getChainId());
        req.setClinicId(chargeSheet.getClinicId());
        req.setList(items);
        req.setUnDispense(isUnDispense ? 1 : 0);

        Map<String, GetDispenseGoodsStockInfoRsp.DispensingGoodsStockItem> dispenseGoodsStockInfoMap = sheetProcessorInfoProvider.getGoodsScProvider().getDispensingGoodsStockItemMap(req);

        if (MapUtils.isEmpty(dispenseGoodsStockInfoMap)) {
            return;
        }

        //bind
        chargeSheetPrintView.getChargeForms()
                .stream()
                .filter(chargeFormPrintView -> org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeFormPrintView.getChargeFormItems()))
                .flatMap(chargeFormPrintView -> chargeFormPrintView.getChargeFormItems().stream())
                .forEach(chargeFormItemPrintView -> {
                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeFormItemPrintView.getComposeChildren())) {
                        chargeFormItemPrintView.getComposeChildren()
                                .forEach(child -> addDispensingGoodsStockInfo(child, dispensingFormItemIdMap, dispenseGoodsStockInfoMap));
                        return;
                    }
                    addDispensingGoodsStockInfo(chargeFormItemPrintView, dispensingFormItemIdMap, dispenseGoodsStockInfoMap);
                });

    }

    private static void addDispensingGoodsStockInfo(ChargeFormItemPrintView chargeFormItemPrintView, Map<String, String> dispensingFormItemIdMap, Map<String, GetDispenseGoodsStockInfoRsp.DispensingGoodsStockItem> dispenseGoodsStockInfoMap) {
        String dispensingFormItemId = dispensingFormItemIdMap.getOrDefault(chargeFormItemPrintView.getId(), null);

        if (org.apache.commons.lang.StringUtils.isEmpty(dispensingFormItemId)) {
            return;
        }

        GetDispenseGoodsStockInfoRsp.DispensingGoodsStockItem dispensingGoodsStockItem = dispenseGoodsStockInfoMap.getOrDefault(dispensingFormItemId, null);

        if (dispensingGoodsStockItem == null) {
            return;
        }

        chargeFormItemPrintView.setGoodsStockInfos(dispensingGoodsStockItem.getList());
    }


    /**
     * 打印退费View
     * 前置：chargeSheet已经完成了算费
     */
    public static ChargeSheetPrintView toRefundPrintView(SheetProcessor sheetProcessor) {
        //初始化
        ChargeSheet chargeSheet = sheetProcessor.chargeSheet;
        SheetProcessorInfoProvider sheetProcessorInfoProvider = sheetProcessor.sheetProcessorInfoProvider;
        PatientInfo patientInfo = sheetProcessor.getPatientInfo();
        List<FormProcessor> formProcessors = sheetProcessor.formProcessors;
        BigDecimal adjustmentFee = sheetProcessor.adjustmentFee;
        BigDecimal refundAdjustmentFee = sheetProcessor.refundAdjustmentFee;
        BigDecimal totalFee = sheetProcessor.totalFee;
        BigDecimal refundTotalFee = sheetProcessor.refundTotalFee;
        BigDecimal refundDiscountFee = sheetProcessor.refundDiscountFee;
        BigDecimal discountFee = sheetProcessor.discountFee;
        BigDecimal _refundFee = sheetProcessor._refundFee;
        BigDecimal _receivedFee = sheetProcessor._receivedFee;
        PatientOrder patientOrder = sheetProcessor.patientOrder;
        sheetProcessor.updateGoodsFeeTypeView();
        cn.abcyun.bis.rpc.sdk.cis.model.clinic.OrganPrintView organ = sheetProcessorInfoProvider.getProductInfoProvider().getOrganPrintInfo(chargeSheet.getClinicId());

        ChargeSheetPrintView chargeSheetPrintView = new ChargeSheetPrintView();
        chargeSheetPrintView.setId(chargeSheet.getId());
        chargeSheetPrintView.setPatient(patientInfo);
        chargeSheetPrintView.setOrgan(organ != null ? new OrganPrintView(organ.getId(), organ.getName(), organ.getName(), organ.getAddressDetail(), organ.getContactPhone(), organ.getLogo(), organ.getCategory(), organ.getHisType()) : null);
        chargeSheetPrintView.setDiagnosedDate(chargeSheet.getDiagnosedDate());
        chargeSheetPrintView.setDiagnosis(Optional.ofNullable(chargeSheet.getAdditional()).map(ChargeSheetAdditional::getDiagnosis).orElse(null));
        chargeSheetPrintView.setDiagnosisInfos(Optional.ofNullable(chargeSheet.getAdditional()).map(ChargeSheetAdditional::getDiagnosisInfos).orElse(new ArrayList<>()));
        chargeSheetPrintView.setExtendDiagnosisInfos(Optional.ofNullable(chargeSheet.getAdditional()).map(ChargeSheetAdditional::getExtendDiagnosisInfos).orElse(new ArrayList<>()));
        chargeSheetPrintView.setMemberInfo(generateMemberPrintInfo(chargeSheet, sheetProcessorInfoProvider));

        //挂号费
        ChargeFormPrintView registrationFormView = new ChargeFormPrintView();
        registrationFormView.setId(AbcIdUtils.getUUID());
        registrationFormView.setSourceFormType(Constants.SourceFormType.REGISTRATION);
        registrationFormView.setPrintFormType(Constants.PrintFormType.REGISTRATION);
        registrationFormView.addChargeFormItemPrintViews(generateNonMedicineRefundPrintView(formProcessors, Constants.ProductType.REGISTRATION));
        registrationFormView.calculateTotalPrice();
        if (registrationFormView.getChargeFormItems().size() > 0) {
            chargeSheetPrintView.addChargeFormPrintView(registrationFormView);
        }

        //快递费
        ChargeFormPrintView expressDeliveryFormView = new ChargeFormPrintView();
        expressDeliveryFormView.setId(AbcIdUtils.getUUID());
        expressDeliveryFormView.setSourceFormType(Constants.SourceFormType.EXPRESS_DELIVERY);
        expressDeliveryFormView.setPrintFormType(Constants.PrintFormType.EXPRESS_DELIVERY);
        expressDeliveryFormView.addChargeFormItemPrintViews(generateNonMedicineRefundPrintView(formProcessors, Constants.ProductType.EXPRESS_DELIVERY));
        expressDeliveryFormView.calculateTotalPrice();
        if (expressDeliveryFormView.getChargeFormItems().size() > 0) {
            chargeSheetPrintView.addChargeFormPrintView(expressDeliveryFormView);
        }

        //签约费
        ChargeFormPrintView familyDoctorSignFormView = new ChargeFormPrintView();
        familyDoctorSignFormView.setId(AbcIdUtils.getUUID());
        familyDoctorSignFormView.setSourceFormType(Constants.SourceFormType.FAMILY_DOCTOR_SIGN);
        familyDoctorSignFormView.setPrintFormType(Constants.PrintFormType.FAMILY_DOCTOR_SIGN);
        familyDoctorSignFormView.addChargeFormItemPrintViews(generateNonMedicineRefundPrintView(formProcessors, Constants.ProductType.FAMILY_DOCTOR_SIGN));
        familyDoctorSignFormView.calculateTotalPrice();
        if (familyDoctorSignFormView.getChargeFormItems().size() > 0) {
            chargeSheetPrintView.addChargeFormPrintView(familyDoctorSignFormView);
        }

        //加工费
        ChargeFormPrintView decoctionFormView = new ChargeFormPrintView();
        decoctionFormView.setId(AbcIdUtils.getUUID());
        decoctionFormView.setSourceFormType(Constants.SourceFormType.PROCESS);
        decoctionFormView.setPrintFormType(Constants.PrintFormType.PROCESS);
        decoctionFormView.addChargeFormItemPrintViews(generateNonMedicineRefundPrintView(formProcessors, Constants.ProductType.PROCESS));
        decoctionFormView.calculateTotalPrice();
        if (decoctionFormView.getChargeFormItems().size() > 0) {
            chargeSheetPrintView.addChargeFormPrintView(decoctionFormView);
        }

        //咨询费
        ChargeFormPrintView onlineConsultationFormView = new ChargeFormPrintView();
        onlineConsultationFormView.setId(AbcIdUtils.getUUID());
        onlineConsultationFormView.setSourceFormType(Constants.SourceFormType.ONLINE_CONSULTATION);
        onlineConsultationFormView.setPrintFormType(Constants.PrintFormType.ONLINE_CONSULTATION);
        onlineConsultationFormView.addChargeFormItemPrintViews(generateNonMedicineRefundPrintView(formProcessors, Constants.ProductType.ONLINE_CONSULTATION));
        onlineConsultationFormView.calculateTotalPrice();
        if (onlineConsultationFormView.getChargeFormItems().size() > 0) {
            chargeSheetPrintView.addChargeFormPrintView(onlineConsultationFormView);
        }

        //检查检验
        ChargeFormPrintView examinationFormView = new ChargeFormPrintView();
        examinationFormView.setId(AbcIdUtils.getUUID());
        examinationFormView.setSourceFormType(Constants.SourceFormType.EXAMINATION);
        examinationFormView.setPrintFormType(Constants.SourceFormType.EXAMINATION);
        examinationFormView.addChargeFormItemPrintViews(generateNonMedicineRefundPrintView(formProcessors, Constants.ProductType.EXAMINATION));
        examinationFormView.calculateTotalPrice();
        if (examinationFormView.getChargeFormItems().size() > 0) {
            chargeSheetPrintView.addChargeFormPrintView(examinationFormView);
        }


        List<ChargeFormItemPrintView> allTreatmentFormItemViews = generateNonMedicineRefundPrintView(formProcessors, Constants.ProductType.TREATMENT);
        Map<Boolean, List<ChargeFormItemPrintView>> treatmentFormItemViewMap = allTreatmentFormItemViews.stream()
                .collect(Collectors.groupingBy(chargeFormItemPrintView -> chargeFormItemPrintView.getProductType() == Constants.ProductType.TREATMENT && chargeFormItemPrintView.getProductSubType() == Constants.ProductType.SubType.TREATMENT_OTHER));

        List<ChargeFormItemPrintView> inTreatmentFormOtherFeeFormItemViews = treatmentFormItemViewMap.getOrDefault(Boolean.TRUE, new ArrayList<>());
        List<ChargeFormItemPrintView> treatmentFormItemViews = treatmentFormItemViewMap.getOrDefault(Boolean.FALSE, new ArrayList<>());

        //治疗理疗
        ChargeFormPrintView treatmentFormView = new ChargeFormPrintView();
        treatmentFormView.setId(AbcIdUtils.getUUID());
        treatmentFormView.setSourceFormType(Constants.SourceFormType.TREATMENT);
        treatmentFormView.setPrintFormType(Constants.PrintFormType.TREATMENT);

        treatmentFormView.addChargeFormItemPrintViews(treatmentFormItemViews);
        treatmentFormView.calculateTotalPrice();
        if (treatmentFormView.getChargeFormItems().size() > 0) {
            chargeSheetPrintView.addChargeFormPrintView(treatmentFormView);
        }

        //其他费用
        ChargeFormPrintView otherFormView = new ChargeFormPrintView();
        otherFormView.setId(AbcIdUtils.getUUID());
        otherFormView.setSourceFormType(Constants.SourceFormType.OTHER_FEE);
        otherFormView.setPrintFormType(Constants.PrintFormType.OTHER_FEE);

        otherFormView.addChargeFormItemPrintViews(inTreatmentFormOtherFeeFormItemViews);
        otherFormView.addChargeFormItemPrintViews(generateNonMedicineRefundPrintView(formProcessors, Constants.ProductType.OTHER_FEE));
        otherFormView.calculateTotalPrice();
        if (otherFormView.getChargeFormItems().size() > 0) {
            chargeSheetPrintView.addChargeFormPrintView(otherFormView);
        }

        //材料商品
        if (organ == null || organ.getHisType() != Organ.HisType.CIS_HIS_TYPE_PHARMACY) {
            ChargeFormPrintView materialFormView = new ChargeFormPrintView();
            materialFormView.setId(AbcIdUtils.getUUID());
            materialFormView.setSourceFormType(Constants.SourceFormType.MATERIAL);
            materialFormView.setPrintFormType(Constants.PrintFormType.MATERIAL);
            materialFormView.addChargeFormItemPrintViews(generateNonMedicineRefundPrintView(formProcessors, Constants.ProductType.MATERIAL));
            materialFormView.addChargeFormItemPrintViews(generateNonMedicineRefundPrintView(formProcessors, Constants.ProductType.SALE_PRODUCT));
            materialFormView.calculateTotalPrice();
            if (materialFormView.getChargeFormItems().size() > 0) {
                chargeSheetPrintView.addChargeFormPrintView(materialFormView);
            }
        } else {
            ChargeFormPrintView materialFormView = new ChargeFormPrintView();
            materialFormView.setId(AbcIdUtils.getUUID());
            materialFormView.setSourceFormType(Constants.SourceFormType.MATERIAL);
            materialFormView.setPrintFormType(Constants.PrintFormType.MATERIAL);
            materialFormView.addChargeFormItemPrintViews(generateNonMedicineRefundPrintView(formProcessors, Constants.ProductType.MATERIAL));
            materialFormView.calculateTotalPrice();
            if (materialFormView.getChargeFormItems().size() > 0) {
                chargeSheetPrintView.addChargeFormPrintView(materialFormView);
            }
            ChargeFormPrintView productFormView = new ChargeFormPrintView();
            productFormView.setId(AbcIdUtils.getUUID());
            productFormView.setSourceFormType(Constants.SourceFormType.ADDITIONAL_SALE_PRODUCT_FORM);
            productFormView.setPrintFormType(Constants.PrintFormType.ADDITIONAL_SALE_PRODUCT_FORM);
            productFormView.addChargeFormItemPrintViews(generateNonMedicineRefundPrintView(formProcessors, Constants.ProductType.SALE_PRODUCT));
            productFormView.calculateTotalPrice();
            if (productFormView.getChargeFormItems().size() > 0) {
                chargeSheetPrintView.addChargeFormPrintView(productFormView);
            }
        }

        //套餐处理
        ChargeFormPrintView composeProductFormView = new ChargeFormPrintView();
        composeProductFormView.setId(AbcIdUtils.getUUID());
        composeProductFormView.setSourceFormType(Constants.SourceFormType.COMPOSE_PRODUCT);
        composeProductFormView.setPrintFormType(Constants.PrintFormType.COMPOSE_PRODUCT);
        composeProductFormView.addChargeFormItemPrintViews(generateNonMedicineRefundPrintView(formProcessors, Constants.ProductType.COMPOSE_PRODUCT));
        composeProductFormView.calculateTotalPrice();
        if (composeProductFormView.getChargeFormItems().size() > 0) {
            chargeSheetPrintView.addChargeFormPrintView(composeProductFormView);
        }

        //眼镜处方处理
        ChargeFormPrintView eyeFormView = new ChargeFormPrintView();
        eyeFormView.setId(AbcIdUtils.getUUID());
        eyeFormView.setSourceFormType(Constants.SourceFormType.PRESCRIPTION_EYE);
        eyeFormView.setPrintFormType(Constants.PrintFormType.PRESCRIPTION_EYE);
        eyeFormView.addChargeFormItemPrintViews(generateNonMedicineRefundPrintView(formProcessors, Constants.ProductType.EYE));
        eyeFormView.calculateTotalPrice();
        if (eyeFormView.getChargeFormItems().size() > 0) {
            chargeSheetPrintView.addChargeFormPrintView(eyeFormView);
        }

        //护理医嘱
        ChargeFormPrintView nursingFormView = new ChargeFormPrintView();
        nursingFormView.setId(AbcIdUtils.getUUID());
        nursingFormView.setSourceFormType(Constants.SourceFormType.NURSING);
        nursingFormView.setPrintFormType(Constants.PrintFormType.NURSING);
        nursingFormView.addChargeFormItemPrintViews(generateNonMedicineRefundPrintView(formProcessors, Constants.ProductType.NURSE));
        nursingFormView.calculateTotalPrice();
        if (nursingFormView.getChargeFormItems().size() > 0) {
            chargeSheetPrintView.addChargeFormPrintView(nursingFormView);
        }

        //手术
        ChargeFormPrintView surgeryFormView = new ChargeFormPrintView();
        surgeryFormView.setId(AbcIdUtils.getUUID());
        surgeryFormView.setSourceFormType(Constants.SourceFormType.SURGERY);
        surgeryFormView.setPrintFormType(Constants.PrintFormType.SURGERY);
        surgeryFormView.addChargeFormItemPrintViews(generateNonMedicineRefundPrintView(formProcessors, Constants.ProductType.SURGERY));
        surgeryFormView.calculateTotalPrice();
        if (surgeryFormView.getChargeFormItems().size() > 0) {
            chargeSheetPrintView.addChargeFormPrintView(surgeryFormView);
        }

        //处方
        List<ChargeFormPrintView> prescriptionFormPrintViewList = formProcessors
                .stream()
                .sorted((a, b) -> {
                    if (a.getSourceFormType() == b.getSourceFormType()) {
                        return ObjectUtils.compare(a.getSort(), b.getSort());
                    } else {
                        return ObjectUtils.compare(a.getSourceFormType(), b.getSourceFormType());
                    }
                })
                .filter(formProcessor -> formProcessor.isMedicalToPrintToClient())
                .map(formProcessor -> formProcessor.generateMedicineMaterialProductPrintView((formProcessor1, itemProcessor) -> itemProcessor.generateRefundPrintView()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        chargeSheetPrintView.addChargeFormPrintViews(prescriptionFormPrintViewList);

        updateGoodsFeeChildProductType(chargeSheetPrintView.getChargeForms(), Optional.ofNullable(organ).map(cn.abcyun.bis.rpc.sdk.cis.model.clinic.OrganPrintView::getHisType).orElse(0));


        // 找到挂号处收费已支付金额信息
//        PriceCell registrationPriceCell = getRegistrationPaidPriceCell();
        BigDecimal netAdjustmentFee = adjustmentFee.subtract(refundAdjustmentFee);

        chargeSheetPrintView.setTotalFee(MathUtils.setScaleTwo(totalFee.subtract(refundTotalFee).add(netAdjustmentFee)));
        chargeSheetPrintView.setRefundFee(chargeSheet.getRefundFee());
        chargeSheetPrintView.setDiscountFee(MathUtils.setScaleTwo(discountFee.subtract(refundDiscountFee)));
        chargeSheetPrintView.setReceivableFee(MathUtils.setScaleTwo(MathUtils.wrapBigDecimalAdd(chargeSheetPrintView.getTotalFee(), chargeSheetPrintView.getDiscountFee())));
        chargeSheetPrintView.setNetIncomeFee(MathUtils.setScaleTwo(_receivedFee.add(_refundFee)));

        // 支付方式分类
        if (chargeSheet.getChargeTransactions() != null) {
            Map<Long, Integer> payModeMaps = getPayModeTypes(sheetProcessorInfoProvider, chargeSheet.getChainId());
            Map<String, ChargeAction.PayActionInfo> payModeNameMap = ChargeUtils.generatePayModeNameMap(chargeSheet.getChargeActions(), payModeMaps);
            chargeSheet.getChargeTransactions().forEach(chargeTransaction -> {
                chargeTransaction.setPayModeType(payModeMaps.getOrDefault(Long.valueOf(chargeTransaction.getPayMode()), 0));
            });
            List<ChargeTransactionPrintView> chargeTransactionPrintViews = chargeSheet.getChargeTransactions()
                    .stream()
                    .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                    .filter(chargeTransaction -> chargeTransaction.getPaySource() == Constants.ChargeSource.CHARGE || chargeTransaction.getPaySource() == Constants.ChargeSource.WE_CLINIC || chargeTransaction.getPaySource() == Constants.ChargeSource.DEVICE || chargeTransaction.getPaySource() == Constants.ChargeSource.WE_APP)
                    .filter(chargeTransaction -> MathUtils.wrapBigDecimalCompare(chargeTransaction.getAmount(), BigDecimal.ZERO) < 0)
                    .collect(Collectors.toMap(ChargeUtils::generateTransactionUniqueKey, chargeTransaction -> {
                        ChargeTransactionPrintView chargeTransactionPrintView = new ChargeTransactionPrintView();
                        chargeTransactionPrintView.setAmount(chargeTransaction.getAmount());
                        chargeTransactionPrintView.setPayMode(chargeTransaction.getPayMode());
                        chargeTransactionPrintView.setPaySubMode(chargeTransaction.getPaySubMode());
                        chargeTransactionPrintView.setThirdPartyPayCardId(chargeTransaction.getThirdPartyPayCardId());

                        return chargeTransactionPrintView;
                    }, (a, b) -> {
                        a.setAmount(MathUtils.wrapBigDecimalAdd(a.getAmount(), b.getAmount()));
                        return a;
                    }))
                    .entrySet()
                    .stream()
                    .map(entry -> {
                        String payModeKey = ChargeUtils.generatePayModeKey(entry.getValue().getPayMode(), entry.getValue().getPaySubMode(), payModeMaps.getOrDefault(Long.valueOf(entry.getValue().getPayMode()), 0), entry.getKey());
                        ChargeAction.PayActionInfo payActionInfo = payModeNameMap.getOrDefault(payModeKey, new ChargeAction.PayActionInfo());
                        entry.getValue().setPayModeName(payActionInfo.getPayModeName());
                        entry.getValue().setPaySubModeName(payActionInfo.getPaySubModeName());
                        return entry.getValue();
                    }).collect(Collectors.toList());
            chargeSheetPrintView.setChargeTransactions(chargeTransactionPrintViews);
        }
        // 设置个人现金支付
        chargeSheetPrintView.setPersonalPaymentFee(
                MathUtils.wrapBigDecimalSubtract(chargeSheetPrintView.getNetIncomeFee(), chargeSheetPrintView.getHealthCardPaymentFee())
        );

        // 药品分类汇总
        chargeSheetPrintView.buildSubTotals();
        chargeSheetPrintView.setWholeMedicalBills(generateWholeMedicalBills(chargeSheetPrintView.getChargeForms(), chargeSheet.getChainId(), sheetProcessorInfoProvider, sheetProcessor.getFeeTypeSortDtoMap()));

        // 如果有会员卡支付，找到最后一次会员卡支付的余额
        if (chargeSheet.getChargeTransactions() != null) {
            ChargeTransaction firstRefundMemberCardChargeTransaction = chargeSheet.getChargeTransactions()
                    .stream()
                    .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.MEMBER_CARD)
                    .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                    .filter(chargeTransaction -> MathUtils.wrapBigDecimalOrZero(chargeTransaction.getAmount()).compareTo(BigDecimal.ZERO) < 0)
                    .sorted((a, b) -> -ObjectUtils.compare(b.getCreated(), a.getCreated()))
                    .findFirst()
                    .orElse(null);

            ChargeTransaction lastRefundMemberCardChargeTransaction = chargeSheet.getChargeTransactions()
                    .stream()
                    .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.MEMBER_CARD)
                    .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                    .filter(chargeTransaction -> MathUtils.wrapBigDecimalOrZero(chargeTransaction.getAmount()).compareTo(BigDecimal.ZERO) < 0)
                    .sorted((a, b) -> -ObjectUtils.compare(a.getCreated(), b.getCreated()))
                    .findFirst()
                    .orElse(null);

            ChargeTransaction firstHealthCardChargeTransaction = chargeSheet.getChargeTransactions()
                    .stream()
                    .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.HEALTH_CARD)
                    .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                    .filter(chargeTransaction -> MathUtils.wrapBigDecimalOrZero(chargeTransaction.getAmount()).compareTo(BigDecimal.ZERO) >= 0)
                    .filter(chargeTransaction -> chargeTransaction.getPaySource() == Constants.ChargeSource.CHARGE || chargeTransaction.getPaySource() == Constants.ChargeSource.WE_CLINIC || chargeTransaction.getPaySource() == Constants.ChargeSource.DEVICE)
                    .sorted((a, b) -> -ObjectUtils.compare(b.getCreated(), a.getCreated()))
                    .findFirst()
                    .orElse(null);

            ChargeTransaction lastHealthCardChargeTransaction = chargeSheet.getChargeTransactions()
                    .stream()
                    .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.HEALTH_CARD)
                    .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                    .filter(chargeTransaction -> MathUtils.wrapBigDecimalOrZero(chargeTransaction.getAmount()).compareTo(BigDecimal.ZERO) >= 0)
                    .filter(chargeTransaction -> chargeTransaction.getPaySource() == Constants.ChargeSource.CHARGE || chargeTransaction.getPaySource() == Constants.ChargeSource.WE_CLINIC || chargeTransaction.getPaySource() == Constants.ChargeSource.DEVICE)
                    .sorted((a, b) -> -ObjectUtils.compare(a.getCreated(), b.getCreated()))
                    .findFirst()
                    .orElse(null);

            List<ChargeTransaction> healthCardChargeTransactions = chargeSheet.getChargeTransactions()
                    .stream()
                    .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.HEALTH_CARD)
                    .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                    .filter(chargeTransaction -> MathUtils.wrapBigDecimalOrZero(chargeTransaction.getAmount()).compareTo(BigDecimal.ZERO) >= 0)
                    .filter(chargeTransaction -> chargeTransaction.getPaySource() == Constants.ChargeSource.CHARGE || chargeTransaction.getPaySource() == Constants.ChargeSource.WE_CLINIC || chargeTransaction.getPaySource() == Constants.ChargeSource.DEVICE)
                    .sorted((a, b) -> -ObjectUtils.compare(b.getCreated(), a.getCreated()))
                    .collect(Collectors.toList());

            PatientInfo cisPatientInfo = sheetProcessorInfoProvider.getPatientInfoProvider().findPatientInfoById(chargeSheet.getChainId(), chargeSheet.getClinicId(), chargeSheet.getMemberId(), false, false);

            if (cisPatientInfo != null && firstRefundMemberCardChargeTransaction != null && lastRefundMemberCardChargeTransaction != null) {
                chargeSheetPrintView.setMemberCardBeginningBalance(MathUtils.wrapBigDecimalAdd(firstRefundMemberCardChargeTransaction.getThirdPartyPayCardBalance(),
                        firstRefundMemberCardChargeTransaction.getAmount()));

                chargeSheetPrintView.setMemberCardBalance(lastRefundMemberCardChargeTransaction.getThirdPartyPayCardBalance());
                chargeSheetPrintView.setMemberCardMobile(cisPatientInfo.getMobile());
            }


            //这份代码保留，只为了兼容老逻辑，未来会删除
            if (lastHealthCardChargeTransaction != null && firstHealthCardChargeTransaction != null) {
                chargeSheetPrintView.setHealthCardBalance(lastHealthCardChargeTransaction.getThirdPartyPayCardBalance());


                ThirdPartyPayInfo firstThirdPartyPayInfo = JsonUtils.readValue(firstHealthCardChargeTransaction.getThirdPartyPayInfoJson(), ThirdPartyPayInfo.class);
                if (firstThirdPartyPayInfo != null && firstThirdPartyPayInfo.getCqShebaoExtraInfo() != null) {
                    chargeSheetPrintView.setHealthCardBeginningBalance(MathUtils.wrapBigDecimalAdd(firstThirdPartyPayInfo.getCqShebaoExtraInfo().getAccountBalance(),
                            firstThirdPartyPayInfo.getCqShebaoExtraInfo().getAccountPaymentFee()));
                } else {
                    chargeSheetPrintView.setHealthCardBeginningBalance(MathUtils.wrapBigDecimalAdd(firstHealthCardChargeTransaction.getThirdPartyPayCardBalance(),
                            firstHealthCardChargeTransaction.getAmount()));
                }

                chargeSheetPrintView.setHealthCardNo(lastHealthCardChargeTransaction.getThirdPartyPayCardId());
                ThirdPartyPayInfo lastThirdPartyPayInfo = JsonUtils.readValue(lastHealthCardChargeTransaction.getThirdPartyPayInfoJson(), ThirdPartyPayInfo.class);
                if (lastThirdPartyPayInfo != null) {
                    chargeSheetPrintView.setHealthCardOwner(lastThirdPartyPayInfo.getCardOwner());
                    chargeSheetPrintView.setHealthCardOwnerRelationToPatient(lastThirdPartyPayInfo.getRelationToPatient());
                }

                //帐户支付金额
                BigDecimal accountPaymentFee = BigDecimal.ZERO;
                //统筹支付金额
                BigDecimal fundPaymentFee = BigDecimal.ZERO;
                //其它支付金额
                BigDecimal otherPaymentFee = BigDecimal.ZERO;
                //持卡人类型 职工 居民 离休干部 等
                String cardOwnerType = Optional.ofNullable(firstThirdPartyPayInfo).map(ThirdPartyPayInfo::getCardOwnerType).orElse(null);
                String cardId = Optional.ofNullable(firstThirdPartyPayInfo).map(ThirdPartyPayInfo::getCardId).orElse(null);

                //自负金额
                BigDecimal selfConceitFee = BigDecimal.ZERO;
                //自费金额
                BigDecimal selfPayFee = BigDecimal.ZERO;

                for (ChargeTransaction healthCardChargeTransaction : healthCardChargeTransactions) {
                    ThirdPartyPayInfo thirdPartyPayInfo = JsonUtils.readValue(healthCardChargeTransaction.getThirdPartyPayInfoJson(), ThirdPartyPayInfo.class);
                    if (thirdPartyPayInfo == null) {
                        continue;
                    }
                    accountPaymentFee = MathUtils.wrapBigDecimalAdd(accountPaymentFee, thirdPartyPayInfo.getAccountPaymentFee());
                    fundPaymentFee = MathUtils.wrapBigDecimalAdd(fundPaymentFee, thirdPartyPayInfo.getFundPaymentFee());
                    otherPaymentFee = MathUtils.wrapBigDecimalAdd(otherPaymentFee, thirdPartyPayInfo.getOtherPaymentFee());
                    selfConceitFee = MathUtils.wrapBigDecimalAdd(selfConceitFee, thirdPartyPayInfo.getSelfConceitFee());
                    selfPayFee = MathUtils.wrapBigDecimalAdd(selfPayFee, thirdPartyPayInfo.getSelfPayFee());
                }
                chargeSheetPrintView.setHealthCardAccountPaymentFee(accountPaymentFee);
                chargeSheetPrintView.setHealthCardFundPaymentFee(fundPaymentFee);
                chargeSheetPrintView.setHealthCardOtherPaymentFee(otherPaymentFee);
                chargeSheetPrintView.setHealthCardCardOwnerType(cardOwnerType);
                chargeSheetPrintView.setHealthCardSelfConceitFee(selfConceitFee);
                chargeSheetPrintView.setHealthCardSelfPayFee(selfPayFee);
                chargeSheetPrintView.setHealthCardId(cardId);
            }
        }

        //查询收费单对应的欠费数据
        List<ChargeOweSheet> chargeOweSheets = new ArrayList<>();
        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED) {
            chargeOweSheets = Optional.ofNullable(sheetProcessorInfoProvider).map(SheetProcessorInfoProvider::getChargeOweSheetProvider)
                    .map(chargeOweSheetProvider -> chargeOweSheetProvider.findChargeOweSheetsByChargeSheetId(chargeSheet.getClinicId(), chargeSheet.getId())).orElse(new ArrayList<>());
        }
        ChargeTransactionAndActionDto chargeTransactionAndActionDto = getChargeTransactionAndChargeActionContainOwe(sheetProcessor.chargeSheet, chargeOweSheets);

        bindShebaoPrintInfo(chargeSheet, QueryChargeSheetShebaoInfoReq.PayTaskType.REFUND, chargeSheetPrintView, sheetProcessorInfoProvider, chargeTransactionAndActionDto);

        bindGoodsDispenseInfo(chargeSheet, chargeSheetPrintView, true, sheetProcessorInfoProvider);

        // 收费员信息
        List<String> employeeIds = new ArrayList<>();

        ChargeTransaction flag = chargeSheet.getChargeTransactions().stream()
                .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                .filter(chargeTransaction -> chargeTransaction.getPaySource() == Constants.ChargeSource.CHARGE
                        || chargeTransaction.getPaySource() == Constants.ChargeSource.WE_CLINIC
                        || chargeTransaction.getPaySource() == Constants.ChargeSource.DEVICE
                        || chargeTransaction.getPaySource() == Constants.ChargeSource.WE_APP)
                .filter(chargeTransaction -> MathUtils.wrapBigDecimalCompare(chargeTransaction.getAmount(), BigDecimal.ZERO) < 0)
                .findFirst().orElse(new ChargeTransaction());
        String refundById = "";
        Instant refundTime = null;
        if (flag != null) {
            refundById = flag.getCreatedBy();
            refundTime = flag.getCreated();
        }

        if (!TextUtils.isEmpty(refundById)) {
            employeeIds.add(refundById);
        }

        if (!TextUtils.isEmpty(chargeSheet.getSellerId())) {
            employeeIds.add(chargeSheet.getSellerId());
        }

        //如果收费单含有配镜处方需要绑定验光师姓名
        List<String> optometristList = chargeSheetPrintView.getChargeForms().stream()
                .filter(form -> form.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_TYPE_EYE_GLASS && !StringUtils.isEmpty(form.getOptometristId()))
                .map(form -> form.getOptometristId())
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(optometristList)) {
            employeeIds.addAll(optometristList);
        }


        Map<String, String> employeeNameMap = sheetProcessorInfoProvider.getEmployeeInfoProvider().findEmployeeList(chargeSheet.getChainId(), employeeIds).stream().collect(Collectors.toMap(Employee::getId, Employee::getName, (a, b) -> a));


        String refundByName = "";
        if (!TextUtils.isEmpty(refundById)) {
            if (Objects.equals(refundById, Constants.ANONYMOUS_PATIENT_ID)) {
                refundByName = "自助退款";
            } else {
                refundByName = employeeNameMap.getOrDefault(refundById, "");
            }
        }
        chargeSheetPrintView.setRefundByName(refundByName);
        chargeSheetPrintView.setChargedTime(refundTime);

        String sellerName = "";
        if (!TextUtils.isEmpty(chargeSheet.getSellerId())) {
            sellerName = employeeNameMap.getOrDefault(chargeSheet.getSellerId(), "");
        }

        String doctorName = "";
        String nationalDoctorCode = "";
        String doctorId = chargeSheet.getType() == ChargeSheet.Type.DIRECT_SALE
                || chargeSheet.getType() == ChargeSheet.Type.THERAPY
                || chargeSheet.getType() == ChargeSheet.Type.EXAMINATION_INSPECTION
                ? Optional.ofNullable(chargeSheet.getAdditional()).map(ChargeSheetAdditional::getTranscribeDoctorId).orElse(null)
                : chargeSheet.getDoctorId();
        if (!TextUtils.isEmpty(doctorId)) {
            //查询医生的名称快照
            Instant doctorIdSnapTime = Optional.ofNullable(chargeSheet.getDiagnosedDate()).orElse(Instant.now());
            QueryEmployeeSnapReq queryEmployeeSnapReq = new QueryEmployeeSnapReq();
            queryEmployeeSnapReq.setEmployeeId(doctorId);
            queryEmployeeSnapReq.setBusinessTimes(Collections.singletonList(doctorIdSnapTime));
            doctorName = Optional.ofNullable(sheetProcessorInfoProvider.getClinicProvider().queryEmployeeSnap(new BatchQueryEmployeeSnapshotReq()
                            .setChainId(chargeSheet.getChainId())
                            .addBusinessTimeEmployeeSnap(doctorId, doctorIdSnapTime))
                    )
                    .map(rsp -> rsp.getEmployeeNameByBusTime(doctorId, doctorIdSnapTime))
                    .orElse("");

            ClinicEmployee clinicEmployee = sheetProcessorInfoProvider.getClinicProvider().queryEmployeeClinicInfoById(doctorId, chargeSheet.getClinicId());
            if (clinicEmployee != null && clinicEmployee.getClinicInfo() != null) {
                nationalDoctorCode = clinicEmployee.getClinicInfo().getNationalDoctorCode();
            }
        }

        String departmentName = "", departmentCaty = "";
        if (chargeSheet.getAdditional() != null && org.apache.commons.lang3.StringUtils.isNotEmpty(chargeSheet.getAdditional().getDepartmentId())) {
            Department department = sheetProcessorInfoProvider.getClinicProvider().queryDepartmentById(chargeSheet.getAdditional().getDepartmentId());
            if (department != null) {
                departmentName = department.getName();
                departmentCaty = org.apache.commons.lang3.StringUtils.isNotEmpty(department.getSecondMedicalCode()) ? department.getSecondMedicalCode() : department.getMainMedicalCode();
            }
        }

        if (patientOrder != null) {
            chargeSheetPrintView.setDepartmentName(departmentName);
            chargeSheetPrintView.setDepartmentCaty(departmentCaty);
            chargeSheetPrintView.setPatientOrderNo(String.format("%08d", patientOrder.getNo()));
        }

        chargeSheetPrintView.setSellerName(sellerName);
        chargeSheetPrintView.setDoctorName(doctorName);
        chargeSheetPrintView.setNationalDoctorCode(nationalDoctorCode);
        /**
         * 绑定验光师姓名
         */
        chargeSheetPrintView.getChargeForms().stream()
                .filter(form -> form.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_TYPE_EYE_GLASS && !StringUtils.isEmpty(form.getOptometristId()))
                .forEach(form -> form.setOptometristName(employeeNameMap.get(form.getOptometristId())));
        //处理检查检验里面的组合项，将子项提出来，这个逻辑放在最后处理
        if (Objects.equals(Constants.RegionId.HANGZHOU, Optional.ofNullable(organ).map(cn.abcyun.bis.rpc.sdk.cis.model.clinic.OrganPrintView::getAddressCityId).orElse(null))) {
            chargeSheetPrintView.getChargeForms()
                    .stream()
                    .filter(chargeFormPrintView -> chargeFormPrintView.getSourceFormType() == Constants.SourceFormType.EXAMINATION || chargeFormPrintView.getSourceFormType() == Constants.SourceFormType.COMPOSE_PRODUCT)
                    .filter(chargeFormPrintView -> chargeFormPrintView.getChargeFormItems() != null)
                    .forEach(chargeFormPrintView -> {
                        if (chargeFormPrintView.getSourceFormType() == Constants.SourceFormType.EXAMINATION) {
                            chargeFormPrintView.setChargeFormItems(chargeFormPrintView.getChargeFormItems().stream()
                                    .map(chargeFormItemPrintView -> {
                                        if (chargeFormItemPrintView.getIsExaminationSplitPrint() == 1 && org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeFormItemPrintView.getComposeChildren())) {
                                            return chargeFormItemPrintView.getComposeChildren();
                                        }
                                        return Collections.singletonList(chargeFormItemPrintView);
                                    })
                                    .flatMap(Collection::stream)
                                    .collect(Collectors.toList())
                            );
                            return;
                        }

                        if (chargeFormPrintView.getSourceFormType() == Constants.SourceFormType.COMPOSE_PRODUCT) {
                            chargeFormPrintView.getChargeFormItems().stream()
                                    .filter(chargeFormItemPrintView -> chargeFormItemPrintView.getProductType() == Constants.ProductType.EXAMINATION)
                                    .filter(chargeFormItemPrintView -> org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeFormItemPrintView.getComposeChildren()))
                                    .forEach(chargeFormItemPrintView -> chargeFormItemPrintView.setComposeChildren(chargeFormItemPrintView.getComposeChildren().stream()
                                            .map(child -> {
                                                if (child.getIsExaminationSplitPrint() == 1 && org.apache.commons.collections.CollectionUtils.isNotEmpty(child.getComposeChildren())) {
                                                    return child.getComposeChildren();
                                                }
                                                return Collections.singletonList(child);
                                            })
                                            .flatMap(Collection::stream)
                                            .collect(Collectors.toList()))
                                    );
                        }

                    });
        }
        return chargeSheetPrintView;
    }

    /**
     * 打印退费View
     * 前置：chargeSheet已经完成了算费
     */
    public static ChargeSheetPrintView pharmacyToRefundPrintView(PharmacySheetProcessor sheetProcessor) {
        //初始化
        ChargeSheet chargeSheet = sheetProcessor.chargeSheet;
        SheetProcessorInfoProvider sheetProcessorInfoProvider = sheetProcessor.sheetProcessorInfoProvider;
        PatientInfo patientInfo = sheetProcessor.getPatientInfo();
        List<PharmacyFormProcessor> formProcessors = sheetProcessor.formProcessors;
        BigDecimal adjustmentFee = sheetProcessor.adjustmentFee;
        BigDecimal refundAdjustmentFee = sheetProcessor.refundAdjustmentFee;
        BigDecimal totalFee = sheetProcessor.totalFee;
        BigDecimal refundTotalFee = sheetProcessor.refundTotalFee;
        BigDecimal refundDiscountFee = sheetProcessor.refundDiscountFee;
        BigDecimal discountFee = sheetProcessor.discountFee;
        BigDecimal _refundFee = sheetProcessor._refundFee;
        BigDecimal _receivedFee = sheetProcessor._receivedFee;
        PatientOrder patientOrder = sheetProcessor.patientOrder;

        cn.abcyun.bis.rpc.sdk.cis.model.clinic.OrganPrintView organ = sheetProcessorInfoProvider.getProductInfoProvider().getOrganPrintInfo(chargeSheet.getClinicId());

        ChargeSheetPrintView chargeSheetPrintView = new ChargeSheetPrintView();
        chargeSheetPrintView.setId(chargeSheet.getId());
        chargeSheetPrintView.setPatient(patientInfo);
        chargeSheetPrintView.setOrgan(organ != null ? new OrganPrintView(organ.getId(), organ.getName(), organ.getName(), organ.getAddressDetail(), organ.getContactPhone(), organ.getLogo(), organ.getCategory(), organ.getHisType()) : null);
        chargeSheetPrintView.setDiagnosedDate(chargeSheet.getDiagnosedDate());
        chargeSheetPrintView.setDiagnosis(Optional.ofNullable(chargeSheet.getAdditional()).map(ChargeSheetAdditional::getDiagnosis).orElse(null));
        chargeSheetPrintView.setDiagnosisInfos(Optional.ofNullable(chargeSheet.getAdditional()).map(ChargeSheetAdditional::getDiagnosisInfos).orElse(new ArrayList<>()));
        chargeSheetPrintView.setExtendDiagnosisInfos(Optional.ofNullable(chargeSheet.getAdditional()).map(ChargeSheetAdditional::getExtendDiagnosisInfos).orElse(new ArrayList<>()));
        chargeSheetPrintView.setMemberInfo(generateMemberPrintInfo(chargeSheet, sheetProcessorInfoProvider));


        //挂号费
        ChargeFormPrintView registrationFormView = new ChargeFormPrintView();
        registrationFormView.setId(AbcIdUtils.getUUID());
        registrationFormView.setSourceFormType(Constants.SourceFormType.REGISTRATION);
        registrationFormView.setPrintFormType(Constants.PrintFormType.REGISTRATION);
        registrationFormView.addChargeFormItemPrintViews(generatePharmacyNonMedicineRefundPrintView(formProcessors, Constants.ProductType.REGISTRATION));
        registrationFormView.calculateTotalPrice();
        if (registrationFormView.getChargeFormItems().size() > 0) {
            chargeSheetPrintView.addChargeFormPrintView(registrationFormView);
        }

        //快递费
        ChargeFormPrintView expressDeliveryFormView = new ChargeFormPrintView();
        expressDeliveryFormView.setId(AbcIdUtils.getUUID());
        expressDeliveryFormView.setSourceFormType(Constants.SourceFormType.EXPRESS_DELIVERY);
        expressDeliveryFormView.setPrintFormType(Constants.PrintFormType.EXPRESS_DELIVERY);
        expressDeliveryFormView.addChargeFormItemPrintViews(generatePharmacyNonMedicineRefundPrintView(formProcessors, Constants.ProductType.EXPRESS_DELIVERY));
        expressDeliveryFormView.calculateTotalPrice();
        if (expressDeliveryFormView.getChargeFormItems().size() > 0) {
            chargeSheetPrintView.addChargeFormPrintView(expressDeliveryFormView);
        }

        //签约费
        ChargeFormPrintView familyDoctorSignFormView = new ChargeFormPrintView();
        familyDoctorSignFormView.setId(AbcIdUtils.getUUID());
        familyDoctorSignFormView.setSourceFormType(Constants.SourceFormType.FAMILY_DOCTOR_SIGN);
        familyDoctorSignFormView.setPrintFormType(Constants.PrintFormType.FAMILY_DOCTOR_SIGN);
        familyDoctorSignFormView.addChargeFormItemPrintViews(generatePharmacyNonMedicineRefundPrintView(formProcessors, Constants.ProductType.FAMILY_DOCTOR_SIGN));
        familyDoctorSignFormView.calculateTotalPrice();
        if (familyDoctorSignFormView.getChargeFormItems().size() > 0) {
            chargeSheetPrintView.addChargeFormPrintView(familyDoctorSignFormView);
        }

        //加工费
        ChargeFormPrintView decoctionFormView = new ChargeFormPrintView();
        decoctionFormView.setId(AbcIdUtils.getUUID());
        decoctionFormView.setSourceFormType(Constants.SourceFormType.PROCESS);
        decoctionFormView.setPrintFormType(Constants.PrintFormType.PROCESS);
        decoctionFormView.addChargeFormItemPrintViews(generatePharmacyNonMedicineRefundPrintView(formProcessors, Constants.ProductType.PROCESS));
        decoctionFormView.calculateTotalPrice();
        if (decoctionFormView.getChargeFormItems().size() > 0) {
            chargeSheetPrintView.addChargeFormPrintView(decoctionFormView);
        }

        //咨询费
        ChargeFormPrintView onlineConsultationFormView = new ChargeFormPrintView();
        onlineConsultationFormView.setId(AbcIdUtils.getUUID());
        onlineConsultationFormView.setSourceFormType(Constants.SourceFormType.ONLINE_CONSULTATION);
        onlineConsultationFormView.setPrintFormType(Constants.PrintFormType.ONLINE_CONSULTATION);
        onlineConsultationFormView.addChargeFormItemPrintViews(generatePharmacyNonMedicineRefundPrintView(formProcessors, Constants.ProductType.ONLINE_CONSULTATION));
        onlineConsultationFormView.calculateTotalPrice();
        if (onlineConsultationFormView.getChargeFormItems().size() > 0) {
            chargeSheetPrintView.addChargeFormPrintView(onlineConsultationFormView);
        }

        //检查检验
        ChargeFormPrintView examinationFormView = new ChargeFormPrintView();
        examinationFormView.setId(AbcIdUtils.getUUID());
        examinationFormView.setSourceFormType(Constants.SourceFormType.EXAMINATION);
        examinationFormView.setPrintFormType(Constants.SourceFormType.EXAMINATION);
        examinationFormView.addChargeFormItemPrintViews(generatePharmacyNonMedicineRefundPrintView(formProcessors, Constants.ProductType.EXAMINATION));
        examinationFormView.calculateTotalPrice();
        if (examinationFormView.getChargeFormItems().size() > 0) {
            chargeSheetPrintView.addChargeFormPrintView(examinationFormView);
        }


        List<ChargeFormItemPrintView> allTreatmentFormItemViews = generatePharmacyNonMedicineRefundPrintView(formProcessors, Constants.ProductType.TREATMENT);
        Map<Boolean, List<ChargeFormItemPrintView>> treatmentFormItemViewMap = allTreatmentFormItemViews.stream()
                .collect(Collectors.groupingBy(chargeFormItemPrintView -> chargeFormItemPrintView.getProductType() == Constants.ProductType.TREATMENT && chargeFormItemPrintView.getProductSubType() == Constants.ProductType.SubType.TREATMENT_OTHER));

        List<ChargeFormItemPrintView> inTreatmentFormOtherFeeFormItemViews = treatmentFormItemViewMap.getOrDefault(Boolean.TRUE, new ArrayList<>());
        List<ChargeFormItemPrintView> treatmentFormItemViews = treatmentFormItemViewMap.getOrDefault(Boolean.FALSE, new ArrayList<>());

        //治疗理疗
        ChargeFormPrintView treatmentFormView = new ChargeFormPrintView();
        treatmentFormView.setId(AbcIdUtils.getUUID());
        treatmentFormView.setSourceFormType(Constants.SourceFormType.TREATMENT);
        treatmentFormView.setPrintFormType(Constants.PrintFormType.TREATMENT);

        treatmentFormView.addChargeFormItemPrintViews(treatmentFormItemViews);
        treatmentFormView.calculateTotalPrice();
        if (treatmentFormView.getChargeFormItems().size() > 0) {
            chargeSheetPrintView.addChargeFormPrintView(treatmentFormView);
        }

        //其他费用
        ChargeFormPrintView otherFormView = new ChargeFormPrintView();
        otherFormView.setId(AbcIdUtils.getUUID());
        otherFormView.setSourceFormType(Constants.SourceFormType.OTHER_FEE);
        otherFormView.setPrintFormType(Constants.PrintFormType.OTHER_FEE);

        otherFormView.addChargeFormItemPrintViews(inTreatmentFormOtherFeeFormItemViews);
        otherFormView.addChargeFormItemPrintViews(generatePharmacyNonMedicineRefundPrintView(formProcessors, Constants.ProductType.OTHER_FEE));
        otherFormView.calculateTotalPrice();
        if (otherFormView.getChargeFormItems().size() > 0) {
            chargeSheetPrintView.addChargeFormPrintView(otherFormView);
        }

        //材料商品
        if (organ == null || organ.getHisType() != Organ.HisType.CIS_HIS_TYPE_PHARMACY) {
            ChargeFormPrintView materialFormView = new ChargeFormPrintView();
            materialFormView.setId(AbcIdUtils.getUUID());
            materialFormView.setSourceFormType(Constants.SourceFormType.MATERIAL);
            materialFormView.setPrintFormType(Constants.PrintFormType.MATERIAL);
            materialFormView.addChargeFormItemPrintViews(generatePharmacyNonMedicineRefundPrintView(formProcessors, Constants.ProductType.MATERIAL));
            materialFormView.addChargeFormItemPrintViews(generatePharmacyNonMedicineRefundPrintView(formProcessors, Constants.ProductType.SALE_PRODUCT));
            materialFormView.calculateTotalPrice();
            if (materialFormView.getChargeFormItems().size() > 0) {
                chargeSheetPrintView.addChargeFormPrintView(materialFormView);
            }
        } else {
            ChargeFormPrintView materialFormView = new ChargeFormPrintView();
            materialFormView.setId(AbcIdUtils.getUUID());
            materialFormView.setSourceFormType(Constants.SourceFormType.MATERIAL);
            materialFormView.setPrintFormType(Constants.PrintFormType.MATERIAL);
            materialFormView.addChargeFormItemPrintViews(generatePharmacyNonMedicineRefundPrintView(formProcessors, Constants.ProductType.MATERIAL));
            materialFormView.calculateTotalPrice();
            if (materialFormView.getChargeFormItems().size() > 0) {
                chargeSheetPrintView.addChargeFormPrintView(materialFormView);
            }
            ChargeFormPrintView productFormView = new ChargeFormPrintView();
            productFormView.setId(AbcIdUtils.getUUID());
            productFormView.setSourceFormType(Constants.SourceFormType.ADDITIONAL_SALE_PRODUCT_FORM);
            productFormView.setPrintFormType(Constants.PrintFormType.ADDITIONAL_SALE_PRODUCT_FORM);
            productFormView.addChargeFormItemPrintViews(generatePharmacyNonMedicineRefundPrintView(formProcessors, Constants.ProductType.SALE_PRODUCT));
            productFormView.calculateTotalPrice();
            if (productFormView.getChargeFormItems().size() > 0) {
                chargeSheetPrintView.addChargeFormPrintView(productFormView);
            }
        }

        //套餐处理
        ChargeFormPrintView composeProductFormView = new ChargeFormPrintView();
        composeProductFormView.setId(AbcIdUtils.getUUID());
        composeProductFormView.setSourceFormType(Constants.SourceFormType.COMPOSE_PRODUCT);
        composeProductFormView.setPrintFormType(Constants.PrintFormType.COMPOSE_PRODUCT);
        composeProductFormView.addChargeFormItemPrintViews(generatePharmacyNonMedicineRefundPrintView(formProcessors, Constants.ProductType.COMPOSE_PRODUCT));
        composeProductFormView.calculateTotalPrice();
        if (composeProductFormView.getChargeFormItems().size() > 0) {
            chargeSheetPrintView.addChargeFormPrintView(composeProductFormView);
        }

        //眼镜处方处理
        ChargeFormPrintView eyeFormView = new ChargeFormPrintView();
        eyeFormView.setId(AbcIdUtils.getUUID());
        eyeFormView.setSourceFormType(Constants.SourceFormType.PRESCRIPTION_EYE);
        eyeFormView.setPrintFormType(Constants.PrintFormType.PRESCRIPTION_EYE);
        eyeFormView.addChargeFormItemPrintViews(generatePharmacyNonMedicineRefundPrintView(formProcessors, Constants.ProductType.EYE));
        eyeFormView.calculateTotalPrice();
        if (eyeFormView.getChargeFormItems().size() > 0) {
            chargeSheetPrintView.addChargeFormPrintView(eyeFormView);
        }

        //护理医嘱
        ChargeFormPrintView nursingFormView = new ChargeFormPrintView();
        nursingFormView.setId(AbcIdUtils.getUUID());
        nursingFormView.setSourceFormType(Constants.SourceFormType.NURSING);
        nursingFormView.setPrintFormType(Constants.PrintFormType.NURSING);
        nursingFormView.addChargeFormItemPrintViews(generatePharmacyNonMedicineRefundPrintView(formProcessors, Constants.ProductType.NURSE));
        nursingFormView.calculateTotalPrice();
        if (nursingFormView.getChargeFormItems().size() > 0) {
            chargeSheetPrintView.addChargeFormPrintView(nursingFormView);
        }

        //手术
        ChargeFormPrintView surgeryFormView = new ChargeFormPrintView();
        surgeryFormView.setId(AbcIdUtils.getUUID());
        surgeryFormView.setSourceFormType(Constants.SourceFormType.SURGERY);
        surgeryFormView.setPrintFormType(Constants.PrintFormType.SURGERY);
        surgeryFormView.addChargeFormItemPrintViews(generatePharmacyNonMedicineRefundPrintView(formProcessors, Constants.ProductType.SURGERY));
        surgeryFormView.calculateTotalPrice();
        if (surgeryFormView.getChargeFormItems().size() > 0) {
            chargeSheetPrintView.addChargeFormPrintView(surgeryFormView);
        }


        //处方
        List<ChargeFormPrintView> prescriptionFormPrintViewList = formProcessors
                .stream()
                .sorted((a, b) -> {
                    if (a.getSourceFormType() == b.getSourceFormType()) {
                        return ObjectUtils.compare(a.getSort(), b.getSort());
                    } else {
                        return ObjectUtils.compare(a.getSourceFormType(), b.getSourceFormType());
                    }
                })
                .filter(formProcessor -> formProcessor.isMedicalToPrintToClient())
                .map(formProcessor -> formProcessor.generateMedicineMaterialProductPrintView((formProcessor1, itemProcessor) -> itemProcessor.generateRefundPrintView()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        chargeSheetPrintView.addChargeFormPrintViews(prescriptionFormPrintViewList);
        updateGoodsFeeChildProductType(chargeSheetPrintView.getChargeForms(), Optional.ofNullable(organ).map(cn.abcyun.bis.rpc.sdk.cis.model.clinic.OrganPrintView::getHisType).orElse(0));

        // 药店赠品
        ChargeFormPrintView giftFormView = new ChargeFormPrintView();
        giftFormView.setId(AbcIdUtils.getUUID());
        giftFormView.setSourceFormType(Constants.SourceFormType.GIFT_PRODUCT);
        giftFormView.setPrintFormType(Constants.PrintFormType.GIFT_PRODUCT);
        giftFormView.addChargeFormItemPrintViews(generatePharmacyGiftMedicineRefundPrintView(formProcessors));
        giftFormView.calculateTotalPrice();
        if (!CollectionUtils.isEmpty(giftFormView.getChargeFormItems())) {
            chargeSheetPrintView.addChargeFormPrintView(giftFormView);
        }


        ChargeSheetPrintPriceInfo chargeSheetPrintPriceInfo = sheetProcessor.sheetCalculator.generateSheetRefundPrintPriceInfo();
        chargeSheetPrintView.setTotalFee(chargeSheetPrintPriceInfo.getTotalFee());
        chargeSheetPrintView.setSinglePromotionFee(chargeSheetPrintPriceInfo.getSinglePromotionFee());
        chargeSheetPrintView.setPackagePromotionFee(chargeSheetPrintPriceInfo.getPackagePromotionFee());
        chargeSheetPrintView.setReceivableFee(chargeSheetPrintPriceInfo.getReceivableFee());
        chargeSheetPrintView.setNetIncomeFee(MathUtils.setScaleTwo(_receivedFee.add(_refundFee)));
        chargeSheetPrintView.setSinglePromotionedTotalFee(chargeSheetPrintPriceInfo.getSinglePromotionedTotalFee());

        // 支付方式分类
        if (chargeSheet.getChargeTransactions() != null) {
            Map<Long, Integer> payModeMaps = getPayModeTypes(sheetProcessorInfoProvider, chargeSheet.getChainId());
            Map<String, ChargeAction.PayActionInfo> payModeNameMap = ChargeUtils.generatePayModeNameMap(chargeSheet.getChargeActions(), payModeMaps);
            chargeSheet.getChargeTransactions().forEach(chargeTransaction -> {
                chargeTransaction.setPayModeType(payModeMaps.getOrDefault(Long.valueOf(chargeTransaction.getPayMode()), 0));
            });
            List<ChargeTransactionPrintView> chargeTransactionPrintViews = chargeSheet.getChargeTransactions()
                    .stream()
                    .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                    .filter(chargeTransaction -> chargeTransaction.getPaySource() == Constants.ChargeSource.CHARGE || chargeTransaction.getPaySource() == Constants.ChargeSource.WE_CLINIC || chargeTransaction.getPaySource() == Constants.ChargeSource.DEVICE || chargeTransaction.getPaySource() == Constants.ChargeSource.WE_APP)
                    .filter(chargeTransaction -> MathUtils.wrapBigDecimalCompare(chargeTransaction.getAmount(), BigDecimal.ZERO) < 0)
                    .collect(Collectors.toMap(ChargeUtils::generateTransactionUniqueKey, chargeTransaction -> {
                        ChargeTransactionPrintView chargeTransactionPrintView = new ChargeTransactionPrintView();
                        chargeTransactionPrintView.setAmount(chargeTransaction.getAmount());
                        chargeTransactionPrintView.setPayMode(chargeTransaction.getPayMode());
                        chargeTransactionPrintView.setPaySubMode(chargeTransaction.getPaySubMode());
                        chargeTransactionPrintView.setThirdPartyPayCardId(chargeTransaction.getThirdPartyPayCardId());

                        return chargeTransactionPrintView;
                    }, (a, b) -> {
                        a.setAmount(MathUtils.wrapBigDecimalAdd(a.getAmount(), b.getAmount()));
                        return a;
                    }))
                    .entrySet()
                    .stream()
                    .map(entry -> {
                        String payModeKey = ChargeUtils.generatePayModeKey(entry.getValue().getPayMode(), entry.getValue().getPaySubMode(), payModeMaps.getOrDefault(Long.valueOf(entry.getValue().getPayMode()), 0), entry.getKey());
                        ChargeAction.PayActionInfo payActionInfo = payModeNameMap.getOrDefault(payModeKey, new ChargeAction.PayActionInfo());
                        entry.getValue().setPayModeName(payActionInfo.getPayModeName());
                        entry.getValue().setPaySubModeName(payActionInfo.getPaySubModeName());
                        return entry.getValue();
                    }).collect(Collectors.toList());
            chargeSheetPrintView.setChargeTransactions(chargeTransactionPrintViews);
        }
        // 设置个人现金支付
        chargeSheetPrintView.setPersonalPaymentFee(
                MathUtils.wrapBigDecimalSubtract(chargeSheetPrintView.getNetIncomeFee(), chargeSheetPrintView.getHealthCardPaymentFee())
        );

        // 药品分类汇总
        chargeSheetPrintView.buildSubTotals();
        chargeSheetPrintView.setWholeMedicalBills(generateWholeMedicalBills(chargeSheetPrintView.getChargeForms(), chargeSheet.getChainId(), sheetProcessorInfoProvider, null));

        // 如果有会员卡支付，找到最后一次会员卡支付的余额
        if (chargeSheet.getChargeTransactions() != null) {
            ChargeTransaction firstRefundMemberCardChargeTransaction = chargeSheet.getChargeTransactions()
                    .stream()
                    .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.MEMBER_CARD)
                    .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                    .filter(chargeTransaction -> MathUtils.wrapBigDecimalOrZero(chargeTransaction.getAmount()).compareTo(BigDecimal.ZERO) < 0)
                    .sorted((a, b) -> -ObjectUtils.compare(b.getCreated(), a.getCreated()))
                    .findFirst()
                    .orElse(null);

            ChargeTransaction lastRefundMemberCardChargeTransaction = chargeSheet.getChargeTransactions()
                    .stream()
                    .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.MEMBER_CARD)
                    .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                    .filter(chargeTransaction -> MathUtils.wrapBigDecimalOrZero(chargeTransaction.getAmount()).compareTo(BigDecimal.ZERO) < 0)
                    .sorted((a, b) -> -ObjectUtils.compare(a.getCreated(), b.getCreated()))
                    .findFirst()
                    .orElse(null);

            ChargeTransaction firstHealthCardChargeTransaction = chargeSheet.getChargeTransactions()
                    .stream()
                    .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.HEALTH_CARD)
                    .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                    .filter(chargeTransaction -> MathUtils.wrapBigDecimalOrZero(chargeTransaction.getAmount()).compareTo(BigDecimal.ZERO) >= 0)
                    .filter(chargeTransaction -> chargeTransaction.getPaySource() == Constants.ChargeSource.CHARGE || chargeTransaction.getPaySource() == Constants.ChargeSource.WE_CLINIC || chargeTransaction.getPaySource() == Constants.ChargeSource.DEVICE)
                    .sorted((a, b) -> -ObjectUtils.compare(b.getCreated(), a.getCreated()))
                    .findFirst()
                    .orElse(null);

            ChargeTransaction lastHealthCardChargeTransaction = chargeSheet.getChargeTransactions()
                    .stream()
                    .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.HEALTH_CARD)
                    .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                    .filter(chargeTransaction -> MathUtils.wrapBigDecimalOrZero(chargeTransaction.getAmount()).compareTo(BigDecimal.ZERO) >= 0)
                    .filter(chargeTransaction -> chargeTransaction.getPaySource() == Constants.ChargeSource.CHARGE || chargeTransaction.getPaySource() == Constants.ChargeSource.WE_CLINIC || chargeTransaction.getPaySource() == Constants.ChargeSource.DEVICE)
                    .sorted((a, b) -> -ObjectUtils.compare(a.getCreated(), b.getCreated()))
                    .findFirst()
                    .orElse(null);

            List<ChargeTransaction> healthCardChargeTransactions = chargeSheet.getChargeTransactions()
                    .stream()
                    .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.HEALTH_CARD)
                    .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                    .filter(chargeTransaction -> MathUtils.wrapBigDecimalOrZero(chargeTransaction.getAmount()).compareTo(BigDecimal.ZERO) >= 0)
                    .filter(chargeTransaction -> chargeTransaction.getPaySource() == Constants.ChargeSource.CHARGE || chargeTransaction.getPaySource() == Constants.ChargeSource.WE_CLINIC || chargeTransaction.getPaySource() == Constants.ChargeSource.DEVICE)
                    .sorted((a, b) -> -ObjectUtils.compare(b.getCreated(), a.getCreated()))
                    .collect(Collectors.toList());

            PatientInfo cisPatientInfo = sheetProcessorInfoProvider.getPatientInfoProvider().findPatientInfoById(chargeSheet.getChainId(), chargeSheet.getClinicId(), chargeSheet.getMemberId(), false, false);

            if (cisPatientInfo != null && firstRefundMemberCardChargeTransaction != null && lastRefundMemberCardChargeTransaction != null) {
                chargeSheetPrintView.setMemberCardBeginningBalance(MathUtils.wrapBigDecimalAdd(firstRefundMemberCardChargeTransaction.getThirdPartyPayCardBalance(),
                        firstRefundMemberCardChargeTransaction.getAmount()));

                chargeSheetPrintView.setMemberCardBalance(lastRefundMemberCardChargeTransaction.getThirdPartyPayCardBalance());
                chargeSheetPrintView.setMemberCardMobile(cisPatientInfo.getMobile());
            }


            //这份代码保留，只为了兼容老逻辑，未来会删除
            if (lastHealthCardChargeTransaction != null && firstHealthCardChargeTransaction != null) {
                chargeSheetPrintView.setHealthCardBalance(lastHealthCardChargeTransaction.getThirdPartyPayCardBalance());


                ThirdPartyPayInfo firstThirdPartyPayInfo = JsonUtils.readValue(firstHealthCardChargeTransaction.getThirdPartyPayInfoJson(), ThirdPartyPayInfo.class);
                if (firstThirdPartyPayInfo != null && firstThirdPartyPayInfo.getCqShebaoExtraInfo() != null) {
                    chargeSheetPrintView.setHealthCardBeginningBalance(MathUtils.wrapBigDecimalAdd(firstThirdPartyPayInfo.getCqShebaoExtraInfo().getAccountBalance(),
                            firstThirdPartyPayInfo.getCqShebaoExtraInfo().getAccountPaymentFee()));
                } else {
                    chargeSheetPrintView.setHealthCardBeginningBalance(MathUtils.wrapBigDecimalAdd(firstHealthCardChargeTransaction.getThirdPartyPayCardBalance(),
                            firstHealthCardChargeTransaction.getAmount()));
                }

                chargeSheetPrintView.setHealthCardNo(lastHealthCardChargeTransaction.getThirdPartyPayCardId());
                ThirdPartyPayInfo lastThirdPartyPayInfo = JsonUtils.readValue(lastHealthCardChargeTransaction.getThirdPartyPayInfoJson(), ThirdPartyPayInfo.class);
                if (lastThirdPartyPayInfo != null) {
                    chargeSheetPrintView.setHealthCardOwner(lastThirdPartyPayInfo.getCardOwner());
                    chargeSheetPrintView.setHealthCardOwnerRelationToPatient(lastThirdPartyPayInfo.getRelationToPatient());
                }

                //帐户支付金额
                BigDecimal accountPaymentFee = BigDecimal.ZERO;
                //统筹支付金额
                BigDecimal fundPaymentFee = BigDecimal.ZERO;
                //其它支付金额
                BigDecimal otherPaymentFee = BigDecimal.ZERO;
                //持卡人类型 职工 居民 离休干部 等
                String cardOwnerType = Optional.ofNullable(firstThirdPartyPayInfo).map(ThirdPartyPayInfo::getCardOwnerType).orElse(null);
                String cardId = Optional.ofNullable(firstThirdPartyPayInfo).map(ThirdPartyPayInfo::getCardId).orElse(null);

                //自负金额
                BigDecimal selfConceitFee = BigDecimal.ZERO;
                //自费金额
                BigDecimal selfPayFee = BigDecimal.ZERO;

                for (ChargeTransaction healthCardChargeTransaction : healthCardChargeTransactions) {
                    ThirdPartyPayInfo thirdPartyPayInfo = JsonUtils.readValue(healthCardChargeTransaction.getThirdPartyPayInfoJson(), ThirdPartyPayInfo.class);
                    if (thirdPartyPayInfo == null) {
                        continue;
                    }
                    accountPaymentFee = MathUtils.wrapBigDecimalAdd(accountPaymentFee, thirdPartyPayInfo.getAccountPaymentFee());
                    fundPaymentFee = MathUtils.wrapBigDecimalAdd(fundPaymentFee, thirdPartyPayInfo.getFundPaymentFee());
                    otherPaymentFee = MathUtils.wrapBigDecimalAdd(otherPaymentFee, thirdPartyPayInfo.getOtherPaymentFee());
                    selfConceitFee = MathUtils.wrapBigDecimalAdd(selfConceitFee, thirdPartyPayInfo.getSelfConceitFee());
                    selfPayFee = MathUtils.wrapBigDecimalAdd(selfPayFee, thirdPartyPayInfo.getSelfPayFee());
                }
                chargeSheetPrintView.setHealthCardAccountPaymentFee(accountPaymentFee);
                chargeSheetPrintView.setHealthCardFundPaymentFee(fundPaymentFee);
                chargeSheetPrintView.setHealthCardOtherPaymentFee(otherPaymentFee);
                chargeSheetPrintView.setHealthCardCardOwnerType(cardOwnerType);
                chargeSheetPrintView.setHealthCardSelfConceitFee(selfConceitFee);
                chargeSheetPrintView.setHealthCardSelfPayFee(selfPayFee);
                chargeSheetPrintView.setHealthCardId(cardId);
            }
        }

        //查询收费单对应的欠费数据
        List<ChargeOweSheet> chargeOweSheets = new ArrayList<>();
        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED) {
            chargeOweSheets = Optional.ofNullable(sheetProcessorInfoProvider).map(SheetProcessorInfoProvider::getChargeOweSheetProvider)
                    .map(chargeOweSheetProvider -> chargeOweSheetProvider.findChargeOweSheetsByChargeSheetId(chargeSheet.getClinicId(), chargeSheet.getId())).orElse(new ArrayList<>());
        }
        ChargeTransactionAndActionDto chargeTransactionAndActionDto = getChargeTransactionAndChargeActionContainOwe(sheetProcessor.chargeSheet, chargeOweSheets);

        bindShebaoPrintInfo(chargeSheet, QueryChargeSheetShebaoInfoReq.PayTaskType.REFUND, chargeSheetPrintView, sheetProcessorInfoProvider, chargeTransactionAndActionDto);

        bindGoodsDispenseInfo(chargeSheet, chargeSheetPrintView, true, sheetProcessorInfoProvider);

        // 收费员信息
        List<String> employeeIds = new ArrayList<>();

        ChargeTransaction flag = chargeSheet.getChargeTransactions().stream()
                .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                .filter(chargeTransaction -> chargeTransaction.getPaySource() == Constants.ChargeSource.CHARGE
                        || chargeTransaction.getPaySource() == Constants.ChargeSource.WE_CLINIC
                        || chargeTransaction.getPaySource() == Constants.ChargeSource.DEVICE
                        || chargeTransaction.getPaySource() == Constants.ChargeSource.WE_APP)
                .filter(chargeTransaction -> MathUtils.wrapBigDecimalCompare(chargeTransaction.getAmount(), BigDecimal.ZERO) < 0)
                .findFirst().orElse(new ChargeTransaction());
        String refundById = "";
        Instant refundTime = null;
        if (flag != null) {
            refundById = flag.getCreatedBy();
            refundTime = flag.getCreated();
        }

        if (!TextUtils.isEmpty(refundById)) {
            employeeIds.add(refundById);
        }

        if (!TextUtils.isEmpty(chargeSheet.getSellerId())) {
            employeeIds.add(chargeSheet.getSellerId());
        }

        //如果收费单含有配镜处方需要绑定验光师姓名
        List<String> optometristList = chargeSheetPrintView.getChargeForms().stream()
                .filter(form -> form.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_TYPE_EYE_GLASS && !StringUtils.isEmpty(form.getOptometristId()))
                .map(form -> form.getOptometristId())
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(optometristList)) {
            employeeIds.addAll(optometristList);
        }

        Set<String> needQueryNationalDoctorCodeIdList = new HashSet<>();
        // 药师
        String pharmacistId = null;
        if (chargeSheet.getAdditional() != null && chargeSheet.getAdditional().getExtendedInfo() != null
                && !StringUtils.isEmpty(chargeSheet.getAdditional().getExtendedInfo().getPharmacistId())) {
            pharmacistId = chargeSheet.getAdditional().getExtendedInfo().getPharmacistId();
            employeeIds.add(pharmacistId);
            needQueryNationalDoctorCodeIdList.add(pharmacistId);
        }
        if (chargeSheet.getAdditional() != null) {
            chargeSheetPrintView.setRemarks(chargeSheet.getAdditional().getRemarks());
        }


        Map<String, String> employeeNameMap = sheetProcessorInfoProvider.getEmployeeInfoProvider().findEmployeeList(chargeSheet.getChainId(), employeeIds).stream().collect(Collectors.toMap(Employee::getId, Employee::getName, (a, b) -> a));


        String refundByName = "";
        if (!TextUtils.isEmpty(refundById)) {
            if (Objects.equals(refundById, Constants.ANONYMOUS_PATIENT_ID)) {
                refundByName = "自助退款";
            } else {
                refundByName = employeeNameMap.getOrDefault(refundById, "");
            }
        }
        chargeSheetPrintView.setRefundByName(refundByName);
        chargeSheetPrintView.setChargedTime(refundTime);

        String sellerName = "";
        if (!TextUtils.isEmpty(chargeSheet.getSellerId())) {
            sellerName = employeeNameMap.getOrDefault(chargeSheet.getSellerId(), "");
        }

        if (chargeSheet.getAdditional() != null && chargeSheet.getAdditional().getExtendedInfo() != null
                && !StringUtils.isEmpty(chargeSheet.getAdditional().getExtendedInfo().getPharmacistId())) {
            String pharmacistName = employeeNameMap.getOrDefault(chargeSheet.getAdditional().getExtendedInfo().getPharmacistId(), "");
            chargeSheetPrintView.setPharmacistName(pharmacistName);
        }

        String doctorName = "";
        String nationalDoctorCode = "";
        String doctorId = chargeSheet.getType() == ChargeSheet.Type.DIRECT_SALE
                || chargeSheet.getType() == ChargeSheet.Type.THERAPY
                || chargeSheet.getType() == ChargeSheet.Type.EXAMINATION_INSPECTION
                ? Optional.ofNullable(chargeSheet.getAdditional()).map(ChargeSheetAdditional::getTranscribeDoctorId).orElse(null)
                : chargeSheet.getDoctorId();
        if (!TextUtils.isEmpty(doctorId)) {
            //查询医生的名称快照
            Instant doctorIdSnapTime = Optional.ofNullable(chargeSheet.getDiagnosedDate()).orElse(Instant.now());
            QueryEmployeeSnapReq queryEmployeeSnapReq = new QueryEmployeeSnapReq();
            queryEmployeeSnapReq.setEmployeeId(doctorId);
            queryEmployeeSnapReq.setBusinessTimes(Collections.singletonList(doctorIdSnapTime));
            doctorName = Optional.ofNullable(sheetProcessorInfoProvider.getClinicProvider().queryEmployeeSnap(new BatchQueryEmployeeSnapshotReq()
                            .setChainId(chargeSheet.getChainId())
                            .addBusinessTimeEmployeeSnap(doctorId, doctorIdSnapTime))
                    )
                    .map(rsp -> rsp.getEmployeeNameByBusTime(doctorId, doctorIdSnapTime))
                    .orElse("");
            needQueryNationalDoctorCodeIdList.add(doctorId);

            //ClinicEmployee clinicEmployee = sheetProcessorInfoProvider.getClinicProvider().queryEmployeeClinicInfoById(doctorId, chargeSheet.getClinicId());
            //if (clinicEmployee != null && clinicEmployee.getClinicInfo() != null) {
            //    nationalDoctorCode = clinicEmployee.getClinicInfo().getNationalDoctorCode();
            //}
        }
        if (!needQueryNationalDoctorCodeIdList.isEmpty()) {
            List<EmployeeClinicInfo> employeeClinicInfos = sheetProcessorInfoProvider.getClinicProvider().queryClinicEmployeeInfo(chargeSheet.getChainId(),
                    chargeSheet.getClinicId(), new ArrayList<>(needQueryNationalDoctorCodeIdList));
            Map<String, EmployeeClinicInfo> employeeIdToClinicEmployee = ListUtils.toMap(employeeClinicInfos, EmployeeClinicBasicInfo::getEmployeeId);
            EmployeeClinicInfo doctorClinicEmployee = employeeIdToClinicEmployee.get(doctorId);
            EmployeeClinicInfo pharmacistClinicEmployee = employeeIdToClinicEmployee.get(pharmacistId);
            if (doctorClinicEmployee != null) {
                nationalDoctorCode = doctorClinicEmployee.getNationalDoctorCode();
            }
            if (pharmacistClinicEmployee != null) {
                chargeSheetPrintView.setPharmacistNationalDoctorCode(pharmacistClinicEmployee.getNationalDoctorCode());
            }
        }

        String departmentName = "", departmentCaty = "";
        if (chargeSheet.getAdditional() != null && org.apache.commons.lang3.StringUtils.isNotEmpty(chargeSheet.getAdditional().getDepartmentId())) {
            Department department = sheetProcessorInfoProvider.getClinicProvider().queryDepartmentById(chargeSheet.getAdditional().getDepartmentId());
            if (department != null) {
                departmentName = department.getName();
                departmentCaty = org.apache.commons.lang3.StringUtils.isNotEmpty(department.getSecondMedicalCode()) ? department.getSecondMedicalCode() : department.getMainMedicalCode();
            }
        }

        if (patientOrder != null) {
            chargeSheetPrintView.setDepartmentName(departmentName);
            chargeSheetPrintView.setDepartmentCaty(departmentCaty);
            chargeSheetPrintView.setPatientOrderNo(String.format("%08d", patientOrder.getNo()));
        }

        chargeSheetPrintView.setSellerName(sellerName);
        chargeSheetPrintView.setDoctorName(doctorName);
        chargeSheetPrintView.setNationalDoctorCode(nationalDoctorCode);
        /**
         * 绑定验光师姓名
         */
        chargeSheetPrintView.getChargeForms().stream()
                .filter(form -> form.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_TYPE_EYE_GLASS && !StringUtils.isEmpty(form.getOptometristId()))
                .forEach(form -> form.setOptometristName(employeeNameMap.get(form.getOptometristId())));
        //处理检查检验里面的组合项，将子项提出来，这个逻辑放在最后处理
        if (Objects.equals(Constants.RegionId.HANGZHOU, Optional.ofNullable(organ).map(cn.abcyun.bis.rpc.sdk.cis.model.clinic.OrganPrintView::getAddressCityId).orElse(null))) {
            chargeSheetPrintView.getChargeForms()
                    .stream()
                    .filter(chargeFormPrintView -> chargeFormPrintView.getSourceFormType() == Constants.SourceFormType.EXAMINATION || chargeFormPrintView.getSourceFormType() == Constants.SourceFormType.COMPOSE_PRODUCT)
                    .filter(chargeFormPrintView -> chargeFormPrintView.getChargeFormItems() != null)
                    .forEach(chargeFormPrintView -> {
                        if (chargeFormPrintView.getSourceFormType() == Constants.SourceFormType.EXAMINATION) {
                            chargeFormPrintView.setChargeFormItems(chargeFormPrintView.getChargeFormItems().stream()
                                    .map(chargeFormItemPrintView -> {
                                        if (chargeFormItemPrintView.getIsExaminationSplitPrint() == 1 && org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeFormItemPrintView.getComposeChildren())) {
                                            return chargeFormItemPrintView.getComposeChildren();
                                        }
                                        return Collections.singletonList(chargeFormItemPrintView);
                                    })
                                    .flatMap(Collection::stream)
                                    .collect(Collectors.toList())
                            );
                            return;
                        }

                        if (chargeFormPrintView.getSourceFormType() == Constants.SourceFormType.COMPOSE_PRODUCT) {
                            chargeFormPrintView.getChargeFormItems().stream()
                                    .filter(chargeFormItemPrintView -> chargeFormItemPrintView.getProductType() == Constants.ProductType.EXAMINATION)
                                    .filter(chargeFormItemPrintView -> org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeFormItemPrintView.getComposeChildren()))
                                    .forEach(chargeFormItemPrintView -> chargeFormItemPrintView.setComposeChildren(chargeFormItemPrintView.getComposeChildren().stream()
                                            .map(child -> {
                                                if (child.getIsExaminationSplitPrint() == 1 && org.apache.commons.collections.CollectionUtils.isNotEmpty(child.getComposeChildren())) {
                                                    return child.getComposeChildren();
                                                }
                                                return Collections.singletonList(child);
                                            })
                                            .flatMap(Collection::stream)
                                            .collect(Collectors.toList()))
                                    );
                        }

                    });
        }
        return chargeSheetPrintView;
    }


    private static List<ChargeFormItemPrintView> generateNonMedicinePrintView(List<FormProcessor> formProcessors, int productType, List<Integer> itemFilterStatus) {
        return formProcessors
                .stream()
                .filter(formProcessor -> formProcessor.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_CHINESE &&
                        formProcessor.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_WESTERN
//                      &&  formProcessor.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_INFUSION
                        && formProcessor.getSourceFormType() != Constants.SourceFormType.GIFT_PRODUCT
//                        && formProcessor.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_EXTERNAL
                )
                .sorted(Comparator.comparing(formProcessor -> (formProcessor.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_INFUSION
                                || formProcessor.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_EXTERNAL) ? 1 : 0)
                )
                .map(formProcessor -> formProcessor.getItemProcessorList()
                        .stream()
                        .sorted((a, b) -> ObjectUtils.compare(a.getSort(), b.getSort()))
                        .filter(itemProcessor -> {
                            // 只有 耗材、检查检验、治疗理疗、其它费用 才能从输注和外治处方中取，其它productType 不从输注和外治处方取
                            if (productType != Constants.ProductType.MATERIAL
                                    && productType != Constants.ProductType.SALE_PRODUCT
                                    && productType != Constants.ProductType.EXAMINATION
                                    && productType != Constants.ProductType.TREATMENT
                                    && productType != Constants.ProductType.OTHER_FEE) {
                                if (formProcessor.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_INFUSION
                                    || formProcessor.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_EXTERNAL){
                                    return false;
                                }
                            }
                            return itemProcessor.getProductType() == productType;
                        })
                        .map(itemProcessor -> itemProcessor.generatePrintView(itemFilterStatus))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()))
                .flatMap(Collection::stream)
//                .flatMap(formProcessor -> formProcessor.getItemProcessorList().stream().sorted((a, b) -> ObjectUtils.compare(a.getSort(), b.getSort())))
//                .filter(itemProcessor -> itemProcessor.getProductType() == productType)
//                .map(ItemProcessor::generatePrintView)
//                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private static List<ChargeFormItemPrintView> generatePharmacyNonMedicinePrintView(List<PharmacyFormProcessor> formProcessors, int productType) {
        return formProcessors
                .stream()
                .filter(formProcessor -> formProcessor.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_CHINESE &&
                        formProcessor.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_WESTERN &&
                        formProcessor.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_INFUSION
                        && formProcessor.getSourceFormType() != Constants.SourceFormType.GIFT_PRODUCT
                        && formProcessor.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_EXTERNAL
                )
                .sorted((a, b) -> ObjectUtils.compare(a.getSort(), b.getSort()))
                .map(formProcessor -> formProcessor.getItemProcessorList()
                        .stream()
                        .sorted((a, b) -> ObjectUtils.compare(a.getSort(), b.getSort()))
                        .filter(itemProcessor -> itemProcessor.getIsGift() == Constants.ChargeFormItemGiftType.NOT_GIFT)
                        .filter(itemProcessor -> itemProcessor.getProductType() == productType)
                        .map(PharmacyItemProcessor::generatePrintView)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()))
                .flatMap(Collection::stream)
//                .flatMap(formProcessor -> formProcessor.getItemProcessorList().stream().sorted((a, b) -> ObjectUtils.compare(a.getSort(), b.getSort())))
//                .filter(itemProcessor -> itemProcessor.getProductType() == productType)
//                .map(ItemProcessor::generatePrintView)
//                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private static List<ChargeFormItemPrintView> generatePharmacyGiftMedicinePrintView(List<PharmacyFormProcessor> formProcessors) {
        return formProcessors
                .stream()
                .sorted((a, b) -> ObjectUtils.compare(a.getSort(), b.getSort()))
                .map(formProcessor -> formProcessor.getItemProcessorList()
                        .stream()
                        .sorted((a, b) -> ObjectUtils.compare(a.getSort(), b.getSort()))
                        .filter(itemProcessor -> itemProcessor.getIsGift() > Constants.ChargeFormItemGiftType.NOT_GIFT)
                        .map(PharmacyItemProcessor::generatePrintView)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    private static List<ChargeFormItemPrintView> generatePharmacyGiftMedicineRefundPrintView(List<PharmacyFormProcessor> formProcessors) {
        return formProcessors
                .stream()
                .sorted((a, b) -> ObjectUtils.compare(a.getSort(), b.getSort()))
                .map(formProcessor -> formProcessor.getItemProcessorList()
                        .stream()
                        .sorted((a, b) -> ObjectUtils.compare(a.getSort(), b.getSort()))
                        .filter(itemProcessor -> itemProcessor.getIsGift() > Constants.ChargeFormItemGiftType.NOT_GIFT)
                        .map(PharmacyItemProcessor::generateRefundPrintView)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    private static List<ChargeFormItemPrintView> generateNonMedicineRefundPrintView(List<FormProcessor> formProcessors, int productType) {
        return formProcessors
                .stream()
                .filter(formProcessor -> formProcessor.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_CHINESE
                                && formProcessor.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_WESTERN
//                      &&  formProcessor.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_INFUSION
                                && formProcessor.getSourceFormType() != Constants.SourceFormType.GIFT_PRODUCT
//                        && formProcessor.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_EXTERNAL
                )
                .sorted(Comparator.comparing(formProcessor -> (formProcessor.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_INFUSION
                        || formProcessor.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_EXTERNAL) ? 1 : 0))
                .map(formProcessor -> formProcessor.getItemProcessorList()
                        .stream()
                        .sorted((a, b) -> ObjectUtils.compare(a.getSort(), b.getSort()))
                        .filter(itemProcessor -> {
                            // 只有 耗材、检查检验、治疗理疗、其它费用 才能从输注和外治处方中取，其它productType 不从输注和外治处方取
                            if (productType != Constants.ProductType.MATERIAL
                                    && productType != Constants.ProductType.SALE_PRODUCT
                                    && productType != Constants.ProductType.EXAMINATION
                                    && productType != Constants.ProductType.TREATMENT
                                    && productType != Constants.ProductType.OTHER_FEE) {
                                if (formProcessor.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_INFUSION
                                        || formProcessor.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_EXTERNAL){
                                    return false;
                                }
                            }
                            return itemProcessor.getProductType() == productType;
                        })
                        .map(ItemProcessor::generateRefundPrintView)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    private static List<ChargeFormItemPrintView> generatePharmacyNonMedicineRefundPrintView(List<PharmacyFormProcessor> formProcessors, int productType) {
        return formProcessors
                .stream()
                .filter(formProcessor -> formProcessor.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_CHINESE
                                && formProcessor.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_WESTERN
//                      &&  formProcessor.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_INFUSION
                                && formProcessor.getSourceFormType() != Constants.SourceFormType.GIFT_PRODUCT
//                        && formProcessor.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_EXTERNAL
                )
                .sorted(Comparator.comparing(formProcessor -> (formProcessor.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_INFUSION
                        || formProcessor.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_EXTERNAL) ? 1 : 0))
                .map(formProcessor -> formProcessor.getItemProcessorList()
                        .stream()
                        .sorted((a, b) -> ObjectUtils.compare(a.getSort(), b.getSort()))
                        .filter(itemProcessor -> {
                            // 只有 耗材、检查检验、治疗理疗、其它费用 才能从输注和外治处方中取，其它productType 不从输注和外治处方取
                            if (productType != Constants.ProductType.MATERIAL
                                    && productType != Constants.ProductType.SALE_PRODUCT
                                    && productType != Constants.ProductType.EXAMINATION
                                    && productType != Constants.ProductType.TREATMENT
                                    && productType != Constants.ProductType.OTHER_FEE) {
                                if (formProcessor.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_INFUSION
                                        || formProcessor.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_EXTERNAL){
                                    return false;
                                }
                            }
                            return itemProcessor.getProductType() == productType;
                        })
                        .map(PharmacyItemProcessor::generateRefundPrintView)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }


    private static ChargeFormItemPrintView generateAdjustmentPrintView(BigDecimal adjustmentFee, BigDecimal refundAdjustmentFee) {
        if (adjustmentFee.subtract(refundAdjustmentFee).compareTo(BigDecimal.ZERO) <= 0) {
            return null;
        }

        ChargeFormItemPrintView itemPrintView = new ChargeFormItemPrintView();
        itemPrintView.setCount(BigDecimal.ONE);
        itemPrintView.setUnitCount(BigDecimal.ONE);
        itemPrintView.setDoseCount(BigDecimal.ONE);
        itemPrintView.setType(ChargeFormItemPrintView.Type.OTHER);
        itemPrintView.setUnit("次");
        itemPrintView.setName("其他");
        itemPrintView.setId(AbcIdUtils.getUUID());
        itemPrintView.setTotalPrice(MathUtils.setScaleTwo(adjustmentFee.subtract(refundAdjustmentFee)));
        return itemPrintView;
    }

    private static void bindShebaoPrintInfo(ChargeSheet chargeSheet, int payTaskType, ChargeSheetPrintView chargeSheetPrintView, SheetProcessorInfoProvider sheetProcessorInfoProvider, ChargeTransactionAndActionDto chargeTransactionAndActionDto) {

        //判断社保信息出处
        String chargeId = chargeSheetPrintView.getId();

        if (chargeTransactionAndActionDto != null) {
            for (ChargeTransaction chargeTransaction : chargeTransactionAndActionDto.getChargeTransactions()) {
                if (ChargeTransaction.ChargeType.OWE != chargeTransaction.getChargeType() && Constants.ChargePayMode.HEALTH_CARD == chargeTransaction.getPayMode()) {
                    chargeId = chargeTransaction.getChargeSheetId();
                    break;
                }

                if (ChargeTransaction.ChargeType.OWE == chargeTransaction.getChargeType() && Constants.ChargePayMode.HEALTH_CARD == chargeTransaction.getPayMode()) {
                    if (chargeTransactionAndActionDto.getLastOweSheet() != null) {
                        chargeId = chargeTransactionAndActionDto.getLastOweSheet().getId().toString();
                    }
                }
            }
        }


        QueryChargeSheetShebaoInfoReq req = new QueryChargeSheetShebaoInfoReq();
        req.setChainId(chargeSheet.getChainId());
        req.setClinicId(chargeSheet.getClinicId());
        req.setDoctorId(chargeSheet.getDoctorId());
        req.setChargeSheetId(chargeId);
        req.setPayTaskType(payTaskType);
        req.setIsCharged((chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED || chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_CHARGED) ? QueryChargeSheetShebaoInfoReq.IsCharged.NOT : QueryChargeSheetShebaoInfoReq.IsCharged.CHARGED);
        if (payTaskType == QueryChargeSheetShebaoInfoReq.PayTaskType.REFUND) {
            req.setSheetReceivableFee(chargeSheet.getRefundFee().abs());
        } else {
            req.setSheetReceivableFee(chargeSheet.calculateNetIncomeFeeForShebaoPrint());
        }
        if (chargeSheetPrintView.getChargeForms() != null) {
            req.setGoodsItems(chargeSheetPrintView.getChargeForms()
                    .stream()
                    .filter(chargeFormPrintView -> chargeFormPrintView.getChargeFormItems() != null)
                    .flatMap(chargeFormPrintView -> chargeFormPrintView.getChargeFormItems().stream())
                    .filter(chargeFormItemPrintView -> !TextUtils.isEmpty(chargeFormItemPrintView.getProductId()))
                    .map(chargeFormItemPrintView -> {
                        List<QueryChargeSheetShebaoInfoReq.GoodsItem> goodsItems = new ArrayList<>();
                        addChildrenShebaoInfoGoodsItem(chargeFormItemPrintView, goodsItems, true);
                        return goodsItems;
                    })
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList()));
        }

        QueryChargeSheetShebaoInfoRsp rsp = sheetProcessorInfoProvider.getShebaoInfoProvider().queryChargeSheetShebaoInfo(req);
        if (rsp == null) {
            return;
        }

        Map<String, QueryChargeSheetShebaoInfoRsp.GoodsItem> groupByChargeFormItemIdOrGoodsItemIdMap = PrintUtils.convertShebaoGoodsItemMap(rsp.getGoodsItems());

        chargeSheetPrintView.setHospitalCode(rsp.getHospitalCode());
        chargeSheetPrintView.setDoctorWorkNo(rsp.getDoctorWorkNo());
        if (chargeSheetPrintView.getChargeForms() != null) {
            chargeSheetPrintView.getChargeForms()
                    .stream()
                    .filter(chargeFormPrintView -> chargeFormPrintView.getChargeFormItems() != null)
                    .flatMap(chargeFormPrintView -> chargeFormPrintView.getChargeFormItems().stream())
                    .filter(chargeFormItemPrintView -> !TextUtils.isEmpty(chargeFormItemPrintView.getProductId()))
                    .forEach(chargeFormItemPrintView -> bindChildrenShebaoPrintInfo(chargeFormItemPrintView, groupByChargeFormItemIdOrGoodsItemIdMap));
        }
        BigDecimal personalPaymentFee = MathUtils.wrapBigDecimalSubtract(chargeSheetPrintView.getNetIncomeFee(), chargeSheetPrintView.getHealthCardPaymentFee());
        ChargeSheetPrintView.ShebaoPaymentExtend shebaoPaymentExtend = ChargeSheetPrintView.ShebaoPaymentExtend.ofShebaoPaymentExtend(rsp.getShebaoPayment(), rsp.getRegion(), personalPaymentFee);
        if (payTaskType == QueryChargeSheetShebaoInfoReq.PayTaskType.REFUND) {
            personalPaymentFee = MathUtils.wrapBigDecimalSubtract(chargeSheetPrintView.getRefundFee(), chargeSheetPrintView.getHealthCardPaymentFee());
            if (shebaoPaymentExtend != null) {
                shebaoPaymentExtend.setPersonalPaymentFee(MathUtils.wrapBigDecimalSubtract(chargeSheetPrintView.getRefundFee(), chargeSheetPrintView.getHealthCardPaymentFee()));
            }
        }
        chargeSheetPrintView.setPersonalPaymentFee(personalPaymentFee);
        chargeSheetPrintView.setShebaoPayment(shebaoPaymentExtend);
    }

    private static void bindChildrenShebaoPrintInfo(ChargeFormItemPrintView chargeFormItemPrintView,
                                                    Map<String, QueryChargeSheetShebaoInfoRsp.GoodsItem> groupByChargeFormItemIdOrGoodsItemIdMap) {

        //先通过id去找，找不到就用productId去找
        QueryChargeSheetShebaoInfoRsp.GoodsItem shebaoGoodsItem = Optional.ofNullable(groupByChargeFormItemIdOrGoodsItemIdMap.getOrDefault(chargeFormItemPrintView.getId(), null))
                .orElse(groupByChargeFormItemIdOrGoodsItemIdMap.getOrDefault(chargeFormItemPrintView.getProductId(), null));
        bindShebaoPrintInfo(chargeFormItemPrintView, shebaoGoodsItem);

        if ((ChargeFormItemUtils.isParentItem(chargeFormItemPrintView.getComposeType(), chargeFormItemPrintView.getGoodsFeeType()) || chargeFormItemPrintView.getIsExaminationSplitPrint() == 1)
                && org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeFormItemPrintView.getComposeChildren())) {
            chargeFormItemPrintView.getComposeChildren().forEach(child -> bindChildrenShebaoPrintInfo(child, groupByChargeFormItemIdOrGoodsItemIdMap));
        }

    }

    public static void addChildrenShebaoInfoGoodsItem(ChargeFormItemPrintView chargeFormItemPrintView, List<QueryChargeSheetShebaoInfoReq.GoodsItem> goodsItems, boolean needItemId) {
        goodsItems.add(generateShebaoInfoGoodsItem(chargeFormItemPrintView, needItemId));

        if (org.apache.commons.collections.CollectionUtils.isEmpty(chargeFormItemPrintView.getComposeChildren())) {
            return;
        }

        if ((ChargeFormItemUtils.isParentItem(chargeFormItemPrintView.getComposeType(), chargeFormItemPrintView.getGoodsFeeType()) || chargeFormItemPrintView.getIsExaminationSplitPrint() == 1)
                && org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeFormItemPrintView.getComposeChildren())) {
            boolean childNeedItemId;
            if (chargeFormItemPrintView.getIsExaminationSplitPrint() == 1) {
                childNeedItemId = false;
            } else {
                childNeedItemId = needItemId;
            }
            chargeFormItemPrintView.getComposeChildren().forEach(child -> addChildrenShebaoInfoGoodsItem(child, goodsItems, childNeedItemId));
        }
    }


    public static QueryChargeSheetShebaoInfoReq.GoodsItem generateShebaoInfoGoodsItem(ChargeFormItemPrintView chargeFormItemPrintView, boolean needItemId) {

        if (chargeFormItemPrintView == null) {
            return null;
        }

        QueryChargeSheetShebaoInfoReq.GoodsItem reqGoodsItem = new QueryChargeSheetShebaoInfoReq.GoodsItem();
        if (needItemId) {
            reqGoodsItem.setChargeFormItemId(chargeFormItemPrintView.getId());
        }
        reqGoodsItem.setId(chargeFormItemPrintView.getProductId());
        reqGoodsItem.setType(chargeFormItemPrintView.getProductType());
        reqGoodsItem.setSubType(chargeFormItemPrintView.getProductSubType());
        reqGoodsItem.setDetItemFeeSumamt(MathUtils.wrapBigDecimalOrZero(chargeFormItemPrintView.getDiscountedPrice()).abs());

        return reqGoodsItem;
    }


    private static void bindShebaoPrintInfo(ChargeFormItemPrintView chargeFormItemPrintView, QueryChargeSheetShebaoInfoRsp.GoodsItem rspItem) {

        if (chargeFormItemPrintView == null || rspItem == null) {
            return;
        }

        chargeFormItemPrintView.setSocialCode(rspItem.getSocialCode());
        chargeFormItemPrintView.setHisCode(rspItem.getHisCode());
        chargeFormItemPrintView.setMedicalFeeGrade(rspItem.getMedicalFeeGrade());
        chargeFormItemPrintView.setOwnExpenseRatio(rspItem.getOwnExpenseRatio());
        chargeFormItemPrintView.setOwnExpenseFee(rspItem.getOwnExpenseFee());
        chargeFormItemPrintView.setInscpScpAmt(rspItem.getInscpScpAmt());
        chargeFormItemPrintView.setOverlmtAmt(rspItem.getOverlmtAmt());
        if (chargeFormItemPrintView.getProductType() == Constants.ProductType.EXAMINATION ||
                chargeFormItemPrintView.getProductType() == Constants.ProductType.TREATMENT ||
                chargeFormItemPrintView.getProductType() == Constants.ProductType.REGISTRATION) {
            if (!TextUtils.isEmpty(rspItem.getSpec())) {
                chargeFormItemPrintView.setSocialUnit(rspItem.getSpec());
            }

            if (!TextUtils.isEmpty(rspItem.getSocialName())) {
                chargeFormItemPrintView.setSocialName(rspItem.getSocialName());
            }
        }
    }

    private static List<GiftCouponPrintView> generateGiftCouponPrintViews(ChargeSheet chargeSheet) {

        if (chargeSheet == null
                || CollectionUtils.isEmpty(chargeSheet.getGiftRulePromotionInfos())
                || StringUtils.isEmpty(chargeSheet.getPatientId())
                || Constants.ANONYMOUS_PATIENT_ID.equals(chargeSheet.getPatientId())) {
            return new ArrayList<>();
        }

        return chargeSheet.getGiftRulePromotionInfos().stream()
                .filter(giftRulePromotionInfo -> giftRulePromotionInfo.getIsDeleted() == 0)
                .filter(giftRulePromotionInfo -> !StringUtils.isEmpty(giftRulePromotionInfo.getChecked()) && "true".equals(giftRulePromotionInfo.getChecked()))
                .filter(giftRulePromotionInfo -> !StringUtils.isEmpty(giftRulePromotionInfo.getGiftCoupons()))
                .flatMap(giftRulePromotionInfo -> JsonUtils.getList(giftRulePromotionInfo.getGiftCoupons(), GiftCouponView.class).stream())
                .filter(giftCouponView -> giftCouponView.getCount() > 0)
                .map(giftCouponView -> {
                    GiftCouponPrintView giftCouponPrintView = new GiftCouponPrintView();
                    giftCouponPrintView.setId(giftCouponView.getPromotionId());
                    giftCouponPrintView.setName(giftCouponView.getName());
                    giftCouponPrintView.setCount(giftCouponView.getCount());
                    giftCouponPrintView.setValidDays(giftCouponView.getValidDays());
                    giftCouponPrintView.setValidEnd(giftCouponView.getValidEnd());
                    giftCouponPrintView.setValidType(giftCouponView.getValidType());
                    return giftCouponPrintView;
                })
                .collect(Collectors.toList());
    }

//    private static List<HealthCardExtraItemPrintView> generateCqHealthCardExtraItems(PayExtraInfo.CqShebaoExtraInfo cqShebaoExtraInfo) {
//        if (cqShebaoExtraInfo == null) {
//            return null;
//        }
//
//        List<HealthCardExtraItemPrintView> healthCardExtraItemPrintViews = new ArrayList<>();
//
//        addHealthCardExtraItemPrintViewWhenValid(healthCardExtraItemPrintViews, "统筹支付", cqShebaoExtraInfo.getFundPaymentFee());
//        addHealthCardExtraItemPrintViewWhenValid(healthCardExtraItemPrintViews, "账户支付", cqShebaoExtraInfo.getAccountPaymentFee());
//        addHealthCardExtraItemPrintViewWhenValid(healthCardExtraItemPrintViews, "公务员补助", cqShebaoExtraInfo.getCivilServiceSubsidy());
//        addHealthCardExtraItemPrintViewWhenValid(healthCardExtraItemPrintViews, "大额理赔", cqShebaoExtraInfo.getLargeClaimAmount());
//        addHealthCardExtraItemPrintViewWhenValid(healthCardExtraItemPrintViews, "公务员返还", cqShebaoExtraInfo.getCivilServiceBack());
//
//        BigDecimal civilAidAmount = MathUtils.wrapBigDecimalSubtract(cqShebaoExtraInfo.getCivilAidAmount(), cqShebaoExtraInfo.getHealthHelpFundFee());
//        civilAidAmount = MathUtils.wrapBigDecimalSubtract(civilAidAmount, cqShebaoExtraInfo.getPrecisionPovertyFee());
//        civilAidAmount = MathUtils.wrapBigDecimalSubtract(civilAidAmount, cqShebaoExtraInfo.getOtherPovertyReliefFee());
//        addHealthCardExtraItemPrintViewWhenValid(healthCardExtraItemPrintViews, "民政救助", civilAidAmount);
//
//        addHealthCardExtraItemPrintViewWhenValid(healthCardExtraItemPrintViews, "健康扶贫", cqShebaoExtraInfo.getHealthHelpFundFee());
//        addHealthCardExtraItemPrintViewWhenValid(healthCardExtraItemPrintViews, "精准脱贫", cqShebaoExtraInfo.getPrecisionPovertyFee());
//        addHealthCardExtraItemPrintViewWhenValid(healthCardExtraItemPrintViews, "其他扶贫", cqShebaoExtraInfo.getOtherPovertyReliefFee());
//        addHealthCardExtraItemPrintViewWhenValid(healthCardExtraItemPrintViews, "超标扣款金额", cqShebaoExtraInfo.getSingleDiseaseSupport());
//        addHealthCardExtraItemPrintViewWhenValid(healthCardExtraItemPrintViews, "账户抵用金额", cqShebaoExtraInfo.getTotalSubstituteFee());
//        return healthCardExtraItemPrintViews;
//    }

    private static void addHealthCardExtraItemPrintViewWhenValid(List<HealthCardExtraItemPrintView> healthCardExtraItemPrintViews, String label, BigDecimal value) {
        if (value != null && value.compareTo(BigDecimal.ZERO) != 0) {
            healthCardExtraItemPrintViews.add(new HealthCardExtraItemPrintView(label, MathUtils.setScaleTwo(value).toPlainString()));
        }
    }

    private static Map<Long, ChargePayModeConfigSimple> getChargePayModeConfigSimple(SheetProcessorInfoProvider sheetProcessorInfoProvider, ChargeSheet chargeSheet) {
        if (sheetProcessorInfoProvider != null && chargeSheet != null && !StringUtils.isEmpty(chargeSheet.getChainId())) {
            return sheetProcessorInfoProvider.getChargePayModeProvider().getChargePayModeConfigSimpleByChainId(chargeSheet.getChainId());
        }
        return new HashMap<>();
    }

    public static ChargeSheetSummary toSummaryView(SheetProcessor sheetProcessor, List<ChargeOweSheet> chargeOweSheets) throws ServiceInternalException {
        //初始化
        ChargeSheet chargeSheet = sheetProcessor.chargeSheet;
        SheetProcessorInfoProvider sheetProcessorInfoProvider = sheetProcessor.sheetProcessorInfoProvider;
        PatientInfo patientInfo = sheetProcessor.getPatientInfo();
        List<FormProcessor> formProcessors = sheetProcessor.formProcessors;
        BigDecimal adjustmentFee = sheetProcessor.adjustmentFee;
        BigDecimal refundAdjustmentFee = sheetProcessor.refundAdjustmentFee;
        BigDecimal totalFee = sheetProcessor.totalFee;
        BigDecimal sourceTotalFee = sheetProcessor.sourceTotalFee;
        BigDecimal unitAdjustmentFee = sheetProcessor.unitAdjustmentFee;
        BigDecimal refundTotalFee = sheetProcessor.refundTotalFee;
        BigDecimal refundDiscountFee = sheetProcessor.refundDiscountFee;
        BigDecimal discountFee = sheetProcessor.discountFee;
        BigDecimal _refundFee = sheetProcessor._refundFee;
        BigDecimal _receivedFee = sheetProcessor._receivedFee;
        BigDecimal _adjustmentDiscountFee = sheetProcessor._adjustmentDiscountFee;
        BigDecimal receivableFee = sheetProcessor.receivableFee;
        BigDecimal realReceivableFee = sheetProcessor.realReceivableFee;
        BigDecimal _adjustmentAddFee = sheetProcessor._adjustmentAddFee;
        BigDecimal _owedRefundFee = sheetProcessor._owedRefundFee;
        BigDecimal oddFee = sheetProcessor.oddFee;
        Integer roundingType = sheetProcessor.roundingType;
        BigDecimal afterRoundingDiscountedTotalFee = sheetProcessor.afterRoundingDiscountedTotalFee;
        BigDecimal draftAdjustmentFee = sheetProcessor.draftAdjustmentFee;
        BigDecimal sheBaoReceivableFee = sheetProcessor.sheBaoReceivableFee;
        PatientOrder patientOrder = sheetProcessor.patientOrder;

        ChargeSheetSummary chargeSheetSummary = new ChargeSheetSummary();
        // 操作信息
        Set<String> employeeIds = new HashSet<>();
        if (!TextUtils.isEmpty(chargeSheet.getCreatedBy())) {
            employeeIds.add(chargeSheet.getCreatedBy());
        }
        if (!TextUtils.isEmpty(chargeSheet.getLastModifiedBy())) {
            employeeIds.add(chargeSheet.getLastModifiedBy());
        }
        if (!TextUtils.isEmpty(chargeSheet.getChargedBy())) {
            employeeIds.add(chargeSheet.getChargedBy());
        }
        Map<String, String> employeeMap = sheetProcessorInfoProvider.getEmployeeInfoProvider().findEmployeeIdNameMap(chargeSheet.getChainId(), new ArrayList<>(employeeIds));
        chargeSheetSummary.setCreatedByName(employeeMap.getOrDefault(chargeSheet.getCreatedBy(), null));
        chargeSheetSummary.setCreated(chargeSheet.getCreated());
        chargeSheetSummary.setDiagnosedDate(chargeSheet.getDiagnosedDate() != null ? chargeSheet.getDiagnosedDate() : chargeSheet.getCreated());
        chargeSheetSummary.setOutpatientAdjustmentFee(chargeSheet.getOutpatientAdjustmentFee());
        if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED) {
            chargeSheetSummary.setOddFee(oddFee);
            chargeSheetSummary.setDraftAdjustmentFee(draftAdjustmentFee);
        } else {
            chargeSheetSummary.setOddFee(chargeSheet.getOddFee());
            chargeSheetSummary.setDraftAdjustmentFee(chargeSheet.getDraftAdjustmentFee());
        }

        chargeSheetSummary.setLastModifiedByName(employeeMap.getOrDefault(chargeSheet.getLastModifiedBy(), null));
        chargeSheetSummary.setLastModified(chargeSheet.getLastModified());


        chargeSheetSummary.setChargedByName(employeeMap.getOrDefault(chargeSheet.getChargedBy(), null));
        chargeSheetSummary.setChargedTime(chargeSheet.getChargedTime());

        //医生名字取快照
        if (!TextUtils.isEmpty(chargeSheet.getChargeSheetDoctorId())) {

            Instant doctorIdSnapTime = Optional.ofNullable(chargeSheet.getDiagnosedDate()).orElse(Instant.now());

            BatchQueryEmployeeSnapshotRsp queryEmployeeSnapRsp = sheetProcessorInfoProvider.getClinicProvider().queryEmployeeSnap(new BatchQueryEmployeeSnapshotReq()
                    .setChainId(chargeSheet.getChainId())
                    .addBusinessTimeEmployeeSnap(chargeSheet.getChargeSheetDoctorId(), doctorIdSnapTime)
            );
            chargeSheetSummary.setDoctorName(queryEmployeeSnapRsp.getEmployeeNameByBusTime(chargeSheet.getChargeSheetDoctorId(), doctorIdSnapTime));
        }

        // 支付方式
        // 只收集大于0的情况
        chargeSheetSummary.setPaymentSummaryInfos(new ArrayList<>());
        if (chargeSheet.getChargeTransactions() != null) {
            Function<List<String>, List<PatientCardView>> queryPatientCardViewFunc = promotionCardIds -> sheetProcessorInfoProvider.getPromotionProvider().listPatientsCardsByIds(chargeSheet.getChainId(), promotionCardIds);
            Function<String, MemberInfo> queryMemberInfoFunc = memberId -> sheetProcessorInfoProvider.getPatientInfoProvider().findMemberInfo(memberId, chargeSheet.getClinicId(), chargeSheet.getChainId());
            chargeSheetSummary.setPaymentSummaryInfos(generatePaymentSummaryInfos(chargeSheet, chargeOweSheets, queryPatientCardViewFunc, queryMemberInfoFunc, sheetProcessorInfoProvider));
        }

        // 如果有会员卡支付，找到最后一次会员卡支付的余额
        if (chargeSheet.getChargeTransactions() != null) {
            ChargeTransaction lastMemberCardChargeTransaction = chargeSheet.getChargeTransactions()
                    .stream()
                    .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.MEMBER_CARD)
                    .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                    .sorted((a, b) -> -ObjectUtils.compare(a.getCreated(), b.getCreated()))
                    .findFirst()
                    .orElse(null);


            PatientInfo memberPatientInfo = null;
            if (chargeSheet.getPatientId().equals(chargeSheet.getMemberId())) {
                memberPatientInfo = patientInfo;
            } else if (!StringUtils.isEmpty(chargeSheet.getMemberId())) {
                //TODO robinsli 也只是取手机号
                memberPatientInfo = sheetProcessorInfoProvider.getPatientInfoProvider().findPatientInfoById(chargeSheet.getChainId(), chargeSheet.getClinicId(), chargeSheet.getMemberId(), false, false);
            }

            if (memberPatientInfo != null && lastMemberCardChargeTransaction != null) {
                chargeSheetSummary.setMemberCardBalance(lastMemberCardChargeTransaction.getThirdPartyPayCardBalance());
                chargeSheetSummary.setMemberCardMobile(memberPatientInfo.getMobile());
            }
        }

        // 找到挂号处收费已支付金额信息
//        PriceCell registrationPriceCell = getRegistrationPaidPriceCell();

        chargeSheetSummary.setTotalFee(totalFee);
        chargeSheetSummary.setSourceTotalFee(sourceTotalFee);
        chargeSheetSummary.setUnitAdjustmentFee(unitAdjustmentFee);
        chargeSheetSummary.setDiscountFee(discountFee.subtract(_adjustmentDiscountFee));
        chargeSheetSummary.setAdjustmentFee(_adjustmentDiscountFee.compareTo(BigDecimal.ZERO) < 0 ? _adjustmentDiscountFee : _adjustmentAddFee.compareTo(BigDecimal.ZERO) > 0 ? _adjustmentAddFee : adjustmentFee);
        chargeSheetSummary.setReceivableFee(receivableFee);
        chargeSheetSummary.setSheBaoReceivableFee(sheBaoReceivableFee);
        chargeSheetSummary.setReceivedFee(_receivedFee);
        chargeSheetSummary.setRefundFee(_refundFee);
        chargeSheetSummary.setNeedPayFee(realReceivableFee);
        chargeSheetSummary.setRoundingType(roundingType);
        chargeSheetSummary.setAfterRoundingDiscountedTotalFee(afterRoundingDiscountedTotalFee);
        chargeSheetSummary.setOweFee(Optional.ofNullable(chargeOweSheets).orElse(new ArrayList<>())
                .stream()
                .filter(chargeOweSheet -> chargeOweSheet.getIsOldRecord() == 0)
                .max(Comparator.comparing(ChargeOweSheet::getCreated))
                .map(ChargeOweSheet::calculateReceivableFee)
                .orElse(BigDecimal.ZERO)
        );

        //微诊所的总金额展示：如果待收费，则直接等于totalFee+议价值， 如果已收，判断是议价加价还是议价减价，如果议价加价，则等于totalFee - 以价值，如果减价，则等于totalFee
        chargeSheetSummary.setPatientTotalFee(ChargeUtils.fixPatientTotalFee(chargeSheet.getStatus(), chargeSheetSummary.getTotalFee(), chargeSheetSummary.getAdjustmentFee()));

        // 对于从老系统迁移过来的已付费订单，设置needPayFee为0
        if (!TextUtils.isEmpty(chargeSheet.getV1Id()) && (chargeSheet.getStatus() == Constants.ChargeSheetStatus.CHARGED ||
                chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_REFUNDED || chargeSheet.getStatus() == Constants.ChargeSheetStatus.REFUNDED)) {
            chargeSheetSummary.setNeedPayFee(BigDecimal.ZERO);
        }
        chargeSheetSummary.setOwedRefundFee(MathUtils.setScaleTwo(_owedRefundFee));
        chargeSheetSummary.setNetIncomeFee(MathUtils.setScaleTwo(_receivedFee.add(_refundFee)));

        chargeSheetSummary.setNetTotalFee(totalFee.subtract(refundTotalFee));
        chargeSheetSummary.setNetDiscountFee(discountFee.subtract(refundDiscountFee));
        chargeSheetSummary.setNetAdjustmentFee(adjustmentFee.subtract(refundAdjustmentFee));

        chargeSheetSummary.setChineseMedicineFee(formProcessors.stream().map(formProcessor -> formProcessor.summaryTotalFee(FormProcessor.SummaryFeeType.CHINESE_MEDICINE)).reduce(BigDecimal.ZERO, BigDecimal::add));
        chargeSheetSummary.setWesternMedicineFee(formProcessors.stream().map(formProcessor -> formProcessor.summaryTotalFee(FormProcessor.SummaryFeeType.WESTERN_MEDICINE)).reduce(BigDecimal.ZERO, BigDecimal::add));
        chargeSheetSummary.setMaterialFee(formProcessors.stream().map(formProcessor -> formProcessor.summaryTotalFee(FormProcessor.SummaryFeeType.MATERIAL)).reduce(BigDecimal.ZERO, BigDecimal::add));
        chargeSheetSummary.setExaminationTreatmentFee(formProcessors.stream().map(formProcessor -> formProcessor.summaryTotalFee(FormProcessor.SummaryFeeType.EXAMINATION_TREATMENT)).reduce(BigDecimal.ZERO, BigDecimal::add));
        chargeSheetSummary.setRegistrationFee(formProcessors.stream().map(formProcessor -> formProcessor.summaryTotalFee(FormProcessor.SummaryFeeType.REGISTRATION)).reduce(BigDecimal.ZERO, BigDecimal::add));

        chargeSheetSummary.setChineseMedicineDiscountFee(formProcessors.stream().map(formProcessor -> formProcessor.summaryDiscountFee(FormProcessor.SummaryFeeType.CHINESE_MEDICINE)).reduce(BigDecimal.ZERO, BigDecimal::add));
        chargeSheetSummary.setWesternMedicineDiscountFee(formProcessors.stream().map(formProcessor -> formProcessor.summaryDiscountFee(FormProcessor.SummaryFeeType.WESTERN_MEDICINE)).reduce(BigDecimal.ZERO, BigDecimal::add));
        chargeSheetSummary.setMaterialDiscountFee(formProcessors.stream().map(formProcessor -> formProcessor.summaryDiscountFee(FormProcessor.SummaryFeeType.MATERIAL)).reduce(BigDecimal.ZERO, BigDecimal::add));
        chargeSheetSummary.setExaminationDiscountFee(formProcessors.stream().map(formProcessor -> formProcessor.summaryDiscountFee(FormProcessor.SummaryFeeType.EXAMINATION)).reduce(BigDecimal.ZERO, BigDecimal::add));
        chargeSheetSummary.setRegistrationDiscountFee(formProcessors.stream().map(formProcessor -> formProcessor.summaryDiscountFee(FormProcessor.SummaryFeeType.REGISTRATION)).reduce(BigDecimal.ZERO, BigDecimal::add));
        chargeSheetSummary.setTreatmentDiscountFee(formProcessors.stream().map(formProcessor -> formProcessor.summaryDiscountFee(FormProcessor.SummaryFeeType.TREATMENT)).reduce(BigDecimal.ZERO, BigDecimal::add));


        return chargeSheetSummary;
    }

    public static ChargeSheetSummary toPharmacySummaryView(PharmacySheetProcessor sheetProcessor, List<ChargeOweSheet> chargeOweSheets) throws ServiceInternalException {
        //初始化
        ChargeSheet chargeSheet = sheetProcessor.chargeSheet;
        SheetProcessorInfoProvider sheetProcessorInfoProvider = sheetProcessor.sheetProcessorInfoProvider;
        PatientInfo patientInfo = sheetProcessor.getPatientInfo();
        List<PharmacyFormProcessor> formProcessors = sheetProcessor.formProcessors;
        BigDecimal adjustmentFee = sheetProcessor.adjustmentFee;
        BigDecimal refundAdjustmentFee = sheetProcessor.refundAdjustmentFee;
        BigDecimal totalFee = sheetProcessor.totalFee;
        BigDecimal sourceTotalFee = sheetProcessor.sourceTotalFee;
        BigDecimal unitAdjustmentFee = sheetProcessor.unitAdjustmentFee;
        BigDecimal itemAdjustmentTotalFee = sheetProcessor.itemAdjustmentTotalFee;
        BigDecimal refundTotalFee = sheetProcessor.refundTotalFee;
        BigDecimal refundDiscountFee = sheetProcessor.refundDiscountFee;
        BigDecimal discountFee = sheetProcessor.discountFee;
        BigDecimal _refundFee = sheetProcessor._refundFee;
        BigDecimal _receivedFee = sheetProcessor._receivedFee;
        BigDecimal _adjustmentDiscountFee = sheetProcessor._adjustmentDiscountFee;
        BigDecimal receivableFee = sheetProcessor.receivableFee;
        BigDecimal realReceivableFee = sheetProcessor.realReceivableFee;
        BigDecimal _adjustmentAddFee = sheetProcessor._adjustmentAddFee;
        BigDecimal _owedRefundFee = sheetProcessor._owedRefundFee;
        BigDecimal oddFee = sheetProcessor.oddFee;
        Integer roundingType = sheetProcessor.roundingType;
        BigDecimal afterRoundingDiscountedTotalFee = sheetProcessor.afterRoundingDiscountedTotalFee;
        BigDecimal draftAdjustmentFee = sheetProcessor.draftAdjustmentFee;
        BigDecimal sheBaoReceivableFee = sheetProcessor.sheBaoReceivableFee;
        BigDecimal singlePromotionFee = sheetProcessor.singlePromotionFee;
        BigDecimal packagePromotionFee = sheetProcessor.packagePromotionFee;
        PatientOrder patientOrder = sheetProcessor.patientOrder;

        ChargeSheetSummary chargeSheetSummary = new ChargeSheetSummary();
        // 操作信息
        Set<String> employeeIds = new HashSet<>();
        if (!TextUtils.isEmpty(chargeSheet.getCreatedBy())) {
            employeeIds.add(chargeSheet.getCreatedBy());
        }
        if (!TextUtils.isEmpty(chargeSheet.getLastModifiedBy())) {
            employeeIds.add(chargeSheet.getLastModifiedBy());
        }
        if (!TextUtils.isEmpty(chargeSheet.getChargedBy())) {
            employeeIds.add(chargeSheet.getChargedBy());
        }
        Map<String, String> employeeMap = sheetProcessorInfoProvider.getEmployeeInfoProvider().findEmployeeIdNameMap(chargeSheet.getChainId(), new ArrayList<>(employeeIds));
        chargeSheetSummary.setCreatedByName(employeeMap.getOrDefault(chargeSheet.getCreatedBy(), null));
        chargeSheetSummary.setCreated(chargeSheet.getCreated());
        chargeSheetSummary.setDiagnosedDate(chargeSheet.getDiagnosedDate() != null ? chargeSheet.getDiagnosedDate() : chargeSheet.getCreated());
        chargeSheetSummary.setOutpatientAdjustmentFee(chargeSheet.getOutpatientAdjustmentFee());
        if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED) {
            chargeSheetSummary.setOddFee(oddFee);
            chargeSheetSummary.setDraftAdjustmentFee(draftAdjustmentFee);
        } else {
            chargeSheetSummary.setOddFee(chargeSheet.getOddFee());
            chargeSheetSummary.setDraftAdjustmentFee(chargeSheet.getDraftAdjustmentFee());
        }

        chargeSheetSummary.setLastModifiedByName(employeeMap.getOrDefault(chargeSheet.getLastModifiedBy(), null));
        chargeSheetSummary.setLastModified(chargeSheet.getLastModified());


        chargeSheetSummary.setChargedByName(employeeMap.getOrDefault(chargeSheet.getChargedBy(), null));
        chargeSheetSummary.setChargedTime(chargeSheet.getChargedTime());

        //医生名字取快照
        if (!TextUtils.isEmpty(chargeSheet.getDoctorId())) {

            Instant doctorIdSnapTime = Optional.ofNullable(chargeSheet.getDiagnosedDate()).orElse(Instant.now());

            BatchQueryEmployeeSnapshotRsp queryEmployeeSnapRsp = sheetProcessorInfoProvider.getClinicProvider().queryEmployeeSnap(new BatchQueryEmployeeSnapshotReq()
                    .setChainId(chargeSheet.getChainId())
                    .addBusinessTimeEmployeeSnap(chargeSheet.getDoctorId(), doctorIdSnapTime)
            );
            chargeSheetSummary.setDoctorName(queryEmployeeSnapRsp.getEmployeeNameByBusTime(chargeSheet.getDoctorId(), doctorIdSnapTime));
        }
        // 支付方式
        // 只收集大于0的情况
        chargeSheetSummary.setPaymentSummaryInfos(new ArrayList<>());
        if (chargeSheet.getChargeTransactions() != null) {
            Function<List<String>, List<PatientCardView>> queryPatientCardViewFunc = promotionCardIds -> sheetProcessorInfoProvider.getPromotionProvider().listPatientsCardsByIds(chargeSheet.getChainId(), promotionCardIds);
            Function<String, MemberInfo> queryMemberInfoFunc = memberId -> sheetProcessorInfoProvider.getPatientInfoProvider().findMemberInfo(memberId, chargeSheet.getClinicId(), chargeSheet.getChainId());
            chargeSheetSummary.setPaymentSummaryInfos(generatePaymentSummaryInfos(chargeSheet, chargeOweSheets, queryPatientCardViewFunc, queryMemberInfoFunc, sheetProcessorInfoProvider));
        }

        // 如果有会员卡支付，找到最后一次会员卡支付的余额
        if (chargeSheet.getChargeTransactions() != null) {
            ChargeTransaction lastMemberCardChargeTransaction = chargeSheet.getChargeTransactions()
                    .stream()
                    .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.MEMBER_CARD)
                    .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                    .sorted((a, b) -> -ObjectUtils.compare(a.getCreated(), b.getCreated()))
                    .findFirst()
                    .orElse(null);


            PatientInfo memberPatientInfo = null;
            if (chargeSheet.getPatientId().equals(chargeSheet.getMemberId())) {
                memberPatientInfo = patientInfo;
            } else if (!StringUtils.isEmpty(chargeSheet.getMemberId())) {
                //TODO robinsli 也只是取手机号
                memberPatientInfo = sheetProcessorInfoProvider.getPatientInfoProvider().findPatientInfoById(chargeSheet.getChainId(), chargeSheet.getClinicId(), chargeSheet.getMemberId(), false, false);
            }

            if (memberPatientInfo != null && lastMemberCardChargeTransaction != null) {
                chargeSheetSummary.setMemberCardBalance(lastMemberCardChargeTransaction.getThirdPartyPayCardBalance());
                chargeSheetSummary.setMemberCardMobile(memberPatientInfo.getMobile());
            }
        }

        // 找到挂号处收费已支付金额信息
//        PriceCell registrationPriceCell = getRegistrationPaidPriceCell();

        chargeSheetSummary.setTotalFee(totalFee);
        chargeSheetSummary.setSourceTotalFee(sourceTotalFee);
        chargeSheetSummary.setUnitAdjustmentFee(unitAdjustmentFee);
        chargeSheetSummary.setDiscountFee(discountFee.subtract(_adjustmentDiscountFee));
        chargeSheetSummary.setAdjustmentFee(_adjustmentDiscountFee.compareTo(BigDecimal.ZERO) < 0 ? _adjustmentDiscountFee : _adjustmentAddFee.compareTo(BigDecimal.ZERO) > 0 ? _adjustmentAddFee : adjustmentFee);
        chargeSheetSummary.setReceivableFee(receivableFee);
        chargeSheetSummary.setSheBaoReceivableFee(sheBaoReceivableFee);
        chargeSheetSummary.setReceivedFee(_receivedFee);
        chargeSheetSummary.setRefundFee(_refundFee);
        chargeSheetSummary.setNeedPayFee(realReceivableFee);
        chargeSheetSummary.setRoundingType(roundingType);
        chargeSheetSummary.setAfterRoundingDiscountedTotalFee(afterRoundingDiscountedTotalFee);
        chargeSheetSummary.setSingleDiscountTotalFee(MathUtils.wrapBigDecimalAdd(singlePromotionFee, unitAdjustmentFee));
        chargeSheetSummary.setPackageDiscountTotalFee(MathUtils.wrapBigDecimalAdd(packagePromotionFee, itemAdjustmentTotalFee));
        chargeSheetSummary.setDiscountTotalFee(MathUtils.wrapBigDecimalAdd(chargeSheetSummary.getSingleDiscountTotalFee(), chargeSheetSummary.getPackageDiscountTotalFee()));

        chargeSheetSummary.setOweFee(Optional.ofNullable(chargeOweSheets).orElse(new ArrayList<>())
                .stream()
                .filter(chargeOweSheet -> chargeOweSheet.getIsOldRecord() == 0)
                .max(Comparator.comparing(ChargeOweSheet::getCreated))
                .map(ChargeOweSheet::calculateReceivableFee)
                .orElse(BigDecimal.ZERO)
        );

        //微诊所的总金额展示：如果待收费，则直接等于totalFee+议价值， 如果已收，判断是议价加价还是议价减价，如果议价加价，则等于totalFee - 以价值，如果减价，则等于totalFee
        chargeSheetSummary.setPatientTotalFee(ChargeUtils.fixPatientTotalFee(chargeSheet.getStatus(), chargeSheetSummary.getTotalFee(), chargeSheetSummary.getAdjustmentFee()));

        // 对于从老系统迁移过来的已付费订单，设置needPayFee为0
        if (!TextUtils.isEmpty(chargeSheet.getV1Id()) && (chargeSheet.getStatus() == Constants.ChargeSheetStatus.CHARGED ||
                chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_REFUNDED || chargeSheet.getStatus() == Constants.ChargeSheetStatus.REFUNDED)) {
            chargeSheetSummary.setNeedPayFee(BigDecimal.ZERO);
        }
        chargeSheetSummary.setOwedRefundFee(MathUtils.setScaleTwo(_owedRefundFee));
        chargeSheetSummary.setNetIncomeFee(MathUtils.setScaleTwo(_receivedFee.add(_refundFee)));

        chargeSheetSummary.setNetTotalFee(totalFee.subtract(refundTotalFee));
        chargeSheetSummary.setNetDiscountFee(discountFee.subtract(refundDiscountFee));
        chargeSheetSummary.setNetAdjustmentFee(adjustmentFee.subtract(refundAdjustmentFee));

        chargeSheetSummary.setChineseMedicineFee(formProcessors.stream().map(formProcessor -> formProcessor.summaryTotalFee(PharmacyFormProcessor.SummaryFeeType.CHINESE_MEDICINE)).reduce(BigDecimal.ZERO, BigDecimal::add));
        chargeSheetSummary.setWesternMedicineFee(formProcessors.stream().map(formProcessor -> formProcessor.summaryTotalFee(PharmacyFormProcessor.SummaryFeeType.WESTERN_MEDICINE)).reduce(BigDecimal.ZERO, BigDecimal::add));
        chargeSheetSummary.setMaterialFee(formProcessors.stream().map(formProcessor -> formProcessor.summaryTotalFee(PharmacyFormProcessor.SummaryFeeType.MATERIAL)).reduce(BigDecimal.ZERO, BigDecimal::add));
        chargeSheetSummary.setExaminationTreatmentFee(formProcessors.stream().map(formProcessor -> formProcessor.summaryTotalFee(PharmacyFormProcessor.SummaryFeeType.EXAMINATION_TREATMENT)).reduce(BigDecimal.ZERO, BigDecimal::add));
        chargeSheetSummary.setRegistrationFee(formProcessors.stream().map(formProcessor -> formProcessor.summaryTotalFee(PharmacyFormProcessor.SummaryFeeType.REGISTRATION)).reduce(BigDecimal.ZERO, BigDecimal::add));

        chargeSheetSummary.setChineseMedicineDiscountFee(formProcessors.stream().map(formProcessor -> formProcessor.summaryDiscountFee(PharmacyFormProcessor.SummaryFeeType.CHINESE_MEDICINE)).reduce(BigDecimal.ZERO, BigDecimal::add));
        chargeSheetSummary.setWesternMedicineDiscountFee(formProcessors.stream().map(formProcessor -> formProcessor.summaryDiscountFee(PharmacyFormProcessor.SummaryFeeType.WESTERN_MEDICINE)).reduce(BigDecimal.ZERO, BigDecimal::add));
        chargeSheetSummary.setMaterialDiscountFee(formProcessors.stream().map(formProcessor -> formProcessor.summaryDiscountFee(PharmacyFormProcessor.SummaryFeeType.MATERIAL)).reduce(BigDecimal.ZERO, BigDecimal::add));
        chargeSheetSummary.setExaminationDiscountFee(formProcessors.stream().map(formProcessor -> formProcessor.summaryDiscountFee(PharmacyFormProcessor.SummaryFeeType.EXAMINATION)).reduce(BigDecimal.ZERO, BigDecimal::add));
        chargeSheetSummary.setRegistrationDiscountFee(formProcessors.stream().map(formProcessor -> formProcessor.summaryDiscountFee(PharmacyFormProcessor.SummaryFeeType.REGISTRATION)).reduce(BigDecimal.ZERO, BigDecimal::add));
        chargeSheetSummary.setTreatmentDiscountFee(formProcessors.stream().map(formProcessor -> formProcessor.summaryDiscountFee(PharmacyFormProcessor.SummaryFeeType.TREATMENT)).reduce(BigDecimal.ZERO, BigDecimal::add));


        return chargeSheetSummary;
    }

    public static List<PaymentSummaryInfo> generatePaymentSummaryInfos(ChargeSheet chargeSheet, List<ChargeOweSheet> chargeOweSheets, Function<List<String>, List<PatientCardView>> queryPatientCardViewFunc, Function<String, MemberInfo> queryMemberInfoFunc, SheetProcessorInfoProvider infoProvider) {

        if (Objects.isNull(chargeSheet)) {
            return new ArrayList<>();
        }
        ChargeTransactionAndActionDto transactionAndActionDto = getChargeTransactionAndChargeActionContainOwe(chargeSheet, chargeOweSheets);

        //处理卡项是否失效的问题
        List<String> availablePatientCardIds = queryAvailablePatientCardIds(transactionAndActionDto.getChargeTransactions(), queryPatientCardViewFunc);

        //处理会员卡是否失效的问题
        boolean memberIsAvailable = queryMemberIsAvailable(transactionAndActionDto.getChargeTransactions(), chargeSheet.getMemberId(), queryMemberInfoFunc);

        //处理部分还款时，对于生成可退支付列表这里，要过滤掉，因为还没还完，不能进行退费
        List<ChargeTransaction> chargeTransactions = transactionAndActionDto.getChargeTransactions();
        if (chargeSheet.getOwedStatus() == ChargeSheet.OwedStatus.OWING) {
            chargeTransactions = chargeTransactions.stream()
                    .filter(chargeTransaction -> chargeTransaction.getChargeType() != ChargeTransaction.ChargeType.OWE)
                    .collect(Collectors.toList());
        }

        List<PaymentSummaryInfo> paymentSummaryInfos = generatePaymentSummaryInfos(chargeTransactions, transactionAndActionDto.getChargeActions(), availablePatientCardIds, memberIsAvailable, infoProvider);

        //处理医保支付与个账POS支付之间先后退费的关系，退费限制先退医保，再退个账pos
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(paymentSummaryInfos)) {
            PaymentSummaryInfo shebaoPaymentSummaryInfo = paymentSummaryInfos.stream()
                    .filter(paymentSummaryInfo -> paymentSummaryInfo.getPayMode() == Constants.ChargePayMode.HEALTH_CARD
                            && paymentSummaryInfo.getIsCanRefund() == 1
                    )
                    .findFirst()
                    .orElse(null);

            if (Objects.nonNull(shebaoPaymentSummaryInfo)) {
                paymentSummaryInfos.stream()
                        .filter(paymentSummaryInfo -> paymentSummaryInfo.getPayMode() == Constants.ChargePayMode.SHEBAO_QINGDAO_UNION_POS_PAY
                                && paymentSummaryInfo.getIsCanRefund() == 1
                        )
                        .forEach(paymentSummaryInfo -> {
                            paymentSummaryInfo.setIsCanRefund(0);
                            paymentSummaryInfo.setDisableRefundCode(PaymentSummaryInfo.DisableRefundCode.REFUND_AFTER_SHEBAO);
                        });
            }

        }
        return paymentSummaryInfos;

    }

    private static List<String> queryAvailablePatientCardIds(List<ChargeTransaction> chargeTransactions, Function<List<String>, List<PatientCardView>> queryPatientCardViewFunc) {

        if (org.apache.commons.collections.CollectionUtils.isEmpty(chargeTransactions)) {
            return new ArrayList<>();
        }

        List<String> promotionCardIds = chargeTransactions.stream()
                .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                .filter(chargeTransaction -> MathUtils.wrapBigDecimalCompare(chargeTransaction.getAmount(), BigDecimal.ZERO) > 0)
                .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.PROMOTION_CARD)
                .filter(chargeTransaction -> org.apache.commons.lang3.StringUtils.isNotEmpty(chargeTransaction.getThirdPartyPayCardId()))
                .map(ChargeTransaction::getThirdPartyPayCardId)
                .distinct()
                .collect(Collectors.toList());

        if (org.apache.commons.collections.CollectionUtils.isEmpty(promotionCardIds)) {
            return new ArrayList<>();
        }

        return Optional.ofNullable(queryPatientCardViewFunc)
                .map(func -> func.apply(promotionCardIds))
                .orElse(new ArrayList<>())
                .stream()
                .filter(patientCardView -> patientCardView.isAvailable())
                .map(PatientCardView::getId)
                .distinct()
                .collect(Collectors.toList());
    }

    private static boolean queryMemberIsAvailable(List<ChargeTransaction> chargeTransactions, String chargeSheetMemberId, Function<String, MemberInfo> queryMemberInfoFunc) {

        if (org.apache.commons.collections.CollectionUtils.isEmpty(chargeTransactions)) {
            return false;
        }

        String memberId = null;
        boolean paidByMemberCard = chargeTransactions.stream()
                .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                .filter(chargeTransaction -> MathUtils.wrapBigDecimalCompare(chargeTransaction.getAmount(), BigDecimal.ZERO) > 0)
                .anyMatch(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.MEMBER_CARD);

        if (paidByMemberCard) {
            String transactionMemberId = chargeTransactions.stream()
                    .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0
                            && MathUtils.wrapBigDecimalCompare(chargeTransaction.getAmount(), BigDecimal.ZERO) > 0
                            && chargeTransaction.getPayMode() == Constants.ChargePayMode.MEMBER_CARD
                            && org.apache.commons.lang3.StringUtils.isNotEmpty(chargeTransaction.getThirdPartyPayCardId())
                    )
                    .findFirst()
                    .map(ChargeTransaction::getThirdPartyPayCardId)
                    .orElse(null);
            memberId = org.apache.commons.lang3.StringUtils.isNotEmpty(transactionMemberId) ? transactionMemberId : chargeSheetMemberId;
        }

        MemberInfo memberInfo = null;
        try {
            String finalMemberId = memberId;
            memberInfo = Optional.ofNullable(queryMemberInfoFunc)
                    .map(func -> func.apply(finalMemberId))
                    .orElse(null);
        } catch (Exception e) {
            sLogger.error("findMemberInfo error, msg: {}", e.getMessage());
        }
        return Objects.nonNull(memberInfo);
    }

    public static ChargeSheetTraceCodesRsp toPharmacyChargeSheetTraceCodesRsp(PharmacySheetProcessor sheetProcessor) {
        ChargeSheetTraceCodesRsp chargeSheetTraceCodesRsp = ChargeSheetTraceCodesRsp.fromChargeSheet(sheetProcessor.chargeSheet);
        if (Objects.isNull(chargeSheetTraceCodesRsp)) {
            return null;
        }
        return chargeSheetTraceCodesRsp
                .setChargeForms(
                        Optional.ofNullable(sheetProcessor.formProcessors)
                                .orElse(Lists.newArrayList())
                                .stream()
                                .map(ChargeFormProtocol::generatePharmacyChargeFormTraceCodes)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList())
                );
    }

    @Data
    @Accessors(chain = true)
    public static class ChargeTransactionAndActionDto {

        private List<ChargeTransaction> chargeTransactions = new ArrayList<>();
        private List<ChargeAction> chargeActions = new ArrayList<>();
        private List<ChargeAction> changePayModeActions = new ArrayList<>();
        private ChargeOweSheet lastOweSheet;

    }

    public static ChargeTransactionAndActionDto getChargeTransactionAndChargeActionContainOwe(ChargeSheet chargeSheet, List<ChargeOweSheet> chargeOweSheets) {
        List<ChargeTransaction> memoryChargeTransactions = Optional.ofNullable(chargeSheet.getChargeTransactions()).orElse(new ArrayList<>())
                .stream()
                .map(chargeTransaction -> {
                    ChargeTransaction memoryChargeTransaction = new ChargeTransaction();
                    BeanUtils.copyProperties(chargeTransaction, memoryChargeTransaction);
                    if (memoryChargeTransaction.getHospitalSheetId() != null) {
                        memoryChargeTransaction.setChargeType(ChargeTransaction.ChargeType.HOSPITAL);
                    }
                    return memoryChargeTransaction;
                })
                .collect(Collectors.toList());
        List<ChargeAction> memoryChargeActions = Optional.ofNullable(chargeSheet.getChargeActions()).orElse(new ArrayList<>())
                .stream()
                .filter(chargeAction -> chargeAction.getType() != ChargeAction.Type.CHANGE_PAY_MODE)
                .map(chargeAction -> {
                    ChargeAction memoryChargeAction = new ChargeAction();
                    BeanUtils.copyProperties(chargeAction, memoryChargeAction);
                    return memoryChargeAction;
                })
                .collect(Collectors.toList());

        List<ChargeAction> changePayModeActions = Optional.ofNullable(chargeSheet.getChargeActions()).orElse(new ArrayList<>())
                .stream()
                .filter(chargeAction -> chargeAction.getType() == ChargeAction.Type.CHANGE_PAY_MODE)
                .map(chargeAction -> {
                    ChargeAction memoryChargeAction = new ChargeAction();
                    BeanUtils.copyProperties(chargeAction, memoryChargeAction);
                    return memoryChargeAction;
                })
                .collect(Collectors.toList());

        ChargeOweSheet lastOweSheet = null;
        if (chargeSheet.getHospitalSheetId() == null && org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeOweSheets)) {
            lastOweSheet = chargeOweSheets.stream()
                    .sorted(Comparator.comparing(ChargeOweSheet::getCreated).reversed())
                    .filter(chargeOweSheet -> chargeOweSheet.getIsOldRecord() == 0)
                    .findFirst()
                    .orElse(null);

            if (chargeSheet.getOwedStatus() == ChargeSheet.OwedStatus.NO_OWED) {

                //已还
                List<ChargeTransaction> oweChargeTransactions = new ArrayList<>();
                if (lastOweSheet != null && !CollectionUtils.isEmpty(lastOweSheet.getTransactionRecords())) {
                    oweChargeTransactions = lastOweSheet.getTransactionRecords()
                            .stream()
                            .filter(record -> record.getType() == ChargeCombineOrderTransaction.Type.PAY)
                            .map(ChargeSheetFeeProtocol::convertToChargeTransaction)
                            .collect(Collectors.toList());
                }

                List<ChargeAction> oweChargeActions = chargeOweSheets.stream()
                        .filter(chargeOweSheet -> org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeOweSheet.getTransactionRecords()))
                        .map(ChargeOweSheet::getTransactionRecords)
                        .flatMap(Collection::stream)
                        .filter(transactionRecord -> transactionRecord.getType() == ChargeCombineOrderTransaction.Type.PAY)
                        .map(ChargeSheetFeeProtocol::convertToChargeAction)
                        .collect(Collectors.toList());

                memoryChargeTransactions.removeIf(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.OWE_PAY);
                memoryChargeTransactions.addAll(oweChargeTransactions);
                memoryChargeActions.addAll(oweChargeActions);
            } else if (chargeSheet.getOwedStatus() == ChargeSheet.OwedStatus.OWING) {
                //待还
                List<ChargeAction> oweChargeActions = chargeOweSheets.stream()
                        .filter(chargeOweSheet -> org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeOweSheet.getTransactionRecords()))
                        .map(ChargeOweSheet::getTransactionRecords)
                        .flatMap(Collection::stream)
                        .filter(record -> record.getType() == ChargeCombineOrderTransaction.Type.PAY)
                        .map(ChargeSheetFeeProtocol::convertToChargeAction)
                        .collect(Collectors.toList());

                //当前已还的金额
                BigDecimal currentRepaymentFee = Optional.ofNullable(lastOweSheet)
                        .map(ChargeOweSheet::getTransactionRecords)
                        .orElse(new ArrayList<>())
                        .stream()
                        .filter(record -> record.getType() == ChargeCombineOrderTransaction.Type.PAY)
                        .map(record -> record.getAmount())
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                List<ChargeTransaction> oweChargeTransactions = Optional.ofNullable(lastOweSheet)
                        .map(chargeOweSheet -> chargeOweSheet.getTransactionRecords())
                        .orElse(new ArrayList<>())
                        .stream()
                        .filter(record -> record.getType() == ChargeCombineOrderTransaction.Type.PAY)
                        .map(ChargeSheetFeeProtocol::convertToChargeTransaction)
                        .collect(Collectors.toList());

                if (MathUtils.wrapBigDecimalCompare(currentRepaymentFee, BigDecimal.ZERO) > 0) {
                    memoryChargeTransactions.stream()
                            .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.OWE_PAY)
                            .findFirst()
                            .ifPresent(chargeTransaction -> chargeTransaction.setAmount(MathUtils.wrapBigDecimalSubtract(chargeTransaction.getAmount(), currentRepaymentFee)));

                }

                memoryChargeTransactions.addAll(oweChargeTransactions);
                memoryChargeActions.addAll(oweChargeActions);
            }
        }

        //排个序
        memoryChargeTransactions = memoryChargeTransactions.stream().sorted(Comparator.comparing(ChargeTransaction::getCreated)).collect(Collectors.toList());
        memoryChargeActions = memoryChargeActions.stream().sorted(Comparator.comparing(ChargeAction::getCreated)).collect(Collectors.toList());

        return new ChargeTransactionAndActionDto()
                .setChargeTransactions(memoryChargeTransactions)
                .setChargeActions(memoryChargeActions)
                .setChangePayModeActions(changePayModeActions)
                .setLastOweSheet(lastOweSheet);
    }

    /**
     * @param chargeTransactions
     * @return
     */
    public static List<PaymentSummaryInfo> generatePaymentSummaryInfos(List<ChargeTransaction> chargeTransactions, List<ChargeAction> chargeActions, List<String> availablePatientCardIds, boolean memberIsAvailable, SheetProcessorInfoProvider infoProvider) {

        if (org.apache.commons.collections.CollectionUtils.isEmpty(chargeTransactions)) {
            return new ArrayList<>();
        }

        Map<Long, Integer> payModeTypes = getPayModeTypes(infoProvider, chargeTransactions.get(0).getChainId());
        Map<String, ChargeAction.PayActionInfo> payModeNameMap = ChargeUtils.generatePayModeNameMap(chargeActions, payModeTypes);
        chargeTransactions.forEach(chargeTransaction -> chargeTransaction.setPayModeType(payModeTypes.getOrDefault(Long.valueOf(chargeTransaction.getPayMode()), Constants.ChargePayModeType.UN_KNOW)));

        Map<String, List<ChargeTransaction>> groupByAmountTransactionsMap = chargeTransactions.stream()
                .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                .collect(Collectors.groupingBy(chargeTransaction -> {
                    if (MathUtils.wrapBigDecimalCompare(chargeTransaction.getAmount(), BigDecimal.ZERO) > 0) {
                        return "notZeroPayTransactions";
                    }
                    if (MathUtils.wrapBigDecimalCompare(chargeTransaction.getAmount(), BigDecimal.ZERO) == 0) {
                        return "zeroPayTransactions";
                    }
                    if (MathUtils.wrapBigDecimalCompare(chargeTransaction.getAmount(), BigDecimal.ZERO) < 0) {
                        return "refundTransactions";
                    }
                    return "";
                }));


        Map<String, PaymentSummaryInfo> notZeroPaymentSummaryInfoMap = groupByAmountTransactionsMap.getOrDefault("notZeroPayTransactions", new ArrayList<>())
                .stream()
                .collect(Collectors.toMap(ChargeUtils::generateTransactionUniqueKey, chargeTransaction -> {
                    PaymentSummaryInfo summaryInfo = new PaymentSummaryInfo();
                    summaryInfo.setThirdPartyPayCardId(chargeTransaction.getThirdPartyPayCardId());
                    summaryInfo.setChargePayTransactionId(chargeTransaction.getId());
                    summaryInfo.setAmount(chargeTransaction.getAmount());
                    summaryInfo.setPayMode(chargeTransaction.getPayMode());
                    summaryInfo.setPaySubMode(chargeTransaction.getPaySubMode());
                    summaryInfo.setPayModeType(chargeTransaction.getPayModeType());
                    summaryInfo.setPresentAmount(MathUtils.wrapBigDecimalOrZero(chargeTransaction.getPresentAmount()));
                    summaryInfo.setPrincipalAmount(MathUtils.wrapBigDecimalOrZero(chargeTransaction.getPrincipalAmount()));
                    if (chargeTransaction.getPayMode() == Constants.ChargePayMode.OUTPATIENT_CENTER_PAY) {
                        summaryInfo.setGroupId(chargeTransaction.getChargeActionId());
                    }
                    if (MathUtils.wrapBigDecimalCompare(summaryInfo.getAmount(), BigDecimal.ZERO) >= 0) {
                        summaryInfo.setPaidAmount(summaryInfo.getAmount());
                        summaryInfo.addTransactionId(chargeTransaction.getId());
                    }
                    return summaryInfo;
                }, (a, b) -> {
                    if (MathUtils.wrapBigDecimalCompare(b.getAmount(), BigDecimal.ZERO) >= 0) {
                        a.setPaidAmount(MathUtils.wrapBigDecimalAdd(a.getPaidAmount(), b.getAmount()));
                        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(b.getTransactionIds())) {
                            b.getTransactionIds().forEach(a::addTransactionId);
                        }
                    }
                    a.setAmount(MathUtils.max(BigDecimal.ZERO, a.getAmount().add(b.getAmount())));
                    a.setPresentAmount(MathUtils.max(BigDecimal.ZERO, MathUtils.wrapBigDecimalAdd(a.getPresentAmount(), b.getPresentAmount())));
                    a.setPrincipalAmount(MathUtils.max(BigDecimal.ZERO, MathUtils.wrapBigDecimalAdd(a.getPrincipalAmount(), b.getPrincipalAmount())));
                    return a;
                }));

        groupByAmountTransactionsMap.getOrDefault("refundTransactions", new ArrayList<>())
                .stream()
                .forEach(chargeTransaction -> {
                    String key = ChargeUtils.generateTransactionUniqueKey(chargeTransaction);
                    PaymentSummaryInfo paymentSummaryInfo = notZeroPaymentSummaryInfoMap.get(key);

                    if (paymentSummaryInfo == null) {
                        return;
                    }

                    paymentSummaryInfo.setRefundedAmount(MathUtils.wrapBigDecimalAdd(paymentSummaryInfo.getRefundedAmount(), chargeTransaction.getAmount()));
                    paymentSummaryInfo.setAmount(MathUtils.max(BigDecimal.ZERO, paymentSummaryInfo.getAmount().add(chargeTransaction.getAmount())));
                    paymentSummaryInfo.setPresentAmount(MathUtils.max(BigDecimal.ZERO, MathUtils.wrapBigDecimalAdd(paymentSummaryInfo.getPresentAmount(), chargeTransaction.getPresentAmount())));
                    paymentSummaryInfo.setPrincipalAmount(MathUtils.max(BigDecimal.ZERO, MathUtils.wrapBigDecimalAdd(paymentSummaryInfo.getPrincipalAmount(), chargeTransaction.getPrincipalAmount())));
                });

        List<PaymentSummaryInfo> paymentSummaryInfos = notZeroPaymentSummaryInfoMap.entrySet()
                .stream()
                .map(entry -> {
                    PaymentSummaryInfo paymentSummaryInfo = entry.getValue();
                    paymentSummaryInfo.setIsCanRefund(MathUtils.wrapBigDecimalCompare(paymentSummaryInfo.getAmount(), BigDecimal.ZERO) > 0 ? 1 : 0);

                    ChargeAction.PayActionInfo payActionInfo = payModeNameMap.getOrDefault(paymentSummaryInfo.generatePayModeKey(entry.getKey()), new ChargeAction.PayActionInfo());
                    paymentSummaryInfo.setPayModeName(payActionInfo.getPayModeName());
                    paymentSummaryInfo.setPaySubModeName(payActionInfo.getPaySubModeName());
                    return paymentSummaryInfo;
                })
                .collect(Collectors.toList());


        //找到收0元且没有被退费的的收费方式
        List<String> payZeroChargeActionIds = chargeActions.stream()
                .filter(chargeAction -> chargeAction.getType() == ChargeAction.Type.PAY || chargeAction.getType() == ChargeAction.Type.REPAYMENT)
                .map(ChargeAction::getId)
                .collect(Collectors.toList());

        List<String> refundChargeActionIds = chargeActions.stream()
                .filter(chargeAction -> chargeAction.getType() == ChargeAction.Type.REFUND)
                .map(ChargeAction::getId)
                .collect(Collectors.toList());

        if (org.apache.commons.collections.CollectionUtils.isEmpty(payZeroChargeActionIds)) {
            return paymentSummaryInfos;
        }

        List<String> refundPayModeKeys = chargeTransactions
                .stream()
                .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                .filter(chargeTransaction -> refundChargeActionIds.contains(chargeTransaction.getChargeActionId()))
                .map(ChargeUtils::generateTransactionUniqueKey)
                .collect(Collectors.toList());

        //已收的0元的支付方式

        List<PaymentSummaryInfo> zeroPaySummaryInfos = groupByAmountTransactionsMap.getOrDefault("zeroPayTransactions", new ArrayList<>())
                .stream()
                .filter(chargeTransaction -> payZeroChargeActionIds.contains(chargeTransaction.getChargeActionId()))
                .collect(Collectors.toMap(ChargeUtils::generateTransactionUniqueKey, chargeTransaction -> {
                    PaymentSummaryInfo summaryInfo = new PaymentSummaryInfo();
                    summaryInfo.setThirdPartyPayCardId(chargeTransaction.getThirdPartyPayCardId());
                    summaryInfo.setChargePayTransactionId(chargeTransaction.getId());
                    summaryInfo.setAmount(chargeTransaction.getAmount());
                    summaryInfo.setPayMode(chargeTransaction.getPayMode());
                    summaryInfo.setPaySubMode(chargeTransaction.getPaySubMode());
                    summaryInfo.setPayModeType(chargeTransaction.getPayModeType());
                    summaryInfo.setPresentAmount(MathUtils.wrapBigDecimalOrZero(chargeTransaction.getPresentAmount()));
                    summaryInfo.setPrincipalAmount(MathUtils.wrapBigDecimalOrZero(chargeTransaction.getPrincipalAmount()));
                    summaryInfo.setPaidAmount(summaryInfo.getAmount());
                    summaryInfo.addTransactionId(chargeTransaction.getId());
                    summaryInfo.setIsCanRefund(1);
                    return summaryInfo;
                }, (a, b) -> {
                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(b.getTransactionIds())) {
                        b.getTransactionIds().forEach(a::addTransactionId);
                    }
                    return a;
                }))
                .entrySet()
                .stream()
                .filter(entry -> !notZeroPaymentSummaryInfoMap.keySet().contains(entry.getKey()))
                .map(entry -> {
                    PaymentSummaryInfo paymentSummaryInfo = entry.getValue();
                    paymentSummaryInfo.setIsCanRefund(!refundPayModeKeys.contains(entry.getKey()) ? 1 : 0);
                    paymentSummaryInfo.setDisableRefundCode(paymentSummaryInfo.getIsCanRefund() == 0 ? PaymentSummaryInfo.DisableRefundCode.IS_REFUNDED : null);

                    ChargeAction.PayActionInfo payActionInfo = payModeNameMap.getOrDefault(paymentSummaryInfo.generatePayModeKey(entry.getKey()), new ChargeAction.PayActionInfo());
                    paymentSummaryInfo.setPayModeName(payActionInfo.getPayModeName());
                    paymentSummaryInfo.setPaySubModeName(payActionInfo.getPaySubModeName());
                    return paymentSummaryInfo;
                })
                .collect(Collectors.toList());

        paymentSummaryInfos.addAll(zeroPaySummaryInfos);

        //标记卡项如果不能退了，就标记为不可退
        paymentSummaryInfos.stream()
                .filter(paymentSummaryInfo -> paymentSummaryInfo.getIsCanRefund() == 1)
                .forEach(paymentSummaryInfo -> {
                    if (paymentSummaryInfo.getPayMode() == Constants.ChargePayMode.PROMOTION_CARD && org.apache.commons.lang3.StringUtils.isNotEmpty(paymentSummaryInfo.getThirdPartyPayCardId())) {
                        if (!availablePatientCardIds.contains(paymentSummaryInfo.getThirdPartyPayCardId())) {
                            paymentSummaryInfo.setIsCanRefund(0);
                            paymentSummaryInfo.setDisableRefundCode(PaymentSummaryInfo.DisableRefundCode.PROMOTION_CARD_NOT_AVAILABLE);
                        }
                    }
                    if (!memberIsAvailable && paymentSummaryInfo.getPayMode() == Constants.ChargePayMode.MEMBER_CARD) {
                        paymentSummaryInfo.setIsCanRefund(0);
                        paymentSummaryInfo.setDisableRefundCode(PaymentSummaryInfo.DisableRefundCode.MEMBER_CARD_NOT_AVAILABLE);
                    }
                });

        return paymentSummaryInfos;
    }


    public static ChargeSheetView toChargeSheetView(SheetProcessor sheetProcessor, boolean isCopyByOutpatient, boolean isContainGiftGoods, int source, boolean withAllTransaction) {
        // 初始化
        ChargeSheet chargeSheet = sheetProcessor.chargeSheet;
        SheetProcessorInfoProvider sheetProcessorInfoProvider = sheetProcessor.sheetProcessorInfoProvider;
        PatientInfo patientInfo = sheetProcessor.getPatientInfo();
        List<FormProcessor> formProcessors = sheetProcessor.formProcessors;
        MemberInfo memberInfo = sheetProcessor.memberInfo;
        List<PromotionView> availablePromotionViews = sheetProcessor.availablePromotionViews;
        List<GiftRulePromotionView> availableGiftRulePromotionViews = sheetProcessor.availableGiftRulePromotionViews;
        List<CouponPromotionView> availableCouponPromotionViews = sheetProcessor.availableCouponPromotionViews;
        List<PatientCardPromotionView> availablePatientCardPromotionViews = sheetProcessor.availablePatientCardPromotionViews;
        List<VerifyInfoView> availableChargeVerifyInfos = sheetProcessor.availableChargeVerifyInfos;
        List<PatientPointDeductProductPromotionView> availablePatientPointDeductProductPromotionViews = sheetProcessor.availablePatientPointDeductProductPromotionViews;
        MedicalRecord medicalRecord = sheetProcessor.medicalRecord;
        PatientOrder patientOrder = sheetProcessor.patientOrder;
        int dispensingStatus = sheetProcessor.dispensingStatus;
        Instant dispensedTime = sheetProcessor.dispensedTime;
        String memberId = sheetProcessor.memberId;
        String dispensedBy = sheetProcessor.dispensedBy;
        PatientPointsInfoView patientPointsInfoView = sheetProcessor.patientPointsInfoView;
        ChargeConfigDetailView chargeConfigDetail = sheetProcessor.chargeConfigDetail;

        // 查询收费单对应的欠费数据
        List<ChargeOweSheet> chargeOweSheets = new ArrayList<>();
        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED) {
            chargeOweSheets = Optional.ofNullable(sheetProcessorInfoProvider).map(SheetProcessorInfoProvider::getChargeOweSheetProvider)
                    .map(chargeOweSheetProvider -> chargeOweSheetProvider.findChargeOweSheetsByChargeSheetId(chargeSheet.getClinicId(), chargeSheet.getId())).orElse(new ArrayList<>());
        }
        sheetProcessor.setChargeOweSheets(chargeOweSheets);

        ChargeSheetView chargeSheetView = new ChargeSheetView();
        ChargeSheetAdditional chargeSheetAdditional = chargeSheet.getAdditional();
        if (chargeSheetAdditional != null) {
            chargeSheetAdditional.setCloneChargeSheetSnapshot(JsonUtils.readValue(chargeSheetAdditional.getCloneChargeSheetSnapshotJson(), CloneChargeSheetSnapshot.class));
            BeanUtils.copyProperties(chargeSheetAdditional, chargeSheetView);
            if (Objects.nonNull(chargeSheetAdditional.getExtendedInfo())) {
                BeanUtils.copyProperties(chargeSheetAdditional.getExtendedInfo(), chargeSheetView);
            }
        }
        BeanUtils.copyProperties(chargeSheet, chargeSheetView, "chargeForms");
        // 零售转录时处理把transcribeDoctorId赋值给doctorId
        if (ChargeUtils.isCanUpdateChiefComplaint(chargeSheet.getType(), chargeSheet.getClonePrescriptionType())) {
            if (!(chargeSheet.getType() == ChargeSheet.Type.OUTPATIENT && chargeSheet.getClonePrescriptionType() == ChargeSheetAdditional.ClonePrescriptionType.FROM_PHOTO_BY_OUTPATIENT_DOCTOR)) {
                chargeSheetView.setDoctorId(chargeSheetView.getTranscribeDoctorId());
            }
        }
        if (chargeSheetView.getIsDraft() == Constants.ChargeSheetDraftStatus.IS_DRAFT
                && patientInfo != null
                && Constants.ANONYMOUS_PATIENT_ID.equals(patientInfo.getId())) {
            chargeSheetView.setPatientId(null);
            patientInfo.setId(null);
        }
        chargeSheetView.setPatient(patientInfo);
        chargeSheetView.setAirPharmacyOrderId(Optional.ofNullable(patientOrder).map(PatientOrder::getAirPharmacyOrderId).orElse(null));
        chargeSheetView.setPatientOrderNo(Objects.nonNull(patientOrder) ? String.valueOf(patientOrder.getNo()) : "");
        chargeSheetView.setChargeSheetSummary(ChargeSheetFeeProtocol.toSummaryView(sheetProcessor, chargeOweSheets));
        chargeSheetView.setPrintable(generatePrintable(chargeSheet, patientOrder, () -> Optional.ofNullable(sheetProcessor.getSheetProcessorInfoProvider())
                .map(SheetProcessorInfoProvider::getPropertyProvider)
                .map(propertyProvider -> propertyProvider.getPrintMedicalDocumentsInfusionAndTreatmentContent(chargeSheetView.getClinicId()))
                .orElse(null)
        ));
//        chargeSheetView.setDataSignature(ChargeUtils.sign(chargeSheet));
        List<ChargeFormView> chargeFormViews = formProcessors.stream()
                .map(formProcessor -> {
                    ChargeFormView chargeFormView = ChargeFormProtocol.generateChargeFormView(formProcessor, isContainGiftGoods, chargeSheet.getStatus());
                    if (Objects.nonNull(chargeFormView) && Objects.nonNull(chargeConfigDetail)
                            && chargeConfigDetail.getRefundRestriction() != ChargeCalculateConfig.RefundRestriction.DISPENSE
                            && !CollectionUtils.isEmpty(formProcessor.getDispensingFormInfoList())) {
                        chargeFormView.setAuditOrCompoundStatus(YesOrNo.NO);
                        boolean auditCompound = formProcessor.checkDispensingFormAuditCompound(chargeConfigDetail, false, false);
                        if (auditCompound) {
                            chargeFormView.setAuditOrCompoundStatus(YesOrNo.YES);
                        }
                    }
                    return chargeFormView;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(chargeFormViews)) {
            chargeFormViews = new ArrayList<>();
        }
        if (chargeSheet.getDeliveryType() == ChargeSheet.DeliveryType.DELIVERY_TO_HOME && chargeSheet.getDeliveryInfo() != null) {
            ChargeFormView deliveryChargeFormView = generateDeliveryChargeFormView(chargeSheet, chargeFormViews);
            if (deliveryChargeFormView != null) {
                chargeFormViews.add(deliveryChargeFormView);
            }
        }
//        if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED && chargeSheet.getIsDecoction() == 1) {
//            ChargeFormView decoctionChargeFormView = generateDecoctionChargeFormView(chargeSheet, chargeFormViews);
//            if (decoctionChargeFormView != null) {
//                chargeFormViews.add(decoctionChargeFormView);
//            }
//        }
        chargeSheetView.setChargeForms(chargeFormViews);
        sortChargeFormViews(chargeSheetView);
        chargeSheetView.setStatusName(StatusNameTranslator.translateChargeSheetStatus(chargeSheetView.getOwedStatus(), chargeSheetView.getStatus(), chargeSheetView.getIsDraft() == 1, chargeSheetView.getOutpatientStatus(), chargeSheetView.getIsClosed()));
        chargeSheetView.setMemberDiscountInfo(JsonUtils.loadAsJsonNode(chargeSheet.getMemberDiscountInfoJson()));
        chargeSheetView.setMemberInfo(memberInfo);
        if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED) {
            chargeSheetView.setPatientPointsInfo(patientPointsInfoView);
            chargeSheetView.setPromotions(availablePromotionViews);
            chargeSheetView.setPatientCardPromotions(availablePatientCardPromotionViews);
            chargeSheetView.setPatientPointDeductProductPromotions(availablePatientPointDeductProductPromotionViews);
            chargeSheetView.setVerifyInfoViews(availableChargeVerifyInfos);
            if (!CollectionUtils.isEmpty(availableCouponPromotionViews)) {
                chargeSheetView.setCouponPromotions(availableCouponPromotionViews.stream().sorted(Comparator.comparingInt(CouponPromotionView::getIsCanBeUsed).reversed()).collect(Collectors.toList()));
            } else {
                chargeSheetView.setCouponPromotions(new ArrayList<>());
            }
            if (!CollectionUtils.isEmpty(availableGiftRulePromotionViews)) {
                chargeSheetView.setGiftRulePromotions(availableGiftRulePromotionViews.stream().sorted(Comparator.comparingInt(GiftRulePromotionView::getIsCanBeUsed).reversed()).collect(Collectors.toList()));
            } else {
                chargeSheetView.setGiftRulePromotions(new ArrayList<>());
            }
        } else {
            chargeSheetView.setPatientPointsInfo(DTOConverter.convertToPatientPointsInfoView(chargeSheet.getPatientPointsPromotionInfo()));
            SheetPromotionInfo sheetPromotionInfo = JsonUtils.readValue(chargeSheet.getPromotionInfoJson(), SheetPromotionInfo.class);
            chargeSheetView.setPromotions(sheetPromotionInfo != null ? sheetPromotionInfo.getPromotions() : null);
            List<CouponPromotionView> couponPromotionViews = null;
            if (!CollectionUtils.isEmpty(chargeSheet.getCouponPromotionInfos())) {
                couponPromotionViews = chargeSheet.getCouponPromotionInfos().stream()
                        .filter(chargeCouponPromotionInfo -> chargeCouponPromotionInfo.getIsDeleted() == 0)
                        .map(DTOConverter::convertToCouponPromotionView)
                        .filter(CouponPromotionView::getChecked)
                        .collect(Collectors.toList());
            }
            chargeSheetView.setCouponPromotions(couponPromotionViews);

            List<GiftRulePromotionView> giftRulePromotionViews = null;
            if (!CollectionUtils.isEmpty(chargeSheet.getGiftRulePromotionInfos())) {
                giftRulePromotionViews = chargeSheet.getGiftRulePromotionInfos().stream()
                        .filter(giftRulePromotionInfo -> giftRulePromotionInfo.getIsDeleted() == 0)
                        .map(DTOConverter::convertToGiftRulePromotionView)
                        .filter(GiftRulePromotionView::getChecked)
                        .collect(Collectors.toList());
            }
            chargeSheetView.setGiftRulePromotions(giftRulePromotionViews);
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeSheet.getPatientCardPromotionInfos())) {
                chargeSheetView.setPatientCardPromotions(chargeSheet.getPatientCardPromotionInfos()
                        .stream()
                        .filter(patientCardPromotionInfo -> patientCardPromotionInfo.getIsDeleted() == 0)
                        .map(PatientCardPromotionView::ofPatientCardPromotionView)
                        .collect(Collectors.toList())
                );
            }
            chargeSheetView.setPatientPointDeductProductPromotions(new ArrayList<>(Optional.ofNullable(chargeSheet.getPatientPointsDeductProductPromotionInfo())
                    .filter(patientPointsDeductProductPromotionInfo -> patientPointsDeductProductPromotionInfo.getIsDeleted() == 0)
                    .map(ChargePatientPointsDeductProductPromotionInfo::getDeductItems)
                    .orElse(new ArrayList<>()))
            );

            if (!CollectionUtils.isEmpty(chargeSheet.getChargeVerifyInfos())) {
                chargeSheetView.setVerifyInfoViews(chargeSheet.getChargeVerifyInfos().stream()
                        .filter(chargeVerifyInfo -> chargeVerifyInfo.getIsDeleted() == 0)
                        .map(VerifyInfoView::ofChargeVerifyInfo).collect(Collectors.toList()));

            }

        }
        chargeSheetView.setMedicalRecord(medicalRecord);
        chargeSheetView.setDispensingStatus(dispensingStatus);
        if (chargeSheet.getAdditional() != null) {
            chargeSheetView.setSelfPayStatus(chargeSheet.getAdditional().getSelfPayStatus());
        }
        chargeSheetView.setDispensedTime(dispensedTime);
        chargeSheetView.setMemberId(memberId);
        chargeSheetView.setChargeTransactions(generateChargeTransactionViews(chargeSheet, chargeOweSheets, true, withAllTransaction, sheetProcessorInfoProvider));
        chargeSheetView.setChargeActions(generateChargeAction(chargeSheet, chargeOweSheets));
        chargeSheetView.setChangePayModeRecords(ChargeSheetFeeProtocol.generateChangePayModeRecords(chargeSheet.getChangePayModeRecords(), chargeSheet.getChargeActions()));
        chargeSheetView.setShebaoSettlePrintSheetId(generateShebaoSettlePrintSheetId(chargeSheetView, chargeOweSheets));
        if (patientOrder != null) {
            chargeSheetView.setSource(patientOrder.getSource());
            if (!isCopyByOutpatient) {
                chargeSheetView.setShebaoCardInfo(patientOrder.getShebaoCardInfo());
            }
        }
        chargeSheetView.setDeliveryInfo(ChargeDeliveryInfoView.ofChargeDeliveryInfoView(chargeSheet.getDeliveryInfo()));
        if (chargeSheet.getIsDecoction() == 1) {
            chargeSheetView.setDecoctionInfo(generateDecoctionInfoView(chargeSheet));
        }
        if (chargeSheetView.getPrintable() != null) {// 重置是否可打印退药单
            chargeSheetView.getPrintable().setUndispensingSheet(chargeSheetView.getHasUndispense());
        }
        fillEmployeeName(chargeSheetView, chargeSheet, dispensedBy, sheetProcessorInfoProvider);
        // 绑定空中药房form的支付状态
        bindFormAiyPharmacyPayStatus(chargeSheetView, sheetProcessorInfoProvider, source);
        // 绑定空中药房加工名字
        fillAirMedicineStateScopeName(chargeSheetView, sheetProcessorInfoProvider);
        // 微诊所和设备机处理加工规则删除了的情况
        removeProcessFormByDisabledProcessRule(chargeSheetView, sheetProcessorInfoProvider, source);
        // 填充加工费的name
        fillProcessInfoName(chargeSheetView, sheetProcessorInfoProvider);
        // 锁单状态字段填充
        fillLockStatusField(chargeSheetView, sheetProcessorInfoProvider.getChargePayProvider(), chargeSheet, source);
        return chargeSheetView;
    }

    public static ChargeSheetView toPharmacyChargeSheetView(PharmacySheetProcessor sheetProcessor, boolean isCopyByOutpatient, boolean isContainGiftGoods, int source, boolean withAllTransaction) {
        //初始化
        ChargeSheet chargeSheet = sheetProcessor.chargeSheet;
        SheetProcessorInfoProvider sheetProcessorInfoProvider = sheetProcessor.sheetProcessorInfoProvider;
        PatientInfo patientInfo = sheetProcessor.getPatientInfo();
        List<PharmacyFormProcessor> formProcessors = sheetProcessor.formProcessors;
        MemberInfo memberInfo = sheetProcessor.memberInfo;
        List<PromotionView> availablePromotionViews = sheetProcessor.availablePromotionViews;
        List<GiftRulePromotionView> availableGiftRulePromotionViews = sheetProcessor.availableGiftRulePromotionViews;
        List<CouponPromotionView> availableCouponPromotionViews = sheetProcessor.availableCouponPromotionViews;
        List<PatientCardPromotionView> availablePatientCardPromotionViews = sheetProcessor.availablePatientCardPromotionViews;
        List<PatientPointDeductProductPromotionView> availablePatientPointDeductProductPromotionViews = sheetProcessor.availablePatientPointDeductProductPromotionViews;
        MedicalRecord medicalRecord = sheetProcessor.medicalRecord;
        PatientOrder patientOrder = sheetProcessor.patientOrder;
        int dispensingStatus = sheetProcessor.dispensingStatus;
        Instant dispensedTime = sheetProcessor.dispensedTime;
        String memberId = sheetProcessor.memberId;
        String dispensedBy = sheetProcessor.dispensedBy;
        PatientPointsInfoView patientPointsInfoView = sheetProcessor.patientPointsInfoView;

        //查询收费单对应的欠费数据
        List<ChargeOweSheet> chargeOweSheets = new ArrayList<>();
        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED) {
            chargeOweSheets = Optional.ofNullable(sheetProcessorInfoProvider).map(SheetProcessorInfoProvider::getChargeOweSheetProvider)
                    .map(chargeOweSheetProvider -> chargeOweSheetProvider.findChargeOweSheetsByChargeSheetId(chargeSheet.getClinicId(), chargeSheet.getId())).orElse(new ArrayList<>());
        }
        sheetProcessor.setChargeOweSheets(chargeOweSheets);

        ChargeSheetView chargeSheetView = new ChargeSheetView();
        ChargeSheetAdditional chargeSheetAdditional = chargeSheet.getAdditional();
        if (chargeSheetAdditional != null) {
            chargeSheetAdditional.setCloneChargeSheetSnapshot(JsonUtils.readValue(chargeSheetAdditional.getCloneChargeSheetSnapshotJson(), CloneChargeSheetSnapshot.class));
            BeanUtils.copyProperties(chargeSheetAdditional, chargeSheetView);
            if (chargeSheetAdditional.getExtendedInfo() != null) {
                BeanUtils.copyProperties(chargeSheetAdditional.getExtendedInfo(), chargeSheetView);
            }

        }
        BeanUtils.copyProperties(chargeSheet, chargeSheetView, "chargeForms");

        //零售转录时处理把transcribeDoctorId赋值给doctorId
        if (ChargeUtils.isCanUpdateChiefComplaint(chargeSheet.getType(), chargeSheet.getClonePrescriptionType())) {
            if (!(chargeSheet.getType() == ChargeSheet.Type.OUTPATIENT && chargeSheet.getClonePrescriptionType() == ChargeSheetAdditional.ClonePrescriptionType.FROM_PHOTO_BY_OUTPATIENT_DOCTOR)) {
                chargeSheetView.setDoctorId(chargeSheetView.getTranscribeDoctorId());
            }
        }

        if (chargeSheetView.getIsDraft() == Constants.ChargeSheetDraftStatus.IS_DRAFT
                && patientInfo != null
                && Constants.ANONYMOUS_PATIENT_ID.equals(patientInfo.getId())) {
            chargeSheetView.setPatientId(null);
            patientInfo.setId(null);
        }
        chargeSheetView.setPatient(patientInfo);
        chargeSheetView.setAirPharmacyOrderId(Optional.ofNullable(patientOrder).map(PatientOrder::getAirPharmacyOrderId).orElse(null));
        chargeSheetView.setPatientOrderNo(Objects.nonNull(patientOrder) ? String.valueOf(patientOrder.getNo()) : "");
        chargeSheetView.setChargeSheetSummary(ChargeSheetFeeProtocol.toPharmacySummaryView(sheetProcessor, chargeOweSheets));
        chargeSheetView.setPrintable(generatePharmacyPrintable(chargeSheet, patientOrder, sheetProcessor));
//        chargeSheetView.setDataSignature(ChargeUtils.sign(chargeSheet));
        List<ChargeFormView> chargeFormViews = formProcessors.stream().map(formProcessor -> ChargeFormProtocol.generatePharmacyChargeFormView(formProcessor, isContainGiftGoods, chargeSheet.getStatus())).filter(Objects::nonNull).collect(Collectors.toList());
        if (chargeFormViews == null) {
            chargeFormViews = new ArrayList<>();
        }

        if (chargeSheet.getDeliveryType() == ChargeSheet.DeliveryType.DELIVERY_TO_HOME && chargeSheet.getDeliveryInfo() != null) {
            ChargeFormView deliveryChargeFormView = generateDeliveryChargeFormView(chargeSheet, chargeFormViews);
            if (deliveryChargeFormView != null) {
                chargeFormViews.add(deliveryChargeFormView);
            }
        }

//        if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED && chargeSheet.getIsDecoction() == 1) {
//            ChargeFormView decoctionChargeFormView = generateDecoctionChargeFormView(chargeSheet, chargeFormViews);
//            if (decoctionChargeFormView != null) {
//                chargeFormViews.add(decoctionChargeFormView);
//            }
//        }

        chargeSheetView.setChargeForms(chargeFormViews);
        sortChargeFormViews(chargeSheetView);
        chargeSheetView.setStatusName(StatusNameTranslator.translateChargeSheetStatus(chargeSheetView.getOwedStatus(), chargeSheetView.getStatus(), chargeSheetView.getIsDraft() == 1, chargeSheetView.getOutpatientStatus(), chargeSheetView.getIsClosed()));
        chargeSheetView.setMemberDiscountInfo(JsonUtils.loadAsJsonNode(chargeSheet.getMemberDiscountInfoJson()));
        chargeSheetView.setMemberInfo(memberInfo);
        if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED) {
            chargeSheetView.setPatientPointsInfo(patientPointsInfoView);
            chargeSheetView.setPromotions(availablePromotionViews);
            chargeSheetView.setPatientCardPromotions(availablePatientCardPromotionViews);
            chargeSheetView.setPatientPointDeductProductPromotions(availablePatientPointDeductProductPromotionViews);
            if (!CollectionUtils.isEmpty(availableCouponPromotionViews)) {
                chargeSheetView.setCouponPromotions(availableCouponPromotionViews.stream().sorted(Comparator.comparingInt(CouponPromotionView::getIsCanBeUsed).reversed()).collect(Collectors.toList()));
            } else {
                chargeSheetView.setCouponPromotions(new ArrayList<>());
            }

            if (!CollectionUtils.isEmpty(availableGiftRulePromotionViews)) {
                chargeSheetView.setGiftRulePromotions(availableGiftRulePromotionViews.stream().sorted(Comparator.comparingInt(GiftRulePromotionView::getIsCanBeUsed).reversed()).collect(Collectors.toList()));
            } else {
                chargeSheetView.setGiftRulePromotions(new ArrayList<>());
            }
        } else {

            chargeSheetView.setPatientPointsInfo(DTOConverter.convertToPatientPointsInfoView(chargeSheet.getPatientPointsPromotionInfo()));

            SheetPromotionInfo sheetPromotionInfo = JsonUtils.readValue(chargeSheet.getPromotionInfoJson(), SheetPromotionInfo.class);
            chargeSheetView.setPromotions(sheetPromotionInfo != null ? sheetPromotionInfo.getPromotions() : null);

            List<CouponPromotionView> couponPromotionViews = null;
            if (!CollectionUtils.isEmpty(chargeSheet.getCouponPromotionInfos())) {
                couponPromotionViews = chargeSheet.getCouponPromotionInfos().stream()
                        .filter(chargeCouponPromotionInfo -> chargeCouponPromotionInfo.getIsDeleted() == 0)
                        .map(DTOConverter::convertToCouponPromotionView)
                        .filter(CouponPromotionView::getChecked)
                        .collect(Collectors.toList());
            }
            chargeSheetView.setCouponPromotions(couponPromotionViews);

            List<GiftRulePromotionView> giftRulePromotionViews = null;
            if (!CollectionUtils.isEmpty(chargeSheet.getGiftRulePromotionInfos())) {
                giftRulePromotionViews = chargeSheet.getGiftRulePromotionInfos().stream()
                        .filter(giftRulePromotionInfo -> giftRulePromotionInfo.getIsDeleted() == 0)
                        .map(DTOConverter::convertToGiftRulePromotionView)
                        .filter(GiftRulePromotionView::getChecked)
                        .collect(Collectors.toList());
            }
            chargeSheetView.setGiftRulePromotions(giftRulePromotionViews);

            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeSheet.getPatientCardPromotionInfos())) {
                chargeSheetView.setPatientCardPromotions(chargeSheet.getPatientCardPromotionInfos()
                        .stream()
                        .filter(patientCardPromotionInfo -> patientCardPromotionInfo.getIsDeleted() == 0)
                        .map(PatientCardPromotionView::ofPatientCardPromotionView)
                        .collect(Collectors.toList())
                );
            }

            chargeSheetView.setPatientPointDeductProductPromotions(Optional.ofNullable(chargeSheet.getPatientPointsDeductProductPromotionInfo())
                    .filter(p -> p.getIsDeleted() == 0)
                    .map(ChargePatientPointsDeductProductPromotionInfo::getDeductItems)
                    .orElse(new ArrayList<>())
                    .stream()
                    .collect(Collectors.toList())
            );
        }

        chargeSheetView.setMedicalRecord(medicalRecord);
        chargeSheetView.setDispensingStatus(dispensingStatus);
        if (chargeSheet.getAdditional() != null) {
            chargeSheetView.setSelfPayStatus(chargeSheet.getAdditional().getSelfPayStatus());
        }
        chargeSheetView.setDispensedTime(dispensedTime);
        chargeSheetView.setMemberId(memberId);
        chargeSheetView.setChargeTransactions(generateChargeTransactionViews(chargeSheet, chargeOweSheets, true, withAllTransaction, sheetProcessorInfoProvider));
        chargeSheetView.setChargeActions(generateChargeAction(chargeSheet, chargeOweSheets));
        chargeSheetView.setChangePayModeRecords(ChargeSheetFeeProtocol.generateChangePayModeRecords(chargeSheet.getChangePayModeRecords(), chargeSheet.getChargeActions()));
        chargeSheetView.setShebaoSettlePrintSheetId(generateShebaoSettlePrintSheetId(chargeSheetView, chargeOweSheets));

        if (patientOrder != null) {
            chargeSheetView.setSource(patientOrder.getSource());
            if (!isCopyByOutpatient) {
                chargeSheetView.setShebaoCardInfo(patientOrder.getShebaoCardInfo());
            }
        }

        chargeSheetView.setDeliveryInfo(ChargeDeliveryInfoView.ofChargeDeliveryInfoView(chargeSheet.getDeliveryInfo()));

        if (chargeSheet.getIsDecoction() == 1) {
            chargeSheetView.setDecoctionInfo(generateDecoctionInfoView(chargeSheet));
        }

        fillEmployeeName(chargeSheetView, chargeSheet, dispensedBy, sheetProcessorInfoProvider);

        //绑定空中药房form的支付状态
        bindFormAiyPharmacyPayStatus(chargeSheetView, sheetProcessorInfoProvider, source);

        //绑定空中药房加工名字
        fillAirMedicineStateScopeName(chargeSheetView, sheetProcessorInfoProvider);

        //微诊所和设备机处理加工规则删除了的情况
        removeProcessFormByDisabledProcessRule(chargeSheetView, sheetProcessorInfoProvider, source);

        //填充加工费的name
        fillProcessInfoName(chargeSheetView, sheetProcessorInfoProvider);

        //锁单状态字段填充
        fillLockStatusField(chargeSheetView, sheetProcessorInfoProvider.getChargePayProvider(), chargeSheet, source);

        return chargeSheetView;
    }

    private static void fillEmployeeName(ChargeSheetView chargeSheetView, ChargeSheet chargeSheet, String dispensedBy, SheetProcessorInfoProvider sheetProcessorInfoProvider) {
        // 收费员信息
        Set<String> employeeIds = new HashSet<>();
        Set<String> departmentIds = new HashSet<>();
        if (!TextUtils.isEmpty(chargeSheet.getSellerId())) {
            employeeIds.add(chargeSheet.getSellerId());
        }

        if (!TextUtils.isEmpty(dispensedBy)) {
            employeeIds.add(dispensedBy);
        }

        if (org.apache.commons.lang3.StringUtils.isNotEmpty(chargeSheetView.getPharmacistId())) {
            employeeIds.add(chargeSheetView.getPharmacistId());
        }

        if (!TextUtils.isEmpty(chargeSheetView.getConsultantId())) {
            employeeIds.add(chargeSheetView.getConsultantId());
        }

        if (org.apache.commons.lang3.StringUtils.isNotEmpty(chargeSheetView.getSellerDepartmentId())) {
            departmentIds.add(chargeSheetView.getSellerDepartmentId());
        }
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(chargeSheetView.getDepartmentId())) {
            departmentIds.add(chargeSheetView.getDepartmentId());
        }

        employeeIds.addAll(
                Optional.ofNullable(chargeSheetView.getChargeForms()).orElse(new ArrayList<>())
                        .stream()
                        .map(chargeForm -> {
                            Set<String> employeeIdList = new HashSet<>();
                            if (chargeForm.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY && chargeForm.getMedicalRecord() != null && !StringUtils.isEmpty(chargeForm.getMedicalRecord().getDoctorId())) {
                                employeeIdList.add(chargeForm.getMedicalRecord().getDoctorId());
                            }

                            if (chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_TYPE_EYE_GLASS && chargeForm.getUsageInfo() != null) {
                                UsageInfo usageInfo = JsonUtils.readValue(chargeForm.getUsageInfo(), UsageInfo.class);
                                if (!StringUtils.isEmpty(usageInfo.getOptometristId())) {
                                    employeeIdList.add(usageInfo.getOptometristId());
                                    chargeForm.setOptometristId(usageInfo.getOptometristId());
                                }
                            }

                            Optional.ofNullable(chargeForm.getChargeFormItems())
                                    .orElse(new ArrayList<>())
                                    .forEach(chargeFormItem -> {
                                        if (org.apache.commons.lang3.StringUtils.isNotBlank(chargeFormItem.getDoctorId())) {
                                            employeeIdList.add(chargeFormItem.getDoctorId());
                                        }

                                        if (org.apache.commons.lang3.StringUtils.isNotBlank(chargeFormItem.getUnitAdjustmentFeeLastModifiedBy())) {
                                            employeeIdList.add(chargeFormItem.getUnitAdjustmentFeeLastModifiedBy());
                                        }

                                        if (org.apache.commons.lang3.StringUtils.isNotBlank(chargeFormItem.getNurseId())) {
                                            employeeIdList.add(chargeFormItem.getNurseId());
                                        }
                                        if (org.apache.commons.lang3.StringUtils.isNotBlank(chargeFormItem.getDepartmentId())) {
                                            departmentIds.add(chargeFormItem.getDepartmentId());
                                        }
                                    });
                            return employeeIdList;
                        })
                        .flatMap(Collection::stream)
                        .collect(Collectors.toSet())
        );

        employeeIds.addAll(Optional.ofNullable(chargeSheetView.getChargeActions()).orElse(new ArrayList<>()).stream()
                .map(ChargeAction::getCreatedBy).collect(Collectors.toSet()));

        employeeIds.addAll(Optional.ofNullable(chargeSheetView.getChargeActions()).orElse(new ArrayList<>()).stream()
                .map(ChargeAction::getCheckerId).filter(org.apache.commons.lang3.StringUtils::isNotBlank).collect(Collectors.toSet()));

        employeeIds.addAll(Optional.ofNullable(chargeSheetView.getChangePayModeRecords()).orElse(new ArrayList<>()).stream()
                .filter(chargeChangePayModeRecord -> org.apache.commons.lang.StringUtils.isBlank(chargeChangePayModeRecord.getCreatedByName()))
                .map(ChargeChangePayModeRecordView::getCreatedBy).collect(Collectors.toSet())
        );


        Map<String, String> employeeNameMap = Optional.ofNullable(sheetProcessorInfoProvider.getEmployeeInfoProvider())
                .map(employeeInfoProvider -> employeeInfoProvider.findEmployeeIdNameMap(chargeSheet.getChainId(), new ArrayList<>(employeeIds)))
                .orElse(new HashMap<>());

        Map<String, String> departmentNameMap = Optional.ofNullable(sheetProcessorInfoProvider.getClinicProvider())
                .map(clinicProvider -> clinicProvider.queryDepartmentIdNameMapByIds(new ArrayList<>(departmentIds)))
                .orElse(new HashMap<>());

        Optional.ofNullable(chargeSheetView.getChargeActions()).orElse(new ArrayList<>())
                .forEach(chargeAction -> {
                    chargeAction.setCreatedByName(TextUtils.alwaysString(employeeNameMap.get(chargeAction.getCreatedBy())));
                    chargeAction.setCheckerName(TextUtils.alwaysString(employeeNameMap.get(chargeAction.getCheckerId())));
                });
        Optional.ofNullable(chargeSheetView.getChargeActions()).orElse(new ArrayList<>()).sort((a, b) -> ObjectUtils.compare(Optional.ofNullable(a.getSpecifiedChargedTime()).orElse(a.getCreated()), Optional.ofNullable(b.getSpecifiedChargedTime()).orElse(b.getCreated())));

        Optional.ofNullable(chargeSheetView.getChangePayModeRecords()).orElse(new ArrayList<>())
                .stream()
                .filter(chargeChangePayModeRecordView -> org.apache.commons.lang3.StringUtils.isBlank(chargeChangePayModeRecordView.getCreatedByName()))
                .forEach(chargeChangePayModeRecordView -> chargeChangePayModeRecordView.setCreatedByName(TextUtils.alwaysString(employeeNameMap.get(chargeChangePayModeRecordView.getCreatedBy()))));
        Optional.ofNullable(chargeSheetView.getChangePayModeRecords()).orElse(new ArrayList<>()).sort(Comparator.comparing(ChargeChangePayModeRecordView::getCreated));

        if (org.apache.commons.lang3.StringUtils.isNotEmpty(chargeSheetView.getSellerDepartmentId())) {
            chargeSheetView.setSellerDepartmentName(departmentNameMap.get(chargeSheetView.getSellerDepartmentId()));
        }
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(chargeSheetView.getDepartmentId())) {
            chargeSheetView.setDepartmentName(departmentNameMap.get(chargeSheetView.getDepartmentId()));
        }

        String sellerName = "";
        if (!TextUtils.isEmpty(chargeSheet.getSellerId())) {
            sellerName = employeeNameMap.getOrDefault(chargeSheet.getSellerId(), "");
        }
        chargeSheetView.setSellerName(sellerName);

        String dispensedByName = "";
        if (!TextUtils.isEmpty(dispensedBy)) {
            dispensedByName = employeeNameMap.getOrDefault(dispensedBy, "");
        }
        chargeSheetView.setDispensedByName(dispensedByName);

        if (org.apache.commons.lang3.StringUtils.isNotEmpty(chargeSheetView.getPharmacistId())) {
            chargeSheetView.setPharmacistName(employeeNameMap.getOrDefault(chargeSheetView.getPharmacistId(), ""));
        }

        if (!TextUtils.isEmpty(chargeSheetView.getDoctorId())) {
            chargeSheetView.setDoctorName(Optional.ofNullable(chargeSheetView.getChargeSheetSummary()).map(ChargeSheetSummary::getDoctorName).orElse(null));
        }

        if (!TextUtils.isEmpty(chargeSheetView.getConsultantId())) {
            chargeSheetView.setConsultantName(employeeNameMap.getOrDefault(chargeSheetView.getConsultantId(), ""));
        }

        if (!CollectionUtils.isEmpty(chargeSheetView.getChargeForms())) {
            boolean isContainMedicineChinese = chargeSheetView.getChargeForms()
                    .stream()
                    .flatMap(chargeFormView -> chargeFormView.getChargeFormItems().stream())
                    .anyMatch(chargeFormItemView -> chargeFormItemView.getProductType() == Constants.ProductType.MEDICINE && chargeFormItemView.getProductSubType() == Constants.ProductType.SubType.MEDICINE_CHINESE);
            chargeSheetView.setIsContainMedicineChinese(isContainMedicineChinese ? 1 : 0);

            boolean isContainDispensingProduct = chargeSheetView.getChargeForms()
                    .stream()
                    .flatMap(chargeFormView -> chargeFormView.getChargeFormItems().stream())
                    .anyMatch(chargeFormItemView -> chargeFormItemView.getProductType() == Constants.ProductType.MEDICINE || (chargeFormItemView.getProductType() == Constants.ProductType.MATERIAL && chargeFormItemView.getProductSubType() == Constants.ProductType.SubType.MEDICAL_MATERIAL) || chargeFormItemView.getProductType() == Constants.ProductType.SALE_PRODUCT);
            chargeSheetView.setIsContainDispensingProduct(isContainDispensingProduct ? 1 : 0);

            chargeSheetView.getChargeForms()
                    .forEach(chargeForm -> {
                        if (chargeForm.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY && chargeForm.getMedicalRecord() != null && !StringUtils.isEmpty(chargeForm.getMedicalRecord().getDoctorId())) {
                            ChargeAirPharmacyMedicalRecordView medicalRecordView = chargeForm.getMedicalRecord();
                            medicalRecordView.setDoctorName(employeeNameMap.getOrDefault(medicalRecordView.getDoctorId(), ""));
                        }

                        if (chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_TYPE_EYE_GLASS && !StringUtils.isEmpty(chargeForm.getOptometristId())) {
                            chargeForm.setOptometristName(employeeNameMap.getOrDefault(chargeForm.getOptometristId(), ""));
                        }

                        Optional.ofNullable(chargeForm.getChargeFormItems())
                                .orElse(new ArrayList<>())
                                .forEach(chargeFormItem -> {
                                    if (org.apache.commons.lang3.StringUtils.isNotEmpty(chargeFormItem.getDoctorId())) {
                                        chargeFormItem.setDoctorName(employeeNameMap.getOrDefault(chargeFormItem.getDoctorId(), ""));
                                    }

                                    if (org.apache.commons.lang3.StringUtils.isNotEmpty(chargeFormItem.getUnitAdjustmentFeeLastModifiedBy())) {
                                        chargeFormItem.setUnitAdjustmentFeeLastModifiedByName(employeeNameMap.getOrDefault(chargeFormItem.getUnitAdjustmentFeeLastModifiedBy(), ""));
                                    }

                                    if (org.apache.commons.lang3.StringUtils.isNotEmpty(chargeFormItem.getNurseId())) {
                                        chargeFormItem.setNurseName(employeeNameMap.getOrDefault(chargeFormItem.getNurseId(), ""));
                                    }
                                    if (org.apache.commons.lang3.StringUtils.isNotEmpty(chargeFormItem.getDepartmentId())) {
                                        chargeFormItem.setDepartmentName(departmentNameMap.getOrDefault(chargeFormItem.getDepartmentId(), ""));

                                    }
                                });
                    });
        }
    }

    private static String generateShebaoSettlePrintSheetId(ChargeSheetView chargeSheetView, List<ChargeOweSheet> chargeOweSheets) {

        if (Objects.isNull(chargeSheetView)) {
            return null;
        }

        if (chargeSheetView.getStatus() != Constants.ChargeSheetStatus.CHARGED && chargeSheetView.getStatus() != Constants.ChargeSheetStatus.PART_REFUNDED) {
            return null;
        }

        if (org.apache.commons.collections.CollectionUtils.isEmpty(chargeSheetView.getChargeTransactions())) {
            return null;
        }

        ChargeTransactionView shebaoChargeTransactionView = chargeSheetView.getChargeTransactions()
                .stream()
                .filter(chargeTransactionView -> chargeTransactionView.getIsRefunded() == 0
                        && MathUtils.wrapBigDecimalCompare(chargeTransactionView.getAmount(), BigDecimal.ZERO) >= 0
                        && chargeTransactionView.getPayMode() == Constants.ChargePayMode.HEALTH_CARD || chargeTransactionView.getPayMode() == Constants.ChargePayMode.SHEBAO_MULAID_PAY
                        && org.apache.commons.lang3.StringUtils.isNotEmpty(chargeTransactionView.getThirdPartyPayTransactionId())
                )
                .findFirst()
                .orElse(null);

        if (Objects.isNull(shebaoChargeTransactionView)) {
            return null;
        }


        //部分退费状态下，判断可退金额是否大于社保支付金额
        if (chargeSheetView.getStatus() == Constants.ChargeSheetStatus.PART_REFUNDED) {
            BigDecimal netIncomeFee = Optional.ofNullable(chargeSheetView.getChargeSheetSummary())
                    .map(ChargeSheetSummary::getNetIncomeFee)
                    .orElse(BigDecimal.ZERO);

            if (MathUtils.wrapBigDecimalCompare(netIncomeFee, shebaoChargeTransactionView.getAmount()) < 0) {
                return null;
            }
        }

        if (shebaoChargeTransactionView.getChargeType() == ChargeTransaction.ChargeType.NORMAL) {
            return chargeSheetView.getId();
        }

        if (shebaoChargeTransactionView.getChargeType() == ChargeTransaction.ChargeType.OWE) {
            return Optional.ofNullable(chargeOweSheets).orElse(new ArrayList<>())
                    .stream()
                    .filter(chargeOweSheet -> chargeOweSheet.getIsOldRecord() == 0)
                    .max(Comparator.comparing(ChargeOweSheet::getCreated))
                    .map(chargeOweSheet -> String.valueOf(chargeOweSheet.getId()))
                    .orElse(null);
        }

        return null;
    }

    private static void bindFormAiyPharmacyPayStatus(ChargeSheetView chargeSheetView, SheetProcessorInfoProvider sheetProcessorInfoProvider, int source) {

        if (chargeSheetView == null
                || org.apache.commons.collections.CollectionUtils.isEmpty(chargeSheetView.getChargeForms())
                || source != Constants.ChargeSource.CHARGE
                || chargeSheetView.getIsClosed() == 1
                || (chargeSheetView.getStatus() != Constants.ChargeSheetStatus.CHARGED && chargeSheetView.getStatus() != Constants.ChargeSheetStatus.PART_REFUNDED)
                || sheetProcessorInfoProvider == null
                || sheetProcessorInfoProvider.getAirPharmacyProvider() == null
        ) {
            return;
        }

        List<ChargeFormView> airPharmacyForms = chargeSheetView.getChargeForms()
                .stream()
                .filter(chargeFormView -> chargeFormView.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY)
                .filter(chargeFormView -> chargeFormView.getStatus() == Constants.ChargeFormStatus.CHARGED)
                .collect(Collectors.toList());

        if (org.apache.commons.collections.CollectionUtils.isEmpty(airPharmacyForms)) {
            return;
        }

        List<String> airPharmacyChargeFormIds = airPharmacyForms.stream().map(ChargeFormView::getId).collect(Collectors.toList());

        Map<String, CreateOrderView> airPharmacyOrderStatusMap = sheetProcessorInfoProvider.getAirPharmacyProvider().findAirPharmacyOrderStatusByChargeFormIdsAndClinicId(airPharmacyChargeFormIds, chargeSheetView.getClinicId());

        airPharmacyForms.forEach(chargeFormView -> {
            CreateOrderView createOrderView = airPharmacyOrderStatusMap.getOrDefault(chargeFormView.getId(), null);
            int isAirPharmacyNeedPaid = createOrderView != null && createOrderView.getStatus() == Constant.OrderStatus.UNPAID ? 1 : 0;
            if (isAirPharmacyNeedPaid == 1) {
                chargeFormView.setIsAirPharmacyNeedPaid(isAirPharmacyNeedPaid);
                chargeFormView.setAirPharmacyOrderId(createOrderView.getOrderId());
            }
        });

    }

    private static void removeProcessFormByDisabledProcessRule(ChargeSheetView chargeSheetView, SheetProcessorInfoProvider sheetProcessorInfoProvider, int source) {

        if (!Constants.ChargeSource.patientPaySources().contains(source)
                || chargeSheetView == null
                || CollectionUtils.isEmpty(chargeSheetView.getChargeForms())
                || sheetProcessorInfoProvider == null
        ) {
            return;
        }

        List<UsageTypeInfo> availableUsages = sheetProcessorInfoProvider.getChargeRuleProvider().findAvailableUsages(chargeSheetView.getClinicId());

        Map<String, UsageTypeInfo> usageTypeInfoMap = ListUtils.toMap(availableUsages, usageTypeInfo -> String.format("%s-%s", usageTypeInfo.getType(), usageTypeInfo.getSubType()));

        Predicate<ChargeFormView> needRemoveProcessFormPredicate = chargeFormView -> chargeFormView.getProcessInfo() != null
                && usageTypeInfoMap.getOrDefault(String.format("%s-%s", chargeFormView.getProcessInfo().getType(), chargeFormView.getProcessInfo().getSubType()), null) == null
                && MathUtils.wrapBigDecimalCompare(chargeFormView.getTotalPrice(), BigDecimal.ZERO) == 0;

        chargeSheetView.getChargeForms().removeIf(needRemoveProcessFormPredicate::test);
    }

    private static void fillProcessInfoName(ChargeSheetView chargeSheetView, SheetProcessorInfoProvider sheetProcessorInfoProvider) {

        if (chargeSheetView == null || CollectionUtils.isEmpty(chargeSheetView.getChargeForms()) || sheetProcessorInfoProvider == null) {
            return;
        }

        List<UsageTypeInfo> usageTypeInfos = sheetProcessorInfoProvider.getChargeRuleProvider().findAvailableUsages(chargeSheetView.getClinicId());

        Map<String, UsageTypeInfo> usageTypeInfoMap = ListUtils.toMap(Optional.ofNullable(usageTypeInfos).orElse(Lists.newArrayList()), usageTypeInfo -> String.format("%s-%s", usageTypeInfo.getType(), usageTypeInfo.getSubType()));

        chargeSheetView.getChargeForms().stream()
                .filter(chargeFormView -> chargeFormView.getSourceFormType() == Constants.SourceFormType.PROCESS)
                .filter(chargeFormView -> chargeFormView.getProcessInfo() != null)
                .forEach(chargeFormView -> {
                    ProcessInfoView processInfo = chargeFormView.getProcessInfo();
                    UsageTypeInfo usageTypeInfo = usageTypeInfoMap.getOrDefault(String.format("%s-%s", processInfo.getType(), processInfo.getSubType()), null);
                    if (usageTypeInfo != null) {
                        processInfo.setName(usageTypeInfo.getName());
                    } else {
                        processInfo.setName(Constants.SystemProductId.PROCESS_NAME);
                    }
                });
    }

    private static void fillLockStatusField(ChargeSheetView chargeSheetView, ChargePayProvider chargePayProvider, ChargeSheet chargeSheet, int source) {
        chargeSheetView.setLockStatus(chargeSheet.getLockStatus());
        chargeSheetView.setAutoUnlockTime(chargeSheet.getAutoUnlockTime());
        if (chargeSheet.getAutoUnlockTime() != null) {
            chargeSheetView.setAutoUnlockRemainTimeSecond(ChronoUnit.SECONDS.between(Instant.now(), chargeSheet.getAutoUnlockTime()));
        }

        if (source == Constants.ChargeSource.DEVICE || source == Constants.ChargeSource.WE_CLINIC) {
            chargeSheetView.setCanUnLockChargeSheet(ChargeUtils.isChargerLockStatus(chargeSheetView.getLockStatus()));
        } else {
            if (chargeSheetView.getLockStatus() == Constants.ChargeSheetLockStatusV2.NONE) {
                chargeSheetView.setCanEditChargeSheetForPC(1);
            }
            chargeSheetView.setCanUnLockChargeSheet(ChargeUtils.isChargerLockStatus(chargeSheetView.getLockStatus()));
        }

        //支付或退费锁单
        if (chargeSheet.isLockingForPayOrRefund()) {
            chargeSheetView.setLockPayTransactionInfo(LockPayTransactionView.of(chargePayProvider.findLatestWaitingPayTransaction(chargeSheet.getId())));
        }
    }

    private static void fillAirMedicineStateScopeName(ChargeSheetView chargeSheetView, SheetProcessorInfoProvider sheetProcessorInfoProvider) {

        //判断是否有空中药房的form
        boolean containAirPharmacyForm = Optional.ofNullable(chargeSheetView.getChargeForms())
                .orElse(new ArrayList<>())
                .stream()
                .anyMatch(chargeFormView -> chargeFormView.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY);

        if (!containAirPharmacyForm) {
            return;
        }

        if (sheetProcessorInfoProvider.getAirPharmacyProvider() != null && !CollectionUtils.isEmpty(chargeSheetView.getChargeForms())) {
            List<BusinessScopeVO> businessScopeVOS = sheetProcessorInfoProvider.getAirPharmacyProvider().listMedicineStateScopeByType();

            if (!CollectionUtils.isEmpty(businessScopeVOS)) {

                Map<String, BusinessScopeVO> businessScopeVOMap = ListUtils.toMap(businessScopeVOS, BusinessScopeVO::getId);

                chargeSheetView.getChargeForms()
                        .stream()
                        .filter(chargeFormView -> chargeFormView.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY || chargeFormView.getPharmacyType() == GoodsConst.PharmacyType.VIRTUAL_PHARMACY)
                        .filter(chargeFormView -> !StringUtils.isEmpty(chargeFormView.getMedicineStateScopeId()))
                        .forEach(chargeFormView -> {

//                            boolean isContainProcessItem = false;
//
//                            if (!CollectionUtils.isEmpty(chargeFormView.getChargeFormItems())) {
//                                isContainProcessItem = chargeFormView.getChargeFormItems().stream().anyMatch(chargeFormItemView -> chargeFormItemView.getProductType() == Constants.ProductType.PROCESS);
//                            }

                            BusinessScopeVO businessScopeVO = businessScopeVOMap.get(chargeFormView.getMedicineStateScopeId());

                            if (businessScopeVO != null) {
                                chargeFormView.setMedicineStateScopeName(businessScopeVO.getName());
                            }
                        });
            }
        }
    }

    private static ChargeFormView generateDecoctionChargeFormView(ChargeSheet chargeSheet, List<ChargeFormView> chargeFormViews) {
        if (chargeFormViews == null) {
            return null;
        }

        boolean isContainDecoction = chargeFormViews.stream().anyMatch(chargeFormView -> chargeFormView.getSourceFormType() == Constants.SourceFormType.PROCESS);

        if (isContainDecoction) {
            return null;
        }

        ChargeFormView chargeFormView = new ChargeFormView();

        chargeFormView.setId(AbcIdUtils.getUUID());
        chargeFormView.setChainId(chargeSheet.getChainId());
        chargeFormView.setClinicId(chargeSheet.getClinicId());
        chargeFormView.setPatientOrderId(chargeSheet.getPatientOrderId());
        chargeFormView.setChargeSheetId(chargeSheet.getId());
        chargeFormView.setSourceFormType(Constants.SourceFormType.PROCESS);
        chargeFormView.setStatus(Constants.ChargeFormStatus.UNCHARGED);
        List<ChargeFormItemView> chargeFormItems = new ArrayList<>();
        ChargeFormItemView deliveryChargeFormItemView = generateDecoctionChargeFormItemView(chargeSheet, chargeFormView);
        chargeFormItems.add(deliveryChargeFormItemView);
        chargeFormView.setChargeFormItems(chargeFormItems);
        return chargeFormView;
    }

    private static ChargeFormItemView generateDecoctionChargeFormItemView(ChargeSheet chargeSheet, ChargeFormView chargeFormView) {

        ChargeFormItemView chargeFormItemView = new ChargeFormItemView();
        chargeFormItemView.setId(AbcIdUtils.getUUID());
        chargeFormItemView.setChainId(chargeFormView.getChainId());
        chargeFormItemView.setClinicId(chargeFormView.getClinicId());
        chargeFormItemView.setPatientOrderId(chargeFormView.getPatientOrderId());
        chargeFormItemView.setChargeSheetId(chargeSheet.getId());
        chargeFormItemView.setChargeFormId(chargeFormView.getId());
        chargeFormItemView.setStatus(Constants.ChargeFormItemStatus.UNCHARGED);
        chargeFormItemView.setName(Constants.SystemProductId.PROCESS_NAME);
        chargeFormItemView.setUnitCostPrice(BigDecimal.ZERO);
        chargeFormItemView.setUnitCount(BigDecimal.ONE);
        chargeFormItemView.setDoseCount(BigDecimal.ONE);
        chargeFormItemView.setUnitPrice(null);
        chargeFormItemView.setTotalPrice(null);
        chargeFormItemView.setDiscountPrice(BigDecimal.ZERO);
        chargeFormItemView.setProductType(Constants.ProductType.PROCESS);
        chargeFormItemView.setProductSubType(1);

        DecoctionInfoReq decoctionInfoReq = new DecoctionInfoReq();
        decoctionInfoReq.setDecoctionFee(BigDecimal.ZERO);

        chargeFormItemView.setProductInfo(JsonUtils.dumpAsJsonNode(decoctionInfoReq));

        return chargeFormItemView;
    }

    private static DecoctionInfoView generateDecoctionInfoView(ChargeSheet chargeSheet) {
        DecoctionInfoView decoctionInfoView = new DecoctionInfoView();
        if (chargeSheet.getChargeForms() != null) {
            ChargeFormItem decoctionChargeFormItem = chargeSheet.getChargeForms()
                    .stream()
                    .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                    .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                    .filter(item -> item.getIsDeleted() == 0)
                    .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.PROCESS)
                    .findFirst().orElse(null);

            if (decoctionChargeFormItem != null && decoctionChargeFormItem.getProductInfo() != null) {
                DecoctionInfoReq decoctionInfoReq = JsonUtils.readValue(decoctionChargeFormItem.getProductInfo(), DecoctionInfoReq.class);
                BeanUtils.copyProperties(decoctionInfoReq, decoctionInfoView);
            }
        }

        return decoctionInfoView;
    }

    private static ChargeFormView generateDeliveryChargeFormView(ChargeSheet chargeSheet, List<ChargeFormView> chargeFormViews) {
        boolean isContainExpressDelivery = chargeFormViews.stream().anyMatch(chargeFormView -> chargeFormView.getSourceFormType() == Constants.SourceFormType.EXPRESS_DELIVERY);
        if (isContainExpressDelivery) {
            return null;
        }
        ChargeFormView chargeFormView = new ChargeFormView();
        chargeFormView.setId(AbcIdUtils.getUUID());
        chargeFormView.setChainId(chargeSheet.getChainId());
        chargeFormView.setClinicId(chargeSheet.getClinicId());
        chargeFormView.setPatientOrderId(chargeSheet.getPatientOrderId());
        chargeFormView.setChargeSheetId(chargeSheet.getId());
        chargeFormView.setSourceFormType(Constants.SourceFormType.EXPRESS_DELIVERY);
        chargeFormView.setStatus(Constants.ChargeFormStatus.UNCHARGED);
        chargeFormView.setTotalPrice(chargeSheet.getDeliveryInfo().getDeliveryFee());
        List<ChargeFormItemView> chargeFormItems = new ArrayList<>();
        ChargeFormItemView deliveryChargeFormItemView = generateDeliveryChargeFormItemView(chargeSheet, chargeFormView, chargeSheet.getDeliveryInfo());
        chargeFormItems.add(deliveryChargeFormItemView);
        chargeFormView.setChargeFormItems(chargeFormItems);
        return chargeFormView;
    }

    private static ChargeFormItemView generateDeliveryChargeFormItemView(ChargeSheet chargeSheet, ChargeFormView chargeFormView, ChargeDeliveryInfo deliveryInfo) {
        ChargeFormItemView chargeFormItemView = new ChargeFormItemView();
        chargeFormItemView.setId(AbcIdUtils.getUUID());
        chargeFormItemView.setChainId(chargeFormView.getChainId());
        chargeFormItemView.setClinicId(chargeFormView.getClinicId());
        chargeFormItemView.setPatientOrderId(chargeFormView.getPatientOrderId());
        chargeFormItemView.setChargeSheetId(chargeSheet.getId());
        chargeFormItemView.setChargeFormId(chargeFormView.getId());
        chargeFormItemView.setStatus(Constants.ChargeFormItemStatus.UNCHARGED);
        chargeFormItemView.setName(Constants.SystemProductId.EXPRESS_DELIVERY_NAME);
        chargeFormItemView.setUnitCostPrice(BigDecimal.ZERO);
        chargeFormItemView.setUnitCount(BigDecimal.ONE);
        chargeFormItemView.setDoseCount(BigDecimal.ONE);
        if (deliveryInfo != null) {
            chargeFormItemView.setUnitPrice(deliveryInfo.getDeliveryFee());
            chargeFormItemView.setTotalPrice(deliveryInfo.getDeliveryFee());
        }
        chargeFormItemView.setDiscountPrice(BigDecimal.ZERO);
        chargeFormItemView.setProductType(Constants.ProductType.EXPRESS_DELIVERY);
        chargeFormItemView.setProductSubType(1);
        chargeFormItemView.setProductInfo(JsonUtils.dumpAsJsonNode(deliveryInfo));
        return chargeFormItemView;
    }

    public static ChargeSheetPrintable generatePrintable(ChargeSheet chargeSheet, PatientOrder patientOrder, Supplier<PrintMedicalDocumentsInfusionAndTreatmentProperty> queryPropertySupplier) {
        ChargeSheetPrintable chargeSheetPrintable = new ChargeSheetPrintable();
        if (Objects.isNull(chargeSheet) || CollectionUtils.isEmpty(chargeSheet.getChargeForms())) {
            return chargeSheetPrintable;
        }
        if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.CHARGED || chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_REFUNDED) {
            chargeSheetPrintable.setChargeSheet(true);
        }
        if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.REFUNDED || chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_REFUNDED) {
            chargeSheetPrintable.setRefundChargeSheet(true);
        }
        if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.CHARGED || chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_REFUNDED) {
            chargeSheetPrintable.setDispensingSheet(
                    Optional.ofNullable(chargeSheet.getChargeForms())
                            .orElse(new ArrayList<>())
                            .stream()
                            .filter(chargeForm -> chargeForm.getIsDeleted() == YesOrNo.NO)
                            .filter(chargeForm -> chargeForm.getSourceFormType() != Constants.SourceFormType.AIR_PHARMACY)
                            .flatMap(chargeForm -> Optional.ofNullable(chargeForm.getChargeFormItems()).orElse(new ArrayList<>()).stream())
                            .filter(item -> item.getIsDeleted() == YesOrNo.NO)
                            .filter(chargeFormItem -> chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED
                                    && chargeFormItem.getSourceItemType() == Constants.SourceItemType.NORMAL
                                    && chargeFormItem.getGoodsFeeType() != GoodsFeeType.FEE_CHILD
                                    && MathUtils.calculateTotalCount(chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount()).compareTo(BigDecimal.ZERO) > 0
                            ).anyMatch(ChargeFormItem::isCanDispensing)
            );
        }
        boolean prescription = Optional.ofNullable(chargeSheet.getChargeForms()).orElse(new ArrayList<>())
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == YesOrNo.NO)
                .anyMatch(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_TYPE_EYE_GLASS);
        chargeSheetPrintable.setPrescription(prescription);
        if (patientOrder != null &&
                (patientOrder.getSource() == PatientOrder.Source.REGISTRATION
                        || patientOrder.getSource() == PatientOrder.Source.OUTPATIENT
                        || patientOrder.getSource() == PatientOrder.Source.COPYWRITING_PRESCRIPTION
                        || patientOrder.getSource() == PatientOrder.Source.ONLINE_CONSULTATION
                ) && !prescription
        ) {
            List<ChargeForm> chargeForms;
            if (patientOrder.getSource() == PatientOrder.Source.OUTPATIENT || patientOrder.getSource() == PatientOrder.Source.ONLINE_CONSULTATION || patientOrder.getSource() == PatientOrder.Source.REGISTRATION) {
                chargeForms = ChargeUtils.getChargeFormsFilterMaterialAndProductAndCompose(chargeSheet);
            } else {
                chargeForms = ChargeUtils.getChargeFormsFilterMaterialAndCompose(chargeSheet);
            }
            prescription = chargeForms.stream()
                    .filter(chargeForm -> chargeForm.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_EXTERNAL)
                    .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                    .filter(item -> item.getIsDeleted() == YesOrNo.NO)
                    .filter(chargeFormItem -> chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.CHARGED || chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.UNCHARGED)
                    .filter(chargeFormItem -> !TextUtils.isEmpty(chargeFormItem.getSourceFormItemId()))
                    .anyMatch(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.MEDICINE || chargeFormItem.getProductType() == Constants.ProductType.MATERIAL || chargeFormItem.getProductType() == Constants.ProductType.SALE_PRODUCT)
                    ||
                    chargeForms.stream()
                            .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_EXTERNAL)
                            .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                            .filter(item -> item.getIsDeleted() == YesOrNo.NO)
                            .filter(chargeFormItem -> chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.CHARGED || chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.UNCHARGED)
                            .anyMatch(chargeFormItem -> !TextUtils.isEmpty(chargeFormItem.getSourceFormItemId()));
            chargeSheetPrintable.setPrescription(prescription);
        } else if (chargeSheet.getType() == ChargeSheet.Type.CLONE_PRESCRIPTION && Objects.nonNull(chargeSheet.getAdditional())) {
            // 拍照续方判断是否可以打印处方
            int clonePrescriptionType = chargeSheet.getAdditional().getClonePrescriptionType();
            if (clonePrescriptionType == ChargeSheetAdditional.ClonePrescriptionType.FROM_PHOTO_BY_MC_PATIENT
                    && org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeSheet.getAttachments())) {
                chargeSheetPrintable.setPrescription(true);
                chargeSheetPrintable.setPrescriptionPrintType(ChargeSheetPrintable.PrescriptionPrintType.PHOTO_BY_MC_PATIENT);
            }
//            else if (clonePrescriptionType == ChargeSheetAdditional.ClonePrescriptionType.FROM_HISTORY_CHARGE_SHEET
//                    && Objects.nonNull(chargeSheet.getAdditional())
//                    && Objects.nonNull(chargeSheet.getAdditional().getCloneChargeSheetSnapshot())) {
//                //历史处方续方判断是否可以打印处方
//                CloneChargeSheetSnapshot cloneChargeSheetSnapshot = chargeSheet.getAdditional().getCloneChargeSheetSnapshot();
//
//                List<String> sourceFormIds = Optional.ofNullable(cloneChargeSheetSnapshot.getChargeForms())
//                        .orElse(new ArrayList<>())
//                        .stream()
//                        .filter(chargeForm -> org.apache.commons.lang3.StringUtils.isNotEmpty(chargeForm.getSourceFormId()))
//                        .map(CloneChargeSheetSnapshot.ChargeForm::getSourceFormId)
//                        .distinct()
//                        .collect(Collectors.toList());
//
//                if (cloneChargeSheetSnapshot.getType() == ChargeSheet.Type.OUTPATIENT && org.apache.commons.collections.CollectionUtils.isNotEmpty(sourceFormIds)) {
//                    chargeSheetPrintable.setPrescription(true);
//                    chargeSheetPrintable.setPrescriptionPrintType(ChargeSheetPrintable.PrescriptionPrintType.HISTORY_CHARGE_SHEET);
//                }
//            }
        }

        boolean includeExternal = true;
        boolean containProductOtherPrice = true;
        boolean containProductMaterialsPrice = false;
        if (queryPropertySupplier != null) {
            PrintMedicalDocumentsInfusionAndTreatmentProperty medicalDocumentsInfusionAndTreatmentProperty = queryPropertySupplier.get();
            includeExternal = Optional.ofNullable(medicalDocumentsInfusionAndTreatmentProperty)
                    .map(PrintMedicalDocumentsInfusionAndTreatmentProperty::getInfusion)
                    .map(PrintMedicalDocumentsInfusion::getContent)
                    .map(PrintMedicalDocumentsInfusion.InfusionContent::getIncludeExternal)
                    .orElse(0) == 1;
            containProductOtherPrice = Optional.ofNullable(medicalDocumentsInfusionAndTreatmentProperty)
                    .map(PrintMedicalDocumentsInfusionAndTreatmentProperty::getTreatmentContent)
                    .map(MedicalDocumentsTreatmentContent::getProductOtherPrice)
                    .orElse(1) == 1;
            containProductMaterialsPrice = Optional.ofNullable(medicalDocumentsInfusionAndTreatmentProperty)
                    .map(PrintMedicalDocumentsInfusionAndTreatmentProperty::getTreatmentContent)
                    .map(MedicalDocumentsTreatmentContent::getProductMaterialsPrice)
                    .orElse(0) == 1;
        }
        boolean finalIncludeExternal = includeExternal;

        chargeSheetPrintable.setExecuteTreatmentSheet(ChargeUtils.getChargeSheetItemsFilterMaterialAndPrescriptionExternal(chargeSheet)
                .stream()
                .filter(chargeFormItem -> chargeFormItem.getGoodsFeeType() != GoodsFeeType.FEE_CHILD)
                .filter(chargeFormItem -> chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.CHARGED || chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.UNCHARGED)
                .anyMatch(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.TREATMENT && chargeFormItem.getProductSubType() == Constants.ProductType.SubType.TREATMENT_PHYSIOTHERAPY));

        chargeSheetPrintable.setExecuteInfusionSheet(ChargeUtils.getChargeSheetItemsFilterMaterialAndPrescriptionExternal(chargeSheet)
                .stream()
                .filter(chargeFormItem -> chargeFormItem.getGoodsFeeType() != GoodsFeeType.FEE_CHILD)
                .filter(chargeFormItem -> chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.CHARGED || chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.UNCHARGED)
                .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.MEDICINE || chargeFormItem.getProductType() == Constants.ProductType.MATERIAL || chargeFormItem.getProductType() == Constants.ProductType.SALE_PRODUCT || (chargeFormItem.getProductType() == Constants.ProductType.TREATMENT && chargeFormItem.getProductSubType() == Constants.ProductType.SubType.TREATMENT_TREATMENT))
                .anyMatch(chargeFormItem -> ChargeUtils.isExecutableItem(chargeFormItem, finalIncludeExternal)));

        if (chargeSheet.getType() == ChargeSheet.Type.DIRECT_SALE) {
            chargeSheetPrintable.setMedicineTag(
                    Optional.ofNullable(chargeSheet.getChargeForms()).orElse(new ArrayList<>())
                            .stream()
                            .filter(chargeForm -> chargeForm.getIsDeleted() == YesOrNo.NO)
                            .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE)
                            .filter(chargeForm -> chargeForm.getChargeFormItems() != null)
                            .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                            .filter(item -> item.getIsDeleted() == YesOrNo.NO)
                            .filter(chargeFormItem -> chargeFormItem.getGoodsFeeType() != GoodsFeeType.FEE_CHILD)
                            .filter(chargeFormItem -> chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.CHARGED || chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.UNCHARGED)
                            .anyMatch(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.MEDICINE));
        } else {
            chargeSheetPrintable.setMedicineTag(
                    ChargeUtils.getChargeSheetItemsFilterMaterialAndPrescriptionExternalAndCompose(chargeSheet)
                            .stream()
                            .filter(chargeFormItem -> chargeFormItem.getGoodsFeeType() != GoodsFeeType.FEE_CHILD)
                            .filter(chargeFormItem -> chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.CHARGED || chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.UNCHARGED)
                            .anyMatch(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.MEDICINE));
        }
        chargeSheetPrintable.setPatientTag(!StringUtils.isEmpty(chargeSheet.getPatientId()) && !chargeSheet.getPatientId().equals(Constants.ANONYMOUS_PATIENT_ID));
        chargeSheetPrintable.setExecuteTransfusionSheet(ChargeUtils.getChargeSheetItemsFilterMaterialAndPrescriptionExternal(chargeSheet)
                .stream()
                .filter(chargeFormItem -> chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.CHARGED || chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.UNCHARGED)
                .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.MEDICINE || chargeFormItem.getProductType() == Constants.ProductType.MATERIAL || chargeFormItem.getProductType() == Constants.ProductType.SALE_PRODUCT)
                .anyMatch(chargeFormItem -> ChargeUtils.isTransfusionExecutableItem(chargeFormItem, finalIncludeExternal)));

        // 打印治疗理疗单需要包含其他费用类型
        boolean finalContainProductOtherPrice = containProductOtherPrice;
        boolean finalContainProductMaterialsPrice = containProductMaterialsPrice;

        boolean executeTherapySheet = chargeSheet.getChargeForms()
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == YesOrNo.NO)
                .filter(chargeForm -> chargeForm.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_EXTERNAL
                        && chargeForm.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_CHINESE
                        && chargeForm.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_WESTERN
                        && chargeForm.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_INFUSION
                        && chargeForm.getSourceFormType() != Constants.SourceFormType.SURGERY
                )
                .filter(chargeForm -> chargeForm.getChargeFormItems() != null)
                .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                .filter(item -> item.getIsDeleted() == YesOrNo.NO)
                .filter(chargeFormItem -> chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.CHARGED || chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.UNCHARGED)
                .filter(chargeFormItem -> chargeFormItem.getGoodsFeeType() != GoodsFeeType.FEE_CHILD)
                .anyMatch(chargeFormItem -> ChargeUtils.getNeedExecuteTherapySheetPredicate(finalContainProductOtherPrice, finalContainProductMaterialsPrice).test(chargeFormItem));

        // 判断治疗理疗单是否包含外治处方
        executeTherapySheet = executeTherapySheet || ChargeUtils.getChargeSheetForms(chargeSheet, Constants.SourceFormType.PRESCRIPTION_EXTERNAL)
                .stream()
                .filter(chargeForm -> org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeForm.getChargeFormItems()))
                .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                .filter(item -> item.getIsDeleted() == YesOrNo.NO)
                .filter(chargeFormItem -> chargeFormItem.getGoodsFeeType() != GoodsFeeType.FEE_CHILD)
                .anyMatch(chargeFormItem -> chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.CHARGED || chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.UNCHARGED);

        chargeSheetPrintable.setExecuteTherapySheet(executeTherapySheet);

        chargeSheetPrintable.setExamination(ChargeUtils.getChargeSheetItems(chargeSheet)
                .stream()
                .filter(chargeFormItem -> chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.CHARGED || chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.UNCHARGED)
                .anyMatch(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.EXAMINATION));
        chargeSheetPrintable.setExaminationExamination(ChargeUtils.getChargeSheetItems(chargeSheet)
                .stream()
                .filter(chargeFormItem -> chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.CHARGED || chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.UNCHARGED)
                .anyMatch(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.EXAMINATION && chargeFormItem.getProductSubType() == Constants.ProductType.SubType.EXAMINATION_EXAMINATION));
        chargeSheetPrintable.setExaminationInspection(ChargeUtils.getChargeSheetItems(chargeSheet)
                .stream()
                .filter(chargeFormItem -> chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.CHARGED || chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.UNCHARGED)
                .anyMatch(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.EXAMINATION && chargeFormItem.getProductSubType() == Constants.ProductType.SubType.EXAMINATION_INSPECTION));
        chargeSheetPrintable.setExaminationExaminationBarCode(chargeSheetPrintable.isExaminationExamination());
        return chargeSheetPrintable;
    }

    public static ChargeSheetPrintable generatePharmacyPrintable(ChargeSheet chargeSheet, PatientOrder patientOrder, PharmacySheetProcessor sheetProcessor) {
        PropertyProvider propertyProvider = Optional.ofNullable(sheetProcessor).map(s -> s.sheetProcessorInfoProvider)
                .map(SheetProcessorInfoProvider::getPropertyProvider)
                .orElse(null);
        Supplier<PrintMedicalDocumentsInfusionAndTreatmentProperty> queryPropertySupplier = null;
        if (Objects.nonNull(propertyProvider)) {
            queryPropertySupplier = () -> propertyProvider.getPrintMedicalDocumentsInfusionAndTreatmentContent(chargeSheet.getClinicId());
        }
        return generatePharmacyPrintable(chargeSheet, patientOrder, queryPropertySupplier);
    }

    public static ChargeSheetPrintable generatePharmacyPrintable(ChargeSheet chargeSheet, PatientOrder patientOrder, Supplier<PrintMedicalDocumentsInfusionAndTreatmentProperty> queryPropertySupplier) {
        ChargeSheetPrintable chargeSheetPrintable = new ChargeSheetPrintable();
        if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.CHARGED || chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_REFUNDED) {
            chargeSheetPrintable.setChargeSheet(true);
        }
        if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.REFUNDED || chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_REFUNDED) {
            chargeSheetPrintable.setRefundChargeSheet(true);
        }
        if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.CHARGED || chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_REFUNDED) {
            chargeSheetPrintable.setDispensingSheet(
                    Optional.ofNullable(chargeSheet.getChargeForms())
                            .orElse(new ArrayList<>())
                            .stream()
                            .filter(chargeForm -> chargeForm.getIsDeleted() == YesOrNo.NO)
                            .filter(chargeForm -> chargeForm.getSourceFormType() != Constants.SourceFormType.AIR_PHARMACY)
                            .flatMap(chargeForm -> Optional.ofNullable(chargeForm.getChargeFormItems()).orElse(new ArrayList<>()).stream())
                            .filter(item -> item.getIsDeleted() == YesOrNo.NO)
                            .filter(chargeFormItem -> chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED
                                    && chargeFormItem.getSourceItemType() == Constants.SourceItemType.NORMAL
                                    && chargeFormItem.getGoodsFeeType() != GoodsFeeType.FEE_CHILD
                                    && MathUtils.calculateTotalCount(chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount()).compareTo(BigDecimal.ZERO) > 0
                            ).anyMatch(ChargeFormItem::isCanDispensing)
            );
        }
        boolean prescription = chargeSheet.getChargeForms()
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == YesOrNo.NO)
                .anyMatch(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_TYPE_EYE_GLASS);
        chargeSheetPrintable.setPrescription(prescription);
        if (Objects.nonNull(patientOrder) &&
                (patientOrder.getSource() == PatientOrder.Source.REGISTRATION
                        || patientOrder.getSource() == PatientOrder.Source.OUTPATIENT
                        || patientOrder.getSource() == PatientOrder.Source.COPYWRITING_PRESCRIPTION
                        || patientOrder.getSource() == PatientOrder.Source.ONLINE_CONSULTATION
                ) && !prescription
        ) {
            List<ChargeForm> chargeForms;
            if (patientOrder.getSource() == PatientOrder.Source.OUTPATIENT || patientOrder.getSource() == PatientOrder.Source.ONLINE_CONSULTATION || patientOrder.getSource() == PatientOrder.Source.REGISTRATION) {
                chargeForms = ChargeUtils.getChargeFormsFilterMaterialAndProductAndCompose(chargeSheet);
            } else {
                chargeForms = ChargeUtils.getChargeFormsFilterMaterialAndCompose(chargeSheet);
            }
            prescription = chargeForms.stream()
                    .filter(chargeForm -> chargeForm.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_EXTERNAL)
                    .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                    .filter(item -> item.getIsDeleted() == YesOrNo.NO)
                    .filter(chargeFormItem -> chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.CHARGED || chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.UNCHARGED)
                    .filter(chargeFormItem -> org.apache.commons.lang3.StringUtils.isNotBlank(chargeFormItem.getSourceFormItemId()))
                    .anyMatch(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.MEDICINE || chargeFormItem.getProductType() == Constants.ProductType.MATERIAL || chargeFormItem.getProductType() == Constants.ProductType.SALE_PRODUCT)
                    ||
                    chargeForms.stream()
                            .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_EXTERNAL)
                            .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                            .filter(item -> item.getIsDeleted() == YesOrNo.NO)
                            .filter(chargeFormItem -> chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.CHARGED || chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.UNCHARGED)
                            .anyMatch(chargeFormItem -> org.apache.commons.lang3.StringUtils.isNotBlank(chargeFormItem.getSourceFormItemId()));
            chargeSheetPrintable.setPrescription(prescription);
        } else if (chargeSheet.getType() == ChargeSheet.Type.CLONE_PRESCRIPTION && Objects.nonNull(chargeSheet.getAdditional())) {
            // 拍照续方判断是否可以打印处方
            int clonePrescriptionType = chargeSheet.getAdditional().getClonePrescriptionType();
            if (clonePrescriptionType == ChargeSheetAdditional.ClonePrescriptionType.FROM_PHOTO_BY_MC_PATIENT
                    && org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeSheet.getAttachments())) {
                chargeSheetPrintable.setPrescription(true);
                chargeSheetPrintable.setPrescriptionPrintType(ChargeSheetPrintable.PrescriptionPrintType.PHOTO_BY_MC_PATIENT);
            }
        }

        boolean includeExternal = true;
        boolean containProductOtherPrice = true;
        boolean containProductMaterialsPrice = false;
        if (Objects.nonNull(queryPropertySupplier)) {
            PrintMedicalDocumentsInfusionAndTreatmentProperty medicalDocumentsInfusionAndTreatmentProperty = queryPropertySupplier.get();
            includeExternal = Optional.ofNullable(medicalDocumentsInfusionAndTreatmentProperty)
                    .map(PrintMedicalDocumentsInfusionAndTreatmentProperty::getInfusion)
                    .map(PrintMedicalDocumentsInfusion::getContent)
                    .map(PrintMedicalDocumentsInfusion.InfusionContent::getIncludeExternal)
                    .orElse(YesOrNo.NO) == YesOrNo.YES;
            containProductOtherPrice = Optional.ofNullable(medicalDocumentsInfusionAndTreatmentProperty)
                    .map(PrintMedicalDocumentsInfusionAndTreatmentProperty::getTreatmentContent)
                    .map(MedicalDocumentsTreatmentContent::getProductOtherPrice)
                    .orElse(YesOrNo.YES) == YesOrNo.YES;
            containProductMaterialsPrice = Optional.ofNullable(medicalDocumentsInfusionAndTreatmentProperty)
                    .map(PrintMedicalDocumentsInfusionAndTreatmentProperty::getTreatmentContent)
                    .map(MedicalDocumentsTreatmentContent::getProductMaterialsPrice)
                    .orElse(YesOrNo.NO) == YesOrNo.YES;
        }

        chargeSheetPrintable.setExecuteTreatmentSheet(ChargeUtils.getChargeSheetItemsFilterMaterialAndPrescriptionExternal(chargeSheet)
                .stream()
                .filter(chargeFormItem -> chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.CHARGED || chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.UNCHARGED)
                .anyMatch(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.TREATMENT && chargeFormItem.getProductSubType() == Constants.ProductType.SubType.TREATMENT_PHYSIOTHERAPY));

        boolean finalIncludeExternal = includeExternal;
        chargeSheetPrintable.setExecuteInfusionSheet(ChargeUtils.getChargeSheetItemsFilterMaterialAndPrescriptionExternal(chargeSheet)
                .stream()
                .filter(chargeFormItem -> chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.CHARGED || chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.UNCHARGED)
                .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.MEDICINE || chargeFormItem.getProductType() == Constants.ProductType.MATERIAL || chargeFormItem.getProductType() == Constants.ProductType.SALE_PRODUCT || (chargeFormItem.getProductType() == Constants.ProductType.TREATMENT && chargeFormItem.getProductSubType() == Constants.ProductType.SubType.TREATMENT_TREATMENT))
                .anyMatch(chargeFormItem -> ChargeUtils.isExecutableItem(chargeFormItem, finalIncludeExternal)));

        if (chargeSheet.getType() == ChargeSheet.Type.DIRECT_SALE) {
            chargeSheetPrintable.setMedicineTag(
                    Optional.ofNullable(chargeSheet.getChargeForms()).orElse(new ArrayList<>())
                            .stream()
                            .filter(chargeForm -> chargeForm.getIsDeleted() == YesOrNo.NO)
                            .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE)
                            .filter(chargeForm -> org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeForm.getChargeFormItems()))
                            .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                            .filter(item -> item.getIsDeleted() == YesOrNo.NO)
                            .filter(chargeFormItem -> chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.CHARGED || chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.UNCHARGED)
                            .anyMatch(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.MEDICINE));
        } else {
            chargeSheetPrintable.setMedicineTag(
                    ChargeUtils.getChargeSheetItemsFilterMaterialAndPrescriptionExternalAndCompose(chargeSheet)
                            .stream()
                            .filter(chargeFormItem -> chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.CHARGED || chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.UNCHARGED)
                            .anyMatch(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.MEDICINE));
        }
        chargeSheetPrintable.setPatientTag(!StringUtils.isEmpty(chargeSheet.getPatientId()) && !chargeSheet.getPatientId().equals(Constants.ANONYMOUS_PATIENT_ID));

        chargeSheetPrintable.setExecuteTransfusionSheet(ChargeUtils.getChargeSheetItemsFilterMaterialAndPrescriptionExternal(chargeSheet)
                .stream()
                .filter(chargeFormItem -> chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.CHARGED || chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.UNCHARGED)
                .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.MEDICINE || chargeFormItem.getProductType() == Constants.ProductType.MATERIAL || chargeFormItem.getProductType() == Constants.ProductType.SALE_PRODUCT)
                .anyMatch(chargeFormItem -> ChargeUtils.isTransfusionExecutableItem(chargeFormItem, finalIncludeExternal)));

        // 打印治疗理疗单需要包含其他费用类型
        boolean finalContainProductOtherPrice = containProductOtherPrice;
        boolean finalContainProductMaterialsPrice = containProductMaterialsPrice;
        boolean executeTherapySheet = chargeSheet.getChargeForms()
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == YesOrNo.NO)
                .filter(chargeForm -> chargeForm.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_EXTERNAL
                        && chargeForm.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_CHINESE
                        && chargeForm.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_WESTERN
                        && chargeForm.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_INFUSION
                )
                .filter(chargeForm -> org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeForm.getChargeFormItems()))
                .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                .filter(item -> item.getIsDeleted() == YesOrNo.NO)
                .filter(chargeFormItem -> chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.CHARGED || chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.UNCHARGED)
                .filter(chargeFormItem -> chargeFormItem.getGoodsFeeType() != GoodsFeeType.FEE_CHILD)
                .anyMatch(chargeFormItem -> ChargeUtils.getNeedExecuteTherapySheetPredicate(finalContainProductOtherPrice, finalContainProductMaterialsPrice).test(chargeFormItem));

        // 判断治疗理疗单是否包含外治处方
        executeTherapySheet = executeTherapySheet || ChargeUtils.getChargeSheetForms(chargeSheet, Constants.SourceFormType.PRESCRIPTION_EXTERNAL)
                .stream()
                .filter(chargeForm -> org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeForm.getChargeFormItems()))
                .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                .filter(item -> item.getIsDeleted() == YesOrNo.NO)
                .filter(chargeFormItem -> chargeFormItem.getGoodsFeeType() != GoodsFeeType.FEE_CHILD)
                .anyMatch(chargeFormItem -> chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.CHARGED || chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.UNCHARGED);

        chargeSheetPrintable.setExecuteTherapySheet(executeTherapySheet);

        chargeSheetPrintable.setExamination(ChargeUtils.getChargeSheetItems(chargeSheet)
                .stream()
                .filter(chargeFormItem -> chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.CHARGED || chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.UNCHARGED)
                .anyMatch(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.EXAMINATION));
        chargeSheetPrintable.setExaminationExamination(ChargeUtils.getChargeSheetItems(chargeSheet)
                .stream()
                .filter(chargeFormItem -> chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.CHARGED || chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.UNCHARGED)
                .anyMatch(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.EXAMINATION && chargeFormItem.getProductSubType() == Constants.ProductType.SubType.EXAMINATION_EXAMINATION));
        chargeSheetPrintable.setExaminationInspection(ChargeUtils.getChargeSheetItems(chargeSheet)
                .stream()
                .filter(chargeFormItem -> chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.CHARGED || chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.UNCHARGED)
                .anyMatch(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.EXAMINATION && chargeFormItem.getProductSubType() == Constants.ProductType.SubType.EXAMINATION_INSPECTION));
        chargeSheetPrintable.setExaminationExaminationBarCode(chargeSheetPrintable.isExaminationExamination());
        return chargeSheetPrintable;
    }

    private static void sortChargeFormViews(ChargeSheetView chargeSheetView) {
        if (chargeSheetView.getChargeForms() == null) {
            return;
        }
        chargeSheetView.getChargeForms().sort((a, b) -> {
            if (ObjectUtils.compare(a.getSourceFormType(), b.getSourceFormType()) != 0) {
                return ObjectUtils.compare(a.getSourceFormType(), b.getSourceFormType());
            } else {
                return ObjectUtils.compare(a.getSort(), b.getSort());
            }
        });
        chargeSheetView.getChargeForms().stream()
                .filter(chargeFormView -> chargeFormView.getChargeFormItems() != null)
                .forEach(chargeFormView -> chargeFormView.getChargeFormItems().sort((a, b) -> ObjectUtils.compare(a.getSort(), b.getSort())));
    }

    private static void sortChargeFormViews(ChargeSheetForHospitalView chargeSheetForHospitalView) {
        if (chargeSheetForHospitalView.getChargeForms() == null) {
            return;
        }

        chargeSheetForHospitalView.getChargeForms().sort((a, b) -> {
            if (ObjectUtils.compare(a.getSourceFormType(), b.getSourceFormType()) != 0) {
                return ObjectUtils.compare(a.getSourceFormType(), b.getSourceFormType());
            } else {
                return ObjectUtils.compare(a.getSort(), b.getSort());
            }
        });

        chargeSheetForHospitalView.getChargeForms().stream()
                .filter(chargeFormView -> chargeFormView.getChargeFormItems() != null)
                .forEach(chargeFormView -> chargeFormView.getChargeFormItems().sort((a, b) -> ObjectUtils.compare(a.getSort(), b.getSort())));
    }

    public static List<ChargeTransactionView> generateChargeTransactionViews(ChargeSheet chargeSheet, List<ChargeOweSheet> chargeOweSheets, boolean withRefundTransaction, boolean withAllTransaction, SheetProcessorInfoProvider infoProvider) {

        if (Objects.isNull(chargeSheet)) {
            return new ArrayList<>();
        }

        ChargeTransactionAndActionDto transactionAndActionDto = getChargeTransactionAndChargeActionContainOwe(chargeSheet, chargeOweSheets);

        return generateChargeTransactionViews(transactionAndActionDto.getChargeTransactions(),
                transactionAndActionDto.getChargeActions(),
                transactionAndActionDto.getChangePayModeActions(),
                withRefundTransaction,
                withAllTransaction,
                infoProvider);
    }

    public static ChargeTransaction convertToChargeTransaction(ChargeOweCombineTransactionRecord transactionRecord) {

        if (Objects.isNull(transactionRecord)) {
            return null;
        }

        ChargeTransaction chargeTransaction = new ChargeTransaction();
        chargeTransaction.setId(transactionRecord.getId().toString())
                .setChainId(transactionRecord.getChainId())
                .setClinicId(transactionRecord.getClinicId())
                .setChargeActionId(transactionRecord.getId().toString())
                .setChargeSheetId(transactionRecord.getOweSheetId().toString())
                .setPayMode(transactionRecord.getPayMode())
                .setPaySubMode(transactionRecord.getPaySubMode())
                .setAmount(transactionRecord.getAmount())
                .setRefundedAmount(transactionRecord.getRefundedAmount())
                .setPresentAmount(transactionRecord.getPresentAmount())
                .setPrincipalAmount(transactionRecord.getPrincipalAmount())
                .setChargeType(ChargeTransaction.ChargeType.OWE)
                .setCreated(transactionRecord.getCreated());
        Optional.ofNullable(transactionRecord.getOweCombineTransaction())
                .ifPresent(oweCombineTransaction -> chargeTransaction.setThirdPartyPayCardId(oweCombineTransaction.getThirdPartyPayCardId())
                        .setThirdPartyPayTransactionId(oweCombineTransaction.getThirdPartyPayTransactionId())
                        .setThirdPartyPayCardBalance(oweCombineTransaction.getThirdPartyPayCardBalance())
                        .setThirdPartyPayInfo(oweCombineTransaction.getThirdPartyPayInfo())
                        .setThirdPartyPayInfoJson(JsonUtils.dump(oweCombineTransaction.getThirdPartyPayInfo())));
        return chargeTransaction;
    }

    private static ChargeAction convertToChargeAction(ChargeOweCombineTransactionRecord transactionRecord) {

        if (Objects.isNull(transactionRecord)) {
            return null;
        }

        int type = 0;

        if (transactionRecord.getType() == ChargeCombineOrderTransaction.Type.PAY) {
            type = ChargeAction.Type.REPAYMENT;
        } else if (transactionRecord.getType() == ChargeCombineOrderTransaction.Type.REFUND) {
            type = ChargeAction.Type.REFUND;
        }

        ChargeAction.PayActionInfo payActionInfo = new ChargeAction.PayActionInfo();
        payActionInfo.setPayMode(transactionRecord.getPayMode());

        //这里做兼容，将payModeDisplayName拆分成原始的payModeName和paySubModeName
        ChargePayModeUtils.PayModeNameDto payModeNameDto = ChargePayModeUtils.splitHistoryPayModeDisplayName(transactionRecord.getPayMode(), transactionRecord.getPayModeDisplayName());

        payActionInfo.setPayModeName(payModeNameDto.getPayModeName());
        payActionInfo.setPaySubMode(transactionRecord.getPaySubMode());
        payActionInfo.setPaySubModeName(payModeNameDto.getPaySubModeName());
        payActionInfo.setAmount(transactionRecord.getAmount());
        ChargeAction chargeAction = new ChargeAction();

        Optional.ofNullable(transactionRecord.getOweCombineTransaction())
                .ifPresent(oweCombineTransaction -> {
                    chargeAction.setChargeComment(oweCombineTransaction.getChargeComment());
                    payActionInfo.setThirdPartyPayCardId(oweCombineTransaction.getThirdPartyPayCardId());
                });
        List<ChargeAction.PayActionInfo> payActionInfos = Arrays.asList(payActionInfo);

        chargeAction.setId(transactionRecord.getId().toString())
                .setChainId(transactionRecord.getChainId())
                .setClinicId(transactionRecord.getClinicId())
                .setType(type)
                .setPayStatus(PayStatus.SUCCESS)
                .setAmount(transactionRecord.getAmount())
                .setPayActionInfos(payActionInfos)
                .setPayActionInfoJson(JsonUtils.dump(payActionInfos))
                .setCreated(transactionRecord.getCreated())
                .setCreatedBy(transactionRecord.getCreatedBy());

        return chargeAction;
    }

    public static List<ChargeTransactionView> generateChargeTransactionViews(List<ChargeTransaction> chargeTransactions,
                                                                             List<ChargeAction> chargeActions,
                                                                             List<ChargeAction> changePayModeActions,
                                                                             boolean withRefundTransaction,
                                                                             boolean withAllTransaction,
                                                                             SheetProcessorInfoProvider infoProvider) {
        if (chargeTransactions == null || chargeTransactions.size() == 0) {
            return new ArrayList<>();
        }

        List<String> refundChargeActionIds = chargeActions.stream()
                .filter(chargeAction -> chargeAction.getType() == ChargeAction.Type.REFUND)
                .filter(chargeAction -> chargeAction.getPayStatus() == PayStatus.SUCCESS)
                .map(ChargeAction::getId)
                .collect(Collectors.toList());

        List<String> refundedZeroTransactionIds = chargeTransactions.stream()
                .filter(chargeTransaction -> MathUtils.wrapBigDecimalCompare(chargeTransaction.getAmount(), BigDecimal.ZERO) == 0)
                .filter(chargeTransaction -> refundChargeActionIds.contains(chargeTransaction.getChargeActionId()))
                .map(ChargeTransaction::getId)
                .collect(Collectors.toList());

        List<String> isRefundedZeroPayTransactionIds = chargeTransactions.stream()
                .filter(chargeTransaction -> org.apache.commons.lang3.StringUtils.isNotEmpty(chargeTransaction.getAssociateTransactionId()))
                .filter(chargeTransaction -> MathUtils.wrapBigDecimalCompare(chargeTransaction.getAmount(), BigDecimal.ZERO) == 0)
                .map(chargeTransaction -> chargeTransaction.getAssociateTransactionId())
                .distinct()
                .collect(Collectors.toList());

        //这里是为了兼容历史数据，有修改支付方式的记录，获取支付方式名称时需要通过action获取
        List<ChargeAction> payModeNameActions = new ArrayList<>();
        Optional.ofNullable(chargeActions)
                .ifPresent(payModeNameActions::addAll);
        Optional.ofNullable(changePayModeActions)
                .ifPresent(payModeNameActions::addAll);

        Map<Long, Integer> payModeMaps = getPayModeTypes(infoProvider, chargeTransactions.get(0).getChainId());
        Map<String, ChargeAction.PayActionInfo> payModeNameMap = ChargeUtils.generatePayModeNameMap(payModeNameActions, payModeMaps);

        List<ChargeTransactionView> chargeTransactionViews = chargeTransactions.stream()
                .sorted((a, b) -> ObjectUtils.compare(a.getCreated(), b.getCreated()))
                .filter(chargeTransaction -> {
                    if (withRefundTransaction) {
                        return true;
                    } else {
                        return chargeTransaction.getAmount().compareTo(BigDecimal.ZERO) >= 0;
                    }
                })
                .map(chargeTransaction -> {
                    if (!withAllTransaction && chargeTransaction.getIsPaidback() == 1) {
                        return null;
                    }

                    ChargeTransactionView chargeTransactionView = new ChargeTransactionView();
                    chargeTransaction.setPayModeType(payModeMaps.getOrDefault(Long.valueOf(chargeTransaction.getPayMode()), Constants.ChargePayModeType.UN_KNOW));
                    BeanUtils.copyProperties(chargeTransaction, chargeTransactionView);
                    String payModeNameKey = ChargeUtils.generateTransactionUniqueKey(chargeTransaction);

                    if (chargeTransaction.getPayMode() == Constants.ChargePayMode.ABC_PAY || chargeTransaction.getPayModeType() == Constants.ChargePayModeType.THIRD_PARTY_COMMON_PAY) {
                        String abcPayKey = String.format("%d-%d", chargeTransaction.getPayMode(), chargeTransaction.getPaySubMode());
                        ChargeAction.PayActionInfo payActionInfo = payModeNameMap.getOrDefault(abcPayKey, new ChargeAction.PayActionInfo());
                        chargeTransactionView.setPayModeName(payActionInfo.getPayModeName());
                        chargeTransactionView.setPaySubModeName(payActionInfo.getPaySubModeName());
                        chargeTransactionView.setChangePayModeName(payActionInfo.getPayModeName());
                    } else {
                        chargeTransactionView.setPayModeName(payModeNameMap.getOrDefault(payModeNameKey, new ChargeAction.PayActionInfo()).getPayModeName());
                        chargeTransactionView.setPaySubModeName(payModeNameMap.getOrDefault(payModeNameKey, new ChargeAction.PayActionInfo()).getPaySubModeName());
                        chargeTransactionView.setChangePayModeName(payModeNameMap.getOrDefault(String.valueOf(chargeTransactionView.getChangePayMode()), new ChargeAction.PayActionInfo()).getPayModeName());
                    }

                    if (chargeTransactionView.getAmount().compareTo(BigDecimal.ZERO) < 0) {
                        chargeTransactionView.setIsRefunded(1);
                    } else {
                        chargeTransactionView.setIsRefunded(MathUtils.wrapBigDecimalAdd(chargeTransaction.getRefundedAmount(), chargeTransaction.getAmount()).compareTo(BigDecimal.ZERO) <= 0 ? 1 : 0);
                    }

                    if (MathUtils.wrapBigDecimalCompare(chargeTransactionView.getAmount(), BigDecimal.ZERO) == 0) {
                        if (isRefundedZeroPayTransactionIds.contains(chargeTransactionView.getId()) || refundedZeroTransactionIds.contains(chargeTransactionView.getId())) {
                            chargeTransactionView.setIsRefunded(1);
                        } else {
                            chargeTransactionView.setIsRefunded(0);
                        }
                    }
                    chargeTransactionView.setThirdPartyPayInfo(JsonUtils.readValue(chargeTransaction.getThirdPartyPayInfoJson(), ThirdPartyPayInfo.class));
                    return chargeTransactionView;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return chargeTransactionViews;
    }

    public static List<ChargeAction> generateChargeAction(ChargeSheet chargeSheet, List<ChargeOweSheet> chargeOweSheets) {

        if (Objects.isNull(chargeSheet)) {
            return new ArrayList<>();
        }

        ChargeTransactionAndActionDto transactionAndActionDto = getChargeTransactionAndChargeActionContainOwe(chargeSheet, chargeOweSheets);
        return filterNoDisplayChargeActions(transactionAndActionDto.getChargeActions());
    }

    public static List<ChargeAction> filterNoDisplayChargeActions(List<ChargeAction> chargeActions) {
        if (chargeActions == null || chargeActions.size() == 0) {
            return chargeActions;
        }

        chargeActions = chargeActions.stream()
                .filter(chargeAction -> chargeAction.getPayStatus() == PayStatus.SUCCESS)
                .collect(Collectors.toList());
        return chargeActions;
    }

    public static RegistrationChargeSheetView toRegistrationChargeSheetView(SheetProcessor sheetProcessor) {
        //初始化
        ChargeSheet chargeSheet = sheetProcessor.chargeSheet;
        SheetProcessorInfoProvider sheetProcessorInfoProvider = sheetProcessor.sheetProcessorInfoProvider;
        PatientInfo patientInfo = sheetProcessor.getPatientInfo();
        List<FormProcessor> formProcessors = sheetProcessor.formProcessors;
        List<ProcessInfoView> processInfoViews = sheetProcessor.processInfoViews;
        MemberInfo memberInfo = sheetProcessor.memberInfo;
        List<PromotionView> availablePromotionViews = sheetProcessor.availablePromotionViews;
        List<GiftRulePromotionView> availableGiftRulePromotionViews = sheetProcessor.availableGiftRulePromotionViews;
        List<CouponPromotionView> availableCouponPromotionViews = sheetProcessor.availableCouponPromotionViews;
        List<PatientCardPromotionView> availablePatientCardPromotionViews = sheetProcessor.availablePatientCardPromotionViews;
        TherapySheet therapySheet = sheetProcessor.therapySheet;
        MedicalRecord medicalRecord = sheetProcessor.medicalRecord;
        PatientOrder patientOrder = sheetProcessor.patientOrder;
        int dispensingStatus = sheetProcessor.dispensingStatus;
        Instant dispensedTime = sheetProcessor.dispensedTime;
        String memberId = sheetProcessor.memberId;
        String dispensedBy = sheetProcessor.dispensedBy;
        PatientPointsInfoView patientPointsInfoView = sheetProcessor.patientPointsInfoView;

        RegistrationChargeSheetView registrationChargeSheetView = new RegistrationChargeSheetView();
        BeanUtils.copyProperties(chargeSheet, registrationChargeSheetView, "chargeForms");

        if (chargeSheet.getAdditional() != null) {
            registrationChargeSheetView.setInvoiceStatus(chargeSheet.getAdditional().getInvoiceStatus());
        }

        List<ChargeFormView> chargeFormViews = formProcessors.stream().map(formProcessor -> ChargeFormProtocol.generateChargeFormView(formProcessor, false, chargeSheet.getStatus())).collect(Collectors.toList());
        registrationChargeSheetView.setChargeForms(chargeFormViews);
        registrationChargeSheetView.setStatusName(StatusNameTranslator.translateChargeSheetStatus(registrationChargeSheetView.getOwedStatus(), registrationChargeSheetView.getStatus(), false, null));
        registrationChargeSheetView.setMemberInfo(memberInfo);
        if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED) {
            registrationChargeSheetView.setPromotions(availablePromotionViews);
            registrationChargeSheetView.setCouponPromotions(Optional.ofNullable(availableCouponPromotionViews).orElse(new ArrayList<>()).stream().sorted(Comparator.comparingInt(CouponPromotionView::getIsCanBeUsed).reversed()).collect(Collectors.toList()));
            registrationChargeSheetView.setGiftRulePromotions(Optional.ofNullable(availableGiftRulePromotionViews).orElse(new ArrayList<>()).stream().sorted(Comparator.comparingInt(GiftRulePromotionView::getIsCanBeUsed).reversed()).collect(Collectors.toList()));
            registrationChargeSheetView.setPatientPointsInfo(patientPointsInfoView);
            registrationChargeSheetView.setPatientCardPromotions(availablePatientCardPromotionViews);
        } else if (chargeSheet.getType() == ChargeSheet.Type.REGISTRATION) {
            registrationChargeSheetView.setPatientPointsInfo(DTOConverter.convertToPatientPointsInfoView(chargeSheet.getPatientPointsPromotionInfo()));
            SheetPromotionInfo sheetPromotionInfo = JsonUtils.readValue(chargeSheet.getPromotionInfoJson(), SheetPromotionInfo.class);
            registrationChargeSheetView.setPromotions(sheetPromotionInfo != null ? sheetPromotionInfo.getPromotions() : null);
            List<CouponPromotionView> couponPromotionViews = null;
            if (!CollectionUtils.isEmpty(chargeSheet.getCouponPromotionInfos())) {
                couponPromotionViews = chargeSheet.getCouponPromotionInfos().stream()
                        .filter(chargeCouponPromotionInfo -> chargeCouponPromotionInfo.getIsDeleted() == 0)
                        .map(DTOConverter::convertToCouponPromotionView)
                        .filter(couponPromotionView -> couponPromotionView.getChecked())
                        .collect(Collectors.toList());
            }
            registrationChargeSheetView.setCouponPromotions(couponPromotionViews);

            List<GiftRulePromotionView> giftRulePromotionViews = null;
            if (!CollectionUtils.isEmpty(chargeSheet.getGiftRulePromotionInfos())) {
                giftRulePromotionViews = chargeSheet.getGiftRulePromotionInfos().stream()
                        .filter(giftRulePromotionInfo -> giftRulePromotionInfo.getIsDeleted() == 0)
                        .map(DTOConverter::convertToGiftRulePromotionView)
                        .filter(giftRulePromotionView -> giftRulePromotionView.getChecked())
                        .collect(Collectors.toList());
            }
            registrationChargeSheetView.setGiftRulePromotions(giftRulePromotionViews);
            registrationChargeSheetView.setPatientCardPromotions(Optional.ofNullable(chargeSheet.getPatientCardPromotionInfos()).orElse(new ArrayList<>())
                    .stream()
                    .filter(patientCardPromotionInfo -> patientCardPromotionInfo.getIsDeleted() == 0)
                    .map(PatientCardPromotionView::ofPatientCardPromotionView)
                    .collect(Collectors.toList())
            );
        }

        if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.CHARGED
                || chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_REFUNDED
                || chargeSheet.getStatus() == Constants.ChargeSheetStatus.REFUNDED) {
            Instant firstChargedTime = Optional.ofNullable(chargeSheet.getFirstChargedTime()).orElse(chargeSheet.getCreated());
            Instant canOperateTime = LocalDateTime.ofInstant(firstChargedTime, ZoneId.systemDefault()).plusYears(Constants.CHARGE_SHEET_CAN_OPERATE_YEAR).toInstant(ZoneOffset.of("+8"));

            if (canOperateTime.compareTo(Instant.now()) < 0) {
                registrationChargeSheetView.setIsDisabledOperate(1);
                registrationChargeSheetView.setDisabledOperateReason(ChargeServiceError.CHARGE_SHEET_ONE_YEAR_AGO.getMessage());
            }
        }

        registrationChargeSheetView.setDataSignature(ChargeUtils.sign(chargeSheet));
        registrationChargeSheetView.setMemberId(memberId);
        registrationChargeSheetView.setUseMemberFlag(chargeSheet.getUseMemberFlag());
        if (chargeSheet.getType() == ChargeSheet.Type.REGISTRATION) {
            //查询收费单对应的欠费数据
            List<ChargeOweSheet> chargeOweSheets = Optional.ofNullable(sheetProcessorInfoProvider).map(SheetProcessorInfoProvider::getChargeOweSheetProvider)
                    .map(chargeOweSheetProvider -> chargeOweSheetProvider.findChargeOweSheetsByChargeSheetId(chargeSheet.getClinicId(), chargeSheet.getId())).orElse(new ArrayList<>());

            registrationChargeSheetView.setChargeTransactions(generateChargeTransactionViews(chargeSheet, chargeOweSheets, false, false, sheetProcessorInfoProvider));
            registrationChargeSheetView.setChargeActions(generateChargeAction(chargeSheet, chargeOweSheets));
            registrationChargeSheetView.setChargeSheetSummary(toSummaryView(sheetProcessor, chargeOweSheets));
            registrationChargeSheetView.setIsOnlyPaidByAbcPayMode(generateIsOnlyPaidByAbcPayMode(chargeSheet));
        } else {
            List<ChargeTransactionView> chargeTransactionViews = new ArrayList<>();
            if (!CollectionUtils.isEmpty(chargeSheet.getChargeTransactions())) {
                Map<Long, Integer> payModeMaps = getPayModeTypes(sheetProcessorInfoProvider, chargeSheet.getChainId());
                Map<String, ChargeAction.PayActionInfo> payModeNameMap = ChargeUtils.generatePayModeNameMap(chargeSheet.getChargeActions(), payModeMaps);

                chargeSheet.getChargeTransactions().forEach(chargeTransaction -> {
                    chargeTransaction.setPayModeType(payModeMaps.getOrDefault(Long.valueOf(chargeTransaction.getPayMode()), Constants.ChargePayModeType.UN_KNOW));
                    ChargeTransactionView chargeTransactionView = new ChargeTransactionView();
                    BeanUtils.copyProperties(chargeTransaction, chargeTransactionView);

                    String payModeNameKey = ChargeUtils.generateTransactionUniqueKey(chargeTransaction);

                    if (chargeTransaction.getPayMode() == Constants.ChargePayMode.ABC_PAY || chargeTransaction.getPayModeType() == Constants.ChargePayModeType.THIRD_PARTY_COMMON_PAY) {
                        String abcPayKey = String.format("%d-%d", chargeTransaction.getPayMode(), chargeTransaction.getPaySubMode());
                        ChargeAction.PayActionInfo payActionInfo = payModeNameMap.getOrDefault(abcPayKey, new ChargeAction.PayActionInfo());
                        chargeTransactionView.setPayModeName(payActionInfo.getPayModeName());
                        chargeTransactionView.setPaySubModeName(payActionInfo.getPaySubModeName());
                        chargeTransactionView.setChangePayModeName(payActionInfo.getPayModeName());
                    } else {
                        chargeTransactionView.setPayModeName(payModeNameMap.getOrDefault(payModeNameKey, new ChargeAction.PayActionInfo()).getPayModeName());
                        chargeTransactionView.setPaySubModeName(payModeNameMap.getOrDefault(payModeNameKey, new ChargeAction.PayActionInfo()).getPaySubModeName());
                        chargeTransactionView.setChangePayModeName(payModeNameMap.getOrDefault(String.valueOf(chargeTransactionView.getChangePayMode()), new ChargeAction.PayActionInfo()).getPayModeName());
                    }
                    chargeTransactionViews.add(chargeTransactionView);
                });
            }
            registrationChargeSheetView.setChargeTransactions(chargeTransactionViews);
            ChargeSheetSummary chargeSheetSummary = new ChargeSheetSummary();
            ChargeFormItem registrationChargeFormItem = ChargeUtils.getChargeSheetItems(chargeSheet)
                    .stream()
                    .filter(chargeFormItem -> chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.REFUNDED)
                    .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.REGISTRATION)
                    .findFirst().orElse(null);

            if (registrationChargeFormItem != null) {
                chargeSheetSummary.setTotalFee(registrationChargeFormItem.getTotalPrice());
                chargeSheetSummary.setDiscountFee(registrationChargeFormItem.getDiscountPrice());
                chargeSheetSummary.setReceivableFee(MathUtils.wrapBigDecimalAdd(registrationChargeFormItem.getTotalPrice(), registrationChargeFormItem.getDiscountPrice()));
                chargeSheetSummary.setReceivedFee(registrationChargeFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED ? chargeSheetSummary.getReceivableFee() : BigDecimal.ZERO);
            }
            registrationChargeSheetView.setChargeSheetSummary(chargeSheetSummary);
        }

        if (registrationChargeSheetView.getChargeActions() == null) {
            registrationChargeSheetView.setChargeActions(new ArrayList<>());
        }
        updateChargeActionCreateByNameAndSort(sheetProcessorInfoProvider, registrationChargeSheetView.getChargeActions());

        if (patientOrder != null) {
            registrationChargeSheetView.setSource(patientOrder.getSource());
        }

        return registrationChargeSheetView;
    }

    public static RegistrationChargeSheetView toRegistrationChargeSheetView(PharmacySheetProcessor sheetProcessor) {
        //初始化
        ChargeSheet chargeSheet = sheetProcessor.chargeSheet;
        SheetProcessorInfoProvider sheetProcessorInfoProvider = sheetProcessor.sheetProcessorInfoProvider;
        PatientInfo patientInfo = sheetProcessor.getPatientInfo();
        List<PharmacyFormProcessor> formProcessors = sheetProcessor.formProcessors;
        List<ProcessInfoView> processInfoViews = sheetProcessor.processInfoViews;
        MemberInfo memberInfo = sheetProcessor.memberInfo;
        List<PromotionView> availablePromotionViews = sheetProcessor.availablePromotionViews;
        List<GiftRulePromotionView> availableGiftRulePromotionViews = sheetProcessor.availableGiftRulePromotionViews;
        List<CouponPromotionView> availableCouponPromotionViews = sheetProcessor.availableCouponPromotionViews;
        List<PatientCardPromotionView> availablePatientCardPromotionViews = sheetProcessor.availablePatientCardPromotionViews;
        TherapySheet therapySheet = sheetProcessor.therapySheet;
        MedicalRecord medicalRecord = sheetProcessor.medicalRecord;
        PatientOrder patientOrder = sheetProcessor.patientOrder;
        int dispensingStatus = sheetProcessor.dispensingStatus;
        Instant dispensedTime = sheetProcessor.dispensedTime;
        String memberId = sheetProcessor.memberId;
        String dispensedBy = sheetProcessor.dispensedBy;
        PatientPointsInfoView patientPointsInfoView = sheetProcessor.patientPointsInfoView;

        RegistrationChargeSheetView registrationChargeSheetView = new RegistrationChargeSheetView();
        BeanUtils.copyProperties(chargeSheet, registrationChargeSheetView, "chargeForms");

        if (chargeSheet.getAdditional() != null) {
            registrationChargeSheetView.setInvoiceStatus(chargeSheet.getAdditional().getInvoiceStatus());
        }

        List<ChargeFormView> chargeFormViews = formProcessors.stream().map(formProcessor -> ChargeFormProtocol.generateChargeFormView(formProcessor, false, chargeSheet.getStatus())).collect(Collectors.toList());
        registrationChargeSheetView.setChargeForms(chargeFormViews);
        registrationChargeSheetView.setStatusName(StatusNameTranslator.translateChargeSheetStatus(registrationChargeSheetView.getOwedStatus(), registrationChargeSheetView.getStatus(), false, null));
        registrationChargeSheetView.setMemberInfo(memberInfo);
        if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED) {
            registrationChargeSheetView.setPromotions(availablePromotionViews);
            registrationChargeSheetView.setCouponPromotions(Optional.ofNullable(availableCouponPromotionViews).orElse(new ArrayList<>()).stream().sorted(Comparator.comparingInt(CouponPromotionView::getIsCanBeUsed).reversed()).collect(Collectors.toList()));
            registrationChargeSheetView.setGiftRulePromotions(Optional.ofNullable(availableGiftRulePromotionViews).orElse(new ArrayList<>()).stream().sorted(Comparator.comparingInt(GiftRulePromotionView::getIsCanBeUsed).reversed()).collect(Collectors.toList()));
            registrationChargeSheetView.setPatientPointsInfo(patientPointsInfoView);
            registrationChargeSheetView.setPatientCardPromotions(availablePatientCardPromotionViews);
        } else if (chargeSheet.getType() == ChargeSheet.Type.REGISTRATION) {
            registrationChargeSheetView.setPatientPointsInfo(DTOConverter.convertToPatientPointsInfoView(chargeSheet.getPatientPointsPromotionInfo()));
            SheetPromotionInfo sheetPromotionInfo = JsonUtils.readValue(chargeSheet.getPromotionInfoJson(), SheetPromotionInfo.class);
            registrationChargeSheetView.setPromotions(sheetPromotionInfo != null ? sheetPromotionInfo.getPromotions() : null);
            List<CouponPromotionView> couponPromotionViews = null;
            if (!CollectionUtils.isEmpty(chargeSheet.getCouponPromotionInfos())) {
                couponPromotionViews = chargeSheet.getCouponPromotionInfos().stream()
                        .filter(chargeCouponPromotionInfo -> chargeCouponPromotionInfo.getIsDeleted() == 0)
                        .map(DTOConverter::convertToCouponPromotionView)
                        .filter(couponPromotionView -> couponPromotionView.getChecked())
                        .collect(Collectors.toList());
            }
            registrationChargeSheetView.setCouponPromotions(couponPromotionViews);

            List<GiftRulePromotionView> giftRulePromotionViews = null;
            if (!CollectionUtils.isEmpty(chargeSheet.getGiftRulePromotionInfos())) {
                giftRulePromotionViews = chargeSheet.getGiftRulePromotionInfos().stream()
                        .filter(giftRulePromotionInfo -> giftRulePromotionInfo.getIsDeleted() == 0)
                        .map(DTOConverter::convertToGiftRulePromotionView)
                        .filter(giftRulePromotionView -> giftRulePromotionView.getChecked())
                        .collect(Collectors.toList());
            }
            registrationChargeSheetView.setGiftRulePromotions(giftRulePromotionViews);
            registrationChargeSheetView.setPatientCardPromotions(Optional.ofNullable(chargeSheet.getPatientCardPromotionInfos()).orElse(new ArrayList<>())
                    .stream()
                    .filter(patientCardPromotionInfo -> patientCardPromotionInfo.getIsDeleted() == 0)
                    .map(PatientCardPromotionView::ofPatientCardPromotionView)
                    .collect(Collectors.toList())
            );
        }

        if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.CHARGED
                || chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_REFUNDED
                || chargeSheet.getStatus() == Constants.ChargeSheetStatus.REFUNDED) {
            Instant firstChargedTime = Optional.ofNullable(chargeSheet.getFirstChargedTime()).orElse(chargeSheet.getCreated());
            Instant canOperateTime = LocalDateTime.ofInstant(firstChargedTime, ZoneId.systemDefault()).plusYears(Constants.CHARGE_SHEET_CAN_OPERATE_YEAR).toInstant(ZoneOffset.of("+8"));

            if (canOperateTime.compareTo(Instant.now()) < 0) {
                registrationChargeSheetView.setIsDisabledOperate(1);
                registrationChargeSheetView.setDisabledOperateReason(ChargeServiceError.CHARGE_SHEET_ONE_YEAR_AGO.getMessage());
            }
        }

        registrationChargeSheetView.setDataSignature(ChargeUtils.sign(chargeSheet));
        registrationChargeSheetView.setMemberId(memberId);
        registrationChargeSheetView.setUseMemberFlag(chargeSheet.getUseMemberFlag());
        if (chargeSheet.getType() == ChargeSheet.Type.REGISTRATION) {
            //查询收费单对应的欠费数据
            List<ChargeOweSheet> chargeOweSheets = Optional.ofNullable(sheetProcessorInfoProvider).map(SheetProcessorInfoProvider::getChargeOweSheetProvider)
                    .map(chargeOweSheetProvider -> chargeOweSheetProvider.findChargeOweSheetsByChargeSheetId(chargeSheet.getClinicId(), chargeSheet.getId())).orElse(new ArrayList<>());

            registrationChargeSheetView.setChargeTransactions(generateChargeTransactionViews(chargeSheet, chargeOweSheets, false, false, sheetProcessorInfoProvider));
            registrationChargeSheetView.setChargeActions(generateChargeAction(chargeSheet, chargeOweSheets));
            registrationChargeSheetView.setChargeSheetSummary(toPharmacySummaryView(sheetProcessor, chargeOweSheets));
            registrationChargeSheetView.setIsOnlyPaidByAbcPayMode(generateIsOnlyPaidByAbcPayMode(chargeSheet));
        } else {
            List<ChargeTransactionView> chargeTransactionViews = new ArrayList<>();
            if (!CollectionUtils.isEmpty(chargeSheet.getChargeTransactions())) {
                Map<Long, Integer> payModeMaps = getPayModeTypes(sheetProcessorInfoProvider, chargeSheet.getChainId());
                Map<String, ChargeAction.PayActionInfo> payModeNameMap = ChargeUtils.generatePayModeNameMap(chargeSheet.getChargeActions(), payModeMaps);

                chargeSheet.getChargeTransactions().forEach(chargeTransaction -> {
                    ChargeTransactionView chargeTransactionView = new ChargeTransactionView();
                    BeanUtils.copyProperties(chargeTransaction, chargeTransactionView);

                    String payModeNameKey = ChargeUtils.generateTransactionUniqueKey(chargeTransaction);

                    if (chargeTransaction.getPayMode() == Constants.ChargePayMode.ABC_PAY) {
                        String abcPayKey = String.format("%d-%d", chargeTransaction.getPayMode(), chargeTransaction.getPaySubMode());
                        ChargeAction.PayActionInfo payActionInfo = payModeNameMap.getOrDefault(abcPayKey, new ChargeAction.PayActionInfo());
                        chargeTransactionView.setPayModeName(payActionInfo.getPayModeName());
                        chargeTransactionView.setPaySubModeName(payActionInfo.getPaySubModeName());
                        chargeTransactionView.setChangePayModeName(payActionInfo.getPayModeName());
                    } else {
                        chargeTransactionView.setPayModeName(payModeNameMap.getOrDefault(payModeNameKey, new ChargeAction.PayActionInfo()).getPayModeName());
                        chargeTransactionView.setPaySubModeName(payModeNameMap.getOrDefault(payModeNameKey, new ChargeAction.PayActionInfo()).getPaySubModeName());
                        chargeTransactionView.setChangePayModeName(payModeNameMap.getOrDefault(String.valueOf(chargeTransactionView.getChangePayMode()), new ChargeAction.PayActionInfo()).getPayModeName());
                    }
                    chargeTransactionViews.add(chargeTransactionView);
                });
            }
            registrationChargeSheetView.setChargeTransactions(chargeTransactionViews);
            ChargeSheetSummary chargeSheetSummary = new ChargeSheetSummary();
            ChargeFormItem registrationChargeFormItem = ChargeUtils.getChargeSheetItems(chargeSheet)
                    .stream()
                    .filter(chargeFormItem -> chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.REFUNDED)
                    .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.REGISTRATION)
                    .findFirst().orElse(null);

            if (registrationChargeFormItem != null) {
                chargeSheetSummary.setTotalFee(registrationChargeFormItem.getTotalPrice());
                chargeSheetSummary.setDiscountFee(registrationChargeFormItem.getDiscountPrice());
                chargeSheetSummary.setReceivableFee(MathUtils.wrapBigDecimalAdd(registrationChargeFormItem.getTotalPrice(), registrationChargeFormItem.getDiscountPrice()));
                chargeSheetSummary.setReceivedFee(registrationChargeFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED ? chargeSheetSummary.getReceivableFee() : BigDecimal.ZERO);
            }
            registrationChargeSheetView.setChargeSheetSummary(chargeSheetSummary);
        }

        if (registrationChargeSheetView.getChargeActions() == null) {
            registrationChargeSheetView.setChargeActions(new ArrayList<>());
        }
        updateChargeActionCreateByNameAndSort(sheetProcessorInfoProvider, registrationChargeSheetView.getChargeActions());

        if (patientOrder != null) {
            registrationChargeSheetView.setSource(patientOrder.getSource());
        }

        return registrationChargeSheetView;
    }

    private static int generateIsOnlyPaidByAbcPayMode(ChargeSheet chargeSheet) {

        if (chargeSheet == null) {
            return 0;
        }

        if (chargeSheet.getType() != ChargeSheet.Type.REGISTRATION) {
            return 0;
        }

        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.CHARGED) {
            return 0;
        }

        if (org.apache.commons.collections.CollectionUtils.isEmpty(chargeSheet.getChargeTransactions())) {
            return 0;
        }

        List<ChargeTransaction> chargeTransactions = chargeSheet.getChargeTransactions()
                .stream()
                .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                .collect(Collectors.toList());

        if (chargeTransactions.size() != 1) {
            return 0;
        }

        return chargeTransactions.get(0).getPayMode() == Constants.ChargePayMode.ABC_PAY ? 1 : 0;
    }

    private static void updateChargeActionCreateByNameAndSort(SheetProcessorInfoProvider sheetProcessorInfoProvider, List<ChargeAction> chargeActions) {
        if (chargeActions == null || chargeActions.size() == 0) {
            return;
        }
        String chainId = chargeActions.get(0).getChainId();
        Set<String> employeeIds = new HashSet<>();
        employeeIds.addAll(chargeActions.stream().map(ChargeAction::getCreatedBy).collect(Collectors.toSet()));
        employeeIds.addAll(chargeActions.stream().map(ChargeAction::getCheckerId).filter(org.apache.commons.lang3.StringUtils::isNotBlank).collect(Collectors.toSet()));
        List<Employee> employees = sheetProcessorInfoProvider.getEmployeeInfoProvider().findEmployeeList(chainId, new ArrayList<>(employeeIds));
        Map<String, Employee> employeeMap = employees.stream().collect(Collectors.toMap(Employee::getId, Function.identity(), (a, b) -> a));
        chargeActions.forEach(chargeAction -> {
            Employee employee = employeeMap.getOrDefault(chargeAction.getCreatedBy(), null);
            Employee checker = employeeMap.get(chargeAction.getCheckerId());
            chargeAction.setCreatedByName(employee != null ? TextUtils.alwaysString(employee.getName()) : "");
            chargeAction.setCheckerName(checker != null ? TextUtils.alwaysString(checker.getName()) : "");
        });

        chargeActions.sort((a, b) -> ObjectUtils.compare(a.getCreated(), b.getCreated()));
    }

    public static ChargeSheetForHospitalView toChargeSheetForHospitalView(SheetProcessor sheetProcessor) {

        //初始化
        ChargeSheet chargeSheet = sheetProcessor.chargeSheet;
        SheetProcessorInfoProvider sheetProcessorInfoProvider = sheetProcessor.sheetProcessorInfoProvider;
        PatientInfo patientInfo = sheetProcessor.getPatientInfo();
        List<FormProcessor> formProcessors = sheetProcessor.formProcessors;
        List<ProcessInfoView> processInfoViews = sheetProcessor.processInfoViews;
        MemberInfo memberInfo = sheetProcessor.memberInfo;
        List<PromotionView> availablePromotionViews = sheetProcessor.availablePromotionViews;
        List<GiftRulePromotionView> availableGiftRulePromotionViews = sheetProcessor.availableGiftRulePromotionViews;
        List<CouponPromotionView> availableCouponPromotionViews = sheetProcessor.availableCouponPromotionViews;
        List<PatientCardPromotionView> availablePatientCardPromotionViews = sheetProcessor.availablePatientCardPromotionViews;
        TherapySheet therapySheet = sheetProcessor.therapySheet;
        MedicalRecord medicalRecord = sheetProcessor.medicalRecord;
        PatientOrder patientOrder = sheetProcessor.patientOrder;
        int dispensingStatus = sheetProcessor.dispensingStatus;
        Instant dispensedTime = sheetProcessor.dispensedTime;
        String memberId = sheetProcessor.memberId;
        String dispensedBy = sheetProcessor.dispensedBy;
        PatientPointsInfoView patientPointsInfoView = sheetProcessor.patientPointsInfoView;


        ChargeSheetForHospitalView chargeSheetForHospitalView = new ChargeSheetForHospitalView();
        ChargeSheetAdditional chargeSheetAdditional = chargeSheet.getAdditional();
        if (chargeSheetAdditional != null) {
            chargeSheetAdditional.setCloneChargeSheetSnapshot(JsonUtils.readValue(chargeSheetAdditional.getCloneChargeSheetSnapshotJson(), CloneChargeSheetSnapshot.class));
            BeanUtils.copyProperties(chargeSheetAdditional, chargeSheetForHospitalView);
        }
        BeanUtils.copyProperties(chargeSheet, chargeSheetForHospitalView, "chargeForms");

        List<ChargeFormView> chargeFormViews = formProcessors.stream().map(formProcessor -> ChargeFormProtocol.generateChargeFormView(formProcessor, false, chargeSheet.getStatus())).filter(Objects::nonNull).collect(Collectors.toList());
        if (chargeFormViews == null) {
            chargeFormViews = new ArrayList<>();
        }

        if (chargeSheet.getDeliveryType() == ChargeSheet.DeliveryType.DELIVERY_TO_HOME && chargeSheet.getDeliveryInfo() != null) {
            ChargeFormView deliveryChargeFormView = generateDeliveryChargeFormView(chargeSheet, chargeFormViews);
            if (deliveryChargeFormView != null) {
                chargeFormViews.add(deliveryChargeFormView);
            }
        }

        chargeSheetForHospitalView.setChargeForms(chargeFormViews);
        sortChargeFormViews(chargeSheetForHospitalView);
        chargeSheetForHospitalView.setStatusName(StatusNameTranslator.translateChargeSheetStatus(chargeSheetForHospitalView.getOwedStatus(), chargeSheetForHospitalView.getStatus(), chargeSheetForHospitalView.getIsDraft() == 1, chargeSheetForHospitalView.getOutpatientStatus(), chargeSheetForHospitalView.getIsClosed()));
        chargeSheetForHospitalView.setMemberDiscountInfo(JsonUtils.loadAsJsonNode(chargeSheet.getMemberDiscountInfoJson()));
        chargeSheetForHospitalView.setMemberInfo(memberInfo);
        if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED) {
            chargeSheetForHospitalView.setPatientPointsInfo(patientPointsInfoView);
            chargeSheetForHospitalView.setPromotions(availablePromotionViews);
            chargeSheetForHospitalView.setPatientCardPromotions(availablePatientCardPromotionViews);
            if (!CollectionUtils.isEmpty(availableCouponPromotionViews)) {
                chargeSheetForHospitalView.setCouponPromotions(availableCouponPromotionViews.stream().sorted(Comparator.comparingInt(CouponPromotionView::getIsCanBeUsed).reversed()).collect(Collectors.toList()));
            } else {
                chargeSheetForHospitalView.setCouponPromotions(new ArrayList<>());
            }

            if (!CollectionUtils.isEmpty(availableGiftRulePromotionViews)) {
                chargeSheetForHospitalView.setGiftRulePromotions(availableGiftRulePromotionViews.stream().sorted(Comparator.comparingInt(GiftRulePromotionView::getIsCanBeUsed).reversed()).collect(Collectors.toList()));
            } else {
                chargeSheetForHospitalView.setGiftRulePromotions(new ArrayList<>());
            }
        } else {

            chargeSheetForHospitalView.setPatientPointsInfo(DTOConverter.convertToPatientPointsInfoView(chargeSheet.getPatientPointsPromotionInfo()));

            SheetPromotionInfo sheetPromotionInfo = JsonUtils.readValue(chargeSheet.getPromotionInfoJson(), SheetPromotionInfo.class);
            chargeSheetForHospitalView.setPromotions(sheetPromotionInfo != null ? sheetPromotionInfo.getPromotions() : null);

            List<CouponPromotionView> couponPromotionViews = null;
            if (!CollectionUtils.isEmpty(chargeSheet.getCouponPromotionInfos())) {
                couponPromotionViews = chargeSheet.getCouponPromotionInfos().stream()
                        .filter(chargeCouponPromotionInfo -> chargeCouponPromotionInfo.getIsDeleted() == 0)
                        .map(DTOConverter::convertToCouponPromotionView)
                        .filter(couponPromotionView -> couponPromotionView.getChecked())
                        .collect(Collectors.toList());
            }
            chargeSheetForHospitalView.setCouponPromotions(couponPromotionViews);

            List<GiftRulePromotionView> giftRulePromotionViews = null;
            if (!CollectionUtils.isEmpty(chargeSheet.getGiftRulePromotionInfos())) {
                giftRulePromotionViews = chargeSheet.getGiftRulePromotionInfos().stream()
                        .filter(giftRulePromotionInfo -> giftRulePromotionInfo.getIsDeleted() == 0)
                        .map(DTOConverter::convertToGiftRulePromotionView)
                        .filter(giftRulePromotionView -> giftRulePromotionView.getChecked())
                        .collect(Collectors.toList());
            }
            chargeSheetForHospitalView.setGiftRulePromotions(giftRulePromotionViews);

            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeSheet.getPatientCardPromotionInfos())) {
                chargeSheetForHospitalView.setPatientCardPromotions(chargeSheet.getPatientCardPromotionInfos()
                        .stream()
                        .filter(patientCardPromotionInfo -> patientCardPromotionInfo.getIsDeleted() == 0)
                        .map(PatientCardPromotionView::ofPatientCardPromotionView)
                        .collect(Collectors.toList())
                );
            }
        }

        chargeSheetForHospitalView.setDispensingStatus(dispensingStatus);

        chargeSheetForHospitalView.setMemberId(memberId);

        if (chargeSheet.getDeliveryInfo() != null) {
            ChargeDeliveryInfoView chargeDeliveryInfoView = new ChargeDeliveryInfoView();
            BeanUtils.copyProperties(chargeSheet.getDeliveryInfo(), chargeDeliveryInfoView);

            if (chargeSheet.getDeliveryInfo().getDeliveryCompany() != null) {
                ChargeDeliveryCompanyVo chargeDeliveryCompanyVo = new ChargeDeliveryCompanyVo();
                BeanUtils.copyProperties(chargeSheet.getDeliveryInfo().getDeliveryCompany(), chargeDeliveryCompanyVo);
                chargeDeliveryInfoView.setDeliveryCompany(chargeDeliveryCompanyVo);
            }
            chargeSheetForHospitalView.setDeliveryInfo(chargeDeliveryInfoView);
        }

        return chargeSheetForHospitalView;
    }

    public static List<ChargeChangePayModeRecordView> generateChangePayModeRecords(List<ChargeChangePayModeRecord> changePayModeRecords, List<ChargeAction> chargeActions) {

        List<ChargeChangePayModeRecordView> changePayModeRecordViews = new ArrayList<>();


        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(changePayModeRecords)) {
            changePayModeRecordViews.addAll(changePayModeRecords.stream()
                    .map(ChargeChangePayModeRecordView::of)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList()));
        }

        //将历史的修改支付方式的chargeAction转换成ChargeChangePayModeRecordView
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeActions)) {

            changePayModeRecordViews.addAll(chargeActions.stream()
                    .filter(chargeAction -> chargeAction.getType() == ChargeAction.Type.CHANGE_PAY_MODE)
                    .map(ChargeChangePayModeRecordView::ofByChargeAction)
                    .collect(Collectors.toList()));
        }


        changePayModeRecordViews.sort(Comparator.comparing(ChargeChangePayModeRecordView::getCreated));
        return changePayModeRecordViews;
    }

    private static void doReplaceRegistrationFeeNameDisplay(
            String clinicId,
            ChargeSheetPrintView chargeSheetPrintView,
            SheetProcessorInfoProvider sheetProcessorInfoProvider
    ) {
        if (CollectionUtils.isEmpty(chargeSheetPrintView.getMedicalBills())
                && CollectionUtils.isEmpty(chargeSheetPrintView.getWholeMedicalBills())) {
            return;
        }
        PropertyProvider propertyProvider = sheetProcessorInfoProvider.getPropertyProvider();
        // 获取挂号费显示名称
        String registrationFeeNameDisplay = propertyProvider.getRegistrationFeeNameDisplay(clinicId);
        if (!CollectionUtils.isEmpty(chargeSheetPrintView.getMedicalBills())) {
            for (MedicalBillPrintView medicalBillView : chargeSheetPrintView.getMedicalBills()) {
                if (medicalBillView.getPrintType() == ChargeFormItemPrintView.Type.REGISTRATION.getPrintType()) {
                    medicalBillView.setName(registrationFeeNameDisplay);
                    break;
                }
            }
        }
        if (!CollectionUtils.isEmpty(chargeSheetPrintView.getWholeMedicalBills())) {
            for (MedicalBillPrintView medicalBillView : chargeSheetPrintView.getWholeMedicalBills()) {
                if (medicalBillView.getFeeTypeId() == GoodsConst.FeeTypeId.FEE_TYPE_ID_REGISTRATION) {
                    medicalBillView.setName(registrationFeeNameDisplay);
                    break;
                }
            }
        }
    }

}
