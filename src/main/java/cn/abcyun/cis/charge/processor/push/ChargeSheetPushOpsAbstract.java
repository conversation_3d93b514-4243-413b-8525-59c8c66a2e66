package cn.abcyun.cis.charge.processor.push;

import cn.abcyun.cis.charge.base.ChargeServiceException;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.commons.exception.ServiceInternalException;

/**
 * 推送给病人
 */
public abstract class ChargeSheetPushOpsAbstract {
    protected ChargeSheet chargeSheet;
    protected String chainId;
    protected String clinicId;
    protected String operatorId;

    public ChargeSheetPushOpsAbstract(String chainId, String clinicId, String operatorId) {
        this.chainId = chainId;
        this.clinicId = clinicId;
        this.operatorId = operatorId;
    }

    /**
     * 推送订单给病人
     */
    public void doPushToPatient(){
        //构造要推送的完整的chargeSheet对象
        buildCompleteChargeSheet();

        //业务处理之前校验是否能够推送
        checkCanPushToPatientBeforeCalFeeAndThrowException();

        //推送算费
        calculateFee();

        //[JPA]填充要保存落地的字段
        fillRepositoryData();

        //[New对象]如果有新对象需要落地db，在这里实现，比如空中药房的快递和加工信息等
        saveNewToDb();

        //算完费，再检查能否推送给病人
        if (checkCanPushToPatientAfterCalFee()) {
            //发消息给病人
            pushMessageToPatient();
        }
    }



    /**
     * 检查算费后能否推给病人
     */
    protected abstract void checkCanPushToPatientBeforeCalFeeAndThrowException();

    /**
     * 构造chargeSheet
     */
    protected abstract void buildCompleteChargeSheet();


    /**
     * 检查算费后能否推给病人
     */
    protected abstract boolean checkCanPushToPatientAfterCalFee();

    /**
     * 推送的算费
     */
    protected abstract void calculateFee() throws ChargeServiceException, ServiceInternalException;


    /**
     * 推送给病人
     */
    protected abstract void pushMessageToPatient() throws ChargeServiceException, ServiceInternalException;


    /**
     * 【JPA落地】 算费后，部分字段需要再落地，字段的填充
     */
    protected abstract void fillRepositoryData() throws ServiceInternalException;


    /**
     * 【新对象落地】如果有新增的对象需要落地db，请在这个函数内实现
     */
    protected abstract void saveNewToDb() throws ServiceInternalException;

}
