package cn.abcyun.cis.charge.processor.discount;

import cn.abcyun.bis.rpc.sdk.cis.model.promotion.Promotion;

import java.util.HashMap;
import java.util.Map;

public class DiscountRuleProcessorFactory {

    private final static Map<Integer, DiscountRulePromotionGoodsCalculator> PROMOTION_GOODS_CALCULATOR_MAP = new HashMap<Integer, DiscountRulePromotionGoodsCalculator>() {{
        put(Promotion.PromotionDetail.DiscountWay.DISCOUNT, new DiscountRulePromotionGoodsOnlyDiscountCalculator());
        put(Promotion.PromotionDetail.DiscountWay.GIFT, new DiscountRulePromotionGoodsGiftCalculator());
        put(Promotion.PromotionDetail.DiscountWay.FIXED_PRICE, new DiscountRulePromotionGoodsFixedPriceCalculator());
    }};

    private final static DiscountRulePromotionGoodsGiftCalculator.EnoughNGiftGoodsCalculator ENOUGH_N_GIFT_GOODS_CALCULATOR = new DiscountRulePromotionGoodsGiftCalculator.EnoughNGiftGoodsCalculator();

    public static DiscountRuleProcessor createDiscountRule(Promotion promotion) {
        DiscountRuleProcessor discountRuleProcessor = null;
        if (promotion == null) {
            return null;
        }

        switch (promotion.getType()) {
            case Promotion.Type.MEMBER_DISCOUNT:
                discountRuleProcessor = new MemberDiscountRuleProcessor(promotion);
                break;
            case Promotion.Type.NORMAL_DISCOUNT:
                discountRuleProcessor = new NormalDiscountRuleProcessor(promotion);
                break;
            case Promotion.Type.CARD:
                discountRuleProcessor = new PatientCardDiscountRuleProcessor(promotion);
                break;
        }
        return discountRuleProcessor;
    }

    public static DiscountRulePromotionGoodsCalculator createPromotionGoodsCalculator(int discountWay) {
        return PROMOTION_GOODS_CALCULATOR_MAP.get(discountWay);
    }

    public static DiscountRulePromotionGoodsCalculator.GiftGoodsCalculator createGiftGoodsCalculator() {
        return ENOUGH_N_GIFT_GOODS_CALCULATOR;
    }

}
