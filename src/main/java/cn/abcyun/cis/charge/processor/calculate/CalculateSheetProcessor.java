package cn.abcyun.cis.charge.processor.calculate;

import cn.abcyun.cis.charge.api.model.BasicCalculateSheetReq;
import cn.abcyun.cis.charge.api.model.BasicCalculateSheetRsp;
import cn.abcyun.cis.charge.processor.SmartFlatPriceHelper;
import cn.abcyun.cis.charge.util.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.util.Asserts;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class CalculateSheetProcessor {

    private final BasicCalculateSheetReq sheetReq;

    private CalculateModel calculateModel;

    private List<CalculateFormProcessor> formProcessors = new ArrayList<>();

    public CalculateSheetProcessor(BasicCalculateSheetReq sheetReq) {
        this.sheetReq = sheetReq;
        calculateModel = CalculateModel.translateCalculateModel(sheetReq.getHisType());
        build(calculateModel);
    }

    private void build(CalculateModel calculateModel) {

        if (Objects.isNull(sheetReq)) {
            log.info("sheetReq is null");
            return;
        }

        if (sheetReq.getForms() == null) {
            log.info("sheetReq no forms");
            return;
        }

        Asserts.notNull(calculateModel, "calculateModel is null");

        sheetReq.getForms().forEach(formReq -> formProcessors.add(new CalculateFormProcessor(formReq, calculateModel)));

    }

    public BasicCalculateSheetRsp calculate() {

        //计算item的当前值和item本身的议价值
        calculateItemExpectedPrice();

        //计算form的当前值和form本身的议价值
        calculateFormExpectedPrice();

        //计算sheet的当前值和本身的议价值
        calculateSheetExpectedPrice();

        return generateBasicCalculateSheetRsp();
    }

    private BigDecimal calculateCurrentTotalPrice() {
        return formProcessors.stream().map(CalculateFormProcessor::calculateCurrentTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private BasicCalculateSheetRsp generateBasicCalculateSheetRsp() {
        BasicCalculateSheetRsp sheetRsp = new BasicCalculateSheetRsp();
        return sheetRsp
                .setId(sheetReq.getId())
                .setTotalPrice(calculateCurrentTotalPrice())
                .setSourceTotalPrice(sheetReq.getSourceTotalPrice())
                .setIsTotalPriceChanged(sheetReq.getIsTotalPriceChanged())
                .setForms(formProcessors.stream().map(CalculateFormProcessor::generateBasicCalculateFormRsp).collect(Collectors.toList()));
    }

    private void calculateItemExpectedPrice() {
        formProcessors.forEach(CalculateFormProcessor::calculateItemExpectedPrice);
    }

    private void calculateFormExpectedPrice() {
        formProcessors.forEach(CalculateFormProcessor::calculateFormExpectedPrice);
    }

    private void calculateSheetExpectedPrice() {

//        boolean formExistExpectedPrice = formProcessors.stream()
//                .anyMatch(CalculateFormProcessor::existExpectedPrice);
//
//        //form上有议价，则sheet上的议价清空，这两者不能同时存在
//        if (formExistExpectedPrice) {
//            sheetReq.setExpectedTotalPrice(null);
//        }

        if (sheetReq.getExpectedTotalPrice() == null) {
            return;
        }

        formProcessors.forEach(CalculateFormProcessor::resetPrice);

        List<SmartFlatPriceHelper.SmartFlatPriceCell> flatPriceCells = formProcessors.stream()
                .map(CalculateFormProcessor::generateSmartFlatPriceCell)
                .collect(Collectors.toList());

        BigDecimal adjustmentPrice = sheetReq.getExpectedTotalPrice().subtract(sheetReq.getSourceTotalPrice());

        SmartFlatPriceHelper.flat(adjustmentPrice, flatPriceCells, SmartFlatPriceHelper.formSmartFlatTypePriority);

        Map<String, SmartFlatPriceHelper.SmartFlatPriceCell> flatPriceCellMap = ListUtils.toMap(flatPriceCells, SmartFlatPriceHelper.SmartFlatPriceCell::getId);

        formProcessors.forEach(formProcessor -> {

            SmartFlatPriceHelper.SmartFlatPriceCell flatPriceCell = flatPriceCellMap.getOrDefault(formProcessor.getFormReq().getId(), null);

            if (Objects.isNull(flatPriceCell)) {
                return;
            }

            formProcessor.flatAdjustmentPrice(flatPriceCell.getFlatPrice());
        });

    }

}
