package cn.abcyun.cis.charge.processor;


import cn.abcyun.cis.charge.processor.provider.*;

public interface SheetProcessorInfoProvider {
    ProductInfoProvider getProductInfoProvider();

    GoodsScProvider getGoodsScProvider();

    PatientInfoProvider getPatientInfoProvider();

    PatientOrderProvider getPatientOrderProvider();

    EmployeeInfoProvider getEmployeeInfoProvider();

    DispensingInfoProvider getDispensingInfoProvider();

    OutpatientInfoProvider getOutpatientInfoProvider();

    ChargePayProvider getChargePayProvider();

    LimitPriceProvider getLimitPriceProvider();

    ChargePayModeProvider getChargePayModeProvider();

    PromotionProvider getPromotionProvider();

    ChargeRuleProvider getChargeRuleProvider();

    ShebaoInfoProvider getShebaoInfoProvider();

    AirPharmacyProvider getAirPharmacyProvider();

    ChargeSheetInvoiceProvider getChargeSheetInvoiceProvider();

    DynamicConfigProvider getDynamicConfigProvider();

    ClinicProvider getClinicProvider();

    PropertyProvider getPropertyProvider();

    ChargeOweSheetProvider getChargeOweSheetProvider();

    ChargeExecuteProvider getChargeExecuteProvider();

    ChargeExaminationProvider getChargeExaminationProvider();

    GoodsLockingProvider getGoodsLockingProvider();

    CisGoodsProvider getCisGoodsProvider();

    DeliveryProvider getDeliveryProvider();

    OrderNoGeneratorProvider getOrderNoGeneratorProvider();

    RegistrationProvider getRegistrationProvider();

    CisMallOrderProvider getCisMallOrderProvider();

    ChargeCooperationOrderProvider getChargeCooperationOrderProvider();
}
