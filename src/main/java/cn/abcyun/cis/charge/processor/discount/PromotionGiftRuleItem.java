package cn.abcyun.cis.charge.processor.discount;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsItem;
import cn.abcyun.bis.rpc.sdk.cis.model.promotion.Promotion;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.service.dto.GiftRulePromotionView;
import cn.abcyun.cis.charge.service.dto.OncomingGiftRulePromotionView;
import cn.abcyun.cis.charge.service.dto.PromotionAirPharmacyForm;
import cn.abcyun.cis.charge.service.dto.PromotionProductItem;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.cis.commons.rpc.promotion.AvailablePromotionRsp;
import cn.abcyun.cis.commons.util.TextUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class PromotionGiftRuleItem {
    private Promotion promotion;

    private Promotion.PromotionDetail promotionDetail;

    private List<Promotion.PromotionDetail.GiftRule> promotionGiftRules = new ArrayList<>();

    private List<String> promotionGoodIds = new ArrayList<>();


    private List<String> goodsTypeTupleKeys = new ArrayList<>();

    /**
     * 折扣生效的items
     */
    private List<? extends BasicCalculatePromotionItem> efficientItems = new ArrayList<>();

    /**
     * 折扣生效的forms
     */
    List<CalculatePromotionAirPharmacyForm> efficientForms = new ArrayList<>();

    public PromotionGiftRuleItem(Promotion promotion) {
        this.promotion = promotion;
        promotionDetail = promotion.getDetail();
        if (promotionDetail != null) {
            if (!CollectionUtils.isEmpty(promotionDetail.getGiftRules())) {
                promotionGiftRules = promotionDetail.getGiftRules().stream().sorted(Comparator.comparing(Promotion.PromotionDetail.GiftRule::getOrderThresholdPrice).thenComparing(Promotion.PromotionDetail.GiftRule::getDiscountedPrice).reversed()).collect(Collectors.toList());
            }

            if (!CollectionUtils.isEmpty(promotionDetail.getGoodsList())) {
                promotionGoodIds = promotionDetail.getGoodsList().stream().map(promotionGoods -> CalculatePromotionItem.convertPharmacyTypeGoodsIdKey(promotionGoods.getPharmacyType(), promotionGoods.getGoodsId())).collect(Collectors.toList());
            }

            if (promotionDetail.getGoodsTypeTupleList() != null) {
                goodsTypeTupleKeys = promotionDetail.getGoodsTypeTupleList().stream().map(CalculatePromotionAirPharmacyForm::convertToKey).collect(Collectors.toList());
            }
        }

    }

    public BigDecimal getMinOrderThresholdPrice() {
        if (CollectionUtils.isEmpty(promotionGiftRules)) {
            return BigDecimal.ZERO;
        }
        return promotionGiftRules.get(0).getOrderThresholdPrice();
    }

    public BigDecimal getMaxDiscountedPrice() {
        if (CollectionUtils.isEmpty(promotionGiftRules)) {
            return BigDecimal.ZERO;
        }

        return promotionGiftRules.get(0).getDiscountedPrice();
    }

    public String getPromotionId() {
        return promotion.getId();
    }

    public <T extends BasicCalculatePromotionItem> boolean isAvailable(List<T> items) {
        if (promotion == null || CollectionUtils.isEmpty(items)) {
            return false;
        }
        return isAvailableItem(items);
    }

    public <T extends BasicCalculatePromotionItem> boolean isAvailable(List<T> items, List<CalculatePromotionAirPharmacyForm> airPharmacyForms) {
        if (promotion == null || (CollectionUtils.isEmpty(items) && CollectionUtils.isEmpty(airPharmacyForms)) || promotionDetail == null || CollectionUtils.isEmpty(promotionGiftRules)) {
            return false;
        }
        return isAvailableItem(items) || isAvailableAirPharmacyForm(airPharmacyForms);
    }

    private boolean isAvailableAirPharmacyForm(List<CalculatePromotionAirPharmacyForm> airPharmacyForms) {

        BigDecimal calculateTotalPrice = null;

        if (CollectionUtils.isEmpty(airPharmacyForms)) {
            return false;
        }

        boolean available = false;

        List<CalculatePromotionAirPharmacyForm> availableForms = airPharmacyForms.stream()
                .filter(form -> goodsTypeTupleKeys.contains(CalculatePromotionAirPharmacyForm.convertToKey(form)))
                .collect(Collectors.toList());


        if (promotion.getOnlyOriginalPrice() == 1) {
            calculateTotalPrice = availableForms.stream()
                    .filter(CalculatePromotionAirPharmacyForm::canApplyOriginalPricePromotion)
                    .filter(form -> form.getCanDiscountTotalPrice() != null)
                    .map(CalculatePromotionAirPharmacyForm::getCanDiscountTotalPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

        } else if (promotion.getOnlyOriginalPrice() == 0) {
            calculateTotalPrice = availableForms.stream()
                    .map(form -> MathUtils.wrapBigDecimalAdd(form.getCanDiscountTotalPrice(), form.getDiscountPrice()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

        }

        if (calculateTotalPrice.compareTo(promotionGiftRules.get(promotionGiftRules.size() - 1).getOrderThresholdPrice()) >= 0) {
            available = true;
        }
        return available;
    }

    private <T extends BasicCalculatePromotionItem> boolean isAvailableItem(List<T> items) {
        BigDecimal calculateTotalPrice = null;

        if (CollectionUtils.isEmpty(items)) {
            return false;
        }

        boolean available = false;

        List<T> availableItems = items.stream()
                .filter(BasicCalculatePromotionItem::isCanApplyGiftPromotion)
                .filter(item -> promotionGoodIds.contains(item.getPharmacyTypeGoodsIdKey()))
                .collect(Collectors.toList());


        if (promotion.getOnlyOriginalPrice() == 1) {
            calculateTotalPrice = availableItems.stream()
                    .filter(T::canApplyOriginalPricePromotion)
                    .map(T::getCanDiscountTotalPrice)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

        } else if (promotion.getOnlyOriginalPrice() == 0) {
            calculateTotalPrice = availableItems.stream()
                    .map(item -> MathUtils.wrapBigDecimalAdd(item.getCanDiscountTotalPrice(), item.getExistedDiscountPrice()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

        }

        if (calculateTotalPrice.compareTo(promotionGiftRules.get(promotionGiftRules.size() - 1).getOrderThresholdPrice()) >= 0) {
            available = true;
        }
        return available;
    }

    public <T extends BasicCalculatePromotionItem> CalculateGiftRulePromotion generateCalculateGiftRuleRulePromotion(List<T> items, List<CalculatePromotionAirPharmacyForm> airPharmacyForms, String patientId) {
        CalculateGiftRulePromotion calculateGiftRulePromotionFlag = null;
        if (CollectionUtils.isEmpty(items) && CollectionUtils.isEmpty(airPharmacyForms)) {
            return null;
        }

        if (items == null) {
            items = new ArrayList<>();
        }

        if (airPharmacyForms == null) {
            airPharmacyForms = new ArrayList<>();
        }

        BigDecimal calculateTotalPrice = BigDecimal.ZERO;

        List<T> availableItems = items.stream()
                .filter(BasicCalculatePromotionItem::isCanApplyGiftPromotion)
                .filter(item -> promotionGoodIds.contains(item.getPharmacyTypeGoodsIdKey()))
                .filter(item -> !isContainGiftRulePromotion(item.getPromotions()))
                .collect(Collectors.toList());

        List<CalculatePromotionAirPharmacyForm> availableForms = airPharmacyForms.stream()
                .filter(form -> goodsTypeTupleKeys.contains(CalculatePromotionAirPharmacyForm.convertToKey(form)))
                .filter(form -> !isContainGiftRulePromotion(form.getPromotions()))
                .collect(Collectors.toList());


        if (promotion.getOnlyOriginalPrice() == 1) {
            efficientItems = availableItems.stream()
                    .filter(T::canApplyOriginalPricePromotion)
                    .filter(item -> item.getCanDiscountTotalPrice() != null)
                    .collect(Collectors.toList());

            efficientForms = availableForms.stream()
                    .filter(CalculatePromotionAirPharmacyForm::canApplyOriginalPricePromotion)
                    .filter(form -> form.getCanDiscountTotalPrice() != null)
                    .collect(Collectors.toList());

            calculateTotalPrice = MathUtils.wrapBigDecimalAdd(
                    efficientItems.stream()
                            .map(BasicCalculatePromotionItem::getCanDiscountTotalPrice)
                            .reduce(BigDecimal.ZERO, BigDecimal::add),
                    efficientForms.stream()
                            .map(CalculatePromotionAirPharmacyForm::getCanDiscountTotalPrice)
                            .reduce(BigDecimal.ZERO, BigDecimal::add)
            );

        } else if (promotion.getOnlyOriginalPrice() == 0) {
            efficientItems = availableItems;
            efficientForms = availableForms;

            calculateTotalPrice = MathUtils.wrapBigDecimalAdd(
                    efficientItems.stream()
                            .map(item -> MathUtils.wrapBigDecimalAdd(item.getCanDiscountTotalPrice(), item.getExistedDiscountPrice()))
                            .reduce(BigDecimal.ZERO, BigDecimal::add),
                    efficientForms.stream()
                            .map(form -> MathUtils.wrapBigDecimalAdd(form.getCanDiscountTotalPrice(), form.getDiscountPrice()))
                            .reduce(BigDecimal.ZERO, BigDecimal::add)
            );
        }

        if (!CollectionUtils.isEmpty(availableItems) || !CollectionUtils.isEmpty(availableForms)) {

            for (Promotion.PromotionDetail.GiftRule promotionGiftRule : promotionGiftRules) {

                if (promotionGiftRule.getOrderThresholdPrice() != null
                        && promotionGiftRule.getOrderThresholdPrice().compareTo(calculateTotalPrice) <= 0
                ) {

                    if (calculateGiftRulePromotionFlag != null && calculateGiftRulePromotionFlag.getOrderThresholdPrice().compareTo(promotionGiftRule.getOrderThresholdPrice()) >= 0) {
                        continue;
                    }

                    int cycleCount = 1;
                    if (promotionGiftRule.getIsCycle() == 1) {
                        cycleCount = calculateTotalPrice.divide(promotionGiftRule.getOrderThresholdPrice(), 2, RoundingMode.DOWN).intValue();
                    }
                    calculateGiftRulePromotionFlag = generateCalculateGiftRuleRulePromotion(promotionGiftRule, efficientItems, efficientForms, cycleCount);
                }
            }
        }

        //处理赠送的优惠券count
        checkGiftCouponCountAndFillReason(calculateGiftRulePromotionFlag);

        return calculateGiftRulePromotionFlag;
    }

    public static boolean isContainGiftRulePromotion(List<PromotionSimple> promotions) {
        if (CollectionUtils.isEmpty(promotions)) {
            return false;
        }
        return promotions.stream().anyMatch(promotionSimple -> Objects.equals(Promotion.Type.GIFT_RULE, promotionSimple.getType()));
    }

    public <T extends BasicCalculatePromotionItem> CalculateGiftRulePromotion generateCalculateGiftRuleRulePromotion(Promotion.PromotionDetail.GiftRule promotionGiftRule, List<T> availableItems, List<CalculatePromotionAirPharmacyForm> availableForms, int cycleCount) {
        BigDecimal cycleCountDecimal = new BigDecimal(cycleCount);
        CalculateGiftRulePromotion calculateGiftRulePromotionFlag = new CalculateGiftRulePromotion();


        calculateGiftRulePromotionFlag.setDiscountPrice(
                MathUtils.wrapBigDecimalMultiply(promotionGiftRule.getDiscountedPrice().abs().negate(), cycleCountDecimal)
        );

        calculateGiftRulePromotionFlag.setOrderThresholdPrice(promotionGiftRule.getOrderThresholdPrice());

        CalculateGiftRulePromotion.HitGiftRule hitGiftRule = new CalculateGiftRulePromotion.HitGiftRule();
        BeanUtils.copyProperties(promotionGiftRule, hitGiftRule);

        calculateGiftRulePromotionFlag.setHitGiftRule(hitGiftRule);

        calculateGiftRulePromotionFlag.setChargeFormItemIds(
                availableItems.stream().map(BasicCalculatePromotionItem::getId).collect(Collectors.toList())
        );

        calculateGiftRulePromotionFlag.setAirPharmacyChargeFormIds(
                availableForms.stream().map(CalculatePromotionAirPharmacyForm::getId).collect(Collectors.toList())
        );

        List<Promotion.PromotionDetail.GiftRule.GiftCoupon> giftCoupons = promotionGiftRule.getGiftCoupons();

        if (!CollectionUtils.isEmpty(giftCoupons)) {
            calculateGiftRulePromotionFlag.setGiftCoupons(
                    giftCoupons.stream().map(giftCoupon -> generateGiftCouponView(giftCoupon, cycleCount)).collect(Collectors.toList())
            );
        }
        if (!CollectionUtils.isEmpty(promotionGiftRule.getGiftGoods())) {
            calculateGiftRulePromotionFlag.setGiftGoodItems(
                    promotionGiftRule.getGiftGoods().stream().map(giftGood -> generateGiftGoodsView(giftGood, cycleCount)).collect(Collectors.toList())
            );

        }
        calculateGiftRulePromotionFlag.setPromotionId(promotion.getId());
        calculateGiftRulePromotionFlag.setPromotionParentType(promotion.getParentType());
        calculateGiftRulePromotionFlag.setPromotionName(promotion.getName());

        return calculateGiftRulePromotionFlag;
    }

    private void checkGiftCouponCountAndFillReason(CalculateGiftRulePromotion calculateGiftRulePromotion) {

        if (calculateGiftRulePromotion == null || CollectionUtils.isEmpty(calculateGiftRulePromotion.getGiftCoupons())) {
            return;
        }

        for (GiftCouponView giftCouponView : calculateGiftRulePromotion.getGiftCoupons()) {
            Promotion.PromotionDetail.GiftRule.GiftCoupon giftCoupon = giftCouponView.getGiftCoupon();

            int giftStatus = giftCoupon.getGiftStatus();
            int count = giftCouponView.getTotalCount();
            String reason = null;

            if (giftStatus == AvailablePromotionRsp.GiftCouponStatus.SPECIAL_MEMBER) {
                count = 0;
                reason = GiftCouponViewReasonTips.SPECIAL_MEMBER;
            } else if (giftStatus == AvailablePromotionRsp.GiftCouponStatus.TAKE_OFF) {
                count = 0;
                reason = GiftCouponViewReasonTips.COUPON_TAKE_OFF;
            } else if (giftStatus == AvailablePromotionRsp.GiftCouponStatus.INVALID) {
                count = 0;
                reason = GiftCouponViewReasonTips.COUPON_INVALID;
            } else if (giftStatus == AvailablePromotionRsp.GiftCouponStatus.OK
                    || giftStatus == AvailablePromotionRsp.GiftCouponStatus.OBTAINED
                    || giftStatus == AvailablePromotionRsp.GiftCouponStatus.STOCK_OUT) {

                if (giftCoupon.getIsLimitObtainCount() == 0) {
                    if (giftCoupon.getLeftTotalCount() < count) {
                        count = giftCoupon.getLeftTotalCount();
                        reason = String.format(GiftCouponViewReasonTips.COUPON_ONLY_REMAIN, count);
                    }
                } else {
                    int notObtainedCount = giftCoupon.getObtainCountPerUser() - giftCoupon.getObtainedCount();

                    if (notObtainedCount >= 0 && notObtainedCount < count) {
                        count = notObtainedCount;
                        reason = String.format(GiftCouponViewReasonTips.ALREADY_RECEIVED_COUPON, giftCoupon.getObtainedCount(), giftCoupon.getObtainCountPerUser());
                    } else {
                        count = notObtainedCount < 0 ? 0 : count;
                    }

                }

            }

            giftCouponView.setCount(count);
            giftCouponView.setReason(reason);
        }
    }

    public <T extends BasicCalculatePromotionItem> GiftRulePromotionView generateGiftRulePromotionView(List<T> items, List<CalculatePromotionAirPharmacyForm> airPharmacyForms) {
        if (promotionDetail == null || (CollectionUtils.isEmpty(items) && CollectionUtils.isEmpty(airPharmacyForms))) {
            return null;
        }

        if (items == null) {
            items = new ArrayList<>();
        }

        if (airPharmacyForms == null) {
            airPharmacyForms = new ArrayList<>();
        }


        GiftRulePromotionView giftRulePromotionView = new GiftRulePromotionView();
        giftRulePromotionView.setId(promotion.getId());
        giftRulePromotionView.setName(promotion.getName());

        List<PromotionProductItem> usedPromotionProductItems = items.stream()
                .filter(item -> !CollectionUtils.isEmpty(item.getPromotions()))
                .filter(item -> {
                    List<String> promotionIds = item.getPromotions().stream().map(PromotionSimple::getPromotionId).collect(Collectors.toList());
                    return promotionIds.contains(promotion.getId());
                })
                .map(item -> {

                    List<PromotionSimple> promotionSimples = item.getPromotions();
                    Map<String, PromotionSimple> promotionSimpleMap = promotionSimples.stream().collect(Collectors.toMap(PromotionSimple::getPromotionId, Function.identity(), (a, b) -> a));

                    PromotionSimple promotionSimple = promotionSimpleMap.get(promotion.getId());

                    PromotionProductItem promotionProductItem = new PromotionProductItem();
                    promotionProductItem.setId(item.getId());
                    promotionProductItem.setName(item.getDisplayName());
                    promotionProductItem.setDiscountPrice(promotionSimple.getDiscountPrice());
                    return promotionProductItem;
                }).collect(Collectors.toList());

        //将空中药房form转换为PromotionProductItem
        List<PromotionProductItem> usedPromotionAirPharmacyForms = airPharmacyForms.stream()
                .filter(form -> !CollectionUtils.isEmpty(form.getPromotions()))
                .filter(form -> {
                    List<String> promotionIds = form.getPromotions().stream().map(PromotionSimple::getPromotionId).collect(Collectors.toList());
                    return promotionIds.contains(promotion.getId());
                })
                .map(form -> {

                    List<PromotionSimple> promotionSimples = form.getPromotions();
                    Map<String, PromotionSimple> promotionSimpleMap = promotionSimples.stream().collect(Collectors.toMap(PromotionSimple::getPromotionId, Function.identity(), (a, b) -> a));

                    PromotionSimple promotionSimple = promotionSimpleMap.get(promotion.getId());

                    PromotionProductItem promotionProductItem = new PromotionProductItem();
                    promotionProductItem.setId(form.getId());
                    promotionProductItem.setName(form.getName());
                    promotionProductItem.setDiscountPrice(promotionSimple.getDiscountPrice());
                    return promotionProductItem;
                }).collect(Collectors.toList());
        giftRulePromotionView.addProductItems(usedPromotionProductItems);
        giftRulePromotionView.addProductItems(usedPromotionAirPharmacyForms);
        BigDecimal productItemTotalDiscountPrice = giftRulePromotionView.getProductItems().stream()
                .filter(promotionProductItem -> promotionProductItem.getDiscountPrice() != null)
                .map(PromotionProductItem::getDiscountPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal airPharmacyFormTotalDiscountPrice = giftRulePromotionView.getAirPharmacyForms().stream()
                .filter(airPharmacyForm -> airPharmacyForm.getDiscountPrice() != null)
                .map(PromotionAirPharmacyForm::getDiscountPrice).reduce(BigDecimal.ZERO, BigDecimal::add);

        if (productItemTotalDiscountPrice.compareTo(BigDecimal.ZERO) == 0) {
            giftRulePromotionView.setProductItems(new ArrayList<>());
        }

        if (airPharmacyFormTotalDiscountPrice.compareTo(BigDecimal.ZERO) == 0) {
            giftRulePromotionView.setAirPharmacyForms(new ArrayList<>());
        }

        giftRulePromotionView.setDiscountPrice(MathUtils.wrapBigDecimalAdd(productItemTotalDiscountPrice, airPharmacyFormTotalDiscountPrice));
        giftRulePromotionView.setChecked(true);

        return giftRulePromotionView;
    }

    public static GiftCouponView generateGiftCouponView(Promotion.PromotionDetail.GiftRule.GiftCoupon giftCoupon, int cycleCount) {

        if (giftCoupon == null) {
            return null;
        }

        GiftCouponView giftCouponView = new GiftCouponView();
        giftCouponView.setPromotionId(giftCoupon.getPromotionId());
        giftCouponView.setName(giftCoupon.getName());
        giftCouponView.setUnitCount(giftCoupon.getCount());
        giftCouponView.setTotalCount(giftCoupon.getCount() * cycleCount);
        giftCouponView.setLeftTotalCount(giftCoupon.getLeftTotalCount());
        giftCouponView.setGiftCoupon(giftCoupon);
        giftCouponView.setCount(giftCoupon.getCount() * cycleCount);
        giftCouponView.setValidType(giftCoupon.getValidType());
        giftCouponView.setValidEnd(giftCoupon.getValidEnd());
        giftCouponView.setValidDays(giftCoupon.getValidDays());

        return giftCouponView;
    }

//    public static void fillGiftCouponViewReason(GiftCouponView giftCouponView, PromotionGiftRule.GiftCoupon giftCoupon) {
//        if (giftCouponView == null || giftCoupon == null) {
//            return;
//        }
//        if (giftCouponView.getCount() == giftCouponView.getRealCount() && giftCoupon.getCount() == 0) {
//            return;
//        }
//
//        if (giftCouponView.getRealCount() < giftCouponView.getCount()) {
//
//            if (giftCoupon.getLeftCount() == giftCouponView.getRealCount()) {
//                giftCouponView.setReason(String.format(GiftCouponViewReasonTips.COUPON_ONLY_REMAIN, giftCouponView.getRealCount()));
//            }
//
//
//        }
//
//
//    }

    public static GiftRulePromotionView.GiftGoodsItemView generateGiftGoodsView(Promotion.PromotionDetail.GiftRule.GiftGoods giftGood, int cycleCount) {
        GiftRulePromotionView.GiftGoodsItemView giftGoodsItemView = new GiftRulePromotionView.GiftGoodsItemView();
        GoodsItem goodsItem = giftGood.getGoodsItem();
        BeanUtils.copyProperties(giftGood.getGoodsItem(), giftGoodsItemView);
        giftGoodsItemView.setCount(new BigDecimal(giftGood.getCount() * cycleCount));
        if (goodsItem.getType() == Constants.ProductType.MEDICINE) {
            giftGoodsItemView.setName(goodsItem.getMedicineCadn());
        } else {
            giftGoodsItemView.setName(TextUtils.alwaysString(goodsItem.getName()));
        }
        if (goodsItem.getType() == Constants.ProductType.MEDICINE && goodsItem.getSubType() == Constants.ProductType.SubType.MEDICINE_CHINESE) {
            giftGoodsItemView.setUnit(goodsItem.getPieceUnit());
        } else {
            giftGoodsItemView.setUnit(goodsItem.getPackageUnit());
        }
        return giftGoodsItemView;
    }

    public OncomingGiftRulePromotionView generateOncomingGiftRulePromotionView(GiftRulePromotionView availableGiftRulePromotion) {
        OncomingGiftRulePromotionView oncomingGiftRulePromotionView = new OncomingGiftRulePromotionView();
        Promotion.PromotionDetail.GiftRule onCommingGiftRule;
        if (availableGiftRulePromotion == null) {
            onCommingGiftRule = promotionGiftRules.get(0);
        } else {
            onCommingGiftRule = promotionGiftRules.stream()
                    .filter(giftRule -> giftRule.getOrderThresholdPrice().compareTo(availableGiftRulePromotion.getOrderThresholdPrice()) > 0
                            && giftRule.getDiscountedPrice().compareTo(availableGiftRulePromotion.getDiscountPrice()) > 0
                    )
                    .findFirst()
                    .orElse(null);
        }
        //没有找到下一个规则，说明匹配中的就是最高的满减规则了
        if (Objects.isNull(onCommingGiftRule)) {
            return null;
        }

        oncomingGiftRulePromotionView.setId(promotion.getId());
        oncomingGiftRulePromotionView.setName(promotion.getName());
        oncomingGiftRulePromotionView.setOrderThresholdPrice(onCommingGiftRule.getOrderThresholdPrice());
        oncomingGiftRulePromotionView.setDiscountPrice(onCommingGiftRule.getDiscountedPrice());
        oncomingGiftRulePromotionView.setOnlyOriginalPrice(promotion.getOnlyOriginalPrice());
        oncomingGiftRulePromotionView.setGiftCoupons(Optional.ofNullable(onCommingGiftRule.getGiftCoupons())
                .orElse(new ArrayList<>())
                .stream()
                .map(giftCoupon -> generateGiftCouponView(giftCoupon, 1))
                .collect(Collectors.toList())
        );
        oncomingGiftRulePromotionView.setGiftGoodItems(Optional.ofNullable(onCommingGiftRule.getGiftGoods())
                .orElse(new ArrayList<>())
                .stream()
                .map(giftGood -> generateGiftGoodsView(giftGood, 1))
                .collect(Collectors.toList())
        );

        return oncomingGiftRulePromotionView;
    }

    /**
     * 匹配除了已经命中的规则的最低满减
     * @param giftRulePromotionView
     * @return
     */
    public Object matchOnComingGiftRulePromotion(GiftRulePromotionView giftRulePromotionView) {

        if (giftRulePromotionView == null) {

        }

        return null;


    }

    public static class GiftCouponViewReasonTips {

        public static String COUPON_ONLY_REMAIN = "优惠券仅剩%s张";

        public static String ALREADY_RECEIVED_COUPON = "患者已领取%s张（每人限领%s张）";

        public static String SPECIAL_MEMBER = "优惠券限制指定会员可领取";

        public static String COUPON_TAKE_OFF = "优惠券已停止发放";

        public static String COUPON_INVALID = "优惠券已失效";

    }

}
