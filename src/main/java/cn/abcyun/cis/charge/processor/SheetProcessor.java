package cn.abcyun.cis.charge.processor;

import cn.abcyun.bis.rpc.sdk.bis.model.order.CalculatePriceRsp;
import cn.abcyun.bis.rpc.sdk.bis.model.order.CreateOrderView;
import cn.abcyun.bis.rpc.sdk.bis.model.order.LogisticsCompanyView;
import cn.abcyun.bis.rpc.sdk.cis.model.PrescriptionAstResult;
import cn.abcyun.bis.rpc.sdk.cis.model.charge.GoodsFeeType;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.ChainEmployee;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.PracticeInfo;
import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.*;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.config.GoodsConfigView;
import cn.abcyun.bis.rpc.sdk.cis.model.goodslocking.GoodsLockBatchItem;
import cn.abcyun.bis.rpc.sdk.cis.model.goodslocking.GoodsLockingSheet;
import cn.abcyun.bis.rpc.sdk.cis.model.mall.order.VerificationItemView;
import cn.abcyun.bis.rpc.sdk.cis.model.mall.order.VerificationRefundReq;
import cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientBasicInfoView;
import cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientRelateMemberRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.patient.PointPromotionReq;
import cn.abcyun.bis.rpc.sdk.cis.model.patient.PointPromotionRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.patientorder.BusinessLockReq;
import cn.abcyun.bis.rpc.sdk.cis.model.patientorder.BusinessLockVO;
import cn.abcyun.bis.rpc.sdk.cis.model.promotion.*;
import cn.abcyun.bis.rpc.sdk.cis.model.shebao.QueryProcessFeesMatchCodeReqBody;
import cn.abcyun.bis.rpc.sdk.cis.model.shebao.QueryProcessFeesMatchCodeResBody;
import cn.abcyun.bis.rpc.sdk.cis.model.shebao.RpcGetShebaoMatchedCodesReq;
import cn.abcyun.bis.rpc.sdk.property.model.AntimicrobialDrugManagementConfig;
import cn.abcyun.bis.rpc.sdk.property.model.AntimicrobialDrugManagementRule;
import cn.abcyun.bis.rpc.sdk.property.model.CrmPatientFamily;
import cn.abcyun.cis.charge.amqp.MQProducer;
import cn.abcyun.cis.charge.amqp.model.ChargePayTransactionAutoCancelMessage;
import cn.abcyun.cis.charge.api.model.*;
import cn.abcyun.cis.charge.api.model.PatientCardView;
import cn.abcyun.cis.charge.base.*;
import cn.abcyun.cis.charge.base.pay.CombinedPayItem;
import cn.abcyun.cis.charge.model.*;
import cn.abcyun.cis.charge.processor.chargerule.*;
import cn.abcyun.cis.charge.processor.discount.*;
import cn.abcyun.cis.charge.processor.limitprice.CalculateLimitPriceItem;
import cn.abcyun.cis.charge.processor.limitprice.LimitPriceProcessor;
import cn.abcyun.cis.charge.processor.stat.StatRecordResult;
import cn.abcyun.cis.charge.protocol.frontend.ChargeFormProtocol;
import cn.abcyun.cis.charge.rpc.model.ChargeSheetTraceCodesRsp;
import cn.abcyun.cis.charge.service.ChargePayHandleOutpatientCenterPayService;
import cn.abcyun.cis.charge.service.ChargeTransactionRecordService;
import cn.abcyun.cis.charge.service.dto.*;
import cn.abcyun.cis.charge.service.dto.print.ChargeFormItemPrintView;
import cn.abcyun.cis.charge.service.dto.print.ChargeFormPrintView;
import cn.abcyun.cis.charge.service.dto.print.ChargeSheetPrintView;
import cn.abcyun.cis.charge.service.dto.print.MedicalBillPrintView;
import cn.abcyun.cis.charge.util.*;
import cn.abcyun.cis.commons.exception.CisCustomException;
import cn.abcyun.cis.commons.exception.ServiceInternalException;
import cn.abcyun.cis.commons.model.ComposeType;
import cn.abcyun.cis.commons.rpc.crm.PatientInfo;
import cn.abcyun.cis.commons.rpc.outpatient.MedicalRecord;
import cn.abcyun.cis.commons.rpc.outpatient.UsageInfo;
import cn.abcyun.cis.commons.rpc.patient.MemberInfo;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrder;
import cn.abcyun.cis.commons.rpc.pay.PayCallbackReq;
import cn.abcyun.cis.commons.rpc.pay.PayStatus;
import cn.abcyun.cis.commons.rpc.registration.RegistrationForm;
import cn.abcyun.cis.commons.rpc.registration.RegistrationSheet;
import cn.abcyun.cis.commons.rpc.wechat.WeChatPayReq;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.common.log.marker.AbcLogMarker;
import com.google.common.collect.Lists;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.abcyun.cis.charge.processor.ChargeSheetFeeProtocol.generateChargeFormPrintViews;

/**
 * 这个类目前又有算费，又有协议输出，职责不够清晰
 * 核心算费的类就应该只负责算费，
 * 协议输出代码重构移动到SheetProcessorFeeProtocol里面
 */
public class SheetProcessor extends SheetProcessorDispatcher.ISheetProcessor {

    private static final Logger sLogger = LoggerFactory.getLogger(SheetProcessor.class);

    String clinicId;

    String chainId;

    List<FormProcessor> formProcessors = new ArrayList<>();

    Map<String, FormProcessor> formProcessorMap = new HashMap<>();

    StatRecordResult refundChargeStatRecordResult = new StatRecordResult();

    /**
     * 包内可见,不暴露太大的可见性，方便协议输出
     */
    SheetProcessorInfoProvider sheetProcessorInfoProvider;

    /**
     * 是否计算零头
     */
    private boolean calculateOddFee = true;

    /**
     * 包内可见,不暴露太大的可见性，方便协议输出
     */
    private ChargeTransactionRecordService chargeTransactionRecordService;

    private Map<String, ChargeExecuteItem> chargeFormItemExecutedCountMap = new HashMap<>();

    private GoodsConfigView goodsConfigView;

    private PatientInfo patientInfo;

    public List<FormProcessor> getFormProcessors() {
        return formProcessors;
    }

    public List<ChargeOweSheet> getChargeOweSheets() {
        return chargeOweSheets;
    }

    public void setChargeOweSheets(List<ChargeOweSheet> chargeOweSheets) {
        this.chargeOweSheets = chargeOweSheets;
    }

    private List<ChargeOweSheet> chargeOweSheets;

    //查询当前门店的药房号列表
    private List<GoodsPharmacyView> goodsPharmacyViews;

    private GoodsLockingSheet goodsLockingSheet;

    /**
     * 本次社保支付受影响的ids，包含空中药房formId或者chargeFormItemId
     */
    private final Set<String> thisTimeHealthCardAffectedIds = new HashSet<>();

    /*private*/ MedicalRecord medicalRecord;

    /*private*/ TherapySheet therapySheet;

    /*private*/ PatientOrder patientOrder;

    /*private*/ int dispensingStatus = Constants.DispensingStatus.NONE;
    /*private*/ Instant dispensedTime = null;
    /*private*/ String dispensedBy = null;

    /*private*/ String memberId;
    /*private*/ MemberInfo memberInfo;

    /*private*/ List<ProcessInfoView> processInfoViews;
    private PatientCardView thisTimePatientCardView;
    /**
     * 包内可见,不暴露太大的可见性，方便协议输出
     */

    /**
     * 本地药房快递规则
     */
    private CalculateExpressDeliveryChargeSheetRsp.ChargeRuleExpressDeliveryInfo localRuleExpressDeliveryInfo;

    /**
     * 虚拟药房快递规则
     */
    private CalculateExpressDeliveryChargeSheetRsp.ChargeRuleExpressDeliveryInfo virtualRuleExpressDeliveryInfo;


    private int payMode = Constants.ChargePayMode.NONE;
    /**
     * 包内可见,不暴露太大的可见性，方便协议输出
     */
    /*private*/ BigDecimal totalFee;            //总费用
    BigDecimal sourceTotalFee; // 原始总费用
    /*private*/ BigDecimal adjustmentFee;       //议价【扣除】【负数】
    /*private*/ BigDecimal discountFee;         //折扣【扣除】【负数】
    BigDecimal unitAdjustmentFee; // 单位议价的和
    /*private*/ BigDecimal _adjustmentDiscountFee;  //议价优惠，已摊薄到各个收费项中，算费时不加入计算
    /*private*/ BigDecimal _adjustmentAddFee;       //议价加价，已摊薄到各个收费项中，算费时不加入计算
    //    private BigDecimal _roundingFee;        //四舍五入
    BigDecimal draftAdjustmentFee = BigDecimal.ZERO;
    /*private*/ BigDecimal receivableFee;       //应收
    BigDecimal sheBaoReceivableFee; // 社保应收
    BigDecimal oddFee = BigDecimal.ZERO;             //系统议价值（四舍五入的配置规则出来的）
    /**
     * 是否需要计算系统议价值：0：计算，1：不计算
     */
    int isCannotCalculateRounding = 1;
    BigDecimal afterRoundingDiscountedTotalFee = BigDecimal.ZERO;
    /*private*/ BigDecimal refundTotalFee;      //退货商品总价格
    /*private*/ BigDecimal refundDiscountFee;   //退货商品总优惠
    /*private*/ BigDecimal refundAdjustmentFee; //退费时的议价
    //    private BigDecimal _refundRoundingFee;  //退费时的四舍五入
    /*private*/ BigDecimal realReceivableFee;   //扣除退货商品后的应收
    //    private BigDecimal _thisTimeRoundingFee;//本次四舍五入金额
    /*private*/ BigDecimal _receivedFee;
    /*private*/ BigDecimal _refundFee;
    /*private*/ BigDecimal _owedRefundFee;
    /*private*/ BigDecimal _netReceivedFee;     //净收入 _receivedFee + _refundFee
    //    private BigDecimal needPayFee;          //还需支付金额
    Integer roundingType;

    //    private ChargeTransaction statChargeTransaction = null;
//    private BigDecimal statRefundAdjustmentFee = BigDecimal.ZERO;
    private StatRecordProcessor statRecordProcessor;
    private String operatorId;

    /*private*/ List<PromotionView> availablePromotionViews; //所有优惠卷返回的协议，checked的是算了优惠值出来的优惠卷
    List<PatientCardPromotionView> availablePatientCardPromotionViews;
    List<PatientPointDeductProductPromotionView> availablePatientPointDeductProductPromotionViews;
    List<VerifyInfoView> availableChargeVerifyInfos;
    /*private*/ List<PromotionView> usedPromotionViews;
    PatientPointsInfoView patientPointsInfoView;

    /*private*/ List<CouponPromotionView> availableCouponPromotionViews = new ArrayList<>();
    /*private*/ List<CouponPromotionView> usedCouponPromotionViews;

    /*private*/ List<GiftRulePromotionView> availableGiftRulePromotionViews = new ArrayList<>();
    /*private*/ List<GiftRulePromotionView> usedGiftRulePromotionViews;

    private List<String> couponIds;

    List<Promotion> discountPromotions = new ArrayList<>(); //会员卡和普通打折优惠
    List<Promotion> couponPromotions = new ArrayList<>();//优惠券
    List<Promotion> giftRulePromotions = new ArrayList<>();//满减满赠
    List<Promotion> patientCardPromotions = new ArrayList<>(); //营销卡项
    List<VerificationItemView> verificationInfo = new ArrayList<>(); //核销信息

    PointPromotionRsp pointPromotionRsp; //积分抵扣规则
    List<ChargePayItemInfo> thisTimeChargePayItemInfos;

    private ChargeRefundSheet chargeRefundSheet;

    private List<DispensingSheetInfo> dispensingSheetInfos;

    private Map<Long, GoodsFeeTypeConstants.GoodsFeeTypeSortDto> feeTypeSortDtoMap;

    ChargeConfigDetailView chargeConfigDetail;

    private List<QueryProcessFeesMatchCodeResBody.ProcessMatchCode> processMatchCodes = new ArrayList<>();


    private Boolean canPayHealthCard;

    public Boolean getCanPayHealthCard() {

        if (Objects.isNull(canPayHealthCard)) {
            boolean temp = false;
            //沈阳诊间支付也要走医保限价
            if (Constants.ChargePayMode.OUTPATIENT_CENTER_PAY == payMode) {
                temp = true;
            }

            if (Constants.ChargePayMode.HEALTH_CARD == payMode) {
                CombinedPayItem payItem = new CombinedPayItem();
                payItem.setPayMode(payMode);
                temp = needCallThirdPartyPay(payItem, chainId, clinicId);
            }
            canPayHealthCard = temp;
        }

        return canPayHealthCard;
    }

    public List<QueryProcessFeesMatchCodeResBody.ProcessMatchCode> getProcessMatchCodes() {
        return processMatchCodes;
    }

    public void setProcessMatchCodes(List<QueryProcessFeesMatchCodeResBody.ProcessMatchCode> processMatchCodes) {
        this.processMatchCodes = processMatchCodes;
    }

    public Map<Long, GoodsFeeTypeConstants.GoodsFeeTypeSortDto> getFeeTypeSortDtoMap() {
        return feeTypeSortDtoMap;
    }

    public void setFeeTypeSortDtoMap(Map<Long, GoodsFeeTypeConstants.GoodsFeeTypeSortDto> feeTypeSortDtoMap) {
        this.feeTypeSortDtoMap = feeTypeSortDtoMap;
    }

    public List<PromotionView> getAvailablePromotionViews() {
        return availablePromotionViews;
    }

    public List<CouponPromotionView> getAvailableCouponPromotionViews() {
        return availableCouponPromotionViews;
    }

    public List<GiftRulePromotionView> getAvailableGiftRulePromotionViews() {
        return availableGiftRulePromotionViews;
    }

    public List<ChargeSheetProcessInfo> generateToSaveChargeSheetProcessInfos() {

        return chargeSheet.getChargeForms().stream()
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.PROCESS)
                .filter(chargeForm -> chargeForm.getProcessInfo() != null)
                .map(ChargeForm::getProcessInfo)
                .collect(Collectors.toList());
    }

    enum CalculateScene {
        PAY,
        PAY_FOR_LEFT,
        REFUND;

        boolean isRefund() {
            return this.equals(REFUND);
        }
    }

    public SheetProcessor(ChargeSheet chargeSheet) {
        this.chargeSheet = chargeSheet;
        this.clinicId = chargeSheet.getClinicId();
        this.chainId = chargeSheet.getChainId();
    }

    private void addFormProcessor(FormProcessor formProcessor) {
        if (formProcessor == null) {
            return;
        }
        formProcessors.add(formProcessor);
        formProcessorMap.put(formProcessor.getChargeFormId(), formProcessor);
    }

    public void setChargeTransactionRecordService(ChargeTransactionRecordService chargeTransactionRecordService) {
        this.chargeTransactionRecordService = chargeTransactionRecordService;
    }

    public BigDecimal getRealReceivableFee(boolean needCalculate) {
        if (needCalculate) {
            doCalculate(BigDecimal.ZERO, CalculateScene.PAY);
        }
        return realReceivableFee;
    }

    public void setMedicalRecord(MedicalRecord medicalRecord) {
        this.medicalRecord = medicalRecord;
    }

    public void setPatientOrder(PatientOrder patientOrder) {
        this.patientOrder = patientOrder;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public void setSheetProcessorInfoProvider(SheetProcessorInfoProvider sheetProcessorInfoProvider) {
        this.sheetProcessorInfoProvider = sheetProcessorInfoProvider;
    }

    public void setPayMode(int payMode) {
        this.payMode = payMode;
    }

    public void setPatientInfo(PatientInfo patientInfo) {
        this.patientInfo = patientInfo;
    }

    public PatientInfo getPatientInfo() {
        return patientInfo;
    }

    /**
     * ChargeSheet算费类，build主要是给chargeForm以及ChargeFormItem生成对应的processor
     */
    public void build() {
        super.build();
        if (chargeSheet.getChargeForms() != null) {
            for (ChargeForm chargeForm : chargeSheet.getChargeForms()) {
                if (chargeForm.getIsDeleted() != 0) {
                    continue;
                }

                FormProcessor formProcessor = new FormProcessor(chargeForm, getDeletedDataCollector());
                formProcessor.build();//递归为里面的chargeFormItem生成processor
                formProcessors.add(formProcessor);
            }
        } else {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "build, no charge forms");
        }
        formProcessorMap = ListUtils.toMap(formProcessors, FormProcessor::getChargeFormId);

        //给本地药房的中药处方绑定加工费的formId
        Optional.ofNullable(chargeSheet.getChargeForms()).orElse(new ArrayList<>()).stream()
                .filter(c -> c.getIsDeleted() == 0)
                .filter(c -> c.getSourceFormType() == Constants.SourceFormType.PROCESS)
                .map(ChargeForm::getProcessInfo)
                .filter(Objects::nonNull)
                .forEach(processInfo -> {
                    if (StringUtils.isNotEmpty(processInfo.getChargeFormId()) && StringUtils.isNotEmpty(processInfo.getProcessFormId())) {
                        Optional.ofNullable(formProcessorMap.get(processInfo.getChargeFormId()))
                                .ifPresent(chinesePrescriptionForm -> chinesePrescriptionForm.setProcessFormProcessor(formProcessorMap.get(processInfo.getProcessFormId())));
                    }
                });

        statRecordProcessor = new StatRecordProcessor(chargeSheet.getId(), chargeSheet.getChargeVersion(), operatorId);
        statRecordProcessor.setChargeTransactionRecordService(chargeTransactionRecordService);
        memberId = chargeSheet.getMemberId();
    }

    @Override
    public SheetProcessorInfoProvider getSheetProcessorInfoProvider() {
        return sheetProcessorInfoProvider;
    }

    public List<String> getCouponIds() {
        return couponIds;
    }

    private void doCalculate(BigDecimal adjustmentFeeOnPay, CalculateScene calculateScene) {
        if (chargeSheet.getType() == ChargeSheet.Type.MEMBER_RECHARGE) {
            totalFee = chargeSheet.getTotalFee();
            sourceTotalFee = chargeSheet.getTotalFee();
        } else {
            totalFee = MathUtils.setScaleTwo(formProcessors.stream()
                    .map(formProcessor -> formProcessor.getTotalPrice(calculateScene == CalculateScene.REFUND))
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            sourceTotalFee = MathUtils.setScaleTwo(formProcessors.stream()
                    .map(FormProcessor::getSourceTotalPrice)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
        }

        unitAdjustmentFee = MathUtils.setScaleTwo(formProcessors.stream()
                .map(FormProcessor::getUnitAdjustmentFee)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add));

        discountFee = MathUtils.setScaleTwo(formProcessors.stream()
                .map(formProcessor -> formProcessor.getDiscountPrice(calculateScene == CalculateScene.REFUND))
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add));

        refundTotalFee = MathUtils.setScaleTwo(formProcessors.stream()
                .map(formProcessor -> formProcessor.getRefundTotalPrice(calculateScene == CalculateScene.REFUND))
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add));

        refundDiscountFee = MathUtils.setScaleTwo(formProcessors.stream()
                .map(formProcessor -> formProcessor.getRefundDiscountPrice(calculateScene == CalculateScene.REFUND))
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add));

        adjustmentFee = calculateAdditionalFee(chargeSheet.getAdditionalFees(), ChargeAdditionalFee.AdditionalFeeType.ADJUSTMENT_FEE, false);
        refundAdjustmentFee = calculateAdditionalFee(chargeSheet.getAdditionalFees(), ChargeAdditionalFee.AdditionalFeeType.ADJUSTMENT_FEE, true);
        _adjustmentDiscountFee = calculateAdditionalFee(chargeSheet.getAdditionalFees(), ChargeAdditionalFee.AdditionalFeeType.ADJUSTMENT_DISCOUNT_FEE, false);
        _adjustmentAddFee = calculateAdditionalFee(chargeSheet.getAdditionalFees(), ChargeAdditionalFee.AdditionalFeeType.ADJUSTMENT_ADD_FEE, false);
        _owedRefundFee = calculateAdditionalFee(chargeSheet.getAdditionalFees(), ChargeAdditionalFee.AdditionalFeeType.OWED_REFUND_FEE);

        if (calculateScene == CalculateScene.REFUND) {
            refundAdjustmentFee = MathUtils.setScaleTwo(refundAdjustmentFee.add(adjustmentFeeOnPay));
        } else {
            adjustmentFee = MathUtils.setScaleTwo(adjustmentFee.add(adjustmentFeeOnPay));
        }

        receivableFee = totalFee.add(discountFee).add(adjustmentFee);

//        _roundingFee = calculateAdditionalFee(chargeSheet.getAdditionalFees(), ChargeAdditionalFee.AdditionalFeeType.CHARGE_ROUNDING_FEE, false);
//        _refundRoundingFee = calculateAdditionalFee(chargeSheet.getAdditionalFees(), ChargeAdditionalFee.AdditionalFeeType.CHARGE_ROUNDING_FEE, true);

        _receivedFee = MathUtils.setScaleTwo(calculateTransactionsTotalReceivedFee(chargeSheet.getChargeTransactions()));
        _refundFee = MathUtils.setScaleTwo(calculateTransactionsTotalRefundFee(chargeSheet.getChargeTransactions()));
        _netReceivedFee = _receivedFee.add(_refundFee);

//        _thisTimeRoundingFee = BigDecimal.ZERO;
        realReceivableFee = receivableFee.add(_owedRefundFee).subtract(refundAdjustmentFee).subtract(refundTotalFee.add(refundDiscountFee)).subtract(_receivedFee.add(_refundFee));

        sLogger.info("doCalculate id:{}, totalFee:{}, discountFee:{}, refundTotalFee:{}, refundDiscountFee:{}, " +
                        "adjustmentFee:{}, refundAdjustmentFee:{}, adjustmentDiscountFee:{}, adjustmentAddFee:{}, oddFee: {}, owedRefundFee:{}, receivableFee:{}, realReceivableFee:{}, receivedFee:{}, refundFee:{}",
                chargeSheet.getId(), totalFee, discountFee, refundTotalFee, refundDiscountFee, adjustmentFee, refundAdjustmentFee, _adjustmentDiscountFee, _adjustmentAddFee, oddFee, _owedRefundFee, receivableFee,
                realReceivableFee, _receivedFee, _refundFee);
    }

    private boolean isNeedCalculateSystemAdjustmentFee() {
        return chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED
                && !ChargeSheet.Type.notNeedCalculateSystemAdjustmentFee().contains(chargeSheet.getType());
    }

    private BigDecimal calculateRoundingResult(BigDecimal totalFee) {
        if (totalFee == null || totalFee.compareTo(BigDecimal.ZERO) == 0) {
            return totalFee;
        }
        if (sheetProcessorInfoProvider.getChargePayModeProvider() == null) {
            return totalFee;
        }
        ChargeConfigDetailView chargeConfigDetail = getChargeConfigDetailView();
        if (chargeConfigDetail == null) {
            return totalFee;
        }
        roundingType = chargeConfigDetail.getRoundingType();
        return MathUtils.calculateRoundingResult(totalFee, chargeConfigDetail.getRoundingType());
    }

    private ChargeConfigDetailView getChargeConfigDetailView() {
        if (Objects.nonNull(chargeConfigDetail)) {
            return chargeConfigDetail;
        }

        loadChargeConfig();

        return chargeConfigDetail;
    }

    public CalculateProcessChargeRsp calculateProcessSheetFee(boolean isNeedUpdateProduct) {
        if (isNeedUpdateProduct) {
            updateProductInfo(getIsNeedUseLimitPrice(), false, 0, 1);
        }
        doCalculateProcessFee(false);
        CalculateProcessChargeRsp rsp = new CalculateProcessChargeRsp();
        rsp.setProcessInfos(processInfoViews);
        return rsp;
    }

    @Override
    public ChargeSheetTraceCodesRsp generateChargeSheetTraceCodesRsp() {
        return null;
    }

    public CalculateExpressDeliveryChargeSheetRsp calculateExpressDeliveryFee(int calSource, int calculateDeliveryScene) throws ServiceInternalException {
        CalculateExpressDeliveryChargeSheetRsp rsp = new CalculateExpressDeliveryChargeSheetRsp();

        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED) {
            return rsp;
        }

        updateProductInfo(getIsNeedUseLimitPrice(), false, 1, 0);

        BigDecimal expressDeliveryFee = BigDecimal.ZERO;
        if (calculateDeliveryScene == CalculateExpressDeliveryChargeSheetReq.Scene.CHARGE_SHEET) {
            /**
             * 这是算快递费的接口：就是想看一下快递费多少，不管你是什么到付，寄付都算一下。
             * 所以这里强制传 true
             * */
            boolean isNeedUserPayDeliveryFee = true;
            expressDeliveryFee = doCalculateExpressDeliveryFeeForLocal(calSource, isNeedUserPayDeliveryFee);

            if (chargeSheet.getDeliveryInfo() != null) {
                ChargeDeliveryInfoView chargeDeliveryInfoView = new ChargeDeliveryInfoView();
                BeanUtils.copyProperties(chargeSheet.getDeliveryInfo(), chargeDeliveryInfoView);

                if (chargeSheet.getDeliveryInfo().getDeliveryCompany() != null) {
                    ChargeDeliveryCompanyVo chargeDeliveryCompanyVo = new ChargeDeliveryCompanyVo();
                    BeanUtils.copyProperties(chargeSheet.getDeliveryInfo().getDeliveryCompany(), chargeDeliveryCompanyVo);
                    chargeDeliveryInfoView.setDeliveryCompany(chargeDeliveryCompanyVo);
                }
                rsp.setDeliveryInfo(chargeDeliveryInfoView);
            }
            rsp.setExpressDeliveryFee(expressDeliveryFee);
            rsp.setRuleExpressDeliveryInfo(localRuleExpressDeliveryInfo);
        } else if (calculateDeliveryScene == CalculateExpressDeliveryChargeSheetReq.Scene.VIRTUAL_PHARMACY) {
            FormProcessor virtualPharmacyFormProcessor = formProcessors.stream().filter(formProcessor -> formProcessor.getPharmacyType() == GoodsConst.PharmacyType.VIRTUAL_PHARMACY).findFirst().orElse(null);
            VirtualPharmacyCalculateDeliveryFeeDto virtualPharmacyCalculateDeliveryFeeDto = doCalculateExpressDeliveryFeeForVirtual(calSource, virtualPharmacyFormProcessor);
            if (virtualPharmacyCalculateDeliveryFeeDto != null) {
                rsp.setExpressDeliveryFee(virtualPharmacyCalculateDeliveryFeeDto.getExpressDeliveryFee());
                rsp.setRuleExpressDeliveryInfo(virtualPharmacyCalculateDeliveryFeeDto.getVirtualRuleExpressDeliveryInfo());
            }
        }


        if (rsp.getRuleExpressDeliveryInfo() != null) {
            rsp.setIsMarkRule(1);
            //将ruleExpressDeliveryInfo的companyId填入deliveryInfo中
            if (rsp.getDeliveryInfo() != null && rsp.getDeliveryInfo().getDeliveryCompany() != null && StringUtils.isEmpty(rsp.getDeliveryInfo().getDeliveryCompany().getId())) {
                rsp.getDeliveryInfo().getDeliveryCompany().setId(rsp.getRuleExpressDeliveryInfo().getCompanyId());
            }
        }
        return rsp;
    }

    public List<CalculateVirtualPharmacyExpressDeliveryChargeSheetRsp> calculateVirtualPharmacyExpressDeliveryFee(int calSource) throws ServiceInternalException {
        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED) {
            return new ArrayList<>();
        }
        updateProductInfo(getIsNeedUseLimitPrice(), false, 1, 0);
        return formProcessors
                .stream()
                .filter(formProcessor -> formProcessor.getPharmacyType() == GoodsConst.PharmacyType.VIRTUAL_PHARMACY)
                .map(virtualPharmacyFormProcessor -> {
                    CalculateVirtualPharmacyExpressDeliveryChargeSheetRsp rsp = new CalculateVirtualPharmacyExpressDeliveryChargeSheetRsp();
                    rsp.setKeyId(virtualPharmacyFormProcessor.getChargeForm().getKeyId());
                    VirtualPharmacyCalculateDeliveryFeeDto virtualPharmacyCalculateDeliveryFeeDto = doCalculateExpressDeliveryFeeForVirtual(calSource, virtualPharmacyFormProcessor);
                    if (virtualPharmacyCalculateDeliveryFeeDto != null) {
                        rsp.setExpressDeliveryFee(virtualPharmacyCalculateDeliveryFeeDto.getExpressDeliveryFee());
                        rsp.setRuleExpressDeliveryInfo(virtualPharmacyCalculateDeliveryFeeDto.getVirtualRuleExpressDeliveryInfo());
                        if (rsp.getRuleExpressDeliveryInfo() != null) {
                            rsp.setIsMarkRule(1);
                            ChargeAirPharmacyLogistics virtualPharmacyLogistics = virtualPharmacyFormProcessor.getChargeForm().getChargeAirPharmacyLogistics();
                            if (virtualPharmacyLogistics != null) {
                                ChargeDeliveryInfoView deliveryInfoView = new ChargeDeliveryInfoView();
                                BeanUtils.copyProperties(virtualPharmacyLogistics, deliveryInfoView);
                                rsp.setDeliveryInfo(deliveryInfoView);
                            }
                            //将ruleExpressDeliveryInfo的companyId填入deliveryInfo中
                            if (rsp.getDeliveryInfo() != null && rsp.getDeliveryInfo().getDeliveryCompany() != null && StringUtils.isEmpty(rsp.getDeliveryInfo().getDeliveryCompany().getId())) {
                                rsp.getDeliveryInfo().getDeliveryCompany().setId(rsp.getRuleExpressDeliveryInfo().getCompanyId());
                            }
                        }
                    }
                    return rsp;
                })
                .collect(Collectors.toList());
    }


    /**
     * 计算快递费
     * 前置条件：要有快递规则 &寄送地址
     * calSource 算费场景
     *
     * @param isNeedUserPayDeliveryFee 是否需要算快递费 false 不算快递费快递费就是0
     */
    private BigDecimal doCalculateExpressDeliveryFeeForLocal(int calSource, boolean isNeedUserPayDeliveryFee) {
        if (!isNeedUserPayDeliveryFee) {  //不需要快递费
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "doCalculateExpressDeliveryFeeForLocal 0 收费单不需要算快递费");
            return BigDecimal.ZERO;
        }

        //参数检查
        if (sheetProcessorInfoProvider.getChargeRuleProvider() == null || chargeSheet.getDeliveryInfo() == null) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "doCalculateExpressDeliveryFeeForLocal 0 没有快递信息");
            return BigDecimal.ZERO;
        }

        //地址是否完整
        if (!chargeSheet.getDeliveryInfo().isAvailableAddress()) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "doCalculateExpressDeliveryFeeForLocal 0 没有完整的收货地址");
            return BigDecimal.ZERO;
        }

        //找诊所的寄送规则
        String addressProvinceId = chargeSheet.getDeliveryInfo().getAddressProvinceId();
        String addressCityId = chargeSheet.getDeliveryInfo().getAddressCityId();
        String addressDistrictId = chargeSheet.getDeliveryInfo().getAddressDistrictId();
        String deliveryCompanyId = chargeSheet.getDeliveryInfo().getDeliveryCompanyId();
        int deliveryPayType = chargeSheet.getDeliveryInfo().getDeliveryPayType();
        List<String> arrNameAddresses = Arrays.asList(chargeSheet.getDeliveryInfo().getAddressProvinceName(),
                chargeSheet.getDeliveryInfo().getAddressCityName(),
                chargeSheet.getDeliveryInfo().getAddressDistrictName()
        );

        List<ItemProcessor> itemProcessors = formProcessors.stream()
                .filter(formProcessor -> !formProcessor.isAirPharmacy() && formProcessor.getPharmacyType() != GoodsConst.PharmacyType.VIRTUAL_PHARMACY)
                .flatMap(formProcessor -> formProcessor.getItemProcessorList().stream()).collect(Collectors.toList());

        List<ChargeRuleExpressDelivery> chargeRuleExpressDeliveries = sheetProcessorInfoProvider.getChargeRuleProvider().findChargeRuleExpressDelivery(chargeSheet.getChainId(), chargeSheet.getClinicId(), addressProvinceId, addressCityId, addressDistrictId, deliveryCompanyId, deliveryPayType);

        ChargeRuleExpressDelivery chargeRuleExpressDelivery = null;
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(deliveryCompanyId)) {
            chargeRuleExpressDelivery = org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeRuleExpressDeliveries) ? chargeRuleExpressDeliveries.get(0) : null;
        } else if (org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeRuleExpressDeliveries)) {
            chargeRuleExpressDelivery = chargeRuleExpressDeliveries.stream().sorted(Comparator.comparing(ChargeRuleExpressDelivery::getCreated)).findFirst().orElse(null);
        }

        if (Constants.ChargeSource.patientPaySources().contains(calSource) && chargeRuleExpressDelivery == null) {

            //判断地址是否改过，如果没改过，就直接用数据库的价格进行算费
            boolean deliveryChanged = checkChargeSheetDeliveryChanged();

            if (!deliveryChanged) {
                return chargeSheet.getChargeForms().stream()
                        .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                        .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.EXPRESS_DELIVERY)
                        .filter(chargeForm -> chargeForm.getChargeFormItems() != null)
                        .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                        .filter(item -> item.getIsDeleted() == 0)
                        .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.EXPRESS_DELIVERY)
                        .findFirst().map(ChargeFormItem::getUnitPrice).orElse(BigDecimal.ZERO);
            }

        }
        localRuleExpressDeliveryInfo = createChargeRuleExpressDeliveryInfo(chargeRuleExpressDelivery);
        return calculateDeliveryFeeCore(calSource,
                itemProcessors,
                chargeRuleExpressDelivery,
                addressProvinceId,
                addressCityId,
                addressDistrictId,
                deliveryCompanyId,
                arrNameAddresses);
    }

    private boolean checkChargeSheetDeliveryChanged() {
        if (chargeSheet.getDeliveryInfo() == null || chargeSheet.getDbDeliveryInfo() == null) {
            return true;
        }
        ChargeDeliveryInfo deliveryInfo = chargeSheet.getDeliveryInfo();
        ChargeDeliveryInfo dbDeliveryInfo = chargeSheet.getDbDeliveryInfo();
        return !StringUtils.equals(deliveryInfo.getAddressProvinceId(), dbDeliveryInfo.getAddressProvinceId())
                || !StringUtils.equals(deliveryInfo.getAddressCityId(), dbDeliveryInfo.getAddressCityId())
                || !StringUtils.equals(deliveryInfo.getAddressDistrictId(), dbDeliveryInfo.getAddressDistrictId())
                || !StringUtils.equals(deliveryInfo.getDeliveryCompanyId(), Optional.ofNullable(dbDeliveryInfo.getDeliveryCompany()).map(ChargeDeliveryCompany::getId).orElse(""))
                || deliveryInfo.getDeliveryPayType() != dbDeliveryInfo.getDeliveryPayType();
    }

    /**
     * 计算虚拟药房的快递费
     * 前置条件：要有快递规则 &寄送地址
     * calSource 算费场景
     */
    private VirtualPharmacyCalculateDeliveryFeeDto doCalculateExpressDeliveryFeeForVirtual(int calSource, FormProcessor formProcessor) {
        VirtualPharmacyCalculateDeliveryFeeDto virtualPharmacyCalculateDeliveryFeeDto = new VirtualPharmacyCalculateDeliveryFeeDto();
        ChargeAirPharmacyLogistics chargeAirPharmacyLogistics = Optional.ofNullable(formProcessor).map(FormProcessor::getChargeForm).map(ChargeForm::getChargeAirPharmacyLogistics).orElse(null);
        // 参数检查
        if (sheetProcessorInfoProvider.getChargeRuleProvider() == null || formProcessor == null || chargeAirPharmacyLogistics == null) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "doCalculateExpressDeliveryFeeForVirtual 0 没有快递信息");
            return virtualPharmacyCalculateDeliveryFeeDto;
        }

        // 地址是否完整
        if (!chargeAirPharmacyLogistics.isAvailableAddress()) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "doCalculateExpressDeliveryFeeForVirtual 0 没有完整的收货地址");
            return virtualPharmacyCalculateDeliveryFeeDto;
        }

        // 找诊所的寄送规则
        String addressProvinceId = chargeAirPharmacyLogistics.getAddressProvinceId();
        String addressCityId = chargeAirPharmacyLogistics.getAddressCityId();
        String addressDistrictId = chargeAirPharmacyLogistics.getAddressDistrictId();
        String deliveryCompanyId = chargeAirPharmacyLogistics.getDeliveryCompanyId();
        List<String> arrNameAddresses = Arrays.asList(chargeAirPharmacyLogistics.getAddressProvinceName(), chargeAirPharmacyLogistics.getAddressCityName(), chargeAirPharmacyLogistics.getAddressDistrictName());
        List<ItemProcessor> itemProcessors = new ArrayList<>(formProcessor.getItemProcessorList());

        // 兼容老的算费逻辑
        List<ChargeRuleExpressDelivery> chargeRuleExpressDeliveries = sheetProcessorInfoProvider.getChargeRuleProvider().findChargeRuleExpressDelivery(chargeSheet.getChainId(), chargeSheet.getClinicId(), addressProvinceId, addressCityId, addressDistrictId, deliveryCompanyId, Constants.DeliveryPayType.PAID_BY_SHIPPER);

        ChargeRuleExpressDelivery chargeRuleExpressDelivery = null;
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(deliveryCompanyId)) {
            chargeRuleExpressDelivery = org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeRuleExpressDeliveries) ? chargeRuleExpressDeliveries.get(0) : null;
        } else if (org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeRuleExpressDeliveries)) {
            chargeRuleExpressDelivery = chargeRuleExpressDeliveries.stream().min(Comparator.comparing(ChargeRuleExpressDelivery::getCreated)).orElse(null);
        }

        // 如果companyId为空，则将查出来的company填入chargeAirPharmacyLogistics中
        if (StringUtils.isEmpty(deliveryCompanyId) && chargeRuleExpressDelivery != null) {
            chargeAirPharmacyLogistics.setDeliveryCompanyId(chargeRuleExpressDelivery.getCompanyId());
            chargeAirPharmacyLogistics.setDeliveryCompany(new ChargeAirPharmacyLogisticsReq.LogisticsCompanyReq().setId(chargeAirPharmacyLogistics.getDeliveryCompanyId()));
            formProcessor.getChargeForm().getChargeFormItems().stream()
                    .filter(item -> item.getIsDeleted() == 0)
                    .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.EXPRESS_DELIVERY)
                    .forEach(chargeFormItem -> {
                        chargeFormItem.setProductSnapshot(JsonUtils.dump(chargeAirPharmacyLogistics));
                        chargeFormItem.setProductInfo(JsonUtils.dumpAsJsonNode(chargeAirPharmacyLogistics));
                    });
        }

        if (Constants.ChargeSource.patientPaySources().contains(calSource) && chargeRuleExpressDelivery == null) {
            // 判断地址是否改过，如果没改过，就直接用数据库的价格进行算费
            boolean deliveryChanged = formProcessor.checkVirtualDeliveryChanged();
            if (!deliveryChanged) {
                virtualPharmacyCalculateDeliveryFeeDto.setExpressDeliveryFee(
                        formProcessor.getChargeForm().getChargeFormItems().stream()
                                .filter(item -> item.getIsDeleted() == 0)
                                .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.EXPRESS_DELIVERY)
                                .findFirst().map(ChargeFormItem::getUnitPrice).orElse(BigDecimal.ZERO));
                return virtualPharmacyCalculateDeliveryFeeDto;
            }
        }

        virtualRuleExpressDeliveryInfo = createChargeRuleExpressDeliveryInfo(chargeRuleExpressDelivery);
        virtualPharmacyCalculateDeliveryFeeDto.setVirtualRuleExpressDeliveryInfo(virtualRuleExpressDeliveryInfo);
        virtualPharmacyCalculateDeliveryFeeDto.setExpressDeliveryFee(
                calculateDeliveryFeeCore(calSource,
                        itemProcessors,
                        chargeRuleExpressDelivery,
                        addressProvinceId,
                        addressCityId,
                        addressDistrictId,
                        deliveryCompanyId,
                        arrNameAddresses)
        );
        return virtualPharmacyCalculateDeliveryFeeDto;
    }

    private BigDecimal calculateDeliveryFeeCore(int calSource, List<ItemProcessor> itemProcessors, ChargeRuleExpressDelivery chargeRuleExpressDelivery, String addressProvinceId, String addressCityId, String addressDistrictId, String deliveryCompanyId, List<String> arrNameAddresses) {
        BigDecimal expressDeliveryFee = BigDecimal.ZERO;

        if (chargeRuleExpressDelivery == null) { //没算成费，返回失败，至少避免少收钱
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "calculateDeliveryFeeCore 0 诊所没有找到快递规则 chainId={},clinicId={},address:{},{},{},{}",
                    chargeSheet.getChainId(), chargeSheet.getClinicId(), addressProvinceId, addressCityId, addressDistrictId, deliveryCompanyId);
            String fullAddressName = arrNameAddresses.stream().filter(Objects::nonNull).filter(str -> !str.isEmpty()).collect(Collectors.joining(""));
            //自助支付单一定要强校验配送地址
            boolean selfPayChargeSheet = chargeSheet.getAdditional() != null && chargeSheet.getAdditional().getSelfPayStatus() == Constants.ChargeSheetSelfPayStatus.SELF_PAY_WAITING_PAY;
            if (ChargeUtils.deliveryPaidByShipper(chargeSheet) && (calSource == Constants.ChargeSource.WE_CLINIC || calSource == Constants.ChargeSource.WE_APP) && selfPayChargeSheet) { //非自助支付，还是要让收费台付款
                if (!StringUtils.isEmpty(fullAddressName)) {
                    String errorMsg = "你选择的寄送地址:" + fullAddressName + "不在机构寄送支持范围，请联系医生或修改寄送地址";
                    throw new CisCustomException(ChargeServiceError.NO_DELIVERY_COMPANY.getCode(), errorMsg);
                } else {
                    throw new ChargeServiceException(ChargeServiceError.NO_DELIVERY_COMPANY);
                }
            }
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "calculateDeliveryFeeCore 0 找到快递公司的规则");
            return BigDecimal.ZERO;
        }

        //开始算费
        CalculateExpressDeliveryFeeHelper calculateExpressDeliveryFeeHelper = new CalculateExpressDeliveryFeeHelper(chargeRuleExpressDelivery);

        expressDeliveryFee = calculateExpressDeliveryFeeHelper.calculateExpressDeliveryFee(itemProcessors);
        if (expressDeliveryFee.compareTo(BigDecimal.ZERO) <= 0) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "calculateDeliveryFeeCore {}  rule {}", expressDeliveryFee, JsonUtils.dump(chargeRuleExpressDelivery));
        }

        return expressDeliveryFee;
    }

    private CalculateExpressDeliveryChargeSheetRsp.ChargeRuleExpressDeliveryInfo createChargeRuleExpressDeliveryInfo(ChargeRuleExpressDelivery chargeRuleExpressDelivery) {
        ChargeRuleLadderInfoVo vo = null;

        if (Objects.isNull(chargeRuleExpressDelivery)) {
            return null;
        }

        if (chargeRuleExpressDelivery.getLadderInfo() != null) {
            vo = new ChargeRuleLadderInfoVo();
            BeanUtils.copyProperties(chargeRuleExpressDelivery.getLadderInfo(), vo);
        }

        String ruleInfo = ChargeSheetProcessInfoUtils.getRuleInfo(chargeRuleExpressDelivery.getType(), chargeRuleExpressDelivery.getPrice(), vo);

        if (!StringUtils.isEmpty(ruleInfo) && chargeRuleExpressDelivery.getIsFreePostage() == 1) {
            ruleInfo = ruleInfo + "," + ChargeSheetProcessInfoUtils.getFreePostageRuleInfo(chargeRuleExpressDelivery.getFreePostageType(), chargeRuleExpressDelivery.getFreePostageUnitCount(), chargeRuleExpressDelivery.getFreePostageUnit());
        }

        CalculateExpressDeliveryChargeSheetRsp.ChargeRuleExpressDeliveryInfo ruleExpressDeliveryInfo = new CalculateExpressDeliveryChargeSheetRsp.ChargeRuleExpressDeliveryInfo();
        ruleExpressDeliveryInfo.setId(chargeRuleExpressDelivery.getId());
        ruleExpressDeliveryInfo.setName(chargeRuleExpressDelivery.getName());
        ruleExpressDeliveryInfo.setRuleInfo(ruleInfo);
        ruleExpressDeliveryInfo.setCompanyId(chargeRuleExpressDelivery.getCompanyId());
        ruleExpressDeliveryInfo.setFeeTypeId(chargeRuleExpressDelivery.getFeeTypeId());
        return ruleExpressDeliveryInfo;
    }

    private void doCalculateProcessFee(boolean isForPaid) {
        //所有的加工方式
        List<ChargeSheetProcessInfo> processInfos = Optional.ofNullable(chargeSheet.getChargeForms()).orElse(new ArrayList<>()).stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.PROCESS)
                .map(ChargeForm::getProcessInfo)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(processInfos)) {
            return;
        }
        // 里面有0，0表示无加工用于终端展示，计费要把这条去掉
        List<FindChargeRuleProcessesInfo> processesInfos = ChargeUtils.generateFindChargeRuleProcessesInfos(processInfos, isForPaid);
        // view
        processInfoViews = processInfos.stream().map(processInfo -> {
            ProcessInfoView processInfoView = new ProcessInfoView();
            BeanUtils.copyProperties(processInfo, processInfoView);
            return processInfoView;
        }).collect(Collectors.toList());

        // 数据结构转化，一会算出来的快递费会放到里面，这里被修改了，processInfoViews指向的内容也会发生变化。
        Map<String, ChargeSheetProcessInfo> processInfoMap = ListUtils.toMap(processInfos, ChargeSheetProcessInfo::getId);
        Map<String, ProcessInfoView> processInfoRspMap = ListUtils.toMap(processInfoViews, ProcessInfoView::getId);
        Map<String, ChargeForm> chargeFormMap = ListUtils.toMap(chargeSheet.getChargeForms()
                        .stream()
                        .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                        .collect(Collectors.toList()),
                ChargeForm::getId);

        if (!CollectionUtils.isEmpty(processesInfos)) {
            // 加工算费规则，有个这个玩意才能算加工费
            ChargeRuleProcessResult chargeRuleProcessResult = sheetProcessorInfoProvider.getChargeRuleProvider().findChargeRuleProcesses(chargeSheet.getChainId(), chargeSheet.getClinicId(), processesInfos);
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeRuleProcessResult:指定规则{},选定的规则：{}", JsonUtils.dump(processesInfos), JsonUtils.dump(chargeRuleProcessResult));
            List<ChargeRuleProcessResult.ChargeRuleProcessRsp> chargeRuleProcesses = Optional.ofNullable(chargeRuleProcessResult).map(ChargeRuleProcessResult::getChargeRuleProcesses).orElse(Lists.newArrayList());
            if (CollectionUtils.isEmpty(chargeRuleProcesses)) {
                return;
            }


            chargeRuleProcesses.forEach(chargeRuleProcess -> {
                CalculateProcessFeeHelper calculateProcessFeeHelper = new CalculateProcessFeeHelper(chargeRuleProcess);
                for (String chargeSheetProcessInfoId : chargeRuleProcess.getChargeSheetProcessInfoIds()) {
                    ChargeSheetProcessInfo processInfo = processInfoMap.get(chargeSheetProcessInfoId);
                    if (processInfo != null) {
                        List<CalculateProcessFeeHelper.CalculateProcessFeeCell> cells = formProcessors.stream()
                                .filter(formProcessor -> TextUtils.equals(formProcessor.getFormId(), processInfo.getChargeFormId()))
                                .filter(formProcessor -> formProcessor.getItemProcessorList() != null)
                                .flatMap(formProcessor -> formProcessor.getItemProcessorList().stream())
                                .filter(itemProcessor -> itemProcessor.getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED)
                                .map(itemProcessor -> {
                                    ChargeFormItem chargeFormItem = itemProcessor.getChargeFormItem();
                                    CalculateProcessFeeHelper.CalculateProcessFeeCell cell = new CalculateProcessFeeHelper.CalculateProcessFeeCell();
                                    return cell.setName(chargeFormItem.getName())
                                            .setUnit(chargeFormItem.getUnit())
                                            .setProductType(chargeFormItem.getProductType())
                                            .setProductSubType(chargeFormItem.getProductSubType())
                                            .setDoseCount(chargeFormItem.getDoseCount())
                                            .setUnitCount(chargeFormItem.getUnitCount());
                                })
                                .collect(Collectors.toList());
                        CalculateProcessFeeResult result = calculateProcessFeeHelper.calculateProcessFee(cells, processInfo);
                        ProcessInfoView processInfoView = processInfoRspMap.get(chargeSheetProcessInfoId);
                        if (processInfoView != null) {
                            processInfoView.setProcessFee(Optional.ofNullable(result).map(CalculateProcessFeeResult::getFee).orElse(BigDecimal.ZERO));
                            processInfoView.setProcessUsageInfo(JsonUtils.dumpAsJsonNode(chargeRuleProcess.getProcessUsage()));
                            processInfoView.setRuleInfo(Optional.of(chargeRuleProcess).map(ChargeRuleProcessResult.ChargeRuleProcessRsp::getProcessUsage).map(ChargeRuleProcessUsageView::getRuleInfo).orElse(null));
                            processInfoView.setName(Optional.of(chargeRuleProcess).map(ChargeRuleProcessResult.ChargeRuleProcessRsp::getProcessUsage).map(ChargeRuleProcessUsageView::getProcessName).orElse(Constants.SystemProductId.PROCESS_NAME));
                        }
                    }
                }
            });

        }

        if (!CollectionUtils.isEmpty(processInfoViews)) {
            processInfoViews.forEach(item -> {
                ChargeForm chargeForm = chargeFormMap.get(item.getChargeFormId());
                if (chargeForm == null) {
                    return;
                }
                UsageInfo usageInfo = JsonUtils.readValue(chargeForm.getUsageInfoJson(), UsageInfo.class);
                if (usageInfo != null) {
                    usageInfo.setProcessBagUnitCountDecimal(item.getBagUnitCountDecimal());
                    usageInfo.setProcessRemark(item.getProcessRemark());
                    usageInfo.setTotalProcessCount(item.getTotalProcessCount());
                    chargeForm.setUsageInfoJson(JsonUtils.dump(usageInfo));
                    chargeForm.setUsageInfo(JsonUtils.loadAsJsonNode(chargeForm.getUsageInfoJson()));
                }
            });
        }
        // 到这里加工费已经算出来了，在processInfoViews里面
        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "processInfoViews: {}", JsonUtils.dump(processInfoViews));
    }

    /**
     * chargeSheet已经准备OK，可以开始算费
     * 这几个参数都是端上上传的参数
     *
     * @param promotionReqs 会员或普通折扣信息
     *                      couponPromotionReqs 团购
     *                      giftRulePromotionReqs
     *                      processInfoReqs ---加工费，代码里面没有
     *                      payForLeft
     *                      <p>
     *                      算费逻辑的理解：
     */
    public CalculateChargeResult calculateSheetFee(BigDecimal expectedAdjustmentFee,
                                                   BigDecimal expectedOddFee,
                                                   List<PromotionReq> promotionReqs,
                                                   List<CouponPromotionReq> couponPromotionReqs,
                                                   List<GiftRulePromotionReq> giftRulePromotionReqs,
                                                   List<PatientCardPromotionReq> patientCardPromotionReqs,
                                                   List<PatientPointDeductProductPromotionReq> patientPointDeductProductPromotionReqs,
                                                   List<ItemSinglePromotionReq> itemSinglePromotionReqs,
                                                   List<MallVerificationReq> mallVerificationReqs,
                                                   PatientPointsInfoReq patientPointsInfoReq,
                                                   int calSource,
                                                   boolean payForLeft,
                                                   //是否需要卡项余额信息
                                                   boolean needPatientCardBalance) throws ServiceInternalException {
        updatePatientInfo();
        fetchAndSetMemberInfo(false);
        /**
         * 类别1：药品类别的算价
         * 里面的实现其实是查chargeForm里面的药品清单，取后台查这些药品的单价等。
         * 计算药品价格还是要用后台的价格数据，不能以chargeForm里面来算。
         * */
        updateProductInfo(true, false, 1, 1);


        /**
         * 类别2：空中药房快递加工费的算价，计算空中药房的加工费和快递费并赋值
         * */
        updateAirPharmacyChargeFormDeliveryFeeAndProcessFee();

        /**
         * 更新虚拟药房的快递费
         */
        updateVirtualPharmacyChargeFormDeliveryFee(calSource);

        /**
         * 类别3：快递加工费的算价
         * */
        updateExpressDeliveryFeeAndProcessFee(calSource);

        /**
         * 类别4：优惠大打折的算价
         * */
//        fetchAndApplyPromotionInfo(promotionReqs, couponPromotionReqs, giftRulePromotionReqs, patientCardPromotionReqs, patientPointDeductProductPromotionReqs, patientPointsInfoReq, Constants.ChargeSource.CHARGE);
//
//        BigDecimal adjustmentFeeOnCalculate = calculateAdjustFee(expectedAdjustmentFee,
//                MathUtils.wrapBigDecimalOrZero(chargeSheet.getOutpatientAdjustmentFee()),
//                expectedOddFee, chargeSheet.getDraftAdjustmentFee());
//
//        updateReceivableFee(adjustmentFeeOnCalculate);

        ApplyPromotionInfoResult applyPromotionInfoResult = fetchAndApplyPromotionInfo(promotionReqs, couponPromotionReqs, giftRulePromotionReqs, patientCardPromotionReqs, patientPointDeductProductPromotionReqs, mallVerificationReqs, patientPointsInfoReq, expectedAdjustmentFee, expectedOddFee);
        /**
         * 前面都算完了，doCaculate只是对前面每个计算chargeFrom/ChargeFormItem的汇总
         * 个人理解：一个chargeSheet的各种计算价格信息前面已经准备好，这个doCalculate只要算出来的放到成员变量里面即可。
         * doCalculate 完这个对象就是一个 不可变对象了
         * */
        doCalculate(MathUtils.wrapBigDecimalOrZero(applyPromotionInfoResult.getAdjustmentFeeOnCalculate()), payForLeft ? CalculateScene.PAY_FOR_LEFT : CalculateScene.PAY);

        /**
         * 把算费的结果填充到CalculateChargeResult返回
         * */
        return genCalculateChargeResult(needPatientCardBalance);

    }

    /**
     * 更新虚拟药房的快递费和加工费
     */
    private void updateVirtualPharmacyChargeFormDeliveryFee(int calSource) {
        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED) {
            return;
        }
        // 更新快递费
        updateVirtualPharmacyExpressDeliveryFee(calSource);
    }

    /**
     * 把算费的结果填充到CalculateChargeResult返回
     */
    private CalculateChargeResult genCalculateChargeResult(boolean needPatientCardBalance) {
        BigDecimal needPay = realReceivableFee;
        if (needPay.compareTo(BigDecimal.ZERO) < 0) {
            needPay = BigDecimal.ZERO;
        }
        CalculateChargeResult calculateChargeResult = new CalculateChargeResult();
        calculateChargeResult.setChargeSheetId(chargeSheet.getId());
        calculateChargeResult.setAdjustmentFee(adjustmentFee);
        calculateChargeResult.setDraftAdjustmentFee(draftAdjustmentFee);
        calculateChargeResult.setOutpatientAdjustmentFee(chargeSheet.getOutpatientAdjustmentFee());
        calculateChargeResult.setDiscountFee(discountFee);
        calculateChargeResult.setReceivableFee(receivableFee);
        calculateChargeResult.setSheBaoReceivableFee(sheBaoReceivableFee);
        calculateChargeResult.setNeedPayFee(needPay);
        calculateChargeResult.setTotalFee(totalFee);
        calculateChargeResult.setRoundingType(roundingType);
        calculateChargeResult.setOddFee(oddFee);
        calculateChargeResult.setMemberId(memberId);
        calculateChargeResult.setMemberInfo(memberInfo);
        calculateChargeResult.setAfterRoundingDiscountedTotalFee(afterRoundingDiscountedTotalFee);
        calculateChargeResult.setSourceTotalPrice(sourceTotalFee);
        calculateChargeResult.setUnitAdjustmentFee(unitAdjustmentFee);
        calculateChargeResult.setChargeForms(formProcessors.stream()
                .map(formProcessor -> ChargeFormProtocol.generateCalculateResult(formProcessor, false))
                .collect(Collectors.toList()));
        calculateChargeResult.setRetailType(Optional.ofNullable(chargeSheet.getAdditional()).map(ChargeSheetAdditional::getRetailType).orElse(0));
        calculateChargeResult.setBatchExtractChargeSheetIds(Optional.ofNullable(chargeSheet.getAdditional())
                .map(ChargeSheetAdditional::getExtendedInfo)
                .map(ChargeSheetAdditionalExtendInfo::getBatchExtractOriginalSheetIds)
                .orElse(null)
        );


        calculateChargeResult.setChineseMedicineDiscountFee(formProcessors.stream().map(formProcessor -> formProcessor.summaryDiscountFee(FormProcessor.SummaryFeeType.CHINESE_MEDICINE)).reduce(BigDecimal.ZERO, BigDecimal::add));
        calculateChargeResult.setWesternMedicineDiscountFee(formProcessors.stream().map(formProcessor -> formProcessor.summaryDiscountFee(FormProcessor.SummaryFeeType.WESTERN_MEDICINE)).reduce(BigDecimal.ZERO, BigDecimal::add));
        calculateChargeResult.setMaterialDiscountFee(formProcessors.stream().map(formProcessor -> formProcessor.summaryDiscountFee(FormProcessor.SummaryFeeType.MATERIAL)).reduce(BigDecimal.ZERO, BigDecimal::add));
        calculateChargeResult.setExaminationDiscountFee(formProcessors.stream().map(formProcessor -> formProcessor.summaryDiscountFee(FormProcessor.SummaryFeeType.EXAMINATION)).reduce(BigDecimal.ZERO, BigDecimal::add));
        calculateChargeResult.setRegistrationDiscountFee(formProcessors.stream().map(formProcessor -> formProcessor.summaryDiscountFee(FormProcessor.SummaryFeeType.REGISTRATION)).reduce(BigDecimal.ZERO, BigDecimal::add));
        calculateChargeResult.setTreatmentDiscountFee(formProcessors.stream().map(formProcessor -> formProcessor.summaryDiscountFee(FormProcessor.SummaryFeeType.TREATMENT)).reduce(BigDecimal.ZERO, BigDecimal::add));

        calculateChargeResult.setTotalCostPrice(formProcessors.stream().flatMap(formProcessor -> Optional.ofNullable(formProcessor.getItemProcessorList()).orElse(new ArrayList<>()).stream()).map(itemProcessor -> Optional.ofNullable(itemProcessor.getChargeFormItem()).filter(chargeFormItem -> chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.UNSELECTED).map(ChargeFormItem::getTotalCostPrice).orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add));
        if (availablePromotionViews != null && availablePromotionViews.size() > 0) {
            calculateChargeResult.setPromotions(availablePromotionViews);
        }

        if (!CollectionUtils.isEmpty(availableCouponPromotionViews)) {
            calculateChargeResult.setCouponPromotions(availableCouponPromotionViews);
        }

        if (!CollectionUtils.isEmpty(availableGiftRulePromotionViews)) {
            calculateChargeResult.setGiftRulePromotions(availableGiftRulePromotionViews);
        }

        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(availablePatientCardPromotionViews)) {
            calculateChargeResult.setPatientCardPromotions(availablePatientCardPromotionViews);
        }

        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(availablePatientPointDeductProductPromotionViews)) {
            calculateChargeResult.setPatientPointDeductProductPromotions(availablePatientPointDeductProductPromotionViews);
        }


        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(availableChargeVerifyInfos)) {
            calculateChargeResult.setVerifyInfoViews(availableChargeVerifyInfos);
        }

        calculateChargeResult.setPatientPointsInfo(patientPointsInfoView);

        calculateChargeResult.setUseLimitPrice(calculateChargeResult.getChargeForms().stream().anyMatch(CalculateChargeFormView::isUseLimitPrice));
        calculateChargeResult.setUseListingPrice(formProcessors.stream()
                .flatMap(formProcessor -> formProcessor.getItemProcessorList().stream())
                .anyMatch(itemProcessor -> itemProcessor.getChargeFormItem().getPromotionInfo() != null
                        && MathUtils.compareZero(itemProcessor.getChargeFormItem().getPromotionInfo().getListingDiscountFee()) != 0)
        );

        calculateChargeResult.setPatientTotalFee(ChargeUtils.fixPatientTotalFee(chargeSheet.getStatus(), calculateChargeResult.getTotalFee(), calculateChargeResult.getAdjustmentFee()));

        calculateChargeResult.setSourceChargeSheet(chargeSheet);

        //需要查询支付方式
        if (needPatientCardBalance) {
            List<String> uncheckedPatientCardIds = Optional.ofNullable(calculateChargeResult.getPatientCardPromotions()).orElse(new ArrayList<>())
                    .stream()
                    .filter(patientCardPromotionView -> !patientCardPromotionView.getChecked())
                    .map(PatientCardPromotionView::getId)
                    .distinct()
                    .collect(Collectors.toList());

            calculateChargeResult.setCanPaidPatientCards(findAvailablePatientCards(uncheckedPatientCardIds));
        }

        //查询每个item的最后议价的修改人
        bindItemUnitAdjustmentFeeLastModifiedByName(chainId, calculateChargeResult);
        bindMedicalBillPrintView(calculateChargeResult);
        return calculateChargeResult;
    }

    private void bindItemUnitAdjustmentFeeLastModifiedByName(String chainId, CalculateChargeResult calculateChargeResult) {
        if (calculateChargeResult == null || calculateChargeResult.getChargeForms() == null) {
            return;
        }

        Set<String> unitAdjustmentFeeLastModifiedByIds = calculateChargeResult.getChargeForms()
                .stream()
                .flatMap(calculateChargeFormView -> Optional.ofNullable(calculateChargeFormView.getChargeFormItems()).orElse(new ArrayList<>()).stream())
                .map(CalculateChargeFormItemView::getUnitAdjustmentFeeLastModifiedBy)
                .filter(org.apache.commons.lang3.StringUtils::isNotBlank)
                .collect(Collectors.toSet());

        if (org.apache.commons.collections.CollectionUtils.isEmpty(unitAdjustmentFeeLastModifiedByIds)) {
            return;
        }

        Map<String, String> employeeIdNameMap = sheetProcessorInfoProvider.getEmployeeInfoProvider()
                .findEmployeeIdNameMap(chainId, new ArrayList<>(unitAdjustmentFeeLastModifiedByIds));

        calculateChargeResult.getChargeForms()
                .stream()
                .flatMap(calculateChargeFormView -> Optional.ofNullable(calculateChargeFormView.getChargeFormItems()).orElse(new ArrayList<>()).stream())
                .filter(calculateChargeFormItemView -> org.apache.commons.lang3.StringUtils.isNotBlank(calculateChargeFormItemView.getUnitAdjustmentFeeLastModifiedBy()))
                .forEach(calculateChargeFormItemView -> calculateChargeFormItemView.setUnitAdjustmentFeeLastModifiedByName(employeeIdNameMap.get(calculateChargeFormItemView.getUnitAdjustmentFeeLastModifiedBy())));
    }

    /**
     * 查询患者可用的支付方式，及每个卡的余额
     *
     * @return
     */
    private List<PatientCardView> findAvailablePatientCards(List<String> uncheckedPatientCardIds) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(patientCardPromotions)) {
            return new ArrayList<>();
        }

        List<ItemProcessor> itemProcessors = formProcessors.stream()
                .filter(formProcessor -> !formProcessor.isAirPharmacy())
                .flatMap(formProcessor -> formProcessor.getItemProcessorList().stream())
                .collect(Collectors.toList());

        List<FormProcessor> airPharmacyFormProcessors = formProcessors.stream()
                .filter(FormProcessor::isAirPharmacy)
                .collect(Collectors.toList());

        PromotionPatientCardProcessor promotionPatientCardProcessor = new PromotionPatientCardProcessor(patientCardPromotions);

        return promotionPatientCardProcessor.generatePatientCardViews(itemProcessors, airPharmacyFormProcessors, uncheckedPatientCardIds);
    }

    public List<PatientCardView> findCanPaidPatientCards() {
        //收集收费单可以支付的收费项用于查询卡项过滤
        Map<String, GoodsBaseInfo> goodsBaseInfoMap = formProcessors.stream()
                .flatMap(formProcessor -> formProcessor.getGoodsBaseInfosForPartedPaid().entrySet().stream())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        List<GoodsBaseInfo> waitPaidGoods = goodsBaseInfoMap.values().stream().collect(Collectors.toList());

        List<String> otherPatientCardIds = Optional.ofNullable(chargeSheet.getPatientCardPromotionInfos())
                .orElse(new ArrayList<>())
                .stream()
                .filter(patientCardPromotionInfo -> patientCardPromotionInfo.getIsDeleted() == 0)
                .filter(patientCardPromotionInfo -> patientCardPromotionInfo.getIsOutOfUseRangeCard() == 1)
                .map(ChargePatientCardPromotionInfo::getPromotionId)
                .distinct()
                .collect(Collectors.toList());

        List<PromotionPayCardsView> promotionPayCardsViews = sheetProcessorInfoProvider.getPromotionProvider().listPayCards(chargeSheet.getChainId(), chargeSheet.getPatientId(), otherPatientCardIds, waitPaidGoods);

        if (org.apache.commons.collections.CollectionUtils.isEmpty(promotionPayCardsViews)) {
            return new ArrayList<>();
        }

        //从goodsBaseInfoMap找出不是空中药房的id
        List<String> chargeFormItemIds = new ArrayList<>();
        List<String> chargeFormIds = new ArrayList<>();

        goodsBaseInfoMap.entrySet().forEach(entry -> {
            GoodsBaseInfo goodsBaseInfo = entry.getValue();
            if (goodsBaseInfo == null) {
                return;
            }
            if (goodsBaseInfo.getPharmacyType() == GoodsConst.PharmacyType.AIR_PHARMACY) {
                chargeFormIds.add(entry.getKey());
                return;
            }
            chargeFormItemIds.add(entry.getKey());
        });

        List<ItemProcessor> itemProcessors = formProcessors.stream()
                .filter(formProcessor -> !formProcessor.isAirPharmacy())
                .flatMap(formProcessor -> formProcessor.getItemProcessorList().stream())
                .filter(itemProcessor -> chargeFormItemIds.contains(itemProcessor.getItemId()))
                .collect(Collectors.toList());

        List<FormProcessor> airPharmacyFormProcessors = formProcessors.stream()
                .filter(FormProcessor::isAirPharmacy)
                .filter(formProcessor -> chargeFormIds.contains(formProcessor.getFormId()))
                .collect(Collectors.toList());

        return promotionPayCardsViews.stream()
                .map(promotionPayCardsView -> {
                    List<String> canPaidGoodsIds = Optional.ofNullable(promotionPayCardsView.getSupportPaidGoods()).orElse(new ArrayList<>())
                            .stream()
                            .filter(goodsBaseInfo -> goodsBaseInfo.getPharmacyType() != GoodsConst.PharmacyType.AIR_PHARMACY && org.apache.commons.lang3.StringUtils.isNotEmpty(goodsBaseInfo.getGoodsId()))
                            .map(goodsBaseInfo -> CalculatePromotionItem.convertPharmacyTypeGoodsIdKey(goodsBaseInfo.getPharmacyType(), goodsBaseInfo.getGoodsId()))
                            .collect(Collectors.toList());

                    List<String> canPaidGoodsTypeTupleKeyList = Optional.ofNullable(promotionPayCardsView.getSupportPaidGoods()).orElse(new ArrayList<>())
                            .stream()
                            .filter(goodsBaseInfo -> goodsBaseInfo.getPharmacyType() == GoodsConst.PharmacyType.AIR_PHARMACY)
                            .map(goodsBaseInfo -> CalculatePromotionAirPharmacyForm.convertToKey(goodsBaseInfo.getGoodsType(), goodsBaseInfo.getGoodsSubType(), goodsBaseInfo.getGoodsCMSpec(), goodsBaseInfo.getPharmacyType()))
                            .collect(Collectors.toList());

                    List<ItemProcessor> canPaidItemProcessors = itemProcessors.stream()
                            .filter(itemProcessor -> canPaidGoodsIds.contains(CalculatePromotionItem.convertPharmacyTypeGoodsIdKey(itemProcessor.getPharmacyType(), itemProcessor.getProductId())))
                            .collect(Collectors.toList());

                    List<FormProcessor> canPaidAirPharmacyFormProcessors = airPharmacyFormProcessors.stream()
                            .filter(formProcessor -> {
                                GoodsBaseInfo goodsBaseInfo = formProcessor.generateGoodsBaseInfo();
                                if (goodsBaseInfo == null) {
                                    return false;
                                }
                                return canPaidGoodsTypeTupleKeyList.contains(CalculatePromotionAirPharmacyForm.convertToKey(goodsBaseInfo.getGoodsType(), goodsBaseInfo.getGoodsSubType(), goodsBaseInfo.getGoodsCMSpec(), goodsBaseInfo.getPharmacyType()));
                            })
                            .collect(Collectors.toList());

                    return PromotionPatientCardDeductRule.generatePatientCardView(promotionPayCardsView.getCardBalance(),
                            promotionPayCardsView.getId(),
                            promotionPayCardsView.getCardName(),
                            promotionPayCardsView.getPatientId(),
                            Optional.of(promotionPayCardsView).map(PromotionPayCardsView::getPatient).map(PatientBasicInfoView::getName).orElse(null),
                            Optional.of(promotionPayCardsView).map(PromotionPayCardsView::getPatient).map(PatientBasicInfoView::getMobile).orElse(null),
                            promotionPayCardsView.getPrincipal(),
                            promotionPayCardsView.getPresent(),
                            promotionPayCardsView.getEndDate(),
                            canPaidItemProcessors, canPaidAirPharmacyFormProcessors);
                })
                .collect(Collectors.toList());
    }

    private void updateAirPharmacyChargeFormDeliveryFeeAndProcessFee() throws ServiceInternalException {
        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED) {
            return;
        }
        List<ChargeForm> airPharmacyChargeForms = Optional.ofNullable(chargeSheet.getChargeForms()).orElse(new ArrayList<>()).stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(airPharmacyChargeForms)) {
            return;
        }
        if (sheetProcessorInfoProvider.getAirPharmacyProvider() == null) {
            return;
        }
        // 这是到bisorder服务，算费用,算的是快递费和加工费
        List<CalculatePriceRsp.ChargeFormRsp> chargeFormRsps = sheetProcessorInfoProvider.getAirPharmacyProvider().calculatePrice(chargeSheet.getClinicId(), airPharmacyChargeForms);
        if (CollectionUtils.isEmpty(chargeFormRsps)) {
            return;
        }
        Map<String, CalculatePriceRsp.ChargeFormRsp> chargeFormRspMap = ListUtils.toMap(chargeFormRsps, CalculatePriceRsp.ChargeFormRsp::getId);
        formProcessors.stream()
                .filter(formProcessor -> formProcessor.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY)
                .forEach(formProcessor -> {
                    CalculatePriceRsp.ChargeFormRsp chargeFormRsp = chargeFormRspMap.get(formProcessor.getChargeFormId());
                    if (chargeFormRsp != null) {
                        formProcessor.insertOrUpdateAirPharmacyDeliveryProcessIngredientFormItem(chargeFormRsp, operatorId);
                        formProcessor.updateAirPharmacyDeliveryPriceAndProcessPriceAndIngredientPrice(chargeFormRsp);
                        formProcessor.calculateAirPharmacyTotalPrice();
                        formProcessor.setDeliveryPrimaryFormId(chargeFormRsp.getDeliveryPrimaryFormId());

                        if (chargeFormRsp.getLogistics() != null) {
                            formProcessor.updateChargeFormAirPharmacyLogisticsCompany(chargeFormRsp.getLogistics().getLogisticsCompanyId(), chargeFormRsp.getLogistics().getLogisticsCompanyId());
                        }
                    }

                });
       // bindDeliveryCompany(airPharmacyChargeForms, getAirPharmacyDeliveryCompanyFunc);
    }

    private final Function<Collection<String>, Map<String, String>> getAirPharmacyDeliveryCompanyFunc =
            companyIds -> Optional.ofNullable(sheetProcessorInfoProvider.getAirPharmacyProvider().getBisCompanysInfo(new ArrayList<>(companyIds)))
                    .orElseGet(ArrayList::new)
                    .stream()
                    .collect(Collectors.toMap(company -> String.valueOf(company.getId()), LogisticsCompanyView::getName, (a, b) -> a));


    private void bindDeliveryCompany(List<ChargeForm> chargeForms, Function<Collection<String>, Map<String, String>> getDeliveryCompanyFunc) {
        if (CollectionUtils.isEmpty(chargeForms)) {
            return;
        }
        Set<String> companyIds = chargeForms.stream()
                .map(ChargeForm::getChargeAirPharmacyLogistics)
                .filter(Objects::nonNull)
                .map(ChargeAirPharmacyLogistics::getDeliveryCompanyId)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(companyIds)) {
            return;
        }
        Map<String, String> companyIdNameMap = getDeliveryCompanyFunc.apply(companyIds);
        chargeForms.stream()
                .filter(chargeForm -> Objects.nonNull(chargeForm.getChargeAirPharmacyLogistics()))
                .forEach(chargeForm -> {
                    ChargeAirPharmacyLogistics chargeAirPharmacyLogistics = chargeForm.getChargeAirPharmacyLogistics();
                    ChargeAirPharmacyLogisticsReq.LogisticsCompanyReq companyReq = new ChargeAirPharmacyLogisticsReq.LogisticsCompanyReq();
                    companyReq.setId(chargeAirPharmacyLogistics.getDeliveryCompanyId());
                    companyReq.setName(companyIdNameMap.get(chargeAirPharmacyLogistics.getDeliveryCompanyId()));
                    chargeAirPharmacyLogistics.setDeliveryCompany(companyReq);
                });
    }

    private boolean getIsNeedUseLimitPrice() {
        return getCanPayHealthCard();
    }

//    private Map<Long, ChargePayModeConfigSimple> getChargePayModeConfigSimple(){
//        if (sheetProcessorInfoProvider != null && chargeSheet != null && !StringUtils.isEmpty(chargeSheet.getChainId())) {
//            return sheetProcessorInfoProvider.getChargePayModeProvider().getChargePayModeConfigSimpleByChainId(chargeSheet.getChainId());
//        }
//        return new HashMap<>();
//    }


    public CalculateChargeResult calculateRefundSheetFee(RefundInfo refundInfo) throws ServiceInternalException, CisCustomException {
        //updateProductInfo();
        updateDispensingInfo();
        updateRefundInfo(refundInfo, false);
        BigDecimal adjustmentFeeOnRefund = MathUtils.wrapBigDecimalOrZero(refundInfo.getAdjustmentFee());
        adjustmentFeeOnRefund = adjustmentFeeOnRefund.compareTo(BigDecimal.ZERO) > 0 ? adjustmentFeeOnRefund : BigDecimal.ZERO;
        doCalculate(adjustmentFeeOnRefund, CalculateScene.REFUND);

        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "charge fee info, totalFee:{}, discountFee:{}, adjustmentFee:{}, receivableFee:{}, realReceivableFee:{}, receivedFee:{}, adjustmentFeeOnRefund:{}",
                totalFee, discountFee, adjustmentFee, receivableFee, realReceivableFee, _receivedFee, adjustmentFeeOnRefund);

        BigDecimal refundFee = realReceivableFee.negate();

        BigDecimal refundItemsDiscountFee = formProcessors.stream()
                .flatMap(formProcessor -> formProcessor.getAffectedChargeFormItems().stream())
                .filter(Objects::nonNull)
                .map(chargeFormItem -> MathUtils.wrapBigDecimalOrZero(chargeFormItem.getDiscountPrice()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal refundItemsTotalFee = formProcessors.stream()
                .flatMap(formProcessor -> formProcessor.getAffectedChargeFormItems().stream())
                .filter(Objects::nonNull)
                .map(chargeFormItem -> MathUtils.wrapBigDecimalOrZero(chargeFormItem.getTotalPrice()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        CalculateChargeResult calculateChargeResult = new CalculateChargeResult();
        calculateChargeResult.setAdjustmentFee(adjustmentFeeOnRefund);
        calculateChargeResult.setDiscountFee(refundItemsDiscountFee);
        calculateChargeResult.setReceivableFee(receivableFee);
        calculateChargeResult.setNeedRefundFee(refundFee);
        calculateChargeResult.setTotalFee(refundItemsTotalFee);
        calculateChargeResult.setOwedRefundFee(_owedRefundFee);
        calculateChargeResult.setNetIncomeFee(MathUtils.wrapBigDecimalAdd(_receivedFee, _refundFee));
        calculateChargeResult.setReceivedMemberCardFee(getMemberPaidAmount());
        calculateChargeResult.setReceivedHealthCardFee(getHealthCardPaidAmount());
        calculateChargeResult.setChargeForms(formProcessors.stream()
                .map(formProcessor -> ChargeFormProtocol.generateCalculateResult(formProcessor, true))
                .collect(Collectors.toList()));
        calculateChargeResult.setUseLimitPrice(calculateChargeResult.getChargeForms().stream().anyMatch(CalculateChargeFormView::isUseLimitPrice));
        calculateChargeResult.setUseListingPrice(formProcessors.stream()
                .flatMap(formProcessor -> formProcessor.getItemProcessorList().stream())
                .anyMatch(itemProcessor -> itemProcessor.getChargeFormItem().getPromotionInfo() != null
                        && MathUtils.compareZero(itemProcessor.getChargeFormItem().getPromotionInfo().getListingDiscountFee()) != 0)
        );
        return calculateChargeResult;
    }

    public RegistrationChargeSheetView generateRegistrationChargeSheetView() throws ServiceInternalException {
        updateProductInfo(false, false, 1, 1);
        fetchAndSetMemberInfo(false);
        fetchAndApplyPromotionInfo(new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), null, null, null);


        //filter non registration
        formProcessors = formProcessors.stream()
                .filter(formProcessor -> formProcessor.getItemProcessorList() != null)
                .peek(formProcessor -> formProcessor.getItemProcessorList().removeIf(itemProcessor -> itemProcessor.getProductType() != Constants.ProductType.REGISTRATION))
                .filter(formProcessor -> formProcessor.getItemProcessorList().size() > 0)
                .collect(Collectors.toList());

        doCalculate(BigDecimal.ZERO, chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_CHARGED ? CalculateScene.PAY_FOR_LEFT : CalculateScene.PAY);
        return ChargeSheetFeeProtocol.toRegistrationChargeSheetView(this);
    }

    // get
//    public ChargeSheetView generateSheetDetail(boolean isContainGiftGoods) throws ServiceInternalException {
//        return generateSheetDetail(false, isContainGiftGoods);
//    }

//    public ChargeSheetView generateSheetDetailForCopy() throws ServiceInternalException {
//        return generateSheetDetail(true, false);
//    }

    /**
     * abcscene 区分是哪个端上来的拉取详情
     * 见：Constants.ABCScene
     */
    public ChargeSheetView generateSheetDetail(boolean isCopyByOutpatient, boolean isContainGiftGoods, int source, boolean notQuerySheBaoInfo) throws ServiceInternalException {
        return generateSheetDetail(isCopyByOutpatient, isContainGiftGoods, source, notQuerySheBaoInfo, false, Constants.ChargeSheetDispensingQueryCheckType.DISPENSING_QUERY_NOT_CHECK);
    }

    /**
     * abcscene 区分是哪个端上来的拉取详情
     * 见：Constants.ABCScene
     */
    public ChargeSheetView generateSheetDetail(boolean isCopyByOutpatient, boolean isContainGiftGoods, int source, boolean notQuerySheBaoInfo, boolean withAllTransaction, int dispensingQueryCheck) throws ServiceInternalException {
        String dataSignature = ChargeUtils.sign(chargeSheet);
        // 加载config
        loadChargeConfig();
        updatePatientInfo();
        fetchAndSetMemberInfo(false);
        // 将数据库的原价值保留下来，处理议价之后原价也发生变化的情况，原价发生变化，则议价也要跟着变化
        updateChargeFormItemOldSourceUnitPrice();
        updateProductInfo(true, isCopyByOutpatient, notQuerySheBaoInfo ? 1 : 0, getIsNeedUseLimitPrice() ? YesOrNo.YES : YesOrNo.NO);
        // 更新虚拟药房的快递费
        updateVirtualPharmacyChargeFormDeliveryFee(Constants.ChargeSource.CHARGE);
        updateExpressDeliveryFeeAndProcessFee(Constants.ChargeSource.CHARGE);//这里只是用默认值CHARGE
        // TODO [计算快递费和加工费]
        updateAirPharmacyChargeFormDeliveryFeeAndProcessFee();
        updateGoodsPharmacyViews();
        updateChargeFormItemCount();
        if (dispensingQueryCheck != Constants.ChargeSheetDispensingQueryCheckType.DISPENSING_NOT_QUERY) {
            // 拉取发药单信息
            updateDispensingInfo();
            boolean isSupportSheetCheck = dispensingQueryCheck == Constants.ChargeSheetDispensingQueryCheckType.DISPENSING_QUERY_AND_CHECK;
            this.checkDispensingFormAuditCompound(isSupportSheetCheck);
            this.checkChargeSheetDispensingAndExecutionOrThrowException(isSupportSheetCheck, false, null);
        }
        BigDecimal expectedOddFee = null;
        // 锁单中并且支付方式有值时，零头从收费单上取，否则为null。为null时会重新计算零头
        if (chargeSheet.isLocking() && payMode != Constants.ChargePayMode.NONE) {
            expectedOddFee = chargeSheet.getOddFee();
        }
        ApplyPromotionInfoResult applyPromotionInfoResult = fetchAndApplyPromotionInfo(new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), null, null, expectedOddFee);
        BigDecimal unchargedAdjustmentFee = BigDecimal.ZERO;
        if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED) {
//            unchargedAdjustmentFee = MathUtils.wrapBigDecimalAdd(chargeSheet.getOutpatientAdjustmentFee(), chargeSheet.getDraftAdjustmentFee());
            unchargedAdjustmentFee = applyPromotionInfoResult.getAdjustmentFeeOnCalculate();
            updateReceivableFee(unchargedAdjustmentFee);
        }

        doCalculate(unchargedAdjustmentFee, chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_CHARGED ? CalculateScene.PAY_FOR_LEFT : CalculateScene.PAY);
        ChargeSheetView chargeSheetView = ChargeSheetFeeProtocol.toChargeSheetView(this, isCopyByOutpatient, isContainGiftGoods, source, withAllTransaction);
        chargeSheetView.setDataSignature(dataSignature);
        bindPharmacyName(chargeSheetView);
        bindVisitSource(chargeSheetView);
//        bindMedicalBillPrintView(chargeSheetView);
        return chargeSheetView;
    }

    private void loadChargeConfig() {
        chargeConfigDetail = sheetProcessorInfoProvider.getChargePayModeProvider().getBranchConfigDetail(chargeSheet.getChainId(), chargeSheet.getClinicId(), false);
    }

    private void bindMedicalBillPrintView(CalculateChargeResult calculateChargeResult) {
        Organ organ = getSheetProcessorInfoProvider().getClinicProvider().getOrgan(clinicId);

        if (Objects.isNull(organ)) {
            return;
        }

        List<ChargeFormPrintView> chargeFormPrintViews = generateChargeFormPrintViews(formProcessors, BigDecimal.ZERO, BigDecimal.ZERO, Arrays.asList(Constants.ChargeFormItemStatus.CHARGED, Constants.ChargeFormItemStatus.UNCHARGED), organ.getHisType());
        List<MedicalBillPrintView> medicalBill;

        if (organ.getHisType() != Organ.HisType.CIS_HIS_TYPE_HOSPITAL) {
            medicalBill = Arrays.stream(ChargeFormItemPrintView.Type.values())
                    .map(printType -> ChargeSheetPrintView.generateMedicalBillPrintView(printType, chargeFormPrintViews))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(MedicalBillPrintView::getName, Function.identity(), (a, b) -> {
                        a.addCountAndFee(b.getTotalFee(), b.getTotalCount());
                        return a;
                    })).values()
                    .stream()
                    .sorted(Comparator.comparing(medicalBillPrintView -> MedicalBillPrintView.Name.SORTED_NAMES.indexOf(medicalBillPrintView.getName())))
                    .collect(Collectors.toList());
        } else {
            medicalBill = ChargeSheetFeeProtocol.generateWholeMedicalBills(chargeFormPrintViews, chainId, sheetProcessorInfoProvider, null);
        }

        calculateChargeResult.setMedicalBill(medicalBill);
    }


    private void bindVisitSource(ChargeSheetView chargeSheetView) {
        if (Objects.isNull(chargeSheetView) || Objects.isNull(patientOrder)) {
            return;
        }

        chargeSheetView.setVisitSourceId(patientOrder.getVisitSourceId());
        chargeSheetView.setVisitSourceName(patientOrder.getVisitSourceName());
        chargeSheetView.setVisitSourceFrom(patientOrder.getVisitSourceFrom());
        chargeSheetView.setVisitSourceFromName(patientOrder.getVisitSourceFromName());
        chargeSheetView.setVisitSourceRemark(patientOrder.getVisitSourceRemark());

    }

    private void bindPharmacyName(ChargeSheetView chargeSheetView) {
        if (chargeSheetView == null || CollectionUtils.isEmpty(chargeSheetView.getChargeForms())) {
            return;
        }
        List<Integer> pharmacyNos = chargeSheetView.getChargeForms()
                .stream()
                .filter(chargeFormView -> chargeFormView.getPharmacyType() == GoodsConst.PharmacyType.VIRTUAL_PHARMACY || chargeFormView.getPharmacyType() == GoodsConst.PharmacyType.LOCAL_PHARMACY)
                .map(ChargeFormView::getPharmacyNo)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pharmacyNos) || CollectionUtils.isEmpty(goodsPharmacyViews)) {
            return;
        }
        Map<Integer, String> virtualPharmacyNameMap = goodsPharmacyViews.stream()
                .collect(Collectors.toMap(GoodsPharmacyView::getNo, GoodsPharmacyView::getName));
        chargeSheetView.getChargeForms()
                .stream()
                .filter(chargeFormView -> chargeFormView.getPharmacyType() == GoodsConst.PharmacyType.VIRTUAL_PHARMACY
                        || chargeFormView.getPharmacyType() == GoodsConst.PharmacyType.LOCAL_PHARMACY)
                .forEach(chargeFormView -> chargeFormView.setPharmacyName(virtualPharmacyNameMap.getOrDefault(chargeFormView.getPharmacyNo(), null)));
    }

    private void updateGoodsPharmacyViews() {
        goodsPharmacyViews = org.apache.commons.collections.CollectionUtils.isNotEmpty(goodsPharmacyViews) ?
                goodsPharmacyViews :
                sheetProcessorInfoProvider.getGoodsScProvider().findPharmacyByClinic(chargeSheet.getChainId(), chargeSheet.getClinicId());
        Map<Integer, GoodsPharmacyView> goodsPharmacyViewMap = Optional.ofNullable(goodsPharmacyViews).orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.toMap(GoodsPharmacyView::getNo, Function.identity(), (a, b) -> a));
        formProcessors.forEach(formProcessor -> formProcessor.setGoodsPharmacyView(goodsPharmacyViewMap));
    }

    private void updateChargeFormItemOldSourceUnitPrice() {
        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED) {
            return;
        }
        formProcessors.forEach(FormProcessor::updateChargeFormItemOldSourceUnitPrice);
    }

    /**
     * 构建住院单的收费项详情
     *
     * @return
     */
    public ChargeSheetForHospitalView generateChargeSheetForHospitalView() {
        updateProductInfo(false, false, 0, 0);
        /**
         * 更新虚拟药房的快递费
         */
        updateVirtualPharmacyChargeFormDeliveryFee(Constants.ChargeSource.CHARGE);
        updateExpressDeliveryFeeAndProcessFee(Constants.ChargeSource.CHARGE);//这里只是用默认值CHARGE
        updateAirPharmacyChargeFormDeliveryFeeAndProcessFee();

        BigDecimal unchargedAdjustmentFee = BigDecimal.ZERO;
        doCalculate(unchargedAdjustmentFee, chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_CHARGED ? CalculateScene.PAY_FOR_LEFT : CalculateScene.PAY);

        return ChargeSheetFeeProtocol.toChargeSheetForHospitalView(this);
    }

    public ChargeSheetPrintView generatePrintView() throws ServiceInternalException {
        updatePatientInfo();
        fetchAndSetMemberInfo(false);
        updateLocalProductInfo(false, false, 0, 0);
        doCalculate(BigDecimal.ZERO, CalculateScene.PAY);

        return ChargeSheetFeeProtocol.toPrintView(this);
    }

    public ChargeSheetPrintView generateRefundPrintView() {
        updatePatientInfo();
        fetchAndSetMemberInfo(false);
        updateLocalProductInfo(false, false, 0, 0);
        doCalculate(BigDecimal.ZERO, CalculateScene.REFUND);

        return ChargeSheetFeeProtocol.toRefundPrintView(this);
    }

    public void placePayOrderForRegistration(PayInfo payInfo) throws ServiceInternalException, ChargeServiceException {
        fetchAndApplyPromotionInfo(payInfo.getPromotions(), payInfo.getCouponPromotions(), payInfo.getGiftRulePromotions(), payInfo.getPatientCardPromotions(), payInfo.getPatientPointDeductProductPromotions(), new ArrayList<>(), payInfo.getPatientPointsInfo(), null, null);
        fetchAndSetMemberInfo(true);
        chargeSheet.setStatus(Constants.ChargeSheetStatus.UNCHARGED);

        BigDecimal registrationFee = ChargeUtils.getChargeSheetItems(chargeSheet)
                .stream()
                .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.REGISTRATION)
                .findFirst()
                .map(ChargeFormItem::getUnitPrice)
                .orElse(BigDecimal.ZERO);

        if (MathUtils.wrapBigDecimalCompare(payInfo.getReceivableFee(), BigDecimal.ZERO) == 0 && MathUtils.wrapBigDecimalCompare(registrationFee, BigDecimal.ZERO) == 0) {
            updateReceivableFee(BigDecimal.ZERO);
            pay(payInfo, false);
            ChargeUtils.unlockChargeSheetV2(chargeSheet, 0, true);
            chargeSheet.setChargedBy(operatorId);
        } else {
            doCalculate(BigDecimal.ZERO, CalculateScene.PAY);
//            updateChargePayTransactionCanceled(null, operatorId);
        }

        if (CollectionUtils.isEmpty(availablePromotionViews)) {
            chargeSheet.setPromotionInfoJson(null);
            chargeSheet.setPromotionInfo(null);
        } else {
            SheetPromotionInfo sheetPromotionInfo = new SheetPromotionInfo();
            sheetPromotionInfo.setPromotions(availablePromotionViews);
            chargeSheet.setPromotionInfo(sheetPromotionInfo);
            chargeSheet.setPromotionInfoJson(JsonUtils.dump(sheetPromotionInfo));
        }
    }

    /**
     * 锁单这里要算快递费，calSource的值其实取得是payInfo里面的paySource
     */
    public void placePayOrder(PayInfo payInfo) throws ServiceInternalException, ProductInfoChangedException, ChargeServiceException {
        updatePatientInfo();
        fetchAndSetMemberInfo(true);
        updateProductInfo(true, false, 0, 1);
        updateChargeSheetIsDispensing();
        updateChargeFormItemCount();
        checkExecuteItemCountOrThrowException();
        /**
         * 更新虚拟药房的快递费
         */
        updateVirtualPharmacyChargeFormDeliveryFee(payInfo.getPaySource());
        updateExpressDeliveryFeeAndProcessFee(payInfo.getPaySource());
        //计算空中药房的加工费和快递费并赋值
        updateAirPharmacyChargeFormDeliveryFeeAndProcessFee();
//        updateAllUsageProcessFee();
        checkAstResultOrThrowException(payInfo.getPaySource());
        checkProductInfoOrThrowException(payInfo.getPaySource());

        checkAirPharmacyAndThrowException(payInfo.getPaySource());
        checkPatientPointRuleChangedOrException(payInfo.getPatientPointsInfo());
        ApplyPromotionInfoResult applyPromotionInfoResult = fetchAndApplyPromotionInfo(payInfo.getPromotions(), payInfo.getCouponPromotions(), payInfo.getGiftRulePromotions(), payInfo.getPatientCardPromotions(), payInfo.getPatientPointDeductProductPromotions(), payInfo.getMallVerifications(), payInfo.getPatientPointsInfo(), payInfo.getExpectedAdjustmentFee(), payInfo.getExpectedOddFee());
        checkCouponCountAndThrowException();
        checkPriceChangedAndThrowException();
        // 检验是否使用会员/卡项优惠,使用了不允许使用医保收费
        checkUsedDiscountAndShebaoPayOrThrowException();
        //校验批次信息是否发生变化
        checkChargeFormItemBatchInfoChangedOrThrowException();
        //处理满减满赠
        fillChargeFormForGift();

        BigDecimal adjustmentFee = applyPromotionInfoResult.getAdjustmentFeeOnCalculate();
        BigDecimal notFlatPrice = applyAdjustment(adjustmentFee);
        applyChargeFormItemPromotionInfoUnitAdjustmentFee();

        if (MathUtils.wrapBigDecimalCompare(notFlatPrice, BigDecimal.ZERO) < 0) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "议价未摊费完成 notFlatPrice: {}", notFlatPrice);
            throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_ADJUSTMENT_FEE_NOT_FLAT);
        }

        //更新收费项的待收金额
        updateReceivableFee(BigDecimal.ZERO);
        doCalculate(BigDecimal.ZERO, CalculateScene.PAY);
        checkRoundingTypeChangeAndThrowException(payInfo.getRoundingType());

        BigDecimal frontEndReceivableFee = MathUtils.wrapBigDecimalOrZero(payInfo.getReceivableFee());
        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "[placePayOrder] charge fee info, totalFee:{}, discountFee:{}, adjustmentFee:{}, receivableFee:{}, realReceivableFee:{}, receivedFee:{}, frontEndReceivableFee:{}, notFlatPrice: {}",
                totalFee, discountFee, adjustmentFee, receivableFee, realReceivableFee, _receivedFee, frontEndReceivableFee, notFlatPrice);

        if (realReceivableFee.compareTo(BigDecimal.ZERO) < 0) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "realReceivableFee <= 0:" + receivableFee);
            throw new ChargeServiceException(ChargeServiceError.CHARGE_NO_VALID_RECEIVABLE);
        }

        if (realReceivableFee.compareTo(frontEndReceivableFee) != 0) {
            throw new ChargeServiceException(ChargeServiceError.CHARGE_FEE_CHANGED);
        }

        if (adjustmentFee.compareTo(BigDecimal.ZERO) > 0) {
            //兼容历史遗留单据 新加一个type
            insertChargeAdditionFee(ChargeAdditionalFee.AdditionalFeeType.ADJUSTMENT_ADD_FEE, adjustmentFee, false, payInfo.getOperatorId());
        } else if (adjustmentFee.compareTo(BigDecimal.ZERO) < 0) {
            insertChargeAdditionFee(ChargeAdditionalFee.AdditionalFeeType.ADJUSTMENT_DISCOUNT_FEE, adjustmentFee, false, payInfo.getOperatorId());
        }

        if (!CollectionUtils.isEmpty(availablePromotionViews) || adjustmentFee.compareTo(BigDecimal.ZERO) != 0) {
            SheetPromotionInfo sheetPromotionInfo = new SheetPromotionInfo();
            sheetPromotionInfo.setPromotions(availablePromotionViews == null ? new ArrayList<>() : availablePromotionViews);
            sheetPromotionInfo.setAdjustmentFee(adjustmentFee);
            chargeSheet.setPromotionInfo(sheetPromotionInfo);
            chargeSheet.setPromotionInfoJson(JsonUtils.dump(sheetPromotionInfo));
        }

        int payStatus = ChargeFormItem.PayStatus.PARTED_PAID;

        formProcessors.forEach(formProcessor -> formProcessor.pay(payStatus, payInfo.getPaySource(), payInfo.getOperatorId()));
        chargeSheet.setStatus(Constants.ChargeSheetStatus.PART_CHARGED);
        chargeSheet.setChargedBy(operatorId);
    }

    private void checkUsedDiscountAndShebaoPayOrThrowException() {
        // 是否使用折扣
        boolean usedDiscount =
                Optional.ofNullable(patientPointsInfoView).filter(it -> it.getCheckedDeductionPrice() != null).map(PatientPointsInfoView::getCheckedDeductionPrice).orElse(BigDecimal.ZERO).compareTo(BigDecimal.ZERO) != 0
                        || Optional.ofNullable(availablePromotionViews).orElse(new ArrayList<>()).stream().filter(it -> it.getDiscountPrice() != null).anyMatch(it -> it.getDiscountPrice().compareTo(BigDecimal.ZERO) != 0)
                        || Optional.ofNullable(availablePatientCardPromotionViews).orElse(new ArrayList<>()).stream().filter(it -> it.getTotalDeductPrice() != null).anyMatch(it -> it.getTotalDeductPrice().compareTo(BigDecimal.ZERO) != 0)
                        || Optional.ofNullable(availablePatientPointDeductProductPromotionViews).orElse(new ArrayList<>()).stream().filter(it -> it.getTotalDeductPrice() != null).anyMatch(it -> it.getTotalDeductPrice().compareTo(BigDecimal.ZERO) != 0)
                        || Optional.ofNullable(availableCouponPromotionViews).orElse(new ArrayList<>()).stream().filter(it -> it.getDiscountPrice() != null).anyMatch(it -> it.getDiscountPrice().compareTo(BigDecimal.ZERO) != 0)
                        || Optional.ofNullable(availableGiftRulePromotionViews).orElse(new ArrayList<>()).stream().filter(it -> it.getDiscountPrice() != null).anyMatch(it -> it.getDiscountPrice().compareTo(BigDecimal.ZERO) != 0);
        if (!usedDiscount) {
            return;
        }
        // 是否是医保支付
        if (!getIsNeedUseLimitPrice()) {
            return;
        }
        // 使用折扣，是否开启了配置
        if (chargeConfigDetail == null) {
            // 再查次，有可能前面没有查
            chargeConfigDetail = sheetProcessorInfoProvider.getChargePayModeProvider().getBranchConfigDetail(chargeSheet.getChainId(), chargeSheet.getClinicId(), false);
        }
        int usedDiscountNotAllowShebaoSwitch = Optional.ofNullable(chargeConfigDetail).map(ChargeConfigDetailView::getUsedDiscountNotAllowShebaoSwitch).orElse(0);
        if (usedDiscountNotAllowShebaoSwitch == 0) {
            return;
        }
        throw new ChargeServiceException(ChargeServiceError.USED_DISCOUNT_NOT_SHEBAO);
    }

    /**
     * 校验收费单的皮试项目是否都为阳性
     */
    private void checkAstResultOrThrowException(int source) {
        if (sheetProcessorInfoProvider.getChargePayModeProvider() == null) {
            return;
        }
        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED) {
            return;
        }
        // 续方不检测皮试结果
        if (chargeSheet.getType() == ChargeSheet.Type.CLONE_PRESCRIPTION && source == Constants.ChargeSource.WE_CLINIC) {
            return;
        }
        chargeConfigDetail = sheetProcessorInfoProvider.getChargePayModeProvider().getBranchConfigDetail(chargeSheet.getChainId(), chargeSheet.getClinicId(), false);
        int chargeNeedAstPassSwitch = Optional.ofNullable(chargeConfigDetail).map(ChargeConfigDetailView::getChargeNeedAstPassSwitch).orElse(0);
        if (chargeNeedAstPassSwitch == 0) {
            return;
        }
        // 判断是否有需要皮试的收费项
        List<String> needAstResultSourceFormItemIds = formProcessors.stream()
                .filter(formProcessor -> formProcessor.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_INFUSION || formProcessor.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_WESTERN)
                .flatMap(formProcessor -> formProcessor.getItemProcessorList().stream())
                .filter(itemProcessor -> itemProcessor.getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED)
                .filter(itemProcessor -> org.apache.commons.lang3.StringUtils.isNotEmpty(itemProcessor.getSourceFormItemId()))
                .filter(itemProcessor -> Optional.ofNullable(itemProcessor.getUsageInfo()).map(UsageInfo::getAst).orElse(0) == 1)
                .map(ItemProcessor::getSourceFormItemId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(needAstResultSourceFormItemIds)) {
            return;
        }
        List<PrescriptionAstResult> astResults = sheetProcessorInfoProvider.getOutpatientInfoProvider().getOutpatientPrescriptionAstResultList(needAstResultSourceFormItemIds, chargeSheet.getPatientOrderId());
        List<PrescriptionAstResult> availableAstResults = astResults.stream().filter(astResult -> org.apache.commons.lang3.StringUtils.isNotEmpty(astResult.getResult())).collect(Collectors.toList());
        if (availableAstResults.size() != needAstResultSourceFormItemIds.size()) {
            throw new ChargeServiceException(ChargeServiceError.CHARGE_AST_NOT_FINISHED);
        }
        if (availableAstResults.stream().anyMatch(astResult -> Objects.equals("阳性", astResult.getResult()))) {
            throw new ChargeServiceException(ChargeServiceError.CHARGE_AST_RESULT_CONTAIN_POSITIVE);
        }
    }

    private void checkPatientPointRuleChangedOrException(PatientPointsInfoReq patientPointsInfoReq) {
//        if (patientPointsInfoReq == null || !patientPointsInfoReq.getChecked()) {
//            return;
//        }
//
//        if (patientInfo == null || patientInfo.getPatientPoints() == null) {
//            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "patientInfo.patientPoints is null");
//            throw new ChargeServiceException(ChargeServiceError.PATIENT_POINTS_RULE_CHANGED);
//        }
//
//        PatientPointsInfo patientPoints = patientInfo.getPatientPoints();
//        if (!Objects.equals(patientPointsInfoReq.getPointsDeductionRat(), patientPoints.getPointsDeductionRat())) {
//            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "积分规则发生变化, client pointsDeductionRat: {}, server pointsDeductionRat: {}",
//                    patientPointsInfoReq.getPointsDeductionRat(),
//                    patientPoints.getPointsDeductionRat());
//            throw new ChargeServiceException(ChargeServiceError.PATIENT_POINTS_RULE_CHANGED);
//        }
    }

    /**
     * 锁单这里要算快递费，calSource的值其实取得是payInfo里面的paySource
     */
    public void placePayOrderForPayCallback(PayInfo payInfo) throws ServiceInternalException, ChargeServiceException {
//        updatePatientInfo(false);
        updateProductInfo(true, false, 0, 1);
        updateChargeSheetIsDispensing();
//        updateExpressDeliveryFeeAndProcessFee(calSource);
//        //计算空中药房的加工费和快递费并赋值
//        updateAirPharmacyChargeFormDeliveryFeeAndProcessFee();
//        updateAllUsageProcessFee();
//        checkProductInfoOrThrowException();
//        checkPriceChangedAndThrowException();
//        checkAirPharmacyAndThrowException(payInfo.getPaySource());
        updateUsageInfoProcessUsage();
        ApplyPromotionInfoResult applyPromotionInfoResult = fetchAndApplyPromotionInfo(payInfo.getPromotions(), payInfo.getCouponPromotions(), payInfo.getGiftRulePromotions(), payInfo.getPatientCardPromotions(), payInfo.getPatientPointDeductProductPromotions(), payInfo.getMallVerifications(), payInfo.getPatientPointsInfo(), null, chargeSheet.getOddFee());
        checkCouponCountAndThrowException();
//        fetchAndSetMemberInfo();
        //处理满减满赠
        fillChargeFormForGift();

        BigDecimal adjustmentFee = applyPromotionInfoResult.getAdjustmentFeeOnCalculate();

        applyAdjustment(adjustmentFee);
        applyChargeFormItemPromotionInfoUnitAdjustmentFee();

        doCalculate(BigDecimal.ZERO, CalculateScene.PAY);

        //更新收费项的待收金额
        updateReceivableFee(BigDecimal.ZERO);

        BigDecimal frontEndReceivableFee = MathUtils.wrapBigDecimalOrZero(payInfo.getReceivableFee());
        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "[placePayOrderForPayCallback] charge fee info, totalFee:{}, discountFee:{}, adjustmentFee:{}, receivableFee:{}, realReceivableFee:{}, receivedFee:{}, frontEndReceivableFee:{}",
                totalFee, discountFee, adjustmentFee, receivableFee, realReceivableFee, _receivedFee, frontEndReceivableFee);

        if (realReceivableFee.compareTo(BigDecimal.ZERO) < 0) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "realReceivableFee <= 0:" + receivableFee);
            throw new ChargeServiceException(ChargeServiceError.CHARGE_NO_VALID_RECEIVABLE);
        }

        if (realReceivableFee.compareTo(frontEndReceivableFee) != 0) {
            throw new ChargeServiceException(ChargeServiceError.CHARGE_FEE_CHANGED);
        }

        //如果是社保支付，校验本次收费金额是否超过社保应收
//        if (Constants.ChargePayMode.HEALTH_CARD == payMode && sheBaoReceivableFee.compareTo(frontEndReceivableFee) < 0) {
//            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "本次收费金额不能超过社保应收， sheBaoReceivableFee: {}, needPayFee: {}", sheBaoReceivableFee, frontEndReceivableFee);
//            throw new ChargeServiceException(ChargeServiceError.CHARGE_FEE_MORE_THAN_RECEIVABLE);
//        }

        if (adjustmentFee.compareTo(BigDecimal.ZERO) > 0) {
            //兼容历史遗留单据 新加一个type
            insertChargeAdditionFee(ChargeAdditionalFee.AdditionalFeeType.ADJUSTMENT_ADD_FEE, adjustmentFee, false, payInfo.getOperatorId());
        } else if (adjustmentFee.compareTo(BigDecimal.ZERO) < 0) {
            insertChargeAdditionFee(ChargeAdditionalFee.AdditionalFeeType.ADJUSTMENT_DISCOUNT_FEE, adjustmentFee, false, payInfo.getOperatorId());
        }

        if (!CollectionUtils.isEmpty(availablePromotionViews) || adjustmentFee.compareTo(BigDecimal.ZERO) != 0) {
            SheetPromotionInfo sheetPromotionInfo = new SheetPromotionInfo();
            sheetPromotionInfo.setPromotions(availablePromotionViews == null ? new ArrayList<>() : availablePromotionViews);
            sheetPromotionInfo.setAdjustmentFee(adjustmentFee);
            chargeSheet.setPromotionInfo(sheetPromotionInfo);
            chargeSheet.setPromotionInfoJson(JsonUtils.dump(sheetPromotionInfo));
        }

        int payStatus = ChargeFormItem.PayStatus.PARTED_PAID;
        formProcessors.forEach(formProcessor -> formProcessor.pay(payStatus, payInfo.getPaySource(), payInfo.getOperatorId()));
        chargeSheet.setStatus(Constants.ChargeSheetStatus.PART_CHARGED);
        chargeSheet.setChargedBy(operatorId);
    }

    /**
     * 计算整个单子的总议价值
     *
     * @param expectedAdjustmentFee 前端传的希望整单议价的值
     * @param expectedOddFee        前端传的希望系统议价的值
     * @param draftAdjustmentFee    已保存的收费处议价值
     * @return
     */
    private BigDecimal calculateAdjustFee(BigDecimal expectedAdjustmentFee, BigDecimal expectedOddFee, BigDecimal draftAdjustmentFee) {
        BigDecimal adjustmentFee;
        BigDecimal outpatientAdjustmentFee = BigDecimal.ZERO;
        this.draftAdjustmentFee = expectedAdjustmentFee != null ? expectedAdjustmentFee : MathUtils.wrapBigDecimalOrZero(draftAdjustmentFee);
        BigDecimal totalFee;
        BigDecimal discountFee;
        if (chargeSheet.getType() == ChargeSheet.Type.MEMBER_RECHARGE) {
            totalFee = chargeSheet.getTotalFee();
        } else {
            totalFee = MathUtils.setScaleTwo(formProcessors.stream()
                    .map(formProcessor -> formProcessor.getTotalPrice(false))
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        discountFee = MathUtils.setScaleTwo(formProcessors.stream()
                .map(formProcessor -> formProcessor.getDiscountPrice(false))
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        BigDecimal discountedTotalFee = totalFee.add(discountFee);
        BigDecimal needRoundingFee = MathUtils.wrapBigDecimalAdd(discountedTotalFee, outpatientAdjustmentFee);


        afterRoundingDiscountedTotalFee = isCanCalculateOddFee() && calculateOddFee && isNeedCalculateSystemAdjustmentFee() ? calculateRoundingResult(needRoundingFee) : needRoundingFee;

        if (expectedOddFee != null) {
            this.oddFee = expectedOddFee;
        } else if (calculateOddFee && isNeedCalculateSystemAdjustmentFee()) {
            oddFee = MathUtils.wrapBigDecimalSubtract(afterRoundingDiscountedTotalFee, needRoundingFee);
        }

        adjustmentFee = MathUtils.wrapBigDecimalAdd(this.draftAdjustmentFee, outpatientAdjustmentFee, oddFee);
        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "calculateAdjustFee result: adjustmentFee: {}, totalFee: {}, discountFee: {}, afterRoundingDiscountedTotalFee: {}, oddFee: {}, draftAdjustmentFee: {}",
                adjustmentFee, totalFee, discountFee, afterRoundingDiscountedTotalFee, this.oddFee, this.draftAdjustmentFee);
        chargeSheet.setDraftAdjustmentFee(this.draftAdjustmentFee);
        chargeSheet.setOddFee(oddFee);
        return adjustmentFee;
    }

    private boolean isCanCalculateOddFee() {
        ChargeConfigDetailView chargeConfigDetailView = getChargeConfigDetailView();
        if (chargeConfigDetailView.getOddFeeDealType() == 0) {
            return true;
        }

        return chargeConfigDetailView.getOddFeeDealPayModes().contains(Long.valueOf(payMode));
    }

    private void checkRoundingTypeChangeAndThrowException(Integer roundingTypeReq) {
        if (isNeedCalculateSystemAdjustmentFee() && roundingTypeReq != null && roundingType != null && !Objects.equals(roundingTypeReq, roundingType)) {
            sLogger.error("四舍五入的规则发生改变");
            throw new ChargeServiceException(ChargeServiceError.CHARGE_FEE_CHANGED);
        }
    }

    private void checkAirPharmacyAndThrowException(int paySource) throws ChargeServiceException {
        for (FormProcessor formProcessor : formProcessors) {
            if (!formProcessor.isAirPharmacy()) {
                continue;
            }
            // 校验空中药房是否满足起做量
            formProcessor.checkMinimumOrThrowException();
            // 校验空中药房是否缺药
            formProcessor.checkAirPharmacyOrThrowException(paySource);
            // 校验如果空中药房form没有药品，则不应该有议价费用
            formProcessor.checkAirPharmacyExpectedPriceAndUpdateTotalPrice();
            // 校验空中药房的快递费和加工费是否发生变化

//            if (paySource != Constants.ChargeSource.WE_CLINIC) {
//                formProcessor.checkAirPharmacyDeliveryAndProcessAndThrowException();
//            }
        }

    }

//    private void updateAllUsageProcessFee() {
//        if (CollectionUtils.isEmpty(processInfoViews)) {
//            return;
//        }
//
//        Map<String, ProcessInfoView> processInfoRspMap = ListUtils.toMap(processInfoViews, ProcessInfoView::getId);
//
//        chargeSheet.getChargeSheetProcessInfos()
//                .stream()
//                .filter(processInfo -> processInfo.getChecked())
//                .forEach(processInfo -> {
//                    ProcessInfoView processInfoView = processInfoRspMap.get(processInfo.getId());
//                    if (processInfoView != null) {
//                        processInfo.setProcessFee(processInfoView.getProcessFee());
//                        if (processInfoView.getProcessUsageInfo() != null) {
//                            processInfo.setProcessUsageJson(JsonUtils.dump(processInfoView.getProcessUsageInfo()));
//                            processInfo.setProcessUsageInfo(processInfoView.getProcessUsageInfo());
//                        }
//                    }
//
//                });
//    }

    private void checkCouponCountAndThrowException() throws ChargeServiceException {
        if (CollectionUtils.isEmpty(availableCouponPromotionViews)) {
            return;
        }
        for (CouponPromotionView couponPromotionView : availableCouponPromotionViews) {
            if (!couponPromotionView.getChecked()) {
                continue;
            }
            if (couponPromotionView.getCurrentCount() > couponPromotionView.getAvailableCount()) {
                sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "currentCount 大于 availableCount, currentCount: {}, availableCount: {}, promotionId : {}", couponPromotionView.getCurrentCount(), couponPromotionView.getAvailableCount(), couponPromotionView.getId());
                throw new ChargeServiceException(ChargeServiceError.COUPON_NUM_EXCEED);
            }
        }
    }

    private void fillChargeFormForGift() {
        if (CollectionUtils.isEmpty(availableGiftRulePromotionViews)) {
            return;
        }
        List<GiftRulePromotionView.GiftGoodsItemView> giftGoods = availableGiftRulePromotionViews.stream()
                .filter(GiftRulePromotionView::getChecked)
                .map(GiftRulePromotionView::getGiftGoodItems)
                .filter(giftGoodItems -> !CollectionUtils.isEmpty(giftGoodItems))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(giftGoods)) {
            return;
        }
        // 查询goods的默认药房
        QueryGoodsInPharmacyByIdsReq.QueryPharmacyGoodsReq req = new QueryGoodsInPharmacyByIdsReq.QueryPharmacyGoodsReq();
        // 如果是复制历史处方，不指定药房号，根据goods查询的默认药房重新赋值
        req.setPharmacyNo(null);
        req.setPharmacyType(GoodsConst.PharmacyType.LOCAL_PHARMACY);
        req.setSceneType(GoodsConst.SceneType.OUTPATIENT);
        req.setDepartmentId(chargeSheet.getQueryGoodsDepartmentId());
        req.setGoodsIds(giftGoods.stream()
                .map(GiftRulePromotionView.GiftGoodsItemView::getId)
                .filter(org.apache.commons.lang3.StringUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.toList())
        );

        List<QueryGoodsInPharmacyByIdsRsp.QueryPharmacyGoodsRsp> queryPharmacyGoodsRsps = sheetProcessorInfoProvider.getGoodsScProvider().queryGoodsInPharmacyByIds(chargeSheet.getClinicId(), chargeSheet.getChainId(), false, 1, Arrays.asList(req));
        List<GoodsItem> goodsItems = org.apache.commons.collections.CollectionUtils.isNotEmpty(queryPharmacyGoodsRsps) ? queryPharmacyGoodsRsps.get(0).getList() : new ArrayList<>();
        Map<String, GoodsItem> goodsItemIdMap = ListUtils.toMap(goodsItems, GoodsItem::getId);

        ChargeForm chargeForm = GiftChargeUtils.createGiftChargeFormForGiftRulePromotion(chargeSheet, giftGoods, goodsItemIdMap, operatorId);
        FormProcessor formProcessor = new FormProcessor(chargeForm, getDeletedDataCollector());
        formProcessor.build();
        formProcessor.getItemProcessorList().forEach(itemProcessor -> itemProcessor.setReceivableFee(BigDecimal.ZERO));
        addFormProcessor(formProcessor);
    }

    private ChargeTransaction writePayTransaction(String chargeTransactionId, CombinedPayItem payItem, BigDecimal needPayFee, ThirdPartyPayInfo thirdPartyPayInfo, ChargeAction chargeAction, int paySource, String operatorId, String payInfoExtra, boolean isFirstPay) {
        List<ChargeTransaction> chargeTransactions = new ArrayList<>();
        // 记录流水
//        if (payItem.getNetIncome().compareTo(BigDecimal.ZERO) != 0) {
        ChargeTransaction chargeTransaction = ChargeUtils.insertChargeTransaction(chargeTransactionId, chargeSheet, payItem.getPayMode(), payItem.getPaySubMode(),
                payItem.getNetIncome(), payItem.getPrincipalAmount(), payItem.getPresentAmount(), payItem.getAmount(), payItem.getChange(), needPayFee,
                paySource, thirdPartyPayInfo, chargeAction, operatorId);
        chargeTransactions.add(chargeTransaction);
        _receivedFee = _receivedFee.add(chargeTransaction.getAmount());
//        }
        chargeAction.setPayStatus(PayStatus.SUCCESS);

//        List<ChargeAdditionalFee> additionalFees = chargeSheet.getAdditionalFees();

        int payStatus;
        if (needPayFee.compareTo(BigDecimal.ZERO) > 0) {
            payStatus = ChargeFormItem.PayStatus.PARTED_PAID;
        } else {
            payStatus = ChargeFormItem.PayStatus.PAID;
        }
        formProcessors.forEach(formProcessor -> formProcessor.pay(payStatus, paySource, operatorId));
        int statPayType;
        if (payStatus == ChargeFormItem.PayStatus.PARTED_PAID) {
            /**
             *微信支付的回调
             * */
            ChargeUtils.setChargeSheetToPayStatus(chargeSheet, payInfoExtra, Constants.ChargeSheetStatus.PART_CHARGED);
            statPayType = StatRecordProcessor.PayType.PARTED_PAID;
            if (MathUtils.wrapBigDecimalCompare(chargeTransaction.getAmount(), BigDecimal.ZERO) != 0) {
                chargeSheet.setOrderByDate(Instant.now());
            }
        } else {
            ChargeUtils.setChargeSheetToPayStatus(chargeSheet, payInfoExtra, Constants.ChargeSheetStatus.CHARGED);
            //如果是社保支付，则可以开票
//            if (chargeSheet.getAdditional() != null && chargeSheet.getAdditional().getInvoiceStatus() == Constants.ChargeSheetInvoiceStatus.NONE) {
//                chargeSheet.getAdditional().setInvoiceStatus(Constants.ChargeSheetInvoiceStatus.INVOICE_WAITING);
//            }
            if (chargeSheet.getAdditional() != null && Objects.isNull(chargeSheet.getAdditional().getInvoiceStatusFlag())) {
                chargeSheet.getAdditional().setInvoiceStatusFlag(Constants.ChargeSheetInvoiceStatusFlag.HAS_INVOICABLE_ITEM);
            }
            statPayType = StatRecordProcessor.PayType.PAID;
            chargeSheet.setOrderByDate(Instant.now());
            //收费后微诊所强制展示的收费单类型
            if (ChargeSheet.Type.paidSheetWeClinicAlwaysShowTypes().contains(chargeSheet.getType())) {
                chargeSheet.setSendToPatientStatus(1);
            }
            // 全部支付时，处理赠品和全部抵扣完的商品
            formProcessors.forEach(FormProcessor::addGiftGoodsAndAllDeductGoodsInAffectedItemsAndZeroPriceItem);
        }

        chargeSheet.specialChargedTime(chargeAction.getSpecifiedChargedTime());
        chargeSheet.specialFirstChargedTime(chargeAction.getSpecifiedChargedTime());
        dealChargeOwe(chargeTransaction);
        // 查询clinicId的是普通诊所还是医院管家
        boolean isHospital = queryIsHospital();
        // 统计
        statRecordProcessor.setChargeInfo(
                statPayType,
                chargeSheet.getTransactionRecordHandleMode(),
                StatRecordProcessor.RefundFlatType.ALL_FLAT,
                isHospital,
                chargeTransactions,
                formProcessors.stream()
                        .filter(formProcessor -> formProcessor.getSourceFormType() != Constants.SourceFormType.AIR_PHARMACY)
                        .flatMap(formProcessor -> formProcessor.getAffectedChargeFormItems().stream())
                        .filter(Objects::nonNull).collect(Collectors.toList()),
                formProcessors.stream()
                        .filter(formProcessor -> formProcessor.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY)
                        .map(FormProcessor::getAffectedForm)
                        .filter(Objects::nonNull).collect(Collectors.toList()),
                isFirstPay ? formProcessors.stream()
                        .filter(formProcessor -> formProcessor.getSourceFormType() != Constants.SourceFormType.AIR_PHARMACY)
                        .filter(formProcessor -> org.apache.commons.collections.CollectionUtils.isNotEmpty(formProcessor.getItemProcessorList()))
                        .flatMap(formProcessor -> formProcessor.getItemProcessorList().stream())
                        .flatMap(itemProcessor -> itemProcessor.generateStatRecordAffectedDeductedItem().stream())
                        .filter(Objects::nonNull).collect(Collectors.toList())
                        : null,
                null,
                null,
                formProcessors.stream().flatMap(formProcessor -> formProcessor.getChargeFormItems().stream())
                        .filter(Objects::nonNull)
                        .filter(chargeFormItem -> chargeFormItem.getComposeType() == ComposeType.COMPOSE || chargeFormItem.getGoodsFeeType() == GoodsFeeType.FEE_PARENT)
                        .collect(Collectors.toList()),
                generateStatParentChildrenChargeFormItemsMap()
        );
        return chargeTransaction;
    }

    private boolean queryIsHospital() {
        cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ organ = null;
        try {
            organ = sheetProcessorInfoProvider.getClinicProvider().getOrgan(clinicId);
        } catch (Exception e) {
            sLogger.error("query organ error, clinicId:{}, erroe msg: {}", clinicId, e.getMessage());
        }
        return Objects.equals(cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ.HisType.CIS_HIS_TYPE_HOSPITAL, Optional.ofNullable(organ)
                .map(cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ::getHisType)
                .orElse(cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ.HisType.CIS_HIS_TYPE_NORMAL));
    }

    private Map<String, List<ChargeFormItem>> generateStatParentChildrenChargeFormItemsMap() {
        // 这里要打平成母项id和子项关系的一个map，有两种情况，1.套餐母项和套餐子项，
        Map<String, List<ChargeFormItem>> composeChildrenChargeFormItemsMap = generateStatComposeChildrenChargeFormItemsMap();
        // 2.费用母项和费用子项，同时费用母项又可能是套餐子项
        composeChildrenChargeFormItemsMap.putAll(generateStatFeeChildrenChargeFormItemsMap());
        return composeChildrenChargeFormItemsMap;
    }

    public Map<String, List<ChargeFormItem>> generateStatComposeChildrenChargeFormItemsMap() {
        return formProcessors.stream().flatMap(formProcessor -> formProcessor.getItemProcessorList().stream())
                .filter(itemProcessor -> itemProcessor.getComposeType() == ComposeType.COMPOSE)
                .collect(Collectors.toMap(ItemProcessor::getItemId, itemProcessor -> itemProcessor.getComposeChildren()
                                .stream()
                                .map(childrenItemProcessor -> {
                                    List<ChargeFormItem> allChildrenChargeFormItems = new ArrayList<>();
                                    allChildrenChargeFormItems.add(childrenItemProcessor.getChargeFormItem());
                                    allChildrenChargeFormItems.addAll(childrenItemProcessor.getRefundChargeFormItems());
                                    return allChildrenChargeFormItems;
                                }).flatMap(Collection::stream)
                                .filter(chargeFormItem -> chargeFormItem.getComposeType() == ComposeType.COMPOSE_SUB_ITEM)
                                .collect(Collectors.toList()), (a, b) -> a
                        )
                );
    }

    public Map<String, List<ChargeFormItem>> generateStatFeeChildrenChargeFormItemsMap() {
        return formProcessors.stream()
                .flatMap(formProcessor -> formProcessor.getItemProcessorList().stream())
                .flatMap(itemProcessor -> {
                    List<ItemProcessor> itemProcessors = new ArrayList<>();
                    itemProcessors.add(itemProcessor);
                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(itemProcessor.getComposeChildren())) {
                        itemProcessors.addAll(itemProcessor.getComposeChildren());
                    }
                    return itemProcessors.stream();
                })
                .filter(itemProcessor -> itemProcessor.getGoodsFeeType() == GoodsFeeType.FEE_PARENT)
                .collect(Collectors.toMap(ItemProcessor::getItemId, itemProcessor -> itemProcessor.getComposeChildren()
                                .stream()
                                .map(childItemProcessor -> {
                                    List<ChargeFormItem> allChildrenChargeFormItems = new ArrayList<>();
                                    allChildrenChargeFormItems.add(childItemProcessor.getChargeFormItem());
                                    allChildrenChargeFormItems.addAll(childItemProcessor.getRefundChargeFormItems());
                                    return allChildrenChargeFormItems;
                                }).flatMap(Collection::stream)
                                .collect(Collectors.toList()), (a, b) -> a
                        )
                );
    }

    /**
     * 锁单支付，只有异步支付才走这个方法，目前的异步支付：微信支付，医保卡支付
     *
     * @param payInfo
     * @return
     * @throws ChargeServiceException
     * @throws ServiceInternalException
     */
    public PayResult lockPay(PayInfo payInfo, boolean isPayForLeft) {
        if (!isPayForLeft) {
            updatePatientInfo();
            fetchAndSetMemberInfo(true);
            updateProductInfo(true, false, 0, 1);
            updateChargeFormItemCount();
            checkExecuteItemCountOrThrowException();
            /**
             * 更新虚拟药房的快递费
             */
            updateVirtualPharmacyChargeFormDeliveryFee(payInfo.getPaySource());
            updateExpressDeliveryFeeAndProcessFee(payInfo.getPaySource());
            //计算空中药房的加工费和快递费并赋值
            updateAirPharmacyChargeFormDeliveryFeeAndProcessFee();
//            updateAllUsageProcessFee();
            checkAstResultOrThrowException(payInfo.getPaySource());
            checkProductInfoOrThrowException(payInfo.getPaySource());

            checkPatientPointRuleChangedOrException(payInfo.getPatientPointsInfo());
            checkAirPharmacyAndThrowException(payInfo.getPaySource());
            ApplyPromotionInfoResult applyPromotionInfoResult = fetchAndApplyPromotionInfo(payInfo.getPromotions(), payInfo.getCouponPromotions(), payInfo.getGiftRulePromotions(), payInfo.getPatientCardPromotions(), payInfo.getPatientPointDeductProductPromotions(), payInfo.getMallVerifications(), payInfo.getPatientPointsInfo(), payInfo.getExpectedAdjustmentFee(), payInfo.getExpectedOddFee());
            checkCouponCountAndThrowException();
            checkPriceChangedAndThrowException();
            // 检验是否使用会员/卡项优惠,使用了不允许使用医保收费
            checkUsedDiscountAndShebaoPayOrThrowException();
//            BigDecimal adjustmentFee = calculateAdjustFee(payInfo.getExpectedAdjustmentFee(),
//                    MathUtils.wrapBigDecimalOrZero(payInfo.getOutpatientAdjustmentFee()),
//                    payInfo.getExpectedOddFee(),
//                    MathUtils.wrapBigDecimalOrZero(payInfo.getDraftAdjustmentFee()));
//
//            updateReceivableFee(adjustmentFee);

            doCalculate(applyPromotionInfoResult.getAdjustmentFeeOnCalculate(), CalculateScene.PAY);
            checkRoundingTypeChangeAndThrowException(payInfo.getRoundingType());
        } else {
            doCalculate(BigDecimal.ZERO, CalculateScene.PAY_FOR_LEFT);
        }
        updateChargeSheetIsDispensing();
        BigDecimal frontEndReceivableFee = MathUtils.wrapBigDecimalOrZero(payInfo.getReceivableFee());
        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "[lockPay] charge fee info, totalFee:{}, discountFee:{}, adjustmentFee:{}, receivableFee:{}, realReceivableFee:{}, receivedFee:{}, frontEndReceivableFee:{}",
                totalFee, discountFee, adjustmentFee, receivableFee, realReceivableFee, _receivedFee, frontEndReceivableFee);

        if (realReceivableFee.compareTo(BigDecimal.ZERO) < 0) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "realReceivableFee <= 0:" + receivableFee);
            throw new ChargeServiceException(ChargeServiceError.CHARGE_NO_VALID_RECEIVABLE);
        }

        if (realReceivableFee.compareTo(frontEndReceivableFee) != 0) {
            throw new ChargeServiceException(ChargeServiceError.CHARGE_FEE_CHANGED);
        }

        CombinedPayItem payItem = new CombinedPayItem();
        if (Objects.nonNull(payInfo.getCombinedPayment()) && !CollectionUtils.isEmpty(payInfo.getCombinedPayment().getCombinedPayItems())) {
            payItem = payInfo.getCombinedPayment().getCombinedPayItems().get(0);
        }
        updateThisTimeChargePayItemInfos(payItem, null, isPayForLeft);
        BigDecimal needPayFee = payItem.dealChangeFee(realReceivableFee, new ChargeServiceException(ChargeServiceError.CHARGE_FEE_MORE_THAN_RECEIVABLE));
        // 这里开始要区分了，直接入账还是三方支付
        boolean needThirdPartyPay = needCallThirdPartyPay(payItem, chargeSheet.getChainId(), chargeSheet.getClinicId());
        if (!needThirdPartyPay) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "[afterLockChargeSheetPay] 该门店不支持第三方支付");
            throw new ChargeServiceException(ChargeServiceError.CHAIN_NOT_OPEN_THIRD_PART_PAY);
        }
        ChargeAction chargeAction = null;
        int payStatus;
        String thirdPartyPayTaskId = null;
        ThirdPartyPayInfo thirdPartyPayInfo = null;
        String chargePayTransactionId = null;
        ChargeTransaction chargeTransaction = null;
        if (payItem.getNetIncome().compareTo(BigDecimal.ZERO) > 0
                // 0元的收费单，也要支持过医保
                || (payItem.getNetIncome().compareTo(BigDecimal.ZERO) == 0 && payItem.getPayMode() == Constants.ChargePayMode.HEALTH_CARD)) {
            if (chargeSheet.getExpireTime() != null && Constants.ChargeSource.patientPaySources().contains(payInfo.getPaySource())) {
                if (chargeSheet.getExpireTime().isBefore(Instant.now())) {
                    sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "订单已超过截止支付的时间，不能支付");
                    throw new ChargeServiceException(ChargeServiceError.CHARGE_THIRDPART_PAY_TIMEOUT);
                }
            }
            ChargePayInfo chargePayInfo = new ChargePayInfo();
            chargePayInfo.setExtra(payInfo.getExtra());//微信自助支付，需要在微信支付成功的回调里面修改状态
            chargePayInfo.setChainId(chargeSheet.getChainId());
            chargePayInfo.setClinicId(chargeSheet.getClinicId());
            chargePayInfo.setChargeSheetId(chargeSheet.getId());
            chargePayInfo.setChargeActionId(AbcIdUtils.getUUID());
            chargePayInfo.setDoctorId(chargeSheet.getDoctorId());
            chargePayInfo.setSellerId(chargeSheet.getSellerId());
            chargePayInfo.setPatientId(chargeSheet.getPatientId());
            chargePayInfo.setPatientOrderId(chargeSheet.getPatientOrderId());
            chargePayInfo.setDiagnosedDate(chargeSheet.getDiagnosedDate());
            chargePayInfo.setChargeTransactions(chargeSheet.getChargeTransactions());
            chargePayInfo.setPatientOrderNo(patientOrder != null ? (int) patientOrder.getNo() : 0);
            chargePayInfo.setMemberCardPassword(payItem.getMemberCardPassword());
            chargePayInfo.setPayMode(payItem.getPayMode());
            chargePayInfo.setPayModeName(payItem.getPayModeName());
            chargePayInfo.setThisTimeChargePayItemInfos(thisTimeChargePayItemInfos);
            chargePayInfo.setSheetReceivableFee(realReceivableFee);
            chargePayInfo.setReceivableFee(payItem.getNetIncome());
            chargePayInfo.setAuthCode(payItem.getAuthCode());
            chargePayInfo.setChargeTransactionId(AbcIdUtils.getUUID());
            chargePayInfo.setThirdPartyPayCardId(payItem.getThirdPartyPayCardId());
            chargePayInfo.setThirdPartyPayCardPassword(payItem.getThirdPartyPayCardPassword());
            chargePayInfo.setPaySource(payInfo.getPaySource());
            chargePayInfo.setChargeComment(payInfo.getChargeComment());
            String body = chargeSheet.getType() == ChargeSheet.Type.REGISTRATION ? Constants.SystemProductId.REGISTRATION_NAME :
                    chargeSheet.getType() == ChargeSheet.Type.MEMBER_RECHARGE ? "会员充值" :
                            "收费单：" + AbcIdUtils.convertUUidToLongString(chargeSheet.getId());
            chargePayInfo.setBody(body);
            chargePayInfo.setMemberId(memberId);
            chargePayInfo.setOperatorId(operatorId);
            chargePayInfo.setOpenId(payInfo.getOpenId());
            chargePayInfo.setWechatPaySource(chargeSheet.getType() == ChargeSheet.Type.REGISTRATION ? WeChatPayReq.ServiceSource.CHARGE_FOR_REGISTRATION
                    : chargeSheet.getType() == ChargeSheet.Type.ONLINE_CONSULTATION ? WeChatPayReq.ServiceSource.CHARGE_FOR_ONLINE_CONSULTATION
                    : WeChatPayReq.ServiceSource.CHARGE);
            chargePayInfo.setWechatPayTimeExpire(chargeSheet.getExpireTime() != null ? DateUtils.convertInstantToString(chargeSheet.getExpireTime(), DateUtils.DATE_FORMAT_COMPACT_DATETIME) : null);
            chargePayInfo.setClientIp(payInfo.getClientIp());
            chargePayInfo.setPaySubMode(payItem.getPaySubMode());
            chargePayInfo.setIsCannotCalculateRounding(isCannotCalculateRounding);
            chargePayInfo.setAppId(payInfo.getAppId());
            chargePayInfo.setSpecifiedChargedTime(payInfo.getSpecifiedChargedTime());

            ChargePayResult chargePayResult = sheetProcessorInfoProvider.getChargePayProvider().pay(chargePayInfo);
            if (chargePayResult != null) {
                chargePayTransactionId = chargePayResult.getChargePayTransactionId();
                payStatus = chargePayResult.getPayStatus();
                thirdPartyPayTaskId = chargePayResult.getThirdPayTaskId();
                thirdPartyPayInfo = chargePayResult.getPayInfo();
                if (payStatus == PayStatus.SUCCESS) {
                    payItem.setAmount(chargePayResult.getReceivedFee());
                    payItem.setPresentAmount(chargePayResult.getReceivedPresent());
                    payItem.setPrincipalAmount(chargePayResult.getReceivedPrincipal());
                    payItem.setChange(BigDecimal.ZERO);
                    payItem.setPaySubMode(chargePayResult.getPaySubMode());

                    needPayFee = realReceivableFee.subtract(MathUtils.wrapBigDecimalOrZero(payItem.getNetIncome()));
                    needPayFee = needPayFee.compareTo(BigDecimal.ZERO) > 0 ? needPayFee : BigDecimal.ZERO;
                    if (!isPayForLeft) {
                        //处理满减满赠
                        fillChargeFormForGift();
                        applyAdjustment(adjustmentFee);
                        applyChargeFormItemPromotionInfoUnitAdjustmentFee();
                        if (adjustmentFee.compareTo(BigDecimal.ZERO) > 0) {
                            //兼容历史遗留单据 新加一个type
                            insertChargeAdditionFee(ChargeAdditionalFee.AdditionalFeeType.ADJUSTMENT_ADD_FEE, adjustmentFee, false, payInfo.getOperatorId());
                        } else if (adjustmentFee.compareTo(BigDecimal.ZERO) < 0) {
                            insertChargeAdditionFee(ChargeAdditionalFee.AdditionalFeeType.ADJUSTMENT_DISCOUNT_FEE, adjustmentFee, false, payInfo.getOperatorId());
                        }

                        if (!CollectionUtils.isEmpty(availablePromotionViews) || adjustmentFee.compareTo(BigDecimal.ZERO) != 0) {
                            SheetPromotionInfo sheetPromotionInfo = new SheetPromotionInfo();
                            sheetPromotionInfo.setPromotions(availablePromotionViews == null ? new ArrayList<>() : availablePromotionViews);
                            sheetPromotionInfo.setAdjustmentFee(adjustmentFee);
                            chargeSheet.setPromotionInfo(sheetPromotionInfo);
                            chargeSheet.setPromotionInfoJson(JsonUtils.dump(sheetPromotionInfo));
                        }
                        chargeSheet.setIsDraft(0);
                    }
                    updateReceivedPrice(payItem, isPayForLeft);
                    chargeAction = ChargeUtils.insertChargeAction(chargePayInfo.getChargeActionId(), chargeSheet, ChargeAction.Type.PAY, PayStatus.SUCCESS, payItem.getNetIncome(), payItem.getPayActionInfo(), payInfo.getChargeComment(), payInfo.getSpecifiedChargedTime(), payInfo.getOperatorId());
                    chargeTransaction = writePayTransaction(chargePayInfo.getChargeTransactionId(), payItem, needPayFee, thirdPartyPayInfo, chargeAction, payInfo.getPaySource(), payInfo.getOperatorId(), payInfo.getExtra(), !isPayForLeft);
//                    updateChargePayTransactionCanceled(null, operatorId);
                    ChargeUtils.unlockChargeSheetV2(chargeSheet, 0, true);
                } else {
                    lockPatientOrderAndSendAutoCancelMessage(chargePayResult, payInfo.getPaySource());
                }
            } else {
                payStatus = PayStatus.FAILED;
            }
        } else {
            //处理满减满赠
            fillChargeFormForGift();
            applyAdjustment(adjustmentFee);
            applyChargeFormItemPromotionInfoUnitAdjustmentFee();
            updateReceivedPrice(payItem, isPayForLeft);
            if (adjustmentFee.compareTo(BigDecimal.ZERO) > 0) {
                //兼容历史遗留单据 新加一个type
                insertChargeAdditionFee(ChargeAdditionalFee.AdditionalFeeType.ADJUSTMENT_ADD_FEE, adjustmentFee, false, payInfo.getOperatorId());
            } else if (adjustmentFee.compareTo(BigDecimal.ZERO) < 0) {
                insertChargeAdditionFee(ChargeAdditionalFee.AdditionalFeeType.ADJUSTMENT_DISCOUNT_FEE, adjustmentFee, false, payInfo.getOperatorId());
            }

            if (!CollectionUtils.isEmpty(availablePromotionViews) || adjustmentFee.compareTo(BigDecimal.ZERO) != 0) {
                SheetPromotionInfo sheetPromotionInfo = new SheetPromotionInfo();
                sheetPromotionInfo.setPromotions(availablePromotionViews == null ? new ArrayList<>() : availablePromotionViews);
                sheetPromotionInfo.setAdjustmentFee(adjustmentFee);
                chargeSheet.setPromotionInfo(sheetPromotionInfo);
                chargeSheet.setPromotionInfoJson(JsonUtils.dump(sheetPromotionInfo));
            }
            chargeSheet.setIsDraft(0);

            chargeAction = ChargeUtils.insertChargeAction(null, chargeSheet, ChargeAction.Type.PAY, PayStatus.SUCCESS, payItem.getNetIncome(), payItem.getPayActionInfo(), payInfo.getChargeComment(), payInfo.getSpecifiedChargedTime(), payInfo.getOperatorId());
            chargeTransaction = writePayTransaction(null, payItem, needPayFee, null, chargeAction, payInfo.getPaySource(), payInfo.getOperatorId(), payInfo.getExtra(), !isPayForLeft);
//            updateChargePayTransactionCanceled(null, operatorId);
            ChargeUtils.unlockChargeSheetV2(chargeSheet, 0, true);
            payStatus = PayStatus.SUCCESS;
        }

        if (payStatus == PayStatus.SUCCESS) {
            chargeSheet.setChargedBy(operatorId);
        }

        PayResult payResult = new PayResult();
        payResult.setChargePayTransactionId(chargePayTransactionId);
        payResult.setChargeTransactionId(chargeTransaction != null ? chargeTransaction.getId() : null);
        payResult.setStatus(chargeSheet.getStatus());
        payResult.setCreated(Instant.now());
        payResult.setReceivedFee(_receivedFee);
        payResult.setNetIncomeFee(_receivedFee.add(_refundFee));
        payResult.setNeedPay(needPayFee);
        payResult.setChargeActionId(chargeAction != null ? chargeAction.getId() : null);
        payResult.setPayStatus(payStatus);
        payResult.setThirdPartyPayTaskId(thirdPartyPayTaskId);
        payResult.setThirdPartyPayTaskType(thirdPartyPayTaskId != null ? 0 : null);
        payResult.setWeChatPayInfo(thirdPartyPayInfo != null ? thirdPartyPayInfo.getWechatPayInfo() : null);

        return payResult;
    }

    private void lockPatientOrderAndSendAutoCancelMessage(ChargePayResult chargePayResult, int paySource) {

        //收费单锁单
        int addedLockStatus = ChargeUtils.lockChargeSheetForPay(chargeSheet, paySource, realReceivableFee, false, chargeSheet.getExpireTime());

        ChargeSheetLockDetail chargeSheetLockDetail = new ChargeSheetLockDetail();
        chargeSheetLockDetail.setChargeSheetId(chargeSheet.getId())
                .setChargePayTransactionId(chargePayResult.getChargePayTransactionId())
                .setAddedLockStatus(addedLockStatus);

        //锁patientOrder
        MQProducer.doAfterTransactionCommit(() -> sheetProcessorInfoProvider.getPatientOrderProvider().lockPatientOrder(chargeSheet.getPatientOrderId(),
                BusinessLockVO.BusinessKey.CHARGE_SHEET,
                BusinessLockReq.ChargeBusinessScene.CHARGE_SHEET_PAY,
                chargePayResult.getLockPatientOrderExpireTime(),
                chargeSheet.getChainId(),
                chargeSheet.getClinicId(),
                chargeSheetLockDetail,
                operatorId));

        ChargePayTransactionAutoCancelMessage message = new ChargePayTransactionAutoCancelMessage();
        message.setChargePayTransactionId(chargePayResult.getChargePayTransactionId())
                .setChargeSheetId(chargeSheet.getId())
                .setOperatorId(operatorId);

        //发送自动取消支付单消息
        SendMessageUtils.sendChargePayTransactionCancelMessage(message, (int) (chargePayResult.getLockPatientOrderExpireTime() / 1000));

        //发送socket消息通知前端
//        SendMessageUtils.sendPatientOrderLockUnLockMessage(chargeSheet.getChainId(),
//                chargeSheet.getClinicId(),
//                WebMessageBody.WebMessageEvent.CHARGE_SHEET_PAID_PATIENT_ORDER_LOCK,
//                chargeSheet.getId(),
//                chargeSheet.getPatientOrderId(),
//                chargePayResult.getChargePayTransactionId(),
//                operatorId);
    }

    private void lockPatientOrderForRefund(ChargePayRefundResult chargePayResult, int payMode) {

        //退费锁收费单
        int addedLockStatus = ChargeUtils.lockChargeSheetForRefund(chargeSheet);

        ChargeSheetLockDetail chargeSheetLockDetail = new ChargeSheetLockDetail();
        chargeSheetLockDetail.setChargeSheetId(chargeSheet.getId())
                .setChargePayTransactionId(chargePayResult.getChargePayTransactionId())
                .setAddedLockStatus(addedLockStatus);

        //锁patientOrder
        MQProducer.doAfterTransactionCommit(() -> {
            sheetProcessorInfoProvider.getPatientOrderProvider().lockPatientOrder(chargeSheet.getPatientOrderId(),
                    BusinessLockVO.BusinessKey.CHARGE_SHEET,
                    BusinessLockReq.ChargeBusinessScene.CHARGE_SHEET_REFUND,
                    chargePayResult.getLockPatientOrderExpireTime(),
                    chargeSheet.getChainId(),
                    chargeSheet.getClinicId(),
                    chargeSheetLockDetail,
                    operatorId);
        });

        //发送rocketMq delay消息，只有社保才去自动取消支付，其他都不自动取消
        if (Constants.ChargePayMode.SHEBAO_PAY_MODES.contains(payMode) && chargePayResult.getLockPatientOrderExpireTime() != null) {

            ChargePayTransactionAutoCancelMessage message = new ChargePayTransactionAutoCancelMessage();
            message.setChargePayTransactionId(chargePayResult.getChargePayTransactionId())
                    .setChargeSheetId(chargeSheet.getId())
                    .setExpireTime(chargePayResult.getExpireTime())
                    .setOperatorId(operatorId);
            SendMessageUtils.sendChargePayTransactionCancelMessage(message, (int) (chargePayResult.getLockPatientOrderExpireTime() / 1000));
        }
    }

    public void lockChargeSheet(PayInfo payInfo) {
        updatePatientInfo();
        fetchAndSetMemberInfo(true);
        updateProductInfo(true, false, 0, 1);
        updateChargeFormItemCount();
        checkExecuteItemCountOrThrowException();
        /**
         * 更新虚拟药房的快递费
         */
        updateVirtualPharmacyChargeFormDeliveryFee(payInfo.getPaySource());
        updateExpressDeliveryFeeAndProcessFee(payInfo.getPaySource());
        //计算空中药房的加工费和快递费并赋值
        updateAirPharmacyChargeFormDeliveryFeeAndProcessFee();
        checkProductInfoOrThrowException(payInfo.getPaySource());

        checkPatientPointRuleChangedOrException(payInfo.getPatientPointsInfo());
        checkAirPharmacyAndThrowException(payInfo.getPaySource());
        ApplyPromotionInfoResult applyPromotionInfoResult = fetchAndApplyPromotionInfo(payInfo.getPromotions(), payInfo.getCouponPromotions(), payInfo.getGiftRulePromotions(), payInfo.getPatientCardPromotions(), payInfo.getPatientPointDeductProductPromotions(), payInfo.getMallVerifications(), payInfo.getPatientPointsInfo(), payInfo.getExpectedAdjustmentFee(), payInfo.getExpectedOddFee());
        checkCouponCountAndThrowException();

        checkPriceChangedAndThrowException();
//        BigDecimal adjustmentFee = calculateAdjustFee(payInfo.getExpectedAdjustmentFee(),
//                MathUtils.wrapBigDecimalOrZero(payInfo.getOutpatientAdjustmentFee()),
//                payInfo.getExpectedOddFee(),
//                MathUtils.wrapBigDecimalOrZero(payInfo.getDraftAdjustmentFee()));
//
//        updateReceivableFee(adjustmentFee);

        doCalculate(applyPromotionInfoResult.getAdjustmentFeeOnCalculate(), CalculateScene.PAY);
        checkRoundingTypeChangeAndThrowException(payInfo.getRoundingType());

        BigDecimal frontEndReceivableFee = MathUtils.wrapBigDecimalOrZero(payInfo.getReceivableFee());
        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "[lockChargeSheet] charge fee info, totalFee:{}, discountFee:{}, adjustmentFee:{}, receivableFee:{}, realReceivableFee:{}, receivedFee:{}, frontEndReceivableFee:{}",
                totalFee, discountFee, adjustmentFee, receivableFee, realReceivableFee, _receivedFee, frontEndReceivableFee);

        if (realReceivableFee.compareTo(BigDecimal.ZERO) < 0) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "realReceivableFee <= 0:" + receivableFee);
            throw new ChargeServiceException(ChargeServiceError.CHARGE_NO_VALID_RECEIVABLE);
        }

        if (realReceivableFee.compareTo(frontEndReceivableFee) != 0) {
            throw new ChargeServiceException(ChargeServiceError.CHARGE_FEE_CHANGED);
        }

        CombinedPayItem payItem = new CombinedPayItem();
        if (Objects.nonNull(payInfo.getCombinedPayment()) && !CollectionUtils.isEmpty(payInfo.getCombinedPayment().getCombinedPayItems())) {
            payItem = payInfo.getCombinedPayment().getCombinedPayItems().get(0);
        }

        updateThisTimeChargePayItemInfos(payItem, null, false);

        BigDecimal payAmount = payItem.getAmount();
        BigDecimal cashPayAmount = payItem.getPayMode() == Constants.ChargePayMode.CASH ? payItem.getAmount() : BigDecimal.ZERO;
        BigDecimal needPayFee = realReceivableFee.subtract(payAmount);
        BigDecimal changeFee = needPayFee.compareTo(BigDecimal.ZERO) > 0 ? BigDecimal.ZERO : needPayFee.negate(); //找零

        if (realReceivableFee.compareTo(payAmount) < 0 && (cashPayAmount.compareTo(BigDecimal.ZERO) <= 0 || cashPayAmount.compareTo(changeFee) < 0)) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "charge fee more than receivable, realReceivableFee:{}, payAmount:{}, changeFee:{}, cashPayAmount:{}", realReceivableFee, payAmount, changeFee, cashPayAmount);
            throw new ChargeServiceException(ChargeServiceError.CHARGE_FEE_MORE_THAN_RECEIVABLE);
        }

        //锁单
        ChargeUtils.lockChargeSheetForPay(chargeSheet, Constants.ChargeSource.CHARGE, needPayFee, true, null);
    }

    /**
     * 绑定执行项的数量
     */
    private void updateChargeFormItemCount() {
        // 治疗理疗的执行数量
        updateChargeExecuteItemCount();
        // 检查检验的执行数量
//        updateExaminationItemCount();
    }

    /**
     * 绑定治疗理疗的执行项
     */
    private void updateChargeExecuteItemCount() {
        // 如果没有执行项，就不查了
        if (chargeSheet.getExecuteStatus() == ChargeSheet.ExecuteStatus.NONE) {
            return;
        }
        List<ChargeExecuteItem> chargeExecuteItems = sheetProcessorInfoProvider.getChargeExecuteProvider().findChargeExecuteItemByChargeSheetId(chargeSheet.getId());
        if (CollectionUtils.isEmpty(chargeExecuteItems)) {
            return;
        }
        chargeFormItemExecutedCountMap = chargeExecuteItems.stream()
                .collect(Collectors.toMap(ChargeExecuteItem::getChargeFormItemId, Function.identity(), (a, b) -> a));
        formProcessors.stream()
                .flatMap(formProcessor -> formProcessor.getItemProcessorList().stream())
                .forEach(itemProcessor -> itemProcessor.updateChargeFormItemExecutedCount(chargeFormItemExecutedCountMap));
    }

    /**
     * 校验执行项的数量
     */
    private void checkExecuteItemCountOrThrowException() {
        if (MapUtils.isEmpty(chargeFormItemExecutedCountMap)) {
            return;
        }
        Map<String, ChargeFormItem> chargeFormItemIdMap = ChargeUtils.getChargeSheetItems(chargeSheet)
                .stream()
                .collect(Collectors.toMap(ChargeFormItem::getId, Function.identity(), (a, b) -> a));
        for (Map.Entry<String, ChargeExecuteItem> entry : chargeFormItemExecutedCountMap.entrySet()) {
            String chargeFormItemId = entry.getKey();
            ChargeExecuteItem chargeExecuteItem = entry.getValue();
            BigDecimal executedCount = chargeExecuteItem.getExecutedCount();
            // 治疗理疗才校验
            if (chargeExecuteItem.getProductType() != Constants.ProductType.TREATMENT) {
                continue;
            }
            if (MathUtils.wrapBigDecimalCompare(executedCount, BigDecimal.ZERO) <= 0) {
                continue;
            }
            ChargeFormItem chargeFormItem = chargeFormItemIdMap.get(chargeFormItemId);
            if (Objects.isNull(chargeFormItem)) {
                sLogger.error("chargeFormItem is null, chargeFormItemId: {}", chargeFormItemId);
                String errorMessage = String.format(ChargeServiceError.CHARGE_FORM_ITEM_PAY_COUNT_ERROR.getMessage(), chargeExecuteItem.getName(), executedCount.stripTrailingZeros().toPlainString());
                throw new CisCustomException(ChargeServiceError.CHARGE_FORM_ITEM_PAY_COUNT_ERROR.getCode(), errorMessage);
            }
            if (chargeFormItem.getIsDeleted() == 0 && chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.UNCHARGED) {
                BigDecimal totalCount = MathUtils.calculateTotalCount(chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount());
                if (MathUtils.wrapBigDecimalCompare(totalCount, executedCount) < 0) {
                    sLogger.error("可收的数量不能小于已执行的数量，totalCount: {}, executedCount: {}", totalCount, executedCount);
                    String errorMessage = String.format(ChargeServiceError.CHARGE_FORM_ITEM_PAY_COUNT_ERROR.getMessage(), chargeFormItem.getName(), executedCount.stripTrailingZeros().toPlainString());
                    throw new CisCustomException(ChargeServiceError.CHARGE_FORM_ITEM_PAY_COUNT_ERROR.getCode(), errorMessage);
                }
            } else {
                sLogger.error("item已删除或以退单，isDeleted: {}, status: {}", chargeFormItem.getIsDeleted(), chargeFormItem.getStatus());
                String errorMessage = String.format(ChargeServiceError.CHARGE_FORM_ITEM_PAY_COUNT_ERROR.getMessage(), chargeFormItem.getName(), executedCount.stripTrailingZeros().toPlainString());
                throw new CisCustomException(ChargeServiceError.CHARGE_FORM_ITEM_PAY_COUNT_ERROR.getCode(), errorMessage);
            }
        }
    }

    public void updatePromotionForRegistration(PayInfo payInfo) {
        updatePatientInfo();
        fetchAndSetMemberInfo(false);
        fetchAndApplyPromotionInfo(payInfo.getPromotions(), payInfo.getCouponPromotions(), payInfo.getGiftRulePromotions(), payInfo.getPatientCardPromotions(), payInfo.getPatientPointDeductProductPromotions(), payInfo.getMallVerifications(), payInfo.getPatientPointsInfo(), null, null);
        checkCouponCountAndThrowException();
        doCalculate(BigDecimal.ZERO, CalculateScene.PAY);
    }


    public PayResult pay(PayInfo payInfo, boolean isPayForLeft) {
        doCalculate(BigDecimal.ZERO, isPayForLeft ? CalculateScene.PAY_FOR_LEFT : CalculateScene.PAY);
        BigDecimal frontEndReceivableFee = MathUtils.wrapBigDecimalOrZero(payInfo.getReceivableFee());
        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "[pay] charge fee info, totalFee:{}, discountFee:{}, adjustmentFee:{}, receivableFee:{}, realReceivableFee:{}, receivedFee:{}, frontEndReceivableFee:{}",
                totalFee, discountFee, adjustmentFee, receivableFee, realReceivableFee, _receivedFee, frontEndReceivableFee);
        if (realReceivableFee.compareTo(BigDecimal.ZERO) < 0) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "realReceivableFee <= 0:" + receivableFee);
            throw new ChargeServiceException(ChargeServiceError.CHARGE_NO_VALID_RECEIVABLE);
        }
        if (realReceivableFee.compareTo(frontEndReceivableFee) != 0) {
            throw new ChargeServiceException(ChargeServiceError.CHARGE_FEE_CHANGED);
        }
        // 计算入账金额
        CombinedPayItem payItem = new CombinedPayItem();
        if (Objects.nonNull(payInfo.getCombinedPayment()) && !CollectionUtils.isEmpty(payInfo.getCombinedPayment().getCombinedPayItems())) {
            payItem = payInfo.getCombinedPayment().getCombinedPayItems().get(0);
        }

        BigDecimal needPayFee = payItem.dealChangeFee(realReceivableFee, new ChargeServiceException(ChargeServiceError.CHARGE_FEE_MORE_THAN_RECEIVABLE));
        updateThisTimeChargePayItemInfos(payItem, null, isPayForLeft);
        ChargeAction chargeAction = ChargeUtils.insertChargeAction(null, chargeSheet, ChargeAction.Type.PAY, PayStatus.WAITING, payItem.getNetIncome(), payItem.getPayActionInfo(), payInfo.getChargeComment(), payInfo.getSpecifiedChargedTime(), payInfo.getOperatorId());

        // 这里开始要区分了，直接入账还是三方支付
        boolean needThirdPartyPay = needCallThirdPartyPay(payItem, chargeSheet.getChainId(), chargeSheet.getClinicId());
        if (payItem.getPayMode() == Constants.ChargePayMode.WECHAT_PAY && payItem.getPaySubMode() != Constants.ChargePaySubMode.ONLY_RECORD && !needThirdPartyPay) {
            throw new ChargeServiceException(ChargeServiceError.CHAIN_NOT_OPEN_WECHATPAY);
        }

        int payStatus;
        String thirdPartyPayTaskId = null;
        ThirdPartyPayInfo thirdPartyPayInfo = null;
        ChargeTransaction chargeTransaction = null;
        if (payItem.getNetIncome().compareTo(BigDecimal.ZERO) > 0 && needThirdPartyPay) {
            // todo 在这个方法里启调支付接口
            if (chargeSheet.getExpireTime() != null && Constants.ChargeSource.patientPaySources().contains(payInfo.getPaySource())) {
                if (chargeSheet.getExpireTime().isBefore(Instant.now())) {
                    sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "订单已超过截止支付的时间，不能支付");
                    throw new ChargeServiceException(ChargeServiceError.CHARGE_THIRDPART_PAY_TIMEOUT);
                }
            }

            ChargePayInfo chargePayInfo = new ChargePayInfo();
            chargePayInfo.setExtra(payInfo.getExtra());//微信自助支付，需要在微信支付成功的回调里面修改状态
            chargePayInfo.setChainId(chargeSheet.getChainId());
            chargePayInfo.setClinicId(chargeSheet.getClinicId());
            chargePayInfo.setChargeSheetId(chargeSheet.getId());
            chargePayInfo.setChargeActionId(chargeAction.getId());
            chargePayInfo.setDoctorId(chargeSheet.getDoctorId());
            chargePayInfo.setSellerId(chargeSheet.getSellerId());
            chargePayInfo.setPatientId(chargeSheet.getPatientId());
            chargePayInfo.setPatientOrderId(chargeSheet.getPatientOrderId());
            chargePayInfo.setDiagnosedDate(chargeSheet.getDiagnosedDate());
            chargePayInfo.setPatientOrderNo(patientOrder != null ? (int) patientOrder.getNo() : 0);
            chargePayInfo.setPayMode(payItem.getPayMode());
            chargePayInfo.setThirdPartyPayCardId(payItem.getThirdPartyPayCardId());
            chargePayInfo.setThirdPartyPayCardPassword(payItem.getThirdPartyPayCardPassword());
            chargePayInfo.setThisTimeChargePayItemInfos(thisTimeChargePayItemInfos);
            chargePayInfo.setSheetReceivableFee(realReceivableFee);
            chargePayInfo.setReceivableFee(payItem.getNetIncome());
            chargePayInfo.setPaySource(payInfo.getPaySource());
            String body = chargeSheet.getType() == ChargeSheet.Type.REGISTRATION ? Constants.SystemProductId.REGISTRATION_NAME :
                    chargeSheet.getType() == ChargeSheet.Type.MEMBER_RECHARGE ? "会员充值" :
                            "收费单：" + AbcIdUtils.convertUUidToLongString(chargeSheet.getId());
            chargePayInfo.setBody(body);
            chargePayInfo.setMemberId(memberId);
            chargePayInfo.setOperatorId(operatorId);
            chargePayInfo.setOpenId(payInfo.getOpenId());
            chargePayInfo.setClientIp(payInfo.getClientIp());
            chargePayInfo.setPaySubMode(payItem.getPaySubMode());
            chargePayInfo.setPayModeName(payItem.getPayModeName());
            chargePayInfo.setChargeTransactionId(AbcIdUtils.getUUID());
            chargePayInfo.setChargeComment(payInfo.getChargeComment());
            chargePayInfo.setWechatPaySource(chargeSheet.getType() == ChargeSheet.Type.REGISTRATION ? WeChatPayReq.ServiceSource.CHARGE_FOR_REGISTRATION
                    : chargeSheet.getType() == ChargeSheet.Type.ONLINE_CONSULTATION ? WeChatPayReq.ServiceSource.CHARGE_FOR_ONLINE_CONSULTATION
                    : WeChatPayReq.ServiceSource.CHARGE);
            chargePayInfo.setWechatPayTimeExpire(chargeSheet.getExpireTime() != null ? DateUtils.convertInstantToString(chargeSheet.getExpireTime(), DateUtils.DATE_FORMAT_COMPACT_DATETIME) : null);
            chargePayInfo.setMemberCardPassword(payItem.getMemberCardPassword());
            chargePayInfo.setAppId(payInfo.getAppId());
            chargePayInfo.setSpecifiedChargedTime(payInfo.getSpecifiedChargedTime());
            ChargePayResult chargePayResult = sheetProcessorInfoProvider.getChargePayProvider().pay(chargePayInfo);
            if (chargePayResult != null) {
                payStatus = chargePayResult.getPayStatus();
                thirdPartyPayTaskId = chargePayResult.getThirdPayTaskId();
                thirdPartyPayInfo = chargePayResult.getPayInfo();
                if (payStatus == PayStatus.SUCCESS) {
                    //更新收费项的已收金额
                    updateReceivedPrice(payItem, isPayForLeft);
                    payItem.setAmount(chargePayResult.getReceivedFee());
                    payItem.setPresentAmount(chargePayResult.getReceivedPresent());
                    payItem.setPrincipalAmount(chargePayResult.getReceivedPrincipal());
                    payItem.setChange(BigDecimal.ZERO);
                    payItem.setPaySubMode(chargePayResult.getPaySubMode());
                    needPayFee = realReceivableFee.subtract(MathUtils.wrapBigDecimalOrZero(payItem.getNetIncome()));
                    needPayFee = needPayFee.compareTo(BigDecimal.ZERO) > 0 ? needPayFee : BigDecimal.ZERO;
                    chargeTransaction = writePayTransaction(chargePayInfo.getChargeTransactionId(), payItem, needPayFee, chargePayResult.getPayInfo(), chargeAction, payInfo.getPaySource(), payInfo.getOperatorId(), payInfo.getExtra(), !isPayForLeft);
//                    updateChargePayTransactionCanceled(null, operatorId);
                } else {
                    //第三方支付并且没有支付直接支付成功，锁单
                    lockPatientOrderAndSendAutoCancelMessage(chargePayResult, payInfo.getPaySource());
                }
            } else {
                payStatus = PayStatus.FAILED;
            }
        } else {
            //更新收费项的已收金额
            updateReceivedPrice(payItem, isPayForLeft);
            chargeTransaction = writePayTransaction(null, payItem, needPayFee, null, chargeAction, payInfo.getPaySource(), payInfo.getOperatorId(), payInfo.getExtra(), !isPayForLeft);
//            updateChargePayTransactionCanceled(null, operatorId);
            payStatus = PayStatus.SUCCESS;
        }

        if (!isPayForLeft && getIsNeedUseLimitPrice()
                && MathUtils.wrapBigDecimalOrZero(payItem.getNetIncome()).compareTo(MathUtils.wrapBigDecimalOrZero(this.sheBaoReceivableFee)) > 0) {
            throw new ChargeServiceException(ChargeServiceError.CHARGE_NO_VALID_RECEIVABLE);
        }

        PayResult payResult = new PayResult();
        payResult.setStatus(chargeSheet.getStatus());
        payResult.setCreated(Instant.now());
        payResult.setReceivedFee(_receivedFee);
        payResult.setNetIncomeFee(_receivedFee.add(_refundFee));
        payResult.setNeedPay(needPayFee);
        payResult.setChargeActionId(chargeAction.getId());
        payResult.setChargeTransactionId(chargeTransaction != null ? chargeTransaction.getId() : null);
        payResult.setPayStatus(payStatus);
        payResult.setThirdPartyPayTaskId(thirdPartyPayTaskId);
        payResult.setThirdPartyPayTaskType(thirdPartyPayTaskId != null ? 0 : null);
        payResult.setWeChatPayInfo(thirdPartyPayInfo != null ? thirdPartyPayInfo.getWechatPayInfo() : null);
        return payResult;
    }

    private void dealChargeOwe(ChargeTransaction chargeTransaction) {
        Optional.ofNullable(sheetProcessorInfoProvider).map(SheetProcessorInfoProvider::getChargeOweSheetProvider)
                .ifPresent(chargeOweSheetProvider -> chargeOweSheetProvider.insertOrUpdateChargeOweSheet(chargeSheet, chargeTransaction, operatorId));
    }

    public PayResult payForImport(PayInfo payInfo) {
        updatePatientInfo();
        updateProductInfo(true, false, 0, 1);
        updateVirtualPharmacyChargeFormDeliveryFee(payInfo.getPaySource());
        updateExpressDeliveryFeeAndProcessFee(payInfo.getPaySource());
        //计算空中药房的加工费和快递费并赋值
        updateAirPharmacyChargeFormDeliveryFeeAndProcessFee();
        checkProductInfoOrThrowException(payInfo.getPaySource());
        BigDecimal adjustmentFee = calculateAdjustFee(payInfo.getExpectedAdjustmentFee(),
                payInfo.getExpectedOddFee(),
                MathUtils.wrapBigDecimalOrZero(payInfo.getDraftAdjustmentFee()));

        updateReceivableFee(adjustmentFee);
        doCalculate(adjustmentFee, CalculateScene.PAY);
        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "[payForImport] charge fee info, totalFee:{}, discountFee:{}, adjustmentFee:{}, receivableFee:{}, realReceivableFee:{}, receivedFee:{}",
                totalFee, discountFee, adjustmentFee, receivableFee, realReceivableFee, _receivedFee);
        if (realReceivableFee.compareTo(BigDecimal.ZERO) < 0) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "realReceivableFee <= 0:" + receivableFee);
            throw new ChargeServiceException(ChargeServiceError.CHARGE_NO_VALID_RECEIVABLE);
        }

        CombinedPayItem payItem = new CombinedPayItem();
        if (Objects.nonNull(payInfo.getCombinedPayment()) && !CollectionUtils.isEmpty(payInfo.getCombinedPayment().getCombinedPayItems())) {
            payItem = payInfo.getCombinedPayment().getCombinedPayItems().get(0);
        }
        payItem.setAmount(realReceivableFee);

        updateThisTimeChargePayItemInfos(payItem, null, false);

        BigDecimal payAmount = payItem.getAmount();
        BigDecimal cashPayAmount = payItem.getPayMode() == Constants.ChargePayMode.CASH ? payItem.getAmount() : BigDecimal.ZERO;
        BigDecimal needPayFee = realReceivableFee.subtract(payAmount);
        BigDecimal changeFee = needPayFee.compareTo(BigDecimal.ZERO) > 0 ? BigDecimal.ZERO : needPayFee.negate(); //找零
        needPayFee = needPayFee.compareTo(BigDecimal.ZERO) > 0 ? needPayFee : BigDecimal.ZERO;

        if (realReceivableFee.compareTo(payAmount) < 0 && (cashPayAmount.compareTo(BigDecimal.ZERO) <= 0 || cashPayAmount.compareTo(changeFee) < 0)) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "charge fee more than receivable, realReceivableFee:{}, payAmount:{}, changeFee:{}, cashPayAmount:{}", realReceivableFee, payAmount, changeFee, cashPayAmount);
            throw new ChargeServiceException(ChargeServiceError.CHARGE_FEE_MORE_THAN_RECEIVABLE);
        }

        payItem.setChange(changeFee);

        applyAdjustment(adjustmentFee);
        applyChargeFormItemPromotionInfoUnitAdjustmentFee();
        updateReceivedPrice(payItem, false);
        if (adjustmentFee.compareTo(BigDecimal.ZERO) > 0) {
            //兼容历史遗留单据 新加一个type
            insertChargeAdditionFee(ChargeAdditionalFee.AdditionalFeeType.ADJUSTMENT_ADD_FEE, adjustmentFee, false, payInfo.getOperatorId());
        } else if (adjustmentFee.compareTo(BigDecimal.ZERO) < 0) {
            insertChargeAdditionFee(ChargeAdditionalFee.AdditionalFeeType.ADJUSTMENT_DISCOUNT_FEE, adjustmentFee, false, payInfo.getOperatorId());
        }

        if (!CollectionUtils.isEmpty(availablePromotionViews) || adjustmentFee.compareTo(BigDecimal.ZERO) != 0) {
            SheetPromotionInfo sheetPromotionInfo = new SheetPromotionInfo();
            sheetPromotionInfo.setPromotions(availablePromotionViews == null ? new ArrayList<>() : availablePromotionViews);
            sheetPromotionInfo.setAdjustmentFee(adjustmentFee);
            chargeSheet.setPromotionInfo(sheetPromotionInfo);
            chargeSheet.setPromotionInfoJson(JsonUtils.dump(sheetPromotionInfo));
        }
        chargeSheet.setIsDraft(0);

        ChargeAction chargeAction = ChargeUtils.insertChargeAction(null, chargeSheet, ChargeAction.Type.PAY, PayStatus.SUCCESS, payItem.getNetIncome(), payItem.getPayActionInfo(), payInfo.getChargeComment(), null, payInfo.getOperatorId());
        ChargeTransaction chargeTransaction = writePayTransaction(null, payItem, needPayFee, null, chargeAction, payInfo.getPaySource(), payInfo.getOperatorId(), payInfo.getExtra(), true);
        ChargeUtils.unlockChargeSheetV2(chargeSheet, 0, true);
        int payStatus = PayStatus.SUCCESS;
        chargeSheet.setChargedBy(operatorId);

        PayResult payResult = new PayResult();
        payResult.setChargePayTransactionId(null);
        payResult.setChargeTransactionId(chargeTransaction.getId());
        payResult.setStatus(chargeSheet.getStatus());
        payResult.setCreated(Instant.now());
        payResult.setReceivedFee(_receivedFee);
        payResult.setNetIncomeFee(_receivedFee.add(_refundFee));
        payResult.setNeedPay(needPayFee);
        payResult.setChargeActionId(chargeAction != null ? chargeAction.getId() : null);
        payResult.setPayStatus(payStatus);

        return payResult;
    }

    public PayResult payWithOwePayMode(PayInfo payInfo) {
        updatePatientInfo();
        updateProductInfo(true, false, 0, 1);
        fetchAndSetMemberInfo(true);
        /**
         * 更新虚拟药房的快递费
         */
        updateVirtualPharmacyChargeFormDeliveryFee(payInfo.getPaySource());
        updateExpressDeliveryFeeAndProcessFee(payInfo.getPaySource());
        //计算空中药房的加工费和快递费并赋值
        updateAirPharmacyChargeFormDeliveryFeeAndProcessFee();
        BigDecimal adjustmentFee = calculateAdjustFee(payInfo.getExpectedAdjustmentFee(),
                payInfo.getExpectedOddFee(),
                MathUtils.wrapBigDecimalOrZero(payInfo.getDraftAdjustmentFee()));

        updateReceivableFee(adjustmentFee);

        doCalculate(adjustmentFee, CalculateScene.PAY);

        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "[payWithOutPromotion] charge fee info, totalFee:{}, discountFee:{}, adjustmentFee:{}, receivableFee:{}, realReceivableFee:{}, receivedFee:{}",
                totalFee, discountFee, adjustmentFee, receivableFee, realReceivableFee, _receivedFee);

        if (realReceivableFee.compareTo(BigDecimal.ZERO) < 0) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "realReceivableFee <= 0:" + receivableFee);
            throw new ChargeServiceException(ChargeServiceError.CHARGE_NO_VALID_RECEIVABLE);
        }

        CombinedPayItem payItem = new CombinedPayItem();
        if (Objects.nonNull(payInfo.getCombinedPayment()) && !CollectionUtils.isEmpty(payInfo.getCombinedPayment().getCombinedPayItems())) {
            payItem = payInfo.getCombinedPayment().getCombinedPayItems().get(0);
        }
        payItem.setAmount(realReceivableFee);
        updateThisTimeChargePayItemInfos(payItem, null, false);

        BigDecimal payAmount = payItem.getAmount();
        BigDecimal cashPayAmount = payItem.getPayMode() == Constants.ChargePayMode.CASH ? payItem.getAmount() : BigDecimal.ZERO;
        BigDecimal needPayFee = realReceivableFee.subtract(payAmount);
        BigDecimal changeFee = needPayFee.compareTo(BigDecimal.ZERO) > 0 ? BigDecimal.ZERO : needPayFee.negate(); //找零
        needPayFee = needPayFee.compareTo(BigDecimal.ZERO) > 0 ? needPayFee : BigDecimal.ZERO;

        if (realReceivableFee.compareTo(payAmount) < 0 && (cashPayAmount.compareTo(BigDecimal.ZERO) <= 0 || cashPayAmount.compareTo(changeFee) < 0)) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "charge fee more than receivable, realReceivableFee:{}, payAmount:{}, changeFee:{}, cashPayAmount:{}", realReceivableFee, payAmount, changeFee, cashPayAmount);
            throw new ChargeServiceException(ChargeServiceError.CHARGE_FEE_MORE_THAN_RECEIVABLE);
        }
        payItem.setChange(changeFee);

        applyAdjustment(adjustmentFee);
        applyChargeFormItemPromotionInfoUnitAdjustmentFee();
        updateReceivedPrice(payItem, false);
        if (adjustmentFee.compareTo(BigDecimal.ZERO) > 0) {
            //兼容历史遗留单据 新加一个type
            insertChargeAdditionFee(ChargeAdditionalFee.AdditionalFeeType.ADJUSTMENT_ADD_FEE, adjustmentFee, false, payInfo.getOperatorId());
        } else if (adjustmentFee.compareTo(BigDecimal.ZERO) < 0) {
            insertChargeAdditionFee(ChargeAdditionalFee.AdditionalFeeType.ADJUSTMENT_DISCOUNT_FEE, adjustmentFee, false, payInfo.getOperatorId());
        }

        if (!CollectionUtils.isEmpty(availablePromotionViews) || adjustmentFee.compareTo(BigDecimal.ZERO) != 0) {
            SheetPromotionInfo sheetPromotionInfo = new SheetPromotionInfo();
            sheetPromotionInfo.setPromotions(availablePromotionViews == null ? new ArrayList<>() : availablePromotionViews);
            sheetPromotionInfo.setAdjustmentFee(adjustmentFee);
            chargeSheet.setPromotionInfo(sheetPromotionInfo);
            chargeSheet.setPromotionInfoJson(JsonUtils.dump(sheetPromotionInfo));
        }
        chargeSheet.setIsDraft(0);

        if (chargeSheet.getOwedStatus() != ChargeSheet.OwedStatus.OWING) {
            chargeSheet.setOwedStatus(ChargeSheet.OwedStatus.OWING);
        }

        ChargeAction chargeAction = ChargeUtils.insertChargeAction(null, chargeSheet, ChargeAction.Type.PAY, PayStatus.SUCCESS, payItem.getNetIncome(), payItem.getPayActionInfo(), payInfo.getChargeComment(), payInfo.getSpecifiedChargedTime(), payInfo.getOperatorId());
        ChargeTransaction chargeTransaction = writePayTransaction(null, payItem, needPayFee, null, chargeAction, payInfo.getPaySource(), payInfo.getOperatorId(), payInfo.getExtra(), true);
        ChargeUtils.unlockChargeSheetV2(chargeSheet, 0, true);
        int payStatus = PayStatus.SUCCESS;
        chargeSheet.setChargedBy(operatorId);

        PayResult payResult = new PayResult();
        payResult.setChargePayTransactionId(null);
        payResult.setChargeTransactionId(chargeTransaction.getId());
        payResult.setStatus(chargeSheet.getStatus());
        payResult.setCreated(Instant.now());
        payResult.setReceivedFee(_receivedFee);
        payResult.setNetIncomeFee(_receivedFee.add(_refundFee));
        payResult.setNeedPay(needPayFee);
        payResult.setChargeActionId(chargeAction != null ? chargeAction.getId() : null);
        payResult.setPayStatus(payStatus);

        return payResult;
    }

    private void updateReceivedPrice(CombinedPayItem payItem, boolean isPayForLeft) {
        List<ChargePayItemInfo> chargePayItemInfos = new ArrayList<>(thisTimeChargePayItemInfos);
        //如果是首次收费，则将额外的抵扣也算到本次收费上
        if (payItem.getPayMode() == Constants.ChargePayMode.PROMOTION_CARD && !isPayForLeft) {
            List<String> existedChargeFormItemIds = thisTimeChargePayItemInfos.stream()
                    .filter(chargePayItemInfo -> chargePayItemInfo.getPharmacyType() == GoodsConst.PharmacyType.VIRTUAL_PHARMACY
                            || chargePayItemInfo.getPharmacyType() == GoodsConst.PharmacyType.LOCAL_PHARMACY)
                    .map(ChargePayItemInfo::getId).distinct().collect(Collectors.toList());

            List<ChargePayItemInfo> deductedChargePayItemInfos = formProcessors.stream()
                    .filter(formProcessor -> !formProcessor.isAirPharmacy())
                    .flatMap(formProcessor -> formProcessor.getItemProcessorMap().values().stream())
                    .filter(itemProcessor -> !existedChargeFormItemIds.contains(itemProcessor.getItemId()))
                    .filter(itemProcessor -> MathUtils.wrapBigDecimalCompare(itemProcessor.getChargeFormItem().getDeductTotalCount(), BigDecimal.ZERO) > 0)
                    .map(itemProcessor -> itemProcessor.generatePayItem(false))
                    .filter(Objects::nonNull)
                    .peek(chargePayItemInfo -> chargePayItemInfo.setThisTimeReceivableFee(BigDecimal.ZERO))
                    .collect(Collectors.toList());

            chargePayItemInfos.addAll(deductedChargePayItemInfos);
            sLogger.info("首次收费并且是卡项支付，把抵扣的项目也增加的入账列表中，chargePayItemInfos: {}", JsonUtils.dump(chargePayItemInfos));
        }
        Map<String, ChargePayItemInfo> chargePayItemInfoMap = ListUtils.toMap(chargePayItemInfos, ChargePayItemInfo::getId);
        //更新已收金额
        formProcessors.forEach(formProcessor -> formProcessor.updateReceivedPrice(chargePayItemInfoMap, thisTimeHealthCardAffectedIds));
    }


    private void updateThisTimeChargePayItemInfos(CombinedPayItem payItem, List<PayCallbackReq.ChargeFormItem> thisTimeReceivedChargeFormItems, boolean isPayForLeft) {
        bindThisTimeChargePayItemInfos(payItem, thisTimeReceivedChargeFormItems, isPayForLeft);
        payItem.bindPayActionInfo(Optional.ofNullable(thisTimePatientCardView).map(PatientCardView::getName).orElse(""),
                sheetProcessorInfoProvider.getChargePayModeProvider().getChargePayModeConfigSimpleByChainId(chargeSheet.getChainId()));
    }

    private void bindThisTimeChargePayItemInfos(CombinedPayItem payItem, List<PayCallbackReq.ChargeFormItem> thisTimeReceivedChargeFormItems, boolean isPayForLeft) {
        //收集本次支付的收费项列表及每个收费项应收多少钱
        thisTimeChargePayItemInfos = collectThisTimePayItems(payItem, thisTimeReceivedChargeFormItems, isPayForLeft);
        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "thisTimeChargePayItemInfos: {}", JsonUtils.dump(thisTimeChargePayItemInfos));
    }

    /**
     * 收集本次支付的收费项列表及每个收费项应收多少钱
     *
     * @param payItem
     * @param isPayForLeft 是否为部分支付
     */
    private List<ChargePayItemInfo> collectThisTimePayItems(CombinedPayItem payItem, List<PayCallbackReq.ChargeFormItem> thisTimeReceivedChargeFormItems, boolean isPayForLeft) {
        boolean isShebaoPayMode = payItem.getPayMode() == Constants.ChargePayMode.HEALTH_CARD || payItem.getPayMode() == Constants.ChargePayMode.OUTPATIENT_CENTER_PAY || payItem.getPayMode() == Constants.ChargePayMode.SHEBAO_QINGDAO_UNION_POS_PAY;
        List<ChargePayItemInfo> chargePayItemInfos = formProcessors.stream()
                .map(formProcessor -> formProcessor.generatePayItems(isShebaoPayMode))
                .filter(org.apache.commons.collections.CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        if (payItem.getPayMode() == Constants.ChargePayMode.PROMOTION_CARD) {
            return collectThisTimePayItemsForPatientCard(chargePayItemInfos, payItem, isPayForLeft);
        } else if (isShebaoPayMode) {
            //这里根据支付方式和是否为部分支付取对thisTimeReceivedChargeFormItems进行清空，如果为部分支付且不是青岛银联POS支付，将thisTimeReceivedChargeFormItems设置为空
            if (isPayForLeft && payItem.getPayMode() != Constants.ChargePayMode.SHEBAO_QINGDAO_UNION_POS_PAY) {
                thisTimeReceivedChargeFormItems = new ArrayList<>();
            }
            return collectThisTimePayItemsForHealthCard(chargePayItemInfos, payItem, thisTimeReceivedChargeFormItems);
        }
        return collectThisTimePayItemsForNormal(chargePayItemInfos, payItem.getNetIncome());
    }

    private List<ChargePayItemInfo> collectThisTimePayItemsForHealthCard(List<ChargePayItemInfo> chargePayItemInfos, CombinedPayItem payItem, List<PayCallbackReq.ChargeFormItem> thisTimeReceivedChargeFormItems) {

        if (CollectionUtils.isEmpty(thisTimeReceivedChargeFormItems)) {
            //为空就返回所有子项
            return collectThisTimePayItemsForNormal(chargePayItemInfos, payItem.getNetIncome());
        }
        //ChargePayItemInfo的id，可能为空中药房chargeForm的id，可能为item本身的id，可能为套餐子项的id
        Set<String> thisTimeReceivedChargeFormItemIds = thisTimeReceivedChargeFormItems.stream().map(PayCallbackReq.ChargeFormItem::getId).collect(Collectors.toSet());
        //最终可以入账的chargePayItemInfos
        chargePayItemInfos = chargePayItemInfos.stream()
                .map(chargePayItemInfo -> filterAffectedChargePayItemInfo(chargePayItemInfo, thisTimeReceivedChargeFormItemIds))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        //比较可以入账的所有应收金额是否入账
        BigDecimal totalReceivableFee = chargePayItemInfos.stream()
                .map(ChargePayItemInfo::getReceivableFee)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal thisTimeAmount = payItem.getAmount();
        if (MathUtils.wrapBigDecimalCompare(totalReceivableFee, thisTimeAmount) < 0) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "本次入账的item的总应收小于本次入账的金额， totalReceivableFee: {}, thisTimeAmount: {}, chargePayItemInfos: {}", totalReceivableFee, thisTimeAmount, JsonUtils.dump(chargePayItemInfos));
            throw new ChargeServiceException(ChargeServiceError.CHARGE_FEE_MORE_THAN_RECEIVABLE);
        }

        //将本次医保入账的id收集出来
        thisTimeHealthCardAffectedIds.addAll(chargePayItemInfos.stream()
                .flatMap(chargePayItemInfo -> chargePayItemInfo.getAffectedIds().stream())
                .filter(Objects::nonNull)
                .collect(Collectors.toList()));
        if (MathUtils.wrapBigDecimalCompare(totalReceivableFee, thisTimeAmount) == 0) {
            //等于0表示入账金额刚刚好，直接返回
            return chargePayItemInfos;
        }
        //大于0表示本次入账金额需要均摊到每项上
        ChargePayItemInfo.flatChargePayItemInfos(chargePayItemInfos, thisTimeAmount);

        return chargePayItemInfos;
    }

    public static ChargePayItemInfo filterAffectedChargePayItemInfo(ChargePayItemInfo chargePayItemInfo, Set<String> thisTimeReceivedChargeFormItemIds) {
        // 套餐和费用母项处理或者空中药房
        if (chargePayItemInfo.isParentItem()) {
            if (thisTimeReceivedChargeFormItemIds.contains(chargePayItemInfo.getId())) {
                return chargePayItemInfo;
            }
            // 看子项是否有在本次支付的列表中
            List<ChargePayItemInfo> childItemInfos = Optional.ofNullable(chargePayItemInfo.getChildren()).orElse(new ArrayList<>())
                    .stream()
                    .map(childItemInfo -> filterAffectedChargePayItemInfo(childItemInfo, thisTimeReceivedChargeFormItemIds))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(childItemInfos)) {
                return null;
            }
            chargePayItemInfo.setChildren(childItemInfos);
            chargePayItemInfo.setReceivableFee(childItemInfos.stream().map(ChargePayItemInfo::getReceivableFee).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            chargePayItemInfo.setThisTimeReceivableFee(childItemInfos.stream().map(ChargePayItemInfo::getThisTimeReceivableFee).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            return chargePayItemInfo;
        }
        if (thisTimeReceivedChargeFormItemIds.contains(chargePayItemInfo.getId())) {
            return chargePayItemInfo;
        }
        return null;
    }

    private List<ChargePayItemInfo> collectThisTimePayItemsForNormal(List<ChargePayItemInfo> chargePayItemInfos, BigDecimal thisTimeAmount) {
        BigDecimal totalReceivableFee;
        if (chargeSheet.getType() == ChargeSheet.Type.MEMBER_RECHARGE) {
            totalReceivableFee = chargeSheet.getTotalFee();
        } else {
            totalReceivableFee = chargePayItemInfos.stream().map(ChargePayItemInfo::getReceivableFee).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        if (MathUtils.wrapBigDecimalCompare(totalReceivableFee, thisTimeAmount) < 0) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "totalReceivableFee小于本次支付的金额，totalReceivableFee: {}, thisTimeAmount: {}, chargePayItemInfos: {}", totalReceivableFee, thisTimeAmount, JsonUtils.dump(chargePayItemInfos));
            throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_CHANGED);
        }
        if (MathUtils.wrapBigDecimalCompare(totalReceivableFee, thisTimeAmount) == 0) {
            return chargePayItemInfos;
        }
        // 大于0表示本次入账金额需要均摊到每项上
        ChargePayItemInfo.flatChargePayItemInfos(chargePayItemInfos, thisTimeAmount);
        return chargePayItemInfos;
    }

    private List<ChargePayItemInfo> collectThisTimePayItemsForPatientCard(List<ChargePayItemInfo> chargePayItemInfos, CombinedPayItem payItem, boolean isPayForLeft) {
        BigDecimal thisTimeAmount = payItem.getAmount();
        List<PatientCardView> availablePatientCards = isPayForLeft ? findCanPaidPatientCards() :
                findAvailablePatientCards(Optional.ofNullable(availablePatientCardPromotionViews).orElse(new ArrayList<>())
                        .stream()
                        .filter(patientCardPromotionView -> !patientCardPromotionView.getChecked())
                        .map(PatientCardPromotionView::getId)
                        .distinct()
                        .collect(Collectors.toList())
                );

        bindThisTimePatientCardView(payItem, availablePatientCards);
        Map<String, PatientCardView.PromotionCardGoodsItem> promotionCardGoodsItemMap = ListUtils.toMap(thisTimePatientCardView.getProductItems(), PatientCardView.PromotionCardGoodsItem::getId);

        // 本次可以支付的收费项再与本次收费的金额进行比较
        if (MathUtils.wrapBigDecimalCompare(thisTimePatientCardView.getAvailableBalance(), thisTimeAmount) == 0) {
            // 说明该卡是要刷完的，直接用thisTimePatientCardView的值作为本次收费的金额
            return chargePayItemInfos.stream()
                    .filter(chargePayItemInfo -> promotionCardGoodsItemMap.containsKey(chargePayItemInfo.getId()))
                    .peek(chargePayItemInfo -> {
                        PatientCardView.PromotionCardGoodsItem cardGoodsItem = promotionCardGoodsItemMap.get(chargePayItemInfo.getId());
                        chargePayItemInfo.updateThisTimeReceivableFee(cardGoodsItem.getReceivablePrice());
                    }).collect(Collectors.toList());
        }

        // 走到这儿表示刷卡是部分支付，需要再进行摊费处理
        List<FlatReceivedPriceHelper.FlatCell> flatPriceCells = thisTimePatientCardView.getProductItems().stream()
                .filter(cardGoodsItem -> MathUtils.compareZero(cardGoodsItem.getReceivablePrice()) > 0)
                .map(cardGoodsItem -> {
                    FlatReceivedPriceHelper.FlatCell flatPriceCell = new FlatReceivedPriceHelper.FlatCell();
                    flatPriceCell.setId(cardGoodsItem.getId());
                    flatPriceCell.setName(cardGoodsItem.getGoodsName());
                    flatPriceCell.setTotalPrice(cardGoodsItem.getReceivablePrice());
                    flatPriceCell.setMaxFlatPrice(cardGoodsItem.getReceivablePrice());
                    return flatPriceCell;
                }).collect(Collectors.toList());
        FlatReceivedPriceHelper flatPriceHelper = new FlatReceivedPriceHelper(thisTimeAmount);
        flatPriceHelper.flat(flatPriceCells);

        Map<String, FlatReceivedPriceHelper.FlatCell> flatPriceCellMap = ListUtils.toMap(flatPriceCells, FlatReceivedPriceHelper.FlatCell::getId);

        return chargePayItemInfos.stream()
                .filter(chargePayItemInfo -> flatPriceCellMap.containsKey(chargePayItemInfo.getId()))
                .peek(chargePayItemInfo -> {
                    FlatReceivedPriceHelper.FlatCell flatPriceCell = flatPriceCellMap.get(chargePayItemInfo.getId());
                    if (flatPriceCell != null) {
                        chargePayItemInfo.updateThisTimeReceivableFee(flatPriceCell.getFlatPrice());
                    }
                }).collect(Collectors.toList());
    }

    //本次卡可以支付的收费项
    private void bindThisTimePatientCardView(CombinedPayItem payItem, List<PatientCardView> availablePatientCards) {
        Map<String, PatientCardView> patientCardViewMap = ListUtils.toMap(Optional.ofNullable(availablePatientCards).orElse(new ArrayList<>()), PatientCardView::getId);
        if (StringUtils.isBlank(payItem.getThirdPartyPayCardId())) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "卡项支付时，卡项id不能为空");
            throw new ChargeServiceException(ChargeServiceError.CHARGE_NEED_PATIENT_CARD_ID);
        }
        PatientCardView patientCardView = patientCardViewMap.getOrDefault(payItem.getThirdPartyPayCardId(), null);
        if (patientCardView == null) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "卡项支付时，卡项id错误");
            throw new ChargeServiceException(ChargeServiceError.CHARGE_PATIENT_CARD_ERROR);
        }
        // 比较卡项的余额是否够扣
        if (patientCardView.getCardBalance().compareTo(payItem.getAmount()) < 0) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "卡项余额不足， 卡余额: {}， 前端传的支付金额: {}", patientCardView.getCardBalance(), payItem.getAmount());
            throw new ChargeServiceException(ChargeServiceError.CHARGE_AMOUNT_OVERFLOW_PATIENT_CARD_BALANCE);
        }
        if (patientCardView.getAvailableBalance().compareTo(payItem.getAmount()) < 0) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "卡项可以支付的金额小于前端传的支付金额， 可以支付的余额: {}， 前端传的支付金额: {}", patientCardView.getAvailableBalance(), payItem.getAmount());
            throw new ChargeServiceException(ChargeServiceError.CHARGE_AMOUNT_OVERFLOW_PATIENT_CARD_BALANCE);
        }
        thisTimePatientCardView = patientCardView;
    }

    public ChargeTransaction payCallback(CombinedPayItem payItem,
                                         ThirdPartyPayInfo thirdPartyPayInfo,
                                         int paySource,
                                         ChargeAction chargeAction,
                                         String operatorId,
                                         String payInfoExtra,
                                         boolean isFirstPay,
                                         List<PayCallbackReq.ChargeFormItem> thisTimeReceivedChargeFormItems) {
        doCalculate(BigDecimal.ZERO, CalculateScene.PAY_FOR_LEFT);
        BigDecimal payAmount = payItem.getAmount();
        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "[payCallback] charge fee info, totalFee:{}, discountFee:{}, adjustmentFee:{}, receivableFee:{}, realReceivableFee:{}, receivedFee:{}, frontEndReceivableFee:{}",
                totalFee, discountFee, adjustmentFee, receivableFee, realReceivableFee, _receivedFee, payAmount);
        if (realReceivableFee.compareTo(BigDecimal.ZERO) < 0) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "realReceivableFee <= 0:" + receivableFee);
            throw new ChargeServiceException(ChargeServiceError.CHARGE_NO_VALID_RECEIVABLE);
        }
        BigDecimal needPayFee = realReceivableFee.subtract(payAmount);
        bindThisTimeChargePayItemInfos(payItem, thisTimeReceivedChargeFormItems, !isFirstPay);
        updateReceivedPrice(payItem, !isFirstPay);
        if (realReceivableFee.compareTo(payAmount) < 0) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "charge fee more than receivable, realReceivableFee:{}, payAmount:{}", realReceivableFee, payAmount);
            throw new ChargeServiceException(ChargeServiceError.CHARGE_FEE_MORE_THAN_RECEIVABLE);
        }
        return writePayTransaction(null, payItem, needPayFee, thirdPartyPayInfo, chargeAction, paySource, operatorId, payInfoExtra, isFirstPay);
    }

//    private void updateChargePayTransactionCanceled(String chargePayTransactionId, String operatorId) {
//        if (sheetProcessorInfoProvider.getChargePayProvider() == null) {
//            return;
//        }
//        sheetProcessorInfoProvider.getChargePayProvider().updateChargePayTransactionCanceled(chargeSheet.getId(), chargeSheet.getClinicId(), chargePayTransactionId, operatorId);
//    }

    private boolean needCallThirdPartyPay(CombinedPayItem payItem, String chainId, String clinicId) throws ServiceInternalException {
        return sheetProcessorInfoProvider.getChargePayProvider().isThirdPartPayEnable(payItem, chainId, clinicId);
    }

    private ChargeTransaction writeRefundTransaction(String chargeTransactionId, CombinedPayItem payItem, BigDecimal adjustmentFeeOnRefund, BigDecimal thisTimeOwedRefundFee,
                                                     ThirdPartyPayInfo thirdPartyPayInfo, ChargeAction chargeAction, String associatePayTransactionId,
                                                     String associateOweCombinePayTransactionRecordId, int paySource, boolean isRefundRegistration, int refundFlatType, List<ChargeTransactionRecord> addedRecords, String operatorId) {
        if (payItem.getNetIncome().compareTo(BigDecimal.ZERO) > 0) {
            return null;
        }
        List<ChargeTransaction> chargeTransactions = new ArrayList<>();
//        if (payItem.getNetIncome().compareTo(BigDecimal.ZERO) != 0) {
        ChargeTransaction chargeTransaction = ChargeUtils.insertChargeTransaction(chargeTransactionId, chargeSheet, payItem.getPayMode(), payItem.getPaySubMode(), payItem.getNetIncome(),
                payItem.getPrincipalAmount(), payItem.getPresentAmount(), payItem.getNetIncome(), BigDecimal.ZERO, BigDecimal.ZERO, paySource, thirdPartyPayInfo,
                chargeAction, operatorId);
        chargeTransactions.add(chargeTransaction);
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(associateOweCombinePayTransactionRecordId)) {
            chargeTransaction.setAssociateTransactionId(associateOweCombinePayTransactionRecordId);
        } else {
            ChargeTransaction associateChargeTransaction = ChargeUtils.findByChargePayTransactionId(chargeSheet, associatePayTransactionId);
            if (associateChargeTransaction != null) {
                chargeTransaction.setAssociateTransactionId(associateChargeTransaction.getId());
                associateChargeTransaction.setRefundedAmount(MathUtils.wrapBigDecimalAdd(associateChargeTransaction.getRefundedAmount(), chargeTransaction.getAmount()));
                if (payItem.getPayMode() == Constants.ChargePayMode.HEALTH_CARD) {
                    associateChargeTransaction.setAssociateTransactionId(chargeTransaction.getId());
                }
            }
        }
//        }
        chargeAction.setPayStatus(PayStatus.SUCCESS);
        List<ChargeAdditionalFee> chargeAdditionalFees = new ArrayList<>();
        // 记录流水
        if (adjustmentFeeOnRefund != null && adjustmentFeeOnRefund.compareTo(BigDecimal.ZERO) != 0) {
            ChargeAdditionalFee adjustmentFee = insertChargeAdditionFee(ChargeAdditionalFee.AdditionalFeeType.ADJUSTMENT_FEE, adjustmentFeeOnRefund, true, operatorId);
            chargeAdditionalFees.add(adjustmentFee);
        }
        if (thisTimeOwedRefundFee != null && thisTimeOwedRefundFee.compareTo(BigDecimal.ZERO) != 0) {
            ChargeAdditionalFee owedRefundFee = insertChargeAdditionFee(ChargeAdditionalFee.AdditionalFeeType.OWED_REFUND_FEE, thisTimeOwedRefundFee.abs(), thisTimeOwedRefundFee.compareTo(BigDecimal.ZERO) < 0, operatorId);
            chargeAdditionalFees.add(owedRefundFee);
        }
        _refundFee = _refundFee.add(payItem.getNetIncome());
        // update status
        formProcessors.forEach(formProcessor -> formProcessor.refund(operatorId));

        long chargedFormCount = formProcessors.stream()
                .filter(formProcessor -> formProcessor.getSourceFormType() != Constants.SourceFormType.GIFT_PRODUCT)
                .filter(formProcessor -> formProcessor.getStatus() == Constants.ChargeFormStatus.CHARGED).count();
        if (!isRefundRegistration || chargeSheet.getType() == ChargeSheet.Type.REGISTRATION) {
            if (_receivedFee.compareTo(_refundFee.negate()) == 0 && chargedFormCount == 0) {
                //处理赠品退款
                updateRefundInfoForGiftRule(operatorId);
                formProcessors.stream()
                        .filter(formProcessor -> formProcessor.getSourceFormType() == Constants.SourceFormType.GIFT_PRODUCT)
                        .forEach(formProcessor -> formProcessor.refundForGiftRule(operatorId));

                chargeSheet.setStatus(Constants.ChargeSheetStatus.REFUNDED);
            } else {
                chargeSheet.setStatus(Constants.ChargeSheetStatus.PART_REFUNDED);
            }
        } else {
            // 退挂号费
            // TODO FIXME 这里基本上没用了，除非一些老数据走微信支付退号
            if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_REFUNDED || chargeSheet.getStatus() == Constants.ChargeSheetStatus.CHARGED) {
                if (_receivedFee.compareTo(_refundFee.negate()) == 0 && chargedFormCount == 0) {
                    chargeSheet.setStatus(Constants.ChargeSheetStatus.REFUNDED);
                }
            }
        }
        // 退费时清空已上传的告知书
        Optional.ofNullable(chargeSheet.getAdditional()).ifPresent(additional -> additional.setNotificationInfos(null));
        // 处理欠费逻辑
        dealChargeOwe(chargeTransaction);
        // 套餐母项
        List<ChargeFormItem> parentChargeFormItems = formProcessors.stream().flatMap(formProcessor -> formProcessor.getChargeFormItems().stream())
                .filter(Objects::nonNull)
                .filter(chargeFormItem -> chargeFormItem.getComposeType() == ComposeType.COMPOSE)
                .collect(Collectors.toList());
        // 所有费用母项，包含收费和退费的
        parentChargeFormItems.addAll(formProcessors.stream()
                .filter(formProcessor -> formProcessor.getSourceFormType() != Constants.SourceFormType.AIR_PHARMACY)
                .flatMap(formProcessor -> formProcessor.getItemProcessorList().stream())
                .flatMap(itemProcessor -> {
                    List<ChargeFormItem> chargeFormItems = itemProcessor.getAllChargeFormItems();
                    chargeFormItems.addAll(itemProcessor.getRefundChargeFormItems());
                    return chargeFormItems.stream();
                })
                .filter(chargeFormItem -> chargeFormItem.getGoodsFeeType() == GoodsFeeType.FEE_PARENT)
                .collect(Collectors.toList()));
        // 统计
        statRecordProcessor.setChargeInfo(thisTimeOwedRefundFee.compareTo(BigDecimal.ZERO) <= 0 ? StatRecordProcessor.PayType.REFUND : StatRecordProcessor.PayType.PARTED_REFUND,
                chargeSheet.getTransactionRecordHandleMode(),
                refundFlatType,
                queryIsHospital(),
                chargeTransactions,
                formProcessors.stream()
                        .filter(formProcessor -> formProcessor.getSourceFormType() != Constants.SourceFormType.AIR_PHARMACY)
                        .flatMap(formProcessor -> formProcessor.getAffectedChargeFormItems().stream())
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()),
                formProcessors.stream()
                        .filter(formProcessor -> formProcessor.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY)
                        .map(FormProcessor::getAffectedForm)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()),
                formProcessors.stream().flatMap(formProcessor -> formProcessor.getStatRecordAffectedDeductedItems().stream()).filter(Objects::nonNull).collect(Collectors.toList()),
                formProcessors.stream().flatMap(formProcessor -> formProcessor.getRefundedChargeFormItems().stream().filter(Objects::nonNull)).collect(Collectors.toList()),
                formProcessors.stream()
                        .filter(formProcessor -> formProcessor.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY)
                        .filter(formProcessor -> formProcessor.getStatus() == Constants.ChargeFormStatus.REFUNDED)
                        .map(FormProcessor::getChargeForm)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()),
                parentChargeFormItems,
                generateStatRefundChildrenChargeFormItemsMap());
        // 退费的records
        refundChargeStatRecordResult = generateChargeTransactionRecords(addedRecords, new ArrayList<>());
        // 基于退费的records修改收费项的已收金额
        updateReceivedFeeForRefund(chargeSheet.getStatus() == Constants.ChargeSheetStatus.REFUNDED, Optional.ofNullable(refundChargeStatRecordResult).map(StatRecordResult::getAddedRecords).orElse(new ArrayList<>()));
        return chargeTransaction;
    }

    public Map<String, List<ChargeFormItem>> generateStatRefundChildrenChargeFormItemsMap() {
        // 1、收集套餐母项和子项关系，母项为收费的母项，子项为收费的子项和退费的子项
        Map<String, List<ChargeFormItem>> composeChildrenChargeFormItemsMap = generateStatComposeChildrenChargeFormItemsMap();
        // 2、收集当前所有的退费的费用母项与费用子项的关系
        composeChildrenChargeFormItemsMap.putAll(generateStatRefundFeeChildrenChargeFormItemsMap());
        return composeChildrenChargeFormItemsMap;
    }

    private Map<String, List<ChargeFormItem>> generateStatRefundFeeChildrenChargeFormItemsMap() {
        return formProcessors.stream().flatMap(formProcessor -> formProcessor.getItemProcessorList().stream())
                .flatMap(itemProcessor -> {
                    List<ItemProcessor> itemProcessors = new ArrayList<>();
                    itemProcessors.add(itemProcessor);
                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(itemProcessor.getComposeChildren())) {
                        itemProcessors.addAll(itemProcessor.getComposeChildren());
                    }
                    return itemProcessors.stream();
                })
                .filter(itemProcessor -> itemProcessor.getGoodsFeeType() == GoodsFeeType.FEE_PARENT)
                .flatMap(itemProcessor -> itemProcessor.getRefundChargeFormItems().stream())
                .filter(chargeFormItem -> chargeFormItem.getGoodsFeeType() == GoodsFeeType.FEE_CHILD
                        && StringUtils.isNotBlank(chargeFormItem.getComposeParentFormItemId())
                )
                .collect(Collectors.groupingBy(ChargeFormItem::getComposeParentFormItemId));
    }

    private void updateReceivedFeeForRefund(boolean isRefundFinish, List<ChargeTransactionRecord> refundChargeTransactionRecords) {
        if (isRefundFinish) {
            formProcessors.forEach(FormProcessor::resetReceivedPrice);
            return;
        }
        if (CollectionUtils.isEmpty(refundChargeTransactionRecords)) {
            return;
        }
        /**
         * 所有的chargeformitem 包括退费的item
         */
        List<ChargeFormItem> toSaveChargeFormItems = formProcessors.stream()
                .flatMap(formProcessor -> formProcessor.getItemProcessorMap().values().stream())
                .flatMap(itemProcessor -> itemProcessor.generateToSaveChargeFormItems().stream())
                .collect(Collectors.toList());
        /**
         * 所有退的item的map
         */
        Map<String, String> allItemIdPaidItemIdMap = toSaveChargeFormItems.stream()
                .collect(Collectors.toMap(ChargeFormItem::getId, chargeFormItem -> {
                    if (StringUtils.isNotBlank(chargeFormItem.getAssociateFormItemId())) {
                        return chargeFormItem.getAssociateFormItemId();
                    }
                    return chargeFormItem.getId();
                }, (a, b) -> a));

        Map<String, String> allItemBatchInfoIdPaidItemBatchInfoIdMap = toSaveChargeFormItems.stream()
                .filter(chargeFormItem -> org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeFormItem.getChargeFormItemBatchInfos()))
                .flatMap(chargeFormItem -> chargeFormItem.getChargeFormItemBatchInfos().stream())
                .filter(chargeFormItemBatchInfo -> chargeFormItemBatchInfo.getIsDeleted() == 0)
                .collect(Collectors.toMap(ChargeFormItemBatchInfo::getId, chargeFormItemBatchInfo -> {
                            if (StringUtils.isNotBlank(chargeFormItemBatchInfo.getAssociateItemBatchInfoId())) {
                                return chargeFormItemBatchInfo.getAssociateItemBatchInfoId();
                            }
                            return chargeFormItemBatchInfo.getId();
                        }, (a, b) -> a)
                );

        Map<String, RefundUpdateReceivedItemTemp> chargeFormItemRecordIdMap = refundChargeTransactionRecords.stream()
                .filter(record -> record.getType() != ChargeTransactionRecord.RecordType.AIR_PHARMACY)
                .filter(record -> StringUtils.isNotBlank(record.getChargeFormItemId()))
                .collect(Collectors.toMap(record -> allItemIdPaidItemIdMap.get(record.getChargeFormItemId()),
                        record -> {
                            RefundUpdateReceivedItemTemp refundUpdateReceivedItemTemp = new RefundUpdateReceivedItemTemp();
                            refundUpdateReceivedItemTemp.setThisRefundFee(MathUtils.wrapBigDecimalAdd(record.getTotalPrice(), record.getDiscountPrice()));
                            refundUpdateReceivedItemTemp.setBatchTempList(Optional.ofNullable(record.getBatchInfos()).orElse(new ArrayList<>()).stream().map(batchRecord -> {
                                RefundUpdateReceivedBatchTemp refundUpdateReceivedBatchTemp = new RefundUpdateReceivedBatchTemp();
                                refundUpdateReceivedBatchTemp.setId(allItemBatchInfoIdPaidItemBatchInfoIdMap.get(batchRecord.getChargeFormItemBatchInfoId()));
                                refundUpdateReceivedBatchTemp.setThisRefundFee(batchRecord.getReceivedPrice());
                                return refundUpdateReceivedBatchTemp;
                            }).collect(Collectors.toList()));
                            return refundUpdateReceivedItemTemp;
                        },
                        (a, b) -> {
                            a.setThisRefundFee(MathUtils.wrapBigDecimalAdd(a.getThisRefundFee(), b.getThisRefundFee()));
                            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(b.getBatchTempList())) {
                                if (a.getBatchTempList() == null) {
                                    a.setBatchTempList(new ArrayList<>());
                                }
                                MergeTool.doMerge(b.getBatchTempList(),
                                        a.getBatchTempList(),
                                        (bBatch, aBatch) -> StringUtils.equals(bBatch.getId(), aBatch.getId()),
                                        Function.identity(),
                                        (aBatch) -> false,
                                        (bBatch, aBatch) -> aBatch.setThisRefundFee(MathUtils.wrapBigDecimalAdd(aBatch.getThisRefundFee(), bBatch.getThisRefundFee())));
                            }
                            return a;
                        })
                );

        Map<String, BigDecimal> airPharmacyRecordIdMap = refundChargeTransactionRecords.stream()
                .filter(record -> record.getType() == ChargeTransactionRecord.RecordType.AIR_PHARMACY)
                .filter(record -> record.getAdditional() != null && StringUtils.isNotBlank(record.getAdditional().getChargeFormId()))
                .collect(Collectors.toMap(record -> record.getAdditional().getChargeFormId(),
                        record -> MathUtils.wrapBigDecimalAdd(record.getTotalPrice(), record.getDiscountPrice()),
                        MathUtils::wrapBigDecimalAdd)
                );
        formProcessors.forEach(formProcessor -> formProcessor.updateReceivedFeeForRefund(airPharmacyRecordIdMap));
        formProcessors.stream()
                .flatMap(formProcessor -> formProcessor.getItemProcessorMap().values().stream())
                .forEach(itemProcessor -> itemProcessor.updateReceivedFeeForRefund(chargeFormItemRecordIdMap));
    }

    private void updateRefundInfoForGiftRule(String operatorId) {
        formProcessors.stream()
                .filter(formProcessor -> formProcessor.getSourceFormType() == Constants.SourceFormType.GIFT_PRODUCT)
                .forEach(formProcessor -> formProcessor.updateRefundInfoForGiftRule(operatorId));
    }

    /**
     * 退费
     *
     * @param refundInfo
     * @param keepStatus 退挂号费时需要保持sheet的状态
     * @param isSupportWholeRefundCheck 是否支持整单退费校验
     */
    public RefundResult refund(RefundInfo refundInfo, boolean keepStatus, boolean isSupportWholeRefundCheck) throws ServiceInternalException, CisCustomException {
        loadChargeConfig();
        updateDispensingInfo();
        updateChargeFormItemCount();
        this.checkDispensingFormAuditCompound(true);
        this.checkChargeSheetDispensingAndExecutionOrThrowException(isSupportWholeRefundCheck, true, refundInfo.getChargeForms());
        updateRefundInfo(refundInfo, true);
        BigDecimal adjustmentFeeOnRefund = MathUtils.wrapBigDecimalOrZero(refundInfo.getAdjustmentFee());
        adjustmentFeeOnRefund = adjustmentFeeOnRefund.compareTo(BigDecimal.ZERO) > 0 ? adjustmentFeeOnRefund : BigDecimal.ZERO;
        doCalculate(adjustmentFeeOnRefund, CalculateScene.REFUND);

        CombinedPayItem payItem = refundInfo.getPayItem();
        payItem.setAmount(MathUtils.wrapBigDecimalNegateOrZero(payItem.getAmount()));

        BigDecimal frontEndRefundFee = payItem.getNetIncome();
        BigDecimal frontEndNeedRefundFee = refundInfo.getNeedRefundFee() != null ? refundInfo.getNeedRefundFee().negate() : frontEndRefundFee;
        BigDecimal thisTimeRefundFee = realReceivableFee;
        if (thisTimeRefundFee.compareTo(BigDecimal.ZERO) < 0) { //退费
            if (thisTimeRefundFee.compareTo(_netReceivedFee.negate()) < 0) { //退费大于实收时用实收
                thisTimeRefundFee = _netReceivedFee.negate();
            }
        }

        if (thisTimeRefundFee.compareTo(BigDecimal.ZERO) > 0) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "thisTimeRefundFee >= 0 when refund, thisTimeRefundFee:" + thisTimeRefundFee);
            throw new ChargeServiceException(ChargeServiceError.CHARGE_NO_VALID_NEED_REFUND_FEE);
        }

        if (thisTimeRefundFee.compareTo(frontEndNeedRefundFee) != 0 || frontEndRefundFee.compareTo(BigDecimal.ZERO) > 0) {
            throw new ChargeServiceException(ChargeServiceError.CHARGE_FEE_CHANGED);
        }

        // 不能退超过可退金额 + 欠退金额
        if (frontEndRefundFee.compareTo(thisTimeRefundFee.subtract(_owedRefundFee)) < 0) {
            throw new ChargeServiceException(ChargeServiceError.CHARGE_REFUND_OVERFLOW);
        }

        //查询欠费单流水
        ChargeOweSheet chargeOweSheet = sheetProcessorInfoProvider.getChargeOweSheetProvider().findCurrentChargeOweSheetByChargeSheetId(clinicId, chargeSheet.getId());

        checkOwePayModeCanRefundOrThrowException(chargeSheet, payItem, chargeOweSheet);

        // 计算本次新增的欠退金额
        BigDecimal thisTimeOwedRefundFee = frontEndRefundFee.subtract(thisTimeRefundFee);

        String patientCardName = null;
        if (payItem.getPayMode() == Constants.ChargePayMode.PROMOTION_CARD) {
            Map<String, String> patientCardNameMap = Optional.ofNullable(chargeSheet.getPatientCardPromotionInfos()).orElse(new ArrayList<>())
                    .stream()
                    .filter(patientCardPromotionInfo -> patientCardPromotionInfo.getIsDeleted() == 0)
                    .collect(Collectors.toMap(ChargePatientCardPromotionInfo::getPromotionId, ChargePatientCardPromotionInfo::getName, (a, b) -> a));

            patientCardName = patientCardNameMap.getOrDefault(payItem.getThirdPartyPayCardId(), null);
        }
        //生成chargeAction
        ChargeAction chargeAction;
        if (payItem.getPayMode() == Constants.ChargePayMode.OUTPATIENT_CENTER_PAY) {
            List<ChargeTransaction> payChargeTransactions = ChargePayHandleOutpatientCenterPayService.checkCanRefundAndReturnPayTransactions(chargeSheet.getChargeTransactions(), payItem.getNetIncome().negate());
            List<ChargeAction.PayActionInfo> payActionInfos = payChargeTransactions
                    .stream()
                    .map(chargeTransaction -> {
                        ChargeAction.PayActionInfo payActionInfo = new ChargeAction.PayActionInfo();
                        payActionInfo.setPayMode(chargeTransaction.getPayMode());
                        payActionInfo.setPaySubMode(chargeTransaction.getPaySubMode());
                        payActionInfo.setThirdPartyPayCardId(chargeTransaction.getThirdPartyPayCardId());
                        payActionInfo.setThirdPartyPayTransactionId(chargeTransaction.getThirdPartyPayTransactionId());
                        payActionInfo.setAmount(chargeTransaction.getAmount());

                        ChargePayModeUtils.PayModeNameDto payModeNameDto = ChargePayModeUtils.queryPayModeNameAndPayModeSubName(chargeTransaction.getPayMode(), chargeTransaction.getPaySubMode(), null, () -> null);
                        payActionInfo.setPayModeName(payModeNameDto.getPayModeName());
                        payActionInfo.setPaySubModeName(payModeNameDto.getPaySubModeName());
                        return payActionInfo;
                    }).collect(Collectors.toList());
            chargeAction = ChargeUtils.insertChargeAction(null, chargeSheet, ChargeAction.Type.REFUND, PayStatus.WAITING, payItem.getNetIncome().negate(), payActionInfos, refundInfo.getChargeComment(), null, refundInfo.getOperatorId(), refundInfo.getCheckerId());

        } else {
            payItem.bindPayActionInfo(patientCardName, sheetProcessorInfoProvider.getChargePayModeProvider().getChargePayModeConfigSimpleByChainId(chargeSheet.getChainId()));
            chargeAction = ChargeUtils.insertChargeAction(null, chargeSheet, ChargeAction.Type.REFUND, PayStatus.WAITING, payItem.getNetIncome().negate(), payItem.getPayActionInfo(), refundInfo.getChargeComment(), null, refundInfo.getOperatorId(), refundInfo.getCheckerId());
        }


        //这里开始要区分了，直接入账还是三方支付
        boolean needThirdPartyPay = needCallThirdPartyPay(payItem, chargeSheet.getChainId(), chargeSheet.getClinicId());
        int payStatus = PayStatus.NONE;
        String thirdPartyPayTaskId = null;
        String chargePayTransactionId = null;
        ChargeTransaction chargeTransaction = null;
        if ((payItem.getNetIncome().compareTo(BigDecimal.ZERO) < 0
                || (payItem.getPayMode() == Constants.ChargePayMode.HEALTH_CARD && payItem.getNetIncome().compareTo(BigDecimal.ZERO) == 0))
                && needThirdPartyPay) {
            chargeRefundSheet = generateChargeRefundSheet(thisTimeRefundFee, adjustmentFeeOnRefund, thisTimeOwedRefundFee, refundInfo.getOperatorId());

            ChargePayRefundInfo chargePayRefundInfo = new ChargePayRefundInfo();
            chargePayRefundInfo.setChainId(chargeSheet.getChainId());
            chargePayRefundInfo.setClinicId(chargeSheet.getClinicId());
            chargePayRefundInfo.setChargeSheetId(chargeSheet.getId());
            chargePayRefundInfo.setRefundSheetId(chargeRefundSheet.getId());
            chargePayRefundInfo.setChargeActionId(chargeAction.getId());
            chargePayRefundInfo.setPatientId(chargeSheet.getPatientId());
            chargePayRefundInfo.setPatientOrderId(chargeSheet.getPatientOrderId());
            chargePayRefundInfo.setMemberId(chargeSheet.getMemberId());
            chargePayRefundInfo.setSellerId(chargeSheet.getSellerId());
            chargePayRefundInfo.setDoctorId(chargeSheet.getDoctorId());
            chargePayRefundInfo.setPayMode(payItem.getPayMode());
            chargePayRefundInfo.setPaySubMode(payItem.getPaySubMode());
            chargePayRefundInfo.setPayModeName(payItem.getPayModeName());
            chargePayRefundInfo.setRefundFee(payItem.getNetIncome());
            chargePayRefundInfo.setPaySource(refundInfo.getPaySource());
            chargePayRefundInfo.setAuthCode(payItem.getAuthCode());
            chargePayRefundInfo.setChargeTransactions(chargeSheet.getChargeTransactions());
            chargePayRefundInfo.setChargeOweSheet(chargeOweSheet);
            chargePayRefundInfo.setOperatorId(operatorId);
            chargePayRefundInfo.setChargeTransactionId(AbcIdUtils.getUUID());
            chargePayRefundInfo.setTransactionIds(payItem.getTransactionIds());
            ChargePayRefundResult chargePayRefundResult = sheetProcessorInfoProvider.getChargePayProvider().refund(chargePayRefundInfo);
            if (chargePayRefundResult != null) {
                payStatus = chargePayRefundResult.getPayStatus();
                thirdPartyPayTaskId = chargePayRefundResult.getThirdPartyPayTaskId();
                chargePayTransactionId = chargePayRefundResult.getChargePayTransactionId();
                if (payStatus == PayStatus.SUCCESS) {
                    //红冲发票
//                    destroyInvoice();
                    payItem.setAmount(chargePayRefundResult.getRefundedFee());
                    payItem.setChange(BigDecimal.ZERO);
                    payItem.setPrincipalAmount(chargePayRefundResult.getRefundedPrincipal());
                    payItem.setPresentAmount(chargePayRefundResult.getRefundedPresent());
                    chargeTransaction = writeRefundTransaction(chargePayRefundInfo.getChargeTransactionId(), payItem, chargeRefundSheet.getAdjustmentFee(), chargeRefundSheet.getOwedRefundFee(),
                            chargePayRefundResult.getPayInfo(), chargeAction, chargePayRefundResult.getAssociateThirdPartyPayTaskId(), chargePayRefundResult.getAssociateOweCombinePayTransactionRecordId(), refundInfo.getPaySource(),
                            keepStatus, StatRecordProcessor.RefundFlatType.ALL_FLAT, null, refundInfo.getOperatorId());

                    Map<String, Integer> needRefundDeductGoodsIdMap = Optional.ofNullable(refundInfo.getChargeForms()).orElse(new ArrayList<>()).stream()
                            .filter(chargeForm -> chargeForm.getChargeFormItems() != null)
                            .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                            .filter(item -> item.getIsDeleted() == 0)
                            .filter(chargeFormItem -> MathUtils.wrapBigDecimalCompare(chargeFormItem.getDeductTotalCount(), BigDecimal.ZERO) > 0)
                            .filter(chargeFormItem -> org.apache.commons.lang3.StringUtils.isNotEmpty(chargeFormItem.getProductId()))
                            .filter(chargeFormItem -> chargeFormItem.getComposeType() != ComposeType.COMPOSE_SUB_ITEM)
                            .collect(Collectors.toMap(ChargeFormItem::getProductId, chargeFormItem -> chargeFormItem.getDeductTotalCount().intValue(), Integer::sum));

                    refundDeductGoods(needRefundDeductGoodsIdMap, chargeTransaction.getId(), refundInfo.getOperatorId());

                    Map<String, Integer> refundVerifyMap = formProcessors.stream()
                            .filter(formProcessor -> formProcessor.getSourceFormType() != Constants.SourceFormType.AIR_PHARMACY)
                            .flatMap(formProcessor -> formProcessor.getAffectedChargeFormItems().stream())
                            .filter(chargeFormItem -> MathUtils.wrapBigDecimalCompare(chargeFormItem.getVerifyTotalCount(), BigDecimal.ZERO) > 0)
                            .map(chargeFormItem -> chargeFormItem.getPromotionInfo())
                            .filter(Objects::nonNull)
                            .flatMap(promotionInfo -> promotionInfo.getVerifyDeductInfos().stream())
                            .collect(Collectors.toMap(ChargeDiscountInfo.VerifyDeductInfo::getId, verifyDeductInfo -> verifyDeductInfo.getDeductedCount().intValue(), Integer::sum));
                    refundVerify(refundVerifyMap, refundInfo.getOperatorId());

                } else {
                    lockPatientOrderForRefund(chargePayRefundResult, payItem.getPayMode());
                }
            } else {
                payStatus = PayStatus.FAILED;
            }
        } else {
            //红冲发票
//            destroyInvoice();
            chargeTransaction = writeRefundTransaction(null, payItem, adjustmentFeeOnRefund, thisTimeOwedRefundFee, null, chargeAction, null, null, refundInfo.getPaySource(),
                    keepStatus, StatRecordProcessor.RefundFlatType.ALL_FLAT, null, refundInfo.getOperatorId());

            Map<String, Integer> needRefundDeductGoodsIdMap = Optional.ofNullable(refundInfo.getChargeForms()).orElse(new ArrayList<>()).stream()
                    .filter(chargeForm -> chargeForm.getChargeFormItems() != null)
                    .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                    .filter(item -> item.getIsDeleted() == 0)
                    .filter(chargeFormItem -> MathUtils.wrapBigDecimalCompare(chargeFormItem.getDeductTotalCount(), BigDecimal.ZERO) > 0)
                    .filter(chargeFormItem -> org.apache.commons.lang3.StringUtils.isNotEmpty(chargeFormItem.getProductId()))
                    .filter(chargeFormItem -> chargeFormItem.getComposeType() != ComposeType.COMPOSE_SUB_ITEM)
                    .collect(Collectors.toMap(ChargeFormItem::getProductId, chargeFormItem -> chargeFormItem.getDeductTotalCount().intValue(), Integer::sum));

            refundDeductGoods(needRefundDeductGoodsIdMap, chargeTransaction.getId(), refundInfo.getOperatorId());


            Map<String, Integer> refundVerifyMap = formProcessors.stream()
                    .filter(formProcessor -> formProcessor.getSourceFormType() != Constants.SourceFormType.AIR_PHARMACY)
                    .flatMap(formProcessor -> formProcessor.getAffectedChargeFormItems().stream())
                    .filter(chargeFormItem -> MathUtils.wrapBigDecimalCompare(chargeFormItem.getVerifyTotalCount(), BigDecimal.ZERO) > 0)
                    .filter(chargeFormItem -> StringUtils.isEmpty(chargeFormItem.getComposeParentFormItemId()))
                    .map(chargeFormItem -> chargeFormItem.getPromotionInfo())
                    .filter(Objects::nonNull)
                    .flatMap(promotionInfo -> promotionInfo.getVerifyDeductInfos().stream())
                    .collect(Collectors.toMap(ChargeDiscountInfo.VerifyDeductInfo::getId, verifyDeductInfo -> verifyDeductInfo.getDeductedCount().intValue(), Integer::sum));
            refundVerify(refundVerifyMap, refundInfo.getOperatorId());

            payStatus = PayStatus.SUCCESS;
        }


        RefundResult refundResult = new RefundResult();
        refundResult.setStatus(chargeSheet.getStatus());
        refundResult.setRefundFee(frontEndRefundFee.negate());
        refundResult.setChargePayTransactionId(chargePayTransactionId);
        refundResult.setChargeTransactionId(Objects.isNull(chargeTransaction) ? null : chargeTransaction.getId());
        refundResult.setOwedRefundFee(thisTimeOwedRefundFee.compareTo(BigDecimal.ZERO) > 0 ? thisTimeOwedRefundFee : BigDecimal.ZERO);
        refundResult.setRefundedFee(_refundFee.add(frontEndRefundFee).negate());
        refundResult.setTotalOwedRefundFee(_owedRefundFee.add(thisTimeOwedRefundFee));
        refundResult.setChargeActionId(chargeAction.getId());
        refundResult.setPayStatus(payStatus);
        refundResult.setThirdPartyPayTaskId(thirdPartyPayTaskId);
        refundResult.setThirdPartyPayTaskType(thirdPartyPayTaskId != null ? 1 : null);

        return refundResult;
    }

    /**
     * 检查是否整单是否审核或者已经调配
     *
     * @param isSupportSheetCheck 是否支持收费单检查
     */
    private void checkDispensingFormAuditCompound(boolean isSupportSheetCheck) {
        if (!isSupportSheetCheck) {
            return;
        }
        for (FormProcessor formProcessor : formProcessors) {
            formProcessor.checkDispensingFormAuditCompound(chargeConfigDetail, this.getWholeSheetOperateEnable(), true);
        }
    }

    /**
     * 整单收费/退费是否启用
     *
     * @return true:启用 false:不启用
     */
    private boolean getWholeSheetOperateEnable() {
        return Objects.nonNull(chargeConfigDetail) && chargeConfigDetail.getWholeSheetOperateEnable() == YesOrNo.YES;
    }

    /**
     * 退款前检查是否有部分项目已经执行或者发药
     *
     * @param isSupportSheetCheck 是否支持收费单校验
     * @param isNeedFormItemNumCheck  是否需要校验客户端传入的请求FormItem与数据库的FormItem项目/数量是否一致
     * @param clientChargeFormList 退费信息,当传入NULL时不校验(项目是否增减 + 数量是否正确)
     */
    private void checkChargeSheetDispensingAndExecutionOrThrowException(boolean isSupportSheetCheck, boolean isNeedFormItemNumCheck, List<ChargeForm> clientChargeFormList) {
        // 是否整单收费/退费
        boolean isSupportWholeSheet = isSupportSheetCheck && this.getWholeSheetOperateEnable();
        if (!isSupportWholeSheet) {
            return;
        }
        // 1.需要先判断是否有部分项目已经执行或者发药
        Set<ItemProcessor> itemProcessorSet = new HashSet<>();
        for (FormProcessor formProcessor : formProcessors) {
            // 找出复合条件的item
            itemProcessorSet.addAll(formProcessor.getItemProcessorList().stream()
                    .flatMap(itemProcessor -> {
                        if (itemProcessor.getComposeType() == ComposeType.COMPOSE) {
                            return itemProcessor.getComposeChildren().stream();
                        }
                        return Stream.of(itemProcessor);
                    })
                    .filter(itemProcessor -> {
                        ChargeFormItem chargeFormItem = itemProcessor.getChargeFormItem();
                        return Objects.nonNull(chargeFormItem) && chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED
                                && MathUtils.compareZero(MathUtils.wrapBigDecimalSubtract(
                                MathUtils.calculateTotalCount(chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount()),
                                MathUtils.calculateTotalCount(chargeFormItem.getRefundUnitCount(), chargeFormItem.getRefundDoseCount()))) > 0;
                    }).collect(Collectors.toSet()));
        }
        for (ItemProcessor itemProcessor : itemProcessorSet) {
            // 有项目已经执行
            ChargeExecuteItem chargeExecuteItem = itemProcessor.getChargeExecuteItem();
            if (Objects.nonNull(chargeExecuteItem) && MathUtils.compareZero(chargeExecuteItem.getExecutedCount()) > 0) {
                throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_NOT_SUPPORT_REFUND_BY_PART_EXECUTION);
            }
            // 有项目已经发药
            DispensingInfo dispensingInfo = itemProcessor.getDispensingInfo();
            if (Objects.nonNull(dispensingInfo) && MathUtils.compareZero(MathUtils.calculateTotalCount(dispensingInfo.getDispensedUnitCount(), dispensingInfo.getDispensedDoseCount())) > 0) {
                throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_NOT_SUPPORT_REFUND_BY_PART_DISPENSING, "发药", "退药");
            }
        }
        // 2.判断项目是否需要检验(增减 + 数量)是否正确
        if (!isNeedFormItemNumCheck) {
            return;
        }
        Map<String, ChargeFormItem> reqRefundChargeFormMap = Optional.ofNullable(clientChargeFormList).orElse(Collections.emptyList()).stream()
                .filter(Objects::nonNull)
                .map(ChargeForm::getChargeFormItems)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(ChargeFormItem::getId, chargeFormItem -> chargeFormItem));
        for (ItemProcessor itemProcessor : itemProcessorSet) {
            if (itemProcessor.getIsGift() == YesOrNo.YES) {
                // 这个地方需要过滤出赠品
                continue;
            }
            ChargeFormItem dbChargeFormItem = itemProcessor.getChargeFormItem();
            ChargeFormItem reqChargeFormItem = reqRefundChargeFormMap.get(itemProcessor.getChargeFormItem().getId());
            if (Objects.isNull(reqChargeFormItem)) {
                throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_NOT_SUPPORT_PART_REFUND);
            }
            /**
             * 比较协议:
             *  1.如果该item是含有卡项抵扣或者核销的,则: unitCount * doseCount - refundUnitCount * refundDoseCount = reqUnitCount * reqDoseCount + reqDeductTotalCount + reqVerifyTotalCount
             *  2.如果该item是不含有卡项抵扣或者核销的,则: unitCount * doseCount - refundUnitCount * refundDoseCount = reqUnitCount * reqDoseCount
             */
            BigDecimal dbCanCount = MathUtils.wrapBigDecimalSubtract(MathUtils.calculateTotalCount(dbChargeFormItem.getUnitCount(), dbChargeFormItem.getDoseCount()), MathUtils.calculateTotalCount(dbChargeFormItem.getRefundUnitCount(), dbChargeFormItem.getRefundDoseCount()));
            BigDecimal reqCanCount = MathUtils.calculateTotalCount(reqChargeFormItem.getUnitCount(), reqChargeFormItem.getDoseCount());
            if (MathUtils.compareZero(dbChargeFormItem.getDeductAndVerifyTotalCount()) > 0) {
                reqCanCount = MathUtils.wrapBigDecimalAdd(reqCanCount, reqChargeFormItem.getDeductAndVerifyTotalCount());
            }
            if (MathUtils.wrapBigDecimalCompare(reqCanCount, dbCanCount) != 0) {
                throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_NOT_SUPPORT_PART_REFUND);
            }
        }
    }

    /**
     * 判断欠费退费时，是否满足退费的条件
     *
     * @param chargeSheet
     * @param payItem
     * @param chargeOweSheet
     */
    private void checkOwePayModeCanRefundOrThrowException(ChargeSheet chargeSheet, CombinedPayItem payItem, ChargeOweSheet chargeOweSheet) {
        if (payItem.getPayMode() != Constants.ChargePayMode.OWE_PAY) {
            return;
        }
        // 前端传的退费金额
        BigDecimal frontEndRefundFee = payItem.getNetIncome().abs();
        // 总的欠费金额
        BigDecimal totalOweFee = Optional.ofNullable(chargeSheet.getChargeTransactions()).orElse(new ArrayList<>())
                .stream()
                .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.OWE_PAY)
                .map(chargeTransaction -> MathUtils.wrapBigDecimalOrZero(chargeTransaction.getAmount()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        // 判断退费金额是否超过了欠费的金额
        if (frontEndRefundFee.compareTo(totalOweFee) > 0) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "退的欠费金额超过了总的欠费金额，totalOweFee: {}, frontEndRefundFee: {}", totalOweFee, frontEndRefundFee);
            throw new ChargeServiceException(ChargeServiceError.REFUND_PATIENT_FEE_NOT_VALID);
        }
        // 判断欠费单是否已经开始还款
        if (Objects.isNull(chargeOweSheet)) {
            return;
        }
        if (chargeOweSheet.getStatus() != ChargeOweSheet.Status.UNCHARGED) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "欠费单不是待收状态，不能对收费单进行欠费的退费， chargeOweSheet.status: {}", chargeOweSheet.getStatus());
            throw new ChargeServiceException(ChargeServiceError.CHARGE_OWE_PAY_MODE_CANNOT_REFUND);
        }
    }

    /**
     * 退抵扣
     *
     * @param needRefundDeductGoodsIdMap
     */
    private void refundDeductGoods(Map<String, Integer> needRefundDeductGoodsIdMap, String transactionId, String operatorId) {
        if (MapUtils.isEmpty(needRefundDeductGoodsIdMap)) {
            return;
        }
        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "需要退抵扣的id和数量: {}", JsonUtils.dump(needRefundDeductGoodsIdMap));
        //查询本次收费对应抵扣的卡项列表哪些还在有效期
        List<String> promotionIds = Optional.ofNullable(chargeSheet.getPatientCardPromotionInfos()).orElse(new ArrayList<>())
                .stream()
                .filter(patientCardPromotionInfo -> patientCardPromotionInfo.getIsDeleted() == 0)
                .filter(ChargePatientCardPromotionInfo::getChecked)
                .filter(patientCardPromotionInfo -> org.apache.commons.collections.CollectionUtils.isNotEmpty(patientCardPromotionInfo.getDeductItems()))
                .map(ChargePatientCardPromotionInfo::getPromotionId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(promotionIds)) {
            return;
        }
        List<cn.abcyun.bis.rpc.sdk.cis.model.promotion.PatientCardView> patientCardViews = sheetProcessorInfoProvider.getPromotionProvider().listPatientsCardsByIds(chargeSheet.getChainId(), promotionIds);
        List<String> availablePromotionIds = Optional.ofNullable(patientCardViews).orElse(new ArrayList<>())
                .stream()
                .filter(cn.abcyun.bis.rpc.sdk.cis.model.promotion.PatientCardView::isAvailable)
                .map(cn.abcyun.bis.rpc.sdk.cis.model.promotion.PatientCardView::getId)
                .distinct().collect(Collectors.toList());

        List<CardPatientPresentsDeductReq.Deduct> deducts = chargeSheet.getPatientCardPromotionInfos()
                .stream()
                .filter(patientCardPromotionInfo -> patientCardPromotionInfo.getIsDeleted() == 0)
                .filter(patientCardPromotionInfo -> availablePromotionIds.contains(patientCardPromotionInfo.getPromotionId()))
                .map(patientCardPromotionInfo -> {
                    CardPatientPresentsDeductReq.Deduct deduct = new CardPatientPresentsDeductReq.Deduct();
                    deduct.setPatientCardId(patientCardPromotionInfo.getPromotionId());
                    deduct.setDeductPresentList(patientCardPromotionInfo.getDeductItems().stream()
                            .filter(PromotionDeductItem::getChecked)
                            .filter(promotionDeductItem -> promotionDeductItem.getStatus() == PromotionDeductItem.Status.DEDUCTED || promotionDeductItem.getStatus() == PromotionDeductItem.Status.PARTED_REFUND_DEDUCT)
                            .filter(promotionDeductItem -> promotionDeductItem.getCanRefundCount() > 0)
                            .filter(promotionDeductItem -> needRefundDeductGoodsIdMap.containsKey(promotionDeductItem.getGoodsId()))
                            .map(promotionDeductItem -> {
                                int leftRefundCount = needRefundDeductGoodsIdMap.getOrDefault(promotionDeductItem.getGoodsId(), 0);
                                if (leftRefundCount == 0) {
                                    return null;
                                }
                                int thisTimeRefundCount = Math.min(leftRefundCount, promotionDeductItem.getCanRefundCount());
                                needRefundDeductGoodsIdMap.put(promotionDeductItem.getGoodsId(), leftRefundCount - thisTimeRefundCount);
                                CardPatientPresentsDeductReq.Deduct.Present present = new CardPatientPresentsDeductReq.Deduct.Present();
                                present.setPresentId(promotionDeductItem.getId());
                                present.setDeductCount(thisTimeRefundCount);
                                present.setGoodsId(promotionDeductItem.getGoodsId());
                                present.setGoodsType(promotionDeductItem.getGoodsType());
                                present.setGoodsSubType(promotionDeductItem.getGoodsSubType());
                                present.setGoodsCMSpec(promotionDeductItem.getGoodsCMSpec());
                                promotionDeductItem.setRefundDeductCount(promotionDeductItem.getRefundDeductCount() + thisTimeRefundCount);
                                if (promotionDeductItem.getCanRefundCount() == 0) {
                                    promotionDeductItem.setStatus(PromotionDeductItem.Status.REFUND_DEDUCT);
                                } else {
                                    promotionDeductItem.setStatus(PromotionDeductItem.Status.PARTED_REFUND_DEDUCT);
                                }
                                return present;
                            })
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList()));
                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(deduct.getDeductPresentList())) {
                        FillUtils.fillLastModifiedBy(patientCardPromotionInfo, operatorId);
                    }
                    return deduct;
                })
                .filter(deduct -> org.apache.commons.collections.CollectionUtils.isNotEmpty(deduct.getDeductPresentList()))
                .collect(Collectors.toList());
        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "剩余需要退抵扣的数量: {}", JsonUtils.dump(needRefundDeductGoodsIdMap));
        if (CollectionUtils.isEmpty(deducts)) {
            return;
        }
        CardPatientPresentsDeductReq req = new CardPatientPresentsDeductReq();
        req.setDeductPatientId(chargeSheet.getPatientId());
        req.setChainId(chargeSheet.getChainId());
        req.setClinicId(chargeSheet.getClinicId());
        req.setChargeSheetId(chargeSheet.getId());
        req.setChargeTransactionId(transactionId);
        req.setDeducts(deducts);
        req.setType(CardPatientPresentsDeductReq.Type.UNDO_DEDUCT);
        req.setSellerId(chargeSheet.getSellerId());
        req.setDoctorId(chargeSheet.getDoctorId());
        req.setOperatorId(operatorId);

        sheetProcessorInfoProvider.getPromotionProvider().deduct(req);
    }

    private void refundVerify(Map<String, Integer> refundVerifyMap, String operatorId) {

        if (CollectionUtils.isEmpty(refundVerifyMap)) {
            return;
        }

        chargeSheet.getChargeVerifyInfos().stream()
                .filter(chargeVerifyInfo -> chargeVerifyInfo.getIsDeleted() == 0)
                .flatMap(chargeVerifyInfo -> chargeVerifyInfo.getItemVerifyDetails().stream())
                .flatMap(itemVerifyDetail -> itemVerifyDetail.getVerifyInfoDetails().stream())
                .peek(verifyInfoDetail -> {
                    Integer thisRefundCount = refundVerifyMap.get(verifyInfoDetail.getVerificationCode());

                    if (Objects.nonNull(thisRefundCount) && thisRefundCount > 0) {
                        verifyInfoDetail.setRefundDeductCount(thisRefundCount + verifyInfoDetail.getRefundDeductCount());

                        if (verifyInfoDetail.getCanRefundCount() == 0) {
                            verifyInfoDetail.setStatus(PromotionDeductItem.Status.REFUND_DEDUCT);
                        } else {
                            verifyInfoDetail.setStatus(PromotionDeductItem.Status.PARTED_REFUND_DEDUCT);
                        }
                    }

                });
        VerificationRefundReq req = new VerificationRefundReq();
        req.setChainId(chargeSheet.getChainId());
        req.setClinicId(chargeSheet.getClinicId());
        req.setOperatorId(operatorId);

        List<VerificationRefundReq.VerificationItemRefundReq> verificationItemRefundReqs = new ArrayList<>();
        refundVerifyMap.forEach((code, count) -> {
            VerificationRefundReq.VerificationItemRefundReq verificationItemRefundReq = new VerificationRefundReq.VerificationItemRefundReq();
            verificationItemRefundReq.setVerificationCode(code);
            verificationItemRefundReq.setCount(count);

            verificationItemRefundReqs.add(verificationItemRefundReq);
        });
        req.setVerificationItemRefundReqs(verificationItemRefundReqs);


        sheetProcessorInfoProvider.getCisMallOrderProvider().refundVerification(req);
    }


    /**
     * 红冲发票
     */
//    private void destroyInvoice() {
//
//        if (chargeSheet.getAdditional() == null) {
//            return;
//        }
//
//        int invoiceStatus = Optional.ofNullable(chargeSheet.getAdditional())
//                .map(chargeSheetAdditional -> chargeSheetAdditional.getInvoiceStatus())
//                .orElse(Constants.ChargeSheetInvoiceStatus.NONE);
//
//        //退发票
//        if (Constants.ChargeSheetInvoiceStatus.getInvoicedStatues().contains(invoiceStatus) && sheetProcessorInfoProvider.getChargeSheetInvoiceProvider() != null) {
//            sheetProcessorInfoProvider.getChargeSheetInvoiceProvider().destroyInvoice(chargeSheet, operatorId);
//            return;
//        }
//
//        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.REFUNDED) {
//            return;
//        }
//
//
//        if (invoiceStatus != Constants.ChargeSheetInvoiceStatus.INVOICE_INVALID && invoiceStatus != Constants.ChargeSheetInvoiceStatus.INVOICE_REFUND) {
//            //如果不需要退发票，则将发票状态置为不可开状态
//            invoiceStatus = Constants.ChargeSheetInvoiceStatus.NONE;
//            chargeSheet.getAdditional().setInvoiceStatus(invoiceStatus);
//        }
//    }
    public ChargeTransaction refundCallback(ChargeRefundSheet chargeRefundSheet,
                                            int payMode,
                                            int paySubMode,
                                            BigDecimal refundFee,
                                            String associatePayTransactionId,
                                            int paySource,
                                            ThirdPartyPayInfo thirdPartyPayInfo,
                                            ChargeAction chargeAction,
                                            List<ChargeTransactionRecord> addedRecords,
                                            String operatorId) {
        updateDispensingInfo();
        int refundFlatType = getRefundFlatType(payMode, paySubMode, chargeRefundSheet);
        updateRefundInfo(chargeRefundSheet, operatorId);

        doCalculate(Optional.ofNullable(chargeRefundSheet).map(ChargeRefundSheet::getAdjustmentFee).orElse(BigDecimal.ZERO), CalculateScene.REFUND);

        BigDecimal thirdPayReceivedFee = MathUtils.wrapBigDecimalOrZero(refundFee);
        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "[refundCallback] charge fee info, totalFee:{}, discountFee:{}, adjustmentFee:{}, receivableFee:{}, realReceivableFee:{}, receivedFee:{}, frontEndReceivableFee:{}",
                totalFee, discountFee, adjustmentFee, receivableFee, realReceivableFee, _receivedFee, thirdPayReceivedFee);
        BigDecimal thisTimeRefundFee = realReceivableFee;
        if (thisTimeRefundFee.compareTo(BigDecimal.ZERO) < 0) { //退费
            if (thisTimeRefundFee.compareTo(_netReceivedFee.negate()) < 0) { //退费大于实收时用实收
                thisTimeRefundFee = _netReceivedFee.negate();
            }
        }
        if (thisTimeRefundFee.compareTo(BigDecimal.ZERO) > 0) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "thisTimeRefundFee >= 0 when refund, thisTimeRefundFee:" + thisTimeRefundFee);
            throw new ChargeServiceException(ChargeServiceError.CHARGE_NO_VALID_NEED_REFUND_FEE);
        }
        // 不能退超过可退金额 + 欠退金额
        if (thirdPayReceivedFee.compareTo(thisTimeRefundFee.subtract(_owedRefundFee)) < 0) {
            throw new ChargeServiceException(ChargeServiceError.CHARGE_REFUND_OVERFLOW);
        }
        // 计算本次新增的欠退金额
        BigDecimal thisTimeOwedRefundFee = thirdPayReceivedFee.subtract(thisTimeRefundFee);
        // 计算入账金额
        CombinedPayItem payItem = new CombinedPayItem();
        payItem.setAmount(refundFee);
        payItem.setPayMode(payMode);
        payItem.setPaySubMode(paySubMode);
        payItem.setChange(BigDecimal.ZERO);
        //红冲发票
//        destroyInvoice();
        ChargeTransaction chargeTransaction = writeRefundTransaction(null, payItem, Optional.ofNullable(chargeRefundSheet).map(ChargeRefundSheet::getAdjustmentFee).orElse(BigDecimal.ZERO), thisTimeOwedRefundFee, thirdPartyPayInfo,
                chargeAction, associatePayTransactionId, null, paySource,
                paySource == Constants.ChargeSource.REGISTRATION, refundFlatType, addedRecords, operatorId);
        //退抵扣
        Map<String, Integer> needRefundDeductGoodsIdMap = getNeedRefundDeductGoodsIdMap(chargeRefundSheet);
        refundDeductGoods(needRefundDeductGoodsIdMap, chargeTransaction.getId(), operatorId);
        return chargeTransaction;

    }

    private int getRefundFlatType(int payMode, int paySubMode, ChargeRefundSheet chargeRefundSheet) {
        //本次退费为医保原路退回 && 这些项目都还没有退过 && 有刷了医保卡的项目 && 本次退费的项目包含所有医保刷了卡的项目
        boolean isHealthCardOriginalRefund = isHealthCardRefund(payMode, paySubMode) && sheetContainHealthCardPayItem() && thisTimeRefundItemIsAllNotRefund(chargeRefundSheet) && thisTimeRefundContainAllHealthCardPayItem(chargeRefundSheet);

        return isHealthCardOriginalRefund ? StatRecordProcessor.RefundFlatType.ORIGINAL_PAID_ITEM_FLAT : StatRecordProcessor.RefundFlatType.ALL_FLAT;
    }

    private boolean thisTimeRefundContainAllHealthCardPayItem(ChargeRefundSheet chargeRefundSheet) {
        if (chargeRefundSheet == null || (chargeRefundSheet.getChargeRefundFormItems() == null && chargeRefundSheet.getChargeRefundForms() == null)) {
            return false;
        }
        List<String> healthCardPayChargeFormItemIds = formProcessors
                .stream()
                .filter(formProcessor -> !formProcessor.isAirPharmacy())
                .flatMap(formProcessor -> formProcessor.getItemProcessorList().stream())
                .flatMap(itemProcessor -> {
                    List<ItemProcessor> itemProcessors = new ArrayList<>();
                    itemProcessors.add(itemProcessor);
                    if (itemProcessor.getComposeType() == ComposeType.COMPOSE && org.apache.commons.collections.CollectionUtils.isNotEmpty(itemProcessor.getComposeChildren())) {
                        itemProcessors.addAll(itemProcessor.getComposeChildren());
                    }
                    return itemProcessors.stream();
                })
                .filter(ItemProcessor::isMarkedByHealthCardPay)
                .map(ItemProcessor::getItemId)
                .distinct()
                .collect(Collectors.toList());

        List<String> healthCardPayChargeFormIds = formProcessors
                .stream()
                .filter(FormProcessor::isAirPharmacy)
                .flatMap(formProcessor -> formProcessor.getItemProcessorList().stream())
                .flatMap(itemProcessor -> {
                    List<ItemProcessor> itemProcessors = new ArrayList<>();
                    itemProcessors.add(itemProcessor);
                    if (itemProcessor.getComposeType() == ComposeType.COMPOSE && org.apache.commons.collections.CollectionUtils.isNotEmpty(itemProcessor.getComposeChildren())) {
                        itemProcessors.addAll(itemProcessor.getComposeChildren());
                    }
                    return itemProcessors.stream();
                })
                .filter(ItemProcessor::isMarkedByHealthCardPay)
                .map(ItemProcessor::getChargeFormId)
                .distinct()
                .collect(Collectors.toList());

        boolean isAllContainItem;
        if (chargeRefundSheet.getChargeRefundFormItems() != null) {
            List<String> refundChargeFormItemIds = chargeRefundSheet.getChargeRefundFormItems().stream()
                    .map(ChargeRefundFormItem::getChargeFormItemId)
                    .collect(Collectors.toList());
            isAllContainItem = new HashSet<>(refundChargeFormItemIds).containsAll(healthCardPayChargeFormItemIds);
        } else {
            isAllContainItem = true;
        }

        boolean isAllContainForm;
        if (chargeRefundSheet.getChargeRefundForms() != null) {
            List<String> refundChargeFormIds = chargeRefundSheet.getChargeRefundForms().stream()
                    .map(ChargeRefundForm::getChargeFormId)
                    .collect(Collectors.toList());
            isAllContainForm = new HashSet<>(refundChargeFormIds).containsAll(healthCardPayChargeFormIds);
        } else {
            isAllContainForm = true;
        }
        return isAllContainItem && isAllContainForm;
    }

    private boolean sheetContainHealthCardPayItem() {
        return formProcessors
                .stream()
                .flatMap(formProcessor -> formProcessor.getItemProcessorList().stream())
                .flatMap(itemProcessor -> {
                    List<ItemProcessor> itemProcessors = new ArrayList<>();
                    itemProcessors.add(itemProcessor);
                    if (itemProcessor.getComposeType() == ComposeType.COMPOSE && org.apache.commons.collections.CollectionUtils.isNotEmpty(itemProcessor.getComposeChildren())) {
                        itemProcessors.addAll(itemProcessor.getComposeChildren());
                    }
                    return itemProcessors.stream();
                })
                .anyMatch(ItemProcessor::isMarkedByHealthCardPay);
    }

    /**
     * 本次要退费的项目都还没有退过费
     *
     * @param chargeRefundSheet
     * @return
     */
    private boolean thisTimeRefundItemIsAllNotRefund(ChargeRefundSheet chargeRefundSheet) {
        if (chargeRefundSheet == null || (chargeRefundSheet.getChargeRefundFormItems() == null && chargeRefundSheet.getChargeRefundForms() == null)) {
            return false;
        }
        boolean itemIsAllNotRefund;
        if (chargeRefundSheet.getChargeRefundFormItems() != null) {
            List<String> refundChargeFormItemIds = chargeRefundSheet.getChargeRefundFormItems().stream()
                    .map(ChargeRefundFormItem::getChargeFormItemId)
                    .collect(Collectors.toList());
            List<ItemProcessor> refundItemProcessors = formProcessors
                    .stream()
                    .flatMap(formProcessor -> formProcessor.getItemProcessorList().stream())
                    .flatMap(itemProcessor -> {
                        List<ItemProcessor> itemProcessors = new ArrayList<>();
                        itemProcessors.add(itemProcessor);
                        if (itemProcessor.getComposeType() == ComposeType.COMPOSE && org.apache.commons.collections.CollectionUtils.isNotEmpty(itemProcessor.getComposeChildren())) {
                            itemProcessors.addAll(itemProcessor.getComposeChildren());
                        }
                        return itemProcessors.stream();
                    })
                    .filter(itemProcessor -> refundChargeFormItemIds.contains(itemProcessor.getItemId()))
                    .collect(Collectors.toList());


            itemIsAllNotRefund = refundItemProcessors.stream().allMatch(ItemProcessor::checkItemIsNotRefund);
        } else {
            itemIsAllNotRefund = true;
        }

        boolean formIsAllNotRefund;
        if (chargeRefundSheet.getChargeRefundForms() != null) {
            List<String> refundFormIds = chargeRefundSheet.getChargeRefundForms().stream()
                    .map(ChargeRefundForm::getChargeFormId)
                    .collect(Collectors.toList());

            formIsAllNotRefund = formProcessors.stream()
                    .filter(formProcessor -> refundFormIds.contains(formProcessor.getFormId()))
                    .allMatch(FormProcessor::checkFormIsNotRefund);
        } else {
            formIsAllNotRefund = true;
        }
        return itemIsAllNotRefund && formIsAllNotRefund;
    }

    private boolean isHealthCardRefund(int payMode, int paySubMode) {
        return payMode == Constants.ChargePayMode.HEALTH_CARD
                || (payMode == Constants.ChargePayMode.OUTPATIENT_CENTER_PAY && paySubMode == Constants.ChargePaySubMode.OUTPATIENT_CENTER_PAY_INSURANCE);
    }

    private Map<String, Integer> getNeedRefundDeductGoodsIdMap(ChargeRefundSheet chargeRefundSheet) {
        Map<String, Integer> needRefundDeductGoodsIdMap = new HashMap<>();
        if (chargeRefundSheet == null || chargeRefundSheet.getChargeRefundFormItems() == null) {
            return new HashMap<>();
        }
        Map<String, ItemProcessor> itemProcessorMap = formProcessors
                .stream()
                .flatMap(formProcessor -> formProcessor.getItemProcessorList().stream())
                .collect(Collectors.toMap(ItemProcessor::getItemId, Function.identity(), (a, b) -> a));
        Map<String, ChargeFormItem> chargeFormItemMap = ChargeUtils.getChargeSheetItems(chargeSheet).stream().collect(Collectors.toMap(ChargeFormItem::getId, Function.identity(), (a, b) -> a));
        for (ChargeRefundFormItem refundFormItem : chargeRefundSheet.getChargeRefundFormItems()) {
            if (MathUtils.wrapBigDecimalCompare(refundFormItem.getDeductTotalCount(), BigDecimal.ZERO) <= 0) {
                continue;
            }
            ChargeFormItem sourceChargeFormItem = chargeFormItemMap.getOrDefault(refundFormItem.getChargeFormItemId(), null);
            if (sourceChargeFormItem == null) {
                continue;
            }
            String itemKey = sourceChargeFormItem.getComposeType() == ComposeType.COMPOSE_SUB_ITEM ? sourceChargeFormItem.getComposeParentFormItemId() : sourceChargeFormItem.getId();
            ItemProcessor itemProcessor = itemProcessorMap.getOrDefault(itemKey, null);
            if (itemProcessor == null) {
                continue;
            }
            needRefundDeductGoodsIdMap.put(itemProcessor.getProductId(), refundFormItem.getDeductTotalCount().intValue());
        }
        return needRefundDeductGoodsIdMap;
    }


    public List<ChargeFormItem> getAffectedChargeFormItems() {
        return formProcessors.stream()
                .flatMap(formProcessor -> formProcessor.getAffectedChargeFormItems().stream()).collect(Collectors.toList());
    }

    /**
     * 部分收费状态下退费
     *
     * @param chargeTransactionId
     * @param payItem
     * @param thirdPartyPayInfo
     * @param chargeAction
     * @param associatePayTransactionId
     * @param paySource
     * @param isPaybackFinish
     * @param operatorId
     */
    private void writePaidbackTransaction(String chargeTransactionId, CombinedPayItem payItem, ThirdPartyPayInfo thirdPartyPayInfo, ChargeAction chargeAction,
                                          String associatePayTransactionId, int paySource, boolean isPaybackFinish, String operatorId) {
        if (payItem.getNetIncome().compareTo(BigDecimal.ZERO) > 0) {
            return;
        }
        List<ChargeTransaction> chargeTransactions = new ArrayList<>();
//        if (payItem.getNetIncome().compareTo(BigDecimal.ZERO) != 0) {
        ChargeTransaction chargeTransaction = ChargeUtils.insertChargeTransaction(chargeTransactionId, chargeSheet, payItem.getPayMode(), payItem.getPaySubMode(), payItem.getNetIncome(),
                payItem.getPrincipalAmount(), payItem.getPresentAmount(), payItem.getNetIncome(), BigDecimal.ZERO, BigDecimal.ZERO, paySource, thirdPartyPayInfo,
                chargeAction, operatorId);
        chargeTransactions.add(chargeTransaction);

        ChargeTransaction associateChargeTransaction = ChargeUtils.findByChargePayTransactionId(chargeSheet, associatePayTransactionId);
        if (associateChargeTransaction != null) {
            chargeTransaction.setAssociateTransactionId(associateChargeTransaction.getId());
            associateChargeTransaction.setRefundedAmount(MathUtils.wrapBigDecimalAdd(associateChargeTransaction.getRefundedAmount(), chargeTransaction.getAmount()));
            if (payItem.getPayMode() == Constants.ChargePayMode.HEALTH_CARD) {
                associateChargeTransaction.setAssociateTransactionId(chargeTransaction.getId());
            }
            chargeTransaction.setIsPaidback(1);
            if (MathUtils.wrapBigDecimalCompare(associateChargeTransaction.getAmount(), MathUtils.wrapBigDecimalNegateOrZero(associateChargeTransaction.getRefundedAmount())) == 0) {
                associateChargeTransaction.setIsPaidback(1); //只有退完才能冲抵掉
            }
        }
//        }

        chargeAction.setPayStatus(PayStatus.SUCCESS);

        if (isPaybackFinish) {
            // 退回附加费用
            chargeSheet.getAdditionalFees().forEach(additionalFee -> additionalFee.deleteModel(operatorId));
            chargeSheet.setPromotionInfoJson(null);
            chargeSheet.setPromotionInfo(null);
            // mark existed transaction as paidback
            chargeSheet.getChargeTransactions()
                    .forEach(ts -> ts.setIsPaidback(1));
            formProcessors.forEach(formProcessor -> formProcessor.payBack(operatorId, true));
            chargeSheet.setStatus(Constants.ChargeSheetStatus.UNCHARGED);
            ChargeUtils.refundPatientDeduct(chargeSheet,
                    (chainId, promotionIds) -> sheetProcessorInfoProvider.getPromotionProvider().listPatientsCardsByIds(chainId, promotionIds),
                    (req) -> sheetProcessorInfoProvider.getPromotionProvider().deduct(req),
                    chargeTransactionId,
                    operatorId);
        } else {
            formProcessors.forEach(formProcessor -> formProcessor.payBack(operatorId, false));
        }
        _refundFee = _refundFee.add(payItem.getNetIncome());
        // 处理欠费逻辑
        dealChargeOwe(chargeTransaction);
        // 统计
        statRecordProcessor.setChargeInfo(
                isPaybackFinish ? StatRecordProcessor.PayType.PAID_BACK : StatRecordProcessor.PayType.PARTED_PAID_BACK,
                chargeSheet.getTransactionRecordHandleMode(),
                StatRecordProcessor.RefundFlatType.ALL_FLAT,
                queryIsHospital(),
                chargeTransactions,
                formProcessors.stream()
                        .filter(formProcessor -> formProcessor.getSourceFormType() != Constants.SourceFormType.AIR_PHARMACY)
                        .flatMap(formProcessor -> formProcessor.getAffectedChargeFormItems().stream())
                        .filter(Objects::nonNull).collect(Collectors.toList()),
                formProcessors.stream()
                        .filter(formProcessor -> formProcessor.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY)
                        .map(FormProcessor::getAffectedForm)
                        .filter(Objects::nonNull).collect(Collectors.toList()),
                isPaybackFinish ? formProcessors.stream()
                        .filter(formProcessor -> formProcessor.getSourceFormType() != Constants.SourceFormType.AIR_PHARMACY)
                        .filter(formProcessor -> org.apache.commons.collections.CollectionUtils.isNotEmpty(formProcessor.getItemProcessorList()))
                        .flatMap(formProcessor -> formProcessor.getItemProcessorList().stream())
                        .flatMap(itemProcessor -> itemProcessor.generateStatRecordAffectedDeductedItem().stream())
                        .filter(Objects::nonNull).collect(Collectors.toList())
                        : null,
                null,
                null,
                formProcessors.stream().flatMap(formProcessor -> formProcessor.getChargeFormItems().stream())
                        .filter(Objects::nonNull)
                        .filter(ChargeFormItem::isParentItem)
                        .collect(Collectors.toList()),
                generateStatParentChildrenChargeFormItemsMap()
        );
        // 退费的records
        refundChargeStatRecordResult = generateChargeTransactionRecords();
        // 基于退费的records修改收费项的已收金额
        updateReceivedFeeForRefund(chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED, Optional.ofNullable(refundChargeStatRecordResult).map(StatRecordResult::getAddedRecords).orElse(new ArrayList<>()));
    }

    public RefundResult paidback(RefundInfo refundInfo) throws CisCustomException, ServiceInternalException {
        doCalculate(BigDecimal.ZERO, CalculateScene.REFUND);

        CombinedPayItem payItem = refundInfo.getPayItem();
        payItem.setAmount(MathUtils.wrapBigDecimalNegateOrZero(payItem.getAmount()));
        BigDecimal frontEndRefundFee = payItem.getNetIncome().negate();
        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "paidback fee info, totalFee:{}, discountFee:{}, adjustmentFee:{}, receivableFee:{}, realReceivableFee:{}, receivedFee:{}, frontEndRefundFee:{}",
                totalFee, discountFee, adjustmentFee, receivableFee, realReceivableFee, _receivedFee, frontEndRefundFee);
        BigDecimal paybackFee = _receivedFee.add(_refundFee);
        if (paybackFee.compareTo(BigDecimal.ZERO) <= 0) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "paybackFee <= 0 when refund:" + paybackFee);
            throw new ChargeServiceException(ChargeServiceError.CHARGE_NO_VALID_NEED_REFUND_FEE);
        }
        if (paybackFee.compareTo(frontEndRefundFee) < 0) {
            throw new ChargeServiceException(ChargeServiceError.CHARGE_FEE_CHANGED);
        }
        boolean isPaybackFinish = paybackFee.compareTo(frontEndRefundFee) == 0;
        String patientCardName = null;
        if (payItem.getPayMode() == Constants.ChargePayMode.PROMOTION_CARD) {
            Map<String, String> patientCardNameMap = Optional.ofNullable(chargeSheet.getPatientCardPromotionInfos()).orElse(new ArrayList<>())
                    .stream()
                    .filter(patientCardPromotionInfo -> patientCardPromotionInfo.getIsDeleted() == 0)
                    .collect(Collectors.toMap(ChargePatientCardPromotionInfo::getPromotionId, ChargePatientCardPromotionInfo::getName, (a, b) -> a));
            patientCardName = patientCardNameMap.getOrDefault(payItem.getThirdPartyPayCardId(), null);
        }
        payItem.bindPayActionInfo(patientCardName, sheetProcessorInfoProvider.getChargePayModeProvider().getChargePayModeConfigSimpleByChainId(chargeSheet.getChainId()));
        // 计算入账流水
        ChargeAction chargeAction = ChargeUtils.insertChargeAction(null, chargeSheet, ChargeAction.Type.REFUND, PayStatus.WAITING, payItem.getNetIncome().negate(), payItem.getPayActionInfo(), refundInfo.getChargeComment(), null, refundInfo.getOperatorId(), null);
        // 这里开始要区分了，直接入账还是三方支付
        boolean needThirdPartyPay = needCallThirdPartyPay(payItem, chargeSheet.getChainId(), chargeSheet.getClinicId());
        int payStatus = PayStatus.NONE;
        String thirdPartyPayTaskId = null;
        String chargePayTransactionId = null;
        if (payItem.getNetIncome().compareTo(BigDecimal.ZERO) < 0 && needThirdPartyPay) {
//            chargeRefundSheet = generateChargeRefundSheet(thisTimeRefundFee, adjustmentFeeOnRefund, thisTimeOwedRefundFee, refundInfo.getOperatorId());
            ChargePayRefundInfo chargePayRefundInfo = new ChargePayRefundInfo();
            chargePayRefundInfo.setChainId(chargeSheet.getChainId());
            chargePayRefundInfo.setClinicId(chargeSheet.getClinicId());
            chargePayRefundInfo.setChargeSheetId(chargeSheet.getId());
//            chargePayRefundInfo.setRefundSheetId(chargeRefundSheet.getId());
            chargePayRefundInfo.setChargeActionId(chargeAction.getId());
            chargePayRefundInfo.setPatientId(chargeSheet.getPatientId());
            chargePayRefundInfo.setPatientOrderId(chargeSheet.getPatientOrderId());
            chargePayRefundInfo.setPayMode(payItem.getPayMode());
            chargePayRefundInfo.setPaySubMode(payItem.getPaySubMode());
            chargePayRefundInfo.setPayModeName(payItem.getPayModeName());
            chargePayRefundInfo.setRefundFee(payItem.getNetIncome());
            chargePayRefundInfo.setPaySource(refundInfo.getPaySource());
            chargePayRefundInfo.setChargeTransactions(chargeSheet.getChargeTransactions());
            chargePayRefundInfo.setMemberId(chargeSheet.getMemberId());
            chargePayRefundInfo.setSellerId(chargeSheet.getSellerId());
            chargePayRefundInfo.setDoctorId(chargeSheet.getDoctorId());
            chargePayRefundInfo.setOperatorId(operatorId);
            chargePayRefundInfo.setChargeTransactionId(AbcIdUtils.getUUID());
            chargePayRefundInfo.setTransactionIds(payItem.getTransactionIds());
            ChargePayRefundResult chargePayRefundResult = sheetProcessorInfoProvider.getChargePayProvider().paidback(chargePayRefundInfo);
            if (chargePayRefundResult != null) {
                payStatus = chargePayRefundResult.getPayStatus();
                thirdPartyPayTaskId = chargePayRefundResult.getThirdPartyPayTaskId();
                chargePayTransactionId = chargePayRefundResult.getChargePayTransactionId();
                if (payStatus == PayStatus.SUCCESS) {
                    payItem.setAmount(chargePayRefundResult.getRefundedFee());
                    payItem.setChange(BigDecimal.ZERO);
                    payItem.setPrincipalAmount(chargePayRefundResult.getRefundedPrincipal());
                    payItem.setPresentAmount(chargePayRefundResult.getRefundedPresent());

                    writePaidbackTransaction(chargePayRefundInfo.getChargeTransactionId(), payItem, chargePayRefundResult.getPayInfo(), chargeAction, chargePayRefundResult.getAssociateThirdPartyPayTaskId(),
                            refundInfo.getPaySource(), isPaybackFinish, refundInfo.getOperatorId());
                } else {
                    lockPatientOrderForRefund(chargePayRefundResult, payItem.getPayMode());
                }
            } else {
                payStatus = PayStatus.FAILED;
            }
        } else {
            writePaidbackTransaction(null, payItem, null, chargeAction, null,
                    refundInfo.getPaySource(), isPaybackFinish, refundInfo.getOperatorId());

            payStatus = PayStatus.SUCCESS;
        }


        RefundResult refundResult = new RefundResult();
        refundResult.setStatus(chargeSheet.getStatus());
        refundResult.setRefundFee(frontEndRefundFee);
        refundResult.setOwedRefundFee(BigDecimal.ZERO);
        refundResult.setRefundedFee(_refundFee.negate());
        refundResult.setTotalOwedRefundFee(BigDecimal.ZERO);
        refundResult.setChargeActionId(chargeAction.getId());
        refundResult.setChargePayTransactionId(chargePayTransactionId);
        refundResult.setPayStatus(payStatus);
        refundResult.setThirdPartyPayTaskId(thirdPartyPayTaskId);
        refundResult.setThirdPartyPayTaskType(thirdPartyPayTaskId != null ? 1 : null);

        return refundResult;
    }

    public void paidbackCallback(ChargePayTransaction chargePayTransaction, ChargeAction chargeAction, String operatorId) throws CisCustomException {
        doCalculate(BigDecimal.ZERO, CalculateScene.REFUND);
        BigDecimal thirdPayReceivedFee = MathUtils.wrapBigDecimalOrZero(chargePayTransaction.getAmount());
        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "[paidbackCallback] charge fee info, totalFee:{}, discountFee:{}, adjustmentFee:{}, receivableFee:{}, realReceivableFee:{}, receivedFee:{}, frontEndReceivableFee:{}",
                totalFee, discountFee, adjustmentFee, receivableFee, realReceivableFee, _receivedFee, thirdPayReceivedFee);
        BigDecimal frontEndRefundFee = thirdPayReceivedFee.negate();
        BigDecimal paybackFee = _receivedFee.add(_refundFee);
        if (paybackFee.compareTo(BigDecimal.ZERO) <= 0) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "paybackFee <= 0 when refund:" + paybackFee);
            throw new ChargeServiceException(ChargeServiceError.CHARGE_NO_VALID_NEED_REFUND_FEE);
        }
        if (paybackFee.compareTo(frontEndRefundFee) < 0) {
            throw new ChargeServiceException(ChargeServiceError.CHARGE_FEE_CHANGED);
        }
        boolean isPaybackFinish = paybackFee.compareTo(frontEndRefundFee) == 0;

        // 计算入账金额
        CombinedPayItem payItem = new CombinedPayItem();
        payItem.setAmount(MathUtils.wrapBigDecimalOrZero(chargePayTransaction.getAmount()));
        payItem.setPayMode(chargePayTransaction.getPayMode());
        payItem.setPaySubMode(chargePayTransaction.getPaySubMode());
        payItem.setChange(BigDecimal.ZERO);

        writePaidbackTransaction(null, payItem, chargePayTransaction.getPayInfo(), chargeAction, chargePayTransaction.getAssociatePayTransactionId(),
                chargePayTransaction.getPaySource(), isPaybackFinish, operatorId);
    }

    public ChargeSheet updateProductInfoForDraft() throws ServiceInternalException {
        updateProductInfo(true, false, 1, 0);
        return generateToSaveChargeSheet();
    }

    /**
     * 执行站开单和检查站开单都在调用
     *
     * @return
     * @throws ServiceInternalException
     */
    public ChargeSheet updateProductInfoAndBindMemberId() throws ServiceInternalException {
        updatePatientInfo();
        fetchAndSetMemberInfo(false);
        updateProductInfo(true, false, 1, 0);
        ApplyPromotionInfoResult applyPromotionInfoResult = fetchAndApplyPromotionInfo(new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), null, null, null, null);
        doCalculate(MathUtils.wrapBigDecimalOrZero(applyPromotionInfoResult.getAdjustmentFeeOnCalculate()), CalculateScene.PAY);
        checkAntimicrobialGoodsLimit();
        return generateToSaveChargeSheet();
    }


    public ChargeSheet generateToSaveChargeSheet() {
        chargeSheet.setChargeForms(formProcessors.stream().map(FormProcessor::generateToSaveChargeForm).collect(Collectors.toList()));

        chargeSheet.setTotalFee(totalFee);
        chargeSheet.setDiscountFee(discountFee);
        chargeSheet.setAdditionalFee(calculateAdditionalTotalFee(chargeSheet.getAdditionalFees()));
        chargeSheet.setRefundFee(MathUtils.setScaleTwo(calculateTransactionsTotalRefundFee(chargeSheet.getChargeTransactions())));
        chargeSheet.setReceivableFee(receivableFee);
        chargeSheet.setReceivedFee(MathUtils.setScaleTwo(calculateTransactionsTotalReceivedFee(chargeSheet.getChargeTransactions())));
        if (chargeSheet.getAdditional() != null) {
            chargeSheet.getAdditional().setIsCanBeClone(ChargeUtils.isCanBeCloneForWeClinic(chargeSheet) ? 1 : 0);
        }
        return chargeSheet;
    }

    public ChargeSheet generateToSaveChargeSheetForDraftAdjustmentFee(BigDecimal expectedAdjustmentFee,
                                                                      BigDecimal outpatientAdjustmentFee,
                                                                      BigDecimal draftAdjustmentFee,
                                                                      List<PromotionReq> discountPromotionReqs,
                                                                      List<CouponPromotionReq> couponPromotionReqs,
                                                                      List<GiftRulePromotionReq> giftRulePromotionReqs,
                                                                      List<PatientCardPromotionReq> patientCardPromotionReqs,
                                                                      List<PatientPointDeductProductPromotionReq> patientPointDeductProductPromotionReqs,
                                                                      List<ItemSinglePromotionReq> itemSinglePromotionReqs,
                                                                      PatientPointsInfoReq patientPointsInfoReq,
                                                                      List<MallVerificationReq> mallVerifications) {
        updateProductInfo(false, false, 1, 0);
        // 更新虚拟药房的快递费
        updateVirtualPharmacyChargeFormDeliveryFee(Constants.ChargeSource.CHARGE);
        updateExpressDeliveryFeeAndProcessFee(Constants.ChargeSource.CHARGE);
        ApplyPromotionInfoResult applyPromotionInfoResult = fetchAndApplyPromotionInfo(discountPromotionReqs, couponPromotionReqs, giftRulePromotionReqs, patientCardPromotionReqs, patientPointDeductProductPromotionReqs, mallVerifications, patientPointsInfoReq, expectedAdjustmentFee, null);
//        BigDecimal adjustFee = calculateAdjustFee(expectedAdjustmentFee, MathUtils.wrapBigDecimalOrZero(outpatientAdjustmentFee), null, draftAdjustmentFee);
        doCalculate(applyPromotionInfoResult.getAdjustmentFeeOnCalculate(), CalculateScene.PAY);
        return generateToSaveChargeSheet();
    }

    public List<ChargeAirPharmacyLogistics> generateToSaveChargeAirPharmacyLogistics() {
        return formProcessors.stream()
                .filter(formProcessor -> formProcessor.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY || formProcessor.getPharmacyType() == GoodsConst.PharmacyType.VIRTUAL_PHARMACY)
                .map(FormProcessor::generateToSaveChargeAirPharmacyLogistics)
                .filter(Objects::nonNull)
                .filter(chargeAirPharmacyLogistics -> chargeAirPharmacyLogistics.getIsNeedInsert() == 1)
                .collect(Collectors.toList());
    }

    public List<ChargeAirPharmacyMedicalRecord> generateToSaveChargeAirPharmacyMedicalRecords() {
        return formProcessors.stream()
                .filter(formProcessor -> formProcessor.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY)
                .map(FormProcessor::generateToSaveChargeAirPharmacyMedicalRecord)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }


    private ChargeRefundSheet generateChargeRefundSheet(BigDecimal refundFee, BigDecimal adjustmentFee, BigDecimal owedRefundFee, String operatorId) {
        ChargeRefundSheet chargeRefundSheet = new ChargeRefundSheet();
        chargeRefundSheet.setId(AbcIdUtils.getUUID());
        chargeRefundSheet.setChainId(chargeSheet.getChainId());
        chargeRefundSheet.setClinicId(chargeSheet.getClinicId());
        chargeRefundSheet.setPatientId(chargeSheet.getPatientId());
        chargeRefundSheet.setPatientOrderId(chargeSheet.getPatientOrderId());
        chargeRefundSheet.setChargeSheetId(chargeSheet.getId());
        chargeRefundSheet.setTotalFee(refundFee);
        chargeRefundSheet.setAdjustmentFee(adjustmentFee);
        chargeRefundSheet.setOwedRefundFee(owedRefundFee);
        chargeRefundSheet.setStatus(ChargeRefundSheet.Status.REFUNDING);

        List<ChargeRefundForm> chargeRefundForms = formProcessors.stream()
                .map(FormProcessor::getAddedRefundChargeForm)
                .filter(Objects::nonNull)
                .map(chargeForm -> {
                    ChargeRefundForm chargeRefundForm = new ChargeRefundForm();
                    chargeRefundForm.setId(AbcIdUtils.getUID());
                    chargeRefundForm.setChainId(chargeForm.getChainId());
                    chargeRefundForm.setClinicId(chargeForm.getClinicId());
                    chargeRefundForm.setChargeFormId(chargeForm.getId());
                    chargeRefundForm.setRefundSheetId(chargeRefundSheet.getId());
                    chargeRefundForm.setStatus(ChargeRefundForm.Status.REFUNDING);
                    chargeRefundForm.setTotalPrice(chargeForm.getTotalPrice());
                    chargeRefundForm.setDiscountPrice(chargeForm.getDiscountPrice());

                    FillUtils.fillCreatedBy(chargeRefundForm, operatorId);
                    return chargeRefundForm;
                }).collect(Collectors.toList());
        chargeRefundSheet.setChargeRefundForms(chargeRefundForms);

        List<ChargeRefundFormItem> chargeRefundFormItems = formProcessors.stream()
                .flatMap(formProcessor -> formProcessor.getItemProcessorList().stream())
                .map(ItemProcessor::getAddedRefundChargeItem)
                .flatMap(Collection::stream)
                .map(chargeFormItem -> {
                    ChargeRefundFormItem chargeRefundFormItem = new ChargeRefundFormItem();
                    BeanUtils.copyProperties(chargeFormItem, chargeRefundFormItem);
                    chargeRefundFormItem.setChargeFormItemId(chargeFormItem.getAssociateFormItemId());
                    chargeRefundFormItem.setStatus(ChargeRefundFormItem.Status.REFUNDING);
                    chargeRefundFormItem.setRefundSheetId(chargeRefundSheet.getId());
                    FillUtils.fillCreatedBy(chargeRefundFormItem, operatorId);
                    return chargeRefundFormItem;
                })
                .collect(Collectors.toList());
        chargeRefundSheet.setChargeRefundFormItems(chargeRefundFormItems);
        FillUtils.fillCreatedBy(chargeRefundSheet, operatorId);
        return chargeRefundSheet;
    }

    public ChargeRefundSheet getChargeRefundSheet() {
        return chargeRefundSheet;
    }

    /**
     * @param chargeItemStatuses
     * @return 返回结构的key  example: 1表示药房编号 = 1，药房类型为虚拟药房， value 就是该药房的所有goodsId
     * goodsId-useDismounting
     */
    private Map<Integer, List<QueryGoodsWithStockReq>> collectProductIdsWithPharmacyNo(int pharmacyType, Predicate<FormProcessor> filterFormPredicate, List<Integer> chargeItemStatuses) {
        return formProcessors.stream()
                .filter(formProcessor -> formProcessor.getSourceFormType() != Constants.SourceFormType.AIR_PHARMACY)
                .filter(filterFormPredicate)
                .filter(formProcessor -> formProcessor.getPharmacyType() == pharmacyType)
                .flatMap(formProcessor -> formProcessor.collectProductIdsAndCount(chargeItemStatuses).stream())
                .collect(Collectors.groupingBy(QueryGoodsWithStockReq::getPharmacyNo));
    }

    private List<CollectAirPharmacyProductInfo> collectAirPharmacyProductIds(int chargeItemStatus) {
        return formProcessors.stream()
                .filter(formProcessor -> formProcessor.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY)
                .map(formProcessor -> {
                    CollectAirPharmacyProductInfo airPharmacyProductInfo = new CollectAirPharmacyProductInfo();
                    airPharmacyProductInfo.setVendorId(formProcessor.getVendorId());
                    airPharmacyProductInfo.setProductIds(formProcessor.collectProductIds(chargeItemStatus));
                    return airPharmacyProductInfo;
                }).collect(Collectors.toList());
    }

    private List<QueryChineseMedicinesInPharmacyByCadnsReq.QueryCadnsGoodsItems> collectProductNamesByPrescriptionChinese(int productType, int productSubType) {
        return formProcessors.stream() //所有的chargeForm
                .filter(formProcessor -> formProcessor.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE || formProcessor.getSourceFormType() == Constants.SourceFormType.COMPOSE_PRODUCT)
                .flatMap(formProcessor -> formProcessor.collectQueryCadnsGoodsItems(productType, productSubType).stream())
                .collect(Collectors.toList());
    }

    private String getSpecByProductNames(List<QueryChineseMedicinesInPharmacyByCadnsReq.QueryCadnsGoodsItems> queryCadnsGoodsItems, int productType, int productSubType) {
        if (CollectionUtils.isEmpty(queryCadnsGoodsItems)) {
            return "";
        }
        Set<String> productNames = queryCadnsGoodsItems.stream()
                .map(QueryChineseMedicinesInPharmacyByCadnsReq.QueryCadnsGoodsItems::getMedicineCadn)
                .collect(Collectors.toSet());

        String spec = "";
        for (FormProcessor formProcessor : formProcessors) {
            if (formProcessor.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE) {
                if (!CollectionUtils.isEmpty(formProcessor.getChargeFormItems())) {
                    if (formProcessor.isNameInProductNames(productNames, productType, productSubType)) {
                        spec = formProcessor.getSpecification();
                        break;
                    }
                }
            }
        }
        return spec;
    }

    /**
     * 拉取患者信息
     * 参数 needMember是否需要拉取病人所在家庭成员信息有会员信息返回回来
     */
    private void updatePatientInfo() {
        /**
         * 1、如果不是匿名患者，需要从crm获取patient信息
         * 2、如果是匿名患者，从patientOrder出
         */
        if (StringUtils.isBlank(chargeSheet.getPatientId()) || StringUtils.equals(chargeSheet.getPatientId(), Constants.ANONYMOUS_PATIENT_ID)) {
            if (patientInfo != null) {
                return;
            }
            patientInfo = ChargeUtils.parsePatientInfoFromPatientOrder(patientOrder);
        } else {
            if (sheetProcessorInfoProvider.getPatientInfoProvider() != null) {
                patientInfo = sheetProcessorInfoProvider.getPatientInfoProvider().findPatientInfoById(chargeSheet.getChainId(), chargeSheet.getClinicId(), chargeSheet.getPatientId(), false, false);
            }
            if (Objects.nonNull(patientInfo) && Objects.nonNull(patientOrder)) {
                patientInfo.setAge(patientOrder.getPatientAge());
            }
        }
    }

    /**
     * 药品类的chargeForm，可能被调价
     * 这个函数主要是处理 药品类的跳爱
     */
    private void updateProductInfo(boolean isWithStock, boolean isCopyByOutpatient, int notQuerySheBaoInfo, int withShebaoPayLimitPrice) throws ServiceInternalException {
        updateVirtualPharmacyFormItemPharmacyTypeAndPharmacyNo();
        updateLocalProductInfo(isWithStock, isCopyByOutpatient, notQuerySheBaoInfo, withShebaoPayLimitPrice);
        // 更新挂号单信息
        updateRegistrationInfo();


        //绑定空中药房的productInfo
        updateAirPharmacyProductInfo(isWithStock);
        // 是否需要更新goods的name为最新的name
        formProcessors.forEach(formProcessor -> formProcessor.updateProductInfo(isCopyByOutpatient));

        GoodsConfigView goodsConfig = getGoodsConfigView();
        boolean isLockByBatch = GoodsLockingUtils.isLockByBatch(goodsConfig);
        // 绑定批次信息
        if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED) {
            formProcessors.stream()
                    .flatMap(formProcessor -> formProcessor.getItemProcessorList().stream())
                    .forEach(itemProcessor -> itemProcessor.bindBatchInfo(isLockByBatch, operatorId));
        }
        if (isWithStock) {
            formProcessors.forEach(formProcessor -> formProcessor.updateProductStock(isLockByBatch));
        }
        // 计算空中药房的成本价
        formProcessors.forEach(FormProcessor::updateTotalCostPrice);

        /**
         * 将是否能刷社保
         */
        formProcessors.stream().flatMap(form -> form.getItemProcessorList().stream())
                .forEach(item -> item.setCanPayHealthCard(getCanPayHealthCard()));

    }

    private void updateRegistrationInfo() {
        if (StringUtils.isBlank(chargeSheet.getPatientOrderId())) {
            return;
        }

        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED) {
            return;
        }

        boolean containRegistrationItem = ChargeUtils.getChargeSheetItems(chargeSheet)
                .stream()
                .anyMatch(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.REGISTRATION
                        && chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.UNCHARGED);

        if (!containRegistrationItem) {
            return;
        }

        RegistrationSheet registrationSheet = getSheetProcessorInfoProvider().getRegistrationProvider().findRegistrationSheet(chargeSheet.getPatientOrderId());
        // 查询社保挂号信息
        if (Objects.nonNull(registrationSheet) && !CollectionUtils.isEmpty(registrationSheet.getRegistrationForms())) {

            Map<String, RegistrationForm> registrationFormMap = ListUtils.toMap(registrationSheet.getRegistrationForms(), RegistrationForm::getId);
            formProcessors.forEach(form -> {
                RegistrationForm registrationForm = registrationFormMap.get(form.getChargeForm().getSourceFormId());
                if (Objects.nonNull(registrationForm)) {
                    form.updateRegistrationForm(registrationForm, operatorId);
                }
            });
        }
    }

    private GoodsConfigView getGoodsConfigView() {
        if (goodsConfigView != null) {
            return goodsConfigView;
        }
        goodsConfigView = sheetProcessorInfoProvider.getCisGoodsProvider().getGoodsConfig(clinicId);
        return goodsConfigView;
    }

    /**
     * 校验批次信息是否发生变化，校验规则，基于chargeFormItem生成批次信息和目前锁库系统锁的批次信息进行md5加密比较，如果批次发生变化，直接抛400错误
     */
    private void checkChargeFormItemBatchInfoChangedOrThrowException() {
        if (Objects.isNull(goodsLockingSheet)) {
            return;
        }
        String goodsLockingSheetSign = ChargeFormItemBatchInfoUtils.signGoodsLockingSheet(goodsLockingSheet);
        String chargeFormItemBatchInfoSign = ChargeFormItemBatchInfoUtils.signChargeFormItemBatchInfos(chargeSheet);
        if (!StringUtils.equals(goodsLockingSheetSign, chargeFormItemBatchInfoSign)) {
            sLogger.error("goodsLockingSheetSign not equals chargeFormItemBatchInfoSign, goodsLockingSheetSign:{}, chargeFormItemBatchInfoSign:{}", goodsLockingSheetSign, chargeFormItemBatchInfoSign);
            throw new ChargeServiceException(ChargeServiceError.CHARGE_FORM_ITEM_BATCH_INFO_CHANGED);
        }
    }

    private void updateChargeSheetIsDispensing() {
        if (chargeSheet.getIsDispensing() == 0) {
            return;
        }
        // 判断是否有药
        boolean containDispensingItem = formProcessors.stream()
                .filter(formProcessor -> !formProcessor.isAirPharmacy())
                .flatMap(formProcessor -> formProcessor.getItemProcessorList().stream())
                .anyMatch(ItemProcessor::isCanDispensing);
        if (!containDispensingItem) {
            chargeSheet.setIsDispensing(0);
            return;
        }
        //判断是否有不在我们系统发药的项目
        //查询当前门店的药房号列表
        goodsPharmacyViews = org.apache.commons.collections.CollectionUtils.isNotEmpty(goodsPharmacyViews) ?
                goodsPharmacyViews :
                sheetProcessorInfoProvider.getGoodsScProvider().findPharmacyByClinic(chargeSheet.getChainId(), chargeSheet.getClinicId());
        List<Integer> pharmacyNos = formProcessors.stream()
                .filter(formProcessor -> !formProcessor.isAirPharmacy())
                .flatMap(formProcessor -> formProcessor.getItemProcessorList().stream())
                .map(ItemProcessor::getPharmacyNo)
                .distinct()
                .collect(Collectors.toList());
        Map<Integer, Integer> dispenseFlagMap = Optional.ofNullable(goodsPharmacyViews).orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.toMap(GoodsPharmacyView::getNo, GoodsPharmacyView::getDispenseFlag, (a, b) -> a));
        boolean containDispenseByOpenapi = pharmacyNos.stream()
                .anyMatch(pharmacyNo -> {
                    Integer dispenseFlag = dispenseFlagMap.get(pharmacyNo);
                    if (dispenseFlag == null) {
                        return false;
                    }
                    return dispenseFlag == GoodsConst.DispenseFlag.DISPENSE_BY_OPENAPI;
                });
        // 如果收费单包含api发药，设置为不同时发药
        if (containDispenseByOpenapi) {
            chargeSheet.setIsDispensing(0);
        }
    }

    private void updateVirtualPharmacyFormItemPharmacyTypeAndPharmacyNo() {
        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED) {
            return;
        }

        formProcessors.stream()
                .filter(formProcessor -> formProcessor.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE && formProcessor.getPharmacyType() == GoodsConst.PharmacyType.VIRTUAL_PHARMACY)
                .forEach(FormProcessor::updateVirtualPharmacyFormItemPharmacyTypeAndPharmacyNo);
    }

    private void updateAirPharmacyProductInfo(boolean isWithStock) throws ServiceInternalException {
        List<CollectAirPharmacyProductInfo> airPharmacyProductInfos = new ArrayList<>();
        mergeAirPharmacyProductInfos(airPharmacyProductInfos, collectAirPharmacyProductIds(Constants.ChargeFormItemStatus.UNCHARGED));
        mergeAirPharmacyProductInfos(airPharmacyProductInfos, collectAirPharmacyProductIds(Constants.ChargeFormItemStatus.CHARGED));
        mergeAirPharmacyProductInfos(airPharmacyProductInfos, collectAirPharmacyProductIds(Constants.ChargeFormItemStatus.REFUNDED));
        mergeAirPharmacyProductInfos(airPharmacyProductInfos, collectAirPharmacyProductIds(Constants.ChargeFormItemStatus.UNSELECTED));
        if (CollectionUtils.isEmpty(airPharmacyProductInfos)) {
            return;
        }
        if (sheetProcessorInfoProvider.getAirPharmacyProvider() != null) {
            List<GoodsItem> goodsItems = sheetProcessorInfoProvider.getAirPharmacyProvider().fetchGoodsItems(clinicId, airPharmacyProductInfos, isWithStock);
            Map<String, GoodsItem> airPharmacyProductInfoMap = goodsItems.stream().collect(Collectors.toMap(GoodsItem::getId, Function.identity(), (goodsItem1, goodsItem2) -> goodsItem1));
            Map<Integer, Map<String, GoodsItem>> goodsItemMap = new HashMap<Integer, Map<String, GoodsItem>>() {{
                put(0, airPharmacyProductInfoMap);
            }};
            formProcessors.stream()
                    .filter(FormProcessor::isAirPharmacy)
                    .forEach(formProcessor -> formProcessor.bindGoodsItems(goodsItemMap, null, false, operatorId));
        }
    }

    private void mergeAirPharmacyProductInfos(List<CollectAirPharmacyProductInfo> a, List<CollectAirPharmacyProductInfo> b) {
        if (a == null) {
            a = new ArrayList<>();
        }
        if (CollectionUtils.isEmpty(b)) {
            return;
        }
        a.addAll(b);
        Map<String, CollectAirPharmacyProductInfo> map = a.stream().collect(Collectors.toMap(CollectAirPharmacyProductInfo::getVendorId, Function.identity(), (a1, a2) -> {
            if (a1.getProductIds() == null) {
                a1.setProductIds(new HashSet<>());
            }
            if (!CollectionUtils.isEmpty(a2.getProductIds())) {
                a1.getProductIds().addAll(a2.getProductIds());
            }
            return a1;
        }));
        a.clear();
        a.addAll(map.values());
    }

//    public Map<Integer, Map<Integer, List<QueryGoodsWithStockReq>>> generatePharmacyGoodsIdsMap() {
//        List<Integer> chargeItemStatuses = Arrays.asList(Constants.ChargeFormItemStatus.UNCHARGED,
//                Constants.ChargeFormItemStatus.CHARGED,
//                Constants.ChargeFormItemStatus.REFUNDED,
//                Constants.ChargeFormItemStatus.UNSELECTED);
//        Map<Integer, List<QueryGoodsWithStockReq>> localPharmacyGoodsReqMap = collectProductIdsWithPharmacyNo(GoodsConst.PharmacyType.LOCAL_PHARMACY, chargeItemStatuses);
//        Map<Integer, List<QueryGoodsWithStockReq>> virtualPharmacyGoodsReqMap = collectProductIdsWithPharmacyNo(GoodsConst.PharmacyType.VIRTUAL_PHARMACY, chargeItemStatuses);
//        Map<Integer, Map<Integer, List<QueryGoodsWithStockReq>>> result = new HashMap<>();
//        result.put(GoodsConst.PharmacyType.LOCAL_PHARMACY, localPharmacyGoodsReqMap);
//        result.put(GoodsConst.PharmacyType.VIRTUAL_PHARMACY, virtualPharmacyGoodsReqMap);
//        return result;
//    }

    public List<QueryGoodsInPharmacyByIdsAndStockCountReq.QueryPharmacyGoodsReq> generateQueryPharmacyGoodsReqs() {

        //分中药处方和非中药处方
        Predicate<FormProcessor> prescriptionChineseFilter = formProcessor -> formProcessor.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE;

        List<Integer> chargeItemStatuses = Arrays.asList(Constants.ChargeFormItemStatus.UNCHARGED,
                Constants.ChargeFormItemStatus.CHARGED,
                Constants.ChargeFormItemStatus.REFUNDED,
                Constants.ChargeFormItemStatus.UNSELECTED);

        //中药处方，包含本地药房和虚拟药房，每个处方生成一个对象
        List<QueryGoodsInPharmacyByIdsAndStockCountReq.QueryPharmacyGoodsReq> queryPharmacyGoodsReqs = formProcessors.stream()
                .filter(formProcessor -> formProcessor.getSourceFormType() != Constants.SourceFormType.AIR_PHARMACY)
                .filter(prescriptionChineseFilter)
                .map(formProcessor -> {
                    QueryGoodsInPharmacyByIdsAndStockCountReq.QueryPharmacyGoodsReq req = GoodsLockingUtils.generateQueryPharmacyGoodsReq(formProcessor.getPharmacyNo(),
                            formProcessor.getPharmacyType(),
                            chargeSheet.getQueryGoodsDepartmentId(),
                            formProcessor.collectProductIdsAndCount(chargeItemStatuses));


                    if (req == null) {
                        return null;
                    }

                    //如果是本地药房，找到对应的加工费的类型
                    if (formProcessor.getPharmacyType() == GoodsConst.PharmacyType.LOCAL_PHARMACY) {
                        Optional.ofNullable(formProcessor.getProcessFormProcessor())
                                .map(FormProcessor::getChargeSheetProcessInfo)
                                .filter(processInfo -> !Objects.equals(String.format("%d-%d", processInfo.getType(), processInfo.getSubType()), "0-0"))
                                .ifPresent(processInfo -> {
                                    req.setProcessType(processInfo.getType());
                                    req.setProcessSubType(processInfo.getSubType());
                                });
                    }

                    return req;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        //非中药处方
        Map<Integer, List<QueryGoodsWithStockReq>> groupByPharmacyNoReqs = formProcessors.stream()
                .filter(formProcessor -> formProcessor.getSourceFormType() != Constants.SourceFormType.AIR_PHARMACY)
                .filter(prescriptionChineseFilter.negate())
                .flatMap(formProcessor -> formProcessor.collectProductIdsAndCount(chargeItemStatuses).stream())
                .collect(Collectors.groupingBy(QueryGoodsWithStockReq::getPharmacyNo));
        queryPharmacyGoodsReqs.addAll(groupByPharmacyNoReqs.entrySet()
                .stream()
                .map(entry -> GoodsLockingUtils.generateQueryPharmacyGoodsReq(entry.getKey(),
                        GoodsConst.PharmacyType.LOCAL_PHARMACY,
                        chargeSheet.getQueryGoodsDepartmentId(),
                        entry.getValue())
                )
                .filter(Objects::nonNull)
                .collect(Collectors.toList())
        );

        return queryPharmacyGoodsReqs;
    }

    private void updateLocalProductInfo(boolean isWithStock, boolean isCopyByOutpatient, int notQuerySheBaoInfo, int withShebaoPayLimitPrice) {
        Map<Integer, List<String>> noGoodsItemProductIdMap = new HashMap<>();
        List<QueryGoodsInPharmacyByIdsAndStockCountReq.QueryPharmacyGoodsReq> pharmacyGoodsReqs = generateQueryPharmacyGoodsReqs()
                .stream()
                .peek(queryPharmacyGoodsReq -> {
                    if (isCopyByOutpatient) {
                        queryPharmacyGoodsReq.setPharmacyNo(null);
                    }
                }).collect(Collectors.toList());
        if (sheetProcessorInfoProvider.getProductInfoProvider() != null) {
            List<QueryGoodsInPharmacyByIdsRsp.QueryPharmacyGoodsRsp> pharmacyGoodsItems = sheetProcessorInfoProvider.getGoodsScProvider().queryGoodsInPharmacyByIdsAndStockCount(clinicId, chainId, isWithStock, notQuerySheBaoInfo, withShebaoPayLimitPrice, pharmacyGoodsReqs);
            Map<String, GoodsItem> chargeFormItemIdGoodsItemMap = pharmacyGoodsItems.stream()
                    .filter(queryPharmacyGoodsRsp -> org.apache.commons.collections.CollectionUtils.isNotEmpty(queryPharmacyGoodsRsp.getList()))
                    .flatMap(queryPharmacyGoodsRsp -> queryPharmacyGoodsRsp.getList().stream())
                    .filter(goodsItem -> org.apache.commons.lang3.StringUtils.isNotEmpty(goodsItem.getKeyId()))
                    .collect(Collectors.toMap(GoodsItem::getKeyId, Function.identity(), (a, b) -> a));
            // 复制历史处方不用走下面的逻辑
            if (!isCopyByOutpatient) {
                Map<Integer, QueryGoodsInPharmacyByIdsRsp.QueryPharmacyGoodsRsp> pharmacyGoodsRspMap = pharmacyGoodsItems.stream()
                        .collect(Collectors.toMap(QueryGoodsInPharmacyByIdsRsp.QueryPharmacyGoodsRsp::getPharmacyNo, Function.identity(), (a, b) -> {
                            if (a.getList() == null) {
                                a.setList(new ArrayList<>());
                            }
                            if (b.getList() != null) {
                                a.getList().addAll(b.getList());
                            }
                            return a;
                        }));
                pharmacyGoodsReqs.forEach(queryPharmacyGoodsReq -> {
                    QueryGoodsInPharmacyByIdsRsp.QueryPharmacyGoodsRsp queryPharmacyGoodsRsp = pharmacyGoodsRspMap.getOrDefault(queryPharmacyGoodsReq.getPharmacyNo(), null);
                    if (queryPharmacyGoodsRsp == null || org.apache.commons.collections.CollectionUtils.isEmpty(queryPharmacyGoodsRsp.getList())) {
                        noGoodsItemProductIdMap.put(queryPharmacyGoodsReq.getPharmacyNo(), queryPharmacyGoodsReq.getList().stream()
                                .map(QueryGoodsInPharmacyByIdsAndStockCountReq.QueryGoodsWithStock::getGoodsId)
                                .collect(Collectors.toList())
                        );
                        return;
                    }
                    List<String> existedGoodsIds = queryPharmacyGoodsRsp.getList().stream().map(GoodsItem::getId).collect(Collectors.toList());
                    noGoodsItemProductIdMap.put(queryPharmacyGoodsReq.getPharmacyNo(),
                            queryPharmacyGoodsReq.getList()
                                    .stream()
                                    .map(QueryGoodsInPharmacyByIdsAndStockCountReq.QueryGoodsWithStock::getGoodsId)
                                    .filter(goodsId -> !existedGoodsIds.contains(goodsId))
                                    .collect(Collectors.toList())
                    );
                });
            }
            if (isCopyByOutpatient) {
                List<QueryChineseMedicinesInPharmacyByCadnsReq.QueryCadnsGoodsItems> queryCadnsGoodsItems = collectProductNamesByPrescriptionChinese(Constants.ProductType.MEDICINE,
                        Constants.ProductType.SubType.MEDICINE_CHINESE);
                //找指定规格的药名
                String spec = getSpecByProductNames(queryCadnsGoodsItems,
                        Constants.ProductType.MEDICINE,
                        Constants.ProductType.SubType.MEDICINE_CHINESE);

                if (!CollectionUtils.isEmpty(queryCadnsGoodsItems) && !StringUtils.isEmpty(spec)) {
                    List<GoodsItem> goodsItemsByNames = sheetProcessorInfoProvider
                            .getGoodsScProvider()
                            .findChineseMedicineGoodsListByNames(queryCadnsGoodsItems, spec, clinicId, chargeSheet.getChainId());
                    chargeFormItemIdGoodsItemMap.putAll(Optional.ofNullable(goodsItemsByNames)
                            .orElse(new ArrayList<>())
                            .stream()
                            .filter(goodsItem -> org.apache.commons.lang3.StringUtils.isNotEmpty(goodsItem.getKeyId()))
                            .collect(Collectors.toMap(GoodsItem::getKeyId, Function.identity(), (a, b) -> a)));
                }
            }
            formProcessors.stream()
                    .filter(formProcessor -> !formProcessor.isAirPharmacy())
                    .forEach(formProcessor -> formProcessor.bindGoodsItems(chargeFormItemIdGoodsItemMap, isCopyByOutpatient, operatorId));

            if (isCopyByOutpatient) {
                updateDefaultPharmacyNoForCopyByOutpatient();
            }
        }

        if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED && MapUtils.isNotEmpty(noGoodsItemProductIdMap)) {
            formProcessors.stream()
                    .filter(formProcessor -> formProcessor.getSourceFormType() != Constants.SourceFormType.AIR_PHARMACY)
                    .forEach(formProcessor -> {
                        List<String> noGoodsItemProductIds = noGoodsItemProductIdMap.getOrDefault(formProcessor.getPharmacyNo(), null);
                        if (org.apache.commons.collections.CollectionUtils.isEmpty(noGoodsItemProductIds)) {
                            return;
                        }
                        formProcessor.setChargeFormItemProductIdNull(noGoodsItemProductIds);
                    });
        }

    }

    private List<QueryGoodsInPharmacyByIdsAndStockCountReq.QueryPharmacyGoodsReq> convertToQueryPharmacyGoodsReq(Map<Integer, Map<Integer, List<QueryGoodsWithStockReq>>> pharmacyTypeMap,
                                                                                                                 boolean isCopyByOutpatient) {
        return pharmacyTypeMap.entrySet()
                .stream()
                .map(pharmacyTypeEntry -> {
                    int pharmacyType = pharmacyTypeEntry.getKey();
                    return pharmacyTypeEntry.getValue().entrySet()
                            .stream()
                            .map(pharmacyNoEntry -> {
                                int pharmacyNo = pharmacyNoEntry.getKey();
                                List<QueryGoodsWithStockReq> queryGoodsWithStockReqs = pharmacyNoEntry.getValue();
                                if (org.apache.commons.collections.CollectionUtils.isEmpty(queryGoodsWithStockReqs)) {
                                    return null;
                                }
                                QueryGoodsInPharmacyByIdsAndStockCountReq.QueryPharmacyGoodsReq req = new QueryGoodsInPharmacyByIdsAndStockCountReq.QueryPharmacyGoodsReq();
                                //如果是复制历史处方，不指定药房号，根据goods查询的默认药房重新赋值
                                req.setPharmacyNo(isCopyByOutpatient ? null : pharmacyNo);
                                req.setPharmacyType(pharmacyType);
                                req.setSceneType(GoodsConst.SceneType.OUTPATIENT);
                                req.setDepartmentId(chargeSheet.getQueryGoodsDepartmentId());
                                req.setList(queryGoodsWithStockReqs.stream()
                                        .map(queryGoodsWithStockReq -> {
                                            QueryGoodsInPharmacyByIdsAndStockCountReq.QueryGoodsWithStock queryGoodsWithStock = new QueryGoodsInPharmacyByIdsAndStockCountReq.QueryGoodsWithStock();
                                            queryGoodsWithStock.setGoodsId(queryGoodsWithStockReq.getGoodsId());
                                            Long lockId = queryGoodsWithStockReq.getLockId();
                                            queryGoodsWithStock.setLockId(lockId);
                                            queryGoodsWithStock.setKeyId(queryGoodsWithStockReq.getKeyId());
                                            if (queryGoodsWithStockReq.getUseDismounting() == 1) {
                                                queryGoodsWithStock.setPieceCount(queryGoodsWithStockReq.getPieceCount());
                                            } else {
                                                queryGoodsWithStock.setPackageCount(queryGoodsWithStockReq.getPackageCount());
                                            }
                                            queryGoodsWithStock.setPretendBatchCutItemList(queryGoodsWithStockReq.getPretendBatchCutItemList());
                                            return queryGoodsWithStock;
                                        })
                                        .collect(Collectors.toList())
                                );
                                return req;
                            })
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                })
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    /**
     * 复制历史处方或者续方更新中药处方的默认药房
     */
    private void updateDefaultPharmacyNoForCopyByOutpatient() {
        if (Objects.isNull(sheetProcessorInfoProvider.getGoodsScProvider())) {
            return;
        }

        List<GoodsRulePharmacyItem> goodsRulePharmacyItems = formProcessors.stream()
                .filter(formProcessor -> formProcessor.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE
                        || formProcessor.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_WESTERN
                        || formProcessor.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_INFUSION)
                .map(formProcessor -> {
                    int typeId;
                    if (formProcessor.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_WESTERN
                            || formProcessor.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_INFUSION) {
                        typeId = GoodsConst.GoodsTypeId.MEDICINE_WESTERN_TYPEID;
                    } else {
                        typeId = formProcessor.getSpecificationTypeIdOrDefault();
                    }

                    GoodsRulePharmacyItem goodsRulePharmacyItem = new GoodsRulePharmacyItem();
                    goodsRulePharmacyItem.setTypeId(typeId);
                    goodsRulePharmacyItem.setSceneType(GoodsConst.SceneType.OUTPATIENT);
                    goodsRulePharmacyItem.setDepartmentId(chargeSheet.getQueryGoodsDepartmentId());
                    goodsRulePharmacyItem.setKeyId(formProcessor.getFormId());
                    if (formProcessor.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE) {
                        Optional.ofNullable(formProcessor.getProcessFormProcessor())
                                .map(FormProcessor::getChargeSheetProcessInfo)
                                .filter(processInfo -> !Objects.equals(String.format("%d-%d", processInfo.getType(), processInfo.getSubType()), "0-0"))
                                .ifPresent(processInfo -> {
                                    goodsRulePharmacyItem.setProcessType(processInfo.getType());
                                    goodsRulePharmacyItem.setProcessSubType(processInfo.getSubType());
                                });
                    }
                    return goodsRulePharmacyItem;
                }).collect(Collectors.toList());

        if (org.apache.commons.collections.CollectionUtils.isEmpty(goodsRulePharmacyItems)) {
            return;
        }

        List<QueryGoodsRulePharmacyRsp> queryGoodsRulePharmacyRsps = sheetProcessorInfoProvider.getGoodsScProvider().queryGoodsRulePharmacy(chargeSheet.getChainId(), chargeSheet.getClinicId(), goodsRulePharmacyItems);
        Map<String, GoodsPharmacyBaseView> chargeFormIdGoodsPharmacyBaseViewMap = Optional.ofNullable(queryGoodsRulePharmacyRsps)
                .orElse(new ArrayList<>())
                .stream()
                .filter(queryGoodsRulePharmacyRsp -> StringUtils.isNotEmpty(queryGoodsRulePharmacyRsp.getKeyId()))
                .collect(Collectors.toMap(QueryGoodsRulePharmacyRsp::getKeyId, QueryGoodsRulePharmacyRsp::getPharmacy, (a, b) -> a));
        formProcessors.stream()
                .filter(formProcessor -> formProcessor.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE
                        || formProcessor.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_WESTERN
                        || formProcessor.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_INFUSION)
                .forEach(formProcessor -> {
                    GoodsPharmacyBaseView defaultPharmacy = chargeFormIdGoodsPharmacyBaseViewMap.get(formProcessor.getFormId());
                    formProcessor.getChargeForm().setPharmacyNo(Optional.ofNullable(defaultPharmacy).map(GoodsPharmacyBaseView::getNo).orElse(0));
                    if (Objects.isNull(defaultPharmacy)) {
                        return;
                    }
                    // 只有中药处方才保证item的所有药房一致
                    if (formProcessor.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE) {
                        formProcessor.updateDefaultPharmacyNoForCopyByOutpatient(defaultPharmacy);
                    }
                });
    }

    public GoodsPharmacyView getDefaultPharmacyByTypeId(int typeId, List<GoodsPharmacyView> goodsPharmacyViews) {
        return Optional.ofNullable(goodsPharmacyViews)
                .orElse(new ArrayList<>())
                .stream()
                .filter(goodsPharmacyView -> goodsPharmacyView.getIsDeleted() == 0)
                .filter(goodsPharmacyView -> Objects.nonNull(goodsPharmacyView.getDefaultGoodsTypes()))
                .filter(goodsPharmacyView -> org.apache.commons.collections.CollectionUtils.isNotEmpty(goodsPharmacyView.getDefaultGoodsTypes().getTypeList()))
                .filter(goodsPharmacyView -> {
                    List<Integer> typeIds = goodsPharmacyView.getDefaultGoodsTypes().getTypeList().stream()
                            .map(GoodsSystemType::getTypeId)
                            .distinct()
                            .collect(Collectors.toList());

                    return typeIds.contains(typeId);
                })
                .findFirst()
                .orElse(null);
    }

    private void updateExpressDeliveryFeeAndProcessFee(int calSource) {
        //之前是放到doCalculateProcessFee函数里面，但是可能被下面的状态拦截掉
//        List<ChargeSheetProcessInfo> processInfos = chargeSheet.getChargeSheetProcessInfos();
//        if (!CollectionUtils.isEmpty(processInfos)) {
//            //填充名字
//            ProcessFeeProtocol.fillChargeSheetProcessNames(sheetProcessorInfoProvider.getChargeRuleProvider(), processInfos);
//        }
        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED) {
            return;
        }
        // 更新快递费
        updateExpressDeliveryFee(calSource);
        // 计算加工费
        doCalculateProcessFee(true);
        // 更新加工费
        updateProcessFee();
        // 更新中药处方的usageInfo
        updateUsageInfo();
    }

    private void updateUsageInfo() {
        if (CollectionUtils.isEmpty(processInfoViews)) {
            return;
        }
        boolean isContainProcess = ChargeUtils.getChargeSheetItems(chargeSheet)
                .stream()
                .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.PROCESS && chargeFormItem.getIsDeleted() == 0)
                .anyMatch(chargeFormItem -> chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.UNCHARGED);
        if (!isContainProcess) {
            return;
        }
        Map<String, ProcessInfoView> processInfoMap = processInfoViews.stream()
                .filter(ProcessInfoView::getChecked)
                .filter(processInfoView -> StringUtils.isNotBlank(processInfoView.getChargeFormId()))
                .collect(Collectors.toMap(ProcessInfoView::getChargeFormId, Function.identity()));
        formProcessors.stream()
                .filter(formProcessor -> formProcessor.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE)
                .forEach(formProcessor -> {
                    ProcessInfoView processInfoView = processInfoMap.getOrDefault(formProcessor.getChargeFormId(), null);
                    UsageInfo usageInfo = JsonUtils.readValue(formProcessor.getUsageInfoJson(), UsageInfo.class);
                    formProcessor.setUsageInfoJson(JsonUtils.dump(fillProcessInfoToUsageInfo(usageInfo, processInfoView)));
                });
    }

    private void updateUsageInfoProcessUsage() {

        Map<String, String> processUsageNameMap = chargeSheet.getChargeForms().stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.PROCESS && chargeForm.getStatus() == Constants.ChargeFormStatus.UNCHARGED)
                .filter(chargeForm -> chargeForm.getProcessInfo() != null)
                .filter(chargeForm -> org.apache.commons.lang3.StringUtils.isNotEmpty(chargeForm.getProcessInfo().getChargeFormId()))
                .collect(Collectors.toMap(chargeForm -> chargeForm.getProcessInfo().getChargeFormId(),
                        chargeForm -> Optional.ofNullable(chargeForm.getChargeFormItems())
                                .orElse(new ArrayList<>())
                                .stream()
                                .filter(item -> item.getIsDeleted() == 0)
                                .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.PROCESS)
                                .findFirst()
                                .map(ChargeFormItem::getName)
                                .orElse(Constants.SystemProductId.PROCESS_NAME),
                        (a, b) -> a));

        if (MapUtils.isEmpty(processUsageNameMap)) {
            return;
        }
        formProcessors.stream()
                .filter(formProcessor -> formProcessor.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE)
                .forEach(formProcessor -> {
                    String processUsage = processUsageNameMap.getOrDefault(formProcessor.getChargeFormId(), null);
                    if (StringUtils.isEmpty(processUsage)) {
                        return;
                    }
                    UsageInfo usageInfo = JsonUtils.readValue(formProcessor.getUsageInfoJson(), UsageInfo.class);

                    if (Objects.isNull(usageInfo)) {
                        return;
                    }
                    usageInfo.setProcessUsage(processUsage);
                    formProcessor.setUsageInfoJson(JsonUtils.dump(usageInfo));
                    formProcessor.getChargeForm().setUsageInfo(JsonUtils.dumpAsJsonNode(usageInfo));
                });
    }

    private UsageInfo fillProcessInfoToUsageInfo(UsageInfo usageInfo, ProcessInfoView processInfoView) {
        if (processInfoView == null) {
            return usageInfo;
        }
        if (usageInfo == null) {
            usageInfo = new UsageInfo();
        }
        usageInfo.setProcessUsage(processInfoView.getName());

        usageInfo.setUsageType(processInfoView.getType());
        usageInfo.setUsageSubType(processInfoView.getSubType());
        usageInfo.setProcessBagUnitCount(processInfoView.getBagUnitCount());
        usageInfo.setProcessBagUnitCountDecimal(processInfoView.getBagUnitCountDecimal());
        return usageInfo;
    }

    /***
     * 计算快递费
     * 前置条件，需要有快递收费的chargeForm
     * 快递费的诶费用通过 doCalculateExpressDeliveryFeeForLocal 算出来，这个函数的参数为true表示不算快递费，快递费为0
     * 后置：算出的快递费会设置到chargeForm里面
     * */
    private void updateExpressDeliveryFee(int calSource) {
        boolean needAutoCalculateExpressDeliveryFee = formProcessors.stream()
                .anyMatch(formProcessor ->
                        formProcessor.getSourceFormType() == Constants.SourceFormType.EXPRESS_DELIVERY);
        if (!needAutoCalculateExpressDeliveryFee) {
            return;
        }

        // 只有寄付才需要算快递费
        boolean isNeedUserPayDeliveryFee = false;
        if (ChargeUtils.deliveryPaidByShipper(chargeSheet)) { //寄付
            isNeedUserPayDeliveryFee = true;
        }
        BigDecimal expressDeliveryFee = doCalculateExpressDeliveryFeeForLocal(calSource, isNeedUserPayDeliveryFee);

        // 一个收费单只有一个快递收费chargeformItem，这样写的意义好像不大？？？
        ItemProcessor expressItemProcessor = formProcessors.stream()
                .filter(formProcessor -> formProcessor.getSourceFormType() == Constants.SourceFormType.EXPRESS_DELIVERY)
                .flatMap(formProcessor -> formProcessor.getItemProcessorList().stream()).findFirst().orElse(null);
        if (expressItemProcessor != null) {
            BigDecimal actualExpressDeliveryFee = expressItemProcessor.updateDeliveryPriceAutoUnitPrice(expressDeliveryFee,
                    Optional.ofNullable(localRuleExpressDeliveryInfo)
                            .map(CalculateExpressDeliveryChargeSheetRsp.ChargeRuleExpressDeliveryInfo::getFeeTypeId)
                            .orElse(GoodsConst.FeeTypeId.FEE_TYPE_ID_MEDICINE_SERVICE));
            // 更新快递费
            if (chargeSheet.getDeliveryInfo() != null) {
                chargeSheet.getDeliveryInfo().setDeliveryFee(actualExpressDeliveryFee);
            }
        }
    }


    /**
     * 计算虚拟药房的快递费
     *
     * @param calSource
     */
    private void updateVirtualPharmacyExpressDeliveryFee(int calSource) {
        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED) {
            return;
        }
        List<FormProcessor> virtualPharmacyFormProcessors = formProcessors.stream()
                .filter(formProcessor -> formProcessor.getPharmacyType() == GoodsConst.PharmacyType.VIRTUAL_PHARMACY)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(virtualPharmacyFormProcessors)) {
            return;
        }
        virtualPharmacyFormProcessors.forEach(formProcessor -> {
            BigDecimal deliveryFee = BigDecimal.ZERO;
            VirtualPharmacyCalculateDeliveryFeeDto virtualPharmacyCalculateDeliveryFeeDto = doCalculateExpressDeliveryFeeForVirtual(calSource, formProcessor);
            if (virtualPharmacyCalculateDeliveryFeeDto != null) {
                deliveryFee = virtualPharmacyCalculateDeliveryFeeDto.getExpressDeliveryFee();
            }

            BigDecimal finalDeliveryFee = deliveryFee;
            formProcessor.getItemProcessorList().stream()
                    .filter(itemProcessor -> itemProcessor.getProductType() == Constants.ProductType.EXPRESS_DELIVERY)
                    .forEach(itemProcessor -> itemProcessor.updateDeliveryPriceAutoUnitPrice(finalDeliveryFee,
                            Optional.ofNullable(virtualRuleExpressDeliveryInfo)
                                    .map(CalculateExpressDeliveryChargeSheetRsp.ChargeRuleExpressDeliveryInfo::getFeeTypeId)
                                    .orElse(GoodsConst.FeeTypeId.FEE_TYPE_ID_MEDICINE_SERVICE))
                    );
        });

        List<ChargeForm> chargeForms = virtualPharmacyFormProcessors.stream().map(FormProcessor::getChargeForm).collect(Collectors.toList());
        bindDeliveryCompany(chargeForms, getVirtualPharmacyDeliveryCompanyFunc);
    }

    private final Function<Collection<String>, Map<String, String>> getVirtualPharmacyDeliveryCompanyFunc =
            companyIds -> Optional.ofNullable(sheetProcessorInfoProvider.getDeliveryProvider().listChargeDeliveryCompanyByIds(companyIds))
                    .orElseGet(ArrayList::new)
                    .stream()
                    .collect(Collectors.toMap(ChargeDeliveryCompany::getId, ChargeDeliveryCompany::getName, (a, b) -> a));


    private void updateProcessFee() {
//        if (CollectionUtils.isEmpty(processInfoViews)) {
//            return;
//        }
        Map<String, ProcessInfoView> processInfoViewMap = ListUtils.toMap(Optional.ofNullable(processInfoViews).orElse(Lists.newArrayList()).stream().filter(processInfoView -> org.apache.commons.lang.StringUtils.isNotEmpty(processInfoView.getProcessFormId())).collect(Collectors.toList()), ProcessInfoView::getProcessFormId);
        // 更新加工费
        formProcessors.stream()
                .filter(formProcessor -> formProcessor.getSourceFormType() == Constants.SourceFormType.PROCESS)
                .forEach(formProcessor -> {
                    ProcessInfoView processInfoView = processInfoViewMap.getOrDefault(formProcessor.getChargeFormId(), null);
                    if (processInfoView != null) {
                        ChargeSheetProcessInfo chargeSheetProcessInfo = formProcessor.getChargeSheetProcessInfo();
                        Optional.ofNullable(chargeSheetProcessInfo).ifPresent(processInfo -> {
                            processInfo.setProcessFee(processInfoView.getProcessFee());
                            processInfo.setRuleInfo(processInfoView.getRuleInfo());
                            processInfo.setProcessFee(processInfoView.getProcessFee());
                            processInfo.setProcessUsageJson(JsonUtils.dump(processInfoView.getProcessUsageInfo()));
                            processInfo.setProcessUsageInfo(processInfoView.getProcessUsageInfo());
                        });
                        if (processInfoView.getIsMarkRule() == 1) {
                            formProcessor.getItemProcessorList().stream()
                                    .filter(itemProcessor -> itemProcessor.getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED)
                                    .forEach(itemProcessor -> {
                                        itemProcessor.updateProcessProductId(processInfoView.getType(), processInfoView.getSubType());
                                        itemProcessor.updateProcessSourceUnitPriceAndProductName(MathUtils.wrapBigDecimalOrZero(processInfoView.getProcessFee()), processInfoView.getName(), processInfoView.getFeeTypeIdOrDefaultMedicineService());
                                        itemProcessor.updateProcessProductSubType(processInfoView.getType());

                                    });
                        } else {
                            updateProcessDefault(formProcessor);
                        }
                    } else {
                        updateProcessDefault(formProcessor);
                    }
                });


        /**
         * 更新加工费社保信息
         */
        updateProcessShebaoInfo();

    }

    private void updateProcessDefault(FormProcessor formProcessor) {
        ChargeSheetProcessInfo chargeSheetProcessInfo = formProcessor.getChargeSheetProcessInfo();
        Optional.ofNullable(chargeSheetProcessInfo).ifPresent(processInfo -> processInfo.setProcessFee(BigDecimal.ZERO));
        formProcessor.getItemProcessorList().stream()
                .filter(itemProcessor -> itemProcessor.getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED)
                .forEach(itemProcessor -> {
                    itemProcessor.updateProcessProductId(0, 0);
                    itemProcessor.updateProcessSourceUnitPriceAndProductName(MathUtils.wrapBigDecimalOrZero(BigDecimal.ZERO), Constants.SystemProductId.PROCESS_NAME, GoodsConst.FeeTypeId.FEE_TYPE_ID_MEDICINE_SERVICE);
                    itemProcessor.updateProcessProductSubType(0);
                });
    }

    private List<CalculatePromotionItem> getCalculatePromotionItems() {
        // 非空中药房的ChargeForm 的 chargeFromItem的CalculatePromotionItem，具体哪些会被取出，在getCalculatePromotionItems函数里面
        return formProcessors.stream()
                .filter(formProcessor -> !formProcessor.isAirPharmacy())
                .flatMap(formProcessor -> formProcessor.getCalculatePromotionItems().stream()).collect(Collectors.toList());
    }

    private List<RpcGetShebaoMatchedCodesReq.GetSmartMatchedReqItem> getSmartMatchedReqItems() {
        return formProcessors.stream()
                .filter(formProcessor -> !formProcessor.isAirPharmacy())
                .flatMap(formProcessor -> formProcessor.getSmartMatchedReqItems().stream()).collect(Collectors.toList());
    }

    private List<CalculatePromotionAirPharmacyForm> getCalculatePromotionAirPharmacyForms() {
        // 空中药房或虚拟药房的ChargeForm 计算出来的打折优惠信息
        return formProcessors.stream()
                .filter(FormProcessor::isAirPharmacy)
                .map(FormProcessor::generateCalculatePromotionAirPharmacyForm) //空中药房计算优惠打折信息的核心算法在这个函数里面了
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }


    private void queryPromotionInfo(List<PatientCardPromotionReq> patientCardPromotionReqs, List<MallVerificationReq> mallVerificationReqs) {
        boolean isChargeSheetLocked = chargeSheet.getLockStatus() != Constants.ChargeSheetLockStatus.LOCK_STATUS_UNLOCK;
        List<CalculatePromotionItem> calculatePromotionItems = getCalculatePromotionItems();
        //非空中药房的ChargeForm 的 chargeFromItem的CalculatePromotionItem  的药品ID
        List<GoodsBaseInfo> goodsItems = calculatePromotionItems.stream()
                .map(CalculatePromotionItem::getGoodsBaseInfo)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<CalculatePromotionAirPharmacyForm> calculatePromotionAirPharmacyForms = getCalculatePromotionAirPharmacyForms();
        List<GoodsTypeTuple> goodsTypeTuples = new ArrayList<>(calculatePromotionAirPharmacyForms.stream()
                .map(promotionAirPharmacyForm -> {
                    GoodsTypeTuple goodsTypeTuple = new GoodsTypeTuple();
                    BeanUtils.copyProperties(promotionAirPharmacyForm, goodsTypeTuple);
                    goodsTypeTuple.setPharmacyType(GoodsConst.PharmacyType.AIR_PHARMACY);
                    return goodsTypeTuple;
                })
                .filter(Objects::nonNull) //TODO robinsli map返回的new的对象，这里好像永远为空不到，再说前面calculatePromotionAirPharmacyForms已经过滤了空了
                .collect(Collectors.toMap(CalculatePromotionAirPharmacyForm::convertToKey, Function.identity(), (a, b) -> a))
                .values());
        // 拉取这个诊所对这个病人的这些普通药和空中药房的打折优惠信息，请求服务：abc-cis-promotion-service ，/rpc/promotions/available
        AvailablePromotionRsp promotionRsp = sheetProcessorInfoProvider.getPromotionProvider().fetchPromotions(chargeSheet.getClinicId(),
                chargeSheet.getChainId(),
                memberId,
                chargeSheet.getPatientId(),
                goodsItems,
                goodsTypeTuples,
                findPromotionPatientCardIds(patientCardPromotionReqs, chargeSheet.getPatientCardPromotionInfos()));
        List<Promotion> promotions = Optional.ofNullable(promotionRsp).map(AvailablePromotionRsp::getPromotions).orElse(new ArrayList<>());

        discountPromotions = promotions.stream()
                .filter(promotion -> promotion.getType() == Promotion.Type.MEMBER_DISCOUNT
                        || promotion.getType() == Promotion.Type.NORMAL_DISCOUNT
                        || promotion.getType() == Promotion.Type.CARD)
                .collect(Collectors.toList());

        couponPromotions = promotions.stream()
                .filter(promotion -> promotion.getType() == Promotion.Type.COUPON)
                .collect(Collectors.toList());

        giftRulePromotions = promotions.stream()
                .filter(promotion -> promotion.getType() == Promotion.Type.GIFT_RULE)
                .collect(Collectors.toList());

        patientCardPromotions = promotions.stream()
                .filter(promotion -> promotion.getType() == Promotion.Type.CARD)
                .collect(Collectors.toList());

        // 查询积分的规则
        List<CalculatePromotionItem> localPharmacyItems = Optional.of(calculatePromotionItems)
                .orElse(new ArrayList<>())
                .stream()
                .filter(calculatePromotionItem -> calculatePromotionItem.getPharmacyType() == GoodsPharmacyView.PharmacyType.LOCAL_PHARMACY)
                .collect(Collectors.toList());

        List<PointPromotionReq.GoodsInfo> goodsInfos = localPharmacyItems.stream()
                .filter(item -> Objects.nonNull(item.getGoodsBaseInfo()))
                .map(item -> {
                    GoodsBaseInfo goodsBaseInfo = item.getGoodsBaseInfo();
                    PointPromotionReq.GoodsInfo goodsInfo = new PointPromotionReq.GoodsInfo();
                    return goodsInfo.setGoodsId(goodsBaseInfo.getGoodsId())
                            .setGoodsType(goodsBaseInfo.getGoodsType())
                            .setGoodsSubType(goodsBaseInfo.getGoodsSubType())
                            .setGoodsCMSpec(goodsBaseInfo.getGoodsCMSpec());
                }).collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                                new TreeSet<>(Comparator.comparing(PointPromotionReq.GoodsInfo::getGoodsId))),
                        ArrayList::new));

        //查询积分及积分抵扣项目的规则
        if (!isChargeSheetLocked) {
            pointPromotionRsp = sheetProcessorInfoProvider.getPatientInfoProvider().fetchPatientPointPromotions(chargeSheet.getChainId(), chargeSheet.getPatientId(), goodsInfos);
        }


        /**
         * 查询核销信息
         */
        if (!CollectionUtils.isEmpty(mallVerificationReqs) || !CollectionUtils.isEmpty(chargeSheet.getChargeVerifyInfos())) {
            List<String> verificationCodes = Optional.ofNullable(mallVerificationReqs).orElse(new ArrayList<>()).stream().flatMap(mallVerificationReq -> mallVerificationReq.getVerificationCodes().stream()).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(verificationCodes) && !CollectionUtils.isEmpty(chargeSheet.getChargeVerifyInfos())) {
                verificationCodes = chargeSheet.getChargeVerifyInfos()
                        .stream()
                        .filter(chargeVerifyInfo -> chargeVerifyInfo.getIsDeleted() == 0)
                        .flatMap(chargeVerifyInfo -> chargeVerifyInfo.getItemVerifyDetails().stream())
                        .flatMap(itemVerifyDetail -> itemVerifyDetail.getVerifyInfoDetails().stream())
                        .map(ItemVerifyDetail.VerifyInfoDetail::getVerificationCode).distinct().collect(Collectors.toList());
            }

            verificationInfo = sheetProcessorInfoProvider.getCisMallOrderProvider().getVerificationInfo(chainId, clinicId, verificationCodes);

            if (!CollectionUtils.isEmpty(verificationInfo)) {
                verificationInfo = verificationInfo.stream().filter(verificationItemView -> verificationItemView.getStatus() == VerificationItemView.Status.UN_VERIFICATION).collect(Collectors.toList());
            }
        }

    }

    /**
     * 处理打折促销
     *
     * @param discountPromotionReqs 端上请求带的促销信息
     *                              couponPromotionReqs端上请求带的团购促销信息
     *                              giftRulePromotionReqs 端上请求带的礼品促销信息
     */
    private ApplyPromotionInfoResult fetchAndApplyPromotionInfo(List<PromotionReq> discountPromotionReqs,
                                                                List<CouponPromotionReq> couponPromotionReqs,
                                                                List<GiftRulePromotionReq> giftRulePromotionReqs,
                                                                List<PatientCardPromotionReq> patientCardPromotionReqs,
                                                                List<PatientPointDeductProductPromotionReq> patientPointDeductProductPromotionReqs,
                                                                List<MallVerificationReq> mallVerificationReqs,
                                                                PatientPointsInfoReq patientPointsInfoReq,
                                                                BigDecimal expectedAdjustmentFee,
                                                                BigDecimal expectedOddFee) {
        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED || Constants.RuleTag.ifContainsTag(Optional.ofNullable(chargeSheet.getAdditional()).map(ChargeSheetAdditional::getRuleTag).orElse(0), Constants.RuleTag.notNeedDisCount)) {
            return new ApplyPromotionInfoResult();
        }
        // 查询出所有的营销信息
        queryPromotionInfo(patientCardPromotionReqs, mallVerificationReqs);


        //先检查下是否需要按批次锁库
        if (!getIsNeedUseLimitPrice()) {
            //需要清空医保限价相关的字段
            formProcessors.forEach(formProcessor -> {
                formProcessor.clearLimitInfo();
                formProcessor.clearListingDiscountFee();
            });
        }
        BigDecimal adjustmentFeeOnCalculate = BigDecimal.ZERO;
        // 这里最多循环item个数的次数，防止死循环
        int cycleCount = (int) formProcessors.stream().mapToLong(formProcessor -> formProcessor.getItemProcessorList().size()).sum();

        //不管如何，先走一次匹配营销及算费
        adjustmentFeeOnCalculate = fetchPromotionAndCalculateReceivableFee(discountPromotionReqs,
                couponPromotionReqs,
                giftRulePromotionReqs,
                patientCardPromotionReqs,
                patientPointDeductProductPromotionReqs,
                mallVerificationReqs,
                patientPointsInfoReq,
                expectedAdjustmentFee,
                expectedOddFee,
                true);

        if (getIsNeedUseLimitPrice()) {
            CalculateShebaoPriceResult calculateShebaoPriceResult = null;

            if (getIsEnableListingPrice()) {
                //先循环处理挂网价限价
                calculateShebaoPriceResult = cycleFetchAndApplyShebaoListingPrice(discountPromotionReqs,
                        couponPromotionReqs,
                        giftRulePromotionReqs,
                        patientCardPromotionReqs,
                        patientPointDeductProductPromotionReqs,
                        mallVerificationReqs,
                        patientPointsInfoReq,
                        expectedAdjustmentFee,
                        expectedOddFee,
                        adjustmentFeeOnCalculate,
                        cycleCount);
                expectedAdjustmentFee = calculateShebaoPriceResult.getExpectedAdjustmentFee();
                expectedOddFee = calculateShebaoPriceResult.getExpectedOddFee();
                adjustmentFeeOnCalculate = calculateShebaoPriceResult.getAdjustmentFeeOnCalculate();
            }


            //再循环处理医保规则限价
            calculateShebaoPriceResult = cycleFetchAndApplyShebaoLimitPrice(discountPromotionReqs,
                    couponPromotionReqs,
                    giftRulePromotionReqs,
                    patientCardPromotionReqs,
                    patientPointDeductProductPromotionReqs,
                    mallVerificationReqs,
                    patientPointsInfoReq,
                    expectedAdjustmentFee,
                    expectedOddFee,
                    adjustmentFeeOnCalculate,
                    cycleCount);

            adjustmentFeeOnCalculate = calculateShebaoPriceResult.getAdjustmentFeeOnCalculate();
        }


        //更新sheet维度上的社保应收
        sheBaoReceivableFee = MathUtils.setScaleTwo(formProcessors.stream()
                .map(FormProcessor::getSheBaoReceivableFee)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        return new ApplyPromotionInfoResult().setAdjustmentFeeOnCalculate(adjustmentFeeOnCalculate);

    }

    private boolean getIsEnableListingPrice() {
        return sheetProcessorInfoProvider.getShebaoInfoProvider().isEnableListingPrice(chargeSheet.getClinicId());
    }

    private CalculateShebaoPriceResult cycleFetchAndApplyShebaoLimitPrice(List<PromotionReq> discountPromotionReqs,
                                                                          List<CouponPromotionReq> couponPromotionReqs,
                                                                          List<GiftRulePromotionReq> giftRulePromotionReqs,
                                                                          List<PatientCardPromotionReq> patientCardPromotionReqs,
                                                                          List<PatientPointDeductProductPromotionReq> patientPointDeductProductPromotionReqs,
                                                                          List<MallVerificationReq> mallVerificationReqs,
                                                                          PatientPointsInfoReq patientPointsInfoReq,
                                                                          BigDecimal expectedAdjustmentFee,
                                                                          BigDecimal expectedOddFee,
                                                                          BigDecimal adjustmentFeeOnCalculate,
                                                                          int cycleCount) {

        //再循环处理医保限价
        for (int i = 0; i <= cycleCount; i++) {
            // 构造需要计算限价的item
            List<CalculateLimitPriceItem> calculateLimitPriceItems = formProcessors.stream()
                    .flatMap(formProcessor -> formProcessor.getItemProcessorList().stream())
                    .filter(itemProcessor -> itemProcessor.getCalIsUseLimitPrice() != 1 && itemProcessor.getIsGift() == Constants.ChargeFormItemGiftType.NOT_GIFT)
                    .map(ItemProcessor::generateCalculateLimitPriceItem)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            //计算限价并更新到item上
            boolean limitPriceReCalculate = fetchAndCalculateLimitPrice(calculateLimitPriceItems);

            if (limitPriceReCalculate) {
                calculateOddFee = false;
                //如果有限价的话，需要将议价值清零
                expectedAdjustmentFee = BigDecimal.ZERO;
                expectedOddFee = BigDecimal.ZERO;
                if (patientPointsInfoReq == null) {
                    patientPointsInfoReq = new PatientPointsInfoReq();
                }
                patientPointsInfoReq.setChecked(false);
                adjustmentFeeOnCalculate = fetchPromotionAndCalculateReceivableFee(discountPromotionReqs,
                        couponPromotionReqs,
                        giftRulePromotionReqs,
                        patientCardPromotionReqs,
                        patientPointDeductProductPromotionReqs,
                        mallVerificationReqs,
                        patientPointsInfoReq,
                        expectedAdjustmentFee,
                        expectedOddFee,
                        false);
            } else {
                break;
            }
        }

        return new CalculateShebaoPriceResult()
                .setExpectedOddFee(expectedOddFee)
                .setExpectedAdjustmentFee(expectedAdjustmentFee)
                .setAdjustmentFeeOnCalculate(adjustmentFeeOnCalculate);
    }

    private CalculateShebaoPriceResult cycleFetchAndApplyShebaoListingPrice(List<PromotionReq> discountPromotionReqs,
                                                                            List<CouponPromotionReq> couponPromotionReqs,
                                                                            List<GiftRulePromotionReq> giftRulePromotionReqs,
                                                                            List<PatientCardPromotionReq> patientCardPromotionReqs,
                                                                            List<PatientPointDeductProductPromotionReq> patientPointDeductProductPromotionReqs,
                                                                            List<MallVerificationReq> mallVerificationReqs,
                                                                            PatientPointsInfoReq patientPointsInfoReq,
                                                                            BigDecimal expectedAdjustmentFee,
                                                                            BigDecimal expectedOddFee,
                                                                            BigDecimal adjustmentFeeOnCalculate,
                                                                            int cycleCount) {


        for (int i = 0; i <= cycleCount; i++) {

            boolean shebaoListingPriceReCalculate = false;
            //计算挂网价限价并更新到item上

            shebaoListingPriceReCalculate = fetchAndCalculateShebaoListingPrice();

            if (shebaoListingPriceReCalculate) {
                calculateOddFee = false;
                expectedAdjustmentFee = BigDecimal.ZERO;
                expectedOddFee = BigDecimal.ZERO;
                if (patientPointsInfoReq == null) {
                    patientPointsInfoReq = new PatientPointsInfoReq();
                }
                patientPointsInfoReq.setChecked(false);

                adjustmentFeeOnCalculate = fetchPromotionAndCalculateReceivableFee(discountPromotionReqs,
                        couponPromotionReqs,
                        giftRulePromotionReqs,
                        patientCardPromotionReqs,
                        patientPointDeductProductPromotionReqs,
                        mallVerificationReqs,
                        patientPointsInfoReq,
                        expectedAdjustmentFee,
                        expectedOddFee, false);
            } else {
                //如果没有触发挂网价限价，直接跳出循环
                break;
            }
        }

        return new CalculateShebaoPriceResult()
                .setExpectedOddFee(expectedOddFee)
                .setExpectedAdjustmentFee(expectedAdjustmentFee)
                .setAdjustmentFeeOnCalculate(adjustmentFeeOnCalculate);
    }

    private BigDecimal fetchPromotionAndCalculateReceivableFee(List<PromotionReq> discountPromotionReqs,
                                                               List<CouponPromotionReq> couponPromotionReqs,
                                                               List<GiftRulePromotionReq> giftRulePromotionReqs,
                                                               List<PatientCardPromotionReq> patientCardPromotionReqs,
                                                               List<PatientPointDeductProductPromotionReq> patientPointDeductProductPromotionReqs,
                                                               List<MallVerificationReq> mallVerificationReqs,
                                                               PatientPointsInfoReq patientPointsInfoReq,
                                                               BigDecimal expectedAdjustmentFee,
                                                               BigDecimal expectedOddFee,
                                                               boolean resetWithPromotion) {
        // 匹配折扣
        fetchAndApplyPromotionInfoCore(getCalculatePromotionItems(),
                getCalculatePromotionAirPharmacyForms(),
                patientCardPromotionReqs,
                patientPointDeductProductPromotionReqs,
                discountPromotionReqs,
                giftRulePromotionReqs,
                couponPromotionReqs,
                mallVerificationReqs,
                patientPointsInfoReq,
                resetWithPromotion);
        // 计算议价
        BigDecimal adjustmentFeeOnCalculate = calculateAdjustFee(expectedAdjustmentFee,
                expectedOddFee, chargeSheet.getDraftAdjustmentFee());
        // 将议价平摊到item上
        updateReceivableFee(adjustmentFeeOnCalculate);
        return adjustmentFeeOnCalculate;
    }


    private boolean fetchAndCalculateShebaoListingPrice() {

        boolean reCalculate = false;

        List<CalculateLimitPriceItem> calculateLimitPriceItems = formProcessors.stream()
                .flatMap(formProcessor -> formProcessor.getItemProcessorList().stream())
                .filter(itemProcessor -> itemProcessor.getChargeFormItem().getSourceItemType() == Constants.SourceItemType.NORMAL
                        && itemProcessor.getIsGift() == Constants.ChargeFormItemGiftType.NOT_GIFT
                        && !itemProcessor.containListingPrice()
                )
                .map(ItemProcessor::generateCalculateLimitPriceItemForListingPrice)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "before calculate fetchAndCalculateShebaoListingPrice: {}", JsonUtils.dump(calculateLimitPriceItems));

        //匹配限价
        LimitPriceProcessor calculateListingLimitPriceProcessor = new LimitPriceProcessor();
        calculateListingLimitPriceProcessor.process(calculateLimitPriceItems);

        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "after calculate fetchAndCalculateShebaoListingPrice: {}", JsonUtils.dump(calculateLimitPriceItems));

        Map<String, CalculateLimitPriceItem> calculateLimitPriceItemMap = ListUtils.toMap(calculateLimitPriceItems, CalculateLimitPriceItem::getId);

        reCalculate = calculateLimitPriceItems.stream()
                .anyMatch(CalculateLimitPriceItem::receivableTotalPriceChanged);

        formProcessors.stream()
                .flatMap(formProcessor -> formProcessor.getItemProcessorList().stream())
                .forEach(itemProcessor -> itemProcessor.applyItemListingPrice(calculateLimitPriceItemMap.get(itemProcessor.getItemId())));

        return reCalculate;
    }


    /**
     * 计算限价
     *
     * @param calculateLimitPriceItems
     */
    private boolean fetchAndCalculateLimitPrice(List<CalculateLimitPriceItem> calculateLimitPriceItems) {
        /**
         * 是否需要重新计算
         */
        boolean reCalculate = false;
        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED || !getIsNeedUseLimitPrice()) {
            return reCalculate;
        }

        if (org.apache.commons.collections.CollectionUtils.isEmpty(calculateLimitPriceItems)) {
            return reCalculate;
        }

        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "before limit calculateLimitPriceItems: {}", JsonUtils.dump(calculateLimitPriceItems));

        //匹配限价
        LimitPriceProcessor limitPriceProcessor = new LimitPriceProcessor();
        limitPriceProcessor.process(calculateLimitPriceItems);

        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "after limit calculateLimitPriceItems: {}", JsonUtils.dump(calculateLimitPriceItems));
        reCalculate = calculateLimitPriceItems.stream()
                .anyMatch(CalculateLimitPriceItem::receivableTotalPriceChanged);

        Map<String, CalculateLimitPriceItem> calculateLimitPriceItemMap = ListUtils.toMap(calculateLimitPriceItems, CalculateLimitPriceItem::getId);


        reCalculate = calculateLimitPriceItems.stream()
                .anyMatch(CalculateLimitPriceItem::receivableTotalPriceChanged);

        formProcessors.stream()
                .flatMap(formProcessor -> formProcessor.getItemProcessorList().stream())
                .forEach(itemProcessor -> itemProcessor.applyItemLimitPrice(calculateLimitPriceItemMap.get(itemProcessor.getItemId())));


        return reCalculate;
    }


    private void fetchAndApplyPromotionInfoCore(List<CalculatePromotionItem> calculatePromotionItems,
                                                List<CalculatePromotionAirPharmacyForm> calculatePromotionAirPharmacyForms,
                                                List<PatientCardPromotionReq> patientCardPromotionReqs,
                                                List<PatientPointDeductProductPromotionReq> patientPointDeductProductPromotionReqs,
                                                List<PromotionReq> discountPromotionReqs,
                                                List<GiftRulePromotionReq> giftRulePromotionReqs,
                                                List<CouponPromotionReq> couponPromotionReqs,
                                                List<MallVerificationReq> mallVerificationReqs,
                                                PatientPointsInfoReq patientPointsInfoReq,
                                                boolean resetWithPromotion) {
        /**
         * 营销匹配的顺序
         * 0、核销抵扣
         * 1、卡项抵扣商品
         * 2、积分抵扣商品
         * 3、营销折扣和卡项折扣
         * 4、满减满赠
         * 5、优惠券
         * 6、积分抵现金
         */
        boolean isChargeSheetLocked = chargeSheet.getLockStatus() != Constants.ChargeSheetLockStatus.LOCK_STATUS_UNLOCK;
        // 积分抵扣只能抵扣本地药房的item
        List<CalculatePromotionItem> localPharmacyItems = Optional.ofNullable(calculatePromotionItems)
                .orElse(new ArrayList<>())
                .stream()
                .filter(calculatePromotionItem -> calculatePromotionItem.getPharmacyType() == GoodsPharmacyView.PharmacyType.LOCAL_PHARMACY)
                .collect(Collectors.toList());

        int afterGoodsDeductPoints = Optional.ofNullable(pointPromotionRsp).map(PointPromotionRsp::getPoints).orElse(0);
        //0.核销计算抵扣
        if (isChargeSheetLocked) {
            fetchExistedVerifyPromotion(calculatePromotionItems);
        } else {
            fetchVerifyPromotion(calculatePromotionItems, verificationInfo, mallVerificationReqs);
        }

        //1.计算营销的抵扣
        if (isChargeSheetLocked) {
            //直接从数据库中去匹配卡项抵扣信息
            fetchExistedPatientCardPromotion(calculatePromotionItems);
            //直接从数据库中去匹配积分抵扣信息
            fetchExistedPatientPointsDeductGoodsPromotion(localPharmacyItems);
        } else {
            //卡项项目抵扣匹配
            fetchPatientCardPromotionInfo(patientCardPromotions, calculatePromotionItems, calculatePromotionAirPharmacyForms, patientCardPromotionReqs);
            //积分项目抵扣匹配
            afterGoodsDeductPoints = fetchPatientPointsDeductGoodsPromotion(localPharmacyItems, pointPromotionRsp, patientPointDeductProductPromotionReqs);
        }

        //将折扣中卡项被反选的卡项id剔除掉
        List<String> uncheckedPatientCardIds = Optional.ofNullable(availablePatientCardPromotionViews).orElse(new ArrayList<>()).stream()
                .filter(patientCardPromotionView -> !patientCardPromotionView.getChecked())
                .map(PatientCardPromotionView::getId)
                .distinct()
                .collect(Collectors.toList());
        discountPromotions = Optional.ofNullable(discountPromotions).orElse(new ArrayList<>())
                .stream()
                .filter(promotion -> !uncheckedPatientCardIds.contains(promotion.getId()))
                .collect(Collectors.toList());

        //计算营销的和卡项的折扣
        fetchDiscountPromotionInfo(discountPromotions, calculatePromotionItems, calculatePromotionAirPharmacyForms, discountPromotionReqs);

        //计算优惠券
        if (isChargeSheetLocked) {
            //异步支付并且还在锁单中，表示优惠券在锁单时已经扣除了，这时只需要从数据库中查询出优惠信息绑定到item上即可
            fetchExistedCouponPromotion(calculatePromotionItems, calculatePromotionAirPharmacyForms);
        } else {
            fetchCouponPromotionInfo(couponPromotions, calculatePromotionItems, calculatePromotionAirPharmacyForms, couponPromotionReqs);
        }

        //计算满减满赠
        fetchGiftRulePromotionInfo(giftRulePromotions, calculatePromotionItems, calculatePromotionAirPharmacyForms, giftRulePromotionReqs, chargeSheet.getPatientId());

        //将折扣信息均摊到item上
        Map<String, CalculatePromotionItem> calculatePromotionItemMap = calculatePromotionItems.stream().collect(Collectors.toMap(CalculatePromotionItem::getId, Function.identity(), (a, b) -> a));
        Map<String, CalculatePromotionAirPharmacyForm> calculatePromotionAirPharmacyFormMap = calculatePromotionAirPharmacyForms.stream()
                .collect(Collectors.toMap(CalculatePromotionAirPharmacyForm::getId, Function.identity(), (a, b) -> a));

        formProcessors.forEach(formProcessor -> formProcessor.applyPromotion(calculatePromotionItemMap, calculatePromotionAirPharmacyFormMap, resetWithPromotion));
        // 计算积分使用
        fetchAndApplyPatientPointsDiscountFee(patientPointsInfoReq, isChargeSheetLocked, pointPromotionRsp, afterGoodsDeductPoints);
    }


//    private void applyLimitPrice(List<PromotionReq> discountPromotionReqs,
//                                 List<CouponPromotionReq> couponPromotionReqs,
//                                 List<CouponPromotionReq> giftRulePromotionReqs,
//                                 List<PatientCardPromotionReq> patientCardPromotionReqs,
//                                 List<PatientPointDeductProductPromotionReq> patientPointDeductProductPromotionReqs,
//                                 PatientPointsInfoReq patientPointsInfoReq,
//                                 BigDecimal expectedAdjustmentFee,
//                                 BigDecimal expectedOddFee) {
//
//        AtomicBoolean noLimitLoop = new AtomicBoolean(false);
//
//        if (!getIsNeedUseLimitPrice()) {
//            noLimitLoop.set(true);
//        }
//
//        //查询出所有的营销信息
//        QueryPromotionRsp queryPromotionRsp = queryPromotionInfo(patientCardPromotionReqs);
//
//        //进行预锁库
//        GoodsConfigView goodsConfig = sheetProcessorInfoProvider.getCisGoodsProvider().getGoodsConfig(clinicId);
//
////        GoodsLockingSheet goodsLockingSheet = null;
//        List<ChargeMedicareLimitPriceType> limitPriceTypes = null;
//        List<ChargeMedicareLimitPriceProduct> limitPriceProducts = null;
////        Map<String, GoodsLockingFormItem> lockingFormItemMap = new HashMap<>();
//        //先检查下是否需要按批次锁库
//        if (sheetProcessorInfoProvider.getCisGoodsProvider().isLockByBatch(goodsConfig)) {

    /// /            goodsLockingSheet = sheetProcessorInfoProvider.getGoodsLockingProvider().tryPreLockByChargeSheets(chainId, clinicId, operatorId, chargeSheet, goodsConfig);
    /// /            List<GoodsLockingFormItem> lockingFormItems = goodsLockingSheet.getLockingForms().stream().flatMap(goodsLockingForm -> goodsLockingForm.getLockingFormItems().stream())
    /// /                    .collect(Collectors.toList());
    /// /
    /// /            lockingFormItemMap = ListUtils.toMap(lockingFormItems, GoodsLockingFormItem::getRefId);
//
//            limitPriceTypes = sheetProcessorInfoProvider.getLimitPriceProvider().getLimitPriceTypes(chainId, clinicId);
//            limitPriceProducts = sheetProcessorInfoProvider.getLimitPriceProvider().getLimitPriceProducts(chainId, clinicId);
//        } else {
//            noLimitLoop.set(true);
//        }
//
//
//        //这里最多循环item个数的次数，防止死循环
//        int maxLoop = formProcessors.stream().flatMap(formProcessor -> formProcessor.getItemProcessorList().stream()).collect(Collectors.toList()).size();
//        for (int i = 0; i < maxLoop; i++) {
//
//            CalculatePromotionResult caculatePromotionInfo = caculatePromotionInfo(queryPromotionRsp.getPointPromotionRsp(),
//                    queryPromotionRsp.isChargeSheetLocked(),
//                    getCalculatePromotionItems(),
//                    queryPromotionRsp.getLocalPharmacyItems(),
//                    getCalculatePromotionAirPharmacyForms(),
//                    patientCardPromotionReqs,
//                    patientPointDeductProductPromotionReqs,
//                    discountPromotionReqs,
//                    giftRulePromotionReqs,
//                    couponPromotionReqs);
//
//
//            formProcessors.stream().forEach(formProcessor -> formProcessor.applyPromotion(caculatePromotionInfo.getCalculatePromotionItemMap(), caculatePromotionInfo.getCalculatePromotionAirPharmacyFormMap()));
//
//            //计算积分使用
//            fetchAndApplyPatientPointsDiscountFee(patientPointsInfoReq, queryPromotionRsp.isChargeSheetLocked(), queryPromotionRsp.getPointPromotionRsp(), caculatePromotionInfo.getAfterGoodsDeductPoints());
//
//            BigDecimal adjustmentFeeOnCalculate = calculateAdjustFee(expectedAdjustmentFee,
//                    MathUtils.wrapBigDecimalOrZero(chargeSheet.getOutpatientAdjustmentFee()),
//                    expectedOddFee, chargeSheet.getDraftAdjustmentFee());
//
//            updateReceivableFee(adjustmentFeeOnCalculate);
//
//            if (noLimitLoop.get()) {
//                break;
//            }
//            noLimitLoop.set(true);
//
//            //计算没有被限价的LimitPriceItem
//            List<LimitPriceItem> limitPriceItems = formProcessors.stream().flatMap(formProcessor -> formProcessor.getItemProcessorList().stream())
//                    .filter(itemProcessor -> itemProcessor.getIsUseLimitPrice() != 1)
//                    .map(itemProcessor -> itemProcessor.generateLimitPriceCaculateInfo())
//                    .collect(Collectors.toList());
//            Map<String, LimitPriceItem> limitPriceItemMap = ListUtils.toMap(limitPriceItems, LimitPriceItem::getChargeItemId);
//            sheetProcessorInfoProvider.getLimitPriceProvider().fetchLimitPriceGoodsItemByBatch(chainId, clinicId, limitPriceItems, limitPriceProducts, limitPriceTypes);
//
//            //如果有限价的话，需要重新计算一次优惠
//            formProcessors.stream().flatMap(formProcessor -> formProcessor.getItemProcessorList().stream())
//                    .filter(itemProcessor -> limitPriceItemMap.get(itemProcessor.getChargeFormId()) != null)
//                    .forEach(itemProcessor -> {
//                        itemProcessor.updateLimitInfo();
//
//                        if (itemProcessor.getIsUseLimitPrice() == 1) {
//                            noLimitLoop.set(false);
//                        }
//                    });
//
//            if (noLimitLoop.get()) {
//                break;
//            }
//        }
//
//
//
//
//    }
    private List<String> findPromotionPatientCardIds(List<PatientCardPromotionReq> patientCardPromotionReqs, List<ChargePatientCardPromotionInfo> patientCardPromotionInfos) {
        Set<String> patientCardsIds = new HashSet<>();
        patientCardsIds.addAll(Optional.ofNullable(patientCardPromotionInfos)
                .orElse(new ArrayList<>())
                .stream()
                .filter(patientCardPromotionInfo -> patientCardPromotionInfo.getIsDeleted() == 0)
                .filter(patientCardPromotionInfo -> patientCardPromotionInfo.getChecked()
                        && patientCardPromotionInfo.getIsOutOfUseRangeCard() == 1
                        && org.apache.commons.lang3.StringUtils.isNotEmpty(patientCardPromotionInfo.getPromotionId())
                )
                .map(ChargePatientCardPromotionInfo::getPromotionId)
                .collect(Collectors.toSet())
        );
        patientCardsIds.addAll(Optional.ofNullable(patientCardPromotionReqs)
                .orElse(new ArrayList<>())
                .stream()
                .filter(PatientCardPromotionReq::getChecked)
                .map(PatientCardPromotionReq::getId).collect(Collectors.toSet())
        );
        return new ArrayList<>(patientCardsIds);
    }

    private void fetchExistedPatientPointsDeductGoodsPromotion(List<CalculatePromotionItem> items) {
        if (Objects.isNull(chargeSheet.getPatientPointsDeductProductPromotionInfo())) {
            return;
        }
        availablePatientPointDeductProductPromotionViews = Optional.ofNullable(chargeSheet.getPatientPointsDeductProductPromotionInfo().getDeductItems())
                .orElse(new ArrayList<>())
                .stream()
                .filter(Objects::nonNull)
                .filter(patientPointDeductItem -> patientPointDeductItem.getStatus() == PatientPointDeductProductItem.Status.DEDUCTED)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(availablePatientPointDeductProductPromotionViews)) {
            return;
        }
        Map<String, Map<String, ItemDeductedDetail>> chargeFormItemIdItemDeductedDetailMap = availablePatientPointDeductProductPromotionViews.stream()
                .filter(PatientPointDeductProductPromotionView::getChecked)
                .filter(patientPointDeductPromotionView -> org.apache.commons.collections.CollectionUtils.isNotEmpty(patientPointDeductPromotionView.getItemDeductedDetails()))
                .filter(promotionDeductItem -> org.apache.commons.collections.CollectionUtils.isNotEmpty(promotionDeductItem.getItemDeductedDetails()))
                .collect(Collectors.toMap(PatientPointDeductProductPromotionView::getGoodsId,
                        patientPointDeductPromotionView -> patientPointDeductPromotionView.getItemDeductedDetails()
                                .stream()
                                .collect(Collectors.toMap(ItemDeductedDetail::getId, Function.identity(), (a, b) -> a)),
                        (a, b) -> a)
                );

        items.forEach(item -> {
            Map<String, ItemDeductedDetail> itemDeductedDetailMap = chargeFormItemIdItemDeductedDetailMap.get(item.getProductId());
            if (MapUtils.isEmpty(itemDeductedDetailMap)) {
                return;
            }
            ItemDeductedDetail itemDeductedDetail = itemDeductedDetailMap.get(item.getId());
            if (Objects.isNull(itemDeductedDetail)) {
                return;
            }
            int deductedCount = itemDeductedDetail.getDeductedCount();
            item.setPointDeductedTotalCount(item.getPointDeductedTotalCount() + deductedCount);
            BigDecimal itemDeductedPrice = itemDeductedDetail.getDeductedPrice();
            if (itemDeductedPrice == null) {
                itemDeductedPrice = MathUtils.wrapBigDecimalOrZero(item.getUnitPrice()).multiply(new BigDecimal(itemDeductedDetail.getDeductedCount()).negate());
            }
            item.setPointDeductedTotalPrice(MathUtils.wrapBigDecimalAdd(item.getPointDeductedTotalPrice(), itemDeductedPrice));
            item.addPromotion(new PromotionSimple()
                    .setPromotionId(itemDeductedDetail.getPromotionId())
                    .setPromotionName(itemDeductedDetail.getPromotionName())
                    .setDeductedCount(itemDeductedDetail.getDeductedCount())
                    .setAvailableDeductTotalCount(itemDeductedDetail.getAvailableDeductCount())
                    .setPresentId(itemDeductedDetail.getPresentId())
                    .setDiscountPrice(itemDeductedPrice)
                    .setType(PromotionSimple.Type.PATIENT_POINT_DEDUCT)
                    .setBatchInfos(itemDeductedDetail.getBatchInfos())
            );
        });
    }


    /**
     * 锁单之后只处理已抵扣的信息
     *
     * @param calculatePromotionItems
     */
    private void fetchExistedPatientCardPromotion(List<CalculatePromotionItem> calculatePromotionItems) {
        if (CollectionUtils.isEmpty(chargeSheet.getPatientCardPromotionInfos())) {
            return;
        }
        // 处理已经暂存的优惠信息
        availablePatientCardPromotionViews = chargeSheet.getPatientCardPromotionInfos().stream()
                .filter(patientCardPromotionInfo -> patientCardPromotionInfo.getIsDeleted() == 0)
                .map(PatientCardPromotionView::ofPatientCardPromotionView)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "availablePatientCardPromotionViews: {}", JsonUtils.dump(availablePatientCardPromotionViews));
        if (CollectionUtils.isEmpty(availablePatientCardPromotionViews)) {
            return;
        }
        Map<String, List<ItemDeductedDetail>> chargeFormItemIdItemDeductedDetailMap = availablePatientCardPromotionViews.stream()
                .filter(PatientCardPromotionView::getChecked)
                .filter(patientCardPromotionView -> org.apache.commons.collections.CollectionUtils.isNotEmpty(patientCardPromotionView.getDeductItems()))
                .flatMap(patientCardPromotionView -> patientCardPromotionView.getDeductItems().stream())
                .filter(PromotionDeductItem::getChecked)
                .filter(promotionDeductItem -> promotionDeductItem.getStatus() == PromotionDeductItem.Status.DEDUCTED)
                .filter(promotionDeductItem -> org.apache.commons.collections.CollectionUtils.isNotEmpty(promotionDeductItem.getItemDeductedDetails()))
                .flatMap(promotionDeductItem -> promotionDeductItem.getItemDeductedDetails().stream())
                .collect(Collectors.groupingBy(ItemDeductedDetail::getId));
        // 将数据库的抵扣信息应用到item
        calculatePromotionItems.forEach(item -> {
            List<ItemDeductedDetail> itemDeductedDetails = chargeFormItemIdItemDeductedDetailMap.getOrDefault(item.getId(), new ArrayList<>());
            if (CollectionUtils.isEmpty(itemDeductedDetails)) {
                return;
            }
            List<PromotionSimple> promotionSimples = itemDeductedDetails.stream()
                    .map(itemDeductedDetail -> {
                        BigDecimal itemDeductedPrice = itemDeductedDetail.getDeductedPrice();
                        if (itemDeductedPrice == null) {
                            itemDeductedPrice = MathUtils.wrapBigDecimalOrZero(item.getUnitPrice()).multiply(new BigDecimal(itemDeductedDetail.getDeductedCount()).negate());
                        }
                        return new PromotionSimple()
                                .setPromotionId(itemDeductedDetail.getPromotionId())
                                .setPromotionName(itemDeductedDetail.getPromotionName())
                                .setDeductedCount(itemDeductedDetail.getDeductedCount())
                                .setAvailableDeductTotalCount(itemDeductedDetail.getAvailableDeductCount())
                                .setPresentId(itemDeductedDetail.getPresentId())
                                .setDiscountPrice(itemDeductedPrice)
                                .setType(Promotion.Type.CARD)
                                .setSubType(PromotionSimple.SubType.DISCOUNT_FOR_DEDUCT)
                                .setBatchInfos(itemDeductedDetail.getBatchInfos());
                    }).collect(Collectors.toList());

            BigDecimal allDeductedPrice = promotionSimples.stream()
                    .map(PromotionSimple::getDiscountPrice)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            int allDeductedCount = promotionSimples.stream()
                    .mapToInt(PromotionSimple::getDeductedCount).sum();
            item.setDeductedTotalPrice(MathUtils.wrapBigDecimalAdd(item.getDeductedTotalPrice(), allDeductedPrice));
            item.setDeductedTotalCount(item.getDeductedTotalCount() + allDeductedCount);
            item.addPromotions(promotionSimples);

        });
    }

    /**
     * 锁单之后只处理已抵扣的信息
     *
     * @param calculatePromotionItems
     */
    private void fetchExistedVerifyPromotion(List<CalculatePromotionItem> calculatePromotionItems) {
        if (CollectionUtils.isEmpty(chargeSheet.getChargeVerifyInfos())) {
            return;
        }
        // 处理已经暂存的优惠信息
        availableChargeVerifyInfos = chargeSheet.getChargeVerifyInfos().stream()
                .filter(chargeVerifyInfo -> chargeVerifyInfo.getIsDeleted() == 0)
                .map(VerifyInfoView::ofChargeVerifyInfo)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "availableChargeVerifyInfos: {}", JsonUtils.dump(availableChargeVerifyInfos));
        if (CollectionUtils.isEmpty(availableChargeVerifyInfos)) {
            return;
        }
        Map<String, List<ItemVerifyDetail>> chargeFormItemIdItemDeductedDetailMap = availableChargeVerifyInfos.stream()
                .filter(patientCardPromotionView -> org.apache.commons.collections.CollectionUtils.isNotEmpty(patientCardPromotionView.getItemVerifyDetails()))
                .flatMap(patientCardPromotionView -> patientCardPromotionView.getItemVerifyDetails().stream())
                .filter(promotionDeductItem -> org.apache.commons.collections.CollectionUtils.isNotEmpty(promotionDeductItem.getVerifyInfoDetails()))
                .collect(Collectors.groupingBy(ItemVerifyDetail::getId));
        // 将数据库的抵扣信息应用到item
        calculatePromotionItems.forEach(item -> {
            List<ItemVerifyDetail> itemDeductedDetails = chargeFormItemIdItemDeductedDetailMap.getOrDefault(item.getId(), new ArrayList<>());
            if (CollectionUtils.isEmpty(itemDeductedDetails)) {
                return;
            }
            List<PromotionSimple> promotionSimples = itemDeductedDetails.stream().flatMap(itemDeductedDetail -> itemDeductedDetail.getVerifyInfoDetails().stream())
                    .map(itemDeductedDetail -> {
                        BigDecimal itemDeductedPrice = itemDeductedDetail.getDeductedPrice();
                        if (itemDeductedPrice == null) {
                            itemDeductedPrice = MathUtils.wrapBigDecimalOrZero(item.getUnitPrice()).multiply(new BigDecimal(itemDeductedDetail.getDeductedCount()).negate());
                        }

                        PromotionSimple promotionSimple = new PromotionSimple();
                        promotionSimple.setPromotionId(itemDeductedDetail.getVerificationCode());
                        promotionSimple.setType(PromotionSimple.Type.VERIFY_DEDUCT);
                        promotionSimple.setDiscountPrice(itemDeductedPrice);
                        promotionSimple.setDeductedCount(itemDeductedDetail.getDeductedCount());
                        return promotionSimple;
                    }).collect(Collectors.toList());

            BigDecimal allDeductedPrice = promotionSimples.stream()
                    .map(PromotionSimple::getDiscountPrice)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            int allDeductedCount = promotionSimples.stream()
                    .mapToInt(PromotionSimple::getDeductedCount).sum();
            item.setVerifyDeductedTotalPrice(MathUtils.wrapBigDecimalAdd(item.getVerifyDeductedTotalPrice(), allDeductedPrice));
            item.setVerifyDeductedCount(item.getVerifyDeductedCount() + allDeductedCount);
            item.addPromotions(promotionSimples);

        });
        sLogger.info("qsy test calculatePromotionItems: {}", JsonUtils.dump(calculatePromotionItems));
        sLogger.info("qsy test chargeFormItemIdItemDeductedDetailMap: {}", JsonUtils.dump(chargeFormItemIdItemDeductedDetailMap));

    }

    private void fetchVerifyPromotion(List<CalculatePromotionItem> items, List<VerificationItemView> verificationItemViewList, List<MallVerificationReq> mallVerificationReqs) {

        if (CollectionUtils.isEmpty(verificationItemViewList)) {
            if (chargeSheet.getChargeVerifyInfos() != null) {
                chargeSheet.getChargeVerifyInfos().forEach(chargeVerifyInfo -> chargeVerifyInfo.deleteModel(operatorId));
            }

            return;
        }
        items = items != null ? items : new ArrayList<>();
        // 处理已经暂存的优惠信息
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeSheet.getChargeVerifyInfos())) {
            availableChargeVerifyInfos = chargeSheet.getChargeVerifyInfos().stream()
                    .filter(chargeVerifyInfo -> chargeVerifyInfo.getIsDeleted() == 0)
                    .map(VerifyInfoView::ofChargeVerifyInfo)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }

        VerificationProcessor verificationProcessor = new VerificationProcessor(verificationItemViewList);
        availableChargeVerifyInfos = verificationProcessor.applyVerificationCode(items, availableChargeVerifyInfos, mallVerificationReqs);


        if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED) {
            if (!CollectionUtils.isEmpty(availableChargeVerifyInfos)) {
                List<ChargeVerifyInfo> chargeVerifyInfos = availableChargeVerifyInfos.stream().map(verifyInfoView -> ChargeVerifyInfo.ofChargeVerifyInfo(chargeSheet, verifyInfoView, operatorId)).collect(Collectors.toList());
                if (chargeSheet.getChargeVerifyInfos() == null) {
                    chargeSheet.setChargeVerifyInfos(new ArrayList<>());
                }

                chargeSheet.getChargeVerifyInfos().forEach(chargeVerifyInfo -> chargeVerifyInfo.deleteModel(operatorId));
                chargeSheet.getChargeVerifyInfos().addAll(chargeVerifyInfos);
            } else if (chargeSheet.getChargeVerifyInfos() != null) {
                chargeSheet.getChargeVerifyInfos().forEach(chargeVerifyInfo -> chargeVerifyInfo.deleteModel(operatorId));
            }
        }
        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "availableChargeVerifyInfos:{}, mallVerificationReqs:{}", JsonUtils.dump(availableChargeVerifyInfos), JsonUtils.dump(mallVerificationReqs));

    }

    /**
     * 营销卡项的折扣和抵扣
     *
     * @param patientCardPromotions
     * @param items
     * @param forms
     * @param patientCardPromotionReqs
     */
    private void fetchPatientCardPromotionInfo(List<Promotion> patientCardPromotions, List<CalculatePromotionItem> items, List<CalculatePromotionAirPharmacyForm> forms, List<PatientCardPromotionReq> patientCardPromotionReqs) {
        if (CollectionUtils.isEmpty(patientCardPromotions)) {
            if (chargeSheet.getPatientCardPromotionInfos() != null) {
                chargeSheet.getPatientCardPromotionInfos().forEach(patientCardPromotionInfo -> patientCardPromotionInfo.deleteModel(operatorId));
            }
            return;
        }

        items = items != null ? items : new ArrayList<>();
        forms = forms != null ? forms : new ArrayList<>();

        // 处理已经暂存的优惠信息
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeSheet.getPatientCardPromotionInfos())) {
            availablePatientCardPromotionViews = chargeSheet.getPatientCardPromotionInfos().stream()
                    .filter(patientCardPromotionInfo -> patientCardPromotionInfo.getIsDeleted() == 0)
                    .map(PatientCardPromotionView::ofPatientCardPromotionView)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }

        //找到最好的优惠信息
        PromotionPatientCardProcessor promotionPatientCardProcessor = new PromotionPatientCardProcessor(patientCardPromotions);
        availablePatientCardPromotionViews = promotionPatientCardProcessor.applyBestDeduct(items, availablePatientCardPromotionViews, patientCardPromotionReqs);

        if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED) {
            if (!CollectionUtils.isEmpty(availablePatientCardPromotionViews)) {
                List<ChargePatientCardPromotionInfo> patientCardPromotionInfos = availablePatientCardPromotionViews.stream().map(patientCardPromotionView -> ChargePatientCardPromotionInfo.ofChargePatientCardPromotionInfo(chargeSheet, patientCardPromotionView, operatorId)).collect(Collectors.toList());

                if (chargeSheet.getPatientCardPromotionInfos() == null) {
                    chargeSheet.setPatientCardPromotionInfos(new ArrayList<>());
                }
                chargeSheet.getPatientCardPromotionInfos().forEach(patientCardPromotionInfo -> patientCardPromotionInfo.deleteModel(operatorId));

                chargeSheet.getPatientCardPromotionInfos().addAll(patientCardPromotionInfos);
            } else if (chargeSheet.getPatientCardPromotionInfos() != null) {
                chargeSheet.getPatientCardPromotionInfos().forEach(patientCardPromotionInfo -> patientCardPromotionInfo.deleteModel(operatorId));
            }
        }
        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "availablePatientCardPromotionViews:{}, patientCardPromotionReqs:{}", JsonUtils.dump(availablePatientCardPromotionViews), JsonUtils.dump(patientCardPromotionReqs));
    }

    /**
     * 积分抵扣
     *
     * @param patientPointsInfoReq 端上传回来的数据
     */
    private void fetchAndApplyPatientPointsDiscountFee(PatientPointsInfoReq patientPointsInfoReq, boolean isChargeSheetLocked, PointPromotionRsp pointPromotionRsp, int afterGoodsDeductPoints) {
        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED) {
            return;
        }
        if (isChargeSheetLocked) {
            //已经锁单了，积分已经扣过一次，直接从数据库中取出规则
            ChargePatientPointsPromotionInfo dbPatientPointsPromotionInfo = chargeSheet.getPatientPointsPromotionInfo();
            if (dbPatientPointsPromotionInfo != null) {
                patientPointsInfoView = new PatientPointsInfoView();
                patientPointsInfoView.setChecked(dbPatientPointsPromotionInfo.getChecked())
                        .setTotalPoints(dbPatientPointsPromotionInfo.getTotalPoints())
                        .setMaxDeductionPrice(dbPatientPointsPromotionInfo.getMaxDeductionPrice())
                        .setCheckedDeductionPrice(dbPatientPointsPromotionInfo.getCheckedDeductionPrice())
                        .setPointsDeductionRat(dbPatientPointsPromotionInfo.getPointsDeductionRat());
            }
        } else {
            if (Optional.ofNullable(pointPromotionRsp).map(PointPromotionRsp::getPointsDeductionEnable).orElse(0) != 1) {
                Optional.ofNullable(chargeSheet.getPatientPointsPromotionInfo())
                        .ifPresent(patientPointsPromotionInfo -> patientPointsPromotionInfo.setChecked(false));
                return;
            }
            if (pointPromotionRsp.getPointsDeductionRat() == null || pointPromotionRsp.getPointsDeductionRat() < 1) {
                sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "积分规则配置错误, patientPointsInfo: {}", JsonUtils.dump(pointPromotionRsp));
                throw new ChargeServiceException(ChargeServiceError.PATIENT_POINTS_RULE_ERROR);
            }
            //计算出积分最多可用的金额
            BigDecimal discountedTotalPrice = formProcessors.stream()
                    .map(FormProcessor::getDiscountedPrice)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            ChargePatientPointsPromotionInfo dbPatientPointsPromotionInfo = chargeSheet.getPatientPointsPromotionInfo();

            BigDecimal reqDeductionPrice = Optional.ofNullable(patientPointsInfoReq)
                    .filter(PatientPointsInfoReq::getChecked)
                    .map(PatientPointsInfoReq::getCheckedDeductionPrice).orElse(null);

            BigDecimal dbDeductionPrice = Optional.ofNullable(dbPatientPointsPromotionInfo).map(ChargePatientPointsPromotionInfo::getCheckedDeductionPrice).orElse(null);
            //抵扣的金额
            BigDecimal deductionPrice = reqDeductionPrice;

            if (deductionPrice == null) {
                deductionPrice = dbDeductionPrice;
            }

            deductionPrice = deductionPrice == null ? discountedTotalPrice : MathUtils.min(discountedTotalPrice, deductionPrice);

            //已选择的积分数量
            Integer checkedPoints = null;
            if (deductionPrice != null) {
                checkedPoints = deductionPrice.intValue() * pointPromotionRsp.getPointsDeductionRat();
            }

            int availablePoints = afterGoodsDeductPoints / pointPromotionRsp.getPointsDeductionRat() * pointPromotionRsp.getPointsDeductionRat();
            //已选择的积分数量
            checkedPoints = checkedPoints == null ? availablePoints : Math.min(checkedPoints, availablePoints);
            //抵扣的金额
            deductionPrice = new BigDecimal(checkedPoints / pointPromotionRsp.getPointsDeductionRat());

            if (MathUtils.wrapBigDecimalCompare(deductionPrice, BigDecimal.ZERO) == 0) {
                Optional.ofNullable(chargeSheet.getPatientPointsPromotionInfo())
                        .ifPresent(patientPointsPromotionInfo -> patientPointsPromotionInfo.setChecked(false));
                return;
            }

            boolean checked = false;
            if (patientPointsInfoReq != null) {
                checked = patientPointsInfoReq.getChecked();
            } else if (dbDeductionPrice != null && dbDeductionPrice.compareTo(BigDecimal.ZERO) != 0 && MathUtils.wrapBigDecimalCompare(dbDeductionPrice, deductionPrice) == 0) {
                checked = true;
            }

            int maxDeductionPrice = Math.min(discountedTotalPrice.intValue(), availablePoints / pointPromotionRsp.getPointsDeductionRat());

            patientPointsInfoView = new PatientPointsInfoView();
            patientPointsInfoView.setChecked(checked)
                    .setTotalPoints(pointPromotionRsp.getPoints())
                    .setMaxDeductionPrice(maxDeductionPrice)
                    .setCheckedDeductionPrice(checked ? deductionPrice : null)
                    .setPointsDeductionRat(pointPromotionRsp.getPointsDeductionRat());

            if (chargeSheet.getPatientPointsPromotionInfo() != null) {
                ChargePatientPointsPromotionInfo patientPointsPromotionInfo = chargeSheet.getPatientPointsPromotionInfo();
                patientPointsPromotionInfo.setChecked(patientPointsInfoView.getChecked())
                        .setTotalPoints(patientPointsInfoView.getTotalPoints())
                        .setMaxDeductionPrice(patientPointsInfoView.getMaxDeductionPrice())
                        .setCheckedDeductionPrice(patientPointsInfoView.getCheckedDeductionPrice())
                        .setPointsDeductionRat(patientPointsInfoView.getPointsDeductionRat());
                FillUtils.fillLastModifiedBy(patientPointsPromotionInfo, operatorId);
            } else {
                chargeSheet.setPatientPointsPromotionInfo(ChargeUtils.createChargePatientPointsPromotionInfo(chargeSheet, patientPointsInfoView, operatorId));
            }
        }
        if (patientPointsInfoView == null || !patientPointsInfoView.getChecked() || MathUtils.wrapBigDecimalCompare(patientPointsInfoView.getCheckedDeductionPrice(), BigDecimal.ZERO) <= 0) {
            return;
        }
        BigDecimal toFlatDeductionPrice = patientPointsInfoView.getCheckedDeductionPrice().abs().negate();
        List<SmartFlatPriceHelper.SmartFlatPriceCell> flatPriceCells = formProcessors.stream().flatMap(formProcessor -> formProcessor.getCalculateAdjustmentItems().stream()).collect(Collectors.toList());
        SmartFlatPriceHelper.flat(toFlatDeductionPrice, flatPriceCells);

        Map<String, SmartFlatPriceHelper.SmartFlatPriceCell> flatPriceCellMap = flatPriceCells.stream().collect(Collectors.toMap(SmartFlatPriceHelper.SmartFlatPriceCell::getId, Function.identity(), (a, b) -> a));
        formProcessors.forEach(formProcessor -> formProcessor.applyPatientPointPromotionPrice(flatPriceCellMap));
    }

    /**
     * 积分项目抵扣匹配
     *
     * @param items
     */
    private int fetchPatientPointsDeductGoodsPromotion(List<CalculatePromotionItem> items, PointPromotionRsp pointPromotionRsp, List<PatientPointDeductProductPromotionReq> patientPointDeductProductPromotionReqs) {
        List<PointPromotionRsp.DeductPromotionGoods> deductPromotionGoodsList = Optional.ofNullable(pointPromotionRsp)
                .filter(p -> p.getPointsGoodsDeductionEnable() == 1)
                .map(PointPromotionRsp::getGoodsList)
                .orElse(new ArrayList<>());
        int totalPoints = Optional.ofNullable(pointPromotionRsp).map(PointPromotionRsp::getPoints).orElse(0);

        if (org.apache.commons.collections.CollectionUtils.isEmpty(items) || org.apache.commons.collections.CollectionUtils.isEmpty(deductPromotionGoodsList)) {
            return totalPoints;
        }
        // 处理已经暂存的优惠信息
        if (Objects.nonNull(chargeSheet.getPatientPointsDeductProductPromotionInfo())) {
            availablePatientPointDeductProductPromotionViews = Optional.ofNullable(chargeSheet.getPatientPointsDeductProductPromotionInfo().getDeductItems())
                    .orElse(new ArrayList<>())
                    .stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }
        //匹配积分抵扣商品信息
        PromotionPointDeductGoodsProcessor promotionPointDeductGoodsProcessor = new PromotionPointDeductGoodsProcessor(deductPromotionGoodsList, pointPromotionRsp.getPoints());
        availablePatientPointDeductProductPromotionViews = promotionPointDeductGoodsProcessor.applyDeduct(items, availablePatientPointDeductProductPromotionViews, patientPointDeductProductPromotionReqs);

        if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED && !CollectionUtils.isEmpty(availablePatientPointDeductProductPromotionViews)) {
            ChargePatientPointsDeductProductPromotionInfo chargePatientPointsDeductProductPromotionInfo = ChargeUtils.createChargePatientPointsDeductPromotionInfo(chargeSheet, availablePatientPointDeductProductPromotionViews, totalPoints, operatorId);
            HandleUtils.isTrueOrFalse(chargeSheet.getPatientPointsDeductProductPromotionInfo() != null)
                    .handle(() -> chargeSheet.getPatientPointsDeductProductPromotionInfo()
                                    .setDeductItems(chargePatientPointsDeductProductPromotionInfo.getDeductItems())
                                    .setTotalPoints(chargePatientPointsDeductProductPromotionInfo.getTotalPoints())
                                    .setTotalDeductPrice(chargePatientPointsDeductProductPromotionInfo.getTotalDeductPrice()),
                            () -> chargeSheet.setPatientPointsDeductProductPromotionInfo(chargePatientPointsDeductProductPromotionInfo)
                    );
        }

        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "availablePatientPointDeductProductPromotionViews:{}, patientPointDeductProductPromotionReqs:{}", JsonUtils.dump(availablePatientPointDeductProductPromotionViews), JsonUtils.dump(patientPointDeductProductPromotionReqs));
        return promotionPointDeductGoodsProcessor.getLeftPoints();
    }

    /**
     * 锁单之后只处理数据已经使用的优惠券，并回写到收费项上
     *
     * @param calculatePromotionItems
     * @param calculatePromotionAirPharmacyForms
     */
    private void fetchExistedCouponPromotion(List<CalculatePromotionItem> calculatePromotionItems, List<CalculatePromotionAirPharmacyForm> calculatePromotionAirPharmacyForms) {
        if (CollectionUtils.isEmpty(calculatePromotionItems) && CollectionUtils.isEmpty(calculatePromotionAirPharmacyForms)) {
            return;
        }
        if (CollectionUtils.isEmpty(chargeSheet.getCouponPromotionInfos())) {
            return;
        }
        if (calculatePromotionItems == null) {
            calculatePromotionItems = new ArrayList<>();
        }
        if (calculatePromotionAirPharmacyForms == null) {
            calculatePromotionAirPharmacyForms = new ArrayList<>();
        }
        // 处理已经暂存的优惠信息
        availableCouponPromotionViews = chargeSheet.getCouponPromotionInfos().stream()
                .filter(chargeCouponPromotionInfo -> chargeCouponPromotionInfo.getIsDeleted() == 0)
                .map(DTOConverter::convertToCouponPromotionView).collect(Collectors.toList());

        // 已选中的优惠券
        List<CouponPromotionView> couponPromotionViews = chargeSheet.getCouponPromotionInfos().stream()
                .filter(chargeCouponPromotionInfo -> chargeCouponPromotionInfo.getIsDeleted() == 0)
                .filter(couponPromotionInfo -> couponPromotionInfo.getStatus() == ChargeCouponPromotionInfo.Status.ISUSED)
                .map(DTOConverter::convertToCouponPromotionView)
                .filter(CouponPromotionView::getChecked)
                .collect(Collectors.toList());
        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "couponPromotionViews: {}", JsonUtils.dump(couponPromotionViews));
        Map<String, List<ProductItemCouponPromotionView>> couponPromotionViewMap = new HashMap<>();
        for (CouponPromotionView couponPromotionView : couponPromotionViews) {
            if (!CollectionUtils.isEmpty(couponPromotionView.getProductItems())) {
                for (PromotionProductItem productItem : couponPromotionView.getProductItems()) {
                    List<ProductItemCouponPromotionView> productItemCouponPromotionViews = couponPromotionViewMap.computeIfAbsent(productItem.getId(), key -> new ArrayList<>());
                    ProductItemCouponPromotionView view = new ProductItemCouponPromotionView();
                    view.setChargeFormItemId(productItem.getId());
                    view.setPromotionName(couponPromotionView.getName());
//                    view.setCouponIds(couponPromotionView.getCouponIds());
                    view.setCouponIds(Collections.emptyList());
                    view.setDiscountPrice(productItem.getDiscountPrice());
                    view.setPromotionId(couponPromotionView.getPromotionId());
                    productItemCouponPromotionViews.add(view);
                }
            }
        }
        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "couponPromotionViewMap: {}", JsonUtils.dump(couponPromotionViewMap));
        calculatePromotionItems.forEach(item -> {
            List<ProductItemCouponPromotionView> productItemCouponPromotionViews = couponPromotionViewMap.getOrDefault(item.getId(), null);
            if (productItemCouponPromotionViews != null) {
                BigDecimal discountPrice = productItemCouponPromotionViews.stream().map(ProductItemCouponPromotionView::getDiscountPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                item.setDiscountPrice(MathUtils.wrapBigDecimalAdd(item.getDiscountPrice(), discountPrice));
                productItemCouponPromotionViews.forEach(view -> {
                    List<PromotionSimple> promotionSimples = item.getPromotions();
                    if (promotionSimples == null) {
                        promotionSimples = new ArrayList<>();
                    }
                    PromotionSimple promotionSimple = new PromotionSimple();
                    promotionSimple.setPromotionId(view.getPromotionId());
                    promotionSimple.setPromotionName(view.getPromotionName());
                    promotionSimple.setType(Promotion.Type.COUPON);
//                    promotionSimple.setCouponIds(view.getCouponIds());
                    promotionSimple.setCouponIds(Collections.emptyList());
                    promotionSimple.setDiscountPrice(view.getDiscountPrice());
                    promotionSimples.add(promotionSimple);
                });
            }

        });

        calculatePromotionAirPharmacyForms.forEach(form -> {
            List<ProductItemCouponPromotionView> productItemCouponPromotionViews = couponPromotionViewMap.getOrDefault(form.getId(), null);
            if (productItemCouponPromotionViews != null) {
                BigDecimal discountPrice = productItemCouponPromotionViews.stream().map(ProductItemCouponPromotionView::getDiscountPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                form.setDiscountPrice(MathUtils.wrapBigDecimalAdd(form.getDiscountPrice(), discountPrice));
                productItemCouponPromotionViews.forEach(view -> {
                    List<PromotionSimple> promotionSimples = form.getPromotions();
                    if (promotionSimples == null) {
                        promotionSimples = new ArrayList<>();
                    }
                    PromotionSimple promotionSimple = new PromotionSimple();
                    promotionSimple.setPromotionId(view.getPromotionId());
                    promotionSimple.setPromotionName(view.getPromotionName());
                    promotionSimple.setType(Promotion.Type.COUPON);
//                    promotionSimple.setCouponIds(view.getCouponIds());
                    promotionSimple.setCouponIds(Collections.emptyList());
                    promotionSimple.setDiscountPrice(view.getDiscountPrice());
                    promotionSimples.add(promotionSimple);
                });
            }
        });
    }

    private void fetchGiftRulePromotionInfo(List<Promotion> giftRulePromotions, List<CalculatePromotionItem> calculatePromotionItems, List<CalculatePromotionAirPharmacyForm> calculatePromotionAirPharmacyForms, List<GiftRulePromotionReq> giftRulePromotionReqs, String patientId) {
        if (CollectionUtils.isEmpty(giftRulePromotions) || (CollectionUtils.isEmpty(calculatePromotionItems) && CollectionUtils.isEmpty(calculatePromotionAirPharmacyForms))) {
            if (chargeSheet.getGiftRulePromotionInfos() != null) {
                chargeSheet.getGiftRulePromotionInfos().forEach(giftRulePromotionInfo -> giftRulePromotionInfo.setIsDeleted(1));
            }
            return;
        }
        if (calculatePromotionItems == null) {
            calculatePromotionItems = new ArrayList<>();
        }
        if (calculatePromotionAirPharmacyForms == null) {
            calculatePromotionAirPharmacyForms = new ArrayList<>();
        }
        // 处理已经暂存的优惠信息
        if (!CollectionUtils.isEmpty(chargeSheet.getGiftRulePromotionInfos())) {
            availableGiftRulePromotionViews = chargeSheet.getGiftRulePromotionInfos().stream()
                    .filter(giftRulePromotionInfo -> giftRulePromotionInfo.getIsDeleted() == 0)
                    .map(DTOConverter::convertToGiftRulePromotionView).collect(Collectors.toList());
        }
        PromotionGiftRuleProcessor promotionDiscountProcessor = new PromotionGiftRuleProcessor(giftRulePromotions);
        availableGiftRulePromotionViews = promotionDiscountProcessor.applyBestDiscount(calculatePromotionItems, calculatePromotionAirPharmacyForms, availableGiftRulePromotionViews, giftRulePromotionReqs, patientId);

        if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED) {
            if (!CollectionUtils.isEmpty(availableGiftRulePromotionViews)) {
                List<ChargeGiftRulePromotionInfo> giftRulePromotionInfos = availableGiftRulePromotionViews.stream().map(giftRulePromotionView -> DTOConverter.convertToGiftRulePromotionView(chargeSheet, giftRulePromotionView, operatorId)).collect(Collectors.toList());

                if (chargeSheet.getGiftRulePromotionInfos() == null) {
                    chargeSheet.setGiftRulePromotionInfos(new ArrayList<>());
                }
                chargeSheet.getGiftRulePromotionInfos().forEach(chargeGiftRulePromotionInfo -> chargeGiftRulePromotionInfo.deleteModel(operatorId));

                chargeSheet.getGiftRulePromotionInfos().addAll(giftRulePromotionInfos);
            } else if (chargeSheet.getGiftRulePromotionInfos() != null) {
                //如果没有可匹配的promotion，将数据库中的数据删掉
                chargeSheet.getGiftRulePromotionInfos().forEach(chargeGiftRulePromotionInfo -> chargeGiftRulePromotionInfo.deleteModel(operatorId));
            }

        }
    }

    private void fetchCouponPromotionInfo(List<Promotion> couponPromotions, List<CalculatePromotionItem> calculatePromotionItems, List<CalculatePromotionAirPharmacyForm> calculatePromotionAirPharmacyForms, List<CouponPromotionReq> couponPromotionReqs) {
        if (CollectionUtils.isEmpty(couponPromotions) || (CollectionUtils.isEmpty(calculatePromotionItems) && CollectionUtils.isEmpty(calculatePromotionAirPharmacyForms))) {
            if (chargeSheet.getCouponPromotionInfos() != null) {
                chargeSheet.getCouponPromotionInfos().forEach(chargeCouponPromotionInfo -> chargeCouponPromotionInfo.deleteModel(operatorId));
            }
            return;
        }
        if (calculatePromotionItems == null) {
            calculatePromotionItems = new ArrayList<>();
        }
        if (calculatePromotionAirPharmacyForms == null) {
            calculatePromotionAirPharmacyForms = new ArrayList<>();
        }
        // 处理已经暂存的优惠信息
        if (!CollectionUtils.isEmpty(chargeSheet.getCouponPromotionInfos())) {
            availableCouponPromotionViews = chargeSheet.getCouponPromotionInfos().stream()
                    .filter(chargeCouponPromotionInfo -> chargeCouponPromotionInfo.getIsDeleted() == 0)
                    .map(DTOConverter::convertToCouponPromotionView).collect(Collectors.toList());
        }
        PromotionCouponProcessor promotionDiscountProcessor = new PromotionCouponProcessor(couponPromotions);
        availableCouponPromotionViews = promotionDiscountProcessor.generateCouponPromotionView(calculatePromotionItems, calculatePromotionAirPharmacyForms, availableCouponPromotionViews, couponPromotionReqs);
        if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED) {
            if (!CollectionUtils.isEmpty(availableCouponPromotionViews)) {
                List<ChargeCouponPromotionInfo> couponPromotionInfos = availableCouponPromotionViews.stream()
                        .map(couponPromotionView -> DTOConverter.convertToChargeCouponPromotionInfo(chargeSheet, couponPromotionView, operatorId))
                        .collect(Collectors.toList());
                if (chargeSheet.getCouponPromotionInfos() == null) {
                    chargeSheet.setCouponPromotionInfos(new ArrayList<>());
                }

                chargeSheet.getCouponPromotionInfos().forEach(chargeCouponPromotionInfo -> chargeCouponPromotionInfo.deleteModel(operatorId));
                chargeSheet.getCouponPromotionInfos().addAll(couponPromotionInfos);

            } else if (chargeSheet.getCouponPromotionInfos() != null) {
                chargeSheet.getCouponPromotionInfos().forEach(chargeCouponPromotionInfo -> chargeCouponPromotionInfo.deleteModel(operatorId));
            }

        }

        if (!CollectionUtils.isEmpty(availableCouponPromotionViews)) {
            couponIds = availableCouponPromotionViews.stream()
                    .filter(CouponPromotionView::getChecked)
                    .map(CouponPromotionView::getCouponIds)
                    .flatMap(Collection::stream).distinct().collect(Collectors.toList());
        }

    }

    /**
     * 计算收费单的打折信息
     *
     * @param serverDiscountPromotions 从促销服务拉取的会员卡和普通打折信息
     *                                 calculatePromotionItems 非空中药房的itemProcssor
     *                                 calculatePromotionAirPharmacyForms 空中药房的 itemProcssor
     *                                 discountPromotionReqs 端上请求带上来的 打折信息
     */
    private void fetchDiscountPromotionInfo(List<Promotion> serverDiscountPromotions,
                                            List<CalculatePromotionItem> calculatePromotionItems,
                                            List<CalculatePromotionAirPharmacyForm> calculatePromotionAirPharmacyForms,
                                            List<PromotionReq> clientDiscountPromotionReqs) {
        if (CollectionUtils.isEmpty(serverDiscountPromotions) //打折促销为空，所有优惠都不做了？
                || (CollectionUtils.isEmpty(calculatePromotionItems) && CollectionUtils.isEmpty(calculatePromotionAirPharmacyForms))) {
            return;
        }
        if (calculatePromotionItems == null) {
            calculatePromotionItems = new ArrayList<>();
        }
        if (calculatePromotionAirPharmacyForms == null) {
            calculatePromotionAirPharmacyForms = new ArrayList<>();
        }
        // 处理已经暂存的优惠信息
        if (!TextUtils.isEmpty(chargeSheet.getPromotionInfoJson())) {
            SheetPromotionInfo sheetPromotionInfo = JsonUtils.readValue(chargeSheet.getPromotionInfoJson(), SheetPromotionInfo.class);
            if (sheetPromotionInfo != null) {
                availablePromotionViews = sheetPromotionInfo.getPromotions();
            }
        }
        // 找到最好的优惠信息
        PromotionDiscountProcessor promotionDiscountProcessor = new PromotionDiscountProcessor(serverDiscountPromotions);
        availablePromotionViews = promotionDiscountProcessor.applyBestDiscount(calculatePromotionItems,
                calculatePromotionAirPharmacyForms,
                availablePromotionViews,
                clientDiscountPromotionReqs);

        if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED && !CollectionUtils.isEmpty(availablePromotionViews)) {
            SheetPromotionInfo sheetPromotionInfo = new SheetPromotionInfo();
            sheetPromotionInfo.setPromotions(availablePromotionViews);
            chargeSheet.setPromotionInfoJson(JsonUtils.dump(sheetPromotionInfo));
        }

        usedPromotionViews = availablePromotionViews.stream()
                .filter(promotionView -> !CollectionUtils.isEmpty(promotionView.getProductItems())
                        || !CollectionUtils.isEmpty(promotionView.getAirPharmacyForms()))
                .collect(Collectors.toList());
        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "本次可用折扣:{}, 本次使用折扣:{}, 端上请求的折扣:{}", JsonUtils.dump(availablePromotionViews), JsonUtils.dump(usedPromotionViews), JsonUtils.dump(clientDiscountPromotionReqs));

    }

    // 将议价平摊
    private BigDecimal applyAdjustment(BigDecimal adjustmentFee) {
        List<SmartFlatPriceHelper.SmartFlatPriceCell> flatPriceCells = formProcessors.stream().flatMap(formProcessor -> formProcessor.getCalculateAdjustmentItems().stream()).collect(Collectors.toList());
        BigDecimal notFlatPrice = SmartFlatPriceHelper.flat(adjustmentFee, flatPriceCells);

        Map<String, SmartFlatPriceHelper.SmartFlatPriceCell> flatPriceCellMap = flatPriceCells.stream().collect(Collectors.toMap(SmartFlatPriceHelper.SmartFlatPriceCell::getId, Function.identity(), (a, b) -> a));
        formProcessors.forEach(formProcessor -> formProcessor.applyAdjustment(flatPriceCellMap));
        return notFlatPrice;
    }


    /**
     * 更新item上的unitAdjustmentFee到
     */
    private void applyChargeFormItemPromotionInfoUnitAdjustmentFee() {
        formProcessors.forEach(FormProcessor::applyChargeFormItemPromotionInfoUnitAdjustmentFee);
    }

    private void updateReceivableFee(BigDecimal adjustmentFee) {
        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED) {
            return;
        }
        List<SmartFlatPriceHelper.SmartFlatPriceCell> flatPriceCells = formProcessors.stream()
                .flatMap(formProcessor -> formProcessor.getCalculateAdjustmentItems().stream())
                .collect(Collectors.toList());
        SmartFlatPriceHelper.flat(adjustmentFee, flatPriceCells);
        Map<String, SmartFlatPriceHelper.SmartFlatPriceCell> flatPriceCellMap = flatPriceCells.stream()
                .collect(Collectors.toMap(SmartFlatPriceHelper.SmartFlatPriceCell::getId, Function.identity(), (a, b) -> a));
        formProcessors.forEach(formProcessor -> formProcessor.updateReceivableFee(flatPriceCellMap));
        // 更新sheet维度上的社保应收
        sheBaoReceivableFee = MathUtils.setScaleTwo(formProcessors.stream()
                .map(FormProcessor::getSheBaoReceivableFee)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
    }


    private void fetchAndSetMemberInfo(boolean isPay) {
        int useMemberFlag = chargeSheet.getUseMemberFlag();
        String oldMemberId = memberId;
        // 不是待收
        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED) {
            memberInfo = JsonUtils.readValue(chargeSheet.getMemberInfoJson(), MemberInfo.class);
            return;
        }
        // 锁单中，直接查询memberInfo
        if (chargeSheet.isLocking()) {
            setMemberInfoByMemberId(memberId, false);
            return;
        }
        if (useMemberFlag == ChargeSheetAdditional.UseMemberFlag.DO_NOT_USE_MEMBER) {
            setMemberInfoNull();
        } else if (useMemberFlag == ChargeSheetAdditional.UseMemberFlag.APPOINT_MEMBER) {
            setMemberInfoByMemberId(memberId, false);
        } else if (useMemberFlag == ChargeSheetAdditional.UseMemberFlag.DEFAULT_MEMBER) {
            setDefaultMemberInfo();
        }
        if (isPay && StringUtils.isNotBlank(oldMemberId) && !StringUtils.equals(oldMemberId, memberId)) {
            sLogger.info("支付时会员信息发生改变，不能进行支付, oldMemberId: {}, currentMemberId: {}", oldMemberId, memberId);
            throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_PAID_MEMBER_ID_CHANGED);
        }
    }

    private void setDefaultMemberInfo() {
        if (StringUtils.isBlank(chargeSheet.getPatientId()) || StringUtils.equals(chargeSheet.getPatientId(), Constants.ANONYMOUS_PATIENT_ID)) {
            setMemberInfoByMemberId(memberId, false);
        } else {
            CrmPatientFamily.SharedRights sharedRights = sheetProcessorInfoProvider.getPropertyProvider().getPatientSharedRightsProperty(chainId);
            //会员折扣和会员余额是否共享
            boolean shareMemberBalanceAndDiscount = Optional.ofNullable(sharedRights)
                    .map(CrmPatientFamily.SharedRights::getMemberBalanceAndDiscount)
                    .orElse(1) == 1;
            setMemberInfoByMemberId(chargeSheet.getPatientId(), shareMemberBalanceAndDiscount);
        }
    }

    private void setMemberInfoByMemberId(String chosenPatientId, boolean chooseDefaultMemberInfo) {
        if (StringUtils.isBlank(chosenPatientId)) {
            return;
        }
        PatientRelateMemberRsp patientRelateMemberRsp = sheetProcessorInfoProvider.getPatientInfoProvider().getPatientRelateMember(chosenPatientId, chainId);
        List<cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientMemberInfo> relatePatientMembers = Optional.ofNullable(patientRelateMemberRsp)
                .map(PatientRelateMemberRsp::getRelatePatientMembers)
                .orElse(new ArrayList<>());
        cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientMemberInfo chosenPatientMemberInfo = relatePatientMembers.stream()
                .filter(patientMemberInfo -> StringUtils.equals(chosenPatientId, patientMemberInfo.getPatientId()))
                .findFirst()
                .orElse(chooseDefaultMemberInfo && !CollectionUtils.isEmpty(relatePatientMembers) ? relatePatientMembers.get(0) : null);

        HandleUtils.isTrueOrFalse(Objects.nonNull(chosenPatientMemberInfo))
                .handle(() -> {
                    memberId = chosenPatientMemberInfo.getPatientId();
                    memberInfo = ChargeUtils.convertPatientMemberInfoToMemberInfo(chosenPatientMemberInfo);
                    chargeSheet.setMemberInfoJson(JsonUtils.dump(memberInfo));
                    chargeSheet.setMemberInfo(memberInfo);
                    chargeSheet.setMemberId(memberId);
                }, this::setMemberInfoNull);
    }

    private void setMemberInfoNull() {
        memberId = "";
        chargeSheet.setMemberId(memberId);
        chargeSheet.setMemberInfo(null);
        chargeSheet.setMemberInfoJson(null);
    }


    private void updateRefundInfo(RefundInfo refundInfo, boolean checkCanRefundCount) throws CisCustomException {
        if (CollectionUtils.isEmpty(refundInfo.getChargeForms())) {
            return;
        }
        ChargeFormUtils.updateChargeFormByDb(refundInfo.getChargeForms(), chargeSheet.getChargeForms());
        Map<String, ChargeForm> toRefundChargeFormMap = refundInfo.getChargeForms()
                .stream()
                .filter(chargeForm -> !TextUtils.isEmpty(chargeForm.getId()))
                .collect(Collectors.toMap(ChargeForm::getId, Function.identity(), (a, b) -> a));
        for (FormProcessor formProcessor : formProcessors) {
            ChargeForm toRefundChargeForm = toRefundChargeFormMap.getOrDefault(formProcessor.getFormId(), null);
            if (toRefundChargeForm == null) {
                continue;
            }
            formProcessor.updateRefundInfo(toRefundChargeForm, checkCanRefundCount, refundInfo.getOperatorId());
        }
    }

    private void updateRefundInfo(ChargeRefundSheet chargeRefundSheet, String operatorId) throws CisCustomException {
        if (chargeRefundSheet == null || (chargeRefundSheet.getChargeRefundFormItems() == null && chargeRefundSheet.getChargeRefundForms() == null)) {
            return;
        }
        if (chargeRefundSheet.getChargeRefundFormItems() != null) {
            Map<String, ItemProcessor> itemProcessorMap = formProcessors
                    .stream()
                    .flatMap(formProcessor -> formProcessor.getItemProcessorList().stream())
                    .collect(Collectors.toMap(ItemProcessor::getItemId, Function.identity(), (a, b) -> a));
            Map<String, ChargeFormItem> chargeFormItemMap = ChargeUtils.getChargeSheetItems(chargeSheet).stream().collect(Collectors.toMap(ChargeFormItem::getId, Function.identity(), (a, b) -> a));
            for (ChargeRefundFormItem refundFormItem : chargeRefundSheet.getChargeRefundFormItems()) {
                ChargeFormItem sourceChargeFormItem = chargeFormItemMap.getOrDefault(refundFormItem.getChargeFormItemId(), null);
                if (sourceChargeFormItem == null) {
                    continue;
                }
                String itemKey = sourceChargeFormItem.getComposeType() == ComposeType.COMPOSE_SUB_ITEM ? sourceChargeFormItem.getComposeParentFormItemId() : sourceChargeFormItem.getId();
                ItemProcessor itemProcessor = itemProcessorMap.getOrDefault(itemKey, null);
                if (itemProcessor == null) {
                    continue;
                }
                ChargeFormItem toRefundChargeFormItem = new ChargeFormItem();
                BeanUtils.copyProperties(refundFormItem, toRefundChargeFormItem);
                toRefundChargeFormItem.setComposeType(sourceChargeFormItem.getComposeType());
                toRefundChargeFormItem.setComposeParentFormItemId(sourceChargeFormItem.getComposeParentFormItemId());
                toRefundChargeFormItem.setId(refundFormItem.getChargeFormItemId());
                toRefundChargeFormItem.setDeductTotalCount(refundFormItem.getDeductTotalCount());
                if (MathUtils.compareZero(refundFormItem.getDeductTotalCount()) > 0) {
                    //这里要把抵扣数量减去，unitCount表示费抵扣的退费数量，但是在写入item时unitCount是包含抵扣数量的，所以在这里需要减掉
                    toRefundChargeFormItem.setUnitCount(MathUtils.wrapBigDecimalSubtract(refundFormItem.getUnitCount(), refundFormItem.getDeductTotalCount()));
                }
                itemProcessor.updateRefundInfo(toRefundChargeFormItem, true, operatorId);
            }
        }

        if (chargeRefundSheet.getChargeRefundForms() != null) {
            Map<String, FormProcessor> formProcessorMap = ListUtils.toMap(formProcessors, FormProcessor::getFormId);
            for (ChargeRefundForm chargeRefundForm : chargeRefundSheet.getChargeRefundForms()) {
                FormProcessor formProcessor = formProcessorMap.getOrDefault(chargeRefundForm.getChargeFormId(), null);
                if (formProcessor == null) {
                    continue;
                }
                ChargeForm toRefundChargeForm = new ChargeForm();
                BeanUtils.copyProperties(chargeRefundForm, toRefundChargeForm);
                toRefundChargeForm.setId(chargeRefundForm.getChargeFormId());
                formProcessor.updateRefundInfo(toRefundChargeForm);
            }
        }
    }

    private void updateDispensingInfo() {
        if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED ||
                chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_CHARGED ||
                chargeSheet.getStatus() == Constants.ChargeSheetStatus.REFUNDED) {
            return;
        }
        BigDecimal couldDispensingCount = formProcessors.stream().map(FormProcessor::getCouldDispensingCount).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (couldDispensingCount.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        if (sheetProcessorInfoProvider.getDispensingInfoProvider() == null) {
            return;
        }
        dispensingSheetInfos = sheetProcessorInfoProvider.getDispensingInfoProvider().getDispensingSheetInfoListBySourceSheetId(chargeSheet.getId());
        if (CollectionUtils.isEmpty(dispensingSheetInfos)) {
            // 解决收费完成，马上拉取时状态不对的情况
            dispensingStatus = Constants.DispensingStatus.WAITING;
            return;
        }
        if (dispensingSheetInfos.stream().anyMatch(dispensingSheetInfo -> dispensingSheetInfo.getStatus() == Constants.DispensingStatus.WAITING)) {
            dispensingStatus = Constants.DispensingStatus.WAITING;
        } else if (dispensingSheetInfos.stream().allMatch(dispensingSheetInfo -> dispensingSheetInfo.getStatus() == Constants.DispensingStatus.DISPENSED)) {
            dispensingStatus = Constants.DispensingStatus.DISPENSED;
        }
        // 取本地药房的第一条数据的发药人和发药时间
        DispensingSheetInfo dispensingSheetInfoFlag = dispensingSheetInfos.stream()
                .filter(dispensingSheetInfo -> dispensingSheetInfo.getPharmacyType() == GoodsConst.PharmacyType.LOCAL_PHARMACY)
                .findFirst()
                .orElse(dispensingSheetInfos.stream()
                        .filter(dispensingSheetInfo -> dispensingSheetInfo.getPharmacyType() != GoodsConst.PharmacyType.LOCAL_PHARMACY)
                        .findFirst()
                        .orElse(null)
                );

        if (dispensingSheetInfoFlag != null) {
            dispensedBy = dispensingSheetInfoFlag.getDispensedBy();
            dispensedTime = dispensingSheetInfoFlag.getDispensedTime();
        }
        Map<String, DispensingSheetInfo> dispensingSheetInfoMap = dispensingSheetInfos.stream()
                .collect(Collectors.toMap(dispensingSheetInfo -> String.format("%d-%d", dispensingSheetInfo.getPharmacyType(), dispensingSheetInfo.getPharmacyNo()), Function.identity(), (a, b) -> a));
        Map<Integer, Map<String, DispensingInfo>> pharmacyTypeDispensingInfoMap = dispensingSheetInfos.stream()
                .collect(Collectors.groupingBy(
                        DispensingSheetInfo::getPharmacyType,
                        Collectors.mapping(DispensingSheetInfo::getDispensingInfos,
                                Collectors.collectingAndThen(Collectors.toList(),
                                        s -> s.stream().flatMap(List::stream)
                                                .collect(Collectors.toMap(DispensingInfo::getChargeFormItemId, Function.identity())))))
                );
        Map<String, List<DispensingFormInfo>> formIdToFromInfoList = dispensingSheetInfos.stream()
                .flatMap(it -> it.getDispensingFormInfos().stream())
                .collect(Collectors.groupingBy(DispensingFormInfo::getSourceFormId));
        formProcessors.forEach(formProcessor -> {
            formProcessor.bindDispensingInfo(pharmacyTypeDispensingInfoMap, dispensingSheetInfoMap);
            formProcessor.bindDispensingFormInfoList(formIdToFromInfoList.get(formProcessor.getFormId()));
        });
    }

    public boolean getIsPrintDispensingSheet() {
        if (dispensingSheetInfos == null) {
            return false;
        }
        return dispensingSheetInfos.stream().anyMatch(dispensingSheetInfo -> dispensingSheetInfo.getStatus() == Constants.DispensingStatus.DISPENSED || dispensingSheetInfo.getStatus() == Constants.DispensingStatus.WAITING);
    }

    private void checkProductInfoOrThrowException(int source) {
        //查询goodsConfig
        GoodsConfigView goodsConfig = getGoodsConfigView();
        //是否无库存不允许开出，true 为不允许开出，false为允许开出
        boolean enableNoStockGoods = GoodsLockingUtils.isEnableNoStockGoods(goodsConfig);
        //是否开启锁Goods
        Pair<Boolean, Integer> lockGoodsPair = GoodsLockingUtils.isLockGoodsPair(goodsConfig);
        for (FormProcessor formProcessor : formProcessors) {
            formProcessor.checkProductInfoOrThrowException(source, enableNoStockGoods, lockGoodsPair);
        }

        checkAntimicrobialGoodsLimit();
    }

    private void checkAntimicrobialGoodsLimit() {
        if (!ChargeSheet.Type.directChargeSheetTypes().contains(chargeSheet.getType())) {
            return;
        }

        //收集限制级使用药品
        List<GoodsItem> antimicrobialGoodsItems = null;
        for (FormProcessor formProcessor : formProcessors) {
            antimicrobialGoodsItems = formProcessor.getItemProcessorList().stream()
                    .filter(item -> item.getGoodsItem() != null)
                    .flatMap(item -> {
                        GoodsItem goodsItem = item.getGoodsItem();
                        if (goodsItem.getType() == GoodsConst.GoodsType.COMPOSE && goodsItem.getChildren() != null) {
                            return goodsItem.getChildren().stream();
                        }
                        return Stream.of(goodsItem);
                    })
                    .filter(item -> item.getAntibiotic() != null)
                    .collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(antimicrobialGoodsItems)) {
            return;
        }

        //分级管理配置
        AntimicrobialDrugManagementConfig antimicrobialDrugManagementConfig = sheetProcessorInfoProvider.getPropertyProvider().getAntimicrobialDrugManagementConfig(chargeSheet.getClinicId());
        if (antimicrobialDrugManagementConfig == null || antimicrobialDrugManagementConfig.getEnable() == 0) {
            return;
        }

        //获取开单人的信息
        ChainEmployee employeeChainInfo = sheetProcessorInfoProvider.getEmployeeInfoProvider().getChainEmployee(chargeSheet.getSellerId(), chargeSheet.getChainId());
        Set<String> practiceInfoTitles = new HashSet<>();
        if (employeeChainInfo != null && employeeChainInfo.getChainInfo() != null && !CollectionUtils.isEmpty(employeeChainInfo.getChainInfo().getPracticeInfo())) {
            practiceInfoTitles = employeeChainInfo.getChainInfo().getPracticeInfo().stream().map(PracticeInfo::getTitle).collect(Collectors.toSet());
        }
        Set<String> finalPracticeInfoTitles = practiceInfoTitles;

        boolean isNeedLimit = antimicrobialGoodsItems.stream().anyMatch(goodsItem -> {
            Integer goodsAntibiotic = goodsItem.getAntibiotic();
            if (goodsAntibiotic == null) {
                return false;
            }
            //查看药品的级别
            AntimicrobialDrugManagementRule rule = antimicrobialDrugManagementConfig.getRules().stream().filter(item -> item.getRestrictedLevel() == goodsAntibiotic).findFirst().orElse(null);
            if (rule != null && rule.getAvailableBusiness().contains(AntimicrobialDrugManagementRule.Business.OUTPATIENT)) {
                List<String> availableTitles = rule.getAvailableTitles() == null ? new ArrayList<>() : rule.getAvailableTitles();
                return availableTitles.stream().noneMatch(finalPracticeInfoTitles::contains);
            }
            return false;
        });

        if (isNeedLimit) {
            throw new ChargeServiceException(ChargeServiceError.KANG_JUN_DRUGS_LIMIT);
        }

    }

    private void checkPriceChangedAndThrowException() throws ProductInfoChangedException {
        for (FormProcessor formProcessor : formProcessors) {
            formProcessor.checkPriceChangedAndThrowException();
        }
    }

    public ChargeSheetSummary onlyGenerateSummary() throws ServiceInternalException {
        doCalculate(BigDecimal.ZERO, chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_CHARGED ? CalculateScene.PAY_FOR_LEFT : CalculateScene.PAY);
        //查询收费单对应的欠费数据
        List<ChargeOweSheet> chargeOweSheets = new ArrayList<>();
        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED) {
            chargeOweSheets = Optional.ofNullable(sheetProcessorInfoProvider).map(SheetProcessorInfoProvider::getChargeOweSheetProvider)
                    .map(chargeOweSheetProvider -> chargeOweSheetProvider.findChargeOweSheetsByChargeSheetId(chargeSheet.getClinicId(), chargeSheet.getId())).orElse(new ArrayList<>());
        }
        return ChargeSheetFeeProtocol.toSummaryView(this, chargeOweSheets);
    }

    public ChargeSheetPushOrderInfo generateChargeSheetPushOrderInfo(int source,
                                                                     BigDecimal expectedAdjustmentFee,
                                                                     List<PromotionReq> discountPromotionReqs,
                                                                     List<CouponPromotionReq> couponPromotionReqs,
                                                                     List<GiftRulePromotionReq> giftRulePromotionReqs,
                                                                     List<PatientCardPromotionReq> patientCardPromotionReqs,
                                                                     List<PatientPointDeductProductPromotionReq> patientPointDeductProductPromotionReqs,
                                                                     PatientPointsInfoReq patientPointsInfoReq,
                                                                     List<MallVerificationReq> mallVerifications,
                                                                     boolean checkAirPharmacy) throws ServiceInternalException, ChargeServiceException {
        updatePatientInfo();
        fetchAndSetMemberInfo(false);
        updateProductInfo(getIsNeedUseLimitPrice(), false, 1, 1);
        updateAirPharmacyChargeFormDeliveryFeeAndProcessFee();
        /**
         * 更新虚拟药房的快递费
         */
        updateVirtualPharmacyChargeFormDeliveryFee(Constants.ChargeSource.CHARGE);
        updateExpressDeliveryFeeAndProcessFee(Constants.ChargeSource.CHARGE);//这个场景就是收费台的推送
        if (checkAirPharmacy) {
            checkAirPharmacyAndThrowException(source);
        }
        ApplyPromotionInfoResult applyPromotionInfoResult = fetchAndApplyPromotionInfo(discountPromotionReqs, couponPromotionReqs, giftRulePromotionReqs, patientCardPromotionReqs, patientPointDeductProductPromotionReqs, mallVerifications, patientPointsInfoReq, expectedAdjustmentFee, null);
//        BigDecimal adjustFee = calculateAdjustFee(expectedAdjustmentFee, MathUtils.wrapBigDecimalOrZero(chargeSheet.getOutpatientAdjustmentFee()), null, chargeSheet.getDraftAdjustmentFee());
        doCalculate(MathUtils.wrapBigDecimalOrZero(applyPromotionInfoResult.getAdjustmentFeeOnCalculate()), CalculateScene.PAY);
        BigDecimal needPay = realReceivableFee;
        if (needPay.compareTo(BigDecimal.ZERO) < 0) {
            needPay = BigDecimal.ZERO;
        }
        ChargeSheetPushOrderInfo chargeSheetPushOrderInfo = new ChargeSheetPushOrderInfo();
        chargeSheetPushOrderInfo.setOrderId(AbcIdUtils.convertUUidToLongString(chargeSheet.getId()));
        chargeSheetPushOrderInfo.setNeedPay(needPay.toPlainString());
        chargeSheetPushOrderInfo.setTime(DateUtils.convertInstantToString(Instant.now(), DateUtils.WECHAT_TIME_FORMAT));
        chargeSheetPushOrderInfo.setMedicalItems(ChargeUtils.generateMedicalItems(chargeSheet.getChargeForms()));
        chargeSheetPushOrderInfo.setPatientName(Optional.ofNullable(patientInfo).map(PatientInfo::getName).orElse(""));
        return chargeSheetPushOrderInfo;
    }

    public ChargeSheet generateChargeSheetForClone() {
        updatePatientInfo();
        fetchAndSetMemberInfo(false);

        updateProductInfo(getIsNeedUseLimitPrice(), true, 1, 0);
        updateAirPharmacyChargeFormDeliveryFeeAndProcessFee();
        /**
         * 更新虚拟药房的快递费
         */
        updateVirtualPharmacyChargeFormDeliveryFee(Constants.ChargeSource.CHARGE);
        updateExpressDeliveryFeeAndProcessFee(Constants.ChargeSource.CHARGE);//这个场景就是收费台的推送
        checkAirPharmacyAndThrowException(Constants.ChargeSource.CHARGE);
        ApplyPromotionInfoResult applyPromotionInfoResult = fetchAndApplyPromotionInfo(new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), null, null, null, null);
//        BigDecimal adjustFee = calculateAdjustFee(null, MathUtils.wrapBigDecimalOrZero(chargeSheet.getOutpatientAdjustmentFee()), null, chargeSheet.getDraftAdjustmentFee());
        doCalculate(MathUtils.wrapBigDecimalOrZero(applyPromotionInfoResult.getAdjustmentFeeOnCalculate()), CalculateScene.PAY);
        return generateToSaveChargeSheet();
    }

    public StatRecordResult generateChargeTransactionRecords(List<CreateOrderView> airPharmacyOrders) {
        return statRecordProcessor.generateChargeTransactionRecords(new ArrayList<>(), airPharmacyOrders);
    }

    public StatRecordResult generateChargeTransactionRecords(List<ChargeTransactionRecord> addedRecords, List<CreateOrderView> airPharmacyOrders) {
        return statRecordProcessor.generateChargeTransactionRecords(addedRecords, airPharmacyOrders);
    }

    public StatRecordResult generateChargeTransactionRecords() {
        return generateChargeTransactionRecords(new ArrayList<>());
    }

    public StatRecordResult getRefundChargeStatRecordResult() {
        return Optional.ofNullable(refundChargeStatRecordResult).orElse(new StatRecordResult());
    }

    public List<ChargeFormItemBatchInfo> getCurrentRefundBatchInfos() {
        return formProcessors
                .stream()
                .flatMap(formProcessor -> formProcessor.getItemProcessorList().stream())
                .map(ItemProcessor::getCurrentRefundBatchInfos)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }


    private ChargeAdditionalFee insertChargeAdditionFee(int type, BigDecimal amount, boolean isRefund, String operatorId) {
        ChargeAdditionalFee chargeAdditionalFee = new ChargeAdditionalFee();
        chargeAdditionalFee.setAmount(amount);
        chargeAdditionalFee.setIsRefund(isRefund ? 1 : 0);
        chargeAdditionalFee.setType(type);
        chargeAdditionalFee.setId(AbcIdUtils.getUUID());
        chargeAdditionalFee.setPatientId(chargeSheet.getPatientId());
        chargeAdditionalFee.setPatientOrderId(chargeSheet.getPatientOrderId());
        chargeAdditionalFee.setClinicId(chargeSheet.getClinicId());
        chargeAdditionalFee.setChainId(chargeSheet.getChainId());
        chargeAdditionalFee.setChargeSheetId(chargeSheet.getId());
        FillUtils.fillCreatedBy(chargeAdditionalFee, operatorId);

        if (this.chargeSheet.getAdditionalFees() == null) {
            this.chargeSheet.setAdditionalFees(new ArrayList<>());
        }
        this.chargeSheet.getAdditionalFees().add(chargeAdditionalFee);
        return chargeAdditionalFee;
    }

    private BigDecimal getMemberPaidAmount() {
        if (chargeSheet.getChargeTransactions() == null) {
            return BigDecimal.ZERO;
        }

        return chargeSheet.getChargeTransactions()
                .stream()
                .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0
                        && chargeTransaction.getPayMode() == Constants.ChargePayMode.MEMBER_CARD)
                .map(ChargeTransaction::getAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private BigDecimal getHealthCardPaidAmount() {
        if (chargeSheet.getChargeTransactions() == null) {
            return BigDecimal.ZERO;
        }

        return chargeSheet.getChargeTransactions()
                .stream()
                .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.HEALTH_CARD)
                .filter(chargeTransaction -> !TextUtils.isEmpty(chargeTransaction.getThirdPartyPayTransactionId()))
                .map(ChargeTransaction::getAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public static BigDecimal calculateAdditionalFee(Collection<ChargeAdditionalFee> additionalFees, int type, boolean isRefund) {
        BigDecimal totalFee = BigDecimal.ZERO;
        if (additionalFees == null) {
            return totalFee;
        }

        totalFee = additionalFees.stream()
                .filter(additionalFee -> additionalFee.getIsDeleted() == 0)
                .filter(chargeAdditionalFee -> chargeAdditionalFee.getType() == type)
                .filter(chargeAdditionalFee -> chargeAdditionalFee.getIsRefund() == (isRefund ? 1 : 0))
                .map(ChargeAdditionalFee::getAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return totalFee;
    }


    public static BigDecimal calculateAdditionalFee(Collection<ChargeAdditionalFee> additionalFees, int type) {
        BigDecimal totalFee = BigDecimal.ZERO;
        if (additionalFees == null) {
            return totalFee;
        }

        totalFee = additionalFees.stream()
                .filter(chargeAdditionalFee -> chargeAdditionalFee.getType() == type)
                .map(chargeAdditionalFee -> chargeAdditionalFee.getIsRefund() == 1 ? chargeAdditionalFee.getAmount().negate() : chargeAdditionalFee.getAmount())
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return totalFee;
    }

    public static BigDecimal calculateTransactionsTotalReceivedFee(Collection<ChargeTransaction> chargeTransactions) {
        BigDecimal totalFee = BigDecimal.ZERO;
        if (chargeTransactions == null) {
            return totalFee;
        }

        totalFee = chargeTransactions.stream()
                .filter(Objects::nonNull)
                .filter(chargeTransaction -> chargeTransaction.getAmount().compareTo(BigDecimal.ZERO) > 0)
                .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                .map(ChargeTransaction::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return totalFee;
    }

    public static BigDecimal calculateTransactionsTotalRefundFee(Collection<ChargeTransaction> chargeTransactions) {
        BigDecimal totalFee = BigDecimal.ZERO;
        if (chargeTransactions == null) {
            return totalFee;
        }
        totalFee = chargeTransactions.stream()
                .filter(Objects::nonNull)
                .filter(chargeTransaction -> chargeTransaction.getAmount().compareTo(BigDecimal.ZERO) < 0)
                .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                .map(ChargeTransaction::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return totalFee;
    }

    public static BigDecimal calculateAdditionalTotalFee(Collection<ChargeAdditionalFee> additionalFees) {
        BigDecimal totalFee = BigDecimal.ZERO;
        if (additionalFees == null) {
            return totalFee;
        }
        totalFee = additionalFees.stream()
                .filter(additionalFee -> additionalFee.getIsDeleted() == 0)
                .map(additionalFee -> additionalFee.getIsRefund() == 1 ? MathUtils.wrapBigDecimalOrZero(additionalFee.getAmount()).negate() :
                        MathUtils.wrapBigDecimalOrZero(additionalFee.getAmount()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return totalFee;
    }

//    public static BigDecimal calculateAdditionalOwedRefundTotalFee(Collection<ChargeAdditionalFee> additionalFees) {
//        BigDecimal totalFee = BigDecimal.ZERO;
//        if (additionalFees == null) {
//            return totalFee;
//        }
//        totalFee = additionalFees.stream()
//                .filter(additionalFee -> additionalFee.getIsRefund() == 0 && additionalFee.getType() == ChargeAdditionalFee.AdditionalFeeType.OWED_REFUND_FEE)
//                .map(additionalFee -> MathUtils.wrapBigDecimalOrZero(additionalFee.getAmount()))
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
//        return totalFee;
//    }

    public List<DispensingSheetInfo> getDispensingSheetInfos() {
        return dispensingSheetInfos;
    }

    public List<GoodsLockBatchItem> getPreLockBatchInfo() {
        return formProcessors.stream().flatMap(formProcessor -> formProcessor.getPreLockBatchInfo().stream()).collect(Collectors.toList());
    }

    /**
     * readOnly方法才能使用
     *
     * @return
     */
    public ChargeSheet calculateAndGenerateChargeSheet() {
        updatePatientInfo();
        fetchAndSetMemberInfo(false);
        //将数据库的原价值保留下来，处理议价之后原价也发生变化的情况，原价发生变化，则议价也要跟着变化
        updateChargeFormItemOldSourceUnitPrice();

        updateProductInfo(true, false, 1, 1);
        /**
         * 更新虚拟药房的快递费
         */
        updateVirtualPharmacyChargeFormDeliveryFee(Constants.ChargeSource.CHARGE);
        updateExpressDeliveryFeeAndProcessFee(Constants.ChargeSource.CHARGE);//这里只是用默认值CHARGE
        //TODO [计算快递费和加工费]
        updateAirPharmacyChargeFormDeliveryFeeAndProcessFee();
        updateGoodsPharmacyViews();
        updateDispensingInfo();

        ApplyPromotionInfoResult applyPromotionInfoResult = fetchAndApplyPromotionInfo(new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), null, null, null, null);
        BigDecimal unchargedAdjustmentFee = BigDecimal.ZERO;
        if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED) {
//            unchargedAdjustmentFee = MathUtils.wrapBigDecimalAdd(chargeSheet.getOutpatientAdjustmentFee(), chargeSheet.getDraftAdjustmentFee());
            unchargedAdjustmentFee = applyPromotionInfoResult.getAdjustmentFeeOnCalculate();
            applyAdjustment(unchargedAdjustmentFee);
            applyChargeFormItemPromotionInfoUnitAdjustmentFee();
        }

        doCalculate(unchargedAdjustmentFee, chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_CHARGED ? CalculateScene.PAY_FOR_LEFT : CalculateScene.PAY);
        return generateToSaveChargeSheet();
    }

    public void updateGoodsFeeTypeView() {
        Set<Long> collect = formProcessors.stream().flatMap(formProcessor -> formProcessor.getItemProcessorList().stream())
                .flatMap(itemProcessor -> itemProcessor.getAllChargeFormItems().stream())
                .map(ChargeFormItem::getFeeTypeId).collect(Collectors.toSet());
        Map<Long, GoodsFeeTypeConstants.GoodsFeeTypeSortDto> feeTypeSortDtoMap = getSheetProcessorInfoProvider().getGoodsScProvider().queryGoodsFeeTypeNames(chainId, collect);
        if (MapUtils.isEmpty(feeTypeSortDtoMap)) {
            return;
        }
        setFeeTypeSortDtoMap(feeTypeSortDtoMap);
        formProcessors.forEach(formProcessor -> formProcessor.updateGoodsFeeTypeView(feeTypeSortDtoMap));
    }


    public void updateProcessShebaoInfo() {
        List<ChargeRuleProcessResult.ChargeRuleProcessRsp> chargeRuleProcessRsps = getFormProcessors().stream()
                .map(formProcessor -> {
                    ChargeSheetProcessInfo chargeSheetProcessInfo = formProcessor.getChargeSheetProcessInfo();
                    if (Objects.isNull(chargeSheetProcessInfo)) {
                        return null;
                    }
                    ChargeRuleProcessResult.ChargeRuleProcessRsp chargeRuleProcessRsp = new ChargeRuleProcessResult.ChargeRuleProcessRsp();
                    chargeRuleProcessRsp.setProcessUsage(JsonUtils.readValue(chargeSheetProcessInfo.getProcessUsageJson(), ChargeRuleProcessUsageView.class));
                    chargeRuleProcessRsp.setType(chargeSheetProcessInfo.getType());
                    chargeRuleProcessRsp.setSubType(chargeSheetProcessInfo.getSubType());
                    return chargeRuleProcessRsp;
                })
                .filter(Objects::nonNull)
                .filter(chargeSheetProcessInfo -> Objects.nonNull(chargeSheetProcessInfo.getProcessUsage())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(chargeRuleProcessRsps)) {
            return;
        }
        /**
         * 查询加工社保信息
         */
        if (getIsNeedUseLimitPrice()) {
            QueryProcessFeesMatchCodeReqBody req = new QueryProcessFeesMatchCodeReqBody();
            Map<String, List<ChargeRuleProcessResult.ChargeRuleProcessRsp>> processGroupByProcessId = ListUtils.groupByKey(chargeRuleProcessRsps, process -> Optional.ofNullable(process.getProcessUsage()).map(ChargeRuleProcessUsageView::getProcessId).orElse(""));
            req.setChainId(chainId);
            req.setClinicId(clinicId);
            req.setProcessList(new ArrayList<>());
            processGroupByProcessId.forEach((key, value) -> {
                QueryProcessFeesMatchCodeReqBody.ProcessInfo processInfoReq = new QueryProcessFeesMatchCodeReqBody.ProcessInfo();
                processInfoReq.setProcessId(key);
                processInfoReq.setSubProcesses(new ArrayList<>());
                value.forEach(v -> {
                    QueryProcessFeesMatchCodeReqBody.SubProcessInfo subProcessInfo = new QueryProcessFeesMatchCodeReqBody.SubProcessInfo();
                    subProcessInfo.setSubProcessId(Optional.ofNullable(v.getProcessUsage()).map(ChargeRuleProcessUsageView::getId).orElse(null));
                    subProcessInfo.setSubType(v.getSubType());
                    processInfoReq.getSubProcesses().add(subProcessInfo);
                });
                req.getProcessList().add(processInfoReq);
            });
            QueryProcessFeesMatchCodeResBody feesMatchCodeResBody = sheetProcessorInfoProvider.getShebaoInfoProvider().queryProcessFeesMatchCode(req);
            setProcessMatchCodes(feesMatchCodeResBody.getMatchCodes());
            getFormProcessors().forEach(formProcessor -> {
                ChargeSheetProcessInfo chargeSheetProcessInfo = formProcessor.getChargeSheetProcessInfo();
                if (Objects.isNull(chargeSheetProcessInfo)) {
                    return;
                }
                formProcessor.getItemProcessorList().stream()
                        .filter(itemProcessor -> itemProcessor.getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED)
                        .forEach(itemProcessor -> {
                            Optional<QueryProcessFeesMatchCodeResBody.SubProcessMatchCode> subProcessMatchCodeFinal = Optional.ofNullable(processMatchCodes)
                                    .orElse(new ArrayList<>())
                                    .stream()
                                    .filter(Objects::nonNull)
                                    .filter(processMatchCode -> Objects.equals(processMatchCode.getType(), chargeSheetProcessInfo.getType()))
                                    .flatMap(processMatchCode -> Optional.ofNullable(processMatchCode.getSubProcessMatchCodes()).orElse(new ArrayList<>()).stream())
                                    .filter(Objects::nonNull)
                                    .filter(subProcessMatchCode -> Objects.equals(subProcessMatchCode.getSubType(), chargeSheetProcessInfo.getSubType())).findFirst();
                            if (subProcessMatchCodeFinal.isPresent()) {
                                SheBaoMatchCode sheBaoMatchCode = new SheBaoMatchCode(subProcessMatchCodeFinal.get().getShebaoCode(), subProcessMatchCodeFinal.get().getShebaoPayMode());
                                itemProcessor.updateSheBaoMatchCode(sheBaoMatchCode);
                            }
                        });
            });
        }
    }

    @Override
    public List<ChargeFormItem> getStockAvailableItems() {
        return formProcessors.stream()
                .filter(formProcessor -> !formProcessor.isAirPharmacy())
                .flatMap(formProcessor -> formProcessor.getItemProcessorList().stream())
                .filter(ItemProcessor::getGoodsIsStockAvailable)
                .flatMap(itemProcessor -> itemProcessor.getAllChargeFormItems().stream())
                .collect(Collectors.toList());
    }
}
