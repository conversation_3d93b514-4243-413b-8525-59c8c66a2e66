package cn.abcyun.cis.charge.processor.calculator;

import cn.abcyun.cis.charge.util.MathUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Setter
@Accessors(chain = true)
public class ChargeFormItemPrintPriceInfo {

    @Getter
    private BigDecimal sourceTotalFee;

    private BigDecimal singlePromotionFee;

    private BigDecimal unitAdjustmentFee;

    private BigDecimal packagePromotionFee;

    private BigDecimal adjustmentFee;

    @Getter
    private BigDecimal refundSourceTotalFee;

    private BigDecimal refundSinglePromotionFee;

    private BigDecimal refundUnitAdjustmentFee;

    private BigDecimal refundPackagePromotionFee;

    private BigDecimal refundAdjustmentFee;

    public BigDecimal calculateSingleDiscountFee() {
        return MathUtils.wrapBigDecimalAdd(singlePromotionFee, unitAdjustmentFee);
    }

    public BigDecimal calculateRefundSingleDiscountFee() {
        return MathUtils.wrapBigDecimalAdd(refundSinglePromotionFee, refundUnitAdjustmentFee);
    }

    public BigDecimal calculatePackageDiscountFee() {
        return MathUtils.wrapBigDecimalAdd(packagePromotionFee, adjustmentFee);
    }

    public BigDecimal calculateRefundPackageDiscountFee() {
        return MathUtils.wrapBigDecimalAdd(refundPackagePromotionFee, refundAdjustmentFee);
    }

}
