package cn.abcyun.cis.charge.processor.callback;

import cn.abcyun.bis.rpc.sdk.cis.model.pay.PayCallbackRsp;
import cn.abcyun.cis.charge.api.model.PayCallbackContainPayModeReq;
import cn.abcyun.cis.charge.model.ChargePayTransaction;
import cn.abcyun.cis.charge.model.ChargeSheet;

public interface IChargeThirdPartyRefundCallbackProvider extends IChargeThirdPartyPayModeProvider{

    /**
     * 退款回调
     *
     * @param payCallbackReq
     * @return
     */
    PayCallbackRsp refundCallback(PayCallbackContainPayModeReq payCallbackReq, ChargeSheet chargeSheet, ChargePayTransaction chargePayTransaction);
}
