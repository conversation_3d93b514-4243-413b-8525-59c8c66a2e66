package cn.abcyun.cis.charge.processor.provider;


import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsTypeTuple;
import cn.abcyun.bis.rpc.sdk.cis.model.promotion.*;

import java.util.List;

public interface PromotionProvider {

    AvailablePromotionRsp fetchPromotions(String clinicId, String chainId, String memberId, String patientId, List<GoodsBaseInfo> goodsItems, List<GoodsTypeTuple> goodsTypeTuples, List<String> patientCardIds);

    List<PromotionPayCardsView> listPayCards(String chainId, String patientId, List<String> otherPatientCardIds, List<GoodsBaseInfo> waitPaidGoods);

    void deduct(CardPatientPresentsDeductReq req);

    /**
     * 根据卡ids查询卡详情
     * @param chainId
     * @param promotionIds
     */
    List<PatientCardView> listPatientsCardsByIds(String chainId, List<String> promotionIds);
}
