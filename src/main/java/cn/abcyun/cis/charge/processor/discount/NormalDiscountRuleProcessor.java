package cn.abcyun.cis.charge.processor.discount;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsItem;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsTypeTuple;
import cn.abcyun.bis.rpc.sdk.cis.model.promotion.Promotion;
import cn.abcyun.cis.charge.processor.PromotionQueryGiftGoodsDto;
import cn.abcyun.cis.charge.service.dto.PromotionAirPharmacyForm;
import cn.abcyun.cis.charge.service.dto.PromotionProductItem;
import cn.abcyun.cis.charge.service.dto.PromotionView;
import cn.abcyun.cis.charge.service.dto.SinglePromotionView;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.cis.commons.util.ListUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

public class NormalDiscountRuleProcessor extends DiscountRuleProcessor {
    protected Promotion.PromotionDetail promotionDetail;

    protected Map<String, Promotion.PromotionDetail.PromotionGoods> promotionGoodItemMap = new HashMap<>();

    protected Map<String, GoodsTypeTuple> goodsTypeTupleMap = new HashMap<>();

    public NormalDiscountRuleProcessor(Promotion promotion) {
        super(promotion);
        init(promotion);
    }

    public void init(Promotion promotion) {
        promotionDetail = promotion.getDetail();
        if (promotionDetail != null) {
            if (promotionDetail.getGoodsList() != null) {
                promotionGoodItemMap = promotionDetail.getGoodsList()
                        .stream()
                        .collect(Collectors.toMap(promotionGoods -> CalculatePromotionItem.convertPharmacyTypeGoodsIdKey(promotionGoods.getPharmacyType(), promotionGoods.getGoodsId()),
                                Function.identity(), (a, b) -> a));
            }
            if (promotionDetail.getGoodsTypeTupleList() != null) {
                goodsTypeTupleMap = ListUtils.toMap(promotionDetail.getGoodsTypeTupleList(), CalculatePromotionAirPharmacyForm::convertToKey);
            }
        }
    }


    @Override
    public void applyBestDiscount(List<CalculatePromotionItem> items, List<CalculatePromotionAirPharmacyForm> airPharmacyForms) {
        if (promotionDetail == null || (CollectionUtils.isEmpty(items) && CollectionUtils.isEmpty(airPharmacyForms))) {
            return;
        }

        if (!CollectionUtils.isEmpty(items)) {
            items.forEach(item -> applyDiscountCore(item, promotionGoodItemMap));
        }

        if (!CollectionUtils.isEmpty(airPharmacyForms)) {
            airPharmacyForms.forEach(form -> applyDiscountForAirPharmacyForm(goodsTypeTupleMap, form));
        }
    }

    @Override
    public PromotionView generatePromotionView(List<CalculatePromotionItem> items, List<CalculatePromotionAirPharmacyForm> airPharmacyForms) {
        if (promotionDetail == null) {
            return null;
        }

        PromotionView promotionView = new PromotionView();
        promotionView.setParentId(promotion.getParentId());
        promotionView.setId(promotion.getId());
        promotionView.setName(promotion.getName());
        promotionView.setType(promotion.getType());
        // promotionView.setRuleName(generateRuleName());
        promotionView.setParentId(promotion.getParentId());
        //自助取号机需要展示优惠卷的过期时间等，所以这里吧promotion的coupon信息返回给端上
        if (promotion.getDetail() != null && promotion.getDetail().getCoupons() != null) {
            List<Promotion.PromotionDetail.Coupon> coupons = promotion.getDetail().getCoupons();
            if (coupons.size() > 0) {
                promotionView.setCoupon(coupons.get(0));
            }
        }

        promotionView.addProductItems(getUsedPromotionProductItems(items));
        promotionView.addProductItems(getUsedPromotionProductItemsForAirPharmacy(airPharmacyForms));
        BigDecimal totalDiscountPrice = MathUtils.wrapBigDecimalAdd(
                promotionView.getProductItems().stream().map(PromotionProductItem::getDiscountPrice).reduce(BigDecimal.ZERO, BigDecimal::add),
                promotionView.getAirPharmacyForms().stream().map(PromotionAirPharmacyForm::getDiscountPrice).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add)
        );
        promotionView.setDiscountPrice(totalDiscountPrice);
        promotionView.setChecked(true);
        return promotionView;
    }

    @Override
    public SinglePromotionView generateSinglePromotionView(CalculateSinglePromotionItem item, Map<String, GoodsItem> singlePromotionGiftGoodsItemMap) {
        if (promotionDetail == null) {
            return null;
        }

        SinglePromotionView promotionView = new SinglePromotionView();
        promotionView.setId(promotion.getId());
        promotionView.setName(promotion.getName());
        promotionView.setType(promotion.getType());
        promotionView.setParentType(promotion.getParentType());
        promotionView.setParentId(promotion.getParentId());

        //自助取号机需要展示优惠卷的过期时间等，所以这里吧promotion的coupon信息返回给端上
        if (promotion.getDetail() != null && promotion.getDetail().getCoupons() != null) {
            List<Promotion.PromotionDetail.Coupon> coupons = promotion.getDetail().getCoupons();
            if (coupons.size() > 0) {
                promotionView.setCoupon(coupons.get(0));
            }
        }
        Promotion.PromotionDetail.PromotionGoods promotionGoodItem = promotionGoodItemMap.getOrDefault(item.getPharmacyTypeGoodsIdKey(), null);

        if (promotionGoodItem == null) {
            return null;
        }

        //如果item不参与赠品，则直接返回null
        if (!item.isCanApplyGiftPromotion() && Objects.equals(promotionGoodItem.getDiscountWay(), Promotion.PromotionDetail.DiscountWay.GIFT)) {
            return null;
        }

        DiscountRulePromotionGoodsCalculator promotionGoodsCalculator = DiscountRuleProcessorFactory.createPromotionGoodsCalculator(promotionGoodItem.getDiscountWay());

        if (promotionGoodsCalculator == null) {
            return null;
        }

        //计算对应的优惠金额
        DiscountRulePromotionGoodsCalculator.PromotionGoodsCalculateResult promotionGoodsCalculateResult = promotionGoodsCalculator.calculateDiscountPrice(item, promotion.getId(), promotionGoodItem, singlePromotionGiftGoodsItemMap);

        if (promotionGoodsCalculateResult == null || promotionGoodsCalculateResult.getIsHit() != 1) {
            return null;
        }


        promotionView.setHitRuleDetail(promotionGoodsCalculateResult.getHitRuleDetail());
        promotionView.setDiscountPrice(promotionGoodsCalculateResult.getDiscountPrice());
        promotionView.setDisplayDiscountPrice(promotionGoodsCalculateResult.calculateDisplayDiscountPrice());
        promotionView.setParticipationDiscountCount(promotionGoodsCalculateResult.getParticipationDiscountCount());
        promotionView.setLeftSaleCount(promotionGoodItem.getLeftSaleCount());
        promotionView.setPromotionGoods(promotionGoodItem);
        promotionView.setGiftGoodsList(promotionGoodsCalculateResult.getGiftGoodsList());
        promotionView.setChecked(false);
        return promotionView;
    }

    /**
     * 更新限购规则的限购数量
     * @param item
     * @param bestSinglePromotion
     */
    public void updateLeftSaleCount(CalculateSinglePromotionItem item, SinglePromotionView bestSinglePromotion) {

        if (Objects.isNull(bestSinglePromotion) || bestSinglePromotion.getParticipationDiscountCount() == null) {
            return;
        }

        Promotion.PromotionDetail.PromotionGoods promotionGoodItem = promotionGoodItemMap.getOrDefault(item.getPharmacyTypeGoodsIdKey(), null);

        if (Objects.isNull(promotionGoodItem)) {
            return;
        }

        if (Objects.isNull(promotionGoodItem.getLeftSaleCount())) {
            return;
        }

        promotionGoodItem.setLeftSaleCount(MathUtils.max(BigDecimal.ZERO, MathUtils.wrapBigDecimalSubtract(promotionGoodItem.getLeftSaleCount(), bestSinglePromotion.getParticipationDiscountCount())));
    }


    @Override
    public PromotionQueryGiftGoodsDto generatePromotionQueryGiftGoodsDto(CalculateSinglePromotionItem item) {

        if (promotionDetail == null) {
            return null;
        }

        Promotion.PromotionDetail.PromotionGoods promotionGoodItem = promotionGoodItemMap.getOrDefault(item.getPharmacyTypeGoodsIdKey(), null);

        if (promotionGoodItem == null) {
            return null;
        }

        DiscountRulePromotionGoodsCalculator.GiftGoodsCalculator giftGoodsCalculator = DiscountRuleProcessorFactory.createGiftGoodsCalculator();

        if (giftGoodsCalculator == null) {
            return null;
        }

        DiscountRulePromotionGoodsCalculator.PromotionGiftGoodsResult giftGoodsResult = giftGoodsCalculator.calculateGiftGoods(item, promotionGoodItem);

        if (Objects.isNull(giftGoodsResult)) {
            return null;
        }

        return new PromotionQueryGiftGoodsDto()
                .setPromotionId(promotion.getId())
                .setChargeFormItemId(item.getId())
                .setGoodsId(item.getProductId())
                .setGiftGoodsId(giftGoodsResult.getGoodsId())
                .setGiftGoodsCount(giftGoodsResult.getCount())
                .setIsDismounting(giftGoodsResult.getIsDismounting());
    }

    @Override
    public PromotionQueryGiftGoodsDto generateOnComingPromotionQueryGiftGoodsDto(CalculateSinglePromotionItem item) {

        if (promotionDetail == null) {
            return null;
        }

        Promotion.PromotionDetail.PromotionGoods promotionGoodItem = promotionGoodItemMap.getOrDefault(item.getPharmacyTypeGoodsIdKey(), null);

        if (promotionGoodItem == null) {
            return null;
        }

        DiscountRulePromotionGoodsCalculator.GiftGoodsCalculator giftGoodsCalculator = DiscountRuleProcessorFactory.createGiftGoodsCalculator();

        if (giftGoodsCalculator == null) {
            return null;
        }

        DiscountRulePromotionGoodsCalculator.PromotionGiftGoodsResult giftGoodsResult = giftGoodsCalculator.calculateOnComingGiftGoods(item, promotionGoodItem);

        if (Objects.isNull(giftGoodsResult)) {
            return null;
        }

        return new PromotionQueryGiftGoodsDto()
                .setPromotionId(promotion.getId())
                .setChargeFormItemId(item.getId())
                .setGoodsId(item.getProductId())
                .setGiftGoodsId(giftGoodsResult.getGoodsId())
                .setGiftGoodsCount(giftGoodsResult.getCount())
                .setIsDismounting(giftGoodsResult.getIsDismounting());
    }

    @Override
    public boolean isAvailableRuleForItem(CalculatePromotionItem item) {
        if (promotionDetail == null || item == null) {
            return false;
        }
        boolean available = false;

        Promotion.PromotionDetail.PromotionGoods promotionGoodItem = promotionGoodItemMap.getOrDefault(item.getPharmacyTypeGoodsIdKey(), null);
        if (promotionGoodItem != null) {
            available = true;
        }
        return available;
    }

    @Override
    public boolean isAvailableRuleForSingleItem(CalculateSinglePromotionItem item) {
        if (promotionDetail == null || item == null) {
            return false;
        }

        return promotionGoodItemMap.getOrDefault(item.getPharmacyTypeGoodsIdKey(), null) != null;
    }

    @Override
    public boolean isAvailableRuleForAirPharmacyForm(CalculatePromotionAirPharmacyForm form) {
        if (goodsTypeTupleMap == null || form == null) {
            return false;
        }
        boolean available = false;

        GoodsTypeTuple goodsTypeTuple = goodsTypeTupleMap.getOrDefault(CalculatePromotionAirPharmacyForm.convertToKey(form), null);
        if (goodsTypeTuple != null) {
            available = true;
        }
        return available;
    }
}
