package cn.abcyun.cis.charge.processor.limitprice;

import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsSheBaoMatchedCodesInfo;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.ShebaoPayLimitPriceInfo;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.model.ChargeMedicareLimitPriceType;
import cn.abcyun.cis.charge.util.MathUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 处理医保限价
 */
public class LimitPriceProcessor {


    /**
     * 匹配限价
     *
     * @param items
     */
    public void process(List<CalculateLimitPriceItem> items) {

        if (CollectionUtils.isEmpty(items)) {
            return;
        }

        items.forEach(item -> {
            item.initShebaoReceivablePrice();
            calculateLimitPrice(item);
        });

    }

    private static void calculateLimitPrice(CalculateLimitPriceItem item) {

        if (Objects.isNull(item)) {
            return;
        }

        //单瓶限价如果是费用母项 只要子项中有一个符合有社保码且能使用社保支付的条件则母项可以被限价
        if (!item.getCanPayByHealthCard() || !item.hasGoodsLimitInfo()) {
            return;
        }


        /**
         * 2024-12 改版，如果父项是费用母项，则子项限价规则由子项决定，不再使用父项限价规则
         */
        if (item.getIsCompose() == YesOrNo.YES || item.getIsFeeParent() == YesOrNo.YES) {
                item.getComposeChildren().forEach(LimitPriceProcessor::calculateLimitPrice);
                summaryPrentInfo(item);
        } else {
            calculateLimitPriceCore(item);
        }
    }

    /**
     * 1.单价超过限价单价或者总价超过限价总价目前都认为是触发了限价
     * 这样是为了避免以下情况：item的单价：0.1111    数量：5  总价：0.5555保留两位0.56    限价单价0.1112    这里向下保留两位小数
     * 改动前：如果只比较单价的话就不会触发限价，结果就是总价为 0.56  单价为 0.1111
     * 这样的数据到了社保那边后会触发反算 即使用 0.56÷5 = 0.112 算出单价后上报，这样就会触发限价。
     *
     * 改动后: 总价大于限价总价（这里的限价总价为限价单价乘数量 不处理精度） 触发限价   item落地为单价
     *
     *
     *
     * 2.计算限价总价时实行向下保留两位小数，如果item或者batch被限价了需要利用限价总价重新计算限价单价以及限价零头
     * 这样来保证item上单价乘数量加上零头 四舍五入等于总价后，社保那边拿到总价反算单价时不会超过限价价格
     *
     * 改动前：原单价0.1323 数量为8 总价为0.1323×8=1.06  限价单价为0.1322
     * 触发限价后 item记录价格为 1.06  单价为 0.1322
     * 这样的数据到了社保那边后会触发反算 即使用 1.06÷8 = 0.1325 算出单价后上报，这样就会触发限价。
     *
     * 改动后：原单价0.1323 数量为8 总价为0.1323×8=1.06  限价单价为0.1322  限价总价为1.0576(用于比较的限价总价)
     * 原单价大于限价单价 触发限价。 这个时候重新计算限价总价向下为 1.05 计算限价单价向下为 0.1312
     * item记录价格为 1.05  单价为 0.1312
     */
    private static void calculateLimitPriceCore(CalculateLimitPriceItem item) {
        if (Objects.isNull(item) || !item.hasGoodsLimitInfo()) {
            return;
        }

        /**
         * 和goods约定 即使是
         */

        //有单品限价优先使用单品限价
        if (item.getGoodsLimitPriceInfo().getType() == ShebaoPayLimitPriceInfo.LimitInfoType.PRODUCT || item.getGoodsLimitPriceInfo().getType() == ShebaoPayLimitPriceInfo.LimitInfoType.SHEBAO || CollectionUtils.isEmpty(item.getBatchInfos())) {

            calculateLimitPriceFormProductRule(item);
        } else {

            batchLimitProcess(item, ShebaoPayLimitPriceInfo.LimitInfoType.TYPES);
        }

    }

    private static void batchLimitProcess(CalculateLimitPriceItem item, int type) {

        if (CollectionUtils.isEmpty(item.getBatchInfos())) {
            return;
        }

        AtomicReference<Integer> exceedLimitPriceRule = new AtomicReference<>();

        item.getBatchInfos()
                .stream()
                .forEach(batchInfo -> {

                    GoodsLimitPriceInfo goodsLimitPriceInfo = null;
                    /**
                     * 如果是含有批次的goods的类型限价 则使用批次上的限价信息 如果批次上的限价信息为空 则 todo
                     * 否则使用goods上的限价信息
                     */
                    if (type == ShebaoPayLimitPriceInfo.LimitInfoType.TYPES) {
                        goodsLimitPriceInfo = batchInfo.getGoodsLimitPriceInfo();

                    } else {
                        goodsLimitPriceInfo = item.getGoodsLimitPriceInfo();
                    }


                    if (Objects.isNull(goodsLimitPriceInfo)) {
                        return;
                    }
                    exceedLimitPriceRule.set(goodsLimitPriceInfo.getExceedLimitPriceRule());
                    BigDecimal limitUnitPrice = goodsLimitPriceInfo.getLimitPrice();
                    ChargeMedicareLimitPriceType.LimitDetail limitDetail = goodsLimitPriceInfo.getLimitDetail();
                    if (Objects.isNull(limitUnitPrice)) {
                        return;
                    }

                    BaseLimitPriceInfo.LimitInfo limitInfo = new BaseLimitPriceInfo.LimitInfo();
                    limitInfo.setType(type)
                            .setLimitPrice(limitUnitPrice)
                            .setLimitFractionPrice(BigDecimal.ZERO)
                            .setLimitDetail(limitDetail)
                            .setLimitPriceFromWay(CalculateLimitPriceItem.LimitPriceFromWay.BATCH)
                            .setExceedLimitPriceRule(goodsLimitPriceInfo.getExceedLimitPriceRule());

                    batchInfo.compareAndSetLimitPriceInfo(limitInfo);


                });


        calculateLimitPriceFormBatchRule(exceedLimitPriceRule.get(), item);

    }

    public static void calculateLimitPriceFormBatchRule(int exceedLimitPriceRule, CalculateLimitPriceItem item) {
        //判断批次列表中是否有限价
        CalculateLimitPriceItem.CalculateLimitPriceItemBatchInfo firstLimitedItemBatchInfo = item.getBatchInfos().stream()
                .filter(CalculateLimitPriceItem.CalculateLimitPriceItemBatchInfo::isLimited)
                .findFirst()
                .orElse(null);

        BigDecimal batchInfoShebaoReceivableTotalPrice = item.getBatchInfos().stream()
                .map(CalculateLimitPriceItem.CalculateLimitPriceItemBatchInfo::getSheBaoReceivablePrice)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        //根据批次限价的社保应收总金额反算item的社保应收单价
        MathUtils.CalculateExpectedUnitPriceResult calculateExpectedUnitPriceResult = MathUtils.calculateExpectedUnitPrice(item.getUnitCount(), item.getDoseCount(), batchInfoShebaoReceivableTotalPrice);

        BaseLimitPriceInfo.LimitInfo itemLimitInfo = new BaseLimitPriceInfo.LimitInfo();
        itemLimitInfo.setType(BaseLimitPriceInfo.LimitInfoType.TYPES);
        itemLimitInfo.setLimitPrice(calculateExpectedUnitPriceResult.expectedUnitPrice);
        itemLimitInfo.setLimitTotalPrice(batchInfoShebaoReceivableTotalPrice);
        itemLimitInfo.setLimitPriceFromWay(CalculateLimitPriceItem.LimitPriceFromWay.BATCH);
        itemLimitInfo.setLimitFractionPrice(calculateExpectedUnitPriceResult.fractionPrice);
        itemLimitInfo.setExceedLimitPriceRule(exceedLimitPriceRule);
        itemLimitInfo.setLimitDetail(item.getGoodsLimitPriceInfo().getLimitDetail());
        /**
         * 计算限价差值
         */
        itemLimitInfo.calculateLimitFee(item.getReceivableTotalPrice());

        /**
         * item社保应收等于批次社保应收的和
         */
        item.setSheBaoReceivablePrice(batchInfoShebaoReceivableTotalPrice);
        item.setLimitInfo(itemLimitInfo);

        /**
         * 计算item的限价单价上限
         */
        BigDecimal upperLimitTotalPrice = item.getBatchInfos().stream().map(batch ->
                MathUtils.wrapBigDecimalMultiply(batch.getLimitTotalPrice(), batch.getTotalCount())
        ).reduce(BigDecimal.ZERO, BigDecimal::add);
        MathUtils.CalculateExpectedUnitPriceResult upperUnitPriceResult = MathUtils.calculateExpectedUnitPrice(item.getUnitCount(), item.getDoseCount(), upperLimitTotalPrice);
        item.setUpperLimitUnitPrice(upperUnitPriceResult.expectedUnitPrice);


        if (Objects.isNull(firstLimitedItemBatchInfo)) {
            return;
        }

        item.setExceedLimitPriceRule(exceedLimitPriceRule);


        item.setLimited(true);
        item.setLimitPriceFromWay(CalculateLimitPriceItem.LimitPriceFromWay.BATCH);
        item.setLimitUnitPrice(calculateExpectedUnitPriceResult.expectedUnitPrice);
        item.setLimitTotalPrice(batchInfoShebaoReceivableTotalPrice);
        item.setLimitFractionPrice(calculateExpectedUnitPriceResult.fractionPrice);
    }


    public static void summaryPrentInfo(CalculateLimitPriceItem item) {

        if (CollectionUtils.isEmpty(item.getComposeChildren())) {
            return;
        }

        item.setSheBaoReceivablePrice(item.getComposeChildren()
                .stream()
                .map(CalculateLimitPriceItem::getSheBaoReceivablePrice)
                .filter(Objects::nonNull)
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO));

        /**
         * 如果套餐子项中有一项为限价不转自费，则母项设置不转自费（这个是为了清空优惠）
         */
        int composeItemExceedLimitPriceRule = Constants.ExceedLimitPriceRule.SELF_PAY;
        if (item.getComposeChildren().stream().anyMatch(child -> child.isLimited() && child.getExceedLimitPriceRule() == Constants.ExceedLimitPriceRule.NO_PAY)) {
            composeItemExceedLimitPriceRule = Constants.ExceedLimitPriceRule.NO_PAY;
        }

        /**
         * 汇总母项信息
         */
        if (item.getComposeChildren().stream().anyMatch(CalculateLimitPriceItem::isLimited)) {
            calculateLimitPriceFormTypeRuleSalePriceByChildren(composeItemExceedLimitPriceRule, item);
        }

    }


    private static void calculateLimitPriceFormTypeRuleSalePriceByChildren(Integer exceedLimitPriceRule, CalculateLimitPriceItem item) {

        item.setExceedLimitPriceRule(exceedLimitPriceRule);
        BigDecimal batchInfoShebaoReceivableTotalPrice = item.getComposeChildren().stream()
                .map(CalculateLimitPriceItem::getSheBaoReceivablePrice)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);


        //根据批次限价的社保应收总金额反算item的社保应收单价
        MathUtils.CalculateExpectedUnitPriceResult calculateExpectedUnitPriceResult = MathUtils.calculateExpectedUnitPrice(item.getUnitCount(), item.getDoseCount(), batchInfoShebaoReceivableTotalPrice);
        item.setLimited(true);
        item.setLimitPriceFromWay(CalculateLimitPriceItem.LimitPriceFromWay.ITEM);
        item.setLimitUnitPrice(calculateExpectedUnitPriceResult.expectedUnitPrice);
        item.setLimitTotalPrice(batchInfoShebaoReceivableTotalPrice);
        item.setLimitFractionPrice(calculateExpectedUnitPriceResult.fractionPrice);

        BaseLimitPriceInfo.LimitInfo itemLimitInfo = new BaseLimitPriceInfo.LimitInfo();
        itemLimitInfo.setType(BaseLimitPriceInfo.LimitInfoType.SHEBAO);
        itemLimitInfo.setLimitPrice(item.getLimitUnitPrice());
        itemLimitInfo.setLimitTotalPrice(item.getLimitTotalPrice());
        itemLimitInfo.setLimitPriceFromWay(item.getLimitPriceFromWay());
        itemLimitInfo.setLimitFractionPrice(item.getLimitFractionPrice());
        itemLimitInfo.setExceedLimitPriceRule(item.getExceedLimitPriceRule());

        BigDecimal selfPayPrice = item.getComposeChildren().stream().filter(CalculateLimitPriceItem::isLimited).map(childItem -> childItem.getLimitInfo().getSelfPayPrice()).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);

        /**
         * 不转自费差值
         */
        BigDecimal notSelfPayPrice = item.getComposeChildren().stream().filter(CalculateLimitPriceItem::isLimited).map(childItem -> childItem.getLimitInfo().getNotSelfPayPrice()).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        itemLimitInfo.setNotSelfPayPrice(notSelfPayPrice);
        itemLimitInfo.setSelfPayPrice(selfPayPrice);

        item.setLimitInfo(itemLimitInfo);
    }



    /**
     * 单品限价或者医保目录限价
     *
     * @param item
     */
    private static void calculateLimitPriceFormProductRule(CalculateLimitPriceItem item) {

        if (Objects.isNull(item) || !item.hasGoodsLimitInfo()) {
            return;
        }

        GoodsLimitPriceInfo goodsLimitPriceInfo = item.getGoodsLimitPriceInfo();

        BigDecimal limitUnitPrice = goodsLimitPriceInfo.getLimitPrice();

        if (!CollectionUtils.isEmpty(item.getBatchInfos())) {
            batchLimitProcess(item, goodsLimitPriceInfo.getType());
        } else {
            BaseLimitPriceInfo.LimitInfo limitInfo = new BaseLimitPriceInfo.LimitInfo();
            limitInfo.setType(goodsLimitPriceInfo.getType())
                    .setLimitDetail(goodsLimitPriceInfo.getLimitDetail())
                    .setLimitPriceFromWay(CalculateLimitPriceItem.LimitPriceFromWay.ITEM)
                    .setLimitFractionPrice(BigDecimal.ZERO)
                    .setLimitPrice(limitUnitPrice)
                    .setExceedLimitPriceRule(goodsLimitPriceInfo.getExceedLimitPriceRule());


            item.compareAndSetLimitPriceInfo(limitInfo);

            /**
             * 改版后母项不会走到这里，所以不需要处理
             */
//            //这里如果是母项 需要将子项的限价的信息也构建出来 且母项的社保应收需要通过子项的社保应收价格汇总
//            if (!CollectionUtils.isEmpty(item.getComposeChildren())) {
//                buildFeeTypeChildrenLimitInfo(item.getComposeChildren(), limitInfo, item.isLimited());
//
//                item.setSheBaoReceivablePrice(item.getComposeChildren().stream().map(child -> child.getSheBaoReceivablePrice()).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
//
//            }
        }


    }

    /**
     * 判断是否限价
     * @param limitPrice
     * @param receivableUnitPrice
     * @param receivableTotalPrice
     * @param count
     * @return
     */
    public static boolean ifLimited(BigDecimal limitPrice, BigDecimal receivableUnitPrice, BigDecimal receivableTotalPrice, BigDecimal count) {

        if (Objects.isNull(limitPrice) || Objects.isNull(receivableUnitPrice) || Objects.isNull(receivableTotalPrice) || Objects.isNull(count)) {
            return false;
        }

        // 限价单价乘以数量是否小于应收总价
        BigDecimal limitTotalPrice = limitPrice.multiply(count);

        // 如果限价单价小于应收单价，则触发限价
        return limitPrice.compareTo(receivableUnitPrice) < 0 || limitTotalPrice.compareTo(receivableTotalPrice) < 0;
    }
}
