package cn.abcyun.cis.charge.processor.stat.calculator;

import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.cis.charge.helper.ChargeTransactionRecordDiscountInfoHelper;
import cn.abcyun.cis.charge.model.ChargeTransaction;
import cn.abcyun.cis.charge.processor.FlatReceivedPriceHelper;
import cn.abcyun.cis.charge.processor.StatRecordProcessor;
import cn.abcyun.cis.charge.processor.limitprice.BaseLimitPriceInfo;
import cn.abcyun.cis.charge.processor.stat.StatRecordBatchInfoCalculateCell;
import cn.abcyun.cis.charge.processor.stat.StatRecordByChooseCalculateCell;
import cn.abcyun.cis.charge.service.dto.ChargeDiscountInfo;
import cn.abcyun.cis.charge.util.FlatPriceTool;
import cn.abcyun.cis.charge.util.JsonUtils;
import cn.abcyun.cis.charge.util.ListUtils;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.common.log.marker.AbcLogMarker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class StatRecordPartedRefundCalculator extends StatRecordAbstractCalculator {

    private final IStatRecordBatchInfoCalculator batchInfoPartedRefundCalculator;
    private final IStatRecordBatchInfoCalculator batchInfoRefundCalculator;


    public StatRecordPartedRefundCalculator() {
        batchInfoPartedRefundCalculator = new BatchInfoPartedRefundCalculator();
        batchInfoRefundCalculator = new BatchInfoRefundCalculator();
    }

    @Override
    public BigDecimal flatCell(ChargeTransaction chargeTransaction, List<StatRecordByChooseCalculateCell> cells, BigDecimal toRecordPrice, int refundFlatType) {

        //本次受影响的收费项的折后总金额
        BigDecimal discountedPriceSum = cells.stream().map(StatRecordByChooseCalculateCell::getDiscountedPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        //本次已经记录了record表的折后总金额
        BigDecimal recordDiscountedPriceSum = cells.stream().map(StatRecordByChooseCalculateCell::getRecordedDiscountedPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        //剩余需要记录到record表的折后总金额
        BigDecimal leftToRecordDiscountedPriceSum = discountedPriceSum.subtract(recordDiscountedPriceSum);

        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "discountedPriceSum: {}, recordDiscountedPriceSum: {}, leftToRecordDiscountedPriceSum: {}", discountedPriceSum, recordDiscountedPriceSum, leftToRecordDiscountedPriceSum);

        //这是最后一发
        if (MathUtils.wrapBigDecimalCompare(leftToRecordDiscountedPriceSum, toRecordPrice) <= 0) {
            for (StatRecordByChooseCalculateCell cell : cells) {
                cell.setToRecordDiscountedPrice(cell.getLeftToRecordDiscountedPrice());
                cell.setToRecordTotalCostPrice(cell.getTotalCostPrice().subtract(cell.getRecordedTotalCostPrice()));
                cell.setToRecordDiscountPrice(cell.getDiscountPrice().subtract(cell.getRecordedDiscountPrice()));
                cell.setToRecordPromotionDiscountInfo(ChargeTransactionRecordDiscountInfoHelper.generateToRecordChargeTransactionRecordDiscountInfoForLastRecord(cell.getItemPromotionInfo(), cell.getRecordedChargeTransactionRecordDiscountInfo()));
            }
            return toRecordPrice.subtract(leftToRecordDiscountedPriceSum);
        }


        BigDecimal leftToRecordPrice = toRecordPrice;

        //退费时要基于金额去平摊
        //计算本次退费选择的项目的能退的总金额
        BigDecimal cellLeftCanRecordPriceSum = cells.stream()
                .map(StatRecordByChooseCalculateCell::getLeftCanRecordPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        //比较toRecordPrice和cellLeftCanRecordPriceSum
        if (toRecordPrice.compareTo(cellLeftCanRecordPriceSum) < 0) {
            //本次退费为选择项目之后改小了金额，可能为全部项目的部分退费，也可能为部分项目的部分退费，需要将toRecordPrice进行平摊

            List<String> handledIds = new ArrayList<>();
            if (refundFlatType == StatRecordProcessor.RefundFlatType.ORIGINAL_PAID_ITEM_FLAT && StringUtils.isNotEmpty(chargeTransaction.getAssociateTransactionId())) {
                for (StatRecordByChooseCalculateCell calculateCell : cells) {
                    BigDecimal toRecordDiscountedPrice = MathUtils.min(calculateCell.getRecordedDiscountedPriceByPayChargeTransactionId(chargeTransaction.getAssociateTransactionId()), calculateCell.getLeftCanRecordPrice());
                    calculateCell.setToRecordDiscountedPrice(toRecordDiscountedPrice);
                    handledIds.add(calculateCell.getId());
                    leftToRecordPrice = leftToRecordPrice.subtract(toRecordDiscountedPrice);
                }
            }

            if (leftToRecordPrice.compareTo(BigDecimal.ZERO) != 0) {
                FlatReceivedPriceHelper flatReceivedPriceHelper = new FlatReceivedPriceHelper(leftToRecordPrice);

                List<FlatReceivedPriceHelper.FlatCell> flatCells = cells
                        .stream()
                        .filter(calculateCell -> !handledIds.contains(calculateCell.getId()))
                        .map(calculateCell -> {
                            //剩余可记录的总金额
                            BigDecimal leftCanRecordPrice = calculateCell.getLeftCanRecordPrice();
                            return new FlatReceivedPriceHelper.FlatCell()
                                    .setId(calculateCell.getId())
                                    .setTotalPrice(leftCanRecordPrice)
                                    .setMaxFlatPrice(leftCanRecordPrice);
                        }).collect(Collectors.toList());
                flatReceivedPriceHelper.flat(flatCells);

                Map<String, FlatReceivedPriceHelper.FlatCell> flatCellMap = ListUtils.toMap(flatCells, FlatReceivedPriceHelper.FlatCell::getId);

                for (StatRecordByChooseCalculateCell calculateCell : cells) {
                    FlatReceivedPriceHelper.FlatCell flatCell = flatCellMap.getOrDefault(calculateCell.getId(), null);
                    if (Objects.isNull(flatCell)) {
                        continue;
                    }
                    BigDecimal toRecordDiscountedPrice = Optional.ofNullable(flatCell.getFlatPrice()).orElse(BigDecimal.ZERO);
                    calculateCell.setToRecordDiscountedPrice(toRecordDiscountedPrice);
                    leftToRecordPrice = leftToRecordPrice.subtract(toRecordDiscountedPrice);
                }
            }

            IStatRecordCalculator.fillOtherToRecordFields(cells);
        } else {
            //本次退费会退欠退金额，需要记录选择项目的普通退费数据，同时需要对欠退金额进行摊费记录
            cells.forEach(cell -> {
                cell.setToRecordDiscountedPrice(cell.getLeftCanRecordPrice());
                cell.setToRecordTotalCostPrice(cell.getTotalCostPrice().subtract(cell.getRecordedTotalCostPrice()));
                cell.setToRecordDiscountPrice(cell.getDiscountPrice().subtract(cell.getRecordedDiscountPrice()));
                cell.setToRecordPromotionDiscountInfo(ChargeTransactionRecordDiscountInfoHelper.generateToRecordChargeTransactionRecordDiscountInfoForLastRecord(cell.getItemPromotionInfo(), cell.getRecordedChargeTransactionRecordDiscountInfo()));
            });
            leftToRecordPrice = leftToRecordPrice.subtract(cellLeftCanRecordPriceSum);
        }

        return leftToRecordPrice;
    }

    @Override
    public void flatCellBatchInfo(List<StatRecordByChooseCalculateCell> cells) {

        for (StatRecordByChooseCalculateCell cell : cells) {
            if (!cell.canDealBatchInfo()) {
                continue;
            }

            //判断是否退完，如果退完了，直接用减法，如果没退完，才走平摊逻辑
            if (MathUtils.wrapBigDecimalCompare(cell.getToRecordDiscountedPriceResult(), cell.getLeftToRecordDiscountedPrice()) == 0) {
                //把剩余的数据直接赋值到批次cell上
                super.doCalculateBatchInfo(cell, batchInfoRefundCalculator);
            } else {
                super.doCalculateBatchInfo(cell, batchInfoPartedRefundCalculator);
            }
        }
    }


    public static class BatchInfoPartedRefundCalculator implements IStatRecordBatchInfoCalculator {

        @Override
        public void flatCellBatchInfoDiscountedPrice(StatRecordByChooseCalculateCell calculateCell) {
            FlatPriceTool.flatReceivedPriceAndApply(calculateCell.getToRecordDiscountedPrice(), calculateCell.getBatchInfoCalculateCells()
                    .stream()
                    .map(batchInfoCalculateCell -> new FlatPriceTool.IFlatCell<FlatReceivedPriceHelper.FlatCell>() {
                        @Override
                        protected FlatReceivedPriceHelper.FlatCell genFlatCell() {
                            return new FlatReceivedPriceHelper.FlatCell()
                                    .setId(batchInfoCalculateCell.getId())
                                    .setTotalPrice(batchInfoCalculateCell.getDiscountedPrice())
                                    .setMaxFlatPrice(batchInfoCalculateCell.getLeftToRecordDiscountedPrice());
                        }

                        @Override
                        protected void apply(BigDecimal flatPrice) {
                            batchInfoCalculateCell.setToRecordDiscountedPrice(flatPrice);
                        }
                    }).collect(Collectors.toList())
            );
        }

        @Override
        public void flatCellBatchInfoDiscountInfo(StatRecordByChooseCalculateCell calculateCell) {
            ChargeDiscountInfo toRecordPromotionDiscountInfo = calculateCell.getToRecordDiscountInfoResult();
            if (Objects.isNull(toRecordPromotionDiscountInfo)) {
                return;
            }

            ChargeDiscountInfo upperLimitPromotionInfo = toRecordPromotionDiscountInfo;
            //顺序，整单议价 -> 折扣 -> 单项议价
            for (StatRecordBatchInfoCalculateCell batchInfoCalculateCell : calculateCell.getBatchInfoCalculateCells()) {
                ChargeDiscountInfo leftToRecordPromotionInfo = batchInfoCalculateCell.getLeftToRecordPromotionInfo();

                if (MathUtils.wrapBigDecimalCompare(batchInfoCalculateCell.getDiscountedPrice(), BigDecimal.ZERO) == 0) {
                    batchInfoCalculateCell.setToRecordPromotionDiscountInfo(leftToRecordPromotionInfo);
                    continue;
                }

                ChargeTransactionRecordDiscountInfoHelper.FlatBatchInfoPromotionInfoResult flatBatchInfoPromotionInfoResult = ChargeTransactionRecordDiscountInfoHelper.flatBatchInfoPromotionInfo(
                        upperLimitPromotionInfo,
                        batchInfoCalculateCell.getPromotionInfo(),
                        leftToRecordPromotionInfo,
                        batchInfoCalculateCell.getToRecordDiscountedPrice(),
                        batchInfoCalculateCell.getDiscountedPrice(),
                        Optional.ofNullable(batchInfoCalculateCell.getChargeFormItemBatchInfo().getLimitInfo()).map(BaseLimitPriceInfo.LimitInfo::getExceedLimitPriceRule).orElse(YesOrNo.YES) == YesOrNo.NO);
                batchInfoCalculateCell.setToRecordPromotionDiscountInfo(flatBatchInfoPromotionInfoResult.getBatchPromotionInfo());
                batchInfoCalculateCell.setToRecordSourceTotalPrice(flatBatchInfoPromotionInfoResult.getSourceTotalPrice());

                //merge upperLimitDiscountInfo
                upperLimitPromotionInfo = ChargeTransactionRecordDiscountInfoHelper.mergeLeftPromotionInfo(upperLimitPromotionInfo, flatBatchInfoPromotionInfoResult.getBatchPromotionInfo());
            }

            //如果还有剩余，则从第一个批次把零头放进去
            if (upperLimitPromotionInfo != null && !upperLimitPromotionInfo.isDiscountPriceEmpty()) {

                for (StatRecordBatchInfoCalculateCell batchInfoCalculateCell : calculateCell.getBatchInfoCalculateCells()) {

                    ChargeDiscountInfo leftToRecordPromotionInfo = batchInfoCalculateCell.getLeftToRecordPromotionInfo();

                    BigDecimal fractionPrice = ChargeTransactionRecordDiscountInfoHelper.addFractionPromotionInfo(leftToRecordPromotionInfo, batchInfoCalculateCell.getToRecordPromotionDiscountInfo(), upperLimitPromotionInfo, batchInfoCalculateCell.getToRecordSourceTotalPrice());

                    batchInfoCalculateCell.setToRecordSourceTotalPrice(MathUtils.wrapBigDecimalSubtract(batchInfoCalculateCell.getToRecordSourceTotalPrice(), fractionPrice));

                    if (upperLimitPromotionInfo.isDiscountPriceEmpty()) {
                        break;
                    }
                }
            }

            //校验平摊的金额是否正确
            BigDecimal itemPromotionAllPrice = toRecordPromotionDiscountInfo.calculateAllPrice();
            BigDecimal batchPromotionAllPrice = calculateCell.getBatchInfoCalculateCells()
                    .stream()
                    .map(StatRecordBatchInfoCalculateCell::getToRecordPromotionDiscountInfo)
                    .filter(Objects::nonNull)
                    .map(ChargeDiscountInfo::calculateAllPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            if (MathUtils.wrapBigDecimalCompare(itemPromotionAllPrice, batchPromotionAllPrice) != 0) {
                log.error("批次平摊优惠信息失败, itemPromotionAllPrice: {}, batchPromotionAllPrice: {}, calculateCell: {}", itemPromotionAllPrice, batchPromotionAllPrice, JsonUtils.dump(calculateCell));
                throw new IllegalStateException("批次平摊优惠信息失败");
            }
        }

        @Override
        public void fillOtherFields(StatRecordByChooseCalculateCell calculateCell) {
            BigDecimal upperLimitTotalPrice = calculateCell.getToRecordTotalCostPrice();
            for (StatRecordBatchInfoCalculateCell batchInfoCalculateCell : calculateCell.getBatchInfoCalculateCells()) {
                BigDecimal discountedPrice = batchInfoCalculateCell.getDiscountedPrice();
                BigDecimal totalCostPrice = batchInfoCalculateCell.getTotalCostPrice();
                BigDecimal leftToRecordTotalCostPrice = batchInfoCalculateCell.getLeftToRecordTotalCostPrice();
                BigDecimal toRecordTotalCostPrice = BigDecimal.ZERO;
                if (discountedPrice.compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }

                toRecordTotalCostPrice = MathUtils.wrapBigDecimalOrZero(totalCostPrice).multiply(batchInfoCalculateCell.getToRecordDiscountedPrice()).divide(discountedPrice, 2, RoundingMode.UP);
                toRecordTotalCostPrice = MathUtils.min(toRecordTotalCostPrice, upperLimitTotalPrice);
                toRecordTotalCostPrice = MathUtils.min(toRecordTotalCostPrice, leftToRecordTotalCostPrice);

                batchInfoCalculateCell.setToRecordTotalCostPrice(toRecordTotalCostPrice);
                upperLimitTotalPrice = upperLimitTotalPrice.subtract(toRecordTotalCostPrice);
            }

            BigDecimal batchTotalCostPrice = calculateCell.getBatchInfoCalculateCells()
                    .stream()
                    .map(StatRecordBatchInfoCalculateCell::getToRecordTotalCostPrice)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            //这里直接用批次的成本价总和覆盖item的成本价
            calculateCell.setToRecordTotalCostPrice(batchTotalCostPrice);
        }
    }

    public static class BatchInfoRefundCalculator implements IStatRecordBatchInfoCalculator {

        @Override
        public void flatCellBatchInfoDiscountedPrice(StatRecordByChooseCalculateCell calculateCell) {
            for (StatRecordBatchInfoCalculateCell batchInfoCalculateCell : calculateCell.getBatchInfoCalculateCells()) {
                batchInfoCalculateCell.setToRecordDiscountedPrice(batchInfoCalculateCell.getLeftToRecordDiscountedPrice());
            }
        }

        /**
         * 如果是已支付，直接记录剩余的值就行
         * @param calculateCell
         */
        @Override
        public void flatCellBatchInfoDiscountInfo(StatRecordByChooseCalculateCell calculateCell) {

            ChargeDiscountInfo toRecordPromotionDiscountInfo = calculateCell.getToRecordDiscountInfoResult();
            if (Objects.isNull(toRecordPromotionDiscountInfo)) {
                return;
            }
            //顺序，整单议价 -> 折扣 -> 单项议价
            for (StatRecordBatchInfoCalculateCell batchInfoCalculateCell : calculateCell.getBatchInfoCalculateCells()) {
                batchInfoCalculateCell.setToRecordPromotionDiscountInfo(batchInfoCalculateCell.getLeftToRecordPromotionInfo());
                batchInfoCalculateCell.setToRecordSourceTotalPrice(batchInfoCalculateCell.getLeftToRecordSourceTotalPrice());
            }

            //校验平摊的金额是否正确
            BigDecimal itemPromotionAllPrice = toRecordPromotionDiscountInfo.calculateAllPrice();
            BigDecimal batchPromotionAllPrice = calculateCell.getBatchInfoCalculateCells()
                    .stream()
                    .map(StatRecordBatchInfoCalculateCell::getToRecordPromotionDiscountInfo)
                    .filter(Objects::nonNull)
                    .map(ChargeDiscountInfo::calculateAllPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            if (MathUtils.wrapBigDecimalCompare(itemPromotionAllPrice, batchPromotionAllPrice) != 0) {
                log.error("批次平摊优惠信息失败, itemPromotionAllPrice: {}, batchPromotionAllPrice: {}, calculateCell: {}", itemPromotionAllPrice, batchPromotionAllPrice, JsonUtils.dump(calculateCell));
                throw new IllegalStateException("批次平摊优惠信息失败");
            }
        }

        @Override
        public void fillOtherFields(StatRecordByChooseCalculateCell calculateCell) {
            for (StatRecordBatchInfoCalculateCell batchInfoCalculateCell : calculateCell.getBatchInfoCalculateCells()) {
                batchInfoCalculateCell.setToRecordTotalCostPrice(batchInfoCalculateCell.getLeftToRecordTotalCostPrice());
            }

            BigDecimal batchTotalCostPrice = calculateCell.getBatchInfoCalculateCells()
                    .stream()
                    .map(StatRecordBatchInfoCalculateCell::getToRecordTotalCostPrice)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            calculateCell.setToRecordTotalCostPrice(batchTotalCostPrice);
        }
    }


}
