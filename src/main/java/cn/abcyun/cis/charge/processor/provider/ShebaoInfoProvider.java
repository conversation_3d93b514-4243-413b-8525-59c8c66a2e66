package cn.abcyun.cis.charge.processor.provider;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsSheBaoMatchedCodesInfo;
import cn.abcyun.bis.rpc.sdk.cis.model.shebao.*;
import cn.abcyun.cis.commons.rpc.shebao.QueryChargeSheetShebaoInfoReq;
import cn.abcyun.cis.commons.rpc.shebao.QueryChargeSheetShebaoInfoRsp;

import java.util.List;
import java.util.Map;

public interface ShebaoInfoProvider {
    QueryChargeSheetShebaoInfoRsp queryChargeSheetShebaoInfo(QueryChargeSheetShebaoInfoReq req);

    List<GoodsSheBaoMatchedCodesInfo> getMatchedSheBaoCodes(String chainId,
                                                            String clinicId,
                                                            boolean notQueryGoodsFlag,
                                                            List<RpcGetShebaoMatchedCodesReq.GetSmartMatchedReqItem> items);

    Map<String, QueryRegistrationFeesMatchCodeResBody.RegistrationFeesMatchCode> queryRegistrationFeesMatchCode(String chainId, String clinicId, List<QueryRegistrationFeesMatchCodeReqBody.DoctorInfo> doctorInfoList);

    QueryProcessFeesMatchCodeResBody queryProcessFeesMatchCode(QueryProcessFeesMatchCodeReqBody req);

    boolean isEnableListingPrice(String clinicId);
}
