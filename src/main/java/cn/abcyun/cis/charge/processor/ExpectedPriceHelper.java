package cn.abcyun.cis.charge.processor;

import cn.abcyun.cis.charge.api.model.BasicCalculateItemReq;
import cn.abcyun.cis.charge.model.ChargeFormItem;
import cn.abcyun.cis.charge.processor.calculate.CalculateModel;
import cn.abcyun.cis.charge.util.FlatPriceTool;
import cn.abcyun.cis.charge.util.MathUtils;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class ExpectedPriceHelper {

    public static void process(BasicCalculateItemReq itemReq, BigDecimal expectedUnitPrice, BigDecimal expectedTotalPrice, BigDecimal expectedTotalPriceRatio, CalculateModel calculateModel) {
        CalculateExpectedPriceResult result = calculateExpectedPrice(itemReq.getUnitCount(), itemReq.getDoseCount(), itemReq.getUnitPrice(), itemReq.getSourceUnitPrice(), itemReq.calculateSourceTotalPrice(), itemReq.getFractionPrice(), expectedUnitPrice, expectedTotalPrice, expectedTotalPriceRatio, calculateModel);
        itemReq.setExpectedUnitPrice(result.getExpectedUnitPrice())
                .setExpectedTotalPrice(result.getExpectedTotalPrice())
                .setExpectedTotalPriceRatio(result.getExpectedTotalPriceRatio())
                .setUnitPrice(result.getUnitPrice())
                .setTotalPrice(result.getTotalPrice())
                .setTotalPriceRatio(result.getTotalPriceRatio())
                .setFractionPrice(result.getFractionPrice());
        itemReq.calculateTotalPrice();
    }


    @Data
    @Accessors(chain = true)
    public static class CalculateExpectedPriceResult {
        /**
         * 单价
         */
        private BigDecimal unitPrice;
        /**
         * 金额
         */
        private BigDecimal totalPrice;
        /**
         * 零头
         */
        private BigDecimal fractionPrice;
        /**
         * 金额比例
         */
        private BigDecimal totalPriceRatio;
        /**
         * 期待的单价
         */
        private BigDecimal expectedUnitPrice;
        /**
         * 期待的金额
         */
        private BigDecimal expectedTotalPrice;
        /**
         * 期待的金额比例
         */
        private BigDecimal expectedTotalPriceRatio;

        /**
         * 单项议价的金额与原金额的差值
         */
        private BigDecimal unitAdjustmentFee;

        public boolean isExpectedPrice() {
            return expectedUnitPrice != null || expectedTotalPrice != null || expectedTotalPriceRatio != null;
        }
    }

    /**
     * 计算议价
     *
     * @param unitCount
     * @param doseCount
     * @param unitPrice               当前单价
     * @param sourceUnitPrice
     * @param fractionPrice
     * @param expectedUnitPrice
     * @param expectedTotalPrice
     * @param expectedTotalPriceRatio
     * @param calculateModel          如果没有议价时，是使用unitPrice还是使用sourceUnitPrice，已作废，门诊算费和收费算费都统一成一套算法了
     * @return
     */
    public static CalculateExpectedPriceResult calculateExpectedPrice(BigDecimal unitCount,
                                                                      BigDecimal doseCount,
                                                                      BigDecimal unitPrice,
                                                                      BigDecimal sourceUnitPrice,
                                                                      BigDecimal sourceTotalPrice,
                                                                      BigDecimal fractionPrice,
                                                                      BigDecimal expectedUnitPrice,
                                                                      BigDecimal expectedTotalPrice,
                                                                      BigDecimal expectedTotalPriceRatio,
                                                                      CalculateModel calculateModel) {
        CalculateExpectedPriceResult result = new CalculateExpectedPriceResult();

        BigDecimal sourceFractionPrice = BigDecimal.ZERO;

        if (sourceTotalPrice != null) {
            BigDecimal sourceTotalPriceFlag = MathUtils.calculateTotalPrice(sourceUnitPrice, unitCount, doseCount, 2);
            sourceFractionPrice = MathUtils.max(sourceTotalPrice.subtract(sourceTotalPriceFlag), BigDecimal.ZERO);
        } else {
            sourceTotalPrice = MathUtils.calculateTotalPrice(sourceUnitPrice, unitCount, doseCount, 2);
        }

        //优先级：单项议单价 > 单项议总价 > 单项议总价比例
        if (expectedUnitPrice != null) {
            result.setUnitPrice(expectedUnitPrice)
                    .setFractionPrice(BigDecimal.ZERO)
                    .setTotalPrice(MathUtils.calculateTotalPrice(expectedUnitPrice, unitCount, doseCount, 2))
                    .setTotalPriceRatio(MathUtils.calculateTotalPriceRatio(sourceUnitPrice, unitCount, doseCount, result.getTotalPrice()))
                    .setExpectedUnitPrice(expectedUnitPrice)
                    .setExpectedTotalPrice(null)
                    .setExpectedTotalPriceRatio(null)
                    .setUnitAdjustmentFee(MathUtils.wrapBigDecimalSubtract(result.getTotalPrice(), sourceTotalPrice));
        } else if (expectedTotalPrice != null) {
            MathUtils.CalculateExpectedUnitPriceResult calculateExpectedUnitPriceResult = MathUtils.calculateExpectedUnitPrice(unitCount, doseCount, expectedTotalPrice);
            result.setUnitPrice(calculateExpectedUnitPriceResult.expectedUnitPrice)
                    .setFractionPrice(calculateExpectedUnitPriceResult.fractionPrice)
                    .setTotalPrice(expectedTotalPrice)
                    .setTotalPriceRatio(MathUtils.calculateTotalPriceRatio(sourceUnitPrice, unitCount, doseCount, result.getTotalPrice()))
                    .setExpectedUnitPrice(null)
                    .setExpectedTotalPrice(expectedTotalPrice)
                    .setExpectedTotalPriceRatio(null)
                    .setUnitAdjustmentFee(MathUtils.wrapBigDecimalSubtract(result.getTotalPrice(), sourceTotalPrice));
        } else if (expectedTotalPriceRatio != null) {
            BigDecimal totalPrice = MathUtils.calculateTotalPriceFromExpectedTotalPriceRatio(unitCount, doseCount, sourceUnitPrice, expectedTotalPriceRatio);
            MathUtils.CalculateExpectedUnitPriceResult calculateExpectedUnitPriceResult = MathUtils.calculateExpectedUnitPrice(unitCount, doseCount, totalPrice);

            result.setUnitPrice(calculateExpectedUnitPriceResult.expectedUnitPrice)
                    .setFractionPrice(calculateExpectedUnitPriceResult.fractionPrice)
                    .setTotalPrice(totalPrice)
                    .setTotalPriceRatio(expectedTotalPriceRatio)
                    .setExpectedUnitPrice(null)
                    .setExpectedTotalPrice(null)
                    .setExpectedTotalPriceRatio(expectedTotalPriceRatio)
                    .setUnitAdjustmentFee(MathUtils.wrapBigDecimalSubtract(result.getTotalPrice(), sourceTotalPrice));
        } else {
            result.setUnitPrice(sourceUnitPrice)
                    .setTotalPrice(sourceTotalPrice)
                    .setFractionPrice(sourceFractionPrice)
                    .setTotalPriceRatio(BigDecimal.ONE)
                    .setExpectedUnitPrice(null)
                    .setExpectedTotalPrice(null)
                    .setExpectedTotalPriceRatio(null)
                    .setUnitAdjustmentFee(null);
        }

        return result;
    }

    public static void processCompose(BasicCalculateItemReq itemReq, List<BasicCalculateItemReq> subItemReqs, BigDecimal expectedUnitPrice, BigDecimal expectedTotalPrice, BigDecimal expectedTotalPriceRatio, CalculateModel calculateModel) {
        process(itemReq, expectedUnitPrice, expectedTotalPrice, expectedTotalPriceRatio, calculateModel);

        //for compose sub item
        BigDecimal parentTotalPrice = itemReq.getTotalPrice();
        BigDecimal subItemSourceTotalPrice = subItemReqs.stream()
                .map(BasicCalculateItemReq::calculateSourceTotalPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (MathUtils.wrapBigDecimalCompare(parentTotalPrice, subItemSourceTotalPrice) != 0) {
            FlatPriceTool.flatPriceAndApply(parentTotalPrice, subItemReqs.stream()
                    .map(subItem -> new FlatPriceTool.IFlatCell<FlatPriceHelper.FlatPriceCell>() {
                        @Override
                        protected FlatPriceHelper.FlatPriceCell genFlatCell() {
                            FlatPriceHelper.FlatPriceCell flatPriceCell = new FlatPriceHelper.FlatPriceCell();
                            flatPriceCell.setId(subItem.getId());
                            flatPriceCell.setName(subItem.getName());
                            flatPriceCell.setPriority(FlatPriceHelper.PRIORITY_HIGH);
                            BigDecimal subSourceUnitPrice = subItem.getSourceUnitPrice();
                            flatPriceCell.setTotalPrice(MathUtils.calculateTotalPrice(subSourceUnitPrice, subItem.getUnitCount(), subItem.getDoseCount(), 2));
                            return flatPriceCell;
                        }

                        @Override
                        protected void apply(BigDecimal flatPrice) {
                            process(subItem, null, flatPrice, null, calculateModel);
                        }
                    }).collect(Collectors.toList())
            );
        } else {
            subItemReqs.forEach(subItem -> process(subItem, null, null, null, calculateModel));
        }
    }


    public static void process(ChargeFormItem chargeFormItem,
                               BigDecimal sourceUnitPrice,
                               BigDecimal sourceTotalPrice,
                               BigDecimal oldSourceUnitPrice,
                               BigDecimal expectedUnitPrice,
                               BigDecimal expectedTotalPrice,
                               BigDecimal expectedTotalPriceRatio) {

        //如果老的原价和现在的原价不一致，计算差值，同时应用的议价上
        if (oldSourceUnitPrice != null) {

            BigDecimal compareSourceUnitPrice = sourceUnitPrice.setScale(4, RoundingMode.HALF_UP);
            oldSourceUnitPrice = oldSourceUnitPrice.setScale(4, RoundingMode.HALF_UP);
            if (MathUtils.wrapBigDecimalCompare(compareSourceUnitPrice, oldSourceUnitPrice) != 0) {

                //重新计算议价值
                if (expectedUnitPrice != null) {
                    expectedUnitPrice = MathUtils.max(BigDecimal.ZERO, sourceUnitPrice.add(expectedUnitPrice).subtract(oldSourceUnitPrice).setScale(2, RoundingMode.HALF_UP));
                } else if (expectedTotalPrice != null) {
                    BigDecimal oldSourceTotalPrice = MathUtils.calculateTotalPrice(oldSourceUnitPrice, chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount(), 2);
                    expectedTotalPrice = MathUtils.max(BigDecimal.ZERO, expectedTotalPrice.subtract(oldSourceTotalPrice).add(sourceTotalPrice).setScale(2, RoundingMode.HALF_UP));
                }
            }

        }

        //处理修改处方剂量的问题
        if (chargeFormItem.getExpectedDoseCount() != null) {
            BigDecimal expectedDoseCount = chargeFormItem.getExpectedDoseCount();

            if (expectedDoseCount.compareTo(BigDecimal.ONE) < 0) {
                expectedDoseCount = BigDecimal.ONE;
            }

            BigDecimal doseCount = chargeFormItem.getDoseCount();
            if (doseCount == null || doseCount.compareTo(BigDecimal.ONE) < 0) {
                doseCount = BigDecimal.ONE;
            }

            BigDecimal doseCountRate = expectedDoseCount.divide(doseCount, 4, RoundingMode.FLOOR);

            expectedTotalPrice = Objects.nonNull(expectedTotalPrice) ? expectedTotalPrice.multiply(doseCountRate).setScale(2, RoundingMode.HALF_UP) : null;

            if (expectedTotalPrice != null) {
                chargeFormItem.setExpectedTotalPrice(expectedTotalPrice);
                if (chargeFormItem.getDoctorSourceTotalPrice() != null) {
                    chargeFormItem.setDoctorSourceTotalPrice(expectedTotalPrice);
                }
            }

            chargeFormItem.setDoseCount(expectedDoseCount);
            chargeFormItem.setExpectedDoseCount(null);
        }

        CalculateExpectedPriceResult result = calculateExpectedPrice(chargeFormItem.getUnitCount(),
                chargeFormItem.getDoseCount(),
                chargeFormItem.getUnitPrice(),
                sourceUnitPrice,
                sourceTotalPrice,
                chargeFormItem.getFractionPrice(),
                expectedUnitPrice,
                expectedTotalPrice,
                expectedTotalPriceRatio,
                CalculateModel.NORMAL_RESET);
        chargeFormItem.setUnitPrice(result.getUnitPrice())
                .setTotalPrice(result.getTotalPrice())
                .setFractionPrice(result.getFractionPrice())
                .setTotalPriceRatio(result.getTotalPriceRatio())
                .setExpectedUnitPrice(result.getExpectedUnitPrice())
                .setExpectedTotalPrice(result.getExpectedTotalPrice())
                .setExpectedTotalPriceRatio(result.getExpectedTotalPriceRatio())
                .setUnitAdjustmentFee(result.getUnitAdjustmentFee());
        chargeFormItem.calculateTotalPrice();

//        if (expectedUnitPrice != null) {
//            chargeFormItem.setUnitPrice(expectedUnitPrice);
//            if (!chargeFormItem.isChildItem()) {
//                chargeFormItem.setExpectedUnitPrice(expectedUnitPrice);
//            }
//            chargeFormItem.setExpectedTotalPrice(null);
//        } else if (expectedTotalPrice != null) {
//            MathUtils.CalculateExpectedUnitPriceResult result = MathUtils.calculateExpectedUnitPrice(chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount(), expectedTotalPrice);
//            chargeFormItem.setExpectedUnitPrice(null);
//            if (!chargeFormItem.isChildItem()) {
//                chargeFormItem.setExpectedTotalPrice(expectedTotalPrice);
//            }
//            chargeFormItem.setFractionPrice(MathUtils.wrapBigDecimalOrZero(result.fractionPrice));
//            chargeFormItem.setUnitPrice(result.expectedUnitPrice);
//        } else {
//            if (!chargeFormItem.isChildItem()) {
//                chargeFormItem.setFractionPrice(BigDecimal.ZERO);
//            }
//            chargeFormItem.setUnitPrice(sourceUnitPrice);
//        }

    }

    public static void process(ChargeFormItem chargeFormItem, BigDecimal sourceUnitPrice, BigDecimal expectedUnitPrice, BigDecimal expectedTotalPrice, BigDecimal expectedTotalPriceRatio) {
        process(chargeFormItem, sourceUnitPrice, chargeFormItem.getSourceTotalPrice(), null, expectedUnitPrice, expectedTotalPrice, expectedTotalPriceRatio);
    }

    /**
     * 递归处理议价
     *
     * @param itemProcessor
     * @param composeChildren
     * @param oldParentSourceUnitPrice
     */
    public static void processParentExpectedPrice(ItemProcessor itemProcessor, List<ItemProcessor> composeChildren, BigDecimal oldParentSourceUnitPrice) {
        ChargeFormItem chargeFormItem = itemProcessor.getChargeFormItem();

        if (!chargeFormItem.isParentItem()) {
            return;
        }

        if (chargeFormItem.isTopItem()) {
            process(chargeFormItem, chargeFormItem.getSourceUnitPrice(), chargeFormItem.getSourceTotalPrice(), oldParentSourceUnitPrice, chargeFormItem.getExpectedUnitPrice(), chargeFormItem.getExpectedTotalPrice(), chargeFormItem.getExpectedTotalPriceRatio());
        }

        BigDecimal expectedUnitPrice = chargeFormItem.getExpectedUnitPrice();
        BigDecimal expectedTotalPrice = chargeFormItem.getExpectedTotalPrice();
        BigDecimal expectedTotalPriceRatio = chargeFormItem.getExpectedTotalPriceRatio();

        //for compose sub item
        if (expectedUnitPrice != null || expectedTotalPriceRatio != null) {
            expectedTotalPrice = chargeFormItem.getTotalPrice();
        }
        if (expectedTotalPrice != null) {

            FlatPriceTool.flatPriceAndApply(expectedTotalPrice, composeChildren.stream()
                    .map(subItemProcessor -> new FlatPriceTool.IFlatCell<FlatPriceHelper.FlatPriceCell>() {
                        @Override
                        protected FlatPriceHelper.FlatPriceCell genFlatCell() {
                            ChargeFormItem subItem = subItemProcessor.getChargeFormItem();
                            FlatPriceHelper.FlatPriceCell flatPriceCell = new FlatPriceHelper.FlatPriceCell();
                            flatPriceCell.setId(subItem.getId());
                            flatPriceCell.setName(subItem.getName());
                            flatPriceCell.setPriority(FlatPriceHelper.PRIORITY_HIGH);
                            BigDecimal subSourceUnitPrice = subItem.getSourceUnitPrice() != null ? subItem.getSourceUnitPrice() : subItem.getUnitPrice();
                            flatPriceCell.setTotalPrice(MathUtils.calculateTotalPrice(subSourceUnitPrice, subItem.getUnitCount(), subItem.getDoseCount(), 2));
                            return flatPriceCell;
                        }

                        @Override
                        protected void apply(BigDecimal flatPrice) {
                            ChargeFormItem subItem = subItemProcessor.getChargeFormItem();
                            BigDecimal subSourceUnitPrice = subItem.getSourceUnitPrice() != null ? subItem.getSourceUnitPrice() : subItem.getUnitPrice();
                            process(subItem, subSourceUnitPrice, null, flatPrice, null);
                        }
                    }).collect(Collectors.toList())
            );
        } else {
            processComposeChildren(chargeFormItem.getTotalPrice(),
                    composeChildren.stream()
                            .map(ItemProcessor::getChargeFormItem)
                            .collect(Collectors.toList())
            );
        }
        composeChildren.forEach(subItemProcessor -> processParentExpectedPrice(subItemProcessor, subItemProcessor.getComposeChildren(), null));
    }

    public static void processCompose(ChargeFormItem parentChargeFormItem, List<ChargeFormItem> subChargeFormItems, BigDecimal sourceUnitPrice, BigDecimal sourceTotalPrice, BigDecimal oldParentSourceUnitPrice, BigDecimal expectedUnitPrice, BigDecimal expectedTotalPrice, BigDecimal expectedTotalPriceRatio) {
        process(parentChargeFormItem, sourceUnitPrice, sourceTotalPrice, oldParentSourceUnitPrice, expectedUnitPrice, expectedTotalPrice, expectedTotalPriceRatio);

        expectedUnitPrice = parentChargeFormItem.getExpectedUnitPrice();
        expectedTotalPrice = parentChargeFormItem.getExpectedTotalPrice();
        expectedTotalPriceRatio = parentChargeFormItem.getExpectedTotalPrice();

        //for compose sub item
        if (expectedUnitPrice != null) {
            expectedTotalPrice = MathUtils.calculateTotalPrice(expectedUnitPrice, parentChargeFormItem.getUnitCount(), parentChargeFormItem.getDoseCount());
        } else if (expectedTotalPriceRatio != null) {
            expectedTotalPrice = parentChargeFormItem.getTotalPrice();
        }

        if (expectedTotalPrice != null) {
            FlatPriceTool.flatPriceAndApply(expectedTotalPrice, subChargeFormItems.stream()
                    .filter(subChargeFormItem -> subChargeFormItem.getIsDeleted() == 0)
                    .map(subItem -> new FlatPriceTool.IFlatCell<FlatPriceHelper.FlatPriceCell>() {
                        @Override
                        protected FlatPriceHelper.FlatPriceCell genFlatCell() {
                            FlatPriceHelper.FlatPriceCell flatPriceCell = new FlatPriceHelper.FlatPriceCell();
                            flatPriceCell.setId(subItem.getId());
                            flatPriceCell.setName(subItem.getName());
                            flatPriceCell.setPriority(FlatPriceHelper.PRIORITY_HIGH);
                            BigDecimal subSourceUnitPrice = subItem.getSourceUnitPrice() != null ? subItem.getSourceUnitPrice() : subItem.getUnitPrice();
                            flatPriceCell.setTotalPrice(MathUtils.calculateTotalPrice(subSourceUnitPrice, subItem.getUnitCount(), subItem.getDoseCount(), 2));
                            return flatPriceCell;
                        }

                        @Override
                        protected void apply(BigDecimal flatPrice) {
                            BigDecimal subSourceUnitPrice = subItem.getSourceUnitPrice() != null ? subItem.getSourceUnitPrice() : subItem.getUnitPrice();
                            process(subItem, subSourceUnitPrice, null, flatPrice, null);
                        }
                    }).collect(Collectors.toList())
            );
        } else {
            processComposeChildren(parentChargeFormItem.getTotalPrice(), subChargeFormItems);
        }
    }

    /**
     * 计算套餐子项的算费单价，用于保证每个套餐子项的金额加起来等于套餐母项的金额
     *
     * @param totalPrice
     * @param subChargeFormItems 举个例子：母项3.5*1
     *                           子项1：4.193*0.4
     *                           子项2：1.82*1
     *                           母项数量开42个时，总金额应该等于3.5*42=147
     *                           子项1：4.193*0.4*42=70.4424 四舍五入之后为：70.44
     *                           子项2：1.82*42=76.44 四舍五入之后为76.44
     *                           和为146.88 != 147
     *                           这个方法解决这种由于子项价格在母项数量很大时，精度被扩大的情况
     *                           直接将147按比例分摊到每个子项上，再将每个子项的总金额除以数量，得到子项的单价
     */
    private static void processComposeChildren(BigDecimal totalPrice, List<ChargeFormItem> subChargeFormItems) {
        totalPrice = MathUtils.wrapBigDecimalOrZero(totalPrice);

        if (CollectionUtils.isEmpty(subChargeFormItems)) {
            return;
        }

        FlatPriceTool.flatPriceAndApply(totalPrice, subChargeFormItems.stream()
                .filter(subChargeFormItem -> subChargeFormItem.getIsDeleted() == 0)
                .map(subItem -> new FlatPriceTool.IFlatCell<FlatPriceHelper.FlatPriceCell>() {
                    @Override
                    protected FlatPriceHelper.FlatPriceCell genFlatCell() {
                        FlatPriceHelper.FlatPriceCell flatPriceCell = new FlatPriceHelper.FlatPriceCell();
                        flatPriceCell.setId(subItem.getId());
                        flatPriceCell.setName(subItem.getName());
                        flatPriceCell.setPriority(FlatPriceHelper.PRIORITY_HIGH);
                        flatPriceCell.setTotalPrice(subItem.getSourceTotalPrice());
                        return flatPriceCell;
                    }

                    @Override
                    protected void apply(BigDecimal flatPrice) {
                        MathUtils.CalculateExpectedUnitPriceResult result = MathUtils.calculateExpectedUnitPrice(subItem.getUnitCount(), subItem.getDoseCount(), flatPrice);
                        subItem.setExpectedTotalPriceRatio(null);
                        subItem.setExpectedUnitPrice(null);
                        subItem.setExpectedTotalPrice(null);
                        subItem.setFractionPrice(MathUtils.wrapBigDecimalOrZero(result.fractionPrice));
                        subItem.setUnitPrice(result.expectedUnitPrice);
                        subItem.setSourceTotalPrice(flatPrice);
                        subItem.calculateTotalPrice();
                    }
                }).collect(Collectors.toList())
        );
    }


    @Data
    @Accessors(chain = true)
    public static class CalculateExpectedPriceV2Result {

        private BigDecimal actualUnitPrice;

        private BigDecimal unitPriceRatio;

        private BigDecimal expectedUnitPrice;

        private BigDecimal expectedUnitPriceRatio;

        private BigDecimal expectedTotalPrice;

        private BigDecimal fractionPrice;

        /**
         * 单项议价的金额与原金额的差值
         */
        private BigDecimal unitAdjustmentFee;

    }

    /**
     * 计算议价2.0
     *
     * @param preExpectedActualTotalPrice 后端计算的单项折扣后的总金额
     * @param unitCount                   单剂的数量
     * @param doseCount                   剂量
     * @param sourceUnitPrice             原始单价
     * @param expectedUnitPrice           期望单价
     * @param expectedUnitPriceRatio      期望单价比例
     * @param expectedTotalPrice          期望总价
     * @return 计算结果
     */
    public static CalculateExpectedPriceV2Result calculateExpectedPriceV2(BigDecimal preExpectedActualTotalPrice,
                                                                          BigDecimal unitCount,
                                                                          BigDecimal doseCount,
                                                                          BigDecimal sourceUnitPrice,
                                                                          BigDecimal sourceTotalPrice,
                                                                          BigDecimal expectedUnitPrice,
                                                                          BigDecimal expectedUnitPriceRatio,
                                                                          BigDecimal expectedTotalPrice) {
        BigDecimal actualUnitPrice;
        BigDecimal actualTotalPrice;
        BigDecimal fractionPrice;
        BigDecimal unitPriceRatio = BigDecimal.ONE;
        BigDecimal preExpectedActualUnitPrice;
        if (MathUtils.wrapBigDecimalCompare(preExpectedActualTotalPrice, sourceTotalPrice) == 0) {
            actualTotalPrice = sourceTotalPrice;
            fractionPrice = BigDecimal.ZERO;
            preExpectedActualUnitPrice = sourceUnitPrice;
            actualUnitPrice = preExpectedActualUnitPrice;
        } else {
            actualTotalPrice = preExpectedActualTotalPrice;
            MathUtils.CalculateExpectedUnitPriceResult calculateExpectedUnitPriceResult = MathUtils.calculateExpectedUnitPrice(unitCount, doseCount, preExpectedActualTotalPrice);
            preExpectedActualUnitPrice = calculateExpectedUnitPriceResult.expectedUnitPrice;
            fractionPrice = calculateExpectedUnitPriceResult.fractionPrice;
            actualUnitPrice = preExpectedActualUnitPrice;
        }

        if (expectedUnitPrice != null) {
            expectedUnitPriceRatio = null;
            expectedTotalPrice = null;
            actualUnitPrice = expectedUnitPrice;
            fractionPrice = BigDecimal.ZERO;
            actualTotalPrice = MathUtils.calculateTotalPrice(actualUnitPrice, unitCount, doseCount, 2);
            unitPriceRatio = MathUtils.calculateUnitPriceRatio(preExpectedActualUnitPrice, actualUnitPrice);
        } else if (expectedUnitPriceRatio != null) {
            expectedTotalPrice = null;
            unitPriceRatio = expectedUnitPriceRatio;
            fractionPrice = BigDecimal.ZERO;
            actualUnitPrice = MathUtils.wrapBigDecimalMultiply(preExpectedActualUnitPrice, unitPriceRatio).setScale(2, RoundingMode.HALF_UP);
            actualTotalPrice = MathUtils.calculateTotalPrice(actualUnitPrice, unitCount, doseCount, 2);
        } else if (expectedTotalPrice != null) {
            actualTotalPrice = expectedTotalPrice;
            MathUtils.CalculateExpectedUnitPriceResult calculateExpectedUnitPriceResult = MathUtils.calculateExpectedUnitPrice(unitCount, doseCount, expectedTotalPrice);
            actualUnitPrice = calculateExpectedUnitPriceResult.expectedUnitPrice;
            fractionPrice = calculateExpectedUnitPriceResult.fractionPrice;
            unitPriceRatio = MathUtils.calculateUnitPriceRatio(preExpectedActualUnitPrice, actualUnitPrice);
        }

        return new CalculateExpectedPriceV2Result()
                .setActualUnitPrice(actualUnitPrice)
                .setUnitPriceRatio(unitPriceRatio)
                .setExpectedUnitPrice(expectedUnitPrice)
                .setExpectedUnitPriceRatio(expectedUnitPriceRatio)
                .setExpectedTotalPrice(expectedTotalPrice)
                .setFractionPrice(fractionPrice)
                .setUnitAdjustmentFee(MathUtils.wrapBigDecimalSubtract(actualTotalPrice, preExpectedActualTotalPrice));
    }
}
