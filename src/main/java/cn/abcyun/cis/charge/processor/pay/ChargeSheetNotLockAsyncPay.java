package cn.abcyun.cis.charge.processor.pay;

import cn.abcyun.cis.charge.amqp.RocketMqProducer;
import cn.abcyun.cis.charge.api.model.ChargeFormReq;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.base.pay.CombinedPayItem;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.repository.ChargePayTransactionRepository;
import cn.abcyun.cis.charge.service.ChargeService;
import cn.abcyun.cis.charge.service.ChargeTransactionRecordService;
import cn.abcyun.cis.charge.service.dto.ConvertChargeSheetInfo;
import cn.abcyun.cis.charge.service.dto.PayChargeSheetInfo;
import cn.abcyun.cis.charge.service.rpc.CisPatientOrderService;
import cn.abcyun.cis.charge.util.ChargeUtils;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 未锁单异步支付
 */
@Slf4j
public class ChargeSheetNotLockAsyncPay extends ChargeSheetLockedAsyncPay {

    protected List<ChargeFormReq> chargeFormReqs;

    protected boolean isNotConvertChargeSheet = false;

    public ChargeSheetNotLockAsyncPay(ChargeService chargeService,
                                      ChargeSheet chargeSheet,
                                      PayChargeSheetInfo payChargeSheetInfo,
                                      PatientOrder patientOrder,
                                      List<ChargeFormReq> chargeFormReqs,
                                      ChargeTransactionRecordService chargeTransactionRecordService,
                                      int paySource,
                                      CombinedPayItem payItem,
                                      RocketMqProducer rocketMqProducer,
                                      CisPatientOrderService cisPatientOrderService,
                                      ChargePayTransactionRepository chargePayTransactionRepository,
                                      String operatorId) {
        super(chargeService, chargeSheet, payChargeSheetInfo, patientOrder, chargeTransactionRecordService, paySource, payItem, rocketMqProducer, cisPatientOrderService, chargePayTransactionRepository, operatorId);
        this.chargeFormReqs = chargeFormReqs;
        this.rocketMqProducer = rocketMqProducer;
    }

    public boolean getIsNotConvertChargeSheet() {
        return isNotConvertChargeSheet;
    }

    public void setIsNotConvertChargeSheet(boolean notConvertChargeSheet) {
        isNotConvertChargeSheet = notConvertChargeSheet;
    }

    @Override
    protected void buildChargeSheet() {
        if (!isNotConvertChargeSheet) {
            //先转化chargeSheet
            ConvertChargeSheetInfo convertChargeSheetInfo = new ConvertChargeSheetInfo();
            convertChargeSheetInfo.setForPaid(true);
            convertChargeSheetInfo.setOperatorId(operatorId);
            chargeSheet = chargeService.convertChargeSheet(chargeSheet,
                    payChargeSheetInfo,
                    chargeFormReqs,
                    convertChargeSheetInfo);
        }
        if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED) {
            chargeSheet.setDeliveryType(ChargeUtils.generateDeliveryType(chargeSheet));
            if (chargeSheet.getDeliveryType() == ChargeSheet.DeliveryType.DELIVERY_TO_HOME) {
                chargeService.createOrUpdateDeliveryInfo(chargeSheet.getDeliveryInfo(), chargeSheet, operatorId, payChargeSheetInfo.getDeliveryInfo());
            }
        }
        super.buildChargeSheet();
    }

}
