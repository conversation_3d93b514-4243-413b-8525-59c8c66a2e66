package cn.abcyun.cis.charge.processor.discount;

import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.commons.util.MathUtils;
import cn.abcyun.cis.commons.util.TextUtils;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public abstract class BasicCalculatePromotionItem {

    private String id;
    private String productId;
    private int productType;
    private int productSubType;
    private int useDismounting;
    private String name;
    //    private BigDecimal unitCount;
    private BigDecimal doseCount;
    private BigDecimal totalCount;
    private BigDecimal unitPrice;
    private BigDecimal totalPrice;
    private BigDecimal discountPrice;
    private int pharmacyType;
    private List<PromotionSimple> promotions = new ArrayList<>();

    private List<CalculatePromotionItemBatchInfo> batchInfos = new ArrayList<>();

    private boolean canApplyGiftPromotion = true;

    public abstract BigDecimal getCanDiscountTotalPrice();

    public abstract BigDecimal getExistedDiscountPrice();

    public abstract boolean canApplyOriginalPricePromotion();

    public BigDecimal addDiscountPrice(BigDecimal discountPrice) {
        this.discountPrice = MathUtils.wrapBigDecimalAdd(this.discountPrice, discountPrice);
        return this.discountPrice;
    }

    public void addPromotion(PromotionSimple promotionSimple) {
        if (promotions == null) {
            promotions = new ArrayList<>();
        }
        promotions.add(promotionSimple);
    }

    public void addPromotions(List<PromotionSimple> promotionSimples) {
        if (promotions == null) {
            promotions = new ArrayList<>();
        }
        promotions.addAll(promotionSimples);
    }

    public String getDisplayName() {
        return getProductType() == Constants.ProductType.REGISTRATION && !TextUtils.alwaysString(getName()).contains(Constants.SystemProductId.REGISTRATION_NAME) ? String.format("%s-%s", Constants.SystemProductId.REGISTRATION_NAME, TextUtils.alwaysString(getName())) : getName();
    }

    public String getPharmacyTypeGoodsIdKey() {
        return convertPharmacyTypeGoodsIdKey(pharmacyType, getProductId());
    }


    public static String convertPharmacyTypeGoodsIdKey(int pharmacyType, String goodsId) {
        return String.format("%d-%s", pharmacyType, goodsId);
    }
}
