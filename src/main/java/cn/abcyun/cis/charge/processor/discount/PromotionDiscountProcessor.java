package cn.abcyun.cis.charge.processor.discount;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsItem;
import cn.abcyun.bis.rpc.sdk.cis.model.promotion.Promotion;
import cn.abcyun.cis.charge.api.model.ItemSinglePromotionReq;
import cn.abcyun.cis.charge.api.model.PromotionReq;
import cn.abcyun.cis.charge.api.model.SinglePromotionReq;
import cn.abcyun.cis.charge.processor.PromotionQueryGiftGoodsDto;
import cn.abcyun.cis.charge.service.dto.PromotionView;
import cn.abcyun.cis.charge.service.dto.SinglePromotionView;
import cn.abcyun.cis.charge.util.MergeTool;
import cn.abcyun.cis.commons.util.TextUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class PromotionDiscountProcessor {
    private List<DiscountRuleProcessor> discountRuleProcessors = new ArrayList<>();

    //构造的时候传入的折扣规则
    public PromotionDiscountProcessor(List<Promotion> promotions) {
        if (promotions != null) {
            discountRuleProcessors = promotions.stream().map(DiscountRuleProcessorFactory::createDiscountRule).filter(Objects::nonNull).collect(Collectors.toList());
        }
    }

    /**
     * 计算最优的折扣信息
     */
    public List<PromotionView> applyBestDiscount(List<CalculatePromotionItem> normalPharmacyForms,//非空中药房
                                                 List<CalculatePromotionAirPharmacyForm> airPharmacyForms,//空中药房
                                                 List<PromotionView> existedPromotionViews,
                                                 List<PromotionReq> clientPromotionReqs) {
        if (discountRuleProcessors == null || discountRuleProcessors.size() == 0) {
            return new ArrayList<>();
        }

        if (clientPromotionReqs == null) {
            clientPromotionReqs = new ArrayList<>();
        }

        Map<Boolean, Set<String>> promotionIdMap = clientPromotionReqs.stream()
                .filter(Objects::nonNull)
                .filter(promotionReq -> StringUtils.isNotEmpty(promotionReq.getId()))
                .collect(Collectors.partitioningBy(PromotionReq::getChecked, Collectors.mapping(PromotionReq::getId, Collectors.toSet())));

        Set<String> unselectedPromotionIds = promotionIdMap.getOrDefault(Boolean.FALSE, new HashSet<>());
        Set<String> selectedPromotionIds = promotionIdMap.getOrDefault(Boolean.TRUE, new HashSet<>());


        if (existedPromotionViews != null) {
            Set<String> existedUnselectedPromotionIds = existedPromotionViews.stream()
                    .filter(promotionView -> !promotionView.getChecked()  //老的没有checked的优惠卷
                            && !TextUtils.isEmpty(promotionView.getId())
                            && !selectedPromotionIds.contains(promotionView.getId())) //还要没在用上指定选定的id里面
                    .map(PromotionView::getId)
                    .collect(Collectors.toSet());
            unselectedPromotionIds.addAll(existedUnselectedPromotionIds);
        }

        List<DiscountRuleProcessor> availableRules = discountRuleProcessors.stream()
                .filter(discountRuleProcessor ->
                        discountRuleProcessor.isAvailableRule(normalPharmacyForms, airPharmacyForms))
                .collect(Collectors.toList());

        availableRules.stream()
                .filter(discountRuleProcessor -> !unselectedPromotionIds.contains(discountRuleProcessor.getPromotionId())) //只计算选中的优惠卷
                .forEach(discountRuleProcessor -> discountRuleProcessor.applyBestDiscount(normalPharmacyForms, airPharmacyForms)); //计算这个卷的优惠信息

        List<PromotionView> promotionViews = discountRuleProcessors.stream()
                .map(discountRuleProcessor -> discountRuleProcessor.generatePromotionView(normalPharmacyForms, airPharmacyForms)) //输出终端协议
                .filter(Objects::nonNull)
                .map(promotionView -> {
                    if (unselectedPromotionIds.contains(promotionView.getId())) {
                        promotionView.setChecked(false);//false表示没有算过费的优惠卷
                    }
                    return promotionView;
                })
                .collect(Collectors.toList());

        return promotionViews;
    }

    /**
     * 计算最优的单品折扣折扣信息
     */
    public void applyBestSingleDiscount(List<ItemSinglePromotionReq> itemSinglePromotionReqs,
                                        List<CalculateSinglePromotionItem> singlePromotionItems,
                                        Map<String, GoodsItem> singlePromotionGiftGoodsItemMap) {
        if (CollectionUtils.isEmpty(discountRuleProcessors)) {
            singlePromotionItems.forEach(singlePromotionItem -> singlePromotionItem.setSinglePromotionViews(new ArrayList<>()));
            return;
        }

        Map<String, List<SinglePromotionReq>> itemIdSinglePromotionReqMap = Optional.ofNullable(itemSinglePromotionReqs).orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.toMap(ItemSinglePromotionReq::getId, ItemSinglePromotionReq::getSinglePromotions, (a, b) -> a));

        singlePromotionItems.forEach(singlePromotionItem -> applyBestSingleDiscount(itemIdSinglePromotionReqMap.getOrDefault(singlePromotionItem.getId(), new ArrayList<>()),
                singlePromotionItem,
                singlePromotionGiftGoodsItemMap));
    }

    public void applyBestSingleDiscount(List<SinglePromotionReq> clientReqs,
                                        CalculateSinglePromotionItem singlePromotionItem,
                                        Map<String, GoodsItem> singlePromotionGiftGoodsItemMap) {
        List<SinglePromotionReq> existedReqs = singlePromotionItem.getSinglePromotionViews()
                .stream()
                .map(singlePromotionView -> new SinglePromotionReq()
                        .setId(singlePromotionView.getId())
                        .setChecked(singlePromotionView.getChecked())
                        .setExpectedChecked(singlePromotionView.getExpectedChecked()))
                .collect(Collectors.toList());

        //将前端的req与已存在的req进行合并
        MergeTool.doMerge(clientReqs,
                existedReqs,
                (clientReq, existedReq) -> Objects.equals(clientReq.getId(), existedReq.getId()),
                Function.identity(),
                existedReq -> false,
                (clientReq, existedReq) -> existedReq.setChecked(clientReq.getChecked())
                        .setExpectedChecked(clientReq.getExpectedChecked())
        );

        existedReqs.removeIf(existedReq -> StringUtils.isEmpty(existedReq.getId()));

        //找到第一个手动选中的id
        String expectedCheckedId = existedReqs.stream()
                .filter(promotionReq -> promotionReq.getExpectedChecked() != null
                        && promotionReq.getExpectedChecked())
                .findFirst()
                .map(SinglePromotionReq::getId)
                .orElse(null);

        //找出所有用户手动操作的id，不管是选中还是非选中
        Set<String> expectedIds = existedReqs.stream()
                .filter(promotionReq -> StringUtils.isNotEmpty(promotionReq.getId()) && Objects.nonNull(promotionReq.getExpectedChecked()))
                .map(SinglePromotionReq::getId)
                .collect(Collectors.toSet());

        //过滤出有效的营销规则
        List<DiscountRuleProcessor> availableRules = discountRuleProcessors.stream()
                .filter(discountRuleProcessor -> discountRuleProcessor.isAvailableRuleForSingleItem(singlePromotionItem))
                .collect(Collectors.toList());

        List<SinglePromotionView> allSinglePromotionViews = availableRules.stream()
                .map(discountRuleProcessor -> discountRuleProcessor.generateSinglePromotionView(singlePromotionItem, singlePromotionGiftGoodsItemMap))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        SinglePromotionView checkedSinglePromotion = null;
        if (StringUtils.isNotEmpty(expectedCheckedId)) {
            //找出用户手动选中的id
            checkedSinglePromotion = allSinglePromotionViews.stream()
                    .filter(singlePromotionView -> Objects.equals(singlePromotionView.getId(), expectedCheckedId))
                    .findFirst()
                    .orElse(null);
        }

        allSinglePromotionViews
                .stream()
                .filter(singlePromotionView -> expectedIds.contains(singlePromotionView.getId()))
                .forEach(singlePromotionView -> singlePromotionView.setExpectedChecked(false));

        if (Objects.nonNull(checkedSinglePromotion)) {
            applyCheckedSinglePromotion(singlePromotionItem, allSinglePromotionViews, checkedSinglePromotion, Boolean.TRUE, availableRules);
            return;
        }

        //如果前端指定了优惠，但是又找不到，说明这个规则是错的，需要重新匹配活动
        if (StringUtils.isNotEmpty(expectedCheckedId)) {
            allSinglePromotionViews.forEach(singlePromotionView -> singlePromotionView.setExpectedChecked(null));
        }

        //找出优惠最大的规则
        SinglePromotionView bestSinglePromotion = allSinglePromotionViews.stream()
                .filter(singlePromotionView -> !Objects.equals(singlePromotionView.getExpectedChecked(), Boolean.FALSE))
                .min(Comparator.comparing(SinglePromotionView::getDisplayDiscountPrice))
                .orElse(null);

        applyCheckedSinglePromotion(singlePromotionItem, allSinglePromotionViews, bestSinglePromotion, null, availableRules);
    }

    private void applyCheckedSinglePromotion(CalculateSinglePromotionItem singlePromotionItem,
                                             List<SinglePromotionView> allSinglePromotionViews,
                                             SinglePromotionView checkedSinglePromotionView,
                                             Boolean expectedChecked,
                                             List<DiscountRuleProcessor> availableRules) {
        singlePromotionItem.setSinglePromotionViews(allSinglePromotionViews);

        if (Objects.isNull(checkedSinglePromotionView)) {
            return;
        }

        checkedSinglePromotionView.setChecked(true);
        checkedSinglePromotionView.setExpectedChecked(expectedChecked);
        singlePromotionItem.bindCheckedPromotion(checkedSinglePromotionView);
        //更新规则的限购数量，下次循环匹配时，直接使用限购数量进行比较
        availableRules
                .stream()
                .filter(discountRuleProcessor -> Objects.equals(discountRuleProcessor.getPromotionId(), checkedSinglePromotionView.getId()))
                .findFirst()
                .ifPresent(bestDiscountRuleProcessor -> bestDiscountRuleProcessor.updateLeftSaleCount(singlePromotionItem, checkedSinglePromotionView));
    }

    /**
     * 找出赠品的goodsItem信息
     *
     * @param singlePromotionItems
     * @return
     */
    public List<PromotionQueryGiftGoodsDto> findAvailablePromotionQueryGiftGoodsDtos(List<CalculateSinglePromotionItem> singlePromotionItems) {

        if (CollectionUtils.isEmpty(singlePromotionItems)) {
            return new ArrayList<>();
        }

        if (CollectionUtils.isEmpty(discountRuleProcessors)) {
            return new ArrayList<>();
        }


        return singlePromotionItems
                .stream()
                .map(this::findPromotionQueryGiftGoodsDtosForSingle)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    private List<PromotionQueryGiftGoodsDto> findPromotionQueryGiftGoodsDtosForSingle(CalculateSinglePromotionItem singlePromotionItem) {
        List<DiscountRuleProcessor> availableRules = discountRuleProcessors.stream()
                .filter(discountRuleProcessor -> discountRuleProcessor.isAvailableRuleForSingleItem(singlePromotionItem))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(availableRules)) {
            return new ArrayList<>();
        }

        return availableRules.stream()
                .map(discountRuleProcessor -> discountRuleProcessor.generatePromotionQueryGiftGoodsDto(singlePromotionItem))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 找出预告赠品的goodsItem信息
     *
     * @param singlePromotionItems
     * @return
     */
    public List<PromotionQueryGiftGoodsDto> findOnComingPromotionQueryGiftGoodsDtos(List<CalculateSinglePromotionItem> singlePromotionItems) {

        if (CollectionUtils.isEmpty(singlePromotionItems)) {
            return new ArrayList<>();
        }

        if (CollectionUtils.isEmpty(discountRuleProcessors)) {
            return new ArrayList<>();
        }


        return singlePromotionItems
                .stream()
                .map(this::findOnComingPromotionQueryGiftGoodsDtosForSingle)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    private List<PromotionQueryGiftGoodsDto> findOnComingPromotionQueryGiftGoodsDtosForSingle(CalculateSinglePromotionItem singlePromotionItem) {
        List<DiscountRuleProcessor> availableRules = discountRuleProcessors.stream()
                .filter(discountRuleProcessor -> discountRuleProcessor.isAvailableRuleForSingleItem(singlePromotionItem))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(availableRules)) {
            return new ArrayList<>();
        }

        return availableRules.stream()
                .map(discountRuleProcessor -> discountRuleProcessor.generateOnComingPromotionQueryGiftGoodsDto(singlePromotionItem))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
