package cn.abcyun.cis.charge.processor.push;

import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ;
import cn.abcyun.cis.charge.amqp.MQProducer;
import cn.abcyun.cis.charge.amqp.RocketMqProducer;
import cn.abcyun.cis.charge.api.model.ChargeSheetPushOrderInfo;
import cn.abcyun.cis.charge.api.model.CreateOrFindChargeSheetInfo;
import cn.abcyun.cis.charge.api.model.SaveChargeSheetDraftReq;
import cn.abcyun.cis.charge.base.ChargeServiceError;
import cn.abcyun.cis.charge.base.ChargeServiceException;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.base.GoodsLockScene;
import cn.abcyun.cis.charge.model.ChargeForm;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.model.ChargeSheetAdditional;
import cn.abcyun.cis.charge.model.SheetPromotionInfo;
import cn.abcyun.cis.charge.processor.SheetProcessor;
import cn.abcyun.cis.charge.service.*;
import cn.abcyun.cis.charge.service.dto.PromotionView;
import cn.abcyun.cis.charge.service.rpc.CisCrmService;
import cn.abcyun.cis.charge.service.rpc.CisPatientOrderService;
import cn.abcyun.cis.charge.service.rpc.CisScClinicService;
import cn.abcyun.cis.charge.util.ChargeFormUtils;
import cn.abcyun.cis.charge.util.JsonUtils;
import cn.abcyun.cis.charge.util.PatientUtils;
import cn.abcyun.cis.commons.amqp.message.ChargeSheetMessage;
import cn.abcyun.cis.commons.exception.ServiceInternalException;
import cn.abcyun.cis.commons.message.ToCMessage;
import cn.abcyun.cis.commons.rpc.crm.PatientInfo;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static cn.abcyun.cis.charge.base.ChargeServiceError.*;

/**
 * 门诊推送给病人
 */
public class ChargeSheetPushOpsCharge extends ChargeSheetPushOpsAbstract {
    private static final Logger sLogger = LoggerFactory.getLogger(ChargeSheetPushOpsCharge.class);

    private SaveChargeSheetDraftReq req;
    private boolean isNewInsert;
    private SheetProcessor sheetProcessor;
    private ChargeSheetPushOrderInfo chargeSheetPushOrderInfo;
    private ChargeService chargeService;
    private PatientOrderService patientOrderService;

    private CisPatientOrderService cisPatientOrderService;

    private MQProducer mqProducer;
    private RocketMqProducer rocketMqProducer;
    private OutpatientService mOutpatientService;
    private ChargeSheetService mChargeSheetService;
    private CrmService oldCrmService;
    private DispensingService dispensingService;
    private PatientInfo patientInfo;
    private CisCrmService crmService;
    private CisScClinicService cisScClinicService;

    public ChargeSheetPushOpsCharge(SaveChargeSheetDraftReq req, String chainId, String clinicId, String operatorId,
                                    ChargeService chargeService, PatientOrderService patientOrderService,
                                    MQProducer mQProducer,
                                    RocketMqProducer rocketMqProducer,
                                    OutpatientService outpatientService, ChargeSheetService chargeSheetService,
                                    CrmService oldCrmService, DispensingService dispensingService, CisCrmService crmService,
                                    CisScClinicService cisScClinicService,CisPatientOrderService cisPatientOrderService) {
        super(chainId, clinicId, operatorId);
        this.req = req;
        this.chargeService = chargeService;
        this.patientOrderService = patientOrderService;
        this.mqProducer = mQProducer;
        this.rocketMqProducer = rocketMqProducer;
        this.mOutpatientService = outpatientService;
        this.mChargeSheetService = chargeSheetService;
        this.oldCrmService = oldCrmService;
        this.dispensingService = dispensingService;
        this.crmService = crmService;
        this.cisScClinicService = cisScClinicService;
        this.cisPatientOrderService = cisPatientOrderService;
    }

    @Override
    protected void checkCanPushToPatientBeforeCalFeeAndThrowException() {

        if (chargeSheet == null || chargeSheet.getChargeForms() == null) {
            throw new ChargeServiceException(ChargeServiceError.CHARGE_FORM_IS_EMPTY);
        }

        for (ChargeForm chargeForm : chargeSheet.getChargeForms()) {

            if (chargeForm.getIsDeleted() != 0) {
                continue;
            }

            if (chargeForm.getSourceFormType() != Constants.SourceFormType.AIR_PHARMACY) {
                continue;
            }

            //校验空中药房的form是否有药品
            if (!ChargeFormUtils.isContainMedicineForAirPharmacyChargeForm(chargeForm)) {
                throw new ChargeServiceException(ChargeServiceError.AIR_PHARMACY_NO_MEDICINE);
            }
        }

    }

    /**
     * 构造chargeSheet
     */
    @Override
    protected void buildCompleteChargeSheet(){
        CreateOrFindChargeSheetInfo createOrFindChargeSheetInfo = new CreateOrFindChargeSheetInfo();
        createOrFindChargeSheetInfo.setReq(req);
        createOrFindChargeSheetInfo.setClinicId(clinicId);
        createOrFindChargeSheetInfo.setChainId(chainId);
        createOrFindChargeSheetInfo.setOperatorId(operatorId);
        createOrFindChargeSheetInfo.setType(CreateOrFindChargeSheetInfo.Type.PUSH_ORDER);
        /**
         * 这两句代码合并的时候要注意
         * */
        chargeSheet = chargeService.createOrFindChargeSheet(createOrFindChargeSheetInfo).getChargeSheet();
        isNewInsert = createOrFindChargeSheetInfo.isNewInsert();

        if (chargeSheet.getLockStatus() > 0) {
            throw new ChargeServiceException(CHARGE_SHEET_IS_LOCK_BY_DEVICE);
        }

        //推送不能自动发药
        chargeSheet.setIsDispensing(0);

        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED) {
            throw new ChargeServiceException(PUSHORDER_SHEET_STATUS_ERROR);
        }

        if (chargeSheet.getCheckStatus() == ChargeSheet.CheckStatus.NEED_PATIENT_CHECK) {
            throw new ChargeServiceException(PUSHORDER_SHEET_CHECK_STATUS_ERROR);
        }

        if (chargeSheet.getDeliveryType() == ChargeSheet.DeliveryType.DELIVERY_TO_HOME) {
            chargeService.createOrUpdateDeliveryInfo(chargeSheet.getDeliveryInfo(), chargeSheet, operatorId, req.getDeliveryInfo());
        }

        if (chargeSheet.getIsDraft() == 1
                && ChargeSheet.Type.canUpdatePatientOrderTypes().contains(chargeSheet.getType())
                && req.getPatient() != null && StringUtils.isNotBlank(req.getPatient().getId()) && !Objects.equals(Constants.ANONYMOUS_PATIENT_ID, req.getPatient().getId())
        ) {
            PatientOrder patientOrder = patientOrderService.updatePatientOrder(chargeSheet.getPatientOrderId(),
                    req.getPatient(),
                    req.getShebaoCardInfo(),
                    chargeSheet.getClinicId(),
                    chargeSheet.getChainId(),
                    operatorId, false);
            sLogger.info("patientOrder: {}", JsonUtils.dump(patientOrder));
            if (Constants.ANONYMOUS_PATIENT_ID.equals(chargeSheet.getPatientId())) {
                chargeSheet.setPatientId(patientOrder.getPatientId());
            }
        }

        if (StringUtils.isNotEmpty(req.getVisitSourceId())) {
            cisPatientOrderService.updateVisitSource(chargeSheet.getChainId(), chargeSheet.getClinicId(), chargeSheet.getPatientOrderId(), req.getVisitSourceFrom(), req.getVisitSourceId(), req.getVisitSourceRemark(), operatorId);
        }

        if (Constants.ANONYMOUS_PATIENT_ID.equals(chargeSheet.getPatientId())) {
            throw new ChargeServiceException(PUSHORDER_SHEET_NOT_ALLOWED_ANONYMOUS_PATIENT);
        }

        this.patientInfo = PatientUtils.getPatientInfoById(chainId, clinicId, Optional.ofNullable(chargeSheet).map(ChargeSheet::getPatientId).orElse(""), oldCrmService);
    }

    @Override
    protected boolean checkCanPushToPatientAfterCalFee() {
        //病人是否能收到微信消息
        if (!crmService.canWeChatMessagePushToPatient(patientInfo.getChainId(),patientInfo.getId())) {
            sLogger.info("canWeChatMessagePushToPatient false,chargeSheet.getPatientId()={}", chargeSheet.getPatientId());
            throw new ChargeServiceException(ChargeServiceError.WECLINIC_WECHAT_MSG_CANNOT_PUSHTO);
        }
        return true;
    }

    /**
     * 推送的算费
     */
    @Override
    protected void calculateFee() throws ChargeServiceException, ServiceInternalException {
        sheetProcessor = new SheetProcessor(chargeSheet);
        sheetProcessor.setSheetProcessorInfoProvider(chargeService);
        sheetProcessor.setPayMode(Constants.ChargePayMode.WECHAT_PAY);
        sheetProcessor.setOperatorId(operatorId);
        sheetProcessor.build();

        chargeSheetPushOrderInfo = sheetProcessor.generateChargeSheetPushOrderInfo(Constants.ChargeSource.CHARGE, req.getExpectedAdjustmentFee(), req.getPromotions(), req.getCouponPromotions(), req.getGiftRulePromotions(), req.getPatientCardPromotions(), req.getPatientPointDeductProductPromotions(), req.getPatientPointsInfo(), req.getMallVerifications(), true);
        sheetProcessor.generateToSaveChargeSheet();
    }


    /**
     * 推送给病人
     */
    @Override
    protected void pushMessageToPatient() throws ChargeServiceException, ServiceInternalException {
        String clinicName = Optional.ofNullable(cisScClinicService.getOrgan(chargeSheet.getClinicId())).map(Organ::getName).orElse("");
        chargeSheetPushOrderInfo.setMedicalItems(Optional.ofNullable(chargeSheet.getAdditional()).map(ChargeSheetAdditional::getAbstractInfo).orElse(""));

        ToCMessage toCMessage = TocMessageService.getChargeSheetWaitPayMessage(chargeSheet, operatorId, chargeSheetPushOrderInfo, clinicName);

        toCMessage.setSendTime(new Date());
        sLogger.info("toCMessage: {}", JsonUtils.dump(toCMessage));
        int isOnline = chargeSheet.getIsOnline();
        int type = chargeSheet.getType();
        String patientOrderId = chargeSheet.getPatientOrderId();
        String chargeSheetId = chargeSheet.getId();
        int chargeSheetStatus = chargeSheet.getStatus();
        int checkStatus = chargeSheet.getCheckStatus();

        MQProducer.doAfterTransactionCommit(() -> {
            if (isOnline == 1 && ChargeSheet.Type.OUTPATIENT == type) {
                mOutpatientService.sendConsultationChargeSheet(patientOrderId, chargeSheetId, chargeSheetStatus, new BigDecimal(chargeSheetPushOrderInfo.getNeedPay()), checkStatus);
            }
            rocketMqProducer.sendToCMessage(toCMessage);
        });
    }


    /**
     * 保存db
     */
    @Override
    protected void fillRepositoryData() throws ServiceInternalException {
        List<PromotionView> availablePromotionViews = sheetProcessor.getAvailablePromotionViews();

        if (!CollectionUtils.isEmpty(availablePromotionViews)) {
            SheetPromotionInfo sheetPromotionInfo = new SheetPromotionInfo();
            sheetPromotionInfo.setPromotions(availablePromotionViews);
            chargeSheet.setPromotionInfo(sheetPromotionInfo);
            chargeSheet.setPromotionInfoJson(JsonUtils.dump(sheetPromotionInfo));
        }


        if (new BigDecimal(chargeSheetPushOrderInfo.getNeedPay()).compareTo(BigDecimal.ZERO) <= 0) {
            throw new ChargeServiceException(CHARGE_NO_VALID_RECEIVABLE);
        }

        chargeService.updateChargeSheetForPushOrder(chargeSheet, new BigDecimal(chargeSheetPushOrderInfo.getNeedPay()));

        chargeSheet.setSendToPatientStatus(1);
        chargeSheet.setCheckStatus(ChargeSheet.CheckStatus.NOT_NEED_CHECK);//回退以前老逻辑

        if (isNewInsert) {
            chargeService.doAfterChargeSheetChange(chargeSheet, Lists.newArrayList(), Lists.newArrayList(), Maps.newHashMap(), ChargeSheetMessage.MSG_TYPE_CREATED, null, null, operatorId);
        } else {
            chargeService.doAfterChargeSheetChange(chargeSheet, Lists.newArrayList(), Lists.newArrayList(), Maps.newHashMap(), ChargeSheetMessage.MSG_TYPE_UPDATE, null, null, operatorId);
        }

    }

    //数据落地
    @Override
    protected void saveNewToDb() throws ServiceInternalException {
        //落地锁库数据
        chargeService.goodsLockAndSaveBatchInfo(chargeSheet, GoodsLockScene.ONLY_LOCK_BILLING, false, operatorId);

        //门诊推送，这里是新对象的落地
        mChargeSheetService.saveSheetAndExecuteItems(chargeSheet, sheetProcessor.getDeletedDataCollector(), true, operatorId);
    }

    public ChargeSheet getChargeSheet() {
        return chargeSheet;
    }
}
