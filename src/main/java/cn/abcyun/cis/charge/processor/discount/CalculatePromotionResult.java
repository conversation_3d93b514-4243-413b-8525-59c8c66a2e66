package cn.abcyun.cis.charge.processor.discount;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

@Data
@Accessors(chain = true)
public class CalculatePromotionResult {

   private Map<String, CalculatePromotionItem> calculatePromotionItemMap;

   private Map<String, CalculatePromotionAirPharmacyForm> calculatePromotionAirPharmacyFormMap;

   private int afterGoodsDeductPoints;
}
