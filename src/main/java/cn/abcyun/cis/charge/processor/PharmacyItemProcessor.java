package cn.abcyun.cis.charge.processor;

import cn.abcyun.bis.rpc.sdk.bis.model.order.OrderProcessRuleView;
import cn.abcyun.bis.rpc.sdk.bis.model.order.OrderRuleIngredientView;
import cn.abcyun.bis.rpc.sdk.cis.model.charge.ChargeConstants;
import cn.abcyun.bis.rpc.sdk.cis.model.charge.GoodsFeeType;
import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.*;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.util.TraceCodeUtils;
import cn.abcyun.bis.rpc.sdk.cis.model.goodslocking.GoodsLockBatchItem;
import cn.abcyun.bis.rpc.sdk.cis.model.goodslocking.GoodsLockingFormItem;
import cn.abcyun.bis.rpc.sdk.cis.model.promotion.GoodsBaseInfo;
import cn.abcyun.cis.charge.api.model.GoodsItemBatchInfo;
import cn.abcyun.cis.charge.api.model.LimitPriceGoodsItem;
import cn.abcyun.cis.charge.api.model.ProcessProduct;
import cn.abcyun.cis.charge.base.*;
import cn.abcyun.cis.charge.digitalinvoice.ChargeSheetNuoNuoInvoiceService;
import cn.abcyun.cis.charge.factory.ChargeFormItemFactory;
import cn.abcyun.cis.charge.helper.ChargeTransactionRecordDiscountInfoHelper;
import cn.abcyun.cis.charge.model.*;
import cn.abcyun.cis.charge.processor.calculator.BaseItemProcessor;
import cn.abcyun.cis.charge.processor.calculator.ChargeFormItemPrintPriceInfo;
import cn.abcyun.cis.charge.processor.discount.CalculatePointRatePromotionGoodsItem;
import cn.abcyun.cis.charge.processor.discount.CalculateSinglePromotionItem;
import cn.abcyun.cis.charge.processor.discount.PromotionSimple;
import cn.abcyun.cis.charge.processor.discount.PromotionSimpleGroup;
import cn.abcyun.cis.charge.processor.limitprice.BaseLimitPriceInfo;
import cn.abcyun.cis.charge.processor.limitprice.CalculateLimitPriceItem;
import cn.abcyun.cis.charge.processor.limitprice.GoodsLimitPriceInfo;
import cn.abcyun.cis.charge.processor.limitprice.LimitPriceProcessor;
import cn.abcyun.cis.charge.rpc.model.ChargeFormItemTraceCodesRsp;
import cn.abcyun.cis.charge.service.dto.*;
import cn.abcyun.cis.charge.service.dto.print.ChargeFormItemPrintView;
import cn.abcyun.cis.charge.util.*;
import cn.abcyun.cis.commons.exception.CisCustomException;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.model.ComposeType;
import cn.abcyun.cis.commons.rpc.outpatient.UsageInfo;
import cn.abcyun.cis.commons.rpc.registration.RegistrationFormItem;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.cis.commons.util.UUIDUtils;
import cn.abcyun.common.log.marker.AbcLogMarker;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

public class PharmacyItemProcessor extends BaseItemProcessor {
    private static final Logger sLogger = LoggerFactory.getLogger(PharmacyItemProcessor.class);

    private List<ChargeFormItem> affectedItems = new ArrayList<>();

    private StatRecordAffectedDeductedItem affectedDeductItem;

    private ChargeFormItem addedRefundChargeItem;

    private boolean isSaveAddedRefundChargeItem = false;

    private GoodsItem goodsItem;

    private List<GoodsItemBatchInfo> goodsItemBatchInfo;

    private BigDecimal doseCount;

    private BigDecimal unitCount;

    private int refundType = Constants.ChargeFormItemRefundType.UNIT;

    private BigDecimal refundDoseCount = BigDecimal.ZERO;

    private BigDecimal refundUnitCount = BigDecimal.ZERO;

    private BigDecimal stockPieceCount = BigDecimal.ZERO;

    private BigDecimal stockPackageCount = BigDecimal.ZERO;

    private boolean isStockAvailable = false;
    private boolean isDismountingInvalid = false;

    /**
     * 是否缺少药品资料
     */
    private boolean isLackGoodsItem = false;

    /**
     * 平摊了议价之后的总值
     */
    private BigDecimal receivableFee;

    /**
     * 是否禁止销售
     */
    private boolean isForbidSale = false;

    private DispensingInfo dispensingInfo = null;

    private GoodsPharmacyView goodsPharmacyView;

    private BigDecimal oldSourceUnitPrice;

    /**
     * form的药房类型，只对本地药房和虚拟药房生效，空中药房不看该字段
     * {@link GoodsConst.PharmacyType}
     */
    private int formPharmacyType = GoodsConst.PharmacyType.LOCAL_PHARMACY;

    private int formSourceFormType;

    /**
     * item退费的标记 0：普通的退费项，10：叶开泰退费项，需要给提示
     */
    private int itemRefundFlag;

    private BigDecimal executedCount;

    /**
     * 此次算费的限价情况
     */
    private int calIsUseLimitPrice;

    /**
     * 社保应收金额
     */
    private BigDecimal sheBaoReceivableFee;

    private SheBaoMatchCode sheBaoMatchCode;
    /**
     * 批次信息处理器
     */
    @Getter
    private List<ItemBatchInfoProcessor> itemBatchInfoProcessors = new ArrayList<>();

    @Setter
    private Map<String, GoodsBatchInfoSnap> goodsBatchInfoSnapBatchIdMap = new HashMap<>();

    //算费匹配的所有优惠信息
    private List<SinglePromotionView> allSinglePromotions = new ArrayList<>();


    private Boolean canPayHealthCard;

    public Boolean getCanPayHealthCard() {
        return canPayHealthCard;
    }

    public void setCanPayHealthCard(Boolean canPayHealthCard) {
        this.canPayHealthCard = canPayHealthCard;

        if (!CollectionUtils.isEmpty(composeChildren)) {
            composeChildren.forEach(itemProcessor -> itemProcessor.setCanPayHealthCard(canPayHealthCard));
        }
    }

    public ChargeFormItemPrintPriceInfo generateItemPrintPriceInfo() {

        return new ChargeFormItemPrintPriceInfo()
                .setSourceTotalFee(chargeFormItem.getSourceTotalPrice())
                .setSinglePromotionFee(chargeFormItem.calculateSinglePromotionPrice())
                .setUnitAdjustmentFee(chargeFormItem.getUnitAdjustmentFee())
                .setPackagePromotionFee(chargeFormItem.calculatePackagePromotionPrice())
                .setAdjustmentFee(chargeFormItem.getAdjustmentPrice())
                .setRefundSourceTotalFee(getRefundSourceTotalFee())
                .setRefundSinglePromotionFee(getRefundSinglePromotionFee())
                .setRefundUnitAdjustmentFee(getRefundUnitAdjustmentFee())
                .setRefundPackagePromotionFee(getRefundPackagePromotionFee())
                .setRefundAdjustmentFee(getRefundAdjustmentFee());
    }

    public List<ChargeFormItemBatchInfo> getCurrentRefundBatchInfos() {
        return Optional.ofNullable(addedRefundChargeItem)
                .map(ChargeFormItem::getChargeFormItemBatchInfos)
                .orElse(new ArrayList<>());
    }

    public CalculateSinglePromotionItem generateCalculateSinglePromotionItem() {
        CalculateSinglePromotionItem discountItem = new CalculateSinglePromotionItem();
        discountItem.setId(getItemId());
        discountItem.setName(getName());
        discountItem.setProductId(getProductId());
        discountItem.setProductType(getProductType());
        discountItem.setProductSubType(getProductSubType());
        discountItem.setTotalPrice(chargeFormItem.getSourceTotalPrice());
        discountItem.setUnitPrice(chargeFormItem.getSourceUnitPrice());
        discountItem.setDoseCount(chargeFormItem.getDoseCount());
        discountItem.setUseDismounting(chargeFormItem.getUseDismounting());
        discountItem.setTotalCount(MathUtils.calculateTotalCount(getUnitCount(), getDoseCount()));
        discountItem.setPharmacyType(getPharmacyType());
        discountItem.setSinglePromotionViews(Optional.ofNullable(chargeFormItem.getAdditional())
                .map(ChargeFormItemAdditional::getSinglePromotions)
                .orElse(new ArrayList<>())
                .stream()
                .map(itemSinglePromotion -> {
                    SinglePromotionView singlePromotionView = new SinglePromotionView();
                    BeanUtils.copyProperties(itemSinglePromotion, singlePromotionView);
                    return singlePromotionView;
                })
                .collect(Collectors.toList())
        );
        discountItem.setGoodsBaseInfo(generateGoodsBaseInfo());

        return discountItem;
    }

    public List<ChargeFormItemTraceCodesRsp> generatePharmacyChargeFormItemTraceCodes() {
        List<ChargeFormItemTraceCodesRsp> itemTraceCodesRspList = Lists.newArrayList();
        if (this.isParent()) {
            this.composeChildren
                    .stream()
                    .map(PharmacyItemProcessor::generatePharmacyChargeFormItemTraceCodes)
                    .filter(Objects::nonNull)
                    .flatMap(Collection::stream)
                    .forEach(itemTraceCodesRspList::add);
        }
        // 不是已收
        if (!Objects.equals(chargeFormItem.getStatus(), Constants.ChargeFormItemStatus.CHARGED)) {
            return null;
        }
        // 追溯码使用发药的追溯码，因为发药可以对收费单传下去的追溯码进行修改
        List<TraceableCode> traceableCodeList = null;
        if (Objects.nonNull(dispensingInfo) && org.apache.commons.collections.CollectionUtils.isNotEmpty(dispensingInfo.getTraceableCodeList())) {
            traceableCodeList = dispensingInfo.getTraceableCodeList();
        }

        ChargeFormItemTraceCodesRsp itemTraceCodesRsp = ChargeFormItemTraceCodesRsp.fromChargeFormItemAndAdditional(chargeFormItem, traceableCodeList);
        if (Objects.isNull(itemTraceCodesRsp)){
            return null;
        }
        itemTraceCodesRsp.setProductInfo(goodsItem);
        itemTraceCodesRspList.add(itemTraceCodesRsp);

        // 合并已退的数量信息
        BigDecimal leftChargedUnitCount = refundType == Constants.ChargeFormItemRefundType.UNIT ? unitCount.subtract(refundUnitCount) : unitCount;
        itemTraceCodesRsp.setUnitCount(leftChargedUnitCount);
        itemTraceCodesRsp.setDoseCount(getLeftChargedDoseCount());

        if (getItemV2Status() != Constants.ChargeFormItemStatus.REFUNDED) {
            itemTraceCodesRsp.setStatus(Constants.ChargeFormItemStatus.CHARGED);
        } else {
            itemTraceCodesRsp.setStatus(Constants.ChargeFormItemStatus.REFUNDED);
        }

        // 更新特殊的追溯码
        TraceCodeUtils.updateTraceCodeDrugIdentificationCode(itemTraceCodesRsp.getTraceableCodeList());
        return itemTraceCodesRspList;
    }


    public static class ItemRefundFlag {
        public static final int NORMAL = 0;
        public static final int YEKAITAI = 10;
    }

    /**
     * 预锁库结果 如果item被限价，最后会使用这个批次信息指定锁库
     */
    private List<GoodsLockBatchItem> preLockBatchInfo = new ArrayList<>();

    public List<GoodsLockBatchItem> getPreLockBatchInfo() {

        if (getComposeType() == ComposeType.COMPOSE) {
            return composeChildren.stream()
                    .flatMap(itemProcessor -> itemProcessor.getPreLockBatchInfo().stream())
                    .collect(Collectors.toList());
        }

        return preLockBatchInfo;
    }

    public void setPreLockBatchInfo(List<GoodsLockBatchItem> preLockBatchInfo) {
        this.preLockBatchInfo = preLockBatchInfo;
    }

    public List<PharmacyItemProcessor> getComposeChildren() {
        return composeChildren;
    }

    public BigDecimal getReceivableFee() {
        return receivableFee;
    }

    public void setReceivableFee(BigDecimal receivableFee) {
        this.receivableFee = receivableFee;
    }

    public void setSheBaoReceivableFee(BigDecimal sheBaoReceivableFee) {
        this.sheBaoReceivableFee = sheBaoReceivableFee;
    }

    public void setRefundType() {

        if (isLocalChinesePrescription()) {
            this.refundType = Constants.ChargeFormItemRefundType.DOSE;
            return;
        }
        this.refundType = Constants.ChargeFormItemRefundType.UNIT;
    }

    public int getCalIsUseLimitPrice() {
        return calIsUseLimitPrice;
    }

    public void setCalIsUseLimitPrice(int calIsUseLimitPrice) {
        this.calIsUseLimitPrice = calIsUseLimitPrice;
        chargeFormItem.setIsUseLimitPrice(calIsUseLimitPrice);
    }

    public PharmacyItemProcessor(int chargeVersion, ChargeFormItem chargeFormItem, ChargeForm chargeForm, ChargeSheetRelationDto toDeleteChargeSheetRelationDto) {
        this.chargeFormItem = chargeFormItem;
        this.chargeVersion = chargeVersion;
        this.toDeleteChargeSheetRelationDto = toDeleteChargeSheetRelationDto;
        doseCount = chargeFormItem.getDoseCount();
        unitCount = chargeFormItem.getUnitCount();
        if (chargeForm != null) {
            formPharmacyType = chargeForm.getPharmacyType();
            formSourceFormType = chargeForm.getSourceFormType();
            setRefundType();
        }
        // 在状态的已支付时能确认已收的值，在待收时需要在算费时实时计算，不能在这儿赋值
        if (chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED) {
            receivableFee = MathUtils.max(BigDecimal.ZERO, MathUtils.wrapBigDecimalSubtract(getDiscountedPrice(), chargeFormItem.getReceivedPrice()));
            sheBaoReceivableFee = receivableFee;
        }
        if (chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED
                && org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeFormItem.getChargeFormItemBatchInfos())) {
            boolean updateReceivableFee = org.apache.commons.lang3.StringUtils.isNotBlank(chargeFormItem.getLockId());
            itemBatchInfoProcessors = chargeFormItem.getChargeFormItemBatchInfos()
                    .stream()
                    .map(batchInfo -> new ItemBatchInfoProcessor(batchInfo, updateReceivableFee))
                    .collect(Collectors.toList());
            // 走应收平摊到批次上
            if (!updateReceivableFee) {
                FlatPriceTool.flatPriceAndApply(receivableFee, itemBatchInfoProcessors.stream()
                        .map(itemBatchInfoProcessor -> new FlatPriceTool.IFlatCell<FlatPriceHelper.FlatPriceCell>() {
                            @Override
                            protected FlatPriceHelper.FlatPriceCell genFlatCell() {
                                ChargeFormItemBatchInfo chargeFormItemBatchInfo = itemBatchInfoProcessor.getChargeFormItemBatchInfo();
                                return new FlatPriceHelper.FlatPriceCell()
                                        .setId(chargeFormItemBatchInfo.getId())
                                        .setName("flatItemReceivableFeeToBatch")
                                        .setTotalPrice(chargeFormItemBatchInfo.calculateReceivableFee())
                                        .setPriority(FlatPriceHelper.PRIORITY_HIGH);
                            }

                            @Override
                            protected void apply(BigDecimal flatPrice) {
                                itemBatchInfoProcessor.setReceivableFeeAndShebaoReceivableFee(flatPrice);
                            }
                        }).collect(Collectors.toList()));
            }
        }
    }

    public void addRefundChargeFormItem(ChargeFormItem chargeFormItem) {
        if (chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.REFUNDED) {
            return;
        }
        refundChargeFormItems.add(chargeFormItem);
        if (!isLocalChinesePrescription()) {
            refundUnitCount = refundUnitCount.add(chargeFormItem.getUnitCount());
            refundDoseCount = chargeFormItem.getDoseCount();
            return;
        }
        // 如果退完了，直接赋值
        if (MathUtils.calculateTotalCount(unitCount, doseCount).subtract(refundUnitCount.multiply(refundDoseCount))
                .subtract(MathUtils.calculateTotalCount(chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount())).compareTo(BigDecimal.ZERO) <= 0) {
            refundUnitCount = unitCount;
            refundDoseCount = doseCount;
            return;
        }
        if (MathUtils.wrapBigDecimalCompare(unitCount, chargeFormItem.getUnitCount()) == 0) {
            // unit一样，退的是doseCount
            refundUnitCount = unitCount;
            refundDoseCount = refundDoseCount.add(chargeFormItem.getDoseCount());
            refundType = Constants.ChargeFormItemRefundType.DOSE;
            return;
        }
        // 中药处方，计算退的剂量和数量
        if (MathUtils.wrapBigDecimalCompare(doseCount, chargeFormItem.getDoseCount()) == 0) {
            // 剂量一样，退的是unit
            refundDoseCount = doseCount;
            refundUnitCount = refundUnitCount.add(chargeFormItem.getUnitCount());
            refundType = Constants.ChargeFormItemRefundType.UNIT;
        }
    }

    public boolean isCanDispensing() {
        if (isParent() && org.apache.commons.collections.CollectionUtils.isNotEmpty(composeChildren)) {
            return composeChildren.stream().anyMatch(PharmacyItemProcessor::isCanDispensing);
        }
        return chargeFormItem.isCanDispensing();
    }

    public void addComposeChild(PharmacyItemProcessor child) {
        if (child == null || TextUtils.isEmpty(child.getItemId())) {
            return;
        }
        if (composeChildren.stream().anyMatch(itemProcessor -> TextUtils.equals(itemProcessor.getItemId(), child.getItemId()))) {
            throw new RuntimeException("repeated compose child");
        }
        composeChildren.add(child);
    }

    public void pay(int payStatus, int paySource, String operatorId) {
        if (isParent()) {
            composeChildren.forEach(itemProcessor -> itemProcessor.pay(payStatus, paySource, operatorId));
        }
        if (getItemStatus() != Constants.ChargeFormItemStatus.UNCHARGED && getItemStatus() != Constants.ChargeFormItemStatus.CHARGED) {
            return;
        }
        if (chargeFormItem.getPayStatus() == ChargeFormItem.PayStatus.PAID) {
            return;
        }
        chargeFormItem.setStatus(Constants.ChargeFormItemStatus.CHARGED);
        chargeFormItem.setV2Status(Constants.ChargeFormItemStatus.CHARGED);
        chargeFormItem.setPayStatus(payStatus);
        chargeFormItem.setPaySource(paySource);
        FillUtils.fillLastModifiedBy(chargeFormItem, operatorId);
//        if (sourceFormType != Constants.SourceFormType.AIR_PHARMACY) {
//            addAffectedItems(chargeFormItem);
//        }
    }

    // 把charged和unselected重置为uncharged
    public void payBack(String operatorId, boolean payBackFinished, int sourceFormType) {
        if (isParent()) {
            composeChildren.forEach(itemProcessor -> itemProcessor.payBack(operatorId, payBackFinished, sourceFormType));
        }
        if (getItemStatus() != Constants.ChargeFormItemStatus.CHARGED && getItemStatus() != Constants.ChargeFormItemStatus.UNSELECTED) {
            return;
        }
        if (!payBackFinished) {
            if (sourceFormType != Constants.SourceFormType.AIR_PHARMACY) {
                addAffectedItems(chargeFormItem);
            }
            return;
        }
        if (chargeFormItem.getPayStatus() != ChargeFormItem.PayStatus.UNPAID) {
            chargeFormItem.setPayStatus(ChargeFormItem.PayStatus.UNPAID);
            //之前是unselected的不能加入到affectedItems
            if (sourceFormType != Constants.SourceFormType.AIR_PHARMACY) ;
            addAffectedItems(chargeFormItem);
        }

        chargeFormItem.setReceivedPrice(BigDecimal.ZERO);
//        chargeFormItem.setUnitPrice(chargeFormItem.getSourceUnitPrice());
        chargeFormItem.setAdjustmentPrice(BigDecimal.ZERO);
//        chargeFormItem.setExpectedTotalPrice(null);
//        chargeFormItem.setExpectedUnitPrice(null);
//        chargeFormItem.setFractionPrice(BigDecimal.ZERO);
        chargeFormItem.setPromotionPrice(BigDecimal.ZERO);
        chargeFormItem.setDeductTotalCount(BigDecimal.ZERO);
        chargeFormItem.setPromotionInfoJson(null);
        chargeFormItem.setCouponPromotionInfoJson(null);
        chargeFormItem.setGiftRulePromotionInfoJson(null);
        chargeFormItem.calculateDiscountedPrice();
        chargeFormItem.setStatus(Constants.ChargeFormItemStatus.UNCHARGED);
        chargeFormItem.setV2Status(Constants.ChargeFormItemStatus.UNCHARGED);
        FillUtils.fillLastModifiedBy(chargeFormItem, operatorId);

        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(itemBatchInfoProcessors)) {
            itemBatchInfoProcessors.forEach(itemBatchInfoProcessor -> itemBatchInfoProcessor.payBack(operatorId, payBackFinished));
        }
    }

    public void refund(String operatorId) {
        if (isParent()) {
            composeChildren.forEach(itemProcessor -> itemProcessor.refund(operatorId));
            if (getComposeType() == ComposeType.COMPOSE) {
                boolean allChildrenRefunded = composeChildren.stream().filter(itemProcessor -> itemProcessor.getItemV2Status() != Constants.ChargeFormItemStatus.REFUNDED).count() == 0;
                if (getItemV2Status() != Constants.ChargeFormItemStatus.REFUNDED && allChildrenRefunded) {
                    try {
                        ChargeFormItem toRefundChargeFormItem = new ChargeFormItem();
                        toRefundChargeFormItem.setId(chargeFormItem.getId());
                        toRefundChargeFormItem.setDoseCount(chargeFormItem.getDoseCount());
                        toRefundChargeFormItem.setName(chargeFormItem.getName());
                        toRefundChargeFormItem.setUnitCount(chargeFormItem.getUnitCount());
                        doUpdateRefundInfo(toRefundChargeFormItem, false, operatorId);
                    } catch (CisCustomException e) {
                        sLogger.error("refund exception", e);
                    }
                    doRefund(operatorId);
                }
            }
            if (getGoodsFeeType() == GoodsFeeType.FEE_PARENT) {
                doRefund(operatorId);
            }
        } else {
            doRefund(operatorId);
        }
    }

    private void doRefund(String operatorId) {
        if (addedRefundChargeItem == null) {
            return;
        }
        chargeFormItem.setRefundTotalPrice(MathUtils.wrapBigDecimalAdd(chargeFormItem.getRefundTotalPrice(), addedRefundChargeItem.getTotalPrice()));
        chargeFormItem.setRefundDiscountPrice(MathUtils.wrapBigDecimalAdd(chargeFormItem.getRefundDiscountPrice(), addedRefundChargeItem.getDiscountPrice()));
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(addedRefundChargeItem.getChargeFormItemBatchInfos())
                && org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeFormItem.getChargeFormItemBatchInfos())) {
            Map<String, ChargeFormItemBatchInfo> paidBatchInfoIdMap = chargeFormItem.getChargeFormItemBatchInfos().stream()
                    .collect(Collectors.toMap(ChargeFormItemBatchInfo::getId, Function.identity()));
            addedRefundChargeItem.getChargeFormItemBatchInfos()
                    .forEach(refundBatchInfo -> {
                        ChargeFormItemBatchInfo paidBatchInfo = paidBatchInfoIdMap.get(refundBatchInfo.getAssociateItemBatchInfoId());
                        if (Objects.isNull(paidBatchInfo)) {
                            sLogger.error("not found paidBatchInfo, id: {}", refundBatchInfo.getAssociateItemBatchInfoId());
                            throw new NotFoundException("not found paidBatchInfo");
                        }
                        paidBatchInfo.addRefundUnitCount(refundBatchInfo.getUnitCount());
                        paidBatchInfo.addRefundTotalPrice(refundBatchInfo.getTotalPrice());
                        FillUtils.fillLastModifiedBy(paidBatchInfo, operatorId);
                    });
        }

        if (isLocalChinesePrescription() && refundType == Constants.ChargeFormItemRefundType.DOSE) {
            chargeFormItem.setRefundDoseCount(MathUtils.wrapBigDecimalAdd(chargeFormItem.getRefundDoseCount(), addedRefundChargeItem.getDoseCount()));
            chargeFormItem.setRefundUnitCount(chargeFormItem.getUnitCount());
            if (chargeFormItem.getRefundDoseCount().compareTo(chargeFormItem.getDoseCount()) >= 0) {
                chargeFormItem.setV2Status(Constants.ChargeFormItemStatus.REFUNDED);
            }
        } else {
            chargeFormItem.setRefundDoseCount(chargeFormItem.getDoseCount());
            chargeFormItem.setRefundUnitCount(MathUtils.wrapBigDecimalAdd(chargeFormItem.getRefundUnitCount(), addedRefundChargeItem.getUnitCount()));
            if (chargeFormItem.getRefundUnitCount().compareTo(chargeFormItem.getUnitCount()) >= 0) {
                chargeFormItem.setV2Status(Constants.ChargeFormItemStatus.REFUNDED);
            }
        }

        doUpdateFormItemTraceableCodeUsedStatus();

        FillUtils.fillLastModifiedBy(chargeFormItem, operatorId);

        addAffectedItems(addedRefundChargeItem);
        isSaveAddedRefundChargeItem = true;

        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "do refund id:{}, name:{}, unitCount:{}, doseCount{}, unitPrice:{}, discountPrice:{}, totalPrice:{}",
                chargeFormItem.getId(), chargeFormItem.getName(), addedRefundChargeItem.getUnitCount(), addedRefundChargeItem.getDoseCount(),
                addedRefundChargeItem.getUnitPrice(), addedRefundChargeItem.getDiscountPrice(), addedRefundChargeItem.getTotalPrice());
    }

    public void updateRefundInfo(ChargeFormItem toRefundChargeFormItem, boolean checkCanRefundCount, String operatorId) throws CisCustomException {
        if (toRefundChargeFormItem == null) {
            return;
        }
        if (getComposeType() == ComposeType.COMPOSE) {
            if (!TextUtils.equals(toRefundChargeFormItem.getComposeParentFormItemId(), getItemId())) {
                return;
            }
            PharmacyItemProcessor subItemProcessor = composeChildren.stream().filter(child -> TextUtils.equals(toRefundChargeFormItem.getId(), child.getItemId())).findFirst().orElse(null);
            if (subItemProcessor == null) {
                return;
            }
            if (subItemProcessor.getGoodsFeeType() == GoodsFeeType.FEE_PARENT) {
                //费用母项执行退费逻辑
                subItemProcessor.doUpdateRefundInfoForGoodsFeeParent(toRefundChargeFormItem, checkCanRefundCount, operatorId);
                return;
            }
            subItemProcessor.doUpdateRefundInfo(toRefundChargeFormItem, checkCanRefundCount, operatorId);
        } else if (getGoodsFeeType() == GoodsFeeType.FEE_PARENT) {
            //费用母项执行退费逻辑
            doUpdateRefundInfoForGoodsFeeParent(toRefundChargeFormItem, checkCanRefundCount, operatorId);
        } else {
            doUpdateRefundInfo(toRefundChargeFormItem, checkCanRefundCount, operatorId);
        }
    }

    private void doUpdateRefundInfoForGoodsFeeParent(ChargeFormItem toRefundChargeFormItem, boolean checkCanRefundCount, String operatorId) {
        if (Objects.isNull(toRefundChargeFormItem) || getGoodsFeeType() != GoodsFeeType.FEE_PARENT) {
            return;
        }
        List<ChargeFormItem> childAddedRefundItems = composeChildren.stream().map(feeChildProcessor -> {
                    //先根据母项生成费用子项的退费项
                    ChargeFormItem childToRefundChargeFormItem = DTOConverter.generateGoodsFeeTypeChildRefundItem(toRefundChargeFormItem, chargeFormItem.getUnitCount(), feeChildProcessor.getChargeFormItem());
                    //调用子项退费
                    return feeChildProcessor.doUpdateRefundInfo(childToRefundChargeFormItem, checkCanRefundCount, operatorId);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        //将费用子项的数据合并为费用母项的数据
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(childAddedRefundItems)) {
            addedRefundChargeItem = new ChargeFormItem();
            BeanUtils.copyProperties(chargeFormItem, addedRefundChargeItem);
            addedRefundChargeItem.setId(AbcIdUtils.getUUID());
            addedRefundChargeItem.setReceivedPrice(BigDecimal.ZERO);
            addedRefundChargeItem.setUnitCount(toRefundChargeFormItem.getUnitCount().add(MathUtils.wrapBigDecimalOrZero(toRefundChargeFormItem.getDeductTotalCount())));
            if (isLocalChinesePrescription()) {
                addedRefundChargeItem.setDoseCount(toRefundChargeFormItem.getDoseCount());
            }
            addedRefundChargeItem.setStatus(Constants.ChargeFormItemStatus.REFUNDED);
            addedRefundChargeItem.setV2Status(Constants.ChargeFormItemStatus.NONE);
            addedRefundChargeItem.setAssociateFormItemId(chargeFormItem.getId());
            addedRefundChargeItem.setRefundUnitCount(BigDecimal.ZERO);
            addedRefundChargeItem.setRefundDiscountPrice(BigDecimal.ZERO);
            addedRefundChargeItem.setRefundTotalPrice(BigDecimal.ZERO);
            addedRefundChargeItem.setDeductTotalCount(toRefundChargeFormItem.getDeductTotalCount());
            addedRefundChargeItem.setChargeFormItemBatchInfos(new ArrayList<>());

            //原价
            BigDecimal parentTotalPrice = BigDecimal.ZERO;
            //折扣 = 营销优惠 + 议价减价
            BigDecimal parentDiscountPrice = BigDecimal.ZERO;
            //营销优惠
            BigDecimal parentPromotionPrice = BigDecimal.ZERO;
            //议价
            BigDecimal parentAdjustmentPrice = BigDecimal.ZERO;
            //单项议价
            BigDecimal parentUnitAdjustmentFee = BigDecimal.ZERO;
            //实收
            BigDecimal parentReceivedPrice = BigDecimal.ZERO;

            List<ChargeDiscountInfo> childPromotionInfos = new ArrayList<>();

            for (ChargeFormItem childAddedRefundItem : childAddedRefundItems) {
                parentTotalPrice = MathUtils.wrapBigDecimalAdd(parentTotalPrice, childAddedRefundItem.getTotalPrice());
                parentAdjustmentPrice = MathUtils.wrapBigDecimalAdd(parentAdjustmentPrice, childAddedRefundItem.getAdjustmentPrice());
                parentUnitAdjustmentFee = MathUtils.wrapBigDecimalAdd(parentUnitAdjustmentFee, childAddedRefundItem.getUnitAdjustmentFee());
                parentPromotionPrice = MathUtils.wrapBigDecimalAdd(parentPromotionPrice, childAddedRefundItem.getPromotionPrice());
                parentDiscountPrice = MathUtils.wrapBigDecimalAdd(parentDiscountPrice, childAddedRefundItem.getDiscountPrice());
                parentReceivedPrice = MathUtils.wrapBigDecimalAdd(parentReceivedPrice, childAddedRefundItem.getReceivedPrice());
                Optional.ofNullable(childAddedRefundItem.getPromotionInfo()).ifPresent(childPromotionInfos::add);
                //回写母项id
                childAddedRefundItem.setComposeParentFormItemId(addedRefundChargeItem.getId());
            }

            addedRefundChargeItem.setTotalPrice(parentTotalPrice);
            addedRefundChargeItem.setPromotionPrice(parentPromotionPrice);
            addedRefundChargeItem.setDiscountPrice(parentDiscountPrice);
            addedRefundChargeItem.setAdjustmentPrice(parentAdjustmentPrice);
            addedRefundChargeItem.setUnitAdjustmentFee(parentUnitAdjustmentFee);
            addedRefundChargeItem.setReceivedPrice(parentReceivedPrice);
            addedRefundChargeItem.setDeductTotalCount(toRefundChargeFormItem.getDeductTotalCount());
            addedRefundChargeItem.setFractionPrice(BigDecimal.ZERO);

            //将所有子项的折扣信息合并成一个
            ChargeDiscountInfo parentPromotionInfo = ChargeTransactionRecordDiscountInfoHelper.mergeChargeTransactionRecordDiscountInfo(childPromotionInfos);
            BigDecimal deductedPromotionPrice = BigDecimal.ZERO;
            //单独处理抵扣数量问题
            if (MathUtils.wrapBigDecimalCompare(addedRefundChargeItem.getDeductTotalCount(), BigDecimal.ZERO) > 0) {

                if (parentPromotionInfo == null) {
                    sLogger.info("parentPromotionInfo can not be null");
                    throw new IllegalStateException("parentPromotionInfo can not be null");
                }

                List<ChargeDiscountInfo.DeductDiscountInfo> addedRefundDeductDiscountInfos = calculateDeductPromotionInfos(addedRefundChargeItem.getDeductTotalCount());
                deductedPromotionPrice = addedRefundDeductDiscountInfos.stream()
                        .map(ChargeDiscountInfo.DeductDiscountInfo::getDiscountPrice)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                parentPromotionInfo.setDeductDiscountInfos(addedRefundDeductDiscountInfos);

                affectedDeductItem = new StatRecordAffectedDeductedItem()
                        .setId(addedRefundChargeItem.getId())
                        .setCount(addedRefundChargeItem.getDeductTotalCount())
                        .setDeductedUnitAdjustmentFee(parentPromotionInfo.getDeductedUnitAdjustmentFee())
                        .setUnitPrice(addedRefundChargeItem.getUnitPrice())
                        .setTotalPrice(deductedPromotionPrice);
            }

            //反算零头
            BigDecimal fractionPrice = addedRefundChargeItem.getTotalPrice()
                    .subtract(MathUtils.calculateTotalPrice(addedRefundChargeItem.getUnitPrice(), MathUtils.wrapBigDecimalSubtract(addedRefundChargeItem.getUnitCount(), addedRefundChargeItem.getDeductTotalCount()), addedRefundChargeItem.getDoseCount(), 2))
                    .subtract(deductedPromotionPrice.negate())
                    .subtract(MathUtils.wrapBigDecimalCompare(addedRefundChargeItem.getAdjustmentPrice(), BigDecimal.ZERO) > 0 ? addedRefundChargeItem.getAdjustmentPrice() : BigDecimal.ZERO);

            if (fractionPrice.compareTo(BigDecimal.ZERO) > 0) {
                addedRefundChargeItem.setFractionPrice(fractionPrice);
            }

            Optional.ofNullable(parentPromotionInfo)
                    .ifPresent(p -> {
                        ChargeDiscountInfo itemPromotionInfo = new ChargeDiscountInfo();
                        BeanUtils.copyProperties(p, itemPromotionInfo);
                        addedRefundChargeItem.setPromotionInfo(itemPromotionInfo);
                        addedRefundChargeItem.setPromotionInfoJson(JsonUtils.dump(itemPromotionInfo));
                    });
            addedRefundChargeItem.calculateRefundDiscountedPrice(deductedPromotionPrice);
            FillUtils.fillCreatedBy(addedRefundChargeItem, operatorId);

            addRefundChargeFormItem(addedRefundChargeItem);
        }

    }

    public void checkDeductComposeItemCanRefund(List<ChargeFormItem> toRefundChargeFormItems) {
        if (getComposeType() != ComposeType.COMPOSE) {
            return;
        }

        if (MathUtils.wrapBigDecimalCompare(getDeductedTotalCount(), BigDecimal.ZERO) <= 0) {
            return;
        }

        Map<String, ChargeFormItem> allToRefundComposeChildren = toRefundChargeFormItems.stream()
                .filter(toRefundChargeFormItem -> toRefundChargeFormItem.getComposeType() == ComposeType.COMPOSE_SUB_ITEM
                        && TextUtils.equals(toRefundChargeFormItem.getComposeParentFormItemId(), getItemId())
                ).collect(Collectors.toMap(ChargeFormItem::getId, Function.identity(), (a, b) -> a));

        for (PharmacyItemProcessor subItemProcessor : composeChildren) {
            ChargeFormItem toRefundChargeFormItem = allToRefundComposeChildren.get(subItemProcessor.getItemId());
            if (toRefundChargeFormItem == null) {
                sLogger.error("toRefundChargeFormItem is null");
                throw new ChargeServiceException(ChargeServiceError.REFUND_DEDUCT_COMPOSE_MUST_ALL_REFUND);
            }

            if (MathUtils.calculateTotalCount(subItemProcessor.getUnitCount(), subItemProcessor.getDoseCount())
                    .compareTo(MathUtils.wrapBigDecimalAdd(toRefundChargeFormItem.getDeductTotalCount(), MathUtils.calculateTotalCount(toRefundChargeFormItem.getUnitCount(), toRefundChargeFormItem.getDoseCount()))) != 0) {
                sLogger.error("抵扣过的套餐的子项没有全部退完，不能退套餐");
                throw new ChargeServiceException(ChargeServiceError.REFUND_DEDUCT_COMPOSE_MUST_ALL_REFUND);
            }
        }
    }

    private ChargeFormItem doUpdateRefundInfo(ChargeFormItem toRefundChargeFormItem, boolean checkCanRefundCount, String operatorId) throws CisCustomException {
        if (getItemStatus() != Constants.ChargeFormItemStatus.CHARGED || unitCount.compareTo(BigDecimal.ZERO) <= 0) {
            return null;
        }
        if (!TextUtils.equals(chargeFormItem.getId(), toRefundChargeFormItem.getId())) {
            return null;
        }
        BigDecimal toRefundTotalCount = toRefundChargeFormItem.getDoseCount().multiply(toRefundChargeFormItem.getUnitCount());
        BigDecimal canRefundTotalCount = getCanRefundDoseCount().multiply(getCanRefundUnitCount());
        BigDecimal canRefundDeductCount = getCanRefundDeductCount();
        if (!isLocalChinesePrescription() && MathUtils.wrapBigDecimal(toRefundChargeFormItem.getDoseCount(), BigDecimal.ONE).compareTo(MathUtils.wrapBigDecimal(chargeFormItem.getDoseCount(), BigDecimal.ONE)) != 0) {
            throw new CisCustomException(ChargeServiceError.REFUND_DOSE_COUNT_NOT_MATCH.getCode(), TextUtils.alwaysString(toRefundChargeFormItem.getName()) + ChargeServiceError.REFUND_DOSE_COUNT_NOT_MATCH.getMessage());
        }
        // 校验数量是否超过可退
        if (checkCanRefundCount) {
            if (toRefundTotalCount.compareTo(canRefundTotalCount.subtract(canRefundDeductCount)) > 0) {
                sLogger.error("toRefundTotalCount > canRefundTotalCount.subtract(canRefundDeductCount), id: {}, toRefundTotalCount: {}, canRefundTotalCount: {}, canRefundDeductCount: {}", toRefundChargeFormItem.getId(), toRefundTotalCount, canRefundTotalCount, canRefundDeductCount);
                throw new CisCustomException(ChargeServiceError.REFUND_UNIT_COUNT_OUT.getCode(), TextUtils.alwaysString(toRefundChargeFormItem.getName()) + ChargeServiceError.REFUND_UNIT_COUNT_OUT.getMessage());
            }
            //抵扣次数是否超过可退
            if (MathUtils.wrapBigDecimalOrZero(toRefundChargeFormItem.getDeductTotalCount()).compareTo(canRefundDeductCount) > 0) {
                throw new CisCustomException(ChargeServiceError.REFUND_DEDUCT_TOTAL_COUNT_OUT.getCode(), TextUtils.alwaysString(toRefundChargeFormItem.getName()) + ChargeServiceError.REFUND_DEDUCT_TOTAL_COUNT_OUT.getMessage());
            }
            // 校验执行次数是否满足可退条件
            if (executedCount != null && toRefundTotalCount.compareTo(MathUtils.max(canRefundTotalCount.subtract(executedCount), BigDecimal.ZERO)) > 0) {
                sLogger.error("退之后的数量小于了执行数量，toRefundTotalCount: {}, canRefundTotalCount: {}, executedCount: {}", toRefundTotalCount, canRefundTotalCount, executedCount);
                String errorMessage = String.format(ChargeServiceError.CHARGE_FORM_ITEM_REFUND_COUNT_ERROR.getMessage(), TextUtils.alwaysString(toRefundChargeFormItem.getName()), MathUtils.max(canRefundTotalCount.subtract(executedCount), BigDecimal.ZERO).stripTrailingZeros().toPlainString());
                throw new CisCustomException(ChargeServiceError.CHARGE_FORM_ITEM_REFUND_COUNT_ERROR.getCode(), errorMessage);
            }
        }
        addedRefundChargeItem = new ChargeFormItem();
        BeanUtils.copyProperties(chargeFormItem, addedRefundChargeItem);
        if (chargeFormItem.getAdditional() != null) {
            ChargeFormItemAdditional refundAdditional = JsonUtils.readValue(JsonUtils.dump(chargeFormItem.getAdditional()), ChargeFormItemAdditional.class);
            if (Objects.nonNull(refundAdditional)) {
                refundAdditional.setSinglePromotions(null);
            }
            addedRefundChargeItem.setAdditional(refundAdditional);
        }

        // 处理追溯码
        ChargeFormItemAdditional refundAdditional = toRefundChargeFormItem.getAdditional();
        if (Objects.nonNull(refundAdditional) && Objects.nonNull(chargeFormItem.getAdditional())
                && org.apache.commons.collections.CollectionUtils.isNotEmpty(refundAdditional.getTraceableCodeList())
                && org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeFormItem.getAdditional().getTraceableCodeList())) {
            Set<String> traceableCodeSet = refundAdditional.getTraceableCodeList().stream().map(TraceableCode::getNo).collect(Collectors.toSet());
            for (TraceableCode traceableCode : chargeFormItem.getAdditional().getTraceableCodeList()) {
                if (traceableCodeSet.contains(traceableCode.getNo())) {
                    // 2表示已退状态
                    traceableCode.setUsed(2);
                }
            }
        }

        addedRefundChargeItem.setId(AbcIdUtils.getUUID());
        addedRefundChargeItem.setReceivedPrice(BigDecimal.ZERO);
        addedRefundChargeItem.setUnitCount(toRefundChargeFormItem.getUnitCount().add(MathUtils.wrapBigDecimalOrZero(toRefundChargeFormItem.getDeductTotalCount())));
        if (isLocalChinesePrescription()) {
            addedRefundChargeItem.setDoseCount(toRefundChargeFormItem.getDoseCount());
        }
        addedRefundChargeItem.setStatus(Constants.ChargeFormItemStatus.REFUNDED);
        addedRefundChargeItem.setV2Status(Constants.ChargeFormItemStatus.NONE);
        addedRefundChargeItem.setAssociateFormItemId(chargeFormItem.getId());
        addedRefundChargeItem.setRefundUnitCount(BigDecimal.ZERO);
        addedRefundChargeItem.setRefundDiscountPrice(BigDecimal.ZERO);
        addedRefundChargeItem.setRefundTotalPrice(BigDecimal.ZERO);
        addedRefundChargeItem.setDeductTotalCount(toRefundChargeFormItem.getDeductTotalCount());
        addedRefundChargeItem.setChargeFormItemBatchInfos(new ArrayList<>());
        addedRefundChargeItem.setIsExpectedBatch(toRefundChargeFormItem.getIsExpectedBatch());

        // 本次抵扣的promotion值
        List<ChargeDiscountInfo.DeductDiscountInfo> addedRefundDeductDiscountInfos = calculateDeductPromotionInfos(toRefundChargeFormItem.getDeductTotalCount());
        BigDecimal deductPromotionPrice = addedRefundDeductDiscountInfos.stream()
                .map(ChargeDiscountInfo.DeductDiscountInfo::getDiscountPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (!CollectionUtils.isEmpty(chargeFormItem.getChargeFormItemBatchInfos())) {
            //校验退费数量与指定批次的数量是否一致，不一致则清空指定的批次信息，走推荐退批次的逻辑
            toRefundChargeFormItem.amendExpectedBatch();

            addRefundBatchInfo(addedRefundChargeItem,
                    toRefundChargeFormItem.getIsExpectedBatch() == 1 ? toRefundChargeFormItem.getChargeFormItemBatchInfos() : null,
                    checkCanRefundCount,
                    operatorId);
        }
        // 抵扣掉的议价金额
        BigDecimal deductedUnitAdjustmentFee = calculateRefundDeductedUnitAdjustmentFee(addedRefundChargeItem.getDeductTotalCount());
        // 计算退费金额
        // 本次退完
        boolean isAllRefund = addedRefundChargeItem.getUnitCount().multiply(addedRefundChargeItem.getDoseCount())
                .add(refundUnitCount.multiply(refundDoseCount))
                .compareTo(unitCount.multiply(doseCount)) == 0;
        if (isAllRefund) {
            ChargeDiscountInfo discountInfo = ChargeTransactionRecordDiscountInfoHelper.generateToRecordChargeTransactionRecordDiscountInfoForLastRecord(chargeFormItem.getPromotionInfo(), getRefundPromotionInfo());
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(addedRefundDeductDiscountInfos)) {
                discountInfo = discountInfo != null ? discountInfo : new ChargeDiscountInfo();
                // 按顺序退抵扣
                discountInfo.setDeductDiscountInfos(addedRefundDeductDiscountInfos);
            }
            addedRefundChargeItem.setPromotionInfoJson(JsonUtils.dump(discountInfo));
            addedRefundChargeItem.setPromotionInfo(discountInfo);
            addedRefundChargeItem.setPromotionPrice(MathUtils.wrapBigDecimalOrZero(chargeFormItem.getPromotionPrice()).subtract(getRefundPromotionPrice()));
            addedRefundChargeItem.setAdjustmentPrice(MathUtils.wrapBigDecimalOrZero(chargeFormItem.getAdjustmentPrice()).subtract(getRefundAdjustmentPrice()));
            addedRefundChargeItem.setUnitAdjustmentFee(MathUtils.wrapBigDecimalOrZero(chargeFormItem.getUnitAdjustmentFee()).subtract(getRefundUnitAdjustmentFee()));
            addedRefundChargeItem.setFractionPrice(MathUtils.wrapBigDecimalOrZero(chargeFormItem.getFractionPrice()).subtract(getRefundFractionPrice()));
            addedRefundChargeItem.setDiscountPrice(MathUtils.wrapBigDecimalOrZero(chargeFormItem.getDiscountPrice()).subtract(getRefundDiscountPrice()));
            addedRefundChargeItem.setSourceTotalPrice(MathUtils.max(MathUtils.wrapBigDecimalSubtract(chargeFormItem.getSourceTotalPrice(), getRefundSourceTotalFee()), BigDecimal.ZERO));
            addedRefundChargeItem.setTotalCostPrice(MathUtils.max(BigDecimal.ZERO, MathUtils.wrapBigDecimalOrZero(chargeFormItem.getTotalCostPrice()).subtract(getRefundTotalCostPrice())));
            if (getComposeType() == ComposeType.COMPOSE) {
                addedRefundChargeItem.setTotalPrice(chargeFormItem.getTotalPrice());
            } else {
                addedRefundChargeItem.setTotalPrice(getTotalPrice(true).subtract(getRefundTotalPriceView()));
            }
        } else {
            // 收费项总的抵扣的promotion值
            BigDecimal itemDeductPromotionPrice = Optional.ofNullable(chargeFormItem.getPromotionInfo()).map(ChargeDiscountInfo::getDeductDiscountInfos)
                    .orElse(new ArrayList<>())
                    .stream().map(ChargeDiscountInfo.DeductDiscountInfo::getDiscountPrice)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal totalNum, refundNum;
            // 如果是走批次限价，那么退款的时候要从批次往上算
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(addedRefundChargeItem.getChargeFormItemBatchInfos())) {
                List<ChargeDiscountInfo> recordDiscountInfos = addedRefundChargeItem.getChargeFormItemBatchInfos()
                        .stream()
                        .map(ChargeFormItemBatchInfo::getPromotionInfo)
                        .filter(Objects::nonNull)
                        .map(itemPromotionInfo -> {
                            ChargeDiscountInfo recordDiscountInfo = new ChargeDiscountInfo();
                            BeanUtils.copyProperties(itemPromotionInfo, recordDiscountInfo);
                            return recordDiscountInfo;
                        })
                        .collect(Collectors.toList());
                ChargeDiscountInfo addedPromotionInfo = Optional.ofNullable(ChargeTransactionRecordDiscountInfoHelper.mergeChargeTransactionRecordDiscountInfo(recordDiscountInfos))
                        .map(recordDiscountInfo -> {
                            ChargeDiscountInfo itemPromotionInfo = new ChargeDiscountInfo();
                            BeanUtils.copyProperties(recordDiscountInfo, itemPromotionInfo);
                            return itemPromotionInfo;
                        }).orElse(null);

                addedRefundChargeItem.setPromotionInfoJson(JsonUtils.dump(addedPromotionInfo));
                addedRefundChargeItem.setPromotionInfo(addedPromotionInfo);
                addedRefundChargeItem.setSourceTotalPrice(addedRefundChargeItem.getChargeFormItemBatchInfos()
                        .stream()
                        .map(ChargeFormItemBatchInfo::getSourceTotalPrice)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                );
                if (addedPromotionInfo != null) {
                    addedRefundChargeItem.setPromotionPrice(addedPromotionInfo.calculatePromotionPrice());
                    addedRefundChargeItem.setAdjustmentPrice(addedPromotionInfo.getAdjustmentFee());
                    addedRefundChargeItem.setUnitAdjustmentFee(addedPromotionInfo.getUnitAdjustmentFee());
                } else {
                    addedRefundChargeItem.setAdjustmentPrice(BigDecimal.ZERO);
                    addedRefundChargeItem.setUnitAdjustmentFee(BigDecimal.ZERO);
                    addedRefundChargeItem.setPromotionPrice(BigDecimal.ZERO);
                }

                BigDecimal refundTotalPrice = addedRefundChargeItem.getChargeFormItemBatchInfos().stream().map(ChargeFormItemBatchInfo::getTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                addedRefundChargeItem.setTotalPrice(refundTotalPrice);

                addedRefundChargeItem.setTotalCostPrice(addedRefundChargeItem.getChargeFormItemBatchInfos()
                        .stream()
                        .map(ChargeFormItemBatchInfo::getTotalCostPrice)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                );
                int unitCostPriceScale = chargeFormItem.getUnitCostPrice().stripTrailingZeros().scale();
                MathUtils.CalculateExpectedUnitPriceResult unitCostPriceResult = MathUtils.calculateExpectedUnitPriceBase(addedRefundChargeItem.getUnitCount(), addedRefundChargeItem.getDoseCount(), addedRefundChargeItem.getTotalCostPrice(), unitCostPriceScale, 2);
                addedRefundChargeItem.setUnitCostPrice(unitCostPriceResult.expectedUnitPrice);
            } else {
                totalNum = MathUtils.wrapBigDecimalSubtract(MathUtils.wrapBigDecimalMultiply(chargeFormItem.getDoseCount(), unitCount), chargeFormItem.getDeductTotalCount());
                refundNum = toRefundChargeFormItem.getDoseCount().multiply(toRefundChargeFormItem.getUnitCount());
                // 除了抵扣之外本次要退的折扣比例
                BigDecimal excludeDeductPromotionPrice = BigDecimal.ZERO;
                if (totalNum.compareTo(BigDecimal.ZERO) != 0) {
                    excludeDeductPromotionPrice = chargeFormItem.getPromotionPrice().subtract(itemDeductPromotionPrice).multiply(refundNum).divide(totalNum, 2, RoundingMode.HALF_UP);
                    ChargeDiscountInfo discountInfo = ChargeTransactionRecordDiscountInfoHelper.generateToRecordChargeTransactionRecordDiscountInfo(chargeFormItem.getPromotionInfo(), excludeDeductPromotionPrice, refundNum, totalNum, chargeVersion == ChargeVersionConstants.V1);
                    if (discountInfo != null || org.apache.commons.collections.CollectionUtils.isNotEmpty(addedRefundDeductDiscountInfos)) {
                        ChargeDiscountInfo addedRefundItemPromotionInfo = new ChargeDiscountInfo();
                        if (discountInfo != null) {
                            BeanUtils.copyProperties(discountInfo, addedRefundItemPromotionInfo);
                        }
                        addedRefundItemPromotionInfo.setDeductDiscountInfos(addedRefundDeductDiscountInfos);
                        addedRefundItemPromotionInfo.setDeductedUnitAdjustmentFee(deductedUnitAdjustmentFee);
                        addedRefundItemPromotionInfo.setUnitAdjustmentFee(MathUtils.wrapBigDecimalAdd(addedRefundItemPromotionInfo.getUnitAdjustmentFeeIgnoreDeduct(), deductedUnitAdjustmentFee));
                        addedRefundChargeItem.setPromotionInfoJson(JsonUtils.dump(addedRefundItemPromotionInfo));
                        addedRefundChargeItem.setPromotionInfo(addedRefundItemPromotionInfo);
                        addedRefundChargeItem.setAdjustmentPrice(addedRefundItemPromotionInfo.getAdjustmentFee());
                        addedRefundChargeItem.setUnitAdjustmentFee(addedRefundItemPromotionInfo.getUnitAdjustmentFee());
                    } else {
                        addedRefundChargeItem.setAdjustmentPrice(BigDecimal.ZERO);
                        addedRefundChargeItem.setUnitAdjustmentFee(BigDecimal.ZERO);
                    }
                    addedRefundChargeItem.setSourceTotalPrice(chargeFormItem.getSourceTotalPrice().multiply(refundNum).divide(totalNum, 2, RoundingMode.HALF_DOWN));
                    addedRefundChargeItem.setTotalCostPrice(MathUtils.wrapBigDecimalMultiply(addedRefundChargeItem.getUnitCostPrice(), MathUtils.calculateTotalCount(addedRefundChargeItem.getUnitCount(), addedRefundChargeItem.getDoseCount())).setScale(4, RoundingMode.DOWN));
                } else {
                    addedRefundChargeItem.setAdjustmentPrice(BigDecimal.ZERO);
                    addedRefundChargeItem.setUnitAdjustmentFee(BigDecimal.ZERO);
                    addedRefundChargeItem.setTotalCostPrice(BigDecimal.ZERO);

                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(addedRefundDeductDiscountInfos)) {
                        ChargeDiscountInfo addedRefundItemPromotionInfo = new ChargeDiscountInfo();
                        addedRefundItemPromotionInfo.setDeductDiscountInfos(addedRefundDeductDiscountInfos);
                        addedRefundItemPromotionInfo.setDeductedUnitAdjustmentFee(deductedUnitAdjustmentFee);
                        addedRefundItemPromotionInfo.setUnitAdjustmentFee(MathUtils.wrapBigDecimalAdd(addedRefundItemPromotionInfo.getUnitAdjustmentFeeIgnoreDeduct(), deductedUnitAdjustmentFee));
                        addedRefundChargeItem.setPromotionInfoJson(JsonUtils.dump(addedRefundItemPromotionInfo));
                        addedRefundChargeItem.setPromotionInfo(addedRefundItemPromotionInfo);
                        addedRefundChargeItem.setUnitAdjustmentFee(addedRefundItemPromotionInfo.getUnitAdjustmentFee());
                    }
                }
                addedRefundChargeItem.setPromotionPrice(excludeDeductPromotionPrice.add(deductPromotionPrice));

                BigDecimal refundTotalPrice = addedRefundChargeItem.getChargeFormItemBatchInfos().stream().map(ChargeFormItemBatchInfo::getTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                addedRefundChargeItem.setTotalPrice(refundTotalPrice);

                // 校验 如果没有限价 则算出来的单价和原单价一致
                if (chargeFormItem.getIsUseLimitPrice() == 0) {
                    addedRefundChargeItem.setUnitPrice(chargeFormItem.getUnitPrice());
                    BigDecimal fractionPrice = MathUtils.wrapBigDecimalSubtract(addedRefundChargeItem.getTotalPrice(), MathUtils.calculateTotalPrice(addedRefundChargeItem.getUnitPrice(), addedRefundChargeItem.getUnitCount(), addedRefundChargeItem.getDoseCount(), 2));
                    addedRefundChargeItem.setFractionPrice(MathUtils.max(fractionPrice, BigDecimal.ZERO));
                } else {
                    int unitScale = chargeFormItem.getUnitPrice().stripTrailingZeros().scale();
                    MathUtils.CalculateExpectedUnitPriceResult priceResult = MathUtils.calculateExpectedUnitPriceBase(addedRefundChargeItem.getUnitCount(), addedRefundChargeItem.getDoseCount(), refundTotalPrice, unitScale, 2);
                    addedRefundChargeItem.setFractionPrice(priceResult.fractionPrice);
                    addedRefundChargeItem.setUnitPrice(priceResult.expectedUnitPrice);
                }
                addedRefundChargeItem.calculateRefundDiscountedPrice(deductPromotionPrice);
            }
        }
        // 退营销活动的赠品数量 refundSinglePromotionSellLimitCount
        refundSinglePromotionSellLimitCount(addedRefundChargeItem);
        FillUtils.fillCreatedBy(addedRefundChargeItem, operatorId);
        if (MathUtils.wrapBigDecimalCompare(addedRefundChargeItem.getDeductTotalCount(), BigDecimal.ZERO) > 0) {
            affectedDeductItem = new StatRecordAffectedDeductedItem()
                    .setId(addedRefundChargeItem.getId())
                    .setCount(addedRefundChargeItem.getDeductTotalCount())
                    .setDeductedUnitAdjustmentFee(deductedUnitAdjustmentFee)
                    .setUnitPrice(addedRefundChargeItem.getUnitPrice())
                    .setTotalPrice(deductPromotionPrice);
        }
        if (chargeVersion == ChargeVersionConstants.V1) {
            addedRefundChargeItem.setReceivablePrice(addedRefundChargeItem.calculateDiscountedPriceV2());
        }
        addRefundChargeFormItem(addedRefundChargeItem);
        return addedRefundChargeItem;
    }

    public BigDecimal getRefundTotalCostPrice() {
        return doCalculateEachRefundFeeFuncCore(ChargeFormItem::getTotalCostPrice);
    }

    private void refundSinglePromotionSellLimitCount(ChargeFormItem addedRefundChargeItem) {
        if (chargeFormItem.getAdditional() == null || org.apache.commons.collections.CollectionUtils.isEmpty(chargeFormItem.getAdditional().getSinglePromotions())) {
            return;
        }
        ItemSinglePromotion paidItemSinglePromotion = chargeFormItem.getAdditional().getSinglePromotions()
                .stream()
                .filter(itemSinglePromotion -> itemSinglePromotion.getParticipationDiscountCount() != null
                        && itemSinglePromotion.getChecked()
                        && itemSinglePromotion.getLeftSaleCount() != null
                        && (itemSinglePromotion.getStatus() == ItemSinglePromotion.Status.USED || itemSinglePromotion.getStatus() == ItemSinglePromotion.Status.PARTED_REFUND))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(paidItemSinglePromotion)) {
            return;
        }
        //计算本次退的数量
        BigDecimal refundTotalCount = MathUtils.calculateTotalCount(addedRefundChargeItem.getUnitCount(), addedRefundChargeItem.getDoseCount());

        if (MathUtils.compareZero(refundTotalCount) <= 0) {
            return;
        }

        ChargeFormItemAdditional additional = Optional.ofNullable(addedRefundChargeItem.getAdditional()).orElse(new ChargeFormItemAdditional());
        additional.setSinglePromotions(null);

        BigDecimal refundParticipationDiscountCount = MathUtils.min(MathUtils.wrapBigDecimalSubtract(paidItemSinglePromotion.getParticipationDiscountCount(), paidItemSinglePromotion.getRefundParticipationDiscountCount()), refundTotalCount);

        ItemSinglePromotion refundSinglePromotion = new ItemSinglePromotion();
        BeanUtils.copyProperties(paidItemSinglePromotion, refundSinglePromotion);
        refundSinglePromotion.setParticipationDiscountCount(refundParticipationDiscountCount)
                .setStatus(ItemSinglePromotion.Status.UN_USE)
                .setRefundParticipationDiscountCount(null);

        additional.setSinglePromotions(Collections.singletonList(refundSinglePromotion));
        addedRefundChargeItem.setAdditional(additional);
    }

    /**
     * 计算退费的被抵扣的单项议价
     *
     * @param currentRefundDeductTotalCount 当前退费的抵扣数量
     * @return
     */
    private BigDecimal calculateRefundDeductedUnitAdjustmentFee(BigDecimal currentRefundDeductTotalCount) {
        if (MathUtils.wrapBigDecimalCompare(currentRefundDeductTotalCount, BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        //计算当前可退的数量
        BigDecimal canRefundDeductCount = MathUtils.wrapBigDecimalSubtract(chargeFormItem.getDeductTotalCount(), getRefundedDeductedCount());
        BigDecimal canRefundDeductUnitAdjustmentFee = Optional.ofNullable(chargeFormItem.getPromotionInfo())
                .map(ChargeDiscountInfo::getDeductedUnitAdjustmentFee)
                .orElse(BigDecimal.ZERO)
                .subtract(
                        refundChargeFormItems.stream()
                                .map(refundChargeFormItem -> Optional.ofNullable(refundChargeFormItem.getPromotionInfo())
                                        .map(ChargeDiscountInfo::getDeductedUnitAdjustmentFee)
                                        .orElse(BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add)
                );
        if (canRefundDeductCount.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        if (currentRefundDeductTotalCount.compareTo(canRefundDeductCount) >= 0) {
            return canRefundDeductUnitAdjustmentFee;
        }
        return Optional.ofNullable(chargeFormItem.getPromotionInfo())
                .map(ChargeDiscountInfo::getDeductedUnitAdjustmentFee)
                .orElse(BigDecimal.ZERO)
                .multiply(currentRefundDeductTotalCount)
                .divide(chargeFormItem.getDeductTotalCount(), 2, RoundingMode.DOWN);
    }

    private BigDecimal getRefundedDeductedCount() {
        return refundChargeFormItems.stream()
                .map(ChargeFormItem::getDeductTotalCount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public List<ChargeDiscountInfo.DeductDiscountInfo> calculateDeductPromotionInfos(BigDecimal deductedCount) {
        if (MathUtils.wrapBigDecimalCompare(deductedCount, BigDecimal.ZERO) <= 0) {
            return new ArrayList<>();
        }
        //得到canRefundDeductDiscountInfos
        List<ChargeDiscountInfo.DeductDiscountInfo> canRefundDeductDiscountInfos = getCanRefundDeductDiscountInfos();

        BigDecimal leftToRecordCount = deductedCount;
        List<ChargeDiscountInfo.DeductDiscountInfo> addedRefundDeductDiscountInfos = new ArrayList<>();
        for (ChargeDiscountInfo.DeductDiscountInfo itemDeductDiscountInfo : canRefundDeductDiscountInfos) {
            if (leftToRecordCount.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }
            if (MathUtils.wrapBigDecimalCompare(itemDeductDiscountInfo.getDeductedCount(), BigDecimal.ZERO) <= 0) {
                continue;
            }
            //计算当前可退的抵扣数量
            BigDecimal currentDeductCount = MathUtils.min(itemDeductDiscountInfo.getDeductedCount(), leftToRecordCount);
            BigDecimal currentDeductPrice = MathUtils.calculateTotalPrice(currentDeductCount, itemDeductDiscountInfo.getUnitPrice()).negate();
            if (MathUtils.wrapBigDecimalCompare(currentDeductCount, itemDeductDiscountInfo.getDeductedCount()) == 0) {
                currentDeductPrice = itemDeductDiscountInfo.getDiscountPrice();
            }

            ChargeDiscountInfo.DeductDiscountInfo currentDeductDiscountInfo = new ChargeDiscountInfo.DeductDiscountInfo();
            BeanUtils.copyProperties(itemDeductDiscountInfo, currentDeductDiscountInfo);
            currentDeductDiscountInfo.setDeductedCount(currentDeductCount);
            currentDeductDiscountInfo.setDiscountPrice(currentDeductPrice);

            addedRefundDeductDiscountInfos.add(currentDeductDiscountInfo);

            leftToRecordCount = leftToRecordCount.subtract(currentDeductCount);
        }

        return addedRefundDeductDiscountInfos;
    }

    private List<ChargeDiscountInfo.DeductDiscountInfo> getCanRefundDeductDiscountInfos() {
        //收费的抵扣列表
        List<ChargeDiscountInfo.DeductDiscountInfo> itemDeductDiscountInfos = Optional.ofNullable(chargeFormItem.getPromotionInfo())
                .map(itemPromotionInfo -> JsonUtils.readValue(JsonUtils.dump(itemPromotionInfo), ChargeDiscountInfo.class))
                .map(ChargeDiscountInfo::getDeductDiscountInfos)
                .orElse(new ArrayList<>());

        if (org.apache.commons.collections.CollectionUtils.isEmpty(itemDeductDiscountInfos)) {
            return new ArrayList<>();
        }

        //已退的抵扣列表
        Map<String, ChargeDiscountInfo.DeductDiscountInfo> refundedDeductPromotionInfoMap = Optional.ofNullable(getRefundPromotionInfo())
                .map(ChargeDiscountInfo::getDeductDiscountInfos)
                .orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.toMap(ChargeDiscountInfo.DeductDiscountInfo::getPresentId, Function.identity(), (a, b) -> a));


        if (MapUtils.isEmpty(refundedDeductPromotionInfoMap)) {
            return itemDeductDiscountInfos;
        }

        //收费的抵扣列表减去退费的抵扣列表得到剩余可退的抵扣列表
        return itemDeductDiscountInfos.stream()
                .peek(deductDiscountInfo -> {
                    ChargeDiscountInfo.DeductDiscountInfo refundedDeductDiscountInfo = refundedDeductPromotionInfoMap.get(deductDiscountInfo.getPresentId());
                    if (Objects.isNull(refundedDeductDiscountInfo)) {
                        return;
                    }

                    deductDiscountInfo.setDeductedCount(MathUtils.wrapBigDecimalSubtract(deductDiscountInfo.getDeductedCount(), refundedDeductDiscountInfo.getDeductedCount()));
                    deductDiscountInfo.setDiscountPrice(MathUtils.wrapBigDecimalSubtract(deductDiscountInfo.getDiscountPrice(), refundedDeductDiscountInfo.getDiscountPrice()));
                })
                .sorted(Comparator.comparing(deductDiscountInfo -> Long.parseLong(deductDiscountInfo.getId())))
                .collect(Collectors.toList());
    }

    private void addRefundBatchInfo(ChargeFormItem refundChargeItem, List<ChargeFormItemBatchInfo> toRefundBatchInfos, boolean checkCanRefundCount, String operatorId) {
        refundChargeItem.setChargeFormItemBatchInfos(new ArrayList<>());
        BigDecimal needRefundCount = MathUtils.wrapBigDecimalMultiply(refundChargeItem.getUnitCount(), refundChargeItem.getDoseCount());
        List<ChargeFormItemBatchInfo> paidBatchInfos = ChargeFormItemUtils.getEfficientChargeFormItemBatchInfos(chargeFormItem);
        List<ChargeFormItemBatchInfo> existedRefundBatchInfos = getRefundChargeFormItemBatchInfos();

        List<ChargeFormItemBatchInfo> addedRefundBatchInfos;

        //没有指定批次退，走默认匹配逻辑
        if (CollectionUtils.isEmpty(toRefundBatchInfos)) {
            addedRefundBatchInfos = defaultRefundBatchInfo(refundChargeItem, paidBatchInfos, existedRefundBatchInfos, needRefundCount, operatorId);
        } else {
            addedRefundBatchInfos = appointRefundBatchInfo(refundChargeItem, paidBatchInfos, existedRefundBatchInfos, toRefundBatchInfos, checkCanRefundCount, operatorId);
        }

        //校验退费数量是否与批次数量一致
        BigDecimal batchTotalCount = addedRefundBatchInfos
                .stream()
                .map(ChargeFormItemBatchInfo::getUnitCount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (checkCanRefundCount && needRefundCount.compareTo(batchTotalCount) != 0) {
            sLogger.error("退费数量与退的批次数量不一致, chargeFormItemId: {}，needRefundCount: {}, batchTotalCount: {}, addedRefundBatchInfos: {}", chargeFormItem.getId(), needRefundCount, batchTotalCount, JsonUtils.dump(addedRefundBatchInfos));
            throw new ChargeServiceException(ChargeServiceError.CHARGE_FORM_ITEM_BATCH_INFO_CHANGED);
        }

        refundChargeItem.setChargeFormItemBatchInfos(addedRefundBatchInfos);
    }

    private List<ChargeFormItemBatchInfo> getRefundChargeFormItemBatchInfos() {
        return Optional.ofNullable(refundChargeFormItems)
                .orElse(new ArrayList<>())
                .stream()
                .filter(r -> org.apache.commons.collections.CollectionUtils.isNotEmpty(r.getChargeFormItemBatchInfos()))
                .flatMap(r -> r.getChargeFormItemBatchInfos().stream())
                .collect(Collectors.toList());
    }

    /**
     * 指定批次列表退费
     *
     * @param addedRefundChargeItem 本次退费新增的chargeFormItem
     * @param canRefundBatchInfos   可退的批次列表
     * @param toRefundBatchInfos    指定退的批次列表
     * @param checkCanRefundCount   是否校验退费数量
     * @return
     */
    private List<ChargeFormItemBatchInfo> appointRefundBatchInfo(ChargeFormItem addedRefundChargeItem,
                                                                 List<ChargeFormItemBatchInfo> canRefundBatchInfos,
                                                                 List<ChargeFormItemBatchInfo> existedRefundBatchInfos,
                                                                 List<ChargeFormItemBatchInfo> toRefundBatchInfos,
                                                                 boolean checkCanRefundCount,
                                                                 String operatorId) {
        addedRefundChargeItem.setIsExpectedBatch(1);
        List<ChargeFormItemBatchInfo> addedRefundBatchInfos = new ArrayList<>();
        Map<String, ChargeFormItemBatchInfo> canRefundBatchInfoMap = ListUtils.toMap(canRefundBatchInfos, ChargeFormItemBatchInfo::getId);
        Map<String, List<ChargeFormItemBatchInfo>> refundBatchInfoIdMap = existedRefundBatchInfos.stream().filter(chargeFormItemBatchInfo -> org.apache.commons.lang3.StringUtils.isNotEmpty(chargeFormItemBatchInfo.getAssociateItemBatchInfoId()))
                .collect(Collectors.groupingBy(ChargeFormItemBatchInfo::getAssociateItemBatchInfoId));

        for (ChargeFormItemBatchInfo toRefundBatchInfo : toRefundBatchInfos) {
            ChargeFormItemBatchInfo canRefundBatchInfo = canRefundBatchInfoMap.get(toRefundBatchInfo.getId());
            if (canRefundBatchInfo == null) {
                sLogger.error("canRefundBatchInfo is null, batchId: {}", toRefundBatchInfo.getId());
                throw new ChargeServiceException(ChargeServiceError.CHARGE_FORM_ITEM_BATCH_INFO_NOT_FOUND.getCode(), String.format(ChargeServiceError.CHARGE_FORM_ITEM_BATCH_INFO_NOT_FOUND.getMessage(), toRefundBatchInfo.getBatchId()));
            }

            if (checkCanRefundCount && MathUtils.wrapBigDecimalCompare(canRefundBatchInfo.getCanRefundCount(), toRefundBatchInfo.getUnitCount()) < 0) {
                sLogger.error("canRefundBatchInfo.getCanRefundCount() <= toRefundBatchInfo.getUnitCount(), chargeFormItemBatchInfoId: {}, canRefundCount: {}, toRefundCount: {}", canRefundBatchInfo.getId(), canRefundBatchInfo.getCanRefundCount(), toRefundBatchInfo.getUnitCount());
                throw new ChargeServiceException(ChargeServiceError.CHARGE_FORM_ITEM_BATCH_INFO_REFUND_COUNT_NOT_ENOUGH.getCode(), String.format(ChargeServiceError.CHARGE_FORM_ITEM_BATCH_INFO_REFUND_COUNT_NOT_ENOUGH.getMessage(), canRefundBatchInfo.getCanRefundCount(), chargeFormItem.getUnit()));
            }

            List<ChargeFormItemBatchInfo> refundBatchInfos = refundBatchInfoIdMap.getOrDefault(canRefundBatchInfo.getId(), new ArrayList<>());

            ChargeFormItemBatchInfo addedRefundBatchInfo = ChargeFormItemBatchInfoUtils.generateRefundChargeFormItemBatchInfo(canRefundBatchInfo,
                    addedRefundChargeItem.getId(),
                    MathUtils.min(canRefundBatchInfo.getCanRefundCount(), toRefundBatchInfo.getUnitCount()),
                    refundBatchInfos,
                    chargeVersion,
                    operatorId);

            if (Objects.nonNull(addedRefundBatchInfo)) {
                addedRefundBatchInfos.add(addedRefundBatchInfo);
            }
        }

        return addedRefundBatchInfos;
    }

    /**
     * 默认匹配批次列表退费
     *
     * @param refundChargeItem
     * @param paidBatchInfos          收费的批次list
     * @param existedRefundBatchInfos 已存在的退费的批次list
     * @param needRefundCount
     * @return
     */
    private List<ChargeFormItemBatchInfo> defaultRefundBatchInfo(ChargeFormItem refundChargeItem,
                                                                 List<ChargeFormItemBatchInfo> paidBatchInfos,
                                                                 List<ChargeFormItemBatchInfo> existedRefundBatchInfos,
                                                                 BigDecimal needRefundCount,
                                                                 String operatorId) {
        List<ChargeFormItemBatchInfo> addedRefundBatchInfos = new ArrayList<>();
        if (MathUtils.wrapBigDecimalCompare(needRefundCount, BigDecimal.ZERO) <= 0) {
            return addedRefundBatchInfos;
        }
        // 获取对应发药信息 找出可以退的批次信息，药店是先退费再退药，和诊所管家先退药再退费流程不一样，所以这里不使用发药的可退批次
//        List<DispensingInfo.BatchInfo> batchInfoList = Optional.ofNullable(dispensingInfo).map(DispensingInfo::getBatchInfoList).orElse(new ArrayList<>());
        List<DispensingInfo.BatchInfo> batchInfoList = Lists.newArrayList();

        Map<String, DispensingInfo.BatchInfo> dispensingBatchInfoMap = ListUtils.toMap(batchInfoList, DispensingInfo.BatchInfo::getBatchId);
        Map<String, List<ChargeFormItemBatchInfo>> refundBatchInfoIdMap = existedRefundBatchInfos.stream().filter(chargeFormItemBatchInfo -> org.apache.commons.lang3.StringUtils.isNotEmpty(chargeFormItemBatchInfo.getAssociateItemBatchInfoId()))
                .collect(Collectors.groupingBy(ChargeFormItemBatchInfo::getAssociateItemBatchInfoId));

        for (ChargeFormItemBatchInfo canRefundBatchInfo : paidBatchInfos) {
            if (MathUtils.wrapBigDecimalCompare(needRefundCount, BigDecimal.ZERO) <= 0) {
                break;
            }
            DispensingInfo.BatchInfo dispensingBatchInfo = dispensingBatchInfoMap.get(canRefundBatchInfo.getBatchId());
            if (dispensingBatchInfo == null) {
                dispensingBatchInfo = new DispensingInfo.BatchInfo();
            }
            canRefundBatchInfo.setDispensedRefundUnitCount(dispensingBatchInfo.getRefundUnitCount());
            canRefundBatchInfo.setDispensedUnitCount(dispensingBatchInfo.getUnitCount());
            /**
             * 获取这个批次能退的数量
             */
            BigDecimal canRefundCount = canRefundBatchInfo.getCanRefundCount();

            if (MathUtils.wrapBigDecimalCompare(canRefundCount, BigDecimal.ZERO) <= 0) {
                continue;
            }

            List<ChargeFormItemBatchInfo> refundBatchInfos = refundBatchInfoIdMap.getOrDefault(canRefundBatchInfo.getId(), new ArrayList<>());

            ChargeFormItemBatchInfo addedRefundBatchInfo = ChargeFormItemBatchInfoUtils.generateRefundChargeFormItemBatchInfo(canRefundBatchInfo,
                    refundChargeItem.getId(),
                    MathUtils.min(canRefundCount, needRefundCount),
                    refundBatchInfos,
                    chargeVersion,
                    operatorId);

            if (Objects.nonNull(addedRefundBatchInfo)) {
                needRefundCount = MathUtils.wrapBigDecimalSubtract(needRefundCount, addedRefundBatchInfo.getUnitCount());
                addedRefundBatchInfos.add(addedRefundBatchInfo);
            }

        }
        return addedRefundBatchInfos;
    }

    private ChargeDiscountInfo getRefundPromotionInfo() {
        List<ChargeDiscountInfo> refundChargeTransactionRecordDiscountInfos = refundChargeFormItems.stream().map(ChargeFormItem::getPromotionInfo).filter(Objects::nonNull).collect(Collectors.toList());

        return ChargeTransactionRecordDiscountInfoHelper.mergeChargeTransactionRecordDiscountInfo(refundChargeTransactionRecordDiscountInfos);
    }


    public void renew() {
        if (isParent()) {
            composeChildren.forEach(PharmacyItemProcessor::renew);
        }

        chargeFormItem.setStatus(Constants.ChargeFormItemStatus.UNCHARGED);
        chargeFormItem.setV2Status(Constants.ChargeFormItemStatus.UNCHARGED);
        chargeFormItem.setPayStatus(ChargeFormItem.PayStatus.UNPAID);
        chargeFormItem.setPromotionInfoJson(null);
        chargeFormItem.setAdjustmentPrice(BigDecimal.ZERO);
        chargeFormItem.setPromotionPrice(BigDecimal.ZERO);
        chargeFormItem.setRefundTotalPrice(BigDecimal.ZERO);
        chargeFormItem.setRefundDiscountPrice(BigDecimal.ZERO);
        chargeFormItem.setRefundUnitCount(BigDecimal.ZERO);
        chargeFormItem.setExpectedTotalPrice(null);
        chargeFormItem.setExpectedUnitPrice(null);
        chargeFormItem.setExpectedTotalPriceRatio(null);
        chargeFormItem.setFractionPrice(null);
        chargeFormItem.setUnitPrice(chargeFormItem.getSourceUnitPrice());
        chargeFormItem.calculateDiscountedPrice();
        if (refundChargeFormItems != null) {
            refundChargeFormItems.forEach(refundChargeFormItem -> refundChargeFormItem.setIsDeleted(1));
        }
    }

    public void setGoodsItem(GoodsItem goodsItem, String operatorId) {
        if (goodsItem == null) {
            return;
        }
        this.goodsItem = goodsItem;

        if (Objects.nonNull(goodsItem.getShebao())) {
            GoodsSheBaoMatchedCodesInfo shebao = goodsItem.getShebao();
            updateSheBaoMatchCode(new SheBaoMatchCode(shebao.getNationalCode(), shebao.getPayMode()));
        }

        if (getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED) {
            if (goodsItem.getType() == Constants.ProductType.COMPOSE_PRODUCT) {
                chargeFormItem.setComposeType(ComposeType.COMPOSE);
                chargeFormItem.setFeeComposeType(GoodsConst.FeeComposeType.FEE_TYPE_COMPOSE_GOODS);
            }
            if (goodsItem.getFeeComposeType() == GoodsConst.FeeComposeType.FEE_TYPE_COMPOSE_FEE) {
                chargeFormItem.setGoodsFeeType(GoodsFeeType.FEE_PARENT);
            }
        }

        if (isParent()) {
            List<GoodsItem> subGoodsItems = getSubGoodsItemFromGoodsItem(goodsItem);

            if (getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED) {
                insertOrUpdateComposeChildren(chargeFormItem, composeChildren, subGoodsItems, operatorId);
            }

            Map<String, GoodsItem> subGoodsItemMap = Optional.ofNullable(subGoodsItems)
                    .orElse(new ArrayList<>())
                    .stream()
                    .collect(Collectors.toMap(GoodsItem::getId, Function.identity(), (a, b) -> a));

            composeChildren.forEach(itemProcessor -> {
                GoodsItem subGoodItem = subGoodsItemMap.getOrDefault(itemProcessor.getProductId(), null);
                if (subGoodItem != null && subGoodItem.getStatus() < GoodsConst.GoodsStatus.NO_VISIBLE) {

                    itemProcessor.setGoodsItem(subGoodItem, operatorId);
                    if (getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED) {
                        itemProcessor.setComposeParentUnitCount(chargeFormItem.getUnitCount());
                    }
                }
            });
        }
    }

    public List<GoodsItem> getSubGoodsItemFromGoodsItem(GoodsItem goodsItem) {

        if (Objects.isNull(goodsItem)) {
            return new ArrayList<>();
        }

        List<GoodsItem> subGoodsItems = new ArrayList<>();
        if (goodsItem.getType() == Constants.ProductType.COMPOSE_PRODUCT) {
            subGoodsItems = goodsItem.getChildren();
        } else if (goodsItem.getFeeComposeType() == GoodsConst.FeeComposeType.FEE_TYPE_COMPOSE_FEE) {
            subGoodsItems = goodsItem.getFeeComposeList();
        }
        return subGoodsItems;
    }

    private void insertOrUpdateComposeChildren(ChargeFormItem
                                                       parentItem, List<PharmacyItemProcessor> composeChildren, List<GoodsItem> subGoodsItems, String operatorId) {

        if (org.apache.commons.collections.CollectionUtils.isEmpty(subGoodsItems)) {
            composeChildren.clear();
            return;
        }

        //比较两个对象一致方法
        BiFunction<GoodsItem, PharmacyItemProcessor, Boolean> isEqualKeyFunc = (subGoodsItem, subItemProcessor) -> Objects.equals(subGoodsItem.getId(), subItemProcessor.getProductId());

        //新增itemProcessor
        Function<GoodsItem, PharmacyItemProcessor> insertFunc = subGoodsItem -> {
            ChargeFormItem subChargeFormItem = DTOConverter.generateComposeSubFormItem(parentItem, subGoodsItem, operatorId);
            return new PharmacyItemProcessor(chargeVersion, subChargeFormItem, null, toDeleteChargeSheetRelationDto);
        };

        //删除itemProcessor
        Function<PharmacyItemProcessor, Boolean> deleteFunc = itemProcessor -> {
            List<ChargeFormItem> children = itemProcessor.getAllChargeFormItems();
            children.forEach(child -> {
                child.deleteModel(operatorId);
                toDeleteChargeSheetRelationDto.addChargeFormItemAndRelationData(child, isDeleted -> Objects.equals(isDeleted, 1));
            });

            return true;
        };

        //更新itemProcessor
        BiConsumer<GoodsItem, PharmacyItemProcessor> updateFunc = (subGoodsItem, subItemProcessor) -> {
            int isDiscounting = subGoodsItem.getComposeUseDismounting();

            ChargeFormItem subItem = subItemProcessor.getChargeFormItem();
            subItem.setUseDismounting(isDiscounting)
                    .setPharmacyType(subGoodsItem.getPharmacyType())
                    .setPharmacyNo(subGoodsItem.getPharmacyNo())
                    .setName(subGoodsItem.getName())
                    .setDoseCount(parentItem.getDoseCount())
                    .setFeeComposeType(subGoodsItem.getFeeComposeType())
                    .setFeeTypeId(subGoodsItem.getFeeTypeId());
            DTOConverter.appendShebaoPayType(parentItem, subItem, subGoodsItem);
            DTOConverter.appendComposeSubFormItemPrice(parentItem, subGoodsItem, subItem);
            cn.abcyun.cis.commons.util.FillUtils.fillLastModifiedBy(subItem, operatorId);
        };

        MergeTool.doMerge(subGoodsItems,
                composeChildren,
                isEqualKeyFunc,
                insertFunc,
                deleteFunc,
                updateFunc);

        composeChildren.sort(Comparator.comparing(PharmacyItemProcessor::getSort));
    }

    private boolean composeChildrenIsAllEqual
            (List<PharmacyItemProcessor> composeChildren, List<LimitPriceGoodsItem> limitPriceChildren) {

        if (composeChildren == null) {
            composeChildren = new ArrayList<>();
        }

        if (limitPriceChildren == null) {
            limitPriceChildren = new ArrayList<>();
        }

        List<String> composeChildIds = composeChildren.stream().filter(itemProcessor -> !StringUtils.isEmpty(itemProcessor.getProductId())).map(PharmacyItemProcessor::getProductId).collect(Collectors.toList());

        List<String> goodsItemChildIds = limitPriceChildren.stream().filter(goodsItemChildren -> !StringUtils.isEmpty(goodsItemChildren.getId())).map(LimitPriceGoodsItem::getId).collect(Collectors.toList());

        return ListUtils.isListEqual(composeChildIds, goodsItemChildIds);
    }

    public String getChargeFormId() {
        return chargeFormItem.getChargeFormId();
    }

    public void setUnitCount(BigDecimal unitCount) {
        chargeFormItem.setUnitCount(unitCount);
    }

    public void setComposeParentUnitCount(BigDecimal composeParentUnitCount) {
        chargeFormItem.setComposeParentUnitCount(composeParentUnitCount);
    }

    public void setDispensingInfo(Map<String, DispensingInfo> dispensingInfoMap) {
        if (getComposeType() == ComposeType.COMPOSE) {
            composeChildren.forEach(itemProcessor -> itemProcessor.setDispensingInfo(dispensingInfoMap));
        }
        this.dispensingInfo = dispensingInfoMap.getOrDefault(getItemId(), null);
    }

    public String getItemId() {
        return chargeFormItem.getId();
    }

    public String getComposeParentFormItemId() {
        return chargeFormItem.getComposeParentFormItemId();
    }

    /**
     * 超限价不转自费，就是超过部分不受
     * 无需折扣
     */
    public boolean isLimitNoPay() {
        if (getComposeType() == ComposeType.COMPOSE) {
            return composeChildren.stream().anyMatch(PharmacyItemProcessor::isLimitNoPay);
        }

        if (calIsUseLimitPrice == 0) {
            return false;
        }

        Integer exceedLimitPriceRule = Optional.ofNullable(chargeFormItem.getAdditional())
                .map(ChargeFormItemAdditional::getLimitInfo)
                .map(BaseLimitPriceInfo.LimitInfo::getExceedLimitPriceRule)
                .orElse(null);

        //如果没有批次。直接看item的限价是否是限价转自费，为空时，默认为限价转自费
        if (org.apache.commons.collections.CollectionUtils.isEmpty(itemBatchInfoProcessors)) {
            exceedLimitPriceRule = exceedLimitPriceRule == null ? Constants.ExceedLimitPriceRule.NO_PAY : exceedLimitPriceRule;
            return exceedLimitPriceRule == Constants.ExceedLimitPriceRule.NO_PAY;
        }

        //如果有批次。item的配置有，exceedLimitPriceRule有值直接判断，如果为空，判断批次是否包含限价转自费
        if (exceedLimitPriceRule != null) {
            return exceedLimitPriceRule == Constants.ExceedLimitPriceRule.NO_PAY;
        }

        return itemBatchInfoProcessors.stream()
                .anyMatch(ItemBatchInfoProcessor::isLimitNoPay);
    }

    public int getIsGift() {
        return chargeFormItem.getIsGift();
    }

    public int getItemV2Status() {
        return chargeFormItem.getV2Status();
    }

    public String getProductId() {
        return TextUtils.alwaysString(chargeFormItem.getProductId());
    }

    public int getPharmacyNo() {
        return chargeFormItem.getPharmacyNo();
    }

    public int getUseDismounting() {
        return chargeFormItem.getUseDismounting();
    }

    public String getName() {
        return chargeFormItem.getName();
    }

    public void setPromotion(List<PromotionSimple> promotions, BigDecimal promotionPrice) {
        if (getItemStatus() != Constants.ChargeFormItemStatus.UNCHARGED) {
            return;
        }
        ChargeDiscountInfo promotionInfo = null;
        //把promotions分成普通折扣、抵扣、优惠券、满减满赠
        if (!CollectionUtils.isEmpty(promotions)) {
            promotionInfo = new ChargeDiscountInfo();
            PromotionSimpleGroup promotionGroup = new PromotionSimpleGroup(promotions);
            promotionInfo.addDiscountPromotionInfos(promotionGroup.convertToDiscountPromotionInfos())
                    .addDiscountPromotionInfos(promotionGroup.convertToPointDeductPromotionInfos())
                    .addCouponInfos(promotionGroup.convertToCouponInfos())
                    .addGiftRulePromotionInfos(promotionGroup.convertToGiftRulePromotionInfos())
                    .addDeductDiscountInfos(promotionGroup.convertToDeductDiscountInfos(chargeFormItem.getUnitPrice()));
        }
        setPromotionCore(promotionInfo, promotionPrice);
        /**
         * 这里要把母项的折扣平摊到每个子项上，用递归的方式实现，目前最多有3层，套餐母项 -> 套餐子项同时又是费用母项 -> 费用子项
         */
        flatPromotionToChildren(promotionInfo, getTotalPrice(false), promotionPrice);
    }


    private void flatPromotionToChildren(ChargeDiscountInfo promotionInfo, BigDecimal totalPrice, BigDecimal
            promotionPrice) {
        if (isParent()) {
            final BigDecimal[] childrenExcludeDeductPromotionPrice = {BigDecimal.ZERO};
            final BigDecimal[] childrenDeductPromotionPrice = {BigDecimal.ZERO};
            //这里处理了折扣的所有内容，抵扣会存在零头没摊下去的情况，需要在后面处理
            composeChildren.forEach(itemProcessor -> {
                Pair<BigDecimal, BigDecimal> pair = itemProcessor.setComposeChildrenPromotion(promotionInfo, totalPrice);
                BigDecimal excludeDeductPromotionPrice = pair.getLeft();
                childrenDeductPromotionPrice[0] = MathUtils.wrapBigDecimalAdd(childrenDeductPromotionPrice[0], pair.getRight());
                childrenExcludeDeductPromotionPrice[0] = MathUtils.wrapBigDecimalAdd(childrenExcludeDeductPromotionPrice[0], excludeDeductPromotionPrice);
            });

            //这里不直接取promotionInfo里面的抵扣金额是因为母项本身存在零头，这个零头无法摊到子项上，所以这个算法不包含母项本身的零头
            BigDecimal parentDeductPromotionPrice = Optional.ofNullable(promotionInfo).map(ChargeDiscountInfo::getDeductPromotionPrice).orElse(BigDecimal.ZERO);
            //这里处理了抵扣的零头，抵扣的零头需要摊到子项上，去找能处理零头的子项
            BigDecimal leftParentDeductFractionPrice = MathUtils.wrapBigDecimalSubtract(parentDeductPromotionPrice, childrenDeductPromotionPrice[0]);
            if (leftParentDeductFractionPrice.compareTo(BigDecimal.ZERO) < 0) {
                //表示还有抵扣零头没处理干净
                //把所有子项已经摊下去的抵扣金额加起来与母项比较
                Map<String, BigDecimal> childPromotionIdDeductPriceMap = composeChildren.stream()
                        .flatMap(itemProcessor -> Optional.ofNullable(itemProcessor.getChargeFormItem().getPromotionInfo())
                                .map(ChargeDiscountInfo::getDeductDiscountInfos)
                                .orElse(new ArrayList<>()).stream())
                        .collect(Collectors.toMap(ChargeDiscountInfo.DeductDiscountInfo::getId, ChargeDiscountInfo.PromotionInfo::getDiscountPrice, MathUtils::wrapBigDecimalAdd));

                Map<String, BigDecimal> parentPromotionIdDeductPriceMap = Optional.ofNullable(promotionInfo).map(ChargeDiscountInfo::getDeductDiscountInfos)
                        .orElse(new ArrayList<>())
                        .stream()
                        .collect(Collectors.toMap(ChargeDiscountInfo.DeductDiscountInfo::getId, deductDiscountInfo -> {
                            BigDecimal parentDeductPrice = deductDiscountInfo.getDiscountPrice();

                            BigDecimal childTotalDeductPrice = childPromotionIdDeductPriceMap.getOrDefault(deductDiscountInfo.getId(), parentDeductPrice);

                            return parentDeductPrice.subtract(childTotalDeductPrice);
                        }));

                composeChildren.forEach(childItemProcessor -> childItemProcessor.updateDeductFractionPromotionPrice(parentPromotionIdDeductPriceMap, leftParentDeductFractionPrice));
            }

            if (leftParentDeductFractionPrice.compareTo(BigDecimal.ZERO) < 0) {
                sLogger.error("母项的抵扣金额平摊到子项失败，chargeFormItemId:{}, leftParentDeductFractionPrice: {}", chargeFormItem.getId(), leftParentDeductFractionPrice);
            }

            //校验摊费是否全部摊完，要排除掉抵扣的金额
            BigDecimal parentExcludeDeductPromotionPrice = promotionPrice.subtract(parentDeductPromotionPrice);

            if (MathUtils.wrapBigDecimalCompare(childrenExcludeDeductPromotionPrice[0], parentExcludeDeductPromotionPrice) != 0) {
                sLogger.info("子项折扣平摊失败，childrenExcludeDeductPromotionPrice: {}, parentExcludeDeductPromotionPrice: {}", childrenExcludeDeductPromotionPrice[0], parentExcludeDeductPromotionPrice);
                throw new IllegalStateException("套餐子项折扣平摊失败");
            }
        }


    }

    private void updateDeductFractionPromotionPrice
            (Map<String, BigDecimal> parentPromotionIdDeductPriceMap, BigDecimal leftParentDeductFractionPrice) {

        if (MapUtils.isEmpty(parentPromotionIdDeductPriceMap)) {
            return;
        }

        if (MathUtils.wrapBigDecimalCompare(leftParentDeductFractionPrice, BigDecimal.ZERO) >= 0) {
            return;
        }

        List<ChargeDiscountInfo.DeductDiscountInfo> deductDiscountInfos = Optional.ofNullable(chargeFormItem.getPromotionInfo())
                .map(ChargeDiscountInfo::getDeductDiscountInfos)
                .orElse(new ArrayList<>());

        if (org.apache.commons.collections.CollectionUtils.isEmpty(deductDiscountInfos)) {
            return;
        }

        BigDecimal discountedPrice = chargeFormItem.calculateDiscountedPrice();

        if (MathUtils.wrapBigDecimalCompare(discountedPrice, BigDecimal.ZERO) <= 0) {
            return;
        }

        AtomicReference<BigDecimal> currentDeductFractionPrice = new AtomicReference<>(BigDecimal.ZERO);
        deductDiscountInfos.forEach(child -> {
            BigDecimal deductLeftFractionPrice = parentPromotionIdDeductPriceMap.get(child.getId());

            if (MathUtils.wrapBigDecimalCompare(deductLeftFractionPrice, BigDecimal.ZERO) >= 0) {
                return;
            }

            BigDecimal currentFractionPrice = MathUtils.min(discountedPrice.add(currentDeductFractionPrice.get()), deductLeftFractionPrice.abs()).negate();

            if (currentFractionPrice.compareTo(BigDecimal.ZERO) >= 0) {
                return;
            }

            child.setDiscountPrice(MathUtils.wrapBigDecimalAdd(child.getDiscountPrice(), currentFractionPrice));
            currentDeductFractionPrice.set(MathUtils.wrapBigDecimalAdd(currentDeductFractionPrice.get(), currentFractionPrice));
            parentPromotionIdDeductPriceMap.put(child.getId(), MathUtils.wrapBigDecimalSubtract(deductLeftFractionPrice, currentFractionPrice));
        });

        chargeFormItem.setPromotionInfoJson(JsonUtils.dump(chargeFormItem.getPromotionInfo()));
        chargeFormItem.setPromotionPrice(MathUtils.wrapBigDecimalAdd(chargeFormItem.getPromotionPrice(), currentDeductFractionPrice.get()));
        chargeFormItem.calculateDiscountedPrice();
    }

    private void setPromotionCore(ChargeDiscountInfo itemPromotionInfo, BigDecimal promotionPrice) {
        chargeFormItem.setPromotionPrice(promotionPrice);
        receivableFee = chargeFormItem.calculateDiscountedPriceV2();
        if (Objects.nonNull(itemPromotionInfo)) {
            chargeFormItem.setPromotionInfo(itemPromotionInfo);
            chargeFormItem.setDeductTotalCount(itemPromotionInfo.getDeductTotalCount());
            chargeFormItem.setPromotionInfoJson(JsonUtils.dump(itemPromotionInfo));
        } else {
            chargeFormItem.setDeductTotalCount(BigDecimal.ZERO);
            chargeFormItem.setVerifyTotalCount(BigDecimal.ZERO);
            chargeFormItem.setPromotionInfo(null);
            chargeFormItem.setPromotionInfoJson("");
        }

        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(itemBatchInfoProcessors)) {
            ChargeFormItemBatchInfoUtils.flatSinglePromotionToBatchInfo(itemBatchInfoProcessors, itemPromotionInfo, chargeFormItem.getSourceTotalPrice());
            ChargeFormItemBatchInfoUtils.flatUnitAdjustmentFeeByProcessor(itemBatchInfoProcessors, chargeFormItem.getUnitAdjustmentFee());
            ChargeFormItemBatchInfoUtils.flatPackagePromotionToBatchInfo(itemBatchInfoProcessors, itemPromotionInfo, chargeFormItem.getSourceTotalPrice()
                    .add(chargeFormItem.calculateSinglePromotionPrice())
                    .add(MathUtils.wrapBigDecimalOrZero(chargeFormItem.getUnitAdjustmentFee()))
            );


            BigDecimal batchTotalPromotionPrice = itemBatchInfoProcessors.stream()
                    .map(itemBatchInfoProcessor -> {
                        ChargeDiscountInfo promotionInfo = itemBatchInfoProcessor.getChargeFormItemBatchInfo().getPromotionInfo();
                        if (promotionInfo == null) {
                            return BigDecimal.ZERO;
                        }
                        return promotionInfo.getExcludeDeductPromotionPrice();
                    })
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            if (MathUtils.wrapBigDecimalCompare(promotionPrice, batchTotalPromotionPrice) != 0) {
                sLogger.info("批次折扣平摊失败，promotionPrice: {}, childrenPromotionPrice: {}", promotionPrice, batchTotalPromotionPrice);
                throw new IllegalStateException("批次折扣平摊失败");
            }
        }

    }

    private Pair<BigDecimal, BigDecimal> setComposeChildrenPromotion(ChargeDiscountInfo
                                                                             parentPromotionInfo, BigDecimal parentTotalPrice) {

        if (Objects.isNull(parentPromotionInfo)) {
            setPromotionCore(null, BigDecimal.ZERO);
            flatPromotionToChildren(null, getTotalPrice(false), BigDecimal.ZERO);
            return Pair.of(BigDecimal.ZERO, BigDecimal.ZERO);
        }

        BiConsumer<ChargeDiscountInfo.PromotionInfo, ChargeDiscountInfo.PromotionInfo> flatChildDiscountPriceConsumer = (parent, child) -> {
            BigDecimal childTotalPrice = getTotalPrice(false);
            BigDecimal childDiscountPrice = BigDecimal.ZERO;
            if (MathUtils.wrapBigDecimalCompare(parentTotalPrice, BigDecimal.ZERO) <= 0) {
                childDiscountPrice = childTotalPrice;
            } else {
                childDiscountPrice = parent.getDiscountPrice().abs().multiply(childTotalPrice).divide(parentTotalPrice, 2, RoundingMode.UP);
            }
            //保障不能超过子项的总金额
            childDiscountPrice = MathUtils.min(childTotalPrice, childDiscountPrice);
            //保障不能超过折扣的剩余金额
            childDiscountPrice = MathUtils.min(childDiscountPrice, MathUtils.wrapBigDecimalSubtract(parent.getDiscountPrice(), parent.getExpendDiscountPrice()).abs()).negate();

            child.setDiscountPrice(childDiscountPrice);
            //单个具体折扣已经使用的折扣金额
            parent.setExpendDiscountPrice(MathUtils.wrapBigDecimalAdd(parent.getExpendDiscountPrice(), childDiscountPrice));
        };

        ChargeDiscountInfo childItemPromotionInfo = new ChargeDiscountInfo();
        childItemPromotionInfo
                .setDiscountPromotionInfos(parentPromotionInfo.getDiscountPromotionInfos()
                        .stream()
                        .map(parent -> {
                            ChargeDiscountInfo.PromotionInfo child = new ChargeDiscountInfo.PromotionInfo();
                            BeanUtils.copyProperties(parent, child, "expendDiscountPrice");
                            flatChildDiscountPriceConsumer.accept(parent, child);
                            return child;
                        }).collect(Collectors.toList()))
                .setGiftRulePromotionInfos(parentPromotionInfo.getGiftRulePromotionInfos()
                        .stream()
                        .map(parent -> {
                            ChargeDiscountInfo.PromotionInfo child = new ChargeDiscountInfo.PromotionInfo();
                            BeanUtils.copyProperties(parent, child, "expendDiscountPrice");
                            flatChildDiscountPriceConsumer.accept(parent, child);
                            return child;
                        }).collect(Collectors.toList()))
                .setCouponInfos(parentPromotionInfo.getCouponInfos()
                        .stream()
                        .map(parent -> {
                            ChargeDiscountInfo.CouponInfo child = new ChargeDiscountInfo.CouponInfo();
                            BeanUtils.copyProperties(parent, child, "expendDiscountPrice");
                            flatChildDiscountPriceConsumer.accept(parent, child);
                            return child;
                        }).collect(Collectors.toList()))
                .setDeductDiscountInfos(parentPromotionInfo.getDeductDiscountInfos()
                        .stream()
                        .map(parent -> {
                            ChargeDiscountInfo.DeductDiscountInfo child = new ChargeDiscountInfo.DeductDiscountInfo();
                            BeanUtils.copyProperties(parent, child, "expendDiscountPrice");
                            //子项在单个套餐中的数量
                            BigDecimal singleComposeChildUnitCount = Objects.isNull(goodsItem) ? BigDecimal.ONE : chargeFormItem.getUseDismounting() == 0 ? goodsItem.getComposePackageCount() : goodsItem.getComposePieceCount();
                            singleComposeChildUnitCount = MathUtils.wrapBigDecimalCompare(singleComposeChildUnitCount, BigDecimal.ONE) < 0 ? BigDecimal.ONE : singleComposeChildUnitCount;
                            BigDecimal deductTotalCount = singleComposeChildUnitCount.multiply(child.getDeductedCount());
                            child.setDeductedCount(deductTotalCount);
                            child.setUnitPrice(chargeFormItem.getUnitPrice());
                            child.setDiscountPrice(MathUtils.calculateTotalPrice(child.getDeductedCount(), child.getUnitPrice()).negate());
                            return child;
                        }).collect(Collectors.toList())
                );
        BigDecimal excludeDeductPromotionPrice = childItemPromotionInfo.getExcludeDeductPromotionPrice();
        BigDecimal deductPromotionPrice = childItemPromotionInfo.getDeductPromotionPrice();
        BigDecimal promotionPrice = excludeDeductPromotionPrice.add(deductPromotionPrice);

        setPromotionCore(childItemPromotionInfo, promotionPrice);

        flatPromotionToChildren(childItemPromotionInfo, getTotalPrice(false), promotionPrice);
        return Pair.of(excludeDeductPromotionPrice, deductPromotionPrice);
    }

    public void addPatientPointPromotionPrice(BigDecimal promotionPrice) {

        if (getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED) {
            chargeFormItem.setPromotionPrice(MathUtils.wrapBigDecimalAdd(chargeFormItem.getPromotionPrice(), promotionPrice));
            ChargeDiscountInfo promotionInfo = Optional.ofNullable(chargeFormItem.getPromotionInfo()).orElse(new ChargeDiscountInfo());
            promotionInfo.setPatientPointPromotionFee(promotionPrice);
            chargeFormItem.setPromotionInfo(promotionInfo);
            chargeFormItem.setPromotionInfoJson(JsonUtils.dump(promotionInfo));

            ChargeFormItemBatchInfoUtils.flatPatientPointPromotionPrice(itemBatchInfoProcessors, promotionPrice);

            receivableFee = chargeFormItem.calculateDiscountedPriceV2();

            if (isParent()) {
                List<FlatPriceHelper.FlatPriceCell> flatPriceCells = composeChildren.stream().map(itemProcessor -> {
                    FlatPriceHelper.FlatPriceCell flatPriceCell = new FlatPriceHelper.FlatPriceCell();
                    flatPriceCell.setId(itemProcessor.getItemId());
                    flatPriceCell.setName(itemProcessor.getName());
                    flatPriceCell.setPriority(FlatPriceHelper.PRIORITY_HIGH);
                    flatPriceCell.setTotalPrice(itemProcessor.getReceivableFee());
                    return flatPriceCell;
                }).collect(Collectors.toList());

                FlatPriceHelper flatPriceHelper = new FlatPriceHelper(promotionPrice);
                flatPriceHelper.flat(flatPriceCells);
                Map<String, FlatPriceHelper.FlatPriceCell> flatPriceCellMap = flatPriceCells.stream().collect(Collectors.toMap(FlatPriceHelper.FlatPriceCell::getId, Function.identity(), (a, b) -> a));
                composeChildren.forEach(itemProcessor -> {
                    FlatPriceHelper.FlatPriceCell flatPriceCell = flatPriceCellMap.getOrDefault(itemProcessor.getItemId(), null);
                    if (flatPriceCell != null) {
                        itemProcessor.addPatientPointPromotionPrice(flatPriceCell.getFlatPrice());
                    }
                });

            }
        }
    }

    public BigDecimal calculateReceivableFee() {
        return chargeFormItem.calculateDiscountedPriceV2();
    }

    public void setAdjustmentPrice(BigDecimal adjustmentPrice) {
        if (getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED) {
            chargeFormItem.setAdjustmentPrice(adjustmentPrice);
            receivableFee = chargeFormItem.calculateDiscountedPriceV2();
            chargeFormItem.setReceivablePrice(receivableFee);

            //追加校验批次的应收是否和item的应收一致，如果不一致，直接报错
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(itemBatchInfoProcessors)) {
                ChargeFormItemBatchInfoUtils.flatAdjustmentFee(itemBatchInfoProcessors, adjustmentPrice);
                BigDecimal batchTotalReceivableFee = itemBatchInfoProcessors.stream()
                        .map(itemBatchInfoProcessor -> itemBatchInfoProcessor.getChargeFormItemBatchInfo().getReceivablePrice())
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                if (MathUtils.wrapBigDecimalCompare(receivableFee, batchTotalReceivableFee) != 0) {
                    sLogger.error("批次应收和item应收不一致，chargeFormItemId:{}, receivableFee: {}, batchTotalReceivableFee: {}", chargeFormItem.getId(), receivableFee, batchTotalReceivableFee);
                    throw new ChargeServiceException(ChargeServiceError.CHARGE_FEE_CHANGED);
                }
            }

            ChargeDiscountInfo promotionInfo = chargeFormItem.getPromotionInfo();
            if (promotionInfo == null) {
                promotionInfo = new ChargeDiscountInfo();
            }
            promotionInfo.setAdjustmentFee(adjustmentPrice);
            chargeFormItem.setPromotionInfo(promotionInfo);
            chargeFormItem.setPromotionInfoJson(JsonUtils.dump(promotionInfo));

            if (isParent() && org.apache.commons.collections.CollectionUtils.isNotEmpty(composeChildren)) {
                List<SmartFlatPriceHelper.SmartFlatPriceCell> flatPriceCells = composeChildren.stream().map(itemProcessor -> new SmartFlatPriceHelper.SmartFlatPriceCell(itemProcessor.getItemId(), itemProcessor.getName(),
                        itemProcessor.calculateReceivableFee(), itemProcessor.getProductType())).collect(Collectors.toList());

                SmartFlatPriceHelper.flat(adjustmentPrice, flatPriceCells);
                Map<String, SmartFlatPriceHelper.SmartFlatPriceCell> flatPriceCellMap = flatPriceCells.stream().collect(Collectors.toMap(SmartFlatPriceHelper.SmartFlatPriceCell::getId, Function.identity(), (a, b) -> a));
                composeChildren.forEach(itemProcessor -> {
                    SmartFlatPriceHelper.SmartFlatPriceCell flatPriceCell = flatPriceCellMap.getOrDefault(itemProcessor.getItemId(), null);
                    if (flatPriceCell != null) {
                        itemProcessor.setAdjustmentPrice(flatPriceCell.getFlatPrice());
                    }
                });
            }
        }
    }

    public void updateReceivableFee(BigDecimal flatPrice) {
        if (getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED) {
            receivableFee = chargeFormItem.calculateDiscountedPriceV2().add(MathUtils.wrapBigDecimalOrZero(flatPrice));

            updateItemBatchInfoReceivableFee(flatPrice);

            if (isParent()) {
                List<SmartFlatPriceHelper.SmartFlatPriceCell> flatPriceCells = composeChildren.stream().map(itemProcessor -> new SmartFlatPriceHelper.SmartFlatPriceCell(itemProcessor.getItemId(), itemProcessor.getName(),
                        itemProcessor.getReceivableFee(), itemProcessor.getProductType())).collect(Collectors.toList());

                SmartFlatPriceHelper.flat(flatPrice, flatPriceCells);
                Map<String, SmartFlatPriceHelper.SmartFlatPriceCell> flatPriceCellMap = flatPriceCells.stream().collect(Collectors.toMap(SmartFlatPriceHelper.SmartFlatPriceCell::getId, Function.identity(), (a, b) -> a));
                composeChildren.forEach(itemProcessor -> {
                    SmartFlatPriceHelper.SmartFlatPriceCell flatPriceCell = flatPriceCellMap.getOrDefault(itemProcessor.getItemId(), null);
                    if (flatPriceCell != null) {
                        itemProcessor.updateReceivableFee(flatPriceCell.getFlatPrice());
                    }
                });
            }

            if (calIsUseLimitPrice != 1) {
                sheBaoReceivableFee = calculateShebaoReceivableTotalPrice(null);
                updateItemBatchInfoSheBaoReceivableFee(sheBaoReceivableFee);
            }
        }
    }

    private void updateItemBatchInfoSheBaoReceivableFee(BigDecimal sheBaoReceivableFee) {

        if (org.apache.commons.collections.CollectionUtils.isEmpty(itemBatchInfoProcessors)) {
            return;
        }

        FlatPriceTool.flatPriceAndApply(sheBaoReceivableFee, itemBatchInfoProcessors.stream()
                .map(itemBatchInfoProcessor -> new FlatPriceTool.IFlatCell<FlatPriceHelper.FlatPriceCell>() {

                    @Override
                    protected FlatPriceHelper.FlatPriceCell genFlatCell() {
                        ChargeFormItemBatchInfo itemBatchInfo = itemBatchInfoProcessor.getChargeFormItemBatchInfo();
                        return new FlatPriceHelper.FlatPriceCell()
                                .setId(itemBatchInfo.getId())
                                .setName("平摊社保应收")
                                .setTotalPrice(itemBatchInfo.calculateReceivableFee())
                                .setPriority(FlatPriceHelper.PRIORITY_HIGH);
                    }

                    @Override
                    protected void apply(BigDecimal sheBaoReceivableFee) {
                        itemBatchInfoProcessor.updateSheBaoReceivablePrice(sheBaoReceivableFee);
                    }
                }).collect(Collectors.toList())
        );
    }

    private void updateItemBatchInfoReceivableFee(BigDecimal receivableFee) {

        if (org.apache.commons.collections.CollectionUtils.isEmpty(itemBatchInfoProcessors)) {
            return;
        }

        FlatPriceTool.flatPriceAndApply(receivableFee, itemBatchInfoProcessors.stream()
                .map(itemBatchInfoProcessor -> new FlatPriceTool.IFlatCell<FlatPriceHelper.FlatPriceCell>() {

                    @Override
                    protected FlatPriceHelper.FlatPriceCell genFlatCell() {
                        ChargeFormItemBatchInfo itemBatchInfo = itemBatchInfoProcessor.getChargeFormItemBatchInfo();
                        return new FlatPriceHelper.FlatPriceCell()
                                .setId(itemBatchInfo.getId())
                                .setName("平摊批次应收")
                                .setTotalPrice(itemBatchInfo.calculateReceivableFee())
                                .setPriority(FlatPriceHelper.PRIORITY_HIGH);
                    }

                    @Override
                    protected void apply(BigDecimal flatPrice) {
                        itemBatchInfoProcessor.updateReceivableFee(flatPrice);
                    }
                }).collect(Collectors.toList())
        );
    }

    public int getPaySource() {
        return chargeFormItem.getPaySource();
    }

    public int getProductType() {
        return chargeFormItem.getProductType();
    }

    public String getGoodsCMSpec() {
        return goodsItem != null ? goodsItem.getCMSpec() : null;
    }

    public int getProductSubType() {
        return chargeFormItem.getProductSubType();
    }

    public BigDecimal getUnitCount() {
        return unitCount;
    }

    public BigDecimal getRefundUnitCount() {
        return refundUnitCount;
    }

    public int getRefundType() {
        return refundType;
    }

    public BigDecimal getRefundDoseCount() {
        return refundDoseCount;
    }

    public BigDecimal getDoseCount() {
        return doseCount;
    }

    public int getSort() {
        return chargeFormItem.getSort();
    }

    public int getComposeType() {
        return chargeFormItem.getComposeType();
    }

    public boolean isChild() {
        return chargeFormItem.isChildItem();
    }

    public int getGoodsFeeType() {
        return chargeFormItem.getGoodsFeeType();
    }

    public GoodsItem getGoodsItem() {
        return goodsItem;
    }


    /**
     * 商品的价格，不算折扣的情况下
     * 根据自己的状态，在不通的场景下价格不同
     *
     * @param isRefundScene
     * @return
     */
    public BigDecimal getTotalPrice(boolean isRefundScene) {
        return getItemCalculator().getTotalPrice(isRefundScene);
    }

    public BigDecimal getSourceTotalPrice(boolean isRefundScene) {
        return doCalculateEachFeeFuncCore(isRefundScene, ChargeFormItem::getSourceTotalPrice);
    }

    public BigDecimal getGiftSourceTotalPrice(boolean isRefundScene) {

        if (chargeFormItem.getIsGift() != Constants.ChargeFormItemGiftType.PROMOTION_GIFT
                && chargeFormItem.getIsGift() != Constants.ChargeFormItemGiftType.MARKED_GIFT
        ) {
            return BigDecimal.ZERO;
        }

        return getSourceTotalPrice(isRefundScene);
    }


    public BigDecimal getUnitAdjustmentFee(boolean isRefundScene) {
        return doCalculateEachFeeFuncCore(isRefundScene, ChargeFormItem::getUnitAdjustmentFee);
    }

    public BigDecimal getTotalCostPrice() {
        return MathUtils.wrapBigDecimalOrZero(chargeFormItem.getTotalCostPrice());
    }

    /**
     * 商品折扣，负数
     *
     * @param isRefundScene
     * @return
     */
    public BigDecimal getDiscountPrice(boolean isRefundScene) {
        return doCalculateEachFeeFuncCore(isRefundScene, ChargeFormItem::getDiscountPrice);
    }

    public BigDecimal getPromotionPrice(boolean isRefundScene) {
        return doCalculateEachFeeFuncCore(isRefundScene, ChargeFormItem::getPromotionPrice);
    }

    public BigDecimal getSinglePromotionFee(boolean isRefundScene) {
        return doCalculateEachFeeFuncCore(isRefundScene, ChargeFormItem::calculateSinglePromotionPrice);
    }

    public BigDecimal getPackagePromotionFee(boolean isRefundScene) {
        return doCalculateEachFeeFuncCore(isRefundScene, ChargeFormItem::calculatePackagePromotionPrice);
    }

    public BigDecimal getRefundSourceTotalFee() {
        return doCalculateEachRefundFeeFuncCore(ChargeFormItem::getSourceTotalPrice);
    }

    public BigDecimal getRefundUnitAdjustmentFee() {
        return doCalculateEachRefundFeeFuncCore(ChargeFormItem::getUnitAdjustmentFee);
    }

    public BigDecimal getRefundSinglePromotionFee() {
        return doCalculateEachRefundFeeFuncCore(ChargeFormItem::calculateSinglePromotionPrice);
    }

    public BigDecimal getRefundPackagePromotionFee() {
        return doCalculateEachRefundFeeFuncCore(ChargeFormItem::calculatePackagePromotionPrice);
    }

    public BigDecimal getAdjustmentPrice() {
        BigDecimal adjustmentPrice = BigDecimal.ZERO;

        if (getItemStatus() == Constants.ChargeFormItemStatus.UNSELECTED) {
            return adjustmentPrice;
        }

        if (isParent()) {
            return composeChildren.stream().map(PharmacyItemProcessor::getAdjustmentPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        return MathUtils.wrapBigDecimalOrZero(chargeFormItem.getAdjustmentPrice());
    }

    //todo 诊所管家使用时需要重写
    public BigDecimal getRefundTotalPriceView() {
        if (isParent()) {
            return composeChildren.stream().map(PharmacyItemProcessor::getRefundTotalPriceView).reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        BigDecimal totalPrice = BigDecimal.ZERO;
        if (getItemStatus() != Constants.ChargeFormItemStatus.CHARGED) {
            return totalPrice;
        }

        totalPrice = refundChargeFormItems.stream().map(ChargeFormItemUtils::calculateTotalPriceView).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        return totalPrice;
    }

    public BigDecimal getRefundDiscountPrice() {
        return doCalculateEachRefundFeeFuncCore(ChargeFormItem::getDiscountPrice);
    }

    public BigDecimal getRefundPromotionPrice() {
        return doCalculateEachRefundFeeFuncCore(ChargeFormItem::getPromotionPrice);
    }

    public BigDecimal getRefundAdjustmentFee() {
        return doCalculateEachRefundFeeFuncCore(ChargeFormItem::getAdjustmentPrice);
    }

    public BigDecimal getSheBaoReceivableFee() {
        if (getComposeType() == ComposeType.COMPOSE) {
            return composeChildren.stream().map(PharmacyItemProcessor::getSheBaoReceivableFee).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        return this.sheBaoReceivableFee;
    }

    public BigDecimal getRefundAdjustmentPrice() {
        return doCalculateEachRefundFeeFuncCore(ChargeFormItem::getAdjustmentPrice);
    }

    public BigDecimal getRefundFractionPrice() {
        if (getComposeType() == ComposeType.COMPOSE) {
            return composeChildren.stream().map(PharmacyItemProcessor::getRefundFractionPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        BigDecimal discountPrice = BigDecimal.ZERO;
        if (getItemStatus() != Constants.ChargeFormItemStatus.CHARGED) {
            return discountPrice;
        }

        discountPrice = refundChargeFormItems.stream().map(ChargeFormItem::getFractionPrice).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        return discountPrice;
    }

    public BigDecimal getNetIncomePrice() {
        return chargeFormItem.calculateDiscountedPriceV2().subtract(getRefundDiscountedTotalPrice());
    }

    public BigDecimal getCouldDispensingCount() {
        BigDecimal couldDispensingCount = BigDecimal.ZERO;
        if (getItemStatus() != Constants.ChargeFormItemStatus.CHARGED) {
            return couldDispensingCount;
        }

        if (isParent()) {
            return composeChildren.stream()
                    .map(itemProcessor -> itemProcessor.getCouldDispensingCount())
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        if (!chargeFormItem.isCanDispensing()) {
            return couldDispensingCount;
        }
        return MathUtils.calculateTotalCount(unitCount, doseCount).subtract(refundDoseCount.multiply(refundUnitCount));
    }

    public BigDecimal getCouldExecuteUnitCount() {
        BigDecimal couldExecuteCount = BigDecimal.ZERO;
        if (getItemStatus() != Constants.ChargeFormItemStatus.CHARGED) {
            return couldExecuteCount;
        }

        if (getProductType() != Constants.ProductType.TREATMENT) {
            return couldExecuteCount;
        }
        return unitCount.subtract(refundUnitCount);
    }

    private BigDecimal getCanRefundDoseCount() {

        //处理套餐的可退数量
        if (getComposeType() == ComposeType.COMPOSE && !CollectionUtils.isEmpty(composeChildren)) {

            boolean composeCanRefund = composeChildren.stream().anyMatch(child -> MathUtils.wrapBigDecimalOrZero(child.calculateCanRefundDoseCount()).compareTo(BigDecimal.ZERO) > 0);

            if (composeCanRefund) {
                return calculateCanRefundDoseCount();
            } else {
                return BigDecimal.ZERO;
            }

        }
        return calculateCanRefundDoseCount();
    }

    private BigDecimal calculateCanRefundDoseCount() {
        if (refundType == Constants.ChargeFormItemRefundType.UNIT) {
            return doseCount;
        }

        BigDecimal canRefundDoseCount = doseCount.subtract(refundDoseCount);

        //如果是虚拟药房的，则必须要发了药才能退费，如果还未发药，就不允许退费
//        if (formPharmacyType == GoodsConst.PharmacyType.VIRTUAL_PHARMACY) {
//            if (chargeFormItem.isCanDispensing() && dispensingInfo == null) {
//                return BigDecimal.ZERO;
//            }
//            return canRefundDoseCount;
//        }

//        if (chargeFormItem.isCanDispensing() && chargeFormItem.getIsAirPharmacy() == 0) {
//            if (dispensingInfo == null) {
//                canRefundDoseCount = BigDecimal.ZERO;
//            } else {
//                if (goodsPharmacyView != null && goodsPharmacyView.getExternalPharmacyConfig() != null && Objects.equals(goodsPharmacyView.getExternalPharmacyConfig().getUnChargeCheckExternal(), 1)
//                        && dispensingInfo.getEnableRefundFee() != null && dispensingInfo.getEnableRefundFee() == 0
//                ) {
//                    itemRefundFlag = ItemRefundFlag.YEKAITAI;
//                }
//                canRefundDoseCount = doseCount.subtract(dispensingInfo.getDispensedDoseCount()).subtract(refundDoseCount);
//            }
//        }

        if (BigDecimal.ZERO.compareTo(canRefundDoseCount) > 0) {
            canRefundDoseCount = BigDecimal.ZERO;
        }

        return canRefundDoseCount;
    }

    public BigDecimal getCanRefundUnitCount() {

        //处理套餐的可退数量
        if (getComposeType() == ComposeType.COMPOSE && !CollectionUtils.isEmpty(composeChildren)) {

            boolean composeCanRefund = composeChildren.stream().anyMatch(child -> MathUtils.wrapBigDecimalOrZero(child.calculateCanRefundUnitCount()).compareTo(BigDecimal.ZERO) > 0);

            if (composeCanRefund) {
                return calculateCanRefundUnitCount();
            } else {
                return BigDecimal.ZERO;
            }

        }

        return calculateCanRefundUnitCount();
    }

    private BigDecimal calculateCanRefundUnitCount() {
        BigDecimal canRefundUnitCount = unitCount.subtract(refundUnitCount);

//        if (chargeFormItem.isCanDispensing() && chargeFormItem.getIsAirPharmacy() == 0) {
//            if (dispensingInfo == null) {
//                canRefundUnitCount = BigDecimal.ZERO;
//            } else {
//                if (goodsPharmacyView != null && goodsPharmacyView.getExternalPharmacyConfig() != null && Objects.equals(goodsPharmacyView.getExternalPharmacyConfig().getUnChargeCheckExternal(), 1)
//                        && dispensingInfo.getEnableRefundFee() != null && dispensingInfo.getEnableRefundFee() == 0
//                ) {
//                    itemRefundFlag = ItemRefundFlag.YEKAITAI;
//                }
//                if (refundType == Constants.ChargeFormItemRefundType.DOSE) {
//                    canRefundUnitCount = unitCount;
//                } else {
//                    canRefundUnitCount = unitCount.subtract(dispensingInfo.getDispensedUnitCount()).subtract(refundUnitCount);
//                }
//            }
//        }

        if (BigDecimal.ZERO.compareTo(canRefundUnitCount) > 0) {
            canRefundUnitCount = BigDecimal.ZERO;
        }

        return canRefundUnitCount;
    }

    //TODO robinsli ？？？
    public void updateProductInfo(boolean needLatestGoodsName, int sourceFormType) {
        if (isParent()) {
            composeChildren.forEach(itemProcessor -> itemProcessor.updateProductInfo(needLatestGoodsName, sourceFormType));
        }

        if (getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED) {
            BigDecimal unitPrice = BigDecimal.ZERO, sourceFractionPrice = BigDecimal.ZERO;

            boolean isRegistration = chargeFormItem.getProductType() == Constants.ProductType.REGISTRATION;
            boolean isProcess = chargeFormItem.getProductType() == Constants.ProductType.PROCESS && chargeFormItem.getIsAirPharmacy() == 0;
            boolean isExpressDelivery = chargeFormItem.getProductType() == Constants.ProductType.EXPRESS_DELIVERY && chargeFormItem.getIsAirPharmacy() == 0;
//            boolean isChineseMedicine = chargeFormItem.getProductType() == Constants.ProductType.MEDICINE && chargeFormItem.getProductSubType() == Constants.ProductType.SubType.MEDICINE_CHINESE;

            //这儿默认值就是前端传的值
            chargeFormItem.setUnitCostPrice(BigDecimal.ZERO);
            chargeFormItem.setIsUseLimitPrice(0);
            unitPrice = chargeFormItem.getUnitPrice();

            if (isRegistration) {
                RegistrationFormItem registrationFormItem = JsonUtils.readValue(chargeFormItem.getProductSnapshot(), RegistrationFormItem.class);
                if (registrationFormItem != null) {
                    chargeFormItem.setUnitCostPrice(registrationFormItem.getCostUnitPrice());
                    unitPrice = Optional.ofNullable(registrationFormItem.getSourceFee()).orElse(registrationFormItem.getFee());
                }
            }
            chargeFormItem.setProductInfo(JsonUtils.loadAsJsonNode(chargeFormItem.getProductSnapshot()));

            if (goodsItem != null && !Constants.SystemProductId.SYSTEM_PRODUCT_IDS.contains(goodsItem.getId())) {

                if (sourceFormType != Constants.SourceFormType.AIR_PHARMACY && needLatestGoodsName) {
                    chargeFormItem.setName(goodsItem.getDisplayName());
                }

                chargeFormItem.setFeeComposeType(goodsItem.getFeeComposeType());
                chargeFormItem.setFeeTypeId(goodsItem.getFeeTypeId());
                ChargeFormItemFactory.insertChargeFormItemAdditionalIfNeed(chargeFormItem);
                chargeFormItem.getAdditional().setGoodsVersion(goodsItem.getGoodsVersion());

                if (sourceFormType == Constants.SourceFormType.AIR_PHARMACY || (goodsItem.getStatus() != null && goodsItem.getStatus() < GoodsConst.GoodsStatus.NO_VISIBLE)) {
                    if (isChild()) {
                        chargeFormItem.setUseDismounting(goodsItem.getComposeUseDismounting());
                    }

                    // 如果是中药，直接走拆零逻辑
                    boolean isChineseMedicine = goodsItem.getType() == Constants.ProductType.MEDICINE && goodsItem.getSubType() == Constants.ProductType.SubType.MEDICINE_CHINESE;
                    if (isChineseMedicine) {
                        chargeFormItem.setUseDismounting(1);
                    }

                    chargeFormItem.setProductInfo(JsonUtils.dumpAsJsonNode(goodsItem));
                    chargeFormItem.setProductSnapshot(JsonUtils.dump(goodsItem));
                    chargeFormItem.setProductType(goodsItem.getType());
                    chargeFormItem.setProductSubType(goodsItem.getSubType());

                    if (StringUtils.isEmpty(chargeFormItem.getProductId())) {
                        chargeFormItem.setProductId(goodsItem.getId());
                    }

                    ///这里要处理批次是否足够，如果不够，走正常计算单价，如果够，需要反算单价，记录零头
                    if (MathUtils.calculateTotalCount(chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount()).compareTo(BigDecimal.ZERO) > 0
                            && goodsItem.getShortagePieceCount() == null
                            && goodsItem.getShortagePackageCount() == null
                            && org.apache.commons.collections.CollectionUtils.isNotEmpty(goodsItem.getGoodsBatchInfoList())
                            && goodsItem.getTotalSalePrice() != null
                    ) {
                        BigDecimal totalSalePrice = goodsItem.getTotalSalePrice().setScale(2, RoundingMode.HALF_UP);
                        BigDecimal doseCount = Objects.nonNull(chargeFormItem.getExpectedDoseCount()) ? chargeFormItem.getExpectedDoseCount() : chargeFormItem.getDoseCount();
                        MathUtils.CalculateExpectedUnitPriceResult calculateExpectedUnitPriceResult = MathUtils.calculateExpectedUnitPriceBase(unitCount, doseCount, totalSalePrice, getItemCalculateUnitPriceScale(), 2);
                        unitPrice = calculateExpectedUnitPriceResult.expectedUnitPrice;
                        sourceFractionPrice = calculateExpectedUnitPriceResult.fractionPrice;
                    } else {
                        if (chargeFormItem.getUseDismounting() == 1) {
                            //单卖
                            BigDecimal piecePrice = MathUtils.wrapBigDecimalOrZero(isChild() ? goodsItem.getComposePiecePrice() : goodsItem.getPiecePrice());
                            unitPrice = piecePrice;
                            if (!(sourceFormType == Constants.SourceFormType.PRESCRIPTION_EXTERNAL && chargeFormItem.getProductType() == Constants.ProductType.TREATMENT)) {
                                chargeFormItem.setUnit(goodsItem.getPieceUnit());
                            }
                        } else {
                            //盒卖
                            BigDecimal packagePrice = MathUtils.wrapBigDecimalOrZero(isChild() ? goodsItem.getComposePackagePrice() : goodsItem.getPackagePrice());
                            unitPrice = packagePrice;
                            if (!(sourceFormType == Constants.SourceFormType.PRESCRIPTION_EXTERNAL && chargeFormItem.getProductType() == Constants.ProductType.TREATMENT)) {
                                chargeFormItem.setUnit(goodsItem.getPackageUnit());
                            }
                        }
                    }


                    //治疗和检验项使用goods信息计算成本
                    if (chargeFormItem.getProductType() == Constants.ProductType.TREATMENT || chargeFormItem.getProductType() == Constants.ProductType.EXAMINATION || chargeFormItem.getProductType() == Constants.ProductType.COMPOSE_PRODUCT || chargeFormItem.getProductType() == Constants.ProductType.OTHER_FEE) {
                        chargeFormItem.setUnitCostPrice(goodsItem.getPackageCostPrice());
                    }

                    if (chargeFormItem.getProductType() != Constants.ProductType.MEDICINE) {
                        chargeFormItem.setName(goodsItem.getName());
                    } else if (isChild() && chargeFormItem.getProductType() == Constants.ProductType.MEDICINE) {
                        //套餐中增加了药品，需要组装name
                        chargeFormItem.setName(formatGoodsName(goodsItem));
                    }

                    if ((isChild() ? goodsItem.getComposeUseDismounting() : goodsItem.getDismounting()) == 0 && chargeFormItem.getUseDismounting() == 1) {
                        isDismountingInvalid = true;
                    }
                    /**
                     * 这里现在要用批次去判断 某一个批次限价了，这个item就会标记为限价
                     */
//                    chargeFormItem.setIsUseLimitPrice(goodsItem.isLimitPriceEffect() ? 1 : 0);
                    chargeFormItem.setPharmacyNo(goodsItem.getPharmacyNo());
                } else {
                    setProductIdAndPriceNull();
                }

                if (sourceFormType != Constants.SourceFormType.AIR_PHARMACY && goodsItem.getDisableSell() == 1) {
                    isForbidSale = true;
                }

                //处理套餐子项零头的问题
                if (isChild()) {
                    //计算套餐子项的零头总额，goods上的零头是一个套餐的零头，这里要乘以套餐的总数量
                    sourceFractionPrice = MathUtils.calculateTotalPrice(chargeFormItem.getComposeParentUnitCount(), goodsItem.getComposeFractionPrice());
                }

                //自备药品或者赠品把价格写为0元
                if (chargeFormItem.getSourceItemType() == Constants.SourceItemType.SELF_PROVIDED) {
                    unitPrice = BigDecimal.ZERO;
                    chargeFormItem.setFractionPrice(BigDecimal.ZERO);
                    chargeFormItem.clearExpectedPrice();
                }
            }

            if (getIsGift() == Constants.ChargeFormItemGiftType.MARKED_GIFT) {
                chargeFormItem.clearExpectedPrice();
            }

            if (isProcess || isExpressDelivery) {
                if (chargeFormItem.getSourceUnitPrice() == null) {
                    chargeFormItem.setSourceUnitPrice(unitPrice);
                }
            } else {
                chargeFormItem.setSourceUnitPrice(unitPrice);
            }

            chargeFormItem.setGoodsTypeId(Optional.ofNullable(goodsItem).map(g -> String.valueOf(g.getTypeId())).orElse(null));
            chargeFormItem.setSourceTotalPrice(MathUtils.wrapBigDecimalAdd(MathUtils.calculateTotalPrice(unitPrice, unitCount, Optional.ofNullable(chargeFormItem.getExpectedDoseCount()).orElse(chargeFormItem.getDoseCount()), 2), sourceFractionPrice));
            chargeFormItem.calculateTotalCostPrice();

            if (chargeFormItem.getSourceItemType() == Constants.SourceItemType.SELF_PROVIDED) {
                chargeFormItem.setUnitPrice(BigDecimal.ZERO);
                chargeFormItem.setExpectedUnitPrice(null);
                chargeFormItem.setExpectedTotalPrice(null);
                chargeFormItem.setExpectedTotalPriceRatio(null);
                chargeFormItem.setFractionPrice(BigDecimal.ZERO);
                chargeFormItem.calculateTotalPriceV2();
            } else {
                //药店算费不在这里去算单项议价，在单项打折之后再算
                chargeFormItem.setUnitPrice(unitPrice);
                chargeFormItem.calculateTotalPrice();
                doseCount = chargeFormItem.getDoseCount();
            }
        } else {
            if (goodsItem != null
                    && !Constants.ProductType.SYSTEM_FEE_PRODUCT_TYPE.contains(chargeFormItem.getProductType())
                    && getComposeType() == ComposeType.NOT_COMPOSE
                    && chargeFormItem.getGoodsFeeType() == GoodsFeeType.FEE_OWN) {
                chargeFormItem.setProductInfo(JsonUtils.dumpAsJsonNode(goodsItem));
            } else {
                chargeFormItem.setProductInfo(JsonUtils.loadAsJsonNode(chargeFormItem.getProductSnapshot()));
            }

        }

    }

    private int getItemCalculateUnitPriceScale() {
        return ChargeFormItemUtils.genItemCalculateUnitPriceScale(chargeFormItem.getProductType(), chargeFormItem.getProductSubType());
    }

    public void bindBatchInfo(String operatorId) {

        if (chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.UNCHARGED) {
            return;
        }

        if (goodsItem == null || org.apache.commons.collections.CollectionUtils.isEmpty(goodsItem.getGoodsBatchInfoList())) {
            return;
        }


        Map<String, GoodsBatchInfo> goodsBatchInfoMap = goodsItem.getGoodsBatchInfoList().stream()
                .collect(Collectors.toMap(goodsItemBatchInfo -> String.valueOf(goodsItemBatchInfo.getBatchId()), Function.identity(), (a, b) -> a));

        //新增或修改批次信息
        ChargeFormItemBatchInfoUtils.insertOrUpdateChargeFormItemBatchInfosForGoodsBatchInfo(chargeFormItem, goodsItem.getPieceNum(), goodsItem.getGoodsBatchInfoList(), operatorId);

        chargeFormItem.getChargeFormItemBatchInfos().removeIf(chargeFormItemBatchInfo -> {
            if (chargeFormItemBatchInfo.getIsDeleted() != 1) {
                return false;
            }
            toDeleteChargeSheetRelationDto.addChargeFormItemBatchInfo(chargeFormItemBatchInfo);
            return true;
        });

        ChargeFormItemBatchInfoUtils.flatSourceTotalPriceToBatchInfo(chargeFormItem.getChargeFormItemBatchInfos(),
                chargeFormItem.getSourceTotalPrice(),
                ChargeFormItemUtils.genItemCalculateUnitPriceScale(chargeFormItem.getProductType(), chargeFormItem.getProductSubType()),
                GoodsConst.checkFlagOn(goodsItem.getPriceType(), GoodsConst.PriceType.PKG_PRICE_MAKEUP)
        );
        //单独计算批次的totalPrice的算法
        Optional.ofNullable(chargeFormItem.getChargeFormItemBatchInfos()).orElse(new ArrayList<>()).forEach(ChargeFormItemBatchInfo::calculateTotalPriceV2);


        List<ChargeFormItemBatchInfo> chargeFormItemBatchInfos = chargeFormItem.getChargeFormItemBatchInfos();

        //2、生成itemBatchInfoProcessor
        itemBatchInfoProcessors = chargeFormItemBatchInfos.stream()
                .map(chargeFormItemBatchInfo -> new ItemBatchInfoProcessor(chargeFormItemBatchInfo, false))
                .peek(itemBatchInfoProcessor -> {
                    GoodsBatchInfo goodsBatchInfo = goodsBatchInfoMap.get(itemBatchInfoProcessor.getChargeFormItemBatchInfo().getBatchId());
                    itemBatchInfoProcessor.setGoodsBatchInfo(goodsBatchInfo);
                })
                .collect(Collectors.toList());


        //3、根据批次的成本价计算item的平均成本价，由于item上只有单个的成本价，这里会有精度问题，目前还没有需求需要处理这个精度，如果要保证批次的成本的和等于item的成本的和，需要在item上增加costTotalPrice（成本总价）、costFractionPrice（成本总价零头）字段，同时，全流程需要处理成本总价零头
        BigDecimal totalCount = MathUtils.calculateTotalCount(chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount());
        if (totalCount.compareTo(BigDecimal.ZERO) > 0) {
            //如果批次的单价都是一样的，不需要计算
            BigDecimal itemTotalCostPrice = chargeFormItemBatchInfos.stream()
                    .map(chargeFormItemBatchInfo -> MathUtils.calculateTotalPrice(chargeFormItemBatchInfo.getUnitCostPrice(), chargeFormItemBatchInfo.getUnitCount()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(2, RoundingMode.HALF_UP);
            chargeFormItem.setUnitCostPrice(itemTotalCostPrice.divide(totalCount, 4, RoundingMode.DOWN));
            chargeFormItem.setTotalCostPrice(itemTotalCostPrice);
        } else {
            chargeFormItem.setUnitCostPrice(BigDecimal.ZERO);
            chargeFormItem.setTotalCostPrice(BigDecimal.ZERO);
        }

    }

    private String formatGoodsName(GoodsItem goodsItem) {
        String name = "";

        if (goodsItem == null) {
            return "";
        }

        String medicineCadn = goodsItem.getMedicineCadn();
        String goodsName = goodsItem.getName();

        if (!StringUtils.isEmpty(medicineCadn)) {
            name = medicineCadn + (!StringUtils.isEmpty(goodsName) ? String.format("(%s)", goodsName) : "");
        } else {
            name = goodsName;
        }

        return name;
    }

    public void updateProductStock(int pharmacyType) {
        if (isParent()) {
            composeChildren.forEach(itemProcessor -> itemProcessor.updateProductStock(pharmacyType));
        }

        if (getItemStatus() != Constants.ChargeFormItemStatus.UNCHARGED) {
            return;
        }

        if (pharmacyType == GoodsConst.PharmacyType.VIRTUAL_PHARMACY) {

            if (goodsItem != null) {
                stockPackageCount = goodsItem.getStockPackageCount();
                stockPieceCount = goodsItem.getStockPieceCount();
            }

            isStockAvailable = true;

            return;
        }
        // 数量
        BigDecimal _totalCount = MathUtils.calculateTotalCount(chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount());

        if (chargeFormItem.getSourceItemType() == Constants.SourceItemType.SELF_PROVIDED) {
            stockPackageCount = _totalCount;
            stockPieceCount = _totalCount;
            isStockAvailable = true;
            return;
        }

        if (getProductType() != Constants.ProductType.MATERIAL && getProductType() != Constants.ProductType.MEDICINE && getProductType() != Constants.ProductType.SALE_PRODUCT) {
            stockPackageCount = _totalCount;
            stockPieceCount = _totalCount;
            isStockAvailable = true;
            return;
        }

        if (goodsItem == null) {
            stockPieceCount = BigDecimal.ZERO;
            stockPackageCount = BigDecimal.ZERO;
            isStockAvailable = false;
            return;
        }

        // 成本价
        BigDecimal lastPackageCostPrice = MathUtils.wrapBigDecimalOrZero(goodsItem.getLastPackageCostPrice());
        if (chargeFormItem.getUseDismounting() == 1) {
            BigDecimal totalCostPrice = BigDecimal.ZERO;
            BigDecimal unitCostPrice = BigDecimal.ZERO;
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(itemBatchInfoProcessors)) {
                Pair<BigDecimal, BigDecimal> costPricePair = calculateItemUnitCostPriceForBatch();
                unitCostPrice = costPricePair.getRight();
                totalCostPrice = costPricePair.getLeft();
            } else {
                unitCostPrice = lastPackageCostPrice.divide(MathUtils.wrapBigDecimalOrZero(goodsItem.getPieceNum()), 5, RoundingMode.HALF_UP);
                totalCostPrice = MathUtils.calculateTotalPrice(unitCostPrice, chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount()).setScale(4, RoundingMode.HALF_UP);
            }
            chargeFormItem.setUnitCostPrice(unitCostPrice);
            chargeFormItem.setTotalCostPrice(totalCostPrice);
            BigDecimal _stockPackage = MathUtils.wrapBigDecimalOrZero(goodsItem.getStockPackageCount());
            BigDecimal _stockPiece = MathUtils.wrapBigDecimalOrZero(goodsItem.getStockPieceCount());
            BigDecimal _stockCount = _stockPackage.multiply(MathUtils.wrapBigDecimalOrZero(goodsItem.getPieceNum())).add(_stockPiece);

            isStockAvailable = _stockCount.compareTo(_totalCount) >= 0;
        } else {
            BigDecimal totalCostPrice = BigDecimal.ZERO;
            BigDecimal unitCostPrice = BigDecimal.ZERO;
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(itemBatchInfoProcessors)) {
                Pair<BigDecimal, BigDecimal> costPricePair = calculateItemUnitCostPriceForBatch();
                unitCostPrice = costPricePair.getRight();
                totalCostPrice = costPricePair.getLeft();
            } else {
                unitCostPrice = lastPackageCostPrice;
                totalCostPrice = MathUtils.calculateTotalPrice(unitCostPrice, chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount()).setScale(4, RoundingMode.HALF_UP);
            }
            chargeFormItem.setUnitCostPrice(unitCostPrice);
            chargeFormItem.setTotalCostPrice(totalCostPrice);
            BigDecimal _stockPackage = MathUtils.wrapBigDecimalOrZero(goodsItem.getStockPackageCount());

            isStockAvailable = _stockPackage.compareTo(_totalCount) >= 0;
        }

        if (isStockAvailable && org.apache.commons.collections.CollectionUtils.isNotEmpty(itemBatchInfoProcessors)) {
            BigDecimal batchTotalCount = itemBatchInfoProcessors.stream()
                    .map(itemBatchInfoProcessor -> itemBatchInfoProcessor.getChargeFormItemBatchInfo().getUnitCount())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            if (MathUtils.wrapBigDecimalCompare(MathUtils.calculateTotalCount(chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount()), batchTotalCount) > 0) {
                isStockAvailable = false;
            }
        }

        stockPackageCount = goodsItem.getStockPackageCount();
        stockPieceCount = goodsItem.getStockPieceCount();
    }

    private Pair<BigDecimal, BigDecimal> calculateItemUnitCostPriceForBatch() {
        BigDecimal totalCount = MathUtils.calculateTotalCount(chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount());
        if (totalCount.compareTo(BigDecimal.ZERO) > 0) {
            //如果批次的成本价都是一样的，不需要计算
            BigDecimal batchTotalCostPrice = itemBatchInfoProcessors.stream()
                    .map(itemBatchInfoProcessor -> itemBatchInfoProcessor.getChargeFormItemBatchInfo().getTotalCostPrice())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            return Pair.of(batchTotalCostPrice, batchTotalCostPrice.divide(totalCount, 4, RoundingMode.DOWN));
        } else {
            return Pair.of(BigDecimal.ZERO, BigDecimal.ZERO);
        }
    }

    public boolean getGoodsIsStockAvailable() {

        if (chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.UNCHARGED) {
            return true;
        }

        if (isParent()) {
            return composeChildren.stream().anyMatch(PharmacyItemProcessor::getGoodsIsStockAvailable);
        }
        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeFormItemId: {}, isStockAvailable: {}", chargeFormItem.getId(), isStockAvailable);
        return isStockAvailable;
    }


    public void checkProductInfoOrThrowException(boolean checkGoodsStock) throws ProductInfoChangedException {
        if (isParent()) {
            for (PharmacyItemProcessor child : composeChildren) {
                child.checkProductInfoOrThrowException(checkGoodsStock);
            }
        }

        if (getItemStatus() == Constants.ChargeFormStatus.UNCHARGED && chargeFormItem.getSourceItemType() != Constants.SourceItemType.SELF_PROVIDED) {
            //校验药品是否缺少了药品资料
            if (isLackGoodsItem) {
                sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeFormItem id: {}, productId: {}, chargeSheetId: {} 有id没有goodsItem", chargeFormItem.getId(), getProductId(), chargeFormItem.getChargeSheetId());
                throw new ProductInfoChangedException(generateCurrentChargeFormItemView(), ProductInfoChangedException.Type.GOODS_IS_LACK_GOODS_ITEM);
            }

            // 校验商品是否未定价
            if (Objects.nonNull(goodsItem) && Objects.isNull(goodsItem.getPackagePrice())){
                throw new ProductInfoChangedException(generateCurrentChargeFormItemView(), ProductInfoChangedException.Type.GOODS_NOT_SET_PRICE);
            }

            //校验商品是否禁止销售
            if (isForbidSale) {
                sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeFormItem id:{}, productId:{}, chargeSheetId:{} isForbidSale", chargeFormItem.getId(), getProductId(), chargeFormItem.getChargeSheetId());
                throw new ProductInfoChangedException(generateCurrentChargeFormItemView(), ProductInfoChangedException.Type.GOODS_IS_FORBID_SALE);
            }

            //PC上收费不校验药品库存是否足够
            if (chargeFormItem.getGoodsFeeType() != GoodsFeeType.FEE_CHILD && !isStockAvailable && checkGoodsStock) {
                sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeFormItem id:{}, productId:{}, chargeSheetId:{} out stock", chargeFormItem.getId(), getProductId(), chargeFormItem.getChargeSheetId());
                throw new ProductInfoChangedException(generateCurrentChargeFormItemView(), ProductInfoChangedException.Type.STOCK_CHANGED);
            }

            if (isParent()) {
                //校验如果有是母项，但是没有children，不允许收费
                if (org.apache.commons.collections.CollectionUtils.isEmpty(composeChildren)) {
                    sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeFormItem id: {}, productId: {}, chargeSheetId: {} 有母项没有子项", chargeFormItem.getId(), getProductId(), chargeFormItem.getChargeSheetId());
                    throw new ProductInfoChangedException(generateCurrentChargeFormItemView(), ProductInfoChangedException.Type.GOODS_PRICE_AND_SHEBAO_CODE_CHANGED);
                }

//                //校验子项不能重复
//                List<GoodsItem> subGoodsItems = getSubGoodsItemFromGoodsItem(goodsItem);
//                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(subGoodsItems)) {
//                    Set<String> subGoodsItemIds = subGoodsItems.stream()
//                            .map(GoodsItem::getId)
//                            .filter(org.apache.commons.lang3.StringUtils::isNotEmpty)
//                            .collect(Collectors.toSet());
//
//                    if (subGoodsItemIds.size() != subGoodsItems.size()) {
//                        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeFormItem id:{}, productId:{}, chargeSheetId:{}, goodsItem: {}", chargeFormItem.getId(), getProductId(), chargeFormItem.getChargeSheetId(), JsonUtils.dump(goodsItem));
//                        throw new CisCustomException(ChargeServiceError.GOODS_CHILDREN_REPEAT.getCode(), String.format(ChargeServiceError.GOODS_CHILDREN_REPEAT.getMessage(), chargeFormItem.getName()));
//                    }
//                }

            }

            if (isDismountingInvalid) {
                sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeFormItem id:{}, productId:{}, chargeSheetId:{} dismounting changed", chargeFormItem.getId(), getProductId(), chargeFormItem.getChargeSheetId());
                throw new ProductInfoChangedException(generateCurrentChargeFormItemView(), ProductInfoChangedException.Type.DISMOUNTING_CHANGED);
            }
        }
    }

    public void checkPriceChangedAndThrowException() throws ProductInfoChangedException {
        if (getItemStatus() != Constants.ChargeFormStatus.UNCHARGED) {
            return;
        }

        if (chargeFormItem.isExpectedPriceItem() || chargeFormItem.getFrontEndUnitPrice() == null) {
            return;
        }

        if (chargeFormItem.getFrontEndUnitPrice().compareTo(MathUtils.wrapBigDecimalOrZero(chargeFormItem.getUnitPrice())) != 0) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeFormItem id:{}, productId:{}, chargeSheetId:{} price changed, frontEndUnitPrice: {}, unitPrice: {}", chargeFormItem.getId(), getProductId(), chargeFormItem.getChargeSheetId(), chargeFormItem.getFrontEndUnitPrice(), chargeFormItem.getUnitPrice());
            throw new ProductInfoChangedException(generateCurrentChargeFormItemView(), ProductInfoChangedException.Type.PRICE_CHANGED);
        }
    }

    public CalculateChargeFormItemView generateCalculateResult(boolean isRefundScene) {
        CalculateChargeFormItemView calculateChargeFormItemResult = new CalculateChargeFormItemView();
        BeanUtils.copyProperties(chargeFormItem, calculateChargeFormItemResult);
        Optional.ofNullable(chargeFormItem.getAdditional()).ifPresent(chargeFormItemAdditional -> BeanUtils.copyProperties(chargeFormItemAdditional, calculateChargeFormItemResult));
        calculateChargeFormItemResult.setUnitPrice(calculateUnitPriceView());
        calculateChargeFormItemResult.setUseLimitPrice(getCalIsUseLimitPrice() == 1);
        calculateChargeFormItemResult.setReceivableTotalFee(receivableFee);
        calculateChargeFormItemResult.setSheBaoReceivableFee(sheBaoReceivableFee);
        calculateChargeFormItemResult.setSinglePromotions(allSinglePromotions);
        //赠品返回productInfo
        if (getIsGift() == 1) {
            calculateChargeFormItemResult.setProductInfo(JsonUtils.dumpAsJsonNode(goodsItem));
        }
        if (isRefundScene) {
            if (addedRefundChargeItem != null) {
                if (isLocalChinesePrescription()) {
                    calculateChargeFormItemResult.setDoseCount(addedRefundChargeItem.getDoseCount());
                }
                calculateChargeFormItemResult.setUnitCount(addedRefundChargeItem.getUnitCount());
                calculateChargeFormItemResult.setDiscountPrice(addedRefundChargeItem.getDiscountPrice());
                calculateChargeFormItemResult.setUnitPrice(addedRefundChargeItem.getUnitPrice());
                calculateChargeFormItemResult.setTotalPrice(ChargeFormItemUtils.calculateTotalPriceView(addedRefundChargeItem));
                calculateChargeFormItemResult.setAdjustmentPrice(addedRefundChargeItem.getAdjustmentPrice());
                calculateChargeFormItemResult.setDiscountedTotalPrice(addedRefundChargeItem.calculateDiscountedPriceV2());
                calculateChargeFormItemResult.setSourceTotalPrice(addedRefundChargeItem.getSourceTotalPrice());
                calculateChargeFormItemResult.setChargeFormItemBatchInfos(generateCalculateRefundBatchInfoViews());
                calculateChargeFormItemResult.setIsExpectedBatch(addedRefundChargeItem.getIsExpectedBatch());
                calculateChargeFormItemResult.setSinglePromotions(null);
            } else {
                if (isLocalChinesePrescription()) {
                    calculateChargeFormItemResult.setDoseCount(BigDecimal.ZERO);
                }
                calculateChargeFormItemResult.setUnitCount(BigDecimal.ZERO);
                calculateChargeFormItemResult.setDiscountPrice(BigDecimal.ZERO);
                calculateChargeFormItemResult.setTotalPrice(BigDecimal.ZERO);
                calculateChargeFormItemResult.setAdjustmentPrice(BigDecimal.ZERO);
                calculateChargeFormItemResult.setDiscountedTotalPrice(BigDecimal.ZERO);
            }
        } else {
            if (!isParent()) {
                calculateChargeFormItemResult.setChargeFormItemBatchInfos(generateCalculateBatchInfoViews());
                calculateChargeFormItemResult.setLimitInfo(Optional.ofNullable(chargeFormItem.getAdditional()).map(ChargeFormItemAdditional::getLimitInfo).orElse(null));
                calculateChargeFormItemResult.setStockPieceCount(stockPieceCount);
                calculateChargeFormItemResult.setStockPackageCount(stockPackageCount);
            }
            calculateChargeFormItemResult.setDiscountPrice(getDiscountPrice(false));
            calculateChargeFormItemResult.setTotalPrice(calculateTotalPriceView());
            calculateChargeFormItemResult.setAdjustmentPrice(getAdjustmentPrice());
            calculateChargeFormItemResult.setDiscountedTotalPrice(chargeFormItem.calculateDiscountedPriceV2());
            if (MathUtils.wrapBigDecimalCompare(calculateChargeFormItemResult.getUnitCount(), BigDecimal.ZERO) <= 0) {
                calculateChargeFormItemResult.setSinglePromotionedUnitPrice(chargeFormItem.getSourceUnitPrice());
                calculateChargeFormItemResult.setSinglePromotionedTotalPrice(BigDecimal.ZERO);
            } else {
                BigDecimal singlePromotionPrice = chargeFormItem.calculateSinglePromotionPrice();
                if (MathUtils.wrapBigDecimalCompare(singlePromotionPrice, BigDecimal.ZERO) == 0) {
                    calculateChargeFormItemResult.setSinglePromotionedUnitPrice(chargeFormItem.getSourceUnitPrice());
                    calculateChargeFormItemResult.setSinglePromotionedTotalPrice(chargeFormItem.getSourceTotalPrice());
                } else {
                    calculateChargeFormItemResult.setSinglePromotionedTotalPrice(chargeFormItem.getSourceTotalPrice()
                            .add(singlePromotionPrice)
                    );
                    calculateChargeFormItemResult.setSinglePromotionedUnitPrice(calculateChargeFormItemResult.getSinglePromotionedTotalPrice()
                            .divide(calculateChargeFormItemResult.getUnitCount(), 2, RoundingMode.HALF_UP)
                    );
                }
            }

        }

        if (isParent()) {
            List<CalculateChargeFormItemView> composeChildrenViews = composeChildren.stream().map(itemProcessor -> itemProcessor.generateCalculateResult(isRefundScene)).collect(Collectors.toList());
            calculateChargeFormItemResult.setComposeChildren(composeChildrenViews);
            calculateChargeFormItemResult.setDiscountPrice(composeChildrenViews.stream().map(CalculateChargeFormItemView::getDiscountPrice).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            calculateChargeFormItemResult.setTotalPrice(composeChildrenViews.stream().map(CalculateChargeFormItemView::getTotalPrice).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            calculateChargeFormItemResult.setAdjustmentPrice(composeChildrenViews.stream().map(CalculateChargeFormItemView::getAdjustmentPrice).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            calculateChargeFormItemResult.setDiscountedTotalPrice(composeChildrenViews.stream().map(CalculateChargeFormItemView::getDiscountedTotalPrice).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        if(chargeFormItem.getAdditional() != null){
            calculateChargeFormItemResult.setTraceableCodeList( chargeFormItem.getAdditional().getTraceableCodeList());
        } else{
            calculateChargeFormItemResult.setTraceableCodeList(null);
        }
        TraceCodeUtils.updateTraceCodeDrugIdentificationCode(calculateChargeFormItemResult.getTraceableCodeList());

        return calculateChargeFormItemResult;
    }

    private BigDecimal calculateTotalPriceView() {
        return doCalculateEachFeeFuncCore(false, ChargeFormItemUtils::calculateTotalPriceView);
    }

    private BigDecimal calculateUnitPriceView() {
        return chargeFormItem.getUnitPrice();
    }

    private List<ChargeFormItemBatchInfoView> generateCalculateBatchInfoViews() {

        if (CollectionUtils.isEmpty(itemBatchInfoProcessors)) {
            return null;
        }

        return itemBatchInfoProcessors.stream()
                .map(ItemBatchInfoProcessor::generateChargeFormItemBatchInfoView)
                .filter(chargeFormItemBatchInfoView -> MathUtils.wrapBigDecimalCompare(chargeFormItemBatchInfoView.getUnitCount(), BigDecimal.ZERO) > 0)
                .collect(Collectors.toList());
    }

    private List<ChargeFormItemBatchInfoView> generateCalculateRefundBatchInfoViews() {

        if (addedRefundChargeItem == null || org.apache.commons.collections.CollectionUtils.isEmpty(addedRefundChargeItem.getChargeFormItemBatchInfos())) {
            return null;
        }

        return addedRefundChargeItem.getChargeFormItemBatchInfos()
                .stream()
                .map(chargeFormItemBatchInfo -> {
                    ChargeFormItemBatchInfoView chargeItemBatchInfoView = ChargeFormItemBatchInfoView.toChargeItemBatchInfoView(chargeFormItemBatchInfo, null);
                    chargeItemBatchInfoView.setId(chargeFormItemBatchInfo.getAssociateItemBatchInfoId());
                    return chargeItemBatchInfoView;
                })
                .collect(Collectors.toList());
    }

    private List<ChargeFormItemBatchInfoView> generateBatchInfoViews() {

        if (CollectionUtils.isEmpty(itemBatchInfoProcessors)) {
            return new ArrayList<>();
        }

        return itemBatchInfoProcessors.stream()
                .filter(itemBatchInfoProcessor -> itemBatchInfoProcessor.getIsNotCharged() == 0)
                .map(itemBatchInfoProcessor -> itemBatchInfoProcessor.generateChargeFormItemBatchInfoView(chargeFormItem.getStatus()))
                .filter(Objects::nonNull)
                .peek(chargeFormItemBatchInfoView -> {
                    if (MapUtils.isNotEmpty(goodsBatchInfoSnapBatchIdMap)) {
                        GoodsBatchInfoSnap goodsBatchInfoSnap = goodsBatchInfoSnapBatchIdMap.get(chargeFormItemBatchInfoView.getBatchId());
                        if (goodsBatchInfoSnap != null) {
                            chargeFormItemBatchInfoView.setExpiryDate(goodsBatchInfoSnap.getExpiryDate());
                            chargeFormItemBatchInfoView.setBatchNo(goodsBatchInfoSnap.getBatchNo());
                        }
                        chargeFormItemBatchInfoView.setGoodsBatchInfoSnap(JsonUtils.dumpAsJsonNode(goodsBatchInfoSnap));
                    }
                })
                .collect(Collectors.toList());
    }

    private ChargeFormItemView generateCurrentChargeFormItemView() {
        ChargeFormItemView chargeFormItemView = new ChargeFormItemView();
        BeanUtils.copyProperties(chargeFormItem, chargeFormItemView, "chargeFormItemBatchInfos");
        chargeFormItemView.setStockPackageCount(stockPackageCount);
        chargeFormItemView.setStockPieceCount(stockPieceCount);
        chargeFormItemView.setProductInfo(JsonUtils.loadAsJsonNode(chargeFormItem.getProductSnapshot()));
        chargeFormItemView.setUsageInfo(JsonUtils.loadAsJsonNode(chargeFormItem.getUsageInfoJson()));
        return chargeFormItemView;
    }

    public List<ChargeFormItemView> generateChargeFormItemView(boolean isContainGiftGoods) {
        if (!isContainGiftGoods && chargeFormItem.getIsGift() == Constants.ChargeFormItemGiftType.PROMOTION_GIFT) {
            return new ArrayList<>();
        }
        List<ChargeFormItemView> chargeFormItemViews = new ArrayList<>();

        ChargeFormItemView chargeFormItemView = new ChargeFormItemView();
        BeanUtils.copyProperties(chargeFormItem, chargeFormItemView);
        if (Objects.nonNull(chargeFormItem.getAdditional())) {
            BeanUtils.copyProperties(chargeFormItem.getAdditional(), chargeFormItemView);
        }
        chargeFormItemView.setReceivableTotalFee(receivableFee);
        chargeFormItemView.setSheBaoReceivableTotalFee(sheBaoReceivableFee);
        chargeFormItemView.setIsMarkedByHealthCardPay(Optional.ofNullable(chargeFormItem.getPromotionInfo())
                .map(ChargeDiscountInfo::getIsMarkedByHealthCardPay)
                .orElse(0)
        );
        // 如果有批次信息处理批次信息
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(itemBatchInfoProcessors)) {
            chargeFormItemView.setChargeFormItemBatchInfos(generateBatchInfoViews());
        }
        // 只有在待收时才返回单项议价值
        HandleUtils.isTrue(getItemStatus() != Constants.ChargeFormItemStatus.UNCHARGED)
                .handle(() -> {
                    chargeFormItemView.setExpectedUnitPrice(null);
                    chargeFormItemView.setExpectedTotalPrice(null);
                    chargeFormItemView.setExpectedTotalPriceRatio(null);
                    chargeFormItemView.setDoctorSourceUnitPrice(null);
                    chargeFormItemView.setDoctorSourceTotalPrice(null);
                });
        if (getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED && getProductType() != Constants.ProductType.REGISTRATION && getProductType() != Constants.ProductType.EXPRESS_DELIVERY && getProductType() != Constants.ProductType.PROCESS && goodsItem == null) {
            chargeFormItemView.setIsProductDeleted(1);
        }
        if (getItemStatus() == Constants.ChargeFormItemStatus.UNSELECTED && chargeFormItemView.getProductInfo() == null && goodsItem != null) {
            chargeFormItemView.setProductInfo(JsonUtils.dumpAsJsonNode(goodsItem));
        }
        chargeFormItemView.setNeedExecutive(Optional.ofNullable(goodsItem).map(GoodsItem::getNeedExecutive).orElse(0));
        chargeFormItemView.setIsOutOfStock(getIsOutOfStock());
        chargeFormItemView.setStockPackageCount(stockPackageCount);
        chargeFormItemView.setStockPieceCount(stockPieceCount);
        chargeFormItemView.setUsageInfo(JsonUtils.loadAsJsonNode(chargeFormItem.getUsageInfoJson()));
        chargeFormItemView.setPromotionInfo(JsonUtils.loadAsJsonNode(chargeFormItem.getPromotionInfoJson()));
        chargeFormItemView.setSinglePromotions(allSinglePromotions);

        // 药品-西药、中成药 耗材-医疗器械
        chargeFormItemView.setIsShowTraceableCode(chargeFormItemView.showTraceableCode() ? Constants.ChargeFormItemShowTraceableCode.SHOW : Constants.ChargeFormItemShowTraceableCode.HIDE);

        List<ChargeFormItemView> composeChildrenViews = composeChildren.stream()
                .map(itemProcessor -> itemProcessor.generateChargeFormItemView(isContainGiftGoods)).flatMap(Collection::stream)
                .sorted((a, b) -> ObjectUtils.compare(a.getSort(), b.getSort()))
                .collect(Collectors.toList());
        chargeFormItemView.setComposeChildren(composeChildrenViews);

        if (getItemStatus() == Constants.ChargeFormItemStatus.UNCHARGED || getItemStatus() == Constants.ChargeFormItemStatus.UNSELECTED) {
            chargeFormItemView.setDiscountedTotalPrice(MathUtils.wrapBigDecimalOrZero(receivableFee));
            chargeFormItemViews.add(chargeFormItemView);
            // 设置追溯码是否可以显示
            chargeFormItemView.setIsShowTraceableCode(getItemStatus() != Constants.ChargeFormItemStatus.UNSELECTED && chargeFormItemView.showTraceableCode() ? Constants.ChargeFormItemShowTraceableCode.SHOW : Constants.ChargeFormItemShowTraceableCode.HIDE);
        } else if (getItemStatus() == Constants.ChargeFormItemStatus.CHARGED) {
            BigDecimal leftChargedUnitCount = refundType == Constants.ChargeFormItemRefundType.UNIT ? unitCount.subtract(refundUnitCount) : unitCount;
            chargeFormItemView.setUnitCount(leftChargedUnitCount);
            chargeFormItemView.setDoseCount(getLeftChargedDoseCount());
            chargeFormItemView.setDiscountPrice(getDiscountPrice(false).subtract(getRefundDiscountPrice()));
            chargeFormItemView.setPromotionPrice(getPromotionPrice(false).subtract(getRefundPromotionPrice()));
            chargeFormItemView.setAdjustmentPrice(getAdjustmentPrice().subtract(getRefundAdjustmentPrice()));
            chargeFormItemView.setTotalPrice(MathUtils.wrapBigDecimalSubtract(ChargeFormItemUtils.calculateTotalPriceView(chargeFormItem), getRefundTotalPriceView()));
            chargeFormItemView.setDiscountedTotalPrice(MathUtils.wrapBigDecimalSubtract(chargeFormItem.calculateDiscountedPriceV2(), getRefundDiscountedTotalPrice()));
            chargeFormItemView.setDisplayDiscountedUnitPrice(chargeFormItem.calculateDisplayDiscountedUnitPrice());

            chargeFormItemView.setCanRefundDoseCount(getCanRefundDoseCount());
            chargeFormItemView.setCanRefundUnitCount(getCanRefundUnitCount());
            chargeFormItemView.setDeductedTotalCount(getDeductedTotalCount());
            chargeFormItemView.setCanRefundDeductCount(getCanRefundDeductCount());
            chargeFormItemView.setItemRefundFlag(itemRefundFlag);

            // 处理追溯码问题: 已收费的才处理追溯码
            if (Objects.nonNull(dispensingInfo) && org.apache.commons.collections.CollectionUtils.isNotEmpty(dispensingInfo.getTraceableCodeList())) {
                chargeFormItemView.setTraceableCodeList(dispensingInfo.getTraceableCodeList());
            } else{
                chargeFormItemView.setTraceableCodeList(null);
            }

            ChargeFormItemView refundChargeFormItemView = new ChargeFormItemView();
            BeanUtils.copyProperties(chargeFormItemView, refundChargeFormItemView);
            if (getItemV2Status() != Constants.ChargeFormItemStatus.REFUNDED) {
                chargeFormItemView.setIsShowTraceableCode(chargeFormItemView.showTraceableCode() ? Constants.ChargeFormItemShowTraceableCode.SHOW : Constants.ChargeFormItemShowTraceableCode.HIDE);
                chargeFormItemViews.add(chargeFormItemView);

                // 部分退费的退费项needExecutive 设置为0 by 陈磊
                refundChargeFormItemView.setNeedExecutive(0);
                refundChargeFormItemView.setIsShowTraceableCode(Constants.ChargeFormItemShowTraceableCode.HIDE);
            } else {
                // 全部退费的逻辑
                if (Objects.nonNull(dispensingInfo) && org.apache.commons.collections.CollectionUtils.isNotEmpty(dispensingInfo.getTraceableCodeList())) {
                    refundChargeFormItemView.setTraceableCodeList( dispensingInfo.getTraceableCodeList());
                }
                else{
                    refundChargeFormItemView.setTraceableCodeList(null);
                }
                // 退完不展示追溯码
                refundChargeFormItemView.setIsShowTraceableCode(Constants.ChargeFormItemShowTraceableCode.HIDE);
            }

            refundChargeFormItemView.setStatus(Constants.ChargeFormItemStatus.REFUNDED);
            refundChargeFormItemView.setUnitCount(refundUnitCount);
            refundChargeFormItemView.setDoseCount(refundDoseCount);
            refundChargeFormItemView.setTotalPrice(getRefundTotalPriceView());
            refundChargeFormItemView.setPromotionPrice(getRefundPromotionPrice());
            refundChargeFormItemView.setDiscountPrice(getRefundDiscountPrice());
            refundChargeFormItemView.setAdjustmentPrice(getRefundAdjustmentPrice());
            refundChargeFormItemView.setDiscountedTotalPrice(getRefundDiscountedTotalPrice());
            refundChargeFormItemView.setChargeFormItemBatchInfos(generateRefundBatchInfoViews());

            if (refundType == Constants.ChargeFormItemRefundType.UNIT && leftChargedUnitCount.compareTo(BigDecimal.ZERO) > 0) {
                refundChargeFormItemView.setId(AbcIdUtils.getUUID());
                refundChargeFormItemView.setComposeChildren(new ArrayList<>()); //clear
            }

            if ((refundType == Constants.ChargeFormItemRefundType.UNIT && refundChargeFormItemView.getUnitCount().compareTo(BigDecimal.ZERO) > 0)
                    || (refundType == Constants.ChargeFormItemRefundType.DOSE && getItemV2Status() == Constants.ChargeFormItemStatus.REFUNDED)
            ) {
                refundChargeFormItemView.setDisplayDiscountedTotalPrice(MathUtils.calculateTotalPrice(chargeFormItemView.getDisplayDiscountedUnitPrice(), refundChargeFormItemView.getUnitCount(), refundChargeFormItemView.getDoseCount()));
                chargeFormItemView.setDisplayDiscountedTotalPrice(MathUtils.wrapBigDecimalSubtract(chargeFormItem.calculateDiscountedPrice(), refundChargeFormItemView.getDisplayDiscountedTotalPrice()));
                chargeFormItemViews.add(refundChargeFormItemView);
            } else {
                chargeFormItemView.setDisplayDiscountedTotalPrice(chargeFormItem.calculateDiscountedPrice());
            }

            if (getComposeType() == ComposeType.COMPOSE && org.apache.commons.collections.CollectionUtils.isNotEmpty(composeChildrenViews)) {
                long refundedCount = composeChildrenViews.stream().filter(subItem -> subItem.getStatus() == Constants.ChargeFormItemStatus.REFUNDED).count();
                if (refundedCount > 0) {
                    chargeFormItemView.setStatus(Constants.ChargeFormItemStatus.COMPOSE_SUB_REFUNDED);
                }
                if (composeChildrenViews.stream().anyMatch(it -> it.getIsShowTraceableCode() == Constants.ChargeFormItemShowTraceableCode.SHOW)) {
                    chargeFormItemView.setIsShowTraceableCode(Constants.ChargeFormItemShowTraceableCode.SHOW);
                }
            }
        } else if (getItemStatus() == Constants.ChargeFormItemStatus.REFUNDED && chargeFormItem.getIsAirPharmacy() == 1) {
            chargeFormItemView.setUnitCount(chargeFormItem.getUnitCount());
            chargeFormItemView.setDiscountPrice(chargeFormItem.getDiscountPrice());
            chargeFormItemView.setTotalPrice(chargeFormItem.getTotalPrice());
            chargeFormItemView.setDiscountedTotalPrice(MathUtils.wrapBigDecimalSubtract(chargeFormItem.calculateDiscountedPriceV2(), getRefundDiscountedTotalPrice()));
            chargeFormItemView.setAdjustmentPrice(getAdjustmentPrice());

            chargeFormItemView.setCanRefundDoseCount(doseCount);
            chargeFormItemView.setCanRefundUnitCount(getCanRefundUnitCount());
            chargeFormItemViews.add(chargeFormItemView);
        }
        ChargeUtils.updateChargeFormItemViewTraceCode(chargeFormItemViews);
        return chargeFormItemViews;
    }

    private BigDecimal getRefundDiscountedTotalPrice() {
        return doCalculateEachRefundFeeFuncCore(ChargeFormItem::calculateDiscountedPriceV2);
    }

    /**
     * 生成退费的批次列表
     *
     * @return
     */
    private List<ChargeFormItemBatchInfoView> generateRefundBatchInfoViews() {

        if (CollectionUtils.isEmpty(refundChargeFormItems)) {
            return new ArrayList<>();
        }

        return new ArrayList<>(refundChargeFormItems.stream()
                .filter(refundChargeFormItem -> org.apache.commons.collections.CollectionUtils.isNotEmpty(refundChargeFormItem.getChargeFormItemBatchInfos()))
                .flatMap(refundChargeFormItem -> refundChargeFormItem.getChargeFormItemBatchInfos().stream())
                .filter(chargeFormItemBatchInfo -> org.apache.commons.lang3.StringUtils.isNotEmpty(chargeFormItemBatchInfo.getAssociateItemBatchInfoId()))
                .collect(Collectors.toMap(ChargeFormItemBatchInfo::getAssociateItemBatchInfoId,
                        chargeItemBatchInfo -> {
                            ChargeFormItemBatchInfoView chargeItemBatchInfoView = ChargeFormItemBatchInfoView.toChargeItemBatchInfoView(chargeItemBatchInfo, null);
                            if (MapUtils.isNotEmpty(goodsBatchInfoSnapBatchIdMap)) {
                                GoodsBatchInfoSnap goodsBatchInfoSnap = goodsBatchInfoSnapBatchIdMap.get(chargeItemBatchInfoView.getBatchId());
                                if (goodsBatchInfoSnap != null) {
                                    chargeItemBatchInfoView.setExpiryDate(goodsBatchInfoSnap.getExpiryDate());
                                    chargeItemBatchInfoView.setBatchNo(goodsBatchInfoSnap.getBatchNo());
                                }
                                chargeItemBatchInfoView.setGoodsBatchInfoSnap(JsonUtils.dumpAsJsonNode(goodsBatchInfoSnap));
                            }
                            return chargeItemBatchInfoView;
                        },
                        (a, b) -> {
                            a.setUnitCount(MathUtils.wrapBigDecimalAdd(a.getUnitCount(), b.getUnitCount()));
                            a.setTotalPrice(MathUtils.wrapBigDecimalAdd(a.getTotalPrice(), b.getTotalPrice()));
                            return a;
                        }))
                .values()
        );
    }

    private boolean isLocalChinesePrescription() {
        return formSourceFormType == Constants.SourceFormType.PRESCRIPTION_CHINESE && formPharmacyType == GoodsConst.PharmacyType.LOCAL_PHARMACY;
    }

    private BigDecimal getLeftChargedDoseCount() {

        if (!isLocalChinesePrescription()) {
            return doseCount;
        }

        //中药处方的doseCount要视情况而定，先判断是否退完了，如果退完了，直接返回原doseCount
        if (MathUtils.calculateTotalCount(unitCount, doseCount).subtract(refundDoseCount.multiply(refundUnitCount)).compareTo(BigDecimal.ZERO) <= 0) {
            return doseCount;
        }

        //如果剂量一样，说明退的是uint
        if (doseCount.subtract(refundDoseCount).compareTo(BigDecimal.ZERO) <= 0) {
            return doseCount;
        }

        return doseCount.subtract(refundDoseCount);
    }

    private BigDecimal getCanRefundDeductCount() {

        BigDecimal deductedTotalCount = getDeductedTotalCount();

        if (MathUtils.wrapBigDecimalCompare(deductedTotalCount, BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }

        return deductedTotalCount.subtract(getRefundedDeductCount());
    }

    private BigDecimal getRefundedDeductCount() {
        return refundChargeFormItems.stream()
                .map(ChargeFormItem::getDeductTotalCount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public BigDecimal getDeductedTotalCount() {
        ChargeDiscountInfo promotionInfo = chargeFormItem.getPromotionInfo();

        if (promotionInfo == null || org.apache.commons.collections.CollectionUtils.isEmpty(promotionInfo.getDeductDiscountInfos())) {
            return null;
        }

        return promotionInfo.getDeductDiscountInfos().stream().map(deductDiscountInfo -> MathUtils.wrapBigDecimalOrZero(deductDiscountInfo.getDeductedCount())).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private int getIsOutOfStock() {
        if (getItemStatus() != Constants.ChargeFormItemStatus.UNCHARGED
                || (getProductType() != Constants.ProductType.MATERIAL
                && getProductType() != Constants.ProductType.MEDICINE
                && getProductType() != Constants.ProductType.SALE_PRODUCT
                && getProductType() != Constants.ProductType.COMPOSE_PRODUCT)
        ) {
            return 0;
        } else if (getProductType() == Constants.ProductType.COMPOSE_PRODUCT) {
            return composeChildren.stream().anyMatch(itemProcessor -> itemProcessor.getIsOutOfStock() == 1) ? 1 : 0;
        } else {
            return isStockAvailable ? 0 : 1;
        }
    }

    public List<ChargeFormItem> generateToSaveChargeFormItems() {
        List<ChargeFormItem> toSaveChargeFormItems = new ArrayList<>();
        toSaveChargeFormItems.add(chargeFormItem);
        //对于已退项目的保存，只有标记isSaveAddedRefundChargeItem为true时，才算真正入账，这样才保存addedRefundChargeItem
        toSaveChargeFormItems.addAll(refundChargeFormItems.stream()
                .filter(item -> (isSaveAddedRefundChargeItem || addedRefundChargeItem == null || !TextUtils.equals(item.getId(), addedRefundChargeItem.getId())))
                .collect(Collectors.toList())
        );

        if (isParent()) {
            toSaveChargeFormItems.addAll(composeChildren.stream()
                    .map(PharmacyItemProcessor::generateToSaveChargeFormItems)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList())
            );
        }
        return toSaveChargeFormItems;
    }

    public List<ChargeFormItem> getAffectedItems() {
        List<ChargeFormItem> allAffectedItems = new ArrayList<>();
        allAffectedItems.addAll(affectedItems);
        if (isParent()) {
            allAffectedItems.addAll(composeChildren.stream()
                    .map(PharmacyItemProcessor::getAffectedItems)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList())
            );
        }
        return allAffectedItems;
    }

    public List<ChargeFormItem> getRefundChargeFormItems() {
        List<ChargeFormItem> allRefundChargeFormItems = new ArrayList<>();
        allRefundChargeFormItems.addAll(refundChargeFormItems);
        if (isParent()) {
            allRefundChargeFormItems.addAll(composeChildren.stream()
                    .map(PharmacyItemProcessor::getRefundChargeFormItems)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList())
            );
        }
        return allRefundChargeFormItems;
    }

    public List<ChargeFormItem> getSelfRefundChargeFormItems() {
        return refundChargeFormItems;
    }

    public List<ChargeFormItem> getAddedRefundChargeItem() {
        List<ChargeFormItem> addedRefundChargeItems = new ArrayList<>();
        if (getComposeType() == ComposeType.COMPOSE) {
            addedRefundChargeItems = composeChildren.stream().map(PharmacyItemProcessor::getAddedRefundChargeItem).flatMap(Collection::stream).collect(Collectors.toList());
        } else {
            if (addedRefundChargeItem != null) {
                addedRefundChargeItems.add(addedRefundChargeItem);
            }
        }
        return addedRefundChargeItems;
    }

    public ChargeFormItem getSelfAddedRefundChargeItem() {
        return addedRefundChargeItem;
    }

    public ChargeFormItem getChargeFormItem() {
        return chargeFormItem;
    }

    public List<ChargeFormItem> getAllChargeFormItems() {
        List<ChargeFormItem> allChargeFormItems = new ArrayList<>();
        allChargeFormItems.add(chargeFormItem);

        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(composeChildren)) {
            allChargeFormItems.addAll(composeChildren.stream()
                    .flatMap(child -> child.getAllChargeFormItems().stream())
                    .collect(Collectors.toList())
            );
        }
        return allChargeFormItems;
    }

    public ChargeFormItemPrintView generatePrintView() {
        if (getItemStatus() != Constants.ChargeFormItemStatus.CHARGED) {
            return null;
        }

        if (refundType == Constants.ChargeFormItemRefundType.UNIT && MathUtils.wrapBigDecimalOrZero(unitCount).compareTo(MathUtils.wrapBigDecimalOrZero(refundUnitCount)) <= 0) {
            return null;
        }

        if (refundType == Constants.ChargeFormItemRefundType.DOSE && MathUtils.wrapBigDecimalOrZero(doseCount).compareTo(MathUtils.wrapBigDecimalOrZero(refundDoseCount)) <= 0) {
            return null;
        }

        if (chargeFormItem.getSourceItemType() == Constants.SourceItemType.SELF_PROVIDED) {
            return null;
        }

        //这儿看起来没有必要判断，只要是处方都打印
//        if (getPaySource() != Constants.ChargeSource.CHARGE && getPaySource() != Constants.ChargeSource.WE_CLINIC) {
//            return null;
//        }

        ChargeFormItemPrintView chargeFormItemPrintView = new ChargeFormItemPrintView();
        if (getProductType() == Constants.ProductType.REGISTRATION) {
            chargeFormItemPrintView.setName("诊费");
        } else {
            chargeFormItemPrintView.setName(chargeFormItem.getName());
        }

        BigDecimal netSingleDiscountFee = MathUtils.wrapBigDecimalAdd(chargeFormItem.calculateSinglePromotionPrice(), chargeFormItem.getUnitAdjustmentFee())
                .subtract(MathUtils.wrapBigDecimalAdd(getRefundSinglePromotionFee(), getRefundUnitAdjustmentFee()));
        BigDecimal netPackageDiscountFee = MathUtils.wrapBigDecimalAdd(chargeFormItem.calculatePackagePromotionPrice(), chargeFormItem.getAdjustmentPrice())
                .subtract(MathUtils.wrapBigDecimalAdd(getRefundPackagePromotionFee(), getRefundAdjustmentFee()));

        chargeFormItemPrintView.setId(chargeFormItem.getId());
        chargeFormItemPrintView.setCount(MathUtils.calculateTotalCount(unitCount, doseCount).subtract(refundDoseCount.multiply(refundUnitCount)));
        chargeFormItemPrintView.setUnitCount(MathUtils.wrapBigDecimalOrZero(unitCount));
        chargeFormItemPrintView.setDoseCount(MathUtils.wrapBigDecimal(doseCount, BigDecimal.ONE));

        chargeFormItemPrintView.setTotalPrice(MathUtils.wrapBigDecimalSubtract(chargeFormItem.getSourceTotalPrice(), getRefundSourceTotalFee())
                .add(MathUtils.max(BigDecimal.ZERO, netSingleDiscountFee))
                .add(MathUtils.max(BigDecimal.ZERO, netPackageDiscountFee)));

        chargeFormItemPrintView.setDiscountedPrice(getNetIncomePrice());
        chargeFormItemPrintView.setUnit(chargeFormItem.getUnit());
        chargeFormItemPrintView.setProductId(chargeFormItem.getProductId());
        chargeFormItemPrintView.setProductType(chargeFormItem.getProductType());
        chargeFormItemPrintView.setProductSubType(chargeFormItem.getProductSubType());
        chargeFormItemPrintView.setComposeType(chargeFormItem.getComposeType());
        chargeFormItemPrintView.setSourceItemType(chargeFormItem.getSourceItemType());
        chargeFormItemPrintView.setFeeTypeId(chargeFormItem.getFeeTypeId());
        chargeFormItemPrintView.setGoodsFeeType(chargeFormItem.getGoodsFeeType());
        chargeFormItemPrintView.setFeeComposeType(chargeFormItem.getFeeComposeType());
        if (chargeFormItem.getProductInfo() != null) {
            chargeFormItemPrintView.setProductInfo(chargeFormItem.getProductInfo());
        } else {
            chargeFormItemPrintView.setProductInfo(Optional.ofNullable(goodsItem).map(JsonUtils::dumpAsJsonNode).orElse(null));
        }

        BigDecimal totalCount = chargeFormItemPrintView.getCount();

        ChargeFormItemUtils.ChargeFormItemUnitPriceDto chargeFormItemUnitPriceDto = ChargeFormItemUtils.calculateItemUnitPriceAndDiscountedUnitPriceV1(chargeFormItem.getSourceUnitPrice(),
                totalCount,
                chargeFormItemPrintView.getTotalPrice(),
                chargeFormItemPrintView.getDiscountedPrice(),
                netSingleDiscountFee,
                netPackageDiscountFee,
                chargeFormItem.getProductType(),
                chargeFormItem.getProductSubType());

        chargeFormItemPrintView.setUnitPrice(chargeFormItemUnitPriceDto.getUnitPrice());
        chargeFormItemPrintView.setDiscountedUnitPrice(chargeFormItemUnitPriceDto.getDiscountedUnitPrice());

        if (!Constants.ProductType.SYSTEM_FEE_PRODUCT_TYPE.contains(getProductType())) {
            GoodsItem goodsItem = Optional.ofNullable(JsonUtils.readValue(chargeFormItem.getProductSnapshot(), GoodsItem.class)).orElse(this.goodsItem);
            if (goodsItem != null) {
                chargeFormItemPrintView.setPosition(goodsItem.getPosition());
                chargeFormItemPrintView.setDisplaySpec(goodsItem.getDisplaySpec());
                chargeFormItemPrintView.setCmSpec(goodsItem.getCMSpec());
            }
        }


        if (getProductType() == Constants.ProductType.MEDICINE && getProductSubType() == Constants.ProductType.SubType.MEDICINE_CHINESE) {
            UsageInfo usageInfo = JsonUtils.readValue(chargeFormItem.getUsageInfoJson(), UsageInfo.class);
            if (usageInfo != null && !TextUtils.isEmpty(usageInfo.getSpecialRequirement())) {
                chargeFormItemPrintView.setSpecialRequirement(usageInfo.getSpecialRequirement());
            }
        }

        if (getProductType() == Constants.ProductType.REGISTRATION || getProductType() == Constants.ProductType.TREATMENT || getProductType() == Constants.ProductType.EXAMINATION) {
            if (TextUtils.isEmpty(chargeFormItemPrintView.getUnit())) {
                chargeFormItemPrintView.setUnit("次");
            }
        }

        chargeFormItemPrintView.setType(ChargeFormItemUtils.generateChargeFormItemPrintType(chargeFormItem, goodsItem));
        chargeFormItemPrintView.setGoodsTypeId(getGoodsTypeId());

        if (isParent()) {
            chargeFormItemPrintView.setComposeChildren(composeChildren.stream()
                    .map(PharmacyItemProcessor::generatePrintView)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList()));
        } else if (isExaminationSplitPrint()) {
            chargeFormItemPrintView.setIsExaminationSplitPrint(1);
            chargeFormItemPrintView.setComposeChildren(generateExaminationCombineItemPrintView());
        }
        // 牙位
        if (chargeFormItem.getAdditional() != null && !CollectionUtils.isEmpty(chargeFormItem.getAdditional().getToothNos())) {
            chargeFormItemPrintView.setToothNos(chargeFormItem.getAdditional().getToothNos());
        }
        // 单项议价、单项优惠
        chargeFormItemPrintView.setUnitAdjustmentFee(MathUtils.wrapBigDecimalSubtract(chargeFormItem.getUnitAdjustmentFee(),
                getRefundUnitAdjustmentFee()));
        chargeFormItemPrintView.setSinglePromotionFee(MathUtils.wrapBigDecimalSubtract(chargeFormItem.calculateSinglePromotionPrice(), getRefundSinglePromotionFee()));
        chargeFormItemPrintView.setSinglePromotions(generateSinglePromotionView());

        return chargeFormItemPrintView;
    }

    private Integer getGoodsTypeId() {
        if (Objects.nonNull(goodsItem)) {
            return goodsItem.getTypeId();
        }
        return null;
    }

    public ChargeFormItemPrintView generateRefundPrintView() {
        List<ChargeFormItemPrintView> composeRefundItemChildren = composeChildren.stream()
                .map(PharmacyItemProcessor::generateRefundPrintView)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 如果是套餐，并且套餐子项退款item为空，return
        if (getComposeType() == ComposeType.COMPOSE) {
            if (CollectionUtils.isEmpty(composeRefundItemChildren)) {
                return null;
            }
        } else if (org.apache.commons.collections.CollectionUtils.isEmpty(refundChargeFormItems)) {
            return null;
        }

        if (chargeFormItem.getSourceItemType() == Constants.SourceItemType.SELF_PROVIDED) {
            return null;
        }

        ChargeFormItemPrintView chargeFormItemPrintView = new ChargeFormItemPrintView();
        if (getProductType() == Constants.ProductType.REGISTRATION) {
            chargeFormItemPrintView.setName("诊费");
        } else {
            chargeFormItemPrintView.setName(chargeFormItem.getName());
        }


        BigDecimal refundSingleDiscountFee = MathUtils.wrapBigDecimalAdd(getRefundSinglePromotionFee(), getRefundUnitAdjustmentFee());
        BigDecimal refundPackageDiscountFee = MathUtils.wrapBigDecimalAdd(getRefundPackagePromotionFee(), getRefundAdjustmentFee());
        BigDecimal refundTotalPrice = MathUtils.wrapBigDecimalOrZero(getRefundSourceTotalFee())
                .add(MathUtils.max(BigDecimal.ZERO, refundSingleDiscountFee))
                .add(MathUtils.max(BigDecimal.ZERO, refundPackageDiscountFee));

        chargeFormItemPrintView.setId(chargeFormItem.getId());
        chargeFormItemPrintView.setCount(MathUtils.wrapBigDecimalOrZero(refundUnitCount).multiply(refundDoseCount));
        chargeFormItemPrintView.setUnitCount(MathUtils.wrapBigDecimalOrZero(refundUnitCount));
        chargeFormItemPrintView.setDoseCount(refundDoseCount);
        //产品需求，退费单打印时需要打印真正的折后金额，但是单价又要求是正值，且和收费单打印的单价算法一致
        chargeFormItemPrintView.setTotalPrice(getRefundDiscountedTotalPrice().negate());
        chargeFormItemPrintView.setDiscountedPrice(getRefundDiscountedTotalPrice().negate());
        chargeFormItemPrintView.setUnit(chargeFormItem.getUnit());
        chargeFormItemPrintView.setProductId(chargeFormItem.getProductId());
        chargeFormItemPrintView.setProductType(chargeFormItem.getProductType());
        chargeFormItemPrintView.setProductSubType(chargeFormItem.getProductSubType());
        chargeFormItemPrintView.setComposeType(chargeFormItem.getComposeType());
        chargeFormItemPrintView.setSourceItemType(chargeFormItem.getSourceItemType());
        chargeFormItemPrintView.setFeeTypeId(chargeFormItem.getFeeTypeId());
        chargeFormItemPrintView.setGoodsFeeType(chargeFormItem.getGoodsFeeType());
        chargeFormItemPrintView.setFeeComposeType(chargeFormItem.getFeeComposeType());
        if (chargeFormItem.getProductInfo() != null) {
            chargeFormItemPrintView.setProductInfo(chargeFormItem.getProductInfo());
        } else {
            chargeFormItemPrintView.setProductInfo(Optional.ofNullable(goodsItem).map(JsonUtils::dumpAsJsonNode).orElse(null));
        }
        // todo 药店暂时无退费小票打印费用类型名称需求
//        chargeFormItemPrintView.setFeeTypeName(Optional.ofNullable(goodsFeeTypeSortDto).map(GoodsFeeTypeConstants.GoodsFeeTypeSortDto::getName).orElse(null));

        BigDecimal totalCount = MathUtils.calculateTotalCount(chargeFormItemPrintView.getUnitCount(), chargeFormItemPrintView.getDoseCount());

        ChargeFormItemUtils.ChargeFormItemUnitPriceDto chargeFormItemUnitPriceDto = ChargeFormItemUtils.calculateItemUnitPriceAndDiscountedUnitPriceV1(chargeFormItem.getSourceUnitPrice(),
                totalCount,
                refundTotalPrice,
                chargeFormItemPrintView.getDiscountedPrice().negate(),
                refundSingleDiscountFee.negate(),
                refundPackageDiscountFee.negate(),
                chargeFormItem.getProductType(),
                chargeFormItem.getProductSubType());

        chargeFormItemPrintView.setUnitPrice(chargeFormItemUnitPriceDto.getUnitPrice());
        chargeFormItemPrintView.setDiscountedUnitPrice(chargeFormItemUnitPriceDto.getDiscountedUnitPrice().negate());

        if (getProductType() == Constants.ProductType.MEDICINE || getProductType() == Constants.ProductType.MATERIAL) {
            GoodsItem goodsItem = Optional.ofNullable(JsonUtils.readValue(chargeFormItem.getProductSnapshot(), GoodsItem.class)).orElse(this.goodsItem);
            if (goodsItem != null) {
                chargeFormItemPrintView.setPosition(goodsItem.getPosition());
                chargeFormItemPrintView.setDisplaySpec(goodsItem.getDisplaySpec());
            }
        }


        if (getProductType() == Constants.ProductType.MEDICINE && getProductSubType() == Constants.ProductType.SubType.MEDICINE_CHINESE) {
            UsageInfo usageInfo = JsonUtils.readValue(chargeFormItem.getUsageInfoJson(), UsageInfo.class);
            if (usageInfo != null && !TextUtils.isEmpty(usageInfo.getSpecialRequirement())) {
                chargeFormItemPrintView.setSpecialRequirement(usageInfo.getSpecialRequirement());
            }
        }

        if (getProductType() == Constants.ProductType.REGISTRATION || getProductType() == Constants.ProductType.TREATMENT || getProductType() == Constants.ProductType.EXAMINATION) {
            if (TextUtils.isEmpty(chargeFormItemPrintView.getUnit())) {
                chargeFormItemPrintView.setUnit("次");
            }
        }

        chargeFormItemPrintView.setType(ChargeFormItemUtils.generateChargeFormItemPrintType(chargeFormItem, goodsItem));

        if (isParent()) {
            chargeFormItemPrintView.setComposeChildren(composeChildren.stream()
                    .map(PharmacyItemProcessor::generateRefundPrintView)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList()));
        } else if (isExaminationSplitPrint()) {
            chargeFormItemPrintView.setIsExaminationSplitPrint(1);
            chargeFormItemPrintView.setComposeChildren(generateRefundExaminationCombineItemPrintView(chargeFormItemPrintView));
        }
        chargeFormItemPrintView.setSinglePromotionFee(getRefundSinglePromotionFee());
        chargeFormItemPrintView.setUnitAdjustmentFee(getRefundUnitAdjustmentFee());
        chargeFormItemPrintView.setSinglePromotions(generateSinglePromotionView());

        return chargeFormItemPrintView;
    }

    public List<SinglePromotionView> generateSinglePromotionView() {
        return Optional.ofNullable(chargeFormItem.getAdditional())
                .map(ChargeFormItemAdditional::getSinglePromotions)
                .orElse(new ArrayList<>())
                .stream()
                .map(itemSinglePromotion -> {
                    SinglePromotionView singlePromotionView = new SinglePromotionView();
                    BeanUtils.copyProperties(itemSinglePromotion, singlePromotionView);
                    return singlePromotionView;
                })
                .collect(Collectors.toList());
    }

    private List<ChargeFormItemPrintView> generateExaminationCombineItemPrintView() {

        GoodsItem goodsItem = JsonUtils.readValue(chargeFormItem.getProductSnapshot(), GoodsItem.class);

        List<GoodsItem> examinationGoodsChildren = goodsItem.getChildren();
        BigDecimal discountedTotalPrice = chargeFormItem.calculateDiscountedPrice();
        BigDecimal discountedUnitPrice = discountedTotalPrice.divide(chargeFormItem.getUnitCount(), 4, RoundingMode.DOWN);
        AtomicReference<BigDecimal> oddPrice = new AtomicReference<>(discountedTotalPrice.subtract(discountedUnitPrice.multiply(chargeFormItem.getUnitCount())));

        List<InvoiceDetailItemExtend> invoiceDetailItemExtends = ChargeSheetNuoNuoInvoiceService.generateInvoiceDetailItemExtendsAndFlatPrice(chargeFormItem, examinationGoodsChildren, discountedUnitPrice, null);

        if (org.apache.commons.collections.CollectionUtils.isEmpty(invoiceDetailItemExtends)) {
            return new ArrayList<>();
        }

        List<ChargeFormItemPrintView> chargeFormItemPrintViews = invoiceDetailItemExtends
                .stream()
                .map(invoiceDetailItemExtend -> {


                    ChargeFormItemPrintView chargeFormItemPrintView = new ChargeFormItemPrintView();
                    chargeFormItemPrintView.setName(invoiceDetailItemExtend.getGoodsName());
                    chargeFormItemPrintView.setId(UUIDUtils.randomUUID());
                    chargeFormItemPrintView.setCount(invoiceDetailItemExtend.getNum());
                    chargeFormItemPrintView.setUnitCount(invoiceDetailItemExtend.getGoodsItem().getComposePackageCount());
                    chargeFormItemPrintView.setDoseCount(MathUtils.wrapBigDecimal(doseCount, BigDecimal.ONE));
                    chargeFormItemPrintView.setTotalPrice(invoiceDetailItemExtend.getUnitPrice().multiply(invoiceDetailItemExtend.getNum()));
                    chargeFormItemPrintView.setDiscountedPrice(invoiceDetailItemExtend.getUnitPrice().multiply(invoiceDetailItemExtend.getNum()));
                    chargeFormItemPrintView.setUnit(invoiceDetailItemExtend.getUnit());
                    chargeFormItemPrintView.setProductId(invoiceDetailItemExtend.getGoodsItem().getId());
                    chargeFormItemPrintView.setProductType(invoiceDetailItemExtend.getGoodsItem().getType());
                    chargeFormItemPrintView.setProductSubType(invoiceDetailItemExtend.getGoodsItem().getSubType());
                    chargeFormItemPrintView.setComposeType(ComposeType.COMPOSE_SUB_ITEM);
                    Optional.ofNullable(invoiceDetailItemExtend.getGoodsItem()).ifPresent(g -> {
                        chargeFormItemPrintView.setMedicalFeeGrade(g.getMedicalFeeGrade());
                        chargeFormItemPrintView.setOwnExpenseRatio(g.getOwnExpenseRatio());
                    });

                    //单项议总价时可能产生零头，如果出现零头，就把零头算在第一个子项上
                    if (MathUtils.wrapBigDecimalCompare(oddPrice.get(), BigDecimal.ZERO) != 0) {
                        chargeFormItemPrintView.setTotalPrice(MathUtils.wrapBigDecimalAdd(chargeFormItemPrintView.getTotalPrice(), oddPrice.get()));
                        chargeFormItemPrintView.setDiscountedPrice(MathUtils.wrapBigDecimalAdd(chargeFormItemPrintView.getDiscountedPrice(), oddPrice.get()));
                        oddPrice.set(BigDecimal.ZERO);
                    }

                    //计算单价和折扣后的单价
                    ChargeFormItemUtils.ChargeFormItemUnitPriceDto chargeFormItemUnitPriceDto = ChargeFormItemUtils.calculateItemUnitPriceAndDiscountedUnitPrice(invoiceDetailItemExtend.getUnitPrice(),
                            chargeFormItemPrintView.getCount(),
                            chargeFormItemPrintView.getTotalPrice(),
                            chargeFormItemPrintView.getDiscountedPrice(),
                            BigDecimal.ZERO,
                            chargeFormItemPrintView.getProductType(),
                            chargeFormItemPrintView.getProductSubType());

                    chargeFormItemPrintView.setUnitPrice(chargeFormItemUnitPriceDto.getUnitPrice());
                    chargeFormItemPrintView.setDiscountedUnitPrice(chargeFormItemUnitPriceDto.getDiscountedUnitPrice());

                    return chargeFormItemPrintView;
                }).collect(Collectors.toList());

        return chargeFormItemPrintViews;
    }

    private List<ChargeFormItemPrintView> generateRefundExaminationCombineItemPrintView(ChargeFormItemPrintView
                                                                                                combineItemPrintView) {

        GoodsItem goodsItem = JsonUtils.readValue(chargeFormItem.getProductSnapshot(), GoodsItem.class);

        List<GoodsItem> examinationGoodsChildren = goodsItem.getChildren();
        BigDecimal discountedTotalPrice = combineItemPrintView.getDiscountedPrice();
        BigDecimal discountedUnitPrice = discountedTotalPrice.divide(chargeFormItem.getUnitCount(), 4, RoundingMode.DOWN);
        BigDecimal oddPrice = discountedTotalPrice.subtract(discountedUnitPrice.multiply(chargeFormItem.getUnitCount()));

        List<InvoiceDetailItemExtend> invoiceDetailItemExtends = ChargeSheetNuoNuoInvoiceService.generateRefundInvoiceDetailItemExtendsAndFlatPrice(combineItemPrintView.getCount(), examinationGoodsChildren, discountedUnitPrice, null);

        if (org.apache.commons.collections.CollectionUtils.isEmpty(invoiceDetailItemExtends)) {
            return new ArrayList<>();
        }

        List<ChargeFormItemPrintView> chargeFormItemPrintViews = invoiceDetailItemExtends.stream().map(invoiceDetailItemExtend -> {
            ChargeFormItemPrintView chargeFormItemPrintView = new ChargeFormItemPrintView();
            chargeFormItemPrintView.setName(invoiceDetailItemExtend.getGoodsName());
            chargeFormItemPrintView.setId(UUIDUtils.randomUUID());
            chargeFormItemPrintView.setCount(invoiceDetailItemExtend.getNum());
            chargeFormItemPrintView.setUnitCount(invoiceDetailItemExtend.getGoodsItem().getComposePackageCount());
            chargeFormItemPrintView.setDoseCount(MathUtils.wrapBigDecimal(doseCount, BigDecimal.ONE));
            chargeFormItemPrintView.setTotalPrice(invoiceDetailItemExtend.getUnitPrice().multiply(invoiceDetailItemExtend.getNum()));
            chargeFormItemPrintView.setDiscountedPrice(invoiceDetailItemExtend.getUnitPrice().multiply(invoiceDetailItemExtend.getNum()));
            chargeFormItemPrintView.setUnit(invoiceDetailItemExtend.getUnit());
            chargeFormItemPrintView.setProductId(invoiceDetailItemExtend.getGoodsItem().getId());
            chargeFormItemPrintView.setProductType(invoiceDetailItemExtend.getGoodsItem().getType());
            chargeFormItemPrintView.setProductSubType(invoiceDetailItemExtend.getGoodsItem().getSubType());
            chargeFormItemPrintView.setComposeType(ComposeType.COMPOSE_SUB_ITEM);
            Optional.ofNullable(invoiceDetailItemExtend.getGoodsItem()).ifPresent(g -> {
                chargeFormItemPrintView.setMedicalFeeGrade(g.getMedicalFeeGrade());
                chargeFormItemPrintView.setOwnExpenseRatio(g.getOwnExpenseRatio());
            });
            return chargeFormItemPrintView;
        }).collect(Collectors.toList());

        //如果出现零头，就把零头算在第一个子项上
        if (MathUtils.wrapBigDecimalCompare(oddPrice, BigDecimal.ZERO) != 0) {
            ChargeFormItemPrintView firstChargeFormItemPrintView = chargeFormItemPrintViews.get(0);
            firstChargeFormItemPrintView.setTotalPrice(MathUtils.wrapBigDecimalAdd(firstChargeFormItemPrintView.getTotalPrice(), oddPrice));
            firstChargeFormItemPrintView.setDiscountedPrice(MathUtils.wrapBigDecimalAdd(firstChargeFormItemPrintView.getDiscountedPrice(), oddPrice));
        }

        return chargeFormItemPrintViews;
    }

    private boolean isExaminationSplitPrint() {

        if (getProductType() != Constants.ProductType.EXAMINATION) {
            return false;
        }

        GoodsItem goodsItem = JsonUtils.readValue(chargeFormItem.getProductSnapshot(), GoodsItem.class);
        if (goodsItem == null) {
            return false;
        }

        return org.apache.commons.lang3.StringUtils.isEmpty(goodsItem.getShebaoCode()) && goodsItem.getCombineType() == GoodsConst.GoodsCombine.COMBINE;
    }


    private void addAffectedItems(ChargeFormItem chargeFormItem) {

//        if(chargeFormItem.getIsGift() == 1) {
//            return;
//        }

        affectedItems.removeIf(item -> TextUtils.equals(item.getId(), chargeFormItem.getId()));
        affectedItems.add(chargeFormItem);
    }

    public void resetReceivedPrice() {
        if (isParent()) {
            composeChildren.forEach(PharmacyItemProcessor::resetReceivedPrice);
        }

        chargeFormItem.setReceivedPrice(BigDecimal.ZERO);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(itemBatchInfoProcessors)) {
            itemBatchInfoProcessors.forEach(ItemBatchInfoProcessor::resetReceivedPrice);
        }
    }

    public void refundForGiftRule(String operatorId) {
        if (isParent()) {
            composeChildren.forEach(itemProcessor -> itemProcessor.doRefundForGiftRule(operatorId));
        } else {
            doRefundForGiftRule(operatorId);
        }
    }

    private void doRefundForGiftRule(String operatorId) {
        if (addedRefundChargeItem == null) {
            return;
        }

        chargeFormItem.setRefundUnitCount(chargeFormItem.getUnitCount());
        chargeFormItem.setV2Status(Constants.ChargeFormItemStatus.REFUNDED);
        isSaveAddedRefundChargeItem = true;
        addAffectedItems(addedRefundChargeItem);
        FillUtils.fillLastModifiedBy(chargeFormItem, operatorId);
    }

    public void updateRefundInfoForGiftRule(String operatorId) {
        if (getComposeType() == ComposeType.COMPOSE) {
            composeChildren.forEach(itemProcessor -> itemProcessor.doUpdateRefundInfoForGiftRule(operatorId));
        } else {
            doUpdateRefundInfoForGiftRule(operatorId);
        }
    }

    private void doUpdateRefundInfoForGiftRule(String operatorId) {

        if (getItemStatus() != Constants.ChargeFormItemStatus.CHARGED || unitCount.compareTo(BigDecimal.ZERO) <= 0 || getIsGift() != 1) {
            return;
        }
        addedRefundChargeItem = new ChargeFormItem();
        BeanUtils.copyProperties(chargeFormItem, addedRefundChargeItem);
        addedRefundChargeItem.setId(AbcIdUtils.getUUID());
        addedRefundChargeItem.setStatus(Constants.ChargeFormItemStatus.REFUNDED);
        addedRefundChargeItem.setV2Status(Constants.ChargeFormItemStatus.NONE);
        addedRefundChargeItem.setAssociateFormItemId(chargeFormItem.getId());
        addedRefundChargeItem.setRefundUnitCount(BigDecimal.ZERO);
        addedRefundChargeItem.setRefundDiscountPrice(BigDecimal.ZERO);
        addedRefundChargeItem.setRefundTotalPrice(BigDecimal.ZERO);
        addedRefundChargeItem.setChargeFormItemBatchInfos(new ArrayList<>());
        if (!CollectionUtils.isEmpty(addedRefundChargeItem.getChargeFormItemBatchInfos())) {
            chargeFormItem.getChargeFormItemBatchInfos().forEach(chargeFormItemBatchInfo -> {

                ChargeFormItemBatchInfo refundChargeFormItemBatchInfo = new ChargeFormItemBatchInfo();
                BeanUtils.copyProperties(chargeFormItemBatchInfo, refundChargeFormItemBatchInfo);
                refundChargeFormItemBatchInfo.setId(AbcIdUtils.getUUID());
                refundChargeFormItemBatchInfo.setChargeFormItemId(addedRefundChargeItem.getId());
                chargeFormItemBatchInfo.setRefundUnitCount(chargeFormItemBatchInfo.getUnitCount());
                chargeFormItemBatchInfo.setRefundTotalPrice(chargeFormItemBatchInfo.getTotalPrice());

                addedRefundChargeItem.getChargeFormItemBatchInfos().add(refundChargeFormItemBatchInfo);
            });
        }

        FillUtils.fillCreatedBy(addedRefundChargeItem, operatorId);

        addRefundChargeFormItem(addedRefundChargeItem);
    }

    public void updateProcessSourceUnitPriceAndProductName(BigDecimal autoUnitPrice, String productName, Long
            feeTypeId) {
        updateAutoUnitPrice(autoUnitPrice, feeTypeId);
        chargeFormItem.setName(productName);
        JsonNode productInfo = chargeFormItem.getProductInfo();
        if (Objects.nonNull(productInfo)) {
            ((ObjectNode) productInfo).put("decoctionFee", autoUnitPrice);
            chargeFormItem.setProductSnapshot(JsonUtils.dump(productInfo));
        }
    }

    public void updateProcessProductSubType(int subType) {
        chargeFormItem.setProductSubType(subType);
    }

    public void updateAutoUnitPrice(BigDecimal autoUnitPrice, Long feeTypeId) {
//        ExpectedPriceHelper.processProcessFeeOrDeliveryFee(chargeFormItem, chargeFormItem.getSourceUnitPrice(), chargeFormItem.getExpectedUnitPrice(), chargeFormItem.getExpectedTotalPrice(), autoUnitPrice);
        ExpectedPriceHelper.process(chargeFormItem, autoUnitPrice, chargeFormItem.getExpectedUnitPrice(), chargeFormItem.getExpectedTotalPrice(), chargeFormItem.getExpectedTotalPriceRatio());
        chargeFormItem.setSourceUnitPrice(autoUnitPrice);
        BigDecimal sourceTotalPrice = MathUtils.calculateTotalPrice(autoUnitPrice, unitCount, doseCount, 2);
        chargeFormItem.setSourceTotalPrice(sourceTotalPrice);
        chargeFormItem.setFeeTypeId(feeTypeId);
        ExpectedPriceHelper.process(chargeFormItem,
                chargeFormItem.getSourceUnitPrice(),
                chargeFormItem.getSourceTotalPrice(),
                null,
                chargeFormItem.getExpectedUnitPrice(),
                chargeFormItem.getExpectedTotalPrice(),
                chargeFormItem.getExpectedTotalPriceRatio());
    }

    public void updateDeliveryPriceAutoUnitPrice(BigDecimal autoUnitPrice, Long feeTypeId) {
        updateAutoUnitPrice(autoUnitPrice, feeTypeId);
        JsonNode productInfo = chargeFormItem.getProductInfo();
        if (Objects.nonNull(productInfo)) {
            ((ObjectNode) productInfo).put("deliveryFee", autoUnitPrice);
            chargeFormItem.setProductSnapshot(JsonUtils.dump(productInfo));
        }
    }

    public void updateAirPharmacyProcessPrice(BigDecimal processPrice, OrderProcessRuleView airPharmacyProcessRule) {
        chargeFormItem.setUnitPrice(processPrice);
        chargeFormItem.setSourceUnitPrice(processPrice);
        if (airPharmacyProcessRule != null) {
            chargeFormItem.setProductInfo(JsonUtils.dumpAsJsonNode(airPharmacyProcessRule));
            chargeFormItem.setProductSnapshot(JsonUtils.dump(airPharmacyProcessRule));
        }
        chargeFormItem.calculateTotalPriceV2();
        BigDecimal sourceTotalPrice = MathUtils.calculateTotalPrice(processPrice, unitCount, doseCount, 2);
        chargeFormItem.setSourceTotalPrice(sourceTotalPrice);
        ExpectedPriceHelper.process(chargeFormItem,
                chargeFormItem.getSourceUnitPrice(),
                chargeFormItem.getSourceTotalPrice(),
                oldSourceUnitPrice,
                chargeFormItem.getExpectedUnitPrice(),
                chargeFormItem.getExpectedTotalPrice(),
                chargeFormItem.getExpectedTotalPriceRatio());
    }

    public void updateAirPharmacyDeliveryPrice(BigDecimal deliveryPrice) {
        chargeFormItem.setUnitPrice(deliveryPrice);
        chargeFormItem.setSourceUnitPrice(deliveryPrice);
        chargeFormItem.calculateTotalPriceV2();
        BigDecimal sourceTotalPrice = MathUtils.calculateTotalPrice(deliveryPrice, unitCount, doseCount, 2);
        chargeFormItem.setSourceTotalPrice(sourceTotalPrice);
        ExpectedPriceHelper.process(chargeFormItem,
                chargeFormItem.getSourceUnitPrice(),
                chargeFormItem.getSourceTotalPrice(),
                oldSourceUnitPrice,
                chargeFormItem.getExpectedUnitPrice(),
                chargeFormItem.getExpectedTotalPrice(),
                chargeFormItem.getExpectedTotalPriceRatio());
    }

    public void updateAirPharmacyIngredientPrice(BigDecimal ingredientPrice, OrderRuleIngredientView ingredientView) {
        chargeFormItem.setUnitPrice(MathUtils.wrapBigDecimalOrZero(ingredientPrice));
        chargeFormItem.setSourceUnitPrice(MathUtils.wrapBigDecimalOrZero(ingredientPrice));
        if (ingredientView != null) {
            chargeFormItem.setProductInfo(JsonUtils.dumpAsJsonNode(ingredientView));
            chargeFormItem.setProductSnapshot(JsonUtils.dump(ingredientView));
        }
        chargeFormItem.calculateTotalPriceV2();
        BigDecimal sourceTotalPrice = MathUtils.calculateTotalPrice(ingredientPrice, unitCount, doseCount, 2);
        chargeFormItem.setSourceTotalPrice(sourceTotalPrice);
        ExpectedPriceHelper.process(chargeFormItem,
                chargeFormItem.getSourceUnitPrice(),
                chargeFormItem.getSourceTotalPrice(),
                oldSourceUnitPrice,
                chargeFormItem.getExpectedUnitPrice(),
                chargeFormItem.getExpectedTotalPrice(),
                chargeFormItem.getExpectedTotalPriceRatio());
    }

    public void refundAirPharmacyItem(String operatorId) {
        if (chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.CHARGED) {
            return;
        }

        chargeFormItem.setRefundTotalPrice(chargeFormItem.getTotalPrice());
        chargeFormItem.setRefundDiscountPrice(chargeFormItem.getDiscountPrice());
        chargeFormItem.setRefundUnitCount(chargeFormItem.getUnitCount());
        chargeFormItem.setStatus(Constants.ChargeFormItemStatus.REFUNDED);
        if (chargeFormItem.getRefundUnitCount().compareTo(chargeFormItem.getUnitCount()) >= 0) {
            chargeFormItem.setV2Status(Constants.ChargeFormItemStatus.REFUNDED);
        }
        FillUtils.fillLastModifiedBy(chargeFormItem, operatorId);
    }

    /**
     * 控制这个chargeFromItem是否能打印到打印单上
     * true 能打印
     * 更好的抽象应该是是把chrageFormItem抽象成子类，由成员函数来控制自己
     */
    public boolean enablePrintToClient(int ownChargeFormSourceType) {
        if (ownChargeFormSourceType == Constants.SourceFormType.PRESCRIPTION_WESTERN ||
                ownChargeFormSourceType == Constants.SourceFormType.PRESCRIPTION_INFUSION ||
                ownChargeFormSourceType == Constants.SourceFormType.PRESCRIPTION_CHINESE ||
                ownChargeFormSourceType == Constants.SourceFormType.PRESCRIPTION_EXTERNAL ||
                ownChargeFormSourceType == Constants.SourceFormType.GIFT_PRODUCT) {
            return true;
        }

        //空中药房的打印条件，空中药房的chargeform里面有快递和加工非，要剖掉
        if (ownChargeFormSourceType == Constants.SourceFormType.AIR_PHARMACY
                && (this.getProductType() != Constants.ProductType.EXPRESS_DELIVERY
                && this.getProductType() != Constants.ProductType.PROCESS)) {
            return true;
        }

        if ((ownChargeFormSourceType == Constants.SourceFormType.ADDITIONAL_SALE_PRODUCT_FORM
                || ownChargeFormSourceType == Constants.SourceFormType.ADDITIONAL_FORM)
                && this.getProductType() == Constants.ProductType.MEDICINE) {
            return true;
        }
        return false;

    }

    public void setItemStatusUnselected() {
        chargeFormItem.setStatus(Constants.ChargeFormItemStatus.UNSELECTED);
    }

    public void setProductIdAndPriceNull() {
//        chargeFormItem.setProductId("");
        isLackGoodsItem = true;
        chargeFormItem.setUnitPrice(BigDecimal.ZERO);
        chargeFormItem.setExpectedUnitPrice(null);
        chargeFormItem.setTotalPrice(BigDecimal.ZERO);
        chargeFormItem.setExpectedTotalPrice(null);
        chargeFormItem.setExpectedTotalPriceRatio(null);
    }

    public BigDecimal getDiscountedPrice() {
        return chargeFormItem.calculateDiscountedPriceV2();
    }

    public UsageInfo getUsageInfo() {
        return Optional.ofNullable(chargeFormItem.getUsageInfo()).map(u -> JsonUtils.readValue(u, UsageInfo.class)).orElse(null);
    }

    public String getSourceFormItemId() {
        return chargeFormItem.getSourceFormItemId();
    }

    public ChargePayItemInfo generatePayItem(boolean isShebaoPayMode) {

        BigDecimal receivableFee = isShebaoPayMode ? sheBaoReceivableFee : this.receivableFee;

        ChargePayItemInfo chargePayItemInfo = new ChargePayItemInfo().setId(chargeFormItem.getId())
                .setName(chargeFormItem.getName())
                .setGoodsId(chargeFormItem.getProductId())
                .setGoodsType(chargeFormItem.getProductType())
                .setGoodsSubType(chargeFormItem.getProductSubType())
                .setGoodsCMSpec(getGoodsCMSpec())
                .setPharmacyType(chargeFormItem.getPharmacyType())
                .setComposeType(chargeFormItem.getComposeType())
                .setGoodsFeeType(chargeFormItem.getGoodsFeeType())
                .setReceivableFee(MathUtils.wrapBigDecimalOrZero(receivableFee))
                .setThisTimeReceivableFee(MathUtils.wrapBigDecimalOrZero(receivableFee));

        if (isParent()) {
            chargePayItemInfo.setChildren(composeChildren.stream()
                    .map(itemProcessor -> itemProcessor.generatePayItem(isShebaoPayMode))
                    .collect(Collectors.toList()));
        }

        /**
         * 如果有批次则要创建 payItemBatchInfos
         */
        if (!CollectionUtils.isEmpty(itemBatchInfoProcessors)) {
            chargePayItemInfo.setPayItemBatchInfos(itemBatchInfoProcessors.stream().map(itemBatchInfoProcessor -> itemBatchInfoProcessor.generatePayItemBatchInfo(isShebaoPayMode)).collect(Collectors.toList()));
        }

        return chargePayItemInfo;
    }

    public void updateReceivedPrice(ChargePayItemInfo chargePayItemInfo, boolean isAddAffectedItem, Set<String> thisTimeHealthCardAffectedIds) {

        if (chargePayItemInfo == null) {
            return;
        }

        BigDecimal thisTimeReceivableFee = chargePayItemInfo.getThisTimeReceivableFee();

        updateReceivedPriceCore(thisTimeReceivableFee, chargePayItemInfo.getPayItemBatchInfos(), isAddAffectedItem, thisTimeHealthCardAffectedIds.contains(chargeFormItem.getId()));

        if (isParent() && org.apache.commons.collections.CollectionUtils.isNotEmpty(chargePayItemInfo.getChildren())) {
            Map<String, ChargePayItemInfo> childChargePayItemInfoMap = ListUtils.toMap(chargePayItemInfo.getChildren(), ChargePayItemInfo::getId);
            composeChildren.forEach(itemProcessor -> itemProcessor.updateReceivedPrice(childChargePayItemInfoMap.get(itemProcessor.getItemId()), isAddAffectedItem, thisTimeHealthCardAffectedIds));
        }
    }

    public void updateReceivedPriceCore(BigDecimal thisTimeReceivableFee, List<ChargePayItemBatchInfo> payItemBatchInfos, boolean isAddAffectedItem, boolean needMarkHealthPaidItem) {

        chargeFormItem.setReceivedPrice(MathUtils.wrapBigDecimalAdd(chargeFormItem.getReceivedPrice(), thisTimeReceivableFee));
        chargeFormItem.setThisTimeReceivableFee(thisTimeReceivableFee);
        if (getComposeType() != ComposeType.COMPOSE && needMarkHealthPaidItem) {
            ChargeDiscountInfo promotionInfo = chargeFormItem.getPromotionInfo();
            promotionInfo = promotionInfo != null ? promotionInfo : new ChargeDiscountInfo();
            promotionInfo.setIsMarkedByHealthCardPay(1);
            chargeFormItem.setPromotionInfo(promotionInfo);
            chargeFormItem.setPromotionInfoJson(JsonUtils.dump(promotionInfo));
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(itemBatchInfoProcessors)) {
                itemBatchInfoProcessors.forEach(ItemBatchInfoProcessor::markHealthCardPaid);
            }
        }

        if (isAddAffectedItem) {
            addAffectedItems(chargeFormItem);
        }

        /**
         * 如果含有批次处理批次的实收
         */
        if (!CollectionUtils.isEmpty(itemBatchInfoProcessors) && !CollectionUtils.isEmpty(payItemBatchInfos)) {
            Map<String, ChargePayItemBatchInfo> payItemBatchInfoMap = ListUtils.toMap(payItemBatchInfos, ChargePayItemBatchInfo::getId);
            itemBatchInfoProcessors.forEach(itemBatchInfoProcessor -> {
                ChargePayItemBatchInfo chargePayItemBatchInfo = payItemBatchInfoMap.get(itemBatchInfoProcessor.getItemBatchInfoId());

                if (Objects.isNull(chargePayItemBatchInfo)) {
                    return;
                }

                itemBatchInfoProcessor.updateReceivedPrice(chargePayItemBatchInfo.getThisTimeReceivableFee());
            });
        }

    }

    public GoodsBaseInfo generateGoodsBaseInfo() {
        GoodsBaseInfo goodsBaseInfo = new GoodsBaseInfo();
        goodsBaseInfo.setGoodsId(getProductId());
        goodsBaseInfo.setGoodsType(getProductType());
        goodsBaseInfo.setGoodsSubType(getProductSubType());
        goodsBaseInfo.setCustomTypeId(Optional.ofNullable(goodsItem).map(GoodsItem::getCustomTypeId).orElse(null));
        if (GoodsConst.GoodsType.DECOCT_FEE == getProductType()) {
            goodsBaseInfo.setGoodsCMSpec(ProcessProduct.getProcessProductCmSpec(getProductId()));
        } else {
            goodsBaseInfo.setGoodsCMSpec(getGoodsCMSpec());
        }

        if (chargeFormItem.getProductType() == Constants.ProductType.REGISTRATION) {
            Optional.ofNullable(JsonUtils.readValue(chargeFormItem.getProductSnapshot(), RegistrationFormItem.class))
                    .ifPresent(registrationFormItem -> {
                        if (org.apache.commons.lang3.StringUtils.isNotBlank(registrationFormItem.getDoctorId())) {
                            goodsBaseInfo.setEmployeeId(registrationFormItem.getDoctorId());
                        }
                        if (org.apache.commons.lang3.StringUtils.isNotBlank(registrationFormItem.getDepartmentId())) {
                            goodsBaseInfo.setDepartmentId(registrationFormItem.getDepartmentId());
                        }
                    });
        } else if (chargeFormItem.getProductType() == Constants.ProductType.ONLINE_CONSULTATION) {
            Optional.ofNullable(JsonUtils.readValue(chargeFormItem.getProductSnapshot(), OnlineConsultationItemInfo.class))
                    .ifPresent(itemInfo -> {
                        goodsBaseInfo.setEmployeeId(itemInfo.getDoctorId());
                        goodsBaseInfo.setClinicId(itemInfo.getClinicId());
                    });
        }

        goodsBaseInfo.setPharmacyType(formPharmacyType);
        return goodsBaseInfo;
    }

    public FlatPriceHelper.FlatPriceCell generateFlatPriceCellForRefreshReceivedFee() {

        FlatPriceHelper.FlatPriceCell flatPriceCell = new FlatPriceHelper.FlatPriceCell();
        flatPriceCell.setId(getItemId());
        flatPriceCell.setPriority(FlatPriceHelper.PRIORITY_HIGH);
        flatPriceCell.setTotalPrice(chargeFormItem.getTotalPrice().add(chargeFormItem.getDiscountPrice()));
        return flatPriceCell;
    }

    public void addGiftGoodsAndAllDeductGoodsInAffectedItemsAndZeroPriceItem() {
        //赠品
        if (getIsGift() == 1) {
            addAffectedItems(chargeFormItem);
            return;
        }

        if (chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.CHARGED) {
            return;
        }

        BigDecimal deductedTotalCount = Optional.ofNullable(chargeFormItem.getPromotionInfo())
                .map(ChargeDiscountInfo::getDeductDiscountInfos)
                .orElse(new ArrayList<>())
                .stream()
                .map(deductDiscountInfo -> MathUtils.wrapBigDecimalOrZero(deductDiscountInfo.getDeductedCount()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        //全部抵扣的商品
        if (MathUtils.wrapBigDecimalCompare(deductedTotalCount, BigDecimal.ZERO) != 0
                && MathUtils.wrapBigDecimalCompare(deductedTotalCount, MathUtils.calculateTotalCount(chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount())) == 0
        ) {
            addAffectedItems(chargeFormItem);
            return;
        }

        //0元项目也一起加进去
        if (MathUtils.wrapBigDecimalCompare(receivableFee, BigDecimal.ZERO) == 0) {
            addAffectedItems(chargeFormItem);
        }

        if (getComposeType() == ComposeType.COMPOSE) {
            composeChildren.forEach(PharmacyItemProcessor::addGiftGoodsAndAllDeductGoodsInAffectedItemsAndZeroPriceItem);
        }
    }

    public List<StatRecordAffectedDeductedItem> generateStatRecordAffectedDeductedItem() {

        if (MathUtils.wrapBigDecimalCompare(chargeFormItem.getDeductTotalCount(), BigDecimal.ZERO) <= 0) {
            return new ArrayList<>();
        }

        List<StatRecordAffectedDeductedItem> result = new ArrayList<>();
        result.add(new StatRecordAffectedDeductedItem()
                .setId(chargeFormItem.getId())
                .setCount(chargeFormItem.getDeductTotalCount())
                .setUnitPrice(chargeFormItem.getUnitPrice())
                .setDeductedUnitAdjustmentFee(Optional.ofNullable(chargeFormItem.getPromotionInfo())
                        .map(ChargeDiscountInfo::getDeductedUnitAdjustmentFee)
                        .orElse(BigDecimal.ZERO)
                )
                .setTotalPrice(Optional.ofNullable(chargeFormItem.getPromotionInfo())
                        .map(ChargeDiscountInfo::getDeductPromotionPrice)
                        .orElse(BigDecimal.ZERO)
                )
        );

        if (isParent()) {
            result.addAll(composeChildren.stream()
                    .flatMap(itemProcessor -> itemProcessor.generateStatRecordAffectedDeductedItem().stream())
                    .collect(Collectors.toList()));
        }

        return result;
    }

    public List<StatRecordAffectedDeductedItem> getStatRecordAffectedDeductedItem() {
        List<StatRecordAffectedDeductedItem> affectedDeductItems = new ArrayList<>();
        if (Objects.nonNull(affectedDeductItem)) {
            affectedDeductItems.add(affectedDeductItem);
        }
        if (isParent()) {
            affectedDeductItems.addAll(composeChildren.stream()
                    .flatMap(itemProcessor -> itemProcessor.getStatRecordAffectedDeductedItem().stream())
                    .collect(Collectors.toList()));
        }
        return affectedDeductItems;
    }

    public void updateReceivedFeeForRefund(Map<String, RefundUpdateReceivedItemTemp> chargeFormItemRecordIdMap) {

        if (isParent()) {
            composeChildren.forEach(itemProcessor -> itemProcessor.updateReceivedFeeForRefund(chargeFormItemRecordIdMap));
        }

        RefundUpdateReceivedItemTemp refundUpdateReceivedItemTemp = chargeFormItemRecordIdMap.get(chargeFormItem.getId());
        if (Objects.nonNull(refundUpdateReceivedItemTemp)) {
            chargeFormItem.setReceivedPrice(MathUtils.max(BigDecimal.ZERO, MathUtils.wrapBigDecimalSubtract(chargeFormItem.getReceivedPrice(), refundUpdateReceivedItemTemp.getThisRefundFee())));
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(itemBatchInfoProcessors)
                    && org.apache.commons.collections.CollectionUtils.isNotEmpty(refundUpdateReceivedItemTemp.getBatchTempList())) {
                Map<String, RefundUpdateReceivedBatchTemp> refundBatchInfoIdMap = ListUtils.toMap(refundUpdateReceivedItemTemp.getBatchTempList(), RefundUpdateReceivedBatchTemp::getId);
                itemBatchInfoProcessors.forEach(itemBatchInfoProcessor -> itemBatchInfoProcessor.updateReceivedFeeForRefund(refundBatchInfoIdMap.get(itemBatchInfoProcessor.getItemBatchInfoId())));
            }
        }

    }

    public boolean isAllDeduct() {
        return MathUtils.wrapBigDecimalOrZero(chargeFormItem.getDeductTotalCount())
                .compareTo(MathUtils.calculateTotalCount(chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount())) == 0;
    }

    public void updateAirPharmacyItemExpectedTotalPrice(BigDecimal adjustmentFee) {
        BigDecimal totalPrice = MathUtils.wrapBigDecimalAdd(MathUtils.calculateTotalPrice(chargeFormItem.getUnitPrice(), chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount(), 2), chargeFormItem.getFractionPrice());
        BigDecimal expectedTotalPrice = totalPrice.add(MathUtils.wrapBigDecimalOrZero(adjustmentFee));
        chargeFormItem.setExpectedTotalPrice(expectedTotalPrice);
        chargeFormItem.setExpectedTotalPriceRatio(null);
        ExpectedPriceHelper.process(chargeFormItem, chargeFormItem.getSourceUnitPrice(), chargeFormItem.getExpectedUnitPrice(), chargeFormItem.getExpectedTotalPrice(), chargeFormItem.getExpectedTotalPriceRatio());
        chargeFormItem.calculateDiscountedPrice();
    }

    public void clearAirPharmacyItemAdjustmentAndPromotion() {
        chargeFormItem.setExpectedTotalPrice(chargeFormItem.getDoctorSourceTotalPrice());
        chargeFormItem.setAdjustmentPrice(BigDecimal.ZERO);
        chargeFormItem.setDiscountPrice(BigDecimal.ZERO);
        chargeFormItem.setPromotionPrice(BigDecimal.ZERO);
        chargeFormItem.setExpectedTotalPriceRatio(null);
        chargeFormItem.setDeductTotalCount(BigDecimal.ZERO);
        chargeFormItem.setVerifyTotalCount(BigDecimal.ZERO);
        chargeFormItem.setPromotionInfo(null);
        chargeFormItem.setPromotionInfoJson(null);
        ExpectedPriceHelper.process(chargeFormItem, chargeFormItem.getSourceUnitPrice(), chargeFormItem.getExpectedUnitPrice(), chargeFormItem.getExpectedTotalPrice(), chargeFormItem.getExpectedTotalPriceRatio());
        chargeFormItem.calculateDiscountedPrice();
    }

    public void updateChargeFormItemOldSourceUnitPrice() {
        if (Constants.ProductType.SYSTEM_FEE_PRODUCT_TYPE.contains(chargeFormItem.getProductType())) {
            return;
        }

        if (isParent()) {
            composeChildren.forEach(PharmacyItemProcessor::updateChargeFormItemOldSourceUnitPrice);
        }

        oldSourceUnitPrice = chargeFormItem.getSourceUnitPrice();
    }

    /**
     * 根据加工费类型->更新加工费id
     *
     * @param type    {@link ChargeRuleProcessUsageType#type}
     * @param subType {@link ChargeRuleProcessUsageType#subType}
     */
    public void updateProcessProductId(int type, int subType) {
        String processProductId = ProcessProduct.getProcessProductId(type, subType);
        if (org.apache.commons.lang3.StringUtils.isBlank(processProductId)) {
            return;
        }
        chargeFormItem.setProductId(processProductId);
    }

    public int getPharmacyType() {
        return formPharmacyType;
    }

    /**
     * 是否被社保刷卡支付过
     *
     * @return
     */
    public boolean isMarkedByHealthCardPay() {
        return Optional.ofNullable(chargeFormItem.getPromotionInfo())
                .map(ChargeDiscountInfo::getIsMarkedByHealthCardPay)
                .orElse(0) == 1;

    }

    /**
     * 判断item是否退过费
     *
     * @return
     */
    public boolean checkItemIsNotRefund() {
        return chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED && org.apache.commons.collections.CollectionUtils.isEmpty(refundChargeFormItems);
    }

    public void updateDefaultPharmacyNoForCopyByOutpatient(GoodsPharmacyBaseView defaultPharmacy) {

        if (Objects.isNull(defaultPharmacy)) {
            return;
        }

        updatePharmacyTypeAndPharmacyNo(defaultPharmacy.getType(), defaultPharmacy.getNo());

        if (Objects.isNull(goodsItem)) {
            return;
        }

        if (goodsItem.getPharmacyNo() == defaultPharmacy.getNo()) {
            return;
        }

        List<GoodsItem.PharmacyGoodsStock> pharmacyGoodsStockList = goodsItem.getPharmacyGoodsStockList();

        GoodsItem.PharmacyGoodsStock defaultPharmacyGoodsStock = Optional.ofNullable(pharmacyGoodsStockList)
                .orElse(new ArrayList<>())
                .stream()
                .filter(pharmacyGoodsStock -> pharmacyGoodsStock.getPharmacyNo() == defaultPharmacy.getNo())
                .findFirst()
                .orElse(null);

        if (defaultPharmacyGoodsStock == null) {
            goodsItem.setPharmacyNo(defaultPharmacy.getNo());
            goodsItem.setPharmacyName(defaultPharmacy.getName());
            goodsItem.setStockPieceCount(BigDecimal.ZERO);
            goodsItem.setStockPackageCount(BigDecimal.ZERO);
        } else {
            goodsItem.setPharmacyNo(defaultPharmacyGoodsStock.getPharmacyNo());
            goodsItem.setPharmacyName(defaultPharmacyGoodsStock.getPharmacyName());
            goodsItem.setStockPackageCount(MathUtils.wrapBigDecimalOrZero(defaultPharmacyGoodsStock.getStockPackageCount()));
            goodsItem.setStockPieceCount(MathUtils.wrapBigDecimalOrZero(defaultPharmacyGoodsStock.getStockPieceCount()));
        }
    }

    public void updatePharmacyTypeAndPharmacyNo(int pharmacyType, int pharmacyNo) {
        chargeFormItem.setPharmacyType(pharmacyType);
        chargeFormItem.setPharmacyNo(pharmacyNo);
    }

    public void updateChargeFormItemExecutedCount(Map<String, ChargeExecuteItem> chargeFormItemExecutedCountMap) {

        if (getComposeType() == ComposeType.COMPOSE) {
            composeChildren.forEach(itemProcessor -> itemProcessor.updateChargeFormItemExecutedCount(chargeFormItemExecutedCountMap));
        }
        BigDecimal executedCount = Optional.ofNullable(chargeFormItemExecutedCountMap.getOrDefault(getItemId(), null))
                .map(ChargeExecuteItem::getExecutedCount)
                .orElse(null);

        if (executedCount == null) {
            return;
        }

        if (formSourceFormType == Constants.SourceFormType.PRESCRIPTION_INFUSION || formSourceFormType == Constants.SourceFormType.PRESCRIPTION_WESTERN) {
            return;
        }

        this.executedCount = executedCount;
    }

    public void setGoodsPharmacyView(Map<Integer, GoodsPharmacyView> goodsPharmacyViewMap) {
        if (MapUtils.isEmpty(goodsPharmacyViewMap)) {
            return;
        }
        goodsPharmacyView = goodsPharmacyViewMap.get(getPharmacyNo());
    }

    public void updatePreBatchInfo(GoodsLockingFormItem goodsLockingFormItem) {
        if (goodsLockingFormItem != null && !CollectionUtils.isEmpty(goodsLockingFormItem.getGoodsLockBatchItemList())) {
            this.setPreLockBatchInfo(goodsLockingFormItem.getGoodsLockBatchItemList());
        } else {
            List<ChargeFormItemBatchInfo> chargeFormItemBatchInfos = chargeFormItem.getChargeFormItemBatchInfos();
            if (CollectionUtils.isEmpty(chargeFormItemBatchInfos)) {
                return;
            }
            List<GoodsLockBatchItem> goodsLockBatchItems = chargeFormItemBatchInfos.stream().map(this::ofChargeFormItemBatchInfo).collect(Collectors.toList());
            this.setPreLockBatchInfo(goodsLockBatchItems);
        }
    }

    private GoodsLockBatchItem ofChargeFormItemBatchInfo(ChargeFormItemBatchInfo batchInfo) {

        if (batchInfo == null) {
            return null;
        }

        GoodsLockBatchItem goodsLockBatchItem = new GoodsLockBatchItem();
        goodsLockBatchItem.setBatchId(StringUtils.isEmpty(batchInfo.getBatchId()) ? null : Long.parseLong(batchInfo.getBatchId()));
        goodsLockBatchItem.setBatchNo(batchInfo.getBatchNo());
        goodsLockBatchItem.setLockFormItemId(chargeFormItem.getId());
        goodsLockBatchItem.setGoodsId(chargeFormItem.getProductId());
        goodsLockBatchItem.setExpiryDate(batchInfo.getExpiryDate());
        goodsLockBatchItem.setGoodsName(chargeFormItem.getName());

        if (chargeFormItem.getUseDismounting() == 1) {
            goodsLockBatchItem.setLockingBatchPieceCount(batchInfo.getUnitCount());
        } else {
            goodsLockBatchItem.setLockingBatchPackageCount(batchInfo.getUnitCount());
        }

        goodsLockBatchItem.setRefId(chargeFormItem.getId());
        goodsLockBatchItem.setUnitCostPrice(batchInfo.getUnitCostPrice());
        return goodsLockBatchItem;
    }

    public CalculateLimitPriceItem generateCalculateLimitPriceItem() {

        return generateCalculateLimitPriceItemCore();
    }

    public CalculateLimitPriceItem generateCalculateLimitPriceItemCore() {

        if (chargeFormItem.getStatus() != ChargeConstants.ChargeFormItemStatus.UNCHARGED) {
            return null;
        }

        //只返回需要计算限价的收费项目：中药、西成药、医用材料、检查检验、治疗项目、理疗项目
        if (chargeFormItem.isTopItem()
                && getProductType() != Constants.ProductType.MEDICINE
                && getProductType() != Constants.ProductType.MATERIAL
                && getProductType() != Constants.ProductType.EXAMINATION
                && getProductType() != Constants.ProductType.TREATMENT
                && getProductType() != Constants.ProductType.COMPOSE_PRODUCT
                && getProductType() != Constants.ProductType.OTHER_FEE
                && getProductType() != Constants.ProductType.REGISTRATION) {
            return null;
        }

        if (getProductType() != Constants.ProductType.REGISTRATION && Objects.isNull(goodsItem)) {
            return null;
        }

        CalculateLimitPriceItem calculateLimitPriceItem = new CalculateLimitPriceItem();
        calculateLimitPriceItem.setId(chargeFormItem.getId());
        calculateLimitPriceItem.setReceivableUnitPrice(ChargeFormItemUtils.calculateReceivableUnitFee(receivableFee,
                chargeFormItem.getUnitPrice(),
                chargeFormItem.getUnitCount(),
                chargeFormItem.getDoseCount(),
                chargeFormItem.getDeductTotalCount()));
        calculateLimitPriceItem.setReceivableTotalPrice(receivableFee);
        calculateLimitPriceItem.setProductId(chargeFormItem.getProductId());
        calculateLimitPriceItem.setProductType(chargeFormItem.getProductType());
        calculateLimitPriceItem.setProductSubType(chargeFormItem.getProductSubType());
        calculateLimitPriceItem.setUseDismounting(chargeFormItem.getUseDismounting());
        calculateLimitPriceItem.setUnitCount(chargeFormItem.getUnitCount());
        calculateLimitPriceItem.setDoseCount(chargeFormItem.getDoseCount());
        Integer shebaoPayType = Optional.ofNullable(JsonUtils.readValue(chargeFormItem.getUsageInfo(), UsageInfo.class)).map(UsageInfo::getPayType).orElse(ChargeConstants.ChargeFormItemPayType.SHEBAO_PERSONAL_PAYMENT);

        calculateLimitPriceItem.setCanPayByHealthCard(ChargeGoodsItemUtils.isCanPayByHealthCard(goodsItem, shebaoPayType));
        //生成批次计算限价对象
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(itemBatchInfoProcessors)) {
            calculateLimitPriceItem.setBatchInfos(itemBatchInfoProcessors.stream()
                    .map(batchInfo -> batchInfo.generateCalculateLimitPriceItemBatchInfo(chargeFormItem.getUseDismounting()))
                    .collect(Collectors.toList()));
        }
        if (getComposeType() == ComposeType.COMPOSE_SUB_ITEM) {
            calculateLimitPriceItem.setGoodsPiecePrice(goodsItem.getComposePiecePrice());
            calculateLimitPriceItem.setGoodsPackagePrice(goodsItem.getComposePackagePrice());
            calculateLimitPriceItem.setGoodsLimitPriceInfo(GoodsLimitPriceInfo.createGoodsLimitPriceInfo(goodsItem.getShebaoPayLimitPriceInfo(), chargeFormItem.getUseDismounting()));
        } else if (chargeFormItem.getProductType() == Constants.ProductType.REGISTRATION) {
            calculateLimitPriceItem.setGoodsPackagePrice(chargeFormItem.getUnitCostPrice());
        } else {
            calculateLimitPriceItem.setGoodsPiecePrice(goodsItem.getPiecePrice());
            calculateLimitPriceItem.setGoodsPackagePrice(goodsItem.getPackagePrice());
            calculateLimitPriceItem.setGoodsLimitPriceInfo(GoodsLimitPriceInfo.createGoodsLimitPriceInfo(goodsItem.getShebaoPayLimitPriceInfo(), chargeFormItem.getUseDismounting()));
        }
        calculateLimitPriceItem.setUnitCostPrice(chargeFormItem.getUnitCostPrice());

        if (isParent()) {

            if (chargeFormItem.getGoodsFeeType() == GoodsFeeType.FEE_PARENT) {
                calculateLimitPriceItem.setIsFeeParent(YesOrNo.YES);
            }
            if (chargeFormItem.getComposeType() == ComposeType.COMPOSE) {
                calculateLimitPriceItem.setIsCompose(YesOrNo.YES);
            }

            calculateLimitPriceItem.setComposeChildren(composeChildren.stream().map(PharmacyItemProcessor::generateCalculateLimitPriceItemCore).filter(Objects::nonNull).collect(Collectors.toList()));
        }

        return calculateLimitPriceItem;
    }

    public void applyItemLimitPrice(Map<String, CalculateLimitPriceItem> calculateLimitPriceItemMap) {

        if (chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.UNCHARGED) {
            return;
        }

        CalculateLimitPriceItem calculateLimitPriceItem = calculateLimitPriceItemMap.get(this.chargeFormItem.getId());

        if (Objects.isNull(calculateLimitPriceItem) || !calculateLimitPriceItem.isLimited()) {
            return;
        }

        if (isParent()) {

            applyParentItemLimitPrice(calculateLimitPriceItem, false);

        } else {
            applyNormalChildItemLimitPrice(calculateLimitPriceItem, false);
        }

    }

    private void clearPromotionInfoAndRecalculateLimit(CalculateLimitPriceItem limitPriceItem) {
        /**
         * 强制清空优惠的情况下 只清空优惠不修改unitPrice 和 totalPrice  如果被限价了会在下面的代码中处理限价价格
         */
        clearPromotionAndAdjustmentFee();
        limitPriceItem.setReceivableTotalPrice(chargeFormItem.getTotalPrice());
        limitPriceItem.setReceivableUnitPrice(chargeFormItem.getUnitPrice());
        this.receivableFee = chargeFormItem.getTotalPrice();

        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(itemBatchInfoProcessors)) {
            itemBatchInfoProcessors.forEach(itemBatchInfoProcessor -> {
                ChargeFormItemBatchInfo chargeFormItemBatchInfo = itemBatchInfoProcessor.getChargeFormItemBatchInfo();
                Optional.ofNullable(chargeFormItemBatchInfo.getPromotionInfo()).ifPresent(ChargeDiscountInfo::clearPromotionAndAdjustmentFee);

                chargeFormItemBatchInfo.calculateTotalPrice();
                chargeFormItemBatchInfo.calculateReceivableFee();
                chargeFormItemBatchInfo.setUnitPrice(chargeFormItemBatchInfo.getSourceUnitPrice());
            });

            /**
             * 清空批次的优惠后重新处理批次的限价
             */
            if (!CollectionUtils.isEmpty(limitPriceItem.getBatchInfos())) {

                Map<String, CalculateLimitPriceItem.CalculateLimitPriceItemBatchInfo> calLimitBatchMap = ListUtils.toMap(limitPriceItem.getBatchInfos(), CalculateLimitPriceItem.CalculateLimitPriceItemBatchInfo::getId);

                itemBatchInfoProcessors.forEach(itemBatchInfoProcessor -> {
                    CalculateLimitPriceItem.CalculateLimitPriceItemBatchInfo itemBatchInfo = calLimitBatchMap.get(itemBatchInfoProcessor.getItemBatchInfoId());

                    if (Objects.isNull(itemBatchInfo)) {
                        return;
                    }


                    itemBatchInfo.setReceivableTotalPrice(itemBatchInfoProcessor.getChargeFormItemBatchInfo().calculateReceivableFee());
                    if (MathUtils.wrapBigDecimalCompare(itemBatchInfoProcessor.getChargeFormItemBatchInfo().getUnitPrice(), itemBatchInfo.getUpperLimitUnitPrice()) > 0 && Objects.nonNull(itemBatchInfo.getLimitInfo())) {
                        itemBatchInfo.markIsLimited(itemBatchInfo.getUpperLimitUnitPrice(), itemBatchInfo.getLimitInfo().getExceedLimitPriceRule(), itemBatchInfo.getLimitInfo());
                        itemBatchInfo.setSheBaoReceivablePrice(itemBatchInfo.getLimitTotalPrice());
                    }

                });
            }

            /**
             * 如果有批次被限价则 item标记限价
             */
            Optional<CalculateLimitPriceItem.CalculateLimitPriceItemBatchInfo> limitPriceItemBatchInfo = limitPriceItem.getBatchInfos().stream().filter(limitPriceBatch -> limitPriceBatch.isLimited()).findFirst();
            if (limitPriceItemBatchInfo.isPresent()) {

                /**
                 * 计算批次限价
                 */
                LimitPriceProcessor.calculateLimitPriceFormBatchRule(limitPriceItemBatchInfo.get().getExceedLimitPriceRule(), limitPriceItem);

            }


        } else {
            /**
             * 清空优惠后重新处理限价信息
             */
            if (MathUtils.wrapBigDecimalCompare(chargeFormItem.getUnitPrice(), limitPriceItem.getUpperLimitUnitPrice()) > 0 && Objects.nonNull(limitPriceItem.getLimitInfo())) {
                limitPriceItem.markIsLimited(limitPriceItem.getUpperLimitUnitPrice(), limitPriceItem.getLimitInfo().getExceedLimitPriceRule(), limitPriceItem.getLimitInfo());
            }
        }

    }

    public void applyParentItemLimitPrice(CalculateLimitPriceItem calculateLimitPriceItemParent, boolean needForceClearPromotio) {

        /**
         * 如果母项限价对象为空则直接返回
         */
        if (Objects.isNull(calculateLimitPriceItemParent) || CollectionUtils.isEmpty(calculateLimitPriceItemParent.getComposeChildren())) {
            return;
        }

        /**
         * 先判断子项中有没有限价不转自费的情况如果有则强制清空优惠
         */
        if (calculateLimitPriceItemParent.getComposeChildren().stream().anyMatch(item -> item.isLimited() && item.getExceedLimitPriceRule() != null
                && item.getExceedLimitPriceRule() == Constants.ExceedLimitPriceRule.NO_PAY)) {
            clearPromotionAndAdjustmentFee(null, null, null);
            needForceClearPromotio = true;
        }

        Map<String, CalculateLimitPriceItem> childCalculateLimitPriceItemMap = ListUtils.toMap(calculateLimitPriceItemParent.getComposeChildren(), CalculateLimitPriceItem::getId);


        boolean finalNeedForceClearPromotio = needForceClearPromotio;
        composeChildren.forEach(composeChild -> {
            CalculateLimitPriceItem calculateLimitPriceItemChild = childCalculateLimitPriceItemMap.get(composeChild.getItemId());
            composeChild.applyChildItemLimitPrice(calculateLimitPriceItemChild, finalNeedForceClearPromotio);
        });

        //套餐里面如果子项限价，需要计算套餐的限价金额，同时也需要计算套餐的社保应收
        this.sheBaoReceivableFee = getSheBaoReceivableFee();
        /**
         * 这里也会重新计算应收  套餐中含有费用项 费用项子项可能都没被限价，但是可能会被清空优惠，所以这里也要重新计算应收
         */
        this.receivableFee = composeChildren.stream()
                .map(PharmacyItemProcessor::getReceivableFee)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal composeTotalPrice = composeChildren.stream()
                .map(itemProcessor -> itemProcessor.getChargeFormItem().getTotalPrice())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        chargeFormItem.setTotalPrice(composeTotalPrice);

        boolean existedChildLimitPrice = composeChildren.stream()
                .anyMatch(itemProcessor -> itemProcessor.getCalIsUseLimitPrice() == 1);

        if (existedChildLimitPrice) {
            setCalIsUseLimitPrice(1);
            BigDecimal totalCount = MathUtils.calculateTotalCount(chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount());

            /**
             * 这里只有套餐需要计算成本价
             */
            if (getComposeType() == ComposeType.COMPOSE) {
                //计算item成本价
                if (MathUtils.wrapBigDecimalCompare(totalCount, BigDecimal.ZERO) > 0) {
                    BigDecimal totalCostPrice = composeChildren
                            .stream()
                            .map(itemProcessor -> {
                                ChargeFormItem chargeFormItem = itemProcessor.getChargeFormItem();
                                return MathUtils.calculateTotalPrice(chargeFormItem.getUnitCostPrice(), chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount());
                            }).reduce(BigDecimal.ZERO, BigDecimal::add);
                    chargeFormItem.setUnitCostPrice(totalCostPrice.divide(totalCount).setScale(4, RoundingMode.DOWN)); //保留四位小数向下取整
                    chargeFormItem.setTotalCostPrice(totalCostPrice);
                }
            }


            MathUtils.CalculateExpectedUnitPriceResult result = MathUtils.calculateExpectedUnitPrice(chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount(), composeTotalPrice);
            chargeFormItem.setUnitPrice(result.expectedUnitPrice);

            chargeFormItem.setFractionPrice(result.fractionPrice);

            chargeFormItem.calculateTotalPrice();


            if (MathUtils.wrapBigDecimalCompare(this.receivableFee, chargeFormItem.calculateDiscountedPrice()) != 0) {

            }

        }
    }


    public void applyNormalChildItemLimitPrice(CalculateLimitPriceItem limitPriceItem, boolean needForceClearPromotion) {
        if (Objects.isNull(limitPriceItem)) {
            return;
        }

        setReceivableFee(limitPriceItem.calculateReceivableTotalPrice());
        setSheBaoReceivableFee(limitPriceItem.getSheBaoReceivablePrice());


        /**
         * 如果需要强制清空优惠 清空item优惠的同时也要清空批次优惠
         */
        if (needForceClearPromotion) {
            clearPromotionInfoAndRecalculateLimit(limitPriceItem);
        }


        ChargeFormItemFactory.insertChargeFormItemAdditionalIfNeed(chargeFormItem);
        chargeFormItem.getAdditional().setLimitInfo(limitPriceItem.getLimitInfo());

        if (!limitPriceItem.isLimited()) {

            return;
        }

        setCalIsUseLimitPrice(1);


        if (Objects.isNull(chargeFormItem.getPromotionInfo())) {
            ChargeDiscountInfo chargeDiscountInfo = new ChargeDiscountInfo();
            chargeFormItem.setPromotionInfo(chargeDiscountInfo);
        }

        chargeFormItem.getPromotionInfo().setLimitFee(limitPriceItem.getLimitInfo().getLimitFee());


        // 超过限价部分不收费，限价来自item本身，直接更新
        if (limitPriceItem.getExceedLimitPriceRule() != null
                && limitPriceItem.getExceedLimitPriceRule() == Constants.ExceedLimitPriceRule.NO_PAY) {

            clearPromotionAndAdjustmentFee(limitPriceItem.getLimitUnitPrice(), limitPriceItem.getLimitTotalPrice(), limitPriceItem.getLimitFractionPrice());

            if (limitPriceItem.getLimitPriceFromWay() == CalculateLimitPriceItem.LimitPriceFromWay.BATCH) {
                //按批次限价时，直接覆盖批次上的限价金额
                Map<String, CalculateLimitPriceItem.CalculateLimitPriceItemBatchInfo> itemBatchInfoIdMap = ListUtils.toMap(Optional.ofNullable(limitPriceItem.getBatchInfos()).orElse(new ArrayList<>()), CalculateLimitPriceItem.CalculateLimitPriceItemBatchInfo::getId);
                itemBatchInfoProcessors.forEach(itemBatchInfoProcessor -> itemBatchInfoProcessor.applyLimitPrice(itemBatchInfoIdMap.get(itemBatchInfoProcessor.getItemBatchInfoId())));
            }

        } else if (org.apache.commons.collections.CollectionUtils.isNotEmpty(itemBatchInfoProcessors)) {

            /**
             * 限价转自费且含有批次才会走到这
             */

            //更新批次的社保应收
            Map<String, CalculateLimitPriceItem.CalculateLimitPriceItemBatchInfo> calculateLimitPriceItemBatchInfoMap = ListUtils.toMap(Optional.ofNullable(limitPriceItem.getBatchInfos()).orElse(new ArrayList<>()), CalculateLimitPriceItem.CalculateLimitPriceItemBatchInfo::getId);
            itemBatchInfoProcessors.forEach(itemBatchInfoProcessor -> {
                CalculateLimitPriceItem.CalculateLimitPriceItemBatchInfo itemBatchInfo = calculateLimitPriceItemBatchInfoMap.get(itemBatchInfoProcessor.getItemBatchInfoId());
                BigDecimal batchInfoSheBaoReceivableTotalPrice = Optional.ofNullable(itemBatchInfo)
                        .map(CalculateLimitPriceItem.CalculateLimitPriceItemBatchInfo::getSheBaoReceivablePrice)
                        .orElse(BigDecimal.ZERO);
                itemBatchInfoProcessor.setItemBatchInfoLimit(batchInfoSheBaoReceivableTotalPrice);
                itemBatchInfoProcessor.setIsUseLimitPrice(Optional.ofNullable(itemBatchInfo).map(BaseLimitPriceInfo::isLimited).orElse(false) ? YesOrNo.YES : YesOrNo.NO);
                itemBatchInfoProcessor.applyLimitPrice(itemBatchInfo);


            });
        }
    }

    public void applyChildItemLimitPrice(CalculateLimitPriceItem limitPriceItem, boolean needForceClearPromotio) {
        if (Objects.isNull(limitPriceItem)) {
            return;
        }

        if (getGoodsFeeType() == GoodsFeeType.FEE_PARENT) {
            //applyGoodsFeeParentLimitPrice(limitPriceItem, calculateLimitPriceItemParent);
            applyParentItemLimitPrice(limitPriceItem, needForceClearPromotio);
            return;
        }

        applyNormalChildItemLimitPrice(limitPriceItem, needForceClearPromotio);

    }


    private void clearPromotionAndAdjustmentFee(BigDecimal limitUnitPrice, BigDecimal limitTotalPrice, BigDecimal limitFractionPrice) {


        chargeFormItem.setUnitPrice(limitUnitPrice);


        chargeFormItem.setTotalPrice(limitTotalPrice);


        chargeFormItem.setFractionPrice(limitFractionPrice);


        clearPromotionAndAdjustmentFee();
    }

    private void clearPromotionAndAdjustmentFee() {

        chargeFormItem.setUnitAdjustmentFee(null);
        chargeFormItem.setDeductTotalCount(BigDecimal.ZERO);
        chargeFormItem.setVerifyTotalCount(BigDecimal.ZERO);
        chargeFormItem.setPromotionInfo(null);
        chargeFormItem.setPromotionInfoJson(null);
        chargeFormItem.setAdjustmentPrice(BigDecimal.ZERO);
        chargeFormItem.setDiscountPrice(BigDecimal.ZERO);
        chargeFormItem.setPromotionPrice(BigDecimal.ZERO);

        chargeFormItem.calculateTotalPrice();

    }

    private void updateGoodsFeeChildrenShebaoReceivableFee() {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(composeChildren)) {
            return;
        }

        List<FlatReceivedPriceHelper.FlatCell> cells = composeChildren.stream()
                .map(child -> new FlatReceivedPriceHelper.FlatCell()
                        .setId(child.getItemId())
                        .setName(child.getChargeFormItem().getName())
                        .setTotalPrice(child.getChargeFormItem().getTotalPrice())
                        .setMaxFlatPrice(child.getReceivableFee())
                ).collect(Collectors.toList());

        FlatReceivedPriceHelper flatReceivedPriceHelper = new FlatReceivedPriceHelper(sheBaoReceivableFee);
        flatReceivedPriceHelper.flat(cells);

        Map<String, FlatReceivedPriceHelper.FlatCell> cellMap = ListUtils.toMap(cells, FlatReceivedPriceHelper.FlatCell::getId);
        composeChildren.forEach(child -> {
            FlatReceivedPriceHelper.FlatCell flatCell = cellMap.get(child.getItemId());
            if (Objects.nonNull(flatCell)) {
                child.setCalIsUseLimitPrice(1);
                child.setSheBaoReceivableFee(flatCell.getFlatPrice());
            }
        });
    }

    private void updateGoodsFeeChildrenReceivableFee() {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(composeChildren)) {
            return;
        }

        List<FlatReceivedPriceHelper.FlatCell> cells = composeChildren.stream()
                .map(child -> new FlatReceivedPriceHelper.FlatCell()
                        .setId(child.getItemId())
                        .setName(child.getChargeFormItem().getName())
                        .setTotalPrice(child.getChargeFormItem().getTotalPrice())
                        .setMaxFlatPrice(child.getReceivableFee())
                ).collect(Collectors.toList());

        FlatReceivedPriceHelper flatReceivedPriceHelper = new FlatReceivedPriceHelper(receivableFee);
        flatReceivedPriceHelper.flat(cells);

        Map<String, FlatReceivedPriceHelper.FlatCell> cellMap = ListUtils.toMap(cells, FlatReceivedPriceHelper.FlatCell::getId);
        composeChildren.forEach(child -> {
            FlatReceivedPriceHelper.FlatCell flatCell = cellMap.get(child.getItemId());
            if (Objects.nonNull(flatCell)) {
                child.setReceivableFee(flatCell.getFlatPrice());
                child.setCalIsUseLimitPrice(1);
                MathUtils.CalculateExpectedUnitPriceResult calculateExpectedUnitPriceResult = MathUtils.calculateExpectedUnitPrice(child.getUnitCount(), child.getDoseCount(), flatCell.getFlatPrice());
                child.clearPromotionAndAdjustmentFee(calculateExpectedUnitPriceResult.expectedUnitPrice, flatCell.getFlatPrice(), calculateExpectedUnitPriceResult.fractionPrice);
            }
        });

    }

    public void applyChargeFormItemPromotionInfoUnitAdjustmentFee() {
        if (chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.UNCHARGED) {
            return;
        }

        if (isParent()) {
            composeChildren.forEach(PharmacyItemProcessor::applyChargeFormItemPromotionInfoUnitAdjustmentFee);
        }

        BigDecimal unitAdjustmentFee = MathUtils.wrapBigDecimalOrZero(chargeFormItem.getUnitAdjustmentFee());

        if (unitAdjustmentFee.compareTo(BigDecimal.ZERO) == 0) {

            ChargeDiscountInfo itemPromotionInfo = chargeFormItem.getPromotionInfo();
            if (itemPromotionInfo != null) {
                itemPromotionInfo.setUnitAdjustmentFee(BigDecimal.ZERO);
                itemPromotionInfo.setDeductedUnitAdjustmentFee(BigDecimal.ZERO);
                chargeFormItem.setPromotionInfo(itemPromotionInfo);
                chargeFormItem.setPromotionInfoJson(JsonUtils.dump(itemPromotionInfo));
            }

            Optional.ofNullable(chargeFormItem.getAdditional())
                    .ifPresent(chargeFormItemAdditional -> chargeFormItemAdditional.setUnitAdjustmentFeeLastModifiedBy(null));
            return;
        }

        ChargeDiscountInfo itemPromotionInfo = Optional.ofNullable(chargeFormItem.getPromotionInfo()).orElse(new ChargeDiscountInfo());

        BigDecimal deductedUnitAdjustmentFee = calculateDeductedUnitAdjustmentFee();

        itemPromotionInfo.setUnitAdjustmentFee(unitAdjustmentFee);
        itemPromotionInfo.setDeductedUnitAdjustmentFee(deductedUnitAdjustmentFee);
        itemPromotionInfo.setUnitAdjustmentFeeIgnoreDeduct(unitAdjustmentFee.subtract(deductedUnitAdjustmentFee));
        chargeFormItem.setPromotionInfo(itemPromotionInfo);
        chargeFormItem.setPromotionInfoJson(JsonUtils.dump(itemPromotionInfo));
    }

    private BigDecimal calculateDeductedUnitAdjustmentFee() {

        //就算被抵扣的单项议价值
        BigDecimal totalCount = MathUtils.calculateTotalCount(chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount());

        //计算抵扣数量对应的单项议价金额
        if (MathUtils.wrapBigDecimalCompare(chargeFormItem.getDeductTotalCount(), BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        } else if (chargeFormItem.getDeductTotalCount().compareTo(totalCount) == 0) {
            return MathUtils.wrapBigDecimalOrZero(chargeFormItem.getUnitAdjustmentFee());
        }
        return chargeFormItem.getUnitAdjustmentFee().multiply(chargeFormItem.getDeductTotalCount()).divide(totalCount, 2, RoundingMode.DOWN);
    }

    public void fetchSingleItemExpectedPrice(CalculateSinglePromotionItem calculateSinglePromotionItem) {

        if (chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.UNCHARGED) {
            return;
        }

        BigDecimal singlePromotionPriceTotal = BigDecimal.ZERO;
        if (Objects.nonNull(calculateSinglePromotionItem)) {
            PromotionSimpleGroup promotionGroup = new PromotionSimpleGroup(calculateSinglePromotionItem.getPromotions());

            //单品折扣的总金额（负值）
            singlePromotionPriceTotal = promotionGroup.getDiscountPromotions()
                    .stream()
                    .map(PromotionSimple::getDiscountPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        //折扣后的总金额
        BigDecimal backPreExpectedActualTotalPrice = MathUtils.max(MathUtils.wrapBigDecimalAdd(chargeFormItem.getSourceTotalPrice(), singlePromotionPriceTotal), BigDecimal.ZERO);

        ExpectedPriceHelper.CalculateExpectedPriceV2Result calculateExpectedPriceV2Result = ExpectedPriceHelper.calculateExpectedPriceV2(backPreExpectedActualTotalPrice,
                chargeFormItem.getUnitCount(),
                chargeFormItem.getDoseCount(),
                chargeFormItem.getSourceUnitPrice(),
                chargeFormItem.getSourceTotalPrice(),
                chargeFormItem.getExpectedUnitPrice(),
                chargeFormItem.getExpectedTotalPriceRatio(),
                chargeFormItem.getExpectedTotalPrice()
        );

        sLogger.info("calculateExpectedPriceV2Result: {}", JsonUtils.dump(calculateExpectedPriceV2Result));


        //更新item的议价金额
        chargeFormItem.setUnitPrice(calculateExpectedPriceV2Result.getActualUnitPrice())
                .setTotalPriceRatio(calculateExpectedPriceV2Result.getUnitPriceRatio())
                .setExpectedUnitPrice(calculateExpectedPriceV2Result.getExpectedUnitPrice())
                .setExpectedTotalPriceRatio(calculateExpectedPriceV2Result.getExpectedUnitPriceRatio())
                .setExpectedTotalPrice(calculateExpectedPriceV2Result.getExpectedTotalPrice())
                .setFractionPrice(calculateExpectedPriceV2Result.getFractionPrice())
                .setUnitAdjustmentFee(calculateExpectedPriceV2Result.getUnitAdjustmentFee());


        if (Objects.nonNull(calculateSinglePromotionItem)) {
            allSinglePromotions = calculateSinglePromotionItem.getSinglePromotionViews();

            List<ItemSinglePromotion> itemSinglePromotions = Optional.ofNullable(allSinglePromotions)
                    .orElse(new ArrayList<>())
                    .stream()
                    .filter(singlePromotionView -> singlePromotionView.getChecked() || singlePromotionView.getExpectedChecked() != null)
                    .map(singlePromotionView -> {
                        ItemSinglePromotion itemSinglePromotion = new ItemSinglePromotion();
                        BeanUtils.copyProperties(singlePromotionView, itemSinglePromotion);
                        if (!singlePromotionView.getChecked()) {
                            itemSinglePromotion.setHitRuleDetail(null);
                        }
                        return itemSinglePromotion;
                    }).collect(Collectors.toList());

            ChargeFormItemAdditional additional = Optional.ofNullable(chargeFormItem.getAdditional())
                    .orElse(new ChargeFormItemAdditional());
            additional.setSinglePromotions(itemSinglePromotions);

            chargeFormItem.setAdditional(additional);

            calculateSinglePromotionItem.setUnitAdjustmentFee(chargeFormItem.getUnitAdjustmentFee());
            calculateSinglePromotionItem.setOriginalPrice(MathUtils.wrapBigDecimalCompare(singlePromotionPriceTotal, BigDecimal.ZERO) == 0
                    && MathUtils.wrapBigDecimalCompare(calculateSinglePromotionItem.getUnitAdjustmentFee(), BigDecimal.ZERO) == 0
            );
        }
    }

    /**
     * 计算社保应收
     *
     * @return
     */
    public BigDecimal calculateShebaoReceivableTotalPrice(Integer parentProductType) {

        /**
         * 如果没有开通医保， 或者不是使用社保支付 则直接返回应收
         */
        if (Objects.isNull(getCanPayHealthCard()) || !getCanPayHealthCard()) {
            return receivableFee;
        }
        /**
         * 先判断usageInfo里是否禁止刷社保
         */
        if (Objects.nonNull(chargeFormItem.getUsageInfo())) {
            UsageInfo usageInfo = JsonUtils.readValue(chargeFormItem.getUsageInfo(), UsageInfo.class);

            if (Objects.nonNull(usageInfo) && Optional.ofNullable(usageInfo.getPayType()).orElse(0) == ChargeConstants.ChargeFormItemPayType.SHEBAO_PERSONAL_DISABLED) {
                return BigDecimal.ZERO;
            }
        }

        if (chargeFormItem.getProductType() == Constants.ProductType.REGISTRATION && CollectionUtils.isEmpty(composeChildren)) {
            return BigDecimal.ZERO;
        }

        if (isParent()) {
            return composeChildren.stream().map(itemProcessor -> itemProcessor.calculateShebaoReceivableTotalPrice(chargeFormItem.getProductType())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        }

        if (Objects.equals(parentProductType, Constants.ProductType.REGISTRATION) && Objects.isNull(sheBaoMatchCode)) {
            return BigDecimal.ZERO;
        }

        if (Objects.nonNull(sheBaoMatchCode) && !ChargeGoodsItemUtils.isCanPayByHealthCardNormal(sheBaoMatchCode.getShebaoPayMode(), sheBaoMatchCode.getShebaoCode())) {
            return BigDecimal.ZERO;
        }


        /**
         * 组合项目单独处理
         */
        if (Objects.nonNull(goodsItem) && goodsItem.getCombineType() == GoodsConst.GoodsCombine.COMBINE && goodsItem.getType() == Constants.ProductType.EXAMINATION) {

            if (!ChargeGoodsItemUtils.isCanPayByHealthCard(goodsItem, ChargeConstants.ChargeFormItemPayType.SHEBAO_PERSONAL_PAYMENT)) {
                return BigDecimal.ZERO;
            } else {
                return receivableFee;
            }
        }


        return receivableFee;

    }

    public void clearLimitInfo() {

        if (isParent()) {
            composeChildren.forEach(PharmacyItemProcessor::clearLimitInfo);
        }

        setCalIsUseLimitPrice(0);
        Optional.ofNullable(chargeFormItem.getAdditional())
                .ifPresent(additional -> additional.setLimitInfo(null));

        Optional.ofNullable(chargeFormItem.getPromotionInfo())
                .ifPresent(promotionInfo -> {
                    promotionInfo.setLimitFee(null);
                    chargeFormItem.setPromotionInfoJson(JsonUtils.dump(promotionInfo));
                });

        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(itemBatchInfoProcessors)) {
            itemBatchInfoProcessors.forEach(ItemBatchInfoProcessor::clearLimitInfo);
        }
    }

    /**
     * 更新标记赠品item
     */
    public void updateMarkedGiftItem() {

        if (chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.UNCHARGED) {
            return;
        }

        //先清空优惠信息和议价信息
        getAllChargeFormItems().forEach(ChargeFormItem::clearExpectedPrice);

        //处理赠品的优惠
        PromotionSimple promotionSimple = new PromotionSimple();

        promotionSimple.setDiscountPrice(this.calculateGiftPromotionPrice());
        //没有promotionId，但是这个字段不能为空，这里写死成唯一key
        promotionSimple.setPromotionId("marked-gift-discount");
        promotionSimple.setPromotionName("自主赠送");
        promotionSimple.setType(PromotionSimple.Type.MARKED_GIFT_DISCOUNT);
        CalculateSinglePromotionItem giftCalculateSinglePromotionItem = generateCalculateSinglePromotionItem();
        giftCalculateSinglePromotionItem.addPromotionAndDiscountPrice(promotionSimple);

        fetchSingleItemExpectedPrice(giftCalculateSinglePromotionItem);
        setPromotion(giftCalculateSinglePromotionItem.getPromotions(), promotionSimple.getDiscountPrice());
        this.updateReceivableFee(BigDecimal.ZERO);
    }


    public void updateSheBaoMatchCode(SheBaoMatchCode sheBaoMatchCode) {
        this.sheBaoMatchCode = sheBaoMatchCode;
    }

    public void applyPointRatePromotion(CalculatePointRatePromotionGoodsItem pointRatePromotionGoodsItem) {

        if (isParent()) {
            composeChildren.forEach(child -> child.applyPointRatePromotion(pointRatePromotionGoodsItem));
        }

        if (Objects.isNull(pointRatePromotionGoodsItem) || Objects.isNull(pointRatePromotionGoodsItem.getPointRatePromotionSimple())) {
            Optional.ofNullable(chargeFormItem.getAdditional())
                    .ifPresent(additional -> additional.setPointRateInfo(null));
            return;
        }

        CalculatePointRatePromotionGoodsItem.PointRatePromotionSimple pointRatePromotionSimple = pointRatePromotionGoodsItem.getPointRatePromotionSimple();

        if (MathUtils.compareZero(pointRatePromotionSimple.getPointRate()) <= 0) {
            Optional.ofNullable(chargeFormItem.getAdditional())
                    .ifPresent(additional -> additional.setPointRateInfo(null));
            return;
        }


        ChargeFormItemAdditional additional = Optional.ofNullable(chargeFormItem.getAdditional())
                .orElse(new ChargeFormItemAdditional());


        ItemPointRateInfo itemPointRateInfo = new ItemPointRateInfo();
        BeanUtils.copyProperties(pointRatePromotionSimple, itemPointRateInfo);
        additional.setPointRateInfo(itemPointRateInfo);

        chargeFormItem.setAdditional(additional);
    }

    /**
     * 更新追溯码使用状态
     * 全退的情况下，才更新。
     * 退费后查收费单详情不查发药单，拿不到发药单的追溯码情况，所以这里更新收费的发药单使用状态
     */
    private void doUpdateFormItemTraceableCodeUsedStatus() {
        if (chargeFormItem == null || chargeFormItem.getV2Status() != Constants.ChargeFormItemStatus.REFUNDED) {
            return;
        }
        if (chargeFormItem.getAdditional() == null || CollectionUtils.isEmpty(chargeFormItem.getAdditional().getTraceableCodeList())) {
            return;
        }
        chargeFormItem.getAdditional().getTraceableCodeList().forEach(traceableCode -> {
            traceableCode.setUsed(2);
        });
    }

    public QueryGoodsWithStockReq generateQueryGoodsWithStockReq(boolean alwaysCollectBatch) {
        QueryGoodsWithStockReq queryGoodsWithStock = new QueryGoodsWithStockReq();
        if (!CollectionUtils.isEmpty(getComposeChildren()) && isCompose()) {

            List<QueryGoodsInPharmacyByIdsAndStockCountReq.QueryGoodsWithStock> goodsWithStockReqsChildren = getComposeChildren().stream().map(pharmacyItemProcessor -> pharmacyItemProcessor.generateQueryGoodsWithStockReq(alwaysCollectBatch)).collect(Collectors.toList());
            queryGoodsWithStock.setChildren(goodsWithStockReqsChildren);
        }

        BigDecimal doseCount = Objects.nonNull(chargeFormItem.getExpectedDoseCount()) ? chargeFormItem.getExpectedDoseCount() : this.getDoseCount();
        BigDecimal unitCount = this.getUnitCount();
        if (doseCount == null || doseCount.compareTo(BigDecimal.ONE) < 0) {
            doseCount = BigDecimal.ONE;
        }
        if (unitCount == null) {
            unitCount = BigDecimal.ZERO;
        }
        BigDecimal totalCount = unitCount.multiply(doseCount);

        QueryGoodsWithStockReq queryGoodsWithStockReq = new QueryGoodsWithStockReq();
        queryGoodsWithStockReq.setKeyId(this.getItemId());
        queryGoodsWithStockReq.setGoodsId(this.getProductId());
        queryGoodsWithStockReq.setLockId(org.apache.commons.lang3.StringUtils.isNotEmpty(chargeFormItem.getLockId()) ? Long.parseLong(chargeFormItem.getLockId()) : null);
        queryGoodsWithStockReq.setUseDismounting(this.getUseDismounting());
        queryGoodsWithStockReq.setPharmacyNo(this.getPharmacyNo());
        if (alwaysCollectBatch
                || chargeFormItem.needUpdateBatch()) {
            queryGoodsWithStockReq.setPretendBatchCutItemList(Optional.ofNullable(chargeFormItem.getChargeFormItemBatchInfos())
                    .orElse(new ArrayList<>())
                    .stream()
                    .filter(chargeFormItemBatchInfo -> chargeFormItemBatchInfo.getIsDeleted() == 0)
                    .map(chargeFormItemBatchInfo -> {
                        QueryGoodsInPharmacyByIdsAndStockCountReq.PretendBatchCutItem pretendBatchCutItem = new QueryGoodsInPharmacyByIdsAndStockCountReq.PretendBatchCutItem();
                        if (this.getUseDismounting() == 1) {
                            pretendBatchCutItem.setPieceCount(chargeFormItemBatchInfo.getUnitCount());
                        } else {
                            pretendBatchCutItem.setPackageCount(chargeFormItemBatchInfo.getUnitCount());
                        }
                        pretendBatchCutItem.setBatchId(org.apache.commons.lang3.StringUtils.isNotEmpty(chargeFormItemBatchInfo.getBatchId()) ? Long.parseLong(chargeFormItemBatchInfo.getBatchId()) : null);
                        return pretendBatchCutItem;
                    })
                    .collect(Collectors.toList())
            );
        }
        if (this.getUseDismounting() == 1) {
            queryGoodsWithStockReq.setPieceCount(totalCount);
        } else {
            queryGoodsWithStockReq.setPackageCount(totalCount);
        }

        return queryGoodsWithStockReq;
    }

    public BigDecimal calculateGiftPromotionPrice() {
        /**
         * 这里药品赠品不支持为0元，如果商品本身为0.01或者0则不处理
         */
        if (this.getProductType() == Constants.ProductType.MEDICINE) {
            BigDecimal totalCount = MathUtils.wrapBigDecimalMultiply(chargeFormItem.getDoseCount(), chargeFormItem.getUnitCount());
            BigDecimal discountPrice = MathUtils.wrapBigDecimalSubtract(chargeFormItem.getSourceTotalPrice(), MathUtils.calculateTotalPrice(new BigDecimal("0.01"), totalCount, 2));

            return MathUtils.compareZero(discountPrice) <= 0 ? chargeFormItem.getSourceTotalPrice().negate() : discountPrice.negate();
        } else {
            return chargeFormItem.getSourceTotalPrice().negate();
        }
    }
}
