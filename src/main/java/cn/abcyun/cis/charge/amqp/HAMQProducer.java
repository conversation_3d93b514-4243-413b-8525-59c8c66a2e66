package cn.abcyun.cis.charge.amqp;

import cn.abcyun.cis.charge.amqp.model.ChargeRefundSuccessMessage;
import cn.abcyun.cis.charge.amqp.model.ChargeRefundTaskStartMessage;
import cn.abcyun.bis.rpc.sdk.cis.message.charge.ChargeSheetSyncMessage;
import cn.abcyun.bis.rpc.sdk.cis.model.charge.ChargeFormItem;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.controller.api.ChargeFormItemMerger;
import cn.abcyun.cis.charge.model.*;
import cn.abcyun.cis.charge.service.dto.ChargeSheetExtend;
import cn.abcyun.cis.charge.service.dto.DTOConverter;
import cn.abcyun.cis.charge.util.ChargeUtils;
import cn.abcyun.cis.commons.amqp.message.ChargeSheetMessage;
import cn.abcyun.cis.commons.amqp.message.PatientOrderMessage;
import cn.abcyun.cis.commons.amqp.message.charge.ChargeOweSheetMessage;
import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.common.log.marker.AbcLogMarker;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class HAMQProducer {

    @Value("${amqp-ha.fanout-exchange.name}")
    private String fanoutExchangeName;

    @Value("${amqp-ha.fanout-exchange-charge-sheet.name}")
    private String fanoutExchangeChargeSheet;

    @Value("${amqp-ha.fanout-exchange-charge-owe-sheet.name}")
    private String fanoutExchangeChargeOweSheet;

    @Value("${amqp-ha.fanout-exchange-charge-refund-task-start.name}")
    private String fanoutExchangeChargeRefundTaskStartName;

    @Value("${amqp-ha.fanout-exchange-charge-refund-success.name}")
    private String fanoutExchangeChargeRefundSuccess;

    /**
     * 收费单同步给别的服务的广播消息
     */
    @Value("${amqp-ha.fanout-exchange-charge-sheet-sync.name}")
    private String fanoutExchangeChargeSheetSync;

    @Autowired
    @Qualifier("amqp-cloud-rabbit-template")
    private RabbitTemplate rabbitTemplate;


    public void notifyChargeMessage(String patientOrderId, String clinicId, String chargeSheetId, String patientId, String operatorId, int msgType, Instant ageLockTime) {
        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "notifyChargeFinished patientOrderId:" + patientOrderId + ", operatorId:" + operatorId);
        PatientOrderMessage message = new PatientOrderMessage();
        message.setOperatorId(operatorId);
        message.setPatientOrderId(patientOrderId);
        message.setClinicId(clinicId);
        message.setChargeSheetId(chargeSheetId);
        message.setType(msgType);
        message.setPatientId(patientId);
        message.setAgeLockTime(ageLockTime);

        rabbitTemplate.convertAndSend(fanoutExchangeName, null, message);
    }


    public void notifyChargeSheetMessage(ChargeSheet chargeSheet,
                                         List<ChargeTransaction> currentAddedTransactions,
                                         List<ChargeTransactionRecord> allChargeTransactionRecords,
                                         Map<String, List<ChargeFormItemBatchInfo>> currentRefundBatchInfoMap,
                                         int msgType,
                                         Integer msgSubType,
                                         String operatorId) {
        ChargeSheet memoryChargeSheet = ChargeUtils.deepCopyChargeSheet(chargeSheet);
        if (msgType == ChargeSheetMessage.MSG_TYPE_DELETE) {
            memoryChargeSheet.setChargeForms(new ArrayList<>());
        }
        // 只有收退费动作才需要当前新增的交易流水
        if (msgType != ChargeSheetMessage.MSG_TYPE_PARTY_CHARGED
                && msgType != ChargeSheetMessage.MSG_TYPE_CHARGED
                && msgType != ChargeSheetMessage.MSG_TYPE_PARTY_REFUND
                && msgType != ChargeSheetMessage.MSG_TYPE_REFUNDED
                && msgType != ChargeSheetMessage.MSG_TYPE_PARTY_PAID_BACK
                && msgType != ChargeSheetMessage.MSG_TYPE_PAID_BACK) {
            currentAddedTransactions = new ArrayList<>();
        }
        // 只有部分退费和退费才有退费的批次信息
        if (msgType != ChargeSheetMessage.MSG_TYPE_REFUNDED && msgType != ChargeSheetMessage.MSG_TYPE_PARTY_REFUND) {
            currentRefundBatchInfoMap = new HashMap<>();
        }
        String dataSignature = null;
        if (msgType == ChargeSheetMessage.MSG_TYPE_CREATED || msgType == ChargeSheetMessage.MSG_TYPE_UPDATE) {
            dataSignature = ChargeUtils.sign(chargeSheet);
        }
        ChargeSheetExtend chargeSheetExtend = DTOConverter.convertToChargeSheetExtend(memoryChargeSheet);
        ChargeFormItemMerger.mergeForChargeSheet(chargeSheetExtend);

        Map<String, String> formItemIdAssociateFormItemIdMap = Optional.ofNullable(chargeSheet.getChargeForms())
                .orElse(Lists.newArrayList())
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> !org.springframework.util.CollectionUtils.isEmpty(chargeForm.getChargeFormItems()))
                .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                .filter(chargeFormItem -> chargeFormItem.getIsDeleted() == 0)
                .filter(chargeFormItem -> !StringUtils.isEmpty(chargeFormItem.getAssociateFormItemId()))
                .collect(Collectors.toMap(cn.abcyun.cis.charge.model.ChargeFormItem::getId, cn.abcyun.cis.charge.model.ChargeFormItem::getAssociateFormItemId));

        ChargeSheetMessage.ChargeSheet messageChargeSheet = ChargeUtils.convertToMessageChargeSheet(chargeSheetExtend, currentAddedTransactions, allChargeTransactionRecords, formItemIdAssociateFormItemIdMap, currentRefundBatchInfoMap);

        ChargeSheetMessage message = new ChargeSheetMessage();
        message.setType(msgType);
        message.setSubType(msgSubType);
        message.setDataSignature(dataSignature);
        message.setChargeSheet(messageChargeSheet);
        message.setOperatorId(operatorId);
        message.setTimestamp(Instant.now());
        log.info(AbcLogMarker.MARKER_LONG_TIME, "notifyChargeSheetMessage msg:{}", JsonUtils.dump(message));
        rabbitTemplate.convertAndSend(fanoutExchangeChargeSheet, null, message);
    }

    public void sendChargeSheetSyncMessage(ChargeSheet chargeSheet, String operatorId) {
        MQProducer.doAfterTransactionCommit(() -> {
            if (chargeSheet == null) {
                return;
            }

            //目前只有咨询计划单需要同步
            if (chargeSheet.getType() != ChargeSheet.Type.MEDICAL_PLAN) {
                return;
            }

            ChargeSheetSyncMessage chargeSheetSyncMessage = new ChargeSheetSyncMessage();
            cn.abcyun.bis.rpc.sdk.cis.model.charge.ChargeSheet syncChargeSheet = JsonUtils.readValue(JsonUtils.dump(chargeSheet), cn.abcyun.bis.rpc.sdk.cis.model.charge.ChargeSheet.class);

            if (syncChargeSheet.getIsDeleted() == 1) {
                syncChargeSheet.setChargeForms(new ArrayList<>());
            }

            //单独维护form的totalPrice
            Optional.ofNullable(syncChargeSheet.getChargeForms())
                    .orElse(new ArrayList<>())
                    .stream()
                    .filter(chargeForm -> chargeForm.getSourceFormType() != Constants.SourceFormType.AIR_PHARMACY)
                    .forEach(chargeForm -> chargeForm.setTotalPrice(Optional.ofNullable(chargeForm.getChargeFormItems())
                                    .orElse(new ArrayList<>())
                                    .stream().map(ChargeFormItem::getTotalPrice)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                            )
                    );

            chargeSheetSyncMessage.setChargeSheet(syncChargeSheet);
            chargeSheetSyncMessage.setOperatorId(operatorId);

            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "sendChargeSheetSyncMessage: {}", JsonUtils.dump(chargeSheetSyncMessage));

            rabbitTemplate.convertAndSend(fanoutExchangeChargeSheetSync, null, chargeSheetSyncMessage);
        });
    }

    /**
     * 发送欠费单消息
     *
     * @param chargeOweSheets
     * @param msgType
     * @param operatorId
     */
    public void notifyChargeOweSheetMessage(List<ChargeOweSheet> chargeOweSheets, int msgType, String operatorId) {
        MQProducer.doAfterTransactionCommit(() -> notifyChargeOweSheetMessageCore(chargeOweSheets, msgType, operatorId));
    }

    private void notifyChargeOweSheetMessageCore(List<ChargeOweSheet> chargeOweSheets, int msgType, String operatorId) {
        if (CollectionUtils.isEmpty(chargeOweSheets)) {
            return;
        }

        ChargeOweSheet firstChargeOweSheet = chargeOweSheets.get(0);

        ChargeOweSheetMessage chargeOweSheetMessage = new ChargeOweSheetMessage();
        chargeOweSheetMessage.setChainId(firstChargeOweSheet.getChainId())
                .setClinicId(firstChargeOweSheet.getClinicId())
                .setChargeOweSheets(chargeOweSheets.stream()
                        .map(chargeOweSheet -> {
                            ChargeOweSheetMessage.ChargeOweSheet messageChargeOweSheet = new ChargeOweSheetMessage.ChargeOweSheet();
                            BeanUtils.copyProperties(chargeOweSheet, messageChargeOweSheet);
                            messageChargeOweSheet.setId(Objects.toString(chargeOweSheet.getId(), ""));
                            return messageChargeOweSheet;
                        }).collect(Collectors.toList())
                )
                .setPatientId(firstChargeOweSheet.getPatientId())
                .setType(msgType)
                .setOperatorId(operatorId);

        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "notifyChargeOweSheetMessage msg:{}", JsonUtils.dump(chargeOweSheetMessage));
        rabbitTemplate.convertAndSend(fanoutExchangeChargeOweSheet, null, chargeOweSheetMessage);
    }


    /**
     * 发送退费任务开始消息
     * @param chargeRefundTask
     */
    public void sendRefundTaskStartMessageAfterTransactionCommit(ChargeRefundTask chargeRefundTask, String refundTaskItemId, String operatorId) {
        if (Objects.isNull(chargeRefundTask)) {
            return;
        }
        ChargeRefundTaskStartMessage chargeRefundTaskStartMessage = ChargeRefundTaskStartMessage.ofChargeRefundTaskStartMessage(chargeRefundTask, refundTaskItemId, operatorId);
        MQProducer.doAfterTransactionCommit(() -> sendRefundTaskStartMessage(chargeRefundTaskStartMessage));
    }

    private void sendRefundTaskStartMessage(ChargeRefundTaskStartMessage chargeRefundTaskStartMessage) {
        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX,"sendRefundTaskStartMessage msg:{}", JsonUtils.dump(chargeRefundTaskStartMessage));
        rabbitTemplate.convertAndSend(fanoutExchangeChargeRefundTaskStartName, null, chargeRefundTaskStartMessage);
    }

    public void sendRefundSuccessMessage(ChargeSheet chargeSheet, ChargePayTransaction chargePayTransaction, String operatorId) {
        ChargeRefundSuccessMessage message = new ChargeRefundSuccessMessage();
        message.setChargeSheet(chargeSheet);
        message.setChargePayTransaction(chargePayTransaction);
        message.setOperatorId(operatorId);

        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "sendRefundSuccessMessage msg:{}", JsonUtils.dump(message));

        MQProducer.doAfterTransactionCommit(() -> rabbitTemplate.convertAndSend(fanoutExchangeChargeRefundSuccess, null, message));
    }
}
