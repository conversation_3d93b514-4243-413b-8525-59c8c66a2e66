package cn.abcyun.cis.charge.amqp.model;

import cn.abcyun.bis.rpc.sdk.cis.model.outpatient.ExtendDiagnosisInfo;
import cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientInfo;
import cn.abcyun.cis.charge.model.ChargeCooperationOrder;
import cn.abcyun.cis.charge.util.UsageInfoUtil;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
public class ChargeCoPharmacyOrderMessage {

    /**
     * 药店总部id
     */
    @NotEmpty(message = "chainId不能为空")
    private String chainId;

    /**
     * 药店子店id
     */
    @NotEmpty(message = "clinicId不能为空")
    private String clinicId;

    /**
     * 来源患者id
     */
    @NotEmpty(message = "sourcePatientId不能为空")
    private String sourcePatientId;

    /**
     * 来源患者信息
     */
    @NotNull(message = "患者信息不能为空")
    private PatientInfo sourcePatientInfo;

    /**
     * 来源诊所子店id
     */
    @NotEmpty(message = "来源子店id不能为空")
    private String sourceClinicId;

    /**
     * 来源诊所总部id
     */
    @NotEmpty(message = "来源诊所总部id不能为空")
    private String sourceChainId;

    /**
     * 来源就诊单id
     */
    @NotEmpty(message = "来源就诊单id不能为空")
    private String sourcePatientOrderId;

    private ChargeCooperationOrder.PatientOrderSimple sourcePatientOrder;

    /**
     * 来源单据id，比如门诊单id
     */
    @NotEmpty(message = "来源单据id不能为空")
    private String sourceSheetId;

    /**
     * 来源诊所名称
     */
    private String sourceClinicName;

    /**
     * 来源诊所医生
     */
    private String sourceDoctorId;

    @Valid
    private List<ChargeCooperationOrderMessage> cooperationOrders;

    /**
     * 操作人id
     */
    @NotEmpty(message = "operatorId不能为空")
    private String sourceOperatorId;


    private String sourceOperatorName;


    @Data
    public static class ChargeCooperationOrderMessage {

        @NotEmpty(message = "sourceFormId不能为空")
        private String sourceFormId;


        /**
         * 来源医生id
         */
        private String sourceDoctorId;

        /**
         * 来源的医生名称
         */
        private String sourceDoctorName;

        /**
         * 来源诊断信息
         */
        private List<ExtendDiagnosisInfo> extendDiagnosisInfos;

        private UsageInfoUtil.DbUsageInfo usageInfo;

        private int sourceFormType;

        private String specification;

        private int pharmacyType;

        private int pharmacyNo;

        @Valid
        private List<ChargeCooperationOrderItemMessage> cooperationOrderItems;
    }

    @Data
    @Accessors(chain = true)
    public static class ChargeCooperationOrderItemMessage {

        @NotEmpty(message = "sourceFormItemId不能为空")
        private String sourceItemId;

        private String goodsId;

        private int goodsType;

        private int goodsSubType;

        private String name;

        private int isDismounting;

        private String unit;

        @NotNull(message = "单价不能为空")
        @DecimalMin(value = "0", message = "单价不能小于0")
        private BigDecimal unitPrice;

        private BigDecimal fractionPrice;

        private BigDecimal unitCount;

        private BigDecimal expectedUnitPrice;

        private BigDecimal sourceUnitPrice;

        private BigDecimal doseCount;

        private BigDecimal totalPrice;

        private int sort;

        private int composeType;

        private String composeParentFormItemId;

        private BigDecimal receivedPrice;

        private BigDecimal expectedTotalPrice;

        private int pharmacyType;

        private int pharmacyNo;

        private int feeComposeType;

        private Long feeTypeId;

        private int goodsFeeType;

        private UsageInfoUtil.DbUsageInfo usageInfo;

        private BigDecimal expectedTotalPriceRatio;

        private BigDecimal sourceTotalPrice;

    }
}
