package cn.abcyun.cis.charge.amqp;

import cn.abcyun.bis.rpc.sdk.cis.message.charge.ChargeSheetPaidMessageForGiftCoupon;
import cn.abcyun.bis.rpc.sdk.cis.message.charge.ChargeSheetRefundMessageForGiftCoupon;
import cn.abcyun.bis.rpc.sdk.cis.message.charge.*;
import cn.abcyun.bis.rpc.sdk.cis.message.charge.record.ChargeTransactionRecordMessage;
import cn.abcyun.bis.rpc.sdk.cis.model.charge.ChargeSheetRegisterInfoUpdateMessage;
import cn.abcyun.bis.rpc.sdk.cis.model.goodslocking.*;
import cn.abcyun.bis.rpc.sdk.cis.model.invoice.message.AutoInvoiceMessage;
import cn.abcyun.bis.rpc.sdk.cis.model.invoice.message.PaidFeeChangeMessage;
import cn.abcyun.bis.rpc.sdk.cis.model.message.ToBMessage;
import cn.abcyun.bis.rpc.sdk.cis.model.message.TodoMessageBuilder;
import cn.abcyun.cis.charge.amqp.model.ChargePayTransactionAutoCancelMessage;
import cn.abcyun.cis.charge.amqp.model.ChargeSheetLockingAutoUnlockMessage;
import cn.abcyun.cis.charge.amqp.model.MessageConvertor;
import cn.abcyun.cis.charge.api.model.EmployeeView;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.model.ChargeFormItem;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.model.ChargeTransactionRecord;
import cn.abcyun.cis.charge.service.dto.ChargeExecuteRecordView;
import cn.abcyun.cis.charge.util.ChargeUtils;
import cn.abcyun.cis.commons.amqp.message.ChargeDeliveryMessage;
import cn.abcyun.cis.commons.amqp.message.ServiceAlertMessage;
import cn.abcyun.cis.commons.amqp.message.TreatmentExecuteMessage;
import cn.abcyun.cis.commons.amqp.message.charge.ChargeSheetAutoUnlockMessage;
import cn.abcyun.cis.commons.amqp.message.charge.*;
import cn.abcyun.cis.commons.amqp.message.patient.merge.PatientMergeTaskReport;
import cn.abcyun.cis.commons.message.ToCMessage;
import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.common.log.marker.AbcLogMarker;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Component
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class RocketMqProducer {


    private final RocketMQTemplate rocketMqTemplate;

    @Value("${spring.application.name}")
    private String serviceName;

    @Value("${rocketmq.topic.cis-charge.name}")
    private String cisChargeTopicName;

    @Value("${rocketmq.topic.cis-charge-delay.name}")
    private String cisChargeDelayTopicName;

    @Value("${rocketmq.topic.cis-invoice.name}")
    private String cisInvoiceTopicName;

    @Value("${rocketmq.topic.cis-dashboard.name}")
    private String cisDashboardTopicName;

    @Value("${rocketmq.topic.cis-message.name}")
    private String cisMessageTopicName;

    @Value("${rocketmq.topic.cis-crm.name}")
    private String cisCrmTopicName;

    @Value("${rocketmq.topic.cis-monitor.name}")
    private String cisMonitorTopicName;

    @Value("${rocketmq.topic.cis-goods-locking.name}")
    private String cisGoodsLockingTopicName;

    @Value("${rocketmq.tag.cis-charge-delay.online-charge-sheet-push-again}")
    private String onlineChargeSheetPushAgain;

    @Value("${rocketmq.tag.cis-charge.charge-sheet-paid-success-inform-business}")
    private String chargeSheetPaidSuccessInformBusiness;

    @Value("${rocketmq.tag.cis-charge.charge-sheet-auto-unlock}")
    private String chargeSheetAutoUnlock;

    @Value("${rocketmq.tag.cis-charge.add-air-pharmacy-vendor-used-count}")
    private String addAirPharmacyVendorUsedCount;

    @Value("${rocketmq.tag.cis-charge.hospital-charge-sheet-change}")
    private String hospitalChargeSheetChange;

    @Value("${rocketmq.tag.cis-invoice.auto-invoice}")
    private String cisInvoiceAutoInvoice;

    @Value("${rocketmq.tag.cis-invoice.paid-fee-change}")
    private String cisInvoicePaidFeeChange;

    @Value("${rocketmq.tag.cis-charge.charge-delivery}")
    private String changeDelivery;

    @Value("${rocketmq.tag.cis-charge.patient-owe-amount-changed}")
    private String patientOweAmountChanged;

    @Value("${rocketmq.tag.cis-dashboard.dashboard-todo}")
    private String dashboardTodo;

    @Value("${rocketmq.tag.cis-message.to-b}")
    private String toB;

    @Value("${rocketmq.tag.cis-message.to-c}")
    private String toC;

    @Value("${rocketmq.tag.cis-charge.treatment-execute}")
    private String treatmentExecute;

    @Value("${rocketmq.tag.cis-crm.patient-merge-report}")
    private String patientMergeReport;

    @Value("${rocketmq.tag.cis-charge-delay.order-auto-unlock}")
    private String orderAutoUnlock;

    @Value("${rocketmq.tag.cis-charge-delay.pay-transaction-cancel}")
    private String chargePayTransactionAutoCancel;

    @Value("${rocketmq.tag.cis-charge-delay.locking-auto-unlock}")
    private String lockingAutoUnlock;

    @Value("${rocketmq.tag.cis-monitor.service-alert}")
    private String serviceAlert;

    @Value("${rocketmq.tag.cis-charge.charge-sheet-paid-gift-coupon}")
    private String chargeSheetPaidGiftCoupon;

    @Value("${rocketmq.tag.cis-charge.charge-sheet-refund-gift-coupon}")
    private String chargeSheetRefundGiftCoupon;

    @Value("${rocketmq.tag.cis-charge.charge-sheet-register-info-update}")
    private String chargeSheetRegisterInfoUpdate;

    @Value("${rocketmq.tag.cis-charge.update-deduct-transaction-id}")
    private String updateDeDuctTransactionId;

    @Value("${rocketmq.tag.cis-goods-locking.reg-lock-relation}")
    private String goodsLockingRegLockRelation;

    @Value("${rocketmq.tag.cis-charge.transaction-record}")
    private String cisChargeTransactionRecord;

    @Value("${rocketmq.tag.cis-charge.charge-sheet-update-remark}")
    private String cisChargeChargeSheetUpdateRemark;

    @Value("${rocketmq.tag.cis-charge.re-new-lock}")
    private String cisChargeChargeRenewLock;


    /**
     * 发送网诊收费单在15分钟内还未确认的再次推送的消息，时间15分钟
     *
     * @param onlineChargeSheetPushAgainDelayMessage
     */
    public void sendOnlineChargeSheetPushAgainDelayMessage(OnlineChargeSheetPushAgainDelayMessage onlineChargeSheetPushAgainDelayMessage) {

        onlineChargeSheetPushAgainDelayMessage.setEndTime(Instant.now().plusMillis(Constants.ONLINE_CHARGE_SHEET_PUSH_AGAIN_DELAY_MILLIS));

        Message<OnlineChargeSheetPushAgainDelayMessage> message = MessageBuilder.withPayload(onlineChargeSheetPushAgainDelayMessage).build();
        sendMessageCore("sendOnlineChargeSheetPushAgainDelayMessage", message, sendOnlineChargeSheetPushAgainDelayMessageSupplier(message));

    }

    public void sendOrderAutoUnlockMessage(ChargeSheetAutoUnlockMessage message) {
        MQProducer.doAfterTransactionCommit(() -> sendMessageCore("sendOrderAutoUnlockMessage",
                MessageBuilder.withPayload(message).build(),
                () -> rocketMqTemplate.syncSend(getDestination(cisChargeTopicName, chargeSheetAutoUnlock), message))
        );
    }


    /**
     * 发送收费单异步支付成功的消息，主要用于比如：患者会员充值、患者开卡、患者卡充值等业务，
     * 这类业务需要告诉对应的服务患者已经付费成功，可以入账了
     *
     * @param
     */
    public void sendChargeSheetPaidSuccessNeedInformBusinessMessage(ChargeSheetPaidSuccessNeedInformBusinessMessage message) {
        MQProducer.doAfterTransactionCommit(() -> sendMessageCore("sendChargeSheetPaidSuccessNeedInformBusinessMessage",
                MessageBuilder.withPayload(message).build(),
                () -> rocketMqTemplate.syncSend(getDestination(cisChargeTopicName, chargeSheetPaidSuccessInformBusiness), message)));

    }

    public <T> Supplier<SendResult> sendOnlineChargeSheetPushAgainDelayMessageSupplier(Message<T> message) {
        return () -> rocketMqTemplate.syncSendDelayTimeSeconds(getDestination(cisChargeDelayTopicName, onlineChargeSheetPushAgain), message, 15 * 60);
    }

    /**
     * 发送发票自动打印消息
     *
     * @param message 消息体
     */
    public void syncSendAutoInvoiceMessage(AutoInvoiceMessage message) {
        sendMessageCore("sendAutoInvoiceMessage",
                MessageBuilder.withPayload(message).build(),
                () -> rocketMqTemplate.syncSend(getDestination(cisInvoiceTopicName, cisInvoiceAutoInvoice), message)
        );
    }

    /**
     * 发送发票金额变更消息
     *
     * @param message 消息体
     */
    public void syncSendInvoicePaidFeeChangeMessage(PaidFeeChangeMessage message) {
        sendMessageCore("sendPaidFeeChangeMessage",
                MessageBuilder.withPayload(message).build(),
                () -> rocketMqTemplate.syncSend(getDestination(cisInvoiceTopicName, cisInvoicePaidFeeChange), message)
        );
    }

    public <T> void sendMessageCore(String label, Message<T> message, Supplier<SendResult> sendSupplier) {
        try {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "{}: {}", label, JsonUtils.dump(message));
            SendResult sendResult = sendSupplier.get();
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "{} sendResult: {}", label, JsonUtils.dump(sendResult));
        } catch (Exception e) {
            log.error("{} failed: msg = {}", label, JsonUtils.dump(message));
            log.error("sendMessageCore error.", e);
        }
    }

    private String getDestination(String topic, String tag) {
        if (StringUtils.isBlank(topic)) {
            throw new IllegalStateException("topic is null");
        }
        if (StringUtils.isBlank(tag)) {
            return topic;
        }
        return String.format("%s:%s", topic, tag);
    }

    public void sendAddAirPharmacyVendorUsedCount(AirPharmacyAddVendorUsedCountMessage message) {
        MQProducer.doAfterTransactionCommit(() -> sendMessageCore("sendAddAirPharmacyVendorUsedCount",
                MessageBuilder.withPayload(message).build(),
                () -> rocketMqTemplate.syncSend(getDestination(cisChargeTopicName, addAirPharmacyVendorUsedCount), message)));
    }

    public void sendHospitalChargeSheetMessage(HospitalChargeSheetMessage hospitalChargeSheetMessage) {
        MQProducer.doAfterTransactionCommit(() -> sendMessageCore("sendHospitalChargeSheetMessage",
                MessageBuilder.withPayload(hospitalChargeSheetMessage).build(),
                () -> rocketMqTemplate.syncSend(getDestination(cisChargeTopicName, hospitalChargeSheetChange), hospitalChargeSheetMessage)));
    }

    public void sendChangeDeliveryMessage(ChargeDeliveryMessage message) {
        MQProducer.doAfterTransactionCommit(() -> sendMessageCore("sendChangeDeliveryMessage",
                MessageBuilder.withPayload(message).build(),
                () -> rocketMqTemplate.syncSend(getDestination(cisChargeTopicName, changeDelivery), message)));
    }

    public void sendChargePatientOweAmountChangedName(ChargePatientOweAmountChangedMessage message){
        MQProducer.doAfterTransactionCommit(() -> sendMessageCore("sendChargePatientOweAmountChangedName",
                MessageBuilder.withPayload(message).build(),
                () -> rocketMqTemplate.syncSend(getDestination(cisChargeTopicName, patientOweAmountChanged), message)));
    }

    /**
     * 发送dashboardtodo消息
     * @param message
     */
    public void notifyDashboardTodoMessage(TodoMessageBuilder.DashboardTodoMessage message) {
        sendMessageCore("notifyDashboardTodoMessage",
                MessageBuilder.withPayload(message).build(),
                () -> rocketMqTemplate.syncSend(getDestination(cisDashboardTopicName, dashboardTodo), message));
    }

    /**
     * 发送tobMessage
     * @param message
     */
    public void sendTobMessage(ToBMessage message) {
        MQProducer.doAfterTransactionCommit(() -> sendMessageCore("sendTobMessage",
                MessageBuilder.withPayload(message).build(),
                () -> rocketMqTemplate.syncSend(getDestination(cisMessageTopicName, toB), message)));
    }

    /**
     * 发送tocMessage
     * @param message
     */
    public void sendToCMessage(ToCMessage message) {
        sendMessageCore("sendToCMessage",
                MessageBuilder.withPayload(message).build(),
                () -> rocketMqTemplate.syncSend(getDestination(cisMessageTopicName, toC), message));
    }

    public void sendToCMessageAfterTransactionCommit(ToCMessage message) {
        MQProducer.doAfterTransactionCommit(() -> sendToCMessage(message));
    }




    public void notifyTreatmentExecuteMessage(ChargeExecuteRecordView executeRecord, String patientId, String patientOrderId, String operatorId) {
        TreatmentExecuteMessage message = new TreatmentExecuteMessage()
                .setExecuteRecordId(executeRecord.getId())
                .setChargeSheetId(executeRecord.getChargeSheetId())
                .setChainId(executeRecord.getChainId())
                .setClinicId(executeRecord.getClinicId())
                .setPatientId(patientId)
                .setExecuteClinicId(executeRecord.getExecuteClinicId())
                .setPatientOrderId(patientOrderId)
                .setExecutorIds(Optional.ofNullable(executeRecord.getExecutors())
                        .orElse(Lists.newArrayList())
                        .stream()
                        .map(EmployeeView::getId)
                        .collect(Collectors.toList())
                )
                .setExecuteDate(executeRecord.getExecuteDate())
                .setItems(Optional.ofNullable(executeRecord.getItems())
                        .orElse(Lists.newArrayList())
                        .stream()
                        .map(itemView -> new TreatmentExecuteMessage.ExecuteRecordItem()
                                .setId(String.valueOf(itemView.getId()))
                                .setExecuteCount(BigDecimal.valueOf(itemView.getCount()))
                                .setChargeFormItemId(itemView.getChargeFormItemId())
                                .setExecuteItemId(itemView.getExecuteItemId())
                                .setExecuteItemProductId(itemView.getProductId())
                                .setExecuteItemName(itemView.getExecuteItemName())
                                .setUnit(itemView.getUnit())
                        )
                        .collect(Collectors.toList()))
                .setExecuteStatus(executeRecord.getStatus())
                .setCreated(Instant.now())
                .setOperatorId(operatorId);

        MQProducer.doAfterTransactionCommit(() -> sendMessageCore("notifyTreatmentExecuteMessage",
                MessageBuilder.withPayload(message).build(),
                () -> rocketMqTemplate.syncSend(getDestination(cisChargeTopicName, treatmentExecute), message)));
    }

    /**
     * 患者合并任务报告消息
     * @param message
     */
    public void notifyPatientMergeTaskReportMessage(PatientMergeTaskReport message) {
        MQProducer.doAfterTransactionCommit(() -> sendMessageCore("notifyPatientMergeTaskReportMessage",
                MessageBuilder.withPayload(message).build(),
                () -> rocketMqTemplate.syncSend(getDestination(cisCrmTopicName, patientMergeReport), message)));
    }

    /**
     * 发送收费单自动解锁消息
     * @param chargeSheetAutoUnlockMessage
     * @param delayedSeconds
     */
    public void sendOrderAutoUnlockDelayMessage(ChargeSheetAutoUnlockMessage chargeSheetAutoUnlockMessage, int delayedSeconds) {
        Message<ChargeSheetAutoUnlockMessage> message = MessageBuilder.withPayload(chargeSheetAutoUnlockMessage).build();
        MQProducer.doAfterTransactionCommit(() -> sendMessageCore("sendOrderAutoUnlockDelayMessage",
                message,
                () -> rocketMqTemplate.syncSendDelayTimeSeconds(getDestination(cisChargeDelayTopicName, orderAutoUnlock), message, delayedSeconds)
        ));
    }

    /**
     * 发送支付订单自动关闭消息
     * @param chargePayTransactionAutoCancelMessage
     * @param delayedSeconds
     */
    public void sendChargePayTransactionCancelMessage(ChargePayTransactionAutoCancelMessage chargePayTransactionAutoCancelMessage, long delayedSeconds) {
        Message<ChargePayTransactionAutoCancelMessage> message = MessageBuilder.withPayload(chargePayTransactionAutoCancelMessage).build();
        MQProducer.doAfterTransactionCommit(() -> sendMessageCore("sendChargePayTransactionCancelMessage",
                message,
                () -> rocketMqTemplate.syncSendDelayTimeSeconds(getDestination(cisChargeDelayTopicName, chargePayTransactionAutoCancel), message, delayedSeconds)
        ));
    }

    public void sendLockingAutoUnlockDelayMessage(ChargeSheetLockingAutoUnlockMessage chargeSheetLockingAutoUnlockMessage) {
        Message<ChargeSheetLockingAutoUnlockMessage> message = MessageBuilder.withPayload(chargeSheetLockingAutoUnlockMessage).build();
        MQProducer.doAfterTransactionCommit(() -> sendMessageCore("sendLockingAutoUnlockDelayMessage",
                message,
                () -> rocketMqTemplate.syncSendDelayTimeSeconds(getDestination(cisChargeDelayTopicName, lockingAutoUnlock), message, Constants.LOCKING_AUTO_UNLOCK_DELAY_SECOND)
        ));
    }

    public void sendUpdateDeDuctTransactionId(ChargeUpdateDeductTransactionIdMessage message){
        MQProducer.doAfterTransactionCommit(() -> sendMessageCore("sendUpdateDeDuctTransactionId",
                MessageBuilder.withPayload(message).build(),
                () -> rocketMqTemplate.syncSend(getDestination(cisChargeTopicName, updateDeDuctTransactionId), message)));
    }

    /**
     * 发送小喇叭消息
     * @param title
     * @param content
     * @param extraData
     */
    public void sendServiceAlertMessage(String title, String content, JsonNode extraData) {
        if (!org.apache.commons.lang.StringUtils.isEmpty(content)) {
            content = content.length() > 512 ? content.substring(0, 512) : content;
        }
        if (!org.apache.commons.lang.StringUtils.isEmpty(title)) {
            title = title.length() > 256 ? title.substring(0, 256) : title;
        }

        ServiceAlertMessage message = new ServiceAlertMessage();
        message.setServiceName(serviceName);

        message.setTitle(title);
        message.setContent(content);
        message.setCreated(Instant.now());
        message.setExtraData(extraData);

        MQProducer.doAfterTransactionCommit(() -> sendMessageCore("notifyServiceAlertMessage",
                MessageBuilder.withPayload(message).build(),
                () -> rocketMqTemplate.syncSend(getDestination(cisMonitorTopicName, serviceAlert), message))
        );
    }


    public void sendChargeSheetPaidGiftCoupon(ChargeSheetPaidMessageForGiftCoupon message) {
        MQProducer.doAfterTransactionCommit(() -> sendMessageCore("sendChargeSheetPaidGiftCoupon",
                MessageBuilder.withPayload(message).build(),
                () -> rocketMqTemplate.syncSend(getDestination(cisChargeTopicName, chargeSheetPaidGiftCoupon), message))
        );
    }

    public void sendChargeSheetRefundGiftCoupon(ChargeSheetRefundMessageForGiftCoupon message) {
        MQProducer.doAfterTransactionCommit(() -> sendMessageCore("sendChargeSheetRefundGiftCoupon",
                MessageBuilder.withPayload(message).build(),
                () -> rocketMqTemplate.syncSend(getDestination(cisChargeTopicName, chargeSheetRefundGiftCoupon), message))
        );
    }

    public void sendChargeSheetRegisterInfoUpdateMessage(ChargeSheetRegisterInfoUpdateMessage message) {
        MQProducer.doAfterTransactionCommit(() -> sendMessageCore("sendChargeSheetRegisterInfoUpdateMessage",
                MessageBuilder.withPayload(message).build(),
                () -> rocketMqTemplate.syncSend(getDestination(cisChargeTopicName, chargeSheetRegisterInfoUpdate), message))
        );
    }

    /**
     * 发送锁库信息变更消息，门诊单据生成收费单时，需要将新生成的chargeFormItemId与门诊锁的id进行绑定，这个步骤在锁库服务绑定，这里发送消息通知锁库服务
     */
    public void sendGoodsLockingUpdateMessage(ChargeSheet chargeSheet, String operatorId) {
        if (Objects.isNull(chargeSheet)) {
            return;
        }

        Map<String, List<ChargeFormItem>> groupByFormIdMap = ChargeUtils.getChargeSheetItems(chargeSheet)
                .stream()
                .filter(chargeFormItem -> StringUtils.isNotEmpty(chargeFormItem.getLockId()) && chargeFormItem.getIsDeleted() == 0)
                .collect(Collectors.groupingBy(ChargeFormItem::getChargeFormId));

        GoodsLockingSheet goodsLockingSheet = new GoodsLockingSheet();
        goodsLockingSheet.setLockSheetId(chargeSheet.getId());
        goodsLockingSheet.setLockingForms(groupByFormIdMap
                .entrySet()
                .stream()
                .map(entry -> {
                    String chargeFormId = entry.getKey();
                    List<ChargeFormItem> chargeFormItems = entry.getValue();
                    GoodsLockingForm goodsLockingForm = new GoodsLockingForm();
                    goodsLockingForm.setLockFormId(chargeFormId);
                    goodsLockingForm.setLockingFormItems(chargeFormItems.stream()
                            .map(chargeFormItem -> {
                                GoodsLockingFormItem goodsLockingFormItem = new GoodsLockingFormItem();
                                goodsLockingFormItem.setLockId(Long.parseLong(chargeFormItem.getLockId()));
                                goodsLockingFormItem.setLockFormItemId(chargeFormItem.getId());
                                return goodsLockingFormItem;
                            })
                            .collect(Collectors.toList())
                    );
                    return goodsLockingForm;
                })
                .collect(Collectors.toList())
        );

        //没有form，就不需要发消息了
        if (CollectionUtils.isEmpty(goodsLockingSheet.getLockingForms())) {
            return;
        }

        LockingServerGoodsLockingReq goodsLockingReq = new LockingServerGoodsLockingReq();
        goodsLockingReq.setChainId(chargeSheet.getChainId());
        goodsLockingReq.setClinicId(chargeSheet.getClinicId());
        goodsLockingReq.setLockingSheetList(Collections.singletonList(goodsLockingSheet));
        goodsLockingReq.setOperatorId(operatorId);
        goodsLockingReq.setType(GoodsLockingType.HIS_OPEN_LOCK);
        goodsLockingReq.setScene(GoodsLockingScene.CHARGE);

        MQProducer.doAfterTransactionCommit(() -> sendMessageCore("sendGoodsLockingUpdateMessage",
                MessageBuilder.withPayload(goodsLockingReq).build(),
                () -> rocketMqTemplate.syncSend(getDestination(cisGoodsLockingTopicName, goodsLockingRegLockRelation), goodsLockingReq))
        );
    }

    public void sendChargeTransactionRecordMessage(List<ChargeTransactionRecord> chargeTransactionRecords) {

        if (CollectionUtils.isEmpty(chargeTransactionRecords)) {
            return;
        }

        MQProducer.doAfterTransactionCommit(() -> {
            ChargeTransactionRecordMessage chargeTransactionRecordMessage = new ChargeTransactionRecordMessage();
            chargeTransactionRecordMessage.setChargeTransactionRecords(JsonUtils.dumpAsJsonNode(chargeTransactionRecords));
            chargeTransactionRecordMessage.setOperatorId(chargeTransactionRecords.get(0).getCreatedBy());
            sendMessageCore("sendChargeTransactionRecordMessage",
                    MessageBuilder.withPayload(chargeTransactionRecordMessage).build(),
                    () -> rocketMqTemplate.syncSend(getDestination(cisChargeTopicName, cisChargeTransactionRecord), chargeTransactionRecordMessage));
        });

    }

    public void sendChargeSheetUpdateRemark(ChargeSheetUpdateRemarkMessage sheetUpdateRemarkMessage) {
        MQProducer.doAfterTransactionCommit(() -> sendMessageCore("sendChargeSheetUpdateRemark",
                MessageBuilder.withPayload(sheetUpdateRemarkMessage).build(),
                () -> rocketMqTemplate.syncSend(getDestination(cisChargeTopicName, cisChargeChargeSheetUpdateRemark), sheetUpdateRemarkMessage))
        );
    }


    public void sendChargeSheetRelockMessage(ChargeSheet chargeSheet, int reLockType, String operatorId) {

        if (Objects.isNull(chargeSheet)) {
            return;
        }

        //只有门诊单和咨询计划单才发送消息
        if (chargeSheet.getType() != ChargeSheet.Type.OUTPATIENT && chargeSheet.getType() != ChargeSheet.Type.MEDICAL_PLAN) {
            return;
        }

        MQProducer.doAfterTransactionCommit(() -> {
            ChargeSheetReLockGoodsMessage message = MessageConvertor.convertToChargeSheetReLockGoodsMessage(chargeSheet, reLockType, operatorId);

            sendMessageCore("sendChargeSheetRelockMessage",
                    MessageBuilder.withPayload(message).build(),
                    () -> rocketMqTemplate.syncSend(getDestination(cisChargeTopicName, cisChargeChargeRenewLock), message));
        });
    }
}
