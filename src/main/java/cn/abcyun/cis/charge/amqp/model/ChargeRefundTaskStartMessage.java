package cn.abcyun.cis.charge.amqp.model;

import cn.abcyun.cis.charge.model.ChargeRefundTask;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.Objects;

@Data
@Accessors(chain = true)
public class ChargeRefundTaskStartMessage {

    private String id;

    /**
     * 当前需要执行的任务子项id
     */
    private String taskItemId;

    /**
     * 连锁id
     */
    private String chainId;

    /**
     * 门店id
     */
    private String clinicId;

    /**
     * 收费单id
     */
    private String chargeSheetId;

    /**
     * 退费的总金额（正值）
     */
    private BigDecimal amount;

    /**
     * 任务状态：0：退费中，10：部分退费成功，20：全部退费成功，30：退费失败
     * {@link ChargeRefundTask.Status}
     */
    private int status;

    private String operatorId;

    public static ChargeRefundTaskStartMessage ofChargeRefundTaskStartMessage(ChargeRefundTask chargeRefundTask, String refundTaskItemId, String operatorId) {
        if (Objects.isNull(chargeRefundTask)) {
            return null;
        }

        ChargeRefundTaskStartMessage chargeRefundTaskStartMessage = new ChargeRefundTaskStartMessage();
        BeanUtils.copyProperties(chargeRefundTask, chargeRefundTaskStartMessage);
        chargeRefundTaskStartMessage.setOperatorId(operatorId);
        chargeRefundTaskStartMessage.setTaskItemId(refundTaskItemId);
        return chargeRefundTaskStartMessage;
    }

}
