package cn.abcyun.cis.charge.amqp.model;

import cn.abcyun.bis.rpc.sdk.bis.model.goods.TryToDispenseRsp;
import cn.abcyun.bis.rpc.sdk.bis.model.order.CreateOrderView;
import lombok.Data;

import java.util.List;

@Data
public class ChargeAirPharmacyCreateOrderTccCancelMessage {

    private TryToDispenseRsp tryToDispenseRsp;

    private List<CreateOrderView> createOrderView;

    private String operatorId;

}
