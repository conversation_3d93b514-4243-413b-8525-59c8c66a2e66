package cn.abcyun.cis.charge.util;

import cn.abcyun.bis.rpc.sdk.bis.model.order.CreateOrderView;
import cn.abcyun.bis.rpc.sdk.cis.model.charge.GoodsFeeType;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.LockConfig;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ;
import cn.abcyun.bis.rpc.sdk.cis.model.common.BitFlagUtils;
import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.bis.rpc.sdk.cis.model.dispensing.DispensingFormItem;
import cn.abcyun.bis.rpc.sdk.cis.model.dispensing.DispensingSheet;
import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.*;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.util.TraceCodeUtils;
import cn.abcyun.bis.rpc.sdk.cis.model.mall.order.VerificationRefundReq;
import cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientBasicInfoView;
import cn.abcyun.bis.rpc.sdk.cis.model.promotion.CardPatientPresentsDeductReq;
import cn.abcyun.bis.rpc.sdk.cis.model.promotion.PatientCardView;
import cn.abcyun.cis.charge.api.model.*;
import cn.abcyun.cis.charge.base.*;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.base.pay.CombinedPayItem;
import cn.abcyun.cis.charge.model.*;
import cn.abcyun.cis.charge.model.ChargeAction;
import cn.abcyun.cis.charge.model.ChargeDeliveryInfo;
import cn.abcyun.cis.charge.model.ChargeForm;
import cn.abcyun.cis.charge.model.ChargeFormItem;
import cn.abcyun.cis.charge.model.ChargeFormItemBatchInfo;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.model.ChargeSheetAdditional;
import cn.abcyun.cis.charge.model.ChargeSheetProcessInfo;
import cn.abcyun.cis.charge.model.ChargeTransaction;
import cn.abcyun.cis.charge.model.ChargeTransactionRecord;
import cn.abcyun.cis.charge.model.ThirdPartyPayInfo;
import cn.abcyun.cis.charge.processor.ExpectedPriceHelper;
import cn.abcyun.cis.charge.processor.PayInfo;
import cn.abcyun.cis.charge.processor.chargerule.FindChargeRuleProcessesInfo;
import cn.abcyun.cis.charge.service.ChargeService;
import cn.abcyun.cis.charge.service.ShebaoService;
import cn.abcyun.cis.charge.service.TobMessageService;
import cn.abcyun.cis.charge.service.dto.*;
import cn.abcyun.cis.charge.service.dto.ChargeFormItemView;
import cn.abcyun.cis.charge.service.dto.PatientPointsInfoView;
import cn.abcyun.cis.charge.service.dto.PromotionDeductItem;
import cn.abcyun.cis.charge.service.dto.PromotionView;
import cn.abcyun.cis.charge.service.rpc.CisScGoodsService;
import cn.abcyun.cis.commons.amqp.message.ChargeSheetMessage;
import cn.abcyun.cis.commons.amqp.message.PatientOrderMessage;
import cn.abcyun.cis.commons.model.CisPatientAddress;
import cn.abcyun.cis.commons.model.CisPatientAge;
import cn.abcyun.cis.commons.model.CisPatientInfo;
import cn.abcyun.cis.commons.model.ComposeType;
import cn.abcyun.cis.commons.rpc.charge.*;
import cn.abcyun.cis.commons.rpc.charge.ChargeAdditionalFee;
import cn.abcyun.cis.commons.rpc.charge.ChargeDeliveryCompany;
import cn.abcyun.cis.commons.rpc.charge.ChargeDeliveryInfoView;
import cn.abcyun.cis.commons.rpc.crm.PatientInfo;
import cn.abcyun.cis.commons.rpc.crm.PatientMemberInfo;
import cn.abcyun.cis.commons.rpc.outpatient.ExtendDiagnosisInfo;
import cn.abcyun.cis.commons.rpc.outpatient.UsageInfo;
import cn.abcyun.cis.commons.rpc.patient.MemberInfo;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrder;
import cn.abcyun.cis.commons.rpc.registration.RegistrationFormItem;
import cn.abcyun.cis.commons.util.ExecutableUtils;
import cn.abcyun.cis.commons.util.MD5Utils;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.common.log.marker.AbcLogMarker;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class ChargeUtils {
    private static final Logger sLogger = LoggerFactory.getLogger(ChargeUtils.class);

    public static ChargeAction insertChargeAction(String chargeActionId,
                                                  ChargeSheet chargeSheet,
                                                  int type,
                                                  int payStatus,
                                                  BigDecimal amount,
                                                  ChargeAction.PayActionInfo payActionInfo,
                                                  String chargeComment,
                                                  Instant specifiedChargedTime,
                                                  String operatorId) {
        return insertChargeAction(chargeActionId, chargeSheet, type, payStatus, amount, Collections.singletonList(payActionInfo), chargeComment, specifiedChargedTime, operatorId, null);
    }

    public static ChargeAction insertChargeAction(String chargeActionId,
                                                  ChargeSheet chargeSheet,
                                                  int type,
                                                  int payStatus,
                                                  BigDecimal amount,
                                                  List<ChargeAction.PayActionInfo> payActionInfos,
                                                  String chargeComment,
                                                  Instant specifiedChargedTime,
                                                  String operatorId) {
        return insertChargeAction(chargeActionId, chargeSheet, type, payStatus, amount, payActionInfos, chargeComment, specifiedChargedTime, operatorId, null);
    }

    public static ChargeAction insertChargeAction(String chargeActionId,
                                                  ChargeSheet chargeSheet,
                                                  int type,
                                                  int payStatus,
                                                  BigDecimal amount,
                                                  ChargeAction.PayActionInfo payActionInfo,
                                                  String chargeComment,
                                                  Instant specifiedChargedTime,
                                                  String operatorId,
                                                  String checkerId) {
        Assert.notNull(chargeSheet, "chargeSheet can not be null");
        Assert.notNull(payActionInfo, "payActionInfo can not be null");
        return insertChargeAction(chargeActionId, chargeSheet, type, payStatus, amount, Collections.singletonList(payActionInfo), chargeComment, specifiedChargedTime, operatorId, checkerId);
    }

    public static ChargeAction insertChargeAction(String chargeActionId, ChargeSheet chargeSheet, int type, int payStatus, BigDecimal amount, List<ChargeAction.PayActionInfo> payActionInfos, String chargeComment, Instant specifiedChargedTime, String operatorId, String checkerId) {
        ChargeAction chargeAction = new ChargeAction();
        chargeAction.setId(org.apache.commons.lang3.StringUtils.isNotEmpty(chargeActionId) ? chargeActionId : AbcIdUtils.getUUID());
        chargeAction.setPatientId(chargeSheet.getPatientId());
        chargeAction.setPatientOrderId(chargeSheet.getPatientOrderId());
        chargeAction.setClinicId(chargeSheet.getClinicId());
        chargeAction.setChainId(chargeSheet.getChainId());
        chargeAction.setChargeSheetId(chargeSheet.getId());
        chargeAction.setPayStatus(payStatus);

        chargeAction.setPayActionInfoJson(JsonUtils.dump(payActionInfos));
        chargeAction.setAmount(amount);
        chargeAction.setType(type);
        chargeAction.setChargeComment(chargeComment);
        chargeAction.setCreated(Instant.now());
        chargeAction.setCreatedBy(operatorId);
        chargeAction.setCheckerId(checkerId);
        chargeAction.setSpecifiedChargedTime(specifiedChargedTime);
        if (chargeSheet.getChargeActions() == null) {
            chargeSheet.setChargeActions(new ArrayList<>());
        }
        chargeSheet.getChargeActions().add(chargeAction);
        return chargeAction;
    }


    /**
     * 已作废了
     * see {@link ChargePayModeUtils#buildAbcPaySubModeName(int)}
     *
     * @param payActionInfo
     * @param paySubMode
     */
    @Deprecated
    public static void buildAbcPaySubModeName(ChargeAction.PayActionInfo payActionInfo, int paySubMode) {
        payActionInfo.setPaySubMode(paySubMode);
        payActionInfo.setPayMode(Constants.ChargePayMode.ABC_PAY);
        payActionInfo.setPayModeName("ABC支付");
        switch (paySubMode) {
            case Constants.ChargePaySubMode.ABC_PAY_WECHAT_NATIVE:
            case Constants.ChargePaySubMode.ABC_PAY_WECHAT_JS:
            case Constants.ChargePaySubMode.ABC_PAY_WECHAT_MINI:
            case Constants.ChargePaySubMode.ALLIN_PAY_SCAN_WECHAT:
                payActionInfo.setPaySubModeName("微信");
                break;
            case Constants.ChargePaySubMode.ABC_PAY_ALI_NATIVE:
            case Constants.ChargePaySubMode.ALLIN_PAY_SCAN_ALI:
                payActionInfo.setPaySubModeName("支付宝");
                break;
            case Constants.ChargePaySubMode.ABC_PAY_UNION_NATIVE:
            case Constants.ChargePaySubMode.ALLIN_PAY_SCAN_UNION:
                payActionInfo.setPaySubModeName("银联");
                break;
        }
    }

    private static int getChargeTransactionOperateNo(List<ChargeTransaction> chargeTransactions) {
        int operateNo = 0;
        if (CollectionUtils.isEmpty(chargeTransactions)) {
            return operateNo;
        }

        ChargeTransaction chargeTransaction = chargeTransactions.stream().max(Comparator.comparing(ChargeTransaction::getOperateNo)).get();
        if (chargeTransaction != null) {
            operateNo = chargeTransaction.getOperateNo() + 1;
        }
        return operateNo;
    }


    public static ChargeAction updateChargeActionAmount(ChargeAction chargeAction, int payMode, int paySubMode, String payModeName, String thirdPartyPayCardId, BigDecimal amount) {
        // 由于ABC支付在业务系统展示为3层,所以这里单独处理为两层

        ChargeAction.PayActionInfo payActionInfo = new ChargeAction.PayActionInfo();
        payActionInfo.setPayMode(payMode);
        payActionInfo.setPaySubMode(paySubMode);
        payActionInfo.setAmount(amount);
        payActionInfo.setPayModeName(payModeName);
        payActionInfo.setThirdPartyPayCardId(thirdPartyPayCardId);
        if (payMode == Constants.ChargePayMode.ABC_PAY) {
            buildAbcPaySubModeName(payActionInfo, paySubMode);
        }
        List<ChargeAction.PayActionInfo> payActionInfos = new ArrayList<>();
        payActionInfos.add(payActionInfo);
        chargeAction.setPayActionInfoJson(JsonUtils.dump(payActionInfos));
        chargeAction.setPayActionInfos(payActionInfos);
        chargeAction.setAmount(amount);
        return chargeAction;
    }


    @Deprecated
    public static ChargeTransaction insertChargeTransaction(ChargeSheet chargeSheet, int payMode, int paySubMode, BigDecimal amount, BigDecimal principalAmount, BigDecimal presentAmount,
                                                            BigDecimal income, BigDecimal change, BigDecimal needPay, int paySource, String thirdPartyPayTransactionId,
                                                            BigDecimal thirdPartyPayCardBalance, String thirdPartyPayCardId, String thirdPartyPayCardOwner, String thirdPartyPayIdCardNum, ChargeAction chargeAction, String operatorId) {
        ThirdPartyPayInfo thirdPartyPayInfo = null;
        if (!TextUtils.isEmpty(thirdPartyPayTransactionId)) {
            thirdPartyPayInfo = new ThirdPartyPayInfo();
            thirdPartyPayInfo.setCardId(thirdPartyPayCardId);
            thirdPartyPayInfo.setTransactionId(thirdPartyPayTransactionId);
            thirdPartyPayInfo.setCardOwner(thirdPartyPayCardOwner);
            thirdPartyPayInfo.setCardBalance(thirdPartyPayCardBalance);
            thirdPartyPayInfo.setIdCardNum(thirdPartyPayIdCardNum);
        }
        return insertChargeTransaction(null, chargeSheet, payMode, paySubMode, amount, principalAmount, presentAmount, income, change, needPay, paySource, thirdPartyPayInfo, chargeAction, operatorId);
    }


    public static ChargeTransaction insertChargeTransaction(String chargeTransactionId, ChargeSheet chargeSheet, int payMode, int paySubMode, BigDecimal amount, BigDecimal principalAmount, BigDecimal presentAmount,
                                                            BigDecimal income, BigDecimal change, BigDecimal needPay, int paySource, ThirdPartyPayInfo thirdPartyPayInfo,
                                                            ChargeAction chargeAction, String operatorId) {
        ChargeTransaction chargeTransaction = new ChargeTransaction();
        chargeTransaction.setAmount(amount);
        chargeTransaction.setPresentAmount(presentAmount);
        chargeTransaction.setPrincipalAmount(principalAmount);
        chargeTransaction.setPayMode(payMode);
        chargeTransaction.setPaySubMode(paySubMode);
        chargeTransaction.setIncome(income);
        chargeTransaction.setChange(change); //正数
        chargeTransaction.setNeedPay(needPay); //正数
        chargeTransaction.setChangePayMode(Constants.ChargePayMode.CASH);
        chargeTransaction.setId(!StringUtils.isEmpty(chargeTransactionId) ? chargeTransactionId : AbcIdUtils.getUUID());
        chargeTransaction.setPatientId(chargeSheet.getPatientId());
        chargeTransaction.setPatientOrderId(chargeSheet.getPatientOrderId());
        chargeTransaction.setClinicId(chargeSheet.getClinicId());
        chargeTransaction.setChainId(chargeSheet.getChainId());
        chargeTransaction.setChargeSheetId(chargeSheet.getId());
        chargeTransaction.setSellerId(chargeSheet.getSellerId());
        chargeTransaction.setPaySource(paySource);
        chargeTransaction.setHospitalOrderId(chargeSheet.getHospitalOrderId());
        chargeTransaction.setHospitalSheetId(chargeSheet.getHospitalSheetId());

        if (thirdPartyPayInfo != null) {
            chargeTransaction.setThirdPartyPayTransactionId(thirdPartyPayInfo.getTransactionId());
            chargeTransaction.setThirdPartyPayCardBalance(thirdPartyPayInfo.getCardBalance());
            chargeTransaction.setThirdPartyPayCardId(thirdPartyPayInfo.getCardId());
            chargeTransaction.setThirdPartyPayInfo(thirdPartyPayInfo);
            chargeTransaction.setThirdPartyPayInfoJson(JsonUtils.dump(thirdPartyPayInfo));
        }

        chargeTransaction.setChargeActionId(Optional.ofNullable(chargeAction).map(ChargeAction::getId).orElse(""));
        chargeTransaction.setSpecifiedChargedTime(Optional.ofNullable(chargeAction).map(ChargeAction::getSpecifiedChargedTime).orElse(null));
        chargeTransaction.setOperateNo(getChargeTransactionOperateNo(chargeSheet.getChargeTransactions()));

        FillUtils.fillCreatedBy(chargeTransaction, operatorId);

        if (chargeSheet.getChargeTransactions() == null) {
            chargeSheet.setChargeTransactions(new ArrayList<>());
        }

        //处理社保现金支付问题
        dealHealthCardCashFee(chargeSheet.getChargeTransactions(), chargeTransaction, chargeAction);
        chargeSheet.getChargeTransactions().add(chargeTransaction);
        return chargeTransaction;
    }

    private static void dealHealthCardCashFee(List<ChargeTransaction> chargeTransactions, ChargeTransaction thisTimeChargeTransaction, ChargeAction chargeAction) {
        if (thisTimeChargeTransaction == null) {
            return;
        }

        //如果本次支付是社保支付
        if (thisTimeChargeTransaction.getPayMode() == Constants.ChargePayMode.HEALTH_CARD) {

            if (chargeAction.getType() == ChargeAction.Type.PAY) {
                BigDecimal healthCardCashReceivableFee = Optional.ofNullable(thisTimeChargeTransaction.getThirdPartyPayInfo()).map(ThirdPartyPayInfo::getReceivableFee).orElse(thisTimeChargeTransaction.getAmount());
                String medType = Optional.ofNullable(thisTimeChargeTransaction.getThirdPartyPayInfo()).map(ThirdPartyPayInfo::getMedType).orElse("");

                thisTimeChargeTransaction.setHealthCardCashReceivedFee(BigDecimal.ZERO);
                thisTimeChargeTransaction.setHealthCardCashReceivableFee(healthCardCashReceivableFee);
                thisTimeChargeTransaction.setHealthCardMedType(medType);
            } else {
                chargeTransactions.stream()
                        .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                        .filter(chargeTransaction -> chargeTransaction.getAmount().compareTo(BigDecimal.ZERO) >= 0)
                        .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.HEALTH_CARD)
                        .max(Comparator.comparing(ChargeTransaction::getOperateNo))
                        .ifPresent(lastHealthCardChargeTransaction ->
                                thisTimeChargeTransaction.setHealthCardMedType(lastHealthCardChargeTransaction.getHealthCardMedType())
                        );
            }

            return;
        }

        ChargeTransaction lastHealthCardChargeTransaction = chargeTransactions.stream()
                .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                .filter(chargeTransaction -> chargeTransaction.getAmount().compareTo(BigDecimal.ZERO) >= 0)
                .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.HEALTH_CARD)
                .sorted(Comparator.comparing(ChargeTransaction::getOperateNo).reversed())
                .findFirst().orElse(null);

        if (lastHealthCardChargeTransaction == null) {
            return;
        }

        BigDecimal healthCardCashReceivableFee = MathUtils.wrapBigDecimalOrZero(lastHealthCardChargeTransaction.getHealthCardCashReceivableFee());

        if (healthCardCashReceivableFee.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }

        BigDecimal totalHealthCardCashReceivedFee = chargeTransactions.stream()
                .filter(chargeTransaction -> chargeTransaction.getOperateNo() > lastHealthCardChargeTransaction.getOperateNo())
                .filter(chargeTransaction -> chargeTransaction.getPayMode() != Constants.ChargePayMode.HEALTH_CARD)
                .map(chargeTransaction -> MathUtils.wrapBigDecimalOrZero(chargeTransaction.getHealthCardCashReceivedFee()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        //本次为收费时
        if (chargeAction.getType() == ChargeAction.Type.PAY) {

            if (healthCardCashReceivableFee.subtract(totalHealthCardCashReceivedFee).compareTo(BigDecimal.ZERO) <= 0) {
                return;
            }

            BigDecimal thisTimeHealthCardCashReceivedFee = MathUtils.min(thisTimeChargeTransaction.getAmount(), healthCardCashReceivableFee.subtract(totalHealthCardCashReceivedFee));
            thisTimeChargeTransaction.setHealthCardCashReceivedFee(thisTimeHealthCardCashReceivedFee);
            thisTimeChargeTransaction.setHealthCardMedType(lastHealthCardChargeTransaction.getHealthCardMedType());

        } else {

            BigDecimal thisTimeHealthCardCashReceivedFee = MathUtils.min(thisTimeChargeTransaction.getAmount().abs(), totalHealthCardCashReceivedFee).negate();
            thisTimeChargeTransaction.setHealthCardCashReceivedFee(thisTimeHealthCardCashReceivedFee);
            thisTimeChargeTransaction.setHealthCardMedType(lastHealthCardChargeTransaction.getHealthCardMedType());

        }
    }

    public static ChargeTransaction findByChargePayTransactionId(ChargeSheet chargeSheet, String chargePayTransactionId) {
        if (chargeSheet == null || chargeSheet.getChargeTransactions() == null || TextUtils.isEmpty(chargePayTransactionId)) {
            return null;
        }

        return chargeSheet.getChargeTransactions()
                .stream()
                .filter(chargeTransaction -> TextUtils.equals(chargeTransaction.getThirdPartyPayTransactionId(), chargePayTransactionId))
                .findFirst()
                .orElse(null);
    }

    public static List<ChargeFormItem> getChargeSheetItems(ChargeSheet chargeSheet) {
        if (chargeSheet == null || chargeSheet.getChargeForms() == null) {
            return new ArrayList<>();
        }
        return chargeSheet.getChargeForms()
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getChargeFormItems() != null)
                .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                .filter(item -> item.getIsDeleted() == 0)
                .collect(Collectors.toList());
    }

    public static List<ChargeFormItemBatchInfo> getChargeSheetItemBatchInfos(ChargeSheet chargeSheet) {
        List<ChargeFormItem> chargeFormItems = getChargeSheetItems(chargeSheet);

        if (org.apache.commons.collections.CollectionUtils.isEmpty(chargeFormItems)) {
            return new ArrayList<>();
        }

        return chargeFormItems.stream()
                .filter(chargeFormItem -> chargeFormItem.getStatus() == Constants.ChargeSheetStatus.UNCHARGED)
                .flatMap(chargeFormItem -> Optional.ofNullable(chargeFormItem.getChargeFormItemBatchInfos())
                        .orElse(new ArrayList<>())
                        .stream())
                .filter(chargeFormItemBatchInfo -> chargeFormItemBatchInfo.getIsDeleted() == 0)
                .collect(Collectors.toList());
    }

    public static List<ChargeFormItem> getChargeSheetItemsFilterSourceFormTypes(ChargeSheet chargeSheet, List<Integer> filterSourceFormTypes) {
        if (chargeSheet == null || chargeSheet.getChargeForms() == null) {
            return new ArrayList<>();
        }
        if (filterSourceFormTypes == null) {
            filterSourceFormTypes = new ArrayList<>();
        }
        List<Integer> finalFilterSourceFormTypes = filterSourceFormTypes;
        return chargeSheet.getChargeForms()
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getChargeFormItems() != null)
                .filter(chargeForm -> !finalFilterSourceFormTypes.contains(chargeForm.getSourceFormType()))
                .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                .filter(item -> item.getIsDeleted() == 0)
                .collect(Collectors.toList());
    }

    public static List<ChargeFormItem> getChargeSheetItemsFilterMaterialAndPrescriptionExternal(ChargeSheet chargeSheet) {
        if (chargeSheet == null || chargeSheet.getChargeForms() == null) {
            return new ArrayList<>();
        }
        return chargeSheet.getChargeForms()
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getSourceFormType() != Constants.SourceFormType.MATERIAL && chargeForm.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_EXTERNAL)
                .filter(chargeForm -> chargeForm.getChargeFormItems() != null)
                .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                .filter(item -> item.getIsDeleted() == 0)
                .collect(Collectors.toList());
    }

    public static List<ChargeFormItem> getChargeSheetItemsFilterMaterialAndPrescriptionExternalAndCompose(ChargeSheet chargeSheet) {
        if (chargeSheet == null || chargeSheet.getChargeForms() == null) {
            return new ArrayList<>();
        }
        return chargeSheet.getChargeForms()
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getSourceFormType() != Constants.SourceFormType.AIR_PHARMACY
                        && chargeForm.getSourceFormType() != Constants.SourceFormType.MATERIAL
                        && chargeForm.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_EXTERNAL
                        && chargeForm.getSourceFormType() != Constants.SourceFormType.COMPOSE_PRODUCT)
                .filter(chargeForm -> chargeForm.getChargeFormItems() != null)
                .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                .filter(item -> item.getIsDeleted() == 0)
                .collect(Collectors.toList());
    }

    public static List<ChargeForm> getChargeFormsFilterMaterialAndCompose(ChargeSheet chargeSheet) {
        if (chargeSheet == null || chargeSheet.getChargeForms() == null) {
            return new ArrayList<>();
        }
        return chargeSheet.getChargeForms()
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getSourceFormType() != Constants.SourceFormType.MATERIAL && chargeForm.getSourceFormType() != Constants.SourceFormType.COMPOSE_PRODUCT)
                .filter(chargeForm -> chargeForm.getChargeFormItems() != null || chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_TYPE_EYE_GLASS)
                .collect(Collectors.toList());
    }

    public static List<ChargeForm> getChargeFormsFilterMaterialAndProductAndCompose(ChargeSheet chargeSheet) {
        if (chargeSheet == null || chargeSheet.getChargeForms() == null) {
            return new ArrayList<>();
        }
        return chargeSheet.getChargeForms()
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> (chargeForm.getSourceFormType() != Constants.SourceFormType.MATERIAL && chargeForm.getSourceFormType() != Constants.SourceFormType.ADDITIONAL_SALE_PRODUCT_FORM) && chargeForm.getSourceFormType() != Constants.SourceFormType.COMPOSE_PRODUCT)
                .filter(chargeForm -> chargeForm.getChargeFormItems() != null || chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_TYPE_EYE_GLASS)
                .collect(Collectors.toList());
    }

    public static List<ChargeFormItem> getChargeSheetItems(List<ChargeSheet> chargeSheets) {
        if (chargeSheets == null) {
            return new ArrayList<>();
        }
        return chargeSheets.stream().flatMap(chargeSheet -> getChargeSheetItems(chargeSheet).stream()).collect(Collectors.toList());
    }

    public static List<ChargeFormItem> getChargeFormItems(ChargeForm chargeForm) {
        if (chargeForm == null || chargeForm.getChargeFormItems() == null) {
            return new ArrayList<>();
        }
        return chargeForm.getChargeFormItems()
                .stream()
                .filter(item -> item.getIsDeleted() == 0)
                .collect(Collectors.toList());
    }

    /**
     * 找出退费项
     *
     * @param chargeSheet
     * @return
     */
    public static Map<String, List<ChargeFormItem>> generateRefundChargeFormItemsMap(ChargeSheet chargeSheet) {
        List<ChargeFormItem> chargeFormItems = ChargeUtils.getChargeSheetItems(chargeSheet);
        if (CollectionUtils.isEmpty(chargeFormItems)) {
            return Collections.emptyMap();
        }

        chargeFormItems = chargeFormItems.stream()
                .filter(chargeFormItem -> chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.REFUNDED)
                .filter(chargeFormItem -> !TextUtils.isEmpty(chargeFormItem.getAssociateFormItemId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(chargeFormItems)) {
            return Collections.emptyMap();
        }

        Map<String, List<ChargeFormItem>> refundChargeFormItemsMap = ListUtils.groupByKey(chargeFormItems, ChargeFormItem::getAssociateFormItemId);
        return refundChargeFormItemsMap;
    }

    public static List<ChargeTransaction> getChargeTransactions(ChargeSheet chargeSheet) {
        if (chargeSheet == null || chargeSheet.getChargeTransactions() == null) {
            return new ArrayList<>();
        }
        return chargeSheet.getChargeTransactions();
    }

    public static List<ChargeAction> getChargeActions(ChargeSheet chargeSheet) {
        if (chargeSheet == null || chargeSheet.getChargeActions() == null) {
            return new ArrayList<>();
        }
        return chargeSheet.getChargeActions();
    }

    public static List<CalculateChargeFormItemView> getCalculateChargeFormItemViews(CalculateChargeResult calculateChargeResult) {
        if (calculateChargeResult == null || calculateChargeResult.getChargeForms() == null) {
            return new ArrayList<>();
        }
        return calculateChargeResult.getChargeForms()
                .stream()
                .filter(calculateChargeFormView -> calculateChargeFormView.getChargeFormItems() != null)
                .flatMap(calculateChargeFormView -> calculateChargeFormView.getChargeFormItems().stream())
                .collect(Collectors.toList());
    }

    public static List<ChargeForm> getChargeSheetForms(ChargeSheet chargeSheet) {
        if (chargeSheet == null || chargeSheet.getChargeForms() == null) {
            return new ArrayList<>();
        }
        return chargeSheet.getChargeForms()
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .collect(Collectors.toList());
    }

    public static List<ChargeForm> getChargeSheetForms(ChargeSheet chargeSheet, int sourceFormType) {
        if (chargeSheet == null || chargeSheet.getChargeForms() == null) {
            return new ArrayList<>();
        }
        return chargeSheet.getChargeForms().stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getSourceFormType() == sourceFormType).collect(Collectors.toList());
    }

    public static List<ChargeForm> getChargeSheetForms(ChargeSheet chargeSheet, List<Integer> sourceFormTypes) {
        if (chargeSheet == null || chargeSheet.getChargeForms() == null) {
            return new ArrayList<>();
        }
        return chargeSheet.getChargeForms().stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> sourceFormTypes.contains(chargeForm.getSourceFormType())).collect(Collectors.toList());
    }

    public static <C> Collection<C> wrapList(Collection<C> c) {
        if (c == null) {
            return new ArrayList<>();
        }
        return c;
    }

    /**
     * 重置收费单信息
     *
     * @param chargeSheet
     * @param withAdjustmentPrice 是否包含议价
     */
    public static void renewChargeSheet(ChargeSheet chargeSheet, boolean withAdjustmentPrice, String operatorId) {
        if (chargeSheet.getChargeForms() != null) {
            chargeSheet.getChargeForms().forEach(chargeForm -> renewChargeForm(chargeForm, withAdjustmentPrice, operatorId));
            chargeSheet.getChargeForms().forEach(chargeForm -> {
                if ((CollectionUtils.isEmpty(chargeForm.getChargeFormItems()) || chargeForm.getSourceFormType() == Constants.SourceFormType.GIFT_PRODUCT) && chargeForm.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_TYPE_EYE_GLASS) {
                    chargeForm.deleteModel(operatorId);
                }
            });
        }

        if (chargeSheet.getAdditionalFees() != null) {
            chargeSheet.getAdditionalFees().forEach(chargeAdditional -> chargeAdditional.deleteModel(operatorId));
        }

        if (chargeSheet.getChargeTransactions() != null) {
            chargeSheet.getChargeTransactions().forEach(chargeTransaction -> chargeTransaction.setIsPaidback(1));
        }

        chargeSheet.setStatus(Constants.ChargeSheetStatus.UNCHARGED);
        chargeSheet.setPromotionInfoJson(null);

        if (!CollectionUtils.isEmpty(chargeSheet.getCouponPromotionInfos())) {
            chargeSheet.getCouponPromotionInfos().forEach(chargeCouponPromotionInfo -> chargeCouponPromotionInfo.deleteModel(operatorId));
        }

        if (!CollectionUtils.isEmpty(chargeSheet.getGiftRulePromotionInfos())) {
            chargeSheet.getGiftRulePromotionInfos().forEach(chargeGiftRulePromotionInfo -> chargeGiftRulePromotionInfo.deleteModel(operatorId));
        }

        if (chargeSheet.getAdditional() != null) {
            ChargeSheetAdditional additional = chargeSheet.getAdditional();
            additional.setAutoUnlockTime(null);
            additional.setLockStatus(Constants.ChargeSheetLockStatus.LOCK_STATUS_UNLOCK);
            additional.setLockStatusV2(Constants.ChargeSheetLockStatusV2.NONE);
            chargeSheet.getAdditional().setInvoiceStatus(Constants.ChargeSheetInvoiceStatus.NONE);
        }

        if (chargeSheet.getPatientPointsPromotionInfo() != null) {
            chargeSheet.getPatientPointsPromotionInfo().deleteModel(operatorId);
        }

        chargeSheet.setOddFee(BigDecimal.ZERO);
        if (ChargeSheet.Type.paidSheetWeClinicAlwaysShowTypes().contains(chargeSheet.getType())) {
            chargeSheet.setSendToPatientStatus(0);
        }
        Optional.ofNullable(chargeSheet.getAdditional())
                .ifPresent(addtional -> addtional.setUseMemberFlag(ChargeSheetAdditional.UseMemberFlag.DEFAULT_MEMBER));

        if (!withAdjustmentPrice) {
            chargeSheet.setDraftAdjustmentFee(BigDecimal.ZERO);
        }
    }

    public static void renewChargeForm(ChargeForm chargeForm, boolean withAdjustmentPrice, String operatorId) {
        if (chargeForm.getChargeFormItems() == null) {
            return;
        }
        /**
         * 因为退费单的设计本地药房的单是删除了再插入新的，而空中药房的设计是标记删初，不能删掉只能把状态再打开【陈磊】
         * */
        if (chargeForm.getSourceFormType() != Constants.SourceFormType.AIR_PHARMACY) {
            chargeForm.getChargeFormItems().forEach(chargeFormItem -> {
                if (chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.REFUNDED || chargeFormItem.getIsGift() == Constants.ChargeFormItemGiftType.PROMOTION_GIFT) {
                    chargeFormItem.deleteModel(operatorId);
                }
            });
        } else {
            //空中药房，不能删
        }
        chargeForm.setIsCanBeRefund(0);
        chargeForm.getChargeFormItems().forEach(chargeFormItem -> renewChargeFormItem(chargeFormItem, withAdjustmentPrice, operatorId));
        chargeForm.setStatus(Constants.ChargeFormStatus.UNCHARGED);
        chargeForm.setReceivedPrice(BigDecimal.ZERO);

        if (chargeForm.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY) {
            chargeForm.setPromotionInfoJson(null);
            chargeForm.setCouponPromotionInfoJson(null);
            chargeForm.setGiftRulePromotionInfoJson(null);
            chargeForm.setDiscountPrice(BigDecimal.ZERO);
            chargeForm.setAdjustmentPrice(BigDecimal.ZERO);
            chargeForm.setExpectedPriceFlag(0);
            chargeForm.setPromotionPrice(BigDecimal.ZERO);
            chargeForm.setRefundTotalPrice(BigDecimal.ZERO);
            chargeForm.setRefundDiscountPrice(BigDecimal.ZERO);
            chargeForm.setRefundUnitCount(BigDecimal.ZERO);
            if (!withAdjustmentPrice) {
                chargeForm.setExpectedTotalPrice(null);
                chargeForm.setTotalPrice(chargeForm.getChargeFormItems().stream()
                        .filter(item -> item.getIsDeleted() == 0)
                        .map(ChargeFormItem::getTotalPrice)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                );
            }
        }
    }

    public static void renewChargeFormItem(ChargeFormItem chargeFormItem, boolean withAdjustmentPrice, String operatorId) {
        chargeFormItem.setStatus(Constants.ChargeFormItemStatus.UNCHARGED);
        chargeFormItem.setV2Status(Constants.ChargeFormItemStatus.UNCHARGED);
        chargeFormItem.setPayStatus(ChargeFormItem.PayStatus.UNPAID);
        chargeFormItem.setPromotionInfoJson(null);
        chargeFormItem.setCouponPromotionInfoJson(null);
        chargeFormItem.setGiftRulePromotionInfoJson(null);
        chargeFormItem.setDiscountPrice(BigDecimal.ZERO);
        chargeFormItem.setAdjustmentPrice(BigDecimal.ZERO);
        chargeFormItem.setUnitAdjustmentFee(BigDecimal.ZERO);

        chargeFormItem.setFractionPrice(BigDecimal.ZERO);
        chargeFormItem.setPromotionPrice(BigDecimal.ZERO);
        chargeFormItem.setRefundTotalPrice(BigDecimal.ZERO);
        chargeFormItem.setRefundDiscountPrice(BigDecimal.ZERO);
        chargeFormItem.setRefundUnitCount(BigDecimal.ZERO);
        chargeFormItem.setRefundDoseCount(null);
        chargeFormItem.setReceivedPrice(BigDecimal.ZERO);
        chargeFormItem.setReceivablePrice(BigDecimal.ZERO);
        chargeFormItem.setLockId(null);
        chargeFormItem.setDeductTotalCount(null);

        if (!withAdjustmentPrice) {
            chargeFormItem.setExpectedUnitPrice(null);
            chargeFormItem.setExpectedTotalPrice(null);
            chargeFormItem.setExpectedTotalPriceRatio(null);
        }

        if (chargeFormItem.getChargeFormItemBatchInfos() != null) {
            chargeFormItem.getChargeFormItemBatchInfos().forEach(chargeFormItemBatchInfo -> chargeFormItemBatchInfo.deleteModel(operatorId));
        }

        if (chargeFormItem.getAdditional() != null && !CollectionUtils.isEmpty(chargeFormItem.getAdditional().getTraceableCodeList())) {
            // 退费重收追溯码重置为未使用状态
            for (TraceableCode traceableCode : chargeFormItem.getAdditional().getTraceableCodeList()) {
                if (traceableCode != null && traceableCode.getUsed() != 0) {
                    traceableCode.setUsed(0);
                }
            }
        }

        ExpectedPriceHelper.process(chargeFormItem, chargeFormItem.getSourceUnitPrice(), chargeFormItem.getExpectedUnitPrice(), chargeFormItem.getExpectedTotalPrice(), chargeFormItem.getExpectedTotalPriceRatio());
    }

    public static boolean isExecutableItem(ChargeFormItem chargeFormItem, boolean includeExternal) {
        if (chargeFormItem == null) {
            return false;
        }

        if (chargeFormItem.getProductType() == Constants.ProductType.TREATMENT || chargeFormItem.getProductType() == Constants.ProductType.NURSE) {
            return true;
        }
        return isTransfusionExecutableItem(chargeFormItem, includeExternal);
    }

    public static Predicate<ChargeFormItem> getNeedExecuteTherapySheetPredicate(boolean containProductOtherPrice, boolean containProductMaterialsPrice) {
        return chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.TREATMENT //治疗理疗
                || chargeFormItem.getProductType() == Constants.ProductType.NURSE //护理
                //包含其他费用且item为其他收费项
                || (containProductOtherPrice && chargeFormItem.getProductType() == Constants.ProductType.OTHER_FEE)
                //包含商品材料且item为商品或材料
                || (containProductMaterialsPrice && (chargeFormItem.getProductType() == Constants.ProductType.MATERIAL || chargeFormItem.getProductType() == Constants.ProductType.SALE_PRODUCT));

    }

    public static boolean isTransfusionExecutableItem(ChargeFormItem chargeFormItem, boolean includeExternal) {
        if (chargeFormItem == null) {
            return false;
        }

        UsageInfo firstItemUsageInfo = JsonUtils.readValue(chargeFormItem.getUsageInfoJson(), UsageInfo.class);

        if (firstItemUsageInfo == null) {
            return false;
        }

        if (includeExternal) {
            return ExecutableUtils.isExecutableUsage(firstItemUsageInfo.getUsage());
        }

        return ExecutableUtils.isExecutableUsage(firstItemUsageInfo.getUsage()) && !ExecutableUtils.isExecutableExternalUsage(firstItemUsageInfo.getUsage());
    }


    public static boolean isContainsRegistration(ChargeSheet chargeSheet) {
        boolean ret = false;
        if (chargeSheet == null || chargeSheet.getChargeForms() == null) {
            return ret;
        }
        ret = getChargeSheetItems(chargeSheet).stream()
                .filter(chargeFormItem -> chargeFormItem.getIsDeleted() == 0)
                .anyMatch(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.REGISTRATION);
        return ret;
    }

    public static boolean isContainsNonRegistration(ChargeSheet chargeSheet) {
        boolean ret = false;
        if (chargeSheet == null || chargeSheet.getChargeForms() == null) {
            return ret;
        }
        ret = getChargeSheetItems(chargeSheet).stream()
                .filter(chargeFormItem -> chargeFormItem.getIsDeleted() == 0)
                .anyMatch(chargeFormItem -> chargeFormItem.getProductType() != Constants.ProductType.REGISTRATION);
        return ret;
    }

    public static ChargeSheet pickRegistrationChargeSheet(List<ChargeSheet> chargeSheets) {
        if (chargeSheets == null) {
            return null;
        }

        return chargeSheets.stream().filter(ChargeUtils::isContainsRegistration).findFirst().orElse(null);
    }

    public static RegistrationFormItem registrationFormItemCopyAndRemoveFeeDetail(RegistrationFormItem registrationFormItem) {

        if (Objects.isNull(registrationFormItem)) {
            return null;
        }

        RegistrationFormItem registrationFormItemCopy = JsonUtils.readValue(JsonUtils.dump(registrationFormItem), RegistrationFormItem.class);

        registrationFormItemCopy.setFeeDetails(null);
        return registrationFormItemCopy;
    }


    public static ChargeSheet pickOutpatientChargeSheet(List<ChargeSheet> chargeSheets) {
        if (chargeSheets == null) {
            return null;
        }

        return chargeSheets
                .stream()
                .filter(chargeSheet -> chargeSheet.getType() == ChargeSheet.Type.OUTPATIENT)
                .findFirst().orElse(null);
    }

    public static ChargeSheet pickMedicalPlanChargeSheet(List<ChargeSheet> chargeSheets) {
        if (chargeSheets == null) {
            return null;
        }

        return chargeSheets
                .stream()
                .filter(chargeSheet -> chargeSheet.getType() == ChargeSheet.Type.MEDICAL_PLAN)
                .findFirst().orElse(null);
    }

    public static ChargeSheet pickChargeSheetByClonePrescriptionType(List<ChargeSheet> chargeSheets, int clonePrescriptionType) {
        if (chargeSheets == null) {
            return null;
        }

        return chargeSheets.stream().filter(chargeSheet -> chargeSheet.getClonePrescriptionType() == clonePrescriptionType).findFirst().orElse(null);
    }

    public static ChargeSheet pickBindRegistrationChargeSheet(List<ChargeSheet> chargeSheets) {
        if (chargeSheets == null) {
            return null;
        }
        return chargeSheets.stream()
                .filter(chargeSheet -> !TextUtils.isEmpty(chargeSheet.getRegistrationChargeSheetId()))
                .findFirst()
                .orElse(null);
    }

    public static Integer mergeChargeSheetStatus(Integer a, Integer b) {
        if (a == null) {
            return b;
        }

        if (b == null) {
            return a;
        }
        return a < b ? a : b;
    }

    public static List<PromotionView> mergePromotionViewList(List<PromotionView> a, List<PromotionView> b) {
        if (a == null && b == null) {
            return new ArrayList<>();
        }

        if (a == null) {
            return b;
        }

        if (b == null) {
            return a;
        }

        a.addAll(b);
        return new ArrayList<>(a.stream().filter(promotionView -> !TextUtils.isEmpty(promotionView.getId())).collect(Collectors.toMap(PromotionView::getId, Function.identity(), (sa, sb) -> {
            sa.setDiscountPrice(MathUtils.wrapBigDecimalAdd(sa.getDiscountPrice(), sb.getDiscountPrice()));
            if (sa.getProductItems() == null) {
                sa.setProductItems(new ArrayList<>());
            }
            if (sb.getProductItems() != null) {
                sa.getProductItems().addAll(sb.getProductItems());
            }
            return sa;
        })).values());
    }


    public static String sign(ChargeSheet chargeSheet) {
        Map<String, String> keyData = getChargeSheetItems(chargeSheet)
                .stream()
                .filter(chargeFormItem -> chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.REFUNDED)
                .collect(Collectors.toMap(ChargeFormItem::getId,
                        chargeFormItem -> String.format("%s-%d-%d-%d-%d",
                                TextUtils.alwaysString(chargeFormItem.getProductId()),
                                MathUtils.wrapBigDecimalOrZero(chargeFormItem.getUnitCount()).intValue(),
                                MathUtils.wrapBigDecimalOrZero(chargeFormItem.getDoseCount()).intValue(),
                                chargeFormItem.getPharmacyNo(),
                                chargeFormItem.getUseDismounting()),
                        (a, b) -> a));
        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "keyData: {}", JsonUtils.dump(keyData));
        return signMap(keyData);
    }

    public static String signMap(Map<String, String> keyData) {
        if (keyData == null || keyData.size() == 0) {
            return "";
        }

        String data = keyData.keySet().stream().sorted().map(key -> String.format("%s:%s", key, keyData.get(key))).collect(Collectors.joining(","));
        String signStr = MD5Utils.sign(data);
        return signStr;
    }

    public static String generateMedicalItems(List<ChargeForm> chargeForms, boolean isContainUnSelectedItem) {

        if (CollectionUtils.isEmpty(chargeForms)) {
            return "";
        }

        List<ChargeFormItem> chargeFormItems = new ArrayList<>();
        if (isContainUnSelectedItem) {
            chargeFormItems.addAll(chargeForms.stream()
                    .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                    .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                    .filter(item -> item.getIsDeleted() == 0)
                    .filter(chargeFormItem -> chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.UNCHARGED || chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.UNSELECTED)
                    .filter(chargeFormItem -> chargeFormItem.getComposeType() != ComposeType.COMPOSE_SUB_ITEM && chargeFormItem.getGoodsFeeType() != GoodsFeeType.FEE_CHILD)
                    .collect(Collectors.toList())
            );

        } else {
            chargeFormItems.addAll(chargeForms.stream()
                    .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                    .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                    .filter(item -> item.getIsDeleted() == 0)
                    .filter(chargeFormItem -> chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.UNCHARGED)
                    .filter(chargeFormItem -> chargeFormItem.getComposeType() != ComposeType.COMPOSE_SUB_ITEM && chargeFormItem.getGoodsFeeType() != GoodsFeeType.FEE_CHILD)
                    .collect(Collectors.toList())
            );

        }
        return generateMedicalItemsStr(chargeFormItems);
    }

    public static String generateMedicalItemsStr(List<ChargeFormItem> chargeFormItems) {
        String medicalItems = "";
        if (org.apache.commons.collections.CollectionUtils.isEmpty(chargeFormItems)) {
            return "";
        }

        int registrationNum = 0;
        int chineseMedicineNum = 0;
        int westernMedicineNum = 0;
        int examineNum = 0;
        int therapyNum = 0;
        int materialNum = 0;
        int expressDeliveryNum = 0;
        int decoctionNum = 0;
        int composeProductNum = 0;
        int familyDoctorSignNum = 0;
        int otherFeeNum = 0;
        int nurseNum = 0;
        int glassNum = 0;
        int surgeryNum = 0;
        for (ChargeFormItem chargeFormItem : chargeFormItems) {
            int productType = chargeFormItem.getProductType();
            int productSubType = chargeFormItem.getProductSubType();
            switch (productType) {
                case Constants.ProductType.REGISTRATION:
                    registrationNum += 1;
                    break;
                case Constants.ProductType.EXPRESS_DELIVERY:
                    expressDeliveryNum += 1;
                    break;
                case Constants.ProductType.PROCESS:
                    decoctionNum += 1;
                    break;
                case Constants.ProductType.MEDICINE:
                    if (productSubType == Constants.ProductType.SubType.MEDICINE_CHINESE) {
                        chineseMedicineNum += 1;
                    } else {
                        westernMedicineNum += 1;
                    }
                    break;
                case Constants.ProductType.MATERIAL:
                case Constants.ProductType.SALE_PRODUCT:
                    materialNum += 1;
                    break;
                case Constants.ProductType.EYE:
                    glassNum += 1;
                    break;
                case Constants.ProductType.TREATMENT:
                    therapyNum += 1;
                    break;
                case Constants.ProductType.NURSE:
                    nurseNum += 1;
                    break;
                case Constants.ProductType.COMPOSE_PRODUCT:
                    composeProductNum += 1;
                    break;
                case Constants.ProductType.EXAMINATION:
                    examineNum += 1;
                    break;
                case Constants.ProductType.FAMILY_DOCTOR_SIGN:
                    familyDoctorSignNum += 1;
                    break;
                case Constants.ProductType.SURGERY:
                    surgeryNum += 1;
                    break;
                case Constants.ProductType.OTHER_FEE:
                    otherFeeNum += 1;
            }
        }

        List<String> medicalItemList = new ArrayList<>();
        if (registrationNum > 0) {
            medicalItemList.add(String.format("挂号%s次", registrationNum));
        }
        if (composeProductNum > 0) {
            medicalItemList.add(String.format("套餐%s项", composeProductNum));
        }
        if (expressDeliveryNum > 0) {
            medicalItemList.add(String.format("%s%s项", Constants.SystemProductId.EXPRESS_DELIVERY_NAME, expressDeliveryNum));
        }
        if (decoctionNum > 0) {
            medicalItemList.add(String.format("%s%s项", Constants.SystemProductId.PROCESS_NAME, decoctionNum));
        }
        if (chineseMedicineNum > 0) {
            medicalItemList.add(String.format("中药%s味", chineseMedicineNum));
        }
        if (westernMedicineNum > 0) {
            medicalItemList.add(String.format("西成药%s种", westernMedicineNum));
        }
        if (examineNum > 0) {
            medicalItemList.add(String.format("检查检验%s项", examineNum));
        }
        if (nurseNum > 0) {
            medicalItemList.add(String.format("护理%s项", nurseNum));
        }
        if (therapyNum > 0) {
            medicalItemList.add(String.format("治疗理疗%s项", therapyNum));
        }
        if (materialNum > 0) {
            medicalItemList.add(String.format("材料商品%s种", materialNum));
        }

        if (glassNum > 0) {
            medicalItemList.add(String.format("眼镜%s种", glassNum));
        }
        if (surgeryNum > 0) {
            medicalItemList.add(String.format("手术%s项", surgeryNum));
        }
        if (otherFeeNum > 0) {
            medicalItemList.add(String.format("其他费用%s项", otherFeeNum));
        }
        if (familyDoctorSignNum > 0) {
            medicalItemList.add(String.format("家庭医生签约%s次", familyDoctorSignNum));
        }
        if (!CollectionUtils.isEmpty(medicalItemList)) {
            medicalItems = String.join("、", medicalItemList);

            if (medicalItems.length() > 15) {
                medicalItems = medicalItems.substring(0, 15) + "...";
            }
            return medicalItems;
        }
        return "";
    }


    /**
     * @param chargeSheet
     * @param isContainUnSelectedItem
     * @param hisType
     */
    public static void updateChargeSheetAbstractInfo(ChargeSheet chargeSheet, boolean isContainUnSelectedItem, int hisType) {
        if (chargeSheet == null || chargeSheet.getAdditional() == null) {
            return;
        }
        ChargeSheetAdditional additional = chargeSheet.getAdditional();
        String abstractInfo = ChargeUtils.generateMedicalItems(chargeSheet.getChargeForms(), isContainUnSelectedItem);
        additional.setAbstractInfo(abstractInfo);

        if (hisType == Organ.HisType.CIS_HIS_TYPE_PHARMACY) {
            ChargeSheetAdditionalExtendInfo extendInfo = additional.getExtendedInfo();
            if (extendInfo == null) {
                extendInfo = new ChargeSheetAdditionalExtendInfo();
                additional.setExtendedInfo(extendInfo);
            }

            String goodsAbstract = ChargeUtils.generateChargeFormItemsAbstract(chargeSheet.getChargeForms());
            extendInfo.setGoodsAbstract(goodsAbstract);
        }
    }

    public static String generateChargeFormItemsAbstract(List<ChargeForm> chargeForms) {
        if (CollectionUtils.isEmpty(chargeForms)) {
            return null;
        }

        List<ChargeFormItem> chargeFormItems = new ArrayList<>();
        chargeFormItems.addAll(chargeForms.stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                .filter(item -> item.getIsDeleted() == 0)
                .filter(chargeFormItem -> chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.UNSELECTED)
                .filter(chargeFormItem -> chargeFormItem.getComposeType() != ComposeType.COMPOSE_SUB_ITEM)
                .collect(Collectors.toList())
        );

        if (CollectionUtils.isEmpty(chargeFormItems)) {
            return "";
        }

        Set<String> productIds = new HashSet<>();
        StringBuilder sb = new StringBuilder();
        for (ChargeFormItem chargeFormItem : chargeFormItems) {
            if (TextUtils.isEmpty(chargeFormItem.getName())) {
                continue;
            }
            if (productIds.contains(chargeFormItem.getProductId())) {
                continue;
            }
            productIds.add(chargeFormItem.getProductId());
            if (sb.length() > 0) {
                sb.append("、");
            }
            sb.append(chargeFormItem.getName());
            if (sb.length() > 128) {
                sb.append("、...");
                break;
            }
        }
        return sb.toString();
    }

    public static String generateMedicalItems(List<ChargeForm> chargeForms) {
        return generateMedicalItems(chargeForms, false);
    }

    public static BigDecimal calculateTransactionsNetIncomeFee(ChargeSheet chargeSheet) {
        if (chargeSheet == null || chargeSheet.getChargeTransactions() == null) {
            return BigDecimal.ZERO;
        }

        return chargeSheet.getChargeTransactions()
                .stream()
                .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                .map(ChargeTransaction::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }


    public static int generateIsDecoction(ChargeSheet chargeSheet) {

        if (chargeSheet == null || CollectionUtils.isEmpty(chargeSheet.getChargeForms())) {
            return 0;
        }

        boolean decoctionFlag = chargeSheet.getChargeForms().stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.PROCESS)
                .filter(chargeForm -> chargeForm.getChargeFormItems() != null)
                .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                .filter(item -> item.getIsDeleted() == 0)
                .anyMatch(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.PROCESS && chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.UNCHARGED);

        return decoctionFlag ? 1 : 0;
    }

    public static int generateDeliveryType(ChargeSheet chargeSheet) {

        if (chargeSheet == null || CollectionUtils.isEmpty(chargeSheet.getChargeForms())) {
            return 0;
        }

        boolean deliveryTypeFlag = chargeSheet.getChargeForms()
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.EXPRESS_DELIVERY)
                .filter(chargeForm -> chargeForm.getChargeFormItems() != null)
                .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                .filter(item -> item.getIsDeleted() == 0)
                .anyMatch(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.EXPRESS_DELIVERY && chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.UNCHARGED);
        return deliveryTypeFlag ? 1 : 0;
    }

    public static boolean isContainDeliveryItem(List<ChargeFormReq> chargeForms) {
        return Optional.ofNullable(chargeForms)
                .orElse(new ArrayList<>())
                .stream()
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.EXPRESS_DELIVERY)
                .filter(chargeForm -> chargeForm.getChargeFormItems() != null)
                .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                .anyMatch(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.EXPRESS_DELIVERY);
    }

    public static String generateMedicalInfo(ChargeSheet chargeSheet) {
        List<ChargeFormItem> chargeFormItems = getChargeSheetItems(chargeSheet);
        if (chargeSheet == null || CollectionUtils.isEmpty(chargeFormItems)) {
            return null;
        }

        long medicineWesternCount = chargeFormItems.stream()
                .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.MEDICINE
                        && (chargeFormItem.getProductSubType() == Constants.ProductType.SubType.MEDICINE_WESTERN || chargeFormItem.getProductSubType() == Constants.ProductType.SubType.MEDICINE_CHINESE_COMPOSE))
                .count();

        String medicineWestern = medicineWesternCount > 0 ? String.format("西药%s种", medicineWesternCount) : "";

        long medicineChineseCount = chargeFormItems.stream()
                .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.MEDICINE
                        && chargeFormItem.getProductSubType() == Constants.ProductType.SubType.MEDICINE_CHINESE)
                .count();

        String medicineChinese = medicineChineseCount > 0 ? String.format("中药%s味", medicineChineseCount) : "";

        List<String> medicalInfos = Stream.of(
                        medicineWestern,
                        medicineChinese
                ).filter(info -> !TextUtils.isEmpty(info))
                .collect(Collectors.toList());

        return String.join("，", medicalInfos);
    }

    /**
     * 根据中药处方构造了ChargeSheetProcessInfo，这个方法不是完整的ChargeSheetProcessInfo，还缺失processFormId属性，该属性要在构造加工费的form才能拿到
     *
     * @param chargeSheet
     * @param operatorId
     * @return
     */
    public static List<ChargeSheetProcessInfo> generateChargeSheetProcessInfos(ChargeSheet chargeSheet, String operatorId) {

        if (chargeSheet == null || chargeSheet.getChargeForms() == null) {
            return null;
        }

        List<ChargeSheetProcessInfo> processInfos = new ArrayList<>();

        chargeSheet.getChargeForms().stream()
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE
                        && chargeForm.getPharmacyType() == GoodsConst.PharmacyType.LOCAL_PHARMACY
                        && chargeForm.getIsDeleted() == 0
                )
                .forEach(chargeForm -> {
                    UsageInfo usageInfo = JsonUtils.readValue(chargeForm.getUsageInfoJson(), UsageInfo.class);
                    if (usageInfo != null && usageInfo.getIsDecoction()) {
                        ChargeSheetProcessInfo processInfo = new ChargeSheetProcessInfo();
                        processInfo.setId(AbcIdUtils.getUID());
                        processInfo.setChainId(chargeSheet.getChainId());
                        processInfo.setClinicId(chargeSheet.getClinicId());
                        processInfo.setChargeSheetId(chargeSheet.getId());
                        processInfo.setChargeFormId(chargeForm.getId());
                        processInfo.setType(usageInfo.getUsageType() != null ? usageInfo.getUsageType() : 0);
                        processInfo.setSubType(usageInfo.getUsageSubType() != null ? usageInfo.getUsageSubType() : 0);
                        processInfo.setBagUnitCount(usageInfo.getProcessBagUnitCount());
                        processInfo.setBagUnitCountDecimal(usageInfo.getProcessBagUnitCountDecimal());
                        processInfo.setProcessRemark(usageInfo.getProcessRemark());
                        processInfo.setTotalProcessCount(usageInfo.getTotalProcessCount());
                        processInfo.setChecked(true);
                        FillUtils.fillCreatedBy(processInfo, operatorId);
                        processInfos.add(processInfo);
                    }
                });

        return processInfos;
    }

    public static boolean isCanBeCloneForWeClinic(ChargeSheet chargeSheet) {
        if (chargeSheet == null || chargeSheet.getChargeForms() == null) {
            return false;
        }
        List<ChargeFormItem> chargeFormItems = getChargeSheetItems(chargeSheet);
        return chargeFormItems.stream()
                .filter(item -> item.getComposeType() != ComposeType.COMPOSE_SUB_ITEM)
                .filter(item -> item.getV2Status() == Constants.ChargeFormItemStatus.CHARGED)
                .anyMatch(item -> item.getProductType() == Constants.ProductType.MEDICINE);
    }

    public static List<FindChargeRuleProcessesInfo> generateFindChargeRuleProcessesInfos(List<ChargeSheetProcessInfo> processInfos, boolean isForPaid) {
        if (processInfos == null) {
            return null;
        }

        if (isForPaid) {
            return processInfos.stream()
                    .filter(processInfo -> processInfo.getChecked() && processInfo.getType() != 0)
                    .collect(Collectors.groupingBy(processInfo -> String.format("%d-%d", processInfo.getType(), processInfo.getSubType()))).values().stream().map(processesInfos -> {
                        ChargeSheetProcessInfo processInfo = processesInfos.get(0);
                        FindChargeRuleProcessesInfo findChargeRuleProcessesInfo = new FindChargeRuleProcessesInfo();
                        findChargeRuleProcessesInfo.setType(processInfo.getType());
                        findChargeRuleProcessesInfo.setSubType(processInfo.getSubType());
                        findChargeRuleProcessesInfo.setChargeSheetProcessInfoIds(processesInfos.stream().map(ChargeSheetProcessInfo::getId).collect(Collectors.toList()));
                        return findChargeRuleProcessesInfo;
                    }).collect(Collectors.toList());
        } else {
            return processInfos.stream()
                    .filter(processInfo -> processInfo.getType() != 0)
                    .collect(Collectors.groupingBy(processInfo -> String.format("%d-%d", processInfo.getType(), processInfo.getSubType()))).values().stream().map(processesInfos -> {
                        ChargeSheetProcessInfo processInfo = processesInfos.get(0);
                        FindChargeRuleProcessesInfo findChargeRuleProcessesInfo = new FindChargeRuleProcessesInfo();
                        findChargeRuleProcessesInfo.setType(processInfo.getType());
                        findChargeRuleProcessesInfo.setSubType(processInfo.getSubType());
                        findChargeRuleProcessesInfo.setChargeSheetProcessInfoIds(processesInfos.stream().map(ChargeSheetProcessInfo::getId).collect(Collectors.toList()));
                        return findChargeRuleProcessesInfo;
                    }).collect(Collectors.toList());
        }
    }

    public static void deleteChargeSheet(ChargeSheet chargeSheet, String operatorId) {
        if (chargeSheet == null) {
            return;
        }

        if (StringUtils.isEmpty(operatorId)) {
            operatorId = Constants.ANONYMOUS_PATIENT_ID;
        }

        chargeSheet.deleteModel(operatorId);
    }

    public static List<ChargeAirPharmacyLogistics> getChargeAirPharmacyLogistics(ChargeSheet chargeSheet) {

        if (chargeSheet == null || chargeSheet.getChargeForms() == null) {
            return new ArrayList<>();
        }

        return chargeSheet.getChargeForms()
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm ->
                        (chargeForm.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY || chargeForm.getPharmacyType() == GoodsConst.PharmacyType.VIRTUAL_PHARMACY)
                                && chargeForm.getChargeAirPharmacyLogistics() != null
                                && chargeForm.getChargeAirPharmacyLogistics().logisticsValidateToInsertIntoRepository()
                )
                .map(ChargeForm::getChargeAirPharmacyLogistics)
                .filter(chargeAirPharmacyLogistics -> chargeAirPharmacyLogistics.getIsNeedInsert() == 1)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public static List<ChargeAirPharmacyMedicalRecord> getChargeAirPharmacyMedicalRecord(ChargeSheet chargeSheet) {

        if (chargeSheet == null || chargeSheet.getChargeForms() == null) {
            return new ArrayList<>();
        }

        return chargeSheet.getChargeForms()
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY)
                .filter(chargeForm -> chargeForm.getChargeAirPharmacyMedicalRecord() != null)
                .map(ChargeForm::getChargeAirPharmacyMedicalRecord)
                .filter(medicalRecord -> medicalRecord.getIsNeedInsert() == 1)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public static List<ChargeForm> filterAirPharmacyChargeForm(ChargeSheet chargeSheet) {

        if (chargeSheet == null || chargeSheet.getChargeForms() == null) {
            return null;
        }

        return chargeSheet.getChargeForms().stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY).collect(Collectors.toList());
    }


    public static CisPatientInfo parseCisPatientInfoFromPatientOrder(PatientOrder patientOrder) {
        if (patientOrder == null) {
            return null;
        }

        CisPatientInfo cisPatientInfo = new CisPatientInfo();
        cisPatientInfo.setName(patientOrder.getPatientName());
        cisPatientInfo.setSex(patientOrder.getPatientSex());
        cisPatientInfo.setId(patientOrder.getPatientId());
        cisPatientInfo.setMobile(patientOrder.getPatientMobile());
        cisPatientInfo.setIsMember(patientOrder.getIsMember());
        cisPatientInfo.setAge(patientOrder.getPatientAge());
        cisPatientInfo.setBirthday(patientOrder.getPatientBirthday());
        PatientInfo patientInfo = patientOrder.getPatientInfo();
        if (patientInfo != null) {
            cisPatientInfo.setIdCardType(patientInfo.getIdCardType());
            cisPatientInfo.setIdCard(patientInfo.getIdCard());
        }
        return cisPatientInfo;
    }

    public static CisPatientInfo convertCisPatientInfoFromPatientInfo(cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientInfo patientInfo) {
        if (patientInfo == null) {
            return null;
        }

        CisPatientInfo cisPatientInfo = new CisPatientInfo();
        BeanUtils.copyProperties(patientInfo, cisPatientInfo, "isMember", "age");
        cisPatientInfo.setIsMember(patientInfo.getIsMember());
        cisPatientInfo.setAge(Optional.ofNullable(patientInfo.getAge()).map(a -> {
            CisPatientAge cisPatientAge = new CisPatientAge();
            cisPatientAge.setDay(a.getDay());
            cisPatientAge.setMonth(a.getMonth());
            cisPatientAge.setYear(a.getYear());
            return cisPatientAge;
        }).orElse(null));

        return cisPatientInfo;
    }

    public static CisPatientInfo convertCisPatientInfoFromCrmPatientInfo(cn.abcyun.cis.commons.rpc.crm.PatientInfo patientInfo) {
        if (patientInfo == null) {
            return null;
        }

        CisPatientInfo cisPatientInfo = new CisPatientInfo();
        BeanUtils.copyProperties(patientInfo, cisPatientInfo, "isMember", "age");
        cisPatientInfo.setIsMember(patientInfo.getIsMember());
        cisPatientInfo.setAge(Optional.ofNullable(patientInfo.getAge()).map(a -> {
            CisPatientAge cisPatientAge = new CisPatientAge();
            cisPatientAge.setDay(a.getDay());
            cisPatientAge.setMonth(a.getMonth());
            cisPatientAge.setYear(a.getYear());
            return cisPatientAge;
        }).orElse(null));
        cisPatientInfo.setAddress(Optional.ofNullable(patientInfo.getAddress()).map(a -> {
            CisPatientAddress address = new CisPatientAddress();
            BeanUtils.copyProperties(a, address);
            return address;
        }).orElse(null));

        return cisPatientInfo;
    }

    public static CisPatientInfo convertCommonCisPatientInfoFromSdkCisPatientInfo(cn.abcyun.bis.rpc.sdk.cis.model.patient.CisPatientInfo patientInfo) {
        if (patientInfo == null) {
            return null;
        }

        CisPatientInfo cisPatientInfo = new CisPatientInfo();
        BeanUtils.copyProperties(patientInfo, cisPatientInfo);
        cisPatientInfo.setAge(Optional.ofNullable(patientInfo.getAge()).map(a -> {
            CisPatientAge cisPatientAge = new CisPatientAge();
            cisPatientAge.setDay(a.getDay());
            cisPatientAge.setMonth(a.getMonth());
            cisPatientAge.setYear(a.getYear());
            return cisPatientAge;
        }).orElse(null));
        cisPatientInfo.setAddress(Optional.ofNullable(patientInfo.getAddress()).map(a -> {
            CisPatientAddress address = new CisPatientAddress();
            BeanUtils.copyProperties(a, address);
            return address;
        }).orElse(null));

        return cisPatientInfo;
    }

    public static PatientInfo parsePatientInfoFromPatientOrder(PatientOrder patientOrder) {
        if (patientOrder == null) {
            return null;
        }

        PatientInfo patientInfo = new PatientInfo();
        patientInfo.setName(patientOrder.getPatientName());
        patientInfo.setSex(patientOrder.getPatientSex());
        patientInfo.setId(patientOrder.getPatientId());
        patientInfo.setMobile(patientOrder.getPatientMobile());
        patientInfo.setIsMember(patientOrder.getIsMember());
        patientInfo.setAge(patientOrder.getPatientAge());
        patientInfo.setBirthday(patientOrder.getPatientBirthday());
        return patientInfo;
    }

    public static cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientInfo concertToSdkPatientInfoFromCommonCisPatientInfo(CisPatientInfo cisPatientInfo) {
        if (cisPatientInfo == null) {
            return null;
        }

        cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientInfo sdkPatientInfo = copyPatientInfo(cisPatientInfo);
        sdkPatientInfo.setAge(Optional.ofNullable(cisPatientInfo.getAge()).map(a -> {
            cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientInfo.PatientAge cisPatientAge = new cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientInfo.PatientAge();
            cisPatientAge.setDay(a.getDay());
            cisPatientAge.setMonth(a.getMonth());
            cisPatientAge.setYear(a.getYear());
            return cisPatientAge;
        }).orElse(null));
        sdkPatientInfo.setAddress(Optional.ofNullable(cisPatientInfo.getAddress()).map(a -> {
            cn.abcyun.bis.rpc.sdk.cis.model.patient.CisPatientAddress address = new cn.abcyun.bis.rpc.sdk.cis.model.patient.CisPatientAddress();
            BeanUtils.copyProperties(a, address);
            return address;
        }).orElse(null));

        return sdkPatientInfo;
    }

    private static cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientInfo copyPatientInfo(CisPatientInfo sourcePatientInfo) {
        if (sourcePatientInfo == null) {
            return null;
        }

        if (sourcePatientInfo.getIsMember() == null) {
            sourcePatientInfo.setIsMember(0);
        }

        if (sourcePatientInfo.getWxBindStatus() == null) {
            sourcePatientInfo.setWxBindStatus(0);
        }

        cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientInfo patientInfoCopy = new cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientInfo();
        BeanUtils.copyProperties(sourcePatientInfo, patientInfoCopy);
        return patientInfoCopy;
    }

    public static MemberInfo parseMemberInfoFromPatientInfo(PatientInfo patientInfo) {

        if (patientInfo.getMemberInfo() == null || patientInfo == null) {
            return null;
        }

        PatientMemberInfo patientMemberInfo = patientInfo.getMemberInfo();

        return parseMemberInfoFromPatientMemberInfo(patientMemberInfo, patientInfo.getIsMember());
    }

    public static MemberInfo parseMemberInfoFromPatientMemberInfo(PatientMemberInfo patientMemberInfo, int isMember) {

        if (patientMemberInfo == null) {
            return null;
        }

        MemberInfo memberInfo = new MemberInfo();

        BeanUtils.copyProperties(patientMemberInfo, memberInfo);
        MemberInfo.Patient patient = new MemberInfo.Patient();
        patient.setId(patientMemberInfo.getPatientId());
        patient.setName(patientMemberInfo.getPatientName());
        patient.setIsMember(isMember);
        patient.setMobile(patientMemberInfo.getPatientMobile());
        memberInfo.setPatient(patient);

        if (patientMemberInfo.getMemberTypeInfo() != null) {
            MemberInfo.MemberType memberType = new MemberInfo.MemberType();
            memberType.setId(patientMemberInfo.getMemberTypeInfo().getMemberTypeId());
            memberType.setName(patientMemberInfo.getMemberTypeInfo().getMemberTypeName());
            memberInfo.setMemberType(memberType);
        }

        return memberInfo;
    }

    /**
     * 构建执行站收费单摘要信息
     *
     * @param chargeSheetType       收费单类型
     * @param chargeExecuteItems    收费单执行项
     * @param chargeFormItems       收费单子项集合
     * @param chargeSheetAdditional 收费单扩展属性
     * @return 执行站摘要
     */
    public static String generateNurseAbstractInfo(int chargeSheetType,
                                                   List<ChargeExecuteItem> chargeExecuteItems,
                                                   List<ChargeFormItem> chargeFormItems,
                                                   ChargeSheetAdditional chargeSheetAdditional) {
        if (CollectionUtils.isEmpty(chargeFormItems)) {
            return "";
        }

        StringJoiner stringJoiner = new StringJoiner(",");
        // 1、首先拼接治疗理疗项
        chargeExecuteItems.stream()
                .filter(chargeExecuteItem -> Objects.equals(chargeExecuteItem.getProductType(), Constants.ProductType.TREATMENT) || Objects.equals(chargeExecuteItem.getProductType(), Constants.ProductType.NURSE))
                .forEach(chargeExecuteItem -> stringJoiner.add(chargeExecuteItem.getName()));
        // 2、chargeFormItemId<->ChargeExecuteItem map
        Map<String, ChargeExecuteItem> chargeFormItemIdExecuteItemMap = ListUtils.toMap(chargeExecuteItems, ChargeExecuteItem::getChargeFormItemId);

        // 3、过滤出输注处方、外用项目
        Map<String, Integer> usageRemainExecuteCountMap = new HashMap<>(4);
        // 同一个处方同一个分组只计算一次总次数
        Map<String, Set<Integer>> chargeFormIdGroupIds = new HashMap<>(8);
        chargeFormItems
                .stream()
                .filter(chargeFormItem -> Objects.equals(chargeFormItem.getStatus(), Constants.ChargeFormItemStatus.UNCHARGED)
                        || Objects.equals(chargeFormItem.getStatus(), Constants.ChargeFormItemStatus.CHARGED)
                )
                .filter(chargeFormItem -> MathUtils.wrapBigDecimalCompare(chargeFormItem.getUnitCount(), chargeFormItem.getRefundUnitCount()) > 0)
                .forEach(chargeFormItem -> {
                    UsageInfo usageInfo = JsonUtils.readValue(chargeFormItem.getUsageInfoJson(), UsageInfo.class);
                    if (Objects.isNull(usageInfo)
                            || Objects.isNull(usageInfo.getExecutedTotalCount())
                            || (!ExecutableUtils.isExecutableUsageOnly(usageInfo.getUsage()) && !Constants.ChargeSheetIncludeItemType.EXTERNAL_USE_NAMES.contains(usageInfo.getUsage()))) {
                        return;
                    }
                    // 判断chargeFormItem 对应的处方分组是否已经计算过
                    if (Objects.nonNull(chargeFormItem.getGroupId())) {
                        if (chargeFormIdGroupIds.getOrDefault(chargeFormItem.getChargeFormId(), new HashSet<>(1)).contains(chargeFormItem.getGroupId())) {
                            return;
                        }

                        chargeFormIdGroupIds.computeIfAbsent(chargeFormItem.getChargeFormId(), chargeFormId -> new HashSet<>()).add(chargeFormItem.getGroupId());
                    }
                    String usage = usageInfo.getUsage();
                    usageRemainExecuteCountMap.put(
                            usage,
                            usageRemainExecuteCountMap.getOrDefault(usage, 0)
                                    +
                                    Optional.ofNullable(chargeFormItemIdExecuteItemMap.get(chargeFormItem.getId()))
                                            .map(chargeExecuteItem -> MathUtils.max(MathUtils.wrapBigDecimalSubtract(chargeExecuteItem.getUnitCount(), chargeExecuteItem.getExecutedCount()), BigDecimal.ZERO).intValue())
                                            .orElse(usageInfo.getExecutedTotalCount())
                    );
                });
        // 外用放在后面
        usageRemainExecuteCountMap
                .entrySet()
                .stream()
                .sorted(Comparator.comparing(entry -> Constants.ChargeSheetIncludeItemType.EXTERNAL_USE_NAMES.contains(entry.getKey()) ? 1 : 0))
                .forEach(
                        entry -> stringJoiner.add(entry.getKey() + (Objects.equals(entry.getValue(), 0) ? "" : entry.getValue() + "次"))
                );
        // 3、过滤出皮试
        long astCount = chargeFormItems
                .stream()
                .filter(chargeFormItem -> Objects.equals(chargeFormItem.getStatus(), Constants.ChargeFormItemStatus.UNCHARGED)
                        || Objects.equals(chargeFormItem.getStatus(), Constants.ChargeFormItemStatus.CHARGED)
                )
                .map(chargeFormItem -> JsonUtils.readValue(chargeFormItem.getUsageInfoJson(), UsageInfo.class))
                .filter(usageInfo -> Objects.nonNull(usageInfo) && Objects.equals(usageInfo.getAst(), 1))
                .count();
        if (astCount > 0) {
            stringJoiner.add("皮试" + astCount + "次");
        }
        String abstractInfo = stringJoiner.toString();
        if (!StringUtils.isEmpty(abstractInfo)) {
            return abstractInfo;
        }

        // 4、诊断、零售开单
        String diagnosis = Objects.nonNull(chargeSheetAdditional) ? chargeSheetAdditional.getDiagnosis() : null;
        return !StringUtils.isEmpty(diagnosis)
                ? diagnosis
                : (Objects.equals(chargeSheetType, ChargeSheet.Type.DIRECT_SALE)
                ? "零售开单"
                : ""
        );
    }

    /**
     * true表示一定是寄付，false 不能说明什么 deliverType端上在某个版本已经没有传，是后台通过generateType再生成的，算费这里没生成过，所以为了保持原有逻辑，关掉这个代码
     */
    public static boolean deliveryPaidByShipper(ChargeSheet chargeSheet) {
        if (/*chargeSheet.getDeliveryType() == ChargeSheet.DeliveryType.DELIVERY_TO_HOME && */chargeSheet.getDeliveryInfo() != null) {
            return chargeSheet.getDeliveryInfo().deliveryPaidByShipper();
        }
        return false;
    }

    /**
     * true表示一定是到付，false 不能说明什么
     */
    public static boolean deliveryPaidByConsignee(ChargeSheet chargeSheet) {
        if (chargeSheet.getDeliveryType() == ChargeSheet.DeliveryType.DELIVERY_TO_HOME && chargeSheet.getDeliveryInfo() != null) {
            return chargeSheet.getDeliveryInfo().deliveryPaidByConsignee();
        }
        return false;
    }

    /**
     * 工具函数，把收费单的状态设置到已完成付费的状态 收费单完成服务费后，需要同时更新selfPayStatus以及checkStatus
     *
     * @param chargeSheetStatus 收费单的状态，目前有收费和部分收费两种状态 extra 是一个json对象，自助支付的selfPayStatus的状态放在里面
     * @return void 但是参数chargeSheet的checkStatus以及selfPayStatus,status会发生该表
     */
    public static void setChargeSheetToPayStatus(ChargeSheet chargeSheet, String extra, int chargeSheetStatus) {
        /**
         *selfPayStatus的解析
         *      目前只有微诊所的自助支付会 设置这个接口字段：selfPayStatus
         *      在用户支付完成后，记录这一单是用户自助支付完成的
         *      微诊所是按 selfPayStatus > checkStatus > status的优先级控制订单的状态
         * */
        int changeToSelfPayStatus = Constants.ChargeSheetSelfPayStatus.DISABLE_SELF_PAY;
        if (chargeSheet.getAdditional() != null && chargeSheet.getAdditional().getSelfPayStatus() == Constants.ChargeSheetSelfPayStatus.SELF_PAY_WAITING_PAY) {
            changeToSelfPayStatus = Constants.ChargeSheetSelfPayStatus.SELF_PAY_CHARGE_PAID;
        }
        PayInfo.PayInfoExtra payInfoExtra = cn.abcyun.cis.charge.util.JsonUtils.readValue(extra, PayInfo.PayInfoExtra.class);
        if (payInfoExtra != null && payInfoExtra.getSelfPayStatus() != null) {
            changeToSelfPayStatus = payInfoExtra.getSelfPayStatus();
        }
        //更新status
        int oldChargeSheetStatus = chargeSheet.getStatus();
        chargeSheet.setStatus(chargeSheetStatus);

        //将收费单置为不需要确认状态 更新checkStatus
        chargeSheet.setCheckStatus(ChargeSheet.CheckStatus.NOT_NEED_CHECK);

        int oldSelfPayStatus = -1;
        if (chargeSheet.getAdditional() != null) {
            oldSelfPayStatus = chargeSheet.getAdditional().getSelfPayStatus();
            chargeSheet.getAdditional().setSelfPayStatus(changeToSelfPayStatus);
        }
        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "updateSelfPayToStableStatus oldSelfPayStatus:{}->{},chargeSheetStatus={}->{}", oldSelfPayStatus, changeToSelfPayStatus, oldChargeSheetStatus, chargeSheetStatus);
    }

    /**
     * chargeSheet 深拷贝 避免chargeSheet被写入db，有时需要拷贝出来
     */
    public static ChargeSheet deepCopyChargeSheet(ChargeSheet chargeSheet) {
        String str = JsonUtils.dump(chargeSheet);
        return JsonUtils.readValue(str, ChargeSheet.class);
    }

    /**
     * 微诊所的总金额展示：如果待收费，则直接等于totalFee+议价值， 如果已收，判断是议价加价还是议价减价，如果议价加价，则等于totalFee - 以价值，如果减价，则等于totalFee
     */
    public static BigDecimal fixPatientTotalFee(int chargeSheetStatus, BigDecimal totalFee, BigDecimal adjustmentFee) {
        //微诊所的总金额展示：如果待收费，则直接等于totalFee+议价值， 如果已收，判断是议价加价还是议价减价，如果议价加价，则等于totalFee - 以价值，如果减价，则等于totalFee
        if (chargeSheetStatus == Constants.ChargeSheetStatus.UNCHARGED) {
            return MathUtils.wrapBigDecimalAdd(totalFee, adjustmentFee);
        } else {
            if (adjustmentFee.compareTo(BigDecimal.ZERO) > 0) {
                return totalFee;
            } else {
                return MathUtils.wrapBigDecimalAdd(totalFee, adjustmentFee);
            }
        }
    }

    public static void checkAirPharmacyFormReqIsContainMedicineAndThrowException(List<ChargeFormReq> chargeForms) {

        if (CollectionUtils.isEmpty(chargeForms)) {
            return;
        }

        for (ChargeFormReq chargeForm : chargeForms) {

            if (chargeForm.getSourceFormType() != Constants.SourceFormType.AIR_PHARMACY) {
                continue;
            }

            if (!ChargeFormUtils.isContainMedicineForAirPharmacyChargeFormReq(chargeForm)) {
                throw new ChargeServiceException(ChargeServiceError.AIR_PHARMACY_NO_MEDICINE);
            }
        }
    }

    public static ChargeDeliveryInfo createChargeDeliveryInfo(ChargeDeliveryReq chargeDeliveryReq, String chargeSheetId, String chainId, String clinicId, String operatorId) {

        if (chargeDeliveryReq == null) {
            return null;
        }

        ChargeDeliveryInfo chargeDeliveryInfo = new ChargeDeliveryInfo();
        chargeDeliveryInfo.setId(AbcIdUtils.getUID());
        BeanUtils.copyProperties(chargeDeliveryReq, chargeDeliveryInfo, "deliveryCompany");
        chargeDeliveryInfo.setChainId(chainId);
        chargeDeliveryInfo.setClinicId(clinicId);
        chargeDeliveryInfo.setChargeSheetId(chargeSheetId);
        if (chargeDeliveryReq.getDeliveryCompany() != null) {
            chargeDeliveryInfo.setDeliveryCompanyId(chargeDeliveryReq.getDeliveryCompany().getId());
        }
        FillUtils.fillCreatedBy(chargeDeliveryInfo, operatorId);
        return chargeDeliveryInfo;
    }

    public static ChargeDeliveryInfo updateChargeDeliveryInfo(UpdateDeliveryInfoReq updateDeliveryInfoReq, ChargeDeliveryInfo chargeDeliveryInfo, String operatorId) {
        if (updateDeliveryInfoReq == null || chargeDeliveryInfo == null) {
            return chargeDeliveryInfo;
        }
        if (!StringUtils.isEmpty(updateDeliveryInfoReq.getChainId())) {
            chargeDeliveryInfo.setChainId(updateDeliveryInfoReq.getChainId());
        }
        if (!StringUtils.isEmpty(updateDeliveryInfoReq.getClinicId())) {
            chargeDeliveryInfo.setClinicId(updateDeliveryInfoReq.getClinicId());
        }
        if (!StringUtils.isEmpty(updateDeliveryInfoReq.getAddressProvinceId())) {
            chargeDeliveryInfo.setAddressProvinceId(updateDeliveryInfoReq.getAddressProvinceId());
        }
        if (!StringUtils.isEmpty(updateDeliveryInfoReq.getAddressProvinceName())) {
            chargeDeliveryInfo.setAddressProvinceName(updateDeliveryInfoReq.getAddressProvinceName());
        }
        if (!StringUtils.isEmpty(updateDeliveryInfoReq.getAddressCityId())) {
            chargeDeliveryInfo.setAddressCityId(updateDeliveryInfoReq.getAddressCityId());
        }
        if (!StringUtils.isEmpty(updateDeliveryInfoReq.getAddressCityName())) {
            chargeDeliveryInfo.setAddressCityName(updateDeliveryInfoReq.getAddressCityName());
        }
        if (!StringUtils.isEmpty(updateDeliveryInfoReq.getAddressDistrictId())) {
            chargeDeliveryInfo.setAddressDistrictId(updateDeliveryInfoReq.getAddressDistrictId());
        }
        if (!StringUtils.isEmpty(updateDeliveryInfoReq.getAddressDistrictName())) {
            chargeDeliveryInfo.setAddressDistrictName(updateDeliveryInfoReq.getAddressDistrictName());
        }
        if (!StringUtils.isEmpty(updateDeliveryInfoReq.getAddressDetail())) {
            chargeDeliveryInfo.setAddressDetail(updateDeliveryInfoReq.getAddressDetail());
        }
        if (!StringUtils.isEmpty(updateDeliveryInfoReq.getDeliveryName())) {
            chargeDeliveryInfo.setDeliveryName(updateDeliveryInfoReq.getDeliveryName());
        }
        if (!StringUtils.isEmpty(updateDeliveryInfoReq.getDeliveryMobile())) {
            chargeDeliveryInfo.setDeliveryMobile(updateDeliveryInfoReq.getDeliveryMobile());
        }
        if (updateDeliveryInfoReq.getDeliveryOrderNo() != null) {
            chargeDeliveryInfo.setDeliveryOrderNo(updateDeliveryInfoReq.getDeliveryOrderNo());
        }
        if (!StringUtils.isEmpty(updateDeliveryInfoReq.getDeliveryCompanyId())) {
            chargeDeliveryInfo.setDeliveryCompanyId(updateDeliveryInfoReq.getDeliveryCompanyId());
        }
        chargeDeliveryInfo.setDeliveryPayType(updateDeliveryInfoReq.getDeliveryPayType());
        FillUtils.fillCreatedBy(chargeDeliveryInfo, operatorId);
        return chargeDeliveryInfo;
    }

    public static ChargeAirPharmacyLogistics updateChargeFormDeliveryInfo(UpdateDeliveryInfoReq updateDeliveryInfoReq, ChargeAirPharmacyLogistics chargeDeliveryInfo, String operatorId) {
        if (updateDeliveryInfoReq == null || chargeDeliveryInfo == null) {
            return chargeDeliveryInfo;
        }
        if (!StringUtils.isEmpty(updateDeliveryInfoReq.getChainId())) {
            chargeDeliveryInfo.setChainId(updateDeliveryInfoReq.getChainId());
        }
        if (!StringUtils.isEmpty(updateDeliveryInfoReq.getClinicId())) {
            chargeDeliveryInfo.setClinicId(updateDeliveryInfoReq.getClinicId());
        }
        if (!StringUtils.isEmpty(updateDeliveryInfoReq.getAddressProvinceId())) {
            chargeDeliveryInfo.setAddressProvinceId(updateDeliveryInfoReq.getAddressProvinceId());
        }
        if (!StringUtils.isEmpty(updateDeliveryInfoReq.getAddressProvinceName())) {
            chargeDeliveryInfo.setAddressProvinceName(updateDeliveryInfoReq.getAddressProvinceName());
        }
        if (!StringUtils.isEmpty(updateDeliveryInfoReq.getAddressCityId())) {
            chargeDeliveryInfo.setAddressCityId(updateDeliveryInfoReq.getAddressCityId());
        }
        if (!StringUtils.isEmpty(updateDeliveryInfoReq.getAddressCityName())) {
            chargeDeliveryInfo.setAddressCityName(updateDeliveryInfoReq.getAddressCityName());
        }
        if (!StringUtils.isEmpty(updateDeliveryInfoReq.getAddressDistrictId())) {
            chargeDeliveryInfo.setAddressDistrictId(updateDeliveryInfoReq.getAddressDistrictId());
        }
        if (!StringUtils.isEmpty(updateDeliveryInfoReq.getAddressDistrictName())) {
            chargeDeliveryInfo.setAddressDistrictName(updateDeliveryInfoReq.getAddressDistrictName());
        }
        if (!StringUtils.isEmpty(updateDeliveryInfoReq.getAddressDetail())) {
            chargeDeliveryInfo.setAddressDetail(updateDeliveryInfoReq.getAddressDetail());
        }
        if (!StringUtils.isEmpty(updateDeliveryInfoReq.getDeliveryName())) {
            chargeDeliveryInfo.setDeliveryName(updateDeliveryInfoReq.getDeliveryName());
        }
        if (!StringUtils.isEmpty(updateDeliveryInfoReq.getDeliveryMobile())) {
            chargeDeliveryInfo.setDeliveryMobile(updateDeliveryInfoReq.getDeliveryMobile());
        }
        if (updateDeliveryInfoReq.getDeliveryOrderNo() != null) {
            chargeDeliveryInfo.setDeliveryNo(updateDeliveryInfoReq.getDeliveryOrderNo());
        }
        if (!StringUtils.isEmpty(updateDeliveryInfoReq.getDeliveryCompanyId())) {
            chargeDeliveryInfo.setDeliveryCompanyId(updateDeliveryInfoReq.getDeliveryCompanyId());
        }
        FillUtils.fillCreatedBy(chargeDeliveryInfo, operatorId);
        return chargeDeliveryInfo;
    }

    /**
     * 病人锁单是否能解锁，这个逻辑封到后台
     */
    public static int isPatientLockStatus(int lockStatus) {
        boolean isPatientLock =
                lockStatus == Constants.ChargeSheetLockStatus.LOCK_STATUS_PATIENT_WECLINIC
                        || lockStatus == Constants.ChargeSheetLockStatus.LOCK_STATUS_PATIENT_DEVICE
                        || lockStatus == Constants.ChargeSheetLockStatus.LOCK_STATUS_PATIENT_WEAPP;
        return isPatientLock ? 1 : 0;
    }

    /**
     * 收费台锁单锁单是否能解锁，这个逻辑封到后台
     */
    public static int isChargerLockStatus(int lockStatus) {
        boolean isChargerLock = (lockStatus & Constants.ChargeSheetLockStatusV2.LOCK_PAY_BY_CHARGE_STATION) > 0;
        return isChargerLock ? 1 : 0;
    }

    /**
     * 获取病人可用的会员卡信息 只能在传needMember为true的返回的patient才能调用这个函数
     */
    public static String getAvailableMemberCard(PatientInfo patientInfo, ChargeSheet chargeSheet) {
        if (patientInfo != null && Objects.equals(patientInfo.getIsMember(), 1) &&
                chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED) {
            if (patientInfo.getMemberInfo() != null && !StringUtils.isEmpty(patientInfo.getMemberInfo().getPatientId())) {
                return patientInfo.getMemberInfo().getPatientId();
            } else {
                //Log
                sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "会员服务返回了是会员，但是meminfo为空,返回会员信息={}", JsonUtils.dump(patientInfo));
            }

        }
        return "";
    }

    /**
     * 是否为异步支付
     *
     * @return
     */
    public static boolean isAsyncPay(CombinedPayItem payItem, ShebaoService shebaoService, String clinicId, String chainId, boolean needHystrix) {
        boolean ret = false;

        if (payItem == null) {
            return ret;
        }

        if (payItem.getPayMode() == Constants.ChargePayMode.MEMBER_CARD
                || payItem.getPayMode() == Constants.ChargePayMode.PROMOTION_CARD
                || payItem.getPayMode() == Constants.ChargePayMode.SHEBAO_AIR_PAY
                || payItem.getPayMode() == Constants.ChargePayMode.SHEBAO_MULAID_PAY
                || payItem.getPayMode() == Constants.ChargePayMode.OUTPATIENT_CENTER_PAY
                || payItem.getPayMode() == Constants.ChargePayMode.SHE_BAO_RAILWAY_PAY
                || payItem.getPayMode() == Constants.ChargePayMode.SHEBAO_YIMA_PAY
                || payItem.getPayMode() == Constants.ChargePayMode.SHEBAO_QINGDAO_UNION_POS_PAY
        ) {
            ret = true;
        } else if (payItem.getPayMode() == Constants.ChargePayMode.HEALTH_CARD) {
            if (Objects.equals(payItem.getPaySubMode(), Constants.ChargePaySubMode.WECHAT_MOBILE_SHEBAO)) {
                ret = true;
            } else {
                try {
                    ret = shebaoService.isShebaoPayEnableWithHystrix(chainId, clinicId, payItem.getPaySubMode());
                } catch (Exception e) {
                    if (!needHystrix) {
                        throw e;
                    }
                }
            }
        } else if (payItem.getPayMode() == Constants.ChargePayMode.WECHAT_PAY &&
                (payItem.getPaySubMode() == Constants.ChargePaySubMode.H5 ||
                        payItem.getPaySubMode() == Constants.ChargePaySubMode.NATIVE ||
                        payItem.getPaySubMode() == Constants.ChargePaySubMode.MINI_PROGRAM
                        || payItem.getPaySubMode() == Constants.ChargePaySubMode.WECHAT_MOBILE_SHEBAO_CASH)) {
            ret = true;
        } else if (payItem.getPayMode() == Constants.ChargePayMode.ABC_PAY) {
            ret = true;
        } else {
            PayModeInfo innerPayMode = InnerPayModes.getPayModeInfo(payItem.getPayMode(), payItem.getPaySubMode());
            if (innerPayMode != null && Objects.equals(innerPayMode.getType(), Constants.ChargePayModeConfigType.THIRD_PARTY_COMMON_PAY)) {
                ret = true;
            }
        }

        return ret;
    }


    public static int getLockStatusByPaySource(int paySource, boolean isPermanentLock) {
        int lockStatus = 0;

        if (isPermanentLock) {
            return Constants.ChargeSheetLockStatus.LOCK_STATUS_PATIENT_PERMANENT;
        }
        switch (paySource) {
            case Constants.ChargeSource.CHARGE:
                lockStatus = Constants.ChargeSheetLockStatus.LOCK_STATUS_CHARGE_STATION;
                break;
            case Constants.ChargeSource.WE_CLINIC:
                lockStatus = Constants.ChargeSheetLockStatus.LOCK_STATUS_PATIENT_WECLINIC;
                break;
            case Constants.ChargeSource.DEVICE:
                lockStatus = Constants.ChargeSheetLockStatus.LOCK_STATUS_PATIENT_DEVICE;
                break;
            case Constants.ChargeSource.WE_APP:
                lockStatus = Constants.ChargeSheetLockStatus.LOCK_STATUS_PATIENT_WEAPP;
                break;
        }
        return lockStatus;
    }

    /**
     * @param paySource
     * @param payType           {@link ChargePayTransaction.PayType}
     * @param isLockChargeSheet
     * @return
     */
    public static int getLockStatusV2ByPaySource(int paySource, int payType, boolean isLockChargeSheet) {
        int lockStatus = 0;

        if (payType == ChargePayTransaction.PayType.PAY) {
            if (isLockChargeSheet) {
                return Constants.ChargeSheetLockStatusV2.LOCK_CHARGE_SHEET;
            }
            switch (paySource) {
                case Constants.ChargeSource.CHARGE:
                    lockStatus = Constants.ChargeSheetLockStatusV2.LOCK_PAY_BY_CHARGE_STATION;
                    break;
                case Constants.ChargeSource.WE_CLINIC:
                case Constants.ChargeSource.WE_APP:
                    lockStatus = Constants.ChargeSheetLockStatusV2.LOCK_PAY_BY_PATIENT_FROM_WE_CLINIC;
                    break;
                case Constants.ChargeSource.DEVICE:
                    lockStatus = Constants.ChargeSheetLockStatusV2.LOCK_PAY_BY_PATIENT_FROM_DEVICE;
                    break;
            }
            return lockStatus;
        } else if (payType == ChargePayTransaction.PayType.REFUND) {
            return Constants.ChargeSheetLockStatusV2.LOCK_REFUND;
        }
        return lockStatus;
    }

    /**
     * 检查收费单是否能够解锁
     *
     * @param lockStatus
     * @param source
     */
    public static boolean chargeSheetIsCanUnlock(int lockStatus, int source, boolean isForPay) {

        if (lockStatus == Constants.ChargeSheetLockStatusV2.NONE) {
            return true;
        }

        if (isForPay) {
            //收费员也可以解锁患者锁的单据
            if (source == Constants.ChargeSource.CHARGE) {
                return true;
            }

            return (BitFlagUtils.checkFlagOn(lockStatus, Constants.ChargeSheetLockStatusV2.LOCK_PAY_BY_PATIENT_FROM_WE_CLINIC) || BitFlagUtils.checkFlagOn(lockStatus, Constants.ChargeSheetLockStatusV2.LOCK_PAY_BY_PATIENT_FROM_DEVICE))
                    && (Constants.ChargeSource.patientPaySources().contains(source));
        } else {
            return BitFlagUtils.checkFlagOn(lockStatus, Constants.ChargeSheetLockStatusV2.LOCK_REFUND);
        }
    }

    public static void unlockChargeSheet(ChargeSheet chargeSheet) {

        if (chargeSheet == null || chargeSheet.getAdditional() == null) {
            return;
        }

        chargeSheet.getAdditional().setLockStatus(Constants.ChargeSheetLockStatus.LOCK_STATUS_UNLOCK);
        chargeSheet.getAdditional().setAutoUnlockTime(null);
    }

    /**
     * @param chargeSheet
     * @param unLockStatus
     * @param unLockSheet  解锁的同时是否直接解锁收费单，如果为true，直接全部解锁，就不看unlockStatus了
     */
    public static void unlockChargeSheetV2(ChargeSheet chargeSheet, int unLockStatus, boolean unLockSheet) {

        if (chargeSheet == null || chargeSheet.getAdditional() == null) {
            return;
        }
        //为了兼容老逻辑，将老字段也设置为解锁
        chargeSheet.getAdditional().setLockStatus(Constants.ChargeSheetLockStatus.LOCK_STATUS_UNLOCK);
        if (unLockSheet) {
            chargeSheet.getAdditional().setLockStatusV2(Constants.ChargeSheetLockStatusV2.NONE);
        } else {
            chargeSheet.getAdditional().setLockStatusV2(BitFlagUtils.offFlag(chargeSheet.getAdditional().getLockStatusV2(), unLockStatus));
        }
        chargeSheet.getAdditional().setAutoUnlockTime(null);
    }


    /**
     * @param chargeSheet
     * @param paySource
     * @param receivableFee
     * @param isLockChargeSheet 是否为锁收费单
     */
    public static int lockChargeSheetForPay(ChargeSheet chargeSheet, int paySource, BigDecimal receivableFee, boolean isLockChargeSheet, Instant expireTime) {

        int addedLockStatus = ChargeUtils.getLockStatusV2ByPaySource(paySource, ChargePayTransaction.PayType.PAY, isLockChargeSheet);

        //增加锁单逻辑
        chargeSheet.getAdditional().setLockStatusV2(BitFlagUtils.onFlag(chargeSheet.getAdditional().getLockStatusV2(), addedLockStatus));

        //设置超时时间为5分钟后
        if (!isLockChargeSheet) {
            chargeSheet.getAdditional().setAutoUnlockTime(expireTime);
        }
        //锁单记录应收
        if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED) {
            chargeSheet.setReceivableFee(receivableFee);
        }

        return addedLockStatus;
    }

    /**
     * @param chargeSheet
     */
    public static int lockChargeSheetForRefund(ChargeSheet chargeSheet) {

        int addedLockStatus = ChargeUtils.getLockStatusV2ByPaySource(0, ChargePayTransaction.PayType.REFUND, false);

        //增加锁单逻辑
        chargeSheet.getAdditional().setLockStatusV2(BitFlagUtils.onFlag(chargeSheet.getAdditional().getLockStatusV2(), addedLockStatus));

        return addedLockStatus;
    }

    /**
     * 是否能够更新用法用量
     *
     * @return
     */
    public static boolean isCanUpdateUsageInfo(int chargeSheetType, int outpatientStatus, int sourceFormType, int clonePrescriptionType) {
        /**
         * （不是门诊或者是门诊且是待诊或者是门诊且是医生拍方抓药）并且处方是空中药房处方或者中药处方
         */
        return (chargeSheetType != ChargeSheet.Type.OUTPATIENT ||
                (chargeSheetType == ChargeSheet.Type.OUTPATIENT && outpatientStatus == ChargeSheet.OutpatientStatus.WAITING) ||
                (chargeSheetType == ChargeSheet.Type.OUTPATIENT && clonePrescriptionType == ChargeSheetAdditional.ClonePrescriptionType.FROM_PHOTO_BY_OUTPATIENT_DOCTOR))
                && (sourceFormType == Constants.SourceFormType.AIR_PHARMACY || sourceFormType == Constants.SourceFormType.PRESCRIPTION_CHINESE
                || (sourceFormType == Constants.SourceFormType.PRESCRIPTION_TYPE_EYE_GLASS && chargeSheetType != ChargeSheet.Type.OUTPATIENT));
    }

    /**
     * 是否能够更新主诉等信息
     *
     * @return
     */
    public static boolean isCanUpdateChiefComplaint(int chargeSheetType, int clonePrescriptionType) {
        /**
         * 零售开单、续方单、门诊医生拍照续方
         */
        return chargeSheetType == ChargeSheet.Type.DIRECT_SALE ||
                chargeSheetType == ChargeSheet.Type.THERAPY ||
                chargeSheetType == ChargeSheet.Type.CLONE_PRESCRIPTION ||
                (chargeSheetType == ChargeSheet.Type.OUTPATIENT && clonePrescriptionType == ChargeSheetAdditional.ClonePrescriptionType.FROM_PHOTO_BY_OUTPATIENT_DOCTOR);
    }

    public static ChargeSheet combineOutpatientChargeSheetAndRegistrationChargeSheetInMemory(ChargeSheet outpatientChargeSheet, ChargeSheet registrationChargeSheet) {
        if (outpatientChargeSheet == null) {
            return null;
        }
        if (registrationChargeSheet == null) {
            return deepCopyChargeSheet(outpatientChargeSheet);
        }

        ChargeSheet memoryOutpatientChargeSheet = deepCopyChargeSheet(outpatientChargeSheet);
        ChargeSheet memoryRegistrationChargeSheet = deepCopyChargeSheet(registrationChargeSheet);

        if (memoryRegistrationChargeSheet != null && memoryRegistrationChargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED) {
            ChargeService.moveRegistrationChargeSheetToOutpatientChargeSheet(memoryRegistrationChargeSheet, memoryOutpatientChargeSheet);
            memoryRegistrationChargeSheet.setIsDeleted(1);
            memoryOutpatientChargeSheet.setRegistrationChargeSheetId(null);
        }
        return memoryOutpatientChargeSheet;
    }

    public static void updateChargeSheetDispensingStatus(ChargeSheet chargeSheet, int dispensingStatus) {
        if (chargeSheet == null || chargeSheet.getAdditional() == null) {
            return;
        }
        chargeSheet.getAdditional().setDispensingStatus(dispensingStatus);
    }

    public static String signForOutpatient(ChargeSheet chargeSheet) {
        String data = Optional.ofNullable(chargeSheet).map(ChargeSheet::getChargeForms)
                .orElse(new ArrayList<>())
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeForm.getChargeFormItems()))
                .filter(chargeForm -> org.apache.commons.lang3.StringUtils.isNotEmpty(chargeForm.getSourceFormId()) || chargeForm.getSourceFormType() == Constants.SourceFormType.PROCESS)
                .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                .filter(chargeFormItem -> chargeFormItem.getIsDeleted() == 0)
                .filter(chargeFormItem -> org.apache.commons.lang3.StringUtils.isNotEmpty(chargeFormItem.getSourceFormItemId()) || chargeFormItem.getProductType() == Constants.ProductType.PROCESS)
                .map(chargeFormItem -> String.format("%s-%s-%s-%s-%d-%d",
                        TextUtils.alwaysString(chargeFormItem.getProductId()),
                        MathUtils.wrapBigDecimalOrZero(chargeFormItem.getExpectedUnitPrice()).stripTrailingZeros().toPlainString(),
                        MathUtils.wrapBigDecimalOrZero(chargeFormItem.getUnitPrice()).stripTrailingZeros().toPlainString(),
                        MathUtils.wrapBigDecimalOrZero(chargeFormItem.getExpectedTotalPrice()).stripTrailingZeros().toPlainString(),
                        MathUtils.wrapBigDecimalOrZero(chargeFormItem.getUnitCount()).intValue(),
                        MathUtils.wrapBigDecimalOrZero(chargeFormItem.getDoseCount()).intValue()))
                .sorted()
                .collect(Collectors.joining(","));
        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "signForOutpatient signData: {}", data);
        return MD5Utils.sign(data);
    }

    public static ChargePatientPointsPromotionInfo createChargePatientPointsPromotionInfo(ChargeSheet chargeSheet, PatientPointsInfoView patientPointsInfoView, String operatorId) {
        if (patientPointsInfoView == null || chargeSheet == null) {
            return null;
        }

        ChargePatientPointsPromotionInfo pointsPromotionInfo = new ChargePatientPointsPromotionInfo();
        pointsPromotionInfo.setId(AbcIdUtils.getUID())
                .setClinicId(chargeSheet.getClinicId())
                .setChainId(chargeSheet.getChainId())
                .setPatientOrderId(chargeSheet.getPatientOrderId())
                .setChargeSheetId(chargeSheet.getId())
                .setChecked(patientPointsInfoView.getChecked())
                .setTotalPoints(patientPointsInfoView.getTotalPoints())
                .setMaxDeductionPrice(patientPointsInfoView.getMaxDeductionPrice())
                .setCheckedDeductionPrice(patientPointsInfoView.getCheckedDeductionPrice())
                .setPointsDeductionRat(patientPointsInfoView.getPointsDeductionRat())
                .setIsNeedInsert(1);

        FillUtils.fillCreatedBy(pointsPromotionInfo, operatorId);
        return pointsPromotionInfo;
    }

    public static ChargePatientPointsDeductProductPromotionInfo createChargePatientPointsDeductPromotionInfo(ChargeSheet chargeSheet, List<PatientPointDeductProductPromotionView> patientPointDeductProductPromotionViews, int totalPoints, String operatorId) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(patientPointDeductProductPromotionViews) || chargeSheet == null) {
            return null;
        }

        ChargePatientPointsDeductProductPromotionInfo patientPointsDeductProductPromotionInfo = new ChargePatientPointsDeductProductPromotionInfo();
        patientPointsDeductProductPromotionInfo.setId(AbcIdUtils.getUID())
                .setPatientOrderId(chargeSheet.getPatientOrderId())
                .setChainId(chargeSheet.getChainId())
                .setClinicId(chargeSheet.getClinicId())
                .setChargeSheetId(chargeSheet.getId())
                .setDeductItems(patientPointDeductProductPromotionViews.stream()
                        .map(patientPointDeductPromotionView -> {
                            PatientPointDeductProductItem patientPointDeductItem = new PatientPointDeductProductItem();
                            BeanUtils.copyProperties(patientPointDeductPromotionView, patientPointDeductItem);
                            return patientPointDeductItem;
                        })
                        .collect(Collectors.toList())
                )
                .setTotalPoints(totalPoints)
                .setTotalDeductPrice(patientPointDeductProductPromotionViews.stream()
                        .filter(PatientPointDeductProductPromotionView::getChecked)
                        .map(PatientPointDeductProductPromotionView::getTotalDeductPrice)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                )
                .setIsNeedInsert(1);

        FillUtils.fillCreatedBy(patientPointsDeductProductPromotionInfo, operatorId);
        return patientPointsDeductProductPromotionInfo;
    }

    public static void insertChargeSheetAdditionalIfNeed(ChargeSheet chargeSheet, String operatorId) {
        if (chargeSheet == null || chargeSheet.getAdditional() != null) {
            return;
        }
        chargeSheet.setAdditional(createChargeSheetAdditional(chargeSheet.getChainId(), chargeSheet.getClinicId(), chargeSheet.getId(), operatorId));
    }

    public static ChargeSheetAdditional createChargeSheetAdditional(String chainId, String clinicId, String chargeSheetId, String operatorId) {
        ChargeSheetAdditional chargeSheetAdditional = new ChargeSheetAdditional();
        chargeSheetAdditional.setId(chargeSheetId);
        chargeSheetAdditional.setChainId(chainId);
        chargeSheetAdditional.setClinicId(clinicId);
        FillUtils.fillCreatedBy(chargeSheetAdditional, operatorId);
        return chargeSheetAdditional;
    }

    public static void updateChargeSheetAdditionalDoctorIdAndDiagnosis(
            ChargeSheetAdditional chargeSheetAdditional,
            String doctorId,
            String departmentId,
            String departmentName,
            List<ExtendDiagnosisInfo> extendDiagnosisInfos) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(doctorId)) {
            chargeSheetAdditional.setTranscribeDoctorId(doctorId);
            Integer retailType = chargeSheetAdditional.getRetailType();
            retailType = BitFlagUtils.offFlag(retailType, ChargeSheetAdditional.RetailType.RETAIL_DIRECT);
            retailType = BitFlagUtils.onFlag(retailType, ChargeSheetAdditional.RetailType.RETAIL_TRANSCRIBE);
            chargeSheetAdditional.setRetailType(retailType);
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(departmentId)) {
            chargeSheetAdditional.setDepartmentId(departmentId);
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(departmentName)) {
            chargeSheetAdditional.setDepartmentName(departmentName);
        }
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(extendDiagnosisInfos)) {
            chargeSheetAdditional.setDiagnosis(ChargeUtils.convertDiagnosisInfo(null, extendDiagnosisInfos));
            chargeSheetAdditional.setExtendDiagnosisInfos(extendDiagnosisInfos);
        }
    }

    public static void updateConcatMobileForOutpatient(ChargeSheet chargeSheet, String operatorId) {
        insertChargeSheetAdditionalIfNeed(chargeSheet, operatorId);

        ChargeForm chargeFormFlag = chargeSheet.getChargeForms()
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE)
                .sorted(Comparator.comparing(ChargeForm::getSort))
                .filter(chargeForm -> !StringUtils.isEmpty(chargeForm.getUsageInfoJson()))
                .filter(chargeForm -> {
                    UsageInfo usageInfo = JsonUtils.readValue(chargeForm.getUsageInfoJson(), UsageInfo.class);

                    return usageInfo != null && usageInfo.getIsDecoction() && !StringUtils.isEmpty(usageInfo.getContactMobile());
                }).findFirst().orElse(null);

        String contactMobile = null;

        if (chargeFormFlag != null) {
            UsageInfo usageInfo = JsonUtils.readValue(chargeFormFlag.getUsageInfoJson(), UsageInfo.class);
            contactMobile = usageInfo != null ? usageInfo.getContactMobile() : null;
        }

        chargeSheet.getAdditional().setContactMobile(contactMobile);
    }

    /**
     * 设置收费单包含哪些收费项字段
     *
     * @param chargeSheet
     * @param existedExecuteItems
     */
    public static void setChargeSheetIncludeItemType(ChargeSheet chargeSheet, List<ChargeExecuteItem> existedExecuteItems) {

        if (chargeSheet == null) {
            return;
        }

        if (org.apache.commons.collections.CollectionUtils.isEmpty(chargeSheet.getChargeForms())) {
            chargeSheet.setIncludeItemType(0);
            return;
        }

        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED && chargeSheet.getStatus() != Constants.ChargeSheetStatus.PART_CHARGED && chargeSheet.getStatus() != Constants.ChargeSheetStatus.CHARGED) {
            return;
        }

        //判断是否包含治疗理疗，输注项，雾化项，皮试项，护理项
        boolean isContainNeedExecuteTreatment = false, isContainInfusion = false, isContainAtomization = false, isContainAst = false, isContainNeedExecuteNurse = false, isContainExternalUse = false;

        //判断是否可执行的治疗理疗收费项
        isContainNeedExecuteTreatment = Optional.ofNullable(existedExecuteItems)
                .orElse(new ArrayList<>())
                .stream()
                .anyMatch(executeItem -> executeItem.getIsDeleted() == 0
                        && executeItem.getProductType() == Constants.ProductType.TREATMENT
                        && MathUtils.wrapBigDecimalCompare(executeItem.getUnitCount(), BigDecimal.ZERO) > 0);

        isContainInfusion = chargeSheet.getChargeForms().stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeForm.getChargeFormItems()))
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_INFUSION || chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_WESTERN)
                .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                .filter(item -> item.getIsDeleted() == 0)
                .filter(chargeFormItem -> chargeFormItem.getGoodsFeeType() != GoodsFeeType.FEE_CHILD)
                .filter(chargeFormItem -> chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.UNCHARGED || chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED)
                .filter(chargeFormItem -> org.apache.commons.lang3.StringUtils.isNotEmpty(chargeFormItem.getUsageInfoJson()))
                .anyMatch(chargeFormItem -> {
                    UsageInfo usageInfo = JsonUtils.readValue(chargeFormItem.getUsageInfoJson(), UsageInfo.class);
                    return Constants.ChargeSheetIncludeItemType.INFUSION_NAMES.contains(Optional.ofNullable(usageInfo).map(UsageInfo::getUsage).orElse(""));
                });

        isContainAtomization = chargeSheet.getChargeForms().stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeForm.getChargeFormItems()))
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_INFUSION || chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_WESTERN)
                .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                .filter(item -> item.getIsDeleted() == 0)
                .filter(chargeFormItem -> chargeFormItem.getGoodsFeeType() != GoodsFeeType.FEE_CHILD)
                .filter(chargeFormItem -> chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.UNCHARGED || chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED)
                .filter(chargeFormItem -> org.apache.commons.lang3.StringUtils.isNotEmpty(chargeFormItem.getUsageInfoJson()))
                .anyMatch(chargeFormItem -> {
                    UsageInfo usageInfo = JsonUtils.readValue(chargeFormItem.getUsageInfoJson(), UsageInfo.class);
                    return Constants.ChargeSheetIncludeItemType.ATOMIZATION_NAMES.contains(Optional.ofNullable(usageInfo).map(UsageInfo::getUsage).orElse(""));
                });


        isContainAst = chargeSheet.getChargeForms().stream()
                .filter(chargeForm -> org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeForm.getChargeFormItems()))
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_INFUSION || chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_WESTERN)
                .flatMap(chargeFormView -> chargeFormView.getChargeFormItems().stream())
                .filter(item -> item.getIsDeleted() == 0)
                .filter(chargeFormItem -> chargeFormItem.getGoodsFeeType() != GoodsFeeType.FEE_CHILD)
                .filter(chargeFormItem -> org.apache.commons.lang3.StringUtils.isNotEmpty(chargeFormItem.getUsageInfoJson()))
                .filter(chargeFormItem -> chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.UNCHARGED || chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED)
                .anyMatch(chargeFormItemView -> {
                    UsageInfo usageInfo = JsonUtils.readValue(chargeFormItemView.getUsageInfoJson(), UsageInfo.class);
                    return Optional.ofNullable(usageInfo).map(UsageInfo::getAst).orElse(0) == 1;
                });

        //判断是否可执行的护理收费项
        isContainNeedExecuteNurse = Optional.ofNullable(existedExecuteItems)
                .orElse(new ArrayList<>())
                .stream()
                .anyMatch(executeItem -> executeItem.getIsDeleted() == 0
                        && executeItem.getProductType() == Constants.ProductType.NURSE
                        && MathUtils.wrapBigDecimalCompare(executeItem.getUnitCount(), BigDecimal.ZERO) > 0);

        isContainExternalUse = chargeSheet.getChargeForms().stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeForm.getChargeFormItems()))
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_INFUSION || chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_WESTERN)
                .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                .filter(item -> item.getIsDeleted() == 0)
                .filter(chargeFormItem -> chargeFormItem.getGoodsFeeType() != GoodsFeeType.FEE_CHILD)
                .filter(chargeFormItem -> chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.UNCHARGED || chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED)
                .filter(chargeFormItem -> org.apache.commons.lang3.StringUtils.isNotEmpty(chargeFormItem.getUsageInfoJson()))
                .anyMatch(chargeFormItem -> {
                    UsageInfo usageInfo = JsonUtils.readValue(chargeFormItem.getUsageInfoJson(), UsageInfo.class);
                    return Constants.ChargeSheetIncludeItemType.EXTERNAL_USE_NAMES.contains(Optional.ofNullable(usageInfo).map(UsageInfo::getUsage).orElse(""));
                });

        int treatmentType = isContainNeedExecuteTreatment ? Constants.ChargeSheetIncludeItemType.NEED_EXECUTE_TREATMENT : 0;
        int infusionType = isContainInfusion ? Constants.ChargeSheetIncludeItemType.INFUSION : 0;
        int atomizationType = isContainAtomization ? Constants.ChargeSheetIncludeItemType.ATOMIZATION : 0;
        int astType = isContainAst ? Constants.ChargeSheetIncludeItemType.AST : 0;
        int nurseType = isContainNeedExecuteNurse ? Constants.ChargeSheetIncludeItemType.NEED_EXECUTE_NURSE : 0;
        int externalUseType = isContainExternalUse ? Constants.ChargeSheetIncludeItemType.EXTERNAL_USE : 0;

        chargeSheet.setIncludeItemType(treatmentType | infusionType | atomizationType | astType | nurseType | externalUseType);

    }

    public static ChargeTransactionRecord createChargeTransactionRecord(ChargeSheet chargeSheet, String chargeTransactionId,
                                                                        ChargeFormItem chargeFormItem,
                                                                        BigDecimal totalPrice, int chargeType, int type, String operatorId) {

        ChargeTransactionRecord record = new ChargeTransactionRecord();
        record.setId(AbcIdUtils.getUUID());
        record.setPatientOrderId(chargeSheet.getPatientOrderId());
        record.setChainId(chargeSheet.getChainId());
        record.setClinicId(chargeSheet.getClinicId());
        record.setChargeSheetId(chargeSheet.getId());
        record.setPatientId(chargeSheet.getPatientId());
        record.setTransactionId(chargeTransactionId);
        record.setChargeType(chargeType);
        record.setType(type);
        record.setTotalPrice(totalPrice);
        record.setTotalCostPrice(BigDecimal.ZERO);
        record.setDiscountPrice(BigDecimal.ZERO);
        Optional.ofNullable(chargeFormItem).ifPresent(c -> {
            record.setChargeFormItemId(c.getId());
            record.setProductId(c.getProductId());
            record.setProductType(c.getProductType());
            record.setProductSubType(c.getProductSubType());
            record.setProductUnitCount(c.getUnitCount());
            record.setFeeTypeId(c.getFeeTypeId());
            record.setGoodsFeeType(c.getGoodsFeeType());
        });

        FillUtils.fillCreatedBy(record, operatorId);
        return record;
    }

    public static MemberInfo convertPatientMemberInfoToMemberInfo(cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientMemberInfo patientMemberInfo) {

        if (patientMemberInfo == null) {
            return null;
        }

        MemberInfo memberInfo = new MemberInfo();
        memberInfo.setPrincipal(patientMemberInfo.getPrincipal());
        memberInfo.setPresent(patientMemberInfo.getPresent());
        memberInfo.setPoints(patientMemberInfo.getPoints());
        memberInfo.setPointsTotal(patientMemberInfo.getPointsTotal());
        MemberInfo.Patient patient = new MemberInfo.Patient();
        patient.setId(patientMemberInfo.getPatientId());
        patient.setName(patientMemberInfo.getPatientName());
        patient.setMobile(patientMemberInfo.getPatientMobile());
        patient.setIsMember(patientMemberInfo.getMemberTypeInfo() != null ? 1 : 0);
        memberInfo.setPatient(patient);
        if (patientMemberInfo.getMemberTypeInfo() != null) {
            MemberInfo.MemberType memberType = new MemberInfo.MemberType();
            memberType.setId(patientMemberInfo.getMemberTypeInfo().getMemberTypeId());
            memberType.setName(patientMemberInfo.getMemberTypeInfo().getMemberTypeName());
            memberInfo.setMemberType(memberType);
        }

        return memberInfo;

    }

    public static void clearChargeSheetAdjustmentFeeAndExpectedPrice(ChargeSheet chargeSheet) {
        if (chargeSheet == null) {
            return;
        }

        chargeSheet.setDraftAdjustmentFee(null);
        chargeSheet.setOutpatientAdjustmentFee(null);
        chargeSheet.setOddFee(null);

        if (chargeSheet.getChargeForms() == null) {
            return;
        }

        chargeSheet.getChargeForms()
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .forEach(ChargeUtils::clearChargeFormExpectedPrice);

    }

    private static void clearChargeFormExpectedPrice(ChargeForm chargeForm) {

        if (chargeForm == null) {
            return;
        }

        chargeForm.setExpectedTotalPrice(null);

        if (chargeForm.getChargeFormItems() == null) {
            return;
        }

        chargeForm.getChargeFormItems()
                .stream()
                .filter(item -> item.getIsDeleted() == 0)
                .forEach(ChargeUtils::clearChargeFormItemExpectedPrice);
    }

    private static void clearChargeFormItemExpectedPrice(ChargeFormItem chargeFormItem) {

        if (chargeFormItem == null) {
            return;
        }

        chargeFormItem.setExpectedUnitPrice(null);
        chargeFormItem.setExpectedTotalPrice(null);
        chargeFormItem.setExpectedTotalPriceRatio(null);
    }

    public static Map<String, ChargeAction.PayActionInfo> generatePayModeNameMap(List<ChargeAction> chargeActions, Map<Long, Integer> payModeTypes) {
        Map<String, ChargeAction.PayActionInfo> payModeNameMap = Optional.ofNullable(chargeActions).orElse(new ArrayList<>())
                .stream()
                .filter(chargeAction -> chargeAction.getPayActionInfos() != null)
                .flatMap(chargeAction -> chargeAction.getPayActionInfos().stream())
                .filter(payActionInfo -> org.apache.commons.lang3.StringUtils.isNotEmpty(payActionInfo.getPayModeName()))
                .collect(Collectors.toMap(payActionInfo -> generatePayModeNameKey(payActionInfo.getPayMode(), payActionInfo.getPaySubMode(), payModeTypes.getOrDefault(Long.valueOf(payActionInfo.getPayMode()), 0), payActionInfo.getThirdPartyPayCardId()),
                        payActionInfo -> payActionInfo, (a, b) -> a)
                );

        return payModeNameMap;
    }

    public static String generatePayModeNameKey(int payMode, int paySubMode, int payModeType, String thirdPartyPayCardId) {
        if (payMode == Constants.ChargePayMode.PROMOTION_CARD) {
            return String.format("%d-%s", payMode, thirdPartyPayCardId);
        }

        if (payMode == Constants.ChargePayMode.ABC_PAY  //abc支付
                || (payMode == Constants.ChargePayMode.HEALTH_CARD && paySubMode == Constants.ChargePaySubMode.WECHAT_MOBILE_SHEBAO_INSURANCE) //医保-移动支付
                || (payMode == Constants.ChargePayMode.WECHAT_PAY && paySubMode == Constants.ChargePaySubMode.WECHAT_MOBILE_SHEBAO_CASH) //医保-微信直付
                || payMode == Constants.ChargePayMode.OUTPATIENT_CENTER_PAY
                || payModeType == Constants.ChargePayModeType.THIRD_PARTY_COMMON_PAY
        ) {
            return String.format("%d-%d", payMode, paySubMode);
        }

        return String.valueOf(payMode);

    }

    public static String generateTransactionUniqueKey(ChargeTransaction chargeTransaction) {

        if (chargeTransaction == null) {
            return "";
        }

        if (chargeTransaction.getPayMode() == Constants.ChargePayMode.PROMOTION_CARD) {
            return String.format("%d-%s", chargeTransaction.getPayMode(), chargeTransaction.getThirdPartyPayCardId());
        }
        if (chargeTransaction.getPayMode() == Constants.ChargePayMode.ABC_PAY
                || (chargeTransaction.getPayMode() == Constants.ChargePayMode.HEALTH_CARD && chargeTransaction.getPaySubMode() == Constants.ChargePaySubMode.WECHAT_MOBILE_SHEBAO_INSURANCE)
                || (chargeTransaction.getPayMode() == Constants.ChargePayMode.WECHAT_PAY && chargeTransaction.getPaySubMode() == Constants.ChargePaySubMode.WECHAT_MOBILE_SHEBAO_CASH)
                || chargeTransaction.getPayMode() == Constants.ChargePayMode.OUTPATIENT_CENTER_PAY
                || chargeTransaction.getPayModeType() == Constants.ChargePayModeType.THIRD_PARTY_COMMON_PAY
        ) {
            if (chargeTransaction.getAmount().compareTo(BigDecimal.ZERO) >= 0) {
                return String.format("%d-%d-%s", chargeTransaction.getPayMode(), chargeTransaction.getPaySubMode(), chargeTransaction.getId());
            } else {
                return String.format("%d-%d-%s", chargeTransaction.getPayMode(), chargeTransaction.getPaySubMode(), chargeTransaction.getAssociateTransactionId());
            }
        }
        return String.valueOf(chargeTransaction.getPayMode());
    }

    /**
     * 退还抵扣次数
     *
     * @param chargeSheet
     * @param operatorId
     */
    public static void refundPatientDeduct(ChargeSheet chargeSheet,
                                           BiFunction<String, List<String>, List<PatientCardView>> listPatientsCardsByIdsFunction,
                                           Consumer<CardPatientPresentsDeductReq> deductConsumer,
                                           String transactionId,
                                           String operatorId) {
        if (chargeSheet == null || CollectionUtils.isEmpty(chargeSheet.getPatientCardPromotionInfos())) {
            return;
        }

        //校验卡项是否可用，如果不可用，就不退了
        List<String> promotionIds = Optional.ofNullable(chargeSheet.getPatientCardPromotionInfos()).orElse(new ArrayList<>())
                .stream()
                .filter(patientCardPromotionInfo -> patientCardPromotionInfo.getIsDeleted() == 0)
                .filter(ChargePatientCardPromotionInfo::getChecked)
                .filter(patientCardPromotionInfo -> org.apache.commons.collections.CollectionUtils.isNotEmpty(patientCardPromotionInfo.getDeductItems()))
                .map(patientCardPromotionInfo -> patientCardPromotionInfo.getPromotionId())
                .distinct()
                .collect(Collectors.toList());

        if (org.apache.commons.collections.CollectionUtils.isEmpty(promotionIds)) {
            return;
        }


        List<cn.abcyun.bis.rpc.sdk.cis.model.promotion.PatientCardView> patientCardViews = listPatientsCardsByIdsFunction.apply(chargeSheet.getChainId(), promotionIds);

        List<String> availablePromotionIds = Optional.ofNullable(patientCardViews).orElse(new ArrayList<>()).stream().map(cn.abcyun.bis.rpc.sdk.cis.model.promotion.PatientCardView::getId).distinct().collect(Collectors.toList());

        if (org.apache.commons.collections.CollectionUtils.isEmpty(availablePromotionIds)) {
            return;
        }

        List<CardPatientPresentsDeductReq.Deduct> deducts = chargeSheet.getPatientCardPromotionInfos().stream()
                .filter(patientCardPromotionInfo -> patientCardPromotionInfo.getIsDeleted() == 0)
                .filter(patientCardPromotionInfo -> availablePromotionIds.contains(patientCardPromotionInfo.getPromotionId()))
                .map(patientCardPromotionInfo -> {
                    CardPatientPresentsDeductReq.Deduct deduct = new CardPatientPresentsDeductReq.Deduct();
                    deduct.setPatientCardId(patientCardPromotionInfo.getPromotionId());
                    deduct.setDeductPresentList(patientCardPromotionInfo.getDeductItems().stream()
                            .filter(promotionDeductItem -> promotionDeductItem.getChecked())
                            .filter(promotionDeductItem -> promotionDeductItem.getStatus() == PromotionDeductItem.Status.DEDUCTED || promotionDeductItem.getStatus() == PromotionDeductItem.Status.PARTED_REFUND_DEDUCT)
                            .peek(promotionDeductItem -> promotionDeductItem.setStatus(PromotionDeductItem.Status.REFUND_DEDUCT))
                            .map(promotionDeductItem -> {
                                CardPatientPresentsDeductReq.Deduct.Present present = new CardPatientPresentsDeductReq.Deduct.Present();
                                present.setPresentId(promotionDeductItem.getId());
                                present.setDeductCount(promotionDeductItem.getCanRefundCount());
                                present.setGoodsId(promotionDeductItem.getGoodsId());
                                present.setGoodsType(promotionDeductItem.getGoodsType());
                                present.setGoodsSubType(promotionDeductItem.getGoodsSubType());
                                present.setGoodsCMSpec(promotionDeductItem.getGoodsCMSpec());
                                return present;
                            }).collect(Collectors.toList()));

                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(deduct.getDeductPresentList())) {
                        FillUtils.fillLastModifiedBy(patientCardPromotionInfo, operatorId);
                    }
                    return deduct;
                })
                .filter(deduct -> org.apache.commons.collections.CollectionUtils.isNotEmpty(deduct.getDeductPresentList()))
                .collect(Collectors.toList());

        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(deducts)) {
            CardPatientPresentsDeductReq req = new CardPatientPresentsDeductReq();
            req.setDeductPatientId(chargeSheet.getPatientId());
            req.setChainId(chargeSheet.getChainId());
            req.setClinicId(chargeSheet.getClinicId());
            req.setChargeSheetId(chargeSheet.getId());
            req.setDeducts(deducts);
            req.setType(CardPatientPresentsDeductReq.Type.UNDO_DEDUCT);
            req.setSellerId(chargeSheet.getSellerId());
            req.setDoctorId(chargeSheet.getDoctorId());
            req.setOperatorId(operatorId);
            req.setChargeTransactionId(transactionId);
            deductConsumer.accept(req);
        }
    }

    public static void refundVerification(ChargeSheet chargeSheet, ChargeService chargeService, String operatorId) {
        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED) {
            return;
        }
        List<VerificationRefundReq.VerificationItemRefundReq> verificationItemRefundReqs = cn.abcyun.cis.commons.util.ListUtils.alwaysList(chargeSheet.getChargeVerifyInfos())
                .stream()
                .filter(chargeVerifyInfo -> chargeVerifyInfo.getIsDeleted() == 0)
                .flatMap(chargeVerifyInfo -> chargeVerifyInfo.getItemVerifyDetails().stream())
                .flatMap(itemVerifyDetail -> itemVerifyDetail.getVerifyInfoDetails().stream())
                .map(verifyInfoDetail -> {

                    VerificationRefundReq.VerificationItemRefundReq verificationItemRefundReq = new VerificationRefundReq.VerificationItemRefundReq();
                    verificationItemRefundReq.setVerificationCode(verifyInfoDetail.getVerificationCode());
                    verificationItemRefundReq.setCount(verifyInfoDetail.getDeductedCount());
                    return verificationItemRefundReq;
                }).collect(Collectors.toList());
        VerificationRefundReq req = new VerificationRefundReq();
        req.setChainId(chargeSheet.getChainId());
        req.setClinicId(chargeSheet.getClinicId());
        req.setOperatorId(operatorId);

        if (org.apache.commons.collections.CollectionUtils.isEmpty(verificationItemRefundReqs)) {
            return;
        }

        req.setVerificationItemRefundReqs(verificationItemRefundReqs);

        chargeService.getCisMallOrderProvider().refundVerification(req);
    }

    public static String convertDiagnosisInfo(List<DiagnosisInfo> diagnosisInfos, List<ExtendDiagnosisInfo> extendDiagnosisInfos) {
        if (!CollectionUtils.isEmpty(extendDiagnosisInfos)) {
            return extendDiagnosisInfos.stream()
                    .filter(Objects::nonNull)
                    .map(dentistryDiagnosis -> {
                        List<DiagnosisInfo> value = dentistryDiagnosis.getValue();
                        if (CollectionUtils.isEmpty(value)) {
                            return null;
                        }
                        return value.stream().map(DiagnosisInfo::getName).filter(name -> !StringUtils.isEmpty(name)).collect(Collectors.joining("，"));
                    }).filter(item -> !StringUtils.isEmpty(item))
                    .collect(Collectors.joining("；"));
        } else if (!CollectionUtils.isEmpty(diagnosisInfos)) {
            return diagnosisInfos.stream()
                    .filter(Objects::nonNull)
                    .map(DiagnosisInfo::getName)
                    .filter(name -> !StringUtils.isEmpty(name))
                    .collect(Collectors.joining("，"));
        } else {
            return "";
        }
    }

    public static ChargeSheetMessage.ChargeSheet convertToMessageChargeSheet(ChargeSheetExtend chargeSheet,
                                                                             List<ChargeTransaction> currentAddedTransactions,
                                                                             List<ChargeTransactionRecord> allChargeTransactionRecords,
                                                                             Map<String, String> formItemIdAssociateFormItemIdMap,
                                                                             Map<String, List<ChargeFormItemBatchInfo>> currentRefundBatchInfoMap) {
        if (chargeSheet == null) {
            return null;
        }
        ChargeSheetMessage.ChargeSheet messageChargeSheet = new ChargeSheetMessage.ChargeSheet();
        BeanUtils.copyProperties(chargeSheet, messageChargeSheet);
        messageChargeSheet.setDiagnosisInfos(chargeSheet.getDiagnosisInfos());
        messageChargeSheet.setDeliveryInfo(Optional.ofNullable(chargeSheet.getDeliveryInfo())
                .map(chargeDeliveryInfo -> {
                    cn.abcyun.cis.commons.rpc.charge.ChargeDeliveryInfo messageDeliveryInfo = new cn.abcyun.cis.commons.rpc.charge.ChargeDeliveryInfo();
                    BeanUtils.copyProperties(chargeDeliveryInfo, messageDeliveryInfo);
                    messageDeliveryInfo.setDeliveryCompany(Optional.ofNullable(chargeDeliveryInfo.getDeliveryCompany())
                            .map(chargeDeliveryCompany -> {
                                ChargeDeliveryCompany messageDeliveryCompany = new ChargeDeliveryCompany();
                                BeanUtils.copyProperties(chargeDeliveryCompany, messageDeliveryCompany);
                                return messageDeliveryCompany;
                            }).orElse(null)
                    );
                    return messageDeliveryInfo;
                }).orElse(null)
        );
        messageChargeSheet.setExtendDiagnosisInfos(chargeSheet.getExtendDiagnosisInfos());
        messageChargeSheet.setChargeForms(Optional.ofNullable(chargeSheet.getChargeForms()).orElse(new ArrayList<>())
                        .stream()
                        .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                        .map(chargeForm -> {
                            ChargeSheetMessage.ChargeForm messageChargeForm = new ChargeSheetMessage.ChargeForm();
                            BeanUtils.copyProperties(chargeForm, messageChargeForm);
                            messageChargeForm.setProcessInfo(Optional.ofNullable(chargeForm.getProcessInfo())
                                    .map(processInfo -> {
                                        cn.abcyun.cis.commons.rpc.charge.ChargeSheetProcessInfo messageProcessInfo = new cn.abcyun.cis.commons.rpc.charge.ChargeSheetProcessInfo();
                                        BeanUtils.copyProperties(processInfo, messageProcessInfo);
                                        return messageProcessInfo;
                                    }).orElse(null)
                            );
                            messageChargeForm.setChargeFormItems(Optional.ofNullable(chargeForm.getChargeFormItems()).orElse(new ArrayList<>())
                                            .stream()
                                            .filter(item -> item.getIsDeleted() == 0)
                                            .map(chargeFormItem -> {
                                                ChargeSheetMessage.ChargeFormItem messageChargeFormItem = new ChargeSheetMessage.ChargeFormItem();
                                                BeanUtils.copyProperties(chargeFormItem, messageChargeFormItem);
                                                // 复制扩展属性信息
                                                if (chargeFormItem.getAdditional() != null) {
                                                    BeanUtils.copyProperties(chargeFormItem.getAdditional(), messageChargeFormItem);
                                                    List<TraceableCode> svrTraceableCodeList = chargeFormItem.getAdditional().getTraceableCodeList();
                                                    if (!CollectionUtils.isEmpty(svrTraceableCodeList)) {
                                                        messageChargeFormItem.setTraceableCodeList(svrTraceableCodeList.stream().map(traceableCode -> {
                                                            //性能 ，明确知道发药要什么字段
                                                            cn.abcyun.cis.commons.amqp.message.TraceableCode messageTraceableCode = new cn.abcyun.cis.commons.amqp.message.TraceableCode();
                                                            messageTraceableCode.setId(traceableCode.getId());
                                                            messageTraceableCode.setNo(traceableCode.getNo());
                                                            messageTraceableCode.setType(traceableCode.getType());
                                                            messageTraceableCode.setBatchId(traceableCode.getBatchId());
                                                            messageTraceableCode.setBatchNo(traceableCode.getBatchNo());
                                                            messageTraceableCode.setGoodsId(traceableCode.getGoodsId());
                                                            messageTraceableCode.setUsed(traceableCode.getUsed());
                                                            //数据兼容 传了his数量用his数量
                                                            if (MathUtils.compareZero(traceableCode.getHisPackageCount()) > 0 || MathUtils.compareZero(traceableCode.getHisPieceCount()) > 0) {
                                                                messageTraceableCode.setHisPieceCount(traceableCode.getHisPieceCount());
                                                                messageTraceableCode.setHisPackageCount(traceableCode.getHisPackageCount());
                                                            } else {
                                                                messageTraceableCode.setCount(traceableCode.getCount());
                                                            }
                                                            messageTraceableCode.setDismountingSn(traceableCode.getDismountingSn());
                                                            return messageTraceableCode;
                                                        }).filter(Objects::nonNull).collect(Collectors.toList()));
                                                    }
                                                    Optional.ofNullable(chargeFormItem.getAdditional().getPointRateInfo())
                                                            .ifPresent(itemPointRateInfo -> {
                                                                ChargeSheetMessage.ItemPointRateInfo messsgePointRateInfo = new ChargeSheetMessage.ItemPointRateInfo();
                                                                BeanUtils.copyProperties(itemPointRateInfo, messsgePointRateInfo);
                                                                messageChargeFormItem.setPointRateInfo(messsgePointRateInfo);
                                                            });
                                                }

                                                messageChargeFormItem.setBatchInfos(Optional.ofNullable(chargeFormItem.getChargeFormItemBatchInfos()).orElse(new ArrayList<>())
                                                        .stream()
                                                        .filter(chargeFormItemBatchInfo -> chargeFormItemBatchInfo.getIsDeleted() == 0)
                                                        .map(chargeFormItemBatchInfo -> {
                                                            ChargeSheetMessage.ChargeFormItemBatchInfo messageBatchInfo = new ChargeSheetMessage.ChargeFormItemBatchInfo();
                                                            BeanUtils.copyProperties(chargeFormItemBatchInfo, messageBatchInfo);
                                                            return messageBatchInfo;
                                                        })
                                                        .collect(Collectors.toList())
                                                );
                                                return messageChargeFormItem;
                                            })
                                            .collect(Collectors.toList())
                            );
                            messageChargeForm.setChargeAirPharmacyLogistics(Optional.ofNullable(chargeForm.getChargeAirPharmacyLogistics())
                                    .map(chargeAirPharmacyLogistics -> {
                                        cn.abcyun.cis.commons.rpc.charge.ChargeAirPharmacyLogisticsView messageChargeAirPharmacyLogistics = new cn.abcyun.cis.commons.rpc.charge.ChargeAirPharmacyLogisticsView();
                                        BeanUtils.copyProperties(chargeAirPharmacyLogistics, messageChargeAirPharmacyLogistics);
                                        messageChargeAirPharmacyLogistics.setDeliveryCompany(
                                                Optional.ofNullable(chargeAirPharmacyLogistics.getDeliveryCompany())
                                                        .map(deliveryCompany -> {
                                                            ChargeDeliveryCompanyView messageDeliveryCompany = new ChargeDeliveryCompanyView();
                                                            BeanUtils.copyProperties(deliveryCompany, messageDeliveryCompany);
                                                            return messageDeliveryCompany;
                                                        }).orElse(null)
                                        );
                                        return messageChargeAirPharmacyLogistics;
                                    }).orElse(null)
                            );
                            return messageChargeForm;
                        })
                        .collect(Collectors.toList())
        );
        messageChargeSheet.setChargeTransactions(Optional.ofNullable(chargeSheet.getChargeTransactions())
                .orElse(new ArrayList<>())
                .stream()
                .map(chargeTransaction -> {
                    cn.abcyun.cis.commons.rpc.charge.ChargeTransaction messageChargeTransaction = new cn.abcyun.cis.commons.rpc.charge.ChargeTransaction();
                    BeanUtils.copyProperties(chargeTransaction, messageChargeTransaction);
                    return messageChargeTransaction;
                }).collect(Collectors.toList())
        );

        // 过滤出currentAddedTransactions下的transactionRecord
        Set<String> currentAddedTransactionIds = Optional.ofNullable(currentAddedTransactions).orElse(Lists.newArrayList()).stream().map(ChargeTransaction::getId).collect(Collectors.toSet());
        List<ChargeTransactionRecord> currentAddedTransactionRecords = Optional.ofNullable(allChargeTransactionRecords).orElse(Lists.newArrayList()).stream()
                .filter(chargeTransactionRecord -> currentAddedTransactionIds.contains(chargeTransactionRecord.getTransactionId()))
                .collect(Collectors.toList());
        // 如果是退费
        Map<String, List<cn.abcyun.cis.commons.rpc.charge.ChargeTransactionRecord>> transactionIdMessageTransactionRecordsMap;
        if (!CollectionUtils.isEmpty(currentAddedTransactionRecords) && Objects.equals(currentAddedTransactionRecords.get(0).getChargeType(), ChargeTransactionRecord.ChargeType.REFUND)) {
            Map<String, Map<String, List<ChargeFormItemBatchInfo>>> currentRefundBatchInfoTransactionIdItemIdMap = Optional.ofNullable(currentRefundBatchInfoMap)
                    .orElse(new HashMap<>())
                    .entrySet()
                    .stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue()
                            .stream()
                            .collect(Collectors.groupingBy(ChargeFormItemBatchInfo::getChargeFormItemId)), (a, b) -> a));
            transactionIdMessageTransactionRecordsMap = currentAddedTransactionRecords
                    .stream()
                    .map(chargeTransactionRecord -> {
                        Map<String, List<ChargeFormItemBatchInfo>> currentRefundBatchInfoItemIdMap = currentRefundBatchInfoTransactionIdItemIdMap.getOrDefault(chargeTransactionRecord.getTransactionId(), new HashMap<>());
                        cn.abcyun.cis.commons.rpc.charge.ChargeTransactionRecord messageTransactionRecord = null;
                        if (Objects.nonNull(chargeTransactionRecord.getAdditional())) {
                            messageTransactionRecord = JsonUtils.readValue(JsonUtils.dump(chargeTransactionRecord.getAdditional()), cn.abcyun.cis.commons.rpc.charge.ChargeTransactionRecord.class);
                        }
                        if (Objects.isNull(messageTransactionRecord)) {
                            messageTransactionRecord = new cn.abcyun.cis.commons.rpc.charge.ChargeTransactionRecord();
                        }
                        BeanUtils.copyProperties(chargeTransactionRecord, messageTransactionRecord);

                        // 将退费项chargeFormItemId替换为关联的收费项chargeFormItemId
                        if (!StringUtils.isEmpty(chargeTransactionRecord.getChargeFormItemId())
                                && formItemIdAssociateFormItemIdMap.containsKey(chargeTransactionRecord.getChargeFormItemId())) {
                            String paidChargeFormItemId = formItemIdAssociateFormItemIdMap.get(chargeTransactionRecord.getChargeFormItemId());
                            messageTransactionRecord.setChargeFormItemId(paidChargeFormItemId);
                            messageTransactionRecord.setBatchInfos(currentRefundBatchInfoItemIdMap.getOrDefault(chargeTransactionRecord.getChargeFormItemId(), new ArrayList<>())
                                    .stream()
                                    .map(chargeFormItemBatchInfo -> {
                                        ChargeSheetMessage.ChargeFormItemBatchInfo messageBatchInfo = new ChargeSheetMessage.ChargeFormItemBatchInfo();
                                        BeanUtils.copyProperties(chargeFormItemBatchInfo, messageBatchInfo);
                                        messageBatchInfo.setChargeFormItemId(paidChargeFormItemId);
                                        return messageBatchInfo;
                                    })
                                    .collect(Collectors.toList())
                            );
                        }
                        return messageTransactionRecord;
                    })
                    .collect(Collectors.groupingBy(cn.abcyun.cis.commons.rpc.charge.ChargeTransactionRecord::getTransactionId));
        } else {
            transactionIdMessageTransactionRecordsMap = currentAddedTransactionRecords.stream()
                    .map(chargeTransactionRecord -> {
                        cn.abcyun.cis.commons.rpc.charge.ChargeTransactionRecord messageTransactionRecord = null;
                        if (Objects.nonNull(chargeTransactionRecord.getAdditional())) {
                            messageTransactionRecord = JsonUtils.readValue(JsonUtils.dump(chargeTransactionRecord.getAdditional()), cn.abcyun.cis.commons.rpc.charge.ChargeTransactionRecord.class);
                        }
                        if (Objects.isNull(messageTransactionRecord)) {
                            messageTransactionRecord = new cn.abcyun.cis.commons.rpc.charge.ChargeTransactionRecord();
                        }
                        BeanUtils.copyProperties(chargeTransactionRecord, messageTransactionRecord);
                        return messageTransactionRecord;
                    })
                    .collect(Collectors.groupingBy(cn.abcyun.cis.commons.rpc.charge.ChargeTransactionRecord::getTransactionId));
        }

        messageChargeSheet.setCurrentAddedTransactions(Optional.ofNullable(currentAddedTransactions).orElse(new ArrayList<>()).stream()
                .map(chargeTransaction -> {
                    cn.abcyun.cis.commons.rpc.charge.ChargeTransaction messageChargeTransaction = new cn.abcyun.cis.commons.rpc.charge.ChargeTransaction();
                    BeanUtils.copyProperties(chargeTransaction, messageChargeTransaction);
                    messageChargeTransaction.setRecords(transactionIdMessageTransactionRecordsMap.get(messageChargeTransaction.getId()));
                    return messageChargeTransaction;
                }).collect(Collectors.toList())
        );
        messageChargeSheet.setAdditionalFees(Optional.ofNullable(chargeSheet.getAdditionalFees()).orElse(new ArrayList<>()).stream()
                .filter(additionalFee -> additionalFee.getIsDeleted() == 0)
                .map(chargeAdditionalFee -> {
                    ChargeAdditionalFee messageChargeAdditionalFee = new ChargeAdditionalFee();
                    BeanUtils.copyProperties(chargeAdditionalFee, messageChargeAdditionalFee);
                    return messageChargeAdditionalFee;
                }).collect(Collectors.toList())
        );
        return messageChargeSheet;
    }

    public static Pair<Boolean, String> isCanUploadNotification(int status, int type, BigDecimal netIncomeFee) {
        if (status != Constants.ChargeSheetStatus.CHARGED && status != Constants.ChargeSheetStatus.PART_REFUNDED) {
            return Pair.of(false, "收费单状态错误");
        }

        if (type != ChargeSheet.Type.OUTPATIENT) {
            return Pair.of(false, "该收费单不支持上传收费告知书");
        }

        if (MathUtils.wrapBigDecimalCompare(Optional.ofNullable(netIncomeFee).orElse(BigDecimal.ZERO), Constants.CHARGE_NEED_UPLOAD_NOTIFICATION_LOWEST_LINE) < 0) {
            return Pair.of(false, "收费单金额未超过500，不支持上传收费告知书");
        }

        return Pair.of(true, "");
    }


    public static List<DiagnosisInfo> generateDiagnosisInfos(List<DiagnosisInfo> diagnosisInfos, List<ExtendDiagnosisInfo> extendDiagnosisInfos) {
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(extendDiagnosisInfos)) {
            return extendDiagnosisInfos.stream()
                    .filter(extendDiagnosisInfo -> org.apache.commons.collections.CollectionUtils.isNotEmpty(extendDiagnosisInfo.getValue()))
                    .flatMap(extendDiagnosisInfo -> extendDiagnosisInfo.getValue().stream())
                    .collect(Collectors.toList());
        }

        return diagnosisInfos;
    }


    public static List<ExtendDiagnosisInfo> generateExtendDiagnosisInfos(List<DiagnosisInfo> diagnosisInfos, List<ExtendDiagnosisInfo> extendDiagnosisInfos) {
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(extendDiagnosisInfos)) {
            return extendDiagnosisInfos;
        }

        if (org.apache.commons.collections.CollectionUtils.isEmpty(diagnosisInfos)) {
            return new ArrayList<>();
        }

        ExtendDiagnosisInfo extendDiagnosisInfo = new ExtendDiagnosisInfo();
        extendDiagnosisInfo.setValue(diagnosisInfos);
        extendDiagnosisInfos = Collections.singletonList(extendDiagnosisInfo);
        return extendDiagnosisInfos;
    }

    public static void checkPayModeOrThrowException(List<CombinedPayItem> payItems) {
        if (CollectionUtils.isEmpty(payItems)) {
            throw new ChargeServiceException(ChargeServiceError.PAY_MODE_NOT_SUPPORT);
        }

        CombinedPayItem payItem = payItems.get(0);

        if (Objects.isNull(payItem)) {
            throw new ChargeServiceException(ChargeServiceError.PAY_MODE_NOT_SUPPORT);
        }

        if (!Constants.ChargePayMode.PATIENT_SUPPORT_PAY_MODES.contains(String.format("%d-%d", payItem.getPayMode(), payItem.getPaySubMode()))) {
            throw new ChargeServiceException(ChargeServiceError.PAY_MODE_NOT_SUPPORT);
        }
    }

    //新的查询门诊状态的接口在用
    public static void appendDeliveryAndProcessInfo(OutpatientSheetCalculateRsp.OutpatientForm outpatientForm,
                                                    ChargeDeliveryInfo deliveryInfo,
                                                    ChargeForm deliveryForm,
                                                    ChargeForm chargeForm,
                                                    ChargeForm processForm,
                                                    boolean isLocalChinesePrescription,
                                                    CreateOrderView createOrderView) {

        if (Objects.isNull(outpatientForm)) {
            return;
        }

        if (Objects.nonNull(deliveryInfo)) {

            ChargeFormItem deliveryFormItem = Optional.ofNullable(deliveryForm).map(ChargeForm::getChargeFormItems).orElse(new ArrayList<>())
                    .stream()
                    .filter(item -> item.getIsDeleted() == 0)
                    .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.EXPRESS_DELIVERY)
                    .filter(chargeFormItem -> chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.REFUNDED && chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.UNSELECTED)
                    .findFirst().orElse(null);

            if (Objects.nonNull(deliveryFormItem)) {
                ChargeDeliveryInfoView chargeDeliveryInfoView = new ChargeDeliveryInfoView();
                BeanUtils.copyProperties(deliveryInfo, chargeDeliveryInfoView);

                if (deliveryInfo.getDeliveryCompany() != null) {
                    ChargeDeliveryCompanyView chargeDeliveryCompanyVo = new ChargeDeliveryCompanyView();
                    BeanUtils.copyProperties(deliveryInfo.getDeliveryCompany(), chargeDeliveryCompanyVo);
                    chargeDeliveryInfoView.setDeliveryCompany(chargeDeliveryCompanyVo);
                }
                outpatientForm.setDeliveryInfo(chargeDeliveryInfoView);
            }
        }

        if ((chargeForm.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY || chargeForm.getPharmacyType() == GoodsConst.PharmacyType.VIRTUAL_PHARMACY) && Objects.nonNull(chargeForm.getChargeAirPharmacyLogistics())) {
            ChargeAirPharmacyLogistics chargeAirPharmacyLogistics = chargeForm.getChargeAirPharmacyLogistics();
            ChargeDeliveryInfoView chargeDeliveryInfoView = new ChargeDeliveryInfoView();
            BeanUtils.copyProperties(chargeAirPharmacyLogistics, chargeDeliveryInfoView);
            if (createOrderView != null && createOrderView.getLogistics() != null) {
                chargeDeliveryInfoView.setDeliveryOrderNo(createOrderView.getLogistics().getLogisticsNo());
            }

            if (chargeAirPharmacyLogistics.getDeliveryCompany() != null) {
                ChargeDeliveryCompanyView chargeDeliveryCompanyVo = new ChargeDeliveryCompanyView();
                BeanUtils.copyProperties(chargeAirPharmacyLogistics.getDeliveryCompany(), chargeDeliveryCompanyVo);
                chargeDeliveryInfoView.setDeliveryCompany(chargeDeliveryCompanyVo);
            }

            outpatientForm.setDeliveryInfo(chargeDeliveryInfoView);

        }


        if (isLocalChinesePrescription && Objects.nonNull(chargeForm) && Objects.nonNull(chargeForm.getUsageInfoJson())) {

            ChargeFormItem processFormItem = Optional.ofNullable(processForm).map(ChargeForm::getChargeFormItems).orElse(new ArrayList<>())
                    .stream()
                    .filter(item -> item.getIsDeleted() == 0)
                    .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.PROCESS)
                    .filter(chargeFormItem -> chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.REFUNDED && chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.UNSELECTED)
                    .findFirst().orElse(null);

            if (Objects.nonNull(processFormItem)) {
                UsageInfo usageInfo = JsonUtils.readValue(chargeForm.getUsageInfoJson(), UsageInfo.class);
                if (Objects.nonNull(usageInfo)) {
                    String processUsage = usageInfo.getProcessUsage();
                    //药房计量小数调整
                    //int processBagUnitCount = usageInfo.getProcessBagUnitCount();
                    int processBagUnitCount = usageInfo.getProcessBagUnitCountDecimal().setScale(0, RoundingMode.CEILING).intValue();
                    String processUsageInfo = "";
                    if (processUsage != null && (processUsage.equals("机器煎药") || processUsage.equals("人工煎药") || processUsage.equals("手工煎药") || processUsage.equals("机器煎药（普通）") || processUsage.equals("机器煎药（浓缩）"))) {
                        processUsageInfo = processUsage;

                        if (processBagUnitCount > 0) {
                            processUsageInfo = String.format("%s，1剂%s袋", processUsageInfo, processBagUnitCount);
                        }
                    } else {
                        processUsageInfo = processUsage;
                    }

                    outpatientForm.setProcessUsageInfo(processUsageInfo);
                    outpatientForm.setContactMobile(usageInfo.getContactMobile());
                }

            }
        }

        //空中药房的代煎信息处理
        boolean isAirPharmacy = chargeForm.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY || (chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE && chargeForm.getPharmacyType() == GoodsConst.PharmacyType.VIRTUAL_PHARMACY);

        if (isAirPharmacy && org.apache.commons.lang3.StringUtils.isNotEmpty(chargeForm.getMedicineStateScopeId())
                && Constants.MedicineStateScopeId.PROCESS_USAGE_MAP.keySet().contains(Long.parseLong(chargeForm.getMedicineStateScopeId()))) {
            outpatientForm.setProcessUsageInfo(Constants.MedicineStateScopeId.PROCESS_USAGE_MAP.getOrDefault(Long.parseLong(chargeForm.getMedicineStateScopeId()), null));
            outpatientForm.setContactMobile(Optional.ofNullable(chargeForm.getChargeAirPharmacyLogistics()).map(ChargeAirPharmacyLogistics::getDeliveryMobile).orElse(""));
        }
    }

    //老的查询门诊状态的接口在用
    public static void appendDeliveryAndProcessInfo(QueryOutpatientSheetChargeStatusRsp.OutpatientForm outpatientForm, ChargeDeliveryInfo deliveryInfo, ChargeForm deliveryForm, ChargeForm chargeForm, ChargeForm processForm, boolean isLocalChinesePrescription) {

        if (Objects.isNull(outpatientForm)) {
            return;
        }

        if (Objects.nonNull(deliveryInfo)) {

            ChargeFormItem deliveryFormItem = Optional.ofNullable(deliveryForm).map(ChargeForm::getChargeFormItems).orElse(new ArrayList<>())
                    .stream()
                    .filter(item -> item.getIsDeleted() == 0)
                    .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.EXPRESS_DELIVERY)
                    .filter(chargeFormItem -> chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.REFUNDED && chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.UNSELECTED)
                    .findFirst().orElse(null);

            if (Objects.nonNull(deliveryFormItem)) {
                ChargeDeliveryInfoView chargeDeliveryInfoView = new ChargeDeliveryInfoView();
                BeanUtils.copyProperties(deliveryInfo, chargeDeliveryInfoView);

                if (deliveryInfo.getDeliveryCompany() != null) {
                    ChargeDeliveryCompanyView chargeDeliveryCompanyVo = new ChargeDeliveryCompanyView();
                    BeanUtils.copyProperties(deliveryInfo.getDeliveryCompany(), chargeDeliveryCompanyVo);
                    chargeDeliveryInfoView.setDeliveryCompany(chargeDeliveryCompanyVo);
                }
                outpatientForm.setDeliveryInfo(chargeDeliveryInfoView);
            }
        }

        if ((chargeForm.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY || chargeForm.getPharmacyType() == GoodsConst.PharmacyType.VIRTUAL_PHARMACY) && Objects.nonNull(chargeForm.getChargeAirPharmacyLogistics())) {
            ChargeAirPharmacyLogistics chargeAirPharmacyLogistics = chargeForm.getChargeAirPharmacyLogistics();
            ChargeDeliveryInfoView chargeDeliveryInfoView = new ChargeDeliveryInfoView();
            BeanUtils.copyProperties(chargeAirPharmacyLogistics, chargeDeliveryInfoView);

            if (chargeAirPharmacyLogistics.getDeliveryCompany() != null) {
                ChargeDeliveryCompanyView chargeDeliveryCompanyVo = new ChargeDeliveryCompanyView();
                BeanUtils.copyProperties(chargeAirPharmacyLogistics.getDeliveryCompany(), chargeDeliveryCompanyVo);
                chargeDeliveryInfoView.setDeliveryCompany(chargeDeliveryCompanyVo);
            }

            chargeDeliveryInfoView.setDeliveryPayType(Constants.DeliveryPayType.PAID_BY_SHIPPER);
            outpatientForm.setDeliveryInfo(chargeDeliveryInfoView);

        }


        if (isLocalChinesePrescription && Objects.nonNull(chargeForm) && Objects.nonNull(chargeForm.getUsageInfoJson())) {

            ChargeFormItem processFormItem = Optional.ofNullable(processForm).map(ChargeForm::getChargeFormItems).orElse(new ArrayList<>())
                    .stream()
                    .filter(item -> item.getIsDeleted() == 0)
                    .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.PROCESS)
                    .filter(chargeFormItem -> chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.REFUNDED && chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.UNSELECTED)
                    .findFirst().orElse(null);

            if (Objects.nonNull(processFormItem)) {
                UsageInfo usageInfo = JsonUtils.readValue(chargeForm.getUsageInfoJson(), UsageInfo.class);
                if (Objects.nonNull(usageInfo)) {
                    String processUsage = usageInfo.getProcessUsage();
                    //药房计量小数调整
                    // int processBagUnitCount = usageInfo.getProcessBagUnitCount();
                    int processBagUnitCount = usageInfo.getProcessBagUnitCountDecimal().setScale(0, RoundingMode.CEILING).intValue();
                    String processUsageInfo = "";
                    if (processUsage != null && (processUsage.equals("机器煎药") || processUsage.equals("人工煎药") || processUsage.equals("手工煎药") || processUsage.equals("机器煎药（普通）") || processUsage.equals("机器煎药（浓缩）"))) {
                        processUsageInfo = processUsage;

                        if (processBagUnitCount > 0) {
                            processUsageInfo = String.format("%s，1剂%s袋", processUsageInfo, processBagUnitCount);
                        }
                    } else {
                        processUsageInfo = processUsage;
                    }

                    outpatientForm.setProcessUsageInfo(processUsageInfo);
                    outpatientForm.setContactMobile(usageInfo.getContactMobile());
                }

            }
        }

        //空中药房的代煎信息处理
        boolean isAirPharmacy = chargeForm.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY || (chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE && chargeForm.getPharmacyType() == GoodsConst.PharmacyType.VIRTUAL_PHARMACY);

        if (isAirPharmacy && org.apache.commons.lang3.StringUtils.isNotEmpty(chargeForm.getMedicineStateScopeId())
                && Constants.MedicineStateScopeId.PROCESS_USAGE_MAP.keySet().contains(Long.parseLong(chargeForm.getMedicineStateScopeId()))) {
            outpatientForm.setProcessUsageInfo(Constants.MedicineStateScopeId.PROCESS_USAGE_MAP.getOrDefault(Long.parseLong(chargeForm.getMedicineStateScopeId()), null));
            outpatientForm.setContactMobile(Optional.ofNullable(chargeForm.getChargeAirPharmacyLogistics()).map(ChargeAirPharmacyLogistics::getDeliveryMobile).orElse(""));
        }
    }

    public static String generateHealthCardPayLevel(List<ChargeTransaction> chargeTransactions, Integer shebaoChargeType) {
        if (CollectionUtils.isEmpty(chargeTransactions)) {
            return Arrays.asList(PatientOrder.ShebaoChargeType.GENERAL_OUTPATIENT, PatientOrder.ShebaoChargeType.MANTEBING_OUTPATIENT).contains(shebaoChargeType)
                    ? translateShebaoChargeType2Name(shebaoChargeType)
                    : "自费";
        }
        boolean containHealthChargeTransaction = chargeTransactions.stream()
                .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                .anyMatch(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.HEALTH_CARD);
        return containHealthChargeTransaction ? translateShebaoChargeType2Name(shebaoChargeType) : "自费";
    }

    public static boolean isPatientSelfPay(List<ChargeTransaction> chargeTransactions) {

        if (org.apache.commons.collections.CollectionUtils.isEmpty(chargeTransactions)) {
            return false;
        }

        return chargeTransactions.stream()
                .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                .filter(chargeTransaction -> MathUtils.wrapBigDecimalOrZero(chargeTransaction.getAmount()).compareTo(BigDecimal.ZERO) > 0)
                .anyMatch(chargeTransaction -> Constants.ChargeSource.patientPaySources().contains(chargeTransaction.getPaySource()));
    }

    public static String translateShebaoChargeType2Name(Integer type) {
        String name = "医保";
        if (type == null) {
            return name;
        }
        if (type == PatientOrder.ShebaoChargeType.GENERAL_OUTPATIENT) {
            name = "医保(普通)";
        }
        if (type == PatientOrder.ShebaoChargeType.MANTEBING_OUTPATIENT) {
            name = "医保(慢特)";
        }
        return name;
    }

    public static boolean checkOweSheetCanPayForShebao(ChargeSheet chargeSheet) {

        if (Objects.isNull(chargeSheet)) {
            return false;
        }

        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.CHARGED) {
            log.info("chargeSheet status error, chargeSheetId: {}", chargeSheet.getId());
            return false;
        }

        //判断收费单是否为全部欠费
        boolean isAllOwePay = Optional.ofNullable(chargeSheet.getChargeTransactions()).orElse(new ArrayList<>())
                .stream()
                .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                .allMatch(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.OWE_PAY && MathUtils.wrapBigDecimalCompare(chargeTransaction.getAmount(), BigDecimal.ZERO) >= 0);

        if (!isAllOwePay) {
            log.info("chargeTransaction is not all owePay, chargeSheetId: {}", chargeSheet.getId());
            return false;
        }

        return true;
    }

    public static boolean checkOweSheetCanPayForShebao(ChargeSheetView chargeSheet) {

        if (Objects.isNull(chargeSheet)) {
            return false;
        }

        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.CHARGED) {
            log.info("chargeSheet status error, chargeSheetId: {}", chargeSheet.getId());
            return false;
        }

        //判断收费单是否为全部欠费
        boolean isAllOwePay = Optional.ofNullable(chargeSheet.getChargeTransactions()).orElse(new ArrayList<>())
                .stream()
                .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                .allMatch(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.OWE_PAY && MathUtils.wrapBigDecimalCompare(chargeTransaction.getAmount(), BigDecimal.ZERO) >= 0);

        if (!isAllOwePay) {
            log.info("chargeTransaction is not all owePay, chargeSheetId: {}", chargeSheet.getId());
            return false;
        }

        return true;
    }

    public static ChargeTransaction getLastChargeTransaction(List<ChargeTransaction> chargeTransactions) {

        if (org.apache.commons.collections.CollectionUtils.isEmpty(chargeTransactions)) {
            return null;
        }

        return chargeTransactions.stream()
                .max(Comparator.comparing(ChargeTransaction::getOperateNo))
                .orElse(null);
    }

    public static void doDeleteChargeSheet(ChargeSheet chargeSheet, ChargeService chargeService, TobMessageService tobMessageService, String operatorId) {
        if (Objects.isNull(chargeSheet)) {
            return;
        }

        Assert.notNull(chargeService, "chargeService is null");
        Assert.notNull(tobMessageService, "tobMessageService is null");

        ChargeUtils.deleteChargeSheet(chargeSheet, operatorId);
        chargeService.notifyChargeSheetStatusChanged(chargeSheet, operatorId, PatientOrderMessage.MSG_TYPE_CHARGE_DELETED);
        chargeService.doAfterChargeSheetChange(chargeSheet, Lists.newArrayList(), Lists.newArrayList(), Maps.newHashMap(), ChargeSheetMessage.MSG_TYPE_DELETE, null, null, operatorId);
        chargeService.goodsLockAndSaveBatchInfo(chargeSheet, GoodsLockScene.RELEASE_LOCK, false, operatorId);
        chargeService.unUseTraceableCode(chargeSheet,operatorId);
        chargeService.notifyChargeSheetSheetTriggerMessage(chargeSheet.getClinicId(), chargeSheet.getPatientOrderId());
        tobMessageService.pushChargeTodoMessage(chargeSheet.getChainId(), chargeSheet.getClinicId(), chargeSheet.getType());
    }

    public static List<OnceRefundCallBackReq> generateOnceRefundCallbackReqs(ChargeAction chargeAction,
                                                                             ChargePayTransaction chargePayTransaction,
                                                                             ChargeRefundSheet chargeRefundSheet) {
        if (chargeAction == null || org.apache.commons.collections.CollectionUtils.isEmpty(chargeAction.getPayActionInfos())) {
            return new ArrayList<>();
        }

        List<ChargeAction.PayActionInfo> payActionInfos = chargeAction.getPayActionInfos();

        List<OnceRefundCallBackReq> onceRefundCallBackReqs = payActionInfos.stream()
                .map(payActionInfo -> {
                    OnceRefundCallBackReq onceRefundCallBackReq = new OnceRefundCallBackReq();
                    onceRefundCallBackReq.setPayMode(payActionInfo.getPayMode())
                            .setPaySubMode(payActionInfo.getPaySubMode())
                            .setRefundFee(payActionInfo.getAmount().negate())
                            .setPaySource(chargePayTransaction.getPaySource())
                            .setThirdPartyPayInfo(chargePayTransaction.getPayInfo());

                    //如果是诊间支付，设置第三方支付id冲payActionInfo上取
                    if (chargePayTransaction.getPayMode() == Constants.ChargePayMode.OUTPATIENT_CENTER_PAY) {
                        onceRefundCallBackReq.setAssociatePayTransactionId(payActionInfo.getThirdPartyPayTransactionId());
                    } else {
                        onceRefundCallBackReq.setAssociatePayTransactionId(chargePayTransaction.getAssociatePayTransactionId());
                    }

                    return onceRefundCallBackReq;
                }).collect(Collectors.toList());

        //chargeRefundSheet放在第一次退费上
        onceRefundCallBackReqs.get(0).setChargeRefundSheet(chargeRefundSheet);
        return onceRefundCallBackReqs;
    }


    public static List<CardPatientPresentsDeductReq.Deduct> getDeductsNodeuct(ChargeSheet chargeSheet) {
        List<CardPatientPresentsDeductReq.Deduct> deducts = chargeSheet.getPatientCardPromotionInfos().stream()
                .filter(patientCardPromotionInfo -> patientCardPromotionInfo.getIsDeleted() == 0)
                .filter(ChargePatientCardPromotionInfo::getChecked)
                .filter(patientCardPromotionInfo -> org.apache.commons.collections.CollectionUtils.isNotEmpty(patientCardPromotionInfo.getDeductItems()))
                .map(patientCardPromotionInfo -> {

                    CardPatientPresentsDeductReq.Deduct deduct = new CardPatientPresentsDeductReq.Deduct();
                    deduct.setPatientCardId(patientCardPromotionInfo.getPromotionId());
                    deduct.setDeductPresentList(patientCardPromotionInfo.getDeductItems().stream()
                            .filter(promotionDeductItem -> promotionDeductItem.getChecked())
                            .filter(promotionDeductItem -> promotionDeductItem.getStatus() == PromotionDeductItem.Status.NOT_DEDUCT)
                            .peek(promotionDeductItem -> promotionDeductItem.setStatus(PromotionDeductItem.Status.DEDUCTED))
                            .map(promotionDeductItem -> {
                                CardPatientPresentsDeductReq.Deduct.Present present = new CardPatientPresentsDeductReq.Deduct.Present();
                                present.setPresentId(promotionDeductItem.getId());
                                present.setDeductCount(promotionDeductItem.getCheckedCount());
                                present.setGoodsId(promotionDeductItem.getGoodsId());
                                present.setGoodsType(promotionDeductItem.getGoodsType());
                                present.setGoodsSubType(promotionDeductItem.getGoodsSubType());
                                present.setGoodsCMSpec(promotionDeductItem.getGoodsCMSpec());
                                return present;
                            }).collect(Collectors.toList()));

                    return deduct;
                })
                .filter(deduct -> org.apache.commons.collections.CollectionUtils.isNotEmpty(deduct.getDeductPresentList()))
                .collect(Collectors.toList());

        return deducts;
    }


    public static boolean hasDeducts(ChargeSheet chargeSheet) {
        return chargeSheet.getPatientCardPromotionInfos().stream()
                .filter(patientCardPromotionInfo -> patientCardPromotionInfo.getIsDeleted() == 0)
                .filter(ChargePatientCardPromotionInfo::getChecked)
                .filter(patientCardPromotionInfo -> org.apache.commons.collections.CollectionUtils.isNotEmpty(patientCardPromotionInfo.getDeductItems()))
                .flatMap(patientCardPromotionInfo -> patientCardPromotionInfo.getDeductItems().stream())
                .anyMatch(promotionDeductItem -> promotionDeductItem.getChecked());
    }

    public static boolean itemExistedLockId(ChargeSheet chargeSheet) {
        return getChargeSheetItems(chargeSheet)
                .stream()
                .anyMatch(item -> org.apache.commons.lang3.StringUtils.isNotEmpty(item.getLockId()));
    }

    /**
     * 是否包含进价加成的goods
     *
     * @param chargeSheet
     * @return
     */
    public static boolean itemExistedGoodsLockBatch(ChargeSheet chargeSheet) {

        return getChargeSheetItems(chargeSheet)
                .stream()
                .filter(item -> item.getProductInfo() != null)
                .anyMatch(ChargeUtils::itemGoodsLockBatch);
    }

    public static boolean itemGoodsLockBatch(ChargeFormItem chargeFormItem) {
        if (Objects.isNull(chargeFormItem) || Objects.isNull(chargeFormItem.getProductInfo())) {
            return false;
        }
        try {
            // 判断json里面是否有lockBatchOps这个字段
            if (chargeFormItem.getProductInfo().has("lockBatchOps")) {
                return chargeFormItem.getProductInfo().get("lockBatchOps").asInt() == LockConfig.LockBatchOps.BATCH;
            }
        } catch (Exception e) {

        }
        //快找里面 没有这个字段走老逻辑
        int priceType = 0;
        try {
            priceType = chargeFormItem.getProductInfo().get("priceType").asInt();
        } catch (Exception e) {

        }
        return GoodsConst.checkFlagOn(priceType, GoodsConst.PriceType.PKG_PRICE_MAKEUP);
    }

    public static BigDecimal calculateLimitFee(BigDecimal limitTotalPrice, BigDecimal receivable) {
        return MathUtils.wrapBigDecimalSubtract(limitTotalPrice, receivable);
    }

    public static String generatePayModeKey(int payMode, int paySubMode, int payModeType, String defaultKey) {
        if (payMode == Constants.ChargePayMode.ABC_PAY
                || (payMode == Constants.ChargePayMode.HEALTH_CARD && paySubMode == Constants.ChargePaySubMode.WECHAT_MOBILE_SHEBAO_INSURANCE)
                || (payMode == Constants.ChargePayMode.WECHAT_PAY && paySubMode == Constants.ChargePaySubMode.WECHAT_MOBILE_SHEBAO_CASH)
                || payMode == Constants.ChargePayMode.OUTPATIENT_CENTER_PAY
                || payModeType == Constants.ChargePayModeType.THIRD_PARTY_COMMON_PAY
        ) {
            return String.format("%d-%d", payMode, paySubMode);
        }

        return defaultKey;
    }

    public static void bindComposeChildren(ChargeSheet chargeSheet) {
        if (chargeSheet == null || chargeSheet.getChargeForms() == null) {
            return;
        }

        List<ChargeFormItem> chargeFormSubItems = getChargeSheetItems(chargeSheet)
                .stream()
                .filter(ChargeFormItem::isChildItem)
                .collect(Collectors.toList());

        Map<String, List<ChargeFormItem>> productFormSubItemMap = ListUtils.groupByKey(chargeFormSubItems, ChargeFormItem::getComposeParentFormItemId);

        getChargeSheetItems(chargeSheet)
                .stream()
                .filter(ChargeFormItem::isParentItem)
                .forEach(chargeFormItem -> {
                    List<ChargeFormItem> subItems = productFormSubItemMap.getOrDefault(chargeFormItem.getId(), null);
                    if (subItems != null) {
                        chargeFormItem.setComposeChildren(subItems);
                    }
                });

    }

    public static <T extends ChargeSheet> void loadChargeSheetProductInfo(T sheet, CisScGoodsService cisScGoodsService) {
        if (sheet == null || sheet.getChargeForms() == null) {
            return;
        }

        sheet.getChargeForms()
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .forEach(chargeForm -> chargeForm.setUsageInfo(JsonUtils.loadAsJsonNode(chargeForm.getUsageInfoJson())));

        Set<String> productIds = new HashSet<>();

        sheet.getChargeForms()
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getChargeFormItems() != null)
                .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                .filter(Objects::nonNull)
                .filter(item -> item.getIsDeleted() == 0)
                .forEach(chargeFormItem -> {
                    if (chargeFormItem.getProductSnapshot() != null) {
                        chargeFormItem.setProductInfo(JsonUtils.loadAsJsonNode(chargeFormItem.getProductSnapshot()));
                    }

                    if (chargeFormItem.getUsageInfoJson() != null) {
                        chargeFormItem.setUsageInfo(JsonUtils.loadAsJsonNode(chargeFormItem.getUsageInfoJson()));
                    }

                    if (StringUtils.isEmpty(chargeFormItem.getProductSnapshot()) && !StringUtils.isEmpty(chargeFormItem.getProductId())) {
                        productIds.add(chargeFormItem.getProductId());
                    }

                });

        if (CollectionUtils.isEmpty(productIds)) {
            return;
        }

        List<QueryGoodsInPharmacyByIdsReq.QueryPharmacyGoodsReq> pharmacyGoodsReqs = sheet.getChargeForms().stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getChargeFormItems() != null)
                .filter(chargeForm -> chargeForm.getSourceFormType() != Constants.SourceFormType.AIR_PHARMACY)
                .map(chargeForm -> {
                    QueryGoodsInPharmacyByIdsReq.QueryPharmacyGoodsReq goodsReq = new QueryGoodsInPharmacyByIdsReq.QueryPharmacyGoodsReq();
                    goodsReq.setPharmacyNo(chargeForm.getPharmacyNo());
                    Set<String> goodsIds = chargeForm.getChargeFormItems().stream()
                            .filter(item -> item.getIsDeleted() == 0)
                            .filter(chargeFormItem -> StringUtils.isEmpty(chargeFormItem.getProductSnapshot()) && !StringUtils.isEmpty(chargeFormItem.getProductId()))
                            .map(ChargeFormItem::getProductId)
                            .collect(Collectors.toSet());
                    goodsReq.setGoodsIds(new ArrayList<>(goodsIds));
                    return goodsReq;
                })
                .filter(queryPharmacyGoodsReq -> org.apache.commons.collections.CollectionUtils.isNotEmpty(queryPharmacyGoodsReq.getGoodsIds()))
                .collect(Collectors.toMap(QueryGoodsInPharmacyByIdsReq.QueryPharmacyGoodsReq::getPharmacyNo, Function.identity(), (a, b) -> {
                    a.getGoodsIds().addAll(b.getGoodsIds());
                    a.setGoodsIds(a.getGoodsIds().stream().distinct().collect(Collectors.toList()));
                    return a;
                })).values().stream().collect(Collectors.toList());


        List<QueryGoodsInPharmacyByIdsRsp.QueryPharmacyGoodsRsp> queryPharmacyGoodsRsps = cisScGoodsService.queryGoodsInPharmacyByIds(sheet.getClinicId(), sheet.getChainId(), true, 1, pharmacyGoodsReqs);

        if (CollectionUtils.isEmpty(queryPharmacyGoodsRsps)) {
            return;
        }

        Map<Integer, QueryGoodsInPharmacyByIdsRsp.QueryPharmacyGoodsRsp> pharmacyGoodsRspMap = ListUtils.toMap(queryPharmacyGoodsRsps, QueryGoodsInPharmacyByIdsRsp.QueryPharmacyGoodsRsp::getPharmacyNo);

        sheet.getChargeForms()
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getChargeFormItems() != null)
                .filter(chargeForm -> chargeForm.getSourceFormType() != Constants.SourceFormType.AIR_PHARMACY)
                .forEach(chargeForm -> {
                    QueryGoodsInPharmacyByIdsRsp.QueryPharmacyGoodsRsp pharmacyGoodsRsp = pharmacyGoodsRspMap.getOrDefault(chargeForm.getPharmacyNo(), null);

                    if (pharmacyGoodsRsp == null || org.apache.commons.collections.CollectionUtils.isEmpty(pharmacyGoodsRsp.getList())) {
                        return;
                    }
                    Map<String, GoodsItem> goodsItemMap = ListUtils.toMap(pharmacyGoodsRsp.getList(), GoodsItem::getId);
                    chargeForm.getChargeFormItems().stream()
                            .filter(item -> item.getIsDeleted() == 0)
                            .filter(chargeFormItem -> !Constants.ProductType.SYSTEM_FEE_PRODUCT_TYPE.contains(chargeFormItem.getProductType()) && chargeFormItem.getComposeType() == ComposeType.NOT_COMPOSE)
                            .filter(chargeFormItem -> !StringUtils.isEmpty(chargeFormItem.getProductId()))
                            .forEach(chargeFormItem -> {
                                GoodsItem goodsItem = goodsItemMap.getOrDefault(chargeFormItem.getProductId(), null);
                                if (goodsItem != null) {
                                    chargeFormItem.setProductInfo(JsonUtils.dumpAsJsonNode(goodsItem));
                                }
                            });
                });
    }

    public static void markChargeSheetGoodsLockFlag(ChargeSheet chargeSheet, String operatorId) {
        insertChargeSheetAdditionalIfNeed(chargeSheet, operatorId);
        ChargeSheetAdditionalExtendInfo extendInfo = Optional.ofNullable(chargeSheet.getAdditional().getExtendedInfo()).orElse(new ChargeSheetAdditionalExtendInfo());

        extendInfo.setGoodsLockFlag(ChargeSheetAdditionalExtendInfo.GoodsLockFlag.LOCKED);
        chargeSheet.getAdditional().setExtendedInfo(extendInfo);
    }

    public static void updateChargeFormItemViewTraceCode(List<ChargeFormItemView> chargeFormItemViewList) {
        if (CollectionUtils.isEmpty(chargeFormItemViewList)) {
            return;
        }
        for (ChargeFormItemView formItemView : chargeFormItemViewList) {
            if (CollectionUtils.isEmpty(formItemView.getTraceableCodeList())) {
                continue;
            }
            TraceCodeUtils.updateTraceCodeDrugIdentificationCode(formItemView.getTraceableCodeList());
        }
    }

    public static List<BasicCreateChargeSheetRsp.ChargeFormRsp> transChargeSheetFormRsp(List<ChargeForm> chargeForms) {
        if (CollectionUtils.isEmpty(chargeForms)) {
            return null;
        }
        List<BasicCreateChargeSheetRsp.ChargeFormRsp> chargeFormRsps = new ArrayList<>();
        for (ChargeForm chargeForm : chargeForms) {
            BasicCreateChargeSheetRsp.ChargeFormRsp chargeFormRsp = new BasicCreateChargeSheetRsp.ChargeFormRsp();
            chargeFormRsp.setId(chargeForm.getId());
            chargeFormRsp.setKeyId(chargeForm.getKeyId());
            chargeFormRsp.setChargeFormItems(generateChargeFormItemRsp(chargeForm.getChargeFormItems()));
            chargeFormRsps.add(chargeFormRsp);
        }
        return chargeFormRsps;
    }

    private static List<BasicCreateChargeSheetRsp.ChargeFormItemRsp> generateChargeFormItemRsp(List<ChargeFormItem> chargeFormItems) {
        if (CollectionUtils.isEmpty(chargeFormItems)) {
            return null;
        }
        List<BasicCreateChargeSheetRsp.ChargeFormItemRsp> chargeFormItemRsps = new ArrayList<>();
        for (ChargeFormItem chargeFormItem : chargeFormItems) {
            BasicCreateChargeSheetRsp.ChargeFormItemRsp chargeFormItemRsp = new BasicCreateChargeSheetRsp.ChargeFormItemRsp();
            chargeFormItemRsp.setId(chargeFormItem.getId());
            chargeFormItemRsp.setKeyId(chargeFormItem.getKeyId());
            chargeFormItemRsp.setComposeChildren(generateChargeFormItemRsp(chargeFormItem.getComposeChildren()));
            chargeFormItemRsps.add(chargeFormItemRsp);
        }
        return chargeFormItemRsps;
    }

    /**
     * 患者信息转换
     */
    public static PatientInfo convertPatientInfo(PatientBasicInfoView view) {
        PatientInfo patient = new PatientInfo();
        patient.setId(view.getId());
        patient.setChainId(view.getChainId());
        patient.setName(view.getName());
        patient.setNamePy(view.getNamePy());
        patient.setNamePyFirst(view.getNamePyFirst());
        patient.setMobile(view.getMobile());
        patient.setCountryCode(view.getCountryCode());
        patient.setSex(view.getSex());
        patient.setBirthday(view.getBirthday());
        cn.abcyun.bis.rpc.sdk.cis.model.patient.CisPatientAge age = view.getAge();
        if (Objects.nonNull(age)) {
            CisPatientAge patientAge = new CisPatientAge();
            patientAge.setYear(age.getYear());
            patientAge.setMonth(age.getMonth());
            patientAge.setDay(age.getDay());
            patient.setAge(patientAge);
        }
        patient.setIsMember(view.getIsMember());
        patient.setIdCard(view.getIdCard());
        return patient;
    }

    public static ChargeSheetRelationDto removeDeleteData(ChargeSheet chargeSheet) {

        if (chargeSheet == null) {
            return null;
        }

        ChargeSheetRelationDto chargeSheetRelationDto = collectChargeSheetDeletedData(chargeSheet);

        cn.abcyun.cis.commons.util.ListUtils.alwaysList(chargeSheet.getAdditionalFees())
                .removeIf(chargeAdditionalFee -> chargeAdditionalFee.getIsDeleted() == 1);

        cn.abcyun.cis.commons.util.ListUtils.alwaysList(chargeSheet.getAttachments())
                .removeIf(chargeAttachment -> chargeAttachment.getIsDeleted() == 1);

        cn.abcyun.cis.commons.util.ListUtils.alwaysList(chargeSheet.getCouponPromotionInfos())
                .removeIf(chargeCouponPromotionInfo -> chargeCouponPromotionInfo.getIsDeleted() == 1);

        cn.abcyun.cis.commons.util.ListUtils.alwaysList(chargeSheet.getGiftRulePromotionInfos())
                .removeIf(chargeGiftRulePromotionInfo -> chargeGiftRulePromotionInfo.getIsDeleted() == 1);

        cn.abcyun.cis.commons.util.ListUtils.alwaysList(chargeSheet.getPatientCardPromotionInfos())
                .removeIf(chargePatientCardPromotionInfo -> chargePatientCardPromotionInfo.getIsDeleted() == 1);

        cn.abcyun.cis.commons.util.ListUtils.alwaysList(chargeSheet.getChargeVerifyInfos())
                .removeIf(chargeVerifyInfo -> chargeVerifyInfo.getIsDeleted() == 1);

        Optional.ofNullable(chargeSheet.getAdditional())
                .filter(a -> a.getIsDeleted() == 1)
                .ifPresent(a -> chargeSheet.setAdditional(null));

        Optional.ofNullable(chargeSheet.getPatientPointsPromotionInfo())
                .filter(a -> a.getIsDeleted() == 1)
                .ifPresent(a -> chargeSheet.setPatientPointsPromotionInfo(null));

        Optional.ofNullable(chargeSheet.getPatientPointsDeductProductPromotionInfo())
                .filter(a -> a.getIsDeleted() == 1)
                .ifPresent(a -> chargeSheet.setPatientPointsDeductProductPromotionInfo(null));

        Optional.ofNullable(chargeSheet.getDeliveryInfo())
                .filter(a -> a.getIsDeleted() == 1)
                .ifPresent(a -> chargeSheet.setDeliveryInfo(null));

        Optional.ofNullable(chargeSheet.getDbDeliveryInfo())
                .filter(a -> a.getIsDeleted() == 1)
                .ifPresent(a -> chargeSheet.setDbDeliveryInfo(null));

        cn.abcyun.cis.commons.util.ListUtils.alwaysList(chargeSheet.getChargeForms()).removeIf(chargeForm -> chargeForm.getIsDeleted() == 1);

        cn.abcyun.cis.commons.util.ListUtils.alwaysList(chargeSheet.getChargeForms())
                .forEach(chargeForm -> {

                    if (chargeForm.getChargeAirPharmacyMedicalRecord() != null && chargeForm.getChargeAirPharmacyMedicalRecord().getIsDeleted() == 1) {
                        chargeForm.setChargeAirPharmacyMedicalRecord(null);
                    }

                    if (chargeForm.getChargeAirPharmacyLogistics() != null && chargeForm.getChargeAirPharmacyLogistics().getIsDeleted() == 1) {
                        chargeForm.setChargeAirPharmacyLogistics(null);
                    }

                    if (chargeForm.getDbChargeAirPharmacyLogistics() != null && chargeForm.getDbChargeAirPharmacyLogistics().getIsDeleted() == 1) {
                        chargeForm.setDbChargeAirPharmacyLogistics(null);
                    }

                    if (chargeForm.getProcessInfo() != null && chargeForm.getProcessInfo().getIsDeleted() == 1) {
                        chargeForm.setProcessInfo(null);
                    }

                    cn.abcyun.cis.commons.util.ListUtils.alwaysList(chargeForm.getChargeFormItems()).removeIf(chargeFormItem -> chargeFormItem.getIsDeleted() == 1);


                    cn.abcyun.cis.commons.util.ListUtils.alwaysList(chargeForm.getChargeFormItems())
                            .forEach(chargeFormItem -> {
                                cn.abcyun.cis.commons.util.ListUtils.alwaysList(chargeFormItem.getChargeFormItemBatchInfos())
                                        .removeIf(chargeFormItemBatchInfo -> chargeFormItemBatchInfo.getIsDeleted() == 1);
                            });
                });

        chargeSheet.addDeletedData(chargeSheetRelationDto);
        return chargeSheetRelationDto;
    }

    public static ChargeSheetRelationDto collectChargeSheetDeletedData(ChargeSheet chargeSheet) {
        if (chargeSheet == null) {
            return null;
        }

        ChargeSheetRelationDto toDeleteChargeSheetRelationDto = new ChargeSheetRelationDto();

        addChargeSheetRelationData(chargeSheet, toDeleteChargeSheetRelationDto, true);

        return toDeleteChargeSheetRelationDto;
    }

    public static void addChargeSheetRelationData(ChargeSheet chargeSheet, ChargeSheetRelationDto chargeSheetRelationDto, Boolean onlyNeedDeleted) {

        if (Objects.isNull(chargeSheet) || Objects.isNull(chargeSheetRelationDto)) {
            return;
        }

        Predicate<Integer> filterPredicate = isDeleted -> !Objects.equals(onlyNeedDeleted, true) || Objects.equals(1, isDeleted);

        cn.abcyun.cis.commons.util.ListUtils.alwaysList(chargeSheet.getAdditionalFees())
                .stream()
                .filter(chargeAdditionalFee -> filterPredicate.test(chargeAdditionalFee.getIsDeleted()))
                .forEach(chargeSheetRelationDto::addChargeAdditionalFee);

        cn.abcyun.cis.commons.util.ListUtils.alwaysList(chargeSheet.getAttachments())
                .stream()
                .filter(chargeAttachment -> filterPredicate.test(chargeAttachment.getIsDeleted()))
                .forEach(chargeSheetRelationDto::addChargeAttachment);

        cn.abcyun.cis.commons.util.ListUtils.alwaysList(chargeSheet.getCouponPromotionInfos())
                .stream()
                .filter(chargeCouponPromotionInfo -> filterPredicate.test(chargeCouponPromotionInfo.getIsDeleted()))
                .forEach(chargeSheetRelationDto::addChargeCouponPromotionInfo);

        cn.abcyun.cis.commons.util.ListUtils.alwaysList(chargeSheet.getGiftRulePromotionInfos())
                .stream()
                .filter(chargeGiftRulePromotionInfo -> filterPredicate.test(chargeGiftRulePromotionInfo.getIsDeleted()))
                .forEach(chargeSheetRelationDto::addChargeGiftRulePromotionInfo);

        cn.abcyun.cis.commons.util.ListUtils.alwaysList(chargeSheet.getPatientCardPromotionInfos())
                .stream()
                .filter(chargePatientCardPromotionInfo -> filterPredicate.test(chargePatientCardPromotionInfo.getIsDeleted()))
                .forEach(chargeSheetRelationDto::addChargePatientCardPromotionInfo);

        cn.abcyun.cis.commons.util.ListUtils.alwaysList(chargeSheet.getChargeVerifyInfos())
                .stream()
                .filter(chargeVerifyInfo -> filterPredicate.test(chargeVerifyInfo.getIsDeleted()))
                .forEach(chargeSheetRelationDto::addChargeVerifyInfo);

        if (Objects.nonNull(chargeSheet.getAdditional()) && filterPredicate.test(chargeSheet.getAdditional().getIsDeleted())) {
            chargeSheetRelationDto.addAdditional(chargeSheet.getAdditional());
        }

        if (Objects.nonNull(chargeSheet.getPatientPointsPromotionInfo()) && filterPredicate.test(chargeSheet.getPatientPointsPromotionInfo().getIsDeleted())) {
            chargeSheetRelationDto.addPatientPointsPromotionInfo(chargeSheet.getPatientPointsPromotionInfo());
        }

        if (Objects.nonNull(chargeSheet.getPatientPointsDeductProductPromotionInfo()) && filterPredicate.test(chargeSheet.getPatientPointsDeductProductPromotionInfo().getIsDeleted())) {
            chargeSheetRelationDto.addPatientPointsDeductProductPromotionInfo(chargeSheet.getPatientPointsDeductProductPromotionInfo());
        }

        if (Objects.nonNull(chargeSheet.getDeliveryInfo()) && filterPredicate.test(chargeSheet.getDeliveryInfo().getIsDeleted())) {
            chargeSheetRelationDto.addDeliveryInfo(chargeSheet.getDeliveryInfo());
        }

        cn.abcyun.cis.commons.util.ListUtils.alwaysList(chargeSheet.getChargeForms())
                .forEach(chargeForm -> chargeSheetRelationDto.addChargeFormAndFormRelationData(chargeForm, filterPredicate));
    }

    public static Long getFeeTypeIdOrDefaultByGoodsTypeId(int goodsType, int goodsSubType, String goodsCmSpec, Integer goodsTypeId, Long feeTypeId, Map<String, GoodsSystemType> goodsTypeKeyToGoodsTypeMap) {

        if (feeTypeId != null && feeTypeId != 0) {
            return feeTypeId;
        }
        goodsTypeId = getGoodsTypeIdOrDefault(goodsType, goodsSubType, goodsCmSpec, goodsTypeId, goodsTypeKeyToGoodsTypeMap);

        return Objects.nonNull(goodsTypeId) ? GoodsFeeTypeConstants.defaultFeeTypeIdByGoodsTypeId(goodsTypeId) : null;
    }

    private static Integer getGoodsTypeIdOrDefault(int goodsType, int goodsSubType, String goodsCmSpec, Integer goodsTypeId, Map<String, GoodsSystemType> goodsTypeKeyToGoodsTypeMap) {
        if (Objects.nonNull(goodsTypeId)) {
            return goodsTypeId;
        }
        return GoodsFeeTypeConstants.convertGoodsTypeIdByGoodsTypeAndSubType(goodsType, goodsSubType, goodsCmSpec, goodsTypeKeyToGoodsTypeMap);
    }

    /**
     * 将ChargeSheet中chargeForms中的chargeFormItem集合对象中的status字段修改为3
     *
     * @param chargeSheet 收费单
     * @param operatorId  操作者ID
     */
    public static void updateChargeFormItemsStatusToUnselected(ChargeSheet chargeSheet, String operatorId) {
        if (chargeSheet == null || CollectionUtils.isEmpty(chargeSheet.getChargeForms())) {
            return;
        }

        chargeSheet.getChargeForms().forEach(chargeForm -> {
            if (chargeForm != null && !CollectionUtils.isEmpty(chargeForm.getChargeFormItems())) {
                chargeForm.getChargeFormItems().forEach(chargeFormItem -> {
                    if (chargeFormItem != null) {
                        chargeFormItem.setStatus(Constants.ChargeFormItemStatus.UNCHARGED);
                        FillUtils.fillLastModifiedBy(chargeFormItem, operatorId);
                    }
                });
            }
        });
    }

    public static void checkTraceCodeNoLength(List<TraceableCode> traceableCodeList) {
        if (CollectionUtils.isEmpty(traceableCodeList)) {
            return;
        }
        traceableCodeList.forEach(it -> {
            if (org.apache.commons.lang3.StringUtils.isEmpty(it.getNo())) {
                return;
            }
            if (it.getNo().length() > 128) {
                throw new ChargeServiceException(ChargeServiceError.NEED_REQUIRED_PARAMETER.getCode(),
                        "追溯码" + it.getNo() + "错误");
            }
        });
    }

    public static void updateConcatMobile(ChargeSheet chargeSheet, String contactMobile) {
        if (chargeSheet == null || chargeSheet.getAdditional() == null) {
            return;
        }
        chargeSheet.getAdditional().setContactMobile(contactMobile);
    }


    public static Integer transWeChatPayModeAndPaySubMode(Integer wechatPaySubMode, Integer chargePaySubMode) {

        if (wechatPaySubMode == null) {
            return 0;
        }

        int paySubMode = Constants.ABC_PAY_SUB_TYPE_MAP.getOrDefault(wechatPaySubMode, 0);

        if (paySubMode == Constants.ChargePaySubMode.ALLIN_PAY_SCAN_ALI && chargePaySubMode == Constants.ChargePaySubMode.ABC_PAY_SCAN_ONE_QR_CODE) {
            paySubMode = Constants.ChargePaySubMode.ABC_PAY_SCAN_ONE_QR_CODE;
        }

        return paySubMode;
    }

    public static ChargeSheet copyChargeSheetForDirectSale(ChargeSheet chargeSheet, Map<String, String> oldItemIdToNewItemId, int hisType) {

        ChargeSheet copiedChargeSheet = new ChargeSheet();
        String copiedChargeSheetId = AbcIdUtils.getUID();

        copiedChargeSheet.setId(copiedChargeSheetId);
        copiedChargeSheet.setPatientId(chargeSheet.getPatientId());
        copiedChargeSheet.setChainId(chargeSheet.getChainId());
        copiedChargeSheet.setClinicId(chargeSheet.getClinicId());
        copiedChargeSheet.setMemberId(chargeSheet.getMemberId());
        copiedChargeSheet.setSellerId(chargeSheet.getSellerId());
        copiedChargeSheet.setSellerDepartmentId(chargeSheet.getSellerDepartmentId());
        copiedChargeSheet.setDoctorId(chargeSheet.getDoctorId());
        copiedChargeSheet.setCopywriterId(chargeSheet.getCopywriterId());
        copiedChargeSheet.setDraftAdjustmentFee(chargeSheet.getDraftAdjustmentFee());
        copiedChargeSheet.setType(ChargeSheet.Type.DIRECT_SALE);
        copiedChargeSheet.setAdditional(
                ChargeSheetAdditionalUtils.copyForDirectSale(chargeSheet.getAdditional(), copiedChargeSheetId, hisType)
        );
        copiedChargeSheet.setChargeForms(
                Optional.ofNullable(chargeSheet.getChargeForms())
                        .orElse(Lists.newArrayList())
                        .stream()
                        .filter(chargeForm -> !Objects.equals(chargeForm.getSourceFormType(), Constants.SourceFormType.SINGLE_PROMOTION_GIFT_PRODUCT)
                                && !Objects.equals(chargeForm.getSourceFormType(), Constants.SourceFormType.GIFT_PRODUCT)
                        )
                        .map(chargeForm -> ChargeFormUtils.copyForDirectSale(chargeForm, copiedChargeSheetId, oldItemIdToNewItemId))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList())
        );
        copiedChargeSheet.setIsDraft(0);
        copiedChargeSheet.setIsClosed(0);
        copiedChargeSheet.setSourceId(chargeSheet.getId());

        return copiedChargeSheet;
    }

    public static void compareBatchIsChangedAndCleanTraceableCodes(ChargeSheetView chargeSheetView,
                                                                   ChargeSheet chargeSheet,
                                                                   Map<String, String> oldItemIdToNewItemId) {
        if (Objects.isNull(chargeSheetView)){
            return;
        }
        Map<String, ChargeFormItem> oldItemIdToOldItemMap = Optional.ofNullable(chargeSheet.getChargeForms())
                .orElse(Lists.newArrayList())
                .stream()
                .filter(chargeForm -> !CollectionUtils.isEmpty(chargeForm.getChargeFormItems()))
                .flatMap(chargeForm -> chargeForm.getChargeFormItems()
                        .stream()
                        .filter(chargeFormItem -> oldItemIdToNewItemId.containsKey(chargeFormItem.getId()))
                )
                .collect(Collectors.toMap(ChargeFormItem::getId, Function.identity()));

        Map<String, String> newItemIdToOldItemId = oldItemIdToNewItemId
                .entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey));

        // 比较新生成的chargeSheetView批次信息是否发生变更
        Optional.ofNullable(chargeSheetView.getChargeForms())
                .orElse(Lists.newArrayList())
                .stream()
                .flatMap(chargeFormView -> chargeFormView.getChargeFormItems().stream())
                .forEach(chargeFormItemView -> ChargeFormItemUtils.compareBatchIsChangedAndCleanTraceableCodes(
                        chargeFormItemView,
                        oldItemIdToOldItemMap,
                        newItemIdToOldItemId
                ));

    }

    public static boolean compareStrEqual(String left, String right) {
        boolean leftEmpty = left == null || org.springframework.util.StringUtils.isEmpty(left.trim());
        boolean rightEmpty = right == null || org.springframework.util.StringUtils.isEmpty(right.trim());
        if (leftEmpty && rightEmpty) {
            return true;
        }
        if (!leftEmpty && !rightEmpty) {
            return left.trim().compareTo(right.trim()) == 0;
        }
        return false;
    }

    public static Map<String, DispensingFormItem> createSourceFormItemIdToDispensingFormItemMap(List<DispensingSheet> dispensingSheets) {
        if (CollectionUtils.isEmpty(dispensingSheets)) {
            return new HashMap<>();
        }

        return dispensingSheets.stream()
                .flatMap(dispensingSheet -> createSourceFormItemIdToDispensingFormItemMap(dispensingSheet).entrySet().stream())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (existing, replacement) -> existing // 如果有重复的 sourceFormItemId，保留第一个
                ));
    }

    public static Map<String, DispensingFormItem> createSourceFormItemIdToDispensingFormItemMap(DispensingSheet dispensingSheet) {
        if (dispensingSheet == null || CollectionUtils.isEmpty(dispensingSheet.getDispensingForms())) {
            return new HashMap<>();
        }

        return dispensingSheet.getDispensingForms()
                .stream()
                .filter(dispensingForm -> dispensingForm.getDispensingFormItems() != null)
                .flatMap(dispensingForm -> dispensingForm.getDispensingFormItems().stream()
                        .map(dispensingFormItem -> {
                            List<DispensingFormItem> dispensingFormItems = new ArrayList<>();
                            if (dispensingFormItem.getComposeType() == ComposeType.COMPOSE) {
                                dispensingFormItems.add(dispensingFormItem);
                                if (!CollectionUtils.isEmpty(dispensingFormItem.getComposeChildren())) {
                                    dispensingFormItems.addAll(dispensingFormItem.getComposeChildren());
                                }
                            } else {
                                dispensingFormItems.add(dispensingFormItem);
                            }
                            return dispensingFormItems;
                        })
                        .flatMap(Collection::stream))
                .filter(Objects::nonNull)
                .filter(dispensingFormItem -> !StringUtils.isEmpty(dispensingFormItem.getSourceFormItemId()))
                .collect(Collectors.toMap(
                        DispensingFormItem::getSourceFormItemId,
                        Function.identity(),
                        (existing, replacement) -> existing // 如果有重复的 sourceFormItemId，保留第一个
                ));
    }

    /**
     * 判断产品线是否支持发票
     */
    public static boolean isSupportInvoiceForHisType(Integer hisType) {
        return Objects.nonNull(hisType) && Arrays.asList(Organ.HisType.CIS_HIS_TYPE_NORMAL, Organ.HisType.CIS_HIS_TYPE_HOSPITAL, Organ.HisType.CIS_HIS_TYPE_PHARMACY).contains(hisType);
    }

    /**
     * 判断部分退款是否支持打印
     */
    public static boolean isPartRefundSupportPrint(ChargeSheet chargeSheet, Function<Pair<String, List<String>>, List<ChargeTransactionRecord>> function) {
        if (CollectionUtils.isEmpty(chargeSheet.getChargeForms())) {
            return true;
        }
        // 生成退费项目map
        Map<String, List<ChargeFormItem>> refundChargeFormItemsMap = ChargeUtils.generateRefundChargeFormItemsMap(chargeSheet);
        if (CollectionUtils.isEmpty(refundChargeFormItemsMap)) {
            return true;
        }
        // 找到医保支付的item
        List<ChargeFormItem> healthCardPayFormItems = chargeSheet.getChargeForms().stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == YesOrNo.NO)
                .filter(form ->  Objects.nonNull(form.getChargeFormItems()))
                .flatMap(form -> form.getChargeFormItems().stream())
                .filter(chargeFormItem -> chargeFormItem.getIsDeleted() == YesOrNo.NO)
                .filter(chargeFormItem -> chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED)
                .filter(chargeFormItem -> Objects.nonNull(chargeFormItem.getPromotionInfo()) && chargeFormItem.getPromotionInfo().getIsMarkedByHealthCardPay() == YesOrNo.YES)
                .collect(Collectors.toList());
        // 如果没有刷过医保的项目,直接返回
        if (CollectionUtils.isEmpty(healthCardPayFormItems)) {
            return true;
        }
        List<ChargeTransaction> chargeTransactions = chargeSheet.getChargeTransactions();
        if(CollectionUtils.isEmpty(chargeTransactions)) {
            return true;
        }
        chargeTransactions = chargeTransactions.stream()
                .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == YesOrNo.NO)
                .collect(Collectors.toList());
        List<String> transactionIds = chargeTransactions.stream()
                .map(ChargeTransaction::getId)
                .filter(org.apache.commons.lang3.StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        List<ChargeTransactionRecord> transactionRecords = function.apply(Pair.of(chargeSheet.getClinicId(), transactionIds));
        if (CollectionUtils.isEmpty(transactionRecords)) {
            return true;
        }
        // 通过判断chargeFormItemId是否为空过滤掉空中药房
        transactionRecords = transactionRecords.stream()
                .filter(record -> org.apache.commons.lang3.StringUtils.isNotBlank(record.getChargeFormItemId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(transactionRecords)) {
            return true;
        }
        Map<String, ChargeTransaction> chargeTransactionMap = ListUtils.toMap(chargeTransactions, ChargeTransaction::getId);
        // 从流水判断是否存在非医保退费
        Map<String, List<ChargeTransactionRecord>> formItemIdTranRecordsMap = ListUtils.groupByKey(transactionRecords, ChargeTransactionRecord::getChargeFormItemId);
        for (ChargeFormItem healthFormItem : healthCardPayFormItems) {
            List<ChargeFormItem> refundChargeFormItems = refundChargeFormItemsMap.get(healthFormItem.getId());
            if (CollectionUtils.isEmpty(refundChargeFormItems)) {
                continue;
            }
            for (ChargeFormItem refundFormItem : refundChargeFormItems) {
                List<ChargeTransactionRecord> tranRecordList = formItemIdTranRecordsMap.get(refundFormItem.getId());
                if (CollectionUtils.isEmpty(tranRecordList)) {
                    continue;
                }
                boolean isNotShebaoRefund = tranRecordList.stream()
                        .anyMatch(chargeTransactionRecord -> chargeTransactionRecord.getChargeType() == ChargeTransactionRecord.ChargeType.REFUND
                                && chargeTransactionMap.get(chargeTransactionRecord.getTransactionId()).getPayMode() != Constants.ChargePayMode.HEALTH_CARD);
                if (isNotShebaoRefund) {
                    return false;
                }
            }
        }
        return true;
    }
}
