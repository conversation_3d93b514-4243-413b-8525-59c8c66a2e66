package cn.abcyun.cis.charge.util;

import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Function;

public class MergeListTool<S, D> {
    private Collection<S> src;
    private Collection<D> dst;
    private BiFunction<? super S, ? super D, Boolean> isEqualKeyFunc;
    private BiConsumer<S, D> updateFunc;
    private Function<S, List<D>> insertFunc;
    private Function<D, Boolean> deleteFunc;

    public MergeListTool setSrc(Collection<S> src) {
        this.src = src;
        return this;
    }

    public MergeListTool setDst(Collection<D> dst) {
        this.dst = dst;
        return this;
    }

    public MergeListTool setIsEqualKeyFunc(BiFunction<? super S, ? super D, Boolean> isEqualKeyFunc) {
        this.isEqualKeyFunc = isEqualKeyFunc;
        return this;
    }

    public MergeListTool setUpdateFunc(BiConsumer<S, D> updateFunc) {
        this.updateFunc = updateFunc;
        return this;
    }

    public MergeListTool setInsertFunc(Function<S, List<D>> insertFunc) {
        this.insertFunc = insertFunc;
        return this;
    }

    public MergeListTool setDeleteFunc(Function<D, Boolean> deleteFunc) {
        this.deleteFunc = deleteFunc;
        return this;
    }

    public static <S, D> void doMerge(Collection<S> src,
                                       Collection<D> dst,
                                       BiFunction<? super S, ? super D, Boolean> isEqualKeyFunc,
                                       Function<S, D> insertFunc,
                                       Function<D, Boolean> deleteFunc,
                                       BiConsumer<S, D> updateFunc) {
        MergeListTool<S, D> mergeTool = new MergeListTool<>();
        mergeTool.setSrc(src)
                .setDst(dst)
                .setIsEqualKeyFunc(isEqualKeyFunc)
                .setInsertFunc(insertFunc)
                .setDeleteFunc(deleteFunc)
                .setUpdateFunc(updateFunc);
        mergeTool.doMerge();
    }
    /**
     * 用 src 跟新 dst的规则
     * 1.src没有指定，那么对dst的每一个元素，执行deleteFunction，deleteFunction返回为true，删除该元素【deleteFunc的参数是dst的元素】
     * 2.dst没有指定，那么对src的每一个元素，执行insertFunc，不关注insertFunc的返回值，【insertFunc的参数是dst的元素】
     * 否认执行如下更新操作：
     * 3.1先把dst里面不再src里面的删掉；判断dst和src的元素是否相等，相等不删除。不相等执行dst上元素的deleteFunction，根据函数返回值决定是否删除dst上的这个元素
     * 3.2 dst和src都有的元素，执行updateFunc（source,dst）
     * 3.3 dst不存在的元素【src新元素】，在新元素上执行D insertFunc（s），把d插入到dst
     * */
    public void doMerge() {
        if (this.src == null && this.dst == null) {
            return;
        }

        if (this.src == null) {
            this.dst.removeIf(d -> deleteFunc.apply(d));
            return;
        }

        if (this.dst == null) {
            this.src.stream().forEach(s -> insertFunc.apply(s));
            return;
        }

        this.dst.removeIf(d -> {
            if (this.src.stream().anyMatch(s -> isEqualKeyFunc.apply(s, d))) {
                return false;
            }

            return deleteFunc.apply(d);
        });

        this.src.stream().forEach(s -> {
            D existedD = this.dst.stream().filter(d -> isEqualKeyFunc.apply(s, d)).findFirst().orElse(null);
            if (existedD != null) {
                updateFunc.accept(s, existedD);
            } else {
                List<D> d = insertFunc.apply(s);
                if (!CollectionUtils.isEmpty(d)) {
                    this.dst.addAll(d);
                }
            }
        });
    }
}
