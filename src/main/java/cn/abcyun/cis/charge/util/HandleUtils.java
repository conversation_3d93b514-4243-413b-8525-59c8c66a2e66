package cn.abcyun.cis.charge.util;


import java.util.Optional;
import java.util.function.Supplier;

public class HandleUtils {

    @FunctionalInterface
    public interface TrueOrFalseHandle {

        /**
         * 分支操作
         *
         * @param trueHandle  为true时要进行的操作
         * @param falseHandle 为false时要进行的操作
         * @return void
         **/
        void handle(Runnable trueHandle, Runnable falseHandle);

    }

    @FunctionalInterface
    public interface TrueOrFalseHandleAndReturn {

        /**
         * 分支操作
         *
         * @param trueHandle  为true时要进行的操作
         * @param falseHandle 为false时要进行的操作
         * @return void
         **/
        <T> T handleAndReturn(Supplier<T> trueHandle, Supplier<T> falseHandle);

    }

    @FunctionalInterface
    public interface TrueHandle {

        /**
         * 分支操作
         *
         * @param trueHandle 为true时要进行的操作
         * @return void
         **/
        void handle(Runnable trueHandle);

    }


    public static TrueHandle isTrue(boolean b) {
        return trueHandle -> {
            if (b) {
                Optional.ofNullable(trueHandle).ifPresent(Runnable::run);
            }
        };
    }


    public static TrueOrFalseHandle isTrueOrFalse(boolean b) {

        return (trueHandle, falseHandle) -> {
            if (b) {
                Optional.ofNullable(trueHandle).ifPresent(Runnable::run);
            } else {
                Optional.ofNullable(falseHandle).ifPresent(Runnable::run);
            }
        };
    }

    public static TrueOrFalseHandleAndReturn isTrueOrFalseReturn(boolean b) {

        return new TrueOrFalseHandleAndReturn() {
            @Override
            public <T> T handleAndReturn(Supplier<T> trueHandle, Supplier<T> falseHandle) {
                if (b) {
                    return trueHandle.get();
                } else {
                    return falseHandle.get();
                }
            }
        };
    }

}
