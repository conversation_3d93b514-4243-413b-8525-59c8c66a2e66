package cn.abcyun.cis.charge.util;

import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.combinorder.dto.CombineOrderTransactionRecordView;
import cn.abcyun.cis.charge.combinorder.dto.CombineOrderTransactionView;
import cn.abcyun.cis.charge.model.ChargeOweCombineTransaction;
import cn.abcyun.cis.charge.model.ChargeOweCombineTransactionRecord;
import cn.abcyun.cis.charge.model.ChargeOweSheet;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Objects;
import java.util.Optional;

@Slf4j
public class ChargeOweSheetUtils {

    public static ChargeOweCombineTransaction generateChargeOweCombineTransaction(String chainId, String clinicId, CombineOrderTransactionView combineOrderTransaction, int source, Long businessId, String chargeComment, String operatorId) {

        if (Objects.isNull(combineOrderTransaction)) {
            return null;
        }

        ChargeOweCombineTransaction oweCombineTransaction = new ChargeOweCombineTransaction();
        oweCombineTransaction.setId(AbcIdUtils.getUIDLong())
                .setChainId(chainId)
                .setClinicId(clinicId)
                .setCombineOrderId(combineOrderTransaction.getOrderId())
                .setCombineOrderTransactionId(combineOrderTransaction.getId())
                .setBusinessId(businessId)
                .setSource(source)
                .setAssociateTransactionId(null)
                .setPayMode(combineOrderTransaction.getPayMode())
                .setPaySubMode(combineOrderTransaction.getPaySubMode())
                .setPayModeDisplayName(combineOrderTransaction.getPayModeDisplayName())
                .setPayModeInfo(combineOrderTransaction.getPayModeInfo())
                .setType(combineOrderTransaction.getType())
                .setAmount(combineOrderTransaction.getAmount())
                .setRefundedAmount(BigDecimal.ZERO)
                .setPresentAmount(combineOrderTransaction.getPresentAmount())
                .setPrincipalAmount(combineOrderTransaction.getPrincipalAmount())
                .setChargeComment(chargeComment);


        Optional.ofNullable(combineOrderTransaction.getThirdPartyPayInfo()).ifPresent(payInfo ->
                oweCombineTransaction.setThirdPartyPayTransactionId(payInfo.getTransactionId())
                        .setThirdPartyPayCardBalance(payInfo.getCardBalance())
                        .setThirdPartyPayCardId(payInfo.getCardId())
                        .setThirdPartyPayOrderId(payInfo.getThirdPartyTransactionId())
                        .setThirdPartyPayInfo(payInfo)
        );

        if (oweCombineTransaction.getTransactionRecords() == null) {
            oweCombineTransaction.setTransactionRecords(new ArrayList<>());
        }

        FillUtils.fillCreatedBy(oweCombineTransaction, operatorId);

        return oweCombineTransaction;

    }

    public static ChargeOweCombineTransactionRecord insertChargeOweCombineTransactionRecord(ChargeOweSheet chargeOweSheet, ChargeOweCombineTransaction oweCombineTransaction, Long associateTransactionRecordId, CombineOrderTransactionRecordView recordView, BigDecimal needPay, String operatorId) {

        if (Objects.isNull(chargeOweSheet) || Objects.isNull(oweCombineTransaction) || Objects.isNull(recordView)) {
            return null;
        }
        ChargeOweCombineTransactionRecord transactionRecord = new ChargeOweCombineTransactionRecord();
        transactionRecord.setId(AbcIdUtils.getUIDLong())
                .setChainId(oweCombineTransaction.getChainId())
                .setClinicId(oweCombineTransaction.getClinicId())
                .setOweSheetId(chargeOweSheet.getId())
                .setOweCombineTransactionId(oweCombineTransaction.getId())
                .setCombineOrderId(oweCombineTransaction.getCombineOrderId())
                .setCombineOrderTransactionId(recordView.getOrderTransactionId())
                .setCombineOrderItemId(recordView.getOrderItemId())
                .setAssociateTransactionRecordId(associateTransactionRecordId)
                .setPayMode(oweCombineTransaction.getPayMode())
                .setPaySubMode(oweCombineTransaction.getPaySubMode())
                .setPayModeDisplayName(oweCombineTransaction.getPayModeDisplayName())
                .setType(oweCombineTransaction.getType())
                .setAmount(recordView.getAmount())
                .setNeedPay(needPay)
                .setRefundedAmount(BigDecimal.ZERO);

        if (oweCombineTransaction.getPayMode() == Constants.ChargePayMode.MEMBER_CARD) {
              transactionRecord.setPresentAmount(recordView.getPresentAmount())
                    .setPrincipalAmount(recordView.getPrincipalAmount());
        } else {
            transactionRecord.setPresentAmount(BigDecimal.ZERO)
                    .setPrincipalAmount(recordView.getAmount());
        }

        FillUtils.fillCreatedBy(transactionRecord, operatorId);

        if (oweCombineTransaction.getTransactionRecords() == null) {
            oweCombineTransaction.setTransactionRecords(new ArrayList<>());
        }

        oweCombineTransaction.getTransactionRecords().add(transactionRecord);
        return transactionRecord;
    }
}
