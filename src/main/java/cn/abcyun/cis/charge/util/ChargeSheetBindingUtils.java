package cn.abcyun.cis.charge.util;

import cn.abcyun.bis.rpc.sdk.bis.model.order.LogisticsCompanyView;
import cn.abcyun.bis.rpc.sdk.cis.model.common.BitFlagUtils;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.cis.charge.api.model.ChargeAirPharmacyLogisticsReq;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.model.*;
import cn.abcyun.cis.charge.repository.ChargeDeliveryCompanyRepository;
import cn.abcyun.cis.charge.repository.ChargeDeliveryTraceRepository;
import cn.abcyun.cis.charge.service.rpc.BisOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class ChargeSheetBindingUtils {

    public static boolean needQueryRelateData(Map<String, Integer> chargeSheetIdRelateDataFlagMap, int currentRelateDataFlag) {
        //先保障都查
        return true;
        //这个为空也保障去查一下，保障查询别少数据
//        if (MapUtils.isEmpty(chargeSheetIdRelateDataFlagMap)) {
//            return true;
//        }
//
//        return chargeSheetIdRelateDataFlagMap.entrySet().stream()
//                .anyMatch(entry -> {
//                    Integer sheetRelateDataFlag = entry.getValue();
//
//                    //如果为空，表示老数据，都去查一下，保障查询别少数据
//                    if (sheetRelateDataFlag == null) {
//                        return true;
//                    }
//
//                    return BitFlagUtils.checkFlagOn(sheetRelateDataFlag, currentRelateDataFlag);
//                });
    }

    public static <T> void fillChargeSheetFieldCore(List<ChargeSheet> chargeSheets,
                                                    Function<List<String>, Map<String, T>> queryIdMapFunction,
                                                    BiConsumer<ChargeSheet, T> setFieldConsumer) {

        if (CollectionUtils.isEmpty(chargeSheets)) {
            return;
        }

        List<String> chargeSheetIds = chargeSheets.stream()
                .map(ChargeSheet::getId)
                .collect(Collectors.toList());

        Map<String, T> chargeSheetIdMap = queryIdMapFunction.apply(chargeSheetIds);

        chargeSheets.forEach(chargeSheet -> setFieldConsumer.accept(chargeSheet, chargeSheetIdMap.getOrDefault(chargeSheet.getId(), null)));
    }

    public static <T> void fillChargeSheetListFieldCore(List<ChargeSheet> chargeSheets,
                                                 Function<List<String>, Map<String, List<T>>> queryFieldFunction,
                                                 BiConsumer<ChargeSheet, List<T>> setFieldConsumer) {

        if (CollectionUtils.isEmpty(chargeSheets)) {
            return;
        }

        List<String> chargeSheetIds = chargeSheets.stream()
                .map(ChargeSheet::getId)
                .collect(Collectors.toList());

        Map<String, List<T>> chargeSheetIdFieldMap = queryFieldFunction.apply(chargeSheetIds);

        chargeSheets.forEach(chargeSheet -> setFieldConsumer.accept(chargeSheet, chargeSheetIdFieldMap.getOrDefault(chargeSheet.getId(), new ArrayList<>())));
    }



    /**
     * 填充空中药房快递公司信息
     */
    public static void fillAirPharmacyLogisticsDeliveryCompany(List<ChargeAirPharmacyLogistics> airPharmacyLogisticsList, BisOrderService bisOrderService) {
        if (CollectionUtils.isNotEmpty(airPharmacyLogisticsList)) {
            List<String> allCompanyIds = airPharmacyLogisticsList.stream()
                    .map(ChargeAirPharmacyLogistics::getDeliveryCompanyId)
                    .filter(deliveryCompanyId -> !StringUtils.isEmpty(deliveryCompanyId))
                    .distinct()
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(allCompanyIds)) {

                //查询缓存中没有的快递公司信息
                List<LogisticsCompanyView> companyViews = bisOrderService.getBisCompanysInfo(allCompanyIds);
                Map<String, LogisticsCompanyView> airPharmacyCompanyMap = ListUtils.toMap(companyViews, companyViews1 -> companyViews1.getId() + "");

                airPharmacyLogisticsList.forEach(chargeAirPharmacyLogistics -> {
                    String deliveryCompanyId = chargeAirPharmacyLogistics.getDeliveryCompanyId();
                    if (!StringUtils.isEmpty(deliveryCompanyId)) {
                        LogisticsCompanyView deliveryCompany = airPharmacyCompanyMap.getOrDefault(deliveryCompanyId, null);
                        if (Objects.nonNull(deliveryCompany)) {
                            ChargeAirPharmacyLogisticsReq.LogisticsCompanyReq deliveryCompanyView = new ChargeAirPharmacyLogisticsReq.LogisticsCompanyReq();
                            deliveryCompanyView.setId(deliveryCompany.getId() + "");
                            deliveryCompanyView.setName(deliveryCompany.getName());
                            chargeAirPharmacyLogistics.setDeliveryCompany(deliveryCompanyView);
                        }
                    }
                });

            }
        }
    }

    public static void bindAirPharmacyForChargeSheetCore(ChargeForm chargeForm, Map<String, ChargeAirPharmacyLogistics> airPharmacyLogisticsMap, Map<String, ChargeAirPharmacyMedicalRecord> airPharmacyMedicalRecordMap) {

        //空中药房bind
        if (chargeForm.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY) {
            ChargeAirPharmacyLogistics airPharmacyLogistics = airPharmacyLogisticsMap.get(chargeForm.getId());
            if (airPharmacyLogistics != null) {
                chargeForm.setChargeAirPharmacyLogistics(airPharmacyLogistics);
            }
            ChargeAirPharmacyMedicalRecord airPharmacyMedicalRecord = airPharmacyMedicalRecordMap.get(chargeForm.getId());
            if (airPharmacyMedicalRecord != null) {
                chargeForm.setChargeAirPharmacyMedicalRecord(airPharmacyMedicalRecord);
            }
        }

        //虚拟药房bind
        if (chargeForm.getPharmacyType() == GoodsConst.PharmacyType.VIRTUAL_PHARMACY) {
            ChargeAirPharmacyLogistics airPharmacyLogistics = airPharmacyLogisticsMap.get(chargeForm.getId());
            if (airPharmacyLogistics != null) {
                chargeForm.setChargeAirPharmacyLogistics(airPharmacyLogistics);
            }
        }
    }

    /**
     * 填充虚拟药房快递公司信息
     */
    public static void fillVirtualPharmacyLogisticsDeliveryCompany(List<ChargeAirPharmacyLogistics> airPharmacyLogisticsList, String chainId, String clinicId, ChargeDeliveryCompanyRepository chargeDeliveryCompanyRepository) {
        if (CollectionUtils.isNotEmpty(airPharmacyLogisticsList)) {
            List<String> allCompanyIds = airPharmacyLogisticsList.stream()
                    .map(ChargeAirPharmacyLogistics::getDeliveryCompanyId)
                    .filter(deliveryCompanyId -> !StringUtils.isEmpty(deliveryCompanyId))
                    .distinct()
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(allCompanyIds)) {
                List<ChargeDeliveryCompany> deliveryCompanies = chargeDeliveryCompanyRepository.findAllByChainIdAndClinicIdAndIdInAndIsDeleted(chainId, clinicId, new ArrayList<>(allCompanyIds), 0);
                Map<String, ChargeDeliveryCompany> deliveryCompanyMap = ListUtils.toMap(deliveryCompanies, ChargeDeliveryCompany::getId);
                airPharmacyLogisticsList.forEach(chargeAirPharmacyLogistics -> {
                    String deliveryCompanyId = chargeAirPharmacyLogistics.getDeliveryCompanyId();
                    if (!StringUtils.isEmpty(deliveryCompanyId)) {
                        ChargeDeliveryCompany deliveryCompany = deliveryCompanyMap.getOrDefault(deliveryCompanyId, null);
                        if (Objects.nonNull(deliveryCompany)) {
                            ChargeAirPharmacyLogisticsReq.LogisticsCompanyReq deliveryCompanyView = new ChargeAirPharmacyLogisticsReq.LogisticsCompanyReq();
                            deliveryCompanyView.setId(deliveryCompany.getId());
                            deliveryCompanyView.setName(deliveryCompany.getName());
                            chargeAirPharmacyLogistics.setDeliveryCompany(deliveryCompanyView);
                        }
                    }
                });
            }
        }
    }

    public static void bindChargeDeliveryInfoCore(ChargeSheet chargeSheet, List<ChargeDeliveryInfo> chargeDeliveryInfos) {
        if (Objects.isNull(chargeSheet)) {
            return;
        }

        if (!CollectionUtils.isEmpty(chargeDeliveryInfos) && chargeDeliveryInfos.size() > 1) {
            for (ChargeDeliveryInfo chargeDeliveryInfo : chargeDeliveryInfos) {
                if (chargeDeliveryInfo.getType() == ChargeDeliveryInfo.ChargeDeliveryType.REAL_DELIVERY_ADDRESS) {
                    chargeSheet.setDeliveryInfo(chargeDeliveryInfo);
                }
            }
        } else {
            if (!CollectionUtils.isEmpty(chargeDeliveryInfos)) {
                chargeSheet.setDeliveryInfo(chargeDeliveryInfos.get(0));
            }
        }
    }

    public static void bindChargeDeliveryTrace(String chainId, List<ChargeDeliveryInfo> chargeDeliveryInfos, ChargeDeliveryTraceRepository chargeDeliveryTraceRepository) {
        if (CollectionUtils.isEmpty(chargeDeliveryInfos)) {
            return;
        }

        List<String> deliveryNos = chargeDeliveryInfos.stream()
                .filter(chargeDeliveryInfo -> org.apache.commons.lang3.StringUtils.isNotEmpty(chargeDeliveryInfo.getDeliveryOrderNo()))
                .map(ChargeDeliveryInfo::getDeliveryOrderNo)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(deliveryNos)) {
            List<ChargeDeliveryTrace> chargeDeliveryTraces = chargeDeliveryTraceRepository.findAllByChainIdAndDeliveryOrderNoIn(chainId, deliveryNos);

            Map<String, ChargeDeliveryTrace> chargeDeliveryTraceMap = Optional.ofNullable(chargeDeliveryTraces)
                    .orElse(new ArrayList<>())
                    .stream()
                    .collect(Collectors.toMap(ChargeDeliveryTrace::getDeliveryOrderNo, Function.identity(), (a, b) -> a));
            chargeDeliveryInfos.stream()
                    .filter(chargeDeliveryInfo -> org.apache.commons.lang3.StringUtils.isNotEmpty(chargeDeliveryInfo.getDeliveryOrderNo()))
                    .forEach(chargeDeliveryInfo -> chargeDeliveryInfo.setChargeDeliveryTrace(chargeDeliveryTraceMap.get(chargeDeliveryInfo.getDeliveryOrderNo())));
        }
    }
}
