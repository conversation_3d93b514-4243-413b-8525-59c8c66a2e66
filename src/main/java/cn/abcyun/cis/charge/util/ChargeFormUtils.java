package cn.abcyun.cis.charge.util;

import cn.abcyun.cis.charge.api.model.ChargeFormReq;
import cn.abcyun.cis.charge.base.ChargeServiceError;
import cn.abcyun.cis.charge.base.ChargeServiceException;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.model.ChargeForm;
import cn.abcyun.cis.charge.model.ChargeFormItem;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.common.log.marker.AbcLogMarker;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/***
 * 从ChargeSheet提取对应ChargeForm，ChargeFormItem的工具类
 * <AUTHOR>
 */

public class ChargeFormUtils {
    private static final Logger sLogger = LoggerFactory.getLogger(ChargeFormUtils.class);

    /**
     * 更新挂号，快递，加工等信息的chargeformItem
     */
    public static List<ChargeFormItem> processRegDeliveryFormItems(ChargeSheet chargeSheet) {
        if (chargeSheet == null || chargeSheet.getChargeForms() == null) {
            return null;
        }
        return chargeSheet.getChargeForms().stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.REGISTRATION
                        || chargeForm.getSourceFormType() == Constants.SourceFormType.EXPRESS_DELIVERY
                        || chargeForm.getSourceFormType() == Constants.SourceFormType.PROCESS)
                .filter(chargeForm -> chargeForm.getChargeFormItems() != null)
                .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                .filter(item -> item.getIsDeleted() == 0)
                .filter(chargeFormItem -> chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.UNCHARGED).collect(Collectors.toList());

    }

    /**
     * 获取所有未支付的chargeForm
     */
    public static List<ChargeForm> getUnChargedForms(ChargeSheet chargeSheet) {
        if (chargeSheet == null || chargeSheet.getChargeForms() == null) {
            return null;
        }
        return chargeSheet.getChargeForms()
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getStatus() == Constants.ChargeFormStatus.UNCHARGED).collect(Collectors.toList());

    }

    public static boolean isContainMedicineForAirPharmacyChargeForm(ChargeForm chargeForm) {

        if (chargeForm == null || chargeForm.getChargeFormItems() == null) {
            return false;
        }

        return chargeForm.getChargeFormItems().stream()
                .filter(item -> item.getIsDeleted() == 0)
                .filter(chargeFormItem -> chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.UNCHARGED)
                .anyMatch(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.MEDICINE);
    }

    public static boolean isContainMedicineForAirPharmacyChargeFormReq(ChargeFormReq chargeForm) {

        if (chargeForm == null || chargeForm.getChargeFormItems() == null) {
            return false;
        }

        return chargeForm.getChargeFormItems().stream()
                .anyMatch(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.MEDICINE);
    }

    public static void checkAirPharmacyLogisticsAndThrowException(ChargeForm chargeForm) {

        if (chargeForm == null || CollectionUtils.isEmpty(chargeForm.getChargeFormItems())) {
            return;
        }

        boolean medicalIsAllUnselected = chargeForm.getChargeFormItems().stream()
                .filter(item -> item.getIsDeleted() == 0)
                .filter(chargeFormItem -> chargeFormItem.getProductType() != Constants.ProductType.PROCESS && chargeFormItem.getProductType() != Constants.ProductType.EXPRESS_DELIVERY && chargeFormItem.getProductType() != Constants.ProductType.INGREDIENT)
                .allMatch(chargeFormItem -> chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.UNSELECTED);

        //如果药品都反选，则不校验快递信息
        if (medicalIsAllUnselected) {
            return;
        }

        if (chargeForm == null || chargeForm.getChargeAirPharmacyLogistics() == null) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "delivery is empty");
            throw new ChargeServiceException(ChargeServiceError.AIR_PHARMACY_DELIVERY_IS_EMPTY);
        }

        if (StringUtils.isEmpty(chargeForm.getChargeAirPharmacyLogistics().getAddressProvinceId())) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "delivery.addressProvinceId is empty");
            throw new ChargeServiceException(ChargeServiceError.AIR_PHARMACY_DELIVERY_IS_EMPTY);
        }

        if (StringUtils.isEmpty(chargeForm.getChargeAirPharmacyLogistics().getAddressCityId())) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "delivery.addressCityId is empty");
            throw new ChargeServiceException(ChargeServiceError.AIR_PHARMACY_DELIVERY_IS_EMPTY);
        }

        if (StringUtils.isEmpty(chargeForm.getChargeAirPharmacyLogistics().getAddressDistrictId())) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "delivery.addressDistrictId is empty");
            throw new ChargeServiceException(ChargeServiceError.AIR_PHARMACY_DELIVERY_IS_EMPTY);
        }

        if (StringUtils.isEmpty(chargeForm.getChargeAirPharmacyLogistics().getDeliveryCompanyId())) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "delivery.deliveryCompanyId is empty");
            throw new ChargeServiceException(ChargeServiceError.AIR_PHARMACY_DELIVERY_IS_EMPTY);
        }
    }

    public static void updateChargeFormByDb(List<ChargeForm> chargeForms, List<ChargeForm> chargeFormsDb) {

        if (CollectionUtils.isEmpty(chargeForms) || CollectionUtils.isEmpty(chargeFormsDb)) {
            return;
        }

        Map<String, ChargeForm> chargeFormDbMap = ListUtils.toMap(chargeFormsDb, ChargeForm::getId);
        chargeForms.stream().forEach(chargeForm -> {
            ChargeForm chargeFormDb = chargeFormDbMap.get(chargeForm.getId());

            if (Objects.isNull(chargeFormDb)) {
                return;
            }

            Map<String, ChargeFormItem> formItemDbMap = ListUtils.toMap(chargeFormDb.getChargeFormItems()
                            .stream()
                            .filter(item -> item.getIsDeleted() == 0)
                            .collect(Collectors.toList()),
                    ChargeFormItem::getId);

            chargeForm.getChargeFormItems().stream()
                    .filter(item -> item.getIsDeleted() == 0)
                    .forEach(item -> {
                        ChargeFormItem chargeFormItem = formItemDbMap.get(item.getId());
                        if (Objects.isNull(chargeFormItem)) {
                            return;
                        }

                        item.setComposeType(chargeFormItem.getComposeType());
                        item.setGoodsFeeType(chargeFormItem.getGoodsFeeType());

                    });

        });

    }

    public static ChargeForm copyForDirectSale(ChargeForm sourceChargeForm,
                                               String chargeSheetId,
                                               Map<String, String> oldItemIdToNewItemId) {
        if (Objects.isNull(sourceChargeForm)) {
            return null;
        }
        String copiedFormId = AbcIdUtils.getUID();

        ChargeForm copiedForm = new ChargeForm();
        copiedForm.setId(copiedFormId);
        copiedForm.setClinicId(sourceChargeForm.getClinicId());
        copiedForm.setChainId(sourceChargeForm.getChainId());
        copiedForm.setChargeSheetId(chargeSheetId);
        copiedForm.setSourceFormType(sourceChargeForm.getSourceFormType());
        copiedForm.setSort(sourceChargeForm.getSort());
        copiedForm.setChargeFormItems(
                copyItemsForDirectSale(sourceChargeForm.getChargeFormItems(), chargeSheetId, copiedFormId, oldItemIdToNewItemId)
        );
        copiedForm.setUsageInfoJson(sourceChargeForm.getUsageInfoJson());
        copiedForm.setUsageInfo(sourceChargeForm.getUsageInfo());
        copiedForm.setSpecification(sourceChargeForm.getSpecification());
        copiedForm.setPharmacyNo(sourceChargeForm.getPharmacyNo());
        copiedForm.setPharmacyType(sourceChargeForm.getPharmacyType());

        return copiedForm;
    }

    private static List<ChargeFormItem> copyItemsForDirectSale(List<ChargeFormItem> chargeFormItems,
                                                               String chargeSheetId,
                                                               String copiedFormId,
                                                               Map<String, String> oldItemIdToNewItemId) {
        if (CollectionUtils.isEmpty(chargeFormItems)) {
            return Lists.newArrayList();
        }
        // 需要记录父项的映射关系，所以这里优先复制父项
        List<ChargeFormItem> copiedItems = chargeFormItems.stream()
                .filter(ChargeFormItem::isParentItem)
                .filter(chargeFormItem->Objects.equals(chargeFormItem.getStatus(), Constants.ChargeFormItemStatus.CHARGED)
                        || Objects.equals(chargeFormItem.getStatus(), Constants.ChargeFormItemStatus.UNSELECTED)
                )
                .map(chargeFormItem -> {
                    ChargeFormItem copiedChargeFormItem = ChargeFormItemUtils.copyForDirectSale(chargeFormItem, chargeSheetId, copiedFormId);
                    if (copiedChargeFormItem != null) {
                        oldItemIdToNewItemId.put(chargeFormItem.getId(), copiedChargeFormItem.getId());
                    }
                    return copiedChargeFormItem;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 复制其它项目（子项、单项）
        chargeFormItems
                .stream()
                .filter(chargeFormItem -> !oldItemIdToNewItemId.containsKey(chargeFormItem.getId()))
                .filter(chargeFormItem->Objects.equals(chargeFormItem.getStatus(), Constants.ChargeFormItemStatus.CHARGED)
                        || Objects.equals(chargeFormItem.getStatus(), Constants.ChargeFormItemStatus.UNSELECTED)
                )
                .forEach(chargeFormItem -> {
                    ChargeFormItem copiedChargeFormItem = ChargeFormItemUtils.copyForDirectSale(chargeFormItem, chargeSheetId, copiedFormId);
                    if (Objects.isNull(copiedChargeFormItem)) {
                        return;
                    }
                    oldItemIdToNewItemId.put(chargeFormItem.getId(), copiedChargeFormItem.getId());
                    if (StringUtils.isNotBlank(chargeFormItem.getComposeParentFormItemId())) {
                        copiedChargeFormItem.setComposeParentFormItemId(oldItemIdToNewItemId.get(chargeFormItem.getComposeParentFormItemId()));
                    }
                    copiedItems.add(copiedChargeFormItem);
                });

        return copiedItems;
    }
}
