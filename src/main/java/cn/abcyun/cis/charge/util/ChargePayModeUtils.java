package cn.abcyun.cis.charge.util;

import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.processor.ChargePayModeConfigSimple;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Supplier;

public class ChargePayModeUtils {

    @Data
    @Accessors(chain = true)
    public static class PayModeNameDto {

        private String payModeName;

        private String paySubModeName;

    }

    /**
     * 与统计对齐的支付方式名称
     * @param payMode
     * @param paySubMode
     * @param payModeName
     * @param paySubModeName
     * @return
     */
    public static String convertPayModeDisplayNameForTransaction(int payMode, int paySubMode, String payModeName, String paySubModeName) {

        //微信支付分微信直付和微信记账
        if (payMode == Constants.ChargePayMode.WECHAT_PAY) {
            if (paySubMode == Constants.ChargePaySubMode.ONLY_RECORD) {
                return "微信记账";
            }
            return getPaySubModeNameOrDefault(paySubModeName, payModeName);
        }

        return convertPayModeDisplayNameForRefund(payMode, paySubMode, payModeName, paySubModeName);
    }

    /**
     * 收费单展示的支付方式名称
     * @param payMode
     * @param paySubMode
     * @param payModeName
     * @param paySubModeName
     * @return
     */
    public static String convertPayModeDisplayNameForRefund(int payMode, int paySubMode, String payModeName, String paySubModeName) {

        //医保支付的需要处理医保移动支付和非医保移动支付
        if (payMode == Constants.ChargePayMode.HEALTH_CARD && paySubMode == Constants.ChargePaySubMode.WECHAT_MOBILE_SHEBAO_INSURANCE) {
            return getPaySubModeNameOrDefault(paySubModeName, payModeName);
        }

        if (payMode == Constants.ChargePayMode.ABC_PAY || payMode == Constants.ChargePayMode.OUTPATIENT_CENTER_PAY || payMode == Constants.ChargePayMode.SHEBAO_YIMA_PAY) {

            if (StringUtils.isBlank(paySubModeName)) {
                return payModeName;
            }
            return String.format("%s（%s）", payModeName, paySubModeName);
        }
        return payModeName;
    }

    private static String getPaySubModeNameOrDefault(String paySubModeName, String payModeName) {
        return StringUtils.isNotBlank(paySubModeName) ? paySubModeName : payModeName;
    }

    public static PayModeNameDto splitHistoryPayModeDisplayName(int payMode, String historyPayModeDisplayName) {
        PayModeNameDto payModeNameDto = new PayModeNameDto().setPayModeName(historyPayModeDisplayName);
        if (payMode != Constants.ChargePayMode.ABC_PAY) {
            return payModeNameDto;
        }

        if (StringUtils.isBlank(historyPayModeDisplayName)) {
            return payModeNameDto;
        }

        String[] payModeNames = historyPayModeDisplayName.split("-");

        if (payModeNames.length == 0) {
            return payModeNameDto;
        }

        if (payModeNames.length == 1) {
            return payModeNameDto.setPayModeName(payModeNames[0]);
        }

        if (payModeNames.length == 2) {
            return payModeNameDto.setPayModeName(payModeNames[0]).setPaySubModeName(payModeNames[1]);
        }

        return payModeNameDto;
    }

    public static PayModeNameDto queryPayModeNameAndPayModeSubName(int payMode, int paySubMode, String patientCardName, Supplier<Map<Long, ChargePayModeConfigSimple>> queryPayModeSupplier) {

        PayModeNameDto payModeNameDto = new PayModeNameDto();

        if (payMode == Constants.ChargePayMode.PROMOTION_CARD) {
            return payModeNameDto.setPayModeName(patientCardName);
        }

        if (payMode == Constants.ChargePayMode.HEALTH_CARD && paySubMode == Constants.ChargePaySubMode.WECHAT_MOBILE_SHEBAO_INSURANCE) {
            payModeNameDto.setPayModeName("医保");
            payModeNameDto.setPaySubModeName("医保移动支付");
            return payModeNameDto;
        }

        if (payMode == Constants.ChargePayMode.OUTPATIENT_CENTER_PAY) {
            payModeNameDto.setPayModeName("诊间支付");
            if (paySubMode == Constants.ChargePaySubMode.OUTPATIENT_CENTER_PAY_INSURANCE) {
                payModeNameDto.setPaySubModeName("医保");
            } else if (paySubMode == Constants.ChargePaySubMode.SHEBAO_CASH_ALI_PAY) {
                payModeNameDto.setPaySubModeName("支付宝");
            } else if (paySubMode == Constants.ChargePaySubMode.SHEBAO_CASH_WECHAT_PAY) {
                payModeNameDto.setPaySubModeName("微信");
            } else if (paySubMode == Constants.ChargePaySubMode.SHEBAO_CASH_BANK_CARD_PAY) {
                payModeNameDto.setPaySubModeName("银行卡");
            }
            return payModeNameDto;
        }

        if (payMode == Constants.ChargePayMode.SHEBAO_YIMA_PAY) {
            payModeNameDto.setPayModeName("一码付");
            if (paySubMode == Constants.ChargePaySubMode.SHEBAO_CASH_ALI_PAY) {
                payModeNameDto.setPaySubModeName("支付宝");
            } else if (paySubMode == Constants.ChargePaySubMode.SHEBAO_CASH_WECHAT_PAY) {
                payModeNameDto.setPaySubModeName("微信");
            } else if (paySubMode == Constants.ChargePaySubMode.SHEBAO_CASH_BANK_CARD_PAY) {
                payModeNameDto.setPaySubModeName("银行卡");
            }
            return payModeNameDto;
        }

        if (payMode == Constants.ChargePayMode.WECHAT_PAY && paySubMode == Constants.ChargePaySubMode.WECHAT_MOBILE_SHEBAO_CASH) {
            payModeNameDto.setPayModeName("微信");
            payModeNameDto.setPaySubModeName("微信直付");
            return payModeNameDto;
        }

        if(payMode == Constants.ChargePayMode.ABC_PAY) {
            return buildAbcPaySubModeName(paySubMode);
        }

        if (Objects.isNull(queryPayModeSupplier) || Objects.isNull(queryPayModeSupplier.get())) {
            return payModeNameDto;
        }

        Optional.ofNullable(queryPayModeSupplier.get().getOrDefault((long) payMode, null))
                .ifPresent(chargePayModeConfigSimple -> payModeNameDto.setPayModeName(chargePayModeConfigSimple.getName()));

        return payModeNameDto;
    }


    public static PayModeNameDto buildAbcPaySubModeName(int paySubMode) {
        PayModeNameDto payModeNameDto = new PayModeNameDto().setPayModeName("ABC支付");
        switch (paySubMode) {
            case Constants.ChargePaySubMode.ABC_PAY_WECHAT_NATIVE:
            case Constants.ChargePaySubMode.ABC_PAY_WECHAT_JS:
            case Constants.ChargePaySubMode.ABC_PAY_WECHAT_MINI:
            case Constants.ChargePaySubMode.ALLIN_PAY_SCAN_WECHAT:
                payModeNameDto.setPaySubModeName("微信");
                break;
            case Constants.ChargePaySubMode.ABC_PAY_ALI_NATIVE:
            case Constants.ChargePaySubMode.ALLIN_PAY_SCAN_ALI:
            case Constants.ChargePaySubMode.ABC_PAY_SCAN_ONE_QR_CODE:
                payModeNameDto.setPaySubModeName("支付宝");
                break;
            case Constants.ChargePaySubMode.ABC_PAY_UNION_NATIVE:
            case Constants.ChargePaySubMode.ALLIN_PAY_SCAN_UNION:
                payModeNameDto.setPaySubModeName("银联");
                break;
        }

        return payModeNameDto;
    }
}
