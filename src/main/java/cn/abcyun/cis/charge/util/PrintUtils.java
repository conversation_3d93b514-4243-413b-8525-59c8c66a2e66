package cn.abcyun.cis.charge.util;

import cn.abcyun.cis.commons.rpc.print.OrganPrintView;
import cn.abcyun.cis.commons.rpc.shebao.QueryChargeSheetShebaoInfoRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class PrintUtils {

    public static OrganPrintView parseToOrganPrintView(cn.abcyun.bis.rpc.sdk.cis.model.clinic.OrganPrintView sourceOrganPrintView) {

        if (Objects.isNull(sourceOrganPrintView)) {
            return null;
        }

        OrganPrintView targetOrganPrintView = new OrganPrintView();
        BeanUtils.copyProperties(sourceOrganPrintView, targetOrganPrintView);

        targetOrganPrintView.setShortName(sourceOrganPrintView.getName());
        targetOrganPrintView.setHisType(sourceOrganPrintView.getHisType());

        if (Objects.nonNull(sourceOrganPrintView.getMedicalDocumentsTitle())) {
            cn.abcyun.bis.rpc.sdk.cis.model.clinic.OrganPrintView.MedicalDocumentsTitle sourceMedicalDocumentsTitle = sourceOrganPrintView.getMedicalDocumentsTitle();
            OrganPrintView.MedicalDocumentsTitle targetMedicalDocumentsTitle = new OrganPrintView.MedicalDocumentsTitle();
            BeanUtils.copyProperties(sourceMedicalDocumentsTitle, targetMedicalDocumentsTitle);
            targetOrganPrintView.setMedicalDocumentsTitle(targetMedicalDocumentsTitle);
        }
        return targetOrganPrintView;
    }

    public static Map<String, QueryChargeSheetShebaoInfoRsp.GoodsItem> convertShebaoGoodsItemMap(List<QueryChargeSheetShebaoInfoRsp.GoodsItem> shebaoGoodsItemList) {
        return Optional.ofNullable(shebaoGoodsItemList)
                .orElse(new ArrayList<>())
                .stream()
                .filter(shebaoGoodsItem -> org.apache.commons.lang3.StringUtils.isNotEmpty(shebaoGoodsItem.getChargeFormItemId())
                        || org.apache.commons.lang3.StringUtils.isNotEmpty(shebaoGoodsItem.getId())
                )
                .collect(Collectors.toMap(shebaoGoodsItem -> {
                    if (org.apache.commons.lang3.StringUtils.isNotEmpty(shebaoGoodsItem.getChargeFormItemId())) {
                        return shebaoGoodsItem.getChargeFormItemId();
                    } else {
                        return shebaoGoodsItem.getId();
                    }
                }, Function.identity(), (a, b) -> a));
    }

}
