package cn.abcyun.cis.charge.util;

import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsBatchInfo;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.QueryGoodsInPharmacyByIdsAndStockCountReq;
import cn.abcyun.bis.rpc.sdk.cis.model.goodslocking.GoodsLockBatchItem;
import cn.abcyun.bis.rpc.sdk.cis.model.goodslocking.GoodsLockingSheet;
import cn.abcyun.cis.charge.base.ChargeVersionConstants;
import cn.abcyun.cis.charge.helper.ChargeTransactionRecordDiscountInfoHelper;
import cn.abcyun.cis.charge.model.BatchTraceableCode;
import cn.abcyun.cis.charge.model.ChargeFormItem;
import cn.abcyun.cis.charge.model.ChargeFormItemBatchInfo;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.processor.FlatPriceHelper;
import cn.abcyun.cis.charge.processor.ItemBatchInfoProcessor;
import cn.abcyun.cis.charge.processor.limitprice.BaseLimitPriceInfo;
import cn.abcyun.cis.charge.service.dto.ChargeDiscountInfo;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.common.log.marker.AbcLogMarker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class ChargeFormItemBatchInfoUtils {

    public static String signChargeFormItemBatchInfos(ChargeSheet chargeSheet) {
        Map<String, String> keyData = ChargeUtils.getChargeSheetItemBatchInfos(chargeSheet)
                .stream()
                .sorted(Comparator.comparing(ChargeFormItemBatchInfo::getChargeFormItemId)
                        .thenComparing(ChargeFormItemBatchInfo::getBatchId))
                .collect(Collectors.toMap(chargeFormItemBatchInfo -> String.format("%s-%s",
                                chargeFormItemBatchInfo.getChargeFormItemId(),
                                TextUtils.alwaysString(chargeFormItemBatchInfo.getBatchId())
                        ),
                        chargeFormItemBatchInfo -> String.format("%s-%s-%s",
                                TextUtils.alwaysString(chargeFormItemBatchInfo.getChargeFormItemId()),
                                TextUtils.alwaysString(chargeFormItemBatchInfo.getBatchId()),
                                MathUtils.wrapBigDecimalOrZero(chargeFormItemBatchInfo.getUnitCount()).stripTrailingZeros().toPlainString()),
                        (a, b) -> a));
        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "signChargeFormItemBatchInfos map: {}", JsonUtils.dump(keyData));
        return ChargeUtils.signMap(keyData);
    }

    public static String signGoodsLockingSheet(GoodsLockingSheet goodsLockingSheet) {
        Map<String, String> keyData = getGoodsLockBatchItems(goodsLockingSheet)
                .stream()
                .filter(goodsLockBatchItem -> StringUtils.isNotEmpty(goodsLockBatchItem.getLockFormItemId())
                        && Objects.nonNull(goodsLockBatchItem.getBatchId())
                )
                .sorted(Comparator.comparing(GoodsLockBatchItem::getLockFormItemId)
                        .thenComparing(GoodsLockBatchItem::getBatchId))
                .collect(Collectors.toMap(goodsLockBatchItem -> String.format("%s-%s",
                                goodsLockBatchItem.getLockFormItemId(),
                                Objects.toString(goodsLockBatchItem.getBatchId(), "")
                        ),
                        goodsLockBatchItem -> {
                            BigDecimal count = MathUtils.max(goodsLockBatchItem.getLockingBatchPackageCount(), goodsLockBatchItem.getLockingBatchPieceCount());
                            return String.format("%s-%s-%s",
                                    TextUtils.alwaysString(goodsLockBatchItem.getLockFormItemId()),
                                    Objects.toString(goodsLockBatchItem.getBatchId(), ""),
                                    count.stripTrailingZeros().toPlainString());
                        },
                        (a, b) -> a));

        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "signGoodsLockingSheet map: {}", JsonUtils.dump(keyData));
        return ChargeUtils.signMap(keyData);
    }

    public static String signQueryGoodsWithStockBatch(List<QueryGoodsInPharmacyByIdsAndStockCountReq.PretendBatchCutItem> pretendBatchCutItems) {
        Map<String, String> pretendBatchSignMap = pretendBatchCutItems
                .stream()
                .sorted(Comparator.comparing(QueryGoodsInPharmacyByIdsAndStockCountReq.PretendBatchCutItem::getBatchId))
                .collect(Collectors.toMap(pretendBatchCutItem -> Objects.toString(pretendBatchCutItem.getBatchId(), null),
                        pretendBatchCutItem -> String.format("%d-%s-%s",
                                pretendBatchCutItem.getBatchId(),
                                MathUtils.wrapBigDecimalOrZero(pretendBatchCutItem.getPieceCount()).stripTrailingZeros().toPlainString(),
                                MathUtils.wrapBigDecimalOrZero(pretendBatchCutItem.getPackageCount()).stripTrailingZeros().toPlainString()),
                        (a, b) -> a)
                );
        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "pretendBatchSignMap map: {}", JsonUtils.dump(pretendBatchSignMap));
        return ChargeUtils.signMap(pretendBatchSignMap);
    }

    public static String signGoodsItemBatch(List<GoodsBatchInfo> goodsBatchInfoList) {
        Map<String, String> goodsItemBatchKeyMap = Optional.ofNullable(goodsBatchInfoList)
                .orElse(new ArrayList<>())
                .stream()
                .sorted(Comparator.comparing(GoodsBatchInfo::getBatchId))
                .collect(Collectors.toMap(goodsBatchInfo -> Objects.toString(goodsBatchInfo.getBatchId(), null),
                        goodsBatchInfo -> String.format("%d-%s-%s",
                                goodsBatchInfo.getBatchId(),
                                MathUtils.wrapBigDecimalOrZero(goodsBatchInfo.getCutPieceCount()).stripTrailingZeros().toPlainString(),
                                MathUtils.wrapBigDecimalOrZero(goodsBatchInfo.getCutPackageCount()).stripTrailingZeros().toPlainString()),
                        (a, b) -> a)
                );
        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "goodsItemBatchKeyMap map: {}", JsonUtils.dump(goodsItemBatchKeyMap));
        return ChargeUtils.signMap(goodsItemBatchKeyMap);
    }

    private static List<GoodsLockBatchItem> getGoodsLockBatchItems(GoodsLockingSheet goodsLockingSheet) {

        if (Objects.isNull(goodsLockingSheet) || CollectionUtils.isEmpty(goodsLockingSheet.getLockingForms())) {
            return Collections.emptyList();
        }

        return goodsLockingSheet.getLockingForms()
                .stream()
                .flatMap(goodsLockingForm -> Optional.ofNullable(goodsLockingForm.getLockingFormItems()).orElse(new ArrayList<>()).stream())
                .flatMap(goodsLockingFormItem -> Optional.ofNullable(goodsLockingFormItem.getGoodsLockBatchItemList()).orElse(new ArrayList<>()).stream())
                .collect(Collectors.toList());
    }

    private static ChargeFormItemBatchInfo generateChargeFormItemBatchInfoFromGoodsLockBatchItem(ChargeFormItem chargeFormItem,
                                                                                                 GoodsLockBatchItem goodsLockBatchItem,
                                                                                                 BigDecimal pieceNum,
                                                                                                 String operatorId) {

        if (Objects.isNull(chargeFormItem) || Objects.isNull(goodsLockBatchItem)) {
            return null;
        }

        BigDecimal unitCount = BigDecimal.ZERO;
        BigDecimal unitCostPrice = goodsLockBatchItem.getUnitCostPrice();
        if (chargeFormItem.getUseDismounting() == 0) {
            unitCount = goodsLockBatchItem.getLockingBatchPackageCount();
        } else {
            unitCount = goodsLockBatchItem.getLockingBatchPieceCount();
            if (MathUtils.wrapBigDecimalCompare(pieceNum, BigDecimal.ZERO) > 0) {
                unitCostPrice = goodsLockBatchItem.getUnitCostPrice().divide(pieceNum, 4, RoundingMode.HALF_UP);
            }
        }

        ChargeFormItemBatchInfo chargeFormItemBatchInfo = new ChargeFormItemBatchInfo();
        chargeFormItemBatchInfo.setId(AbcIdUtils.getUID());
        chargeFormItemBatchInfo.setClinicId(chargeFormItem.getClinicId());
        chargeFormItemBatchInfo.setChainId(chargeFormItem.getChainId());
        chargeFormItemBatchInfo.setPatientOrderId(chargeFormItem.getPatientOrderId());
        chargeFormItemBatchInfo.setChargeSheetId(chargeFormItem.getChargeSheetId());
        chargeFormItemBatchInfo.setChargeFormId(chargeFormItem.getChargeFormId());
        chargeFormItemBatchInfo.setChargeFormItemId(chargeFormItem.getId());
        chargeFormItemBatchInfo.setAssociateItemBatchInfoId(null);
        chargeFormItemBatchInfo.setUnitCostPrice(unitCostPrice);
        chargeFormItemBatchInfo.setUnitCount(unitCount);
        chargeFormItemBatchInfo.setUnitPrice(chargeFormItem.getSourceUnitPrice());
        chargeFormItemBatchInfo.setSourceUnitPrice(chargeFormItem.getSourceUnitPrice());
        chargeFormItemBatchInfo.setRefundUnitCount(BigDecimal.ZERO);
        chargeFormItemBatchInfo.setRefundTotalPrice(BigDecimal.ZERO);
        chargeFormItemBatchInfo.setProductId(goodsLockBatchItem.getGoodsId());
        chargeFormItemBatchInfo.setIsUseLimitPrice(0);
        chargeFormItemBatchInfo.setStockId(Objects.toString(goodsLockBatchItem.getStockId(), null));
        chargeFormItemBatchInfo.setBatchId(Objects.toString(goodsLockBatchItem.getBatchId(), null));
        chargeFormItemBatchInfo.setBatchNo(goodsLockBatchItem.getBatchNo());
        chargeFormItemBatchInfo.setExpiryDate(goodsLockBatchItem.getExpiryDate());
        chargeFormItemBatchInfo.setExpectedTotalPrice(null);
        if (CollectionUtils.isNotEmpty(goodsLockBatchItem.getTraceableCodeList())) {
            chargeFormItemBatchInfo.setTraceableCodes(goodsLockBatchItem.getTraceableCodeList()
                    .stream()
                    .map(BatchTraceableCode::of)
                    .collect(Collectors.toList())
            );
        }
        chargeFormItemBatchInfo.setIsOld(0);
        chargeFormItemBatchInfo.setIsNotCharged(0);
        chargeFormItemBatchInfo.calculateTotalPriceHalfDown();
        FillUtils.fillCreatedBy(chargeFormItemBatchInfo, operatorId);
        return chargeFormItemBatchInfo;
    }


    private static ChargeFormItemBatchInfo generateChargeFormItemBatchInfoFromGoodsBatchInfo(ChargeFormItem chargeFormItem,
                                                                                             BigDecimal pieceNum,
                                                                                             GoodsBatchInfo goodsBatchInfo,
                                                                                             String operatorId) {

        if (Objects.isNull(chargeFormItem) || Objects.isNull(goodsBatchInfo)) {
            return null;
        }

        int useDismounting = chargeFormItem.getUseDismounting();
        BigDecimal unitCount = MathUtils.wrapBigDecimalOrZero(useDismounting == 1 ? goodsBatchInfo.getCutPieceCount() : goodsBatchInfo.getCutPackageCount());
        BigDecimal sourceUnitPrice = MathUtils.wrapBigDecimalOrZero(useDismounting == 1 ? goodsBatchInfo.getPiecePrice() : goodsBatchInfo.getPackagePrice());

        if (MathUtils.wrapBigDecimalCompare(unitCount, BigDecimal.ZERO) <= 0) {
            return null;
        }

        ChargeFormItemBatchInfo chargeFormItemBatchInfo = new ChargeFormItemBatchInfo();
        chargeFormItemBatchInfo.setId(AbcIdUtils.getUID());
        chargeFormItemBatchInfo.setClinicId(chargeFormItem.getClinicId());
        chargeFormItemBatchInfo.setChainId(chargeFormItem.getChainId());
        chargeFormItemBatchInfo.setPatientOrderId(chargeFormItem.getPatientOrderId());
        chargeFormItemBatchInfo.setChargeSheetId(chargeFormItem.getChargeSheetId());
        chargeFormItemBatchInfo.setChargeFormId(chargeFormItem.getChargeFormId());
        chargeFormItemBatchInfo.setChargeFormItemId(chargeFormItem.getId());
        chargeFormItemBatchInfo.setAssociateItemBatchInfoId(null);
        chargeFormItemBatchInfo.setUnitCostPrice(calculateUnitCostPrice(goodsBatchInfo.getPackageCostPrice(), pieceNum, useDismounting));
        chargeFormItemBatchInfo.setUnitCount(unitCount);
        chargeFormItemBatchInfo.setTotalBatchPieceCount(goodsBatchInfo.getCutTotalPieceCount());
        chargeFormItemBatchInfo.setUnitPrice(sourceUnitPrice);
        chargeFormItemBatchInfo.setSourceUnitPrice(sourceUnitPrice);
        chargeFormItemBatchInfo.setRefundUnitCount(BigDecimal.ZERO);
        chargeFormItemBatchInfo.setRefundTotalPrice(BigDecimal.ZERO);
        chargeFormItemBatchInfo.setProductId(chargeFormItem.getProductId());
        chargeFormItemBatchInfo.setIsUseLimitPrice(0);
        chargeFormItemBatchInfo.setStockId(null);
        chargeFormItemBatchInfo.setBatchId(Optional.ofNullable(goodsBatchInfo.getBatchId()).map(String::valueOf).orElse(null));
        chargeFormItemBatchInfo.setBatchNo(goodsBatchInfo.getBatchNo());
        chargeFormItemBatchInfo.setExpiryDate(goodsBatchInfo.getExpiryDate());
        chargeFormItemBatchInfo.setIsOld(0);
        chargeFormItemBatchInfo.setIsNotCharged(0);
        chargeFormItemBatchInfo.setExpectedTotalPrice(null);
        chargeFormItemBatchInfo.setSourceTotalPrice(goodsBatchInfo.getTotalSalePrice());
        chargeFormItemBatchInfo.setTotalPrice(goodsBatchInfo.getTotalSalePrice());
        chargeFormItemBatchInfo.calculateTotalCostPrice();
        FillUtils.fillCreatedBy(chargeFormItemBatchInfo, operatorId);
        return chargeFormItemBatchInfo;
    }

    public static BigDecimal calculateUnitCostPrice(BigDecimal goodsPackageCostPrice, BigDecimal pieceNum, int isDismounting) {
        if (isDismounting == 1) {

            if (pieceNum == null || pieceNum.compareTo(BigDecimal.ZERO) == 0) {
                pieceNum = BigDecimal.ONE;
            }

            return goodsPackageCostPrice.divide(cn.abcyun.cis.commons.util.MathUtils.wrapBigDecimalOrZero(pieceNum), 4, RoundingMode.HALF_UP);
        }
        return goodsPackageCostPrice;
    }

    public static void dealItemBatchInfosFractionPrice(List<ChargeFormItemBatchInfo> chargeFormItemBatchInfos, BigDecimal itemTotalPrice) {

        if (CollectionUtils.isEmpty(chargeFormItemBatchInfos) || Objects.isNull(itemTotalPrice)) {
            return;
        }

        BigDecimal batchInfoTotalPrice = chargeFormItemBatchInfos.stream()
                .map(ChargeFormItemBatchInfo::getTotalPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        chargeFormItemBatchInfos.sort(((a, b) -> {
            Date date1 = DateUtils.parseToDate(a.getExpiryDate(), "yyyy-MM-dd");
            Date date2 = DateUtils.parseToDate(b.getExpiryDate(), "yyyy-MM-dd");

            if (date1 == null) {
                return 1;
            }

            if (date2 == null) {
                return -1;
            }
            return date1.compareTo(date2);
        }));

        if (MathUtils.wrapBigDecimalSubtract(itemTotalPrice, batchInfoTotalPrice).compareTo(BigDecimal.ZERO) > 0) {
            ChargeFormItemBatchInfo lastChargeFormItemBatchInfo = chargeFormItemBatchInfos.get(chargeFormItemBatchInfos.size() - 1);
            lastChargeFormItemBatchInfo.setTotalPrice(MathUtils.wrapBigDecimalAdd(lastChargeFormItemBatchInfo.getTotalPrice(), MathUtils.wrapBigDecimalSubtract(itemTotalPrice, batchInfoTotalPrice)));
        }
    }

    public static void dealItemBatchInfosSourceFractionPrice(List<ChargeFormItemBatchInfo> chargeFormItemBatchInfos, BigDecimal itemSourceTotalPrice) {

        if (CollectionUtils.isEmpty(chargeFormItemBatchInfos) || Objects.isNull(itemSourceTotalPrice)) {
            return;
        }

        BigDecimal batchSourceTotalPrice = chargeFormItemBatchInfos.stream()
                .map(ChargeFormItemBatchInfo::getSourceTotalPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        chargeFormItemBatchInfos.sort(((a, b) -> {
            Date date1 = DateUtils.parseToDate(a.getExpiryDate(), "yyyy-MM-dd");
            Date date2 = DateUtils.parseToDate(b.getExpiryDate(), "yyyy-MM-dd");

            if (date1 == null) {
                return 1;
            }

            if (date2 == null) {
                return -1;
            }
            return date1.compareTo(date2);
        }));

        if (MathUtils.wrapBigDecimalSubtract(itemSourceTotalPrice, batchSourceTotalPrice).compareTo(BigDecimal.ZERO) > 0) {
            ChargeFormItemBatchInfo lastChargeFormItemBatchInfo = chargeFormItemBatchInfos.get(chargeFormItemBatchInfos.size() - 1);
            lastChargeFormItemBatchInfo.setSourceTotalPrice(MathUtils.wrapBigDecimalAdd(lastChargeFormItemBatchInfo.getSourceTotalPrice(), MathUtils.wrapBigDecimalSubtract(itemSourceTotalPrice, batchSourceTotalPrice)));
        }
    }

    public static void insertChargeFormItemBatchInfos(ChargeFormItem chargeFormItem,
                                                      List<GoodsLockBatchItem> goodsLockBatchItemList,
                                                      BigDecimal pieceNum,
                                                      String operatorId) {
        if (CollectionUtils.isEmpty(goodsLockBatchItemList)) {
            return;
        }


        List<ChargeFormItemBatchInfo> chargeFormItemBatchInfos = goodsLockBatchItemList
                .stream()
                .map(goodsLockBatchItem -> generateChargeFormItemBatchInfoFromGoodsLockBatchItem(chargeFormItem, goodsLockBatchItem, pieceNum, operatorId))
                .collect(Collectors.toList());

        //处理批次总金额和item总金额之间的零头问题
        dealItemBatchInfosFractionPrice(chargeFormItemBatchInfos, chargeFormItem.getTotalPrice());
    }

    public static void insertOrUpdateChargeFormItemBatchInfosForGoodsBatchInfo(ChargeFormItem chargeFormItem,
                                                                               BigDecimal pieceNum,
                                                                               List<GoodsBatchInfo> goodsBatchInfos,
                                                                               String operatorId) {
        Assert.notNull(pieceNum, "pieceNum不能为空");

        if (chargeFormItem.getChargeFormItemBatchInfos() == null) {
            chargeFormItem.setChargeFormItemBatchInfos(new ArrayList<>());
        }

        goodsBatchInfos = Optional.ofNullable(goodsBatchInfos).orElse(new ArrayList<>())
                .stream()
                .filter(goodsBatchInfo -> MathUtils.wrapBigDecimalCompare(goodsBatchInfo.getCutPackageCount(), BigDecimal.ZERO) > 0
                        || MathUtils.wrapBigDecimalCompare(goodsBatchInfo.getCutPieceCount(), BigDecimal.ZERO) > 0)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(goodsBatchInfos)) {
            chargeFormItem.getChargeFormItemBatchInfos().forEach(chargeFormItemBatchInfo -> chargeFormItemBatchInfo.deleteModel(operatorId));
            return;
        }

        List<ChargeFormItemBatchInfo> existedBatchInfos = chargeFormItem.getChargeFormItemBatchInfos();


        MergeTool.doMerge(goodsBatchInfos,
                existedBatchInfos,
                (goodsLockBatchItem, existedBatchInfo) -> TextUtils.equals(String.valueOf(goodsLockBatchItem.getBatchId()), existedBatchInfo.getBatchId()) && existedBatchInfo.getIsDeleted() == 0,
                goodsLockBatchItem -> generateChargeFormItemBatchInfoFromGoodsBatchInfo(chargeFormItem, pieceNum, goodsLockBatchItem, operatorId),
                existedBatchInfo -> {
                    existedBatchInfo.deleteModel(operatorId);
                    return false;
                },
                (goodsBatchInfo, existedBatchInfo) -> {
                    int useDismounting = chargeFormItem.getUseDismounting();
                    BigDecimal unitCount = MathUtils.wrapBigDecimalOrZero(useDismounting == 1 ? goodsBatchInfo.getCutPieceCount() : goodsBatchInfo.getCutPackageCount());
                    BigDecimal sourceUnitPrice = MathUtils.wrapBigDecimalOrZero(useDismounting == 1 ? goodsBatchInfo.getPiecePrice() : goodsBatchInfo.getPackagePrice());
                    if (MathUtils.wrapBigDecimalCompare(unitCount, BigDecimal.ZERO) == 0) {
                        return;
                    }

                    existedBatchInfo.setClinicId(chargeFormItem.getClinicId());
                    existedBatchInfo.setChainId(chargeFormItem.getChainId());
                    existedBatchInfo.setPatientOrderId(chargeFormItem.getPatientOrderId());
                    existedBatchInfo.setChargeSheetId(chargeFormItem.getChargeSheetId());
                    existedBatchInfo.setChargeFormId(chargeFormItem.getChargeFormId());
                    existedBatchInfo.setChargeFormItemId(chargeFormItem.getId());
                    existedBatchInfo.setTotalBatchPieceCount(goodsBatchInfo.getCutTotalPieceCount());
                    existedBatchInfo.setUnitCount(unitCount);
                    existedBatchInfo.setUnitCostPrice(calculateUnitCostPrice(goodsBatchInfo.getPackageCostPrice(), pieceNum, useDismounting));
                    existedBatchInfo.setUnitPrice(sourceUnitPrice);
                    existedBatchInfo.setSourceUnitPrice(sourceUnitPrice);
                    existedBatchInfo.setProductId(chargeFormItem.getProductId());
                    existedBatchInfo.setIsUseLimitPrice(0);
                    existedBatchInfo.setStockId(null);
                    existedBatchInfo.setBatchId(Optional.ofNullable(goodsBatchInfo.getBatchId()).map(String::valueOf).orElse(null));
                    existedBatchInfo.setBatchNo(goodsBatchInfo.getBatchNo());
                    existedBatchInfo.setExpiryDate(goodsBatchInfo.getExpiryDate());
                    existedBatchInfo.calculateTotalCostPrice();
                    if (StringUtils.isEmpty(existedBatchInfo.getCreatedBy())) {
                        existedBatchInfo.setCreatedBy(operatorId);
                    }
                    if (Objects.isNull(existedBatchInfo.getCreated())) {
                        existedBatchInfo.setCreated(Instant.now());
                    }

                    existedBatchInfo.setLastModified(Instant.now());
                    existedBatchInfo.setLastModifiedBy(operatorId);
                    existedBatchInfo.setExpectedTotalPrice(null);
                    existedBatchInfo.setSourceTotalPrice(goodsBatchInfo.getTotalSalePrice());
                    existedBatchInfo.setTotalPrice(goodsBatchInfo.getTotalSalePrice());
                }
        );

        /**
         * 将item上的单项议价摊到每个批次上面 然后重新计算批次的totalPrice
         */
        ChargeFormItemBatchInfoUtils.flatUnitAdjustmentFee(chargeFormItem.getChargeFormItemBatchInfos(), chargeFormItem.getUnitAdjustmentFee());

        chargeFormItem.getChargeFormItemBatchInfos()
                .stream()
                .filter(chargeFormItemBatchInfo -> chargeFormItemBatchInfo.getIsDeleted() == 0)
                .forEach(ChargeFormItemBatchInfo::calculateTotalPrice);
    }

    /**
     * @param batchInfos
     * @param sourceTotalPrice
     * @param itemCalculateUnitPriceScale
     * @param goodsItemIsMakeUp           是否为进价加成
     */
    public static void flatSourceTotalPriceToBatchInfo(List<ChargeFormItemBatchInfo> batchInfos,
                                                       BigDecimal sourceTotalPrice,
                                                       int itemCalculateUnitPriceScale,
                                                       boolean goodsItemIsMakeUp) {

        if (CollectionUtils.isEmpty(batchInfos)) {
            return;
        }

        //校验金额是否一致
        BigDecimal batchTotalPrice = batchInfos.stream()
                .filter(batchInfo -> batchInfo.getIsDeleted() == 0)
                .map(ChargeFormItemBatchInfo::getSourceTotalPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (MathUtils.wrapBigDecimalCompare(sourceTotalPrice, batchTotalPrice) == 0) {
            return;
        }

        FlatPriceTool.flatPriceAndApply(sourceTotalPrice, batchInfos.stream()
                .filter(batchInfo -> batchInfo.getIsDeleted() == 0)
                .map(batchInfo -> new FlatPriceTool.IFlatCell<FlatPriceHelper.FlatPriceCell>() {
                    @Override
                    public FlatPriceHelper.FlatPriceCell genFlatCell() {
                        return new FlatPriceHelper.FlatPriceCell()
                                .setId(batchInfo.getId())
                                .setName(String.format("平摊item原价到批次上，batchId:%s", batchInfo.getBatchId()))
                                .setTotalPrice(batchInfo.getSourceTotalPrice())
                                .setPriority(FlatPriceHelper.PRIORITY_HIGH);
                    }

                    @Override
                    protected void apply(BigDecimal flatPrice) {

                        if (MathUtils.wrapBigDecimalCompare(batchInfo.getSourceTotalPrice(), flatPrice) == 0) {
                            return;
                        }

                        batchInfo.setSourceTotalPrice(flatPrice);
                        batchInfo.setTotalPrice(flatPrice);

                        /**
                         * 【【客户反馈】【陈金艳】无法结算，提示超限价；】https://www.tapd.cn/43780818/bugtrace/bugs/view?bug_id=1143780818001057520
                         */
                        //只有进价加成才处理单价，固定售价时不更新单价，用的就是goodsItem的定价
                        if (goodsItemIsMakeUp) {
                            MathUtils.CalculateExpectedUnitPriceResult calculateExpectedUnitPriceResult = MathUtils.calculateExpectedUnitPriceBase(batchInfo.getUnitCount(), BigDecimal.ONE, flatPrice, itemCalculateUnitPriceScale, 2);
                            batchInfo.setSourceUnitPrice(calculateExpectedUnitPriceResult.expectedUnitPrice);
                            batchInfo.setUnitPrice(calculateExpectedUnitPriceResult.expectedUnitPrice);
                        }
                    }

                }).collect(Collectors.toList())
        );
    }

    public static GoodsLockBatchItem generateGoodsLockBatchItem(ChargeFormItemBatchInfo chargeFormItemBatchInfo, ChargeFormItem chargeFormItem) {
        int useDismounting = chargeFormItem.getUseDismounting();
        GoodsLockBatchItem goodsLockBatchItem = new GoodsLockBatchItem();
        goodsLockBatchItem.setBatchId(Optional.ofNullable(chargeFormItemBatchInfo.getBatchId()).map(Long::parseLong).orElse(null));
        goodsLockBatchItem.setStockId(Optional.ofNullable(chargeFormItemBatchInfo.getStockId()).map(Long::parseLong).orElse(null));
        goodsLockBatchItem.setIsDeleted(0);
        goodsLockBatchItem.setBatchNo(chargeFormItemBatchInfo.getBatchNo());
        goodsLockBatchItem.setLockFormItemId(chargeFormItemBatchInfo.getChargeFormItemId());
        goodsLockBatchItem.setRefId(chargeFormItemBatchInfo.getId());
        goodsLockBatchItem.setGoodsId(chargeFormItemBatchInfo.getProductId());
        goodsLockBatchItem.setGoodsName(chargeFormItem.getName());
        goodsLockBatchItem.setLockingTotalPieceCount(chargeFormItemBatchInfo.getTotalBatchPieceCount());
        if (useDismounting == GoodsConst.DismountingStatus.PACKAGE) {
            goodsLockBatchItem.setLockingBatchPackageCount(chargeFormItemBatchInfo.getUnitCount());
        } else {
            goodsLockBatchItem.setLockingBatchPieceCount(chargeFormItemBatchInfo.getUnitCount());
        }
        goodsLockBatchItem.setUnitCostPrice(chargeFormItemBatchInfo.getUnitCostPrice());
        goodsLockBatchItem.setExpiryDate(chargeFormItemBatchInfo.getExpiryDate());

        return goodsLockBatchItem;
    }

    /**
     * 追加积分优惠
     *
     * @param itemBatchInfoProcessors
     * @param promotionPrice
     */
    public static BigDecimal flatPatientPointPromotionPrice(List<ItemBatchInfoProcessor> itemBatchInfoProcessors, BigDecimal promotionPrice) {

        if (CollectionUtils.isEmpty(itemBatchInfoProcessors)) {
            return BigDecimal.ZERO;
        }

        final BigDecimal[] batchPointPromotionPrice = {BigDecimal.ZERO};

        FlatPriceTool.flatPriceAndApply(promotionPrice, itemBatchInfoProcessors.stream()
                .map(itemBatchInfoProcessor -> new FlatPriceTool.IFlatCell<FlatPriceHelper.FlatPriceCell>() {
                    @Override
                    protected FlatPriceHelper.FlatPriceCell genFlatCell() {

                        ChargeFormItemBatchInfo chargeFormItemBatchInfo = itemBatchInfoProcessor.getChargeFormItemBatchInfo();

                        return new FlatPriceHelper.FlatPriceCell()
                                .setId(chargeFormItemBatchInfo.getId())
                                .setName("平摊批次积分")
                                .setTotalPrice(chargeFormItemBatchInfo.getSourceTotalPrice()
                                        .add(chargeFormItemBatchInfo.calculateSinglePromotionPrice())
                                        .add(chargeFormItemBatchInfo.calculatePackagePromotionPrice())
                                        .add(chargeFormItemBatchInfo.calculateUnitAdjustmentFee())
                                )
                                .setPriority(FlatPriceHelper.PRIORITY_HIGH);
                    }

                    @Override
                    protected void apply(BigDecimal flatPrice) {
                        itemBatchInfoProcessor.addPatientPointPromotionPrice(flatPrice);
                        batchPointPromotionPrice[0] = batchPointPromotionPrice[0].add(flatPrice);
                    }
                }).collect(Collectors.toList())
        );
        return batchPointPromotionPrice[0];
    }

    public static void flatUnitAdjustmentFeeByProcessor(List<ItemBatchInfoProcessor> itemBatchInfoProcessors, BigDecimal unitAdjustmentFee) {
        if (CollectionUtils.isEmpty(itemBatchInfoProcessors)) {
            return;
        }

        FlatPriceTool.flatPriceAndApply(unitAdjustmentFee, itemBatchInfoProcessors.stream()
                .map(itemBatchInfoProcessor -> new FlatPriceTool.IFlatCell<FlatPriceHelper.FlatPriceCell>() {

                    @Override
                    protected FlatPriceHelper.FlatPriceCell genFlatCell() {

                        ChargeFormItemBatchInfo chargeFormItemBatchInfo = itemBatchInfoProcessor.getChargeFormItemBatchInfo();

                        return new FlatPriceHelper.FlatPriceCell()
                                .setId(chargeFormItemBatchInfo.getId())
                                .setName("平摊批次单项议价")
                                .setTotalPrice(chargeFormItemBatchInfo.getSourceTotalPrice()
                                        .add(chargeFormItemBatchInfo.calculateSinglePromotionPrice())
                                )
                                .setPriority(FlatPriceHelper.PRIORITY_HIGH);
                    }

                    @Override
                    protected void apply(BigDecimal flatPrice) {
                        itemBatchInfoProcessor.addUnitAdjustmentFee(flatPrice);
                    }
                }).collect(Collectors.toList())
        );
    }


    public static void flatUnitAdjustmentFee(List<ChargeFormItemBatchInfo> chargeFormItemBatchInfos, BigDecimal unitAdjustmentFee) {
        if (CollectionUtils.isEmpty(chargeFormItemBatchInfos)) {
            return;
        }

        FlatPriceTool.flatPriceAndApply(unitAdjustmentFee, chargeFormItemBatchInfos.stream()
                .filter(chargeFormItemBatchInfo -> chargeFormItemBatchInfo.getIsDeleted() == 0)
                .map(batchInfo -> new FlatPriceTool.IFlatCell<FlatPriceHelper.FlatPriceCell>() {

                    @Override
                    protected FlatPriceHelper.FlatPriceCell genFlatCell() {

                        return new FlatPriceHelper.FlatPriceCell()
                                .setId(batchInfo.getId())
                                .setName("平摊批次单项议价")
                                .setTotalPrice(batchInfo.getSourceTotalPrice())
                                .setPriority(FlatPriceHelper.PRIORITY_HIGH);
                    }

                    @Override
                    protected void apply(BigDecimal flatPrice) {
                        batchInfo.addUnitAdjustmentFee(flatPrice);
                    }
                }).collect(Collectors.toList())
        );
    }

    public static void flatAdjustmentFee(List<ItemBatchInfoProcessor> itemBatchInfoProcessors, BigDecimal adjustmentPrice) {

        if (CollectionUtils.isEmpty(itemBatchInfoProcessors)) {
            return;
        }


        FlatPriceTool.flatPriceAndApply(adjustmentPrice, itemBatchInfoProcessors.stream()
                .map(itemBatchInfoProcessor -> new FlatPriceTool.IFlatCell<FlatPriceHelper.FlatPriceCell>() {

                    @Override
                    protected FlatPriceHelper.FlatPriceCell genFlatCell() {

                        ChargeFormItemBatchInfo chargeFormItemBatchInfo = itemBatchInfoProcessor.getChargeFormItemBatchInfo();

                        return new FlatPriceHelper.FlatPriceCell()
                                .setId(chargeFormItemBatchInfo.getId())
                                .setName("平摊批次整单议价")
                                .setTotalPrice(chargeFormItemBatchInfo.getSourceTotalPrice()
                                        .add(chargeFormItemBatchInfo.calculateSinglePromotionPrice())
                                        .add(chargeFormItemBatchInfo.calculatePackagePromotionPrice())
                                        .add(chargeFormItemBatchInfo.calculateUnitAdjustmentFee())
                                )
                                .setPriority(FlatPriceHelper.PRIORITY_HIGH);
                    }

                    @Override
                    protected void apply(BigDecimal flatPrice) {
                        itemBatchInfoProcessor.addAdjustmentFee(flatPrice);
                    }
                }).collect(Collectors.toList())
        );

    }

    public static ChargeDiscountInfo generateRefundPromotionInfo(ChargeFormItemBatchInfo paidBatchInfo,
                                                                 BigDecimal refundReceivablePrice,
                                                                 boolean isAllRefund,
                                                                 List<ChargeFormItemBatchInfo> refundBatchInfos) {

        List<ChargeDiscountInfo> refundPromotionInfos = Optional.ofNullable(refundBatchInfos).orElse(new ArrayList<>())
                .stream()
                .map(ChargeFormItemBatchInfo::getPromotionInfo)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        ChargeDiscountInfo refundPromotionInfo = ChargeTransactionRecordDiscountInfoHelper.mergeChargeTransactionRecordDiscountInfo(refundPromotionInfos);
        ChargeDiscountInfo leftToRefundPromotionInfo = ChargeTransactionRecordDiscountInfoHelper.mergeLeftPromotionInfo(paidBatchInfo.getPromotionInfo(), refundPromotionInfo);

        if (isAllRefund) {
            return leftToRefundPromotionInfo;
        }

        return ChargeTransactionRecordDiscountInfoHelper.flatBatchInfoPromotionInfo(
                        paidBatchInfo.getPromotionInfo(),
                        paidBatchInfo.getPromotionInfo(),
                        leftToRefundPromotionInfo,
                        refundReceivablePrice,
                        paidBatchInfo.getReceivablePrice(),
                        Optional.ofNullable(paidBatchInfo.getLimitInfo()).map(BaseLimitPriceInfo.LimitInfo::getExceedLimitPriceRule).orElse(YesOrNo.YES) == YesOrNo.NO)
                .getBatchPromotionInfo();
    }

    public static ChargeFormItemBatchInfo generateRefundChargeFormItemBatchInfo(ChargeFormItemBatchInfo canRefundBatchInfo,
                                                                                String addedRefundChargeFormItemId,
                                                                                BigDecimal thisRefundCount,
                                                                                List<ChargeFormItemBatchInfo> existedRefundedBatchInfos,
                                                                                int chargeVersion,
                                                                                String operatorId) {

        if (Objects.isNull(canRefundBatchInfo) || MathUtils.wrapBigDecimalCompare(thisRefundCount, BigDecimal.ZERO) <= 0) {
            return null;
        }
        ChargeFormItemBatchInfo addedRefundBatchInfo = new ChargeFormItemBatchInfo();
        BeanUtils.copyProperties(canRefundBatchInfo, addedRefundBatchInfo);
        addedRefundBatchInfo.setId(AbcIdUtils.getUID());
        addedRefundBatchInfo.setBatchId(canRefundBatchInfo.getBatchId());
        addedRefundBatchInfo.setChargeFormItemId(addedRefundChargeFormItemId);
        addedRefundBatchInfo.setAssociateItemBatchInfoId(canRefundBatchInfo.getId());
        addedRefundBatchInfo.setRefundTotalPrice(BigDecimal.ZERO);
        addedRefundBatchInfo.setRefundUnitCount(BigDecimal.ZERO);
        addedRefundBatchInfo.setUnitCount(thisRefundCount);
        addedRefundBatchInfo.setReceivedPrice(BigDecimal.ZERO);

        boolean isAllRefund = MathUtils.wrapBigDecimalCompare(canRefundBatchInfo.getLeftCanRefundCount(), thisRefundCount) == 0;
        //先计算应收、跟记录统计明细的算法保持一致，计算了应收再计算promotionInfo，再反推sourceTotalPrice和totalCostPrice
        BigDecimal totalPriceAllRefund = BigDecimal.ZERO;
        BigDecimal refundReceivablePrice = BigDecimal.ZERO;
        BigDecimal totalCostPrice = BigDecimal.ZERO;
        BigDecimal canRefundReceivablePrice = MathUtils.max(BigDecimal.ZERO, MathUtils.wrapBigDecimalSubtract(canRefundBatchInfo.getReceivablePrice(),
                Optional.ofNullable(existedRefundedBatchInfos).orElse(new ArrayList<>())
                        .stream()
                        .map(ChargeFormItemBatchInfo::getReceivablePrice)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)));
        if (isAllRefund) {
            refundReceivablePrice = canRefundReceivablePrice;
            totalPriceAllRefund = MathUtils.wrapBigDecimalSubtract(canRefundBatchInfo.getTotalPrice(), canRefundBatchInfo.getRefundTotalPrice());
            totalCostPrice = MathUtils.wrapBigDecimalSubtract(canRefundBatchInfo.getTotalCostPrice(), Optional.ofNullable(existedRefundedBatchInfos)
                    .orElse(new ArrayList<>())
                    .stream()
                    .map(ChargeFormItemBatchInfo::getTotalCostPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
            );
        } else {
            refundReceivablePrice = MathUtils.min(canRefundReceivablePrice, addedRefundBatchInfo.getUnitCount().multiply(canRefundBatchInfo.getReceivablePrice()).divide(canRefundBatchInfo.getUnitCount(), 2, RoundingMode.HALF_UP));

            totalCostPrice = MathUtils.calculateTotalPrice(canRefundBatchInfo.getUnitCostPrice(), addedRefundBatchInfo.getUnitCount()).setScale(2, RoundingMode.HALF_DOWN);
        }

        addedRefundBatchInfo.setPromotionInfo(ChargeFormItemBatchInfoUtils.generateRefundPromotionInfo(canRefundBatchInfo, refundReceivablePrice, isAllRefund, existedRefundedBatchInfos));


        /**
         * 单项优惠
         * 单项议价
         * 整单优惠
         * 整单议价
         */
        BigDecimal totalPrice = BigDecimal.ZERO;
        BigDecimal sourceTotalPrice = refundReceivablePrice.subtract(addedRefundBatchInfo.calculateSinglePromotionPrice())
                .subtract(addedRefundBatchInfo.calculateUnitAdjustmentFee())
                .subtract(addedRefundBatchInfo.calculatePackagePromotionPrice())
                .subtract(addedRefundBatchInfo.calculateAdjustmentFee())
                .subtract(addedRefundBatchInfo.calculateListingDiscountFee());

        if (chargeVersion == ChargeVersionConstants.V0) {
            BigDecimal totalPriceLimitFee = BigDecimal.ZERO;
            if (Optional.ofNullable(canRefundBatchInfo.getLimitInfo()).map(BaseLimitPriceInfo.LimitInfo::getExceedLimitPriceRule).orElse(YesOrNo.YES) == YesOrNo.NO) {
                totalPriceLimitFee = addedRefundBatchInfo.calculateLimitFee();
                sourceTotalPrice = sourceTotalPrice.subtract(totalPriceLimitFee);
            }

            totalPrice = MathUtils.wrapBigDecimalAdd(sourceTotalPrice, addedRefundBatchInfo.calculateUnitAdjustmentFee()).add(totalPriceLimitFee).add(addedRefundBatchInfo.calculateListingDiscountFee());
        } else {
            // todo 药店引入医保限价需要考虑这里的计算问题
            totalPrice = MathUtils.wrapBigDecimalAdd(sourceTotalPrice, addedRefundBatchInfo.calculateSinglePromotionPrice(), addedRefundBatchInfo.calculateUnitAdjustmentFee());
            if (isAllRefund) {
                //因为历史原因，之前的批次上的totalPrice没有清晰的定义，导致这个值不准确，先用这个方案做一下兜底，后续等进价加成全量稳定后，再放开
                totalPrice = totalPriceAllRefund;
            }
        }

        if (isAllRefund && MathUtils.wrapBigDecimalCompare(totalPrice, totalPriceAllRefund) != 0) {
            log.error("totalPrice价格错误， totalPrice: {}, totalPriceAllRefund: {},  promotionInfo: {}", totalPrice, totalPriceAllRefund, JsonUtils.dump(addedRefundBatchInfo.getPromotionInfo()));
            throw new IllegalStateException("totalPrice价格错误");
        }

        /**
         * 如果没有单项议价  原价=totalPrice  如果有单项议价  原价 + 单项议价 = totalPrice
         * 如果有整单议价   原价 + 整单（大于0的值） = totalPrice
         */

        if (MathUtils.wrapBigDecimalCompare(sourceTotalPrice, BigDecimal.ZERO) < 0) {
            log.error("sourceTotalPrice不能小于0， sourceTotalPrice: {}, promotionInfo: {}", sourceTotalPrice, JsonUtils.dump(addedRefundBatchInfo.getPromotionInfo()));
            throw new IllegalStateException("sourceTotalPrice不能小于0");
        }

        addedRefundBatchInfo.setTotalPrice(totalPrice);
        addedRefundBatchInfo.setSourceTotalPrice(sourceTotalPrice);
        addedRefundBatchInfo.setReceivablePrice(refundReceivablePrice);
        addedRefundBatchInfo.setTotalCostPrice(totalCostPrice);
        FillUtils.fillCreatedBy(addedRefundBatchInfo, operatorId);

        return addedRefundBatchInfo;
    }

    /**
     * 平摊单品优惠
     *
     * @param itemBatchInfoProcessors
     */
    public static void flatSinglePromotionToBatchInfo(List<ItemBatchInfoProcessor> itemBatchInfoProcessors, ChargeDiscountInfo parentPromotionInfo, BigDecimal parentSourceTotalPrice) {

        if (CollectionUtils.isEmpty(itemBatchInfoProcessors)) {
            return;
        }

        if (Objects.isNull(parentPromotionInfo)) {
            itemBatchInfoProcessors.forEach(ItemBatchInfoProcessor::clearSinglePromotionInfo);
            return;
        }

        itemBatchInfoProcessors.forEach(itemBatchInfoProcessor -> itemBatchInfoProcessor.flatSinglePromotionInfo(parentPromotionInfo, parentSourceTotalPrice));

    }

    public static void flatPackagePromotionToBatchInfo(List<ItemBatchInfoProcessor> itemBatchInfoProcessors, ChargeDiscountInfo parentPromotionInfo, BigDecimal parentPrice) {
        if (CollectionUtils.isEmpty(itemBatchInfoProcessors)) {
            return;
        }

        if (Objects.isNull(parentPromotionInfo)) {
            itemBatchInfoProcessors.forEach(ItemBatchInfoProcessor::clearPackagePromotionInfo);
            return;
        }

        itemBatchInfoProcessors.forEach(itemBatchInfoProcessor -> itemBatchInfoProcessor.flatPackagePromotionInfo(parentPromotionInfo, parentPrice));

    }

    public static void mergeRefundChargeFormItemBatchInfos(List<ChargeFormItemBatchInfo> batchInfos1, List<ChargeFormItemBatchInfo> batchInfos2) {

        if (CollectionUtils.isEmpty(batchInfos1) || CollectionUtils.isEmpty(batchInfos2)) {
            return;
        }


        Map<String, ChargeFormItemBatchInfo> batchInfos2Map = batchInfos2.stream()
                .filter(chargeFormItemBatchInfo -> StringUtils.isNotEmpty(chargeFormItemBatchInfo.getAssociateItemBatchInfoId()))
                .collect(Collectors.toMap(ChargeFormItemBatchInfo::getAssociateItemBatchInfoId, Function.identity()));

        batchInfos1
                .stream()
                .filter(chargeFormItemBatchInfo -> StringUtils.isNotEmpty(chargeFormItemBatchInfo.getAssociateItemBatchInfoId()))
                .forEach(batchInfo1 -> {
                    ChargeFormItemBatchInfo batchInfo2 = batchInfos2Map.get(batchInfo1.getAssociateItemBatchInfoId());
                    if (Objects.nonNull(batchInfo2)) {
                        batchInfo1.setUnitCount(MathUtils.wrapBigDecimalAdd(batchInfo1.getUnitCount(), batchInfo2.getUnitCount()));
                        batchInfo1.setTotalPrice(MathUtils.wrapBigDecimalAdd(batchInfo1.getTotalPrice(), batchInfo2.getTotalPrice()));
                        batchInfo1.setSourceTotalPrice(MathUtils.wrapBigDecimalAdd(batchInfo1.getSourceTotalPrice(), batchInfo2.getSourceTotalPrice()));
                    }
                });
    }

    public static boolean canFlatBatchInfoPrice(ChargeFormItem chargeFormItem) {

        if (CollectionUtils.isEmpty(chargeFormItem.getChargeFormItemBatchInfos())) {
            return false;
        }

        //比较批次的数量是否和item的数量一致，一致才去平摊
        BigDecimal batchTotalCount = chargeFormItem.getChargeFormItemBatchInfos().stream()
                .filter(chargeFormItemBatchInfo -> chargeFormItemBatchInfo.getIsDeleted() == 0)
                .map(ChargeFormItemBatchInfo::getUnitCount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal itemTotalCount = MathUtils.calculateTotalCount(chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount());

        return MathUtils.wrapBigDecimalCompare(itemTotalCount, batchTotalCount) == 0;
    }


    /**
     * 诊所管家平摊积分到批次上
     *
     * @param chargeFormItem
     * @param itemBatchInfoProcessors
     * @param pointPromotionPrice
     */
    public static void flatPointPromotionPriceToBatchInfo(ChargeFormItem chargeFormItem,
                                                          List<ItemBatchInfoProcessor> itemBatchInfoProcessors,
                                                          BigDecimal pointPromotionPrice) {

        if (!canFlatBatchInfoPrice(chargeFormItem)) {
            return;
        }

        BigDecimal batchPointPromotionPrice = flatPatientPointPromotionPrice(itemBatchInfoProcessors, pointPromotionPrice);


        if (MathUtils.wrapBigDecimalCompare(batchPointPromotionPrice, pointPromotionPrice) != 0) {
            log.info("批次积分优惠平摊失败，batchPointPromotionPrice: {}, pointPromotionPrice: {}", batchPointPromotionPrice, pointPromotionPrice);
            throw new IllegalStateException("批次折扣平摊失败");
        }
    }

    /**
     * 将item的营销对象平摊到批次上
     *
     * @param chargeFormItem
     * @param itemBatchInfoProcessors
     * @param itemPromotionInfo
     * @param itemTotalPrice
     * @param promotionPrice
     */
    public static void flatAllPromotionToBatchInfo(ChargeFormItem chargeFormItem,
                                                   List<ItemBatchInfoProcessor> itemBatchInfoProcessors,
                                                   ChargeDiscountInfo itemPromotionInfo,
                                                   BigDecimal itemTotalPrice,
                                                   BigDecimal promotionPrice,
                                                   boolean resetWithPromotion) {

        if (!canFlatBatchInfoPrice(chargeFormItem)) {
            return;
        }

        //计算分母 = itemTotalPrice - 抵扣金额
        BigDecimal allDeductedPrice = Optional.ofNullable(itemPromotionInfo)
                .map(chargeDiscountInfo -> {
                    BigDecimal deductPromotionPrice = chargeDiscountInfo.getDeductPromotionPrice();
                    BigDecimal pointDeductPromotionPrice = chargeDiscountInfo.getPointDeductPromotionPrice();
                    return MathUtils.wrapBigDecimalAdd(deductPromotionPrice, pointDeductPromotionPrice);
                })
                .orElse(BigDecimal.ZERO);

        //用于批次平摊优惠的分母
        BigDecimal batchFlatPromotionDenominator = MathUtils.wrapBigDecimalAdd(itemTotalPrice, allDeductedPrice);

        //校验摊费是否全部摊完，累加批次平摊下去的所有promotionPrice
        BigDecimal batchPromotionTotalPrice = itemBatchInfoProcessors.stream()
                .map(itemBatchInfoProcessor -> itemBatchInfoProcessor.flatAndSetPromotion(itemPromotionInfo, batchFlatPromotionDenominator, resetWithPromotion))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (MathUtils.wrapBigDecimalCompare(batchPromotionTotalPrice, promotionPrice) != 0) {
            log.info("批次折扣平摊失败，batchPromotionTotalPrice: {}, promotionPrice: {}", batchPromotionTotalPrice, promotionPrice);
            throw new IllegalStateException("批次折扣平摊失败");
        }
    }

    public static GoodsBatchInfo convertCommonBatchInfoToSdkBatchInfo(cn.abcyun.cis.commons.rpc.goods.GoodsBatchInfo commonGoodsBatchInfo) {

        if (Objects.isNull(commonGoodsBatchInfo)) {
            return null;
        }

        GoodsBatchInfo goodsBatchInfo = new GoodsBatchInfo();
        BeanUtils.copyProperties(commonGoodsBatchInfo, goodsBatchInfo);
        return goodsBatchInfo;
    }


    /**
     * 进销存有效期存在脏数据
     */
    public static String dealBatchExpireDate(String batchExpireDate) {
        if (StringUtils.isBlank(batchExpireDate)) {
            return null;
        }
        String trimExpireDate = batchExpireDate.trim();
        if (trimExpireDate.length() <= 10) {
            return trimExpireDate;
        }
        return trimExpireDate.substring(0, 10);
    }

    public static ChargeFormItemBatchInfo copyForDirectSale(ChargeFormItemBatchInfo sourceItemBatch) {
        if (Objects.isNull(sourceItemBatch)) {
            return null;
        }

        ChargeFormItemBatchInfo copiedBatchInfo = new ChargeFormItemBatchInfo();
        copiedBatchInfo.setExpiryDate(sourceItemBatch.getExpiryDate());
        copiedBatchInfo.setClinicId(sourceItemBatch.getClinicId());
        copiedBatchInfo.setChainId(sourceItemBatch.getChainId());
        copiedBatchInfo.setUnitCostPrice(sourceItemBatch.getUnitCostPrice());
        copiedBatchInfo.setTotalCostPrice(sourceItemBatch.getTotalCostPrice());
        copiedBatchInfo.setTotalPrice(sourceItemBatch.getTotalPrice());
        copiedBatchInfo.setSourceTotalPrice(sourceItemBatch.getSourceTotalPrice());
        copiedBatchInfo.setSourceUnitPrice(sourceItemBatch.getSourceUnitPrice());
        copiedBatchInfo.setUnitCount(sourceItemBatch.getUnitCount());
        copiedBatchInfo.setTotalBatchPieceCount(sourceItemBatch.getTotalBatchPieceCount());
        copiedBatchInfo.setUnitPrice(sourceItemBatch.getUnitPrice());
        copiedBatchInfo.setProductId(sourceItemBatch.getProductId());
        copiedBatchInfo.setStockId(sourceItemBatch.getStockId());
        copiedBatchInfo.setBatchId(sourceItemBatch.getBatchId());
        copiedBatchInfo.setBatchNo(sourceItemBatch.getBatchNo());

        return copiedBatchInfo;
    }
}
