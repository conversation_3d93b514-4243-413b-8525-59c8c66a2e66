package cn.abcyun.cis.charge.util;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.commons.rpc.registration.RegistrationCategory;
import org.apache.commons.lang3.tuple.Pair;


public class RegistrationUtils {

    public static Pair<String, Integer> registrationCategoryConvertToGoodsInfo(int registrationCategory) {

        Integer goodsSubType = GoodsConst.RegistrationSubType.GENERAL_OUTPATIENT;
        String goodsId = Constants.SystemProductId.REGISTRATION_PRODUCT_ID;

        if (registrationCategory == RegistrationCategory.CONVENIENT_OUTPATIENT) {
            goodsSubType = GoodsConst.RegistrationSubType.CONVENIENT_OUTPATIENT;
            goodsId = Constants.SystemProductId.CONVENIENT_REGISTRATION_PRODUCT_ID;
        } else if (registrationCategory == RegistrationCategory.EXPERT_OUTPATIENT) {
            goodsSubType = GoodsConst.RegistrationSubType.EXPERT_OUTPATIENT;
            goodsId = Constants.SystemProductId.EXPERT_PROCESS_PILL_ENRICHMENT_PRODUCT_ID;
        }


        return Pair.of(goodsId, goodsSubType);
    }
}



