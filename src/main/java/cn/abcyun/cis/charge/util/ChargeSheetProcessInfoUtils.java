package cn.abcyun.cis.charge.util;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.cis.charge.api.model.ChargeRuleLadderInfoVo;
import cn.abcyun.cis.charge.api.model.ChargeRuleProcessUsageView;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.factory.chargeform.ChargeFormFactory;
import cn.abcyun.cis.charge.model.*;
import cn.abcyun.cis.charge.service.dto.UsageTypeInfo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

public class ChargeSheetProcessInfoUtils {

    /**
     * 合并两个加工费信息
     *
     * @param processInfoReqs 终端传递上的计费单里面的加工费信息
     *                        chargeSheet 里面的加工费信息
     * @return 把合并后的加工费信息存在到chargeSheet.getChargeSheetProcessInfos
     */
//    public static void insertOrUpdateChargeSheetProcessInfo(ChargeSheet chargeSheet,
//                                                            List<ProcessInfoReq> processInfoReqs,
//                                                            String operatorId) {
//
//        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED
//                || chargeSheet == null
//                || CollectionUtils.isEmpty(processInfoReqs)) {
//            return;
//        }
//
//        List<ChargeSheetProcessInfo> processInfos = chargeSheet.getChargeSheetProcessInfos();
//        if (processInfos == null) {
//            processInfos = new ArrayList<>();
//            chargeSheet.setChargeSheetProcessInfos(processInfos);
//        }
//        //判断两个加工费是否是同一个的函数
//        BiFunction<ProcessInfoReq, ChargeSheetProcessInfo, Boolean> isEqualKeyFunc = (processInfoReq, processInfo) -> TextUtils.equals(processInfo.getId(), processInfoReq.getId());
//        //插入
//        Function<ProcessInfoReq, ChargeSheetProcessInfo> insertFunc = processInfoReq -> {
//            ChargeSheetProcessInfo processInfo = insertChargeSheetProcessInfo(chargeSheet, processInfoReq, operatorId);
//            return processInfo;
//        };
//
//        Function<ChargeSheetProcessInfo, Boolean> deleteFunc = chargeForm -> true;
//
//        BiConsumer<ProcessInfoReq, ChargeSheetProcessInfo> updateFunc = (processInfoReq, processInfo) -> updateChargeSheetProcessInfo(processInfoReq, processInfo, operatorId);
//
//        MergeTool<ProcessInfoReq, ChargeSheetProcessInfo> mergeTool = new MergeTool<>();
//        mergeTool.setSrc(processInfoReqs)
//                .setDst(processInfos)
//                .setIsEqualKeyFunc(isEqualKeyFunc)
//                .setInsertFunc(insertFunc)
//                .setDeleteFunc(deleteFunc)
//                .setUpdateFunc(updateFunc);
//        mergeTool.doMerge();
//    }


    public static String getRuleInfo(ChargeRuleProcessUsageView processUsageView) {
        String ruleInfo = null;
        if (processUsageView == null) {
            return null;
        }

        ruleInfo = getRuleInfo(processUsageView.getRuleType(), processUsageView.getPrice(), processUsageView.getLadderInfo());
        if (!StringUtils.isEmpty(ruleInfo) && processUsageView.getOverFulfilSwitch() != null && processUsageView.getOverFulfilSwitch() == 1) {

            String overRuleInfo = getOverRuleInfo(processUsageView.getOverFulfilUnitCount(), processUsageView.getOverFulfilAdditionalCount(), processUsageView.getOverFulfilAdditionalPrice());

            if (!StringUtils.isEmpty(overRuleInfo)) {
                ruleInfo = ruleInfo + overRuleInfo;
            }
        }
        return ruleInfo;
    }

    public static String getFreePostageRuleInfo(int freePostageType, BigDecimal freePostageUnitCount, String freePostageUnit) {

        if (freePostageUnitCount == null || StringUtils.isEmpty(freePostageUnit)) {
            return null;
        }

        String freePostageRuleInfo = null;

        switch (freePostageType) {
            case ChargeRuleExpressDelivery.FreePostageType.MEDICINE_FEE:
                freePostageRuleInfo = String.format("药品费用满%s%s包邮", freePostageUnitCount.stripTrailingZeros().toPlainString(), freePostageUnit);
                break;
            case ChargeRuleExpressDelivery.FreePostageType.MEDICINE_DOSAGE:
                freePostageRuleInfo = String.format("药品剂数满%s%s包邮", freePostageUnitCount.stripTrailingZeros().toPlainString(), freePostageUnit);
                break;
            case ChargeRuleExpressDelivery.FreePostageType.MEDICINE_WEIGHT:
                freePostageRuleInfo = String.format("药品重量满%s%s包邮", freePostageUnitCount.stripTrailingZeros().toPlainString(), freePostageUnit);
                break;
            case ChargeRuleExpressDelivery.FreePostageType.TOTAL_PRICE:
                freePostageRuleInfo = String.format("订单金额（不含邮费）满%s%s包邮", freePostageUnitCount.stripTrailingZeros().toPlainString(), freePostageUnit);
                break;
            default:
                freePostageRuleInfo = "";
        }

        return freePostageRuleInfo;
    }

    public static String getOverRuleInfo(Integer overFulfilUnitCount, Integer overFulfilAdditionalCount, BigDecimal overFulfilAdditionalPrice) {
        String overRuleInfo = null;

        if (overFulfilUnitCount == null || overFulfilAdditionalCount == null || overFulfilAdditionalPrice == null) {
            return null;
        }
        overRuleInfo = String.format("（超额：一剂%s袋内免费，每加%s袋加%s元）", overFulfilUnitCount, overFulfilAdditionalCount, overFulfilAdditionalPrice.stripTrailingZeros().toPlainString());
        return overRuleInfo;
    }

    public static String getRuleInfo(int type, BigDecimal price, ChargeRuleLadderInfoVo chargeRuleLadderInfo) {
        String ruleInfo = "";
        if (type == Constants.ChargeRuleType.SETTLED_RULE) {
            ruleInfo = String.format("每个处方%s元", MathUtils.wrapBigDecimalOrZero(price).stripTrailingZeros().toPlainString());
        } else if (type == Constants.ChargeRuleType.LADDER_RULE) {
            if (chargeRuleLadderInfo != null) {

                switch (chargeRuleLadderInfo.getType()) {
                    case ChargeRuleLadderInfo.Type.PRESCRIPTION_DOSAGE:
                        ruleInfo = String.format("处方%s%s内%s元，每加%s%s加%s元", chargeRuleLadderInfo.getUnitCount().stripTrailingZeros().toPlainString(), chargeRuleLadderInfo.getUnit(), chargeRuleLadderInfo.getPrice().stripTrailingZeros().toPlainString(), chargeRuleLadderInfo.getAdditionalCount().stripTrailingZeros().toPlainString(), chargeRuleLadderInfo.getUnit(), chargeRuleLadderInfo.getAdditionalPrice().stripTrailingZeros().toPlainString());
                        break;
                    case ChargeRuleLadderInfo.Type.MEDICINE_WEIGHT:
                        ruleInfo = String.format("重量%s%s内%s元，每加%s%s加%s元", chargeRuleLadderInfo.getUnitCount().stripTrailingZeros().toPlainString(), chargeRuleLadderInfo.getUnit(), chargeRuleLadderInfo.getPrice().stripTrailingZeros().toPlainString(), chargeRuleLadderInfo.getAdditionalCount().stripTrailingZeros().toPlainString(), chargeRuleLadderInfo.getUnit(), chargeRuleLadderInfo.getAdditionalPrice().stripTrailingZeros().toPlainString());
                        break;
                    case ChargeRuleLadderInfo.Type.PROCESS_BAG:
                        ruleInfo = String.format("袋数%s%s内%s元，每加%s%s加%s元", chargeRuleLadderInfo.getUnitCount().stripTrailingZeros().toPlainString(), chargeRuleLadderInfo.getUnit(), chargeRuleLadderInfo.getPrice().stripTrailingZeros().toPlainString(), chargeRuleLadderInfo.getAdditionalCount().stripTrailingZeros().toPlainString(), chargeRuleLadderInfo.getUnit(), chargeRuleLadderInfo.getAdditionalPrice().stripTrailingZeros().toPlainString());
                        break;
                    default:
                        ruleInfo = "";
                }
            }

        }
        return ruleInfo;
    }

    public static String convertToProcessUsage(List<UsageTypeInfo> usageTypeInfosStatic, int type, int subType) {

        if (usageTypeInfosStatic == null) {
            return null;
        }

        UsageTypeInfo usageTypeInfo = usageTypeInfosStatic.stream().filter(u -> u.getType() == type && u.getSubType() == subType).findFirst().orElse(null);

        return usageTypeInfo != null ? usageTypeInfo.getName() : null;
    }

    public static void updateChargeSheetProcessFormId(ChargeSheet existedChargeSheet) {

        if (existedChargeSheet == null || existedChargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED || CollectionUtils.isEmpty(existedChargeSheet.getChargeForms())) {
            return;
        }

        if (
                (existedChargeSheet.getType() == ChargeSheet.Type.OUTPATIENT && existedChargeSheet.getOutpatientStatus() == ChargeSheet.OutpatientStatus.WAITING)
                        || (existedChargeSheet.getClonePrescriptionType() == ChargeSheetAdditional.ClonePrescriptionType.FROM_PHOTO_BY_OUTPATIENT_DOCTOR)
                        || existedChargeSheet.getType() != ChargeSheet.Type.OUTPATIENT
        ) {
            baseUpdateChargeSheetProcessFormId(existedChargeSheet);
        }

    }

    public static void baseUpdateChargeSheetProcessFormId(ChargeSheet existedChargeSheet) {
        List<String> prescriptionChineseFormIds = cn.abcyun.cis.commons.util.ListUtils.alwaysList(existedChargeSheet.getChargeForms())
                .stream()
                .filter(Objects::nonNull)
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE && chargeForm.getPharmacyType() == GoodsConst.PharmacyType.LOCAL_PHARMACY)
                .map(ChargeForm::getId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(prescriptionChineseFormIds)) {
            return;
        }

        //先将加工费的关系处理干净，保留正确的关系，移除错误的关系，重新绑定
        cn.abcyun.cis.commons.util.ListUtils.alwaysList(existedChargeSheet.getChargeForms())
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.PROCESS
                        && chargeForm.getProcessInfo() != null
                        && org.apache.commons.lang3.StringUtils.isNotEmpty(chargeForm.getProcessInfo().getChargeFormId())
                )
                .forEach(chargeForm -> {
                    String chargeFormId = chargeForm.getProcessInfo().getChargeFormId();

                    if (prescriptionChineseFormIds.size() > 0 && prescriptionChineseFormIds.contains(chargeFormId)) {
                        prescriptionChineseFormIds.remove(chargeFormId);
                    } else {
                        chargeForm.getProcessInfo().setChargeFormId(null);
                    }
                });

        if (CollectionUtils.isEmpty(prescriptionChineseFormIds)) {
            return;
        }

        //重新绑定还没有关联关系的加工费和中药处方的关系
        cn.abcyun.cis.commons.util.ListUtils.alwaysList(existedChargeSheet.getChargeForms())
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.PROCESS
                        && chargeForm.getProcessInfo() != null
                        && org.apache.commons.lang3.StringUtils.isEmpty(chargeForm.getProcessInfo().getChargeFormId())
                )
                .forEach(chargeForm -> {
                    if (prescriptionChineseFormIds.size() == 0) {
                        return;
                    }

                    chargeForm.getProcessInfo().setChargeFormId(prescriptionChineseFormIds.remove(0));
                });
    }

    /**
     * 根据chargeSheetProcessInfos找对应的加工费做新增或更新操作
     *
     * @param chargeSheet
     * @param chargeSheetProcessInfos
     */
    public static void insertOrUpdateChargeSheetProcessForm(ChargeSheet chargeSheet, List<ChargeSheetProcessInfo> chargeSheetProcessInfos, String operatorId) {

        if (chargeSheet == null || CollectionUtils.isEmpty(chargeSheet.getChargeForms())) {
            return;
        }

        if (CollectionUtils.isEmpty(chargeSheetProcessInfos)) {
            chargeSheet.getChargeForms()
                    .stream()
                    .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                    .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.PROCESS)
                    .forEach(chargeForm -> chargeForm.deleteModel(operatorId));
            return;
        }

        Map<String, ChargeSheetProcessInfo> processInfoMap = ListUtils.toMap(chargeSheetProcessInfos, ChargeSheetProcessInfo::getChargeFormId);

        //删除多余的加工费form
        chargeSheet.getChargeForms()
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.PROCESS
                        && chargeForm.getProcessInfo() != null
                        && !processInfoMap.keySet().contains(chargeForm.getProcessInfo().getChargeFormId()))
                .forEach(chargeForm -> chargeForm.deleteModel(operatorId));

        //需要新增的加工费form
        List<ChargeForm> needInsertProcessForms = new ArrayList<>();

        //修改已存在的chargeForm
        Map<String, ChargeForm> processChargeFormMap = chargeSheet.getChargeForms().stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.PROCESS)
                .filter(chargeForm -> chargeForm.getProcessInfo() != null)
                .collect(Collectors.toMap(chargeForm -> chargeForm.getProcessInfo().getChargeFormId(), Function.identity()));

        chargeSheetProcessInfos.forEach(processInfo -> {

            ChargeForm processChargeForm = processChargeFormMap.getOrDefault(processInfo.getChargeFormId(), null);

            if (processChargeForm != null) {

                ChargeSheetProcessInfo serverProcessInfo = processChargeForm.getProcessInfo();
                serverProcessInfo.setType(processInfo.getType())
                        .setSubType(processInfo.getSubType())
                        .setBagUnitCount(processInfo.getBagUnitCount())
                        .setBagUnitCountDecimal(processInfo.getBagUnitCountDecimal())
                        .setTotalProcessCount(processInfo.getTotalProcessCount())
                        .setProcessRemark(processInfo.getProcessRemark())
                        .setTakeMedicationTime(processInfo.getTakeMedicationTime());

                FillUtils.fillLastModifiedBy(serverProcessInfo, operatorId);
            } else {
                processChargeForm = ChargeFormFactory.createProcessForm(chargeSheet, processInfo, operatorId);
                needInsertProcessForms.add(processChargeForm);
            }
        });

        chargeSheet.getChargeForms().addAll(needInsertProcessForms);
    }
}
