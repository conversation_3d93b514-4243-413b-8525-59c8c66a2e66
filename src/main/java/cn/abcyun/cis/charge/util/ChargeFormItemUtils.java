package cn.abcyun.cis.charge.util;

import cn.abcyun.bis.rpc.sdk.cis.model.charge.GoodsFeeType;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsItem;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.TraceableCode;
import cn.abcyun.bis.rpc.sdk.cis.model.goodslocking.GoodsLockBatchItem;
import cn.abcyun.bis.rpc.sdk.cis.model.goodslocking.GoodsLockingFormItem;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.model.BatchTraceableCode;
import cn.abcyun.cis.charge.model.ChargeFormItem;
import cn.abcyun.cis.charge.model.ChargeFormItemBatchInfo;
import cn.abcyun.cis.charge.service.dto.ChargeFormItemBatchInfoView;
import cn.abcyun.cis.charge.service.dto.ChargeFormItemView;
import cn.abcyun.cis.charge.service.dto.print.ChargeFormItemPrintView;
import cn.abcyun.cis.commons.model.ComposeType;
import cn.abcyun.cis.commons.rpc.goods.GoodsSnap;
import cn.abcyun.cis.commons.rpc.registration.RegistrationFormItemFeeDetail;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.http.util.Asserts;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class ChargeFormItemUtils {

    @Data
    @Accessors(chain = true)
    public static class PrintUnitPriceAndPrintTotalPriceResult {

        private BigDecimal printUnitPrice;

        private BigDecimal printTotalPrice;

    }

    public static PrintUnitPriceAndPrintTotalPriceResult generatePrintUnitPriceAndPrintTotalPrice(ChargeFormItem chargeFormItem, int chargeSheetStatus) {
        Asserts.notNull(chargeFormItem, "chargeFormItem cannot be null");

        BigDecimal printUnitPrice = BigDecimal.ZERO;
        BigDecimal printTotalPrice = BigDecimal.ZERO;
        //已收和部分退时打印应收金额
        if (chargeSheetStatus == Constants.ChargeSheetStatus.CHARGED || chargeSheetStatus == Constants.ChargeSheetStatus.PART_REFUNDED) {
            printTotalPrice = chargeFormItem.calculateDiscountedPrice().subtract(cn.abcyun.cis.commons.util.MathUtils.wrapBigDecimalAdd(chargeFormItem.getRefundTotalPrice(), chargeFormItem.getRefundDiscountPrice()));
            BigDecimal totalCount = cn.abcyun.cis.commons.util.MathUtils.calculateTotalCount(chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount());
            if (cn.abcyun.cis.commons.util.MathUtils.wrapBigDecimalCompare(totalCount, BigDecimal.ZERO) > 0) {
                printUnitPrice = chargeFormItem.calculateDiscountedPrice().divide(totalCount, 2, RoundingMode.DOWN);
            }
        } else {
            printUnitPrice = chargeFormItem.getUnitPrice();
            printTotalPrice = cn.abcyun.cis.commons.util.MathUtils.wrapBigDecimalSubtract(chargeFormItem.getTotalPrice(), chargeFormItem.getRefundTotalPrice());
        }

        return new PrintUnitPriceAndPrintTotalPriceResult()
                .setPrintUnitPrice(printUnitPrice)
                .setPrintTotalPrice(printTotalPrice);

    }

    @Data
    @Accessors(chain = true)
    public static class ChargeFormItemUnitPriceDto {

        private BigDecimal unitPrice;

        private BigDecimal discountedUnitPrice;

    }

    public static ChargeFormItemUnitPriceDto calculateItemUnitPriceAndDiscountedUnitPriceV1(BigDecimal sourceUnitPrice,
                                                                                            BigDecimal totalCount,
                                                                                            BigDecimal totalPrice,
                                                                                            BigDecimal discountedTotalPrice,
                                                                                            BigDecimal singlePromotionFee,
                                                                                            BigDecimal packagePromotionFee,
                                                                                            int productType,
                                                                                            int productSubType
    ) {
        BigDecimal unitPriceResult = BigDecimal.ZERO;
        BigDecimal discountedUnitPriceResult = BigDecimal.ZERO;
        if (MathUtils.wrapBigDecimalCompare(singlePromotionFee, BigDecimal.ZERO) == 0
                && MathUtils.wrapBigDecimalCompare(packagePromotionFee, BigDecimal.ZERO) == 0) {
            unitPriceResult = sourceUnitPrice;
            if (MathUtils.wrapBigDecimalCompare(totalCount, BigDecimal.ZERO) > 0) {
                discountedUnitPriceResult = discountedTotalPrice.divide(totalCount, 2, RoundingMode.HALF_UP);
            }
        } else if (MathUtils.wrapBigDecimalCompare(totalCount, BigDecimal.ZERO) > 0) {
            int scale = 2;
            if (productType == Constants.ProductType.MEDICINE && (productSubType == Constants.ProductType.SubType.MEDICINE_CHINESE || productSubType == Constants.ProductType.SubType.MEDICINE_WESTERN)) {
                scale = 4;
            }
            unitPriceResult = totalPrice.divide(totalCount, scale, RoundingMode.HALF_UP);
            discountedUnitPriceResult = discountedTotalPrice.divide(totalCount, scale, RoundingMode.HALF_UP);
        }

        return new ChargeFormItemUnitPriceDto()
                .setUnitPrice(unitPriceResult)
                .setDiscountedUnitPrice(discountedUnitPriceResult);
    }


    public static ChargeFormItemUnitPriceDto calculateItemUnitPriceAndDiscountedUnitPrice(BigDecimal unitPrice,
                                                                                          BigDecimal totalCount,
                                                                                          BigDecimal totalPrice,
                                                                                          BigDecimal discountedTotalPrice,
                                                                                          BigDecimal adjustmentPrice,
                                                                                          int productType,
                                                                                          int productSubType
    ) {
        BigDecimal unitPriceResult = BigDecimal.ZERO;
        BigDecimal discountedUnitPriceResult = BigDecimal.ZERO;
        if (MathUtils.wrapBigDecimalCompare(adjustmentPrice, BigDecimal.ZERO) < 0) {
            unitPriceResult = unitPrice;
            if (MathUtils.wrapBigDecimalCompare(totalCount, BigDecimal.ZERO) > 0) {
                discountedUnitPriceResult = discountedTotalPrice.divide(totalCount, 2, RoundingMode.DOWN);
            }
        } else if (MathUtils.wrapBigDecimalCompare(totalCount, BigDecimal.ZERO) > 0) {
            int scale = 2;
            if (productType == Constants.ProductType.MEDICINE && (productSubType == Constants.ProductType.SubType.MEDICINE_CHINESE || productSubType == Constants.ProductType.SubType.MEDICINE_WESTERN)) {
                scale = 4;
            }
            unitPriceResult = totalPrice.divide(totalCount, scale, RoundingMode.DOWN);
            discountedUnitPriceResult = discountedTotalPrice.divide(totalCount, scale, RoundingMode.DOWN);
        }

        return new ChargeFormItemUnitPriceDto()
                .setUnitPrice(unitPriceResult)
                .setDiscountedUnitPrice(discountedUnitPriceResult);
    }

    public static ChargeFormItemPrintView.Type generateChargeFormItemPrintType(ChargeFormItem chargeFormItem, GoodsItem goodsItem) {

        ChargeFormItemPrintView.Type type = ChargeFormItemPrintView.Type.OTHER;

        if (Objects.isNull(chargeFormItem)) {
            return type;
        }

        switch (chargeFormItem.getProductType()) {
            case Constants.ProductType.REGISTRATION:
                type = ChargeFormItemPrintView.Type.REGISTRATION;
                break;
            case Constants.ProductType.MEDICINE:
                if (chargeFormItem.getProductSubType() == Constants.ProductType.SubType.MEDICINE_CHINESE) {
                    String specification = "";
                    if (chargeFormItem.getComposeType() == ComposeType.COMPOSE_SUB_ITEM) {
                        goodsItem = JsonUtils.readValue(chargeFormItem.getProductSnapshot(), GoodsItem.class);
                        specification = Optional.ofNullable(goodsItem).map(GoodsItem::getCMSpec).orElse(null);
                    } else {
                        specification = Optional.ofNullable(goodsItem)
                                .map(GoodsItem::getCMSpec).orElse(null);
                    }
                    type = Objects.equals(specification, Constants.ProductType.MedicineChineseSpecification.PARTICLE)
                            ? ChargeFormItemPrintView.Type.CHINESE_MEDICINE_PARTICLE
                            : ChargeFormItemPrintView.Type.CHINESE_MEDICINE_DRINKS_PIECE;
                } else if (chargeFormItem.getProductSubType() == Constants.ProductType.SubType.MEDICINE_CHINESE_COMPOSE) {
                    type = ChargeFormItemPrintView.Type.CHINESE_COMPOSE_MEDICINE;
                } else {
                    type = ChargeFormItemPrintView.Type.WESTERN_MEDICINE;
                }
                break;
            case Constants.ProductType.MATERIAL:
                type = ChargeFormItemPrintView.Type.MATERIAL;
                break;
            case Constants.ProductType.SALE_PRODUCT:
                type = ChargeFormItemPrintView.Type.SALE_PRODUCT;
                break;
            case Constants.ProductType.TREATMENT:
                if (chargeFormItem.getProductSubType() == Constants.ProductType.SubType.TREATMENT_OTHER) {
                    type = ChargeFormItemPrintView.Type.OTHER;
                } else {
                    type = ChargeFormItemPrintView.Type.TREATMENT;
                }
                break;
            case Constants.ProductType.EXAMINATION:
                if (chargeFormItem.getProductSubType() == Constants.ProductType.SubType.EXAMINATION_EXAMINATION) {
                    type = ChargeFormItemPrintView.Type.EXAMINATION_EXAMINATION;
                } else {
                    type = ChargeFormItemPrintView.Type.EXAMINATION_INSPECTION;
                }
                break;
            case Constants.ProductType.ONLINE_CONSULTATION:
                type = ChargeFormItemPrintView.Type.ONLINE_CONSULTATION;
                break;
            case Constants.ProductType.EXPRESS_DELIVERY:
                type = ChargeFormItemPrintView.Type.EXPRESS_DELIVERY;
                break;
            case Constants.ProductType.PROCESS:
                type = ChargeFormItemPrintView.Type.PROCESS;
                break;
            case Constants.ProductType.COMPOSE_PRODUCT:
                type = ChargeFormItemPrintView.Type.COMPOSE_PRODUCT;
                break;
            case Constants.ProductType.EYE:
                type = ChargeFormItemPrintView.Type.EYE_PRODUCT;
                break;
            case Constants.ProductType.NURSE:
                type = ChargeFormItemPrintView.Type.NURSING;
                break;
            case Constants.ProductType.SURGERY:
                type = ChargeFormItemPrintView.Type.SURGERY;
                break;

        }
        return type;
    }


    public static BigDecimal calculateReceivableUnitFee(BigDecimal receivableTotalFee,
                                                        BigDecimal unitPrice,
                                                        BigDecimal unitCount,
                                                        BigDecimal doseCount,
                                                        BigDecimal deductedTotalCount) {

        if (MathUtils.wrapBigDecimalCompare(receivableTotalFee, BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal leftTotalCount = MathUtils.calculateTotalCount(unitCount, doseCount).subtract(MathUtils.wrapBigDecimalOrZero(deductedTotalCount));
        if (leftTotalCount.compareTo(BigDecimal.ZERO) <= 0) {
            return receivableTotalFee;
        }

        BigDecimal totalPrice = MathUtils.calculateTotalPrice(unitPrice, unitCount, doseCount, 2);

        if (MathUtils.wrapBigDecimalCompare(totalPrice, receivableTotalFee) == 0) {
            return unitPrice;
        }

        BigDecimal receivableUnitFee = receivableTotalFee.divide(leftTotalCount, 2, RoundingMode.DOWN);

        return receivableUnitFee;
    }

    /**
     * 是否为顶层的item，比如直接开一个费用项，直接开一个费用母项，直接开一个套餐
     */
    public static boolean isTopItem(int composeType, int goodsFeeType) {
        return (composeType == ComposeType.NOT_COMPOSE || composeType == ComposeType.COMPOSE)
                && (goodsFeeType == GoodsFeeType.FEE_OWN || goodsFeeType == GoodsFeeType.FEE_PARENT);
    }

    public static boolean isParentItem(int composeType, int goodsFeeType) {
        return composeType == ComposeType.COMPOSE || goodsFeeType == GoodsFeeType.FEE_PARENT;
    }

    public static boolean isChildItem(int composeType, int goodsFeeType) {
        return composeType == ComposeType.COMPOSE_SUB_ITEM || goodsFeeType == GoodsFeeType.FEE_CHILD;
    }

    public static ChargeFormItem mergeRefundChargeFormItems(List<ChargeFormItem> refundChargeFormItems, boolean refundByDose) {

        if (CollectionUtils.isEmpty(refundChargeFormItems)) {
            return null;
        }

        String associateFormItemId = refundChargeFormItems.get(0).getAssociateFormItemId();

        ChargeFormItem refundChargeFormItem = refundChargeFormItems.stream()
                .collect(
                        Collectors.toMap(ChargeFormItem::getAssociateFormItemId, Function.identity(), (item1, item2) -> {
                            if (refundByDose) {
                                item1.setDoseCount(MathUtils.wrapBigDecimalAdd(item1.getDoseCount(), item2.getDoseCount()));
                            } else {
                                item1.setUnitCount(MathUtils.wrapBigDecimalAdd(item1.getUnitCount(), item2.getUnitCount()));
                            }
                            item1.setDeductTotalCount(MathUtils.wrapBigDecimalAdd(item1.getDeductTotalCount(), item2.getDeductTotalCount()));
                            item1.setPromotionPrice(MathUtils.wrapBigDecimalAdd(item1.getPromotionPrice(), item2.getPromotionPrice()));
                            item1.setAdjustmentPrice(MathUtils.wrapBigDecimalAdd(item1.getAdjustmentPrice(), item2.getAdjustmentPrice()));
                            item1.setFractionPrice(MathUtils.wrapBigDecimalAdd(item1.getFractionPrice(), item2.getFractionPrice()));
                            item1.calculateDiscountedPrice();

                            if (CollectionUtils.isNotEmpty(item1.getChargeFormItemBatchInfos()) && CollectionUtils.isNotEmpty(item2.getChargeFormItemBatchInfos())) {
                                ChargeFormItemBatchInfoUtils.mergeRefundChargeFormItemBatchInfos(item1.getChargeFormItemBatchInfos(), item2.getChargeFormItemBatchInfos());
                            }
                            return item1;
                        })
                ).get(associateFormItemId);
        refundChargeFormItem.setIsRefundByDose(refundByDose ? 1 : 0);
        return refundChargeFormItem;
    }

    public static BigDecimal calculateTotalPriceView(ChargeFormItem chargeFormItem) {
        if (chargeFormItem == null) {
            return BigDecimal.ZERO;
        }
        return MathUtils.wrapBigDecimalAdd(chargeFormItem.getSourceTotalPrice(), chargeFormItem.calculateSinglePromotionPrice(), chargeFormItem.getUnitAdjustmentFee());
    }

    public static List<cn.abcyun.cis.charge.model.ChargeFormItemBatchInfo> getEfficientChargeFormItemBatchInfos(ChargeFormItem chargeFormItem) {
        if (Objects.isNull(chargeFormItem) || CollectionUtils.isEmpty(chargeFormItem.getChargeFormItemBatchInfos())) {
            return new ArrayList<>();
        }

        return chargeFormItem.getChargeFormItemBatchInfos()
                .stream()
                .filter(chargeFormItemBatchInfo -> chargeFormItemBatchInfo.getIsDeleted() == 0)
                .filter(item -> item.getIsOld() == 0)
                .sorted((a, b) -> {
                    Date date1 = DateUtils.parseToDate(a.getExpiryDate(), "yyyy-MM-dd");
                    Date date2 = DateUtils.parseToDate(b.getExpiryDate(), "yyyy-MM-dd");
                    if (date1 == null) {
                        return -1;
                    }
                    if (date2 == null) {
                        return 1;
                    }
                    return date1.compareTo(date2) * -1;
                }).collect(Collectors.toList());
    }

    public static void updateItemAfterGoodsLock(ChargeFormItem chargeFormItem, GoodsLockingFormItem goodsLockingFormItem) {

        if (Objects.isNull(chargeFormItem)) {
            return;
        }

        if (Objects.isNull(goodsLockingFormItem)) {
            chargeFormItem.setLockId(null);
            Optional.ofNullable(chargeFormItem.getChargeFormItemBatchInfos())
                    .orElse(new ArrayList<>())
                    .forEach(chargeFormItemBatchInfo -> chargeFormItemBatchInfo.deleteModel(chargeFormItemBatchInfo.getLastModifiedBy()));
            return;
        }

        chargeFormItem.setLockId(Objects.toString(goodsLockingFormItem.getLockId(), null));

        if (CollectionUtils.isEmpty(goodsLockingFormItem.getGoodsLockBatchItemList())) {
            return;
        }

        if (CollectionUtils.isNotEmpty(chargeFormItem.getChargeFormItemBatchInfos())) {
            Map<String, List<TraceableCode>> batchIdTraceableCodeListMap = Optional.ofNullable(goodsLockingFormItem.getGoodsLockBatchItemList())
                    .orElse(new ArrayList<>())
                    .stream()
                    .filter(goodsLockBatchItem -> CollectionUtils.isNotEmpty(goodsLockBatchItem.getTraceableCodeList()))
                    .collect(Collectors.toMap(goodsLockBatchItem -> ObjectUtils.toString(goodsLockBatchItem.getBatchId(), null),
                            GoodsLockBatchItem::getTraceableCodeList,
                            (a, b) -> a));

            chargeFormItem.getChargeFormItemBatchInfos()
                    .stream()
                    .filter(chargeFormItemBatchInfo -> chargeFormItemBatchInfo.getIsDeleted() == 0)
                    .forEach(chargeFormItemBatchInfo -> {
                        List<TraceableCode> traceableCodes = batchIdTraceableCodeListMap.get(chargeFormItemBatchInfo.getBatchId());

                        if (CollectionUtils.isEmpty(traceableCodes)) {
                            return;
                        }

                        chargeFormItemBatchInfo.setTraceableCodes(traceableCodes.stream()
                                .map(BatchTraceableCode::of)
                                .collect(Collectors.toList())
                        );
                    });
        }


    }

    public static int genItemCalculateUnitPriceScale(int productType, int productSubType) {
        int scale = 2;
        if (productType == Constants.ProductType.MEDICINE
                && (productSubType == Constants.ProductType.SubType.MEDICINE_CHINESE || productSubType == Constants.ProductType.SubType.MEDICINE_WESTERN)) {
            scale = 4;
        }
        return scale;
    }

    public static <T extends Object> MathUtils.CalculateExpectedUnitPriceResult calculateRefundUnitPrice(List<T> chargeFormItemBatchInfos,
                                                                                                         BigDecimal unitPrice,
                                                                                                         BigDecimal unitCount,
                                                                                                         BigDecimal doseCount,
                                                                                                         int productType,
                                                                                                         int productSubType,
                                                                                                         BigDecimal totalPrice) {

        MathUtils.CalculateExpectedUnitPriceResult result = new MathUtils.CalculateExpectedTotalPriceRatioResult();
        result.expectedUnitPrice = unitPrice;
        result.fractionPrice = BigDecimal.ZERO;
        if (CollectionUtils.isEmpty(chargeFormItemBatchInfos)) {
            return result;
        }

        BigDecimal totalCount = MathUtils.calculateTotalCount(unitCount, doseCount);

        if (totalCount.compareTo(BigDecimal.ZERO) <= 0) {
            return result;
        }

        int unitPriceScale = ChargeFormItemUtils.genItemCalculateUnitPriceScale(productType, productSubType);

        return MathUtils.calculateExpectedUnitPriceBase(unitCount, doseCount, totalPrice, unitPriceScale, 2);
    }

    public static GoodsItem convertGoodsItemFromRegistrationChildGoodsSnap(RegistrationFormItemFeeDetail registrationFormItemFeeDetail) {

        if (Objects.isNull(registrationFormItemFeeDetail) || Objects.isNull(registrationFormItemFeeDetail.getGoodsSnap())) {
            return null;
        }

        GoodsSnap registrationChildGoodsSnap = registrationFormItemFeeDetail.getGoodsSnap();

        GoodsItem goodsItem = JsonUtils.readValue(JsonUtils.dump(registrationChildGoodsSnap), GoodsItem.class);

        if (goodsItem == null) {
            return goodsItem;
        }

        goodsItem.setId(registrationFormItemFeeDetail.getGoodsId());
        goodsItem.setType(registrationFormItemFeeDetail.getGoodsType());
        goodsItem.setName(registrationFormItemFeeDetail.getGoodsName());
        goodsItem.setDisplayName(registrationFormItemFeeDetail.getGoodsName());
        goodsItem.setSubType(registrationFormItemFeeDetail.getGoodsSubType());
        goodsItem.setComposePiecePrice(registrationChildGoodsSnap.getPiecePrice());
        goodsItem.setComposePackagePrice(registrationChildGoodsSnap.getPackagePrice());
        goodsItem.setComposeUseDismounting(registrationFormItemFeeDetail.getIsDismounting());
        //todo 目前挂号费子项没有拆零的说法，如果以后挂号费子项有拆零逻辑，这里需要处理成本价拆零的问题
        goodsItem.setPackageCostPrice(registrationFormItemFeeDetail.getCostPrice());
        if (registrationFormItemFeeDetail.getIsDismounting() == 1) {
            goodsItem.setComposePieceCount(registrationFormItemFeeDetail.getUnitCount());
        } else {
            goodsItem.setComposePackageCount(registrationFormItemFeeDetail.getUnitCount());
        }
        return goodsItem;
    }

    public static boolean isCanDispensing(int goodsFeeType, int productType, String productId) {
        //费用项不需要扣库存，不需要发药
        if (goodsFeeType == GoodsFeeType.FEE_CHILD) {
            return false;
        }

        //处理系统药品的逻辑，无库存的药品现在也可以收费了，如果是系统药品，就不判断有没有发药的逻辑，系统药品的判断就是 productId为空的药品
        return (productType == Constants.ProductType.MEDICINE || productType == Constants.ProductType.MATERIAL || productType == Constants.ProductType.SALE_PRODUCT || productType == Constants.ProductType.EYE) && !StringUtils.isEmpty(productId);
    }

    public static List<ChargeFormItem> filterIsDeletedItems(List<ChargeFormItem> chargeFormItems) {
        if (CollectionUtils.isEmpty(chargeFormItems)) {
            return new ArrayList<>();
        }

        return chargeFormItems.stream()
                .filter(chargeFormItem -> chargeFormItem.getIsDeleted() == 0)
                .collect(Collectors.toList());
    }

    public static ChargeFormItem copyForDirectSale(ChargeFormItem sourceItem,
                                                   String chargeSheetId,
                                                   String chargeFormId) {
        // 只要已收费和退单的
        if (!Objects.equals(sourceItem.getStatus(), Constants.ChargeFormItemStatus.CHARGED) && !Objects.equals(sourceItem.getStatus(), Constants.ChargeFormItemStatus.UNSELECTED)) {
            return null;
        }

        ChargeFormItem copiedItem = new ChargeFormItem();
        copiedItem.setId(AbcIdUtils.getUID());
        copiedItem.setChargeSheetId(chargeSheetId);
        copiedItem.setChargeFormId(chargeFormId);
        copiedItem.setClinicId(sourceItem.getClinicId());
        copiedItem.setChainId(sourceItem.getChainId());
        copiedItem.setUnit(sourceItem.getUnit());
        copiedItem.setName(sourceItem.getName());
        copiedItem.setUnitCostPrice(sourceItem.getUnitCostPrice());
        copiedItem.setTotalCostPrice(sourceItem.getTotalCostPrice());
        copiedItem.setUnitCount(sourceItem.getUnitCount());
        copiedItem.setDoseCount(sourceItem.getDoseCount());
        copiedItem.setUnitPrice(sourceItem.getUnitPrice());
        copiedItem.setTotalPrice(sourceItem.getTotalPrice());
        copiedItem.setExpectedUnitPrice(sourceItem.getExpectedUnitPrice());
        copiedItem.setSourceUnitPrice(sourceItem.getSourceUnitPrice());
        copiedItem.setSourceTotalPrice(sourceItem.getSourceTotalPrice());
        copiedItem.setFractionPrice(sourceItem.getFractionPrice());
        copiedItem.setExpectedTotalPrice(sourceItem.getExpectedTotalPrice());
        copiedItem.setExpectedTotalPriceRatio(sourceItem.getExpectedTotalPriceRatio());
        copiedItem.setUseDismounting(sourceItem.getUseDismounting());
        copiedItem.setProductType(sourceItem.getProductType());
        copiedItem.setProductSubType(sourceItem.getProductSubType());
        copiedItem.setProductId(sourceItem.getProductId());
        copiedItem.setGroupId(sourceItem.getGroupId());
        copiedItem.setSort(sourceItem.getSort());
        copiedItem.setComposeType(sourceItem.getComposeType());
        copiedItem.setSourceItemType(sourceItem.getSourceItemType());
        copiedItem.setUsageInfoJson(sourceItem.getUsageInfoJson());
        copiedItem.setUsageInfo(sourceItem.getUsageInfo());
        copiedItem.setIsGift(sourceItem.getIsGift());
        copiedItem.setPharmacyType(sourceItem.getPharmacyType());
        copiedItem.setPharmacyNo(sourceItem.getPharmacyNo());
        copiedItem.setFeeComposeType(sourceItem.getFeeComposeType());
        copiedItem.setFeeTypeId(sourceItem.getFeeTypeId());
        copiedItem.setGoodsFeeType(sourceItem.getGoodsFeeType());
        copiedItem.setAdditional(
                ChargeFormItemAdditionalUtils.copyForDirectSale(sourceItem.getAdditional())
        );
        copiedItem.setGoodsTypeId(sourceItem.getGoodsTypeId());
        copiedItem.setIsExpectedBatch(1);
        copiedItem.setChargeFormItemBatchInfos(
                Optional.ofNullable(sourceItem.getChargeFormItemBatchInfos())
                        .orElse(Lists.newArrayList())
                        .stream()
                        .map(ChargeFormItemBatchInfoUtils::copyForDirectSale)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList())
        );

        return copiedItem;
    }

    public static void compareBatchIsChangedAndCleanTraceableCodes(ChargeFormItemView chargeFormItemView,
                                                                   Map<String, ChargeFormItem> oldItemIdToOldItemMap,
                                                                   Map<String, String> newItemIdToOldItemId) {
        // chargeFormItemView是父子结构
        if (!CollectionUtils.isEmpty(chargeFormItemView.getComposeChildren())) {
            chargeFormItemView.getComposeChildren().forEach(child -> compareBatchIsChangedAndCleanTraceableCodes(child, oldItemIdToOldItemMap, newItemIdToOldItemId));
        }

        if (CollectionUtils.isEmpty(chargeFormItemView.getTraceableCodeList())) {
            return;
        }

        // 老批次
        Map<String, BigDecimal> oldBatchIdToBatchUnitCount = Optional.ofNullable(oldItemIdToOldItemMap.get(newItemIdToOldItemId.get(chargeFormItemView.getId())))
                .map(ChargeFormItem::getChargeFormItemBatchInfos)
                .orElse(Lists.newArrayList())
                .stream()
                .collect(Collectors.toMap(ChargeFormItemBatchInfo::getBatchId, ChargeFormItemBatchInfo::getUnitCount));
        if (MapUtils.isEmpty(oldBatchIdToBatchUnitCount)) {
            chargeFormItemView.setTraceableCodeList(Lists.newArrayList());
            return;
        }

        Map<String, BigDecimal> newBatchIdToBatchUnitCount = Optional.ofNullable(chargeFormItemView.getChargeFormItemBatchInfos())
                .orElse(Lists.newArrayList())
                .stream()
                .collect(Collectors.toMap(ChargeFormItemBatchInfoView::getBatchId, ChargeFormItemBatchInfoView::getUnitCount));

        // 如果两个批次数量不一样
        if (oldBatchIdToBatchUnitCount.size() != newBatchIdToBatchUnitCount.size()) {
            chargeFormItemView.setTraceableCodeList(Lists.newArrayList());
            return;
        }

        // 判断数量是否一样
        if (newBatchIdToBatchUnitCount.entrySet()
                .stream()
                .anyMatch(newEntry -> MathUtils.wrapBigDecimalCompare(newEntry.getValue(), oldBatchIdToBatchUnitCount.get(newEntry.getKey())) != 0)) {
            chargeFormItemView.setTraceableCodeList(Lists.newArrayList());
        }
    }

}
