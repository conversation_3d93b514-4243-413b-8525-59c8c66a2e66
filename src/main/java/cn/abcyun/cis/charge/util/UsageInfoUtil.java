package cn.abcyun.cis.charge.util;


import cn.abcyun.cis.commons.rpc.outpatient.EyeExamination;
import cn.abcyun.cis.commons.rpc.outpatient.UsageInfo;
import cn.abcyun.cis.commons.util.TextUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;

public class UsageInfoUtil {

    @Data
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    public static class DbUsageInfo{

        private String specification;
        private Integer ast;
        private String usage;
        private BigDecimal ivgtt;
        private String ivgttUnit;
        private String freq;
        private String dosage;
        private String dosageUnit;
        private Integer days;
        private String specialRequirement;
        @JsonInclude(JsonInclude.Include.NON_DEFAULT)
        private int doseCount;
        private String dailyDosage;
        private String requirement;
        private String usageLevel;
        @JsonInclude(JsonInclude.Include.NON_DEFAULT)
        private boolean isDecoction;
        private String contactMobile;

        private String processRemark;

        /**
         * 加工制法
         */
        private String processUsage;

        /**
         * 一剂的袋数
         */
        @JsonInclude(JsonInclude.Include.NON_DEFAULT)
        private int processBagUnitCount;

        /**
         * 总袋数
         */
        private BigDecimal totalProcessCount;


        /**
         * 一剂的袋数 小数
         */
        private BigDecimal processBagUnitCountDecimal;
        /**
         * 加工包装单位 袋/格
         */
        private String processBagUnit;

        @JsonInclude(JsonInclude.Include.NON_DEFAULT)
        private boolean checked = true;

        /**
         *     DECOCTION: 1, 煎药
         *     CREAM: 2, 制膏
         *     POWDER: 3, 打粉
         *     PILL: 4,  制丸
         */
        private Integer usageType;

        private Integer usageSubType;

        /**
         * item的支付类型
         * {@link cn.abcyun.cis.commons.rpc.charge.Constants.ChargeFormItemPayType}
         */
        private Integer payType;

        /**
         * 服用天数
         */
        private String usageDays;

        /**
         * 注输单执行总数
         */
        private Integer executedTotalCount;

        /**
         * 最大成品出率比例（空中药房使用）
         */
        private BigDecimal finishedRate;

        /**
         * 最小成品出率比例（空中药房使用）
         */
        private BigDecimal finishedRateMin;

        @JsonInclude(JsonInclude.Include.NON_DEFAULT)
        private int verifySignatureStatus;

        private BigDecimal externalUnitCount;


        /**
         * 眼镜处方-验光师
         */
        private String optometristId;
        /**
         * 眼镜处方-眼镜类型
         */
        private Integer glassesType;
        private EyeExamination glassesParams;
        /**
         * 检查目的
         */
        private String purposeOfExamination;
        /**
         * https://modao.cc/proto/NN9JBo8Zs67owf8YW1gaz/sharing?view_mode=read_only&screen=rbpURF4rUx1KtrH56
         * 贵阳康复医院定制小需求
         *[外置处方formItem] 每次3穴位 里面3
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private BigDecimal acupointUnitCount;
        /**
         * [外置处方formItem]每次3穴位 里面穴位
         */
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private String acupointUnit;

        public boolean getChecked() {
            return checked;
        }

        public boolean getIsDecoction() {
            return isDecoction;
        }
    }

    /**
     * 简化usageInfo
     * @param usageInfoJson
     * @return
     */
    public static String simplifyUsageInfo(String usageInfoJson) {
        if (TextUtils.isEmpty(usageInfoJson)) {
            return null;
        }

        DbUsageInfo dbUsageInfo = JsonUtils.readValue(usageInfoJson, DbUsageInfo.class);
        usageInfoJson = JsonUtils.dump(dbUsageInfo);
        return usageInfoJson;
    }

    /**
     * 简化usageInfo
     * @return
     */
    public static DbUsageInfo convertDbUsageInfo(UsageInfo usageInfo) {

        if (usageInfo == null) {
            return null;
        }

        DbUsageInfo dbUsageInfo = new DbUsageInfo();
        BeanUtils.copyProperties(usageInfo, dbUsageInfo);

        return dbUsageInfo;
    }

    public static void main(String[] args) {
        String usageInfoJson = "{\"ast\": null, \"days\": null, \"freq\": null, \"ivgtt\": null, \"usage\": null, \"dosage\": null, \"checked\": true, \"payType\": null, \"doseCount\": 0, \"ivgttUnit\": null, \"usageDays\": null, \"usageType\": null, \"dosageUnit\": null, \"usageLevel\": null, \"dailyDosage\": null, \"glassesType\": null, \"isDecoction\": false, \"requirement\": null, \"finishedRate\": null, \"processUsage\": null, \"usageSubType\": null, \"contactMobile\": null, \"glassesParams\": null, \"optometristId\": null, \"processRemark\": null, \"specification\": null, \"processBagUnit\": null, \"finishedRateMin\": null, \"externalUnitCount\": null, \"totalProcessCount\": null, \"executedTotalCount\": null, \"specialRequirement\": null, \"processBagUnitCount\": 0, \"verifySignatureStatus\": 0, \"processBagUnitCountDecimal\": 0}";
        System.out.println(simplifyUsageInfo(usageInfoJson));
    }

}
