package cn.abcyun.cis.charge.util;

import cn.abcyun.cis.charge.base.ChargeVersionConstants;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.model.ChargeSheetAdditional;
import cn.abcyun.cis.charge.model.ChargeSheetAdditionalExtendInfo;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.Optional;

public class ChargeSheetAdditionalUtils {

    /**
     * @param additional
     * @param registerInfoId 实名登记id
     * @param pharmacistId   药师id
     */
    public static void updateChargeSheetAdditionalExtendInfo(ChargeSheetAdditional additional, String registerInfoId, String pharmacistId) {
        if (additional == null) {
            return;
        }
        //保存实名登记id
        if (StringUtils.isNotBlank(registerInfoId)) {
            additional.setExtendedInfo(Optional.ofNullable(additional.getExtendedInfo())
                    .orElse(new ChargeSheetAdditionalExtendInfo())
                    .setRegisterInfoId(registerInfoId));
        }

        if (StringUtils.isNotBlank(pharmacistId)) {
            additional.setExtendedInfo(Optional.ofNullable(additional.getExtendedInfo())
                    .orElse(new ChargeSheetAdditionalExtendInfo())
                    .setPharmacistId(pharmacistId));
        }
    }

    public static ChargeSheetAdditional copyForDirectSale(ChargeSheetAdditional sourceAdditional, String chargeSheetId, int hisType){
        if (Objects.isNull(sourceAdditional)){
            return null;
        }
        ChargeSheetAdditional copiedAdditional = new ChargeSheetAdditional();
        copiedAdditional.setId(chargeSheetId);
        copiedAdditional.setChainId(sourceAdditional.getChainId());
        copiedAdditional.setClinicId(sourceAdditional.getClinicId());
        copiedAdditional.setInvoiceStatus(Constants.ChargeSheetInvoiceStatus.NONE);
        copiedAdditional.setInvoiceStatusFlag(null);
        copiedAdditional.setDepartmentId(sourceAdditional.getDepartmentId());
        copiedAdditional.setDepartmentName(sourceAdditional.getDepartmentName());
        copiedAdditional.setAbstractInfo(sourceAdditional.getAbstractInfo());
        copiedAdditional.setRemarks(sourceAdditional.getRemarks());
        copiedAdditional.setIsCanBeClone(sourceAdditional.getIsCanBeClone());
        copiedAdditional.setRetailType(ChargeSheetAdditional.RetailType.RETAIL_DIRECT_COPY);
        copiedAdditional.setUseMemberFlag(sourceAdditional.getUseMemberFlag());
        copiedAdditional.setConsultantId(sourceAdditional.getConsultantId());
        copiedAdditional.setRuleTag(sourceAdditional.getRuleTag());
        copiedAdditional.setDeliveryDetail(sourceAdditional.getDeliveryDetail());
        copiedAdditional.setChargeVersion(ChargeVersionConstants.convertChargeVersion(hisType));

        return copiedAdditional;
    }
}
