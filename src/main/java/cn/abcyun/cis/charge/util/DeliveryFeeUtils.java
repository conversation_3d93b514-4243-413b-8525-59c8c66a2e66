package cn.abcyun.cis.charge.util;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsPharmacyBaseView;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsRulePharmacyItem;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.QueryGoodsRulePharmacyRsp;
import cn.abcyun.bis.rpc.sdk.property.base.PropertyKey;
import cn.abcyun.bis.rpc.sdk.property.model.SelfServiceSettings;
import cn.abcyun.bis.rpc.sdk.property.service.PropertyService;
import cn.abcyun.cis.charge.api.model.ChargeAirPharmacyLogisticsReq;
import cn.abcyun.cis.charge.api.model.ChargeDeliveryReq;
import cn.abcyun.cis.charge.api.model.ChargeFormItemReq;
import cn.abcyun.cis.charge.api.model.ChargeFormReq;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.factory.ChargeFormItemFactory;
import cn.abcyun.cis.charge.factory.chargeform.ChargeFormFactory;
import cn.abcyun.cis.charge.factory.logistics.LogisticsFactory;
import cn.abcyun.cis.charge.model.*;
import cn.abcyun.cis.charge.processor.ExpectedPriceHelper;
import cn.abcyun.cis.charge.service.*;
import cn.abcyun.cis.charge.service.rpc.CisScGoodsService;
import cn.abcyun.cis.commons.model.AddressNode;
import cn.abcyun.common.log.marker.AbcLogMarker;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class DeliveryFeeUtils {
    private static final Logger sLogger = LoggerFactory.getLogger(DeliveryFeeUtils.class);

    /**
     * true表示一定是寄付，false 不能说明什么
     */
    public static boolean deliveryPaidByShipper(int deliveryType, ChargeDeliveryReq deliveryInfo) {
        if (deliveryType == ChargeSheet.DeliveryType.DELIVERY_TO_HOME && deliveryInfo != null) {
            return deliveryPaidByShipper(deliveryInfo);
        }
        return false;
    }

    /**
     * true表示一定是到付，false 不能说明什么
     */
    public static boolean deliveryPaidByConsignee(int deliveryType, ChargeDeliveryReq deliveryInfo) {
        if (deliveryType == ChargeSheet.DeliveryType.DELIVERY_TO_HOME && deliveryInfo != null) {
            return deliveryPaidByConsignee(deliveryInfo);
        }
        return false;
    }

    /**
     * 寄付
     */
    public static boolean deliveryPaidByShipper(ChargeDeliveryReq chargeDeliveryReq) {
        return chargeDeliveryReq.getDeliveryPayType() == Constants.DeliveryPayType.PAID_BY_SHIPPER;
    }

    /**
     * 到付 ，算费不算费
     */
    public static boolean deliveryPaidByConsignee(ChargeDeliveryReq chargeDeliveryReq) {
        return chargeDeliveryReq.getDeliveryPayType() == Constants.DeliveryPayType.PAID_BY_CONSIGNEE;
    }

    /**
     * 是否是完整的收货地址
     */
    public static boolean isAvailableAddress(ChargeDeliveryReq chargeDeliveryReq) {
        if (chargeDeliveryReq == null) {
            return false;
        }
        return !StringUtils.isEmpty(chargeDeliveryReq.getAddressProvinceId())
                && !StringUtils.isEmpty(chargeDeliveryReq.getAddressCityId())
                && !StringUtils.isEmpty(chargeDeliveryReq.getAddressDistrictId());
    }

    /**
     * 微信上来的地址没有，省市id，这个函数是把这个id补齐下
     *
     * @return 返回值 true补充过，false 没补充过
     */
    public static boolean fixClientDeliveryReqAddressId(ChargeDeliveryReq deliveryInfo, AddressNodeService addressNodeService) {

        Map<String, AddressNode> addressNodeMap = addressNodeService.getAddressNodeMap();
        if (MapUtils.isEmpty(addressNodeMap) || StringUtils.isEmpty(deliveryInfo.getAddressDistrictId())) {
            sLogger.error("refreshAddressId addressNodeMap is null");
            return false;
        }


        AddressNode districtAddrNode = addressNodeMap.get(deliveryInfo.getAddressDistrictId());
        if (districtAddrNode == null) {
            sLogger.error("refreshAddressId no exist this districtId [{}]", deliveryInfo.getAddressDistrictId());
            return false;
        }

        AddressNode cityAddrNode = districtAddrNode.getParent();
        if (cityAddrNode == null) {
            sLogger.error("refreshAddressId district [{}] no exist city", deliveryInfo.getAddressDistrictId());
            return false;
        }

        AddressNode provinceAddrNode = cityAddrNode.getParent();
        if (provinceAddrNode == null) {
            sLogger.error("refreshAddressId city [{}] district [{}] no exist province", cityAddrNode.getAddressId(), deliveryInfo.getAddressDistrictId());
            return false;
        }
        deliveryInfo.setAddressCityId(cityAddrNode.getAddressId());
        deliveryInfo.setAddressCityName(cityAddrNode.getAddressName());
        deliveryInfo.setAddressProvinceId(provinceAddrNode.getAddressId());
        deliveryInfo.setAddressProvinceName(provinceAddrNode.getAddressName());
        return true;
    }

    /**
     * 用户可能选择新加快递和加工
     * 快递：快递规则 + 快递收费项
     * 参数：从不同的req上来，分别传入
     * 自助支付：两种场景，1：微诊所自助支付，2：设备机自助支付，3：小程序支付
     */
    public static void selfPayModifyProcessAndDelivery(ChargeSheet chargeSheet,
                                                       int deliveryType, //普通快递规则
                                                       ChargeDeliveryReq deliveryInfo,//终端请求，普通快递地址
                                                       List<ChargeFormReq> clientReqChargeForms,//端上请求的收费项目
                                                       int paySource,
                                                       String operatorId,
                                                       int hisType,
                                                       CisScGoodsService scGoodsService,
                                                       PropertyService propertyService) {
        if (chargeSheet == null) {
            return;
        }

        //只有患者端才处理
        if (!(paySource == Constants.ChargeSource.WE_CLINIC
                || paySource == Constants.ChargeSource.WE_APP
                || (paySource == Constants.ChargeSource.DEVICE && isDeviceEnableAutoPay(propertyService, chargeSheet.getClinicId())))) {
            return;
        }

        Map<String, ChargeFormReq> chargeFormReqMap = clientReqChargeForms.stream().collect(Collectors.toMap(ChargeFormReq::getId, Function.identity(), (a, b) -> a));
        List<ChargeForm> clientChargeForms = ClientReqUtils.convertToCalculateChargeForms(clientReqChargeForms, chargeSheet.getId(), chargeSheet.getClinicId(), Optional.ofNullable(chargeSheet.getAdditional()).map(ChargeSheetAdditional::getContactMobile).orElse(null), hisType, operatorId);
        /**
         * 本地药房快递地址的处理：微诊所病人能修改快递地址和加工规则，req参数里面会带病人的设置。所以需要更新一下信息
         * */
        if (deliveryType == ChargeSheet.DeliveryType.DELIVERY_TO_HOME) {

            /**
             * 寄付需要看下收费单里面是否有快递收费的chargeForm，否则要插入一条
             * */
            if (DeliveryFeeUtils.deliveryPaidByShipper(deliveryType, deliveryInfo)) {
                setDeliveryChargeFormFee(chargeSheet, deliveryInfo, clientReqChargeForms, operatorId);
            } else {
                resetDeliveryChargeFormFee(chargeSheet, deliveryInfo, clientReqChargeForms, operatorId, true);
            }
        } else {
            deleteDeliveryChargeFormAndDeliveryInfo(chargeSheet, operatorId);
//            resetDeliveryChargeFormFee(chargeSheet,chargeFormService,deliveryInfo,operatorId,false);
        }

        /**
         * 虚拟药房是否改了快递信息，如果改了，将议价清空
         */
        updateVirtualPharmacyDeliveryExpectedPrice(chargeSheet, chargeFormReqMap);

        /**
         * 本地药房：更新加工规则
         * */
        insertOrUpdateProcessForm(chargeSheet, clientChargeForms, clientReqChargeForms, scGoodsService, operatorId);

        /**
         * 空中药房：medicineRecord和快递地址的处理
         * 空中要放的charge在门诊推单的时候已经加个chargeFormItem了
         * */
        boolean airDeliveryUpdate = false;
        Map<String, ChargeForm> clientMap = clientChargeForms.stream().collect(Collectors.toMap(ChargeForm::getId, chargeForm -> chargeForm));
        for (ChargeForm svrChargeForm : chargeSheet.getChargeForms()) {

            if (svrChargeForm.getIsDeleted() == 1) {
                continue;
            }

            if (svrChargeForm.getSourceFormType() != Constants.SourceFormType.AIR_PHARMACY && svrChargeForm.getPharmacyType() != GoodsConst.PharmacyType.VIRTUAL_PHARMACY) {
                continue;
            }
            ChargeForm clientChargeForm = clientMap.get(svrChargeForm.getId());
            if (clientChargeForm != null) {

                //检查快递地址是否修改
                if (DeliveryService.checkFormDeliveryIsChanged(clientChargeForm.getChargeAirPharmacyLogistics(), svrChargeForm.getChargeAirPharmacyLogistics())) {
                    svrChargeForm.setDeliveryPrimaryFormId(null);
                }

                airDeliveryUpdate = true;
                LogisticsFactory.airPharmacyDeliveryInfoUpdate(svrChargeForm, clientChargeForm.getChargeAirPharmacyLogistics(), operatorId, false);
                ChargeFormFactory.initChargeFormDeliveryInfo(svrChargeForm);
            }
            Map<String, ChargeFormItem> clientFormItemMap = ListUtils.toMap(clientChargeForm.getChargeFormItems(), ChargeFormItem::getId);
            ChargeUtils.getChargeFormItems(svrChargeForm)
                    .stream()
                    .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.PROCESS ||
                            chargeFormItem.getProductType() == Constants.ProductType.EXPRESS_DELIVERY ||
                            chargeFormItem.getProductType() == Constants.ProductType.INGREDIENT)
                    .forEach(chargeFormItem -> {
                        ChargeFormItem clientFormItem = clientFormItemMap.getOrDefault(chargeFormItem.getId(), null);
                        if (clientFormItem != null) {
                            clientFormItem.setDoseCount(BigDecimal.ONE);
                            clientFormItem.setExpectedDoseCount(null);
                        }

                        chargeFormItem.setDoseCount(BigDecimal.ONE);
                        chargeFormItem.setExpectedDoseCount(null);
                    });
        }

        /**
         * 更新到付和寄付信息
         * */
        updateRegDeliveryProcessChargePriceFromClientReq(clientReqChargeForms, ChargeFormUtils.processRegDeliveryFormItems(chargeSheet), deliveryPaidByConsignee(deliveryType, deliveryInfo));

        chargeSheet.setDeliveryType(deliveryType);

    }

    private static void updateVirtualPharmacyDeliveryExpectedPrice(ChargeSheet chargeSheet, Map<String, ChargeFormReq> chargeFormReqMap) {

        if (chargeSheet == null || chargeSheet.getChargeForms() == null || chargeFormReqMap == null) {
            return;
        }

        chargeSheet.getChargeForms()
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE && chargeForm.getPharmacyType() == GoodsConst.PharmacyType.VIRTUAL_PHARMACY)
                .filter(chargeForm -> chargeForm.getChargeAirPharmacyLogistics() != null)
                .forEach(chargeForm -> {
                    if (chargeForm.getChargeAirPharmacyLogistics() != null) {
                        ChargeAirPharmacyLogistics dbChargeAirPharmacyLogistics = new ChargeAirPharmacyLogistics();
                        BeanUtils.copyProperties(chargeForm.getChargeAirPharmacyLogistics(), dbChargeAirPharmacyLogistics, "deliveryCompany");
                        if (chargeForm.getChargeAirPharmacyLogistics().getDeliveryCompany() != null) {
                            ChargeAirPharmacyLogisticsReq.LogisticsCompanyReq logisticsCompanyReq = new ChargeAirPharmacyLogisticsReq.LogisticsCompanyReq();
                            BeanUtils.copyProperties(chargeForm.getChargeAirPharmacyLogistics().getDeliveryCompany(), logisticsCompanyReq);
                            dbChargeAirPharmacyLogistics.setDeliveryCompany(logisticsCompanyReq);
                        }

                        chargeForm.setDbChargeAirPharmacyLogistics(dbChargeAirPharmacyLogistics);
                    }

                    ChargeFormReq chargeFormReq = chargeFormReqMap.getOrDefault(chargeForm.getId(), null);

                    if (chargeFormReq == null || chargeFormReq.getDeliveryInfo() == null) {
                        return;
                    }

                    ChargeAirPharmacyLogisticsReq deliveryInfoReq = chargeFormReq.getDeliveryInfo();
                    ChargeAirPharmacyLogistics deliveryInfo = chargeForm.getChargeAirPharmacyLogistics();

                    if (deliveryInfoReq == null || deliveryInfo == null) {
                        return;
                    }

                    boolean deliveryChanged = !org.apache.commons.lang3.StringUtils.equals(deliveryInfo.getAddressProvinceId(), deliveryInfoReq.getAddressProvinceId())
                            || !org.apache.commons.lang3.StringUtils.equals(deliveryInfo.getAddressCityId(), deliveryInfoReq.getAddressCityId())
                            || !org.apache.commons.lang3.StringUtils.equals(deliveryInfo.getAddressDistrictId(), deliveryInfoReq.getAddressDistrictId())
                            || !org.apache.commons.lang3.StringUtils.equals(deliveryInfo.getDeliveryCompanyId(), Optional.ofNullable(deliveryInfoReq.getDeliveryCompany()).map(ChargeAirPharmacyLogisticsReq.LogisticsCompanyReq::getId).orElse(""));
                    //如果改变了将议价清空
                    if (deliveryChanged) {

                        Optional.ofNullable(chargeForm.getChargeFormItems()).orElse(new ArrayList<>())
                                .stream()
                                .filter(item -> item.getIsDeleted() == 0)
                                .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.EXPRESS_DELIVERY)
                                .forEach(chargeFormItem -> {
                                    chargeFormItem.setExpectedUnitPrice(null);
                                    chargeFormItem.setExpectedTotalPrice(null);
                                    chargeFormItem.setExpectedTotalPriceRatio(null);
                                });

                        Optional.ofNullable(chargeFormReq.getChargeFormItems()).orElse(new ArrayList<>())
                                .stream()
                                .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.EXPRESS_DELIVERY)
                                .forEach(chargeFormItem -> {
                                    chargeFormItem.setExpectedUnitPrice(null);
                                    chargeFormItem.setExpectedTotalPrice(null);
                                    chargeFormItem.setExpectedTotalPriceRatio(null);
                                });
                    }

                });

    }

    public static void insertOrUpdateProcessForm(ChargeSheet chargeSheet, List<ChargeForm> clientChargeForms, List<ChargeFormReq> clientReqChargeForms, CisScGoodsService scGoodsService, String operatorId) {

        if (chargeSheet == null || CollectionUtils.isEmpty(clientChargeForms) || chargeSheet.getLockStatus() != Constants.ChargeSheetLockStatus.LOCK_STATUS_UNLOCK) {
            return;
        }

        if (clientChargeForms == null) {
            clientChargeForms = new ArrayList<>();
        }

        List<ChargeForm> processClientChargeForm = clientChargeForms.stream().filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.PROCESS).collect(Collectors.toList());

        if (chargeSheet.getChargeForms() == null) {
            chargeSheet.setChargeForms(new ArrayList<>());
        }

        List<ChargeForm> existedProcessChargeForms = chargeSheet.getChargeForms()
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.PROCESS)
                .collect(Collectors.toList());
        ChargeForm existedProcessChargeForm = CollectionUtils.isNotEmpty(existedProcessChargeForms) ? existedProcessChargeForms.get(0) : null;

        existedProcessChargeForms.clear();

        List<ChargeForm> processChargeForms = processClientChargeForm.stream()
                .map(clientChargeForm -> ChargeFormFactory.insertAdditionalForm(chargeSheet, clientChargeForm, false, operatorId))
                .collect(Collectors.toList());

        chargeSheet.getChargeForms()
                .stream()
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.PROCESS)
                .forEach(chargeForm -> chargeForm.deleteModel(operatorId));
        chargeSheet.getChargeForms().addAll(processChargeForms);


        ChargeForm processChargeForm = CollectionUtils.isNotEmpty(processChargeForms) ? processChargeForms.get(0) : null;

        updateProcessFormItemExpectedPrice(existedProcessChargeForm, processChargeForm);

        ChargeSheetProcessInfoUtils.baseUpdateChargeSheetProcessFormId(chargeSheet);

        resetChinesePrescriptionFormPharmacyNoIfNeed(chargeSheet.getChainId(), chargeSheet.getClinicId(), existedProcessChargeForm, processChargeForm, chargeSheet.getChargeForms(), clientReqChargeForms, chargeSheet.getQueryGoodsDepartmentId(), scGoodsService);

        ClientReqUtils.updatePrescriptionChineseFormUsageInfoByProcessForm(chargeSheet.getChargeForms(), null);
    }

    private static void resetChinesePrescriptionFormPharmacyNoIfNeed(String chainId,
                                                                     String clinicId,
                                                                     ChargeForm existedProcessChargeForm,
                                                                     ChargeForm processChargeForm,
                                                                     List<ChargeForm> chargeForms,
                                                                     List<ChargeFormReq> clientReqChargeForms,
                                                                     String departmentId,
                                                                     CisScGoodsService scGoodsService) {

        if (Objects.isNull(scGoodsService)) {
            return;
        }

        ChargeSheetProcessInfo existedProcessInfo = Optional.ofNullable(existedProcessChargeForm).map(ChargeForm::getProcessInfo).orElse(null);
        ChargeSheetProcessInfo processInfo = Optional.ofNullable(processChargeForm).map(ChargeForm::getProcessInfo).orElse(null);

        if (Objects.isNull(existedProcessInfo) && Objects.isNull(processInfo)) {
            return;
        }


        if (Objects.nonNull(existedProcessInfo)
                && Objects.nonNull(processInfo)
                && Objects.equals(String.format("%d-%d", existedProcessInfo.getType(),
                        existedProcessInfo.getSubType()),
                String.format("%d-%d", processInfo.getType(), processInfo.getSubType()))) {
            return;
        }

        String chinesePrescriptionFormId = Optional.ofNullable(processInfo)
                .map(ChargeSheetProcessInfo::getChargeFormId)
                .orElse(Optional.ofNullable(existedProcessInfo)
                        .map(ChargeSheetProcessInfo::getChargeFormId)
                        .orElse(null)
                );

        if (org.apache.commons.lang3.StringUtils.isEmpty(chinesePrescriptionFormId)) {
            return;
        }

        ChargeForm chinesePrescriptionForm = Optional.ofNullable(chargeForms)
                .orElse(new ArrayList<>())
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE && chargeForm.getPharmacyType() == GoodsConst.PharmacyType.LOCAL_PHARMACY)
                .filter(chargeForm -> Objects.equals(chargeForm.getId(), chinesePrescriptionFormId))
                .findFirst()
                .orElse(null);

        if (Objects.isNull(chinesePrescriptionForm) || org.apache.commons.lang3.StringUtils.isEmpty(chinesePrescriptionForm.getSpecification())) {
            return;
        }


        GoodsRulePharmacyItem goodsRulePharmacyItem = new GoodsRulePharmacyItem();
        goodsRulePharmacyItem.setTypeId(BisOrderUtils.convertToGoodsTypeId(chinesePrescriptionForm.getSpecification()));
        goodsRulePharmacyItem.setSceneType(GoodsConst.SceneType.OUTPATIENT);
        goodsRulePharmacyItem.setDepartmentId(departmentId);
        goodsRulePharmacyItem.setKeyId(chinesePrescriptionFormId);
        if (Objects.nonNull(processInfo) && !Objects.equals(String.format("%d-%d", processInfo.getType(), processInfo.getSubType()), "0-0")) {
            goodsRulePharmacyItem.setProcessType(processInfo.getType());
            goodsRulePharmacyItem.setProcessSubType(processInfo.getSubType());
        }

        List<QueryGoodsRulePharmacyRsp> queryGoodsRulePharmacyRsps = scGoodsService.queryGoodsRulePharmacy(chainId, clinicId, Collections.singletonList(goodsRulePharmacyItem));

        GoodsPharmacyBaseView goodsPharmacyBaseView = Optional.ofNullable(queryGoodsRulePharmacyRsps)
                .orElse(new ArrayList<>())
                .stream()
                .filter(queryGoodsRulePharmacyRsp -> Objects.equals(queryGoodsRulePharmacyRsp.getKeyId(), chinesePrescriptionFormId))
                .findFirst()
                .map(QueryGoodsRulePharmacyRsp::getPharmacy)
                .orElse(null);

        if (Objects.isNull(goodsPharmacyBaseView)) {
            return;
        }

        //把前端传的参数也一起修正了
        Optional.ofNullable(clientReqChargeForms)
                .orElse(new ArrayList<>())
                .stream()
                .filter(chargeForm -> Objects.equals(chargeForm.getId(), chinesePrescriptionFormId))
                .findFirst()
                .ifPresent(chargeForm -> {
                    chargeForm.setPharmacyNo(goodsPharmacyBaseView.getNo());
                    Optional.ofNullable(chargeForm.getChargeFormItems())
                            .orElse(new ArrayList<>())
                            .stream()
                            .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.MEDICINE)
                            .forEach(chargeFormItem -> chargeFormItem.setPharmacyNo(goodsPharmacyBaseView.getNo()));
                });

        //更新药房号
        chinesePrescriptionForm.setPharmacyNo(goodsPharmacyBaseView.getNo());
        Optional.ofNullable(chinesePrescriptionForm.getChargeFormItems())
                .orElse(new ArrayList<>())
                .stream()
                .filter(item -> item.getIsDeleted() == 0)
                .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.MEDICINE)
                .forEach(chargeFormItem -> chargeFormItem.setPharmacyNo(goodsPharmacyBaseView.getNo()));
    }

    //数据库里面有议价，判断是否有改过加工费的加工方式，如果没改过，就需要将数据库中的议价带到这个chargeFormItem上，以保证议价不丢
    public static void updateProcessFormItemExpectedPrice(ChargeForm existedProcessChargeForm, ChargeForm processChargeForm) {

        if (existedProcessChargeForm == null || processChargeForm == null) {
            return;
        }

        if (existedProcessChargeForm.getProcessInfo() == null || processChargeForm.getProcessInfo() == null) {
            return;
        }

        ChargeSheetProcessInfo existedProcessInfo = existedProcessChargeForm.getProcessInfo();
        ChargeSheetProcessInfo processInfo = processChargeForm.getProcessInfo();

        //比较两个加工费是否加工规则是否一致
        if (!Objects.equals(String.format("%d-%d", existedProcessInfo.getType(), existedProcessInfo.getSubType()), String.format("%d-%d", processInfo.getType(), processInfo.getSubType()))) {
            return;
        }

        if (CollectionUtils.isEmpty(existedProcessChargeForm.getChargeFormItems()) || CollectionUtils.isEmpty(processChargeForm.getChargeFormItems())) {
            return;
        }

        //这里不能filter掉删除的item，因为要去找到之前的item的议价信息
        ChargeFormItem existedProcessChargeFormItem = existedProcessChargeForm.getChargeFormItems()
                .stream()
                .findFirst()
                .orElse(null);
        ChargeFormItem processFormItem = processChargeForm.getChargeFormItems()
                .stream()
                .filter(item -> item.getIsDeleted() == 0)
                .findFirst()
                .orElse(null);

        if (existedProcessChargeFormItem == null || processFormItem == null) {
            return;
        }

        if (existedProcessChargeFormItem.isExpectedPriceItem()) {
            processFormItem.setExpectedTotalPrice(existedProcessChargeFormItem.getExpectedTotalPrice());
            processFormItem.setExpectedUnitPrice(existedProcessChargeFormItem.getExpectedUnitPrice());
            processFormItem.setExpectedTotalPriceRatio(existedProcessChargeFormItem.getExpectedTotalPriceRatio());
        }
    }

    private static void deleteDeliveryChargeFormAndDeliveryInfo(ChargeSheet chargeSheet, String operatorId) {

        if (chargeSheet == null || chargeSheet.getChargeForms() == null) {
            return;
        }

        if (chargeSheet.getDeliveryInfo() != null) {
            chargeSheet.getDeliveryInfo().deleteModel(operatorId);
        }

        chargeSheet.getChargeForms()
                .stream()
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.EXPRESS_DELIVERY)
                .forEach(chargeForm -> chargeForm.deleteModel(operatorId));
    }


    public static boolean isDeviceEnableAutoPay(PropertyService propertyService, String clinicId) {
        if (propertyService == null || StringUtils.isEmpty(clinicId)) {
            return false;
        }

        SelfServiceSettings selfServiceSettings = propertyService.getPropertyValueByKey(PropertyKey.SELFSERVICE_SETTINGS, clinicId, SelfServiceSettings.class);

        return selfServiceSettings != null ? selfServiceSettings.getEnableAutoPay() == 1 : false;

    }

    /**
     * 微诊所上来的请求没有选择快递公司，这个函数是把companyId补齐下，选第一个
     *
     * @return 返回值 true补充过，false 没补充过
     */
    public static boolean fixClientDeliveryReqCompanyId(ChargeDeliveryReq deliveryInfo, ChargeSheet chargeSheet, ChargeRuleService chargeRuleService) {
        if (deliveryInfo == null
                || StringUtils.isEmpty(deliveryInfo.getAddressCityId())
                || StringUtils.isEmpty(deliveryInfo.getAddressProvinceId())
                || StringUtils.isEmpty(deliveryInfo.getAddressDistrictId())) {
            return false;
        }

        //稳妥起见，老的chargeSheet里面有companyId，这里不生成(生成后后面会覆盖chargeSheet)
        if (chargeSheet != null && chargeSheet.getDeliveryInfo() != null && !StringUtils.isEmpty(chargeSheet.getDeliveryInfo().getDeliveryCompanyId())) {
            return false;
        }

        //终端请求没有选快递公司，才计算companyId
        if (deliveryInfo.getDeliveryCompany() != null && !StringUtils.isEmpty(deliveryInfo.getDeliveryCompany().getId())) {
            return false;
        }
        List<ChargeRuleExpressDelivery> chargeRuleExpressDeliveries = chargeRuleService.findChargeRuleExpressDelivery(chargeSheet.getChainId(), chargeSheet.getClinicId(),
                deliveryInfo.getAddressProvinceId(), deliveryInfo.getAddressCityId(), deliveryInfo.getAddressDistrictId(), null, deliveryInfo.getDeliveryPayType());

        ChargeRuleExpressDelivery chargeRuleExpressDelivery = chargeRuleExpressDeliveries.stream().sorted(Comparator.comparing(ChargeRuleExpressDelivery::getCreated)).findFirst().orElse(null);

        if (chargeRuleExpressDelivery != null) { //没算成费，返回失败，至少避免少收钱
            if (deliveryInfo.getDeliveryCompany() == null) {
                deliveryInfo.setDeliveryCompany(new ChargeDeliveryReq.DeliveryCompanyReq());
            }
            deliveryInfo.getDeliveryCompany().setId(chargeRuleExpressDelivery.getCompanyId());
        }
        return true;
    }

    public static void bindChargeDeliveryInfo(ChargeDeliveryReq deliveryInfo, ChargeSheet chargeSheet, String operatorId, ChargeService chargeService, ChargeRuleService chargeRuleService) {
        fixClientDeliveryReqCompanyId(deliveryInfo, chargeSheet, chargeRuleService);
        chargeService.createOrUpdateDeliveryInfo(chargeSheet.getDeliveryInfo(), chargeSheet, operatorId, deliveryInfo);
    }

    //清理快递收费单里面的费率,到付也一定要有个收费项
    private static void resetDeliveryChargeFormFee(ChargeSheet chargeSheet, ChargeDeliveryReq deliveryInfo,
                                                   List<ChargeFormReq> clientReqChargeForms, String operatorId, boolean insertNew) {

        Optional.ofNullable(deliveryInfo).ifPresent(deliveryReq -> deliveryReq.setDeliveryFee(BigDecimal.ZERO));
        //清空议价
        Optional.ofNullable(clientReqChargeForms).orElse(new ArrayList<>())
                .stream()
                .filter(chargeFormReq -> chargeFormReq.getSourceFormType() == Constants.SourceFormType.EXPRESS_DELIVERY)
                .filter(chargeFormReq -> chargeFormReq.getChargeFormItems() != null)
                .flatMap(chargeFormReq -> chargeFormReq.getChargeFormItems().stream())
                .filter(chargeFormItemReq -> chargeFormItemReq.getProductType() == Constants.ProductType.EXPRESS_DELIVERY)
                .forEach(chargeFormItemReq -> {
                    chargeFormItemReq.setExecutedUnitCount(null);
                    chargeFormItemReq.setExpectedTotalPrice(null);
                    chargeFormItemReq.setExpectedTotalPriceRatio(null);
                    chargeFormItemReq.setUnitPrice(BigDecimal.ZERO);
                });

        ChargeForm deliveryChargeForm = chargeSheet.getChargeForms().stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.EXPRESS_DELIVERY).findFirst().orElse(null);
        if (deliveryChargeForm != null && !CollectionUtils.isEmpty(deliveryChargeForm.getChargeFormItems())) {
            ChargeFormItem chargeFormItem = deliveryChargeForm.getChargeFormItems().get(0);
            chargeFormItem.setExpectedUnitPrice(null);
            chargeFormItem.setExpectedTotalPrice(null);
            chargeFormItem.setExpectedTotalPriceRatio(null);
            chargeFormItem.setUnitPrice(BigDecimal.ZERO);
            chargeFormItem.setTotalPrice(BigDecimal.ZERO);
            ExpectedPriceHelper.process(chargeFormItem, deliveryInfo != null ? deliveryInfo.getDeliveryFee() : BigDecimal.ZERO, chargeFormItem.getExpectedUnitPrice(), chargeFormItem.getExpectedTotalPrice(), chargeFormItem.getExpectedTotalPriceRatio());
            chargeFormItem.setSourceUnitPrice(deliveryInfo != null ? deliveryInfo.getDeliveryFee() : BigDecimal.ZERO);
            Optional.ofNullable(deliveryInfo).ifPresent(deliveryReq -> chargeFormItem.setProductSnapshot(JsonUtils.dump(deliveryReq)));
        } //没有收费项目就不用清理了
        else if (insertNew) {
            deliveryChargeForm = ChargeFormFactory.insertExpressDeliveryForm(chargeSheet, deliveryInfo, operatorId);
            if (deliveryChargeForm != null) {
                chargeSheet.getChargeForms().add(deliveryChargeForm);
            }
        }
    }

    //设置，更新收费单里面的费率
    private static void setDeliveryChargeFormFee(ChargeSheet chargeSheet, ChargeDeliveryReq deliveryInfoReq,//普通快递地址
                                                 List<ChargeFormReq> clientReqChargeForms,
                                                 String operatorId) {
        ChargeForm deliveryChargeForm = chargeSheet.getChargeForms().stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.EXPRESS_DELIVERY).findFirst().orElse(null);
        if (deliveryChargeForm == null) { //没有插入一条新的
            deliveryChargeForm = ChargeFormFactory.insertExpressDeliveryForm(chargeSheet, deliveryInfoReq, operatorId);
            if (deliveryChargeForm != null) {
                chargeSheet.getChargeForms().add(deliveryChargeForm);
            }
        } else {
            if (CollectionUtils.isEmpty(deliveryChargeForm.getChargeFormItems())) {//没有chargeFormItem，插入一条新的
                List<ChargeFormItem> chargeFormItems = new ArrayList<>();
                ChargeFormItem formItem = ChargeFormItemFactory.insertExpressDeliveryFormItem(deliveryChargeForm, deliveryInfoReq, operatorId);
                if (formItem != null) {
                    chargeFormItems.add(formItem);
                }
                deliveryChargeForm.setChargeFormItems(chargeFormItems);
            } else { //都有，更新一下价格
                ChargeDeliveryInfo deliveryInfo = chargeSheet.getDeliveryInfo();

                if (deliveryInfo != null) {
                    ChargeDeliveryInfo dbDeliveryInfo = new ChargeDeliveryInfo();
                    BeanUtils.copyProperties(deliveryInfo, dbDeliveryInfo);
                    chargeSheet.setDbDeliveryInfo(dbDeliveryInfo);
                }

                //比较地址是否有被改过，如果没有改过，则使用数据库的金额来计算，有议价算议价，如果改过，清空议价，在算快递时会重新计算快递费
                boolean deliveryChanged = false;
                if (deliveryInfo != null && deliveryInfoReq != null) {
                    deliveryChanged = !org.apache.commons.lang3.StringUtils.equals(deliveryInfo.getAddressProvinceId(), deliveryInfoReq.getAddressProvinceId())
                            || !org.apache.commons.lang3.StringUtils.equals(deliveryInfo.getAddressCityId(), deliveryInfoReq.getAddressCityId())
                            || !org.apache.commons.lang3.StringUtils.equals(deliveryInfo.getAddressDistrictId(), deliveryInfoReq.getAddressDistrictId())
                            || !org.apache.commons.lang3.StringUtils.equals(deliveryInfo.getDeliveryCompanyId(), Optional.ofNullable(deliveryInfoReq.getDeliveryCompany()).map(ChargeDeliveryReq.DeliveryCompanyReq::getId).orElse(""))
                            || deliveryInfo.getDeliveryPayType() != deliveryInfoReq.getDeliveryPayType();
                }

                ChargeFormItem chargeFormItem = deliveryChargeForm.getChargeFormItems().get(0);

                if (deliveryChanged) {
                    chargeFormItem.setExpectedUnitPrice(null);
                    chargeFormItem.setExpectedTotalPrice(null);
                    chargeFormItem.setExpectedTotalPriceRatio(null);

                    Optional.ofNullable(clientReqChargeForms)
                            .orElse(new ArrayList<>())
                            .stream()
                            .filter(chargeForm -> CollectionUtils.isNotEmpty(chargeForm.getChargeFormItems()))
                            .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.EXPRESS_DELIVERY)
                            .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                            .forEach(clientChargeFormItem -> {
                                clientChargeFormItem.setExpectedUnitPrice(null);
                                clientChargeFormItem.setExpectedTotalPrice(null);
                                clientChargeFormItem.setExpectedTotalPriceRatio(null);
                            });
                } else {
                    Optional.ofNullable(clientReqChargeForms)
                            .orElse(new ArrayList<>())
                            .stream()
                            .filter(chargeForm -> CollectionUtils.isNotEmpty(chargeForm.getChargeFormItems()))
                            .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.EXPRESS_DELIVERY)
                            .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                            .filter(clientChargeFormItem -> clientChargeFormItem.getProductType() == Constants.ProductType.EXPRESS_DELIVERY)
                            .forEach(clientChargeFormItem -> {
                                clientChargeFormItem.setExpectedUnitPrice(chargeFormItem.getExpectedUnitPrice());
                                clientChargeFormItem.setExpectedTotalPrice(chargeFormItem.getExpectedTotalPrice());
                                clientChargeFormItem.setExpectedTotalPriceRatio(chargeFormItem.getExpectedTotalPriceRatio());
                            });
                }

                //有老的chargeFrom 只更新ChargeFormItem的sourceUnitPrice，有议价用议价
                ExpectedPriceHelper.process(chargeFormItem, deliveryInfoReq.getDeliveryFee(), chargeFormItem.getExpectedUnitPrice(), chargeFormItem.getExpectedTotalPrice(), chargeFormItem.getExpectedTotalPriceRatio());
                chargeFormItem.setSourceUnitPrice(deliveryInfoReq.getDeliveryFee());
                chargeFormItem.setProductSnapshot(JsonUtils.dump(deliveryInfoReq));
                chargeFormItem.setProductInfo(JsonUtils.dumpAsJsonNode(deliveryInfoReq));

            }
        }
    }

    /**
     * 更新前置条件：chargeSheet有对应的收费项目 同时 请求里面也要有同样id的修改项目 【请求里面只认第一个同id的reqItem】
     * <p>
     * 用端上请求reqChargeForms 更新 chargeSheet里面快递，挂号，加工的收费单 chargeFormItem信息
     * 更新内容：
     * 字段1：sourceUnitPrice --原来单价
     * 如果是快递到付:把unitPrice/sourceUnitPrice设置成请求里面的unitPrice
     * 字段2：productSnapshot 快递设置deliveryFee ，加工设置设置：decoctionFee
     */
    private static void updateRegDeliveryProcessChargePriceFromClientReq(List<ChargeFormReq> reqChargeForms, List<ChargeFormItem> chargeFormItems, boolean deliveryPaidByConsignee) {
        if (reqChargeForms == null || chargeFormItems == null) {
            return;
        }

        Map<String, ChargeFormItemReq> reqChargeFormItemMaps = reqChargeForms
                .stream()
                .filter(chargeForm -> chargeForm.getChargeFormItems() != null)
                .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                .collect(Collectors.toMap(ChargeFormItemReq::getId, Function.identity(), (a, b) -> a));

        chargeFormItems.forEach(chargeFormItem -> {
            ChargeFormItemReq reqChargeFormItem = reqChargeFormItemMaps.getOrDefault(chargeFormItem.getId(), null);
            if (reqChargeFormItem != null && reqChargeFormItem.getSourceUnitPrice() != null) {
                chargeFormItem.setSourceUnitPrice(reqChargeFormItem.getSourceUnitPrice());
                chargeFormItem.setProductInfo(JsonUtils.loadAsJsonNode(chargeFormItem.getProductSnapshot()));
                if (chargeFormItem.getProductInfo() != null && chargeFormItem.getProductInfo() instanceof ObjectNode) {
                    if (chargeFormItem.getProductType() == Constants.ProductType.EXPRESS_DELIVERY) {
                        if (deliveryPaidByConsignee) {
                            chargeFormItem.setUnitPrice(reqChargeFormItem.getUnitPrice());
                            chargeFormItem.setSourceUnitPrice(reqChargeFormItem.getUnitPrice()); //端上会比较这两个值，如果不一样，就会高亮表示有议价
                            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "把端上的请求修改同步到chargeSheet，快递费到付，修改后chargeFormItem={}", JsonUtils.dump(reqChargeFormItem));
                        }

                        ((ObjectNode) chargeFormItem.getProductInfo()).put("deliveryFee", reqChargeFormItem.getSourceUnitPrice());
                        chargeFormItem.setProductSnapshot(JsonUtils.dump(chargeFormItem.getProductInfo()));
                    } else if (chargeFormItem.getProductType() == Constants.ProductType.PROCESS) {
                        ((ObjectNode) chargeFormItem.getProductInfo()).put("decoctionFee", reqChargeFormItem.getSourceUnitPrice());
                        chargeFormItem.setProductSnapshot(JsonUtils.dump(chargeFormItem.getProductInfo()));
                    }
                }
            }
        });
    }
}
