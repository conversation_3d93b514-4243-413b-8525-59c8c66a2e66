package cn.abcyun.cis.charge.util;

import cn.abcyun.cis.charge.api.model.ChargeFormReq;
import cn.abcyun.cis.charge.base.ChargeServiceError;
import cn.abcyun.cis.charge.base.ChargeServiceException;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.base.pay.CombinedPayItem;
import cn.abcyun.cis.charge.model.ChargeAction;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.model.ChargeTransaction;
import cn.abcyun.cis.charge.service.dto.PayChargeSheetInfo;
import cn.abcyun.cis.charge.service.rpc.CisCrmService;
import cn.abcyun.common.log.marker.AbcLogMarker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nonnull;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
public class ChargeSheetCheckUtils {


    public static void checkChargeSheetIsThreeMonthAgoOrThrowException(ChargeSheet chargeSheet) {
        if (chargeSheet == null) {
            return;
        }

        Instant firstChargedTime = Optional.ofNullable(chargeSheet.getFirstChargedTime()).orElse(chargeSheet.getCreated());
        Instant canOperateTime = LocalDateTime.ofInstant(firstChargedTime, ZoneId.systemDefault()).plusYears(Constants.CHARGE_SHEET_CAN_OPERATE_YEAR).toInstant(ZoneOffset.of("+8"));

        if (canOperateTime.compareTo(Instant.now()) < 0) {
            log.info("收费单是两年前的收费单, chargeSheetId: {}", chargeSheet.getId());
            throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_ONE_YEAR_AGO);
        }
    }

    public static void checkChargeSheetCanRefundOrThrowException(ChargeSheet chargeSheet, CombinedPayItem payItem, CisCrmService crmService) {

        //校验是否为导入的收费单
        checkChargeSheetIsImportOrThrowException(chargeSheet);

        //如果是家庭医生签约费，校验家庭医生是否解约，如果未解约，则不能退费
        checkFamilyDoctorIsCancelOrThrowException(chargeSheet, crmService);

        //如果欠费还未退完，不允许退其他的支付方式
        checkOwePayIsAllRefundOrThrowException(chargeSheet, payItem);
    }

    private static void checkChargeSheetIsImportOrThrowException(ChargeSheet chargeSheet) {
        if (chargeSheet == null) {
            return;
        }

        if (chargeSheet.getImportFlag() == ChargeSheet.ImportFlag.IMPORT_SHEET_SHOW_IN_QL) {
            throw new ChargeServiceException(ChargeServiceError.IMPORT_CHARGE_SHEET_CANNOT_REFUND);
        }

    }

    private static void checkOwePayIsAllRefundOrThrowException(ChargeSheet chargeSheet, CombinedPayItem payItem) {

        if (Objects.isNull(chargeSheet) || Objects.isNull(payItem)) {
            return;
        }

        BigDecimal owePayModeCanRefundFee = Optional.ofNullable(chargeSheet.getChargeTransactions())
                .orElse(new ArrayList<>())
                .stream()
                .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.OWE_PAY)
                .map(ChargeTransaction::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (chargeSheet.getOwedStatus() == ChargeSheet.OwedStatus.OWING && MathUtils.wrapBigDecimalCompare(owePayModeCanRefundFee, BigDecimal.ZERO) > 0) {

            //如果含有0元医保可以优先退0元医保
            List<String> shebaoActionIds = Optional.ofNullable(chargeSheet.getChargeTransactions())
                    .orElse(new ArrayList<>())
                    .stream()
                    .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                    .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.HEALTH_CARD)
                    .filter(chargeTransaction -> MathUtils.wrapBigDecimalCompare(chargeTransaction.getAmount(), BigDecimal.ZERO) == 0)
                    .map(ChargeTransaction::getChargeActionId)
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(shebaoActionIds) && Optional.ofNullable(chargeSheet.getChargeActions()).orElse(new ArrayList<>())
                    .stream()
                    .filter(chargeAction -> shebaoActionIds.contains(chargeAction.getId()))
                    .noneMatch(chargeAction -> chargeAction.getType() == ChargeAction.Type.REFUND)) {

                return;
            }




            if (payItem.getPayMode() != Constants.ChargePayMode.OWE_PAY) {
                log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX,"欠费金额还未退完，不能退其他支付方式");
                throw new ChargeServiceException(ChargeServiceError.CHARGE_REFUND_EXISTED_OWE_PAY_AMOUNT);
            }

            if (owePayModeCanRefundFee.compareTo(payItem.getAmount()) < 0) {
                log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX,"可退的欠费金额超过了支付的欠费金额");
                throw new ChargeServiceException(ChargeServiceError.CHARGE_REFUND_OVERFLOW);
            }
        }
    }


    //校验家庭医生是否已解约
    public static void checkFamilyDoctorIsCancelOrThrowException(ChargeSheet chargeSheet, CisCrmService crmService) {
        if (chargeSheet == null || crmService == null) {
            return;
        }

        //如果是家庭医生签约费，校验家庭医生是否解约，如果未解约，则不能退费
        if (chargeSheet.getType() != ChargeSheet.Type.FAMILY_DOCTOR_SIGN) {
            return;
        }

        boolean familyDoctorCanRefund = crmService.checkFamilyDoctorCanRefund(chargeSheet.getId());

        if (!familyDoctorCanRefund) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX,"家庭医生未解约");
            throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_FAMILY_DOCTOR_NOT_TERMINATION);
        }
    }

}
