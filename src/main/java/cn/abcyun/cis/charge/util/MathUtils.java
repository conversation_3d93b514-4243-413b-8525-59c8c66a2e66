package cn.abcyun.cis.charge.util;

import cn.abcyun.cis.commons.rpc.charge.ChargeCalculateConfigRsp;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

public class MathUtils {
    public static BigDecimal calculateTotalCount(BigDecimal unitCount, BigDecimal doseCount) {
        if (doseCount == null || doseCount.compareTo(BigDecimal.ONE) < 0) {
            doseCount = BigDecimal.ONE;
        }
        if (unitCount == null) {
            unitCount = BigDecimal.ZERO;
        }
        return doseCount.multiply(unitCount);
    }

    public static BigDecimal calculateTotalPrice(BigDecimal unitPrice, BigDecimal totalCount) {
        if (unitPrice == null) {
            unitPrice = BigDecimal.ZERO;
        }
        if (totalCount == null) {
            totalCount = BigDecimal.ZERO;
        }
        return unitPrice.multiply(totalCount);
    }

    public static BigDecimal calculateTotalPrice(BigDecimal unitPrice, BigDecimal totalCount, int scale) {
        return calculateTotalPrice(unitPrice, totalCount).setScale(scale, RoundingMode.HALF_UP);
    }

    public static BigDecimal calculateLimitTotalPrice(BigDecimal unitPrice, BigDecimal totalCount) {
        return calculateTotalPrice(unitPrice, totalCount).setScale(2, RoundingMode.FLOOR);
    }

    public static BigDecimal calculateTotalPrice(BigDecimal unitPrice, BigDecimal unitCount, BigDecimal doseCount) {
        if (unitPrice == null) {
            unitPrice = BigDecimal.ZERO;
        }
        if (doseCount == null || doseCount.compareTo(BigDecimal.ONE) < 0) {
            doseCount = BigDecimal.ONE;
        }
        if (unitCount == null) {
            unitCount = BigDecimal.ZERO;
        }
        return unitPrice.multiply(unitCount.multiply(doseCount));
    }


    public static BigDecimal calculateTotalPrice(BigDecimal unitPrice, BigDecimal unitCount, BigDecimal doseCount, int scale) {
        return calculateTotalPrice(unitPrice, unitCount, doseCount).setScale(scale, RoundingMode.HALF_UP);
    }

    public static CalculateExpectedUnitPriceResult calculateExpectedUnitPriceBase(BigDecimal unitCount,
                                                                                  BigDecimal doseCount,
                                                                                  BigDecimal expectedTotalPrice,
                                                                                  int scaleUnit,
                                                                                  int scaleTotal) {
        if (unitCount == null || unitCount.compareTo(BigDecimal.ZERO) == 0) {
            unitCount = BigDecimal.ONE;
        }

        if(doseCount == null || doseCount.compareTo(BigDecimal.ZERO) == 0) {
            doseCount = BigDecimal.ONE;
        }
        BigDecimal totalCount = doseCount.multiply(unitCount);

        CalculateExpectedUnitPriceResult result = new CalculateExpectedUnitPriceResult();
        if (expectedTotalPrice == null) {
            result.expectedUnitPrice = BigDecimal.ZERO;
            result.fractionPrice = BigDecimal.ZERO;
            return result;
        }

        //这里要考虑totalCount也有小数的情况
        result.expectedUnitPrice = expectedTotalPrice.divide(totalCount, scaleUnit, RoundingMode.FLOOR);
        BigDecimal newTotalPrice = MathUtils.calculateTotalPrice(result.expectedUnitPrice, totalCount, scaleTotal);
        result.fractionPrice = MathUtils.max(expectedTotalPrice.subtract(newTotalPrice), BigDecimal.ZERO);
        return result;
    }

    public static CalculateExpectedUnitPriceResult calculateExpectedUnitPrice(BigDecimal unitCount, BigDecimal doseCount, BigDecimal expectedTotalPrice) {
       return calculateExpectedUnitPriceBase(unitCount, doseCount, expectedTotalPrice, 2, 2);
    }

    public static BigDecimal calculateTotalPriceFromExpectedTotalPriceRatio(BigDecimal unitCount, BigDecimal doseCount, BigDecimal sourceUnitPrice, BigDecimal expectedTotalPriceRatio) {
        BigDecimal sourceTotalPrice = calculateTotalPrice(sourceUnitPrice, unitCount, doseCount, 2);

        if (expectedTotalPriceRatio == null || expectedTotalPriceRatio.compareTo(BigDecimal.ONE) == 0) {
            return sourceTotalPrice;
        }

        if (expectedTotalPriceRatio.compareTo(BigDecimal.ZERO) < 0) {
            expectedTotalPriceRatio = BigDecimal.ZERO;
        }
        if (expectedTotalPriceRatio.compareTo(BigDecimal.ONE) > 0) {
            expectedTotalPriceRatio = BigDecimal.ONE;
        }

        return sourceTotalPrice.multiply(expectedTotalPriceRatio).setScale(2, RoundingMode.HALF_UP);
    }


    /**
     * 用来计算成本单价 成本单价保留4位小数
     * @param unitCount
     * @param doseCount
     * @param expectedTotalPrice
     * @return
     */
    public static CalculateExpectedUnitPriceResult calculateExpectedCostUnitPrice(BigDecimal unitCount, BigDecimal doseCount, BigDecimal expectedTotalPrice) {
        return calculateExpectedUnitPriceBase(unitCount, doseCount, expectedTotalPrice, 4, 2);
    }

    public static BigDecimal wrapBigDecimal(BigDecimal decimal, BigDecimal defaultDecimal){
        return decimal == null ? defaultDecimal : decimal;
    }

    public static BigDecimal wrapBigDecimalOrZero(BigDecimal decimal) {
        return decimal == null ? BigDecimal.ZERO : decimal;
    }

    public static BigDecimal wrapBigDecimalAdd(BigDecimal a, BigDecimal b) {
        return wrapBigDecimalOrZero(a).add(wrapBigDecimalOrZero(b));
    }

    public static BigDecimal wrapBigDecimalAdd(BigDecimal... a) {
        if (a == null || a.length == 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal result = BigDecimal.ZERO;
        for (BigDecimal i : a) {
            result = result.add(wrapBigDecimalOrZero(i));
        }
        return result;
    }

    public static BigDecimal wrapBigDecimalSubtract(BigDecimal a, BigDecimal b) {
        return wrapBigDecimalOrZero(a).subtract(wrapBigDecimalOrZero(b));
    }

    public static BigDecimal wrapBigDecimalNegateOrZero(BigDecimal decimal) {
        return wrapBigDecimalOrZero(decimal).negate();
    }

    public static BigDecimal wrapBigDecimalMultiply(BigDecimal a, BigDecimal b) {
        return wrapBigDecimalOrZero(a).multiply(b);
    }

    public static BigDecimal setScaleTwo(BigDecimal decimal){
        if(decimal == null){
            return BigDecimal.ZERO;
        }
        return decimal.setScale(2, RoundingMode.HALF_UP);
    }

    public static BigDecimal setScale(int scale, BigDecimal decimal){
        if (scale <= 0) {
            throw new RuntimeException("scale不能小于0");
        }

        if(decimal == null){
            return BigDecimal.ZERO;
        }
        return decimal.setScale(scale, RoundingMode.HALF_UP);
    }

    public static int convertBigDecimalToFenInt(BigDecimal decimal) {
        BigDecimal scaled = setScaleTwo(decimal);
        return scaled.multiply(new BigDecimal(100)).toBigInteger().intValue();
    }

    public static BigDecimal convertFenIntToBigDecimal(int fen) {
        return new BigDecimal(fen).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
    }


    public static int wrapBigDecimalCompare(BigDecimal a, BigDecimal b) {
        return wrapBigDecimalOrZero(a).compareTo(wrapBigDecimalOrZero(b));
    }

    public static BigDecimal min(BigDecimal a, BigDecimal b) {
        return wrapBigDecimalCompare(a, b) > 0 ? b : a;
    }

    public static BigDecimal minAbs(BigDecimal a, BigDecimal b) {
        a = MathUtils.wrapBigDecimalOrZero(a);
        b= MathUtils.wrapBigDecimalOrZero(b);
        return a.abs().compareTo(b.abs()) > 0 ? b : a;
    }

    public static BigDecimal max(BigDecimal a, BigDecimal b) {
        return wrapBigDecimalCompare(a, b) > 0 ? a : b;
    }

    /**
     * 计算金额的比例
     * @param sourceUnitPrice
     * @param unitCount
     * @param doseCount
     * @param expectedTotalPrice
     */
    public static BigDecimal calculateTotalPriceRatio(BigDecimal sourceUnitPrice, BigDecimal unitCount, BigDecimal doseCount, BigDecimal expectedTotalPrice) {

        if (expectedTotalPrice == null) {
            return BigDecimal.ZERO;
        }

        BigDecimal sourceTotalPrice = calculateTotalPrice(sourceUnitPrice, unitCount, doseCount, 2);

        if (sourceTotalPrice.compareTo(expectedTotalPrice) == 0) {
            return BigDecimal.ONE;
        }

        if (sourceTotalPrice.compareTo(BigDecimal.ZERO) <= 0) {
            return null;
        }

        return expectedTotalPrice.divide(sourceTotalPrice, 2, RoundingMode.UP);
    }

    /**
     * 计算单价的比例
     * @param sourceUnitPrice 原始单价
     * @param expectedUnitPrice 期望单价
     */
    public static BigDecimal calculateUnitPriceRatio(BigDecimal sourceUnitPrice, BigDecimal expectedUnitPrice) {

        if (expectedUnitPrice == null) {
            return BigDecimal.ONE;
        }

        if (MathUtils.wrapBigDecimalCompare(sourceUnitPrice, BigDecimal.ZERO) <= 0) {
            return null;
        }

        if (sourceUnitPrice.compareTo(expectedUnitPrice) == 0) {
            return BigDecimal.ONE;
        }

        return expectedUnitPrice.divide(sourceUnitPrice, 2, RoundingMode.UP);
    }

    public static class CalculateExpectedUnitPriceResult {
        public BigDecimal expectedUnitPrice;
        public BigDecimal fractionPrice;
    }

    public static class CalculateExpectedTotalPriceRatioResult extends CalculateExpectedUnitPriceResult {
        public BigDecimal totalPrice;
    }

    public static List<BigDecimal> flat1DimensionalAmount(BigDecimal amount, List<BigDecimal> weights) {
        List<BigDecimal> flatAmounts = new ArrayList<>();
        if (weights == null || weights.size() == 0) {
            return flatAmounts;
        }
        BigDecimal totalWeight = weights.stream().filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (totalWeight.compareTo(BigDecimal.ZERO) == 0) {
            flatAmounts = IntStream.range(0, weights.size()).mapToObj(x -> BigDecimal.ZERO).collect(Collectors.toList());
            return flatAmounts;
        }
        BigDecimal leftAmount = amount;
        for (int i = 0; i < weights.size() - 1; i++) {
            BigDecimal weight = weights.get(i);
            if (weight == null) {
                continue;
            }
            BigDecimal flatAmount = weight.divide(totalWeight, 2, RoundingMode.HALF_UP).multiply(amount);
            flatAmounts.add(flatAmount);
            leftAmount = leftAmount.subtract(flatAmount);
        }
        flatAmounts.add(leftAmount);
        return flatAmounts;
    }


    public static BigDecimal calculateRoundingResult(BigDecimal source, int roundingType) {
        if (source == null) {
            return source;
        }

        switch (roundingType) {
            case ChargeCalculateConfigRsp.RoundingType.ROUND_UP_TO_JIAO:
                source = source.setScale(1, RoundingMode.UP);
                break;
            case ChargeCalculateConfigRsp.RoundingType.ROUND_UP_TO_YUAN:
                source = source.setScale(0, RoundingMode.UP);
                break;
            case ChargeCalculateConfigRsp.RoundingType.ROUND_DOWN_TO_JIAO:
                source = source.setScale(1, RoundingMode.DOWN);
                break;
            case ChargeCalculateConfigRsp.RoundingType.ROUND_DOWN_TO_YUAN:
                source = source.setScale(0, RoundingMode.DOWN);
                break;
            case ChargeCalculateConfigRsp.RoundingType.ROUND_TO_JIAO:
                source = source.setScale(1, RoundingMode.HALF_UP);
                break;
            case ChargeCalculateConfigRsp.RoundingType.ROUND_TO_YUAN:
                source = source.setScale(0, RoundingMode.HALF_UP);
                break;
        }
        return source;
    }

    /**
     * 与0比较，大于0返回1，等于0返回0，小于0返回-1
     */
    public static int compareZero(BigDecimal decimal) {
        return wrapBigDecimalCompare(decimal, BigDecimal.ZERO);
    }

}
