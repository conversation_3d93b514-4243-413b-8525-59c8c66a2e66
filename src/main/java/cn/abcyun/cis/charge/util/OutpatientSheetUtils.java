package cn.abcyun.cis.charge.util;

import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsItem;
import cn.abcyun.cis.charge.base.ChargeVersionConstants;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.model.ChargeFormItem;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.model.ChargeSheetAdditional;
import cn.abcyun.cis.charge.service.rpc.CisScClinicService;
import cn.abcyun.cis.commons.rpc.outpatient.OutpatientSheet;
import cn.abcyun.cis.commons.rpc.outpatient.PrescriptionForm;
import cn.abcyun.cis.commons.util.ListUtils;
import cn.abcyun.cis.commons.util.TextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class OutpatientSheetUtils {

    public static OutpatientSheet filterOutpatientSheetChargedItem(OutpatientSheet outpatientSheet, Set<String> chargedOutpatientItemIds, boolean needFilterEyeGlassPrescription) {
        if (outpatientSheet == null) {
            return outpatientSheet;
        }


        if (!CollectionUtils.isEmpty(outpatientSheet.getProductForms())) {
            outpatientSheet.getProductForms().stream()
                    .filter(productForm -> productForm.getProductFormItems() != null)
                    .forEach(productForm -> productForm.getProductFormItems()
                            .removeIf(productFormItem -> chargedOutpatientItemIds.contains(productFormItem.getId())
                                    || (StringUtils.isNotEmpty(productFormItem.getComposeParentFormItemId()) && chargedOutpatientItemIds.contains(productFormItem.getComposeParentFormItemId()))
                            )
                    );
        }

        if (outpatientSheet.getPrescriptionForms() != null) {
            outpatientSheet.getPrescriptionForms().stream()
                    .filter(prescriptionForm -> prescriptionForm.getPrescriptionFormItems() != null)
                    .forEach(prescriptionForm -> prescriptionForm.getPrescriptionFormItems()
                            .removeIf(prescriptionFormItem -> chargedOutpatientItemIds.contains(prescriptionFormItem.getId())
                                    || (StringUtils.isNotEmpty(prescriptionFormItem.getComposeParentFormItemId()) && chargedOutpatientItemIds.contains(prescriptionFormItem.getComposeParentFormItemId()))
                            )
                    );

            outpatientSheet.getPrescriptionForms().removeIf(prescriptionForm ->  prescriptionForm.getType() == PrescriptionForm.TYPE_EYE_GLASS && needFilterEyeGlassPrescription);
        }

        return outpatientSheet;
    }

    public static Pair<Boolean, ChargeSheet> createOrFindChargeSheet(List<ChargeSheet> existedChargeSheetList,
                                                                     OutpatientSheet outpatientSheet,
                                                                     int isMember,
                                                                     ChargeSheet registrationChargeSheet,
                                                                     CisScClinicService cisScClinicService,
                                                                     String operatorId) {

        // 排除已收费的项目
        Set<String> chargedOutpatientItemIds = existedChargeSheetList.stream()   //已经收费的项目
                .filter(chargeSheet -> chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED && chargeSheet.getChargeForms() != null)
                .flatMap(chargeSheet -> chargeSheet.getChargeForms().stream())
                .filter(Objects::nonNull)
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getChargeFormItems() != null)
                .filter(chargeForm -> chargeForm.getSourceFormType() != Constants.SourceFormType.REGISTRATION)
                .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                .filter(item -> item.getIsDeleted() == 0)
                .map(ChargeFormItem::getSourceFormItemId)
                .filter(id -> !TextUtils.isEmpty(id))
                .collect(Collectors.toSet());

        /**
         * 是否需要过滤配镜处方 存在的已收费的门诊收费单中有配镜处方且是门诊同步过来的 则新的收费单中不能存在配镜处方
         */
        boolean needFilterEyeGlassPrescription = existedChargeSheetList.stream()
                .filter(cs -> cs.getType() == ChargeSheet.Type.OUTPATIENT)
                .filter(cs -> cs.getStatus() != Constants.ChargeSheetStatus.UNCHARGED)
                .filter(chargeSheet -> chargeSheet.getChargeForms() != null)
                .flatMap(chargeSheet -> chargeSheet.getChargeForms().stream())
                .filter(Objects::nonNull)
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .anyMatch(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_TYPE_EYE_GLASS && StringUtils.isNotEmpty(chargeForm.getSourceFormId()));


        OutpatientSheetUtils.filterOutpatientSheetChargedItem(outpatientSheet, chargedOutpatientItemIds, needFilterEyeGlassPrescription);


        ChargeSheet chargeSheet = existedChargeSheetList
                .stream()
                .filter(cs -> cs.getType() == ChargeSheet.Type.OUTPATIENT)
                .filter(cs -> cs.getClonePrescriptionType() != ChargeSheetAdditional.ClonePrescriptionType.FROM_PHOTO_BY_OUTPATIENT_DOCTOR)
                .filter(cs -> cs.getStatus() == Constants.ChargeSheetStatus.UNCHARGED)
                .findFirst()
                .orElse(null);


        boolean isUpdate = false;

        if (chargeSheet == null) {
            chargeSheet = new ChargeSheet();
            chargeSheet.setId(AbcIdUtils.getUUID());
            chargeSheet.setPatientOrderId(outpatientSheet.getPatientOrderId());
            chargeSheet.setChainId(outpatientSheet.getChainId());
            chargeSheet.setClinicId(outpatientSheet.getClinicId());
            chargeSheet.setPatientId(outpatientSheet.getPatientId());
            chargeSheet.setMemberId(isMember == 1 ? outpatientSheet.getPatientId() : "");
            chargeSheet.setStatus(Constants.ChargeSheetStatus.UNCHARGED);
            chargeSheet.setType(ChargeSheet.Type.OUTPATIENT);
            chargeSheet.setOrderByDate(Instant.now());
            chargeSheet.setSourceId(outpatientSheet.getId());
            ChargeUtils.insertChargeSheetAdditionalIfNeed(chargeSheet, operatorId);
            Organ organ = null;
            try {
                organ = cisScClinicService.getOrgan(chargeSheet.getClinicId());
            }catch (Exception e) {
                log.error("query organ error");
            }
            chargeSheet.getAdditional().setChargeVersion(Optional.ofNullable(organ).map(o -> ChargeVersionConstants.convertChargeVersion(o.getHisType())).orElse(ChargeVersionConstants.V0));
            ChargeSheet bindRegistrationChargeSheet = ChargeUtils.pickBindRegistrationChargeSheet(existedChargeSheetList);
            if (registrationChargeSheet != null && registrationChargeSheet.getType() == ChargeSheet.Type.REGISTRATION && bindRegistrationChargeSheet == null) {
                chargeSheet.setRegistrationChargeSheetId(registrationChargeSheet.getId());
                // 挂号费使用了的会员卡，带到收费处
                if (!TextUtils.isEmpty(registrationChargeSheet.getMemberId())) {
                    chargeSheet.setMemberId(registrationChargeSheet.getMemberId());
                }
            }
            FillUtils.fillCreatedBy(chargeSheet, operatorId);
        } else {
            log.info("insertOrUpdateChargeSheetByPatientOrder, existed charge sheet id:{}", chargeSheet.getId());
            isUpdate = true;
            ChargeSheet firstOutpatientChargeSheet = existedChargeSheetList
                    .stream()
                    .filter(cs -> cs.getType() == ChargeSheet.Type.OUTPATIENT)
                    .min(Comparator.comparing(ChargeSheet::getCreated))
                    .orElse(null);
        }

        if (chargeSheet.getChargeForms() == null) {
            chargeSheet.setChargeForms(new ArrayList<>());
        }



        return Pair.of(isUpdate, chargeSheet);
    }

    public static void preHandleOutpatientSheet(OutpatientSheet outpatientSheet) {
        if (Objects.isNull(outpatientSheet)) {
            return;
        }

        if (outpatientSheet.getPrescriptionForms() != null) {
            outpatientSheet.getPrescriptionForms()
                    .stream()
                    .flatMap(prescriptionForm -> ListUtils.alwaysList(prescriptionForm.getPrescriptionFormItems()).stream())
                    .forEach(prescriptionFormItem -> {

                        if (org.apache.commons.collections.CollectionUtils.isEmpty(prescriptionFormItem.getBatchInfos())) {
                            return;
                        }

                        if (Objects.isNull(prescriptionFormItem.getProductInfo())) {
                            return;
                        }

                        GoodsItem goodsItem = JsonUtils.readValue(prescriptionFormItem.getProductInfo(), GoodsItem.class);

                        if (Objects.isNull(goodsItem)) {
                            return;
                        }

                        if (!GoodsLockingUtils.isLockBatchGoodsItem(goodsItem.getLockBatchOps())) {
                            prescriptionFormItem.setBatchInfos(null);
                        }
                    });
        }

        if (outpatientSheet.getProductForms() != null) {
            outpatientSheet.getProductForms()
                    .stream()
                    .flatMap(productForm -> ListUtils.alwaysList(productForm.getProductFormItems()).stream())
                    .forEach(productFormItem -> {

                        if (org.apache.commons.collections.CollectionUtils.isEmpty(productFormItem.getBatchInfos())) {
                            return;
                        }

                        if (Objects.isNull(productFormItem.getProductInfo())) {
                            return;
                        }

                        GoodsItem goodsItem = JsonUtils.readValue(productFormItem.getProductInfo(), GoodsItem.class);

                        if (Objects.isNull(goodsItem)) {
                            return;
                        }

                        if (!GoodsLockingUtils.isLockBatchGoodsItem(goodsItem.getLockBatchOps())) {
                            productFormItem.setBatchInfos(null);
                        }

                    });
        }

    }
}
