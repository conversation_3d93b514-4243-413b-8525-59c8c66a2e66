package cn.abcyun.cis.charge.util;

import cn.abcyun.cis.commons.util.UUIDUtils;
import cn.abcyun.cis.idgenerator.AbcIdGenerator;
import org.apache.commons.lang.StringUtils;

public class AbcIdUtils {

    private static AbcIdGenerator abcIdGenerator;


    public static String getUUID(){
        return abcIdGenerator != null ? abcIdGenerator.getUUID() : UUIDUtils.randomUUID();
    }

    public static Long getUIDLong(){
        return abcIdGenerator != null ? abcIdGenerator.getUIDLong() : null;
    }

    public static String getUID(){
        return abcIdGenerator.getUID();
    }

    public static void setAbcIdGenerator(AbcIdGenerator abcIdGeneratorDefault){
        abcIdGenerator = abcIdGeneratorDefault;
    }

    public static String convertUUidToLongString(String abcUuid){
        String value = abcUuid;

        if (StringUtils.isEmpty(abcUuid)) {
            return value;
        }

        if (abcUuid.length() != 32) {
            return value;
        }
        try {
            value = String.valueOf(Long.parseLong(abcUuid.substring(16), 16));
        }catch (Exception e){
            e.printStackTrace();
        }
        return value;
    }

}
