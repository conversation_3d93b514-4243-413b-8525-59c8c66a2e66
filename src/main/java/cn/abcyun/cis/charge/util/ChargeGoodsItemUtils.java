package cn.abcyun.cis.charge.util;

import cn.abcyun.bis.rpc.sdk.cis.model.charge.ChargeConstants;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsItem;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsSheBaoMatchedCodesInfo;
import cn.abcyun.cis.charge.base.Constants;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Objects;
import java.util.Optional;

public class ChargeGoodsItemUtils {

    /**
     * 这里没有找到对应的支付模式，默认为正常支付
     * @param payMode
     * @return
     */
    public static boolean isGoodsItemShebaoPorhibitPay(Integer payMode) {
        return Objects.equals(Optional.ofNullable(payMode).orElse(GoodsConst.SheBaoPayMode.NORMAL_PAY), GoodsConst.SheBaoPayMode.PORHIBIT_PAY);
    }


    public static boolean isCanPayByHealthCard(GoodsItem goodsItem, Integer shebaoPayType) {

        if (Objects.equals(shebaoPayType, ChargeConstants.ChargeFormItemPayType.SHEBAO_PERSONAL_DISABLED)) {
            return false;
        }

        if (Objects.isNull(goodsItem)) {
            return true;
        }

        // 如果是组合项目  先判断母项是不是能刷医保 如果母项禁止医保支付则表示组合项目不可以医保支付
        // 如果不是 则判断在母项没有禁止医保支付的条件下判断子项  如果有一个子项可以使用医保支付，则母项可以使用医保支付
        String nationalCode = Optional.ofNullable(goodsItem).map(GoodsItem::getShebao).map(GoodsSheBaoMatchedCodesInfo::getNationalCode).orElse(null);
        Integer payMode = Optional.ofNullable(goodsItem).map(GoodsItem::getShebao).map(GoodsSheBaoMatchedCodesInfo::getPayMode).orElse(null);

        if (goodsItem.getCombineType() == GoodsConst.GoodsCombine.COMBINE && goodsItem.getType() == Constants.ProductType.EXAMINATION) {

            if (isCanPayByHealthCardNormal(payMode, nationalCode)) {
                return true;
            } else {
                //判断子项
                if (CollectionUtils.isEmpty(goodsItem.getChildren())) {
                    return false;
                }

                return goodsItem.getChildren().stream().anyMatch(child -> isCanPayByHealthCard(child, shebaoPayType));

            }

        }

     return isCanPayByHealthCardNormal(payMode, nationalCode);
    }

    public static boolean isCanPayByHealthCardNormal(Integer payMode, String nationalCode) {


        /**
         * 如果是子项且 不能用社保支付或者没有医保码则返回false
         */
        if (ChargeGoodsItemUtils.isGoodsItemShebaoPorhibitPay(payMode)
                || StringUtils.isEmpty(nationalCode) || Objects.equals(nationalCode, "DISABLED")) {

            return false;
        }

        return true;
    }
}
