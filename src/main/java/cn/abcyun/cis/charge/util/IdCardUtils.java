package cn.abcyun.cis.charge.util;

import org.apache.commons.lang3.StringUtils;

/**
 * 身份证工具类
 *
 * <AUTHOR>
 * @version IdCardUtils.java, 2023/7/17 7:18 PM
 */

public class IdCardUtils {
    /**
     * 身份证15位转18位
     *
     * @param idCard 15位的身份证
     * @return 结果
     */
    public static String covert15To18(String idCard) {
        if (StringUtils.isBlank(idCard) || StringUtils.length(idCard) != 15) {
            return idCard;
        }

        StringBuilder sb = new StringBuilder(idCard);
        sb.insert(6, "19");

        int[] weights = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};
        char[] checkCodes = {'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'};

        int sum = 0;
        for (int i = 0; i < sb.length(); i++) {
            sum += (sb.charAt(i) - '0') * weights[i];
        }

        int remainder = sum % 11;
        sb.append(checkCodes[remainder]);
        return sb.toString();
    }
}
