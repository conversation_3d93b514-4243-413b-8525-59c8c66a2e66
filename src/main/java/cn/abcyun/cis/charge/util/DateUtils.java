package cn.abcyun.cis.charge.util;

import org.apache.commons.lang.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.Date;

public class DateUtils {

    public static final String DATE_FORMAT_YYYYMMDD = "yyyyMMdd";
    public static final String WECHAT_TIME_FORMAT = "yyyy-MM-dd HH:mm";
    public static final String WECHAT_PAY_SUCCESS_TIME_FORMAT = "yyyy-MM-dd";
    public static String DATE_FORMAT_COMPACT_DATETIME = "yyyyMMddHHmmss";
    public static String DATE_FORMAT_COMPACT_DATETIME_SSS = "yyyyMMddHHmmssSSS";

    public static String convertInstantToString(Instant instant, String format){
        Date tmpDate= Date.from(instant);
        SimpleDateFormat formatter = new SimpleDateFormat(format);
        return formatter.format(tmpDate);
    }

    public static SimpleDateFormat datetimeFormat(String format) {
        if (StringUtils.isEmpty(format)) {
            throw new RuntimeException("format不能为空");
        }
        return new SimpleDateFormat(format);
    }

    public static Date parseToDate(String dateStr, String format) {
        if(StringUtils.isEmpty(dateStr)) {
            return null;
        }

        Date date = null;
        try {
            date = datetimeFormat(format).parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }

        return date;
    }
}
