package cn.abcyun.cis.charge.util;

import cn.abcyun.bis.rpc.sdk.cis.model.patient.CisPatientAge;
import cn.abcyun.cis.commons.message.SubscribeMessage;
import cn.abcyun.cis.commons.util.DateUtils;
import cn.abcyun.cis.commons.util.TextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;

@Slf4j
public class CommonUtils {

    public static final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    public static final Pattern MOBILE_PATTERN = Pattern.compile("^[1]([2-9])[0-9]{5,11}$");

    public static final Pattern HOME_MOBILE_PATTERN = Pattern.compile("^(?:0[1-9][0-9]{1,5}-?)?[0-9]{5,11}$");

    public static Pattern NUMBER_PATTERN = Pattern.compile("^[0-9]{1,16}$");

    public static Long parseLongId(String idStr) {
        if (StringUtils.isBlank(idStr)) {
            return null;
        }

        Long id = null;
        try {
            id = Long.parseLong(idStr);
        } catch (Exception e) {
            return null;
        }
        return id > 0 ? id : null;
    }

    public static Date getBirthDayFromAge(int ageYear, int ageMonth, int ageDay) {
        return getBirthDayFromAge(Calendar.getInstance(), ageYear, ageMonth, ageDay);
    }

    private static Date getBirthDayFromAge(Calendar today, int ageYear, int ageMonth, int ageDay) {
        int nowYear = today.get(Calendar.YEAR);
        // Calender 月份从 0 开始，所以加 1
        int nowMonth = today.get(Calendar.MONTH) + 1;
        int nowDay = today.get(Calendar.DAY_OF_MONTH);

        int birthYear = nowYear - ageYear;
        int birthMonth = nowMonth - ageMonth;
        int birthDay = nowDay - ageDay;

        if (birthMonth < 0) {
            birthYear -= 1;
            birthMonth += 12;
        }

        if (birthDay < 0) {
            birthMonth -= 1;
            // 我们想要得到出生那个月一共有多少天，所以月份直接传 birthMonth，然后 dayOfMonth 直接传 0，是因为我们就是想取上个月的最后一天的日期（Calender 会自动把 birthMonth - 1）
            int dayOfBirthMonth = new Calendar.Builder().setDate(birthYear, birthMonth, 0).build().get(Calendar.DAY_OF_MONTH);
            // 加完之后可能还是存在为负数的情况，比如 1月31天 减去 4月1日，但是这种情况就交由 Calendar 处理，不需要我们自己处理
            birthDay += dayOfBirthMonth;
        }

        if (birthMonth < 0) {
            birthYear -= 1;
            birthMonth += 12;
        }

        // Calendar 月份从 0 开始，所以减 1
        Calendar birthday = new Calendar.Builder().setDate(birthYear, birthMonth - 1, birthDay).build();
        return DateUtils.getStartTime(birthday.getTime());
    }

    public static String getDateString(Date date) {
        if (date == null) {
            return null;
        }
        return dateTimeFormatter.format(date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
    }

//    public static String getBirthdayByIdCard(String idCard) {
//        if (StringUtils.isEmpty(idCard) || (!IdcardUtil.isValidCard15(idCard) && !IdcardUtil.isValidCard18(idCard))) {
//            return null;
//        }
//
//        String birthday = IdcardUtil.getBirthByIdCard(idCard);
//        if (birthday != null && birthday.length() == 8) {
//            return birthday.substring(0, 4) + "-" + birthday.substring(4, 6) + "-" + birthday.substring(6);
//        }
//        return null;
//    }

    public static boolean isValidPhoneNumber(String number) {
        if (StringUtils.isBlank(number)) {
            return true;
        }

        return MOBILE_PATTERN.matcher(number).matches() || HOME_MOBILE_PATTERN.matcher(number).matches();
    }

    public static boolean isNumberMatch(String str) {
        return NUMBER_PATTERN.matcher(str).matches();
    }


    public static CisPatientAge calculatePatientAge(Instant beginTime, String birthday) {
        if (beginTime == null || StringUtils.isEmpty(birthday)) {
            return null;
        }
        LocalDate startDate = parseLocalDate(birthday);
        if (startDate == null) {
            return null;
        }

        CisPatientAge age = new CisPatientAge();

        try {
            LocalDateTime endDateTime = DateUtils.toLocalDateTime(beginTime);
            LocalDate endDate = endDateTime.toLocalDate();

            int year = endDate.getYear() - startDate.getYear();
            int month = endDate.getMonthValue() - startDate.getMonthValue();
            int days = endDate.getDayOfMonth() - startDate.getDayOfMonth();

            if (month < 0) {
                year--;
                month += 12;
            }

            if (days < 0) {
                // 当月不够减的时候，则需要扣1个月，然后增加1个月的天数到day上
                // 取开始日期的当月最大天数，作为加上的天数
                // 即 days = endDateDay - (startDateDay + startDateMonthLength)
                // 因为每个月的1号都是和其他月的1号差距整月时间的，所以在算天数的时候，需要先让月份进入到下一个月的1号，再看还差多久
                month--;
                int lastMonthLength = startDate.lengthOfMonth();
                days += lastMonthLength;
            }

            if (month < 0) {
                year--;
                month += 12;
            }

            age.setYear(year);
            age.setMonth(month);
            age.setDay(days);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return age;
    }

    private static LocalDate parseLocalDate(String date) {
        if (TextUtils.isEmpty(date)) {
            return null;
        }
        try {
            return LocalDate.parse(date);
        } catch (Exception e) {
            log.warn("parseLocalDate catch exception", e);
        }
        return null;
    }

    public static String generateEventGroupId(String clinicId,
                                              SubscribeMessage.EventGroup group,
                                              List<String> params) {
        Assert.notNull(group, "group must not be null");
        switch (group) {
            case PE_INDIVIDUAL_ORDER:
            case PE_REPORT:
            case PE_REPORT_MANAGER:
            case PE_TODAY:
                params.add(0, clinicId);
                break;

            default:
                break;
        }
        return SubscribeMessage.generateEventGroupId(group, params);
    }

    public static String getAgeText(cn.abcyun.cis.commons.model.CisPatientAge age) {
        if (age == null) {
            return null;
        }

        StringBuilder sb = new StringBuilder();
        sb.append(age.getYear() != null ? age.getYear() : 0).append(".");
        sb.append(age.getMonth() != null ? age.getMonth() : 0).append(".");
        sb.append(age.getDay() != null ? age.getDay() : 0);
        return sb.toString();
    }

    public static String getBirthDayFromAge(cn.abcyun.cis.commons.model.CisPatientAge age) {
        if (age == null) {
            return null;
        }
        int year = age.getYear() != null ? age.getYear() : 0;
        int month = age.getMonth() != null ? age.getMonth() : 0;
        int day = age.getDay() != null ? age.getDay() : 0;

        return age != null ? getDateString(getBirthDayFromAge(year, month, day)) : null;
    }

    public static String getMobileLast4(String mobile) {
        if (StringUtils.isNotEmpty(mobile) && mobile.length() > 4) {
            return mobile.substring(mobile.length() - 4);
        } else {
            return mobile;
        }
    }

    public static <T> T selectNonNullValue(T... values) {
        for (T value : values) {
            if (Objects.nonNull(value)) {
                return value;
            }
        }
        return null;
    }
}
