package cn.abcyun.cis.charge.util;

import cn.abcyun.cis.charge.processor.FlatPriceHelper;
import cn.abcyun.cis.charge.processor.FlatReceivedPriceHelper;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 摊费工具帮助类
 */
@Slf4j
public class FlatPriceTool {

    /**
     * 平摊实收金额，平摊的金额不会超过每个cell的最大可平摊金额，会收到每个cell的最大金额的限制
     *
     * @param toFlatReceivedPrice 需要平摊的金额
     * @param sourceCells         需要平摊的cell集合
     */
    public static void flatReceivedPriceAndApply(BigDecimal toFlatReceivedPrice, List<IFlatCell<FlatReceivedPriceHelper.FlatCell>> sourceCells) {

        if (CollectionUtils.isEmpty(sourceCells)) {
            log.info("sourceCells is empty, toFlatReceivedPrice: {}", toFlatReceivedPrice);
            return;
        }

        FlatReceivedPriceHelper flatReceivedPriceHelper = new FlatReceivedPriceHelper(toFlatReceivedPrice);

        flatReceivedPriceHelper.flat(sourceCells.stream().map(IFlatCell::getFlatCell).collect(Collectors.toList()));

        sourceCells.forEach(IFlatCell::defaultApply);
    }


    /**
     * 平摊实收金额，平摊的金额不会超过每个cell的最大可平摊金额，会收到每个cell的最大金额的限制
     *
     * @param toFlatPrice 需要平摊的金额
     * @param sourceCells         需要平摊的cell集合
     */
    public static void flatPriceAndApply(BigDecimal toFlatPrice, List<IFlatCell<FlatPriceHelper.FlatPriceCell>> sourceCells) {

        if (CollectionUtils.isEmpty(sourceCells)) {
            log.info("sourceCells is empty, toFlatPrice: {}", toFlatPrice);
            return;
        }

        FlatPriceHelper flatPriceHelper = new FlatPriceHelper(toFlatPrice);
        flatPriceHelper.flat(sourceCells.stream().map(IFlatCell::getFlatCell).collect(Collectors.toList()));

        sourceCells.forEach(IFlatCell::defaultApply);
    }

    public abstract static class IFlatCell<T extends IFlatCell.BasicCell> {

        private T flatCell;

        public T getFlatCell() {
            if (Objects.isNull(flatCell)) {
                flatCell = genFlatCell();
            }
            return flatCell;
        }

        /**
         * 生成摊费单元
         *
         * @return
         */
        protected abstract T genFlatCell();

        /**
         * 摊费之后对摊费金额进行业务使用
         *
         * @param flatPrice 摊费之后的金额
         */
        protected abstract void apply(BigDecimal flatPrice);

        /**
         * 大多数情况下，直接使用默认的apply即可，如果需要FlatCell对象本身，那么自己实现这个方法，去获取FlatCell再做其他操作
         */
        public void defaultApply() {
            if (Objects.isNull(getFlatCell())) {
                return;
            }
            apply(getFlatCell().getFlatPrice());
        }

        @Data
        @Accessors(chain = true)
        public static class BasicCell {
            private BigDecimal flatPrice;
        }
    }
}
