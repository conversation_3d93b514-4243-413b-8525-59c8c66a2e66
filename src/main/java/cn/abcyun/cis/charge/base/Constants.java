package cn.abcyun.cis.charge.base;

/**
 *
 *
 * <AUTHOR> @version 1.0 2022-11-15 10:00:00
 * @since JDK 1.8
 */
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.invoice.CreateInvoiceCallbackReq;
import cn.abcyun.bis.rpc.sdk.cis.model.invoice.InvoiceStatusCallbackReq;
import cn.abcyun.bis.rpc.sdk.cis.model.shorturl.UploadAttachmentsByQrCodeCallbackReq;
import cn.abcyun.bis.rpc.sdk.cis.model.wallet.PayOrderCallbackReq;
import cn.abcyun.cis.charge.controller.rpc.ChargeBusinessCallbackRpcController;
import cn.abcyun.cis.commons.rpc.wechat.WeChatPayCallbackReq;
import cn.abcyun.cis.commons.rpc.wechat.WeChatPayReq;
import cn.abcyun.cis.commons.rpc.wechat.WeChatRefundCallbackReq;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;

public class Constants {

    public static final String ANONYMOUS_PATIENT_ID = "00000000000000000000000000000000";
    public static final String SYSTEM_CHAIN_ID = "00000000000000000000000000000000";

    public static final String DEFAULT_OPERATORID = "00000000000000000000000000000000";
    public static final String TREATMENT_SITE_ACUPUNCTURE = "穴位";

    public static final String PUSH_ORDER_REMARK = "请在" + Constants.WECHAT_CHARGE_ORDER_EXPIRE_DAY + "天内完成支付，否则支付订单将失效";

    public static final int WECHAT_CHARGE_ORDER_EXPIRE_DAY = 3;

    /**
     * 收费单可操作的年数
     * 20220829从一年改为了两年
     */
    public static final int CHARGE_SHEET_CAN_OPERATE_YEAR = 2;

    /**
     * 发送网诊收费单在15分钟内还未确认的再次推送的消息，15分钟，转换为毫秒
     */
    public static final int ONLINE_CHARGE_SHEET_PUSH_AGAIN_DELAY_MILLIS = 15 * 60 * 1000;

    /**
     * 收费台锁库后自动解锁时间
     */
    public static final int LOCKING_AUTO_UNLOCK_DELAY_SECOND = 5 * 60;

    public static final int AUTO_UNLOCK_MINUTES_DEFAULT = 5;
    public static final int AUTO_UNLOCK_MINUTES_FOR_SHEBAO = 10;

    /**
     * 支付方式列表redis的过期分钟数
     */
    public static final int CHARGE_PAY_MODE_REDIS_EXPIRE_MINUTES = 10;

    /**
     * 收费单需要上传告知书的门槛金额
     */
    public static final BigDecimal CHARGE_NEED_UPLOAD_NOTIFICATION_LOWEST_LINE = new BigDecimal(500);

    public static class SystemProductId {
        public static final String REGISTRATION_PRODUCT_ID = "00000000000000000000000000000001";
        public static final String ONLINE_CONSULTATION_PRODUCT_ID = "00000000000000000000000000000002";
        public static final String EXPRESS_DELIVERY_PRODUCT_ID = "00000000000000000000000000000003";

        public static final String INGREDIENT_PRODUCT_ID = "00000000000000000000000000000005";
        public static final String FAMILY_DOCTOR_SIGN_ID = "00000000000000000000000000000006";
        // 营销卡项开卡
        public static final String PROMOTION_CARD_OPEN_ID = "00000000000000000000000000000007";
        // 营销卡项充值
        public static final String PROMOTION_CARD_RECHARGE_ID = "00000000000000000000000000000008";
        // 会员充值
        public static final String MEMBER_CARD_RECHARGE_ID = "00000000000000000000000000000009";

        // 加工费
        public static final String PROCESS_PRODUCT_ID = "00000000000000000000000000000010";
        // 加工费-煎药费
        public static final String PROCESS_TISANES_PRODUCT_ID = "00000000000000000000000000000004";
        // 加工费-制膏
        public static final String PROCESS_CREAM_PRODUCT_ID = "00000000000000000000000000000011";
        // 加工费-打粉
        public static final String PROCESS_POWDER_PRODUCT_ID = "00000000000000000000000000000012";
        // 加工费-制丸
        public static final String PROCESS_PILL_PRODUCT_ID = "00000000000000000000000000000013";
        // 加工费-颗粒
        public static final String PROCESS_PARTICLES_PRODUCT_ID = "00000000000000000000000000000014";
        // 加工费-茶包
        public static final String PROCESS_TEA_BAG_PRODUCT_ID = "00000000000000000000000000000015";
        // 加工费-胶囊
        public static final String PROCESS_CAPSULE_PRODUCT_ID = "00000000000000000000000000000016";
        // 加工费-煎药费-手工煎药
        public static final String PROCESS_TISANES_MANUAL_PRODUCT_ID = "00000000000000000000000000000017";
        // 加工费-煎药费-机器煎药（普通）
        public static final String PROCESS_TISANES_MACHINE_NORMAL_PRODUCT_ID = "00000000000000000000000000000018";
        // 加工费-煎药费-机器煎药（浓缩）
        public static final String PROCESS_TISANES_MACHINE_STRONG_PRODUCT_ID = "00000000000000000000000000000019";
        // 加工费-煎药费-机器煎药（普通-先煎）
        public static final String PROCESS_TISANES_MACHINE_NORMAL_DECOCTED_FIRST_PRODUCT_ID = "00000000000000000000000000000037";
        // 加工费-煎药费-机器煎药（浓缩-先煎）
        public static final String PROCESS_TISANES_MACHINE_STRONG_DECOCTED_FIRST_PRODUCT_ID = "00000000000000000000000000000038";
        // 加工费-制膏-儿童膏方
        public static final String PROCESS_CREAM_CHILD_PRODUCT_ID = "00000000000000000000000000000020";
        // 加工费-制膏-成人膏方
        public static final String PROCESS_CREAM_ADULT_PRODUCT_ID = "00000000000000000000000000000021";
        // 加工费-打粉-粗粉
        public static final String PROCESS_POWDER_COARSE_PRODUCT_ID = "00000000000000000000000000000022";
        // 加工费-打粉-细粉
        public static final String PROCESS_POWDER_FINE_PRODUCT_ID = "00000000000000000000000000000023";
        // 加工费-打粉-超细粉
        public static final String PROCESS_POWDER_SUPER_FINE_PRODUCT_ID = "00000000000000000000000000000024";
        // 加工费-制丸-水丸
        public static final String PROCESS_PILL_WATER_PRODUCT_ID = "00000000000000000000000000000025";
        // 加工费-制丸-蜜丸
        public static final String PROCESS_PILL_SWEET_PRODUCT_ID = "00000000000000000000000000000026";
        // 加工费-制丸-水蜜丸
        public static final String PROCESS_PILL_WATER_SWEET_PRODUCT_ID = "00000000000000000000000000000027";
        // 加工费-制丸-浓缩丸
        public static final String PROCESS_PILL_ENRICHMENT_PRODUCT_ID = "00000000000000000000000000000028";

        /**
         * 便民门诊
         */
        public static final String CONVENIENT_REGISTRATION_PRODUCT_ID = "00000000000000000000000000000034";

        /**
         * 专家门诊
         */
        public static final String EXPERT_PROCESS_PILL_ENRICHMENT_PRODUCT_ID = "00000000000000000000000000000035";

        public static final String PROCESS_NAME = "加工费";
        public static final String EXPRESS_DELIVERY_NAME = "快递费";
        public static final String INGREDIENT_NAME = "辅料费";
        public static final String REGISTRATION_NAME = "挂号费";
        public static final String FAMILY_DOCTOR_SIGN_NAME = "签约费";
        public static final String PROMOTION_CARD_OPEN_NAME = "卡项开通费";
        public static final String PROMOTION_CARD_RECHARGE_NAME = "卡项充值费";
        public static final String MEMBER_CARD_RECHARGE_NAME = "会员充值费";


        public static final List<String> SYSTEM_PRODUCT_IDS = Arrays.asList(
                REGISTRATION_PRODUCT_ID,
                ONLINE_CONSULTATION_PRODUCT_ID,
                EXPRESS_DELIVERY_PRODUCT_ID,
                INGREDIENT_PRODUCT_ID,
                FAMILY_DOCTOR_SIGN_ID,
                PROMOTION_CARD_OPEN_ID,
                PROMOTION_CARD_RECHARGE_ID,
                MEMBER_CARD_RECHARGE_ID,

                PROCESS_PRODUCT_ID,
                PROCESS_TISANES_PRODUCT_ID,
                PROCESS_CREAM_PRODUCT_ID,
                PROCESS_POWDER_PRODUCT_ID,
                PROCESS_PILL_PRODUCT_ID,
                PROCESS_PARTICLES_PRODUCT_ID,
                PROCESS_TEA_BAG_PRODUCT_ID,
                PROCESS_CAPSULE_PRODUCT_ID,
                PROCESS_TISANES_MANUAL_PRODUCT_ID,
                PROCESS_TISANES_MACHINE_NORMAL_PRODUCT_ID,
                PROCESS_TISANES_MACHINE_STRONG_PRODUCT_ID,
                PROCESS_TISANES_MACHINE_NORMAL_DECOCTED_FIRST_PRODUCT_ID,
                PROCESS_TISANES_MACHINE_STRONG_DECOCTED_FIRST_PRODUCT_ID,
                PROCESS_CREAM_CHILD_PRODUCT_ID,
                PROCESS_CREAM_ADULT_PRODUCT_ID,
                PROCESS_POWDER_COARSE_PRODUCT_ID,
                PROCESS_POWDER_FINE_PRODUCT_ID,
                PROCESS_POWDER_SUPER_FINE_PRODUCT_ID,
                PROCESS_PILL_WATER_PRODUCT_ID,
                PROCESS_PILL_SWEET_PRODUCT_ID,
                PROCESS_PILL_WATER_SWEET_PRODUCT_ID,
                PROCESS_PILL_ENRICHMENT_PRODUCT_ID
        );
    }

    public static class SourceFormType {
        public static final int NONE = 0;
        public static final int REGISTRATION = 1;
        public static final int EXAMINATION = 2;
        public static final int TREATMENT = 3;
        public static final int PRESCRIPTION_WESTERN = 4;
        public static final int PRESCRIPTION_INFUSION = 5;
        // 中药处方
        public static final int PRESCRIPTION_CHINESE = 6;
        public static final int ADDITIONAL_FORM = 7;
        public static final int ADDITIONAL_SALE_PRODUCT_FORM = 8;
        //材料
        public static final int MATERIAL = 9;

        //整单优惠的赠品
        public static final int GIFT_PRODUCT = 10;

        //套餐
        public static final int COMPOSE_PRODUCT = 11;

        //在线问诊
        public static final int ONLINE_CONSULTATION = 12;

        //快递费用
        public static final int EXPRESS_DELIVERY = 13;

        //加工费
        public static final int PROCESS = 14;

        //空中药房
        public static final int AIR_PHARMACY = 15;

        //外治处方
        public static final int PRESCRIPTION_EXTERNAL = 16;

        //家庭医生签约
        public static final int FAMILY_DOCTOR_SIGN = 17;

        //营销卡项开卡
        public static final int PROMOTION_CARD_OPEN = 18;

        //营销卡项充值
        public static final int PROMOTION_CARD_RECHARGE = 19;

        public static final int MEMBER_CARD_RECHARGE = 20;

        //其他费用
        public static final int OTHER_FEE = 21;

        public static final int PRESCRIPTION_EYE = 22;

        public static final int NURSING = 23;

        public static final int PRESCRIPTION_TYPE_EYE_GLASS = 24;

        /**
         * 单品优惠的赠品
         */
        public static final int SINGLE_PROMOTION_GIFT_PRODUCT = 25;
        // 手术
        public static final int SURGERY = 26;

        /**
         * 20250407
         * 指定赠品，这个为新加的form，由于历史数据存在各个form下都可能有指定赠品，所以不能只根据这个form来判断单子里面有没有指定赠品，需要循环item去判断
         */
        public static final int MARKED_GIFT_PRODUCT = 27;

        public static final List<Integer> OUTPATIENT_SOURCE_FORM_TYPES = new ArrayList<Integer>() {{
            add(EXAMINATION);
            add(TREATMENT);
            add(ADDITIONAL_SALE_PRODUCT_FORM);
            add(MATERIAL);
            add(COMPOSE_PRODUCT);
            add(OTHER_FEE);
            add(NURSING);
            add(PRESCRIPTION_EYE);
            add(SURGERY);
        }};

        public static final List<Integer> MEDICAL_PLAN_SOURCE_FORM_TYPES = new ArrayList<Integer>() {{
            add(EXAMINATION);
            add(TREATMENT);
            add(ADDITIONAL_SALE_PRODUCT_FORM);
            add(MATERIAL);
            add(COMPOSE_PRODUCT);
            add(OTHER_FEE);
        }};


        public static Map<Integer, String> SOURCE_FORM_TYPE_NAME_MAP = new HashMap<Integer, String>() {{
            this.put(NONE, "");
            this.put(REGISTRATION, "挂号");
            this.put(EXAMINATION, "检查检验");
            this.put(TREATMENT, "治疗理疗");
            this.put(PRESCRIPTION_WESTERN, "中西成药处方");
            this.put(PRESCRIPTION_INFUSION, "输注处方");
            this.put(PRESCRIPTION_CHINESE, "中药处方");
            this.put(ADDITIONAL_FORM, "门诊收费单新加收费项目");
            this.put(ADDITIONAL_SALE_PRODUCT_FORM, "门诊收费单新增商品");
            this.put(MATERIAL, "物资");
            this.put(GIFT_PRODUCT, "赠品");
            this.put(COMPOSE_PRODUCT, "套餐");
            this.put(ONLINE_CONSULTATION, "在线问诊");
            this.put(EXPRESS_DELIVERY, "快递费用");
            this.put(PROCESS, "加工费");
            this.put(AIR_PHARMACY, "空中药房");
            this.put(PRESCRIPTION_EXTERNAL, "外治处方");
            this.put(FAMILY_DOCTOR_SIGN, "家庭医生签约");
            this.put(PROMOTION_CARD_OPEN, "营销卡项开卡");
            this.put(PROMOTION_CARD_RECHARGE, "营销卡项充值");
            this.put(MEMBER_CARD_RECHARGE, "会员卡充值");
            this.put(PRESCRIPTION_EYE, "配镜处方");
            this.put(NURSING, "护理医嘱");
            this.put(SURGERY, "手术");
            this.put(MARKED_GIFT_PRODUCT, "指定赠品");
        }};

        public static String getSourceFormTypeName(int sourceFormType) {
            return SOURCE_FORM_TYPE_NAME_MAP.getOrDefault(sourceFormType, "");
        }

    }

    public static class SourceItemType {

        /**
         * 普通
         */
        public static final int NORMAL = 0;

        /**
         * 自备
         */
        public static final int SELF_PROVIDED = 1;

    }

    public static class PrintFormType {
        public static final int NONE = 0;
        public static final int REGISTRATION = 1;
        public static final int EXAMINATION = 2;
        public static final int TREATMENT = 3;
        public static final int PRESCRIPTION_WESTERN = 4;
        public static final int PRESCRIPTION_INFUSION = 5;
        public static final int PRESCRIPTION_CHINESE = 6;
        public static final int ADDITIONAL_FORM = 7;
        public static final int ADDITIONAL_SALE_PRODUCT_FORM = 8;
        //材料
        public static final int MATERIAL = 9;

        //赠品
        public static final int GIFT_PRODUCT = 10;

        //套餐
        public static final int COMPOSE_PRODUCT = 11;

        //在线问诊
        public static final int ONLINE_CONSULTATION = 12;

        //快递费用
        public static final int EXPRESS_DELIVERY = 13;

        //加工费
        public static final int PROCESS = 14;

        //空中药房
        public static final int AIR_PHARMACY = 15;

        //外治处方
        public static final int PRESCRIPTION_EXTERNAL = 16;

        //家庭医生签约
        public static final int FAMILY_DOCTOR_SIGN = 17;

        //营销卡项开卡
        public static final int PROMOTION_CARD_OPEN = 18;

        //营销卡项充值
        public static final int PROMOTION_CARD_RECHARGE = 19;

        /**
         * 其他费用
         */
        public static final int OTHER_FEE = 21;

        public static final int PRESCRIPTION_EYE = 22;

        public static final int NURSING = 23;

        public static final int PRESCRIPTION_TYPE_EYE_GLASS = 24;
        // 手术
        public static final int SURGERY = 26;
    }

    public static class ChargeSheetStatus {
        public static final int UNCHARGED = 0;
        public static final int PART_CHARGED = 1;
        public static final int CHARGED = 2;
        public static final int PART_REFUNDED = 3;
        public static final int REFUNDED = 4;
    }


    public static class ChargeFormStatus {
        public static final int UNCHARGED = 0;
        public static final int CHARGED = 1;
        public static final int REFUNDED = 2;

    }

    public static class ChargeFormItemStatus {
        public static final int NONE = -1;
        public static final int UNCHARGED = 0;
        public static final int CHARGED = 1;
        public static final int REFUNDED = 2;
        public static final int UNSELECTED = 3;
        public static final int COMPOSE_SUB_REFUNDED = 4;

        public static boolean availableExecuteStatus(int formItemStatus) {
            return formItemStatus != NONE && formItemStatus != UNSELECTED;
        }
    }

    public static class ChargeFormItemGiftType {

        /**
         * 不是赠品
         */
        public static final int NOT_GIFT = 0;

        /**
         * 营销活动赠品
         */
        public static final int PROMOTION_GIFT = 1;

        /**
         * 人为标记的赠品
         */
        public static final int MARKED_GIFT = 2;

    }

    /**
     * 追溯码显示状态
     */
    public static class ChargeFormItemShowTraceableCode {

        /**
         * 隐藏
         */
        public static final int HIDE = 0;

        /**
         * 显示
         */
        public static final int SHOW = 1;

    }

    public static class ChargePayMode {
        public static final int NONE = 0;
        public static final int CASH = 1;
        public static final int WECHAT_PAY = 2;
        public static final int ALIPAY = 3;
        public static final int BANK_CARD = 4;
        public static final int HEALTH_CARD = 5;
        public static final int MEMBER_CARD = 6;

        public static final int COUPON = 7;//代金券
        public static final int MEITUAN_PAY = 8;//美团支付
        public static final int KOUBEI_PAY = 9;//口碑支付
        public static final int NUOMI_PAY = 10;//糯米支付
        public static final int SHOUQIANBA_PAY = 11;//收钱吧

        public static final int DIANPING_PAY = 12;//点评
        public static final int FUBEI_PAY = 13;//付呗
        public static final int LAKALA_PAY = 14;//拉卡拉
        public static final int LIANLIAN_PAY = 15;//连连支付

        public static final int YOUZAN_RETAIL = 16; //有赞零售
        public static final int YOUZAN_PLEDGE_CASH = 17; //有赞抵现
        public static final int PROMOTION_CARD = 18; //营销卡项
        public static final int ABC_PAY = 19; //ABC支付
        public static final int OWE_PAY = 20; //欠费支付
        public static final int SHEBAO_AIR_PAY = 21; //青岛空中支付
        public static final int SHEBAO_MULAID_PAY = 23; //广西代支支付，共计支付

        public static final int OUTPATIENT_CENTER_PAY = 24; //沈阳诊间支付

        public static final int SHEBAO_QINGDAO_UNION_POS_PAY = 25; //青岛银联POS支付
        public static final int SHE_BAO_RAILWAY_PAY = 26; //铁路支付

        public static final int SHEBAO_YIMA_PAY = 27; //社保一码付
        
        public static final int COMMON_YIMA_PAY = 28; //通用一码付

        public static final int SYSTEM_PAY_MODE_MAX = 100;

        /**
         * 可通过此方式开通的支付方式ID列表
         */
        public static final List<Long> ALLOWED_PAY_MODE_CONFIG_IDS_BY_JENKINS = Arrays.asList(
                (long) Constants.ChargePayMode.SHE_BAO_RAILWAY_PAY
        );

        public static List<Integer> SHEBAO_PAY_MODES = Arrays.asList(
                HEALTH_CARD,
                OUTPATIENT_CENTER_PAY,
                SHEBAO_AIR_PAY,
                SHEBAO_MULAID_PAY,
                SHE_BAO_RAILWAY_PAY,
                SHEBAO_YIMA_PAY,
                SHEBAO_QINGDAO_UNION_POS_PAY
        );

        //社保标记异常的转换


        /**
         * 患者端支持的收费方式
         */
        public static final List<String> PATIENT_SUPPORT_PAY_MODES = new ArrayList<String>() {{
            //微信
            add(String.format("%d-%d", WECHAT_PAY, ChargePaySubMode.H5));
            add(String.format("%d-%d", WECHAT_PAY, ChargePaySubMode.NATIVE));
            add(String.format("%d-%d", WECHAT_PAY, ChargePaySubMode.MINI_PROGRAM));

            //abc支付
            add(String.format("%d-%d", ABC_PAY, ChargePaySubMode.ABC_PAY_WECHAT_NATIVE));
            add(String.format("%d-%d", ABC_PAY, ChargePaySubMode.ABC_PAY_WECHAT_JS));
            add(String.format("%d-%d", ABC_PAY, ChargePaySubMode.ABC_PAY_WECHAT_MINI));
            add(String.format("%d-%d", ABC_PAY, ChargePaySubMode.ABC_PAY_ALI_NATIVE));
            add(String.format("%d-%d", ABC_PAY, ChargePaySubMode.ABC_PAY_QQ_NATIVE));
            add(String.format("%d-%d", ABC_PAY, ChargePaySubMode.ABC_PAY_UNION_NATIVE));

            //会员卡
            add(String.format("%d-%d", MEMBER_CARD, ChargePaySubMode.ONLY_RECORD));

            //卡项
            add(String.format("%d-%d", PROMOTION_CARD, ChargePaySubMode.ONLY_RECORD));

            // 社保微信支付
            add(String.format("%d-%d", HEALTH_CARD, ChargePaySubMode.WECHAT_MOBILE_SHEBAO));
        }};

        /**
         * 系统默认必选的收费方式
         */
        public static final List<Integer> SYSTEM_REQUIRED_PAY_MODES = Arrays.asList(CASH, WECHAT_PAY, ALIPAY,
                BANK_CARD, HEALTH_CARD, MEMBER_CARD);

        /**
         * 系统默认可选的收费方式
         */
        public static final List<Integer> SYSTEM_OPTIONAL_PAY_MODES = Arrays.asList(COUPON, MEITUAN_PAY, KOUBEI_PAY,
                NUOMI_PAY, SHOUQIANBA_PAY, DIANPING_PAY,
                FUBEI_PAY, LAKALA_PAY, LIANLIAN_PAY,
                YOUZAN_RETAIL, YOUZAN_PLEDGE_CASH, PROMOTION_CARD, OWE_PAY);

        /**
         * 系统有条件限制的方式，目前只有abc,空中支付 支付
         */
        public static final List<Integer> SYSTEM_WITH_CONDITIONAL_PAY_MODES = Arrays.asList(ABC_PAY, SHEBAO_AIR_PAY, SHEBAO_MULAID_PAY, OUTPATIENT_CENTER_PAY, SHEBAO_QINGDAO_UNION_POS_PAY);

        /**
         * 需要校验异步退款结果的支付方式
         */
        public static final List<Integer> NEED_CHECK_REFUND_RESULT_PAY_MODES = Arrays.asList(WECHAT_PAY, ABC_PAY);

        public static boolean canNotUpdatePayMode(int payMode, int paySubMode, String thirdPartyPayTransactionId) {

            if (paySubMode != ChargePaySubMode.ONLY_RECORD || StringUtils.isNotEmpty(thirdPartyPayTransactionId)) {
                return true;
            }

            return payMode == MEMBER_CARD
                    || payMode == ABC_PAY
                    || payMode == OWE_PAY
                    || payMode == PROMOTION_CARD
                    || (payMode == HEALTH_CARD && StringUtils.isNotEmpty(thirdPartyPayTransactionId))
                    || (payMode == WECHAT_PAY && StringUtils.isNotEmpty(thirdPartyPayTransactionId))
                    || payMode == SHEBAO_AIR_PAY
                    || payMode == SHEBAO_MULAID_PAY
                    || payMode == SHE_BAO_RAILWAY_PAY
                    || payMode == OUTPATIENT_CENTER_PAY
                    || payMode == SHEBAO_QINGDAO_UNION_POS_PAY;
        }

        public static List<Integer> changePayModeCanNotToUpdatePayModes() {
            return Arrays.asList(MEMBER_CARD, ABC_PAY, OWE_PAY, PROMOTION_CARD, SHEBAO_AIR_PAY, SHEBAO_MULAID_PAY, OUTPATIENT_CENTER_PAY, SHEBAO_QINGDAO_UNION_POS_PAY);
        }

        /**
         * 可原路退回的支付方式
         */
        public static final List<Integer> SUPPORT_ORIGINAL_REFUND_PAY_MODES = Arrays.asList(ABC_PAY);
    }

    public static class ChargePayModeType {

        public static final int UN_KNOW = -1;

        //系统必选的
        public static final int SYSTEM_REQUIRED = 1;
        //系统可选的
        public static final int SYSTEM_OPTIONAL = 2;
        //诊所自定义的
        public static final int CLINIC_CUSTOMIZED = 3;

        //系统有条件限制的支付方式（需要开通某些开关之后才可以使用的支付方式）
        public static final int SYSTEM_WITH_CONDITIONAL = 4;

        /**
         * 第三方通用支付
         */
        public static final int THIRD_PARTY_COMMON_PAY = 5; // = ChargeConstants.ChargePayModeConfigType.THIRD_PARTY_COMMON_PAY;
    }

    public static class ChargePaySubMode {
        /**
         * 记账
         */
        public static final int ONLY_RECORD = 0;    //仅记账
        /**
         * 社保卡支付
         */
        public static final int HEALTH_CARD = 1;    //社保刷卡
        /**
         * 微信JSAPI支付
         */
        public static final int H5 = 2;             //H5网页支付
        /**
         * 微信二维码支付
         */
        public static final int NATIVE = 3;      //二维码支付
        /**
         * 微信小程序支付
         */
        public static final int MINI_PROGRAM = 4;      //小程序支付
        /**
         * ABC微信扫码支付
         */
        public static final int ABC_PAY_WECHAT_NATIVE = 5;
        /**
         * ABC微信JSAPi支付
         */
        public static final int ABC_PAY_WECHAT_JS = 6;
        /**
         * ABC微信小程序支付
         */
        public static final int ABC_PAY_WECHAT_MINI = 7;
        /**
         * ABC支付宝扫码支付
         */
        public static final int ABC_PAY_ALI_NATIVE = 8;
        /**
         * ABC手机QQ 扫码支付
         */
        public static final int ABC_PAY_QQ_NATIVE = 9;
        /**
         * ABC银联扫码支付
         */
        public static final int ABC_PAY_UNION_NATIVE = 10;
        /**
         * ABC主扫支付(扫码枪)
         */
        public static final int ABC_PAY_SCAN_QR_CODE = 11;
        /**
         * 主扫微信支付
         */
        public static final int ALLIN_PAY_SCAN_WECHAT = 12;
        /**
         * 主扫支付宝支付
         */
        public static final int ALLIN_PAY_SCAN_ALI = 13;
        /**
         * 主扫银联支付
         */
        public static final int ALLIN_PAY_SCAN_UNION = 14;

        /**
         * 诊间支付-医保部分
         */
        public static final int OUTPATIENT_CENTER_PAY_INSURANCE = 15;

        /**
         * 诊间支付或者一码付，这种现金部分的支付渠道（支付宝）
         */
        public static final int SHEBAO_CASH_ALI_PAY = 16;

        /**
         * 诊间支付或者一码付，这种现金部分的支付渠道（微信）
         */
        public static final int SHEBAO_CASH_WECHAT_PAY = 17;

        /**
         * 诊间支付或者一码付，这种现金部分的支付渠道（银行卡）
         */
        public static final int SHEBAO_CASH_BANK_CARD_PAY = 18;

        /**
         * ABC一码付-主扫支付(扫码枪)
         */
        public static final int ABC_PAY_SCAN_ONE_QR_CODE = 19;



        /**
         * 支付宝一码付-主扫支付(扫码枪)
         */
        public static final int ALI_PAY_SCAN_ONE_QR_CODE = 20;
        /**
         * 社保微信支付
         */
        public static final int WECHAT_MOBILE_SHEBAO = 100;
        /**
         * 微信医保支付中-医保部分
         */
        public static final int WECHAT_MOBILE_SHEBAO_INSURANCE = 101;
        /**
         * 微信医保支付中-现金部分
         */
        public static final int WECHAT_MOBILE_SHEBAO_CASH = 120;



        public static final List<Integer> allInPayNeedSendPaySuccessMessageSubMode = Arrays.asList(ABC_PAY_WECHAT_NATIVE, ABC_PAY_WECHAT_JS,
                ABC_PAY_WECHAT_MINI, ABC_PAY_ALI_NATIVE, ABC_PAY_QQ_NATIVE, ABC_PAY_UNION_NATIVE);
    }

    public static class ProductType {

        // 药品
        public static final int MEDICINE = 1;

        public static final int MATERIAL = 2;

        public static final int EXAMINATION = 3;

        public static final int TREATMENT = 4;

        // 挂号
        public static final int REGISTRATION = 5;

        public static final int MEMBER_CARD_RECHARGE = 6;

        public static final int SALE_PRODUCT = 7;
        /**
         * 套餐
         */
        public static final int COMPOSE_PRODUCT = 11;

        public static final int ONLINE_CONSULTATION = 12;

        // 快递费用
        public static final int EXPRESS_DELIVERY = 13;

        // 加工费
        public static final int PROCESS = 14;

        // 辅料费
        public static final int INGREDIENT = 15;

        // 家庭医生签约费
        public static final int FAMILY_DOCTOR_SIGN = 16;

        // 营销卡项开卡
        public static final int PROMOTION_CARD_OPEN = 17;

        // 营销卡项充值
        public static final int PROMOTION_CARD_RECHARGE = 18;

        // 其他费用
        public static final int OTHER_FEE = 19;

        // 眼镜goods类型
        public static final int EYE = 24;

        // 护理
        public static final int NURSE = GoodsConst.GoodsType.NURSE;
        public static final int SURGERY = GoodsConst.GoodsType.SURGERY;

        public static Map<Integer, String> REFUND_NOTIFICATION_NAME_MAP = new HashMap<Integer, String>() {{
            this.put(MEDICINE, "药品药材");
            this.put(MATERIAL, "材料");
            this.put(EXAMINATION, "检查检验");
            this.put(TREATMENT, "治疗理疗");
            this.put(REGISTRATION, "挂号");
            this.put(MEMBER_CARD_RECHARGE, "会员卡充值");
            this.put(SALE_PRODUCT, "商品材料");
            this.put(COMPOSE_PRODUCT, "套餐");
            this.put(ONLINE_CONSULTATION, "咨询");
            this.put(PROCESS, "加工");
            this.put(INGREDIENT, "辅料");
            this.put(OTHER_FEE, "其他");
            this.put(EXPRESS_DELIVERY, "快递");
            this.put(FAMILY_DOCTOR_SIGN, "家庭医生签约");
            this.put(PROMOTION_CARD_OPEN, "营销卡项开卡");
            this.put(PROMOTION_CARD_RECHARGE, "营销卡项充值");
            this.put(EYE, "眼镜");
            this.put(NURSE, "护理医嘱");
            this.put(SURGERY, "手术");
        }};

        public static String getProductTypeName(int productType) {
            return REFUND_NOTIFICATION_NAME_MAP.getOrDefault(productType, "");
        }

        public static final List<Integer> SYSTEM_FEE_PRODUCT_TYPE = Arrays.asList(REGISTRATION, EXPRESS_DELIVERY, PROCESS,
                ONLINE_CONSULTATION, INGREDIENT, FAMILY_DOCTOR_SIGN,
                PROMOTION_CARD_OPEN, PROMOTION_CARD_RECHARGE, MEMBER_CARD_RECHARGE
        );

        /**
         * 不参与打折的goodsType
         *
         * @return
         */
        public static List<Integer> notNeedDiscountProductTypes() {
            return Arrays.asList(MEMBER_CARD_RECHARGE, PROMOTION_CARD_RECHARGE, PROMOTION_CARD_OPEN);
        }

        public static final List<Integer> USE_REMARK_PRODUCT_TYPE = Arrays.asList(MATERIAL, EXAMINATION, PROCESS, SALE_PRODUCT, EXPRESS_DELIVERY, INGREDIENT, REGISTRATION, TREATMENT, COMPOSE_PRODUCT,
                NURSE, OTHER_FEE, ONLINE_CONSULTATION, SURGERY
        );

        public static Map<String, String> WUAI_INVOICE_CODE_MAP = new HashMap<String, String>() {{
            this.put(MEDICINE + "-" + SubType.MEDICINE_WESTERN, "32080108");  //西药费
            this.put(MEDICINE + "-" + SubType.MEDICINE_CHINESE, "32080110");  //中药饮片
            this.put(MEDICINE + "-" + SubType.MEDICINE_CHINESE_COMPOSE, "32080111");  //中成药
            this.put(MATERIAL + "", "32080107");  //卫生材料费
            this.put(EXAMINATION + "-" + SubType.EXAMINATION_INSPECTION, "32080103");  //检查费
            this.put(EXAMINATION + "-" + SubType.EXAMINATION_EXAMINATION, "32080104"); //检验费
            this.put(TREATMENT + "", "32080105");  //治疗费
            this.put(REGISTRATION + "", "32080101"); //挂号费
            this.put(OTHER_FEE + "", "32080199"); //其他费用
            this.put(SURGERY + "", "32080106"); //手术费
        }};


        public static Map<String, String> WUAI_INVOICE_NAME_MAP = new HashMap<String, String>() {{
            this.put("32080108", "西药费");  //西药费
            this.put("32080110", "中药饮片");  //中药饮片
            this.put("32080111", "中成药费");  //中成药
            this.put("32080107", "卫生材料费");  //卫生材料费
            this.put("32080103", "检查费");  //检查费
            this.put("32080104", "化验费"); //检验费
            this.put("32080105", "治疗费");  //治疗费
            this.put("32080101", "挂号费"); //挂号费
            this.put("32080199", "其他门急诊费用"); //其他费用
            this.put("32080106", "手术费"); //手术费
        }};


        public static class SubType {

            // 西药
            public static final int MEDICINE_WESTERN = 1;

            // 中药
            public static final int MEDICINE_CHINESE = 2;

            // 中成药
            public static final int MEDICINE_CHINESE_COMPOSE = 3;

            public static final int MEDICAL_MATERIAL = 1;

            public static final int TREATMENT_TREATMENT = 1;

            public static final int TREATMENT_PHYSIOTHERAPY = 2;

            public static final int TREATMENT_OTHER = 9;

            // 检验费
            public static final int EXAMINATION_EXAMINATION = 1;

            // 检查费
            public static final int EXAMINATION_INSPECTION = 2;
        }

        public static class MedicineChineseSpecification {
            public static final String DRINKS_PIECE = "中药饮片";
            public static final String PARTICLE = "中药颗粒";

            public static final int DRINKS_PIECE_TYPE_ID = 14;
            public static final int PARTICLE_TYPE_ID = 15;

        }
    }

    public static class DispensingStatus {
        public static final int NONE = -1;
        public static final int WAITING = 0;
        public static final int DISPENSED = 1;
        public static final int UNDISPENSED = 2;
    }

    public static class DispensingFormItemStatus {
        public static final int WAITING = 0;
        public static final int DISPENSED = 1;
        public static final int UNDISPENSED = 2;
        public static final int CANCELED = 3;
    }

    /**
     * 一个chargeSheet可以开出多个空中药房订单，目前空中药房的发药状态是维护在每个chargeForm里面的
     * 自助支付需要维护所有空中药房chargeform的发态状态，方便批量接口查询。
     * -1 没有空中药房
     * [0-100) 空中药房发药订单数量
     * 100 空中药房订单全部已经发药 ，假设一个chargesheet不可能开出100个空中药房chargeForm
     */
    public static class AirDispensingStatus {
        public static final int NONE = -1;
        public static final int WAITING = 0; //在订单初始化，发现需要有空中药房的时候就应该初始化
        public static final int ALL_DISPENSED = 100;

    }

    public static class ChargeSheetDraftStatus {
        public static final int IS_DRAFT = 1;
        public static final int NOT_DRAFT = 0;
    }

    public static class ChargeFilterStatusName {
        public static final String UNCHARGED = "uncharged";
        public static final String DRAFT = "draft";
        public static final String CHARGED = "charged";
        public static final String REFUNDED = "refunded";
        public static final String CHARNGED_REFUNDED = "chargedRefunded";
    }

    /**
     * //1:待收，2：网诊，3：挂单，4：欠费
     */
    public static class ChargeQLTab {
        public static final int UNCHARGED = 1;
        public static final int ONLINE = 2;
        public static final int DRAFT = 3;
        public static final int OWE_SHEET = 4;
    }

    public enum LimitPriceTypeEnum {
        // 类别： 1：西成药; 2：中药; 3：医用材料; 4：检查校验; 5：治疗项目，6：理疗项目 7.其他类型

        //西成药(西药和中成药)
        WESTERN_MEDICINE(1, new ArrayList<String>() {{
            add("1,1");
            add("1,3");
        }}, LimitPriceProductType.MEDICINE, Arrays.asList(LimitPriceType.LAST_PRICE, LimitPriceType.SHEBAO_PRICE)),
        //中药
        CHINESE_MEDICINE(2, new ArrayList<String>() {{
            add("1,2");
        }}, LimitPriceProductType.MEDICINE, Arrays.asList(LimitPriceType.LAST_PRICE, LimitPriceType.SHEBAO_PRICE)),
        //医用材料
        MEDICAL_MATERIAL(3, new ArrayList<String>() {{
            add("2,1");
        }}, LimitPriceProductType.MEDICINE, Arrays.asList(LimitPriceType.LAST_PRICE, LimitPriceType.SHEBAO_PRICE)),
        //检查检验
        EXAMINATION(4, new ArrayList<String>() {{
            add("3,1");
            add("3,2");
        }}, LimitPriceProductType.PROJECT, Arrays.asList(LimitPriceType.SALES_PRICE, LimitPriceType.SHEBAO_PRICE)),
        //治疗项目
        TREATMENT(5, new ArrayList<String>() {{
            add("4,1");
        }}, LimitPriceProductType.PROJECT, Arrays.asList(LimitPriceType.SALES_PRICE, LimitPriceType.SHEBAO_PRICE)),
        //理疗项目
        PHYSIOTHERAPY(6, new ArrayList<String>() {{
            add("4,2");
        }}, LimitPriceProductType.PROJECT, Arrays.asList(LimitPriceType.SALES_PRICE, LimitPriceType.SHEBAO_PRICE)),

        //其他费用
        OTHER_FEE(7, new ArrayList<String>() {{
            add("19,0");
        }}, LimitPriceProductType.FEE_TYPE, Arrays.asList(LimitPriceType.SALES_PRICE, LimitPriceType.SHEBAO_PRICE));

        private int type;

        //类别与goods表的关系list，例如：1,1 代表：type=1,sub_type=1
        private List<String> typeRelations;

        //对应v2_charge_medicare_limit_price_product表的type
        private int productType;

        private List<Integer> priceType;

        LimitPriceTypeEnum(int type, List<String> typeRelations, int productType, List<Integer> priceType) {
            this.type = type;
            this.typeRelations = typeRelations;
            this.productType = productType;
            this.priceType = priceType;
        }

        public static String formatProductType(Integer productType, Integer productSubType) {
            String productTypeStr = Optional.ofNullable(productType).map(String::valueOf).orElse("");
            String productSubTypeStr = Optional.ofNullable(productSubType).map(String::valueOf).orElse("");
            return String.format("%s,%s", productTypeStr, productSubTypeStr);
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public List<String> getTypeRelations() {
            return typeRelations;
        }

        public void setTypeRelations(List<String> typeRelations) {
            this.typeRelations = typeRelations;
        }

        public int getProductType() {
            return productType;
        }

        public void setProductType(int productType) {
            this.productType = productType;
        }

        public List<Integer> getPriceType() {
            return priceType;
        }

        public void setPriceType(List<Integer> priceType) {
            this.priceType = priceType;
        }

        public static LimitPriceTypeEnum getLimitPriceTypeEnumByType(int type) {

            for (LimitPriceTypeEnum value : LimitPriceTypeEnum.values()) {
                if (value.getType() == type) {
                    return value;
                }
            }
            return null;
        }

        public static LimitPriceTypeEnum getLimitPriceTypeEnumByGoodsTypeAndSubType(int type, int subType) {
            String goodsTypes = formatProductType(type, subType);

            for (LimitPriceTypeEnum value : LimitPriceTypeEnum.values()) {
                List<String> typeRelations = value.getTypeRelations();

                if (typeRelations.contains(goodsTypes)) {
                    return value;
                }
            }
            return null;
        }

        public static boolean isNeedOpenLockBatch(int type) {
            if (type == LimitPriceTypeEnum.WESTERN_MEDICINE.getType() || type == LimitPriceTypeEnum.CHINESE_MEDICINE.getType() || type == LimitPriceTypeEnum.MEDICAL_MATERIAL.getType()) {
                return true;
            }
            return false;

        }
    }

    public static class LimitPriceProductType {
        public static final Integer MEDICINE = 1;
        public static final Integer PROJECT = 2;
        public static final Integer FEE_TYPE = 3;

        public static final List<Integer> HOSPITAL_TYPE_LIST = Arrays.asList(MEDICINE, FEE_TYPE);
        public static final List<Integer> NOT_HOSPITAL_TYPE_LIST = Arrays.asList(MEDICINE, PROJECT);

    }

    public static class LimitPriceType {
        //最近进价
        public static final Integer LAST_PRICE = 1;
        //销售定价
        public static final Integer SALES_PRICE = 2;

        public static final Integer SHEBAO_PRICE = 3;
    }

    public static class ChargePayModeConfigType {

        //系统默认必选
        public static final int SYSTEM = 1;

        //系统默认可选
        public static final int OPTIONAL = 2;

        //连锁自定义
        public static final int CUSTOM = 3;

        /**
         * 系统内置且有条件的情况下才会开通的支付方式
         */
        public static final int SYSTEM_WITH_CONDITIONAL = 4;

        /**
         * 第三方通用支付
         */
        public static final int THIRD_PARTY_COMMON_PAY = 5;
    }

    /**
     * public static class ChargeSheetStatus {
     * public static final int UNCHARGED = 0;
     * public static final int PART_CHARGED = 1;
     * public static final int CHARGED = 2;
     * public static final int PART_REFUNDED = 3;
     * public static final int REFUNDED = 4;
     * <p>
     * }
     */
    //这些字段不是db存储的，是处理后发送给微诊所的状态字段
    public static class ChargeOrderStatus {
        public static final int WAITING_PAY = 0; //status ==ChargeSheetStatus.UNCHARGED  &&  getReceivedFee == 0
        public static final int PART_PAID = 1;
        public static final int IS_PAID = 2;
        public static final int PART_REFUND = 3;
        public static final int IS_REFUND = 4;
        public static final int WAITING_DISPENSE = 5; //待发药 status == ChargeOrderStatus.IS_PAID && deliveryType == 1 && dispeningStatus=0
        public static final int WAITING_GET_MEDICINE = 6;//待收药
        public static final int WAITING_CONFIRM_PRICE = 7; //划价中 ChargeSheetStatus.UNCHARGED == status && CheckStatus.NEED_CLINIC_CHECK == checkStatus
        public static final int GET_SOME_MEDICINE = 8;//部分收药
        public static final int COMPLETE = 9;//订单完成
        public static final int SOME_DISPENSE = 10;//部分发药
    }

    public static class PaySuccessTips {
        public static final String NO_DISPENSE = "您已支付成功。";
        public static final String NEED_DISPENSE = "您已支付成功，请到药房处取药";
        public static final String SEND_TO_HOME_DISPENSE = "您已支付成功，发药后将通知您";

    }

    public static class ChargeRuleType {
        //固定收费规则
        public static final int SETTLED_RULE = 1;

        //阶梯收费规则
        public static final int LADDER_RULE = 2;
    }

    //门诊处方空中药房type
    public static class OutpatientPrescriptionPharmacyType {

        public static final int LOCAL = 0;

        public static final int AIR_PHARMACY = 1;

    }

    /**
     * 自助支付状态
     * tinyint -128 ，127
     */
    public static class ChargeSheetSelfPayStatus {
        //不能自助支付
        public static final int DISABLE_SELF_PAY = 0;

        //可以自助支付
        public static final int SELF_PAY_WAITING_PAY = 10;

        //自助支付完成订单支付
        public static final int SELF_PAY_USER_SELF_PAID = 20;

        //非自助支付完成支付
        public static final int SELF_PAY_CHARGE_PAID = 30;
    }

    /**
     * 收费自动发药状态
     */
    public static class ChargeAutoDispensingStatus {
        //收费不自动发药
        public static final int NOT_DISPENSING_MEDCINE = 0;

        //收费自动发药
        public static final int CHARGE_AUTO_DISPENSING = 1;
    }

    /**
     * 快递支付类型
     */
    public static class DeliveryPayType {
        //到付
        public static final int PAID_BY_CONSIGNEE = 0;

        //寄款
        public static final int PAID_BY_SHIPPER = 1;
    }


    /**
     * 表示收费单的算费来源场景
     * 不共用 ChargeTransaction.PaySource的原因，这个用的地方太多了
     */
    public static class ChargeSource {
        public static final int CHARGE = 0; //　收费台的算费费/
        public static final int REGISTRATION = 1;//挂号算费
        public static final int MEMBER_CARD_RECHARGE = 2; //患者会员充值处的算费费
        public static final int WE_CLINIC = 3;      //微诊所支付算费
        public static final int DEVICE = 4; //自助服务机算费
        public static final int WE_APP = 5; //小程序

        /**
         * 后台自动退费
         */
        public static final int BACKGROUND_AUTO_REFUND = 99;

        /**
         * 后台算费
         */
        public static final int BACKGROUND = 98;

        public static List<Integer> patientPaySources() {
            return Arrays.asList(WE_CLINIC, DEVICE, WE_APP);
        }

    }

    /**
     * 锁单状态：0：未锁单，10-30：锁单中(10-20是患者锁单，10:患者在微诊所锁的单，11患者在取号机锁的单，20-30是收费台锁单，20收费员锁的单）
     * Rule 1.锁单不能修改收费单
     * Rule 2.谁锁单，谁解锁【角色】
     * Rule 3.锁单状态的单可以支付
     */
    @Deprecated
    public static class ChargeSheetLockStatus {
        public static final int LOCK_STATUS_UNLOCK = 0;  //解锁可编辑状态

        public static final int LOCK_STATUS_PATIENT_WECLINIC = 10; //病人锁单状态 --在微诊所锁单
        public static final int LOCK_STATUS_PATIENT_DEVICE = 11;//病人锁单状态 --在取号机锁单
        public static final int LOCK_STATUS_PATIENT_PERMANENT = 12; //持久加锁，不支持自动解锁，只能通过业务自己解锁
        public static final int LOCK_STATUS_PATIENT_WEAPP = 13; //病人锁单状态 --小程序锁单

        public static final int LOCK_STATUS_CHARGE_STATION = 20; //医生锁单--在收费台锁单
    }

    public static class ChargeSheetLockStatusV2 {
        /**
         * 锁单状态第二版，位存储，0未锁单据，1锁单据，2：微诊所患者锁单，4：自助服务机锁单，8：收费员锁单, 16：退费锁单
         */
        public static final int NONE = 0;
        public static final int LOCK_CHARGE_SHEET = 0x0001; //锁收费单

        public static final int LOCK_PAY_BY_PATIENT_FROM_WE_CLINIC = 0x0002; //微诊所患者支付锁单

        public static final int LOCK_PAY_BY_PATIENT_FROM_DEVICE = 0x0004; //自助服务机支付锁单

        public static final int LOCK_PAY_BY_CHARGE_STATION = 0x0008; //收费员支付锁单

        public static final int LOCK_REFUND = 0x0010; //退费锁单

        public static boolean isLockingForPay(int lockStatus) {
            if (lockStatus == 0) {
                return false;
            }
            return (lockStatus & LOCK_PAY_BY_PATIENT_FROM_WE_CLINIC) > 0
                    || (lockStatus & LOCK_PAY_BY_PATIENT_FROM_DEVICE) > 0
                    || (lockStatus & LOCK_PAY_BY_CHARGE_STATION) > 0;
        }

        public static boolean isLockingForRefund(int lockStatus) {
            if (lockStatus == 0) {
                return false;
            }
            return (lockStatus & LOCK_REFUND) > 0;
        }

        /**
         * 将老的锁单状态转换为新的锁单状态
         * @param oldLockStatus
         * @return
         */
        public static int convertOldLockStatusToLockStatusV2(int oldLockStatus) {
            int lockStatus = NONE;

            switch (oldLockStatus) {
                case ChargeSheetLockStatus.LOCK_STATUS_PATIENT_WECLINIC:
                case ChargeSheetLockStatus.LOCK_STATUS_PATIENT_WEAPP:
                    lockStatus = LOCK_PAY_BY_PATIENT_FROM_WE_CLINIC;
                    break;
                case ChargeSheetLockStatus.LOCK_STATUS_PATIENT_PERMANENT:
                    lockStatus = LOCK_CHARGE_SHEET;
                    break;
                case ChargeSheetLockStatus.LOCK_STATUS_CHARGE_STATION:
                    lockStatus = LOCK_PAY_BY_CHARGE_STATION;
                    break;
                case ChargeSheetLockStatus.LOCK_STATUS_PATIENT_DEVICE:
                    lockStatus = LOCK_PAY_BY_PATIENT_FROM_DEVICE;
                    break;
            }

            return lockStatus;
        }
    }

    /**
     * 收费单的发票状态
     * 0：不可开票，10：待开发票，20：开票中，30：已开票, 31: 开票金额异常（已开票但是收费单金额发生变化），40：开票失败，50：已冲红，60：已作废
     */
    public static class ChargeSheetInvoiceStatus {

        public static final int NONE = 0;

        public static final int INVOICE_WAITING = 10;

        public static final int INVOICE_BEING = 20;

        public static final int INVOICE_SUCCESS = 30;

        public static final int INVOICE_FEE_INCONSISTENT = 31;

        public static final int INVOICE_FAIL = 40;

        /**
         * 冲红中
         */
        public static final int INVOICE_INVALING = 41;

        public static final int INVOICE_INVALID = 50;

        public static final int INVOICE_REFUND = 60;

        public static List<Integer> getInvoicedStatues() {
            return Arrays.asList(INVOICE_SUCCESS, INVOICE_FEE_INCONSISTENT);
        }

        public static List<Integer> enableOperateStatusListByTargetStatus(int targetStatus) {
            switch (targetStatus) {
                case INVOICE_BEING:
                    return Arrays.asList(INVOICE_WAITING, INVOICE_BEING, INVOICE_FAIL, INVOICE_INVALID, INVOICE_REFUND);
                case INVOICE_SUCCESS:
                    return Arrays.asList(INVOICE_WAITING, INVOICE_BEING, INVOICE_SUCCESS, INVOICE_FAIL, INVOICE_INVALING, INVOICE_INVALID, INVOICE_REFUND);
                case INVOICE_FAIL:
                    return Arrays.asList(INVOICE_WAITING, INVOICE_BEING, INVOICE_FAIL, INVOICE_INVALING);
                case INVOICE_INVALING:
                    return Arrays.asList( INVOICE_SUCCESS, INVOICE_INVALING);
                case INVOICE_INVALID:
                    return Arrays.asList( INVOICE_SUCCESS, INVOICE_INVALING, INVOICE_INVALID);
                case INVOICE_REFUND:
                    return Arrays.asList( INVOICE_SUCCESS, INVOICE_INVALING, INVOICE_REFUND);
                default:
                    return Collections.singletonList(targetStatus);

            }
        }

    }

    /**
     * 开票状态标志位（位模式） 1:存在待开票的数据 2:存在已开票的数据 4:开票金额异常 8:开票失败
     */
    public static class ChargeSheetInvoiceStatusFlag {
        public static final int HAS_INVOICABLE_ITEM = 1;
        public static final int HAS_INVOICED_ITEM = 1 << 1;
        public static final int INVOICE_FEE_CHANGER = 1 << 2;
        public static final int INVOICE_FAIL = 1 << 3;

        public static List<Integer> toInvoiceStatus(Integer invoiceStatusFlag) {
            if (invoiceStatusFlag == null) {
                return new ArrayList<>();
            }

            List<Integer> invoiceStatues = new ArrayList<>();
            if ((invoiceStatusFlag & HAS_INVOICABLE_ITEM) > 0) {
                invoiceStatues.add(ChargeSheetInvoiceStatus.INVOICE_WAITING);
                invoiceStatues.add(ChargeSheetInvoiceStatus.INVOICE_BEING);
                invoiceStatues.add(ChargeSheetInvoiceStatus.INVOICE_FAIL);
                invoiceStatues.add(ChargeSheetInvoiceStatus.INVOICE_INVALID);
            }

            if ((invoiceStatusFlag & HAS_INVOICED_ITEM) > 0) {
                invoiceStatues.add(ChargeSheetInvoiceStatus.INVOICE_SUCCESS);
            }

            if ((invoiceStatusFlag & INVOICE_FAIL) > 0) {
                invoiceStatues.add(ChargeSheetInvoiceStatus.INVOICE_FAIL);
            }

            return invoiceStatues;
        }

    }

    public static class RegionId {
        public static final String HANGZHOU = "330100";
        public static final String NANJING = "320100";
    }

    public static class MedicalFeeGradeName {
        public static final String JIA = "甲";
        public static final String YI = "乙";
        public static final String BING = "丙";
    }


    public static class ChargePatientPointsPromotionInfoStatus {
        //状态：0：未使用，1：已使用，2：已退回
        public static final int UNUSED = 0;
        public static final int ISUSED = 1;
        public static final int ISREFUNDED = 2;
    }

    public static class ChargePatientPointsDeductPromotionInfoStatus {
        //状态：0：未使用，1：已使用，2：已退回
        public static final int UNUSED = 0;
        public static final int ISUSED = 1;
        public static final int ISREFUNDED = 2;
    }

    public static final class ChargeSheetIncludeItemType {
        //可执行的诊疗项目
        public static final int NEED_EXECUTE_TREATMENT = 1;
        //输注
        public static final int INFUSION = 2;
        //雾化
        public static final int ATOMIZATION = 4;
        //皮试
        public static final int AST = 8;
        // 可执行护理项
        public static final int NEED_EXECUTE_NURSE = 16;
        // 外用
        public static final int EXTERNAL_USE = 32;

        public static final List<String> INFUSION_NAMES = Arrays.asList(
                "静脉滴注", "静脉注射", "肌内注射", "腔内注射", "皮下注射", "皮内注射", "穴位注射", "直肠滴注", "局部注射", "局部麻醉", "超声透药", "入壶静滴", "输液冲管", "静脉泵入", "鼻饲", "膀胱给药", "椎管内注射"
        );

        public static final List<String> ATOMIZATION_NAMES = Arrays.asList("雾化吸入");

        public static final List<String> EXTERNAL_USE_NAMES = Collections.singletonList("外用");
    }

    public static final class ChargeSheetQueryExceptionType {

        public static final int SHEBAO_PAY = 1;

        public static final int ABC_PAY = 2;

        public static final int WECHAT_PAY = 4;

        public static final int PROMOTION_CARD_PAY = 8;

        public static final int MEMBER_CARD_PAY = 16;

        /**
         * 沈阳诊间支付
         */
        public static final int OUTPATIENT_CENTER_PAY = 32;

        /**
         * 第三方通用支付
         */
        public static final int THIRD_PARTY_COMMON_PAY = 64;

        /**
         * 社保铁路支付
         */
        public static final int SHE_BAO_RAILWAY = 128;

    }

    public static class ChargeSheetQueryExceptionEnum {

//        WECHAT_PAY(Constants.ChargePayMode.WECHAT_PAY, null,"wechat_pay", Constants.ChargeSheetQueryExceptionType.WECHAT_PAY),
//        MEMBER_CARD(Constants.ChargePayMode.MEMBER_CARD, null,"member_card", ChargeSheetQueryExceptionType.MEMBER_CARD_PAY),
//        PROMOTION_CARD(Constants.ChargePayMode.PROMOTION_CARD, null,"promotion_card", ChargeSheetQueryExceptionType.PROMOTION_CARD_PAY),
//        SHEBAO_PAY(Constants.ChargePayMode.HEALTH_CARD, null,"shebao_card", ChargeSheetQueryExceptionType.SHEBAO_PAY),
//        ABC_PAY(Constants.ChargePayMode.ABC_PAY, null,"abc_pay", ChargeSheetQueryExceptionType.ABC_PAY),
//        SHEBAO_AIR_PAY(ChargePayMode.SHEBAO_AIR_PAY, null,"shebao_air_pay", ChargeSheetQueryExceptionType.SHEBAO_PAY),
//        SHEBAO_MULAID_PAY(ChargePayMode.SHEBAO_MULAID_PAY, null,"shebao_mulaid_pay", ChargeSheetQueryExceptionType.SHEBAO_PAY),
//        WECHAT_MOBILE_SHEBAO(ChargePayMode.HEALTH_CARD, ChargePaySubMode.WECHAT_MOBILE_SHEBAO,"wechat_mobile_shebao", ChargeSheetQueryExceptionType.SHEBAO_PAY),
//        /**
//         * 微信医保支付中-医保部分
//         */
//        WECHAT_MOBILE_SHEBAO_INSURANCE(Constants.ChargePayMode.HEALTH_CARD, ChargePaySubMode.WECHAT_MOBILE_SHEBAO_INSURANCE, "wechat_mobile_shebao_insurance", ChargeSheetQueryExceptionType.SHEBAO_PAY),
//        /**
//         * 微信医保支付中-现金部分
//         */
//        WECHAT_MOBILE_SHEBAO_CASH(Constants.ChargePayMode.WECHAT_PAY, ChargePaySubMode.WECHAT_MOBILE_SHEBAO_CASH, "wechat_mobile_shebao_cash", ChargeSheetQueryExceptionType.SHEBAO_PAY),
//
//        OUTPATIENT_CENTER(ChargePayMode.OUTPATIENT_CENTER_PAY, null, "outpatient_center_pay", ChargeSheetQueryExceptionType.OUTPATIENT_CENTER_PAY);
//
//        private int payMode;
//
//        private Integer paySubMode;
//
//        private String payModeKey;
//
//        private int chargeSheetExceptionType;
//
//        ChargeSheetQueryExceptionEnum(int payMode, Integer paySubMode, String payModeKey, int chargeSheetExceptionType) {
//            this.payMode = payMode;
//            this.paySubMode = paySubMode;
//            this.payModeKey = payModeKey;
//            this.chargeSheetExceptionType = chargeSheetExceptionType;
//        }
//
//        public int getPayMode() {
//            return payMode;
//        }
//
//        public Integer getPaySubMode() {
//            return paySubMode;
//        }
//
//        public String getPayModeKey() {
//            return payModeKey;
//        }
//
//        public int getChargeSheetExceptionType() {
//            return chargeSheetExceptionType;
//        }

//        public static String getPayModeKeyByPayMode(int payMode, Integer paySubMode) {
////            return Optional.ofNullable(getEnumByPayMode(payMode, paySubMode))
////                    .map(ChargeSheetQueryExceptionEnum::getPayModeKey).orElse("");
//            return Optional.ofNullable(InnerPayModes.getPayModeInfo(payMode, paySubMode, null))
//                    .map(PayModeInfo::getPayModeKey).orElse("");
//        }

//        public static ChargeSheetQueryExceptionEnum getEnumByPayMode(int payMode, Integer paySubMode) {
//            return Stream.of(ChargeSheetQueryExceptionEnum.values())
//                    .filter(thirdPartyPayModeEnum -> thirdPartyPayModeEnum.getPayMode() == payMode)
//                    .filter(thirdPartyPayModeEnum -> Objects.equals(thirdPartyPayModeEnum.getPaySubMode(), paySubMode) || Objects.isNull(thirdPartyPayModeEnum.getPaySubMode()))
//                    .min(Comparator.comparingInt((ChargeSheetQueryExceptionEnum thirdPartyPayModeEnum) -> Objects.equals(thirdPartyPayModeEnum.getPaySubMode(), paySubMode) ? 0 : 1))
//                    .orElse(null);
//        }

    }

    public static class UsageScopeId {
        public static final Long yinPian = 3L; //饮片
        public static final Long zhiGao = 4L;  //制膏
        public static final Long daFen = 5L;   //打粉
        public static final Long zhiWan = 6L;  //制丸

        public static final Long keLi = 7L;    //颗粒
        public static List<Long> needVirtualPharmacyUsageScopeIds() {
            return Arrays.asList(yinPian, keLi);
        }

    }

    public static class MedicineStateScopeId {
        public static final Long keLi = 8L; //	颗粒剂
        public static final Long ziJian = 9L; //	自煎
        public static final Long daiJian = 10L; //	代煎
        public static final Long pingZhuang = 11L; //	瓶装
        public static final Long daiZhuang = 12L; //	袋装
        public static final Long puTongDaFen = 13L; //	普通打粉
        public static final Long jingPinDaFen = 14L; //	精品打粉
        public static final Long shuiWan = 15L; //	水丸
        public static final Long xiaoMiWan = 16L; //	小蜜丸
        public static final Long daMiWan = 17L; //	大蜜丸
        public static final Long nongSuoShuiMiWan = 18L; //	浓缩水蜜丸
        public static final Long shuiMiWan = 19L; //	水蜜丸
        public static final Long jingPinShuiMiWan = 20L; //	精品水蜜丸
        public static final Long jingPinDaMiWan = 21L; //	精品大蜜丸
        public static final Long jingPinShuiWan = 22L; //	精品水丸
        public static final Long nongSuoShuiWan = 23L; //	浓缩水丸

        public static final Map<Long, String> PROCESS_USAGE_MAP = new HashMap<Long, String>() {{
            put(daiJian, "代煎");
            put(pingZhuang, "瓶装");
            put(daiZhuang, "袋装");
            put(puTongDaFen, "普通打粉");
            put(jingPinDaFen, "精品打粉");
            put(shuiWan, "水丸");
            put(xiaoMiWan, "小蜜丸");
            put(daMiWan, "大蜜丸");
            put(nongSuoShuiMiWan, "浓缩水蜜丸");
            put(shuiMiWan, "水蜜丸");
            put(jingPinShuiMiWan, "精品水蜜丸");
            put(jingPinDaMiWan, "精品大蜜丸");
            put(jingPinShuiWan, "精品水丸");
            put(nongSuoShuiWan, "浓缩水丸");
        }};
    }

    public static class InvoicePrintPreviewType {
        //正常开票
        public static final int BLUE = 0;
        //开冲红发票
        public static final int RED = 1;
        //已冲红要再开发票
        public static final int RED_REOPEN_BLUE = 2;
    }

    /**
     * 收费项退费类型
     */
    public static class ChargeFormItemRefundType {
        /**
         * 按数量退
         */
        public static final int UNIT = 0;

        /**
         * 按剂量退
         */
        public static final int DOSE = 1;

    }

    public static class MatchCodeStatus {
        /**
         * 已对码
         */
        public static final int MATCHED = 1;

    }

    public static class MatchCodeStatusName {

        public static final String NOT_MATCHED_STR = "未对码";

    }

    public static final Map<Integer, Integer> ABC_PAY_SUB_TYPE_MAP = new HashMap<Integer, Integer>() {
        {
            put(WeChatPayReq.PayType.ALLIN_PAY_WECHAT_NATIVE, Constants.ChargePaySubMode.ABC_PAY_WECHAT_NATIVE);
            put(WeChatPayReq.PayType.ALLIN_PAY_WECHAT_JS, Constants.ChargePaySubMode.ABC_PAY_WECHAT_JS);
            put(WeChatPayReq.PayType.ALLIN_PAY_WECHAT_MINI, Constants.ChargePaySubMode.ABC_PAY_WECHAT_MINI);
            put(WeChatPayReq.PayType.ALLIN_PAY_ALI_NATIVE, Constants.ChargePaySubMode.ABC_PAY_ALI_NATIVE);
            put(WeChatPayReq.PayType.ALLIN_PAY_QQ_NATIVE, Constants.ChargePaySubMode.ABC_PAY_QQ_NATIVE);
            put(WeChatPayReq.PayType.ALLIN_PAY_UNION_NATIVE, Constants.ChargePaySubMode.ABC_PAY_UNION_NATIVE);
            put(WeChatPayReq.PayType.ALLIN_PAY_SCAN_WECHAT, Constants.ChargePaySubMode.ALLIN_PAY_SCAN_WECHAT);
            put(WeChatPayReq.PayType.ALLIN_PAY_SCAN_ALI, Constants.ChargePaySubMode.ALLIN_PAY_SCAN_ALI);
            put(WeChatPayReq.PayType.ALLIN_PAY_SCAN_UNION, Constants.ChargePaySubMode.ALLIN_PAY_SCAN_UNION);
        }
    };

    public static class ChargeSheetPushScanCodeType {

        public static final int NURSE = 1;

        public static final int OUTPATIENT = 2;

    }

    public static class TransactionRecordHandleMode {

        //基于选择的数量记录，选多少数量就记录多少数量（新逻辑）
        public static final int RECORD_COUNT_BY_CHOOSE = 0;

        //基于金额反算数量（老逻辑）
        public static final int RECORD_COUNT_BY_PRICE_RATE = 1;
    }

    public static class BusinessCallBackUrl {

        private static final String CHARGE_BASE_URL = "http://abc-cis-charge-service/rpc/charges";

        /**
         * 空中药房callbackUrl
         * {@link cn.abcyun.cis.charge.controller.rpc.ChargeBusinessCallbackRpcController#airPharmacyPayCallback(PayOrderCallbackReq)}
         */
        public static final String AIR_PHARMACY_CALLBACK_URL = CHARGE_BASE_URL + "/air-pharmacy/pay-callback";

        /**
         * 开电子发票回调url
         * {@link cn.abcyun.cis.charge.controller.rpc.ChargeBusinessCallbackRpcController#chargeSheetInvoiceCallback(CreateInvoiceCallbackReq)}
         */
        public static final String CREATE_INVOICE_CALLBACK_URL = CHARGE_BASE_URL + "/invoice/callback";
        /**
         * 发票状态标识回调url
         * {@link ChargeBusinessCallbackRpcController#chargeSheetInvoiceStatusFlagCallback(InvoiceStatusCallbackReq)}
         */
        public static final String INVOICE_STATUS_FLAG_CALLBACK_URL = CHARGE_BASE_URL + "/invoice-status/flag/callback";
        public static final String CREATE_RED_INVOICE_CALLBACK_URL = CHARGE_BASE_URL + "/invoice/chonghong/callback";

        /**
         * 收费单第三方支付回调url
         * {@link cn.abcyun.cis.charge.controller.rpc.ChargeBusinessCallbackRpcController#thirdPartPayCallBack(WeChatPayCallbackReq)}
         */
        public static final String THIRD_PART_PAY_CALLBACK_URL = CHARGE_BASE_URL + "/third-part-pay/pay-callback";

        /**
         * 第三方支付退款回调url
         * {@link cn.abcyun.cis.charge.controller.rpc.ChargeBusinessCallbackRpcController#thirdPartRefundCallBack(WeChatRefundCallbackReq)}
         */
        public static final String THIRD_PART_REFUND_CALLBACK_URL = CHARGE_BASE_URL + "/third-part-pay/refund-callback";


        /**
         * 上传收费告知书回调url
         * {@link cn.abcyun.cis.charge.controller.rpc.ChargeBusinessCallbackRpcController#uploadNotificationCallback(UploadAttachmentsByQrCodeCallbackReq)}
         */
        public static final String UPLOAD_NOTIFICATION_CALLBACK_URL = CHARGE_BASE_URL + "/upload-notification/callback";

        /**
         * 住院单第三方回调url
         * {@link cn.abcyun.cis.charge.hospital.controller.rpc.RpcChargeHospitalSheetCallbackController#thirdPartPayCallBack(WeChatPayCallbackReq)}
         */
        public static final String HOSPITAL_THIRD_PART_PAY_CALLBACK_URL = CHARGE_BASE_URL + "/hospital/third-part-pay/pay-callback";

        /**
         * 住院单第三方回调url
         * {@link cn.abcyun.cis.charge.hospital.controller.rpc.RpcChargeHospitalSheetCallbackController#thirdPartRefundCallBack(WeChatRefundCallbackReq)}
         */
        public static final String HOSPITAL_THIRD_PART_REFUND_CALLBACK_URL = CHARGE_BASE_URL + "/hospital/third-part-pay/refund-callback";

        /**
         * 组合订单支付第三方回调url
         */
        public static final String COMBINE_ORDER_THIRD_PART_PAY_CALLBACK_URL = CHARGE_BASE_URL + "/combine-order/third-part-pay/pay-callback";

        /**
         * 组合订单退款第三方回调url
         */
        public static final String COMBINE_ORDER_THIRD_PART_REFUND_CALLBACK_URL = CHARGE_BASE_URL + "/combine-order/third-part-pay/refund-callback";
        /**
         * 微信医保支付业务回调地址，wechatPay回调到shebao服务，不直接回调到charge
         */
        public static final String WECHAT_PAY_MOBILE_SHEBAO_BUSINESS_CALLBACK_URL = "http://abc-cis-shebao-service/rpc/shebao/mobile-pay/wechat/notify/paid/%s";

        /**
         * ChargeCenter回调url
         * {@link cn.abcyun.bis.rpc.sdk.mp.model.chargecenter.ChargeCenterCallbackReq}
         */
        public static final String CHARGE_CENTER_PAY_CALLBACK_URL = CHARGE_BASE_URL + "/charge-center/pay-callback";
        public static final String CHARGE_CENTER_REFUND_CALLBACK_URL = CHARGE_BASE_URL + "/charge-center/refund-callback";
    }

    public static class RuleTag {
        public static int notNeedDisCount = 1;


        public static boolean ifContainsTag(int tagsValue, int tag) {
            if ((tagsValue & tag) == tag) {
                return true;
            }
            return false;
        }
    }

    /**
     * 医保超限价规则
     */
    public static class ExceedLimitPriceRule {
        /**
         * 不收费
         */
        public static final int NO_PAY = 0;

        /**
         * 自费
         */
        public static final int SELF_PAY = 1;

    }

    public enum CalculateMethod {
        V1, //老的算费规则
        V2; //新的算费规则，目前先在药店管家使用

        public static CalculateMethod convertMethod (int hisType) {
            if (hisType == Organ.HisType.CIS_HIS_TYPE_PHARMACY) {
                return V2;
            }
            return V1;
        }
    }

    public static class UpdateRemarkSceneType {
        public static int ITEM = 0;
        public static int FORM = 1;
        public static int SHEET = 2;
    }

    public static class UnablePrintFlag {
        /**
         * 单项社保支付，非社保退费
         */
        public static final int SHEBAO_PAY_NOT_SHEBAO_REFUND = 1;
    }

    public static class ChargeSheetDispensingQueryCheckType {

        /**
         * 不查询发药单
         */
        public static final int DISPENSING_NOT_QUERY = 0;

        /**
         * 只查询发药单
         */
        public static final int DISPENSING_QUERY_NOT_CHECK = 1;

        /**
         * 查询发药单并校验发药单数据
         */
        public static final int DISPENSING_QUERY_AND_CHECK = 2;

    }
}
