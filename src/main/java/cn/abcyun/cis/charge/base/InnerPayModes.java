package cn.abcyun.cis.charge.base;

import cn.abcyun.cis.charge.service.ChargePayModeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @create 2024-11-28 16:23
 * @Description 内置支付方式
 */

@Component
public class InnerPayModes {

    private static List<PayModeInfo> payModes;

    private static Map<String, PayModeInfo> payModeMap = new HashMap<>();

    private static Map<String, PayModeInfo> payModeNameMap = new HashMap<>();

    public static ChargePayModeService chargePayModeService;

    @Autowired
    public InnerPayModes (ChargePayModeService chargePayModeService) {
        InnerPayModes.chargePayModeService = chargePayModeService;
    }

    /**
     * 从配置PayModeConfiguration注入
     * @param payModes
     */
    public static void setPayModes(List<PayModeInfo> payModes) {
        InnerPayModes.payModes = payModes;
        for (PayModeInfo payMode : payModes) {
            payModeMap.put(getPayModeMapKey(payMode.getPayMode(), payMode.getPaySubMode()), payMode);
            payModeNameMap.put(payMode.getName(), payMode);
        }
    }

    private static String getPayModeMapKey(Integer payMode, Integer paySubMode) {
        return (payMode == null ? 0 : payMode) + "-" + (paySubMode == null ? 0 : paySubMode);
    }

    public static PayModeInfo getPayModeByName(String name) {
        return payModeNameMap.get(name);
    }


    public static String getPayModeKeyByPayMode(Integer payMode, Integer paySubMode) {
        PayModeInfo payModeInfo = getPayModeInfo(payMode, paySubMode);
        return payModeInfo == null ? null : payModeInfo.getPayModeKey();
    }

    public static PayModeInfo getPayModeInfo(Integer payMode, Integer paySubMode) {
        // 根据payMode-paySubMode获取支付方式
        PayModeInfo payModeInfo = payModeMap.get(getPayModeMapKey(payMode, paySubMode));
        if (payModeInfo != null) {
            return payModeInfo;
        }

        // 如果根据payMode获取支付方式
        if (paySubMode != null) {
            payModeInfo = payModeMap.get(getPayModeMapKey(payMode, null));
            if (payModeInfo != null) {
                return payModeInfo;
            }
        }

        // 小于SYSTEM_PAY_MODE_MAX的是内置支付方式
        if (payMode < Constants.ChargePayMode.SYSTEM_PAY_MODE_MAX) {
            return null;
        }

        // 不是内置，从数据库中加载——主要是看是不是第三方通用支付
        payModeInfo = chargePayModeService != null ? chargePayModeService.getPayModeInfo(payMode, paySubMode) : null;
        return payModeInfo;
    }


    public static class InnerPayModeName {
        public static final String WECHAT_PAY = "WECHAT_PAY";
        public static final String MEMBER_CARD = "MEMBER_CARD";
        public static final String PROMOTION_CARD = "PROMOTION_CARD";
        public static final String SHEBAO_PAY = "SHEBAO_PAY";
        public static final String ABC_PAY = "ABC_PAY";
        public static final String SHEBAO_AIR_PAY = "SHEBAO_AIR_PAY";
        public static final String SHEBAO_RAILWAY_PAY = "SHEBAO_RAILWAY_PAY";
        public static final String SHEBAO_MULAID_PAY = "SHEBAO_MULAID_PAY";
        public static final String WECHAT_MOBILE_SHEBAO = "WECHAT_MOBILE_SHEBAO";
        public static final String WECHAT_MOBILE_SHEBAO_INSURANCE = "WECHAT_MOBILE_SHEBAO_INSURANCE";
        public static final String WECHAT_MOBILE_SHEBAO_CASH = "WECHAT_MOBILE_SHEBAO_CASH";
        public static final String OUTPATIENT_CENTER = "OUTPATIENT_CENTER";
        public static final String SHEBAO_YIMA_PAY = "SHEBAO_YIMA_PAY";
        public static final String SHEBAO_QINGDAO_UNION_POS_PAY = "SHEBAO_QINGDAO_UNION_POS_PAY";
    }

    public static class InnerPayModeKey {
        public static final String THIRD_PARTY_COMMON_PAY = "third_party_common_pay";
    }
}
