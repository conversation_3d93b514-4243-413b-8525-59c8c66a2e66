package cn.abcyun.cis.charge.base;

import cn.abcyun.cis.commons.CisServiceError;
import cn.abcyun.common.model.AbcServiceError;

public class ChargeServiceError {
    //从门诊迁移过来，code不做改变
    public static final CisServiceError EXECUTE_ITEM_NOT_EXISTED = new CisServiceError(16009, "执行项不存在");
    public static final CisServiceError EXECUTE_COUNT_OVERFLOW = new CisServiceError(16010, "执行次数大于总次数");
    public static final CisServiceError EXECUTE_REFUND_OVERFLOW = new CisServiceError(16011, "退费次数大于已执行次数");

    public static final CisServiceError NEED_REQUIRED_PARAMETER = new CisServiceError(17001, "缺少参数");
    public static final CisServiceError CREATE_CHARGE_SHEET_FAILED = new CisServiceError(17002, "创建收费单失败");
    public static final CisServiceError NOT_CLINIC_EXISTED = new CisServiceError(17003, "找不到机构");
    public static final CisServiceError CREATE_PATIENT_ORDER_FAILED = new CisServiceError(17004, "创建就诊单失败");
    public static final CisServiceError CHARGE_FEE_CHANGED = new CisServiceError(17005, "费用发生变化，请刷新后再试");
    public static final CisServiceError CHARGE_SHEET_NOT_EXISTED = new CisServiceError(17006, "收费单不存在");
    public static final CisServiceError CHARGE_FORM_NOT_EXISTED = new CisServiceError(17007, "收费表不存在");
    public static final CisServiceError CHARGE_FORM_ITEM_NOT_EXISTED = new CisServiceError(17008, "收费项不存在");
    public static final CisServiceError REFUND_UNIT_COUNT_OUT = new CisServiceError(17009, "退款单位数量大于可退数");

    public static final CisServiceError CHARGE_SHEET_STATUS_ERROR = new CisServiceError(17010, "收费单状态错误");
    public static final CisServiceError CHARGE_NO_VALID_RECEIVABLE = new CisServiceError(17011, "无有效待收金额");
    public static final CisServiceError CHARGE_SHEET_VERIFY_FAILED = new CisServiceError(17012, "收费单校验失败");
    public static final CisServiceError CHARGE_SHEET_OUT_STOCK = new CisServiceError(17013, "【%s】库存不足");
    public static final CisServiceError REFUND_DOSE_COUNT_NOT_MATCH = new CisServiceError(17014, "退款剂数不匹配");
    public static final CisServiceError CHARGE_NO_VALID_NEED_REFUND_FEE = new CisServiceError(17015, "无有效待退金额");
    public static final CisServiceError CHARGE_SHEET_CHARGED = new CisServiceError(17016, "收费单已完成支付");
    public static final CisServiceError CHANGE_PAYMODE_AMOUNT_NOT_MATCH = new CisServiceError(17017, "修改金额不一致");
    public static final CisServiceError CHARGE_REGISTRATION_ALREADY_REFUND = new CisServiceError(17018, "挂号已退费");


    public static final CisServiceError CHARGE_NEED_MEMBER_ID = new CisServiceError(17019, "缺少会员卡号");
    public static final CisServiceError CHARGE_MEMBER_PAY_FAILED = new CisServiceError(17020, "会员卡支付失败");
    public static final CisServiceError CHARGE_MEMBER_REFUND_FAILED = new CisServiceError(17021, "会员卡退费失败");
    public static final CisServiceError CHARGE_REFUND_OVERFLOW = new CisServiceError(17022, "退款金额超过已支付金额");
    public static final CisServiceError NO_CHARGE_ITEMS = new CisServiceError(17023, "无收费项");
    public static final CisServiceError OUTPATIENT_SHEET_NOT_EXISTED = new CisServiceError(17024, "门诊单不存在");
    public static final CisServiceError CHARGE_REFUND_MEMBER_PAID_FEE_NOT_MATCH = new CisServiceError(17025, "会员卡退费金额错误");
    public static final CisServiceError CHARGE_FEE_MORE_THAN_RECEIVABLE = new CisServiceError(17026, "收费金额大于应收金额");
    public static final CisServiceError REFUND_RECEIVED_HEALTH_CARD_NOT_MATCH = new CisServiceError(17027, "%s退费金额与收费金额不一致");
    public static final CisServiceError REFUND_SHEET_NOT_EXISTED = new CisServiceError(17028, "退款单不存在");
    public static final CisServiceError REFUND_RECEIVED_TRANSACTION_NOT_EXISTED = new CisServiceError(17029, "无有效支付流水");
    public static final CisServiceError CHARGE_SHEET_CHANGED = new CisServiceError(17030, "收费单发生改变，请刷新页面");

    public static final CisServiceError PRODUCT_PRICE_CHANGED = new CisServiceError(17050, "价格发生变化");
    public static final CisServiceError PRODUCT_DISMOUNTING_CHANGED = new CisServiceError(17051, "拆零状态发生变化");
    public static final CisServiceError PRINT_CHARGE_SHEET_STATUS_ERROR = new CisServiceError(17052, "当前收费单状态不支持打印");

    public static final CisServiceError CANCEL_CHARGE_SHEET_NOT_SUPPORT = new CisServiceError(17053, "当前收费单不支持取消");
    public static final CisServiceError CANCEL_PART_REFUND_CHARGE_SHEET_AMOUNT_NOT_ZERO = new CisServiceError(17054, "当前收费单已收费金额不为0，不能取消");
    public static final CisServiceError CHARGE_SHEET_ISPAID_FOR_PATIENT = new CisServiceError(17055, "患者正在支付收费单");


    public static final CisServiceError CHARGE_SHEET_DRAFT_NOT_ALLOWED = new CisServiceError(17056, "当前收费单不能挂单");
    public static final CisServiceError CHARGE_SHEET_DRAFT_DELETE_NOT_ALLOWED = new CisServiceError(17057, "当前收费单不能删除");

    public static final CisServiceError LIMIT_PRICE_PRODUCT_REPEAT = new CisServiceError(17058, "请勿重复添加药品或项目");

    public static final CisServiceError CHARGE_THIRDPART_PAY_TIMEOUT = new CisServiceError(17059, "支付订单已超时");

    public static final CisServiceError PAY_MODE_NOT_EXIST = new CisServiceError(17060, "收费方式不存在");

    public static final CisServiceError PAY_MODE_NAME_REPEAT = new CisServiceError(17061, "收费方式不可重复添加");

    public static final CisServiceError PAY_MODE_LEAST_ONE = new CisServiceError(17062, "收费方式至少保留一个");

    public static final CisServiceError SEND_ORDER_INFO_ONLY_ONE_ALLOWED = new CisServiceError(17063, "自动发送支付订单与手动发送支付订单不能同时开启");

    public static final CisServiceError DUPLICATED_DELIVERY_COMPANY_NAME = new CisServiceError(17064, "快递公司名称重复");

    public static final CisServiceError THIRDPARTPAY_CHARGE_SHEET_NOT_SUPPORT = new CisServiceError(17066, "当前收费单不允许支付");

    public static final CisServiceError CHARGE_SHEET_SAVE_NOT_ALLOWED = new CisServiceError(17067, "当前收费单不能保存");

    public static final CisServiceError PUSHORDER_SHEET_NOT_ALLOWED_ANONYMOUS_PATIENT = new CisServiceError(17068, "不能给匿名患者推送收费单");

    public static final CisServiceError PUSHORDER_SHEET_STATUS_ERROR = new CisServiceError(17069, "已进行了部分收费，无法给患者推送支付订单");

    public static final CisServiceError ONLINE_CONSULTATION_CAN_NOT_RENEW = new CisServiceError(17070, "咨询单不能重新收费");

    public static final CisServiceError REPLENISH_SHEET_CHECK_STATUS_ERROR = new CisServiceError(17071, "收费单状态发生变化，请刷新页面");

    public static final CisServiceError PUSHORDER_SHEET_CHECK_STATUS_ERROR = new CisServiceError(17072, "患者还未确认收费单，不能推送");

    public static final CisServiceError CHARGE_SHEET_DETAIL_NOT_EXISTED = new CisServiceError(17073, "收费单不存在");

    public static final CisServiceError REPLENISH_CLINIC_NOT_OPEN_DECOCTION_SWITCH = new CisServiceError(17074, "门诊未开启代煎服务");

    public static final CisServiceError COUPON_NUM_EXCEED = new CisServiceError(17075, "优惠券使用张数超出限制");

    public static final CisServiceError GIFT_HAS_DISPENSED = new CisServiceError(17076, "赠品已发出。需要将赠品退药后才可重新收费");

    public static final CisServiceError CHAIN_NOT_OPEN_WECHATPAY = new CisServiceError(17077, "机构未支持微信支付，请联系机构");

    public static final CisServiceError PATIENT_NOT_EXISTED = new CisServiceError(17081, "患者不存在");

    public static final CisServiceError NOT_FOUND_VALID_CHARGE_FORMS = new CisServiceError(17082, "找不到有效的处方");

    public static final CisServiceError EXPRESS_DELIVERY_ADDRESS_REPEAT = new CisServiceError(17080, "配送范围地址重复");

    public static final CisServiceError CHARGE_RULE_PROCESS_USAGE_REPEAT = new CisServiceError(17081, "该加工服务已配置规则");

    public static final CisServiceError COMPANY_IS_USED = new CisServiceError(17082, "该配送公司已被快递自动算费规则使用，不可删除");

    public static final CisServiceError AIR_PHARMACY_ORDER_STATUS_CHANGED = new CisServiceError(17083, "订单状态发生变化");

    public static final CisServiceError ORDER_PRICE_ERROR = new CisServiceError(17084, "支付金额错误");

    public static final CisServiceError AIR_PHARMACY_FORM_LACK_MEDICAL = new CisServiceError(17085, "空中药房未选择药品");

    public static final CisServiceError ADDRESS_DISTRICT_CODE_NOT_EXIST = new CisServiceError(17086, "区编码不存在");

    public static final CisServiceError ADDRESS_CITY_CODE_NOT_EXIST = new CisServiceError(17087, "市编码不存在");

    public static final CisServiceError ADDRESS_PROVINCE_CODE_NOT_EXIST = new CisServiceError(17088, "省编码不存在");

    // AIR_PHARMACY_DELIVERY_PRICE_EXCHANGED 和 AIR_PHARMACY_PROCESS_PRICE_EXCHANGED code不能修改，前端有特殊处理
    public static final CisServiceError AIR_PHARMACY_DELIVERY_PRICE_EXCHANGED = new CisServiceError(17090, "空中药房快递规则发生变化，请重新确认商品信息及快递信息");
    public static final CisServiceError AIR_PHARMACY_PROCESS_PRICE_EXCHANGED = new CisServiceError(17091, "空中药房加工规则发生变化，请重新确认商品信息及加工信息");

    public static final CisServiceError EXECUTE_EFFECT_LENGTH_MORE_THAN_HUNDRED = new CisServiceError(17092, "执行效果长度大于100");
    public static final CisServiceError CHARGE_FORM_ITEM_REPEAT = new CisServiceError(17093, "收费子项重复");
    public static final AbcServiceError EXECUTE_RECORD_HAD_CANCELED = new AbcServiceError(17095, "执行记录已撤销，请刷新");
    public static final AbcServiceError NO_EXECUTE_ITEM_IN_CHARGE_SHEET = new AbcServiceError(17096, "收费单中不存在执行项");
    public static final AbcServiceError NO_DELIVERY_COMPANY = new AbcServiceError(17097, "机构没有配置快递规则");
    public static final AbcServiceError CROSS_CLINIC_EXECUTE_UNAVAILABLE = new AbcServiceError(17098, "连锁不支持跨店执行和查询");
    public static final AbcServiceError ONLY_CANCEL_SELF_CLINIC_EXECUTE_RECORD = new AbcServiceError(17099, "只能撤销本门店的执行记录");
    public static final CisServiceError COMMON_ERROR_ORDER_LOCKED = new CisServiceError(17100, "收费单发生改变，请刷新页面");
    public static final CisServiceError CHAIN_NOT_OPEN_THIRD_PART_PAY = new CisServiceError(17101, "机构未支持第三方支付");
    public static final CisServiceError CHARGE_SHEET_IS_LOCK_BY_WECLINIC = new CisServiceError(17102, "收费单已支付中");
    public static final CisServiceError CHARGE_SHEET_IS_LOCK_BY_DEVICE = new CisServiceError(17103, "收费单已在支付中");
    public static final CisServiceError CHARGE_SHEET_IS_LOCK_BY_CHARGE = new CisServiceError(17104, "收费单已在收费台锁单，请前往收费台支付");
    public static final CisServiceError COMMENT_LENGTH_MORE_THAN_TWO_THOUSAND = new CisServiceError(17105, "备注长度大于2000");
    public static final CisServiceError TREATMENT_METHOD_EXIST = new CisServiceError(17106, "治疗方法已存在");
    public static final CisServiceError SYSTEM_TEMPLATE_CAN_NOT_MODIFY = new CisServiceError(17107, "系统模板不允许修改");
    public static final CisServiceError SYSTEM_TEMPLATE_CAN_NOT_DELETE = new CisServiceError(17108, "系统模板不允许删除");
    public static final CisServiceError SORT_TEMPLATES_NEED_ALL_TEMPLATE_ID = new CisServiceError(17109, "模板排序需要全部模板");
    public static final CisServiceError CONTAINS_UNAVAILABLE_EFFECT_ID = new CisServiceError(17110, "包含不存在的执行效果");

    public static final CisServiceError CHARGE_SHEET_STATUS_IS_NOT_CHARGED = new CisServiceError(17111, "收费单未收费完成");
    public static final CisServiceError CHARGE_SHEET_INVOICE_STATUS_IS_NONE = new CisServiceError(17112, "当前收费单不能开发票");
    public static final CisServiceError CHARGE_SHEET_INVOICE_STATUS_IS_BEING = new CisServiceError(17113, "收费单正在开票中，请不要重复开票");
    public static final CisServiceError CHARGE_SHEET_INVOICE_STATUS_IS_SUCCESS = new CisServiceError(17114, "收费单已开票，请不要重复开票");
    public static final CisServiceError CHARGE_SHEET_INVOICE_ITEM_IS_EMPTY = new CisServiceError(17115, "无有效的收费项");
    public static final CisServiceError CHARGE_SHEET_INVOICE_RPC_ERROR = new CisServiceError(17116, "开票失败");
    public static final CisServiceError CHARGE_SHEET_INVOICE_DESTROY_ERROR = new CisServiceError(17117, "发票冲红失败");
    public static final CisServiceError CHARGE_SHEET_INVOICE_STATUS_ERROR = new CisServiceError(17118, "收费单发票状态错误");
    /**
     * 不能改，前端对该code有判断处理
     */
    public static final CisServiceError CHARGE_SHEET_GOODS_ITEM_IS_EMPTY = new CisServiceError(17119, "商品已删除");
    public static final CisServiceError ONLY_CAN_EXECUTE_AFTER_PAID = new CisServiceError(17120, "收费后才可执行");
    public static final CisServiceError CHARGE_FORM_ITEM_CHANGED = new CisServiceError(17121, "收费单子项发生改变，请刷新重试");
    public static final CisServiceError CHARGE_SHEET_TYPE_NOT_SUPPORT_CLOSE = new CisServiceError(17122, "当前收费单不支持关闭");
    public static final CisServiceError CHARGE_SHEET_IS_CLOSED = new CisServiceError(17123, "收费单已关闭");
    /**
     * 不能改，前端对该code有判断处理
     */
    public static final CisServiceError CHARGE_SHEET_GOODS_IS_FORBID_SALE = new CisServiceError(17124, "商品已禁止销售");
    public static final CisServiceError CHARGE_SHEET_ADJUSTMENT_FEE_NOT_FLAT = new CisServiceError(17125, "支付金额有误，请核对议价金额后重试");
    public static final CisServiceError CHARGE_SHEET_IS_LOCKED = new CisServiceError(17126, "收费单正在被支付，请稍后刷新再试");
    public static final CisServiceError AIR_PHARMACY_UNDER_MINIMUM = new CisServiceError(17127, "空中药房药品不足起做量");
    public static final CisServiceError CHARGE_AST_NOT_FINISHED = new CisServiceError(17128, "当前收费单内有皮试项目尚未完成，需要皮试通过后才能执行收费");
    public static final CisServiceError CHARGE_AST_RESULT_CONTAIN_POSITIVE = new CisServiceError(17129, "当前收费单内有皮试项目结果呈阳性，请医生调整医嘱后才能执行收费");
    public static final CisServiceError CHARGE_SHEET_IS_LOCK_BY_WEAPP = new CisServiceError(17130, "收费单已在小程序锁单，请在小程序内支付");

    //200-300 微诊所
    public static final CisServiceError WECLINIC_CHARGE_SHEET_ALREADY_CHARGED = new CisServiceError(17200, "订单已支付，点击确定将刷新订单内容");
    public static final CisServiceError WECLINIC_CHARGE_SHEET_CHANGED = new CisServiceError(17201, "订单已修改，点击确定将刷新订单内容");
    public static final CisServiceError WECLINIC_WECHAT_MSG_CANNOT_PUSHTO = new CisServiceError(17202, "推送消息不能触达患者,请协助患者绑定微信");
    public static final CisServiceError WECLINIC_WECHAT_SHEET_ALREADY_PAID = new CisServiceError(17203, "收费单已经付费");
    public static final CisServiceError WECLINIC_CHARGE_SHEET_PAID_FREQUENT = new CisServiceError(17204, "请勿频繁支付，稍后再试");

    public static final CisServiceError AIR_PHARMACY_DELIVERY_STRUCTURE = new CisServiceError(17205, "空中药房算费结构错误");

    public static final CisServiceError AIR_PHARMACY_ORDER_NOT_FOUND = new CisServiceError(17206, "空中药房订单不存在");


    public static final AbcServiceError DISABLE_ADJUST_WHEN_ONLY_SUPPORT_PAY = new AbcServiceError(17207, "只支持支付，不支持议价");

    public static final CisServiceError CHARGE_SHEET_IS_PAID = new CisServiceError(17208, "订单已支付，点击确认刷新订单状态");
    public static final CisServiceError CHARGE_FORM_IS_EMPTY = new CisServiceError(17209, "收费单没有收费项");
    public static final CisServiceError AIR_PHARMACY_NO_MEDICINE = new CisServiceError(17210, "空中药房没有添加药品");
    public static final CisServiceError AIR_PHARMACY_DELIVERY_IS_EMPTY = new CisServiceError(17211, "请填写空中药房的快递信息");
    public static final CisServiceError PATIENT_POINTS_RULE_CHANGED = new CisServiceError(17212, "积分规则发生变化");
    public static final CisServiceError PATIENT_POINTS_RULE_ERROR = new CisServiceError(17213, "积分规则配置错误");
    public static final CisServiceError FAMILY_DOCTOR_SIGN_CAN_NOT_RENEW = new CisServiceError(17214, "签约收费单不能重新收费");
    public static final CisServiceError CHARGE_SHEET_TYPE_IS_NOT_FAMILY_DOCTOR_SIGN = new CisServiceError(17215, "当前收费单不是家庭医生签约收费单");
    public static final CisServiceError CHARGE_SHEET_FAMILY_DOCTOR_NOT_TERMINATION = new CisServiceError(17216, "家庭医生未解约，不能退费");
    public static final CisServiceError CHARGE_SHEET_IS_REFUNDING = new CisServiceError(17217, "本单正在退费中，不可重复发起退费");
    public static final CisServiceError CHARGE_SHEET_ONE_YEAR_AGO = new CisServiceError(17218, "仅可对近两年内的收费单进行操作");
    public static final CisServiceError CHARGE_NEED_PATIENT_CARD_ID = new CisServiceError(17219, "缺少卡项号");
    public static final CisServiceError CHARGE_PATIENT_CARD_ERROR = new CisServiceError(17220, "支付卡号错误，请核对卡号");
    public static final CisServiceError CHARGE_AMOUNT_OVERFLOW_PATIENT_CARD_BALANCE = new CisServiceError(17221, "本次支付金额超过卡项可支付的金额");
    public static final CisServiceError CHARGE_PATIENT_CARD_DEDUCT_FAIL = new CisServiceError(17222, "卡项抵扣失败");
    public static final CisServiceError CHARGE_PATIENT_CARD_DEDUCT_REFUND_FAIL = new CisServiceError(17223, "卡项退抵扣失败");
    public static final CisServiceError NO_CHARGE_PAY_ITEM_INFOS = new CisServiceError(17224, "无有效的支付单项");
    public static final CisServiceError REFUND_PATIENT_FEE_NOT_VALID = new CisServiceError(17225, "退费的金额超过支付的金额");
    public static final CisServiceError PARTED_PAID_CHARGE_SHEET_REFUND_NOT_FINISH = new CisServiceError(17226, "请先完成退费后再收费");
    public static final CisServiceError REFUND_DEDUCT_TOTAL_COUNT_OUT = new CisServiceError(17227, "退款抵扣数量大于可退数");
    public static final CisServiceError PRINT_CHARGE_SHEET_TYPE_ERROR = new CisServiceError(17228, "当前收费单类型不支持打印");
    public static final CisServiceError REFUND_PRICE_ERROR = new CisServiceError(17229, "退费金额有误");
    public static final CisServiceError DELIVERY_INFO_NOT_NULL = new CisServiceError(17230, "快递地址不能为空");
    public static final CisServiceError ALLIN_PAY_CAN_NOT_BE_CLOSED = new CisServiceError(17231, "ABC支付不能取消勾选");
    public static final CisServiceError PAY_SOURCE_NOT_EXIST = new CisServiceError(17232, "支付来源不存在");
    public static final CisServiceError THIRD_PART_PAY_CLINIC_NOT_EXIST = new CisServiceError(17233, "机构不存在");
    public static final CisServiceError ABC_PAY_CANNOT_BE_ZERO = new CisServiceError(17234, "ABC支付金额不能为零");
    public static final CisServiceError ABC_REFUND_CANNOT_BE_ZERO = new CisServiceError(17235, "ABC退款金额不能为零");
    public static final CisServiceError EXAMINATION_FORM_ITEM_CAN_NOT_DELETE = new CisServiceError(17236, "已执行，无法删除");
    public static final CisServiceError AIR_PHARMACY_LACK_GOODS = new CisServiceError(17237, "该门诊单有部分药品缺药，请联系医生重新开方");
    public static final CisServiceError CHARGE_SHEET_IS_UPLOADED_NOTIFICATION_URL = new CisServiceError(17238, "收费单已上传告知书，不能重复上传");
    public static final CisServiceError CHARGE_THIRD_PART_CALLBACK_TYPE_NOT_FOUND = new CisServiceError(17239, "支付回调类型不支持");
    public static final CisServiceError CHARGE_THIRD_PART_CALLBACK_PAY_MODE_NOT_FOUND = new CisServiceError(17240, "支付回调支付方式不支持");
    //不能修改，前端有处理
    public static final CisServiceError CHARGE_SHEET_IS_PAYING = new CisServiceError(17241, "该收费单已有收费正在进行中，确定是否终止");
    public static final CisServiceError CHARGE_SHEET_ERROR_WRITE_RECORD_IS_REFUNDED = new CisServiceError(17242, "该入账订单已退款");
    public static final CisServiceError CHARGE_SHEET_ERROR_WRITE_RECORD_REFUND_PAY_MODE_NOT_FOUND = new CisServiceError(17243, "异常退款方式不支持");
    public static final CisServiceError PAY_MODE_NOT_SUPPORT = new CisServiceError(17244, "收费方式不支持");
    public static final CisServiceError CHARGE_PATIENT_ORDER_NOTIFICATION_UPLOADED = new CisServiceError(17245, "告知书已上传，请勿重复上传");
    public static final CisServiceError CHARGE_PATIENT_ORDER_NOTIFICATION_NOT_ALLOWED = new CisServiceError(17246, "收费金额未超过500，不支持上传收费告知书");
    public static final CisServiceError CHARGE_HOSPITAL_SHEET_PAY_STATUS_ERROR = new CisServiceError(17247, "结算单状态发生变化，请刷新页面");
    public static final CisServiceError CHARGE_HOSPITAL_SHEET_NOT_APPLY_SETTLE = new CisServiceError(17248, "结算单尚未发起结算，不能进行收费");
    public static final CisServiceError CHARGE_HOSPITAL_SHEET_IS_SETTLED = new CisServiceError(17249, "结算单已结算完成");
    public static final CisServiceError CHARGE_HOSPITAL_PAID_AMOUNT_ERROR = new CisServiceError(17250, "收费金额错误");
    public static final CisServiceError CHARGE_HOSPITAL_PAID_AMOUNT_CHANGED = new CisServiceError(17251, "收费金额发生改变，请刷新页面");
    public static final CisServiceError CHARGE_HOSPITAL_SHEET_SETTLE_STATUS_ERROR = new CisServiceError(17252, "结算单结算状态错误");
    public static final CisServiceError CHARGE_HOSPITAL_REFUND_AMOUNT_ERROR = new CisServiceError(17253, "退费金额错误");
    public static final CisServiceError CHARGE_HOSPITAL_REFUND_OVERFLOW = new CisServiceError(17254, "退款金额超过已支付金额");
    //不能修改，前端有特殊处理
    public static final CisServiceError CHARGE_HOSPITAL_CAN_NOT_SETTLE = new CisServiceError(17255, "有部分药品未完成发药或已退药，不能进行结算");
    public static final CisServiceError PUSH_CHARGE_SHEET_STATUS_IS_PARTED_PAID = new CisServiceError(17256, "已进行了部分收费，无法给患者推送支付订单");
    public static final CisServiceError CALCULATE_ITEM_IS_ALL_ZERO_COUNT = new CisServiceError(17257, "数量全部为0的单项不能进行议价");

    public static final CisServiceError CHARGE_OWE_SHEET_STATUS_ERROR = new CisServiceError(17258, "部分单据状态已变更，请刷新重试");
    public static final CisServiceError CHARGE_OWE_SHEET_RECEIVABLE_FEE_CHANGED = new CisServiceError(17259, "待还金额发生变化");
    public static final CisServiceError COMBINE_ORDER_THIRD_PART_CALLBACK_PAY_MODE_NOT_FOUND = new CisServiceError(17260, "支付回调支付方式不支持");
    public static final CisServiceError CHARGE_OWE_SHEET_REFUND_PAY_MODE_NOT_SUPPORT = new CisServiceError(17261, "不支持的退费方式");
    public static final CisServiceError COMBINE_ORDER_STATUS_ERROR = new CisServiceError(17262, "订单状态错误");
    public static final CisServiceError CHARGE_ANONYMOUS_PATIENT_CANNOT_OWE_PAY = new CisServiceError(17263, "匿名患者不能欠费");
    public static final CisServiceError CHARGE_OWE_PAY_MODE_CANNOT_REFUND = new CisServiceError(17264, "请完成还款后再进行退费");
    public static final CisServiceError CHARGE_REFUND_EXISTED_OWE_PAY_AMOUNT = new CisServiceError(17265, "请先完成欠费退费");
    public static final CisServiceError CHARGE_SHEET_TYPE_PWE_PAY_NOT_ALLOWED = new CisServiceError(17265, "不支持欠费，请使用其他方式支付");
    public static final CisServiceError CHARGE_HOSPITAL_RENEW_PAID_ONLY_SUPPORT_OWE_PAY = new CisServiceError(17266, "请使用欠费支付");
    public static final CisServiceError CHARGE_HOSPITAL_SHEET_STATUS_IS_NOT_UNCHARGED = new CisServiceError(17267, "结算单不是待收状态");
    public static final CisServiceError CHARGE_OWE_SHEET_CAN_NOT_PAY_FOR_SHEBAO = new CisServiceError(17268, "仅全部欠费且未进行过退欠费可使用医保结算");
    public static final CisServiceError CHARGE_PATIENT_ORDER_IS_LOCKED = new CisServiceError(17269, "医生正在调整处方医嘱，请稍后再进行收费");

    public static final CisServiceError CHARGE_HOSPITAL_SHEET_CANNOT_CREATE_INVOICE = new CisServiceError(17270, "住院单状态结算状态错误，不能开票");
    public static final CisServiceError CHARGE_HOSPITAL_SHEET_INVOICE_STATUS_IS_BEING = new CisServiceError(17271, "住院单正在开票中，请不要重复开票");
    public static final CisServiceError CHARGE_HOSPITAL_SHEET_INVOICE_STATUS_IS_SUCCESS = new CisServiceError(17272, "住院单已开票，请不要重复开票");
    public static final CisServiceError CHARGE_HOSPITAL_SHEET_INVOICE_STATUS_ERROR = new CisServiceError(17272, "住院单发票状态错误");
    public static final CisServiceError CHARGE_HOSPITAL_SHEET_STATUS_ERROR = new CisServiceError(17273, "住院单收费状态错误");
    public static final CisServiceError CHARGE_SHEET_INVOICE_ANONYMOUS_PATIENT_ID_CANNOT_CREATE = new CisServiceError(17274, "匿名患者不允许开票");
    public static final CisServiceError CHARGE_SHEET_BOSI_INVOICE_CAN_NOT_CREATE = new CisServiceError(17275, "无科室信息，请重收补充信息后开票");
    public static final CisServiceError CHARGE_PAY_CALLBACK_HEALTH_CARD_ITEM_IS_NULL = new CisServiceError(17276, "医保入账项目不能为空");
    public static final CisServiceError CHARGE_PAY_CALLBACK_ITEM_RECEIVED_FEE_NOT_VALID = new CisServiceError(17277, "医保入账金额与子项金额总和不一致");
    public static final CisServiceError CHARGE_FORM_ITEM_PAY_COUNT_ERROR = new CisServiceError(17278, "【%s】收费次数不能低于已执行次数%s次");
    public static final CisServiceError CHARGE_FORM_ITEM_REFUND_COUNT_ERROR = new CisServiceError(17279, "【%s】退费次数不能高于未执行次数%s次");
    public static final CisServiceError CHARGE_FORM_ITEM_HAS_BEEN_EXECUTED_CAN_NOT_DELETE = new CisServiceError(17280, "【%s】已执行过%s次，不可删除，请先撤销执行");
    public static final CisServiceError CHARGE_SHEET_PAID_MEMBER_ID_CHANGED = new CisServiceError(17281, "会员身份发生改变");
    public static final CisServiceError CHARGE_SHEET_CONTAIN_EXECUTED_ITEM = new CisServiceError(17282, "有执行项被执行过，不能修改单据");
    public static final CisServiceError REFUND_DEDUCT_COMPOSE_MUST_ALL_REFUND = new CisServiceError(17283, "抵扣过的套餐必须全部退费");
    public static final CisServiceError IMPORT_CHARGE_SHEET_CANNOT_REFUND = new CisServiceError(17284, "导入的收费单不允许退费");
    public static final CisServiceError AIR_PHARMACY_ORDER_VENDOR_NOT_SAME = new CisServiceError(17285, "不同供应商的订单不能批量付款");

    public static final CisServiceError CHANGE_PAY_MODE_CAN_NOT_SUPPORT = new CisServiceError(17286, "该支付方式不支持直接修改，请退费重收");
    public static final CisServiceError CHANGE_PAY_MODE_TRANSACTION_UPDATE = new CisServiceError(17287, "收费流水信息更新，请刷新后重试");
    public static final CisServiceError CHARGE_NURSE_QUERY_TIME_OVERFLOW = new CisServiceError(17288, "最多可查询3个月内的执行数据");
    public static final CisServiceError CHARGE_FORM_ITEM_BATCH_INFO_CHANGED = new CisServiceError(17289, "批次信息发生变化");
    public static final CisServiceError WECHAT_MOBILE_SHEBAO_REFUND_NOT_SUPPORT = new CisServiceError(17290, "医保移动支付不支持组合退费");
    public static final CisServiceError WECHAT_MOBILE_CASH_REFUND_NEED_AFTER_INSURANCE = new CisServiceError(17291, "请先退医保移动支付医保部分的金额再退现金部分");
    public static final CisServiceError CHARGE_REFUND_TASK_CREATE_ERROR = new CisServiceError(17292, "退费任务创建失败");

    public static final AbcServiceError ONLY_CAN_SHEBAO_PAID_ONCE = new AbcServiceError(17293, "同一个收费单，只能医保支付一次");
    public static final AbcServiceError USED_DISCOUNT_NOT_SHEBAO = new AbcServiceError(17294, "本单如需使用医保结算，请先取消已选择的会员/卡项优惠");
    public static final AbcServiceError HOSPITAL_OWE_SHEET_CANNOT_PAID_ALONE = new AbcServiceError(17295, "长护单据请在客户端单独操作还款");


    public static final AbcServiceError ONLY_PAY_FOR_UNCHARGED_CHARGE_SHEET = new AbcServiceError(17296, "只能支付待收费的收费单");
    public static final AbcServiceError CHARGE_SHEET_NEED_PAY_FEE_ZERO = new AbcServiceError(17297, "收费单应收金额为0，无需支付");
    public static final AbcServiceError CHARGE_SHEET_FEE_CHANGED = new AbcServiceError(17298, "收费单金额已变更，请重新支付");
    public static final AbcServiceError MOBILE_PAY_ORDER_NOT_EXIST = new AbcServiceError(17299, "移动支付订单不存在");
    public static final AbcServiceError MOBILE_PAY_ORDER_TOTAL_FEE_NOT_MATCH = new AbcServiceError(17300, "移动支付订单总金额发生变更");
    public static final AbcServiceError ORGAN_NOT_EXIST = new AbcServiceError(17301, "机构不存在");
    public static final AbcServiceError MOBILE_PAY_ORDER_NOT_COMPLETED = new AbcServiceError(17302, "移动支付订单未完成下单");
    public static final AbcServiceError PAY_MODE_HEALTH_CARD_WECHAT_AUTH_CODE_EMPTY = new AbcServiceError(17303, "微信社保支付时，微信授权码不能为空");
    public static final CisServiceError CHARGE_REFUND_ERROR = new CisServiceError(17304, "退费失败");
    public static final CisServiceError QUERY_CHARGE_SHEET_VIEW_LIST_TOO_MANY = new CisServiceError(17305, "查询频繁，请稍后再试");

    public static final CisServiceError CHARGE_SHEET_REGISTER_INO_NOT_FOUND = new CisServiceError(17306, "登记信息不存在");
    public static final CisServiceError CHARGE_FORM_ITEM_BATCH_INFO_NOT_FOUND = new CisServiceError(17307, "%s批次不存在，请刷新后重试");
    public static final CisServiceError CHARGE_FORM_ITEM_BATCH_INFO_REFUND_COUNT_NOT_ENOUGH = new CisServiceError(17308, "退费数量不能大于可退数量%d%s");

    public static final CisServiceError CHARGE_SALES_ORDER_OUT_OF_TIME_RANG = new CisServiceError(17309, "销售单查询时间超出范围");
    public static final CisServiceError GOODS_BATCH_NOT_EXIST = new CisServiceError(17310, "商品【%s】未找到指定批次【%s】");;
    public static final CisServiceError DEDUCT_BATCH_INFO_NOT_ENOUGH = new CisServiceError(17311, "【%s】批次数量不足抵扣，请确认抵扣数量");

    public static final CisServiceError CHARGE_SINGLE_PROMOTION_GIFT_GOODS_ERROR = new CisServiceError(17312, "单品优惠赠品匹配失败");
    public static final CisServiceError CHARGE_SHEET_LOCK_ERROR = new CisServiceError(17313, "锁库失败");

    public static final CisServiceError COOPERATION_ORDER_EXTRACT_SHEET_FORMS_IS_EMPTY = new CisServiceError(17314, "无有效的收费项");

    public static final CisServiceError COOPERATION_ORDER_IS_DELETED = new CisServiceError(17315, "处方订单已删除");

    public static final CisServiceError COOPERATION_ORDER_NOT_EXIST = new CisServiceError(17316, "处方不存在");

    public static final CisServiceError COOPERATION_ORDER_CHANGED = new CisServiceError(17317, "处方订单发生变化，请重新提单");

    public static final CisServiceError COOPERATION_ORDER_STATUS_ERROR = new CisServiceError(17318, "处方订单发生变化，请刷新页面");

    public static final CisServiceError CHARGE_SHEET_EXISTED_TRANSACTIONS = new CisServiceError(17319, "收费单已存在收费记录，不能删除");

    public static final CisServiceError UNABLE_PRINT_SHEBAO_PAY_NOT_SHEBAO_REFUND = new CisServiceError(17320, "医保现金退费不能开发票");

    public static final CisServiceError GOODS_CHILDREN_REPEAT = new CisServiceError(17321, "项目【%s】存在重复费用项，请修改项目后信息重试");

    public static final CisServiceError REGISTRATION_FEE_CHILD_ERROR = new CisServiceError(17322, "挂号费费用项错误");

    public static final CisServiceError PRESCRIPTION_DOSE_COUNT_ERROR = new CisServiceError(17323, "同一个处方中药品的剂量不同，请检查药品剂量");

    public static final CisServiceError KANG_JUN_DRUGS_LIMIT = new CisServiceError(17324, "存在抗菌药物使用限制");

    public static final CisServiceError ANONYMOUS_PATIENT_NOT_UPDATE = new CisServiceError(17325, "收费单匿名用户不能进行患者信息完善操作");
    public static final CisServiceError CHARGE_SHEET_NOT_ALREADY_PAID = new CisServiceError(17326, "收费单未完成收费或已退费,不能进行患者信息完善操作");
    public static final CisServiceError CHARGE_SHEET_NOT_DIRECT_SALE = new CisServiceError(17327, "收费单不是零售收费,不能进行患者信息完善操作");
    public static final CisServiceError CHARGE_SHEET_DIRECT_IMPROVE_ERROR = new CisServiceError(17328, "收费单患者信息完善操作失败");
    public static final CisServiceError CHARGE_SHEET_PATIENT_NOT_EXIST = new CisServiceError(17329, "收费单患者信息不存在");
    public static final CisServiceError CHARGE_SHEET_PATIENT_NOT_ONLY = new CisServiceError(17330, "收费单患者信息不唯一");
    public static final CisServiceError CHARGE_SHEET_QUERY_TIME_OUT_OF_THREE_DAY = new CisServiceError(17331, "批量查询收费单信息的时间间隔不能超过3天");

    public static final CisServiceError THIRD_PART_COMMON_PAY_CONFIG_ERROR = new CisServiceError(17332, "第三方支付配置错误");
    public static final CisServiceError TRANSACTION_NOT_EXIST = new CisServiceError(17333, "未找到交易记录");
    public static final CisServiceError THIRD_PART_COMMON_PAY_PAY_ERROR = new CisServiceError(17334, "第三方支付失败");

    public static final CisServiceError CHARGE_SHEET_IS_CONTAIN_ABNORMAL_PAY_TRANSACTION = new CisServiceError(17235, "请先处理异常再退费");
    public static final CisServiceError ORGAN_DELIVERY_TO_HOME_CLOSED = new CisServiceError(17336, "门店已关闭快递业务，请切换为到店自取");
    public static final CisServiceError CHARGE_REFUND_CHECK_PERMISSION_DENIED = new CisServiceError(17337, "不是审核人");
    public static final CisServiceError CHARGE_SHEET_NOT_SUPPORT_PART_PAY = new CisServiceError(17338, "当前门店不支持收费单部分支付费用");
    public static final CisServiceError CHARGE_SHEET_NOT_SUPPORT_PART_REFUND = new CisServiceError(17338, "当前门店不支持收费单部分退费");
    public static final AbcServiceError CHARGE_SHEET_NOT_SUPPORT_REFUND_BY_PART_DISPENSING = new AbcServiceError(17338, "收费单内有部分药品已{}，请{}后再整单退费");
    public static final CisServiceError CHARGE_SHEET_NOT_SUPPORT_REFUND_BY_PART_EXECUTION = new CisServiceError(17338, "收费单内有部分项目已经执行，请撤销后再整单退费");

    public static final CisServiceError CHARGE_SHEET_WHOLE_OPERATE_NOT_ALLOW_WITH_NO_STOCK = new CisServiceError(17341, "如需整单收/退费，请先前往开出设置关闭库存不足允许开出");
    public static final CisServiceError SHEBAO_MOBILE_PAY_TIMEOUT = new CisServiceError(17342, "支付已超时，请重新下单");
    // openapi
    public static final CisServiceError CHARGE_SHEET_DELIVERY_NOT_FOUND = new CisServiceError(17340, "快递信息不存在");
    public static final CisServiceError CHARGE_SHEET_STATUS_NOT_SUPPORT_CANCEL_DELIVERY = new CisServiceError(17341, "收费单不是待收费状态，不支持取消快递费");

    public static final CisServiceError DRAFT_BATCH_EXTRACT_PATIENT_ID_DIFFERENT = new CisServiceError(17342, "非同一会员的单据不可合并");
    public static final CisServiceError DRAFT_BATCH_EXTRACT_CHARGE_SHEET_NOT_UNCHARGED = new CisServiceError(17343, "收费单已收费");
    public static final CisServiceError DRAFT_BATCH_EXTRACT_CHARGE_SHEET_NOT_DRAFT = new CisServiceError(17344, "收费单已经不是挂单中");
    public static final CisServiceError DRAFT_BATCH_EXTRACT_CAN_NOT_BE_OUTSOURCE_PRESCRIPTION = new CisServiceError(17345, "电子处方流转的单据不可合并支付");
    public static final CisServiceError DRAFT_BATCH_EXTRACT_CAN_NOT_BE_COOPERATION_ORDER = new CisServiceError(17346, "合作药房处方的单据不可合并支付");
    public static final CisServiceError DRAFT_BATCH_EXTRACT_ONLY_DIRECT_SALE = new CisServiceError(17347, "仅零售收费单可合并支付");
    public static final CisServiceError DRAFT_BATCH_EXTRACT_NOT_SUPPORT = new CisServiceError(17348, "门店不支持批量提单");
    public static final CisServiceError DRAFT_BATCH_EXTRACT_CAN_NOT_BE_CONTAIN_CHINESE_PRESCRIPTION_PIECES = new CisServiceError(17349, "配方饮片销售单不可合并提单");
    public static final CisServiceError NOT_OPEN_ABC_PAYMENT = new CisServiceError(17350, "未开通abc支付");

    public static final CisServiceError GOODS_NOT_SET_PRICE = new CisServiceError(17351, "商品未定价");
    public static final AbcServiceError ONLY_CAN_COPY_DIRECT_SALE_SHEET = new AbcServiceError(17352, "只支持复制零售单");
}
