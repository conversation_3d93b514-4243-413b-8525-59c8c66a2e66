package cn.abcyun.cis.charge.base;


import jodd.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Slf4j
public class ThreadPoolConstants {
    public static final ThreadPoolExecutor BATCH_QUERY_CHARGE_SHEET_VIEW_POOL = new ThreadPoolExecutor(
            4,
            8,
            2,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(200),
            new ThreadFactoryBuilder()
                    .setNameFormat("batchQueryChargeSheetViewPool-%d")
                    .get(),
            (r, executor) -> {
                log.error("批量查询收费单viewList线程不够用了，直接返回400");
                throw new ChargeServiceException(ChargeServiceError.QUERY_CHARGE_SHEET_VIEW_LIST_TOO_MANY);
            }
    );
}
