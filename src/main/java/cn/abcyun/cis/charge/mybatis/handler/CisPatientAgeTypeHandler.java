package cn.abcyun.cis.charge.mybatis.handler;

import cn.abcyun.cis.charge.util.JsonUtils;
import cn.abcyun.cis.commons.model.CisPatientAge;
import cn.abcyun.cis.commons.util.TextUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

@MappedJdbcTypes({JdbcType.VARCHAR})
@MappedTypes({CisPatientAge.class})
public class CisPatientAgeTypeHandler extends BaseTypeHandler<CisPatientAge> {
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, CisPatientAge parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, JsonUtils.dump(parameter));
    }

    @Override
    public CisPatientAge getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String ageJson = rs.getString(columnName);
        if (TextUtils.isEmpty(ageJson)) {
            return null;
        }
        CisPatientAge age = JsonUtils.readValue(ageJson, CisPatientAge.class);
        return age;
    }

    @Override
    public CisPatientAge getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String ageJson = rs.getString(columnIndex);
        if (TextUtils.isEmpty(ageJson)) {
            return null;
        }
        CisPatientAge age = JsonUtils.readValue(ageJson, CisPatientAge.class);
        return age;
    }

    @Override
    public CisPatientAge getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String ageJson = cs.getString(columnIndex);
        if (TextUtils.isEmpty(ageJson)) {
            return null;
        }
        CisPatientAge age = JsonUtils.readValue(ageJson, CisPatientAge.class);
        return age;
    }
}
