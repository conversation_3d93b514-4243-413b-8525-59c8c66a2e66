package cn.abcyun.cis.charge.listener;

import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.service.ChargeService;
import cn.abcyun.cis.charge.service.ChargeSheetService;
import cn.abcyun.cis.charge.service.rpc.CisScClinicService;
import cn.abcyun.cis.charge.util.ExpireRedisUtils;
import cn.abcyun.cis.charge.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.listener.KeyExpirationEventMessageListener;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.Topic;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Optional;
import java.util.regex.Matcher;

/**
 * redis key过期事件监听器
 */
@Component
@Slf4j
public class RedisKeyExpirationEventListener extends KeyExpirationEventMessageListener {

    @Autowired
    public ChargeService mChargeService;

    @Autowired
    public ChargeSheetService chargeSheetService;

    @Autowired
    private CisScClinicService cisScClinicService;

    @Value("${spring.redis.database_listenExpire}")
    private int listenExpireDatabase;

//    private Topic KEYEVENT0_EXPIRED_TOPIC = new PatternTopic("__keyevent@"+listenExpireDatabase+"__:expired");

    /**
     * @param listenerContainer must not be {@literal null}.
     */
    public RedisKeyExpirationEventListener(RedisMessageListenerContainer listenerContainer) {
        super(listenerContainer);
    }

    @Override
    public void doRegister(RedisMessageListenerContainer listenerContainer) {
        Topic KEYEVENT0_EXPIRED_TOPIC = new PatternTopic("__keyevent@"+listenExpireDatabase+"__:expired");
        listenerContainer.addMessageListener(this, KEYEVENT0_EXPIRED_TOPIC);
    }

    @Override
    public void onMessage(Message message, byte[] pattern) {
        //获取过期的key
        String expireKey = new String(message.getBody(), StandardCharsets.UTF_8);


        Matcher stockLockMatcher = ExpireRedisUtils.KeyPattern.GOODS_STOCK_LOCK_PATTERN.matcher(expireKey);
//        if (patientOrderLockMatcher.matches()) {
//            log.info("就诊单解锁过期, redisKey: {}", expireKey);
//            String chainId = patientOrderLockMatcher.group(1);
//            String patientOrderId = patientOrderLockMatcher.group(2);
//
//            chargePatientOrderService.unLockPatientOrderId(chainId, patientOrderId, null);
//        }

        //目前仅用于 自动关单
        if (stockLockMatcher.matches()) {
            log.info("自动关闭收费单  key:{}", JsonUtils.dump(expireKey));
            //关闭 就诊单
            String clinicId = stockLockMatcher.group(1);
            String chargeSheetId = stockLockMatcher.group(2);
            int hisType = Organ.HisType.CIS_HIS_TYPE_NORMAL;
            try {
                hisType = Optional.ofNullable(cisScClinicService.getOrgan(clinicId))
                        .map(Organ::getHisType)
                        .orElse(0);
            } catch (Exception e) {
                log.error("cisScClinicService.getOrgan(clinicId) error.", e);
            }

            mChargeService.closeChargeSheetById(chargeSheetId, clinicId, hisType, Constants.DEFAULT_OPERATORID);
        }


    }

}