package cn.abcyun.cis.charge.config;

import cn.abcyun.cis.core.amqp.AmqpRetryAspect;
import cn.abcyun.cis.core.rabbitmq.CompressMessageConverter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 阿里云rabbitmq配置
 */
@Configuration
@EnableConfigurationProperties(CloudRabbitProperties.class)
public class CloudRabbitConfiguration {

    @Autowired
    private CloudRabbitProperties rabbitProperties;

    @Bean(name = "amqp-cloud-connection-factory")
    public ConnectionFactory getConnectionFactory() {
        // 初始化RabbitMQ连接配置 connectionFactory
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setHost(rabbitProperties.getHost());
        connectionFactory.setPort(rabbitProperties.getPort());
        if (StringUtils.isNotBlank(rabbitProperties.getUsername())) {
            connectionFactory.setUsername(rabbitProperties.getUsername());
        }
        if (StringUtils.isNotBlank(rabbitProperties.getPassword())) {
            connectionFactory.setPassword(rabbitProperties.getPassword());
        }
        connectionFactory.setVirtualHost(rabbitProperties.getVirtualHost());
        // 开启Connection自动重连功能
        connectionFactory.getRabbitConnectionFactory().setAutomaticRecoveryEnabled(true);
        connectionFactory.setConnectionTimeout(300 * 1000);
        connectionFactory.getRabbitConnectionFactory().setHandshakeTimeout(300 * 1000);
        connectionFactory.getRabbitConnectionFactory().setShutdownTimeout(0);

        /*
         * 使用云消息队列 RabbitMQ，推荐使用CacheMode.CONNECTION 模式（建多个connection，程序会缓存一定数量的connection，每个connection中缓存一定数量的channel）。
         * 云消息队列 RabbitMQ 是集群分布式架构，在CONNECTION模式下，创建多个connection 可以更好地和集群的多个MQ服务节点连接，更高效的发送和消费消息。。
         * connection模式下，rabbit admin不生效，dev和tes不能自动创建exchange和queue
         */
        connectionFactory.setCacheMode(CachingConnectionFactory.CacheMode.CONNECTION);
        // CONNECTION模式下，缓存的connection数量
        connectionFactory.setConnectionCacheSize(5);
        // 缓存中保持的channel数量
        connectionFactory.setChannelCacheSize(40);

        //  是 RabbitMQ 中的一种确认模式，用于确认消息是否成功发布到交换机（Exchange）
        connectionFactory.setPublisherConfirmType(CachingConnectionFactory.ConfirmType.CORRELATED);
        // 用于处理消息在发送过程中未能成功路由到任何队列的情况。与 PublisherConfirmType 不同，PublisherReturns 主要关注的是消息的路由失败，而不是消息的发布确认。
        // connectionFactory.setPublisherReturns(rabbitProperties.isPublisherReturns());

        return connectionFactory;
    }

    @Bean
    public CompressMessageConverter compressMessageConverter() {
        return new CompressMessageConverter();
    }

    @Bean(name = "amqp-cloud-rabbit-listener-container-factory")
    public SimpleRabbitListenerContainerFactory rabbitListenerContainerFactory(CompressMessageConverter compressMessageConverter,
                                                                               @Qualifier("amqp-cloud-connection-factory") ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setMessageConverter(compressMessageConverter);
        factory.setConnectionFactory(connectionFactory);
        return factory;
    }

    @Bean(name = "amqp-cloud-rabbit-template")
    public RabbitTemplate rabbitTemplate(CompressMessageConverter compressMessageConverter, AmqpRetryAspect amqpRetryAspect) {
        RabbitTemplate template = new RabbitTemplate(getConnectionFactory());
        template.setMessageConverter(compressMessageConverter);
        template.setConfirmCallback(amqpRetryAspect.confirmCallback());
        return template;
    }
}
