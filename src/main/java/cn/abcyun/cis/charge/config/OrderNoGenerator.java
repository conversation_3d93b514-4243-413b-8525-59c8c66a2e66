package cn.abcyun.cis.charge.config;

import cn.abcyun.cis.charge.processor.provider.OrderNoGeneratorProvider;
import cn.abcyun.cis.charge.repository.ChargeSheetRepository;
import cn.abcyun.cis.commons.util.DateUtils;
import cn.abcyun.cis.core.util.OrderNumberGenerator;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Locale;

/**
 * 订单编号生成
 *
 * <AUTHOR>
 */
@Component
public class OrderNoGenerator implements OrderNoGeneratorProvider {
    private static final String REDIS_KEY = "_chargeSheet:no:%s:%s";

    public static DateTimeFormatter YYYYMMDD_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd", Locale.CHINA);
    private final OrderNumberGenerator orderNumberGenerator;
    private final ChargeSheetRepository chargeSheetRepository;

    public OrderNoGenerator(OrderNumberGenerator orderNumberGenerator, ChargeSheetRepository chargeSheetRepository) {
        this.orderNumberGenerator = orderNumberGenerator;
        this.chargeSheetRepository = chargeSheetRepository;
    }


    /**
     * 生成收费销售单
     * @param clinicId
     * @return
     */
    @Override
    public String generateChargeSheetSellNo(String clinicId) {
        LocalDate localDate = LocalDate.now();
        String datePrefix = localDate.format(YYYYMMDD_FORMATTER);
        orderNumberGenerator.setOrderNumberIndexIfAbsent(Strings.EMPTY,
                "charge_sheet",
                clinicId,
                datePrefix,
                Strings.EMPTY,
                (s, s2) -> chargeSheetRepository.findMaxOrderNoByClinicIdAndCreated(clinicId, Date.from(DateUtils.getBeginOfDay(localDate.toString())), Date.from(DateUtils.getEndOfDay(localDate.toString()))));
        return orderNumberGenerator.generateOrderNumber(Strings.EMPTY, "charge_sheet_sell_no", clinicId, datePrefix, Strings.EMPTY, 6);
    }
}