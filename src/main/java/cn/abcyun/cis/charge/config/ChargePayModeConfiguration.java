package cn.abcyun.cis.charge.config;

import cn.abcyun.cis.charge.base.InnerPayModes;
import cn.abcyun.cis.charge.base.PayModeInfo;
import cn.abcyun.cis.commons.util.TextUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2024-11-28 15:30
 * @Description
 */

@Component
@ConfigurationProperties(prefix = "abc.cis.charge.pay-mode")
public class ChargePayModeConfiguration {

    /**
     * 内置支付方式
     */
    private List<PayModeInfo> innerPayModes;

    public List<PayModeInfo> getInnerPayModes() {
        return innerPayModes;
    }

    public void setInnerPayModes(List<PayModeInfo> innerPayModes) {
        checkInnerPayModes(innerPayModes);
        this.innerPayModes = innerPayModes;
        InnerPayModes.setPayModes(innerPayModes);
    }

    private void checkInnerPayModes(List<PayModeInfo> innerPayModes) {
        if (innerPayModes == null || innerPayModes.isEmpty()) {
            throw new IllegalArgumentException("innerPayModes is empty");
        }

        Set<String> allNames = new HashSet<>();
        for (PayModeInfo payModeInfo : innerPayModes) {
            if (TextUtils.isEmpty(payModeInfo.getName()) || allNames.contains(payModeInfo.getName())) {
                throw new IllegalArgumentException("pay info name is empty or duplicate");
            }
            allNames.add(payModeInfo.getName());
        }
    }
}
