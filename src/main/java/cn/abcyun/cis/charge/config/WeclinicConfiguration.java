package cn.abcyun.cis.charge.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 动态配置
 *
 * <AUTHOR>
 * @version DynamicConfiguration.java, 2020/8/4 上午11:52
 */
@Component
@ConfigurationProperties(prefix = "abc.cis.charge.weclinic")
@Data
public class WeclinicConfiguration {
    private int autoConfirmRecvMedicineSeconds;
}
