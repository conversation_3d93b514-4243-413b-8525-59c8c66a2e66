package cn.abcyun.cis.charge.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 动态配置
 *
 * <AUTHOR>
 * @version DynamicConfiguration.java, 2020/8/4 上午11:52
 */
@Component
@ConfigurationProperties(prefix = "abc.cis.charge")
@Data
public class DynamicConfiguration {
    private List<String> nurseAbstractInfoUsageWhiteList;

    /**
     * 查询goods平均最近进价白名单
     */
    private List<String> queryAverageGoodsPackageCostPriceChainIds;

    /**
     * 加工费折扣只影响煎药的连锁id
     */
    private List<String> processPromotionOnlyEffectJianYaoChainIds;
}
