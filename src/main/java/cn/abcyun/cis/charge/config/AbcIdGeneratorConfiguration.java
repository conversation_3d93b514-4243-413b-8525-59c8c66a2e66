package cn.abcyun.cis.charge.config;

import cn.abcyun.cis.charge.AbcCisChargeServiceApplication;
import cn.abcyun.cis.charge.util.AbcIdUtils;
import cn.abcyun.cis.idgenerator.AbcIdGenerator;
import com.baidu.fsg.uid.worker.DisposableWorkerIdAssigner;
import com.baidu.fsg.uid.worker.WorkerIdAssigner;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@Configuration
@EntityScan(basePackageClasses = {DisposableWorkerIdAssigner.class, AbcCisChargeServiceApplication.class})
@EnableJpaRepositories(basePackageClasses = {DisposableWorkerIdAssigner.class, AbcCisChargeServiceApplication.class})
public class AbcIdGeneratorConfiguration {
    @Bean
    public DisposableWorkerIdAssigner getDisposableWorkerIdAssigner() {
        return new DisposableWorkerIdAssigner();
    }
    @Bean
    public AbcIdGenerator getAbcIdGenerator(WorkerIdAssigner workerIdAssigner) {
        AbcIdGenerator abcIdGenerator = new AbcIdGenerator(workerIdAssigner);
        AbcIdUtils.setAbcIdGenerator(abcIdGenerator);
        return abcIdGenerator;
    }
}
