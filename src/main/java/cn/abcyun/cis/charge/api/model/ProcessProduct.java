package cn.abcyun.cis.charge.api.model;

import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.model.ChargeRuleProcessUsageType;
import com.google.common.collect.ImmutableBiMap;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/5/13 14:18
 */
public class ProcessProduct {
    private static final ImmutableBiMap<String, String> processProductBiMap = new ImmutableBiMap.Builder<String, String>()
            // 加工费
            .put("0_0", Constants.SystemProductId.PROCESS_PRODUCT_ID)
            // 煎药费
            .put("1_0", Constants.SystemProductId.PROCESS_TISANES_PRODUCT_ID)
            .put("1_1", Constants.SystemProductId.PROCESS_TISANES_MANUAL_PRODUCT_ID)
            .put("1_2", Constants.SystemProductId.PROCESS_TISANES_MACHINE_NORMAL_PRODUCT_ID)
            .put("1_3", Constants.SystemProductId.PROCESS_TISANES_MACHINE_STRONG_PRODUCT_ID)
            .put("1_4", Constants.SystemProductId.PROCESS_TISANES_MACHINE_NORMAL_DECOCTED_FIRST_PRODUCT_ID)
            .put("1_5", Constants.SystemProductId.PROCESS_TISANES_MACHINE_STRONG_DECOCTED_FIRST_PRODUCT_ID)
            // 制膏
            .put("2_0", Constants.SystemProductId.PROCESS_CREAM_PRODUCT_ID)
            .put("2_1", Constants.SystemProductId.PROCESS_CREAM_CHILD_PRODUCT_ID)
            .put("2_2", Constants.SystemProductId.PROCESS_CREAM_ADULT_PRODUCT_ID)
            // 打粉
            .put("3_0", Constants.SystemProductId.PROCESS_POWDER_PRODUCT_ID)
            .put("3_1", Constants.SystemProductId.PROCESS_POWDER_COARSE_PRODUCT_ID)
            .put("3_2", Constants.SystemProductId.PROCESS_POWDER_FINE_PRODUCT_ID)
            .put("3_3", Constants.SystemProductId.PROCESS_POWDER_SUPER_FINE_PRODUCT_ID)
            // 制丸
            .put("4_0", Constants.SystemProductId.PROCESS_PILL_PRODUCT_ID)
            .put("4_1", Constants.SystemProductId.PROCESS_PILL_WATER_PRODUCT_ID)
            .put("4_2", Constants.SystemProductId.PROCESS_PILL_SWEET_PRODUCT_ID)
            .put("4_3", Constants.SystemProductId.PROCESS_PILL_WATER_SWEET_PRODUCT_ID)
            .put("4_4", Constants.SystemProductId.PROCESS_PILL_ENRICHMENT_PRODUCT_ID)
            // 颗粒
            .put("5_0", Constants.SystemProductId.PROCESS_PARTICLES_PRODUCT_ID)
            // 茶包
            .put("6_0", Constants.SystemProductId.PROCESS_TEA_BAG_PRODUCT_ID)
            // 胶囊
            .put("7_0", Constants.SystemProductId.PROCESS_CAPSULE_PRODUCT_ID)
            .build();


    /**
     * 根据加工费类型->获取加工费id
     * @param type {@link ChargeRuleProcessUsageType.type}
     * @param subType {@link ChargeRuleProcessUsageType.subType}
     * @return processProductId
     */
    public static String getProcessProductId(int type, int subType) {
        return processProductBiMap.getOrDefault(String.format("%s_%s", type, subType), null);
    }

    public static String getProcessProductCmSpec(String productId) {
        if (StringUtils.isEmpty(productId)) {
            return null;
        }
        String typeGroup = processProductBiMap.inverse().getOrDefault(productId, null);
        if (StringUtils.isEmpty(typeGroup)) {
            return null;
        }
        String[] typeArray = typeGroup.split("_");
        if ("0".equals(typeArray[1])) {
            // 只有三级分类才有cmSpec 即subType不能为0
            return null;
        }
        return typeArray[1];
    }
}
