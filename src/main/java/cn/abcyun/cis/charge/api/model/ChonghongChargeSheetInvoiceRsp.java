package cn.abcyun.cis.charge.api.model;

import cn.abcyun.bis.rpc.sdk.cis.model.invoice.ChonghongInvoiceRsp;
import cn.abcyun.cis.charge.controller.StatusNameTranslator;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ChonghongChargeSheetInvoiceRsp {

    private String chargeSheetId;

    private int invoiceStatus;

    private String invoiceStatusName;

    /**
     * 发票服务返回的对象
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ChonghongInvoiceRsp invoiceRsp;

    public String getInvoiceStatusName() {
        return StatusNameTranslator.translateChargeSheetInvoiceStatusName(invoiceStatus);
    }
}
