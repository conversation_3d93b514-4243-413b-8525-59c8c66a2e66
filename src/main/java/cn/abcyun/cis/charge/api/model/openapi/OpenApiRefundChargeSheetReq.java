package cn.abcyun.cis.charge.api.model.openapi;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class OpenApiRefundChargeSheetReq {

    /**
     * 连锁ID
     */
    private String chainId;

    /**
     * 门店ID
     */
    private String clinicId;

    /**
     * 操作人ID
     */
    private String operatorId;

    /**
     * 需要退费金额
     */
    private BigDecimal needRefundFee;

    /**
     * 总退费
     */
    private BigDecimal refundFee;

    /**
     * 支付方式
     */
    private Integer payMode;

    /**
     * 支付子方式
     */
    private Integer paySubMode;

    /**
     * 收费 form 列表
     */
    private List<OpenApiRefundChargeFormReq> chargeForms;

}
