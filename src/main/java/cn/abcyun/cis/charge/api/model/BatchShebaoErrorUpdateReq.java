package cn.abcyun.cis.charge.api.model;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@Accessors(chain = true)
public class BatchShebaoErrorUpdateReq {

    @NotEmpty(message = "clinicId不能为空")
    private String clinicId;

    @NotEmpty(message = "chargeSheetInfos不能为空")
    @Size(max = 50, message = "最多不能超过20个收费单")
    @Valid
    private List<ChargeSheetInfo> chargeSheetInfos;

    @NotEmpty(message = "operatorId不能为空")
    private String operatorId;

    @Data
    @Accessors(chain = true)
    public static class ChargeSheetInfo {

        @NotEmpty(message = "chargeSheetId不能为空")
        private String chargeSheetId;

        /**
         * 这个作废，统一使用收费单id来清理异常，不维护
         */
        @Deprecated
        private List<ChargePayTransactionInfo> chargePayTransactionInfos;

    }

    @Data
    @Accessors(chain = true)
    public static class ChargePayTransactionInfo {

        @NotEmpty(message = "chargePayTransactionId不能为空")
        private String chargePayTransactionId;

        @NotEmpty(message = "taskId不能为空")
        private String taskId;
    }
}
