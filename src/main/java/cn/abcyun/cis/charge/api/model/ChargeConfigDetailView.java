package cn.abcyun.cis.charge.api.model;

import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Employee;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ChargeConfigDetailView {
    /**
     * 数据表 v2_charge_calculate_config的字段autoSendOrderInfoSwitch.
     * 这个表是关于连锁总店的配置的表，而自助支付的需求需要把这个配置配置到子店上。
     * 所以这个配置在自助支付后不再需要，单独放到redis配置里面。
     * 历史数据兼容性问题：到目前为止autoSendOrderInfoSwitch端上都是只读接口，都是0，所以不存在老数据兼容问题。
     * <p>
     * 0 不能自动推送，需要人工确认 ,1 自动推送。order push status.复用字段
     */
    public static class SelfPayPushOrderStatus {
        public static final int NOT_INIT = 100;  //未初始化，不支持,db里面设置的字段是unsignedInt
        public static final int NEED_CHARGER_CONFIRM = 0; //需要收费员确认
        public static final int AUTO_PUSH_SELF_PAY = 1; // 门诊接诊直接推
    }

    private int roundingType = 0;

    private int bargainSwitch = 0;

    private int singleBargainSwitch = 0;

    private int doctorBargainSwitch = 0;

    private int doctorSingleBargainSwitch = 0;

    private int doctorRegisteredBargainSwitch = 1;

    private int reservationRegisteredBargainSwitch = 1;

    private int doctorCanPushChargeSheetSwitch = 1;
    /**
     * 执行站开单是否可以自主议价
     */
    private int nurseBargainSwitch = 1;

    /**
     * 皮试上位通过的收费单不允许收费
     */
    private int chargeNeedAstPassSwitch;

    private int inspectBargainSwitch;

    /**
     * 欠费收费开关
     */
    private int oweSheetSwitch;

    @Deprecated
    private int autoSendOrderInfoSwitch = SelfPayPushOrderStatus.NOT_INIT;

    /**
     *门诊结束支付订单推送方式; 0：收费员人工推送，1：系统自动推送，2：患者扫码获取
     */
    private List<Integer> sendOrderInfoModes;

    /**
     * 自动关单开关
     */
    private int autoClosedSwitch;
    /**
     * 自动关单时间
     */
    private BigDecimal autoClosedTime;

    /**
     * 自动关单开关V2 0关闭  1开启（offline-task实现）
     */
    private int autoClosedSwitchV2;

    /**
     * 自动关单时间V2 （offline-task实现）
     * <p>
     * [{"label": "当天晚上", "value": 0}, {"label": "次日晚上", "value": 1}, {"label": "第3天晚上", "value": 2}]
     */
    private int autoClosedTimeV2;

    /**
     * 零售开关
     */
    private int directSaleEnable = 1;

    private List<ChargePayModeConfigView> chargePayModeConfigs;

    /**
     * 使用会员/卡项优惠后不允许使用医保收费 0=关闭 1=开启
     */
    private int usedDiscountNotAllowShebaoSwitch;

    /**
     * 卡项开通使用会员卡：0不允许 1允许
     */
    @ApiModelProperty(value = "卡项开通使用会员卡：0不允许 1允许")
    private int openPromotionCardUseMemberSwitch;

    /**
     * 卡项充值使用会员卡：0不允许 1允许
     */
    @ApiModelProperty(value = "卡项充值使用会员卡：0不允许 1允许")
    private int rechargePromotionCardUseMemberSwitch;

    @ApiModelProperty(value = "退费条件限制 0=发药 1=审核 2=调配")
    private int refundRestriction;

    /**
     * 凑整抹零指定支付方式，0：全部支付方式 1：指定支付方式
     */
    @ApiModelProperty(value = "凑整抹零指定支付方式，0：全部支付方式 1：指定支付方式")
    private int oddFeeDealType;

    /**
     * 凑整抹零指定支付方式列表
     */
    @ApiModelProperty(value = "凑整抹零指定支付方式列表")
    private List<Long> oddFeeDealPayModes;

    @ApiModelProperty(value = "退费审核 0=关闭 1=开启")
    private int refundCheck;

    @ApiModelProperty(value = "退费审核人员")
    private List<Employee> refundCheckEmployees;

    /**
     * 整单收费退费开关(0:关闭 1:开启)
      */
    @ApiModelProperty(value = "整单收费退费开关(0:关闭 1:开启)")
    private int wholeSheetOperateEnable;

    @Data
    public static class ChargePayModeConfigView {

        private Long payModeId;

        private String name;

        private String enableIcon;

        private String disableIcon;

        private String shortcutKey;

        private int isEnable;

        private int type;

        private int sort;

        private String config;
    }

}
