package cn.abcyun.cis.charge.api.model.openapi;

import cn.abcyun.cis.charge.api.model.ChargeDeliveryReq;
import cn.abcyun.cis.charge.api.model.ChargeFormReq;
import cn.abcyun.cis.charge.base.pay.CombinedPayItem;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class OpenApiPaidChargeSheetReq {


    private CombinedPayItem combinedPayItem;

    private List<ChargeFormReq> chargeForms;

    private ChargeDeliveryReq deliveryInfo;

    private BigDecimal needPayFee;

    /**
     * 期望的系统议价值
     * 凑整抹零金额，需要在 1 ~ -1 之间
     */
    private BigDecimal expectedOddFee;

    private String clinicId;

    private String chainId;

    private String operatorId;

    /**
     * 收费备注
     */
    private String chargeComment;
}
