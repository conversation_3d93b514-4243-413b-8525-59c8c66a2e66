package cn.abcyun.cis.charge.api.model;

import cn.abcyun.cis.charge.service.dto.ChargeSheetAbstract;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
public class ChargeSheetAbstractListRsp {
    private String keyword;
    private List<ChargeSheetAbstract> result;
    private int offset;
    private int limit;
    private int totalCount;

    /**
     * 挂单总应收
     */
    private BigDecimal draftReceivableTotalFee;

    /**
     * 在本门店的数量【执行站列表使用】
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer countInSelfClinic;
    /**
     * 在其它门店的数量【执行站列表使用】
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer countInOtherClinics;
    /**
     * 收费单列表使用
     */
    private ListSummary summary;

    private Integer tab;

    @Data
    public static class ListSummary {
        @Deprecated
        private int allCount;

        private int unchargedCount;

        private int normalCount;    //tab 1
        private int onlineCount;    //tab 2
        private int draftCount;     //tab 3
        private int owedCount;      //tab 4
    }
}
