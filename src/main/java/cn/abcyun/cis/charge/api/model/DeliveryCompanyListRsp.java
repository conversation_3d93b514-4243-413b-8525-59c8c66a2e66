package cn.abcyun.cis.charge.api.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
public class DeliveryCompanyListRsp {

    private List<DeliveryCompanyWithPayType> deliveryCompanies;

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class DeliveryCompanyWithPayType extends DeliveryCompanyRsp {
        private List<Integer> availablePayTypes;
    }
}
