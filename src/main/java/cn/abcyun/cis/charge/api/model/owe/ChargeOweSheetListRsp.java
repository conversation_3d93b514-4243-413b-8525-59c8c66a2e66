package cn.abcyun.cis.charge.api.model.owe;

import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Data
@Accessors(chain = true)
public class ChargeOweSheetListRsp {

    private List<ChargeOweSheetView> chargeOweSheets;

    /**
     * 总的欠费金额
     */
    private BigDecimal totalOweFee;

    public BigDecimal getTotalOweFee() {

        if (CollectionUtils.isEmpty(chargeOweSheets)) {
            return BigDecimal.ZERO;
        }

        return chargeOweSheets.stream()
                .map(ChargeOweSheetView::getNeedPay)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}
