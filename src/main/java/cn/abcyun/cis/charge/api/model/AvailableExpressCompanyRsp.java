package cn.abcyun.cis.charge.api.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class AvailableExpressCompanyRsp {

    private List<AvailableExpressCompany> companies;


    @Data
    @Accessors(chain = true)
    public static class AvailableExpressCompany {
        private String id;

        private String name;

        private List<Integer> availablePayTypes;

    }

}
