package cn.abcyun.cis.charge.api.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class BaseSuccessRsp {
    private int code;
    private String message;

    public static class Code {

        public final static int OLD_SUCCESS = 200;

        public final static int SUCCESS = 0;

        public final static int FAIL = 1;
    }

    public static BaseSuccessRsp success() {
        return new BaseSuccessRsp(Code.SUCCESS, "成功");
    }

    public static BaseSuccessRsp fail(String message) {
        return new BaseSuccessRsp(Code.FAIL, StringUtils.isNotEmpty(message) ? message : "失败");
    }
}
