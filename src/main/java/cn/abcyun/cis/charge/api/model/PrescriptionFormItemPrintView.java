package cn.abcyun.cis.charge.api.model;

import cn.abcyun.cis.commons.rpc.outpatient.PrescriptionAcupoint;
import cn.abcyun.cis.commons.rpc.outpatient.PrescriptionFormGoodsItem;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class PrescriptionFormItemPrintView {
    private String id;

    private String goodsId;
    private String domainMedicineId;

    private int type = 0;
    private int subType = 1;

    private String medicineCadn;
    private String name;
    private String specification;
    private String manufacturer;
    private Integer ast;
    private String usage;
    private BigDecimal ivgtt;
    private String ivgttUnit;
    private String freq;
    private String dosage;
    private String dosageUnit;
    private Integer days;
    private String specialRequirement;
    private BigDecimal fractionPrice;
    private BigDecimal sourceUnitPrice;
    private BigDecimal totalPrice;
    private int pharmacyType;
    private int pharmacyNo;
    private String pharmacyName;

    private int useDismounting;

    @JsonIgnore
    @JsonProperty("cMSpec")
    private String cMSpec;

    private BigDecimal unitCount;
    private String unit;
    private BigDecimal unitPrice;
    private BigDecimal costUnitPrice;
    private int sort;
    private Integer groupId;

    private JsonNode productInfo;

    private List<PrescriptionAcupoint> acupoints;
    private List<PrescriptionFormGoodsItem> externalGoodsItems;
    //杭州社保使用 医保收费类型
    private Integer payType;
    //收费类型 0 普通 1 自备
    private int chargeType;
    private int chargeStatus;
    private JsonNode astResult;
    /**
     * 外置处方-单次开方使用量
     */
    private BigDecimal externalUnitCount;

    private BigDecimal executedCount;

    private int needExecutive;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal executedTotalPrice;

    /**
     * https://modao.cc/proto/NN9JBo8Zs67owf8YW1gaz/sharing?view_mode=read_only&screen=rbpURF4rUx1KtrH56
     * 贵阳康复医院定制小需求
     *[外置处方formItem] 每次3穴位 里面3
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal acupointUnitCount;
    /**
     * [外置处方formItem]每次3穴位 里面穴位
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String acupointUnit;

}
