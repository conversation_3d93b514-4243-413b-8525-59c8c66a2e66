package cn.abcyun.cis.charge.api.model;

import cn.abcyun.cis.charge.service.dto.ChargeExecuteActionView;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.Instant;
import java.util.List;

/**
 * 收费单 执行记录列表响应
 *
 * <AUTHOR>
 * @version ChargeSheetExecuteActionListRsp.java, 2020/7/20 下午5:00
 */
@Data
@Accessors(chain = true)
public class ChargeSheetExecuteActionListRsp {
    /**
     * 兼容移动端的已有结构
     */
    @Deprecated
    private List<ChargeExecuteActionView> actions;
    /**
     * 执行记录集合
     */
    private List<ChargeExecuteActionView> executeActions;
    /**
     * 收费单创建时间
     */
    private Instant                       chargeSheetCreated;
    /**
     * 收费单开单人id，非创建人Id; 门诊开单-医生，零售开单-销售员，执行站开单-选择的开单人
     */
    private String                        chargeSheetOwnerId;
    /**
     * 收费单开单人名称
     */
    private String                        chargeSheetOwnerName;
    /**
     * 收费单创建人id
     */
    private String                        chargeSheetCreatedBy;
    /**
     * 收费单创建人名称
     */
    private String                        chargeSheetCreatedByName;

    public List<ChargeExecuteActionView> getActions(){
        return this.executeActions;
    }
}
