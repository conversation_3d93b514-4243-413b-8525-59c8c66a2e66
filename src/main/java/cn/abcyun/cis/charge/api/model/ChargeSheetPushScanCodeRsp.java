package cn.abcyun.cis.charge.api.model;

import cn.abcyun.bis.rpc.sdk.cis.model.mc.GetQrCodeRsp;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class ChargeSheetPushScanCodeRsp {

    @ApiModelProperty("收费单id")
    private String chargeSheetId;

    @ApiModelProperty("患者id")
    private String patientId;

    @ApiModelProperty("患者姓名")
    private String patientName;

    @ApiModelProperty("订单总金额")
    private BigDecimal totalFee;

    @ApiModelProperty("收费单摘要信息")
    private String abstractInfo;

    @ApiModelProperty("二维码相关信息")
    private GetQrCodeRsp qrCodeInfo;

    @ApiModelProperty("需要支付的金额")
    private BigDecimal needPayFee;

    /**
     * 诊断
     */
    @ApiModelProperty("诊断")
    private String diagnosis;


}
