package cn.abcyun.cis.charge.api.model;


import cn.abcyun.cis.charge.model.ThirdPartyPayInfo;
import cn.abcyun.cis.charge.service.dto.ChargeTransactionView;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class CreateChargeSheetRsp extends BasicCreateChargeSheetRsp {
    private int owedStatus;
    private BigDecimal needPay;
    private BigDecimal receivedFee;
    private BigDecimal netIncomeFee;        //净收入

    //锁单调整之后，第三方支付时该字段为空
    private String chargeActionId;

    //增加该字段，第三方支付时用这个字段来查询状态
    private String chargePayTransactionId;
    private String chargeTransactionId;

    private String dataSignature;

    private int payStatus;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String thirdPartyPayTaskId;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer thirdPartyPayTaskType;   //0:pay 1:refund
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private ThirdPartyPayInfo.WeChatPayInfo weChatPayInfo;

    private int isNotDispensed;

    private int isContainAirPharmacy;

    private List<String> airPharmacyOrderIds;

    private int isAirPharmacyCanPay;

    /**
     * 取号机，把锁单状态独立出来，新增加的返回字段,如果支付失败可能需要这几个值做交互，返回
     */
    private int lockStatus = 0;//锁单状态：0：未锁单，非0：锁单【10-30：锁单中(10-20是患者锁单，10:患者在微诊所锁的单，11患者在取号机锁的单，20-30是收费台锁单，20收费员锁的单）】

    private int canUnLockChargeSheet = 0;//在锁单状态下，是否可以解锁 0 不可以，1可以

    private Instant autoUnlockTime = null;//锁单状态自动解锁时间

    /**
     * 是否能够完善收费单患者信息(0:不能 1:能)
     */
    private int canImprovePatientFlag;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<ChargeTransactionView> chargeTransactions;

    public int getIsContainAirPharmacy() {
        return CollectionUtils.isEmpty(airPharmacyOrderIds) ? 0 : 1;
    }

}
