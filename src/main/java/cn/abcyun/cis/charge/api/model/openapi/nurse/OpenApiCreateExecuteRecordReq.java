package cn.abcyun.cis.charge.api.model.openapi.nurse;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 创建执行记录请求
 *
 * <AUTHOR>
 * @date 2023-08-14 23:42:17
 **/
@Data
public class OpenApiCreateExecuteRecordReq {

    /**
     * 连锁ID
     */
    @NotBlank(message = "连锁ID [chainId] 不能为空")
    private String chainId;

    /**
     * 门店ID
     */
    @NotBlank(message = "门店ID [clinicId] 不能为空")
    private String clinicId;

    /**
     * 执行人ID列表
     */
    @NotEmpty(message = "执行人ID列表 [executorIds] 不能为空")
    private List<@NotBlank(message = "执行人ID [executorId] 不能为空") String> executorIds;

    /**
     * 操作人ID
     */
    @NotBlank(message = "操作人ID [operatorId] 不能为空")
    private String operatorId;

    /**
     * 执行项
     */
    @NotEmpty(message = "执行项 [items] 不能为空")
    private List<@NotNull(message = "执行项 [item] 不能为空") OpenApiChargeNurseExecuteItemReq> items;

    /**
     * 备注
     */
    private String comment;

}
