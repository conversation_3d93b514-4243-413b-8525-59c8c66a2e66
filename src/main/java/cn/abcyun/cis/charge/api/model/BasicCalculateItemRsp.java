package cn.abcyun.cis.charge.api.model;

import cn.abcyun.cis.charge.util.MathUtils;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@Accessors(chain = true)
public class BasicCalculateItemRsp {

    private String id;
    private String unit;
    private String name;
    private BigDecimal unitCount;
    private BigDecimal doseCount;

    private int productType;

    private int productSubType;

    /**
     * 有议价的单价，判断条件：只要totalPrice != sourceTotalPrice，那currentUnitPrice就有值
     * 20240419 已作废，统一使用item维度的议价模式去计算，不考虑中间态
     */
    @Deprecated
    private BigDecimal currentUnitPrice;

    private int isExpectedPriceCleared;

    /**
     * 当前单价
     */
    private BigDecimal unitPrice;
    /**
     * 原单价
     */
    private BigDecimal sourceUnitPrice;

    /**
     * 当前金额
     */
    private BigDecimal totalPrice;  //总价不算折扣 unitPrice * unitCount * packageCount;

    /**
     * 单项金额打折比例
     */
    private BigDecimal totalPriceRatio;

    private BigDecimal expectedUnitPrice;

    private BigDecimal expectedTotalPrice;

    /**
     * 期望的单项议价比例
     */
    private BigDecimal expectedTotalPriceRatio;

    /**
     * 原金额
     */
    private BigDecimal sourceTotalPrice;

    private BigDecimal unitAdjustmentFee;

    private BigDecimal fractionPrice;

    /**
     * form上平摊下来的金额
     */
    @Deprecated
    private BigDecimal formFlatPrice;

    /**
     * sheet上平摊下来的金额
     */
    @Deprecated
    private BigDecimal sheetFlatPrice;

    private int isUnitPriceChanged;

    private int isTotalPriceChanged;

    private int composeType = 0;

    /**
     * 费用类型
     */
    private int feeComposeType;

    /**
     * 费用类型id
     */
    private Long feeTypeId;

    private int goodsFeeType;

    private List<BasicCalculateItemBatchInfoRsp> batchInfos;

    private List<BasicCalculateItemRsp> composeChildren = new ArrayList<>();

    public BigDecimal calculateTotalPrice() {
        totalPrice = MathUtils.wrapBigDecimalAdd(MathUtils.calculateTotalPrice(unitPrice, unitCount, doseCount, 2), fractionPrice);
        return totalPrice;
    }

}
