package cn.abcyun.cis.charge.api.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class PatientCardPromotionReq {

    /**
     * 卡id
     */
    @ApiModelProperty(value = "卡id")
    private String id;

    @ApiModelProperty(value = "抵扣列表")
    private List<DeductPromotionReq> deductItems;

    @ApiModelProperty(value = "是否选中")
    private boolean checked;

    public boolean getChecked() {
        return checked;
    }

    @Data
    public static class DeductPromotionReq {

        @ApiModelProperty(value = "商品id")
        private String goodsId;

        @ApiModelProperty(value = "是否选中")
        private boolean checked;

        public boolean getChecked() {
            return checked;
        }
    }
}
