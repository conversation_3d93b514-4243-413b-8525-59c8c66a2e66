package cn.abcyun.cis.charge.api.model;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.service.dto.ChargeDeliveryCompanyVo;
import cn.abcyun.cis.core.validator.Values;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class CreateChargeRuleExpressDeliveryReq {

    @NotEmpty(message = "name不能为空")
    @Size(max = 128, message = "name不能超过{max}个字")
    private String name;

    private List<ChargeRuleExpressDeliveryAddressVo> addresses;

    @Values(value = {"1", "2"}, attr = "收费类别")
    private Integer type;

    /**
     * 支持选择的支付类型：0：寄付，1：到付
     * {@link cn.abcyun.cis.charge.base.Constants.DeliveryPayType}
     */
    private List<Integer> availablePayTypes = new ArrayList<Integer>(){{
        add(Constants.DeliveryPayType.PAID_BY_CONSIGNEE);
        add(Constants.DeliveryPayType.PAID_BY_SHIPPER);
    }};

    private BigDecimal price;

    @NotNull(message = "deliveryCompany不能为空")
    private ChargeDeliveryCompanyVo deliveryCompany;

    private ChargeRuleLadderInfoVo ladderInfo;

    @Values(value = {"0", "1"}, attr = "是否开启包邮", notNull = true)
    private Integer isFreePostage;

    private int freePostageType;

    private BigDecimal freePostageUnitCount;

    private String freePostageUnit;

    /**
     * 费用类型id
     */
    private Long feeTypeId;

    public Long getFeeTypeId() {
        return feeTypeId == null ? GoodsConst.FeeTypeId.FEE_TYPE_ID_MEDICINE_SERVICE : feeTypeId;
    }
}
