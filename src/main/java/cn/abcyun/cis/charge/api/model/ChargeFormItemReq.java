package cn.abcyun.cis.charge.api.model;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.TraceableCode;
import cn.abcyun.cis.charge.base.ChargeServiceError;
import cn.abcyun.cis.charge.base.ChargeServiceException;
import cn.abcyun.cis.charge.util.ChargeUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Data
public class ChargeFormItemReq implements Serializable {
    private String id;
    private String keyId;
    private String name;
    private String unit;
    private BigDecimal unitCount;
    private BigDecimal doseCount;
    /**
     * 修改的剂量值，在中药form上有效
     */
    private BigDecimal expectedDoseCount;
    private BigDecimal executedUnitCount;
    private BigDecimal unitPrice;
    private BigDecimal discountPrice;
    private BigDecimal expectedUnitPrice;
    private BigDecimal expectedTotalPrice;
    private BigDecimal sourceUnitPrice;
    private BigDecimal totalPriceRatio;
    //单项议价比例
    private BigDecimal unitPriceRatio;
    //期望的单向议价比例
    private BigDecimal expectedUnitPriceRatio;
    private BigDecimal expectedTotalPriceRatio;
    private BigDecimal totalPrice;
    private String productId;
    private Integer productType;
    private Integer productSubType;
    private int isAirPharmacy;
    private int useDismounting;
    private int sort;
    private BigDecimal deductTotalCount;
    /**
     * 核销次数
     */
    private BigDecimal verifyTotalCount;
    private String sourceFormItemId;

    //煎法
    private String specialRequirement;

    /**
     * 大于0表示赠品
     * {@link cn.abcyun.cis.charge.base.Constants.ChargeFormItemGiftType}
     */
    private int isGift;

    /**
     * 医生id
     */
    private String doctorId;

    /**
     * 医生科室id
     */
    private String departmentId;

    /**
     * 护士id
     */
    private String nurseId;

    private String remark;

    private SimpleUsageInfo usageInfo;

    /**
     * 药房类型
     */
    private int pharmacyType;

    /**
     * 药房号：默认为0
     */
    private int pharmacyNo;

    private Integer feeComposeType;

    private Long feeTypeId;

    /**
     * 算费时的医生信息（挂号费和咨询费需要用doctorId去匹配营销活动）
     */
    private DoctorInfoReq doctorInfo;

    private String unitAdjustmentFeeLastModifiedBy;

    private List<ChargeFormItemReq> composeChildren;

    private List<SinglePromotionReq> singlePromotions;

    /**
     * 是否期望指定批次
     */
    private int isExpectedBatch;

    /**
     * 是否固定数据
     */
    private int isFixedData;

    /**
     * 批次列表
     */
    private List<ChargeFormItemBatchInfoReq> chargeFormItemBatchInfos;

    /**
     * 产生赠品的promotionId
     */
    private String giftGoodsPromotionId;

    /**
     * 为赠品时，需要这个字段来找到是由哪个item赠送的，因为前段新增item时不会传id，只能通过keyId来建立关系，所以用这个字段来找
     */
    private String sourceFormItemKeyId;

    /**
     * 批量提单时的原始chargeFormItemId列表
     */
    private List<String> originalChargeFormItemIds;

    //修正期望批次信息
    public void amendExpectedBatch() {

        if (!Objects.equals(1, isExpectedBatch)) {
            chargeFormItemBatchInfos = new ArrayList<>();
            return;
        }

        if (CollectionUtils.isEmpty(chargeFormItemBatchInfos)) {
            isExpectedBatch = 0;
        }

//
//        chargeFormItemBatchInfos = chargeFormItemBatchInfos.stream()
//                .filter(batchInfoReq -> MathUtils.wrapBigDecimalCompare(batchInfoReq.getUnitCount(), BigDecimal.ZERO) > 0)
//                .collect(Collectors.toList());
//
//        BigDecimal batchTotalCount = chargeFormItemBatchInfos.stream()
//                .map(ChargeFormItemBatchInfoReq::getUnitCount)
//                .filter(Objects::nonNull)
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
//
//        if (MathUtils.wrapBigDecimalCompare(MathUtils.calculateTotalCount(unitCount, doseCount)
//                .add(MathUtils.wrapBigDecimalOrZero(deductTotalCount)), batchTotalCount) != 0) {
//            isExpectedBatch = 0;
//            chargeFormItemBatchInfos = new ArrayList<>();
//        }

    }

    public boolean needUpdateBatch () {
        return (Objects.equals(1, getIsExpectedBatch()) || Objects.equals(1, getIsFixedData())) && getChargeFormItemBatchInfos() != null;
    }

    public void clearAllIds() {
        setId(null);

        if (CollectionUtils.isNotEmpty(getComposeChildren())) {
            getComposeChildren().forEach(ChargeFormItemReq::clearAllIds);
        }

        if (CollectionUtils.isNotEmpty(getChargeFormItemBatchInfos())) {
            getChargeFormItemBatchInfos().forEach(chargeFormItemBatchInfoReq -> chargeFormItemBatchInfoReq.setId(null));
        }
    }

    public void preHandle() {
        if (StringUtils.isBlank(getId())) {
            clearAllIds();
            return;
        }

        if (CollectionUtils.isNotEmpty(getComposeChildren())) {
            getComposeChildren().forEach(ChargeFormItemReq::preHandle);
        }
    }

    @Data
    @Accessors(chain = true)
    public static class SimpleUsageInfo {
        private Integer payType;

    }

    @Data
    @Accessors(chain = true)
    public static class DoctorInfoReq {

        /**
         * 医生id
         */
        private String doctorId;

        /**
         * 科室id
         */
        private String departmentId;

        /**
         * 号种
         */
        private int registrationCategory;

        /**
         * 诊所id
         */
        private String clinicId;

    }

    /**
     * 牙位
     */
    private List<Integer> toothNos;

    /**
     * 追溯码集合
     */
    private List<TraceableCode> traceableCodeList;
    /***
     * 应采数量
     * */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer traceableCodeNum;
    /**
     * 0 未知
     * 策略1: 根据医保规格转为大单位向上取整
     * 例：某药his规格500ml/瓶，医保规格1瓶/瓶，发药1瓶，应采数量 = 1。发药250ml，应采数量 = 1。发药700ml，应采数量 = 2
     * 策略2: 根据医保规格转为小单位
     * 例：某药20片/瓶，医保规格为20片/瓶。发药1瓶，应采数量 =20；发药35片，应采数量 = 35；发药1.5瓶，应采数量 = 30
     * 策略3：大小混合，开的大单位走策略1，开的小单位走策略2
     * 例：某药医保规格10片/瓶。发药1瓶，数量 =1。发药10片，数量 = 10。发药2.5瓶，应采数量 = 3，结算数量 = 2.5。发药14片，数量 =14
     * 策略4：根据医保规格转为大单位向下舍入，拆零不统计（本次新增）
     * 例：某药10支/盒，发药4支，应采 = 0（拆零部分自行采集），发药1盒，应采 = 1，发药24支，应采 = 2
     */
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private int policy;

    /**
     * 实采
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal trCodeCollectedNum;

    /**
     * 拆零实采
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal disTrCodeCollectedNum;


    public void checkParam() {
        if (!CollectionUtils.isEmpty(traceableCodeList)) {
            ChargeUtils.checkTraceCodeNoLength(traceableCodeList);
        }
        if (!CollectionUtils.isEmpty(composeChildren)) {
            composeChildren.forEach(ChargeFormItemReq::checkParam);
        }
    }
}
