package cn.abcyun.cis.charge.api.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
public class BasicCalculateFormRsp {

    private String id;
    private List<BasicCalculateItemRsp> items;

    private int sourceFormType;
    /**
     * 当前总价
     */
    private BigDecimal totalPrice;

    /**
     * 原总价
     */
    private BigDecimal sourceTotalPrice;

    /**
     * sheet上平摊下来的金额，没维护了
     */
    @Deprecated
    private BigDecimal sheetFlatPrice;


    private int isTotalPriceChanged;
}
