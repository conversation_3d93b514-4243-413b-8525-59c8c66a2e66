package cn.abcyun.cis.charge.api.model;

import cn.abcyun.cis.charge.model.ThirdPartyPayInfo;
import cn.abcyun.cis.charge.service.dto.OncePayCallBackReq;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.cis.commons.rpc.pay.PayCallbackReq;
import cn.abcyun.cis.commons.rpc.pay.PayExtraInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Optional;

@Data
@EqualsAndHashCode(callSuper = true)
public class PayCallbackContainPayModeReq extends PayCallbackReq {

    private int payMode;

    /**
     * 业务额外的信息
     * 微信自助支付，需要在微信支付成功的回调里面修改状态
     */
    private String extra;

    private String message;

    public BigDecimal calculateReceivedFee() {
        return MathUtils.wrapBigDecimalAdd(getReceivedFee(), Optional.ofNullable(getThirdPartPayInfo()).map(PayCallbackReq.ThirdPartPayInfo::getReceivedFee).orElse(BigDecimal.ZERO));
    }

    public OncePayCallBackReq generateMainOncePayCallBackReq() {

        ThirdPartyPayInfo thirdPartyPayInfo = new ThirdPartyPayInfo();
        thirdPartyPayInfo.setTransactionId(getTaskId());
        PayExtraInfo payExtraInfo = getExtraInfo();
        if (payExtraInfo != null) {
            thirdPartyPayInfo.setCardId(payExtraInfo.getCardId())
                    .setIdCardNum(payExtraInfo.getIdCardNum())
                    .setCardBalance(payExtraInfo.getCardBalance())
                    .setCardOwner(payExtraInfo.getCardOwner())
                    .setRelationToPatient(payExtraInfo.getRelationToPatient())
                    .setThirdPartyTransactionId(payExtraInfo.getThirdPartyTransactionId())
                    .setCqShebaoExtraInfo(payExtraInfo.getCqShebaoExtraInfo())
                    .setHzShebaoExtraInfo(payExtraInfo.getHzShebaoExtraInfo())
                    .setAccountPaymentFee(payExtraInfo.getAccountPaymentFee())
                    .setFundPaymentFee(payExtraInfo.getFundPaymentFee())
                    .setOtherPaymentFee(payExtraInfo.getOtherPaymentFee())
                    .setCardOwnerType(payExtraInfo.getCardOwnerType())
                    .setSelfConceitFee(payExtraInfo.getSelfConceitFee())
                    .setSelfPayFee(payExtraInfo.getSelfPayFee())
                    .setReceivableFee(MathUtils.wrapBigDecimal(getReceivableFee(), getReceivedFee()))
                    .setMedType(payExtraInfo.getMedType())
                    .setChannelTransactionId(payExtraInfo.getChannelTransactionId());
        }

        OncePayCallBackReq req = new OncePayCallBackReq();
        req.setPayMode(payMode)
                .setPaySubMode(getPaySubMode())
                .setReceivedFee(getReceivedFee())
                .setChargeFormItems(getChargeFormItems())
                .setThirdPartyPayInfo(thirdPartyPayInfo)
                .setExtra(getExtra())
                .setSort(0);

        return req;
    }

}
