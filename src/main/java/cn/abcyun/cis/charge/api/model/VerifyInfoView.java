package cn.abcyun.cis.charge.api.model;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsItem;
import cn.abcyun.cis.charge.model.ChargeVerifyInfo;
import cn.abcyun.cis.charge.processor.discount.ItemDeductedDetail;
import cn.abcyun.cis.charge.processor.discount.ItemVerifyDetail;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@Accessors(chain = true)
public class VerifyInfoView {

    private String id;

    private String goodsId;

    private String goodsName;

    private BigDecimal totalDeductPrice;

    private int totalDeductCount;

    private List<ItemVerifyDetail> itemVerifyDetails = new ArrayList<>();

    @JsonIgnore
    @Transient
    private GoodsItem goodsItem;

    public static VerifyInfoView ofChargeVerifyInfo(ChargeVerifyInfo chargeVerifyInfo) {
        return new VerifyInfoView()
                .setId(chargeVerifyInfo.getId())
                .setGoodsId(chargeVerifyInfo.getGoodsId())
                .setGoodsName(chargeVerifyInfo.getGoodsName())
                .setTotalDeductCount(chargeVerifyInfo.getTotalDeductCount())
                .setTotalDeductPrice(chargeVerifyInfo.getTotalDeductPrice())
                .setItemVerifyDetails(chargeVerifyInfo.getItemVerifyDetails());

    }



}
