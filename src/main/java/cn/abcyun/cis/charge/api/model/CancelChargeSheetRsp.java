package cn.abcyun.cis.charge.api.model;

import cn.abcyun.cis.commons.CisServiceError;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CancelChargeSheetRsp {

    public CancelChargeSheetRsp(CisServiceError error) {
        this.code = error.getCode();
        this.message = error.getMessage();
    }

    private int code;
    private String message;
    private String dataSignature;
}
