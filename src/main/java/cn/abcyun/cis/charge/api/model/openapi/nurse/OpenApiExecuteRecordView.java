package cn.abcyun.cis.charge.api.model.openapi.nurse;

import cn.abcyun.cis.charge.api.model.EmployeeView;
import lombok.Data;

import java.time.Instant;
import java.util.List;

/**
 * 执行记录
 *
 * <AUTHOR>
 * @date 2023-08-15 00:15:21
 **/
@Data
public class OpenApiExecuteRecordView {

    /**
     * 执行记录id
     */
    private String id;

    /**
     * 执行门店id
     */
    private String executeClinicId;

    /**
     * 执行门店名称
     */
    private String executeClinicName;

    /**
     * 状态（0：有效；1：撤销） {{@link cn.abcyun.cis.charge.model.ChargeExecuteRecord.ExecuteRecordStatus}}
     */
    private int status;

    /**
     * 备注
     */
    private String comment;

    /**
     * 执行日期
     */
    private Instant executeDate;

    /**
     * 创建人
     */
    private EmployeeView operator;

    /**
     * 撤销人
     */
    private EmployeeView cancelBy;

    /**
     * 执行人集合
     */
    private List<EmployeeView> executors;

    /**
     * 执行记录对应的执行项集合
     */
    private List<OpenApiExecuteRecordItemView> executeItems;

}
