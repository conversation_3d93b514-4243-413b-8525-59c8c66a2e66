package cn.abcyun.cis.charge.api.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;

@Data
public class PatientExecuteItemView {
    private String id;
    private String chargeFormItemId;
    private String name;
    private String statusName;
    private String clinicName;
    private Instant created;
    private String type;
    private BigDecimal unitPrice;
    private BigDecimal totalPrice;
    private BigDecimal executedCount;
    private BigDecimal unitCount;
    private int executeActionCount;

    @JsonIgnore
    private String clinicId;
}
