package cn.abcyun.cis.charge.api.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
public class QueryChargeSheetByPatientOrderIdsRsp {


    private List<ChargeSheet> chargeSheets;


    @Data
    @Accessors(chain = true)
    public static class ChargeSheet {

        /**
         * 收费单id
         */
        private String id;

        /**
         * 就诊单ID
         */
        private String patientOrderId;

        private String chainId;

        private String clinicId;

        /**
         * 总金额
         */
        private BigDecimal totalFee;

        /**
         * 应收金额，在收费单为待收时，不一定准确
         */
        private BigDecimal receivableFee;

        /**
         * 已收金额
         */
        private BigDecimal receivedFee;

        /**
         * 已退金额
         */
        private BigDecimal refundFee;

        /**
         * 收费单类型
         */
        private int type;

        /**
         * 收费单状态
         */
        private int status;

    }

}
