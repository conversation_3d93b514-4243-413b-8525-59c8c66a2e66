package cn.abcyun.cis.charge.api.model;

import cn.abcyun.bis.rpc.sdk.bis.model.order.CalculateOrderItemRsp;
import cn.abcyun.bis.rpc.sdk.bis.model.order.OrderDeliveryRuleView;
import cn.abcyun.bis.rpc.sdk.bis.model.order.OrderProcessRuleView;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
public class AirPharmacyCalculatePriceRsp {

    private List<FormRsp> forms;

    @Data
    public static class FormRsp {

        private String keyId;

        private String medicineStateScopeId;

        private String usageScopeId;

        private String vendorId;

        private List<CalculateOrderItemRsp> orderItems;

        /**
         * 快递费
         */
        private BigDecimal deliveryPrice;

        /**
         * 加工费
         */
        private BigDecimal processPrice;

        /**
         * 辅料费
         */
        private BigDecimal ingredientPrice;

        /**
         * 成品出率比例
         */
        private BigDecimal finishedRate;

        /**
         * 是否开通辅料配置
         */
        private int isOpenIngredient;

        private int isCanProcess;

        private BigDecimal totalPrice;

        private BigDecimal goodsTotalPrice;

        private OrderDeliveryRuleView orderDeliveryRule;

        private OrderProcessRuleView orderProcessRule;

        private String deliveryPrimaryFormId;

    }
}
