package cn.abcyun.cis.charge.api.model;

import cn.abcyun.bis.rpc.sdk.cis.model.search.CdssSearchResultRsp;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024-01-27 12:16
 * @Description
 *  aggregations data like:
 *     "aggregations" : {
 *         "receivedFeeTotal" : {
 *             "value" : 108.87000274658203
 *         },
 *         "refundFeeTotal" : {
 *             "value" : 0.0
 *         },
 *         "statusCounts" : {
 *             "doc_count_error_upper_bound" : 0,
 *             "sum_other_doc_count" : 0,
 *             "buckets" : [
 *             {
 *                 "key" : "2",
 *                 "doc_count" : 3
 *             }
 *       ]
 *         }
 *     }
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SalesOrderSearchResultRsp extends CdssSearchResultRsp<SalesCdssQLSearchResultItem> {

    private Aggregations aggregations;
    @Data
    public static class Aggregations {
        private TotalPriceRsp receivableFeeTotal;
        private TotalPriceRsp receivedFeeTotal;
        private TotalPriceRsp refundFeeTotal;
        private StatusCounts statusCounts;
    }

    @Data
    public static class TotalPriceRsp {
        private BigDecimal value;
    }

    @Data
    public static class StatusCounts {
        private List<StatusCount> buckets;
    }

    @Data
    public static class StatusCount {
        private String key;
        private Integer doc_count;
    }
}
