package cn.abcyun.cis.charge.api.model;

import cn.abcyun.cis.charge.service.dto.ChargeDeliveryCompanyVo;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(value = { "handler" })
public class ChargeRuleExpressDeliveryRsp {

    private String id;

    private String name;

    private List<Integer> availablePayTypes;

    private ChargeDeliveryCompanyVo company;

    private List<ChargeRuleExpressDeliveryAddressVo> addresses;

    private int status;


}
