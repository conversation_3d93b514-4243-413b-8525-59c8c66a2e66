package cn.abcyun.cis.charge.api.model;

import cn.abcyun.cis.charge.util.MathUtils;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Data
@Accessors(chain = true)
public class BasicCalculateSheetRsp {

    private String id;

    private List<BasicCalculateFormRsp> forms;

    /**
     * 当前总价
     */
    private BigDecimal totalPrice;

    /**
     * 原总价
     */
    private BigDecimal sourceTotalPrice;

    private int isTotalPriceChanged;

    private BigDecimal adjustmentFee;

    public BigDecimal getAdjustmentFee() {
        return MathUtils.wrapBigDecimalSubtract(getTotalPrice(), getSourceTotalPrice());
    }

    public BigDecimal getTotalPrice() {
        return Optional.ofNullable(forms)
                .orElse(new ArrayList<>())
                .stream()
                .map(BasicCalculateFormRsp::getTotalPrice)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}
