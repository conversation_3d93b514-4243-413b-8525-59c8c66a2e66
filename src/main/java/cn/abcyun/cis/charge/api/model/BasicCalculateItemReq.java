package cn.abcyun.cis.charge.api.model;

import cn.abcyun.cis.charge.util.MathUtils;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Data
@Accessors(chain = true)
public class BasicCalculateItemReq {
    @NotEmpty(message = "item.id不能为空")
    private String id;
    private String name;
    private String unit;
    private BigDecimal unitCount;
    private BigDecimal doseCount;
    private int productType;

    private int productSubType;
    /**
     * 当前单价
     */
    @NotNull(message = "单价不能为空")
    @DecimalMin(value = "0", message = "单价不能小于{value}")
    private BigDecimal unitPrice;
    /**
     * 当前总价
     */
    private BigDecimal totalPrice;
    private BigDecimal fractionPrice;
    private BigDecimal expectedUnitPrice;
    private BigDecimal expectedTotalPrice;

    /**
     * 单项议价比例
     */
    private BigDecimal totalPriceRatio;

    /**
     * 期望的单项议价比例
     */
    private BigDecimal expectedTotalPriceRatio;

    /**
     * 前端传的原单价
     */
    private BigDecimal frontSourceUnitPrice;

    @NotNull(message = "原价不能为空")
    @DecimalMin(value = "0", message = "原价不能小于{value}")
    private BigDecimal sourceUnitPrice;

    private BigDecimal oldSourceUnitPrice;

    private BigDecimal sourceTotalPrice;

    /**
     * form上平摊下来的金额
     */
    private BigDecimal formFlatPrice;

    /**
     * sheet上平摊下来的金额
     */
    private BigDecimal sheetFlatPrice;

    private int isUnitPriceChanged;

    private int isTotalPriceChanged;

    private int composeType = 0;

    private int feeComposeType;

    private Long feeTypeId;

    /**
     * {@link cn.abcyun.bis.rpc.sdk.cis.model.charge.GoodsFeeType}
     */
    private int goodsFeeType = 0;

    /**
     * 批次信息，暂时未启用
     */
    @Valid
    private List<BasicCalculateItemBatchInfoReq> batchInfos;

    @Valid
    private List<BasicCalculateItemReq> composeChildren;


    public void clearExpectedPrice() {
        this.expectedUnitPrice = null;
        this.expectedTotalPrice = null;
        this.expectedTotalPriceRatio = null;
    }

    public BigDecimal getFractionPrice() {
        return MathUtils.wrapBigDecimalOrZero(fractionPrice);
    }

    public BigDecimal getUnitPrice() {

        if (Objects.isNull(oldSourceUnitPrice)) {
            return unitPrice;
        }

        if (oldSourceUnitPrice.compareTo(BigDecimal.ZERO) <= 0) {
            return unitPrice;
        }

        if (MathUtils.wrapBigDecimalCompare(oldSourceUnitPrice, sourceUnitPrice) == 0) {
            return unitPrice;
        }

        //计算oldSourceUnitPrice和sourceUnitPrice之间的差价
        BigDecimal additionalSourceUnitPrice = sourceUnitPrice.subtract(oldSourceUnitPrice);

        BigDecimal finalUnitPrice = MathUtils.max(unitPrice.add(additionalSourceUnitPrice), BigDecimal.ZERO);

        return finalUnitPrice;
    }

    public BigDecimal calculateTotalPrice() {
        totalPrice = MathUtils.wrapBigDecimalAdd(MathUtils.calculateTotalPrice(getUnitPrice(), unitCount, doseCount, 2), fractionPrice);
        return totalPrice;
    }

    public BigDecimal getSourceUnitPrice() {
        return Objects.nonNull(sourceUnitPrice) ? sourceUnitPrice : MathUtils.wrapBigDecimalOrZero(getUnitPrice());
    }

    public BigDecimal calculateSourceTotalPrice() {

        if (sourceTotalPrice != null && MathUtils.wrapBigDecimalCompare(sourceTotalPrice, BigDecimal.ZERO) > 0) {
            return sourceTotalPrice;
        }

        return MathUtils.calculateTotalPrice(sourceUnitPrice, unitCount, doseCount, 2);
    }

    public boolean sourceUnitPriceChanged() {
        if (frontSourceUnitPrice == null) {
            return false;
        }

        return MathUtils.wrapBigDecimalCompare(sourceUnitPrice, frontSourceUnitPrice) != 0;
    }
}
