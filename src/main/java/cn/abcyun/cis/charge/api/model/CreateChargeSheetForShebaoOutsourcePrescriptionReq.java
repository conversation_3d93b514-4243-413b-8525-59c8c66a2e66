package cn.abcyun.cis.charge.api.model;

import cn.abcyun.bis.rpc.sdk.cis.model.charge.ChargeSheetRegisterPrescriptionReq;
import cn.abcyun.bis.rpc.sdk.cis.model.patient.CisPatientInfo;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 社保创建外购处方收费单请求
 *
 * <AUTHOR>
 * @date 2023/8/25 13:51
 **/
@Data
public class CreateChargeSheetForShebaoOutsourcePrescriptionReq {

    /**
     * 连锁ID
     */
    @NotBlank(message = "chainId不能为空")
    private String chainId;

    /**
     * 门店ID
     */
    @NotBlank(message = "clinicId不能为空")
    private String clinicId;

    /**
     * 诊所类型，透传clinic上的hisType
     * {@link cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ.HisType}
     */
    private int hisType;

    /**
     * 收费项
     */
    @NotEmpty(message = "收费项不能为空")
    private List<ChargeFormReq> chargeForms;

    /**
     * 患者信息
     */
    private CisPatientInfo patient;

    private String sellerId;

    private String sellerDepartmentId;

    private String pharmacistId;

    @NotEmpty(message = "操作人不能为空")
    private String operatorId;

    /**
     * 处方登记信息
     */
    @Valid
    private ChargeSheetRegisterPrescriptionReq registerPrescription;

}
