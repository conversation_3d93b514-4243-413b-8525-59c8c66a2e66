package cn.abcyun.cis.charge.api.model;

import cn.abcyun.cis.commons.exception.ParamRequiredException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

/**
 * 根据taskId支付收费单请求参数
 *
 * <AUTHOR>
 * @version PayChargeSheetByChargePayTransactionIdReq.java, 2023/7/5 3:44 PM
 */
@Data
@Accessors(chain = true)
public class PayChargeSheetByChargePayTransactionIdReq {
    @ApiModelProperty("连锁id")
    private String chainId;
    @ApiModelProperty("患者openId")
    private String patientOpenId;
    @ApiModelProperty("请求客户端ip地址")
    private String clientIp;
    @ApiModelProperty("支付后回跳的页面，不论成功或者失败均会回跳;调用方可以在链接中加上支付单号，回跳后查询调用方订单状态，如果订单已支付则成功， 如果订单未支付则进行查单操作确认订单结果")
    private String returnUrl;
    @ApiModelProperty("患者定位gps")
    private String patientGps;

    public void validateParams() {
        if (StringUtils.isBlank(chainId)) {
            throw new ParamRequiredException("chainId");
        }
        if (StringUtils.isBlank(patientOpenId)) {
            throw new ParamRequiredException("patientOpenId");
        }
        if (StringUtils.isBlank(clientIp)) {
            throw new ParamRequiredException("clientIp");
        }
        if (StringUtils.isBlank(returnUrl)) {
            throw new ParamRequiredException("returnUrl");
        }
    }
}
