package cn.abcyun.cis.charge.api.model;

import cn.abcyun.bis.rpc.sdk.cis.model.invoice.CreateInvoiceReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class DigitalInvoicePrintPreview extends DigitalInvoiceBasePrintView {

    private String invoiceRemark;

    private List<CreateInvoiceReq.InvoiceDetailItem> items;

}
