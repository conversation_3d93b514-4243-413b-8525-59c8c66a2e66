package cn.abcyun.cis.charge.api.model;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class RefundTaskForRegistrationReq {

    @NotEmpty(message = "就诊单id不能为空")
    private String patientOrderId;

    @NotEmpty(message = "registrationFormItemId不能为空")
    private String registrationFormItemId;

    @NotNull(message = "refundFee不能为空")
    @DecimalMin(value = "0", message = "refundFee不能小于{value}")
    private BigDecimal refundFee;

    private String chargeComment;

    private String payAuthNo;

    @NotEmpty(message = "operatorId不能为空")
    private String operatorId;
}
