package cn.abcyun.cis.charge.api.model.owe;

import cn.abcyun.cis.charge.base.pay.CombinedPayItem;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.cis.commons.exception.ParamNotValidException;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
@Slf4j
public class RefundChargeOweSheetReq {

    @NotNull(message = "payItem不能为空")
    private CombinedPayItem payItem;

    @NotEmpty(message = "oweSheetItems不能为空")
    @Valid
    private List<CombineOweSheetItem> oweSheetItems;

    private int source;

    private Long businessId;

    private String businessPayTransactionId;

    @NotEmpty(message = "patientId不能为空")
    private String patientId;

    private String memberId;

    @Data
    @Accessors(chain = true)
    public static class CombineOweSheetItem {

        @NotNull(message = "oweSheetId不能为空")
        private Long oweSheetId;

        @NotNull(message = "金额不能为空")
        @DecimalMin(value = "0", inclusive = false, message = "金额不能小于{value}")
        private BigDecimal price;

    }

    public void checkParam() {
        BigDecimal totalPrice = payItem.getAmount();
        BigDecimal sumTotalPrice = getOweSheetItems().stream()
                .filter(item -> item.getPrice() != null)
                .map(CombineOweSheetItem::getPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (MathUtils.wrapBigDecimalCompare(totalPrice, sumTotalPrice) != 0) {
            log.info("totalPrice与item累加的price不相等, totalPrice: {}, sumTotalPrice: {}", totalPrice, sumTotalPrice);
            throw new ParamNotValidException("退款金额错误");
        }
    }
}
