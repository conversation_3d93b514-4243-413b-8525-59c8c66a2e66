package cn.abcyun.cis.charge.api.model;

import cn.abcyun.bis.rpc.sdk.cis.model.clinic.OrganPrintView;
import cn.abcyun.cis.commons.rpc.shebao.QueryChargeSheetShebaoInfoRsp;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class RegistrationInvoicePrintView {

    /**
     * 病历号
     */
    private String no;

    /**
     * 姓名
     */
    private String name;

    /**
     * 收费日期
     */
    private String chargeDate;

    /**
     * 挂号费
     */
    private BigDecimal registrationFee;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * eg: 下午4号
     */
    private String orderNo;

    /**
     * 医保编号
     */
    private String socialCode;

    /**
     * 收费员
     */
    private String chargedByName;

    private OrganPrintView organ;

    /**
     * 个人现金支付
     */
    private BigDecimal personalPaymentFee;

    private QueryChargeSheetShebaoInfoRsp.ShebaoPayment shebaoPayment;
}
