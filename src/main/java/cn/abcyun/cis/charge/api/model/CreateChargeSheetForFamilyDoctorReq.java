package cn.abcyun.cis.charge.api.model;

import cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientInfo;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class CreateChargeSheetForFamilyDoctorReq {

    /**
     * 医生id
     */
    @NotEmpty(message = "医生id不能为空")
    private String doctorId;

    private String doctorName;

    /**
     * 家庭医生服务包名
     */
    private String servicePackageName;

    /**
     * 患者信息
     */
    @NotNull(message = "患者信息不能为空")
    private PatientInfo patient;

    /**
     * 诊所id
     */
    @NotEmpty(message = "诊所id不能为空")
    private String clinicId;

    /**
     * 连锁id
     */
    @NotEmpty(message = "连锁id不能为空")
    private String chainId;

    /**
     * 操作人id
     */
    @NotEmpty(message = "操作人id不能为空")
    private String operatorId;

    /**
     * 签约费用
     */
    @NotNull(message = "签约费用不能为空")
    @DecimalMin(value = "0", inclusive = false, message = "签约费不能小于{value}")
    private BigDecimal fee;

    /**
     * 家庭医生的一些基本信息及规则，业务方决定
     */
    private JsonNode familyDoctorInfo;
}
