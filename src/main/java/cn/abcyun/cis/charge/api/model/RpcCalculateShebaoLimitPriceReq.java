package cn.abcyun.cis.charge.api.model;

import cn.abcyun.bis.rpc.sdk.cis.model.shebao.RpcGetShebaoMatchedCodesReq;
import cn.abcyun.cis.charge.processor.limitprice.GoodsLimitPriceInfo;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
public class RpcCalculateShebaoLimitPriceReq {

    @NotEmpty(message = "chainId不能为空")
    private String chainId;

    @NotEmpty(message = "clinicId不能为空")
    private String clinicId;

    @Valid
    private List<CalculateShebaoLimitPriceItemReq> items;

    @Data
    @Accessors(chain = true)
    public static class CalculateShebaoLimitPriceItemReq {

        @NotEmpty(message = "item.id不能为空")
        private String id;

        /**
         * 应收单价
         */
        @NotNull(message = "应收单价不能为空")
        @DecimalMin(value = "0", message = "应收单价不能小于0")
        private BigDecimal receivableUnitPrice;

        /**
         * 应收总价
         */
        @NotNull(message = "应收总价不能为空")
        @DecimalMin(value = "0", message = "应收总价不能小于0")
        private BigDecimal receivableTotalPrice;

        @NotEmpty(message = "商品id不能为空")
        private String productId;

        private int productType;

        private int productSubType;

        private int useDismounting;

        @NotNull(message = "unitCount不能为空")
        @DecimalMin(value = "0", message = "unitCount不能小于0")
        private BigDecimal unitCount;

        @NotNull(message = "doseCount不能为空")
        @DecimalMin(value = "0", message = "doseCount不能小于0")
        private BigDecimal doseCount;

        private int isCompose;

        private int isFeeParent;

        /**
         * goods本身的拆零价格
         */
        private BigDecimal goodsPiecePrice;

        /**
         * goods本身的打包价格
         */
        private BigDecimal goodsPackagePrice;

        /**
         * goods本身的成本价
         */
        @NotNull(message = "unitCostPrice不能为空")
        private BigDecimal unitCostPrice;

        @Valid
        private List<CalculateShebaoLimitPriceItemReq> composeChildren;

        @Valid
        private List<CalculateShebaoLimitPriceItemBatchInfoReq> batchInfos;


//        /**
//         * 匹配社保目录医保限价req
//         */
//        private RpcGetShebaoMatchedCodesReq.GetSmartMatchedReqItem shebaoMatchedCodesItem;

        private boolean canPayByHealthCard;

        /**
         *  商品限价类型和价格信息
         */
        private GoodsLimitPriceInfo goodsLimitPriceInfo;
    }

    @Data
    @Accessors(chain = true)
    public static class CalculateShebaoLimitPriceItemBatchInfoReq {

        @NotEmpty(message = "batchInfoReq.id不能为空")
        private String id;

        private BigDecimal unitCostPrice;

        private BigDecimal receivableTotalPrice;

        private BigDecimal receivableUnitPrice;

        private BigDecimal totalCount;

        /**
         *  商品限价类型和价格信息
         */
        private GoodsLimitPriceInfo goodsLimitPriceInfo;
    }
}
