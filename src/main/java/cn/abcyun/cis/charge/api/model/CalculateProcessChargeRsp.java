package cn.abcyun.cis.charge.api.model;

import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Data
public class CalculateProcessChargeRsp {

    private List<ProcessInfoView> processInfos;

    private BigDecimal processTotalFee = BigDecimal.ZERO;

    public BigDecimal getProcessTotalFee() {

        if (!CollectionUtils.isEmpty(processInfos)) {
            processTotalFee = processInfos.stream()
                    .filter(ProcessInfoView::getChecked)
                    .map(ProcessInfoView::getProcessFee)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        return processTotalFee;
    }

    public void setProcessTotalFee(BigDecimal processTotalFee) {
        this.processTotalFee = processTotalFee;
    }
}
