package cn.abcyun.cis.charge.api.model;

import lombok.Data;

import java.util.List;

@Data
public class ChargeOrderCountViewReq {
   private List<String> patientIds;
   private String chainId;

   /**
    * 微诊所自负支付需求增加字段
    * 2020-08
    * */
   private int   queryType = 15 ;    //根据需要组合
   public static class QueryType {
      public static final int WAITING_PAYING = 1;
      public static final int WAITING_DISPENSING = 2;
      public static final int WAITING_DELIVERY = 4;
      public static final int FINISHED = 8;
   }
}
