package cn.abcyun.cis.charge.api.model.openapi.nurse;

import cn.abcyun.cis.commons.model.CisPatientInfo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.Instant;

/**
 * 开放平台执行列表视图
 *
 * <AUTHOR>
 * @date 2023-08-14 22:57:36
 **/
@Data
public class OpenApiChargeNurseListView {

    /**
     * 收费单ID
     */
    private String id;

    /**
     * 就诊单ID
     */
    private String patientOrderId;

    /**
     * 执行单状态 {@link cn.abcyun.cis.charge.model.ChargeSheet.ExecuteStatus}
     */
    private int status;

    /**
     * 患者信息
     */
    private CisPatientInfo patient;

    /**
     * 执行内容摘要信息
     */
    private String abstractInfo;

    /**
     * 是否已关闭
     */
    private int isClosed;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Instant created;

}
