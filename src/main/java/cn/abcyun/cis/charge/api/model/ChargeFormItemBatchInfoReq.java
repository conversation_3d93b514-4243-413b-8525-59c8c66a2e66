package cn.abcyun.cis.charge.api.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class ChargeFormItemBatchInfoReq {

    private String id;

    private String stockId;
    /**
     * 批次id
     */
    private String batchId;

    /**
     * 批次号
     */
    private String batchNo;

    private String expiryDate;

    /**
     * 批次数量
     */
    private BigDecimal unitCount;

}
