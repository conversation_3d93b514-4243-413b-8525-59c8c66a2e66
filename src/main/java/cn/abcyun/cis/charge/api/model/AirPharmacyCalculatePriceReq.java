package cn.abcyun.cis.charge.api.model;

import cn.abcyun.bis.rpc.sdk.bis.model.order.CalculateLogisticsReq;
import cn.abcyun.bis.rpc.sdk.cis.model.outpatient.UsageInfo;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
public class AirPharmacyCalculatePriceReq {

    @Valid
    private List<FormReq> forms;

    @Data
    public static class FormReq {

        @NotEmpty(message = "keyId不能为空")
        private String keyId;

        @NotEmpty(message = "medicineStateScopeId不能为空")
        private String medicineStateScopeId;

        @NotEmpty(message = "usageScopeId不能为空")
        private String usageScopeId;

        @NotEmpty(message = "goodsTypeId不能为空")
        private String goodsTypeId;

        @NotEmpty(message = "vendorId不能为空")
        private String vendorId;

        @NotNull
        private UsageInfo usageInfo;

        /**
         * 快递费所在的fromid
         */
        private String deliveryPrimaryFormId;

        @NotNull
        private CalculateLogisticsReq deliveryInfo;

        @Valid
        private List<ItemReq> items;

    }

    @Data
    public static class ItemReq {

        private String productId;

        @NotEmpty(message = "item.name不能为空")
        private String name;

        @NotEmpty(message = "item.unitPrice不能为空")
        private BigDecimal unitPrice;

        private String unit;

        @NotNull(message = "item.unitCount不能为空")
        private BigDecimal unitCount;

        @Min(value = 0, message = "doseCount不能小于{value}")
        private int doseCount;

    }
}
