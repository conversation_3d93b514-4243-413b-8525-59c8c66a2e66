package cn.abcyun.cis.charge.api.model;

import cn.abcyun.cis.charge.service.dto.BusinessSheetCalculateChargeSheetDto;
import cn.abcyun.cis.charge.util.ChargeFormItemUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class MedicalPlanSheetDetailCalculateRsp extends BusinessSheetBasicPriceInfo {

    private int status;

    /**
     * 咨询单的收费项
     */
    private List<MedicalPlanForm> medicalPlanForms;

    /**
     * 非咨询单的收费项
     */
    private List<MedicalPlanForm> chargeForms;

    public void buildAllPrice() {
        List<MedicalPlanForm> flag = new ArrayList<>();
        flag.addAll(Optional.ofNullable(this.medicalPlanForms).orElse(new ArrayList<>()));
        flag.addAll(Optional.ofNullable(this.chargeForms).orElse(new ArrayList<>()));
        calculateBasicPriceInfo(flag, this);
    }

    @Data
    @Accessors(chain = true)
    @EqualsAndHashCode(callSuper = true)
    public static class MedicalPlanForm extends BusinessSheetCalculateChargeSheetDto.BusinessBasicChargeForm {
        private List<MedicalPlanFormItem> formItems;

        public void buildAllPrice() {
            //计算总金额时只能计算直接开的item的总金额，子项（包含套餐子项和费用子项）的都不计算
            List<BusinessSheetBasicPriceInfo> items = Optional.ofNullable(formItems).orElse(new ArrayList<>())
                    .stream()
                    .filter(item -> ChargeFormItemUtils.isTopItem(item.getComposeType(), item.getGoodsFeeType()))
                    .collect(Collectors.toList());

            calculateBasicPriceInfo(items, this);
        }

    }

    @Data
    @Accessors(chain = true)
    @EqualsAndHashCode(callSuper = true)
    public static class MedicalPlanFormItem extends BusinessSheetCalculateChargeSheetDto.BusinessBasicChargeFormItem  {

    }
}
