package cn.abcyun.cis.charge.api.model;

import cn.abcyun.cis.core.validator.Values;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ChargeRuleLadderInfoVo {

    private String id;

    private int type;

    private int ruleType;

    private String unit;

    private BigDecimal unitCount;

    private BigDecimal price;

    private BigDecimal additionalCount;

    private BigDecimal additionalPrice;

    private int roundType;

    public static class RoundType{
        public static final int ROUND_DOWN=0;
        public static final int ROUND_UP=1;
    }
    public static class RuleType{
        //快递费规则
        public static final int EXPRESS_DELIVERY = 1;
        //加工费规则
        public static final int PRECESS = 2;
    }
}
