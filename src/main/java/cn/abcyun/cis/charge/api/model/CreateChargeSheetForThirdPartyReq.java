package cn.abcyun.cis.charge.api.model;

import cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientInfo;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class CreateChargeSheetForThirdPartyReq {

    @NotNull(message = "patient不能为空")
    private PatientInfo patient;

    @NotEmpty(message = "clinicId不能为空")
    private String clinicId;

    @NotEmpty(message = "chainId不能为空")
    private String chainId;

    @NotEmpty(message = "operatorId不能为空")
    private String operatorId;

    @NotNull(message = "支付金额不能为空")
    @DecimalMin(value = "0", message = "支付金额不能小于{value}")
    @DecimalMax(value = "100000000", message = "支付金额不能大于{value}")
    private BigDecimal amount;
    /**
     * 期望议价值
     */
    private BigDecimal expectedAdjustmentFee;

    private String sellerId;
    
    /**
     * 类型 {@link Type}
     */
    private int type;

    public static final class Type {

        /**
         * 营销卡开通
         */
        public static final int PROMOTION_CARD_OPEN = 1;

        /**
         * 营销卡充值
         */
        public static final int PROMOTION_CARD_RECHARGE = 2;

        /**
         * 家庭医生
         */
        public static final int FAMILY_DOCTOR_SIGN = 3;

        /**
         * 会员充值
         */
        public static final int MEMBER_CARD_RECHARGE = 4;
    }
}
