package cn.abcyun.cis.charge.api.model;

import cn.abcyun.cis.charge.model.ChargeRefundTaskItem;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

import javax.persistence.Id;
import java.math.BigDecimal;
import java.util.Objects;

@Data
@Accessors(chain = true)
public class ChargeRefundTaskItemView {

    /**
     * 主键id
     */
    @Id
    private String id;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 退费金额
     */
    private BigDecimal amount;

    /**
     * 支付方式
     */
    private int payMode;

    /**
     * 支付子方式
     */
    private int paySubMode;

    /**
     * 状态
     * 0：未发起退费，10：退费中，20：退费成功，30：退费失败
     * {@link ChargeRefundTaskItem.Status}
     */
    private int status;

    private String statusName;

    /**
     * 错误信息
     */
    private String errorMessage;

    public String getStatusName() {
        String statusName = "";
        switch (status) {
            case ChargeRefundTaskItem.Status.WAIT_REFUND:
                statusName = "待申请退款";
                break;
            case ChargeRefundTaskItem.Status.REFUNDING:
                statusName = "退款中";
                break;
            case ChargeRefundTaskItem.Status.REFUND_SUCCESS:
                statusName = "退款成功";
                break;
            case ChargeRefundTaskItem.Status.REFUND_FAIL:
                statusName = "退款失败";
                break;
        }
        return statusName;
    }

    public static ChargeRefundTaskItemView ofChargeRefundTaskItemView(ChargeRefundTaskItem chargeRefundTaskItem) {

        if (Objects.isNull(chargeRefundTaskItem)) {
            return null;
        }

        ChargeRefundTaskItemView chargeRefundTaskItemView = new ChargeRefundTaskItemView();
        BeanUtils.copyProperties(chargeRefundTaskItem, chargeRefundTaskItemView);
        return chargeRefundTaskItemView;
    }
}
