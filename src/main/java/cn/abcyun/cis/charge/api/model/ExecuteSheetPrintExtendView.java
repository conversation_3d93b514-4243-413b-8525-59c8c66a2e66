package cn.abcyun.cis.charge.api.model;

import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.cis.commons.rpc.print.ExecuteFormPrintView;
import cn.abcyun.cis.commons.rpc.print.ExecuteSheetPrintView;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Data
@EqualsAndHashCode(callSuper = true)
public class ExecuteSheetPrintExtendView extends ExecuteSheetPrintView {

    private List<PrescriptionFormPrintView> prescriptionExternalForms;

    public BigDecimal getTotalPrice() {
        BigDecimal executeFormTotalPrice = Optional.ofNullable(getExecuteForms())
                .orElse(new ArrayList<>())
                .stream().map(ExecuteFormPrintView::getTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal prescriptionExternalFormsTotalPrice = Optional.ofNullable(prescriptionExternalForms)
                .orElse(new ArrayList<>())
                .stream().map(PrescriptionFormPrintView::getTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        return MathUtils.wrapBigDecimalAdd(executeFormTotalPrice, prescriptionExternalFormsTotalPrice);
    }
}
