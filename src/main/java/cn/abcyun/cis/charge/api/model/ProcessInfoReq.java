package cn.abcyun.cis.charge.api.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;

@Data
public class ProcessInfoReq {

    private String id;

    private String keyId;

    /**
     * 加工费的formId
     */
    @JsonIgnore
    private String processFormId;

    /**
     * 中药的formId
     */
    private String chargeFormId;

    private boolean checked;

    private int type;

    private int subType;

    private int bagUnitCount;

    /**
     * 带小数的 袋数
     */
    @JsonProperty("processBagUnitCount")
    private BigDecimal processBagUnitCountDecimal;

    private BigDecimal totalProcessCount;

    private String processRemark;

    /**
     * 取药时间
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Instant takeMedicationTime;

    public boolean getChecked() {
        return checked;
    }

    public void setChecked(boolean checked) {
        this.checked = checked;
    }
    
    public BigDecimal getProcessBagUnitCountDecimal(){
        if (processBagUnitCountDecimal == null || processBagUnitCountDecimal.compareTo(BigDecimal.ZERO)<=0){
                processBagUnitCountDecimal = new BigDecimal(bagUnitCount);
        }
        return processBagUnitCountDecimal;
    }

}
