package cn.abcyun.cis.charge.api.model;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Data
@Accessors(chain = true)
public class BasicCalculateFormReq {
    @NotEmpty(message = "form.id不能为空")
    private String id;

    private int sourceFormType;

    private String usageScopeId;

    private String medicineStateScopeId;

    private String vendorId;

    private String vendorName;

    /**
     * 当前总价
     */
    private BigDecimal totalPrice;

    /**
     * 原价
     */
    private BigDecimal sourceTotalPrice;

    private BigDecimal expectedTotalPrice;

    @Valid
    private List<BasicCalculateItemReq> items;

    /**
     * 药房号：默认为0
     */
    private int pharmacyNo;

    /**
     * 药房类型：0：实体药房，1：空中药房，2：虚拟药房，
     */
    private int pharmacyType;


    private BigDecimal sheetFlatPrice;

    private int isTotalPriceChanged;

    public void clearExpectedPrice() {
        this.expectedTotalPrice = null;
    }

    public BigDecimal getSourceTotalPrice() {
        return Optional.ofNullable(items)
                .orElse(new ArrayList<>())
                .stream()
                .map(BasicCalculateItemReq::calculateSourceTotalPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}
