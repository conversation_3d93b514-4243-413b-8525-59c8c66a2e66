package cn.abcyun.cis.charge.api.model;

import cn.abcyun.cis.charge.model.ChargeFormItemBatchInfo;
import cn.abcyun.cis.charge.model.ChargeTransactionRecordBatchInfo;
import cn.abcyun.cis.charge.service.dto.ChargeFormItemBatchInfoView;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import javax.persistence.Id;
import java.math.BigDecimal;
import java.time.Instant;

/**
 * ChargeTransactionRecordBatchInfoView
 *
 * <AUTHOR>
 * @since 2024/7/24 09:56
 **/
@Data
public class ChargeTransactionRecordBatchInfoView {


    @Id
    private String id;

    private String patientOrderId;

    private String chargeSheetId;

    private String transactionId;

    private String transactionRecordId;

    private String productId;

    private String chargeFormItemId;

    private String chargeFormItemBatchInfoId;

    private BigDecimal unitCount;

    private BigDecimal receivedPrice;

    private BigDecimal totalCostPrice;

    private BigDecimal promotionPrice;

    private BigDecimal sourceTotalPrice;

    private String batchId;

    private int isOldRecord;

    private int chargeType;

    private BigDecimal presentAmount;

    private BigDecimal principalAmount;

    private Instant created;

    private String createdBy;

    private String lastModifiedBy;

    private Instant lastModified;

    public static ChargeTransactionRecordBatchInfoView of(ChargeTransactionRecordBatchInfo batchInfo, ChargeFormItemBatchInfo chargeFormItemBatchInfo) {
        if (batchInfo == null) {
            return null;
        }

        ChargeTransactionRecordBatchInfoView batchInfoView = new ChargeTransactionRecordBatchInfoView();
        BeanUtils.copyProperties(batchInfo, batchInfoView);
        if (chargeFormItemBatchInfo != null) {
            batchInfoView.setBatchId(chargeFormItemBatchInfo.getBatchId());
        }
        return batchInfoView;
    }
}
