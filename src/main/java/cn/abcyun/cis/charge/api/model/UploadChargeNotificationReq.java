package cn.abcyun.cis.charge.api.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;

@Data
@Accessors(chain = true)
public class UploadChargeNotificationReq {

    /**
     * 附件地址
     */
    @ApiModelProperty(value = "附件地址")
    @NotEmpty(message = "附件地址不能为空")
    private String url;
    /**
     * 附件名称
     */
    @ApiModelProperty(value = "附件名称")
    private String fileName;
    /**
     * 附件大小
     */
    @ApiModelProperty(value = "附件大小")
    private String fileSize;

    @ApiModelProperty(value = "附件宽")
    private Integer imageWidth;

    @ApiModelProperty(value = "附件高")
    private Integer imageHeight;
}
