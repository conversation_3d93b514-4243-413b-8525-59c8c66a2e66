package cn.abcyun.cis.charge.api.model;

import cn.abcyun.cis.charge.service.dto.*;
import cn.abcyun.cis.charge.service.dto.print.MedicalBillPrintView;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.cis.commons.rpc.patient.MemberInfo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class CalculateChargeSheetRsp {
    private String chargeSheetId;
    private BigDecimal totalFee;            //总费用
    private BigDecimal adjustmentFee;       //议价【扣除】【负数】
    private BigDecimal couponFee;           //代金券抵扣  【负数】
    private BigDecimal discountFee;         //折扣【扣除】【负数】
    private BigDecimal receivableFee;       //应收
    private BigDecimal sheBaoReceivableFee; //社保应收
    private BigDecimal needPayFee;          //需支付金额(收费时)
    private BigDecimal needRefundFee;       //需退费金额(退费时)
    private BigDecimal owedRefundFee;       //欠退金（退费时）
    private BigDecimal maxRefundFee;        //最多可退金额（退费时）
    private BigDecimal draftAdjustmentFee;  //收费处整单议价的值
    private BigDecimal outpatientAdjustmentFee; //门诊处整单议价的值
    private BigDecimal oddFee;  //零头处理值（四舍五入的值）
    private Integer roundingType;  //四舍五入的规则类型
    private BigDecimal afterRoundingDiscountedTotalFee;  //totalFee减去折扣并且四舍五入之后的值
    private BigDecimal excludeDraftAndOddFee; //不包含议价和零头的金额
    private BigDecimal sourceTotalPrice; //原价总金额
    private BigDecimal discountTotalFee; //优惠总金额 = 单项优惠总金额 + 整单优惠总金额
    private BigDecimal singleDiscountTotalFee; //单项优惠总金额
    private BigDecimal packageDiscountTotalFee; //整单优惠总金额

    private BigDecimal promotionDiscountTotalFee;// 折扣总数【负数】包含挂号费已经收了的折扣总数
    private BigDecimal unitAdjustmentFee; //单项议价总值
    private List<CalculateChargeFormView> chargeForms;
    private List<PromotionView> promotions;
    private List<CouponPromotionView> couponPromotions;
    private List<GiftRulePromotionView> giftRulePromotions;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private OncomingGiftRulePromotionView oncomingGiftRulePromotion;
    private List<ProcessInfoView> processInfos;
    private PatientPointsInfoView patientPointsInfo;
    @ApiModelProperty(value = "卡项抵扣信息")
    private List<PatientCardPromotionView> patientCardPromotions;
    private List<PatientPointDeductProductPromotionView> patientPointDeductProductPromotions;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @ApiModelProperty(value = "可用于支付的卡项列表")
    private List<PatientCardView> canPaidPatientCards;

    @ApiModelProperty(value = "核销抵扣信息")
    private List<VerifyInfoView> verifyInfoViews;

    private BigDecimal registrationDiscountFee;
    private BigDecimal westernMedicineDiscountFee;
    private BigDecimal chineseMedicineDiscountFee;
    private BigDecimal treatmentDiscountFee;
    private BigDecimal examinationDiscountFee;
    private BigDecimal materialDiscountFee;
    private boolean isUseLimitPrice = false;
    /**
     * 是否触发挂网价限价
     */
    private boolean isUseListingPrice = false;
    private BigDecimal patientTotalFee;
    private String memberId;
    private MemberInfo memberInfo;
    private List<MedicalBillPrintView> medicalBill;

    /**
     * 成本总价
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal totalCostPrice;

    private Integer retailType;
    /**
     * 批量提单时选中的收费单id列表
     */
    private List<String> batchExtractChargeSheetIds;

    public boolean isUseLimitPrice() {
        return isUseLimitPrice;
    }

    public void setUseLimitPrice(boolean useLimitPrice) {
        isUseLimitPrice = useLimitPrice;
    }

    public BigDecimal getMaxRefundFee() {
        return MathUtils.wrapBigDecimalAdd(needRefundFee, owedRefundFee);
    }

}
