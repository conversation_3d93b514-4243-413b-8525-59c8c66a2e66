package cn.abcyun.cis.charge.api.model;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class RpcAirPharmacyAvailableCompanyReq {

    @NotEmpty(message = "clinicId不能为空")
    private String clinicId;

    @NotEmpty(message = "goodsTypeId不能为空")
    private String goodsTypeId;

    @NotEmpty(message = "vendorId不能为空")
    private String vendorId;

    @NotEmpty(message = "usageScopeId不能为空")
    private String usageScopeId;

    private String medicineStateScopeId;
    /**
     * 省id
     */
    @NotEmpty(message = "addressDistrictId不能为空")
    private String addressDistrictId;

    /**
     * 区名
     */
    @NotEmpty(message = "addressDistrictName不能为空")
    private String addressDistrictName;

}
