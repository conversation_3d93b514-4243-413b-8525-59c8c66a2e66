package cn.abcyun.cis.charge.api.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class RpcOnlyPayChargeSheetReq extends OnlyPayChargeSheetReq{

    @NotEmpty(message = "clinicId不能为空")
    private String clinicId;

    @NotEmpty(message = "操作人不能为空")
    private String operatorId;

}
