package cn.abcyun.cis.charge.api.model;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.cis.charge.service.dto.ChargeDeliveryCompanyVo;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ChargeRuleExpressDeliveryView {

    private String id;

    private String chainId;

    private String clinicId;

    /**
     * 规则名称
     */
    private String name;

    private List<Integer> availablePayTypes;

    private ChargeDeliveryCompanyVo deliveryCompany;

    private List<ChargeRuleExpressDeliveryAddressVo> addresses;

    private ChargeRuleLadderInfoVo ladderInfo;


    private String ruleInfo;

    /**
     * 类别：0：未知，1：固定收费，2：阶梯收费
     */
    private int type;

    /**
     * 固定快递费用
     */
    private BigDecimal price;

    /**
     * 是否免邮：0：不免邮，1：免邮
     */
    private int isFreePostage;

    /**
     * 免邮类别：0：未知，1：处方费，2：处方剂数，3：药品数量
     */
    private int freePostageType;

    /**
     * 免邮门槛
     */
    private BigDecimal freePostageUnitCount;

    /**
     * 免邮单位：元，剂，kg
     */
    private String freePostageUnit;

    /**
     * 状态：0：已禁用，1：已启用
     */
    private int status;

    /**
     * 费用类型id
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long feeTypeId;

    public Long getFeeTypeId() {
        return feeTypeId == null ? GoodsConst.FeeTypeId.FEE_TYPE_ID_MEDICINE_SERVICE : feeTypeId;
    }

}
