package cn.abcyun.cis.charge.api.model;

import cn.abcyun.bis.rpc.sdk.cis.model.patient.CisPatientInfo;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 检查站开单请求参数
 */
@Data
@Accessors(chain = true)
public class CreateChargeSheetForExaminationInspectionReq {

    @NotEmpty(message = "chainId不能为空")
    private String chainId;

    @NotEmpty(message = "clinicId不能为空")
    private String clinicId;

    @NotEmpty(message = "收费项不能为空")
    private List<ChargeFormReq> chargeForms;

    private CisPatientInfo patient;

    private String sellerId;

    private String sellerDepartmentId;

    @NotEmpty(message = "操作人不能为空")
    private String operatorId;
}
