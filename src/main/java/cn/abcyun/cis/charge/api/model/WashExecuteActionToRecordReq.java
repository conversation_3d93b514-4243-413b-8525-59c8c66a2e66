package cn.abcyun.cis.charge.api.model;

import com.google.common.collect.Lists;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang.StringUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 将 execute action 数据洗到 execute record 表请求
 *
 * <AUTHOR>
 * @version WashExecuteActionToRecordReq.java, 2020/8/4 下午9:21
 */
@Data
@Accessors(chain = true)
public class WashExecuteActionToRecordReq {

    public static Map<String, List<String>> onlineEnvClinicsMap = new HashMap<String, List<String>>(){{
        put("P", Arrays.asList("77719d57861646a788726d1c5587f473","fb281d70634a4f74b2141a172037265c"));
        put("G", Arrays.asList("fb281d70634a4f74b2141a172037265c","37733ccc8c6a44258113153e1f87c1ee",
                "45e73419e1a14ecda246d6695037133c","c2ea8ed34cc94dc28f57a1144e37ab60",
                "22ca4183b86147b8ad77b72928e236f2","dbb892be0a4d42fd873bc83a685512df",
                "ffffffff000000000726542801c12000","f83d87abde564e6e8419ea3a000167db",
                "ffffffff00000000053f86e8011ae000","ffffffff0000000005c98e18014ae000",
                "ffffffff0000000006540440014ae000","ffffffff0000000006e7271001b24000",
                "ffffffff0000000006f2140801b24000","ffffffff0000000006f2162001b1c000",
                "ffffffff0000000007b8cb7801f50001","ffffffff0000000007b8d68001f4e000",
                "ffffffff0000000007baca2001f50000","ffffffff0000000007da94a001f50000",
                "ffffffff00000000084d1bf802110000","ffffffff00000000029e5860003b4000",
                "ffffffff000000000824012801f96000","cf1466806f084f92953fb0a125bd0b03",
                "ffffffff0000000007c50ef001f50000","ffffffff0000000005d381d0014b2000",
                "fb354433baf6435da8d65ca4454197c8","6de428ff035d4396a90f0bd3e96180d1"));
    }};

    public static Map<String, List<String>> onlineEnvChainsMap = new HashMap<String, List<String>>(){{
        put("P", Arrays.asList("e040fead29154787b1ee3c88de2be5fa","ffffffff000000000830075002110000"));
        put("G", Arrays.asList("efa2923ba6174f69948447a10ff1cf8b","29686c6e59c84b81a006e3f87628c48d",
                "06bcec85ba934c079d70d13410167730","98ab764c1a29469695c0699d80bd59f2"));
    }};

    /**
     * 线上环境；分 P、G；不指定就全刷
     */
    private String onlineEnv;
    /**
     * executeActionId, 指定需要洗的数据id，单条刷
     */
    private String actionId;
    /**
     * 指定门店id洗数据，支持多个，以英文逗号隔开
     */
    private String clinicId;

    public List<String> getEnvClinics(){
        if (StringUtils.isBlank(onlineEnv)){
            return null;
        }
        return onlineEnvClinicsMap.getOrDefault(onlineEnv.toUpperCase(), Lists.newArrayList());
    }

    public List<String> getEnvChains(){
        if (StringUtils.isBlank(onlineEnv)){
            return null;
        }
        return onlineEnvChainsMap.getOrDefault(onlineEnv.toUpperCase(), Lists.newArrayList());
    }
}
