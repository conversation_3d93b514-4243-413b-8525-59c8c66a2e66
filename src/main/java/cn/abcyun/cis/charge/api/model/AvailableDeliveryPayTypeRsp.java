package cn.abcyun.cis.charge.api.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class AvailableDeliveryPayTypeRsp {

    private List<DeliveryPayTypeView> availablePayTypes;

    /**
     * 配送方式
     */
    @Data
    @Accessors(chain = true)
    public static class DeliveryPayTypeView {

        /**
         * 配送方式
         */
        private int payType;

        /**
         * 可用的公司列表
         */
        List<DeliveryCompanyRsp> companies;

    }

}
