package cn.abcyun.cis.charge.api.model;

import cn.abcyun.cis.charge.base.ChargeServiceError;
import cn.abcyun.cis.charge.base.ChargeServiceException;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.util.DateUtils;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 执行记录创建请求参数
 *
 * <AUTHOR>
 * @version ExecuteRecordCreateReq.java, 2020/7/30 下午4:55
 */
@Data
@Accessors(chain = true)
public class ExecuteRecordCreateReq {
    /**
     * 执行记录详情
     */
    private List<ExecuteRecordCreateItem> items;
    /**
     * 执行人id集合
     */
    private Set<String> executorIds;
    /**
     * 执行效果， 已废弃，使用 comment 字段，字段保留，app老版本可能在使用这个字段
     */
    @Deprecated
    private String executeEffect;
    /**
     * 执行时间，默认为空，满足 导入执行记录需求
     */
    private String executeDate;
    /**
     * 是否是导入；0：否；1：是。导入使用
     */
    private int isImport;
    /**
     * 备注
     */
    private String comment;
    /**
     * 是否需要执行效果；0：否；1：是
     */
    private int needExecuteEffect;
    /**
     * 执行效果对患者是否可见；0：不可见；1：可见。
     */
    private int effectVisibleForPatient;
    /**
     * 执行效果集合
     */
    private List<ExecuteRecordEffectView> effects;
    /**
     * 是否需要上门护理
     */
    private int needHomeCare;
    /**
     * 上门护理地点
     */
    private String homeCareAddress;
    /**
     * 上门护理开始时间
     */
    private String homeCareStartTime;
    /**
     * 执行时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date executeTime;
    /**
     * 上们护理结束时间
     */
    private String homeCareEndTime;


    public String getComment() {
        return StringUtils.isNotBlank(this.comment) ? this.comment : this.executeEffect;
    }

    @Data
    @Accessors(chain = true)
    public static class ExecuteRecordCreateItem {
        /**
         * 收费单子项id
         */
        private String chargeFormItemId;
        /**
         * 执行次数, 鉴于某些执行项的需要执行次数为0，只校验不可小于0
         */
        private Integer count;

        public void validateParam() throws ParamRequiredException {
            if (StringUtils.isBlank(chargeFormItemId)) {
                throw new ParamRequiredException("执行项目");
            }
            if (Objects.isNull(count) || count < 0) {
                throw new ParamRequiredException("执行次数");
            }
        }
    }

    public void validateParam() throws ParamRequiredException, ChargeServiceException {
        validateItems();
        if (CollectionUtils.isEmpty(executorIds)) {
            throw new ParamRequiredException("执行人");
        }
        if (StringUtils.length(this.getComment()) > 2000) {
            throw new ChargeServiceException(ChargeServiceError.COMMENT_LENGTH_MORE_THAN_TWO_THOUSAND);
        }
        if (needExecuteEffect == 0) {
            effects = Lists.newArrayList();
        }
        if (CollectionUtils.isEmpty(effects)) {
            return;
        }
        if (effects.stream()
                .anyMatch(effectView -> StringUtils.isBlank(effectView.getTreatmentMethod())
                        && StringUtils.isBlank(effectView.getTreatmentSite())
                        && StringUtils.isBlank(effectView.getTreatmentResponse())
                        && StringUtils.isBlank(effectView.getEtiologyPathogenesis())
                        && StringUtils.isBlank(effectView.getTreatmentResult())
                        && CollectionUtils.isEmpty(effectView.getAttachments())
                        && StringUtils.isAnyBlank(effectView.getExecuteDate(), effectView.getExecuteTimeStart(), effectView.getExecuteTimeEnd()))) {
            throw new ParamRequiredException("执行效果信息");
        }
        if (effects.stream()
                .anyMatch(effectView -> {
                    String executeDate = effectView.getExecuteDate();
                    String executeTimeStart = effectView.getExecuteTimeStart();
                    String executeTimeEnd = effectView.getExecuteTimeEnd();
                    boolean isAllNotBlank = StringUtils.isNotBlank(executeDate) && StringUtils.isNotBlank(executeTimeStart) && StringUtils.isNotBlank(executeTimeEnd);
                    boolean isAllBlank = StringUtils.isBlank(executeDate) && StringUtils.isBlank(executeTimeStart) && StringUtils.isBlank(executeTimeEnd);
                    return !(isAllNotBlank || isAllBlank);
                })) {
            throw new ParamRequiredException("执行效果的治疗时间要么全部都填,要么全部为空");
        }
        if (effects.stream()
                .anyMatch(effectView -> {
                    String executeDate = effectView.getExecuteDate();
                    if (StringUtils.isNotBlank(executeDate)) {
                        return Objects.isNull(DateUtils.parseLocalDate(executeDate, DateUtils.sFormatterDate));
                    }
                    String executeTimeStart = effectView.getExecuteTimeStart();
                    if (StringUtils.isNotBlank(executeTimeStart)) {
                        return Objects.isNull(DateUtils.parseLocalTime(executeTimeStart, DateUtils.sFormatterHourMin));
                    }
                    String executeTimeEnd = effectView.getExecuteTimeEnd();
                    if (StringUtils.isNotBlank(executeTimeEnd)) {
                        return Objects.isNull(DateUtils.parseLocalTime(executeTimeEnd, DateUtils.sFormatterHourMin));
                    }
                    return false;
                })) {
            throw new ParamRequiredException("执行效果的治疗时间格式输入不正确");
        }
        if (Objects.equals(needHomeCare, 1) && StringUtils.isBlank(homeCareAddress)) {
            throw new ParamRequiredException("上门地点");
        }
        if (StringUtils.length(homeCareAddress) > 120) {
            throw new ParamRequiredException("上门地点不能超过120个字");
        }
    }

    private void validateItems() throws ParamRequiredException, ChargeServiceException {
        if (CollectionUtils.isEmpty(items)) {
            throw new ParamRequiredException("执行记录子项集合");
        }
        if (items.stream().map(ExecuteRecordCreateItem::getChargeFormItemId).collect(Collectors.toSet()).size() != items.size()) {
            throw new ChargeServiceException(ChargeServiceError.CHARGE_FORM_ITEM_REPEAT);
        }
        for (ExecuteRecordCreateItem item : items) {
            item.validateParam();
        }
    }
}
