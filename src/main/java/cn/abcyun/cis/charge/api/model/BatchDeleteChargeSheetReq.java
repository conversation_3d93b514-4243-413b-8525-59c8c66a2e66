package cn.abcyun.cis.charge.api.model;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 批量删除收费单请求
 *
 * <AUTHOR>
 * @since 2024/7/19 17:44
 **/
@Data
public class BatchDeleteChargeSheetReq {

    /**
     * 门店ID
     */
    @NotEmpty(message = "门店ID")
    private String clinicId;

    /**
     * hisType
     *
     * @see cn.abcyun.cis.commons.model.Organ.HisType
     */
    private int hisType;

    /**
     * 收费单ID列表
     */
    @NotEmpty(message = "收费单ID列表不能为空")
    private List<String> chargeSheetIds;

    /**
     * 操作人ID
     */
    private String operatorId;
}
