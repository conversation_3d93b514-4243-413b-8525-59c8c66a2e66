package cn.abcyun.cis.charge.api.model;

import cn.abcyun.cis.charge.model.ChargeCooperationOrderItem;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.Objects;

@Data
@Accessors(chain = true)
public class ChargeCooperationOrderItemView {

    private String id;

    private String orderId;

    private String goodsId;

    private int goodsType;

    private int goodsSubType;

    private String name;

    private int isDismounting;

    private String sourceItemId;

    private String unit;

    private BigDecimal unitPrice;

    private BigDecimal fractionPrice;

    private BigDecimal unitCount;

    private BigDecimal sourceUnitPrice;

    private BigDecimal doseCount;

    private BigDecimal totalPrice;

    private int sort;

    private int composeType;

    private String composeParentFormItemId;

    private int pharmacyType;

    private int pharmacyNo;

    private BigDecimal sourceTotalPrice;


    public static ChargeCooperationOrderItemView of(ChargeCooperationOrderItem item) {

        if (Objects.isNull(item)) {
            return null;
        }

        ChargeCooperationOrderItemView itemView = new ChargeCooperationOrderItemView();
        BeanUtils.copyProperties(item, itemView);
        return itemView;
    }
}
