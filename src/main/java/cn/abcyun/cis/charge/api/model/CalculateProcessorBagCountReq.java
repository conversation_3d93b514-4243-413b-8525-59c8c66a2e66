package cn.abcyun.cis.charge.api.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("加工袋数计算")
public class CalculateProcessorBagCountReq {

    @ApiModelProperty("x日y剂")
    private String dailyDosage;

    @ApiModelProperty("x日y次")
    private String freq;

    @ApiModelProperty("剂数")
    private BigDecimal doseCount;

    @ApiModelProperty("1剂x袋  为空时将通过freq dailyDosage doseCount计算")
    private BigDecimal bagUnitCount;

    @ApiModelProperty("制法 饮片 1 颗粒 2")
    private int type;

    @ApiModelProperty("药房类型")
    private int pharmacyType;

    @ApiModelProperty("每次x袋")
    private String usageLevel;

}
