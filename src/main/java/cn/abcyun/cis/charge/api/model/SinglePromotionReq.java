package cn.abcyun.cis.charge.api.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class SinglePromotionReq {

    @ApiModelProperty("营销活动id")
    private String id;

    @ApiModelProperty("是否选中")
    private boolean checked;

    @ApiModelProperty("期望是否选中，如果有值，表示用户手动操作的，用户操作优先级永远最高")
    private Boolean expectedChecked;

    public boolean getChecked() {
        return checked;
    }

}
