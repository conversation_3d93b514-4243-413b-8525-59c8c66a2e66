package cn.abcyun.cis.charge.api.model;

import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ;
import cn.abcyun.cis.charge.base.Constants;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

@Data
public class MedicareLimitPriceSaveReq {

    @Valid
    @NotNull(message = "types不能为空")
    @Size(min = 0, max = 7, message = "types参数错误")
    private List<MedicareLimitPriceTypeItem> types;

    @Valid
    private List<MedicareLimitPriceProductItem> medicines;

    @Valid
    private List<MedicareLimitPriceProductItem> projects;

    @Valid
    private List<MedicareLimitPriceProductItem> feeGoods;

    public List<MedicareLimitPriceProductItem> getItems(int hisType) {

        List<MedicareLimitPriceProductItem> items = new ArrayList<>();

        if (!CollectionUtils.isEmpty(medicines)) {
            items.addAll(medicines);
            medicines.stream().forEach(item -> item.setType(Constants.LimitPriceProductType.MEDICINE));
        }

        if (hisType == Organ.HisType.CIS_HIS_TYPE_HOSPITAL) {
            if (!CollectionUtils.isEmpty(feeGoods)) {
                items.addAll(feeGoods);
                feeGoods.stream().forEach(item -> item.setType(Constants.LimitPriceProductType.FEE_TYPE));
            }
        } else {
            if (!CollectionUtils.isEmpty(projects)) {
                items.addAll(projects);
                projects.stream().forEach(item -> item.setType(Constants.LimitPriceProductType.PROJECT));
            }
        }

        return items;
    }
}
