package cn.abcyun.cis.charge.api.model;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.cis.core.validator.Values;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CreateChargeRuleProcessUsageReq {

    private String id;

    private ChargeRuleLadderInfoVo ladderInfo;

    /**
     * 制法子类别：0：统一
     */
    private int subType;

    /**
     * 类型：0：未知，1：固定收费，2：阶梯收费
     */
    @Values(value = {"1", "2"}, attr = "processUsage.ruleType", notNull = true)
    private Integer ruleType;

    /**
     * 固定收费金额
     */
    private BigDecimal price;

    /**
     * 是否有超额费用
     */
    @Values(value = {"0", "1"}, attr = "processUsage.overFulfilSwitch", notNull = true)
    private Integer overFulfilSwitch = 0;

    /**
     * 一剂多少袋以内免费
     */
    private Integer overFulfilUnitCount;

    /**
     * 每增加额外的count
     */
    private Integer overFulfilAdditionalCount;

    /**
     * 每个额外的count增加的金额
     */
    private BigDecimal overFulfilAdditionalPrice;

    /**
     * 项目编码
     */
    private String hisCode;

    /**
     * 项目名称
     */
    private String name;

    /**
     * 社保编码
     */
    private String shebaoCode;

    /**
     * 支付方式：0=医保支付（现金支付） 1=自费支付 2=不允许医保支付
     */
    private Integer shebaoPayMode;

    /**
     * 费用类型id
     */
    private Long feeTypeId;

    /**
     * 首页费目类id
     */
    private Integer feeCategoryId;

    public Long getFeeTypeId() {
        return feeTypeId == null ? GoodsConst.FeeTypeId.FEE_TYPE_ID_MEDICINE_SERVICE : feeTypeId;
    }
}
