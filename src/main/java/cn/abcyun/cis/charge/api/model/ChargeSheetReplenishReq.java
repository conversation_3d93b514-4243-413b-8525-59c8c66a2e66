package cn.abcyun.cis.charge.api.model;

import cn.abcyun.cis.core.validator.Values;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class ChargeSheetReplenishReq {

    @NotEmpty(message = "clinicId不能为空")
    private String clinicId;

    private ChargeDeliveryReq deliveryInfo;

    @Values(value = {"0", "1"}, attr = "配送方式", notNull = true)
    private Integer deliveryType;

    private int isDecoction;

    private Integer usageType;

    private Integer usageSubType;

    private String patientId;

    private String operatorId;

//    private List<ChargeFormReq> chargeForms;
//
//    @Data
//    public static class ChargeFormReq{
//        private String id;
//
//        private ChargeAirPharmacyLogisticsReq deliveryInfo;
//    }

    /**
     * 目前端上只传了chargeformId，和快递develryInfo，其他字段都没传
     * */
    private List<ChargeFormReq> chargeForms;


//    private List<ProcessInfoReq> processInfos;
}
