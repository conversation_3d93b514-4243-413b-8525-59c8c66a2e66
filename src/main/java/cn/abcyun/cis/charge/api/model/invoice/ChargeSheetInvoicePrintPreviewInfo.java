package cn.abcyun.cis.charge.api.model.invoice;

import cn.abcyun.cis.charge.api.model.DigitalInvoicePrintPreview;
import cn.abcyun.cis.charge.api.model.MedicalDigitalInvoicePrintView;
import cn.abcyun.cis.charge.api.model.RegistrationInvoicePrintView;
import cn.abcyun.cis.charge.service.dto.print.ChargeSheetPrintView;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 发票预览信息
 */
@Accessors(chain = true)
@Data
public class ChargeSheetInvoicePrintPreviewInfo {
    /**
     * 杭州挂号普通发票预览数据
     */
    private RegistrationInvoicePrintView hzRegistrationNormalInvoicePrintView;
    /**
     * 非杭州挂号（普通收费单、非杭州挂号单）普通发票预览数据
     */
    private ChargeSheetPrintView normalInvoicePrintView;

    /**
     * 增值税电子发票预览数据
     * {@link DigitalInvoicePrintPreview}
     */
    private DigitalInvoicePrintPreview digitalInvoicePrintView;

    /**
     * 医疗电子票据预览数据
     */
    private MedicalDigitalInvoicePrintView medicalDigitalInvoicePrintView;
}
