package cn.abcyun.cis.charge.api.model;

import cn.abcyun.bis.rpc.sdk.cis.model.clinic.EmployeeBasic;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024-01-25 15:14
 * @Description
 */

@ApiModel(value = "SalesOrderAbstractView", description = "")
@Data
public class SalesOrderAbstractView {

    @ApiModelProperty(value = "单据id")
    private String id;

    @JsonIgnore
    private String patientOrderId;

    @ApiModelProperty(value = "销售单号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String orderNo;

    @ApiModelProperty(value = "状态")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer status;

    @ApiModelProperty(value = "应收金额")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal receivableFee;

    @ApiModelProperty(value = "实收金额")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal receivedFee;

    /**
     * 毛利率
     */
    @ApiModelProperty(value = "毛利率", example = "0.2")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal grossProfitRate;

    /**
     * 毛利金额
     */
    @ApiModelProperty(value = "毛利金额", example = "2")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal grossProfit;

    @ApiModelProperty(value = "顾客信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private SalesOrderCustomerInfo customerInfo;

    @ApiModelProperty(value = "销售员信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private EmployeeBasic seller;

    @ApiModelProperty(value = "收费员信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private EmployeeBasic cashier;

    /**
     * 收银员列表（多次收费可能会有多条，从 ChargeTransaction 上获取的）
     */
    @ApiModelProperty(value = "收银员信息列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<EmployeeBasic> cashiers;

    @ApiModelProperty(value = "药剂师信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private EmployeeBasic pharmacist;

//    @ApiModelProperty(value = "商品列表")
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private List<GoodsItemAbstract> goodsList;

    @ApiModelProperty(value = "商品摘要")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String goodsAbstract;

    @ApiModelProperty(value = "创建时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Instant created;

    @ApiModelProperty(value = "收费时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Instant orderByDate;

    @ApiModelProperty(value = "是否已关闭，null/0：否；1：是")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer isClosed;

    @ApiModelProperty(value = "收费单类型")
    private int type;

    /**
     * 合作诊所处方来源诊所id
     */
    @ApiModelProperty(value = "合作诊所处方来源诊所id")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String coSourceClinicId;

    /**
     * 合作诊所处方来源诊所名称
     */
    @ApiModelProperty(value = "合作诊所处方来源诊所名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String coSourceClinicName;

}
