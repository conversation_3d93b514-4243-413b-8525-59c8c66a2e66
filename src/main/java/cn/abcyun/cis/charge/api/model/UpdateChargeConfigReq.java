package cn.abcyun.cis.charge.api.model;

import cn.abcyun.cis.charge.base.ChargeServiceError;
import cn.abcyun.cis.charge.base.ChargeServiceException;
import cn.abcyun.cis.commons.exception.ParamNotValidException;
import cn.abcyun.cis.core.validator.Values;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 */
@Data
public class UpdateChargeConfigReq {

    @Values(value = {"0", "1", "2", "3", "4", "5", "6"}, attr = "零头处理方式", notNull = true)
    private Integer roundingType;

    @Values(value = {"0", "1"}, attr = "收费员整单议价", notNull = true)
    private Integer bargainSwitch;

    @Values(value = {"0", "1"}, attr = "收费员单项议价", notNull = true)
    private Integer singleBargainSwitch;

    @Values(value = {"0", "1"}, attr = "医生整单议价", notNull = true)
    private Integer doctorBargainSwitch;

    @Values(value = {"0", "1"}, attr = "医生单项议价", notNull = true)
    private Integer doctorSingleBargainSwitch;

    @Values(value = {"0", "1"}, attr = "挂号处挂号费议价")
    private Integer doctorRegisteredBargainSwitch;

    @Values(value = {"0", "1"}, attr = "挂号处挂号费议价")
    private Integer reservationRegisteredBargainSwitch;

    @Values(value = {"100", "0", "1"}, attr = "门诊结束自动发送支付订单")
    @Deprecated
    private Integer autoSendOrderInfoSwitch;

    @Values(value = {"0", "1"}, attr = "执行站开单人自主议价")
    private Integer nurseBargainSwitch = 1;

    @Values(value = {"0", "1"}, attr = "欠费收费")
    private Integer oweSheetSwitch = 0;

    @Values(value = {"0", "1"}, attr = "医生是否可推送订单")
    private Integer doctorCanPushChargeSheetSwitch = 1;

    /**
     *
     */
    private int inspectBargainSwitch = 1;

    /**
     *
     */
    @Values(value = {"0", "1"}, attr = "", notNull = true)
    private Integer chargeNeedAstPassSwitch;

    @Valid
    private UpdatePayModeReq payMode;

    /**
     *
     */
    @Values(value = {"0", "1"}, attr = "")
    private int autoClosedSwitch;

    /**
     *
     */
    private BigDecimal autoClosedTime;

    /**
     * 自动关单开关V2 0关闭  1开启（offline-task实现）
     */
    @Values(value = {"0", "1"}, attr = "")
    private int autoClosedSwitchV2;

    /**
     * 自动关单时间V2 （offline-task实现）
     * <p>
     * [{"label": "当天晚上", "value": 0}, {"label": "次日晚上", "value": 1}, {"label": "第3天晚上", "value": 2}]
     */
    private int autoClosedTimeV2;

    /**
     * ; 0：，1：，2：
     */
    private List<Integer> sendOrderInfoModes;

    @Values(value = {"0", "1"}, attr = "")
    private int directSaleEnable = 1;

    @ApiModelProperty(value = "0= 1=")
    private int usedDiscountNotAllowShebaoSwitch = 0;

    /**
     * 0 1
     */
    @Values(value = {"0", "1"}, attr = "")
    @ApiModelProperty(value = "0 1")
    private int openPromotionCardUseMemberSwitch;

    /**
     * 0 1
     */
    @Values(value = {"0", "1"}, attr = "")
    @ApiModelProperty(value = "0 1")
    private int rechargePromotionCardUseMemberSwitch;

    @Values(value = {"0", "1", "2"}, attr = "0= 1= 2=")
    @ApiModelProperty(value = "0= 1= 2=")
    private int refundRestriction;

    /**
     * 凑整抹零指定支付方式，0：全部支付方式 1：指定支付方式
     */
    @ApiModelProperty(value = "凑整抹零指定支付方式，0：全部支付方式 1：指定支付方式")
    @Values(value = {"0", "1"}, attr = "")
    private int oddFeeDealType;

    /**
     * 凑整抹零指定支付方式列表
     */
    @ApiModelProperty(value = "凑整抹零指定支付方式列表")
    private List<Long> oddFeeDealPayModes;


    @Values(value = {"0", "1"}, attr = "退费审核开关 0=关闭 1=开启")
    @ApiModelProperty(value = "退费审核开关 0=关闭 1=开启")
    private int refundCheck;

    @ApiModelProperty(value = "退费审核人员Ids")
    private List<String> refundCheckEmployeeIds;

    @Values(value = {"0", "1"}, attr = "整单收费退费开关")
    @ApiModelProperty(value = "整单收费退费开关(0:关闭 1:开启)")
    private int wholeSheetOperateEnable;

    public Integer getDoctorCanPushChargeSheetSwitch() {
        return doctorCanPushChargeSheetSwitch != null ? doctorCanPushChargeSheetSwitch : 1;
    }

    public void preCheck() {
        if (oddFeeDealPayModes != null && !oddFeeDealPayModes.isEmpty() && payMode != null) {
            List<Long> validPayModes = new ArrayList<>();
            if (payMode.getOptionalPayModes() != null) {
                validPayModes.addAll(payMode.getOptionalPayModes().stream()
                        .filter(mode -> mode.getIsEnable() != null && mode.getIsEnable() == 1)
                        .map(UpdatePayModeReq.PayModeReq::getPayModeId)
                        .collect(Collectors.toList()));
            }
            if (payMode.getCustomizedPayModes() != null) {
                validPayModes.addAll(payMode.getCustomizedPayModes().stream()
                        .filter(mode -> mode.getIsEnable() != null && mode.getIsEnable() == 1)
                        .map(UpdatePayModeReq.PayModeReq::getPayModeId)
                        .collect(Collectors.toList()));
            }
            for (Long payMode : oddFeeDealPayModes) {
                if (!validPayModes.contains(payMode)) {
                    throw new ChargeServiceException(ChargeServiceError.PAY_MODE_NOT_SUPPORT);
                }
            }
        }
    }

    public void validate() {
        if (refundCheck == 1) {
            if (CollectionUtils.isEmpty(refundCheckEmployeeIds)) {
                throw new ParamNotValidException("退费审核人员不能为空");
            }
        }
        if (CollectionUtils.isNotEmpty(refundCheckEmployeeIds) && refundCheckEmployeeIds.stream().anyMatch(StringUtils::isBlank)) {
            throw new ParamNotValidException("退费审核人员Id不能为空");
        }
    }
}
