package cn.abcyun.cis.charge.api.model;

import cn.abcyun.cis.charge.util.MathUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

@Data
@Accessors(chain = true)
public class PatientOweAmountInfo {

    private String patientId;

    @ApiModelProperty("欠费总金额")
    private BigDecimal totalOweAmount;

    /**
     * 每个门店的欠费明细，当请求参数没有传clinicId时，会返回明细，如果传了clinicId，则list中只有一个对象，就是指定门店的数据
     */
    private List<ClinicPatientOweAmountInfo> clinicPatientOweAmountInfos;

    public BigDecimal getTotalOweAmount() {
        return MathUtils.wrapBigDecimalOrZero(totalOweAmount);
    }

    public static PatientOweAmountInfo defaultPatientOweAmountInfo(String patientId, String clinicId) {
        PatientOweAmountInfo patientOweAmountInfo = new PatientOweAmountInfo();
        patientOweAmountInfo.setPatientId(patientId)
                .setTotalOweAmount(BigDecimal.ZERO);

        if (StringUtils.isNotEmpty(clinicId)) {
            ClinicPatientOweAmountInfo clinicPatientOweAmountInfo = new ClinicPatientOweAmountInfo();
            clinicPatientOweAmountInfo.setClinicId(clinicId)
                    .setTotalOweAmount(BigDecimal.ZERO);
            patientOweAmountInfo.setClinicPatientOweAmountInfos(Arrays.asList(clinicPatientOweAmountInfo));
        }

        return patientOweAmountInfo;
    }

    @Data
    @Accessors(chain = true)
    public static class ClinicPatientOweAmountInfo {

        private String clinicId;

        private BigDecimal totalOweAmount;

        public BigDecimal getTotalOweAmount() {
            return MathUtils.wrapBigDecimalOrZero(totalOweAmount);
        }
    }

}
