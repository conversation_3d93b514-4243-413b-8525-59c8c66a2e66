package cn.abcyun.cis.charge.api.model;

import cn.abcyun.bis.rpc.sdk.cis.model.outpatient.ExtendDiagnosisInfo;
import cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientInfo;
import cn.abcyun.cis.charge.factory.ChargeCooperationOrderFactory;
import cn.abcyun.cis.charge.model.ChargeCooperationOrder;
import cn.abcyun.cis.charge.model.ChargeCooperationOrderAbstractInfo;
import cn.abcyun.cis.charge.util.UsageInfoUtil;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Data
@Accessors(chain = true)
public class ChargeCooperationOrderView {

    private String id;

    private String chainId;

    private String clinicId;

    private String sourcePatientId;

    private PatientInfo sourcePatientInfo;

    private String sourceChainId;

    private String sourceClinicId;

    private String sourceFormId;

    private String sourceSheetId;

    private String sourcePatientOrderId;

    private String sourceClinicName;

    private String sourceDoctorId;

    private String sourceDoctorName;

    /**
     * 来源类型{@link ChargeCooperationOrder.SourceType}
     */
    private int sourceType;

    private String relateChargeSheetId;

    /**
     * 提单状态，0：未提取，10已提取
     * {@link ChargeCooperationOrder.ExtractStatus}
     */
    private int extractStatus;

    private String extractDataSignature;

    /**
     * 收费状态（0: 未收费，10：部分收费，20：已收费，30：部分退费，40：已退费, 50:已关闭
     */
    private int status;

    private List<ExtendDiagnosisInfo> extendDiagnosisInfos;

    private UsageInfoUtil.DbUsageInfo usageInfo;

    private List<ChargeCooperationOrderItemView> orderItems;

    private ChargeCooperationOrderAbstractInfo abstractInfo;

    private Instant created;

    /**
     * 提单数据是否发生变化
     */
    private int isExtractDataChanged;


    public static ChargeCooperationOrderView of(ChargeCooperationOrder chargeCooperationOrder) {

        if (Objects.isNull(chargeCooperationOrder)) {
            return null;
        }

        ChargeCooperationOrderView chargeCooperationOrderView = new ChargeCooperationOrderView();
        BeanUtils.copyProperties(chargeCooperationOrder, chargeCooperationOrderView);

        chargeCooperationOrderView.setOrderItems(Optional.ofNullable(chargeCooperationOrder.getOrderItems())
                .orElse(new ArrayList<>())
                .stream()
                .map(ChargeCooperationOrderItemView::of)
                .collect(Collectors.toList())
        );
        if (chargeCooperationOrder.getExtractStatus() == ChargeCooperationOrder.ExtractStatus.EXTRACTED) {
            boolean extractDataChanged = Objects.equals(chargeCooperationOrder.getExtractDataSignature(), ChargeCooperationOrderFactory.generateCooperationOrderDataSignature(chargeCooperationOrder));
            chargeCooperationOrderView.setIsExtractDataChanged(extractDataChanged ? 0 : 1);
        }
        return chargeCooperationOrderView;
    }
}
