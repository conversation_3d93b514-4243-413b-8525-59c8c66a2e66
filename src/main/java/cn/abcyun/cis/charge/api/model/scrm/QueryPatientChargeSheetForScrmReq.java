package cn.abcyun.cis.charge.api.model.scrm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * scrm 查询患者收费单请求
 *
 * <AUTHOR>
 * @since 2024/7/19 17:48
 **/
@ApiModel(value = "QueryPatientChargeSheetForScrmReq", description = "scrm 查询患者收费单请求")
@Data
public class QueryPatientChargeSheetForScrmReq {

    /**
     * 连锁ID
     */
    @ApiModelProperty("连锁ID")
    @NotBlank(message = "连锁ID不能为空")
    private String chainId;

    /**
     * 门店列表
     */
    @ApiModelProperty("门店列表")
    private List<String> clinicIds;

    /**
     * 患者ID
     */
    @ApiModelProperty(value = "患者ID", required = true)
    @NotBlank(message = "患者ID不能为空")
    private String patientId;

    /**
     * 页偏移
     */
    @ApiModelProperty("页偏移")
    private int offset;

    /**
     * 页大小
     */
    @ApiModelProperty("页大小")
    private int limit;

}
