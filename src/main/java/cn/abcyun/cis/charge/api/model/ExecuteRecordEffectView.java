package cn.abcyun.cis.charge.api.model;

import cn.abcyun.cis.charge.model.ChargeExecuteRecordEffect;
import cn.abcyun.cis.commons.util.DateUtils;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 执行记录效果
 *
 * <AUTHOR>
 * @version ExecuteRecordEffectView.java, 2020/11/30 下午5:03
 */
@Data
@Accessors(chain = true)
public class ExecuteRecordEffectView {
    /**
     * 主键id
     */
    private String                                  id;
    /**
     * 执行记录id
     */
    private String                                  executeRecordId;
    /**
     * 治疗方法
     */
    private String                                  treatmentMethod;
    /**
     * 治疗部位
     */
    private String                                  treatmentSite;
    /**
     * 治疗反应
     */
    private String                                  treatmentResponse;
    /**
     * 病因病机
     */
    private String                                  etiologyPathogenesis;
    /**
     * 治疗结果
     */
    private String                                  treatmentResult;
    /**
     * 执行时间(yyyy-MM-dd)
     */
    private String                                    executeDate;
    /**
     * 执行时间(LocalTime范围开始)
     */
    private String                                    executeTimeStart;
    /**
     * 执行时间(LocalTime范围结束)
     */
    private String                                    executeTimeEnd;
    /**
     * 附件
     */
    private List<ExecuteRecordEffectAttachmentView> attachments;

    public static ExecuteRecordEffectView ofChargeExecuteRecordEffect(ChargeExecuteRecordEffect effect) {
        ExecuteRecordEffectView view = new ExecuteRecordEffectView()
                .setId(String.valueOf(effect.getId()))
                .setExecuteRecordId(effect.getExecuteRecordId())
                .setTreatmentMethod(effect.getTreatmentMethod())
                .setTreatmentSite(effect.getTreatmentSite())
                .setTreatmentResponse(effect.getTreatmentResponse())
                .setEtiologyPathogenesis(effect.getEtiologyPathogenesis())
                .setTreatmentResult(effect.getTreatmentResult())
                .setAttachments(
                        Optional.ofNullable(effect.getAttachments()).orElse(Lists.newArrayList())
                                .stream()
                                .map(ExecuteRecordEffectAttachmentView::ofExecuteEffectAttachment)
                                .collect(Collectors.toList())
                );
        LocalDate executeDate = effect.getExecuteDate();
        if (Objects.nonNull(executeDate)) {
            view.setExecuteDate(DateUtils.formatLocalDate(executeDate, DateUtils.sFormatterDate));
        }
        LocalTime executeTimeStart = effect.getExecuteTimeStart();
        if (Objects.nonNull(executeTimeStart)) {
            view.setExecuteTimeStart(executeTimeStart.format(DateUtils.sFormatterHourMin));
        }
        LocalTime executeTimeEnd = effect.getExecuteTimeEnd();
        if (Objects.nonNull(executeTimeEnd)) {
            view.setExecuteTimeEnd(executeTimeEnd.format(DateUtils.sFormatterHourMin));
        }
        return view;
    }
}
