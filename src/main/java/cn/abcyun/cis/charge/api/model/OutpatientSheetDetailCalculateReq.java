package cn.abcyun.cis.charge.api.model;

import cn.abcyun.cis.commons.rpc.outpatient.OutpatientSheet;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class OutpatientSheetDetailCalculateReq {

    @NotNull(message = "outpatientSheet不能为空")
    private OutpatientSheet outpatientSheet;

    /**
     * 挂号费信息
     */
    private RegistrationInfoReq registrationInfo;

    /**
     * 是否需要执行数量
     */
    private int withExecutedCount;

    /**
     * 是否需要快递和加工信息
     */
    private int withDeliveryAndProcess;

    /**
     * 是否需要所有收费项
     */
    private int withAllItems;

    @Data
    @Accessors(chain = true)
    public static class RegistrationInfoReq {

        private BigDecimal unitPrice;

        private String doctorId;

        private String departmentId;

    }

}
