package cn.abcyun.cis.charge.api.model;

import cn.abcyun.bis.rpc.sdk.cis.model.search.CdssQLSearchResultItem;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024-01-27 12:18
 * @Description
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SalesCdssQLSearchResultItem extends CdssQLSearchResultItem {
    private List<String> goodsId;
    private BigDecimal receivableFee;
    private BigDecimal receivedFee;
    private BigDecimal refundFee;
    private Integer invoiceStatus;
}
