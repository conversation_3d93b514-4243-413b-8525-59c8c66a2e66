package cn.abcyun.cis.charge.api.model;

import cn.abcyun.cis.commons.rpc.charge.ChargeDeliveryInfoView;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

@Data
public class PrescriptionFormPrintView {

    private String id;

    private int type;
    private String specification = "";
    private int doseCount;
    private String dailyDosage = "";
    private String usage = "";
    private String freq = "";
    private String requirement = "";
    private String usageLevel = "";
    private int sort;

    @JsonProperty("cMSpec")
    private String cMSpec;

    private boolean isDecoction;

    // 一剂的袋数
    private BigDecimal processBagUnitCount;
    private String processRemark;
    //一共需要加工的袋数
    private BigDecimal totalProcessCount;

    @JsonIgnore
    private String chargeSheetId;

    private Integer usageType;
    private Integer usageSubType;
    private BigDecimal processPrice;
    private BigDecimal ingredientPrice;
    private String usageDays;
    private String auditBy;
    private String auditName;

    private String contactMobile;

    private BigDecimal totalPrice;

    private ChargeDeliveryInfoView deliveryInfo;

    private String processUsageInfo;

    private int chargeStatus;
    private Instant created;
    private int pharmacyType;
    private int pharmacyNo;
    private String pharmacyName;
    private int psychotropicNarcoticType;
    private String chargedByName;
    private String chargedByHandSign;
    /**
     * 发药单 调配人 打印只要人名字就够了
     * */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String compoundName;

    @JsonIgnore
    private BigDecimal refundedTotalPrice;

    private List<PrescriptionFormItemPrintView> prescriptionFormItems;

    public void setIsDecoction(boolean isDecoction) {
        this.isDecoction = isDecoction;
    }

    public boolean getIsDecoction() {
        return isDecoction;
    }
}
