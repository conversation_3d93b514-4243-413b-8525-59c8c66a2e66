package cn.abcyun.cis.charge.api.model;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
public class OutpatientFormItemCalculateReq {
    @NotEmpty(message = "item.id不能为空")
    private String id;
    private String name;
    private String unit;
    private BigDecimal unitCount;
    private BigDecimal doseCount;
    private int productType;
    private int productSubType;
    /**
     * 当前单价
     */
    @NotNull(message = "单价不能为空")
    @DecimalMin(value = "0", message = "单价不能小于{value}")
    private BigDecimal unitPrice;

    /**
     * {@link cn.abcyun.cis.charge.base.Constants.SourceItemType}
     */
    private int sourceItemType;
    /**
     * 当前总价
     */
    private BigDecimal totalPrice;
    private BigDecimal fractionPrice;

    @NotNull(message = "原价不能为空")
    @DecimalMin(value = "0", message = "原价不能小于{value}")
    private BigDecimal sourceUnitPrice;

    private BigDecimal oldSourceUnitPrice;

    /**
     * form上平摊下来的金额
     */
    private BigDecimal formFlatPrice;

    /**
     * sheet上平摊下来的金额
     */
    private BigDecimal sheetFlatPrice;

    private int isUnitPriceChanged;

    private int isTotalPriceChanged;

    private int feeComposeType;

    private Long feeTypeId;

    /**
     * 套餐类型: 0：不是套餐，1：套餐母项，2：套餐子项
     */
    private int composeType = 0;

    private int goodsFeeType = 0;

    /**
     * 药房号：默认为0
     */
    private int pharmacyNo;

    /**
     * 药房类型：0：实体药房，1：空中药房，2：虚拟药房，
     */
    private int pharmacyType;

    @Valid
    private List<OutpatientFormItemCalculateReq> composeChildren;
}
