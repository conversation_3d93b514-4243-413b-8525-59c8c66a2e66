package cn.abcyun.cis.charge.api.model;

import cn.abcyun.bis.rpc.sdk.cis.model.common.BitFlagUtils;
import cn.abcyun.cis.charge.model.ChargeSheetAdditional;
import cn.abcyun.cis.commons.exception.ParamNotValidException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.List;

@Data
public class CalculateChargeSheetReq {
    public static final int PAY_TYPE_CHARGE = 0;
    public static final int PAY_TYPE_REFUND = 1;
    public static final int PAY_TYPE_RENEW = 2;

    private String chargeSheetId;

    private List<ChargeFormReq> chargeForms;

    private List<PromotionReq> promotions;

    private List<CouponPromotionReq> couponPromotions;

    private List<GiftRulePromotionReq> giftRulePromotions;

    @ApiModelProperty(value = "卡项列表列表")
    private List<PatientCardPromotionReq> patientCardPromotions;

    @ApiModelProperty(value = "积分抵扣列表")
    private List<PatientPointDeductProductPromotionReq> patientPointDeductProductPromotions;

    @ApiModelProperty(value = "核销列表")
    private List<MallVerificationReq> mallVerifications;

    private PatientPointsInfoReq patientPointsInfo;

    private String memberId;

    /**
     * 使用会员标记：0：匹配默认会员id，10：使用指定会员，20：不使用会员
     * {@link cn.abcyun.cis.charge.model.ChargeSheetAdditional.UseMemberFlag}
     */
    private int useMemberFlag;

    private BigDecimal adjustmentFee;

    private int payType;

    private int payMode;
    //端上没传，需要自己转化
    @Deprecated
    private int deliveryType;

    private ChargeDeliveryReq deliveryInfo;

    private String patientId;

    private int isDecoction;

    private DecoctionInfoReq decoctionInfo;

    private List<ProcessInfoReq> processInfos;

    /**
     * 期望议价值，只有pc和app上才传
     */
    private BigDecimal expectedAdjustmentFee;

    /**
     * 期望的系统议价值，只有pc和app上才传
     */
    private BigDecimal expectedOddFee;

    //表示系统四舍五入的类型，新的请求都要传这个字段，兼容app的算费请求， roundingType == null 时，adjustmentFee包含了系统议价
    private Integer roundingType;

    /**
     * 是否需要卡项余额信息
     */
    @ApiModelProperty(value = "是否需要卡项余额信息")
    private int isNeedPatientCardBalance;

    /**
     * 锁库任务id
     */
    private String goodsLockingTaskId;

    /**
     * 批量提单时选中的收费单id列表
     */
    private List<String> batchExtractOriginalSheetIds;

    /**
     * 草稿算费类型
     */
    private Integer retailType;

    public void preHandle() {

        if (BitFlagUtils.checkFlagOn(retailType, ChargeSheetAdditional.RetailType.RETAIL_BATCH_EXTRACT) && StringUtils.isEmpty(chargeSheetId)) {
            if (CollectionUtils.isEmpty(batchExtractOriginalSheetIds)) {
                throw new ParamNotValidException("批量提单信息错误，请清空页面重新批量提单");
            }
        }

    }
}
