package cn.abcyun.cis.charge.api.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Data
public class WeClinicChargeHistoryListRsp {
    private List<WeClinicChargeHistoryItem> rows;
    private int offset;
    private int limit;
    private int total;

    @Data
    public static class WeClinicChargeHistoryItem {
        private String id;
        private int type;
        private String patientOrderId;
        private String patientName;
        private String doctorName;
        private String clinicName;
        private String abstractInfo;
        private Instant chargedTime;
        private String diagnosis;
        private String chiefComplaint;
        @JsonIgnore
        private String patientId;

        private String clinicId;

        private String doctorId;

        private Instant diagnosedDate;

    }
}
