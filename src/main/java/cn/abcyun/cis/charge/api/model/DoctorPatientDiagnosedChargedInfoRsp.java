package cn.abcyun.cis.charge.api.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Data
@Accessors(chain = true)
public class DoctorPatientDiagnosedChargedInfoRsp {

    private String chainId;

    private String doctorId;
    
    private long chargeTotalCount;

    private List<PatientDiagnosedChargedInfo> patientDiagnosedChargedInfos;


    public long getChargeTotalCount() {
        return Optional.ofNullable(patientDiagnosedChargedInfos)
                .orElse(new ArrayList<>())
                .stream()
                .map(PatientDiagnosedChargedInfo::getChargedCount)
                .reduce(0L, Long::sum);
    }

    @Data
    @Accessors(chain = true)
    public static class PatientDiagnosedChargedInfo {
        private String patientId;
        private long chargedCount;
    }

}
