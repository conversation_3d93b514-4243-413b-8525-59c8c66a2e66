package cn.abcyun.cis.charge.api.model;

import cn.abcyun.cis.charge.model.ChargeBusinessPrintRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/4/17 上午11:56
 * @description 收费业务打印记录视图
 */
@Getter
@Setter
@ApiModel(value = "收费业务打印记录视图")
public class ChargeBusinessPrintRecordView {

    @ApiModelProperty(value = "主键id")
    private String  id;

    @ApiModelProperty(value = "连锁id")
    private String  chainId;

    @ApiModelProperty(value = "门店id")
    private String  clinicId;

    @ApiModelProperty(value = "业务id")
    private String  businessId;

    @ApiModelProperty(value = "打印类型(0:收费小票 1:发药小票 2:医疗收据 3:医疗收费清单 4:处方 5:输液注射单 6:治疗单 7:检验条码 8:检验申请单 9:检查申请单 10:用药标签 11:患者标签)")
    private int businessType;

    @ApiModelProperty(value = "打印总次数")
    private int printCount;

    public static ChargeBusinessPrintRecordView of(ChargeBusinessPrintRecord record) {
        if (Objects.isNull(record)) {
            return null;
        }
        ChargeBusinessPrintRecordView view = new ChargeBusinessPrintRecordView();
        view.setId(record.getId());
        view.setChainId(record.getChainId());
        view.setClinicId(record.getClinicId());
        view.setBusinessId(record.getBusinessId());
        view.setBusinessType(record.getBusinessType());
        view.setPrintCount(record.getPrintCount());
        return view;
    }
}
