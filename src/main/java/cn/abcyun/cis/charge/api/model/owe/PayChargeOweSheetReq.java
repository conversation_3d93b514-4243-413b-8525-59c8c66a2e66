package cn.abcyun.cis.charge.api.model.owe;

import cn.abcyun.cis.charge.base.pay.CombinedPayItem;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
public class PayChargeOweSheetReq {

    @NotNull(message = "payItem不能为空")
    private CombinedPayItem payItem;

    @NotEmpty(message = "oweSheetItems不能为空")
    @Valid
    private List<CombineOweSheetItem> oweSheetItems;

    @NotEmpty(message = "患者id不能为空")
    private String patientId;

    /**
     * 会员id
     */
    private String memberId;

    /**
     * 收费备注
     */
    private String chargeComment;

    @Data
    @Accessors(chain = true)
    public static class CombineOweSheetItem {

        @NotEmpty(message = "oweSheetId不能为空")
        private String oweSheetId;

        @NotNull(message = "金额不能为空")
        @DecimalMin(value = "0", inclusive = false, message = "金额不能小于{value}")
        private BigDecimal amount;

        @NotNull(message = "应收不能为空")
        @DecimalMin(value = "0", inclusive = false, message = "应收不能小于{value}")
        private BigDecimal receivableFee;

        @JsonIgnore
        private String patientOrderId;
    }
}
