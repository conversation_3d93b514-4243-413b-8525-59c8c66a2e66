package cn.abcyun.cis.charge.api.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

@Data
@Accessors
public class ChargeSheetSimpleListRsp {
    private List<ChargeSheetSimple> chargeSheets;

    @Data
    @Accessors(chain = true)
    public static class ChargeSheetSimple{
        private String id;
        private String patientOrderId;
        private Instant orderByDate;
        private String chainId;
        private String clinicId;

        private String doctorId;
        private String patientId;

        private BigDecimal receivableFee;
        private int status;
        private int executeStatus;
        private int outpatientStatus;
        private int isOnline;
        private String departmentId;

        private BigDecimal refundFee;           //已退

        private BigDecimal receivedFee;         //已收

        /**
         * 收费时间
         */
        private Instant chargedTime;

        /**
         * 就诊时间
         */
        private Instant diagnosedDate;

        /**
         * 收费单类型
         */
        private int type;

        /**
         * 数据标识
         */
        private Integer dataFlag;

        /**
         * 收费员id
         */
        private String chargedBy;

        /**
         * 开单人ID
         */
        private String sellerId;

        /**
         * 创建时间
         */
        private Instant created;
        /**
         * 销售单号
         */
        private String sellNo;

        private int owedStatus;
        /**
         * 是否草稿
         */
        private int isDraft;
    }
}
