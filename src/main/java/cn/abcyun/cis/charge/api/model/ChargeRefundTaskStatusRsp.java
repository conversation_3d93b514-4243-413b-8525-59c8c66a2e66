package cn.abcyun.cis.charge.api.model;

import cn.abcyun.cis.charge.model.ChargeRefundTask;
import cn.abcyun.cis.charge.model.ChargeRefundTaskItem;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Data
@Accessors(chain = true)
public class ChargeRefundTaskStatusRsp {

    private String id;

    private int status;

    /**
     * 是否支持再次发起退费
     */
    private int isCanRefundAgain;

    private String statusName;

    private List<ChargeRefundTaskItemStatusRsp> chargeRefundTaskItems;

    private String errorMessage;

    public String getStatusName() {
        switch (status) {
            case ChargeRefundTask.Status.REFUNDING:
            case ChargeRefundTask.Status.PART_REFUNDED_AND_NEXT_REFUNDING:
                statusName = "退费中";
                break;
            case ChargeRefundTask.Status.PART_REFUND_SUCCESS_PART_REFUND_FAIL:
                statusName = "部分退费成功";
                break;
            case ChargeRefundTask.Status.REFUND_SUCCESS:
                statusName = "退费成功";
                break;
            case ChargeRefundTask.Status.REFUND_FAIL:
                statusName = "退费失败";
                break;
            default:
                statusName = "";
        }

        return statusName;
    }

    @Data
    @Accessors(chain = true)
    public static class ChargeRefundTaskItemStatusRsp {
        private String id;

        private int status;

        /**
         * 是否可以再次发起退费
         */
        private int isCanRefundAgain;

        private String statusName;

        private String errorMessage;

        public String getStatusName() {
            switch (status) {
                case ChargeRefundTaskItem.Status.WAIT_REFUND:
                    statusName = "待退费";
                    break;
                case ChargeRefundTaskItem.Status.REFUNDING:
                    statusName = "退费中";
                    break;
                case ChargeRefundTaskItem.Status.REFUND_SUCCESS:
                    statusName = "退费成功";
                    break;
                case ChargeRefundTaskItem.Status.REFUND_FAIL:
                    statusName = "退费失败";
                    break;
                default:
                    statusName = "";
            }
            return statusName;
        }

        public static ChargeRefundTaskItemStatusRsp ofChargeRefundTaskItemStatusRsp(ChargeRefundTaskItem chargeRefundTaskItem) {
            if (Objects.isNull(chargeRefundTaskItem)) {
                return null;
            }

            return new ChargeRefundTaskItemStatusRsp()
                    .setId(chargeRefundTaskItem.getId())
                    .setStatus(chargeRefundTaskItem.getStatus())
                    .setErrorMessage(chargeRefundTaskItem.getErrorMessage());
        }
    }

    public static ChargeRefundTaskStatusRsp ofChargeRefundTaskStatusRsp(ChargeRefundTask chargeRefundTask) {

        if (Objects.isNull(chargeRefundTask)) {
            return null;
        }

        return new ChargeRefundTaskStatusRsp()
                .setId(chargeRefundTask.getId())
                .setStatus(chargeRefundTask.getStatus())
                .setErrorMessage(chargeRefundTask.getErrorMessage())
                .setChargeRefundTaskItems(
                        Optional.ofNullable(chargeRefundTask.getRefundTaskItems())
                                .orElse(new ArrayList<>())
                                .stream()
                                .map(ChargeRefundTaskItemStatusRsp::ofChargeRefundTaskItemStatusRsp)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList())
                );
    }

}
