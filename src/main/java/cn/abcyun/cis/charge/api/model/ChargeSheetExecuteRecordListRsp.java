package cn.abcyun.cis.charge.api.model;

import cn.abcyun.cis.charge.service.dto.ChargeExecuteRecordView;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.time.Instant;
import java.util.List;

/**
 * 收费单执行记录列表
 *
 * <AUTHOR>
 * @version ChargeSheetExecuteRecordListRsp.java, 2020/7/31 下午5:22
 */
@Data
@Accessors(chain = true)
public class ChargeSheetExecuteRecordListRsp {
    /**
     * 执行记录集合
     */
    private List<ChargeExecuteRecordView> executeRecords;
    /**
     * 收费单创建时间
     */
    private Instant                       chargeSheetCreated;
    /**
     * 收费单开单人id，非创建人Id; 门诊开单-医生，零售开单-销售员，执行站开单-选择的开单人
     */
    private String                        chargeSheetOwnerId;
    /**
     * 收费单开单人名称
     */
    private String                        chargeSheetOwnerName;
    /**
     * 收费单开单人id，非创建人Id; 门诊开单-医生，零售开单-销售员，执行站开单-选择的开单人
     * 对应的部门id
     */
    private String                        chargeSheetOwnerDepartmentId;
    /**
     * 收费单开单人对应的部门名称
     */
    private String                        chargeSheetOwnerDepartmentName;
    /**
     * 收费单创建人id
     */
    private String                        chargeSheetCreatedBy;
    /**
     * 收费单创建人名称
     */
    private String                        chargeSheetCreatedByName;
    /**
     * 收费单创建门店id，有跨店执行的执行记录时才返回
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String                        chargeSheetCreateClinicId;
    /**
     * 收费单创建门店名称，有跨店执行的执行记录时才返回
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String                        chargeSheetCreateClinicName;

    /**
     * 开单时间
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Instant                        billingTime;

    /**
     * 是否存在跨店执行门店；0：否；1：是
     */
    public int getExistCrossExecuteClinic() {
        if (CollectionUtils.isEmpty(executeRecords)) {
            return 0;
        }

        return executeRecords
                .stream()
                .anyMatch(executeRecordView -> !StringUtils.equals(executeRecordView.getClinicId(), executeRecordView.getExecuteClinicId()))
                ? 1
                : 0;
    }


}
