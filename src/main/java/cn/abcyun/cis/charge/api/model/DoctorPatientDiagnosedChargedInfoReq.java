package cn.abcyun.cis.charge.api.model;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@Accessors(chain = true)
public class DoctorPatientDiagnosedChargedInfoReq {

    @NotEmpty(message = "chainId不能为空")
    private String chainId;

    @NotEmpty(message = "doctorId不能为空")
    private String doctorId;

    @NotEmpty(message = "patientIds不能为空")
    private List<String> patientIds;

}
