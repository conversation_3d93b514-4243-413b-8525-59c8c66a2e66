package cn.abcyun.cis.charge.api.model;

import cn.abcyun.bis.rpc.sdk.cis.model.clinic.QueryEmployeeSnapReq;
import cn.abcyun.cis.charge.util.MathUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.Optional;

@Data
@Accessors(chain = true)
public class PatientChargeSheetAbstract {
    private String id;
    private String clinicName;
    private String chargedByName;
    private Instant chargedTime;
    private int status;
    private String statusName;
    private BigDecimal receivableFee;
    private BigDecimal receivedFee;
    /**
     * 排除掉欠费的实收金额
     */
    private BigDecimal receivedIgnoreOweFee;
    private BigDecimal refundedFee;
    private String abstractInfo;
    /**
     * 原价
     */
    private BigDecimal sourceTotalPrice;

    private BigDecimal totalOweAmount;
    /**
     * 如果是门诊单，取doctorId为开单人，否则取sellerId为开单人
     */
    @JsonIgnore
    private String sellerId;

    @ApiModelProperty("开单人姓名")
    private String sellerName;

    @JsonIgnore
    private String chargedBy;

    @JsonIgnore
    private String clinicId;

    private int owedStatus;

    private Instant diagnosedDate;

    @JsonIgnore
    public Instant wrapDiagnosedDate() {
        return Optional.ofNullable(diagnosedDate).orElse(QueryEmployeeSnapReq.LATEST_BUSINESS_TIME);
    }

    public BigDecimal getReceivedIgnoreOweFee() {
        return MathUtils.max(BigDecimal.ZERO, MathUtils.wrapBigDecimalSubtract(receivedFee, totalOweAmount));
    }
}
