package cn.abcyun.cis.charge.api.model;

import cn.abcyun.cis.commons.exception.ParamNotValidException;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;

@Data
@Accessors(chain = true)
public class QueryChargeSheetViewListReq {

    private String clinicId;

    /**
     * 收费单ids，不能超过10个
     */
    private List<String> chargeSheetIds;

    /**
     * 算费是否需要社保限价
     */
    private int isCalculateShebaoLimitPrice;


    public void checkParam() {
        if (StringUtils.isEmpty(clinicId)) {
            throw new ParamRequiredException("clinicId");
        }

        if (CollectionUtils.isEmpty(chargeSheetIds)) {
            throw new ParamRequiredException("chargeSheetIds");
        }

        if (chargeSheetIds.size() > 10) {
            throw new ParamNotValidException("不能超过10个收费单");
        }
    }

}
