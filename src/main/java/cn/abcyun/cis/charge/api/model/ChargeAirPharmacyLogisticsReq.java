package cn.abcyun.cis.charge.api.model;

import cn.abcyun.cis.charge.model.ChargeAirPharmacyLogistics;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.util.StringUtils;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
public class ChargeAirPharmacyLogisticsReq {

    private String id;

    @NotEmpty(message = "addressProvinceId不能为空")
    private String addressProvinceId;

    @NotEmpty(message = "addressProvinceName不能为空")
    private String addressProvinceName;

    @NotEmpty(message = "addressCityId不能为空")
    private String addressCityId;

    @NotEmpty(message = "addressCityName不能为空")
    private String addressCityName;

    @NotEmpty(message = "addressDistrictId不能为空")
    private String addressDistrictId;

    @NotEmpty(message = "addressDistrictName不能为空")
    private String addressDistrictName;

    private String addressDetail;

    @NotNull(message = "deliveryCompany不能为空")
    private LogisticsCompanyReq deliveryCompany;

    private String deliveryMobile;

    private String deliveryName;

    private String deliveryNo;

    private String deliveryOrderNo;

    private int deliveryPayType = ChargeAirPharmacyLogistics.DeliveryPayType.FROM_PAY_FOR;

    public String getDeliveryNo() {
        return StringUtils.isEmpty(deliveryOrderNo) ? deliveryNo : deliveryOrderNo;
    }

    @Data
    @Accessors(chain = true)
    public static class LogisticsCompanyReq {
        private String id;
        private String name;
    }

}
