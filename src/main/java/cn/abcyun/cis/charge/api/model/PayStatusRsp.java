package cn.abcyun.cis.charge.api.model;

import cn.abcyun.bis.rpc.sdk.cis.model.pay.PayStatus;
import cn.abcyun.cis.charge.model.ChargeTransaction;
import cn.abcyun.cis.charge.service.dto.ChargeTransactionView;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class PayStatusRsp {
    private int payStatus;
    private String message;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private BigDecimal needPayFee;
    private String chargeSheetId;
    private int isContainAirPharmacy;
    private List<String> airPharmacyOrderIds;
    private int isAirPharmacyCanPay;
    private String chargeTransactionId;
    /**
     * 是否能够完善收费单患者信息(0:不能 1:能)
     */
    private int canImprovePatientFlag;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<ChargeTransactionView> chargeTransactions = new ArrayList<>();

    public int getIsContainAirPharmacy() {
        return payStatus == PayStatus.SUCCESS && CollectionUtils.isNotEmpty(airPharmacyOrderIds) ? 1 : 0;
    }
}
