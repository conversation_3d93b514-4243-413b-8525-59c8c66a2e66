package cn.abcyun.cis.charge.api.model;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsItem;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.util.MathUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Data
@EqualsAndHashCode(callSuper = true)
public class LimitPriceGoodsItem extends GoodsItem {

    private BigDecimal limitPricePiecePrice;

    private BigDecimal limitPricePackagePrice;

    private BigDecimal composeLimitPricePiecePrice;

    private BigDecimal composeLimitPricePackagePrice;

    private List<LimitPriceGoodsItem> limitPriceChildren;

    //表示是否有医保限价规则
    private boolean isUseLimitPrice = false;

    //表示医保限价是否生效
    private boolean limitPriceEffect = false;

    public boolean isLimitPriceEffect() {
        if (getType() == Constants.ProductType.COMPOSE_PRODUCT && CollectionUtils.isNotEmpty(limitPriceChildren)) {
            return limitPriceChildren.stream().anyMatch(item -> item.isLimitPriceEffect());
        }
        return limitPriceEffect;
    }

    @Override
    public BigDecimal getPiecePrice(){
        if (getType() == Constants.ProductType.COMPOSE_PRODUCT && CollectionUtils.isNotEmpty(limitPriceChildren)) {
            //如果是套餐，看子项是否有限价，如果有限价，应该使用子项限价后的金额相加
            boolean childrenLimitPrice = limitPriceChildren.stream().anyMatch(item -> item.isUseLimitPrice());
            if (childrenLimitPrice) {
                return limitPriceChildren.stream()
                        .map(item -> item.getComposeUseDismounting() == GoodsConst.DismountingStatus.PACKAGE ?
                                MathUtils.calculateTotalPrice(item.getComposePackagePrice(), item.getComposePackageCount(), 2) :
                                MathUtils.calculateTotalPrice(item.getComposePiecePrice(), item.getComposePieceCount(), 2))
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            } else {
                return defaultPiecePrice();
            }
        }
        return defaultPiecePrice();
    }

    private BigDecimal defaultPiecePrice() {
        if (limitPricePiecePrice == null) {
            return super.getPiecePrice();
        }
        if (super.getPiecePrice() == null) {
            return limitPricePiecePrice;
        }


        if (isUseLimitPrice) {
            if (limitPricePiecePrice.compareTo(super.getPiecePrice()) > 0) {
                return super.getPiecePrice();
            }else {
                limitPriceEffect = true;
                return limitPricePiecePrice;
            }
        }else {
            return super.getPiecePrice();
        }
    }

    @Override
    public BigDecimal getPackagePrice(){
        if (getType() == Constants.ProductType.COMPOSE_PRODUCT && CollectionUtils.isNotEmpty(limitPriceChildren)) {
            //如果是套餐，看子项是否有限价，如果有限价，应该使用子项限价后的金额相加
            boolean childrenLimitPrice = limitPriceChildren.stream().anyMatch(item -> item.isUseLimitPrice());
            if (childrenLimitPrice) {
                return limitPriceChildren.stream()
                        .map(item -> item.getComposeUseDismounting() == GoodsConst.DismountingStatus.PACKAGE ?
                                MathUtils.calculateTotalPrice(item.getComposePackagePrice(), item.getComposePackageCount(), 2) :
                                MathUtils.calculateTotalPrice(item.getComposePiecePrice(), item.getComposePieceCount(), 2))
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            } else {
                return defaultPackagePrice();
            }
        }
        return defaultPackagePrice();
    }

    private BigDecimal defaultPackagePrice() {
        if (limitPricePackagePrice == null) {
            return super.getPackagePrice();
        }

        if (super.getPackagePrice() == null) {
            return limitPricePackagePrice;
        }

        if (isUseLimitPrice) {

            if(limitPricePackagePrice.compareTo(super.getPackagePrice()) > 0) {

                return super.getPackagePrice();
            }else {
                limitPriceEffect = true;
                return limitPricePackagePrice;
            }

        }else {
            return super.getPackagePrice();
        }
    }

    @Override
    public BigDecimal getComposePiecePrice() {

        if (composeLimitPricePiecePrice == null) {
            return super.getComposePiecePrice();
        }
        if (super.getComposePiecePrice() == null) {
            return composeLimitPricePiecePrice;
        }

        if (isUseLimitPrice) {

            if (composeLimitPricePiecePrice.compareTo(super.getComposePiecePrice()) > 0) {
                return super.getComposePiecePrice();
            }else {
                limitPriceEffect = true;
                return composeLimitPricePiecePrice;
            }
        }else {
            return super.getComposePiecePrice();
        }

    }

    @Override
    public BigDecimal getComposePackagePrice() {

        if (composeLimitPricePackagePrice == null) {
            return super.getComposePackagePrice();
        }
        if (super.getComposePackagePrice() == null) {
            return composeLimitPricePackagePrice;
        }

        if (isUseLimitPrice) {

            if (composeLimitPricePackagePrice.compareTo(super.getComposePackagePrice()) > 0) {
                return super.getComposePackagePrice();
            }else {
                limitPriceEffect = true;
                return composeLimitPricePackagePrice;
            }
        }else {
            return super.getComposePackagePrice();
        }
    }

}
