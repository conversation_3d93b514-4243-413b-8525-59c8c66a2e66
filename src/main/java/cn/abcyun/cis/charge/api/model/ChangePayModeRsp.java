package cn.abcyun.cis.charge.api.model;

import cn.abcyun.cis.charge.model.ChargeAction;
import cn.abcyun.cis.charge.service.dto.ChargeSheetSummary;
import cn.abcyun.cis.charge.service.dto.ChargeTransactionView;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ChangePayModeRsp {
    private List<ChargeTransactionView> chargeTransactions;
    private List<ChargeAction> chargeActions;
    private List<Integer> payModes;
    private List<ChargeSheetSummary.PayModeView> payModeViews = new ArrayList<>();
    private List<ChargeChangePayModeRecordView> changePayModeRecords = new ArrayList<>();
}
