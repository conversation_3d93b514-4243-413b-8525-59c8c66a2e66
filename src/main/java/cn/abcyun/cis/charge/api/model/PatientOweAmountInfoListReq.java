package cn.abcyun.cis.charge.api.model;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@Accessors(chain = true)
public class PatientOweAmountInfoListReq {

    @NotEmpty(message = "chainIdId不能为空")
    private String chainId;

    /**
     * 如果指定了clinicId，则查询clinicId维度的欠费金额
     */
    private String clinicId;

    @NotEmpty(message = "patientIds不能为空")
    private List<String> patientIds;

}
