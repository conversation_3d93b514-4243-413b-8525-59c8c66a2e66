package cn.abcyun.cis.charge.api.model;

import cn.abcyun.cis.commons.model.CisPatientAge;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create 2024-01-20 17:47
 * @Description
 */

@Data
public class ChargeSheetRegisterIdentityReq {

    @ApiModelProperty("收费单ID，可以为空，当前场景有的话就带上")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String chargeSheetId;

    @ApiModelProperty("注册信息id，没带的话就会新建，带了的话就会更新已有")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonSerialize(using= ToStringSerializer.class)
    private Long registerInfoId;

    @ApiModelProperty("姓名")
    @NotNull(message = "没有填写姓名")
    private String name;

    @ApiModelProperty("性别")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String sex;

    @ApiModelProperty("年龄")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private CisPatientAge age;

    @ApiModelProperty("证件类型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String idCardType;

    @ApiModelProperty("身份证号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String idCard;

    @ApiModelProperty("手机号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String mobile;

}
