package cn.abcyun.cis.charge.api.model;

import cn.abcyun.cis.core.validator.Values;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class CreateChargeRuleProcessReq {

    @Values(value = {"1", "2", "3", "4", "5", "6", "7"}, attr = "制法类型", notNull = true)
    private Integer type;

    @Values(value = {"1", "2"}, attr = "加工方式", notNull = true)
    private Integer processMode;

    @NotEmpty(message = "processUsages不能为空")
    private List<CreateChargeRuleProcessUsageReq> processUsages;


}
