package cn.abcyun.cis.charge.api.model;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
public class OutpatientSheetCalculateReq {

    private String id;

    @NotEmpty(message = "chainId不能为空")
    private String chainId;

    @NotEmpty(message = "clinicId不能为空")
    private String clinicId;

    private String patientOrderId;

    @Valid
    private List<OutpatientFormCalculateReq> outpatientForms;

    private RegistrationInfoReq registrationInfo;

    /**
     * 是否需求执行数量
     */
    private int withExecutedCount;

    /**
     * 是否需要快递和加工信息
     */
    private int withDeliveryAndProcess;

    @Data
    @Accessors(chain = true)
    public static class RegistrationInfoReq {

        private BigDecimal unitPrice;

    }

}
