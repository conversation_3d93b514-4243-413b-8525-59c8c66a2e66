package cn.abcyun.cis.charge.api.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class QueryChargeSheetExceptionStatusListRsp {

    private List<ChargeSheetExceptionStatusInfo> chargeSheetExceptionStatusInfos;

    @Data
    @Accessors(chain = true)
    public static class ChargeSheetExceptionStatusInfo {

        private String chargeSheetId;

        private String patientOrderId;

        private int queryExceptionType;

        private int isContainException;

        public int getIsContainException() {
            return getQueryExceptionType() > 0 ? 1 : 0;
        }
    }
}
