package cn.abcyun.cis.charge.api.model;

import cn.abcyun.cis.commons.rpc.outpatient.ExtendDiagnosisInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/27 下午5:29
 * @description 完善零售收费单用户的信息
 */
@Data
@ApiModel(value = "完善零售收费单用户的信息请求类")
public class ChargeImproveDirectPatientReq {

    @ApiModelProperty(value = "医生id", required = true)
    @NotBlank(message = "医生id不能为空")
    private String doctorId;

    @ApiModelProperty(value = "医生科室的id")
    private String departmentId;

    @ApiModelProperty(value = "医生科室的名称")
    private String departmentName;

    @ApiModelProperty(value = "证件类型", required = true)
    private String idCardType;

    @ApiModelProperty(value = "证件号", required = true)
    private String patientIdCard;

    @ApiModelProperty(value = "患者诊断信息", required = true)
    @NotEmpty(message = "患者诊断信息不能为空")
    private List<ExtendDiagnosisInfo> extendDiagnosisInfos;

 }
