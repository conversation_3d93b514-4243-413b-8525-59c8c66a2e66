package cn.abcyun.cis.charge.api.model;

import cn.abcyun.cis.charge.model.ChargeDeliveryCompany;
import lombok.Data;

@Data
public class DeliveryCompanyRsp {
    private String id;
    private String name;

    public static DeliveryCompanyRsp of(ChargeDeliveryCompany deliveryCompany) {
        if (deliveryCompany == null) {
            return null;
        }
        DeliveryCompanyRsp rsp = new DeliveryCompanyRsp();
        rsp.setId(deliveryCompany.getId());
        rsp.setName(deliveryCompany.getName());
        return rsp;
    }

}
