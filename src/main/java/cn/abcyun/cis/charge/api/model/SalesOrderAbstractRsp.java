package cn.abcyun.cis.charge.api.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024-01-27 17:09
 * @Description
 */

@Data
public class SalesOrderAbstractRsp {

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<SalesOrderAbstractView> rows;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer total;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer offset;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer limit;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private SalesOrderSummaryView summary;

    @ApiModel(value = "SalesOrderSummaryView", description = "")
    @Data
    public static class SalesOrderSummaryView {

        /**
         * 总应收
         */
        @ApiModelProperty("总应收")
        private BigDecimal receivableFeeTotal;

        /**
         * 总收费
         */
        @ApiModelProperty("总收费")
        private BigDecimal receivedFeeTotal;
    }
}
