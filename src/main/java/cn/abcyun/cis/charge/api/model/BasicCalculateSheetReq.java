package cn.abcyun.cis.charge.api.model;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Data
@Accessors(chain = true)
public class BasicCalculateSheetReq {

    @NotEmpty(message = "chainId不能为空")
    private String chainId;

    @NotEmpty(message = "clinicId不能为空")
    private String clinicId;

    private String id;

    private int hisType;

    /**
     * 收费form
     */
    @Valid
    private List<BasicCalculateFormReq> forms;

    /**
     * 期待的总价
     */
    private BigDecimal expectedTotalPrice;

    /**
     * 原总价
     */
    private BigDecimal sourceTotalPrice;

    /**
     * 当前总价
     */
    private BigDecimal totalPrice;

    private int isTotalPriceChanged;

    public BigDecimal getSourceTotalPrice () {
        return Optional.ofNullable(forms)
                .orElse(new ArrayList<>())
                .stream()
                .map(BasicCalculateFormReq::getSourceTotalPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

}
