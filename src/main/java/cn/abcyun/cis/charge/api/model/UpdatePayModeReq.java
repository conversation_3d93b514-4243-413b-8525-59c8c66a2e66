package cn.abcyun.cis.charge.api.model;

import cn.abcyun.cis.core.validator.Values;
import lombok.Data;

import javax.validation.Valid;
import java.util.List;

@Data
public class UpdatePayModeReq {

    @Valid
    private List<PayModeReq> optionalPayModes;

    @Valid
    private List<PayModeReq> customizedPayModes;


    @Data
    public static class PayModeReq{

        private Long payModeId;

        private String name;

        @Values(value = {"0", "1"}, attr = "是否启用")
        private Integer isEnable = 1;

        private Integer sort;

        private Integer type;
    }

}
