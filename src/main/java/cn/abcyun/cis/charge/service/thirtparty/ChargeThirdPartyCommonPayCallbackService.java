package cn.abcyun.cis.charge.service.thirtparty;

import cn.abcyun.bis.rpc.sdk.cis.model.pay.PayStatus;
import cn.abcyun.cis.charge.api.model.PayCallbackContainPayModeReq;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.base.InnerPayModes;
import cn.abcyun.cis.charge.model.ChargePayTransaction;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.repository.ChargePayTransactionRepository;
import cn.abcyun.cis.charge.repository.ChargeTransactionRecordRepository;
import cn.abcyun.cis.charge.service.*;
import cn.abcyun.cis.charge.service.rpc.CisScClinicService;
import cn.abcyun.cis.charge.service.rpc.CisShebaoService;
import cn.abcyun.cis.charge.util.HandleUtils;
import cn.abcyun.cis.idgenerator.AbcIdGenerator;
import cn.abcyun.common.log.marker.AbcLogMarker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2024-12-05 18:06
 * @Description
 */

@Service
@Slf4j
public class ChargeThirdPartyCommonPayCallbackService extends ChargeThirdPartyCallbackPayModeAbstractService {

    public ChargeThirdPartyCommonPayCallbackService(ChargeSheetService chargeSheetService, 
                                                   ChargePayTransactionRepository chargePayTransactionRepository, 
                                                   ChargeService chargeService, 
                                                   AbcIdGenerator abcIdGenerator, 
                                                   SheetProcessorService sheetProcessorService, 
                                                   ChargeTransactionRecordRepository chargeTransactionRecordRepository, 
                                                   ChargeAbnormalTransactionService chargeAbnormalTransactionService, 
                                                   ChargeConfigService chargeConfigService, 
                                                   CisScClinicService scClinicService,
                                                   CisShebaoService shebaoService) {
        super(chargeSheetService, chargePayTransactionRepository, chargeService, abcIdGenerator, sheetProcessorService, chargeTransactionRecordRepository, chargeAbnormalTransactionService, chargeConfigService, scClinicService, shebaoService);
    }

    @Override
    protected boolean needSendPaySuccessMessage(PayCallbackContainPayModeReq payCallbackReq) {
        return false;
    }

    @Override
    protected boolean checkPayCallBackCanExecute(ChargeSheet chargeSheet, PayCallbackContainPayModeReq payCallbackReq, ChargePayTransaction chargePayTransaction) {
        if (chargeSheet == null) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "payCallback chargeSheet = null");
            return false;
        }

        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED && chargeSheet.getStatus() != Constants.ChargeSheetStatus.PART_CHARGED) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "收费单状态不支持入账了，chargeSheet status: {}", chargeSheet.getStatus());
            return false;
        }

        //只要不是待入账或不是已取消就返回false
        if (chargePayTransaction.getPayStatus() != PayStatus.CANCELED && chargePayTransaction.getPayStatus() != PayStatus.WAITING) {
            return false;
        }

        //获取收费单的待收金额
        BigDecimal receivableFee = HandleUtils.isTrueOrFalseReturn(chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_CHARGED || chargeSheet.isLocking())
                .handleAndReturn(chargeSheet::getReceivableFee,
                        () -> calculateUnChargedSheetNeedPayFee(chargeSheet, payCallbackReq.getPayMode(), chargePayTransaction.getPaySource(), payCallbackReq.getOperatorId()));

        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "receivableFee: {}, receivedFee: {}, paySource: {}", receivableFee, payCallbackReq.calculateReceivedFee(), chargePayTransaction.getPaySource());
        //如果是患者端的付费没一定要保证应收等于本次入账金额，因为患者端只能全部付费，如果不是患者端的付费，只要应收大于本次入账金额就行
        return HandleUtils.isTrueOrFalseReturn(Constants.ChargeSource.patientPaySources().contains(chargePayTransaction.getPaySource()))
                .handleAndReturn(() -> receivableFee.compareTo(payCallbackReq.calculateReceivedFee()) == 0,
                        () -> receivableFee.compareTo(payCallbackReq.calculateReceivedFee()) >= 0);
    }

    @Override
    protected void doUpdateChargeSheetInfo(ChargeSheet chargeSheet, PayCallbackContainPayModeReq payCallbackReq) {

    }

    @Override
    public void resetPayModeAndPaySubMode(PayCallbackContainPayModeReq payCallbackReq, ChargePayTransaction chargePayTransaction) {
        payCallbackReq.setPayMode(chargePayTransaction.getPayMode());
    }

    @Override
    public String getPayModeKey() {
        return InnerPayModes.InnerPayModeKey.THIRD_PARTY_COMMON_PAY;
    }
}
