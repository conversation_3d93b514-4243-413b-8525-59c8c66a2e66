package cn.abcyun.cis.charge.service.dto;

import cn.abcyun.cis.commons.rpc.wechat.WeChatPayCallbackReq;
import cn.abcyun.cis.commons.rpc.wechat.WeChatRefundCallbackReq;
import lombok.Data;

/**
 * <AUTHOR>
 * 发送异常小喇叭消息公用类
 */
@Data
public class ThirdPartRpcFailModel {
    private int type;
    private WeChatPayCallbackReq weChatPayCallbackBody;
    private WeChatRefundCallbackReq weChatRefundCallbackBody;
}
