package cn.abcyun.cis.charge.service.dto;

import cn.abcyun.cis.charge.model.ChargeFormItem;
import cn.abcyun.cis.commons.util.ItemParentIdTargetCell;
import org.apache.commons.lang3.StringUtils;

public class ChargeFormItemTargetDto implements ItemParentIdTargetCell {

    private final ChargeFormItem chargeFormItem;

    public ChargeFormItemTargetDto (ChargeFormItem chargeFormItem) {
        this.chargeFormItem = chargeFormItem;
    }

    @Override
    public String getItemId() {
        return chargeFormItem.getId();
    }

    @Override
    public String getSourceItemId() {
        return chargeFormItem.getSourceFormItemId();
    }

    @Override
    public void setParentItemId(String parentItemId) {
        chargeFormItem.setComposeParentFormItemId(parentItemId);
    }

    @Override
    public boolean canProcess() {
        return StringUtils.isNotEmpty(chargeFormItem.getSourceFormItemId());
    }
}
