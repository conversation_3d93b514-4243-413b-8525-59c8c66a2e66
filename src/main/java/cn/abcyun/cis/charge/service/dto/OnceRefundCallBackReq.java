package cn.abcyun.cis.charge.service.dto;

import cn.abcyun.cis.charge.model.ChargeRefundSheet;
import cn.abcyun.cis.charge.model.ThirdPartyPayInfo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 回调退费入账req
 */
@Data
@Accessors(chain = true)
public class OnceRefundCallBackReq {

    private int payMode;

    private int paySubMode;

    /**
     * 退费金额，负值
     */
    private BigDecimal refundFee;

    //退款是关联的退款单号
    private String associatePayTransactionId;

    private ChargeRefundSheet chargeRefundSheet;

    private ThirdPartyPayInfo thirdPartyPayInfo;

    private int paySource;

}

