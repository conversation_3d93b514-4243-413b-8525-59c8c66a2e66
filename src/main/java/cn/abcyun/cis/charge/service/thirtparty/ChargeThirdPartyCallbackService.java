package cn.abcyun.cis.charge.service.thirtparty;

import cn.abcyun.bis.rpc.sdk.cis.model.common.BitFlagUtils;
import cn.abcyun.bis.rpc.sdk.cis.model.pay.PayCallbackRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.pay.PayStatus;
import cn.abcyun.cis.charge.api.model.PayCallbackContainPayModeReq;
import cn.abcyun.cis.charge.api.model.ThirdPartPayCallbackRsp;
import cn.abcyun.cis.charge.base.*;
import cn.abcyun.cis.charge.model.ChargePayTransaction;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.processor.callback.IChargeThirdPartyCallbackProvider;
import cn.abcyun.cis.charge.repository.ChargePayTransactionRepository;
import cn.abcyun.cis.charge.repository.ChargeSheetRepository;
import cn.abcyun.cis.charge.service.ChargeService;
import cn.abcyun.cis.charge.service.ChargeSheetService;
import cn.abcyun.cis.charge.util.ChargePayModeUtils;
import cn.abcyun.cis.charge.util.FillUtils;
import cn.abcyun.cis.charge.util.HandleUtils;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.common.log.marker.AbcLogMarker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ChargeThirdPartyCallbackService {

    private final ChargePayTransactionRepository chargePayTransactionRepository;

    private final ChargeSheetService chargeSheetService;

    private final ChargeService chargeService;

    private Map<Integer, IChargeThirdPartyCallbackProvider> chargeThirdPartyCallbackProviderMap = new HashMap<>();

    @Autowired
    public ChargeThirdPartyCallbackService(ChargePayTransactionRepository chargePayTransactionRepository,
                                           List<IChargeThirdPartyCallbackProvider> chargeThirdPartyPayCallbackProviders,
                                           ChargeSheetService chargeSheetService,
                                           ChargeService chargeService) {
        this.chargePayTransactionRepository = chargePayTransactionRepository;
        this.chargeSheetService = chargeSheetService;
        this.chargeService = chargeService;
        initChargeThirdPartyCallbackServiceMap(chargeThirdPartyPayCallbackProviders);
    }

    public void initChargeThirdPartyCallbackServiceMap(List<IChargeThirdPartyCallbackProvider> chargeThirdPartyPayCallbackProviders) {

        if (CollectionUtils.isEmpty(chargeThirdPartyPayCallbackProviders)) {
            return;
        }

        chargeThirdPartyCallbackProviderMap = chargeThirdPartyPayCallbackProviders.stream().collect(Collectors.toMap(provider -> provider.getPayType(), Function.identity(), (a, b) -> a));
    }

    @Transactional(rollbackFor = Exception.class)
    @Retryable(maxAttempts = 2, include = ObjectOptimisticLockingFailureException.class)
    public ThirdPartPayCallbackRsp callbackExecute(PayCallbackContainPayModeReq payCallbackReq) {
        ThirdPartPayCallbackRsp thirdPartPayCallbackRsp = new ThirdPartPayCallbackRsp();
        thirdPartPayCallbackRsp.setRecordCode(PayCallbackRsp.RecordCode.REJECT);

        ChargePayTransaction chargePayTransaction = chargePayTransactionRepository.findById(payCallbackReq.getRequestTransactionId()).orElse(null);
        if (chargePayTransaction == null) {
            log.info("not found chargePayTransaction by id:{}", payCallbackReq.getRequestTransactionId());
            thirdPartPayCallbackRsp.setRecordCode(PayCallbackRsp.RecordCode.REJECT);
            thirdPartPayCallbackRsp.setMessage("not found chargePayTransaction");
            return thirdPartPayCallbackRsp;
        }

        thirdPartPayCallbackRsp.setChargePayTransaction(chargePayTransaction);

        if (chargePayTransaction.getPayStatus() == PayStatus.SUCCESS) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "该支付已处理成功，不需要再处理，chargePatTransactionId: {}, status: {}", chargePayTransaction.getId(), chargePayTransaction.getPayStatus());
            thirdPartPayCallbackRsp.setRecordCode(PayCallbackRsp.RecordCode.ACCEPT);
            return thirdPartPayCallbackRsp;
        }


        IChargeThirdPartyCallbackProvider chargeThirdPartyCallbackProvider = chargeThirdPartyCallbackProviderMap.getOrDefault(chargePayTransaction.getPayType(), null);

        if (Objects.isNull(chargeThirdPartyCallbackProvider)) {
            throw new ChargeServiceException(ChargeServiceError.CHARGE_THIRD_PART_CALLBACK_TYPE_NOT_FOUND);
        }

        if (TextUtils.isEmpty(payCallbackReq.getOperatorId())) {
            payCallbackReq.setOperatorId(chargePayTransaction.getCreatedBy());
        }


        ChargeSheet chargeSheet = chargeSheetService.findByIdCore(chargePayTransaction.getChargeSheetId());

        if (chargeSheet == null) {
            throw new NotFoundException();
        }

        //如果请求不是支付成功，则直接写入失败信息
        if (payCallbackReq.getPayStatus() != PayStatus.SUCCESS) {
            chargePayTransaction.setPayStatus(PayStatus.FAILED);
            chargePayTransaction.setFailMessage(payCallbackReq.getMessage());
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargePayTransaction no success payCallbackReq.getPayStatus():{}", payCallbackReq.getPayStatus());
            chargeService.releaseLockSheetSource(chargeSheet, chargePayTransaction, false, payCallbackReq.getOperatorId());
            thirdPartPayCallbackRsp.setRecordCode(PayCallbackRsp.RecordCode.ACCEPT);
            return thirdPartPayCallbackRsp;
        }

        if (chargePayTransaction.getPayStatus() != PayStatus.WAITING && chargePayTransaction.getPayStatus() != cn.abcyun.cis.commons.rpc.pay.PayStatus.ERROR) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargePayTransaction payStatus not waiting:{}", chargePayTransaction.getPayStatus());
            thirdPartPayCallbackRsp.setRecordCode(PayCallbackRsp.RecordCode.REJECT);
            thirdPartPayCallbackRsp.setMessage("chargePayTransaction payStatus not waiting");
            return thirdPartPayCallbackRsp;
        }

        PayCallbackRsp payCallbackRsp = chargeThirdPartyCallbackProvider.callback(payCallbackReq, chargeSheet, chargePayTransaction);

        HandleUtils.isTrue(payCallbackRsp != null && payCallbackRsp.getRecordCode() == PayCallbackRsp.RecordCode.ACCEPT)
                .handle(() -> saveChargePayTransactionPayStatus(payCallbackReq, chargeSheet, chargePayTransaction));

        if (payCallbackRsp != null) {
            thirdPartPayCallbackRsp.setRecordCode(payCallbackRsp.getRecordCode());
            thirdPartPayCallbackRsp.setMessage(payCallbackRsp.getMessage());
            thirdPartPayCallbackRsp.setChargeSheetId(chargeSheet.getId());
            thirdPartPayCallbackRsp.setStatus(chargeSheet.getStatus());
            thirdPartPayCallbackRsp.setNeedPay(chargeSheet.calculateNeedPay());
        }

        return thirdPartPayCallbackRsp;
    }

    public void recover(PayCallbackContainPayModeReq payCallbackReq) {
        ChargePayTransaction chargePayTransaction = chargePayTransactionRepository.findById(payCallbackReq.getRequestTransactionId()).orElse(null);
        if (chargePayTransaction == null) {
            return;
        }

        IChargeThirdPartyCallbackProvider chargeThirdPartyCallbackProvider = chargeThirdPartyCallbackProviderMap.getOrDefault(chargePayTransaction.getPayType(), null);

        if (Objects.isNull(chargeThirdPartyCallbackProvider)) {
            return;
        }

        chargeThirdPartyCallbackProvider.recover(payCallbackReq, chargePayTransaction);
    }

    public void saveChargePayTransactionPayStatus(PayCallbackContainPayModeReq payCallbackReq, ChargeSheet chargeSheet, ChargePayTransaction chargePayTransaction) {

        //有异常时，需要清理异常
        if (chargeSheet.getQueryExceptionType() > 0) {
            PayModeInfo payModeInfo = InnerPayModes.getPayModeInfo(chargePayTransaction.getPayMode(), chargePayTransaction.getPaySubMode());
            if (payModeInfo == null) {
                log.error("payModeInfo is null, payMode:{}, paySubMode:{}", chargePayTransaction.getPayMode(), chargePayTransaction.getPaySubMode());
                throw new ChargeServiceException(ChargeServiceError.CHARGE_THIRD_PART_CALLBACK_PAY_MODE_NOT_FOUND);
            }

            Integer exceptionType = payModeInfo.getExceptionType();
            if (exceptionType != null) {
                //查询对应的支付方式是否还有别的异常，如果有，则不清理
                boolean existedExceptionPayMode = chargePayTransactionRepository.existsByChargeSheetIdAndPayModeAndPayStatusAndIsDeleted(chargeSheet.getId(), chargePayTransaction.getPayMode(), cn.abcyun.cis.commons.rpc.pay.PayStatus.ERROR, 0);

                //如果没有异常了，则直接将收费单上的异常移除
                if (!existedExceptionPayMode) {
                    chargeSheet.setQueryExceptionType(BitFlagUtils.offFlag(chargeSheet.getQueryExceptionType(), exceptionType));
                }
            }
        }

        List<ChargePayTransaction> toSaveList = new ArrayList<>();
        chargePayTransaction.setPayStatus(PayStatus.SUCCESS);
        FillUtils.fillLastModifiedBy(chargePayTransaction, payCallbackReq.getOperatorId());
        toSaveList.add(chargePayTransaction);
        if (CollectionUtils.isNotEmpty(chargePayTransaction.getGroupBranchPayTransactions())) {
            toSaveList.addAll(chargePayTransaction.getGroupBranchPayTransactions()
                    .stream()
                    .filter(Objects::nonNull)
                    .map(c -> {
                        c.setPayStatus(PayStatus.SUCCESS);
                        FillUtils.fillLastModifiedBy(c, payCallbackReq.getOperatorId());
                        return c;
                    }).collect(Collectors.toList())
            );
        }

        chargePayTransactionRepository.saveAll(toSaveList);
    }

}
