package cn.abcyun.cis.charge.service;

import cn.abcyun.bis.rpc.sdk.cis.model.patientorder.BusinessLockVO;
import cn.abcyun.bis.rpc.sdk.cis.model.pay.PayCallbackRsp;
import cn.abcyun.bis.rpc.sdk.mp.model.chargecenter.ChargeCenterCallbackReq;
import cn.abcyun.bis.rpc.sdk.mp.model.chargecenter.ChargeCenterCallbackRsp;
import cn.abcyun.bis.rpc.sdk.mp.model.chargecenter.ChargeCenterChargeBusinessInfo;
import cn.abcyun.cis.charge.amqp.MQProducer;
import cn.abcyun.cis.charge.api.model.PayCallbackContainPayModeReq;
import cn.abcyun.cis.charge.api.model.ThirdPartPayCallbackRsp;
import cn.abcyun.cis.charge.model.ChargePayTransaction;
import cn.abcyun.cis.charge.service.rpc.CisPatientOrderService;
import cn.abcyun.cis.charge.service.thirtparty.ChargeThirdPartyCallbackService;
import cn.abcyun.cis.charge.util.JsonUtils;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.rpc.pay.PayExtraInfo;
import cn.abcyun.cis.commons.rpc.pay.PayStatus;
import cn.abcyun.cis.commons.rpc.wechat.WeChatPayCallbackReq;
import cn.abcyun.cis.commons.rpc.wechat.WeChatRefundCallbackReq;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.cis.core.redis.RedisLock;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ChargeThirdPartService {
    private final ChargeThirdPartyCallbackService chargeThirdPartyCallbackService;
    private final CisPatientOrderService patientOrderService;

    public ChargeThirdPartService(ChargeThirdPartyCallbackService chargeThirdPartyCallbackService,
                                  CisPatientOrderService patientOrderService) {
        this.chargeThirdPartyCallbackService = chargeThirdPartyCallbackService;
        this.patientOrderService = patientOrderService;
    }

    /**
     * 处理wechatPay业务方支付回调
     *
     * @param req
     * @return
     */
    public PayCallbackRsp handleThirdPartPayCallBack(WeChatPayCallbackReq req) {

        if (StringUtils.isEmpty(req.getBusinessTradeNo())) {
            throw new ParamRequiredException("businessTradeNo");
        }

        PayCallbackContainPayModeReq payCallbackReq = new PayCallbackContainPayModeReq();
        payCallbackReq.setTaskId(req.getOutTradeNo());
        payCallbackReq.setRequestTransactionId(req.getBusinessTradeNo());
        payCallbackReq.setReceivedFee(MathUtils.convertFenIntToBigDecimal(req.getTotalFee()));
        payCallbackReq.setPayStatus(req.isHasPay() ? PayStatus.SUCCESS : PayStatus.FAILED);
        PayExtraInfo payExtraInfo = new PayExtraInfo();
        payExtraInfo.setThirdPartyTransactionId(req.getTransactionId());
        payExtraInfo.setChannelTransactionId(req.getChannelTransactionId());
        payCallbackReq.setExtraInfo(payExtraInfo);
        //微信自助支付，需要在微信支付成功的回调里面修改状态
        payCallbackReq.setExtra(req.getExtra());
        payCallbackReq.setPaySubMode(Optional.ofNullable(req.getPaySubMode()).orElse(0));
        payCallbackReq.setMessage(req.getMessage());

        return callback(payCallbackReq);
    }

    /**
     * 处理wechatPay业务方退款回调
     *
     * @param req
     * @return
     */
    public PayCallbackRsp handleThirdPartRefundCallBack(WeChatRefundCallbackReq req) {

        if (StringUtils.isEmpty(req.getBusinessRefundNo())) {
            throw new ParamRequiredException("businessRefundNo");
        }

        PayCallbackContainPayModeReq payCallbackReq = new PayCallbackContainPayModeReq();
        payCallbackReq.setTaskId(req.getOutRefundNo());
        payCallbackReq.setRequestTransactionId(req.getBusinessRefundNo());
        payCallbackReq.setReceivedFee(MathUtils.convertFenIntToBigDecimal(req.getRefundFee()).negate());
        payCallbackReq.setPayStatus(req.isHasRefund() ? PayStatus.SUCCESS : PayStatus.FAILED);
        PayExtraInfo payExtraInfo = new PayExtraInfo();
        payExtraInfo.setThirdPartyTransactionId(req.getTransactionId());
        payCallbackReq.setExtraInfo(payExtraInfo);

        return callback(payCallbackReq);
    }


    @RedisLock(key = "'charge.sheet.payCallback:' + #payCallbackReq.requestTransactionId", waitTime = 10)
    public PayCallbackRsp callback(PayCallbackContainPayModeReq payCallbackReq) {

        log.info("payCallback payCallbackReq={}", cn.abcyun.cis.commons.util.JsonUtils.dump(payCallbackReq));
        ThirdPartPayCallbackRsp rsp = new ThirdPartPayCallbackRsp();
        rsp.setRecordCode(PayCallbackRsp.RecordCode.REJECT);
        try {
            rsp = chargeThirdPartyCallbackService.callbackExecute(payCallbackReq);
        } catch (Exception e) {
            log.error("payCallbackProvider.payCallback error.", e);
            rsp.setRecordCode(PayCallbackRsp.RecordCode.REJECT);
        }

        if (rsp != null && rsp.getRecordCode() == PayCallbackRsp.RecordCode.ACCEPT) {
            //调用patientOrder解锁
            if (rsp.getChargePayTransaction() != null) {
                ChargePayTransaction chargePayTransaction = rsp.getChargePayTransaction();
                MQProducer.doAfterTransactionCommit(() -> {
                    String operatorId = TextUtils.isEmpty(payCallbackReq.getOperatorId()) ? chargePayTransaction.getCreatedBy() : payCallbackReq.getOperatorId();
                    patientOrderService.unlockPatientOrder(chargePayTransaction.getPatientOrderId(), BusinessLockVO.BusinessKey.CHARGE_SHEET, chargePayTransaction.getChainId(), chargePayTransaction.getClinicId(), operatorId);
                });


                //发送TobMessage通知前端
//                String event = chargePayTransaction.getPayType() == ChargePayTransaction.PayType.PAY ? WebMessageBody.WebMessageEvent.CHARGE_SHEET_PAID_PATIENT_ORDER_UNLOCK : WebMessageBody.WebMessageEvent.CHARGE_SHEET_REFUND_PATIENT_ORDER_UNLOCK;
//                SendMessageUtils.sendPatientOrderLockUnLockMessage(chargePayTransaction.getChainId(),
//                        chargePayTransaction.getClinicId(),
//                        event,
//                        chargePayTransaction.getChargeSheetId(),
//                        chargePayTransaction.getPatientOrderId(),
//                        chargePayTransaction.getId(),
//                        operatorId
//                );
            }
        } else {
            chargeThirdPartyCallbackService.recover(payCallbackReq);
        }

        return rsp;
    }

    public ChargeCenterCallbackRsp handleChargeCenterPayCallBack(ChargeCenterCallbackReq callbackReq) {
        if (StringUtils.isEmpty(callbackReq.getRequestId())) {
            throw new ParamRequiredException("businessTradeNo");
        }

        ChargeCenterChargeBusinessInfo chargeBusinessInfo = callbackReq.getBusinessExtraInfo() != null ? JsonUtils.readValue(callbackReq.getBusinessExtraInfo(), ChargeCenterChargeBusinessInfo.class) : null;
        if (chargeBusinessInfo == null) {
            throw new ParamRequiredException("businessExtraInfo");
        }

        PayCallbackContainPayModeReq payCallbackReq = new PayCallbackContainPayModeReq();
        payCallbackReq.setTaskId(callbackReq.getRequestId());
        payCallbackReq.setRequestTransactionId(chargeBusinessInfo.getTransactionId());
        payCallbackReq.setReceivedFee(callbackReq.getReceivedPrice());
        payCallbackReq.setPayStatus(callbackReq.getPayStatus() == cn.abcyun.bis.rpc.sdk.cis.model.pay.PayStatus.SUCCESS ? PayStatus.SUCCESS : PayStatus.FAILED);
        payCallbackReq.setPayMode(callbackReq.getPayMode());
        payCallbackReq.setPaySubMode(callbackReq.getPaySubMode());
        payCallbackReq.setExtra(callbackReq.getBusinessExtraInfo() != null ? callbackReq.getBusinessExtraInfo().toString() : null);

        PayCallbackRsp payCallbackRsp = callback(payCallbackReq);

        ChargeCenterCallbackRsp chargeCenterCallbackRsp = new ChargeCenterCallbackRsp();
        chargeCenterCallbackRsp.setCode(payCallbackRsp.getRecordCode() == PayCallbackRsp.RecordCode.ACCEPT ? ChargeCenterCallbackRsp.Code.SUCCESS : ChargeCenterCallbackRsp.Code.FAIL);
        chargeCenterCallbackRsp.setMessage(payCallbackRsp.getMessage());
        return chargeCenterCallbackRsp;
    }
}
