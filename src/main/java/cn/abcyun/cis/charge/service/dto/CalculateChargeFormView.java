package cn.abcyun.cis.charge.service.dto;

import cn.abcyun.bis.rpc.sdk.bis.model.order.OrderProcessRuleView;
import cn.abcyun.cis.charge.api.model.ProcessInfoView;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class CalculateChargeFormView {
    private String id;
    private String keyId;
    private int sourceFormType;
    private List<CalculateChargeFormItemView> chargeFormItems;
    // 业务附加字段，不存入数据库
    private BigDecimal totalPrice; //总价格不算折扣
    private BigDecimal totalDiscountPrice; //总折扣

    //空中药房form总价格不算折扣
    private BigDecimal sourceTotalPrice;

    /**
     * 药品总金额
     */
    private BigDecimal medicineTotalPrice;

    /**
     * 药品总金额
     */
    private BigDecimal sourceMedicineTotalPrice;

    /**
     * 这是空中药房的加工费规则信息，命名没有命好。。
     */
    private OrderProcessRuleView processRule;

    /**
     * 应收（平摊了议价之后的总值）
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal receivableFee;

    @JsonIgnore
    private boolean isUseLimitPrice = false;

    /**
     * 这是本地药房的加工费信息
     */
    private ProcessInfoView processInfo;

    private BigDecimal expectedTotalPrice;
}
