package cn.abcyun.cis.charge.service.thirtparty;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.cis.charge.api.model.PayCallbackContainPayModeReq;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.base.InnerPayModes;
import cn.abcyun.cis.charge.model.*;
import cn.abcyun.cis.charge.repository.ChargePayTransactionRepository;
import cn.abcyun.cis.charge.repository.ChargeTransactionRecordRepository;
import cn.abcyun.cis.charge.service.*;
import cn.abcyun.cis.charge.service.rpc.CisScClinicService;
import cn.abcyun.cis.charge.service.rpc.CisShebaoService;
import cn.abcyun.cis.charge.util.ChargeUtils;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.cis.commons.amqp.message.TraceableCode;
import cn.abcyun.cis.commons.rpc.pay.PayCallbackReq;
import cn.abcyun.cis.idgenerator.AbcIdGenerator;
import cn.abcyun.common.log.marker.AbcLogMarker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ChargeShebaoPayModeCallbackService extends ChargeThirdPartyCallbackPayModeAbstractService {

    @Autowired
    public ChargeShebaoPayModeCallbackService(ChargeSheetService chargeSheetService,
                                              ChargePayTransactionRepository chargePayTransactionRepository,
                                              ChargeService chargeService,
                                              AbcIdGenerator abcIdGenerator,
                                              SheetProcessorService sheetProcessorService,
                                              ChargeTransactionRecordRepository chargeTransactionRecordRepository,
                                              ChargeAbnormalTransactionService chargeAbnormalTransactionService,
                                              ChargeConfigService chargeConfigService,
                                              CisScClinicService scClinicService,
                                              CisShebaoService shebaoService) {
        super(chargeSheetService, chargePayTransactionRepository, chargeService, abcIdGenerator, sheetProcessorService, chargeTransactionRecordRepository, chargeAbnormalTransactionService, chargeConfigService, scClinicService, shebaoService);
    }


    @Override
    public String getPayModeKey() {
        return InnerPayModes.getPayModeByName(InnerPayModes.InnerPayModeName.SHEBAO_PAY).getPayModeKey();
    }

    @Override
    public void resetPayModeAndPaySubMode(PayCallbackContainPayModeReq payCallbackReq, ChargePayTransaction chargePayTransaction) {
        payCallbackReq.setPayMode(chargePayTransaction.getPayMode());
    }

    @Override
    public void updateChargeSheetPatientId(ChargeSheet chargeSheet, ChargePayTransaction chargePayTransaction, PayCallbackContainPayModeReq payCallbackReq) {
        if (chargeSheet.getType() != ChargeSheet.Type.DIRECT_SALE && chargeSheet.getType() != ChargeSheet.Type.THERAPY) {
            return;
        }
        if (!Objects.equals(Constants.ANONYMOUS_PATIENT_ID, chargeSheet.getPatientId())) {
            return;
        }
        if (StringUtils.isEmpty(payCallbackReq.getPatientId()) || Objects.equals(Constants.ANONYMOUS_PATIENT_ID, payCallbackReq.getPatientId())) {
            return;
        }
        String operatorId = payCallbackReq.getOperatorId();
        // 更新patientId
        chargeSheet.setPatientId(payCallbackReq.getPatientId());
        Optional.ofNullable(chargeSheet.getAdditionalFees())
                .orElse(new ArrayList<>())
                .forEach(additionalFee -> {
                    additionalFee.setPatientId(payCallbackReq.getPatientId());
                    additionalFee.setLastModified(Instant.now());
                    additionalFee.setLastModifiedBy(operatorId);
                });

        Optional.ofNullable(chargeSheet.getChargeActions())
                .orElse(new ArrayList<>())
                .forEach(chargeAction -> chargeAction.setPatientId(payCallbackReq.getPatientId()));

        Optional.ofNullable(chargePayTransaction)
                .ifPresent(payTransaction -> {
                    payTransaction.setPatientId(payCallbackReq.getPatientId());
                    payTransaction.setLastModified(Instant.now());
                    payTransaction.setLastModifiedBy(operatorId);
                });

        Optional.ofNullable(chargeSheet.getChargeTransactions())
                .orElse(new ArrayList<>())
                .forEach(chargeTransaction -> {
                    chargeTransaction.setPatientId(payCallbackReq.getPatientId());
                    chargeTransaction.setLastModified(Instant.now());
                    chargeTransaction.setLastModifiedBy(operatorId);
                });
    }

    @Override
    protected boolean checkPayCallBackCanExecute(ChargeSheet chargeSheet, PayCallbackContainPayModeReq payCallbackReq, ChargePayTransaction chargePayTransaction) {

        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED && chargeSheet.getStatus() != Constants.ChargeSheetStatus.PART_CHARGED) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "收费单状态不支持入账了，chargeSheet status: {}", chargeSheet.getStatus());
            return false;
        }
        return true;
    }

    @Override
    protected void doUpdateChargeSheetInfo(ChargeSheet chargeSheet, PayCallbackContainPayModeReq payCallbackReq) {

        if (Objects.isNull(chargeSheet) || Objects.isNull(payCallbackReq)) {
            return;
        }

        //更新追溯码
        updateChargeFormItemTraceableList(chargeSheet, payCallbackReq.getChargeFormItems());

        //更新医生信息
        updateChargeSheetDoctorIdAndDiagnosis(chargeSheet, payCallbackReq);
    }

    private void updateChargeSheetDoctorIdAndDiagnosis(ChargeSheet chargeSheet, PayCallbackContainPayModeReq payCallbackReq) {
        // 只有零售单才更新
        if (!ChargeSheet.Type.supportUpdateDiagnosisForShebaoPayTypes().contains(chargeSheet.getType())) {
            return;
        }
        ChargeUtils.insertChargeSheetAdditionalIfNeed(chargeSheet, payCallbackReq.getOperatorId());
        ChargeUtils.updateChargeSheetAdditionalDoctorIdAndDiagnosis(chargeSheet.getAdditional(), payCallbackReq.getDoctorId(), payCallbackReq.getDoctorDepartmentId(), null, payCallbackReq.getExtendDiagnosisInfos());
    }

    protected void updateChargeFormItemTraceableList(ChargeSheet chargeSheet, List<PayCallbackReq.ChargeFormItem> payChargeFormItemList) {
        if (Objects.isNull(chargeSheet) || CollectionUtils.isEmpty(payChargeFormItemList)) {
            return;
        }
        List<ChargeForm> chargeForms = chargeSheet.getChargeForms();
        if (CollectionUtils.isEmpty(chargeForms)) {
            return;
        }
        List<ChargeFormItem> chargeFormItemList = chargeForms.stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> CollectionUtils.isNotEmpty(chargeForm.getChargeFormItems()))
                .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                .filter(item -> item.getIsDeleted() == 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(chargeFormItemList)) {
            return;
        }
        // 开始组装追溯码数据信息
        Map<String, PayCallbackReq.ChargeFormItem> traceableCodeMap = payChargeFormItemList.stream()
                .filter(item -> Objects.nonNull(item) && CollectionUtils.isNotEmpty(item.getTraceableCodeList()))
                .collect(Collectors.toMap(PayCallbackReq.ChargeFormItem::getId, Function.identity(),(a,b)->a));
        if (MapUtils.isEmpty(traceableCodeMap)) {
            return;
        }
        chargeFormItemList.stream()
                .filter(Objects::nonNull)
                .forEach(chargeFormItem -> {
                    // 如果包含id,则说明医保结算的项目,不论追溯码怎么变都要更改
                    if (traceableCodeMap.containsKey(chargeFormItem.getId())) {
                        List<TraceableCode> traceableCodeList = traceableCodeMap.get(chargeFormItem.getId()).getTraceableCodeList();
                        ChargeFormItemAdditional additional = chargeFormItem.getAdditional();
                        if (Objects.isNull(additional)) {
                            additional = new ChargeFormItemAdditional();
                        }
                        List<cn.abcyun.bis.rpc.sdk.cis.model.goods.TraceableCode> saveTraceableCodeList = new ArrayList<>();
                        if (!CollectionUtils.isEmpty(traceableCodeList)) {
                            traceableCodeList.forEach(traceableCode -> {
                                if(traceableCode.getType() != null && traceableCode.getType() == GoodsConst.DrugIdentificationCodeType.NO_CODE){
                                    return;
                                }
                                cn.abcyun.bis.rpc.sdk.cis.model.goods.TraceableCode saveTraceableCode = new cn.abcyun.bis.rpc.sdk.cis.model.goods.TraceableCode();
                                saveTraceableCode.setId(traceableCode.getId());
                                saveTraceableCode.setType(traceableCode.getType()); //202409支持无码追溯码
                                saveTraceableCode.setNo(traceableCode.getNo());
                                saveTraceableCode.setBatchId(traceableCode.getBatchId());
                                saveTraceableCode.setBatchNo(traceableCode.getBatchNo());
                                saveTraceableCode.setUsed(traceableCode.getUsed());
                                saveTraceableCode.setHisPackageCount(traceableCode.getHisPackageCount());
                                saveTraceableCode.setHisPieceCount(traceableCode.getHisPieceCount());
                                saveTraceableCode.setCount(traceableCode.getCount());
                                saveTraceableCode.setDismountingSn(traceableCode.getDismountingSn());
                                saveTraceableCodeList.add(saveTraceableCode);
                            });
                        }
                        if (!saveTraceableCodeList.isEmpty()) {
                            additional.setTraceableCodeList( saveTraceableCodeList);
                        }
                    }
                });
    }

    @Override
    protected boolean needSendPaySuccessMessage(PayCallbackContainPayModeReq payCallbackReq) {
        return false;
    }

}
