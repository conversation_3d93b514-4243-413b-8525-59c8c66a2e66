//package cn.abcyun.cis.charge.service;
//
//import cn.abcyun.cis.charge.model.ChargeFormItem;
//import cn.abcyun.cis.charge.repository.ChargeFormItemRepository;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.util.List;
//
//@Service
//@Transactional
//@Slf4j
//public class ChargeFormItemService {
//    @Autowired
//    private ChargeFormItemRepository mChargeFormItemRepository;
//
//    public List<ChargeFormItem> findChargeFormItemsByPatientOrderIdAndProductType(String patientOrderId, int productType) {
//        return mChargeFormItemRepository.findByPatientOrderIdAndProductTypeAndIsDeleted(patientOrderId, productType, 0);
//    }
//}
