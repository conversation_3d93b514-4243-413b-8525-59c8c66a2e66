package cn.abcyun.cis.charge.service.dto;

import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.processor.discount.CalculateGiftRulePromotion;
import cn.abcyun.cis.charge.processor.discount.GiftCouponView;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class GiftRulePromotionView {

    private String id;

    private String name;

    private boolean checked;

    private BigDecimal orderThresholdPrice;

    private BigDecimal discountPrice;

    private Boolean expectedChecked;

    private CalculateGiftRulePromotion.HitGiftRule hitGiftRule;

    private List<GiftCouponView> giftCoupons;//赠送优惠券
    private List<GiftGoodsItemView> giftGoodItems; //赠送服务

    private List<PromotionProductItem> productItems = new ArrayList<>();

    private List<PromotionAirPharmacyForm> airPharmacyForms = new ArrayList<>();

    //是否能被使用(0：不能被使用， 1：能被使用)
    private int isCanBeUsed = 1;

    public boolean getChecked() {
        return checked;
    }

    public void addProductItems(List<PromotionProductItem> productItems) {
        if (this.productItems == null) {
            this.productItems = new ArrayList<>();
        }

        if (productItems != null) {
            this.productItems.addAll(productItems);
        }
    }

    @Data
    public static class GiftGoodsItemView {
        private String id;
        private String name;
        private int type;
        private int subType;
        private BigDecimal count;
        private String unit;
        private String chargeFormItemId;
        private boolean checked;
        private Boolean expectedChecked;

        public boolean getChecked() {
            return checked;
        }

        @JsonIgnore
        public int getIsDismounting() {
            int isDismounting = 0;
            if (getType() == Constants.ProductType.MEDICINE && getSubType() == Constants.ProductType.SubType.MEDICINE_CHINESE) {
                isDismounting = 1;
            }
            return isDismounting;
        }
    }
}
