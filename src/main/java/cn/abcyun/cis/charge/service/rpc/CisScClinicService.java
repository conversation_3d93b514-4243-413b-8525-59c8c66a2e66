package cn.abcyun.cis.charge.service.rpc;

import cn.abcyun.bis.rpc.sdk.cis.client.AbcCisScClinicFeignClient;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.*;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.EmployeeView;
import cn.abcyun.cis.charge.processor.provider.ClinicProvider;
import cn.abcyun.cis.charge.util.CaffeineCacheUtils;
import cn.abcyun.cis.charge.util.JsonUtils;
import cn.abcyun.cis.core.util.FeignClientRpcTemplate;
import cn.abcyun.common.log.marker.AbcLogMarker;
import cn.abcyun.common.model.AbcListPage;
import cn.abcyun.common.model.AbcServiceResponseBody;
import com.github.benmanes.caffeine.cache.Cache;
import com.google.common.collect.Lists;
import feign.RetryableException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CisScClinicService implements ClinicProvider {

    @Autowired
    private AbcCisScClinicFeignClient client;

    @Autowired
    private Cache<String, String> departmentIdNameCache;

    @Autowired
    private Cache<String, Organ> organCache;

    /**
     * 根据id获取打印信息
     *
     * @param id
     * @return
     */
    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public OrganPrintView getOrganPrintInfo(String id) {
        if (StringUtils.isEmpty(id)) {
            return null;
        }

        return FeignClientRpcTemplate.dealRpcClientMethod("getOrgan",
                () -> client.getOrganPrintInfo(id),
                id);
    }

    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public ChainOrgan getChainOrgan(String chainId) {
        if (StringUtils.isEmpty(chainId)) {
            return null;
        }

        return FeignClientRpcTemplate.dealRpcClientMethod("getChainOrgan",
                () -> client.getChainOrgan(chainId),
                chainId);
    }

    @Override
    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public Organ getOrgan(String clinicId) {

        return CaffeineCacheUtils.findOneFromCacheOrOtherSource("getOrganFromCache", organCache, clinicId, (notInCatchClinicId) -> {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "未命中内存缓存, notInCacheId: {}", JsonUtils.dump(notInCatchClinicId));
            return getOrganFromRpc(notInCatchClinicId);
        });
    }

    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    private Organ getOrganFromRpc(String clinicId) {

        if (StringUtils.isEmpty(clinicId)) {
            return null;
        }

        return FeignClientRpcTemplate.dealRpcClientMethod("getOrgan",
                () -> client.getOrgan(clinicId),
                clinicId);
    }

    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public Organ getOrganAlwaysReturn(String clinicId) {
        Organ organ = null;
        try {
            organ = getOrgan(clinicId);
        } catch (Exception e) {
            log.error("getOrgan error, {}", e.getMessage());
        }

        return organ;
    }

    @Override
    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public ClinicEmployee queryEmployeeClinicInfoById(String employeeId, String clinicId) {
        ClinicEmployee clinicEmployee = null;
        if (StringUtils.isAnyEmpty(employeeId, clinicId)) {
            return null;
        }
        try {
            clinicEmployee = FeignClientRpcTemplate.dealRpcClientMethod("queryEmployeeClinicInfoById",
                    () -> client.queryEmployeeClinicInfoById(employeeId, clinicId),
                    employeeId, clinicId
            );
        } catch (Exception e) {
            log.error("queryEmployeeClinicInfoById error: {}", e.getMessage());
        }

        return clinicEmployee;

    }

    @Override
    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public List<EmployeeClinicInfo> queryClinicEmployeeInfo(String chainId, String clinicId, List<String> employeeIds) {
        if (CollectionUtils.isEmpty(employeeIds)) {
            return Lists.newArrayList();
        }
        QueryEmployeeClinicInfoByIdsReq req = new QueryEmployeeClinicInfoByIdsReq();
        req.setChainId(chainId);
        req.setClinicId(clinicId);
        req.setEmployeeIds(employeeIds);
        req.setStatusList(Lists.newArrayList(1, 99));
        try {
            AbcListPage<EmployeeClinicInfo> response = FeignClientRpcTemplate.dealRpcClientMethod("queryEmployeeClinicInfoByIds",
                    () -> client.queryEmployeeClinicInfoByIds(req), req);
            if (response != null && !CollectionUtils.isEmpty(response.getRows())) {
                return response.getRows();
            }
        } catch (Exception e) {
            log.error("queryEmployeeClinicInfoByIds error: {}", e.getMessage());
        }

        return Lists.newArrayList();
    }

    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public Employee queryEmployeeById(String employeeId, String chainId) {

        if (StringUtils.isEmpty(employeeId) || StringUtils.isEmpty(chainId)) {
            return null;
        }
        Employee employee = null;
        try {
            employee = FeignClientRpcTemplate.dealRpcClientMethod("queryEmployeeById",
                    () -> client.queryEmployeeById(employeeId, chainId), employeeId, chainId);
        } catch (Exception e) {
            log.error("queryEmployeeById error: {}", e.getMessage());
        }
        return employee;
    }

    @Override
    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public Department queryDepartmentById(String departmentId) {

        if (StringUtils.isEmpty(departmentId)) {
            return null;
        }
        Department department = null;
        try {
            department = FeignClientRpcTemplate.dealRpcClientMethod("queryDepartmentById",
                    () -> client.queryDepartmentById(departmentId),
                    departmentId);
        } catch (Exception e) {
            log.error("queryDepartmentById error: {}", e.getMessage());
        }
        return department;
    }

    @Override
    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public List<Department> queryDepartmentsByIds(List<String> departmentIds) {
        if (CollectionUtils.isEmpty(departmentIds)) {
            return new ArrayList<>();
        }
        QueryDepartmentByIds queryDepartmentByIds = new QueryDepartmentByIds();
        queryDepartmentByIds.setIds(departmentIds);
        AbcListPage<Department> rsp = null;
        try {
            rsp = FeignClientRpcTemplate.dealRpcClientMethod("queryDepartmentByIds",
                    () -> client.queryDepartmentsByIds(queryDepartmentByIds),
                    departmentIds);
        } catch (Exception e) {
            log.error("queryDepartmentByIds error, msg: {}", e.getMessage());
        }

        return Optional.ofNullable(rsp).map(AbcListPage::getRows).orElse(new ArrayList<>());
    }

    @Override
    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public Map<String, String> queryDepartmentIdNameMapByIds(List<String> departmentIds) {
        return CaffeineCacheUtils.findFromCacheOrOtherSource("queryDepartmentIdNameMapByIds", departmentIdNameCache, departmentIds, (notInCacheIds) -> {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "未命中内存缓存, notInCacheIds: {}", JsonUtils.dump(notInCacheIds));
            List<Department> departments = queryDepartmentsByIds(notInCacheIds);
            if (CollectionUtils.isEmpty(departments)) {
                return new HashMap<>();
            }
            return departments.stream().collect(Collectors.toMap(Department::getId, Department::getName));
        });
    }


    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public OrganConfig getOrganConfigById(String chainId) {

        if (StringUtils.isEmpty(chainId)) {
            return null;
        }

        return FeignClientRpcTemplate.dealRpcClientMethod("getOrganConfigById",
                () -> client.getOrganConfigById(chainId),
                chainId);
    }

    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public List<Employee> queryEmployeeListById(String chainId, List<String> employeeIds, int isWithDeleted) {

        if (CollectionUtils.isEmpty(employeeIds)) {
            return new ArrayList<>();
        }


        QueryEmployeeListByIdReq req = new QueryEmployeeListByIdReq();
        req.setIds(employeeIds);
        req.setChainId(chainId);
        req.setIsWithDeleted(isWithDeleted);


        AbcListPage<Employee> employeeListPage = FeignClientRpcTemplate.dealRpcClientMethod("queryEmployeeListById",
                () -> client.queryEmployeeListById(req),
                req);

        return Optional.ofNullable(employeeListPage).map(AbcListPage::getRows).orElse(new ArrayList<>());
    }

    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public Map<String, String> queryEmployeeIdNameMap(String chainId, List<String> employeeIds, int isWithDeleted) {
        return queryEmployeeListById(chainId, employeeIds, isWithDeleted)
                .stream()
                .collect(Collectors.toMap(Employee::getId, Employee::getName, (a, b) -> a));
    }

    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public Map<String, String> getClinicIdNameMap(String chainId) {
        ChainOrgan chainOrgan = getChainOrgan(chainId);

        if (chainOrgan == null) {
            return new HashMap<>();
        }

        Map<String, String> result = new HashMap<>();
        result.put(chainOrgan.getId(), chainOrgan.getName());

        if (CollectionUtils.isNotEmpty(chainOrgan.getChildren())) {
            chainOrgan.getChildren().forEach(child -> result.put(child.getId(), child.getName()));
        }

        return result;
    }

    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public Map<String, Organ> getClinicIdOrganMap(String chainId) {
        ChainOrgan chainOrgan = getChainOrgan(chainId);

        if (chainOrgan == null) {
            return new HashMap<>();
        }

        Map<String, Organ> result = new HashMap<>();
        Organ organ = new Organ();
        BeanUtils.copyProperties(chainOrgan, organ);
        result.put(chainOrgan.getId(), organ);

        if (CollectionUtils.isNotEmpty(chainOrgan.getChildren())) {
            chainOrgan.getChildren().forEach(child -> {
                Organ childOrgan = new Organ();
                BeanUtils.copyProperties(child, childOrgan);
                result.put(child.getId(), childOrgan);
            });
        }

        return result;
    }


    /**
     * 查询诊所的空中药房配置
     *
     * @param chainId
     * @param clinicId
     * @return
     */
    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public ClinicAirPharmacyConfig queryAirPharmacyConfig(String chainId, String clinicId) {

        if (StringUtils.isAnyEmpty(chainId, clinicId)) {
            return null;
        }

        return FeignClientRpcTemplate.dealRpcClientMethod("queryAirPharmacyConfig",
                () -> client.getClinicAirPharmacyConfig(chainId, clinicId),
                chainId, clinicId);
    }

    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public boolean isOpenAirPharmacy(String chainId, String clinicId) {

        ClinicAirPharmacyConfig clinicAirPharmacyConfig = queryAirPharmacyConfig(chainId, clinicId);

        if (clinicAirPharmacyConfig == null) {
            return false;
        }

        return clinicAirPharmacyConfig.isSupportAirPharmacy() && clinicAirPharmacyConfig.getOpenSwitch() == 1;
    }

    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public ClinicPurchaseItemView getClinicPurchaseItem(String clinicId, String purchaseItemKey) {
        return FeignClientRpcTemplate.dealRpcClientMethod(
                "getClinicPurchaseItem",
                () -> client.getClinicPurchaseItem(clinicId, purchaseItemKey),
                null,
                clinicId, purchaseItemKey
        );
    }
    /**
     * 查询医生快照
     * @param batchQueryEmployeeSnapshotReq 入参
     * @return
     */
    @Override
    public BatchQueryEmployeeSnapshotRsp queryEmployeeSnap(BatchQueryEmployeeSnapshotReq batchQueryEmployeeSnapshotReq) {

        Optional.ofNullable(batchQueryEmployeeSnapshotReq)
                .map(BatchQueryEmployeeSnapshotReq::getItemList)
                .orElse(new ArrayList<>())
                .removeIf(item -> CollectionUtils.isEmpty(item.getBusinessTimes()));

        if (batchQueryEmployeeSnapshotReq == null
                || StringUtils.isEmpty(batchQueryEmployeeSnapshotReq.getChainId())
                || CollectionUtils.isEmpty(batchQueryEmployeeSnapshotReq.getItemList())) {
            return new BatchQueryEmployeeSnapshotRsp();
        }

        BatchQueryEmployeeSnapshotRsp rsp = new BatchQueryEmployeeSnapshotRsp();
        rsp.setEmployeeList(new ArrayList<>());

        Lists.partition(batchQueryEmployeeSnapshotReq.getItemList(), 200)
                .forEach(partitionQueryEmployeeSnapReqs -> {
                    BatchQueryEmployeeSnapshotReq req = new BatchQueryEmployeeSnapshotReq();
                    req.setChainId(batchQueryEmployeeSnapshotReq.getChainId());
                    req.setItemList(partitionQueryEmployeeSnapReqs);
                    BatchQueryEmployeeSnapshotRsp partitionRsp = FeignClientRpcTemplate.dealRpcClientMethod("queryEmployeeSnap",
                            () -> client.queryEmployeeSnap(req), req);

                    rsp.getEmployeeList().addAll(Optional.ofNullable(partitionRsp).map(BatchQueryEmployeeSnapshotRsp::getEmployeeList).orElse(new ArrayList<>()));
                });

        return rsp;
    }


    public ChainEmployee queryEmployeeChainInfoByIds(QueryEmployeeChainInfoByIdsReq req) {
        AbcListPage<ChainEmployee> chainEmployeePage = FeignClientRpcTemplate.dealRpcClientMethod("queryEmployeeChainInfoByIds", () -> client.queryEmployeeChainInfoByIds(req), req);
        if (chainEmployeePage == null || org.springframework.util.CollectionUtils.isEmpty(chainEmployeePage.getRows())) {
            return null;
        }

        return chainEmployeePage.getRows().stream().findFirst().orElse(null);
    }

    /**
     * 新增/修改业务权限
     */
    public ClinicBusinessPermissionView upsertBusinessPermission(String chainId, String clinicId, String operatorId, ClinicBusinessPermissionItem clinicBusinessPermissionItem) {
        ClinicBusinessPermissionUpsertReq upsertReq = new ClinicBusinessPermissionUpsertReq();
        upsertReq.setChainId(chainId);
        upsertReq.setClinicId(clinicId);
        upsertReq.setOperatorId(operatorId);
        upsertReq.setKey(clinicBusinessPermissionItem.getKey());
        upsertReq.setBusinessId(clinicBusinessPermissionItem.getBusinessId());
        upsertReq.setPermissionValues(clinicBusinessPermissionItem.getPermissionValues());

        return FeignClientRpcTemplate.dealRpcClientMethod(
                "upsertBusinessPermission",
                true,
                () -> client.upsertBusinessPermission(upsertReq),
                upsertReq
        );
    }

    /**
     * 批量查询业务权限
     *
     * @param chainId       连锁id
     * @param clinicId      诊所 ID
     * @param permissionKey 权限键
     * @param businessIds   业务 ID
     * @return {@link AbcListPage }<{@link ClinicBusinessPermissionView }>
     */
    public List<ClinicBusinessPermissionValue> batchQueryBusinessPermissionValues(String chainId, String clinicId, List<String> permissionKey, List<String> businessIds) {
        BatchListClinicBusinessPermissionReq batchQueryReq = new BatchListClinicBusinessPermissionReq();
        batchQueryReq.setChainId(chainId);
        batchQueryReq.setClinicId(clinicId);
        batchQueryReq.setKeys(permissionKey);
        batchQueryReq.setBusinessIds(businessIds);
        return Optional.ofNullable(
                        FeignClientRpcTemplate.dealRpcClientMethod(
                                "batchQueryBusinessPermissions",
                                () -> client.batchList(batchQueryReq),
                                batchQueryReq
                        )
                ).map(AbcListPage::getRows).orElseGet(Collections::emptyList)
                .stream()
                .map(ClinicBusinessPermissionView::getPermissionValue)
                .filter(org.apache.commons.collections.CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 员工业务场景身份授权
     *
     * @param grantReq auth req
     * @return {@link AbcServiceResponseBody }<{@link EmployeeView }>
     */
    public Employee employeeBusAuthorization(EmployeeBusGrantRpcReq grantReq) {
        return FeignClientRpcTemplate.dealRpcClientMethod(
                "employeeBusAuthorization",
                true,
                () -> client.employeeBusAuthorization(grantReq),
                grantReq
        );
    }
}
