package cn.abcyun.cis.charge.service;

import cn.abcyun.bis.rpc.sdk.cis.model.charge.GoodsFeeType;
import cn.abcyun.bis.rpc.sdk.cis.model.charge.print.ChargeSheetAstResult;
import cn.abcyun.bis.rpc.sdk.cis.model.charge.print.ChargeSheetAstResultRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.BatchQueryEmployeeSnapshotReq;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Department;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Employee;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ;
import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.bis.rpc.sdk.cis.model.copharmacy.CoOutpatientSheetPrintInfoReq;
import cn.abcyun.bis.rpc.sdk.cis.model.copharmacy.CoOutpatientSheetPrintInfoRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.dispensing.DispensingFormPrintRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.dispensing.DispensingSheetOperationRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.examination.ExamSheetSimpleExtendView;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsItem;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.QueryGoodsInPharmacyByIdsRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.invoice.InvoiceView;
import cn.abcyun.bis.rpc.sdk.cis.model.outpatient.UsageInfo;
import cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientMemberBillDetailVO;
import cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientMemberInfoVO;
import cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientMemberTypeVO;
import cn.abcyun.bis.rpc.sdk.property.base.PropertyKey;
import cn.abcyun.bis.rpc.sdk.property.model.MedicalDocumentsTreatmentContent;
import cn.abcyun.bis.rpc.sdk.property.model.NurseExecute;
import cn.abcyun.bis.rpc.sdk.property.model.PrintMedicalDocumentsInfusion;
import cn.abcyun.bis.rpc.sdk.property.service.PropertyService;
import cn.abcyun.cis.charge.api.model.ExecuteSheetPrintExtendView;
import cn.abcyun.cis.charge.api.model.PrescriptionFormItemPrintView;
import cn.abcyun.cis.charge.api.model.PrescriptionFormPrintView;
import cn.abcyun.cis.charge.api.model.RegistrationInvoicePrintView;
import cn.abcyun.cis.charge.base.ChargeServiceError;
import cn.abcyun.cis.charge.base.ChargeServiceException;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.model.*;
import cn.abcyun.cis.charge.processor.ChargeSheetFeeProtocol;
import cn.abcyun.cis.charge.processor.SheetProcessor;
import cn.abcyun.cis.charge.processor.SheetProcessorDispatcher;
import cn.abcyun.cis.charge.repository.ChargeActionRepository;
import cn.abcyun.cis.charge.repository.ChargeOweSheetRepository;
import cn.abcyun.cis.charge.repository.ChargeTransactionRepository;
import cn.abcyun.cis.charge.service.dto.ChargeSheetView;
import cn.abcyun.cis.charge.service.dto.PrintMedicalDocumentsInfusionAndTreatmentProperty;
import cn.abcyun.cis.charge.service.dto.print.*;
import cn.abcyun.cis.charge.service.rpc.CisCoPharmacyService;
import cn.abcyun.cis.charge.service.rpc.CisDispensingService;
import cn.abcyun.cis.charge.service.rpc.CisScClinicService;
import cn.abcyun.cis.charge.service.rpc.CisScGoodsService;
import cn.abcyun.cis.charge.util.*;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.exception.ServiceInternalException;
import cn.abcyun.cis.commons.model.CisPatientInfo;
import cn.abcyun.cis.commons.model.ComposeType;
import cn.abcyun.cis.commons.rpc.crm.PatientInfo;
import cn.abcyun.cis.commons.rpc.outpatient.*;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrder;
import cn.abcyun.cis.commons.rpc.print.*;
import cn.abcyun.cis.commons.rpc.registration.RegistrationFormItem;
import cn.abcyun.cis.commons.rpc.registration.RegistrationSheet;
import cn.abcyun.cis.commons.rpc.shebao.QueryChargeSheetShebaoInfoReq;
import cn.abcyun.cis.commons.rpc.shebao.QueryChargeSheetShebaoInfoRsp;
import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.cis.commons.util.ListUtils;
import cn.abcyun.cis.commons.util.MathUtils;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.cis.core.db.UseReadOnlyDB;
import cn.abcyun.common.log.marker.AbcLogMarker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class ChargePrintService {

    private SheetProcessorService sheetProcessorService;

    private PropertyService propertyService;

    private ChargeSheetService mChargeSheetService;

    private PatientOrderService patientOrderService;

    private OutpatientService mOutpatientService;

    private CisScClinicService cisScClinicService;

    private CrmService crmService;

    private EmployeeService mEmployeeService;

    private OrganProductService mOrganProductService;

    private CisScGoodsService goodsSCService;

    private ChargeExecuteService mChargeExecuteService;

    private PatientService mPatientService;

    private ChargeTransactionRepository mChargeTransactionRepository;

    private ChargeActionRepository chargeActionRepository;

    private RegistrationService registrationService;

    private ShebaoService shebaoService;

    private PropertyProviderService propertyProviderService;

    private final ChargeOweSheetService chargeOweSheetService;

    private final ChargeOweSheetRepository chargeOweSheetRepository;

    private final OutpatientService outpatientService;

    private final ChargeSheetInvoiceService chargeSheetInvoiceService;

    private final ExaminationService examinationService;

    private final ChargeCooperationOrderService cooperationOrderService;

    private final CisCoPharmacyService coPharmacyService;

    private final CisDispensingService cisDispensingService;

    @Autowired
    public ChargePrintService(SheetProcessorService sheetProcessorService,
                              PropertyService propertyService,
                              ChargeSheetService mChargeSheetService,
                              PatientOrderService patientOrderService,
                              OutpatientService mOutpatientService,
                              CisScClinicService cisScClinicService,
                              CrmService crmService, EmployeeService mEmployeeService,
                              OrganProductService mOrganProductService,
                              CisScGoodsService goodsSCService,
                              ChargeExecuteService mChargeExecuteService,
                              PatientService mPatientService,
                              ChargeTransactionRepository mChargeTransactionRepository,
                              RegistrationService registrationService,
                              ShebaoService shebaoService,
                              ChargeActionRepository chargeActionRepository,
                              PropertyProviderService propertyProviderService,
                              ChargeOweSheetService chargeOweSheetService,
                              ChargeOweSheetRepository chargeOweSheetRepository,
                              OutpatientService outpatientService,
                              ChargeSheetInvoiceService chargeSheetInvoiceService,
                              ExaminationService examinationService,
                              ChargeCooperationOrderService cooperationOrderService,
                              CisCoPharmacyService coPharmacyService,
                              CisDispensingService cisDispensingService) {
        this.sheetProcessorService = sheetProcessorService;
        this.propertyService = propertyService;
        this.mChargeSheetService = mChargeSheetService;
        this.patientOrderService = patientOrderService;
        this.mOutpatientService = mOutpatientService;
        this.cisScClinicService = cisScClinicService;
        this.crmService = crmService;
        this.mEmployeeService = mEmployeeService;
        this.mOrganProductService = mOrganProductService;
        this.goodsSCService = goodsSCService;
        this.mChargeExecuteService = mChargeExecuteService;
        this.mPatientService = mPatientService;
        this.mChargeTransactionRepository = mChargeTransactionRepository;
        this.registrationService = registrationService;
        this.shebaoService = shebaoService;
        this.chargeActionRepository = chargeActionRepository;
        this.propertyProviderService = propertyProviderService;
        this.chargeOweSheetService = chargeOweSheetService;
        this.chargeOweSheetRepository = chargeOweSheetRepository;
        this.outpatientService = outpatientService;
        this.chargeSheetInvoiceService = chargeSheetInvoiceService;
        this.examinationService = examinationService;
        this.cooperationOrderService = cooperationOrderService;
        this.coPharmacyService = coPharmacyService;
        this.cisDispensingService = cisDispensingService;
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public ChargeSheetPrintView findChargeSheetPrintViewById(String chargeSheetId) {
        if (TextUtils.isEmpty(chargeSheetId)) {
            return null;
        }

        ChargeSheet chargeSheet = mChargeSheetService.findById(chargeSheetId);
        if (chargeSheet == null) {
            return null;
        }

        if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED || chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_CHARGED) {
            throw new ChargeServiceException(ChargeServiceError.PRINT_CHARGE_SHEET_STATUS_ERROR);
        }

        PatientOrder patientOrder = patientOrderService.findPatientOrder(chargeSheet.getPatientOrderId(), PatientOrderService.getAgeLockTime(chargeSheet));

        SheetProcessorDispatcher.ISheetProcessor sheetProcessor = SheetProcessorDispatcher.newSheetProcessor(chargeSheet);
        sheetProcessor.setSheetProcessorInfoProvider(sheetProcessorService);
        sheetProcessor.setPatientOrder(patientOrder);
        sheetProcessor.build();

//        updateChargeActionCreateByNameAndSort(chargeSheetView.getChargeActions());
        ChargeSheetPrintView chargeSheetPrintView = sheetProcessor.generatePrintView();

        bindInvoiceInfo(chargeSheetPrintView, chargeSheet);
        return chargeSheetPrintView;
    }

    private void bindInvoiceInfo(ChargeSheetPrintView chargeSheetPrintView, ChargeSheet chargeSheet) {
        if (chargeSheetPrintView == null || chargeSheet == null) {
            return;
        }

        boolean isBeenInvoice = Optional.ofNullable(chargeSheet.getAdditional())
                .map(ChargeSheetAdditional::isBeenInvoice)
                .orElse(false);

        if (!isBeenInvoice) {
            return;
        }

        //查询电子发票的url
        InvoiceView invoiceView = null;
        try {
            invoiceView = chargeSheetInvoiceService.getInvoiceByBusinessId(chargeSheet.getClinicId(), chargeSheet.getId(), chargeSheet.getType());
        } catch (Exception e) {
            log.error("getInvoiceByBusinessId error, chargeSheetId:{}, message: {}", chargeSheet.getId(), e.getMessage());
        }

        Optional.ofNullable(invoiceView)
                //.map(InvoiceView::getDigitalInvoice)
                //.map(InvoiceView.DigitalInvoice::getInvoiceUrl)
                .ifPresent(chargeSheetPrintView::setInvoiceView);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public ChargeSheetPrintView findRefundChargeSheetPrintViewById(String chargeSheetId) {
        if (TextUtils.isEmpty(chargeSheetId)) {
            return null;
        }

        ChargeSheet chargeSheet = mChargeSheetService.findById(chargeSheetId);

        if (chargeSheet == null) {
            return null;
        }

        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.REFUNDED && chargeSheet.getStatus() != Constants.ChargeSheetStatus.PART_REFUNDED) {
            throw new ChargeServiceException(ChargeServiceError.PRINT_CHARGE_SHEET_STATUS_ERROR);
        }

        PatientOrder patientOrder = patientOrderService.findPatientOrder(chargeSheet.getPatientOrderId(), PatientOrderService.getAgeLockTime(chargeSheet));

        SheetProcessorDispatcher.ISheetProcessor sheetProcessor = SheetProcessorDispatcher.newSheetProcessor(chargeSheet);
        sheetProcessor.setSheetProcessorInfoProvider(sheetProcessorService);
        sheetProcessor.setPatientOrder(patientOrder);
        sheetProcessor.build();

        return sheetProcessor.generateRefundPrintView();
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public ExecuteSheetPrintExtendView findExecuteSheetPrintViewById(String chargeSheetId, int type, String clinicId, String chainId, Integer hisType) {
        if (TextUtils.isEmpty(chargeSheetId)) {
            return null;
        }

        ChargeSheet chargeSheet = mChargeSheetService.findByIdAndChainId(chargeSheetId, chainId);
        ChargeSheet registrationChargeSheet = null;

        if (chargeSheet == null) {
            return null;
        }

        if (!TextUtils.isEmpty(chargeSheet.getRegistrationChargeSheetId())) {
            registrationChargeSheet = mChargeSheetService.findById(chargeSheet.getRegistrationChargeSheetId());
        }

        // 是否支持跨店执行，开单门店不管是否支持跨店执行都可以查看
        if (!Objects.equals(clinicId, chargeSheet.getClinicId())) {
            boolean enableCross = propertyService.getPropertyValueByKey(PropertyKey.NURSE_EXECUTE, chainId, NurseExecute.class).getEnableCross() == 1;
            if (!enableCross) {
                throw new ChargeServiceException(ChargeServiceError.CROSS_CLINIC_EXECUTE_UNAVAILABLE);
            }
        }

        OutpatientSheet outpatientSheet = null;
        String diagnosis = "";
        String syndrome = "";
        Instant diagnosedDate = chargeSheet.getCreated();
        if (Objects.equals(chargeSheet.getType(), ChargeSheet.Type.THERAPY)) {
            diagnosis = Optional.ofNullable(chargeSheet.getAdditional()).map(ChargeSheetAdditional::getDiagnosis).orElse(null);
        } else {
            outpatientSheet = mOutpatientService.findOutpatientSheet(chargeSheet.getPatientOrderId());
            if (Objects.nonNull(outpatientSheet)) {
                diagnosis = Objects.nonNull(outpatientSheet.getMedicalRecord()) ? outpatientSheet.getMedicalRecord().getDiagnosis() : diagnosis;
                syndrome = outpatientSheet.getMedicalRecord() != null ? outpatientSheet.getMedicalRecord().getSyndrome() : "";
                diagnosedDate = outpatientSheet.getDiagnosedDate();
            }
        }

        ExecuteSheetPrintExtendView executeSheetPrintView = new ExecuteSheetPrintExtendView();
        cn.abcyun.bis.rpc.sdk.cis.model.clinic.OrganPrintView organ = cisScClinicService.getOrganPrintInfo(chargeSheet.getClinicId());
        PatientOrder patientOrder = patientOrderService.findPatientOrder(chargeSheet.getPatientOrderId(), PatientOrderService.getAgeLockTime(chargeSheet));

        CisPatientInfo cisPatientInfo = getCisPatientInfo(chargeSheet.getPatientId(), chargeSheet.getChainId(), chargeSheet.getClinicId(), patientOrder);

        //查询收费单对应的欠费数据
        List<ChargeOweSheet> chargeOweSheets = new ArrayList<>();
        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED) {
            chargeOweSheets = chargeOweSheetService.findChargeOweSheetsByChargeSheetId(chargeSheet.getClinicId(), chargeSheet.getId());
        }
        ChargeSheetFeeProtocol.ChargeTransactionAndActionDto transactionAndActionDto = ChargeSheetFeeProtocol.getChargeTransactionAndChargeActionContainOwe(chargeSheet, chargeOweSheets);
        List<ChargeTransaction> chargeTransactions = transactionAndActionDto.getChargeTransactions();
        chargeTransactions.addAll(Optional.ofNullable(registrationChargeSheet).map(ChargeSheet::getChargeTransactions).orElse(new ArrayList<>()));

        //设置医保费别
        Integer shebaoChargeType = null;
        if (patientOrder != null) {
            shebaoChargeType = patientOrder.getShebaoChargeType();
            executeSheetPrintView.setShebaoCardInfo(patientOrder.getShebaoCardInfo());
        }
        executeSheetPrintView.setHealthCardNo(getHealthCardNo(patientOrder));
        executeSheetPrintView.setHealthCardPayLevel(ChargeUtils.generateHealthCardPayLevel(chargeTransactions, shebaoChargeType));

        executeSheetPrintView.setPatient(cisPatientInfo);
        executeSheetPrintView.setOrgan(PrintUtils.parseToOrganPrintView(organ));
        executeSheetPrintView.setDiagnosis(diagnosis);
        executeSheetPrintView.setExtendDiagnosisInfos(Optional.ofNullable(chargeSheet.getAdditional()).map(ChargeSheetAdditional::getExtendDiagnosisInfos).orElse(new ArrayList<>()));
        executeSheetPrintView.setSyndrome(syndrome);
        executeSheetPrintView.setDiagnosedDate(diagnosedDate);
        executeSheetPrintView.setChargedBy(chargeSheet.getChargedBy());

        Set<String> employeeIds = new HashSet<>();
        HandleUtils.isTrue(StringUtils.isNotEmpty(chargeSheet.getSellerId()))
                .handle(() -> employeeIds.add(chargeSheet.getSellerId()));
        HandleUtils.isTrue(StringUtils.isNotEmpty(chargeSheet.getChargedBy()))
                .handle(() -> employeeIds.add(chargeSheet.getChargedBy()));

        // 医生信息
        List<Employee> doctorEmployees = mEmployeeService.findEmployeeList(chainId, new ArrayList<>(employeeIds));
        HandleUtils.isTrue(CollectionUtils.isNotEmpty(doctorEmployees))
                .handle(() -> {
                    Map<String, Employee> employeeMap = ListUtils.toMap(doctorEmployees, Employee::getId);

                    if (StringUtils.isNotEmpty(chargeSheet.getSellerId())) {
                        Optional.ofNullable(employeeMap.getOrDefault(chargeSheet.getSellerId(), null))
                                .ifPresent(employee -> executeSheetPrintView.setSellerName(employee.getName()));
                    }

                    if (StringUtils.isNotEmpty(chargeSheet.getChargedBy())) {
                        Optional.ofNullable(employeeMap.getOrDefault(chargeSheet.getChargedBy(), null))
                                .ifPresent(employee -> {
                                    executeSheetPrintView.setChargedByHandSign(employee.getHandSign());
                                    executeSheetPrintView.setChargedByName(employee.getName());
                                });
                    }
                });

        if (StringUtils.isNotBlank(chargeSheet.getDoctorId())) {

            Instant doctorIdSnapTime = Optional.ofNullable(chargeSheet.getDiagnosedDate()).orElse(Instant.now());

            Optional.ofNullable(sheetProcessorService.getClinicProvider().queryEmployeeSnap(new BatchQueryEmployeeSnapshotReq()
                                    .setChainId(chainId)
                                    .addBusinessTimeEmployeeSnap(chargeSheet.getDoctorId(), doctorIdSnapTime))
                            .getEmployeeByBusTime(chargeSheet.getDoctorId(), doctorIdSnapTime))
                    .ifPresent(doctorEmployee -> {
                        executeSheetPrintView.setDoctorName(doctorEmployee.getName());
                        executeSheetPrintView.setDoctorSignImgUrl(doctorEmployee.getHandSign());
                    });

        }

        Department department = mOrganProductService.findDepartmentById(outpatientSheet != null ? outpatientSheet.getDepartmentId() : null);
        if (department != null) {
            executeSheetPrintView.setDepartmentName(department.getName());
        }

        if (patientOrder != null) {
            executeSheetPrintView.setPatientOrderNo(patientOrder.getNo());
        }

        if (StringUtils.isEmpty(executeSheetPrintView.getHealthCardNo())) {
            ChargeTransaction lastHealthCardChargeTransaction = chargeSheet.getChargeTransactions()
                    .stream()
                    .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.HEALTH_CARD)
                    .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                    .filter(chargeTransaction -> MathUtils.wrapBigDecimalOrZero(chargeTransaction.getAmount()).compareTo(BigDecimal.ZERO) > 0)
                    .filter(chargeTransaction -> chargeTransaction.getPaySource() == Constants.ChargeSource.CHARGE)
                    .min((a, b) -> -ObjectUtils.compare(a.getCreated(), b.getCreated()))
                    .orElse(null);

            executeSheetPrintView.setHealthCardNo(lastHealthCardChargeTransaction != null ? lastHealthCardChargeTransaction.getThirdPartyPayCardId() : null);
        }

        mChargeExecuteService.patchExecuteInfo(chargeSheet);

        executeSheetPrintView.setExecuteForms(generateExecuteFormPrintViews(chargeSheet, type, outpatientSheet, hisType));
        if (type == ExecuteSheetPrintView.Type.THERAPY) {
            executeSheetPrintView.setPrescriptionExternalForms(generatePrescriptionExternalFormPrintViews(chargeSheet, outpatientSheet));
        }
        //绑定审核调配核发相关信息
        if (type == ExecuteSheetPrintView.Type.WESTERN_INFUSION_MEDICINE) {
            bindAuditInfo(executeSheetPrintView, chargeSheet.getPatientOrderId());
        }
        bindExecuteFormItemsProductInfo(chargeSheet.getClinicId(), chargeSheet.getChainId(), executeSheetPrintView.getExecuteForms(), executeSheetPrintView.getPrescriptionExternalForms());

        return executeSheetPrintView;
    }

    private void bindAuditInfo(ExecuteSheetPrintExtendView executeSheetPrintView, String patientOrderId) {

        if (Objects.isNull(executeSheetPrintView) || org.apache.commons.lang.StringUtils.isBlank(patientOrderId)) {
            return;
        }

        //查询发药单审核记录信息
        DispensingSheetOperationRsp dispensingSheetOperation = cisDispensingService.findDispensingSheetOperation(patientOrderId);

        if (Objects.isNull(dispensingSheetOperation)) {
            return;
        }

        if (StringUtils.isNotBlank(dispensingSheetOperation.getDispensedByName())) {
            executeSheetPrintView.setDispensedBy(dispensingSheetOperation.getDispensedBy());
            executeSheetPrintView.setDispensedByName(dispensingSheetOperation.getDispensedByName());
            executeSheetPrintView.setDispensedByHandSign(dispensingSheetOperation.getDispensedByHandSign());
        }


        if (CollectionUtils.isNotEmpty(dispensingSheetOperation.getForms())) {
            DispensingFormPrintRsp dispensingFormPrintRsp = dispensingSheetOperation.getForms().get(0);
            /**
             * 发药服务返回的是最近的 调配人，找到第一个就可以了
             * */
            executeSheetPrintView.setCompoundId(dispensingFormPrintRsp.getCompoundByEmployeeId());
            executeSheetPrintView.setCompoundName(dispensingFormPrintRsp.getCompoundByUserName());
            executeSheetPrintView.setCompoundByHandSign(dispensingFormPrintRsp.getCompoundByHandSign());

            executeSheetPrintView.setAuditBy(dispensingFormPrintRsp.getAuditBy());
            executeSheetPrintView.setAuditName(dispensingFormPrintRsp.getAuditName());
            executeSheetPrintView.setAuditHandSign(dispensingFormPrintRsp.getAuditHandSign());
        }

    }

    private List<PrescriptionFormPrintView> generatePrescriptionExternalFormPrintViews(ChargeSheet chargeSheet, OutpatientSheet outpatientSheet) {

        if (Objects.isNull(outpatientSheet) || CollectionUtils.isEmpty(outpatientSheet.getPrescriptionForms()) || Objects.isNull(chargeSheet) || CollectionUtils.isEmpty(chargeSheet.getChargeForms())) {
            return new ArrayList<>();
        }

        //将收费单的外治处方转成map，同时item也转成map
        Map<String, ChargeForm> externalChargeForms = chargeSheet.getChargeForms().stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_EXTERNAL && chargeForm.getStatus() != Constants.ChargeFormStatus.REFUNDED)
                .filter(chargeForm -> StringUtils.isNotEmpty(chargeForm.getSourceFormId()) && CollectionUtils.isNotEmpty(chargeForm.getChargeFormItems()))
                .collect(Collectors.toMap(ChargeForm::getSourceFormId, Function.identity(), (a, b) -> a));


        if (MapUtils.isEmpty(externalChargeForms)) {
            return new ArrayList<>();
        }

        return outpatientSheet.getPrescriptionForms()
                .stream()
                .filter(prescriptionForm -> prescriptionForm.getType() == PrescriptionForm.TYPE_EXTERNAL)
                .map(prescriptionForm -> {
                    ChargeForm chargeForm = externalChargeForms.get(prescriptionForm.getId());

                    if (Objects.isNull(chargeForm) || CollectionUtils.isEmpty(chargeForm.getChargeFormItems())) {
                        return null;
                    }

                    Map<String, ChargeFormItem> chargeFormItemMap = chargeForm.getChargeFormItems()
                            .stream()
                            .filter(item -> item.getIsDeleted() == 0)
                            .filter(chargeFormItem -> StringUtils.isNotEmpty(chargeFormItem.getSourceFormItemId()) && (
                                    chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.UNCHARGED || chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED
                            ))
                            .collect(Collectors.toMap(ChargeFormItem::getSourceFormItemId, Function.identity(), (a, b) -> a));


                    PrescriptionFormPrintView prescriptionFormPrintView = new PrescriptionFormPrintView();
                    BeanUtils.copyProperties(prescriptionForm, prescriptionFormPrintView, "prescriptionFormItems", "processBagUnitCount");
                    prescriptionFormPrintView.setChargeStatus(chargeForm.getStatus());
                    prescriptionFormPrintView.setDoseCount(BigDecimal.ONE.intValue());
                    prescriptionFormPrintView.setPrescriptionFormItems(Optional.ofNullable(prescriptionForm.getPrescriptionFormItems())
                            .orElse(new ArrayList<>())
                            .stream()
                            .map(prescriptionFormItem -> generatePrescriptionFormItemPrintView(prescriptionFormItem, chargeFormItemMap.get(prescriptionFormItem.getId())))
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList())
                    );
                    return prescriptionFormPrintView;
                })
                .filter(Objects::nonNull)
                .filter(prescriptionFormPrintView -> CollectionUtils.isNotEmpty(prescriptionFormPrintView.getPrescriptionFormItems()))
                .peek(prescriptionFormPrintView -> {
                    prescriptionFormPrintView.setTotalPrice(prescriptionFormPrintView.getPrescriptionFormItems()
                            .stream()
                            .map(PrescriptionFormItemPrintView::getTotalPrice)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add));
                })
                .collect(Collectors.toList());
    }

    private PrescriptionFormItemPrintView generatePrescriptionFormItemPrintView(PrescriptionFormItem prescriptionFormItem, ChargeFormItem chargeFormItem) {

        if (Objects.isNull(chargeFormItem) || Objects.isNull(prescriptionFormItem)) {
            return null;
        }

        BigDecimal unitCount = cn.abcyun.cis.charge.util.MathUtils.max(MathUtils.wrapBigDecimalSubtract(chargeFormItem.getUnitCount(), chargeFormItem.getRefundUnitCount()), BigDecimal.ZERO);

        if (MathUtils.wrapBigDecimalCompare(unitCount, BigDecimal.ZERO) <= 0) {
            return null;
        }

        PrescriptionFormItemPrintView prescriptionFormItemPrintView = new PrescriptionFormItemPrintView();
        BeanUtils.copyProperties(prescriptionFormItem, prescriptionFormItemPrintView);
        prescriptionFormItemPrintView.setAcupoints(prescriptionFormItem.getAcupoints());
        prescriptionFormItemPrintView.setExternalGoodsItems(prescriptionFormItem.getExternalGoodsItems());
        prescriptionFormItemPrintView.setPayType(prescriptionFormItem.getPayType());
        prescriptionFormItemPrintView.setChargeStatus(chargeFormItem.getStatus());
        prescriptionFormItemPrintView.setUnitCount(unitCount);
        prescriptionFormItemPrintView.setExecutedCount(chargeFormItem.getExecutedUnitCount());
        prescriptionFormItemPrintView.setNeedExecutive(chargeFormItem.getNeedExecutive());
        prescriptionFormItemPrintView.setUnitPrice(chargeFormItem.getUnitPrice());
        prescriptionFormItemPrintView.setTotalPrice(prescriptionFormItem.getTotalPrice());
        if (chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED) {
            prescriptionFormItemPrintView.setTotalPrice(MathUtils.wrapBigDecimalAdd(MathUtils.calculateTotalPrice(chargeFormItem.getUnitPrice(), prescriptionFormItemPrintView.getUnitCount(), chargeFormItem.getDoseCount(), 2), chargeFormItem.getFractionPrice()));
        }
        if (MathUtils.wrapBigDecimalCompare(prescriptionFormItemPrintView.getExecutedCount(), prescriptionFormItemPrintView.getUnitCount()) >= 0) {
            prescriptionFormItemPrintView.setExecutedTotalPrice(prescriptionFormItemPrintView.getTotalPrice());
        } else {
            prescriptionFormItemPrintView.setExecutedTotalPrice(MathUtils.calculateTotalPrice(prescriptionFormItemPrintView.getUnitPrice(), prescriptionFormItemPrintView.getExecutedCount(), 2));
        }
        return prescriptionFormItemPrintView;
    }


    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public RegistrationSheetPrintView findRegistrationSheetPrintViewByPatientOrderId(String patientOrderId, String clinicId) throws ChargeServiceException, ServiceInternalException {
        if (TextUtils.isEmpty(patientOrderId)) {
            return null;
        }

        List<ChargeSheet> chargeSheets = mChargeSheetService.findAllByPatientOrderId(patientOrderId);
        ChargeSheet chargeSheet = ChargeUtils.pickRegistrationChargeSheet(chargeSheets);
        if (chargeSheet == null) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "patientOrderId:{} not registrationChargeSheet existed...", patientOrderId);
            return null;
        }

        ChargeFormItem registrationChargeFormItem = ChargeUtils.getChargeSheetItems(chargeSheet)
                .stream()
                .filter(chargeFormItem -> chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.REFUNDED)
                .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.REGISTRATION)
                .findFirst()
                .orElse(null);
        if (registrationChargeFormItem == null) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "patientOrderId:{} not registrationChargeFormItem existed...", patientOrderId);
            return null;
        }

        RegistrationFormItem registrationFormItem = JsonUtils.readValue(registrationChargeFormItem.getProductSnapshot(), RegistrationFormItem.class);
        if (registrationFormItem == null) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "patientOrderId:{} not registrationFormItem existed...", patientOrderId);
            return null;
        }

        PatientOrder patientOrder = patientOrderService.findPatientOrder(patientOrderId, PatientOrderService.getAgeLockTime(chargeSheet));
        CisPatientInfo patientInfo = ChargeUtils.parseCisPatientInfoFromPatientOrder(patientOrder);
        cn.abcyun.bis.rpc.sdk.cis.model.clinic.OrganPrintView organ = cisScClinicService.getOrganPrintInfo(chargeSheet.getClinicId());

        Department department = mOrganProductService.findDepartmentById(registrationFormItem.getDepartmentId());
        String departmentName = registrationFormItem.getDepartmentName();
        if (department != null) {
            departmentName = ObjectUtils.equals(department.getIsDefault(), 1) ? "" : department.getName();
        }
        String doctorName = registrationFormItem.getDoctorName();
        if (TextUtils.isEmpty(doctorName)) {
            doctorName = "";
        }

        RegistrationSheetPrintView registrationSheetPrintView = new RegistrationSheetPrintView();
        registrationSheetPrintView.setPatient(patientInfo);
        registrationSheetPrintView.setOrgan(organ != null ? new OrganPrintView(organ.getId(), organ.getName(), organ.getName(), organ.getAddressDetail(), organ.getContactPhone()) : null);
        Optional.ofNullable(registrationSheetPrintView.getOrgan()).ifPresent(organPrintView -> organPrintView.setHisType(organ.getHisType()));
        registrationSheetPrintView.setDepartmentName(departmentName);
        registrationSheetPrintView.setDoctorName(doctorName);
        registrationSheetPrintView.setReserveShift(registrationFormItem.getReserveShift());
        registrationSheetPrintView.setRegistrationFee(registrationChargeFormItem.getUnitPrice());
        registrationSheetPrintView.setTotalFee(MathUtils.calculateTotalPrice(registrationChargeFormItem.getUnitPrice(), registrationChargeFormItem.getUnitCount(), 2));
        registrationSheetPrintView.setDiscountFee(registrationChargeFormItem.getDiscountPrice());
        registrationSheetPrintView.setNetIncomeFee(MathUtils.calculateTotalPrice(registrationChargeFormItem.getUnitPrice(), registrationChargeFormItem.getUnitCount(), 2)
                .add(MathUtils.wrapBigDecimalOrZero(registrationChargeFormItem.getDiscountPrice())));
        registrationSheetPrintView.setChargeStatus(registrationChargeFormItem.getStatus());
        registrationSheetPrintView.setIsReserved(registrationFormItem.getIsReserved());
        if (ObjectUtils.equals(registrationFormItem.getIsReserved(), 1)) {
            registrationSheetPrintView.setCreated(null);
        } else {
            registrationSheetPrintView.setCreated(chargeSheet.getCreated());
        }
        String orderNo = String.format("%02d", registrationFormItem.getOrderNo());
        LocalTime orderNoTime = LocalDateTime.ofInstant(chargeSheet.getCreated(), ZoneId.systemDefault()).toLocalTime();
        if (!TextUtils.isEmpty(registrationFormItem.getReserveDate())) {
            try {
                LocalDate reserveDate = LocalDate.parse(registrationFormItem.getReserveDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                registrationSheetPrintView.setReserveDate(String.format("%d月%d日", reserveDate.getMonthValue(), reserveDate.getDayOfMonth()));
                registrationSheetPrintView.setReserveStart(registrationFormItem.getReserveStart());
                registrationSheetPrintView.setReserveEnd(registrationFormItem.getReserveEnd());
                orderNoTime = LocalTime.parse(registrationFormItem.getReserveStart(), DateTimeFormatter.ofPattern("HH:mm"));
            } catch (Exception e) {
                log.error("parse reserveDate error", e);
            }
        }

        if (registrationSheetPrintView.getReserveShift() == null || registrationSheetPrintView.getReserveShift() < 1) {
            if (orderNoTime.getHour() < 12) {
                registrationSheetPrintView.setReserveShift(1);
            } else if (orderNoTime.getHour() < 18) {
                registrationSheetPrintView.setReserveShift(2);
            } else {
                registrationSheetPrintView.setReserveShift(3);
            }
        }
        registrationSheetPrintView.setOrderNo(orderNo);

        // 如果有会员卡支付，找到最后一次会员卡支付的余额
        if (chargeSheet.getChargeTransactions() != null && registrationChargeFormItem.getPaySource() == Constants.ChargeSource.REGISTRATION) {

            List<ChargeTransaction> registrationChargeTransactions = chargeSheet.getChargeTransactions()
                    .stream()
                    .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                    .filter(chargeTransaction -> chargeTransaction.getPaySource() == Constants.ChargeSource.REGISTRATION)
                    .filter(chargeTransaction -> MathUtils.wrapBigDecimalOrZero(chargeTransaction.getAmount()).compareTo(BigDecimal.ZERO) > 0)
                    .collect(Collectors.toList());


            ChargeTransaction firstMemberCardChargeTransaction = registrationChargeTransactions
                    .stream()
                    .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.MEMBER_CARD)
                    .filter(chargeTransaction -> MathUtils.wrapBigDecimalOrZero(chargeTransaction.getAmount()).compareTo(BigDecimal.ZERO) > 0)
                    .min((a, b) -> -ObjectUtils.compare(b.getCreated(), a.getCreated()))
                    .orElse(null);

            ChargeTransaction lastMemberCardChargeTransaction = registrationChargeTransactions
                    .stream()
                    .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.MEMBER_CARD)
                    .filter(chargeTransaction -> MathUtils.wrapBigDecimalOrZero(chargeTransaction.getAmount()).compareTo(BigDecimal.ZERO) > 0)
                    .min((a, b) -> -ObjectUtils.compare(a.getCreated(), b.getCreated()))
                    .orElse(null);

            ChargeTransaction firstHealthCardChargeTransaction = registrationChargeTransactions
                    .stream()
                    .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.HEALTH_CARD)
                    .filter(chargeTransaction -> MathUtils.wrapBigDecimalOrZero(chargeTransaction.getAmount()).compareTo(BigDecimal.ZERO) > 0)
                    .min((a, b) -> -ObjectUtils.compare(b.getCreated(), a.getCreated()))
                    .orElse(null);

            ChargeTransaction lastHealthCardChargeTransaction = registrationChargeTransactions
                    .stream()
                    .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.HEALTH_CARD)
                    .filter(chargeTransaction -> MathUtils.wrapBigDecimalOrZero(chargeTransaction.getAmount()).compareTo(BigDecimal.ZERO) > 0)
                    .min((a, b) -> -ObjectUtils.compare(a.getCreated(), b.getCreated()))
                    .orElse(null);

            PatientInfo memberPatientInfo = mPatientService.findPatientInfoById(chargeSheet.getChainId(), chargeSheet.getClinicId(), chargeSheet.getMemberId(), false, false);

            if (memberPatientInfo != null && lastMemberCardChargeTransaction != null && firstMemberCardChargeTransaction != null) {
                registrationSheetPrintView.setMemberCardBalance(lastMemberCardChargeTransaction.getThirdPartyPayCardBalance());

                registrationSheetPrintView.setMemberCardBeginningBalance(MathUtils.wrapBigDecimalAdd(firstMemberCardChargeTransaction.getThirdPartyPayCardBalance(),
                        firstMemberCardChargeTransaction.getAmount()));

                registrationSheetPrintView.setMemberCardMobile(memberPatientInfo.getMobile());
            }

            if (lastHealthCardChargeTransaction != null && firstHealthCardChargeTransaction != null) {
                registrationSheetPrintView.setHealthCardBalance(lastHealthCardChargeTransaction.getThirdPartyPayCardBalance());

                registrationSheetPrintView.setHealthCardBeginningBalance(MathUtils.wrapBigDecimalAdd(firstHealthCardChargeTransaction.getThirdPartyPayCardBalance(),
                        firstHealthCardChargeTransaction.getAmount()));

                registrationSheetPrintView.setHealthCardNo(lastHealthCardChargeTransaction.getThirdPartyPayCardId());
            }

            Map<Integer, ChargeTransactionPrintView> chargeTransactionPrintViewMap = registrationChargeTransactions
                    .stream()
                    .collect(Collectors.toMap(ChargeTransaction::getPayMode, chargeTransaction -> {
                        ChargeTransactionPrintView chargeTransactionPrintView = new ChargeTransactionPrintView();
                        chargeTransactionPrintView.setAmount(chargeTransaction.getAmount());
                        chargeTransactionPrintView.setPayMode(chargeTransaction.getPayMode());
                        return chargeTransactionPrintView;
                    }, (a, b) -> {
                        a.setAmount(MathUtils.wrapBigDecimalAdd(a.getAmount(), b.getAmount()));
                        return a;
                    }));
            registrationSheetPrintView.setChargeTransactions(new ArrayList<>(chargeTransactionPrintViewMap.values()));
        }

        // 收费员信息
        Employee chargedByEmployee = mEmployeeService.findById(chargeSheet.getChainId(), chargeSheet.getChargedBy());
        if (chargedByEmployee != null) {
            registrationSheetPrintView.setChargedByName(chargedByEmployee.getName());
        }
        registrationSheetPrintView.setChargedTime(chargeSheet.getChargedTime());
        return registrationSheetPrintView;
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public MemberRechargePrintView findMemberRechargePrintViewById(String chainId, String transactionId) throws ServiceInternalException {
        if (TextUtils.isEmpty(transactionId)) {
            return null;
        }

        PatientMemberBillDetailVO memberCardRechargeTransaction = mPatientService.findMemberCardRechargeTransaction(chainId, transactionId);
        if (memberCardRechargeTransaction == null) {
            return null;
        }

        ChargeTransaction chargeTransaction = mChargeTransactionRepository.findByIdAndAmountGreaterThanAndIsDeleted(memberCardRechargeTransaction.getChargeTransactionId(), BigDecimal.ZERO, 0);

        if (chargeTransaction == null) {
            return null;
        }

        ChargeAction chargeAction = null;
        if (chargeTransaction != null && StringUtils.isNotEmpty(chargeTransaction.getChargeActionId())) {
            chargeAction = chargeActionRepository.findById(chargeTransaction.getChargeActionId()).orElse(null);
        }

        Map<Long, Integer> payModeTypes = ChargeSheetFeeProtocol.getPayModeTypes(this.sheetProcessorService, chainId);
        Map<String, ChargeAction.PayActionInfo> payModeNameMap = ChargeUtils.generatePayModeNameMap(Optional.ofNullable(chargeAction).map(c -> Collections.singletonList(c)).orElse(new ArrayList<>()), payModeTypes);
        MemberRechargePrintView printView = new MemberRechargePrintView();
        PatientMemberBillDetailVO.Creator creator = memberCardRechargeTransaction.getCreator();
        PatientMemberInfoVO memberInfo = memberCardRechargeTransaction.getMember();
        PatientMemberTypeVO memberType = memberInfo != null ? memberInfo.getMemberType() : null;

        PatientInfo patientInfo = mPatientService.findPatientInfoById(chainId, memberCardRechargeTransaction.getClinicId(), memberCardRechargeTransaction.getPatientId(), false, false);
        cn.abcyun.bis.rpc.sdk.cis.model.clinic.OrganPrintView organ = cisScClinicService.getOrganPrintInfo(memberCardRechargeTransaction.getClinicId());

        ChargeAction.PayActionInfo payActionInfo = chargeTransaction != null
                ? payModeNameMap.getOrDefault(ChargeUtils.generatePayModeNameKey(chargeTransaction.getPayMode(), chargeTransaction.getPaySubMode(), payModeTypes.getOrDefault(Long.valueOf(chargeTransaction.getPayMode()), Constants.ChargePayModeType.UN_KNOW), chargeTransaction.getThirdPartyPayCardId()), new ChargeAction.PayActionInfo())
                : new ChargeAction.PayActionInfo();

        printView.setPatient(patientInfo);
        printView.setOrgan(organ != null ? new OrganPrintView(organ.getId(), organ.getName(), organ.getName(), organ.getAddressDetail(), organ.getContactPhone()) : null);
        Optional.ofNullable(printView.getOrgan()).ifPresent(organPrintView -> organPrintView.setHisType(organ.getHisType()));
        printView.setPresent(MathUtils.setScaleTwo(memberCardRechargeTransaction.getPresent()));
        printView.setPrincipal(MathUtils.setScaleTwo(memberCardRechargeTransaction.getPrincipal()));
        printView.setMemberCardBalance(MathUtils.setScaleTwo(MathUtils.wrapBigDecimalAdd(memberCardRechargeTransaction.getPresentBalance(),
                memberCardRechargeTransaction.getPrincipalBalance())));
        printView.setNetIncomeFee(MathUtils.setScaleTwo(MathUtils.wrapBigDecimalOrZero(memberCardRechargeTransaction.getPrincipal())));
        printView.setPayMode(chargeTransaction.getPayMode());
        printView.setPaySubMode(chargeTransaction.getPaySubMode());
        printView.setChargedByName(creator != null ? creator.getName() : null);
        printView.setMemberType(memberType != null ? memberType.getName() : null);
        printView.setMemberCardMobile(patientInfo != null ? patientInfo.getMobile() : null);
        printView.setPayModeName(payActionInfo.getPayModeName());
        printView.setPaySubModeName(payActionInfo.getPaySubModeName());
        printView.setChargeComment(memberCardRechargeTransaction.getChargeComment());
        printView.setChargedTime(chargeTransaction != null ? chargeTransaction.getCreated() : Instant.now());
        return printView;
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public ExecuteSheetPrintView findExecuteSheetPrintViewByPatientOrderId(String patientOrderId, int type, Integer hisType) throws ServiceInternalException {
        if (TextUtils.isEmpty(patientOrderId)) {
            return null;
        }

        List<ChargeSheet> chargeSheets = mChargeSheetService.findAllByPatientOrderId(patientOrderId);
        if (chargeSheets.size() == 0) {
            return null;
        }

        ChargeSheet firstChargeSheet = chargeSheets.get(0);
        OutpatientSheet outpatientSheet = mOutpatientService.findOutpatientSheet(patientOrderId);

        cn.abcyun.bis.rpc.sdk.cis.model.clinic.OrganPrintView organ = cisScClinicService.getOrganPrintInfo(firstChargeSheet.getClinicId());

        PatientOrder patientOrder = patientOrderService.findPatientOrder(patientOrderId, null);
        CisPatientInfo patientInfo = ChargeUtils.parseCisPatientInfoFromPatientOrder(patientOrder);

        ExecuteSheetPrintView executeSheetPrintView = new ExecuteSheetPrintView();
        executeSheetPrintView.setPatient(patientInfo);
        executeSheetPrintView.setOrgan(PrintUtils.parseToOrganPrintView(organ));
        executeSheetPrintView.setDiagnosis(outpatientSheet != null && outpatientSheet.getMedicalRecord() != null ? outpatientSheet.getMedicalRecord().getDiagnosis() : null);
        executeSheetPrintView.setSyndrome(outpatientSheet != null && outpatientSheet.getMedicalRecord() != null ? outpatientSheet.getMedicalRecord().getSyndrome() : null);
        executeSheetPrintView.setDiagnosedDate(firstChargeSheet.getDiagnosedDate() != null ? firstChargeSheet.getDiagnosedDate() : firstChargeSheet.getCreated());

        // 医生信息
        if (StringUtils.isNotBlank(firstChargeSheet.getDoctorId())) {
            Instant doctorIdSnapTime = Optional.ofNullable(firstChargeSheet.getDiagnosedDate()).orElse(Instant.now());
            Optional.ofNullable(cisScClinicService.queryEmployeeSnap(new BatchQueryEmployeeSnapshotReq()
                            .setChainId(firstChargeSheet.getChainId())
                            .addBusinessTimeEmployeeSnap(firstChargeSheet.getDoctorId(), doctorIdSnapTime)
                    ).getEmployeeByBusTime(firstChargeSheet.getDoctorId(), doctorIdSnapTime))
                    .ifPresent(employee -> {
                        executeSheetPrintView.setDoctorName(employee.getName());
                        executeSheetPrintView.setDoctorSignImgUrl(employee.getHandSign());
                    });
        }

        Department department = mOrganProductService.findDepartmentById(outpatientSheet != null ? outpatientSheet.getDepartmentId() : null);
        if (department != null) {
            executeSheetPrintView.setDepartmentName(department.getName());
        }

        if (patientOrder != null) {
            executeSheetPrintView.setPatientOrderNo(patientOrder.getNo());
        }

        executeSheetPrintView.setExecuteForms(chargeSheets.stream().flatMap(chargeSheet -> generateExecuteFormPrintViews(chargeSheet, type, outpatientSheet, hisType).stream()).collect(Collectors.toList()));

        return executeSheetPrintView;
    }


    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public PatientTagPrintView findPatientTagPrintViewByPatientOrderId(String patientOrderId, String clinicId, String chainId) throws NotFoundException, ServiceInternalException {

        String doctorName = "";
        List<ChargeSheet> chargeSheets = mChargeSheetService.findAllByPatientOrderId(patientOrderId);

        ChargeSheet chargeSheet = Optional.ofNullable(chargeSheets)
                .orElse(new ArrayList<>())
                .stream()
                .filter(c -> c.getType() == ChargeSheet.Type.OUTPATIENT)
                .findFirst()
                .orElse(null);

        if (Objects.nonNull(chargeSheet)) {
            String chargeSheetDoctorId = chargeSheet.getChargeSheetDoctorId();
            if (StringUtils.isNotBlank(chargeSheetDoctorId)) {
                Instant doctorIdSnapTime = Optional.ofNullable(chargeSheet.getDiagnosedDate()).orElse(Instant.now());
                doctorName = Optional.ofNullable(cisScClinicService.queryEmployeeSnap(new BatchQueryEmployeeSnapshotReq()
                                        .setChainId(chainId)
                                        .addBusinessTimeEmployeeSnap(chargeSheetDoctorId, doctorIdSnapTime))
                                .getEmployeeByBusTime(chargeSheetDoctorId, doctorIdSnapTime))
                        .map(Employee::getName)
                        .orElse(null);
            }
        } else {
            chargeSheet = ChargeUtils.pickRegistrationChargeSheet(chargeSheets);
            if (chargeSheet != null) {

                ChargeFormItem registrationChargeFormItem = ChargeUtils.getChargeSheetItems(chargeSheet)
                        .stream()
                        .filter(chargeFormItem -> chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.REFUNDED)
                        .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.REGISTRATION)
                        .findFirst()
                        .orElse(null);

                if (registrationChargeFormItem != null) {

                    RegistrationFormItem registrationFormItem = JsonUtils.readValue(registrationChargeFormItem.getProductSnapshot(), RegistrationFormItem.class);
                    if (registrationFormItem == null) {
                        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "patientOrderId:{} not registrationFormItem existed...", patientOrderId);
                        return null;
                    }

                    doctorName = registrationFormItem.getDoctorName();
                }

            } else {
                chargeSheet = !CollectionUtils.isEmpty(chargeSheets) ? chargeSheets.get(0) : null;
            }
        }

        if (chargeSheet == null) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeSheet is null patientOrderId: {}", patientOrderId);
            throw new NotFoundException();
        }

        Instant ageLockTime = null;
        //如果只有挂号单，说明还没签到，用当前时间去计算年龄
        if (chargeSheet.getType() == ChargeSheet.Type.REGISTRATION) {
            ageLockTime = Instant.now();
        } else {
            ageLockTime = PatientOrderService.getAgeLockTime(chargeSheet);
        }

        PatientOrder patientOrder = patientOrderService.findPatientOrder(patientOrderId, ageLockTime);
        CisPatientInfo patientInfo = ChargeUtils.parseCisPatientInfoFromPatientOrder(patientOrder);
        if (patientInfo == null) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "patientInfo is null, patientId: {}", chargeSheet.getPatientId());
            throw new NotFoundException();
        }
        cn.abcyun.bis.rpc.sdk.cis.model.clinic.OrganPrintView organ = cisScClinicService.getOrganPrintInfo(chargeSheet.getClinicId());

        PatientTagPrintView patientTagPrintView = new PatientTagPrintView();
        patientTagPrintView.setDoctorName(doctorName);
        patientTagPrintView.setPatientName(patientInfo.getName());
        patientTagPrintView.setOrganName(organ != null ? organ.getName() : "");
        patientTagPrintView.setAge(patientInfo.getAge());
        patientTagPrintView.setSex(patientInfo.getSex());
        patientTagPrintView.setMobile(patientInfo.getMobile());
        patientTagPrintView.setBirthday(patientInfo.getBirthday());
        patientTagPrintView.setPatientOrder(patientOrder);

        return patientTagPrintView;
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public RegistrationInvoicePrintView getRegistrationInvoicePrintViewById(String chargeSheetId, String clinicId) {

        ChargeSheet chargeSheet = mChargeSheetService.findByIdAndClinicId(chargeSheetId, clinicId);

        if (chargeSheet == null) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeSheet is null, chargeSheetId: {}", chargeSheetId);
            throw new NotFoundException();
        }

        if (chargeSheet.getType() != ChargeSheet.Type.REGISTRATION) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "收费单不是挂号的收费单，不能打印，chargeSheetId: {}", chargeSheet.getId());
            throw new ChargeServiceException(ChargeServiceError.PRINT_CHARGE_SHEET_TYPE_ERROR);
        }

        if (!ChargeUtils.isContainsRegistration(chargeSheet)) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "收费单为包含挂号费收费项， chargeSheetId: {}", chargeSheetId);
            throw new ChargeServiceException(ChargeServiceError.PRINT_CHARGE_SHEET_STATUS_ERROR);
        }

        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.CHARGED && chargeSheet.getStatus() != Constants.ChargeSheetStatus.PART_REFUNDED) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "收费单状态不支持打印挂号发票");
            throw new ChargeServiceException(ChargeServiceError.PRINT_CHARGE_SHEET_STATUS_ERROR);
        }

        return generateRegistrationInvoicePrintView(chargeSheet);
    }

    public RegistrationInvoicePrintView generateRegistrationInvoicePrintView(ChargeSheet chargeSheet) {
        ChargeFormItem registrationChargeFormItem = ChargeUtils.getChargeSheetItems(chargeSheet)
                .stream()
                .filter(chargeFormItem -> chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.REFUNDED)
                .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.REGISTRATION)
                .findFirst()
                .orElse(null);

        if (registrationChargeFormItem == null) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeSheetId:{} not registrationChargeFormItem existed...", chargeSheet.getId());
            return null;
        }

        RegistrationSheet registrationSheet = registrationService.findRegistrationSheet(chargeSheet.getPatientOrderId());

        if (registrationSheet == null) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "not existed registrationSheet for patientOrderId:{}", chargeSheet.getId());
            return null;
        }

        RegistrationFormItem registrationFormItem = registrationSheet.getRegistrationForms().stream()
                .filter(registrationForm -> registrationForm.getRegistrationFormItems() != null)
                .flatMap(registrationForm -> registrationForm.getRegistrationFormItems().stream())
                .findFirst().orElse(null);


        PatientOrder patientOrder = patientOrderService.findPatientOrder(chargeSheet.getPatientOrderId(), PatientOrderService.getAgeLockTime(chargeSheet));
        CisPatientInfo patientInfo = ChargeUtils.parseCisPatientInfoFromPatientOrder(patientOrder);
        Employee employee = mEmployeeService.findById(chargeSheet.getChainId(), chargeSheet.getChargedBy());

        QueryChargeSheetShebaoInfoReq req = new QueryChargeSheetShebaoInfoReq();
        req.setChainId(chargeSheet.getChainId());
        req.setClinicId(chargeSheet.getClinicId());
        req.setDoctorId(chargeSheet.getDoctorId());
        req.setChargeSheetId(chargeSheet.getId());
        req.setIsCharged((chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED || chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_CHARGED) ? QueryChargeSheetShebaoInfoReq.IsCharged.NOT : QueryChargeSheetShebaoInfoReq.IsCharged.CHARGED);
        req.setSheetReceivableFee(chargeSheet.calculateNetIncomeFeeForShebaoPrint());
        QueryChargeSheetShebaoInfoReq.GoodsItem reqGoodsItem = new QueryChargeSheetShebaoInfoReq.GoodsItem();
        reqGoodsItem.setId(registrationChargeFormItem.getProductId());
        reqGoodsItem.setChargeFormItemId(registrationChargeFormItem.getId());
        reqGoodsItem.setType(registrationChargeFormItem.getProductType());
        reqGoodsItem.setSubType(registrationChargeFormItem.getProductSubType());
        reqGoodsItem.setDetItemFeeSumamt(registrationChargeFormItem.calculateDiscountedPrice(chargeSheet.getChargeVersion()));
        req.setGoodsItems(Collections.singletonList(reqGoodsItem));
        QueryChargeSheetShebaoInfoRsp rsp = shebaoService.queryChargeSheetShebaoInfo(req);

        Map<String, QueryChargeSheetShebaoInfoRsp.GoodsItem> goodsItemMap = PrintUtils.convertShebaoGoodsItemMap(Optional.ofNullable(rsp)
                .map(QueryChargeSheetShebaoInfoRsp::getGoodsItems)
                .orElse(Collections.emptyList())
        );

        RegistrationInvoicePrintView registrationInvoicePrintView = new RegistrationInvoicePrintView();

        if (patientOrder != null) {
            String no = String.format("%08d", patientOrder.getNo());
            registrationInvoicePrintView.setNo(String.format("No.%s", no));
        }

        registrationInvoicePrintView.setChargeDate(DateUtils.convertInstantToString(chargeSheet.getChargedTime(), "yyyy-MM-dd"));
        registrationInvoicePrintView.setName(Optional.ofNullable(patientInfo).map(CisPatientInfo::getName).orElse(""));
        BigDecimal registrationFee = BigDecimal.ZERO;
        if (chargeSheet.getType() == ChargeSheet.Type.REGISTRATION) {
            registrationFee = chargeSheet.getReceivedFee();
        } else {
            registrationFee = MathUtils.wrapBigDecimalAdd(registrationChargeFormItem.getTotalPrice(), registrationChargeFormItem.getDiscountPrice());
        }
        registrationInvoicePrintView.setRegistrationFee(registrationFee);
        registrationInvoicePrintView.setDepartmentName(registrationFormItem.getDepartmentName());
        registrationInvoicePrintView.setOrderNo(StringUtils.isNotEmpty(registrationFormItem.getOrderNoStr()) ? registrationFormItem.getOrderNoStr() + "号" : "");
        if (ObjectUtils.equals(Constants.ANONYMOUS_PATIENT_ID, chargeSheet.getChargedBy())) {
            registrationInvoicePrintView.setChargedByName("自助支付");
        } else {
            registrationInvoicePrintView.setChargedByName(Optional.ofNullable(employee).map(Employee::getName).orElse(""));
        }

        Optional.ofNullable(Optional.ofNullable(goodsItemMap.getOrDefault(registrationChargeFormItem.getId(), null))
                        .orElse(goodsItemMap.getOrDefault(registrationChargeFormItem.getProductId(), null)))
                .ifPresent(goodsItem -> registrationInvoicePrintView.setSocialCode(goodsItem.getSocialCode()));

        //查询organ信息
        registrationInvoicePrintView.setOrgan(cisScClinicService.getOrganPrintInfo(chargeSheet.getClinicId()));

        BigDecimal healthCardPaymentFee = Optional.ofNullable(chargeSheet.getChargeTransactions()).orElse(new ArrayList<>())
                .stream()
                .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.HEALTH_CARD)
                .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                .map(ChargeTransaction::getAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        registrationInvoicePrintView.setPersonalPaymentFee(MathUtils.wrapBigDecimalSubtract(registrationFee, healthCardPaymentFee));
        registrationInvoicePrintView.setShebaoPayment(Optional.ofNullable(rsp).map(QueryChargeSheetShebaoInfoRsp::getShebaoPayment).orElse(null));
        return registrationInvoicePrintView;
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public cn.abcyun.bis.rpc.sdk.cis.model.outpatient.print.ExaminationSheetPrintView findExaminationSheetPrintViewById(String chargeSheetId, Integer type, String clinicId) {
        if (TextUtils.isEmpty(chargeSheetId)) {
            return null;
        }

        ChargeSheet chargeSheet = mChargeSheetService.findByIdAndClinicId(chargeSheetId, clinicId);
        ChargeSheet registrationChargeSheet = null;
        List<ChargeSheet> allChargeSheets = new ArrayList<>();

        if (chargeSheet == null) {
            return null;
        }

        allChargeSheets.add(chargeSheet);

        if (!TextUtils.isEmpty(chargeSheet.getRegistrationChargeSheetId())) {
            registrationChargeSheet = mChargeSheetService.findById(chargeSheet.getRegistrationChargeSheetId());
        }

        OutpatientSheet outpatientSheet = null;
        String diagnosis = "";
        String syndrome = "";
        String chiefComplaint = "";
        String allergicHistory = "";
        Instant diagnosedDate = chargeSheet.getCreated();
        if (Objects.equals(chargeSheet.getType(), ChargeSheet.Type.THERAPY)) {
            diagnosis = Optional.ofNullable(chargeSheet.getAdditional()).map(ChargeSheetAdditional::getDiagnosis).orElse(null);
        } else {
            outpatientSheet = mOutpatientService.findOutpatientSheet(chargeSheet.getPatientOrderId());
            if (Objects.nonNull(outpatientSheet)) {
                diagnosis = Objects.nonNull(outpatientSheet.getMedicalRecord()) ? outpatientSheet.getMedicalRecord().getDiagnosis() : diagnosis;
                syndrome = outpatientSheet.getMedicalRecord() != null ? outpatientSheet.getMedicalRecord().getSyndrome() : "";
                chiefComplaint = outpatientSheet.getMedicalRecord() != null ? outpatientSheet.getMedicalRecord().getChiefComplaint() : "";
                allergicHistory = outpatientSheet.getMedicalRecord() != null ? outpatientSheet.getMedicalRecord().getAllergicHistory() : "";
                diagnosedDate = outpatientSheet.getDiagnosedDate();
            }
        }

        cn.abcyun.bis.rpc.sdk.cis.model.outpatient.print.ExaminationSheetPrintView examinationSheetPrintView = new cn.abcyun.bis.rpc.sdk.cis.model.outpatient.print.ExaminationSheetPrintView();

        cn.abcyun.bis.rpc.sdk.cis.model.clinic.OrganPrintView organ = cisScClinicService.getOrganPrintInfo(chargeSheet.getClinicId());
        PatientOrder patientOrder = patientOrderService.findPatientOrder(chargeSheet.getPatientOrderId(), PatientOrderService.getAgeLockTime(chargeSheet));

        List<ChargeTransaction> chargeTransactions = Optional.ofNullable(chargeSheet.getChargeTransactions()).orElse(new ArrayList<>());
        chargeTransactions.addAll(Optional.ofNullable(registrationChargeSheet).map(ChargeSheet::getChargeTransactions).orElse(new ArrayList<>()));

        Map<String, List<ChargeOweSheet>> chargeOweSheetsMap = chargeOweSheetRepository.findAllByChargeSheetIdInAndPatientIdAndIsDeleted(allChargeSheets.stream().map(ChargeSheet::getId).collect(Collectors.toList()), chargeSheet.getPatientId(), 0)
                .stream().collect(Collectors.groupingBy(ChargeOweSheet::getChargeSheetId));

        for (ChargeSheet allChargeSheet : allChargeSheets) {
            chargeTransactions.addAll(
                    ChargeSheetFeeProtocol.getChargeTransactionAndChargeActionContainOwe(allChargeSheet, chargeOweSheetsMap.get(allChargeSheet.getId()))
                            .getChargeTransactions()
            );
        }

        // 设置费别
        Integer shebaoChargeType = null;
        if (patientOrder != null) {
            shebaoChargeType = patientOrder.getShebaoChargeType();
            examinationSheetPrintView.setShebaoCardInfo(patientOrder.getShebaoCardInfo());
        }
        examinationSheetPrintView.setHealthCardNo(getHealthCardNo(patientOrder));
        examinationSheetPrintView.setHealthCardPayLevel(ChargeUtils.generateHealthCardPayLevel(chargeTransactions, shebaoChargeType));

        CisPatientInfo cisPatientInfo = getCisPatientInfo(chargeSheet.getPatientId(), chargeSheet.getChainId(), chargeSheet.getClinicId(), patientOrder);
        examinationSheetPrintView.setPatient(cisPatientInfo);
        examinationSheetPrintView.setOrgan(PrintUtils.parseToOrganPrintView(organ));
        examinationSheetPrintView.setDiagnosis(diagnosis);
        examinationSheetPrintView.setSyndrome(syndrome);
        examinationSheetPrintView.setChiefComplaint(chiefComplaint);
        examinationSheetPrintView.setAllergicHistory(allergicHistory);
        examinationSheetPrintView.setDiagnosedDate(diagnosedDate);
        // 医生信息
        if (StringUtils.isNotBlank(chargeSheet.getDoctorId())) {
            Instant doctorIdSnapTime = Optional.ofNullable(chargeSheet.getDiagnosedDate()).orElse(Instant.now());
            Optional.ofNullable(cisScClinicService.queryEmployeeSnap(new BatchQueryEmployeeSnapshotReq()
                            .setChainId(chargeSheet.getChainId())
                            .addBusinessTimeEmployeeSnap(chargeSheet.getDoctorId(), doctorIdSnapTime)
                    ).getEmployeeByBusTime(chargeSheet.getDoctorId(), doctorIdSnapTime))
                    .ifPresent(employee -> {
                        examinationSheetPrintView.setDoctorName(employee.getName());
                        examinationSheetPrintView.setDoctorSignImgUrl(employee.getHandSign());
                    });
        }

        Department department = mOrganProductService.findDepartmentById(outpatientSheet != null ? outpatientSheet.getDepartmentId() : null);
        if (department != null) {
            examinationSheetPrintView.setDepartmentName(department.getName());
        }

        if (patientOrder != null) {
            examinationSheetPrintView.setPatientOrderNo(patientOrder.getNo());
        }

        ChargeUtils.bindComposeChildren(chargeSheet);

        //为了与门诊的排序一致，单独处理这里的顺序

        List<ChargeForm> examinationChargeForms = ChargeUtils.getChargeSheetForms(chargeSheet, Constants.SourceFormType.EXAMINATION);

        Predicate<ChargeFormItem> filterExaminationItemsPredicate = chargeFormItem -> {
            boolean filterFlag = chargeFormItem.getProductType() == Constants.ProductType.EXAMINATION;
            if (type == null) {
                return filterFlag;
            }
            if (type == Constants.ProductType.SubType.EXAMINATION_EXAMINATION) {
                return filterFlag && chargeFormItem.getProductSubType() == Constants.ProductType.SubType.EXAMINATION_EXAMINATION;
            }
            if (type == Constants.ProductType.SubType.EXAMINATION_INSPECTION) {
                return filterFlag && chargeFormItem.getProductSubType() == Constants.ProductType.SubType.EXAMINATION_INSPECTION;
            }
            return filterFlag;
        };

        List<ChargeFormItem> examinationItems = examinationChargeForms
                .stream()
                .flatMap(chargeForm -> Optional.ofNullable(chargeForm.getChargeFormItems()).orElse(new ArrayList<>()).stream())
                .filter(item -> item.getIsDeleted() == 0)
                .filter(filterExaminationItemsPredicate)
                .filter(chargeFormItem -> chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.UNCHARGED || chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED)
                .sorted(Comparator.comparing(ChargeFormItem::getSort))
                .collect(Collectors.toList());

        ChargeUtils.getChargeSheetForms(chargeSheet, Constants.SourceFormType.COMPOSE_PRODUCT)
                .stream()
                .filter(chargeForm -> chargeForm.getChargeFormItems() != null)
                .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                .filter(item -> item.getIsDeleted() == 0)
                .forEach(chargeFormItem -> {
                    if (chargeFormItem.getComposeType() == ComposeType.COMPOSE && !org.springframework.util.CollectionUtils.isEmpty(chargeFormItem.getComposeChildren())) {
                        List<ChargeFormItem> childrenAfterFilter = chargeFormItem.getComposeChildren().stream()
                                .filter(filterExaminationItemsPredicate)
                                .collect(Collectors.toList());
                        if (!org.springframework.util.CollectionUtils.isEmpty(childrenAfterFilter)) {
                            chargeFormItem.setComposeChildren(childrenAfterFilter);
                            examinationItems.add(chargeFormItem);
                        }
                    }
                });


        Map<String, ProductFormItem> examinationProductFormItemMap = Optional.ofNullable(outpatientSheet)
                .map(OutpatientSheet::getProductForms).orElse(new ArrayList<>())
                .stream()
                .filter(productForm -> productForm.getProductFormItems() != null)
                .flatMap(productForm -> productForm.getProductFormItems().stream())
                .filter(productFormItem -> productFormItem.getType() == Constants.ProductType.EXAMINATION)
                .collect(Collectors.toMap(ProductFormItem::getId, Function.identity(), (a, b) -> a));

        // 设置检查检验的信息-这里要按照合并开关查
        Map<String, List<ExamSheetSimpleExtendView>> chargeFormItemIdToExamSheetSimpleViews = examinationService.getExaminationSheetsExtendByPatientOrder(chargeSheet.getPatientOrderId(), chargeSheet.getChainId(), chargeSheet.getClinicId(), type, YesOrNo.YES)
                .stream()
                .flatMap(examSheetSimpleView -> {
                    List<ExamSheetSimpleExtendView> children = examSheetSimpleView.getChildren();
                    if (org.springframework.util.CollectionUtils.isEmpty(children)) {
                        return Stream.of(examSheetSimpleView);
                    }
                    return children.stream().map(ExamSheetSimpleExtendView::getChargeFormItemId)
                            .filter(StringUtils::isNotBlank)
                            //.distinct()
                            .map(chargeFormItemId -> {
                                ExamSheetSimpleExtendView copyExamSheetSimpleView = new ExamSheetSimpleExtendView();
                                BeanUtils.copyProperties(examSheetSimpleView, copyExamSheetSimpleView, "children");
                                copyExamSheetSimpleView.setChargeFormItemId(chargeFormItemId);
                                return copyExamSheetSimpleView;
                            });
                })
                .filter(examSheetSimpleView -> org.apache.commons.lang.StringUtils.isNotEmpty(examSheetSimpleView.getChargeFormItemId()))
                .collect(Collectors.groupingBy(ExamSheetSimpleExtendView::getChargeFormItemId));

        examinationSheetPrintView.setExaminationFormItems(examinationItems.stream()
                .map(chargeFormItem -> generateExaminationFormItemPrintView(chargeFormItem, examinationProductFormItemMap, chargeFormItemIdToExamSheetSimpleViews, chargeSheet.getStatus()))
                .filter(Objects::nonNull)
                .filter(itemPrintView -> MathUtils.wrapBigDecimalOrZero(itemPrintView.getUnitCount()).compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toList()));
        return examinationSheetPrintView;
    }

    private String getHealthCardNo(PatientOrder patientOrder) {
        if (patientOrder != null && patientOrder.getShebaoChargeType() != null) {
            if ((patientOrder.getShebaoChargeType() == PatientOrder.ShebaoChargeType.GENERAL_OUTPATIENT
                    || patientOrder.getShebaoChargeType() == PatientOrder.ShebaoChargeType.MANTEBING_OUTPATIENT)
                    && patientOrder.getShebaoCardInfo() != null) {
                return patientOrder.getShebaoCardInfo().getCardNo();
            }
        }
        return null;
    }

    public CisPatientInfo getCisPatientInfo(String patientId, String chainId, String clinicId, PatientOrder patientOrder) {

        //匿名患者从patientOrder上转
        if (StringUtils.isBlank(patientId) || Objects.equals(patientId, Constants.ANONYMOUS_PATIENT_ID)) {
            return ChargeUtils.parseCisPatientInfoFromPatientOrder(patientOrder);
        }

        PatientInfo patientInfo = crmService.searchPatientInfoByPatientIdFromCrm(chainId, clinicId, patientId, false, false);
        if (patientOrder != null && patientInfo != null) {
            patientInfo.setAge(patientOrder.getPatientAge());
        }

        return ChargeUtils.convertCisPatientInfoFromCrmPatientInfo(patientInfo);
    }


    public static cn.abcyun.bis.rpc.sdk.cis.model.outpatient.print.ExaminationFormItemPrintView generateExaminationFormItemPrintView(ChargeFormItem chargeFormItem,
                                                                                                                                     Map<String, ProductFormItem> examinationProductFormItemMap,
                                                                                                                                     Map<String, List<ExamSheetSimpleExtendView>> chargeFormItemIdToExamSheetSimpleViews,
                                                                                                                                     int chargeSheetStatus) {
        if (Objects.isNull(chargeFormItem)) {
            return null;
        }

        ChargeFormItemUtils.PrintUnitPriceAndPrintTotalPriceResult printUnitPriceAndPrintTotalPriceResult = ChargeFormItemUtils.generatePrintUnitPriceAndPrintTotalPrice(chargeFormItem, chargeSheetStatus);

        cn.abcyun.bis.rpc.sdk.cis.model.outpatient.print.ExaminationFormItemPrintView itemPrintView = new cn.abcyun.bis.rpc.sdk.cis.model.outpatient.print.ExaminationFormItemPrintView();
        itemPrintView.setName(chargeFormItem.getName());
        itemPrintView.setUnit(chargeFormItem.getUnit());
        itemPrintView.setUnitCount(MathUtils.wrapBigDecimalSubtract(chargeFormItem.getUnitCount(), chargeFormItem.getRefundUnitCount()));
        itemPrintView.setUnitPrice(printUnitPriceAndPrintTotalPriceResult.getPrintUnitPrice());
        itemPrintView.setTotalPrice(printUnitPriceAndPrintTotalPriceResult.getPrintTotalPrice());
        itemPrintView.setType(chargeFormItem.getProductType());
        itemPrintView.setSubType(chargeFormItem.getProductSubType());
        itemPrintView.setChargeStatus(chargeFormItem.getStatus());
        itemPrintView.setExamSheetSimpleViews(chargeFormItemIdToExamSheetSimpleViews.get(chargeFormItem.getId()));
        itemPrintView.setId(chargeFormItem.getId());

        if (StringUtils.isNotEmpty(chargeFormItem.getSourceFormItemId())) {
            ProductFormItem productFormItem = examinationProductFormItemMap.getOrDefault(chargeFormItem.getSourceFormItemId(), null);
            if (productFormItem != null) {
                itemPrintView.setRemark(StringUtils.isEmpty(Optional.ofNullable(chargeFormItem.getAdditional()).map(ChargeFormItemAdditional::getRemark).orElse(null)) ? productFormItem.getRemark() : chargeFormItem.getAdditional().getRemark());
                itemPrintView.setToothNos(productFormItem.getToothNos());
            }
        } else if (chargeFormItem.getAdditional() != null) {
            itemPrintView.setRemark(chargeFormItem.getAdditional().getRemark());
            itemPrintView.setToothNos(chargeFormItem.getAdditional().getToothNos());
        }

        if (chargeFormItem.getComposeType() == ComposeType.COMPOSE && chargeFormItem.getComposeChildren() != null) {
            itemPrintView.setComposeType(ComposeType.COMPOSE);
            itemPrintView.setComposeTotalPrice(MathUtils.calculateTotalPrice(itemPrintView.getUnitPrice(), itemPrintView.getUnitCount(), 2));
            itemPrintView.setComposeChildren(chargeFormItem.getComposeChildren()
                    .stream()
                    .map(subItem -> generateExaminationFormItemPrintView(subItem, examinationProductFormItemMap, chargeFormItemIdToExamSheetSimpleViews, chargeSheetStatus))
                    .collect(Collectors.toList()));
        }

        return itemPrintView;
    }

    private void bindExecuteFormItemsProductInfo(String clinicId, String chainId, List<ExecuteFormPrintView> executeFormPrintViewList, List<PrescriptionFormPrintView> prescriptionExternalForms) throws ServiceInternalException {
        executeFormPrintViewList = executeFormPrintViewList == null ? new ArrayList<>() : executeFormPrintViewList;
        prescriptionExternalForms = prescriptionExternalForms == null ? new ArrayList<>() : prescriptionExternalForms;
        if (StringUtils.isEmpty(clinicId)) {
            return;
        }

        Set<String> productIds = executeFormPrintViewList.stream()
                .filter(executeFormPrintView -> !CollectionUtils.isEmpty(executeFormPrintView.getExecuteFormItems()))
                .flatMap(executeFormPrintView -> executeFormPrintView.getExecuteFormItems().stream())
                .filter(executeFormItemPrintView -> !CollectionUtils.isEmpty(executeFormItemPrintView.getGroupItems()))
                .flatMap(executeFormItemPrintView -> executeFormItemPrintView.getGroupItems().stream())
                .filter(executeFormSubItemPrintView -> executeFormSubItemPrintView.getProductInfo() == null)
                .filter(executeFormSubItemPrintView -> !StringUtils.isEmpty(executeFormSubItemPrintView.getProductId()))
                .map(ExecuteFormSubItemPrintView::getProductId)
                .collect(Collectors.toSet());

        productIds.addAll(prescriptionExternalForms.stream()
                .filter(prescriptionExternalForm -> !CollectionUtils.isEmpty(prescriptionExternalForm.getPrescriptionFormItems()))
                .flatMap(prescriptionExternalForm -> prescriptionExternalForm.getPrescriptionFormItems().stream())
                .filter(prescriptionFormItemPrintView -> prescriptionFormItemPrintView.getProductInfo() == null)
                .filter(prescriptionFormItemPrintView -> !StringUtils.isEmpty(prescriptionFormItemPrintView.getGoodsId()))
                .map(PrescriptionFormItemPrintView::getGoodsId)
                .collect(Collectors.toSet()));


        if (!CollectionUtils.isEmpty(productIds)) {

            List<QueryGoodsInPharmacyByIdsRsp.QueryPharmacyGoodsRsp> queryPharmacyGoodsRsps = goodsSCService.queryGoodsInPharmacyByIds(clinicId, chainId, false, 0, CisScGoodsService.generateLocalPharmacyQueryPharmacyGoodsReq(new ArrayList<>(productIds)));

            List<GoodsItem> goodsItems = Optional.ofNullable(queryPharmacyGoodsRsps).orElse(new ArrayList<>()).stream()
                    .filter(queryPharmacyGoodsRsp -> org.apache.commons.collections.CollectionUtils.isNotEmpty(queryPharmacyGoodsRsp.getList()))
                    .flatMap(queryPharmacyGoodsRsp -> queryPharmacyGoodsRsp.getList().stream())
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(goodsItems)) {
                Map<String, GoodsItem> goodsItemMap = goodsItems.stream().collect(Collectors.toMap(GoodsItem::getId, Function.identity(), (a, b) -> a));

                //bind
                executeFormPrintViewList.stream()
                        .filter(executeFormPrintView -> !CollectionUtils.isEmpty(executeFormPrintView.getExecuteFormItems()))
                        .flatMap(executeFormPrintView -> executeFormPrintView.getExecuteFormItems().stream())
                        .filter(executeFormItemPrintView -> !CollectionUtils.isEmpty(executeFormItemPrintView.getGroupItems()))
                        .flatMap(executeFormItemPrintView -> executeFormItemPrintView.getGroupItems().stream())
                        .filter(executeFormSubItemPrintView -> executeFormSubItemPrintView.getProductInfo() == null)
                        .filter(executeFormSubItemPrintView -> !StringUtils.isEmpty(executeFormSubItemPrintView.getProductId()))
                        .forEach(executeFormSubItemPrintView -> {
                            GoodsItem goodsItem = goodsItemMap.get(executeFormSubItemPrintView.getProductId());
                            if (goodsItem != null) {
                                executeFormSubItemPrintView.setProductInfo(JsonUtils.dumpAsJsonNode(goodsItem));
                                executeFormSubItemPrintView.setTradeName(goodsItem.getName());
                                executeFormSubItemPrintView.setMedicineCadn(goodsItem.getMedicineCadn());
                            }
                        });

                prescriptionExternalForms.stream()
                        .filter(prescriptionExternalForm -> !CollectionUtils.isEmpty(prescriptionExternalForm.getPrescriptionFormItems()))
                        .flatMap(prescriptionExternalForm -> prescriptionExternalForm.getPrescriptionFormItems().stream())
                        .filter(prescriptionFormItemPrintView -> prescriptionFormItemPrintView.getProductInfo() == null)
                        .filter(prescriptionFormItemPrintView -> !StringUtils.isEmpty(prescriptionFormItemPrintView.getGoodsId()))
                        .forEach(prescriptionFormItemPrintView -> {
                            GoodsItem goodsItem = goodsItemMap.get(prescriptionFormItemPrintView.getGoodsId());
                            if (goodsItem != null) {
                                prescriptionFormItemPrintView.setProductInfo(JsonUtils.dumpAsJsonNode(goodsItem));
                            }
                        });
            }

        }

    }

    private List<ExecuteFormPrintView> generateExecuteFormPrintViews(ChargeSheet chargeSheet, int type, OutpatientSheet outpatientSheet, Integer hisType) {
        List<ExecuteFormPrintView> executeFormPrintViewList = new ArrayList<>();
        if (chargeSheet == null || chargeSheet.getChargeForms() == null) {
            return executeFormPrintViewList;
        }

        PrintMedicalDocumentsInfusionAndTreatmentProperty medicalDocumentsInfusionAndTreatmentProperty = Optional.ofNullable(propertyProviderService.getPrintMedicalDocumentsInfusionAndTreatmentContent(chargeSheet.getClinicId())).orElse(null);
        boolean includeExternal = Optional.ofNullable(medicalDocumentsInfusionAndTreatmentProperty)
                .map(PrintMedicalDocumentsInfusionAndTreatmentProperty::getInfusion)
                .map(PrintMedicalDocumentsInfusion::getContent)
                .map(PrintMedicalDocumentsInfusion.InfusionContent::getIncludeExternal)
                .orElse(0) == 1;
        boolean containProductOtherPrice = Optional.ofNullable(medicalDocumentsInfusionAndTreatmentProperty)
                .map(PrintMedicalDocumentsInfusionAndTreatmentProperty::getTreatmentContent)
                .map(MedicalDocumentsTreatmentContent::getProductOtherPrice)
                .orElse(1) == 1;
        //如果是医院管家，都不打印其他
        if (hisType != null && hisType == Organ.HisType.CIS_HIS_TYPE_HOSPITAL) {
            containProductOtherPrice = false;
        }

        boolean containProductMaterialsPrice = Optional.ofNullable(medicalDocumentsInfusionAndTreatmentProperty)
                .map(PrintMedicalDocumentsInfusionAndTreatmentProperty::getTreatmentContent)
                .map(MedicalDocumentsTreatmentContent::getProductMaterialsPrice)
                .orElse(0) == 1;

        int executeType = ExecuteFormPrintView.Type.TREATMENT;
        if (type == ExecuteSheetPrintView.Type.WESTERN_INFUSION_MEDICINE) {
            executeFormPrintViewList.addAll(generateTreatmentExecuteFormPrintViews(chargeSheet, outpatientSheet, Constants.ProductType.SubType.TREATMENT_TREATMENT));
            executeFormPrintViewList.addAll(generateMedicineExecuteFormPrintViews(chargeSheet, outpatientSheet, includeExternal));
        } else if (type == ExecuteSheetPrintView.Type.TREATMENT) {
            Predicate<ChargeFormItem> filterItemPredicate = chargeFormItem -> (chargeFormItem.getProductType() == Constants.ProductType.TREATMENT && chargeFormItem.getProductSubType() == Constants.ProductType.SubType.TREATMENT_PHYSIOTHERAPY);
            executeFormPrintViewList.addAll(generateTreatmentExecuteFormPrintViews(chargeSheet, outpatientSheet, executeType, filterItemPredicate));
            executeFormPrintViewList.addAll(generateComposeExecuteFormPrintViews(chargeSheet, outpatientSheet, filterItemPredicate));
        } else if (type == ExecuteSheetPrintView.Type.TRANSFUSION) {
            executeFormPrintViewList.addAll(generateMedicineExecuteFormPrintViews(chargeSheet, outpatientSheet, includeExternal));
        } else if (type == ExecuteSheetPrintView.Type.THERAPY) {
            Predicate<ChargeFormItem> filterItemPredicate = chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.TREATMENT;
            Predicate<ChargeFormItem> composeFilterItemPredicate = ChargeUtils.getNeedExecuteTherapySheetPredicate(containProductOtherPrice, containProductMaterialsPrice);
            executeFormPrintViewList.addAll(generateTreatmentExecuteFormPrintViews(chargeSheet, outpatientSheet, executeType, filterItemPredicate));
            executeFormPrintViewList.addAll(generateComposeExecuteFormPrintViews(chargeSheet, outpatientSheet, composeFilterItemPredicate));
            if (containProductOtherPrice) {
                filterItemPredicate = chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.OTHER_FEE;
                executeType = ExecuteFormPrintView.Type.OTHER_FEE;
                executeFormPrintViewList.addAll(generateTreatmentExecuteFormPrintViews(chargeSheet, outpatientSheet, executeType, filterItemPredicate));
            }
            if (containProductMaterialsPrice) {
                filterItemPredicate = chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.MATERIAL || chargeFormItem.getProductType() == Constants.ProductType.SALE_PRODUCT;
                executeType = ExecuteFormPrintView.Type.MATERIAL_AND_PRODUCT;
                executeFormPrintViewList.addAll(generateTreatmentExecuteFormPrintViews(chargeSheet, outpatientSheet, executeType, filterItemPredicate));
            }
        }

        return executeFormPrintViewList;
    }

    private List<ExecuteFormItemPrintView> generateExecuteFormItemPrintViews(List<ChargeFormItem> formItems, OutpatientSheet outpatientSheet, Map<String, List<ChargeFormItem>> composeSubItemMap, int chargeSheetStatus) {
        if (CollectionUtils.isEmpty(formItems)) {
            return new ArrayList<>();
        }

        Map<String, ProductFormItem> treatmentProductFormItemMap = Optional.ofNullable(outpatientSheet)
                .map(OutpatientSheet::getProductForms).orElse(new ArrayList<>())
                .stream()
                .filter(productForm -> productForm.getProductFormItems() != null)
                .flatMap(productForm -> productForm.getProductFormItems().stream())
                .collect(Collectors.toMap(ProductFormItem::getId, Function.identity(), (a, b) -> a));

        Map<String, PrescriptionFormItem> prescriptionFormItemMap = Optional.ofNullable(outpatientSheet)
                .map(OutpatientSheet::getPrescriptionForms).orElse(new ArrayList<>())
                .stream()
                .filter(prescriptionForm -> prescriptionForm.getPrescriptionFormItems() != null)
                .flatMap(prescriptionForm -> prescriptionForm.getPrescriptionFormItems().stream())
                .filter(Objects::nonNull)
                .filter(prescriptionFormItem -> prescriptionFormItem.getAst() != null)
                .collect(Collectors.toMap(PrescriptionFormItem::getId, Function.identity(), (a, b) -> a));


        List<ExecuteFormItemPrintView> itemPrintViews = formItems.stream()
                .map(chargeFormItem -> {
                    ExecuteFormItemPrintView executeFormItemPrintView = new ExecuteFormItemPrintView();
                    executeFormItemPrintView.setComposeType(chargeFormItem.getComposeType());
                    executeFormItemPrintView.setSourceFormItemId(chargeFormItem.getSourceFormItemId());

                    if (chargeFormItem.getComposeType() == ComposeType.COMPOSE) {
                        List<ChargeFormItem> childrenItems = composeSubItemMap.getOrDefault(chargeFormItem.getId(), null);
                        if (CollectionUtils.isEmpty(childrenItems)) {
                            return null;
                        }
                        executeFormItemPrintView.setComposeName(chargeFormItem.getName());
                        executeFormItemPrintView.setComposeUnit(chargeFormItem.getUnit());
                        executeFormItemPrintView.setComposeUnitCount(MathUtils.wrapBigDecimalSubtract(chargeFormItem.getUnitCount(), chargeFormItem.getRefundUnitCount()));
                        executeFormItemPrintView.setComposeUnitPrice(chargeFormItem.getUnitPrice());
                        executeFormItemPrintView.setComposeTotalPrice(MathUtils.calculateTotalPrice(executeFormItemPrintView.getComposeUnitPrice(), executeFormItemPrintView.getComposeUnitCount(), 2));
                        executeFormItemPrintView.setGroupItems(childrenItems.stream().map(child -> generateExecuteFormSubItemPrintView(child, chargeSheetStatus)).collect(Collectors.toList()));
                    } else {
                        UsageInfo firstItemUsageInfo = JsonUtils.readValue(chargeFormItem.getUsageInfoJson(), UsageInfo.class);
                        executeFormItemPrintView.setCreated(chargeFormItem.getCreated());
                        executeFormItemPrintView.setSort(chargeFormItem.getSort());
                        if (firstItemUsageInfo != null) {
                            BeanUtils.copyProperties(firstItemUsageInfo, executeFormItemPrintView);
                        }
                        executeFormItemPrintView.setGroupItems(new ArrayList<>());
                        executeFormItemPrintView.getGroupItems().add(generateExecuteFormSubItemPrintView(chargeFormItem, chargeSheetStatus));
                    }

                    if (StringUtils.isNotEmpty(chargeFormItem.getSourceFormItemId())) {
                        Optional.ofNullable(treatmentProductFormItemMap.getOrDefault(chargeFormItem.getSourceFormItemId(), null))
                                .ifPresent(treatmentProductFormItem -> {
                                    executeFormItemPrintView.setDays(treatmentProductFormItem.getDays());
                                    executeFormItemPrintView.setRemark(StringUtils.isEmpty(Optional.ofNullable(chargeFormItem.getAdditional()).map(ChargeFormItemAdditional::getRemark).orElse(null)) ? treatmentProductFormItem.getRemark() : chargeFormItem.getAdditional().getRemark());

                                    executeFormItemPrintView.setToothNos(treatmentProductFormItem.getToothNos());
                                    executeFormItemPrintView.setDailyDosage(treatmentProductFormItem.getDailyDosage());
                                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(executeFormItemPrintView.getGroupItems())) {

                                        //如果是套餐，直接将套餐的days给groupItems
                                        if (executeFormItemPrintView.getComposeType() == ComposeType.COMPOSE) {
                                            executeFormItemPrintView.getGroupItems()
                                                    .forEach(subItemPrintView -> {
                                                        subItemPrintView.setRemark(executeFormItemPrintView.getRemark());
                                                        subItemPrintView.setDays(executeFormItemPrintView.getDays());
                                                        subItemPrintView.setToothNos(executeFormItemPrintView.getToothNos());
                                                        subItemPrintView.setDailyDosage(executeFormItemPrintView.getDailyDosage());
                                                    });
                                        } else {
                                            executeFormItemPrintView.getGroupItems().stream()
                                                    .filter(subItemPrintView -> ObjectUtils.equals(subItemPrintView.getSourceFormItemId(), treatmentProductFormItem.getId()))
                                                    .forEach(subItemPrintView -> {
                                                        subItemPrintView.setRemark(StringUtils.isEmpty(Optional.ofNullable(chargeFormItem.getAdditional()).map(ChargeFormItemAdditional::getRemark).orElse(null)) ? treatmentProductFormItem.getRemark() : chargeFormItem.getAdditional().getRemark());
                                                        subItemPrintView.setDays(treatmentProductFormItem.getDays());
                                                        subItemPrintView.setToothNos(treatmentProductFormItem.getToothNos());
                                                        subItemPrintView.setDailyDosage(treatmentProductFormItem.getDailyDosage());
                                                    });
                                        }
                                    }
                                });
                        //bind皮试信息
                        Optional.ofNullable(prescriptionFormItemMap.getOrDefault(chargeFormItem.getSourceFormItemId(), null))
                                .ifPresent(prescriptionFormItem -> {
                                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(executeFormItemPrintView.getGroupItems())) {
                                        executeFormItemPrintView.getGroupItems().stream()
                                                .filter(subItemPrintView -> ObjectUtils.equals(subItemPrintView.getSourceFormItemId(), prescriptionFormItem.getId()))
                                                .forEach(subItemPrintView -> {
                                                    subItemPrintView.setAst(prescriptionFormItem.getAst());
                                                    subItemPrintView.setAstResult(prescriptionFormItem.getAstResult());
                                                });
                                    }
                                });
                    } else if (chargeFormItem.getAdditional() != null) {
                        executeFormItemPrintView.setRemark(chargeFormItem.getAdditional().getRemark());
                        executeFormItemPrintView.setToothNos(chargeFormItem.getAdditional().getToothNos());

                        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(executeFormItemPrintView.getGroupItems())) {

                            //如果是套餐，直接将套餐的days给groupItems
                            if (executeFormItemPrintView.getComposeType() == ComposeType.COMPOSE) {
                                executeFormItemPrintView.getGroupItems()
                                        .forEach(subItemPrintView -> {
                                            subItemPrintView.setRemark(executeFormItemPrintView.getRemark());
                                            subItemPrintView.setDays(executeFormItemPrintView.getDays());
                                        });
                            } else {
                                executeFormItemPrintView.getGroupItems().stream()
                                        .forEach(subItemPrintView -> {
                                            subItemPrintView.setRemark(executeFormItemPrintView.getRemark());
                                            subItemPrintView.setDays(executeFormItemPrintView.getDays());
                                        });
                            }
                        }
                    }

                    return executeFormItemPrintView;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return itemPrintViews;
    }

    private List<ExecuteFormPrintView> generateTreatmentExecuteFormPrintViews(ChargeSheet chargeSheet, OutpatientSheet outpatientSheet, int executeType, Predicate<ChargeFormItem> filterItemPredicate) {
        if (chargeSheet == null || chargeSheet.getChargeForms() == null) {
            return new ArrayList<>();
        }

        List<ChargeFormItem> formItems = ChargeUtils.getChargeSheetForms(chargeSheet)
                .stream()
                .filter(chargeForm -> chargeForm.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_EXTERNAL
                        && chargeForm.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_CHINESE
                        && chargeForm.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_WESTERN
                        && chargeForm.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_INFUSION
                )
                .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                .filter(item -> item.getIsDeleted() == 0)
                .filter(filterItemPredicate)
                .filter(chargeFormItem -> chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.CHARGED || chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.UNCHARGED)
                .filter(chargeFormItem -> !chargeFormItem.isChildItem())
                .sorted(Comparator.comparing(chargeFormItem -> String.format("%d-%s-%04d", chargeFormItem.getComposeType(), chargeFormItem.getChargeFormId(), chargeFormItem.getSort())))
                .collect(Collectors.toList());

        if (formItems.size() == 0) {
            return new ArrayList<>();
        }

        List<ExecuteFormItemPrintView> executeFormItemPrintViews = generateExecuteFormItemPrintViews(formItems, outpatientSheet, new HashMap<>(), chargeSheet.getStatus());

        ExecuteFormPrintView formPrintView = new ExecuteFormPrintView();
        formPrintView.setExecuteFormItems(executeFormItemPrintViews);
        formPrintView.setType(executeType);

        List<ExecuteFormPrintView> formPrintViewList = new ArrayList<>();
        formPrintViewList.add(formPrintView);
        return formPrintViewList;
    }

    /**
     * @param chargeSheet
     * @param treatmentSubType null表示不过滤
     * @return
     */
    private List<ExecuteFormPrintView> generateTreatmentExecuteFormPrintViews(ChargeSheet chargeSheet, OutpatientSheet outpatientSheet, Integer treatmentSubType) {
        if (chargeSheet == null || chargeSheet.getChargeForms() == null) {
            return new ArrayList<>();
        }

        List<ChargeFormItem> formItems = ChargeUtils.getChargeSheetForms(chargeSheet)
                .stream()
                .filter(chargeForm -> chargeForm.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_EXTERNAL)
                .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                .filter(item -> item.getIsDeleted() == 0)
                .filter(chargeFormItem -> (chargeFormItem.getProductType() == Constants.ProductType.TREATMENT && (treatmentSubType == null || chargeFormItem.getProductSubType() == treatmentSubType))
                        || chargeFormItem.getProductType() == Constants.ProductType.COMPOSE_PRODUCT
                )
                .filter(chargeFormItem -> chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.CHARGED || chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.UNCHARGED)
                .filter(chargeFormItem -> !chargeFormItem.isChildItem())
                .sorted(Comparator.comparing(chargeFormItem -> String.format("%d-%s-%d", chargeFormItem.getComposeType(), chargeFormItem.getChargeFormId(), chargeFormItem.getSort())))
                .collect(Collectors.toList());

        Map<String, List<ChargeFormItem>> composeSubItemMap = ChargeUtils.getChargeSheetForms(chargeSheet)
                .stream()
                .filter(chargeForm -> chargeForm.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_EXTERNAL)
                .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                .filter(item -> item.getIsDeleted() == 0)
                .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.TREATMENT)
                .filter(chargeFormItem -> treatmentSubType == null || chargeFormItem.getProductSubType() == treatmentSubType)
                .filter(chargeFormItem -> chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.CHARGED || chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.UNCHARGED)
                .filter(chargeFormItem -> chargeFormItem.getComposeType() == ComposeType.COMPOSE_SUB_ITEM)
                .filter(chargeFormItem -> !TextUtils.isEmpty(chargeFormItem.getComposeParentFormItemId()))
                .collect(Collectors.groupingBy(ChargeFormItem::getComposeParentFormItemId));

        if (formItems.size() == 0) {
            return new ArrayList<>();
        }

        Map<String, ProductFormItem> treatmentProductFormItemMap = Optional.ofNullable(outpatientSheet)
                .map(OutpatientSheet::getProductForms).orElse(new ArrayList<>())
                .stream()
                .filter(productForm -> productForm.getProductFormItems() != null)
                .flatMap(productForm -> productForm.getProductFormItems().stream())
                .collect(Collectors.toMap(ProductFormItem::getId, Function.identity(), (a, b) -> a));


        List<ExecuteFormItemPrintView> itemPrintViews = formItems.stream()
                .map(chargeFormItem -> {
                    ExecuteFormItemPrintView executeFormItemPrintView = new ExecuteFormItemPrintView();
                    executeFormItemPrintView.setComposeType(chargeFormItem.getComposeType());
                    executeFormItemPrintView.setSourceFormItemId(chargeFormItem.getSourceFormItemId());

                    if (chargeFormItem.getComposeType() == ComposeType.COMPOSE) {
                        List<ChargeFormItem> childrenItems = composeSubItemMap.getOrDefault(chargeFormItem.getId(), null);
                        if (CollectionUtils.isEmpty(childrenItems)) {
                            return null;
                        }
                        executeFormItemPrintView.setComposeName(chargeFormItem.getName());
                        executeFormItemPrintView.setComposeUnit(chargeFormItem.getUnit());
                        executeFormItemPrintView.setComposeUnitCount(MathUtils.wrapBigDecimalSubtract(chargeFormItem.getUnitCount(), chargeFormItem.getRefundUnitCount()));
                        executeFormItemPrintView.setComposeUnitPrice(chargeFormItem.getUnitPrice());
                        executeFormItemPrintView.setComposeTotalPrice(MathUtils.calculateTotalPrice(executeFormItemPrintView.getComposeUnitPrice(), executeFormItemPrintView.getComposeUnitCount(), 2));
                        executeFormItemPrintView.setGroupItems(childrenItems.stream().map(child -> generateExecuteFormSubItemPrintView(child, chargeSheet.getStatus())).collect(Collectors.toList()));
                    } else {
                        UsageInfo firstItemUsageInfo = JsonUtils.readValue(chargeFormItem.getUsageInfoJson(), UsageInfo.class);
                        executeFormItemPrintView.setCreated(chargeFormItem.getCreated());
                        executeFormItemPrintView.setSort(chargeFormItem.getSort());
                        if (firstItemUsageInfo != null) {
                            BeanUtils.copyProperties(firstItemUsageInfo, executeFormItemPrintView);
                        }
                        executeFormItemPrintView.setGroupItems(new ArrayList<>());
                        executeFormItemPrintView.getGroupItems().add(generateExecuteFormSubItemPrintView(chargeFormItem, chargeSheet.getStatus()));
                    }

                    if (StringUtils.isNotEmpty(chargeFormItem.getSourceFormItemId())) {
                        Optional.ofNullable(treatmentProductFormItemMap.getOrDefault(chargeFormItem.getSourceFormItemId(), null))
                                .ifPresent(treatmentProductFormItem -> {
                                    executeFormItemPrintView.setDays(treatmentProductFormItem.getDays());
                                    executeFormItemPrintView.setRemark(StringUtils.isEmpty(Optional.ofNullable(chargeFormItem.getAdditional()).map(ChargeFormItemAdditional::getRemark).orElse(null)) ? treatmentProductFormItem.getRemark() : chargeFormItem.getAdditional().getRemark());
                                    executeFormItemPrintView.setToothNos(treatmentProductFormItem.getToothNos());
                                    executeFormItemPrintView.setDailyDosage(treatmentProductFormItem.getDailyDosage());
                                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(executeFormItemPrintView.getGroupItems())) {

                                        //如果是套餐，直接将套餐的days给groupItems
                                        if (executeFormItemPrintView.getComposeType() == ComposeType.COMPOSE) {
                                            executeFormItemPrintView.getGroupItems()
                                                    .forEach(subItemPrintView -> {
                                                        subItemPrintView.setRemark(executeFormItemPrintView.getRemark());
                                                        subItemPrintView.setDays(executeFormItemPrintView.getDays());
                                                        subItemPrintView.setToothNos(executeFormItemPrintView.getToothNos());
                                                        subItemPrintView.setDailyDosage(executeFormItemPrintView.getDailyDosage());
                                                    });
                                        } else {
                                            executeFormItemPrintView.getGroupItems().stream()
                                                    .filter(subItemPrintView -> ObjectUtils.equals(subItemPrintView.getSourceFormItemId(), treatmentProductFormItem.getId()))
                                                    .forEach(subItemPrintView -> {
                                                        subItemPrintView.setRemark(treatmentProductFormItem.getRemark());
                                                        subItemPrintView.setDays(treatmentProductFormItem.getDays());
                                                        subItemPrintView.setToothNos(treatmentProductFormItem.getToothNos());
                                                        subItemPrintView.setDailyDosage(treatmentProductFormItem.getDailyDosage());
                                                    });
                                        }
                                    }
                                });
                    } else if (chargeFormItem.getAdditional() != null) {
                        executeFormItemPrintView.setRemark(chargeFormItem.getAdditional().getRemark());
                        executeFormItemPrintView.setToothNos(chargeFormItem.getAdditional().getToothNos());

                        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(executeFormItemPrintView.getGroupItems())) {

                            //如果是套餐，直接将套餐的days给groupItems
                            if (executeFormItemPrintView.getComposeType() == ComposeType.COMPOSE) {
                                executeFormItemPrintView.getGroupItems()
                                        .forEach(subItemPrintView -> {
                                            subItemPrintView.setRemark(executeFormItemPrintView.getRemark());
                                            subItemPrintView.setDays(executeFormItemPrintView.getDays());
                                        });
                            } else {
                                executeFormItemPrintView.getGroupItems().stream()
                                        .forEach(subItemPrintView -> {
                                            subItemPrintView.setRemark(executeFormItemPrintView.getRemark());
                                            subItemPrintView.setDays(executeFormItemPrintView.getDays());
                                        });
                            }
                        }
                    }

                    return executeFormItemPrintView;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        int printType = ExecuteFormPrintView.Type.TREATMENT;

        ExecuteFormPrintView formPrintView = new ExecuteFormPrintView();
        formPrintView.setExecuteFormItems(itemPrintViews);
        formPrintView.setType(printType);

        List<ExecuteFormPrintView> formPrintViewList = new ArrayList<>();
        formPrintViewList.add(formPrintView);
        return formPrintViewList;
    }

    /**
     * 处理套餐
     *
     * @param chargeSheet
     * @return
     */
    private List<ExecuteFormPrintView> generateComposeExecuteFormPrintViews(ChargeSheet chargeSheet, OutpatientSheet outpatientSheet, Predicate<ChargeFormItem> filterItemPredicate) {
        if (chargeSheet == null || chargeSheet.getChargeForms() == null) {
            return new ArrayList<>();
        }
        Set<String> composeFormItemIds = new HashSet<>();
        Map<String, List<ChargeFormItem>> composeSubItemMap = ChargeUtils.getChargeSheetForms(chargeSheet)
                .stream()
                .filter(chargeForm -> chargeForm.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_EXTERNAL)
                .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                .filter(item -> item.getIsDeleted() == 0)
                .filter(chargeFormItem -> chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.CHARGED || chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.UNCHARGED)
                .filter(chargeFormItem -> chargeFormItem.getComposeType() == ComposeType.COMPOSE_SUB_ITEM)
                .filter(chargeFormItem -> !TextUtils.isEmpty(chargeFormItem.getComposeParentFormItemId()))
                .filter(filterItemPredicate)
                .peek(chargeFormItem -> {
                    if (filterItemPredicate.test(chargeFormItem)) {
                        composeFormItemIds.add(chargeFormItem.getComposeParentFormItemId());
                    }
                })
                .collect(Collectors.groupingBy(ChargeFormItem::getComposeParentFormItemId));

        if (CollectionUtils.isEmpty(composeFormItemIds)) {
            return new ArrayList<>();
        }

        List<ChargeFormItem> composeFormItems = ChargeUtils.getChargeSheetForms(chargeSheet)
                .stream()
                .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                .filter(item -> item.getIsDeleted() == 0)
                .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.COMPOSE_PRODUCT)
                .filter(chargeFormItem -> composeFormItemIds.contains(chargeFormItem.getId()))
                .filter(chargeFormItem -> chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.CHARGED || chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.UNCHARGED)
                .filter(chargeFormItem -> chargeFormItem.getComposeType() == ComposeType.COMPOSE)
                .sorted(Comparator.comparing(chargeFormItem -> String.format("%d-%s-%d", chargeFormItem.getComposeType(), chargeFormItem.getChargeFormId(), chargeFormItem.getSort())))
                .collect(Collectors.toList());

        if (composeFormItems.size() == 0) {
            return new ArrayList<>();
        }

        List<ExecuteFormItemPrintView> executeFormItemPrintViews = generateExecuteFormItemPrintViews(composeFormItems, outpatientSheet, composeSubItemMap, chargeSheet.getStatus());

        ExecuteFormPrintView formPrintView = new ExecuteFormPrintView();
        formPrintView.setExecuteFormItems(executeFormItemPrintViews);
        formPrintView.setType(ExecuteFormPrintView.Type.COMPOSE_PRODUCT);

        List<ExecuteFormPrintView> formPrintViewList = new ArrayList<>();
        formPrintViewList.add(formPrintView);
        return formPrintViewList;
    }

    private ExecuteFormSubItemPrintView generateExecuteFormSubItemPrintView(ChargeFormItem chargeFormItem, int chargeSheetStatus) {

        ChargeFormItemUtils.PrintUnitPriceAndPrintTotalPriceResult printUnitPriceAndPrintTotalPriceResult = ChargeFormItemUtils.generatePrintUnitPriceAndPrintTotalPrice(chargeFormItem, chargeSheetStatus);

        ExecuteFormSubItemPrintView subItemPrintView = new ExecuteFormSubItemPrintView();

        UsageInfo usageInfo = JsonUtils.readValue(chargeFormItem.getUsageInfoJson(), UsageInfo.class);
        if (usageInfo != null) {
            BeanUtils.copyProperties(usageInfo, subItemPrintView);
        }

        subItemPrintView.setSourceFormItemId(chargeFormItem.getSourceFormItemId());
        subItemPrintView.setName(chargeFormItem.getName());
        subItemPrintView.setUnit(chargeFormItem.getUnit());
        subItemPrintView.setUnitPrice(printUnitPriceAndPrintTotalPriceResult.getPrintUnitPrice());
        subItemPrintView.setComposeType(chargeFormItem.getComposeType());
        subItemPrintView.setExecutedCount(chargeFormItem.getExecutedUnitCount());
        subItemPrintView.setNeedExecutive(chargeFormItem.getNeedExecutive());
        subItemPrintView.setUnitCount(MathUtils.wrapBigDecimalSubtract(chargeFormItem.getUnitCount(), chargeFormItem.getRefundUnitCount()));
        subItemPrintView.setTotalPrice(printUnitPriceAndPrintTotalPriceResult.getPrintTotalPrice());
        subItemPrintView.setProductId(chargeFormItem.getProductId());
        subItemPrintView.setProductInfo(JsonUtils.loadAsJsonNode(chargeFormItem.getProductSnapshot()));
        subItemPrintView.setIsGift(chargeFormItem.getIsGift());
        subItemPrintView.setChargeStatus(chargeFormItem.getV2Status());
        if (MathUtils.wrapBigDecimalCompare(subItemPrintView.getExecutedCount(), subItemPrintView.getUnitCount()) >= 0) {
            subItemPrintView.setExecutedTotalPrice(subItemPrintView.getTotalPrice());
        } else {
            subItemPrintView.setExecutedTotalPrice(MathUtils.min(MathUtils.calculateTotalPrice(subItemPrintView.getUnitPrice(), subItemPrintView.getExecutedCount(), 2), subItemPrintView.getTotalPrice()));
        }
        return subItemPrintView;
    }

    private List<ExecuteFormPrintView> generateMedicineExecuteFormPrintViews(ChargeSheet chargeSheet, OutpatientSheet outpatientSheet, boolean includeExternal) {
        if (chargeSheet == null || chargeSheet.getChargeForms() == null) {
            return new ArrayList<>();
        }

        return chargeSheet.getChargeForms().stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .sorted((a, b) -> ObjectUtils.compare(a.getSort(), b.getSort()))
                .map(chargeForm -> generateMedicineExecuteFormPrintView(chargeForm, outpatientSheet, includeExternal, chargeSheet.getStatus())).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private ExecuteFormPrintView generateMedicineExecuteFormPrintView(ChargeForm chargeForm, OutpatientSheet outpatientSheet, boolean includeExternal, int chargeSheetStatus) {
        if (chargeForm == null || chargeForm.getChargeFormItems() == null) {
            return null;
        }


        List<ChargeFormItem> formItems = chargeForm.getChargeFormItems()
                .stream()
                .filter(item -> item.getIsDeleted() == 0)
                .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.MEDICINE
                        || chargeFormItem.getProductType() == Constants.ProductType.MATERIAL)
                .filter(chargeFormItem -> ChargeUtils.isExecutableItem(chargeFormItem, includeExternal))
                .filter(chargeFormItem -> chargeFormItem.getGoodsFeeType() != GoodsFeeType.FEE_CHILD)
                .filter(chargeFormItem -> chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.CHARGED || chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.UNCHARGED)
                .collect(Collectors.toList());

        if (formItems.size() == 0) {
            return null;
        }

        List<ExecuteFormItemPrintView> itemPrintViews = formItems.stream().collect(Collectors.groupingBy(chargeFormItem -> chargeFormItem.getGroupId() != null ? chargeFormItem.getGroupId() : -10000))
                .values()
                .stream()
                .filter(groupItems -> groupItems.size() > 0)
                .map(groupItems -> {
                    ChargeFormItem firstItem = groupItems.get(0);
                    List<ExecuteFormItemPrintView> printViews = new ArrayList<>();
                    if (firstItem.getGroupId() == null) {
                        printViews = groupItems.stream().sorted((a, b) -> ObjectUtils.compare(a.getSort(), b.getSort())).map(chargeFormItem -> {
                            UsageInfo firstItemUsageInfo = JsonUtils.readValue(chargeFormItem.getUsageInfoJson(), UsageInfo.class);
                            ExecuteFormItemPrintView executeFormItemPrintView = new ExecuteFormItemPrintView();
                            executeFormItemPrintView.setComposeType(chargeFormItem.getComposeType());
                            executeFormItemPrintView.setCreated(chargeFormItem.getCreated());
                            executeFormItemPrintView.setGroupId(chargeFormItem.getGroupId());
                            executeFormItemPrintView.setSort(chargeFormItem.getSort());
                            if (firstItemUsageInfo != null) {
                                BeanUtils.copyProperties(firstItemUsageInfo, executeFormItemPrintView);
                            }
                            ExecuteFormSubItemPrintView subItemPrintView = new ExecuteFormSubItemPrintView();
                            if (firstItemUsageInfo != null) {
                                BeanUtils.copyProperties(firstItemUsageInfo, subItemPrintView);
                            }

                            ChargeFormItemUtils.PrintUnitPriceAndPrintTotalPriceResult printUnitPriceAndPrintTotalPriceResult = ChargeFormItemUtils.generatePrintUnitPriceAndPrintTotalPrice(chargeFormItem, chargeSheetStatus);

                            subItemPrintView.setSourceFormItemId(chargeFormItem.getSourceFormItemId());
                            subItemPrintView.setName(chargeFormItem.getName());
                            subItemPrintView.setUnit(chargeFormItem.getUnit());
                            subItemPrintView.setUnitPrice(printUnitPriceAndPrintTotalPriceResult.getPrintUnitPrice());
                            subItemPrintView.setUnitCount(MathUtils.wrapBigDecimalSubtract(chargeFormItem.getUnitCount(), chargeFormItem.getRefundUnitCount()));
                            subItemPrintView.setTotalPrice(printUnitPriceAndPrintTotalPriceResult.getPrintTotalPrice());
                            subItemPrintView.setProductId(chargeFormItem.getProductId());
                            subItemPrintView.setProductInfo(JsonUtils.loadAsJsonNode(chargeFormItem.getProductSnapshot()));
                            subItemPrintView.setIsGift(chargeFormItem.getIsGift());
                            subItemPrintView.setSourceItemType(chargeFormItem.getSourceItemType());
                            executeFormItemPrintView.setGroupItems(new ArrayList<>());
                            executeFormItemPrintView.getGroupItems().add(subItemPrintView);
                            return executeFormItemPrintView;
                        }).collect(Collectors.toList());
                    } else {
                        UsageInfo firstItemUsageInfo = JsonUtils.readValue(firstItem.getUsageInfoJson(), UsageInfo.class);
                        ExecuteFormItemPrintView executeFormItemPrintView = new ExecuteFormItemPrintView();
                        executeFormItemPrintView.setCreated(firstItem.getCreated());
                        executeFormItemPrintView.setGroupId(firstItem.getGroupId());
                        executeFormItemPrintView.setSort(firstItem.getSort());
                        executeFormItemPrintView.setComposeType(firstItem.getComposeType());
                        if (firstItemUsageInfo != null) {
                            BeanUtils.copyProperties(firstItemUsageInfo, executeFormItemPrintView);
                        }

                        executeFormItemPrintView.setGroupItems(groupItems.stream().sorted((a, b) -> ObjectUtils.compare(a.getSort(), b.getSort())).map(subItem -> {
                            UsageInfo subItemUsageInfo = JsonUtils.readValue(subItem.getUsageInfoJson(), UsageInfo.class);
                            ExecuteFormSubItemPrintView subItemPrintView = new ExecuteFormSubItemPrintView();
                            if (subItemUsageInfo != null) {
                                BeanUtils.copyProperties(subItemUsageInfo, subItemPrintView);
                            }

                            ChargeFormItemUtils.PrintUnitPriceAndPrintTotalPriceResult printUnitPriceAndPrintTotalPriceResult = ChargeFormItemUtils.generatePrintUnitPriceAndPrintTotalPrice(subItem, chargeSheetStatus);

                            subItemPrintView.setSourceFormItemId(subItem.getSourceFormItemId());
                            subItemPrintView.setName(subItem.getName());
                            subItemPrintView.setUnit(subItem.getUnit());
                            subItemPrintView.setUnitPrice(printUnitPriceAndPrintTotalPriceResult.getPrintUnitPrice());
                            subItemPrintView.setUnitCount(MathUtils.wrapBigDecimalSubtract(subItem.getUnitCount(), subItem.getRefundUnitCount()));
                            subItemPrintView.setTotalPrice(printUnitPriceAndPrintTotalPriceResult.getPrintTotalPrice());
                            subItemPrintView.setProductId(subItem.getProductId());
                            subItemPrintView.setSourceItemType(subItem.getSourceItemType());
                            subItemPrintView.setProductInfo(JsonUtils.loadAsJsonNode(subItem.getProductSnapshot()));
                            return subItemPrintView;
                        }).collect(Collectors.toList()));
                        printViews.add(executeFormItemPrintView);
                    }
                    return printViews;

                })
                .flatMap(executeFormItemPrintViews -> executeFormItemPrintViews.stream())
                .sorted((a, b) -> ObjectUtils.compare(a.getSort(), b.getSort()))
                .collect(Collectors.toList());

        int printType = chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_INFUSION ? ExecuteFormPrintView.Type.INFUSION_MEDICINE : ExecuteFormPrintView.Type.WESTERN_MEDICINE;

        List<PrescriptionForm> prescriptionForms = Optional.ofNullable(outpatientSheet)
                .map(OutpatientSheet::getPrescriptionForms).orElse(new ArrayList<>())
                .stream()
                .filter(prescriptionForm -> prescriptionForm.getType() == PrescriptionForm.TYPE_WESTERN || prescriptionForm.getType() == PrescriptionForm.TYPE_INFUSION)
                .collect(Collectors.toList());

        Map<String, PrescriptionFormItem> prescriptionFormItemMap = prescriptionForms.stream()
                .filter(prescriptionForm -> prescriptionForm.getPrescriptionFormItems() != null)
                .flatMap(prescriptionForm -> prescriptionForm.getPrescriptionFormItems().stream())
                .filter(Objects::nonNull)
                .filter(prescriptionFormItem -> prescriptionFormItem.getAst() != null)
                .collect(Collectors.toMap(PrescriptionFormItem::getId, Function.identity(), (a, b) -> a));


        itemPrintViews.stream()
                .filter(itemPrintView -> itemPrintView.getGroupItems() != null)
                .flatMap(itemPrintView -> itemPrintView.getGroupItems().stream())
                .forEach(groupItem -> {
                    PrescriptionFormItem prescriptionFormItem = prescriptionFormItemMap.getOrDefault(groupItem.getSourceFormItemId(), null);
                    if (prescriptionFormItem != null) {
                        groupItem.setAst(prescriptionFormItem.getAst());
                        groupItem.setAstResult(prescriptionFormItem.getAstResult());
                    }
                });

        ExecuteFormPrintView formPrintView = new ExecuteFormPrintView();
        formPrintView.setExecuteFormItems(itemPrintViews);
        formPrintView.setType(printType);
        formPrintView.setChargeStatus(chargeForm.getStatus());
        return formPrintView;
    }


    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public EyeGlassPrintView getEyeGlassPrintView(String chargeSheetId) {
        if (TextUtils.isEmpty(chargeSheetId)) {
            return null;
        }

        ChargeSheet chargeSheet = mChargeSheetService.findById(chargeSheetId);

        if (chargeSheet == null) {
            return null;
        }

        PatientOrder patientOrder = patientOrderService.findPatientOrder(chargeSheet.getPatientOrderId(), PatientOrderService.getAgeLockTime(chargeSheet));

        cn.abcyun.bis.rpc.sdk.cis.model.clinic.OrganPrintView organPrintInfo = mOrganProductService.getOrganPrintInfo(chargeSheet.getClinicId());

        PatientInfo patientInfo = crmService.searchPatientInfoByPatientIdFromCrm(chargeSheet.getChainId(), chargeSheet.getClinicId(), patientOrder.getPatientId(), false, false);

        EyeGlassPrintView eyeGlassPrintView = new EyeGlassPrintView();

        eyeGlassPrintView.setId(chargeSheet.getId());
        eyeGlassPrintView.setPatient(patientInfo);
        eyeGlassPrintView.setOrgan(organPrintInfo);
        eyeGlassPrintView.setChargeForms(new ArrayList<>());
        eyeGlassPrintView.setPatientOrderNo(String.format("%08d", patientOrder.getNo()));
        eyeGlassPrintView.setCreated(chargeSheet.getCreated());


        if (CollectionUtils.isEmpty(chargeSheet.getChargeForms())) {
            return eyeGlassPrintView;
        }

        List<String> employeeIds = new ArrayList<>();


        chargeSheet.getChargeForms().stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeFormPrintView -> chargeFormPrintView.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_TYPE_EYE_GLASS).forEach(chargeForm -> {
                    ChargeFormPrintView chargeFormPrintView = new ChargeFormPrintView();
                    chargeFormPrintView.setSourceFormType(chargeForm.getSourceFormType());
                    chargeFormPrintView.setPrintFormType(chargeForm.getSourceFormType());
                    chargeFormPrintView.setId(chargeForm.getId());
                    cn.abcyun.cis.commons.rpc.outpatient.UsageInfo usageInfo = cn.abcyun.cis.charge.util.JsonUtils.readValue(chargeForm.getUsageInfoJson(), cn.abcyun.cis.commons.rpc.outpatient.UsageInfo.class);
                    if (usageInfo != null) {
                        BeanUtils.copyProperties(usageInfo, chargeFormPrintView);
                        chargeFormPrintView.setGlassesParams(usageInfo.getGlassesParams());
                    }

                    eyeGlassPrintView.getChargeForms().add(chargeFormPrintView);

                });


        //如果收费单含有配镜处方需要绑定验光师姓名
        List<String> optometristList = eyeGlassPrintView.getChargeForms().stream()
                .filter(form -> form.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_TYPE_EYE_GLASS && !org.springframework.util.StringUtils.isEmpty(form.getOptometristId()))
                .map(form -> form.getOptometristId())
                .collect(Collectors.toList());
        if (!org.springframework.util.CollectionUtils.isEmpty(optometristList)) {
            employeeIds.addAll(optometristList);
        }


        Map<String, String> employeeNameMap = mEmployeeService.findEmployeeList(chargeSheet.getChainId(), employeeIds).stream().collect(Collectors.toMap(Employee::getId, Employee::getName, (a, b) -> a));

        /**
         * 绑定验光师姓名
         */
        eyeGlassPrintView.getChargeForms().stream()
                .filter(form -> form.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_TYPE_EYE_GLASS && !org.springframework.util.StringUtils.isEmpty(form.getOptometristId()))
                .forEach(form -> form.setOptometristName(employeeNameMap.get(form.getOptometristId())));

        return eyeGlassPrintView;
    }


    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public ChargeSheetView getChargeSheetPrintViewForMedicineLabel(String chargeSheetId) {
        if (TextUtils.isEmpty(chargeSheetId)) {
            return null;
        }

        ChargeSheet chargeSheet = mChargeSheetService.findById(chargeSheetId);

        if (chargeSheet == null) {
            return null;
        }

        MedicalRecord medicalRecord = outpatientService.findMedicalRecord(chargeSheet.getPatientOrderId());

        PatientOrder patientOrder = patientOrderService.findPatientOrder(chargeSheet.getPatientOrderId(), PatientOrderService.getAgeLockTime(chargeSheet));
        ChargeSheet registrationChargeSheet = null;
        if (!TextUtils.isEmpty(chargeSheet.getRegistrationChargeSheetId())) {
            registrationChargeSheet = mChargeSheetService.findById(chargeSheet.getRegistrationChargeSheetId());
            if (registrationChargeSheet != null && registrationChargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED) {
                ChargeService.moveRegistrationChargeSheetToOutpatientChargeSheet(registrationChargeSheet, chargeSheet);
            }
        }

        SheetProcessor sheetProcessor = new SheetProcessor(chargeSheet);
        sheetProcessor.setSheetProcessorInfoProvider(sheetProcessorService);
        sheetProcessor.setMedicalRecord(medicalRecord);
        sheetProcessor.setPatientOrder(patientOrder);
        sheetProcessor.build();

        ChargeSheetView chargeSheetView = sheetProcessor.generateSheetDetail(false, false, Constants.ChargeSource.CHARGE, true);

        if (Objects.isNull(chargeSheetView)) {
            return null;
        }

        if (CollectionUtils.isEmpty(chargeSheetView.getChargeForms())) {
            return chargeSheetView;
        }

        chargeSheetView.getChargeForms().stream()
                .filter(chargeFormPrintView -> chargeFormPrintView.getChargeFormItems() != null)
                .forEach(chargeFormPrintView -> chargeFormPrintView.getChargeFormItems().removeIf(chargeFormItemPrintView -> chargeFormItemPrintView.getProductType() != Constants.ProductType.MEDICINE));
        chargeSheetView.getChargeForms().removeIf(chargeFormPrintView -> CollectionUtils.isEmpty(chargeFormPrintView.getChargeFormItems()));
        return chargeSheetView;
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public ChargeSheetAstResultView getChargeSheetAstResultView(String chargeSheetId, String clinicId) {

        ChargeSheetAstResultView resultView = new ChargeSheetAstResultView();
        resultView.setList(new ArrayList<>());
        ChargeSheetAstResultRsp chargeSheetAstResult = getChargeSheetAstResult(chargeSheetId, clinicId);
        if (Objects.isNull(chargeSheetAstResult) || CollectionUtils.isEmpty(chargeSheetAstResult.getList())) {
            return resultView;
        }
        chargeSheetAstResult.getList().forEach(item -> {
            ChargeSheetAstResultView.AstResultItem astResultItem = new ChargeSheetAstResultView.AstResultItem();
            astResultItem.setFormItemId(item.getChargeFormItemId());
            astResultItem.setGoodsId(item.getGoodsId());
            astResultItem.setAst(item.getAst());
            astResultItem.setAstResult(item.getAstResult());
            resultView.getList().add(astResultItem);
        });
        return resultView;
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public ChargeSheetAstResultRsp getChargeSheetAstResult(String chargeSheetId, String clinicId) {
        ChargeSheet chargeSheet = mChargeSheetService.findByIdAndClinicIdAndIsDeleted(chargeSheetId, clinicId);
        if (Objects.isNull(chargeSheet) || chargeSheet.getType() != ChargeSheet.Type.OUTPATIENT) {
            return null;
        }
        List<ChargeForm> chargeForms = chargeSheet.getChargeForms();
        if (CollectionUtils.isEmpty(chargeForms)) {
            return null;
        }
        List<ChargeForm> infusionChargeFormList =
                chargeForms.stream()
                        .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                        .filter(it -> it.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_INFUSION || it.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_WESTERN).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(infusionChargeFormList)) {
            return null;
        }
        OutpatientSheetExtend outpatientSheetExtend = outpatientService.findOutpatientSheetExtend(chargeSheet.getPatientOrderId());
        if (Objects.isNull(outpatientSheetExtend)) {
            return null;
        }
        List<PrescriptionForm> prescriptionForms = new ArrayList<>();
        if (!CollectionUtils.isEmpty(outpatientSheetExtend.getPrescriptionInfusionForms())) {
            prescriptionForms.addAll(outpatientSheetExtend.getPrescriptionInfusionForms());
        }
        if (!CollectionUtils.isEmpty(outpatientSheetExtend.getPrescriptionWesternForms())) {
            prescriptionForms.addAll(outpatientSheetExtend.getPrescriptionWesternForms());
        }
        Map<String, PrescriptionFormItem> prescriptionFormItemToFormItem = prescriptionForms.stream()
                .filter(prescriptionForm -> !CollectionUtils.isEmpty(prescriptionForm.getPrescriptionFormItems()))
                .flatMap(prescriptionForm -> prescriptionForm.getPrescriptionFormItems().stream())
                .filter(Objects::nonNull)
                .filter(prescriptionFormItem -> prescriptionFormItem.getAst() != null)
                .collect(Collectors.toMap(PrescriptionFormItem::getId, Function.identity(), (a, b) -> a));

        ChargeSheetAstResultRsp resultRsp = new ChargeSheetAstResultRsp();
        resultRsp.setList(new ArrayList<>());
        chargeForms.forEach(form -> form.getChargeFormItems()
                .stream()
                .filter(item -> item.getIsDeleted() == 0)
                .forEach(formItem -> {
                    ChargeSheetAstResult astResult = new ChargeSheetAstResult();
                    astResult.setChargeFormItemId(formItem.getId());
                    astResult.setGoodsId(formItem.getProductId());
                    PrescriptionFormItem prescriptionFormItem = prescriptionFormItemToFormItem.get(formItem.getSourceFormItemId());
                    if (Objects.nonNull(prescriptionFormItem)) {
                        astResult.setAst(prescriptionFormItem.getAst());
                        astResult.setAstResult(prescriptionFormItem.getAstResult());
                    }
                    resultRsp.getList().add(astResult);
                }));

        return resultRsp;
    }

    public CoOutpatientSheetPrintView getCoOutpatientSheetPrintView(String clinicId, String cooperationOrderId) {
        ChargeCooperationOrder cooperationOrder = cooperationOrderService.findById(cooperationOrderId, clinicId);
        if (cooperationOrder == null) {
            throw new ChargeServiceException(ChargeServiceError.COOPERATION_ORDER_NOT_EXIST);
        }

        CoOutpatientSheetPrintInfoReq req = new CoOutpatientSheetPrintInfoReq();
        req.setCoChainId(cooperationOrder.getChainId());
        req.setCoClinicId(cooperationOrder.getClinicId());
        req.setSourceChainId(cooperationOrder.getSourceChainId());
        req.setSourceClinicId(cooperationOrder.getSourceClinicId());
        req.setSourceSheetId(cooperationOrder.getSourceSheetId());
        req.setSourceFormId(cooperationOrder.getSourceFormId());

        CoOutpatientSheetPrintInfoRsp rsp = coPharmacyService.getSourceOutpatientSheetPrintInfo(req);
        CoOutpatientSheetPrintView printView = new CoOutpatientSheetPrintView();
        printView.setPrintInfo(rsp.getPrintInfo());
        printView.setPrintSetting(rsp.getPrintSetting());
        printView.setClinicBasicSetting(rsp.getClinicBasicSetting());
        return printView;
    }
}
