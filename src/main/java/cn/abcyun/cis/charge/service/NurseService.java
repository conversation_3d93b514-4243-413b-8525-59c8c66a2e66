package cn.abcyun.cis.charge.service;

import cn.abcyun.cis.charge.base.ChargeServiceException;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.controller.StatusNameTranslator;
import cn.abcyun.cis.charge.service.dto.ChargeFormItemView;
import cn.abcyun.cis.charge.service.dto.ChargeSheetView;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.rpc.outpatient.ExaminationForm;
import cn.abcyun.cis.commons.rpc.outpatient.ExaminationFormItem;
import cn.abcyun.cis.commons.rpc.outpatient.OutpatientSheetExtend;
import cn.abcyun.cis.commons.rpc.outpatient.PrescriptionForm;
import cn.abcyun.cis.commons.rpc.outpatient.PrescriptionFormItem;
import cn.abcyun.cis.commons.rpc.outpatient.ProductFormItemView;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class NurseService {

    @Autowired
    private ChargeExecuteService mChargeExecuteService;

    @Autowired
    private EmployeeService employeeService;

    public ChargeSheetView patchChargeSheetViewForNurse(ChargeSheetView chargeSheetView, OutpatientSheetExtend outpatientSheetExtend) throws NotFoundException, ChargeServiceException {
        //补充检验结果
        patchChargeSheetViewExaminationResult(chargeSheetView, outpatientSheetExtend);

        //补充皮试信息
        patchChargeSheetViewAst(chargeSheetView, outpatientSheetExtend);

        //补充执行项信息
        mChargeExecuteService.patchExecuteInfo(chargeSheetView);

        //过滤赠品只留下治疗理疗和检查检验
        filterGiftFormTreatmentAndExamination(chargeSheetView);

        //更新状态
        chargeSheetView.setStatusName(StatusNameTranslator.translateChargeSheetStatusForNurse(chargeSheetView.getIsClosed(), chargeSheetView.getStatus(), chargeSheetView.getExecuteStatus(), chargeSheetView.getOnlyExecuteAfterPaid()));

        //处理收费单为部分收费，并且收费项为已收费的情况，子项都处理为未收费
        updateChargeFormItemStatusForChargeSheetPartCharged(chargeSheetView);

        //处理历史的待执行的单子的NeedExecutive
        fillNeedExecutiveForHistory(chargeSheetView);

        //补充门诊备注、外治处方穴位、外治处方的goodsItems
        patchChargeFormItemRemarkAndAcupointsAndExternalGoodsItems(chargeSheetView, outpatientSheetExtend);

        return chargeSheetView;
    }

    private void updateChargeFormItemStatusForChargeSheetPartCharged(ChargeSheetView chargeSheetView) {

        if (chargeSheetView == null
                || chargeSheetView.getChargeForms() == null
                || chargeSheetView.getStatus() != Constants.ChargeSheetStatus.PART_CHARGED) {
            return;
        }

        chargeSheetView.getChargeForms()
                .stream()
                .filter(chargeFormView -> chargeFormView.getChargeFormItems() != null)
                .flatMap(chargeFormView -> chargeFormView.getChargeFormItems().stream())
                .filter(chargeFormItemView -> chargeFormItemView.getStatus() == Constants.ChargeFormItemStatus.CHARGED)
                .forEach(chargeFormItemView -> {
                    chargeFormItemView.setStatus(Constants.ChargeFormItemStatus.UNCHARGED);
                    if (chargeFormItemView.getProductType() == Constants.ProductType.COMPOSE_PRODUCT && chargeFormItemView.getComposeChildren() != null) {
                        chargeFormItemView.getComposeChildren().stream()
                                .filter(child -> child.getStatus() == Constants.ChargeFormItemStatus.CHARGED)
                                .forEach(child -> child.setStatus(Constants.ChargeFormItemStatus.UNCHARGED));
                    }
                });

    }

    private void patchChargeFormItemRemarkAndAcupointsAndExternalGoodsItems(ChargeSheetView chargeSheetView, OutpatientSheetExtend outpatientSheetExtend) {

        if (chargeSheetView == null || outpatientSheetExtend == null || chargeSheetView.getChargeForms() == null) {
            return;
        }

        List<PrescriptionForm> prescriptionExternalForms = new ArrayList<>();
        if (outpatientSheetExtend.getPrescriptionExternalForms() != null) {
            prescriptionExternalForms.addAll(outpatientSheetExtend.getPrescriptionExternalForms());
        }
        Map<String, String> productFormItemIdRemarkMap = Optional.ofNullable(outpatientSheetExtend.getProductForms())
                .orElse(Lists.newArrayList())
                .stream()
                .flatMap(productFormView -> Optional.ofNullable(productFormView.getProductFormItems()).orElse(Lists.newArrayList()).stream())
                .collect(Collectors.toMap(ProductFormItemView::getId, productFormItemView -> Objects.toString(productFormItemView.getRemark(), "")));


        Map<String, PrescriptionFormItem> prescriptionFormItemMap = prescriptionExternalForms.stream()
                .filter(prescriptionForm -> prescriptionForm.getPrescriptionFormItems() != null)
                .flatMap(prescriptionForm -> prescriptionForm.getPrescriptionFormItems().stream())
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(PrescriptionFormItem::getId, Function.identity(), (a, b) -> a));

        chargeSheetView.getChargeForms()
                .stream()
                .filter(chargeFormView -> chargeFormView.getChargeFormItems() != null)
                .filter(chargeFormView -> chargeFormView.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_EXTERNAL
                        || chargeFormView.getSourceFormType() == Constants.SourceFormType.TREATMENT
                        || chargeFormView.getSourceFormType() == Constants.SourceFormType.COMPOSE_PRODUCT
                )
                .forEach(chargeFormView -> {
                    List<ChargeFormItemView> chargeFormItems = chargeFormView.getChargeFormItems();
                    if (chargeFormView.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_EXTERNAL) {
                        chargeFormItems.forEach(chargeFormItemView -> {
                                PrescriptionFormItem prescriptionFormItem = prescriptionFormItemMap.getOrDefault(chargeFormItemView.getSourceFormItemId(), null);
                                if (prescriptionFormItem != null) {
                                    chargeFormItemView.setAcupoints(prescriptionFormItem.getAcupoints());
                                    chargeFormItemView.setExternalGoodsItems(prescriptionFormItem.getExternalGoodsItems());
                                }
                        });
                    }
                    if (chargeFormView.getSourceFormType() == Constants.SourceFormType.TREATMENT || chargeFormView.getSourceFormType() == Constants.SourceFormType.COMPOSE_PRODUCT) {
                        chargeFormItems.forEach(chargeFormItem->chargeFormItem.setOutpatientRemark(productFormItemIdRemarkMap.getOrDefault(chargeFormItem.getSourceFormItemId(), "")));
                    }
                });
    }

    private void filterGiftFormTreatmentAndExamination(ChargeSheetView chargeSheetView) {

        if (chargeSheetView == null || CollectionUtils.isEmpty(chargeSheetView.getChargeForms())) {
            return;
        }
        chargeSheetView.getChargeForms().stream()
                .filter(chargeFormView -> !CollectionUtils.isEmpty(chargeFormView.getChargeFormItems()))
                .filter(chargeFormView -> chargeFormView.getSourceFormType() == Constants.SourceFormType.GIFT_PRODUCT)
                .forEach(chargeFormView -> chargeFormView.getChargeFormItems()
                        .removeIf(chargeFormItemView ->
                                chargeFormItemView.getProductType() != Constants.ProductType.TREATMENT &&
                                chargeFormItemView.getProductType() != Constants.ProductType.EXAMINATION &&
                                chargeFormItemView.getProductType() != Constants.ProductType.COMPOSE_PRODUCT
                        )
                );
        //移除掉没有item的赠品form
        chargeSheetView.getChargeForms().removeIf(chargeFormView -> chargeFormView.getSourceFormType() == Constants.SourceFormType.GIFT_PRODUCT && org.apache.commons.collections.CollectionUtils.isEmpty(chargeFormView.getChargeFormItems()));
    }

    private void fillNeedExecutiveForHistory(ChargeSheetView chargeSheetView) {
        if (chargeSheetView == null || CollectionUtils.isEmpty(chargeSheetView.getChargeForms())) {
            return;
        }

        chargeSheetView.getChargeForms()
                .stream()
                .filter(chargeFormView -> !CollectionUtils.isEmpty(chargeFormView.getChargeFormItems()))
                .flatMap(chargeFormView -> chargeFormView.getChargeFormItems().stream())
                .filter(chargeFormItemView -> chargeFormItemView.getProductType() == Constants.ProductType.TREATMENT)
                .filter(chargeFormItemView -> chargeFormItemView.getProductInfo() != null)
                .forEach(chargeFormItemView -> {
                    JsonNode needExecutive = chargeFormItemView.getProductInfo().get("needExecutive");
                    if (needExecutive == null) {
                        ((ObjectNode) chargeFormItemView.getProductInfo()).put("needExecutive", 1);
                    }
                });

        chargeSheetView.getChargeForms().stream()
                .filter(chargeFormView -> !CollectionUtils.isEmpty(chargeFormView.getChargeFormItems()))
                .flatMap(chargeFormView -> chargeFormView.getChargeFormItems().stream())
                .filter(chargeFormItemView -> chargeFormItemView.getProductType() == Constants.ProductType.COMPOSE_PRODUCT && !CollectionUtils.isEmpty(chargeFormItemView.getComposeChildren()))
                .forEach(chargeFormItemView -> {
                    chargeFormItemView.getComposeChildren().stream()
                            .filter(composeChargeFormItemView -> composeChargeFormItemView.getProductType() == Constants.ProductType.TREATMENT)
                            .filter(composeChargeFormItemView -> composeChargeFormItemView.getProductInfo() != null)
                            .forEach(composeChargeFormItemView -> {
                                JsonNode needExecutive = composeChargeFormItemView.getProductInfo().get("needExecutive");
                                if (needExecutive == null) {
                                    ((ObjectNode) composeChargeFormItemView.getProductInfo()).put("needExecutive", 1);
                                }
                            });
                });
    }

    public void patchChargeSheetViewAst(ChargeSheetView chargeSheetView, OutpatientSheetExtend outpatientSheetExtend) {
        if (chargeSheetView == null || outpatientSheetExtend == null || chargeSheetView.getChargeForms() == null) {
            return;
        }

        List<PrescriptionForm> prescriptionForms = new ArrayList<>();
        if (outpatientSheetExtend.getPrescriptionInfusionForms() != null) {
            prescriptionForms.addAll(outpatientSheetExtend.getPrescriptionInfusionForms());
        }
        if (outpatientSheetExtend.getPrescriptionWesternForms() != null) {
            prescriptionForms.addAll(outpatientSheetExtend.getPrescriptionWesternForms());
        }
        Map<String, PrescriptionFormItem> prescriptionFormItemMap = prescriptionForms.stream()
                .filter(prescriptionForm -> prescriptionForm.getPrescriptionFormItems() != null)
                .flatMap(prescriptionForm -> prescriptionForm.getPrescriptionFormItems().stream())
                .filter(Objects::nonNull)
                .filter(prescriptionFormItem -> prescriptionFormItem.getAst() != null)
                .collect(Collectors.toMap(PrescriptionFormItem::getId, Function.identity(), (a, b) -> a));

        chargeSheetView.getChargeForms().stream()
                .filter(chargeFormView -> chargeFormView.getChargeFormItems() != null)
                .flatMap(chargeFormView -> chargeFormView.getChargeFormItems().stream())
                .forEach(chargeFormItemView -> {
                    PrescriptionFormItem prescriptionFormItem = prescriptionFormItemMap.getOrDefault(chargeFormItemView.getSourceFormItemId(), null);
                    if (prescriptionFormItem != null) {
                        chargeFormItemView.setAst(prescriptionFormItem.getAst());
                        chargeFormItemView.setAstResult(prescriptionFormItem.getAstResult());
                    }
                });
    }

    public void patchChargeSheetViewExaminationResult(ChargeSheetView chargeSheetView, OutpatientSheetExtend outpatientSheetExtend) {
        if (chargeSheetView == null || outpatientSheetExtend == null || chargeSheetView.getChargeForms() == null) {
            return;
        }

        List<ExaminationForm> examinationForms = new ArrayList<>();
        if (outpatientSheetExtend.getExaminationForms() != null) {
            examinationForms.addAll(outpatientSheetExtend.getExaminationForms());
        }

        Map<String, ExaminationFormItem> examinationFormItemMap = examinationForms.stream()
                .filter(examinationForm -> examinationForm.getExaminationFormItems() != null)
                .flatMap(examinationForm -> examinationForm.getExaminationFormItems().stream())
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(ExaminationFormItem::getId, Function.identity(), (a, b) -> a));

        chargeSheetView.getChargeForms().stream()
                .filter(chargeFormView -> chargeFormView.getChargeFormItems() != null)
                .flatMap(chargeFormView -> chargeFormView.getChargeFormItems().stream())
                .forEach(chargeFormItemView -> {
                    ExaminationFormItem examinationFormItem = examinationFormItemMap.getOrDefault(chargeFormItemView.getSourceFormItemId(), null);
                    if (examinationFormItem != null) {
                        chargeFormItemView.setExaminationResult(examinationFormItem.getExaminationResult());
                    }
                });
    }
}
