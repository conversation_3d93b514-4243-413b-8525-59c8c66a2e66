package cn.abcyun.cis.charge.service;

import cn.abcyun.cis.charge.config.OrderNoGenerator;
import cn.abcyun.cis.charge.processor.SheetProcessorInfoProvider;
import cn.abcyun.cis.charge.processor.provider.*;
import cn.abcyun.cis.charge.service.rpc.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SheetProcessorService implements SheetProcessorInfoProvider {

    @Autowired
    private  ChargeRuleService chargeRuleService;
    @Autowired
    private  OrganProductService mOrganProductService;
    @Autowired
    private  EmployeeService mEmployeeService;
    @Autowired
    private  PatientService mPatientService;
    @Autowired
    private  DispensingService mDispensingService;
    @Autowired
    private  OutpatientService mOutpatientService;
    @Autowired
    private  ChargePayService mChargePayService;
    @Autowired
    private  ChargeMedicareLimitPriceService chargeMedicareLimitPriceService;
    @Autowired
    private  ChargeConfigService chargeConfigService;
    @Autowired
    private  PromotionService promotionService;
    @Autowired
    private  ShebaoService shebaoService;
    @Autowired
    private  ChargeAirPharmacyService airPharmacyService;
    @Autowired
    private  CisScGoodsService goodsSCService;
    @Autowired
    private  ChargeSheetInvoiceService chargeSheetInvoiceService;
    @Autowired
    private  DynamicConfigService dynamicConfigService;
    @Autowired
    private  CisScClinicService cisScClinicService;
    @Autowired
    private  PropertyProviderService propertyProviderService;
    @Autowired
    private  ChargeOweSheetService chargeOweSheetService;
    @Autowired
    private  ChargeExecuteService chargeExecuteService;
    @Autowired
    private  ExaminationService examinationService;
    @Autowired
    private CisGoodsLockingService cisGoodsLockingService;

    @Autowired
    private CisGoodsService cisGoodsService;

    @Autowired
    private DeliveryService deliveryService;

    @Autowired
    private OrderNoGenerator orderNoGenerator;

    @Autowired
    private RegistrationService registrationService;

    @Autowired
    private CisMallOrderProvider cisMallOrderProvider;

    @Autowired
    private ChargeCooperationOrderService chargeCooperationOrderService;

    @Autowired
    private CisPatientOrderService cisPatientOrderService;

    @Override
    public ProductInfoProvider getProductInfoProvider() {
        return mOrganProductService;
    }

    @Override
    public GoodsScProvider getGoodsScProvider() {
        return goodsSCService;
    }

    @Override
    public PatientInfoProvider getPatientInfoProvider() {
        return mPatientService;
    }

    @Override
    public PatientOrderProvider getPatientOrderProvider() {
        return cisPatientOrderService;
    }

    @Override
    public EmployeeInfoProvider getEmployeeInfoProvider() {
        return mEmployeeService;
    }

    @Override
    public DispensingInfoProvider getDispensingInfoProvider() {
        return mDispensingService;
    }

    @Override
    public OutpatientInfoProvider getOutpatientInfoProvider() {
        return mOutpatientService;
    }

    @Override
    public ChargePayProvider getChargePayProvider() {
        return mChargePayService;
    }

    @Override
    public LimitPriceProvider getLimitPriceProvider() {
        return chargeMedicareLimitPriceService;
    }

    @Override
    public ChargePayModeProvider getChargePayModeProvider() {
        return chargeConfigService;
    }

    @Override
    public PromotionProvider getPromotionProvider() {
        return promotionService;
    }

    @Override
    public ChargeRuleProvider getChargeRuleProvider() {
        return chargeRuleService;
    }

    @Override
    public ShebaoInfoProvider getShebaoInfoProvider() {
        return shebaoService;
    }

    @Override
    public AirPharmacyProvider getAirPharmacyProvider() {
        return airPharmacyService;
    }

    @Override
    public ChargeSheetInvoiceProvider getChargeSheetInvoiceProvider() {
        return chargeSheetInvoiceService;
    }

    @Override
    public DynamicConfigProvider getDynamicConfigProvider() {
        return dynamicConfigService;
    }

    @Override
    public ClinicProvider getClinicProvider() {
        return cisScClinicService;
    }

    @Override
    public PropertyProvider getPropertyProvider() {
        return propertyProviderService;
    }

    @Override
    public ChargeOweSheetProvider getChargeOweSheetProvider() {
        return chargeOweSheetService;
    }

    @Override
    public ChargeExecuteProvider getChargeExecuteProvider() {
        return chargeExecuteService;
    }

    @Override
    public ChargeExaminationProvider getChargeExaminationProvider() {
        return examinationService;
    }

    @Override
    public GoodsLockingProvider getGoodsLockingProvider() {
        return cisGoodsLockingService;
    }

    @Override
    public CisGoodsProvider getCisGoodsProvider() {
        return cisGoodsService;
    }

    @Override
    public DeliveryProvider getDeliveryProvider() {
        return deliveryService;
    }

    @Override
    public OrderNoGeneratorProvider getOrderNoGeneratorProvider() {
        return orderNoGenerator;
    }

    @Override
    public RegistrationProvider getRegistrationProvider() {
        return registrationService;
    }

    @Override
    public CisMallOrderProvider getCisMallOrderProvider() {
        return cisMallOrderProvider;
    }

    @Override
    public ChargeCooperationOrderProvider getChargeCooperationOrderProvider() {
        return chargeCooperationOrderService;
    }

}
