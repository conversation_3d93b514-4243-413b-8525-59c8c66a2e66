package cn.abcyun.cis.charge.service.rpc;

import cn.abcyun.bis.rpc.sdk.cis.client.AbcCisClinicFeignClient;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.ClinicWithAddress;
import cn.abcyun.cis.core.util.FeignClientRpcTemplate;
import feign.RetryableException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CisClinicService {

    @Autowired
    private AbcCisClinicFeignClient client;

    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public ClinicWithAddress queryClinicById(String clinicId) {

        return FeignClientRpcTemplate.dealRpcClientMethod("queryClinicById",
                () -> client.queryClinicById(clinicId), clinicId);
    }

}
