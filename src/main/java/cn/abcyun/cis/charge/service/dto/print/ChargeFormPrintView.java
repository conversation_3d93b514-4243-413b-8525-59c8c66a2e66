package cn.abcyun.cis.charge.service.dto.print;

import cn.abcyun.cis.charge.util.DateUtils;
import cn.abcyun.cis.commons.rpc.outpatient.EyeExamination;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Data
public class ChargeFormPrintView {
    private String id;
    private List<ChargeFormItemPrintView> chargeFormItems;
    private int sourceFormType;
    /**
     * 打印的form类型
     */
    private int printFormType;

    private BigDecimal totalPrice;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String specification;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer doseCount;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String dailyDosage;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String usage;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String freq;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String requirement;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String usageLevel;

    /**
     * 服用天数
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String usageDays;

    @JsonIgnore
    private String processUsage;
    @JsonIgnore
    private int processBagUnitCount;

    private String optometristId;
    private String optometristName;
    /**
     * 眼镜处方-眼镜类型
     */
    private Integer glassesType;
    private EyeExamination glassesParams;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Instant takeMedicationTime;

    public ChargeFormPrintView() {
        chargeFormItems = new ArrayList<>();
    }


    public void addChargeFormItemPrintView(ChargeFormItemPrintView chargeFormItemPrintView) {
        if (chargeFormItems == null) {
            chargeFormItems = new ArrayList<>();
        }
        chargeFormItems.add(chargeFormItemPrintView);
    }

    public void addChargeFormItemPrintViews(Collection<ChargeFormItemPrintView> chargeFormItemPrintViews) {
        if (chargeFormItems == null) {
            chargeFormItems = new ArrayList<>();
        }
        chargeFormItems.addAll(chargeFormItemPrintViews);
    }

    public void calculateTotalPrice() {
        if (chargeFormItems == null) {
            totalPrice = BigDecimal.ZERO;
        }

        totalPrice =  chargeFormItems.stream().map(ChargeFormItemPrintView::getTotalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public String getProcessUsageInfo() {
        String processUsageInfo = "";
        if (processUsage != null && (processUsage.equals("机器煎药") || processUsage.equals("人工煎药"))) {
            processUsageInfo = processUsage;

            if (processBagUnitCount > 0) {
                processUsageInfo = String.format("%s，1剂%s袋", processUsageInfo, processBagUnitCount);
            }
        } else {
            processUsageInfo = processUsage;
        }
        return processUsageInfo;
    }
}
