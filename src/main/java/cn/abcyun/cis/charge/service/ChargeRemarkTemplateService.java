package cn.abcyun.cis.charge.service;

import cn.abcyun.cis.charge.model.ChargeRemarkTemplate;
import cn.abcyun.cis.charge.repository.ChargeRemarkTemplateRepository;
import cn.abcyun.cis.charge.service.dto.ChargeRemarkTemplateAddReq;
import cn.abcyun.cis.charge.service.dto.ChargeRemarkTemplateView;
import cn.abcyun.cis.charge.util.FillUtils;
import cn.abcyun.cis.charge.util.ListUtils;
import cn.abcyun.cis.idgenerator.AbcIdGenerator;
import cn.abcyun.common.model.AbcListPage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024-08-26 17:12
 * @Description
 */

@Service
@Slf4j
public class ChargeRemarkTemplateService {

    @Autowired
    private AbcIdGenerator idGenerator;

    @Autowired
    private ChargeRemarkTemplateRepository chargeRemarkTemplateRepository;

    public AbcListPage<ChargeRemarkTemplateView> list(String clinicId, String chainId) {
        List<ChargeRemarkTemplate> list = chargeRemarkTemplateRepository.findByClinicIdAndIsDeleted(clinicId, 0);
        if (CollectionUtils.isEmpty(list)) {
            return new AbcListPage<>();
        }

        List<ChargeRemarkTemplateView> viewList = list.stream().map(ChargeRemarkTemplateView::from).collect(Collectors.toList());
        AbcListPage<ChargeRemarkTemplateView> page = new AbcListPage<>();
        page.setRows(viewList);
        return page;
    }

    public AbcListPage<ChargeRemarkTemplateView> update(String clinicId, String chainId, String employeeId, ChargeRemarkTemplateAddReq req) {
        List<ChargeRemarkTemplate> list = chargeRemarkTemplateRepository.findByClinicIdAndIsDeleted(clinicId, 0);
        List<ChargeRemarkTemplateView> updateViewList = req.getUpdateTemplateItemList() != null ? req.getUpdateTemplateItemList().stream().filter(view -> view.getId() != null).collect(Collectors.toList()) : new ArrayList<>();
        Map<Long, ChargeRemarkTemplateView> viewMap = updateViewList != null ? ListUtils.toMap(updateViewList, ChargeRemarkTemplateView::getId) : new HashMap<>();
        List<ChargeRemarkTemplate> updateList = new ArrayList<>();
        // list中的元素，如果在viewMap中能找到，就更新；如果在viewMap中找不到，就删除；如果viewMap中的元素没在list中，就添加
        for (ChargeRemarkTemplate chargeRemarkTemplate : list) {
            ChargeRemarkTemplateView view = viewMap.get(chargeRemarkTemplate.getId());
            if (view != null) {
                chargeRemarkTemplate.setRemark(view.getRemark());
                chargeRemarkTemplate.setSort(view.getSort());
                FillUtils.fillLastModifiedBy(chargeRemarkTemplate, employeeId);
                updateList.add(chargeRemarkTemplate);
            } else {
                chargeRemarkTemplate.setIsDeleted(1);
                FillUtils.fillLastModifiedBy(chargeRemarkTemplate, employeeId);
                updateList.add(chargeRemarkTemplate);
            }
        }

        for (ChargeRemarkTemplateView view : req.getUpdateTemplateItemList()) {
            if (view.getId() == null) {
                ChargeRemarkTemplate chargeRemarkTemplate = new ChargeRemarkTemplate();
                chargeRemarkTemplate.setId(idGenerator.getUIDLong());
                chargeRemarkTemplate.setChainId(chainId);
                chargeRemarkTemplate.setClinicId(clinicId);
                chargeRemarkTemplate.setType(0);
                chargeRemarkTemplate.setRemark(view.getRemark());
                chargeRemarkTemplate.setSort(view.getSort());
                FillUtils.fillCreatedBy(chargeRemarkTemplate, employeeId);
                updateList.add(chargeRemarkTemplate);
            }
        }

        chargeRemarkTemplateRepository.saveAll(updateList);

        List<ChargeRemarkTemplateView> viewList = updateList.stream().map(ChargeRemarkTemplateView::from).collect(Collectors.toList());
        AbcListPage<ChargeRemarkTemplateView> page = new AbcListPage<>();
        page.setRows(viewList);

        return page;
    }
}
