package cn.abcyun.cis.charge.service;

import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Employee;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ;
import cn.abcyun.bis.rpc.sdk.cis.model.mc.GetQrCodeRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.message.ToBMessage;
import cn.abcyun.bis.rpc.sdk.cis.model.message.WebMessageBody;
import cn.abcyun.cis.charge.amqp.MQProducer;
import cn.abcyun.cis.charge.amqp.RocketMqProducer;
import cn.abcyun.cis.charge.api.model.BaseSuccessRsp;
import cn.abcyun.cis.charge.api.model.ChargeSheetPushScanCodeRsp;
import cn.abcyun.cis.charge.base.ChargeServiceError;
import cn.abcyun.cis.charge.base.ChargeServiceException;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.mapper.ChargeMapper;
import cn.abcyun.cis.charge.model.ChargeFormItem;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.model.ChargeSheetAdditional;
import cn.abcyun.cis.charge.processor.SheetProcessor;
import cn.abcyun.cis.charge.service.dto.CalculateChargeResult;
import cn.abcyun.cis.charge.service.dto.ChargeSheetAbstract;
import cn.abcyun.cis.charge.service.dto.PatientOrderLockView;
import cn.abcyun.cis.charge.service.rpc.CisMcService;
import cn.abcyun.cis.charge.util.AbcIdUtils;
import cn.abcyun.cis.charge.util.ChargeUtils;
import cn.abcyun.cis.charge.util.JsonUtils;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.cis.charge.util.RedisUtils;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.model.ComposeType;
import cn.abcyun.cis.commons.rpc.crm.PatientInfo;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrder;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.cis.core.db.UseReadOnlyDB;
import cn.abcyun.common.log.marker.AbcLogMarker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ChargePatientOrderService {

    private final ChargeSheetService chargeSheetService;
    private final CisMcService cisMcService;
    private final CrmService crmService;
    private final SheetProcessorService sheetProcessorService;
    private final MQProducer mqProducer;

    private final RocketMqProducer rocketMqProducer;

    private final EmployeeService employeeService;
    private final PatientOrderService patientOrderService;

    private final ChargeMapper chargeMapper;

    private final OrganProductService organProductService;

    @Autowired
    public ChargePatientOrderService(ChargeSheetService chargeSheetService,
                                     CisMcService cisMcService,
                                     CrmService crmService,
                                     SheetProcessorService sheetProcessorService,
                                     MQProducer mqProducer,
                                     RocketMqProducer rocketMqProducer,
                                     EmployeeService employeeService,
                                     PatientOrderService patientOrderService,
                                     ChargeMapper chargeMapper,
                                     OrganProductService organProductService) {

        this.chargeSheetService = chargeSheetService;
        this.cisMcService = cisMcService;
        this.crmService = crmService;
        this.sheetProcessorService = sheetProcessorService;
        this.mqProducer = mqProducer;
        this.rocketMqProducer = rocketMqProducer;
        this.employeeService = employeeService;
        this.patientOrderService = patientOrderService;
        this.chargeMapper = chargeMapper;
        this.organProductService = organProductService;
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public ChargeSheetPushScanCodeRsp getChargeSheetPushScanCodeByPatientOrderId(String patientOrderId, String chainId, String clinicId, int type, String employeeId) {

        List<ChargeSheet> allChargeSheets = chargeSheetService.findAllByPatientOrderIdAndClinicId(patientOrderId, clinicId);

        ChargeSheetPushScanCodeRsp rsp = generateChargeSheetPushScanCodeRsp(allChargeSheets, type);

        if (rsp == null) {
            return null;
        }

        GetQrCodeRsp orderPayQrCode = cisMcService.getOrderPayQrCode(chainId, rsp.getChargeSheetId(), employeeId, clinicId, rsp.getPatientId());

        PatientInfo patientInfo = null;
        // 匿名患者不调用crm查询
        if (!StringUtils.equals(rsp.getPatientId(), Constants.ANONYMOUS_PATIENT_ID)) {
            patientInfo = crmService.searchPatientInfoByPatientIdFromCrm(chainId, clinicId, rsp.getPatientId(), false, false);
        }

        return rsp.setPatientName(Optional.ofNullable(patientInfo).map(PatientInfo::getName).orElse(null))
                .setQrCodeInfo(orderPayQrCode);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public ChargeSheetPushScanCodeRsp getChargeSheetPushScanCodeByChargeSheetId(String chargeSheetId, String chainId, String clinicId, String employeeId) {

        ChargeSheet chargeSheet = chargeSheetService.findById(chargeSheetId);

        if (Objects.isNull(chargeSheet)) {
            throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_NOT_EXISTED);
        }

        ChargeSheetPushScanCodeRsp rsp = generateChargeSheetPushScanCodeRspCore(Collections.singletonList(chargeSheet), chargeSheet.getType());

        if (rsp == null) {
            return null;
        }

        GetQrCodeRsp orderPayQrCode = cisMcService.getOrderPayQrCode(chainId, rsp.getChargeSheetId(), employeeId, rsp.getPatientId(), rsp.getPatientId());

        PatientInfo patientInfo = null;
        // 匿名患者不调用crm查询
        if (!StringUtils.equals(rsp.getPatientId(), Constants.ANONYMOUS_PATIENT_ID)) {
            patientInfo = crmService.searchPatientInfoByPatientIdFromCrm(chainId, clinicId, rsp.getPatientId(), false, false);
        }

        return rsp.setPatientName(Optional.ofNullable(patientInfo).map(PatientInfo::getName).orElse(null))
                .setQrCodeInfo(orderPayQrCode);
    }

    private ChargeSheetPushScanCodeRsp generateChargeSheetPushScanCodeRsp(List<ChargeSheet> allChargeSheets, int type) {
        if (type == Constants.ChargeSheetPushScanCodeType.OUTPATIENT) {
            return generateOutpatientChargeSheetPushScanCodeRsp(allChargeSheets);
        } else if (type == Constants.ChargeSheetPushScanCodeType.NURSE) {
            return generateChargeSheetPushScanCodeRspCore(allChargeSheets, ChargeSheet.Type.THERAPY);
        }
        return null;
    }

    private ChargeSheetPushScanCodeRsp generateChargeSheetPushScanCodeRspCore(List<ChargeSheet> allChargeSheets, Integer chargeType) {
        ChargeSheet latestChargeSheet = allChargeSheets.stream()
                .filter(chargeSheet -> Objects.isNull(chargeType) || chargeSheet.getType() == chargeType)
                .filter(chargeSheet -> chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED || chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_CHARGED)
                .findFirst().orElse(null);

        if (Objects.isNull(latestChargeSheet)) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX,"未找到对应的收费单");
            throw new NotFoundException();
        }

        if (latestChargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_CHARGED) {
            throw new ChargeServiceException(ChargeServiceError.PUSH_CHARGE_SHEET_STATUS_IS_PARTED_PAID);
        }

        if (StringUtils.isNotEmpty(latestChargeSheet.getRegistrationChargeSheetId())) {
            ChargeSheet registrationChargeSheet = chargeSheetService.findById(latestChargeSheet.getRegistrationChargeSheetId());

            if (Objects.nonNull(registrationChargeSheet) && registrationChargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED) {
                ChargeService.moveRegistrationChargeSheetToOutpatientChargeSheet(registrationChargeSheet, latestChargeSheet);
            }
        }


        SheetProcessor sheetProcessor = new SheetProcessor(latestChargeSheet);
        sheetProcessor.setSheetProcessorInfoProvider(sheetProcessorService);
        sheetProcessor.build();
        CalculateChargeResult calculateChargeResult = sheetProcessor.calculateSheetFee(null, null, new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), null, Constants.ChargeSource.BACKGROUND, false, false);

        BigDecimal totalFee = calculateChargeResult != null ? calculateChargeResult.getNeedPayFee() : BigDecimal.ZERO;

        return new ChargeSheetPushScanCodeRsp()
                .setChargeSheetId(latestChargeSheet.getId())
                .setPatientId(latestChargeSheet.getPatientId())
                .setTotalFee(totalFee)
                .setAbstractInfo(ChargeUtils.generateMedicalItems(latestChargeSheet.getChargeForms(), false))
                .setDiagnosis(Optional.ofNullable(latestChargeSheet.getAdditional()).map(ChargeSheetAdditional::getDiagnosis).orElse(null))
                .setNeedPayFee(Optional.ofNullable(calculateChargeResult).map(CalculateChargeResult::getNeedPayFee).orElse(BigDecimal.ZERO));
    }

    private ChargeSheetPushScanCodeRsp generateOutpatientChargeSheetPushScanCodeRsp(List<ChargeSheet> allChargeSheets) {

        ChargeSheet latestUnchargedChargeSheet = allChargeSheets.stream()
                .filter(chargeSheet -> chargeSheet.getType() == ChargeSheet.Type.OUTPATIENT)
                .filter(chargeSheet -> chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED)
                .sorted(Comparator.comparing(ChargeSheet::getLastModified).reversed())
                .findFirst().orElse(null);

        //判断最后一个收费单是否为待收费，如果为待收费，报400错误
        ChargeSheet latestPartedChargedChargeSheet = allChargeSheets.stream()
                .filter(chargeSheet -> chargeSheet.getType() == ChargeSheet.Type.OUTPATIENT)
                .filter(chargeSheet -> chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_CHARGED)
                .sorted(Comparator.comparing(ChargeSheet::getLastModified).reversed())
                .findFirst().orElse(null);

        if (Objects.isNull(latestUnchargedChargeSheet)) {

            if (Objects.nonNull(latestPartedChargedChargeSheet)) {
                throw new ChargeServiceException(ChargeServiceError.PUSH_CHARGE_SHEET_STATUS_IS_PARTED_PAID);
            }

            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX,"未找到门诊单对应的待收费收费单");
            throw new NotFoundException();
        }

        if (StringUtils.isNotEmpty(latestUnchargedChargeSheet.getRegistrationChargeSheetId())) {
            ChargeSheet registrationChargeSheet = chargeSheetService.findById(latestUnchargedChargeSheet.getRegistrationChargeSheetId());

            if (Objects.nonNull(registrationChargeSheet) && registrationChargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED) {
                ChargeService.moveRegistrationChargeSheetToOutpatientChargeSheet(registrationChargeSheet, latestUnchargedChargeSheet);
            }
        }

        //计算收费单对应的门诊单金额
        BigDecimal totalFee = Optional.ofNullable(latestUnchargedChargeSheet.getChargeForms())
                .orElse(new ArrayList<>())
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> StringUtils.isNotEmpty(chargeForm.getSourceFormId()))
                .filter(chargeForm -> CollectionUtils.isNotEmpty(chargeForm.getChargeFormItems()))
                .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                .filter(item -> item.getIsDeleted() == 0)
                .filter(chargeFormItem -> StringUtils.isNotEmpty(chargeFormItem.getSourceFormItemId()) || chargeFormItem.getProductType() == Constants.ProductType.REGISTRATION)
                .filter(chargeFormItem -> chargeFormItem.getComposeType() != ComposeType.COMPOSE_SUB_ITEM)
                .map(chargeFormItem -> MathUtils.wrapBigDecimalAdd(MathUtils.calculateTotalPrice(chargeFormItem.getUnitPrice(), chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount(), 2), chargeFormItem.getFractionPrice()))
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        List<ChargeFormItem> outpatientChargeFormItems = Optional.ofNullable(latestUnchargedChargeSheet.getChargeForms())
                .orElse(new ArrayList<>())
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> StringUtils.isNotEmpty(chargeForm.getSourceFormId()))
                .filter(chargeForm -> CollectionUtils.isNotEmpty(chargeForm.getChargeFormItems()))
                .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                .filter(item -> item.getIsDeleted() == 0)
                .filter(chargeFormItem -> StringUtils.isNotEmpty(chargeFormItem.getSourceFormItemId()) || chargeFormItem.getProductType() == Constants.ProductType.REGISTRATION)
                .filter(chargeFormItem -> chargeFormItem.getComposeType() != ComposeType.COMPOSE_SUB_ITEM)
                .collect(Collectors.toList());


        SheetProcessor sheetProcessor = new SheetProcessor(latestUnchargedChargeSheet);
        sheetProcessor.setSheetProcessorInfoProvider(sheetProcessorService);
        sheetProcessor.build();
        CalculateChargeResult calculateChargeResult = sheetProcessor.calculateSheetFee(null, null, new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), null, Constants.ChargeSource.BACKGROUND, false, false);

        return new ChargeSheetPushScanCodeRsp()
                .setChargeSheetId(latestUnchargedChargeSheet.getId())
                .setPatientId(latestUnchargedChargeSheet.getPatientId())
                .setTotalFee(totalFee)
                .setAbstractInfo(ChargeUtils.generateMedicalItemsStr(outpatientChargeFormItems))
                .setDiagnosis(Optional.ofNullable(latestUnchargedChargeSheet.getAdditional()).map(ChargeSheetAdditional::getDiagnosis).orElse(null))
                .setNeedPayFee(Optional.ofNullable(calculateChargeResult).map(CalculateChargeResult::getNeedPayFee).orElse(BigDecimal.ZERO));
    }

    /**
     * 给patientOrderId增加redis锁
     *
     * @param patientOrderId
     * @param employeeId
     */
    public BaseSuccessRsp lockPatientOrderId(String patientOrderId, String chainId, String employeeId) {

        String patientOrderLockRedisKey = RedisUtils.generatePatientOrderLockRedisKey(chainId, patientOrderId);
        PatientOrder patientOrder = findPatientOrder(patientOrderId);

        if (Objects.isNull(patientOrder)) {
            return BaseSuccessRsp.fail("未找到有效的就诊单");
        }

        PatientOrderLockView patientOrderLockDto = new PatientOrderLockView();
        patientOrderLockDto.setClinicId(patientOrder.getClinicId())
                .setChainId(chainId)
                .setOperatorId(employeeId)
                .setPatientOrderId(patientOrderId);

        if (StringUtils.isNotEmpty(employeeId) && !TextUtils.equals(employeeId, Constants.ANONYMOUS_PATIENT_ID)) {
            //查询医生名字
            try {
                Employee employee = employeeService.findById(chainId, employeeId);
                patientOrderLockDto.setOperatorName(Optional.ofNullable(employee).map(Employee::getName).orElse(null));
            }catch (Exception e) {
                log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX,"employeeService.findById error: {}", e.getMessage());
            }
        }

        RedisUtils.set(patientOrderLockRedisKey, JsonUtils.dump(patientOrderLockDto), 60, TimeUnit.SECONDS);
        sendPushMessageToWeb(patientOrderLockDto, WebMessageBody.WebMessageEvent.CHARGE_PATIENT_ORDER_LOCK);
        return BaseSuccessRsp.success();
    }

    private PatientOrder findPatientOrder(String patientOrderId) {
        PatientOrder patientOrder = null;
        try {
            patientOrder = patientOrderService.findPatientOrder(patientOrderId, null);
        }catch (Exception e){
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX,"patientOrderService.findPatientOrder error, message: {}", e.getMessage());
        }

        return patientOrder;
    }


    public BaseSuccessRsp unLockPatientOrderId(String chainId, String patientOrderId, String employeeId) {

        PatientOrder patientOrder = findPatientOrder(patientOrderId);

        if (Objects.isNull(patientOrder)) {
            return BaseSuccessRsp.fail("未找到有效的就诊单");
        }

        return unLockPatientOrderId(patientOrderId, chainId, patientOrder.getClinicId(), employeeId);
    }

    /**
     * 给patientOrderId解锁
     *
     * @param patientOrderId
     * @param employeeId
     */
    public BaseSuccessRsp unLockPatientOrderId(String patientOrderId, String chainId, String clinicId, String employeeId) {

        String patientOrderLockRedisKey = RedisUtils.generatePatientOrderLockRedisKey(chainId, patientOrderId);

        RedisUtils.del(patientOrderLockRedisKey);

        PatientOrderLockView patientOrderLockView = new PatientOrderLockView();
        patientOrderLockView.setClinicId(clinicId)
                .setChainId(chainId)
                .setOperatorId(employeeId)
                .setPatientOrderId(patientOrderId);
        //解锁就发送消息
        sendPushMessageToWeb(patientOrderLockView, WebMessageBody.WebMessageEvent.CHARGE_PATIENT_ORDER_UNLOCK);

        return BaseSuccessRsp.success();
    }


    public void sendPushMessageToWeb(PatientOrderLockView patientOrderLockView, String event) {
        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX,"sendPushMessageToWeb patientOrderLockView: {}, event: {}", JsonUtils.dump(patientOrderLockView), event);
        WebMessageBody.WebPushMessage webPushMessage = new WebMessageBody.WebPushMessage();
        webPushMessage.setEvent(event);
        webPushMessage.setScope(WebMessageBody.WebMessageScope.CLINIC);
        webPushMessage.setScopeId(patientOrderLockView.getClinicId());

        webPushMessage.setParams(JsonUtils.dumpAsJsonNode(patientOrderLockView));
        WebMessageBody webMessageBody = new WebMessageBody();
        webMessageBody.setTrigger(webPushMessage);
        webMessageBody.setMemoryFlag(WebMessageBody.MemoryLogType.NO_WRITE_LOG);

        ToBMessage toBMessage = new ToBMessage();
        toBMessage.setChainId(patientOrderLockView.getChainId());
        toBMessage.setClinicId(patientOrderLockView.getClinicId());
        toBMessage.setChannel(ToBMessage.MessageChannel.Web);
        toBMessage.setMsgId(AbcIdUtils.getUUID());
        toBMessage.setBody(JsonUtils.dumpAsJsonNode(webMessageBody));

        rocketMqProducer.sendTobMessage(toBMessage);
    }

    public PatientOrderLockView getPatientOrderLockDetail(String patientOrderId, String chainId) {

        String patientOrderLockStr = RedisUtils.get(RedisUtils.generatePatientOrderLockRedisKey(chainId, patientOrderId));

        if (StringUtils.isEmpty(patientOrderLockStr)) {
            return null;
        }

        return JsonUtils.readValue(patientOrderLockStr, PatientOrderLockView.class);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public List<ChargeSheetAbstract> getChargeSheetAbstractListByPatientOrderId(String chainId, String patientOrderId) {

        int withUndiagnosed = organProductService.isShowUndiagnosedChargeSheet(chainId) ? 1 : 0;

        return chargeMapper.findChargeSheetAbstractListByPatientOrderIdAndChainId(patientOrderId, chainId, withUndiagnosed);
    }
}
