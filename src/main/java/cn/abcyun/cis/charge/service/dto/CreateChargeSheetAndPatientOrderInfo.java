package cn.abcyun.cis.charge.service.dto;

import cn.abcyun.cis.charge.api.model.CreateChargeSheetReq;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.model.ChargeSheetLockingTask;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrderReq;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
public class CreateChargeSheetAndPatientOrderInfo {
    @NotNull
    private CreateType createType;
    @NotNull
    private CreateChargeSheetReq createChargeSheetReq;
    @NotEmpty
    private String clinicId;
    @NotEmpty
    private String chainId;

    private int chargeVersion;

    private int hisType;

    @NotEmpty
    private String operatorId;

    public enum CreateType {
        /**
         * 直接收费创建收费单
         */
        CREATE_FOR_PAID(ChargeSheet.Type.DIRECT_SALE, PatientOrderReq.Source.CHARGE_RETAIL),

        /**
         * 直接创建门诊补单收费单
         */
        CREATE_FOR_DIRECT_OUTPATIENT_ADDITIONAL(ChargeSheet.Type.DIRECT_OUTPATIENT_ADDITIONAL, PatientOrderReq.Source.OUTPATIENT),
        /**
         * 生成草稿创建收费单
         */
        CREATE_FOR_DRAFT(ChargeSheet.Type.DIRECT_SALE, PatientOrderReq.Source.CHARGE_RETAIL),
        /**
         * 执行站创建收费单
         */
        CREATE_FOR_THERAPY(ChargeSheet.Type.THERAPY, PatientOrderReq.Source.THERAPY),

        /**
         * 患者档案处直接开单
         */
        PATIENT_ARCHIVE_DIRECT(ChargeSheet.Type.PATIENT_ARCHIVE_DIRECT, PatientOrderReq.Source.PATIENT_ARCHIVE_DIRECT),
        /**
         * 检查站创建收费单
         */
        CREATE_FOR_EXAMINATION_INSPECTION(ChargeSheet.Type.EXAMINATION_INSPECTION, PatientOrderReq.Source.EXAMINATION_INSPECTION),
        /**
         * 社保创建处方外购收费单
         */
        CREATE_FOR_SHEBAO_OUTSOURCE_PRESCRIPTION(ChargeSheet.Type.OUTSOURCE_PRESCRIPTION, PatientOrderReq.Source.SHEBAO);

        private int chargeSheetType;
        private int patientOrderSource;

        CreateType(int chargeSheetType, int patientOrderSource) {
            this.chargeSheetType = chargeSheetType;
            this.patientOrderSource = patientOrderSource;
        }

        public int getChargeSheetType() {
            return chargeSheetType;
        }

        public void setChargeSheetType(int chargeSheetType) {
            this.chargeSheetType = chargeSheetType;
        }

        public int getPatientOrderSource() {
            return patientOrderSource;
        }

        public void setPatientOrderSource(int patientOrderSource) {
            this.patientOrderSource = patientOrderSource;
        }
    }
}
