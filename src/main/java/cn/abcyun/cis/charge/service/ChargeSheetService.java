package cn.abcyun.cis.charge.service;

import cn.abcyun.bis.rpc.sdk.cis.model.charge.ChargeConstants;
import cn.abcyun.bis.rpc.sdk.cis.model.charge.UpdateTraceableCodeReq;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.BatchQueryEmployeeSnapshotReq;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.BatchQueryEmployeeSnapshotRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Employee;
import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.bis.rpc.sdk.cis.model.dispensing.DispenseConst;
import cn.abcyun.bis.rpc.sdk.cis.model.dispensing.DispensingFormItem;
import cn.abcyun.bis.rpc.sdk.cis.model.dispensing.DispensingSheet;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.*;
import cn.abcyun.bis.rpc.sdk.cis.model.pay.PayStatus;
import cn.abcyun.cis.charge.amqp.HAMQProducer;
import cn.abcyun.cis.charge.amqp.MQProducer;
import cn.abcyun.cis.charge.api.model.*;
import cn.abcyun.cis.charge.api.model.EmployeeView;
import cn.abcyun.cis.charge.api.model.openapi.OpenApiPatientChargeSheetView;
import cn.abcyun.cis.charge.api.model.openapi.OpenApiQueryPatientChargeSheetReq;
import cn.abcyun.cis.charge.api.model.orderlist.*;
import cn.abcyun.cis.charge.base.*;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.base.pay.CombinedPayItem;
import cn.abcyun.cis.charge.cold.ChargeFormItemDaoProxy;
import cn.abcyun.cis.charge.config.OrderNoGenerator;
import cn.abcyun.cis.charge.config.WeclinicConfiguration;
import cn.abcyun.cis.charge.controller.api.ChargeFormItemMerger;
import cn.abcyun.cis.charge.factory.chargeform.ChargeFormFactory;
import cn.abcyun.cis.charge.mapper.ChargeFormItemMapper;
import cn.abcyun.cis.charge.mapper.ChargeMapper;
import cn.abcyun.cis.charge.model.*;
import cn.abcyun.cis.charge.model.ChargeAction;
import cn.abcyun.cis.charge.model.ChargeForm;
import cn.abcyun.cis.charge.model.ChargeFormItem;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.model.ChargeTransaction;
import cn.abcyun.cis.charge.model.ChargeTransactionRecord;
import cn.abcyun.cis.charge.model.ThirdPartyPayInfo;
import cn.abcyun.cis.charge.processor.ChargePayModeConfigSimple;
import cn.abcyun.cis.charge.processor.ChargeSheetFeeProtocol;
import cn.abcyun.cis.charge.processor.SheetProcessorDispatcher;
import cn.abcyun.cis.charge.repository.*;
import cn.abcyun.cis.charge.service.dto.*;
import cn.abcyun.cis.charge.service.rpc.CisScClinicService;
import cn.abcyun.cis.charge.service.rpc.CisScGoodsService;
import cn.abcyun.cis.charge.util.*;
import cn.abcyun.cis.commons.amqp.message.ChargeSheetMessage;
import cn.abcyun.cis.commons.amqp.message.PatientOrderMessage;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.exception.ParamNotValidException;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.model.ComposeType;
import cn.abcyun.cis.commons.rpc.charge.*;
import cn.abcyun.cis.commons.rpc.crm.PatientInfo;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrder;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.cis.core.db.UseReadOnlyDB;
import cn.abcyun.common.log.marker.AbcLogMarker;
import cn.abcyun.common.model.AbcListPage;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ChargeSheetService {

    private static final Logger sLogger = LoggerFactory.getLogger(ChargeSheetService.class);

    @Autowired
    private ChargeSheetRepository chargeSheetRepository;

    @Autowired
    private OrderNoGenerator orderNoGenerator;

    @Autowired
    private ChargeExecuteService mChargeExecuteService;

    @Autowired
    private ChargeMapper mChargeMapper;

    @Autowired
    private PatientOrderService patientOrderService;

    @Autowired
    private CrmService crmService;

    @Autowired
    private ChargeFormItemDaoProxy mChargeFormItemRepository;

    @Autowired
    private ChargeExecuteItemRepository chargeExecuteItemRepository;

    @Autowired
    private ChargeExecuteRecordService chargeExecuteRecordService;

    @Autowired
    private ClinicService clinicService;

    @Autowired
    private WeclinicConfiguration weclinicConfiguration;

    @Autowired
    private HAMQProducer hamqProducer;

    @Autowired
    private ChargeTransactionRecordService chargeTransactionRecordService;

    @Autowired
    private CisScClinicService scClinicService;

    @Autowired
    private ChargeAbnormalTransactionRepository chargeAbnormalTransactionRepository;

    @Autowired
    private ChargeAbnormalTransactionService chargeAbnormalTransactionService;

    @Autowired
    private ChargeConfigService chargeConfigService;

    @Autowired
    private CisScGoodsService goodsService;

    @Autowired
    private ChargeOweSheetRepository chargeOweSheetRepository;

    @Autowired
    private SheetProcessorService sheetProcessorService;

    @Autowired
    private ChargeFormItemMapper chargeFormItemMapper;

    @Autowired
    private TobMessageService tobMessageService;

    @Autowired
    private EmployeeService employeeService;

    @Autowired
    private DispensingService dispensingService;

    @Autowired
    private ChargeSheetBindingService chargeSheetBindingService;
    @Autowired
    private ChargePayModeService chargePayModeService;
    @Autowired
    private ChargeFormItemDaoProxy chargeFormItemDaoProxy;


    @Autowired
    private ChargePayTransactionRepository chargePayTransactionRepository;

    @Transactional(readOnly = true)
    public ChargeSheet findFirstByPatientOrderIdAndType(String orderId, int type) {
        ChargeSheet chargeSheet = chargeSheetRepository.findFirstByPatientOrderIdAndTypeAndIsDeleted(orderId, type, 0).orElse(null);
        chargeSheetBindingService.bindFieldsForOne(chargeSheet);
        return chargeSheet;
    }

    @Transactional(readOnly = true)
    public ChargeSheet findByIdAndClinicId(String id, String clinicId) {

        if (org.apache.commons.lang3.StringUtils.isAnyEmpty(id, clinicId)) {
            return null;
        }

        ChargeSheet chargeSheet = chargeSheetRepository.findByIdAndClinicIdAndIsDeleted(id, clinicId, 0).orElse(null);
        chargeSheetBindingService.bindFieldsForOne(chargeSheet);
        return chargeSheet;
    }

    public List<ChargeSheet> findAllByIdAndClinicId(List<String> ids, String clinicId) {

        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }

        if (org.apache.commons.lang3.StringUtils.isEmpty(clinicId)) {
            return new ArrayList<>();
        }

        List<ChargeSheet> chargeSheets = chargeSheetRepository.findAllByIdInAndClinicIdAndIsDeleted(ids, clinicId, 0);
        chargeSheetBindingService.bindFieldsForMany(chargeSheets);
        return chargeSheets;
    }


    @Transactional(readOnly = true)
    public ChargeSheet findByIdAndChainId(String id, String chainId) {
        ChargeSheet chargeSheet = chargeSheetRepository.findByIdAndChainIdAndIsDeleted(id, chainId, 0).orElse(null);
        chargeSheetBindingService.bindFieldsForOne(chargeSheet);
        return chargeSheet;
    }

    @Transactional(readOnly = true)
    public ChargeSheet findById(String id) {
        return findByIdCore(id);
    }

    public ChargeSheet findByIdCore(String id) {
        ChargeSheet chargeSheet = chargeSheetRepository.findByIdAndIsDeleted(id, 0).orElse(null);
        chargeSheetBindingService.bindFieldsForOne(chargeSheet);
        return chargeSheet;
    }

    public List<ChargeSheet> findAllChargeSheetByPatientOrderId(String patientOrderId, boolean withDeliveryAndProcess) {

        if (org.apache.commons.lang3.StringUtils.isEmpty(patientOrderId)) {
            return new ArrayList<>();
        }
        List<ChargeSheet> chargeSheets = chargeSheetRepository.findAllByPatientOrderIdAndIsDeleted(patientOrderId, 0);

        if (CollectionUtils.isEmpty(chargeSheets)) {
            return new ArrayList<>();
        }

        chargeSheetBindingService.bindFieldsForMany(chargeSheets);
        return chargeSheets;
    }

    public ChargeSheet findByIdContainIsDeleted(String id) {
        ChargeSheet chargeSheet = chargeSheetRepository.findByIdAndIsDeleted(id, 0).orElse(null);
        chargeSheetBindingService.bindFieldsForOne(chargeSheet);
        return chargeSheet;
    }

    public ChargeSheet findByIdAndClinicIdAndIsDeleted(String id, String clinicId) {
        ChargeSheet chargeSheet = chargeSheetRepository.findByIdAndClinicIdAndIsDeleted(id, clinicId, 0).orElse(null);
        chargeSheetBindingService.bindFieldsForOne(chargeSheet);
        return chargeSheet;
    }

    //这里生成的chargeSheet是阉割版本的chargeSheet，只初始化了空中药房和加工费关联表
    @Transactional(readOnly = true)
    public List<ChargeSheet> findAllByPatientOrderId(String patientOrderId) {
        return findAllByPatientOrderId(patientOrderId, null);
    }

    public List<ChargeSheet> findAllByPatientOrderId(String patientOrderId, Integer chargeType) {
        List<ChargeSheet> chargeSheets;
        if (Objects.isNull(chargeType)) {
            chargeSheets = chargeSheetRepository.findAllByPatientOrderIdAndIsDeleted(patientOrderId, 0);
        } else {
            chargeSheets = chargeSheetRepository.findAllByPatientOrderIdAndTypeAndIsDeleted(patientOrderId, chargeType, 0);
        }
        chargeSheetBindingService.bindFieldsForMany(chargeSheets);
        return chargeSheets;
    }

    public List<ChargeSheet> findAllByIds(List<String> chargeSheetIds) {

        if (CollectionUtils.isEmpty(chargeSheetIds)) {
            return new ArrayList<>();
        }

        List<ChargeSheet> chargeSheets = chargeSheetRepository.findAllByIdInAndIsDeleted(chargeSheetIds, 0);

        chargeSheetBindingService.bindFieldsForMany(chargeSheets);

        return chargeSheets;
    }

    public List<ChargeSheet> findAllByIdsOnlyBindAdditional(List<String> chargeSheetIds) {

        if (CollectionUtils.isEmpty(chargeSheetIds)) {
            return new ArrayList<>();
        }

        List<ChargeSheet> chargeSheets = chargeSheetRepository.findAllByIdInAndIsDeleted(chargeSheetIds, 0);

        chargeSheetBindingService.bindChargeSheetAdditional(chargeSheets);

        return chargeSheets;
    }

    public List<ChargeSheet> findAllByPatientOrderIdAndClinicId(String patientOrderId, String clinicId) {

        if (org.apache.commons.lang3.StringUtils.isAnyEmpty(patientOrderId, clinicId)) {
            return new ArrayList<>();
        }

        List<ChargeSheet> chargeSheets = chargeSheetRepository.findAllByPatientOrderIdAndClinicIdAndIsDeleted(patientOrderId, clinicId, 0);

        if (CollectionUtils.isEmpty(chargeSheets)) {
            return chargeSheets;
        }

        chargeSheetBindingService.bindFieldsForMany(chargeSheets);

        return chargeSheets;
    }

    public List<ChargeSheet> findAllByHospitalSheetId(String chainId, String clinicId, Long hospitalSheetId) {

        if (Objects.isNull(hospitalSheetId)) {
            return new ArrayList<>();
        }

        if (org.apache.commons.lang3.StringUtils.isAnyEmpty(chainId, clinicId)) {
            return new ArrayList<>();
        }

        List<ChargeSheet> chargeSheets = chargeSheetRepository.findAllByChainIdAndClinicIdAndHospitalSheetIdAndIsDeleted(chainId, clinicId, hospitalSheetId, 0);
        chargeSheetBindingService.bindFieldsForMany(chargeSheets);

        return chargeSheets;
    }

    public ChargeSheet findByRegistrationChargeSheetId(String registrationChargeSheetId) {
        if (TextUtils.isEmpty(registrationChargeSheetId)) {
            return null;
        }

        ChargeSheet chargeSheet = chargeSheetRepository.findByRegistrationChargeSheetIdAndIsDeleted(registrationChargeSheetId, 0).orElse(null);
        chargeSheetBindingService.bindFieldsForOne(chargeSheet);
        return chargeSheet;
    }


//    public ChargeSheet saveNew(ChargeSheet chargeSheet, String operatorId) {
//        FillUtils.fillCreatedBy(chargeSheet, operatorId);
//
//        if (chargeSheet.getChargeTransactions() != null) {
//            chargeSheet.getChargeTransactions().forEach(chargeTransaction -> FillUtils.fillCreatedBy(chargeTransaction, operatorId));
//        }
//
//        if (chargeSheet.getAdditionalFees() != null) {
//            chargeSheet.getAdditionalFees().forEach(chargeAdditionalFee -> FillUtils.fillCreatedBy(chargeAdditionalFee, operatorId));
//        }
//
//        if (chargeSheet.getChargeForms() != null) {
//            chargeSheet.getChargeForms().forEach(chargeForm -> {
//                FillUtils.fillCreatedBy(chargeForm, operatorId);
//                if (chargeForm.getChargeFormItems() != null) {
//                    chargeForm.getChargeFormItems().forEach(chargeFormItem -> {
//                        FillUtils.fillCreatedBy(chargeFormItem, operatorId);
//                    });
//                }
//            });
//        }
//        // 药店保存单号
//        if (chargeSheet.getChargeVersion() == ChargeVersionConstants.V1 && org.apache.commons.lang3.StringUtils.isBlank(chargeSheet.getSellNo())) {
//            chargeSheet.setSellNo(orderNoGenerator.generateChargeSheetSellNo(chargeSheet.getClinicId()));
//        }
//        return save(chargeSheet, null);
//    }

    public ChargeSheet saveImport(ChargeSheet chargeSheet, String operatorId, Instant created, Instant chargedTime) {
        if (created == null) {
            created = Instant.now();
        }
        chargedTime = Optional.ofNullable(chargedTime).orElse(Instant.now());
        Instant finalCreated = created;

        FillUtils.fillCreatedBy(chargeSheet, operatorId, finalCreated);

        if (chargeSheet.getChargeTransactions() != null) {
            chargeSheet.getChargeTransactions().forEach(chargeTransaction -> FillUtils.fillCreatedBy(chargeTransaction, operatorId, finalCreated));
        }

        if (chargeSheet.getAdditionalFees() != null) {
            chargeSheet.getAdditionalFees().forEach(chargeAdditionalFee -> FillUtils.fillCreatedBy(chargeAdditionalFee, operatorId, finalCreated));
        }

        if (chargeSheet.getChargeForms() != null) {
            chargeSheet.getChargeForms().forEach(chargeForm -> {
                FillUtils.fillCreatedBy(chargeForm, operatorId, finalCreated);
                if (chargeForm.getChargeFormItems() != null) {
                    chargeForm.getChargeFormItems().forEach(chargeFormItem -> {
                        FillUtils.fillCreatedBy(chargeFormItem, operatorId, finalCreated);

                        if (chargeFormItem.getChargeFormItemBatchInfos() != null) {
                            chargeFormItem.getChargeFormItemBatchInfos().forEach(chargeFormItemBatchInfo -> FillUtils.fillCreatedBy(chargeFormItemBatchInfo, operatorId, finalCreated));
                        }
                    });
                }
            });
        }

        if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.CHARGED) {
            chargeSheet.setFirstChargedTime(chargedTime);
            chargeSheet.setChargedTime(chargedTime);
        }

        return save(chargeSheet, null);
    }


    public ChargeSheet update(ChargeSheet chargeSheet, ChargeSheetRelationDto deletedDataDto, String operatorId) {
        FillUtils.fillLastModifiedBy(chargeSheet, operatorId);
        return save(chargeSheet, deletedDataDto);
    }

    public ChargeSheet save(ChargeSheet chargeSheet, ChargeSheetRelationDto chargeSheetRelationDto) {

        // 药店保存单号
        if (chargeSheet.getChargeVersion() == ChargeVersionConstants.V1
                && org.apache.commons.lang3.StringUtils.isBlank(chargeSheet.getSellNo())) {
            chargeSheet.setSellNo(orderNoGenerator.generateChargeSheetSellNo(chargeSheet.getClinicId()));
        }

        saveAllData(chargeSheet, chargeSheetRelationDto);
        chargeSheetRepository.save(chargeSheet);
        ChargeUtils.removeDeleteData(chargeSheet);
        return chargeSheet;
    }

    /**
     * 把createOrFindChargeSheet() 函数返回的chargeSheet的数据存入到db，封装成一个函数 要存四个玩意，分开写容易出错 isNewInsert = true new
     */
    public ChargeSheet saveSheetAndExecuteItems(ChargeSheet chargeSheet, ChargeSheetRelationDto chargeSheetRelationDto, boolean needCheckItemExecutedCount, String operatorId) {
        mChargeExecuteService.createOrUpdateChargeExecuteItems(chargeSheet, needCheckItemExecutedCount, operatorId);
        save(chargeSheet, chargeSheetRelationDto);
        return chargeSheet;
    }


    public void saveAllData(ChargeSheet chargeSheet, ChargeSheetRelationDto deletedDataDto) {
        chargeSheetBindingService.saveAllBindingData(chargeSheet, deletedDataDto);
    }

    /**
     * 【单】【门诊】【查询patientOrderId的OUTPATIENT chargeSheet收费情况】
     */
    @Transactional(readOnly = true)
    public QueryOutpatientSheetChargeStatusRsp queryChargeSheetStatus(String patientOrderId, boolean withExecutedCount, boolean withDeliveryAndProcess) throws NotFoundException {
        List<ChargeSheet> allChargeSheets = findAllChargeSheetByPatientOrderId(patientOrderId, withDeliveryAndProcess);
        List<ChargeSheet> chargeSheets = allChargeSheets.stream()
                .filter(chargeSheet -> chargeSheet.getType() == ChargeSheet.Type.OUTPATIENT)
                .collect(Collectors.toList());

        if (withDeliveryAndProcess) {
            chargeSheets.stream().forEach(chargeSheet -> ClientReqUtils.updatePrescriptionChineseFormUsageInfoByProcessForm(chargeSheet.getChargeForms(), null));
        }

        QueryOutpatientSheetChargeStatusRsp queryRsp = new QueryOutpatientSheetChargeStatusRsp();
        queryRsp.setForms(new ArrayList<>());
        queryRsp.setChargeSheets(chargeSheets.stream().map(chargeSheet -> {
            QueryOutpatientSheetChargeStatusRsp.ChargeSheet rpcChargeSheet = new QueryOutpatientSheetChargeStatusRsp.ChargeSheet();
            BeanUtils.copyProperties(chargeSheet, rpcChargeSheet);
            return rpcChargeSheet;
        }).collect(Collectors.toList()));

        Map<String, List<ChargeOweSheet>> chargeOweSheetsMap = chargeOweSheetRepository.findAllByChargeSheetIdInAndPatientIdAndIsDeleted(allChargeSheets.stream().map(item -> item.getId()).collect(Collectors.toList()), patientOrderId, 0)
                .stream().collect(Collectors.groupingBy(ChargeOweSheet::getChargeSheetId));
        List<ChargeTransaction> chargeTransactionList = new ArrayList<>();
        for (ChargeSheet allChargeSheet : allChargeSheets) {
            chargeTransactionList.addAll(
                    ChargeSheetFeeProtocol.getChargeTransactionAndChargeActionContainOwe(allChargeSheet, chargeOweSheetsMap.get(allChargeSheet.getId()))
                            .getChargeTransactions()
            );
        }

        // 设置费别
        PatientOrder patientOrder = patientOrderService.findPatientOrder(patientOrderId, null);
        Integer shebaoChargeType = null;
        if (patientOrder != null) {
            shebaoChargeType = patientOrder.getShebaoChargeType();
        }
//        queryRsp.setHealthCardPayLevel(ChargeUtils.generateHealthCardPayLevel(allChargeSheets.stream()
//                .filter(chargeSheet -> CollectionUtils.isNotEmpty(chargeSheet.getChargeTransactions()))
//                .flatMap(chargeSheet -> chargeSheet.getChargeTransactions().stream())
//                .collect(Collectors.toList()), shebaoChargeType));
        queryRsp.setHealthCardPayLevel(ChargeUtils.generateHealthCardPayLevel(chargeTransactionList, shebaoChargeType));
        queryRsp.setChargedOutpatientAdjustmentFee(chargeSheets.stream()
                .filter(chargeSheet -> chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED)
                .map(ChargeSheet::getOutpatientAdjustmentFee)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add));


        chargeSheets.forEach(chargeSheet -> ChargeFormItemMerger.mergeForChargeSheet(chargeSheet));

        // sheet status
        if (chargeSheets.stream().filter(chargeSheet -> chargeSheet.getType() != ChargeSheet.Type.REGISTRATION).count() == 0 || chargeSheets.stream().filter(chargeSheet -> chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED || chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_CHARGED).count() > 0) {
            queryRsp.setStatus(Constants.ChargeSheetStatus.UNCHARGED);
        } else if (chargeSheets.stream().filter(chargeSheet -> chargeSheet.getStatus() == Constants.ChargeSheetStatus.CHARGED || chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_REFUNDED).count() > 0) {
            queryRsp.setStatus(Constants.ChargeSheetStatus.CHARGED);
        } else {
            queryRsp.setStatus(Constants.ChargeSheetStatus.REFUNDED);
        }

        //registration fee
        ChargeFormItem registrationChargeFormItem = mChargeFormItemRepository.findFirstChargeFormItemsByPatientOrderIdAndProductType(patientOrderId, Constants.ProductType.REGISTRATION, 0);

//        ChargeFormItem registrationChargeFormItem = registrationChargeFormItems.stream()
//                .filter(chargeFormItem -> chargeFormItem.getV2Status() != Constants.ChargeFormItemStatus.NONE)
//                .findFirst().orElse(null);

        if (registrationChargeFormItem != null) {

            Integer registrationFeeChargeSheetType = chargeSheets.stream().filter(chargeSheet -> Objects.equals(chargeSheet.getId(), registrationChargeFormItem.getChargeSheetId()))
                    .findFirst().map(ChargeSheet::getType).orElse(null);

            queryRsp.setRegistrationFee(MathUtils.wrapBigDecimal(registrationChargeFormItem.getUnitPrice(), BigDecimal.ZERO));
            queryRsp.setRegistrationFeeStatus(registrationChargeFormItem.getV2Status());
            queryRsp.setRegistrationFeeChargeSheetType(registrationFeeChargeSheetType);
        } else {
            queryRsp.setRegistrationFee(null);
            queryRsp.setRegistrationFeeStatus(Constants.ChargeFormItemStatus.UNSELECTED);
        }

        Map<String, BigDecimal> executedIdCountMap = new HashMap<>();
        if (withExecutedCount) {
            boolean anyMatchTreatment = ChargeUtils.getChargeSheetItems(chargeSheets).stream().anyMatch(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.TREATMENT);
            if (anyMatchTreatment) {
                List<ChargeExecuteItem> executeItems = mChargeExecuteService.findByPatientOrderIdAndIsDeleted(patientOrderId);
                executedIdCountMap = executeItems.stream().collect(Collectors.toMap(ChargeExecuteItem::getChargeFormItemId, ChargeExecuteItem::getExecutedCount, (a, b) -> a));
            }

        }

        AtomicBoolean isContainRegistration = new AtomicBoolean(false);
        // form status
        Map<String, BigDecimal> finalExecutedIdCountMap = executedIdCountMap;
        List<QueryOutpatientSheetChargeStatusRsp.OutpatientForm> forms = chargeSheets
                .stream()
                .filter(chargeSheet -> chargeSheet.getChargeForms() != null)
                .map(chargeSheet -> {
                            Map<String, ChargeForm> processFormMap = chargeSheet.getChargeForms().stream()
                                    .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                                    .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.PROCESS)
                                    .filter(chargeForm -> chargeForm.getProcessInfo() != null)
                                    .filter(chargeForm -> org.apache.commons.lang3.StringUtils.isNotEmpty(chargeForm.getProcessInfo().getChargeFormId()))
                                    .collect(Collectors.toMap(chargeForm -> chargeForm.getProcessInfo().getChargeFormId(), Function.identity()));

                            ChargeForm deliveryForm = chargeSheet.getChargeForms().stream()
                                    .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                                    .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.EXPRESS_DELIVERY)
                                    .findFirst().orElse(null);

                            return chargeSheet.getChargeForms().stream()
                                    .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                                    .filter(chargeForm -> chargeForm.getChargeFormItems() != null)
                                    .filter(chargeForm -> org.apache.commons.lang3.StringUtils.isNotEmpty(chargeForm.getSourceFormId()))
                                    .map(chargeForm -> {
                                        QueryOutpatientSheetChargeStatusRsp.OutpatientForm outpatientForm = new QueryOutpatientSheetChargeStatusRsp.OutpatientForm();
                                        outpatientForm.setStatus(chargeForm.getStatus());
                                        outpatientForm.setId(chargeForm.getSourceFormId());
                                        outpatientForm.setChargeSheetId(chargeSheet.getId());

                                        List<String> composeChildrenItemIds = new ArrayList<>();
                                        List<QueryOutpatientSheetChargeStatusRsp.OutpatientFormItem> formItems = chargeForm.getChargeFormItems()
                                                .stream()
                                                .filter(Objects::nonNull)
                                                .filter(item -> item.getIsDeleted() == 0)
                                                .filter(chargeFormItem -> !TextUtils.isEmpty(chargeFormItem.getSourceFormItemId()))
                                                .collect(Collectors.toMap(ChargeFormItem::getSourceFormItemId, Function.identity(), (a, b) -> {
                                                    // 前面已经合并了一次，这里会同时出现的只有部分退费的情况
                                                    if (a.getIsRefundByDose() == 1) {
                                                        BigDecimal doseCountSum = MathUtils.wrapBigDecimalAdd(a.getDoseCount(), b.getDoseCount());
                                                        a.setDoseCount(doseCountSum);
                                                        a.setRefundDoseCount(MathUtils.wrapBigDecimalAdd(a.getRefundDoseCount(), b.getRefundDoseCount()));
                                                    } else {
                                                        BigDecimal unitCountSum = MathUtils.wrapBigDecimalAdd(a.getUnitCount(), b.getUnitCount());
                                                        a.setUnitCount(unitCountSum);
                                                        a.setRefundUnitCount(MathUtils.wrapBigDecimalAdd(a.getRefundUnitCount(), b.getRefundUnitCount()));
                                                    }
                                                    a.setStatus(Constants.ChargeFormItemStatus.CHARGED);
                                                    return a;
                                                }))
                                                .values()
                                                .stream()
                                                .map(chargeFormItem -> {

                                                    if (chargeFormItem.getProductType() == Constants.ProductType.REGISTRATION
                                                            && chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.UNSELECTED) {
                                                        isContainRegistration.set(true);
                                                    }

                                                    QueryOutpatientSheetChargeStatusRsp.OutpatientFormItem formItem = new QueryOutpatientSheetChargeStatusRsp.OutpatientFormItem();
                                                    formItem.setId(chargeFormItem.getSourceFormItemId());
                                                    formItem.setStatus(chargeFormItem.getStatus());
                                                    formItem.setUnitCount(chargeFormItem.getUnitCount());
                                                    formItem.setLockId(chargeFormItem.getLockId());
                                                    if (chargeFormItem.getIsRefundByDose() == 1) {
                                                        formItem.setRefundDoseCount(MathUtils.wrapBigDecimalOrZero(chargeFormItem.getRefundDoseCount()));
                                                        formItem.setRefundUnitCount(BigDecimal.ZERO);
                                                    } else {
                                                        formItem.setRefundDoseCount(BigDecimal.ZERO);
                                                        formItem.setRefundUnitCount(chargeFormItem.getRefundUnitCount());
                                                    }

                                                    formItem.setDoseCount(chargeFormItem.getDoseCount());
                                                    if (withExecutedCount) {
                                                        BigDecimal executedCount = finalExecutedIdCountMap.getOrDefault(chargeFormItem.getId(), null);
                                                        formItem.setExecutedCount(executedCount);
                                                    }

                                                    formItem.setUnitPrice(chargeFormItem.getUnitPrice());
                                                    formItem.setTotalPrice(MathUtils.wrapBigDecimalAdd(MathUtils.calculateTotalPrice(chargeFormItem.getUnitPrice(), chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount(), 2), chargeFormItem.getFractionPrice()));
                                                    formItem.setFractionPrice(chargeFormItem.getFractionPrice());

                                                    if (chargeForm.getStatus() != Constants.ChargeFormStatus.REFUNDED && chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED) {
                                                        BigDecimal refundDoseCount = BigDecimal.ZERO;
                                                        BigDecimal refundUnitCount = BigDecimal.ZERO;

                                                        if (chargeFormItem.getIsRefundByDose() == 1) {
                                                            refundDoseCount = MathUtils.wrapBigDecimalOrZero(chargeFormItem.getRefundDoseCount());
                                                            refundUnitCount = chargeFormItem.getUnitCount();
                                                        } else {
                                                            refundDoseCount = chargeFormItem.getDoseCount();
                                                            refundUnitCount = chargeFormItem.getRefundUnitCount();
                                                        }

                                                        BigDecimal refundTotalPrice = MathUtils.calculateTotalPrice(chargeFormItem.getUnitPrice(), refundUnitCount, refundDoseCount, 2);
                                                        BigDecimal totalPrice = MathUtils.wrapBigDecimalAdd(MathUtils.calculateTotalPrice(chargeFormItem.getUnitPrice(), chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount(), 2),
                                                                chargeFormItem.getFractionPrice());
                                                        formItem.setUnitPrice(chargeFormItem.getUnitPrice());
                                                        formItem.setTotalPrice(MathUtils.max(MathUtils.wrapBigDecimalSubtract(totalPrice, refundTotalPrice), BigDecimal.ZERO));
                                                        formItem.setFractionPrice(chargeFormItem.getFractionPrice());
                                                    }

                                                    if (chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.UNSELECTED) {
                                                        formItem.setUnitPrice(BigDecimal.ZERO);
                                                        formItem.setTotalPrice(BigDecimal.ZERO);
                                                        formItem.setFractionPrice(BigDecimal.ZERO);
                                                    }

                                                    if (chargeFormItem.getComposeType() == ComposeType.COMPOSE_SUB_ITEM) {
                                                        composeChildrenItemIds.add(formItem.getId());
                                                    }
                                                    return formItem;
                                                }).collect(Collectors.toList());
                                        outpatientForm.setFormItems(formItems);
                                        outpatientForm.setTotalPrice(Optional.ofNullable(formItems).orElse(new ArrayList<>())
                                                .stream()
                                                .filter(outpatientFormItem -> !composeChildrenItemIds.contains(outpatientFormItem.getId()))
                                                .map(QueryOutpatientSheetChargeStatusRsp.OutpatientFormItem::getTotalPrice)
                                                .filter(Objects::nonNull)
                                                .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        );

                                        boolean isChinesePrescription = chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE;

                                        if (isChinesePrescription) {
                                            if (CollectionUtils.isNotEmpty(formItems)) {

                                                QueryOutpatientSheetChargeStatusRsp.OutpatientFormItem formItem = formItems.get(0);

                                                outpatientForm.setDoseCount(formItem.getDoseCount());

                                                if (outpatientForm.getStatus() == Constants.ChargeFormStatus.REFUNDED) {
                                                    outpatientForm.setRefundDoseCount(formItem.getDoseCount());
                                                } else {

                                                    BigDecimal refundDoseCount = formItems.stream().filter(outpatientFormItem -> outpatientFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED)
                                                            .findFirst().map(QueryOutpatientSheetChargeStatusRsp.OutpatientFormItem::getRefundDoseCount).orElse(BigDecimal.ZERO);

                                                    //判断是否退的剂量都是一样的
                                                    boolean refundByDose = formItems.stream()
                                                            .filter(outpatientFormItem -> outpatientFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED)
                                                            .allMatch(outpatientFormItem -> MathUtils.wrapBigDecimalOrZero(outpatientFormItem.getRefundDoseCount()).compareTo(refundDoseCount) == 0);
                                                    outpatientForm.setRefundDoseCount(refundByDose ? refundDoseCount : BigDecimal.ZERO);
                                                }

                                            } else {
                                                outpatientForm.setDoseCount(BigDecimal.ZERO);
                                                outpatientForm.setRefundDoseCount(BigDecimal.ZERO);
                                            }
                                        }

                                        if (withDeliveryAndProcess) {
                                            ChargeUtils.appendDeliveryAndProcessInfo(outpatientForm, chargeSheet.getDeliveryInfo(), deliveryForm, chargeForm, processFormMap.getOrDefault(chargeForm.getId(), null), isChinesePrescription);
                                        }
                                        return outpatientForm;
                                    })
                                    .collect(Collectors.toList());
                        }
                )
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        queryRsp.setForms(forms);

        //total fee
        BigDecimal totalFee = Optional.ofNullable(queryRsp.getForms()).orElse(new ArrayList<>())
                .stream()
                .map(form -> form.getTotalPrice())
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal registrationFee = BigDecimal.ZERO;
        if (!isContainRegistration.get() && (queryRsp.getRegistrationFeeStatus() == Constants.ChargeFormItemStatus.UNCHARGED
                || (queryRsp.getRegistrationFeeChargeSheetType() != null && queryRsp.getRegistrationFeeChargeSheetType() != ChargeSheet.Type.OUTPATIENT))) {
            registrationFee = queryRsp.getRegistrationFee();
        }

        if (isContainRegistration.get() || (queryRsp.getRegistrationFeeStatus() == Constants.ChargeFormItemStatus.UNCHARGED
                || (queryRsp.getRegistrationFeeChargeSheetType() != null && queryRsp.getRegistrationFeeChargeSheetType() != ChargeSheet.Type.OUTPATIENT))) {
            queryRsp.setIsTotalPriceContainRegistrationPrice(1);
        }

        queryRsp.setTotalFee(totalFee);
        queryRsp.setTotalPrice(MathUtils.wrapBigDecimalAdd(registrationFee, queryRsp.getTotalFee()));

        //对totalPrice进行四舍五入进行填充
        appendAfterRoundingTotalPrice(chargeSheets, queryRsp);

        return queryRsp;
    }

    private void appendAfterRoundingTotalPrice(List<ChargeSheet> chargeSheets, QueryOutpatientSheetChargeStatusRsp queryRsp) {

        if (Objects.isNull(queryRsp)) {
            return;
        }
        queryRsp.setAfterRoundingTotalPrice(queryRsp.getTotalPrice());
        if (MathUtils.wrapBigDecimalCompare(queryRsp.getTotalPrice(), BigDecimal.ZERO) == 0) {
            return;
        }

        if (CollectionUtils.isEmpty(chargeSheets)) {
            return;
        }

        boolean isAllUncharged = chargeSheets.stream()
                .allMatch(chargeSheet -> chargeSheet.getStatus() == Constants.ChargeSheetStatus.CHARGED || chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED);

        if (!isAllUncharged) {
            return;
        }

        String chainId = chargeSheets.get(0).getChainId();
        ChargeConfigDetailView chargeConfigDetailView = chargeConfigService.getBranchConfigDetail(chainId);
        int currentRoundingType = Optional.ofNullable(chargeConfigDetailView).map(ChargeConfigDetailView::getRoundingType).orElse(0);

        queryRsp.setAfterRoundingTotalPrice(MathUtils.calculateRoundingResult(queryRsp.getTotalPrice(), currentRoundingType));
    }

    /**
     * 【批】【门诊】【查询patientOrderId的OUTPATIENT chargeSheet收费情况】
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public List<OutpatientChargeStatus> queryOutpatientChargeStatuses(List<String> patientOrderIds) throws NotFoundException {
        List<OutpatientChargeStatus> returnList = new ArrayList<>();
        //param check.
        if (CollectionUtils.isEmpty(patientOrderIds)) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "queryOutpatientChargeStatuses  patientOrderIds size zero");
            return returnList;
        }
        //每次最多查100条
        if (patientOrderIds.size() > 100) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "queryOutpatientChargeStatuses  patientOrderIds size too large ${},this time query 100 ids", patientOrderIds.size());
            patientOrderIds = patientOrderIds.stream().limit(100).collect(Collectors.toList());
        }

        //查DB，查处所有的OUTPATIENT chargeSheet
        List<ChargeSheetWithPatientOrderStatus> allChargeSheets = mChargeMapper.findChargeSheetsByPatientOrderId(patientOrderIds, ChargeSheet.Type.OUTPATIENT);

        if (CollectionUtils.isEmpty(allChargeSheets)) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "queryOutpatientChargeStatuses  db query return null");
            // 保证每一个请求参数的patientOrderIds都有一个返回 ,这里不退出
            return new ArrayList<>();
        }

        //以patientOrderId为key，分类每个patientOrderId下的ChargeSheet
        Map<String, List<ChargeSheetWithPatientOrderStatus>> patientOrderIdChargeSheetMap = ListUtils.groupByKey(allChargeSheets != null ? allChargeSheets : new ArrayList<>(), ChargeSheetWithPatientOrderStatus::getPatientOrderId);

        //返回处理。逻辑完全对齐queryChargeSheetStatus签半部分处理 chargeSheet status的逻辑
        for (String patientOrderId : patientOrderIds) { // 保证每一个请求参数的patientOrderIds都有一个返回

            OutpatientChargeStatus chargeStatus = new OutpatientChargeStatus();
            List<ChargeSheetWithPatientOrderStatus> chargeSheets = patientOrderIdChargeSheetMap.get(patientOrderId);
            chargeStatus.setPatientOrderId(patientOrderId);

            if (CollectionUtils.isEmpty(chargeSheets)) {
                sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "queryOutpatientChargeStatuses  patientOrderId={} has no outpatient chargeSheet", patientOrderId);
                continue;
            }

            // sheet status
            long nonRegistrationSheetCount = chargeSheets.stream().filter(chargeSheet -> chargeSheet.getType() != ChargeSheet.Type.REGISTRATION).count();
            long unChargeSheetCount = chargeSheets.stream().filter(chargeSheet -> chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED || chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_CHARGED).count();
            long chargeSheetCount = chargeSheets.stream().filter(chargeSheet -> chargeSheet.getStatus() == Constants.ChargeSheetStatus.CHARGED || chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_REFUNDED).count();

            if (nonRegistrationSheetCount == 0 || unChargeSheetCount > 0) {
                chargeStatus.setStatus(Constants.ChargeSheetStatus.UNCHARGED);
            } else if (chargeSheetCount > 0) {
                chargeStatus.setStatus(Constants.ChargeSheetStatus.CHARGED);
            } else {
                chargeStatus.setStatus(Constants.ChargeSheetStatus.REFUNDED);
            }
            returnList.add(chargeStatus);
        }

        return returnList;
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public List<ChargeStatus> queryChargeStatusByPatientOrderIds(List<String> patientOrderIds) {
        if (patientOrderIds == null || patientOrderIds.size() == 0) {
            return new ArrayList<>();
        }
        List<ChargeStatus> chargeStatusByPatientOrderIds = mChargeMapper.findChargeSheetStatusBatch(patientOrderIds);
        Map<String, ChargeStatus> chargeStatusMap = chargeStatusByPatientOrderIds.stream().collect(Collectors.toMap(
                ChargeStatus::getPatientOrderId,
                Function.identity(),
                (a, b) -> a.getStatus() < b.getStatus() ? a : b)
        );

        List<ChargeStatus> results = patientOrderIds.stream().map(patientOrderId -> {
            ChargeStatus chargeStatus = chargeStatusMap.getOrDefault(patientOrderId, null);
            if (chargeStatus == null) {
                chargeStatus = new ChargeStatus();
                chargeStatus.setId(patientOrderId);
                chargeStatus.setPatientOrderId(patientOrderId);
                chargeStatus.setStatus(Constants.ChargeSheetStatus.UNCHARGED);
            }
            return chargeStatus;
        }).collect(Collectors.toList());
        return results;
    }


    public DeviceSelfPayChargeOrderViewRsp getDeviceSelfPayChargeOrderListByPatientId(DeviceSelfPayChargeOrderListReq chargeOrderListReq, Map<String, PatientInfo> patientInfoMap) throws ParamRequiredException {
        DeviceSelfPayChargeOrderViewRsp rsp = new DeviceSelfPayChargeOrderViewRsp();
        rsp.setResult(new ArrayList<>());

        if (CollectionUtils.isEmpty(chargeOrderListReq.getPatientIds())) {
            return rsp;
        }

        chargeOrderListReq.paramCheck();

        String chainId = chargeOrderListReq.getChainId();
        List<String> patientIds = chargeOrderListReq.getPatientIds();
        Integer offset = chargeOrderListReq.getOffset();
        Integer limit = chargeOrderListReq.getLimit();
        String clinicId = chargeOrderListReq.getClinicId();
        Instant searchBegin = chargeOrderListReq.getSearchBeginDate() != null ? chargeOrderListReq.getSearchBeginDate().toInstant() : null;


        List<DeviceSelfPayChargeOrderView> chargeOrderViewList = mChargeMapper.getDeviceSelfPayChargeOrderListByPatientIds(patientIds, chainId, clinicId, offset, limit, searchBegin);
        int count = mChargeMapper.countDeviceSelfPayChargeOrderListByPatientIds(patientIds, chainId, clinicId, searchBegin);

        //获取门诊单号
        List<String> patientOrderIds = chargeOrderViewList.stream()
                .filter(result -> !StringUtils.isEmpty(result.getPatientOrderId()))
                .map(result -> result.getPatientOrderId()).collect(Collectors.toList());

        Map<String, PatientOrder> patientOrderMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(patientOrderIds)) {
            List<PatientOrder> patientOrders = patientOrderService.findListByIds(patientOrderIds, chainId);
            if (patientOrders == null) {
                patientOrders = new ArrayList<>();
            }
            patientOrderMap = ListUtils.toMap(patientOrders, PatientOrder::getId);
        }


        if (!CollectionUtils.isEmpty(chargeOrderViewList)) {

            Map<String, PatientOrder> finalPatientOrderMap = patientOrderMap;
            chargeOrderViewList.forEach(chargeOrderView -> {
                PatientInfo patientInfo = patientInfoMap.getOrDefault(chargeOrderView.getPatientId(), null);
                if (patientInfo != null) {
                    chargeOrderView.setPatientName(patientInfo.getName());
                    chargeOrderView.setAge(patientInfo.getAge());
                    chargeOrderView.setSex(patientInfo.getSex());
                }
                PatientOrder patientOrder = finalPatientOrderMap.get(chargeOrderView.getPatientOrderId());
                if (patientOrder != null) {
                    chargeOrderView.setPatientOrderNo(patientOrder.getNo() + "");
                }
                if (Constants.ChargeSheetStatus.UNCHARGED == chargeOrderView.getStatus()) {
                    chargeOrderView.setStatus(DeviceSelfPayChargeOrderView.DeviceSelfPayChargeOrderStatus.WAITING_PAY_CAN_MODIFY_SHEET);
                    //TODO  这里应该还有个锁单状态
                } else if (Constants.ChargeSheetStatus.PART_CHARGED == chargeOrderView.getStatus() && chargeOrderView.getReceivedFee().compareTo(chargeOrderView.getReceivableFee()) < 0) {
                    chargeOrderView.setStatus(DeviceSelfPayChargeOrderView.DeviceSelfPayChargeOrderStatus.PART_PAID);
                } else {
                    chargeOrderView.setStatus(DeviceSelfPayChargeOrderView.DeviceSelfPayChargeOrderStatus.PAID_COMPLETE);
                }
                chargeOrderView.setCanUnLockChargeSheet(ChargeUtils.isPatientLockStatus(chargeOrderView.getLockStatus()));
            });

        }

        rsp.setLimit(chargeOrderViewList.size());
        rsp.setOffset(offset);
        rsp.setResult(chargeOrderViewList);
        rsp.setTotalCount(count);
        return rsp;
    }

    public ChargeOrderViewRsp getChargeOrderListByPatientId(ChargeOrderListReq chargeOrderListReq) throws ParamRequiredException {
        chargeOrderListReq.paramCheck();

        String chainId = chargeOrderListReq.getChainId();
        List<String> patientIds = chargeOrderListReq.getPatientIds();
        Integer offset = chargeOrderListReq.getOffset();
        Integer limit = chargeOrderListReq.getLimit();

        List<ChargeOrderView> chargeOrderViewList = null;
        int count = 0;
        switch (chargeOrderListReq.getQueryType()) {
            case ChargeOrderListReq.QueryType.ALL: {
                chargeOrderViewList = mChargeMapper.getChargeOrderListByPatientIds(patientIds, chainId, offset, limit);
                count = mChargeMapper.countChargeOrderListByPatientIds(patientIds, chainId);
                break;
            }
            case ChargeOrderListReq.QueryType.WAITING_PAYING: {
                chargeOrderViewList = mChargeMapper.getPayingChargeOrderListByPatientIds(patientIds, chainId, offset, limit);
                count = mChargeMapper.countPayingChargeOrderListByPatientIds(patientIds, chainId);
                break;
            }
            case ChargeOrderListReq.QueryType.WAITING_DISPENSING: {
                chargeOrderViewList = mChargeMapper.getDispensingChargeOrderListByPatientIds(patientIds, chainId, offset, limit);
                count = mChargeMapper.countDispensingChargeOrderListByPatientIds(patientIds, chainId);
                break;
            }
            case ChargeOrderListReq.QueryType.WAITING_DELIVERY: {
                chargeOrderViewList = mChargeMapper.getDeliveryChargeOrderListByPatientIds(patientIds, chainId, offset, limit, weclinicConfiguration.getAutoConfirmRecvMedicineSeconds());
                count = mChargeMapper.countDeliveryChargeOrderListByPatientIds(patientIds, chainId, weclinicConfiguration.getAutoConfirmRecvMedicineSeconds());
                break;
            }
            case ChargeOrderListReq.QueryType.FINISHED: {
                chargeOrderViewList = mChargeMapper.getFinishedChargeOrderListByPatientIds(patientIds, chainId, offset, limit, weclinicConfiguration.getAutoConfirmRecvMedicineSeconds());
                count = mChargeMapper.countFinishedChargeOrderListByPatientIds(patientIds, chainId, weclinicConfiguration.getAutoConfirmRecvMedicineSeconds());
                break;
            }
        }
        List<String> chargeSheetIds = chargeOrderViewList.stream().map(ChargeOrderView::getChargeSheetId).collect(Collectors.toList());
        Map<String, ChargeFormItemCountDto> chargeFormItemCountDtoMap;
        //根据chargesheetid查询出快递号号快递公司，并将数据主旨成nap
        List<ChargeOrderViewDeliveyInfo> deliveyInfos = null;
        Map<String, ChargeOrderViewDeliveyInfo> mapDeliveyInfos = null;
        if (!CollectionUtils.isEmpty(chargeSheetIds)) {
            //根据chargeSheetIds 设置goodsNum
            List<ChargeFormItemCountDto> chargeFormItemCountDtos = chargeFormItemDaoProxy.getChargeFormItemCountDtoBySheetIds(chargeSheetIds, chainId);
            chargeFormItemCountDtoMap = ListUtils.toMap(chargeFormItemCountDtos, ChargeFormItemCountDto::getChargeSheetId);

            deliveyInfos = mChargeMapper.getDeliveryNoCompany(chargeSheetIds, chainId);
            if (CollectionUtils.isEmpty(deliveyInfos)) {
                mapDeliveyInfos = new HashMap<>();
            } else {
                mapDeliveyInfos = deliveyInfos.stream().collect(Collectors.toMap(ChargeOrderViewDeliveyInfo::getChargeSheetId, deliveyInfo -> deliveyInfo, (a, b) -> a));
            }
        } else {
            chargeFormItemCountDtoMap = null;
        }

        if (!CollectionUtils.isEmpty(chargeOrderViewList)) {
            List<PatientInfo> patientInfos = crmService.getPatientInfoList(chainId, patientIds);
            Map<String, PatientInfo> patientInfoMap = patientInfos.stream().collect(Collectors.toMap(PatientInfo::getId, Function.identity(), (a, b) -> a));
            Map<String, String> clinicIdNameMap = scClinicService.getClinicIdNameMap(chainId);

            Map<String, ChargeOrderViewDeliveyInfo> finalMapDeliveyInfos = mapDeliveyInfos;
            chargeOrderViewList.forEach(chargeOrderView -> {
                if (chargeFormItemCountDtoMap != null) {
                    ChargeFormItemCountDto formItemCountDto = chargeFormItemCountDtoMap.get(chargeOrderView.getChargeSheetId());
                    if (formItemCountDto != null) {
                        chargeOrderView.setGoodsNum(formItemCountDto.getGoodsNum());
                    }
                }

                chargeOrderView.setClinicName(clinicIdNameMap.getOrDefault(chargeOrderView.getClinicId(), ""));

                PatientInfo patientInfo = patientInfoMap.getOrDefault(chargeOrderView.getPatientId(), null);
                if (patientInfo != null) {
                    chargeOrderView.setPatientName(patientInfo.getName());
                }

                //填充快递公司
                ChargeOrderViewDeliveyInfo deliveryInfo = finalMapDeliveyInfos.get(chargeOrderView.getChargeSheetId());
                if (deliveryInfo != null) {
                    chargeOrderView.setDeliveryOrderNo(deliveryInfo.getDeliveryOrderNo());
                    chargeOrderView.setDeliveryCompanyName(deliveryInfo.getDeliveryCompanyName());
                }
                if (Constants.ChargeSheetStatus.UNCHARGED == chargeOrderView.getStatus() && ChargeSheet.CheckStatus.NEED_CLINIC_CHECK == chargeOrderView.getCheckStatus()) {
                    chargeOrderView.setStatus(Constants.ChargeOrderStatus.WAITING_CONFIRM_PRICE);
                }
                if (Constants.ChargeSheetStatus.PART_CHARGED == chargeOrderView.getStatus() && chargeOrderView.getReceivedFee().compareTo(chargeOrderView.getReceivableFee()) < 0) {
                    chargeOrderView.setStatus(Constants.ChargeOrderStatus.WAITING_PAY);
                }
                if (chargeOrderView.getStatus() == Constants.ChargeOrderStatus.IS_PAID) {
                    //待发药
                    if (waitingDispense(chargeOrderView.getDeliveryType(), chargeOrderView.getDispensingStatus(), chargeOrderView.getAirDispensingStatus())) {
                        chargeOrderView.setStatus(Constants.ChargeOrderStatus.WAITING_DISPENSE);
                        chargeOrderView.setIsMedicinePrepared(1);
                    }
                    //部分发药
                    if (partDispense(chargeOrderView.getDeliveryType(), chargeOrderView.getDispensingStatus(), chargeOrderView.getAirDispensingStatus())) {
                        if (ChargeOrderListReq.QueryType.WAITING_DISPENSING == chargeOrderListReq.getQueryType()) {
                            chargeOrderView.setStatus(Constants.ChargeOrderStatus.SOME_DISPENSE);
                        } else {
                            chargeOrderView.setStatus(Constants.ChargeOrderStatus.GET_SOME_MEDICINE);
                        }

                        chargeOrderView.setIsMedicinePrepared(1);
                    }
                    //完成
                    int completeYetReturn = completeYet(chargeOrderView.getDeliveryType(), chargeOrderView.getDispensingStatus(), chargeOrderView.getAirDispensingStatus(), chargeOrderView.getLastModified());
                    if (completeYetReturn > 0) {
                        if (completeYetReturn == Constants.ChargeOrderStatus.GET_SOME_MEDICINE) {
                            if (ChargeOrderListReq.QueryType.WAITING_DISPENSING == chargeOrderListReq.getQueryType()) {
                                chargeOrderView.setStatus(Constants.ChargeOrderStatus.SOME_DISPENSE);
                            } else {
                                chargeOrderView.setStatus(Constants.ChargeOrderStatus.GET_SOME_MEDICINE);
                            }
                        } else {
                            chargeOrderView.setStatus(completeYetReturn);
                        }
                    }
                }
                //填充是否可以解锁给终端
                chargeOrderView.setCanUnLockChargeSheet(ChargeUtils.isPatientLockStatus(chargeOrderView.getLockStatus()));
            });

        }

        ChargeOrderViewRsp rsp = new ChargeOrderViewRsp();
        rsp.setLimit(chargeOrderViewList.size());
        rsp.setOffset(offset);
        rsp.setResult(chargeOrderViewList);
        rsp.setTotalCount(count);
        return rsp;
    }

    /**
     * 待发药 也包含了普通 一个药都没发
     */
    private boolean waitingDispense(int deliveryType, int dispensingStatus, int airDispensingStatus) {
        //这个订单本地药房和空中药房都有快递
        if (deliveryType == ChargeSheet.DeliveryType.DELIVERY_TO_HOME && airDispensingStatus > Constants.AirDispensingStatus.NONE) {
            if ((dispensingStatus == Constants.DispensingStatus.WAITING || dispensingStatus == Constants.DispensingStatus.NONE)
                    && airDispensingStatus == Constants.AirDispensingStatus.WAITING) {
                return true;
            }
            return false;
        } else if (deliveryType == ChargeSheet.DeliveryType.DELIVERY_TO_HOME) {
            if ((dispensingStatus == Constants.DispensingStatus.WAITING || dispensingStatus == Constants.DispensingStatus.NONE)) {
                return true;
            }
        } else if (airDispensingStatus > Constants.AirDispensingStatus.NONE) {
            if (airDispensingStatus == Constants.AirDispensingStatus.WAITING) {
                return true;
            }
        }

        if (deliveryType == ChargeSheet.DeliveryType.NONE) { //本地药房取药
            if ((dispensingStatus == Constants.DispensingStatus.WAITING)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 快递 部分发药
     */
    private boolean partDispense(int deliveryType, int dispensingStatus, int airDispensingStatus) {
        //这个订单本地药房和空中药房都有快递
        if (deliveryType == ChargeSheet.DeliveryType.DELIVERY_TO_HOME
                && airDispensingStatus > Constants.AirDispensingStatus.NONE) {

            if ((dispensingStatus == Constants.DispensingStatus.DISPENSED && airDispensingStatus != Constants.AirDispensingStatus.ALL_DISPENSED)//普通药房发了
                    || (dispensingStatus < Constants.DispensingStatus.DISPENSED && airDispensingStatus > Constants.AirDispensingStatus.WAITING)) { //空中药房发了
                return true;
            }
            return false;
        } else if (deliveryType == ChargeSheet.DeliveryType.DELIVERY_TO_HOME) { //普通药房，只有发药和没发药，没有部分发药状态
            return false;
        } else if (airDispensingStatus > Constants.AirDispensingStatus.WAITING) { //空中药房

            if (airDispensingStatus != Constants.AirDispensingStatus.ALL_DISPENSED) {
                return true;
            }
        }
        return false;
    }

    /**
     * 通过快递 已发药==待收药 0 没有发药 1部分发药 2全发药
     */
    private static final int DISPENSE_NO = 0;
    private static final int DISPENSE_PART = 1;
    private static final int DISPENSE_ALL = 2;
    private static final int DISPENSE_ALL_COMPLETE_WITHOUT_DELAY = 3;

    private int alreadyDispense(int deliveryType, int dispensingStatus, int airDispensingStatus) {
        //这个订单本地药房和空中药房都有快递
        if (deliveryType == ChargeSheet.DeliveryType.DELIVERY_TO_HOME
                && airDispensingStatus > Constants.AirDispensingStatus.NONE) {

            if ((dispensingStatus == Constants.DispensingStatus.DISPENSED
                    && airDispensingStatus == Constants.AirDispensingStatus.ALL_DISPENSED)) {
                return DISPENSE_ALL;
            } else if (dispensingStatus == Constants.DispensingStatus.DISPENSED) {
                return DISPENSE_PART;
            } else if (airDispensingStatus == Constants.AirDispensingStatus.ALL_DISPENSED) {
                return DISPENSE_PART;
            }
            return DISPENSE_NO;
        } else if (deliveryType == ChargeSheet.DeliveryType.DELIVERY_TO_HOME) {//只有本地药房有快递
            if (dispensingStatus == Constants.DispensingStatus.DISPENSED) {
                return DISPENSE_ALL;
            }
            return DISPENSE_NO;
        } else if (airDispensingStatus > Constants.AirDispensingStatus.NONE) {//只有空中药房
            if (airDispensingStatus > Constants.AirDispensingStatus.WAITING && airDispensingStatus < Constants.AirDispensingStatus.ALL_DISPENSED) {
                return DISPENSE_PART;
            } else if (airDispensingStatus == Constants.AirDispensingStatus.ALL_DISPENSED) {
                return DISPENSE_ALL;
            }
            return DISPENSE_NO;
        } else {
            if (dispensingStatus == Constants.DispensingStatus.DISPENSED //发药
                    || dispensingStatus == Constants.DispensingStatus.NONE) { //不需要发药，这里只能用-1来表示不需要发药
                return DISPENSE_ALL_COMPLETE_WITHOUT_DELAY;
            }
        }
        return DISPENSE_NO;
    }

    /**
     * 已完成 通过超时时间自动扭转 0 未发货待收药 非0 状态值
     */

    private int completeYet(int deliveryType, int dispensingStatus, int airDispensingStatus, Instant lastModified) {
        boolean expired = timeToTranverseComplteStatus(lastModified);
        int deliveryDispensed = alreadyDispense(deliveryType, dispensingStatus, airDispensingStatus);
        if (deliveryDispensed == DISPENSE_ALL || deliveryDispensed == DISPENSE_ALL_COMPLETE_WITHOUT_DELAY) {
            if (expired || deliveryDispensed == DISPENSE_ALL_COMPLETE_WITHOUT_DELAY) {
                return Constants.ChargeOrderStatus.COMPLETE;
            } else {
                return Constants.ChargeOrderStatus.WAITING_GET_MEDICINE;
            }
        } else if (deliveryDispensed == DISPENSE_PART) {
            return Constants.ChargeOrderStatus.GET_SOME_MEDICINE;
        } else if (deliveryType == ChargeSheet.DeliveryType.NONE && airDispensingStatus == Constants.AirDispensingStatus.NONE
                && (dispensingStatus == Constants.DispensingStatus.DISPENSED || dispensingStatus == Constants.DispensingStatus.NONE)) {
            return Constants.ChargeOrderStatus.COMPLETE; //本地药房取药 或者没有药的情况，付完钱就算完了
        }
        return 0;
    }

    private boolean timeToTranverseComplteStatus(Instant lastModified) {
        //超过lastModified,自动转成完成状态
        if (lastModified != null) {
            Instant nowTime = Instant.now();
            long seconds = nowTime.getEpochSecond() - lastModified.getEpochSecond();
            if (seconds >= weclinicConfiguration.getAutoConfirmRecvMedicineSeconds()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 微诊所我的治疗理疗单
     *
     * @param chainId                  连锁id
     * @param patientIds               患者id集合
     * @param chargeSheetExecuteStatus 收费单执行状态
     * @param offset                   分页下标
     * @param limit                    分页条数
     * @param beginDate                开始日期
     * @param endDate                  结束日期
     * @return 查询结果
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public AbcListPage<WeClinicMyExecuteSheetListView> pageListMyExecuteSheetsForWeClinic(String chainId,
                                                                                          String clinicId,
                                                                                          List<String> patientIds,
                                                                                          Integer chargeSheetExecuteStatus,
                                                                                          int offset,
                                                                                          int limit,
                                                                                          LocalDate beginDate,
                                                                                          LocalDate endDate) throws ParamRequiredException {
        if (StringUtils.isEmpty(chainId)) {
            throw new ParamRequiredException("chainId");
        }
        AbcListPage<WeClinicMyExecuteSheetListView> executeSheetPage = new AbcListPage<>();
        executeSheetPage.setLimit(limit);
        executeSheetPage.setOffset(offset);
        executeSheetPage.setRows(Lists.newArrayList());
        if (CollectionUtils.isEmpty(patientIds)) {
            return executeSheetPage;
        }
        LocalDateTime beginTime = beginDate != null ? cn.abcyun.cis.commons.util.DateUtils.beginOfDay(beginDate) : null;
        LocalDateTime endTime = endDate != null ? cn.abcyun.cis.commons.util.DateUtils.endOfDay(endDate) : null;
        offset = Math.max(offset, 0);
        if (limit <= 0) {
            limit = 20;
        }
        // 1、统计查询有执行项的收费单, 部分老数据修改收费单为退费时，未修改执行状态为已退费，所以查询已退费的执行单时，需要将收费单状态为已退费条件加入，不能只靠执行状态为已退费的这个条件
        List<Integer> executeStatuses = Lists.newArrayList();
        List<Integer> chargeSheetStatuses = Lists.newArrayList();
        chargeSheetStatuses.add(Constants.ChargeSheetStatus.UNCHARGED);
        chargeSheetStatuses.add(Constants.ChargeSheetStatus.PART_CHARGED);
        chargeSheetStatuses.add(Constants.ChargeSheetStatus.CHARGED);
        chargeSheetStatuses.add(Constants.ChargeSheetStatus.PART_REFUNDED);
        if (Objects.nonNull(chargeSheetExecuteStatus)) {
            executeStatuses.add(chargeSheetExecuteStatus);
            if (Objects.equals(chargeSheetExecuteStatus, ChargeSheet.ExecuteStatus.REFUNDED)) {
                chargeSheetStatuses.add(Constants.ChargeSheetStatus.REFUNDED);
            }
        } else {
            executeStatuses.add(ChargeSheet.ExecuteStatus.WAITING);
            executeStatuses.add(ChargeSheet.ExecuteStatus.FINISHED);
            executeStatuses.add(ChargeSheet.ExecuteStatus.REFUNDED);
            chargeSheetStatuses.add(Constants.ChargeSheetStatus.REFUNDED);
        }
        int total = mChargeMapper.countExecutiveSheet(chainId, clinicId, patientIds, executeStatuses, chargeSheetStatuses, 0, beginTime, endTime);
        executeSheetPage.setTotal(total);
        if (total == 0) {
            return executeSheetPage;
        }

        // 2、分页查询有执行项的收费单
        List<WeClinicMyExecuteSheetListView> chargeSheetList = mChargeMapper.pageListExecutiveSheet(chainId, clinicId, patientIds, executeStatuses, chargeSheetStatuses,
                0, offset, limit, beginTime, endTime);
        if (CollectionUtils.isEmpty(chargeSheetList)) {
            return executeSheetPage;
        }

        // 3、查询患者信息
        patientIds = chargeSheetList.stream().map(WeClinicMyExecuteSheetListView::getPatientId).distinct().collect(Collectors.toList());
        Map<String, String> patientIdNameMap = crmService.getPatientInfoList(chainId, patientIds)
                .stream()
                .collect(Collectors.toMap(PatientInfo::getId, PatientInfo::getName));

        // 4、查询收费单子项中需要展示在微诊所的治疗理疗项
        List<String> chargeSheetIds = chargeSheetList.stream().map(WeClinicMyExecuteSheetListView::getChargeSheetId).collect(Collectors.toList());
        List<Integer> displayStatuses = Arrays.asList(Constants.ChargeFormItemStatus.UNCHARGED, Constants.ChargeFormItemStatus.CHARGED, Constants.ChargeFormItemStatus.REFUNDED);
        Map<String, List<ChargeFormItem>> chargeSheetFormItemsMap = ListUtils.groupByKey(mChargeFormItemRepository
                .findByChargeSheetIdInAndIsDeleted(chargeSheetIds, 0)
                .stream()
                .filter(chargeFormItem -> Objects.equals(chargeFormItem.getProductType(), Constants.ProductType.TREATMENT)
                        && displayStatuses.contains(chargeFormItem.getV2Status())
                )
                .collect(Collectors.toList()), ChargeFormItem::getChargeSheetId);
        Map<String, ChargeExecuteItem> chargeFormItemExecuteItemMap = ListUtils.toMap(chargeExecuteItemRepository.findByChargeSheetIdInAndIsDeleted(chargeSheetIds, 0), ChargeExecuteItem::getChargeFormItemId);

        // 5、组装数据结果
        chargeSheetList = chargeSheetList.stream()
                .filter(chargeSheet -> chargeSheetFormItemsMap.containsKey(chargeSheet.getChargeSheetId()))
                .peek(chargeSheet -> {
                    // 部分老数据修改收费单为退费时，未修改执行状态为已退费
                    if (Objects.equals(chargeSheet.getStatus(), Constants.ChargeSheetStatus.REFUNDED)
                            && !Objects.equals(chargeSheet.getExecuteStatus(), ChargeSheet.ExecuteStatus.REFUNDED)) {
                        chargeSheet.setExecuteStatus(ChargeSheet.ExecuteStatus.REFUNDED);
                    }
                    chargeSheet
                            .setPatientName(patientIdNameMap.get(chargeSheet.getPatientId()))
                            .setExecuteItems(chargeSheetFormItemsMap.get(chargeSheet.getChargeSheetId())
                                    .stream()
                                    .filter(chargeFormItem -> chargeFormItemExecuteItemMap.containsKey(chargeFormItem.getId()))
                                    .map(chargeFormItem -> appendExecuteInfoToChargeFormItem(chargeFormItemExecuteItemMap, chargeFormItem, chargeSheet.getChargeSheetId()))
                                    .collect(Collectors.toList())
                            );
                }).collect(Collectors.toList());
        executeSheetPage.setRows(chargeSheetList);

        return executeSheetPage;
    }

    public AbcListPage<WeClinicMyExecuteSheetListView> pageListMyExecuteSheetsForWeClinicV2(String chainId,
                                                                                            String clinicId,
                                                                                            String patientId,
                                                                                            Integer chargeSheetExecuteStatus,
                                                                                            LocalDate beginDate,
                                                                                            LocalDate endDate,
                                                                                            int offset,
                                                                                            int limit) {
        return null;
    }

    /**
     * 根据收费单id、连锁id查询患者微诊所我的治疗理疗单详情
     *
     * @param chargeSheetId 收费单id
     * @param chainId       连锁id
     * @return 微诊所我的治疗理疗单详情
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public WeClinicMyExecuteSheetDetailView getMyExecuteSheetDetail(String chargeSheetId, String chainId) throws NotFoundException, ChargeServiceException {
        ChargeSheet chargeSheet = chargeSheetRepository.findByIdAndChainIdAndIsDeleted(chargeSheetId, chainId, 0).orElse(null);
        if (Objects.isNull(chargeSheet)) {
            throw new NotFoundException();
        }
        // 1、判断收费单执行状态
        if (Objects.equals(chargeSheet.getExecuteStatus(), ChargeSheet.ExecuteStatus.NONE)) {
            throw new ChargeServiceException(ChargeServiceError.NO_EXECUTE_ITEM_IN_CHARGE_SHEET);
        }

        // 2、判断是否存在需要展示的收费单子项
        List<Integer> displayStatuses = Arrays.asList(Constants.ChargeFormItemStatus.UNCHARGED, Constants.ChargeFormItemStatus.CHARGED, Constants.ChargeFormItemStatus.REFUNDED);
        List<ChargeFormItem> treatmentChargeFormItems = mChargeFormItemRepository.findByChargeSheetIdInAndIsDeleted(Collections.singletonList(chargeSheetId), 0)
                .stream()
                .filter(chargeFormItem -> Objects.equals(chargeFormItem.getProductType(), Constants.ProductType.TREATMENT)
                        && displayStatuses.contains(chargeFormItem.getV2Status())
                )
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(treatmentChargeFormItems)) {
            throw new ChargeServiceException(ChargeServiceError.NO_EXECUTE_ITEM_IN_CHARGE_SHEET);
        }

        // 3、查询执行项集合
        Map<String, ChargeExecuteItem> chargeFormItemExecuteItemMap = ListUtils.toMap(chargeExecuteItemRepository.findByChargeSheetIdAndIsDeleted(chargeSheetId, 0), ChargeExecuteItem::getChargeFormItemId);
        treatmentChargeFormItems = treatmentChargeFormItems
                .stream()
                .filter(chargeFormItem -> chargeFormItemExecuteItemMap.containsKey(chargeFormItem.getId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(treatmentChargeFormItems)) {
            throw new ChargeServiceException(ChargeServiceError.NO_EXECUTE_ITEM_IN_CHARGE_SHEET);
        }

        // 4、查询存在治疗理疗项的执行记录列表
        List<ChargeExecuteRecordView> executeRecords = chargeExecuteRecordService
                .listExecuteRecordByChargeSheetId(chargeSheetId, chargeSheet.getClinicId(), chainId, ChargeExecuteRecord.ExecuteRecordStatus.EXECUTED)
                .getExecuteRecords()
                .stream()
                .filter(executeRecordView -> CollectionUtils.isNotEmpty(executeRecordView.getItems()))
                .collect(Collectors.toList());

        // 5、查询组织信息
        ClinicForCharge clinic = clinicService.getClinicById(chargeSheet.getClinicId());
        String clinicName = Optional.ofNullable(clinic)
                .map(clinicForCharge -> Objects.toString(clinic.getShortName(), clinic.getName()))
                .orElse(null);

        // 6、查询患者信息
        String patientName = Optional.ofNullable(crmService.searchPatientInfoByPatientIdFromCrm(chargeSheet.getChainId(), chargeSheet.getClinicId(), chargeSheet.getPatientId(), false, false))
                .map(PatientInfo::getName).orElse(null);

        // 7、治疗理疗默认单位
        Set<String> treatmentDefaultUnits = goodsService.getGoodsSysUnit(chainId, (int) GoodsConst.GoodsType.TREATMENT).stream().map(GoodsCustomUnitRsp.ClientCustomUnitItem::getName).collect(Collectors.toSet());
        Function<String, String> recordItemCountUnitConcatFunc = itemUnit -> treatmentDefaultUnits.contains(itemUnit) ? "" : "*";

        return new WeClinicMyExecuteSheetDetailView()
                .setChargeSheetId(chargeSheetId)
                .setPatientId(chargeSheet.getPatientId())
                .setPatientName(patientName)
                .setCreated(chargeSheet.getCreated())
                .setExecuteStatus(Objects.equals(chargeSheet.getStatus(), Constants.ChargeSheetStatus.REFUNDED)
                        ? ChargeSheet.ExecuteStatus.REFUNDED
                        : chargeSheet.getExecuteStatus())
                .setClinic(clinic)
                .setExecuteItems(treatmentChargeFormItems
                        .stream()
                        .map(chargeFormItem -> appendExecuteInfoToChargeFormItem(chargeFormItemExecuteItemMap, chargeFormItem, chargeSheetId))
                        .collect(Collectors.toList()))
                .setExecuteRecords(executeRecords
                        .stream()
                        .map(executeRecord -> {
                            int effectVisibleForPatient = executeRecord.getEffectVisibleForPatient();
                            List<ExecuteRecordEffectView> effects = Lists.newArrayList();
                            if (effectVisibleForPatient == 1 && !CollectionUtils.isEmpty(executeRecord.getEffects())) {
                                effects = executeRecord.getEffects();
                            }

                            return new WeClinicExecuteRecordView()
                                    .setId(executeRecord.getId())
                                    .setChargeSheetId(chargeSheetId)
                                    .setChainId(chainId)
                                    .setClinicId(executeRecord.getClinicId())
                                    .setClinicName(clinicName)
                                    .setStatus(executeRecord.getStatus())
                                    .setExecuteDate(executeRecord.getExecuteDate())
                                    .setEffectVisibleForPatient(effectVisibleForPatient)
                                    .setExecutorNames(executeRecord.getExecutors().stream().map(EmployeeView::getName).collect(Collectors.joining("，")))
                                    .setExecuteRecordItems(executeRecord.getItems()
                                            .stream()
                                            .map(executeRecordItem -> String.format("%s*%s%s%s", executeRecordItem.getExecuteItemName(), executeRecordItem.getCount(), recordItemCountUnitConcatFunc.apply(executeRecordItem.getUnit()), executeRecordItem.getUnit()))
                                            .collect(Collectors.joining("，"))
                                    )
                                    .setExecuteEffects(
                                            effects
                                                    .stream()
                                                    .map(executeRecordEffectView -> new WeClinicChargeExecuteRecordEffectView()
                                                            .setExecuteRecordId(executeRecordEffectView.getExecuteRecordId())
                                                            .setTreatmentMethod(executeRecordEffectView.getTreatmentMethod())
                                                            .setTreatmentSite(executeRecordEffectView.getTreatmentSite())
                                                            .setTreatmentResponse(executeRecordEffectView.getTreatmentResponse())
                                                            .setEtiologyPathogenesis(executeRecordEffectView.getEtiologyPathogenesis())
                                                            .setTreatmentResult(executeRecordEffectView.getTreatmentResult())
                                                            .setExecuteDate(executeRecordEffectView.getExecuteDate())
                                                            .setExecuteTimeStart(executeRecordEffectView.getExecuteTimeStart())
                                                            .setExecuteTimeEnd(executeRecordEffectView.getExecuteTimeEnd())
                                                            .setAttachments(
                                                                    Optional.ofNullable(executeRecordEffectView.getAttachments()).orElse(Lists.newArrayList())
                                                                            .stream()
                                                                            .map(ExecuteRecordEffectAttachmentView::getUrl)
                                                                            .collect(Collectors.toList())
                                                            )
                                                    )
                                                    .collect(Collectors.toList())
                                    );
                        })
                        .collect(Collectors.toList())
                );
    }


    private ChargeFormItemExecuteView appendExecuteInfoToChargeFormItem(Map<String, ChargeExecuteItem> chargeFormItemExecuteItemMap, ChargeFormItem chargeFormItem, String chargeSheetId) {
        ChargeFormItemExecuteView formItemExecuteView = new ChargeFormItemExecuteView()
                .setId(chargeFormItem.getId())
                .setChainId(chargeFormItem.getChainId())
                .setClinicId(chargeFormItem.getClinicId())
                .setChargeSheetId(chargeSheetId)
                .setProductId(chargeFormItem.getProductId())
                .setProductName(chargeFormItem.getName())
                .setProductType(chargeFormItem.getProductType())
                .setProductSubType(chargeFormItem.getProductSubType())
                .setStatus(chargeFormItem.getV2Status());
        if (Objects.equals(chargeFormItem.getV2Status(), Constants.ChargeFormItemStatus.REFUNDED)) {
            return formItemExecuteView;
        }
        ChargeExecuteItem chargeExecuteItem = chargeFormItemExecuteItemMap.get(chargeFormItem.getId());
        return formItemExecuteView.setUnit(chargeFormItem.getUnit())
                .setUnitCount(chargeExecuteItem.getUnitCount())
                .setExecutedCount(chargeExecuteItem.getExecutedCount())
                .setRetainCount(MathUtils.wrapBigDecimalSubtract(chargeExecuteItem.getUnitCount(), chargeExecuteItem.getExecutedCount()));
    }

    public ChargeOrderCountViewRsp getChargeOrderCountByPatientId(ChargeOrderCountViewReq req) throws ParamRequiredException {

        List<String> patientIds = req.getPatientIds();
        ChargeOrderCountViewRsp rsp = new ChargeOrderCountViewRsp();
        int queryType = req.getQueryType();
        if ((queryType & ChargeOrderCountViewReq.QueryType.WAITING_PAYING) != 0) {
            rsp.setWaitingPayCount(mChargeMapper.countPayingChargeOrderListByPatientIds(patientIds, req.getChainId()));
        }
        if ((queryType & ChargeOrderCountViewReq.QueryType.WAITING_DISPENSING) != 0) {
            rsp.setWaitingDispensingCount(mChargeMapper.countDispensingChargeOrderListByPatientIds(patientIds, req.getChainId()));
        }
        if ((queryType & ChargeOrderCountViewReq.QueryType.WAITING_DELIVERY) != 0) {
            rsp.setWaitingDeliveryCount(mChargeMapper.countDeliveryChargeOrderListByPatientIds(patientIds, req.getChainId(), weclinicConfiguration.getAutoConfirmRecvMedicineSeconds()));
        }
        return rsp;
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public ChargeSheetSimpleListRsp queryPaidChargeSheetsByDate(String clinicId, Instant beginDate, Instant endDate) {
        if (org.apache.commons.lang3.StringUtils.isBlank(clinicId) || Objects.isNull(beginDate) || Objects.isNull(endDate)) {
            throw new ParamRequiredException("clinicId、beginDate、endDate");
        }
        Duration duration = Duration.between(beginDate, endDate);
        if (duration.getSeconds() > 3 * 86400) {
            throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_QUERY_TIME_OUT_OF_THREE_DAY);
        }
        List<ChargeSheetSimpleListRsp.ChargeSheetSimple> chargeSheetSimples = mChargeMapper.queryPaidChargeSheetsByDate(clinicId, beginDate, endDate);
        // @TODO 设置收费单的DataFlag,目前从Transaction交易记录表来判断,后续直接从ChargeSheet获取
        if (CollectionUtils.isNotEmpty(chargeSheetSimples)) {
            List<String> chargeSheedIdList = mChargeMapper.queryChargeTransactionHealthCard(clinicId, chargeSheetSimples.parallelStream().map(ChargeSheetSimpleListRsp.ChargeSheetSimple::getId).distinct().collect(Collectors.toList()), Constants.ChargePayMode.HEALTH_CARD, YesOrNo.NO, YesOrNo.NO);
            if (CollectionUtils.isNotEmpty(chargeSheedIdList)) {
                chargeSheetSimples.parallelStream().forEach(simple -> {
                    if (chargeSheedIdList.contains(simple.getId())) {
                        simple.setDataFlag(ChargeConstants.DataFlag.HAS_CHARGE_SHEET_SHE_BAO_PAID);
                    }
                });
            }
        }
        ChargeSheetSimpleListRsp rsp = new ChargeSheetSimpleListRsp();
        rsp.setChargeSheets(Optional.ofNullable(chargeSheetSimples).orElse(new ArrayList<>()));
        return rsp;
    }

    @Transactional(rollbackFor = Exception.class)
    public DeleteChargeSheetForFamilyDoctorCancelRsp deleteChargeSheetForFamilyDoctorCancel(String chargeSheetId, String operatorId) {
        DeleteChargeSheetForFamilyDoctorCancelRsp rsp = new DeleteChargeSheetForFamilyDoctorCancelRsp();
        ChargeSheet chargeSheet = findByIdContainIsDeleted(chargeSheetId);

        if (chargeSheet == null) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeSheet is null, chargeSheetId: {}", chargeSheetId);
            throw new NotFoundException();
        }

        if (chargeSheet.getIsDeleted() == 1) {
            return rsp.setCode(DeleteChargeSheetForFamilyDoctorCancelRsp.Code.DELETE_SUCCESS)
                    .setChargeSheetId(chargeSheetId);
        }

        if (chargeSheet.getType() != ChargeSheet.Type.FAMILY_DOCTOR_SIGN) {
            throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_TYPE_IS_NOT_FAMILY_DOCTOR_SIGN);
        }

        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED) {
            return rsp.setCode(DeleteChargeSheetForFamilyDoctorCancelRsp.Code.NOT_NEED_DELETED)
                    .setChargeSheetId(chargeSheetId);
        }

        chargeSheet.deleteModel(operatorId);

        return rsp.setCode(DeleteChargeSheetForFamilyDoctorCancelRsp.Code.DELETE_SUCCESS)
                .setChargeSheetId(chargeSheetId);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public cn.abcyun.bis.rpc.sdk.cis.model.charge.ChargeSheetSimpleListRsp.ChargeSheetSimple findChargeSheetSimpleById(String chargeSheetId) {
        return mChargeMapper.findChargeSheetSimpleById(chargeSheetId);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public List<ChargeSheetSimpleListRsp.ChargeSheetSimple> listSimpleChargeSheets(String chainId, List<String> chargeSheetIds) {


        if (StringUtils.isEmpty(chainId) || CollectionUtils.isEmpty(chargeSheetIds)) {
            return new ArrayList<>();
        }

        return Optional.ofNullable(mChargeMapper.listChargeSheetSimpleByIds(chainId, chargeSheetIds))
                .orElse(new ArrayList<>());
    }

    @Transactional(rollbackFor = Exception.class)
    public CreateChargeSheetForThirdPartyRsp createChargeSheetForThirdParty(CreateChargeSheetForThirdPartyReq req) {

        int chargeSheetType = ChargeSheet.Type.UNKNOWN;

        Function<ChargeSheet, ChargeForm> createChargeFormFunction = null;

        switch (req.getType()) {
            case CreateChargeSheetForThirdPartyReq.Type.PROMOTION_CARD_OPEN:
                chargeSheetType = ChargeSheet.Type.PROMOTION_CARD_OPEN;
                createChargeFormFunction = chargeSheet -> ChargeFormFactory.insertPromotionCardOpenForm(chargeSheet, req.getAmount(), req.getOperatorId());
                break;
            case CreateChargeSheetForThirdPartyReq.Type.PROMOTION_CARD_RECHARGE:
                chargeSheetType = ChargeSheet.Type.PROMOTION_CARD_RECHARGE;
                createChargeFormFunction = chargeSheet -> ChargeFormFactory.insertPromotionCardRechargeForm(chargeSheet, req.getAmount(), req.getOperatorId());
                break;
            case CreateChargeSheetForThirdPartyReq.Type.MEMBER_CARD_RECHARGE:
                chargeSheetType = ChargeSheet.Type.MEMBER_RECHARGE;
                createChargeFormFunction = chargeSheet -> ChargeFormFactory.insertMemberCardRechargeForm(chargeSheet, req.getAmount(), req.getOperatorId());
        }

        if (chargeSheetType == ChargeSheet.Type.UNKNOWN) {
            throw new ParamNotValidException("type参数不合法");
        }

        // 如果非开卡收费单，并且议价值为0，设置以价值为null
        if (Objects.nonNull(req.getExpectedAdjustmentFee()) && req.getExpectedAdjustmentFee().compareTo(BigDecimal.ZERO) == 0 && !Objects.equals(chargeSheetType, ChargeSheet.Type.PROMOTION_CARD_OPEN)) {
            req.setExpectedAdjustmentFee(null);
        }
        // 只有开卡的收费单才允许在这里进行议价
        if (Objects.nonNull(req.getExpectedAdjustmentFee()) && !Objects.equals(chargeSheetType, ChargeSheet.Type.PROMOTION_CARD_OPEN)) {
            throw new ChargeServiceException(ChargeServiceError.DISABLE_ADJUST_WHEN_ONLY_SUPPORT_PAY);
        }

        ChargeSheet chargeSheet = createChargeSheetForThirdParty(req.getPatient(), req.getClinicId(),
                req.getChainId(), chargeSheetType, req.getAmount(), req.getSellerId(),
                createChargeFormFunction, req.getOperatorId(), req.getExpectedAdjustmentFee(),
                ChargeVersionConstants.convertChargeVersion(req.getClinicId(), scClinicService));

        //【ChargeForm算费】--  初始化算费对象，核心是绑定了ChargeSheet和ChargeService
        SheetProcessorDispatcher.ISheetProcessor sheetProcessor = SheetProcessorDispatcher.newSheetProcessor(chargeSheet);
        sheetProcessor.setSheetProcessorInfoProvider(sheetProcessorService);
        sheetProcessor.setOperatorId(req.getOperatorId());
        sheetProcessor.build(); //这个函数是一个很重要的函数，她会为每个ChargeFrom，ChargeFormItem生成对应的Processor算费对象
        CalculateChargeResult calculateChargeResult = sheetProcessor.calculateSheetFee(chargeSheet.getDraftAdjustmentFee(), null, new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), null, null, Constants.ChargeSource.CHARGE, false, false);

        save(chargeSheet, null);

        MQProducer.doAfterTransactionCommit(() -> hamqProducer.notifyChargeMessage(chargeSheet.getPatientOrderId(),
                chargeSheet.getClinicId(),
                chargeSheet.getId(),
                chargeSheet.getPatientId(),
                req.getOperatorId(),
                PatientOrderMessage.MSG_TYPE_CHARGE_CREATED,
                PatientOrderService.getAgeLockTimeForMessage(chargeSheet, PatientOrderMessage.MSG_TYPE_CHARGE_CREATED)));
        tobMessageService.pushChargeTodoMessage(chargeSheet.getChainId(), chargeSheet.getClinicId(), chargeSheet.getType());

        CreateChargeSheetForThirdPartyRsp rsp = new CreateChargeSheetForThirdPartyRsp();
        rsp.setChargeSheetId(chargeSheet.getId());
        rsp.setPatientOrderId(chargeSheet.getPatientOrderId());
        rsp.setStatus(chargeSheet.getStatus());
        rsp.setNeedPayFee(calculateChargeResult.getNeedPayFee());
        return rsp;
    }

    private ChargeSheet createChargeSheetForThirdParty(cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientInfo patientInfo,
                                                       String clinicId,
                                                       String chainId,
                                                       int chargeSheetType,
                                                       BigDecimal amount,
                                                       String sellerId,
                                                       Function<ChargeSheet, ChargeForm> createChargeFormFunction,
                                                       String operatorId,
                                                       BigDecimal expectedAdjustmentFee,
                                                       int chargeVersion) {

        PatientOrder patientOrder = patientOrderService.createPatientOrder(PatientOrder.SourceClientType.WECLINIC,
                ChargeUtils.convertCisPatientInfoFromPatientInfo(patientInfo),
                null, clinicId, chainId, operatorId, chargeSheetType, null, null, null);
        if (patientOrder == null) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "createChargeSheet... 创建就诊单失败");
            throw new ChargeServiceException(ChargeServiceError.CREATE_PATIENT_ORDER_FAILED);
        }
        ChargeSheet chargeSheet = new ChargeSheet();
        chargeSheet.setId(AbcIdUtils.getUUID());
        chargeSheet.setPatientOrderId(patientOrder.getId());
        chargeSheet.setChainId(patientOrder.getChainId());
        chargeSheet.setClinicId(patientOrder.getClinicId());
        chargeSheet.setPatientId(patientOrder.getPatientId());
        chargeSheet.setType(chargeSheetType);
        chargeSheet.setChargeForms(new ArrayList<>());
        chargeSheet.setTotalFee(amount);
        chargeSheet.setSellerId(sellerId);
        chargeSheet.setDiscountFee(BigDecimal.ZERO);
        chargeSheet.setAdditionalFee(BigDecimal.ZERO);
        chargeSheet.setRefundFee(BigDecimal.ZERO);
        chargeSheet.setReceivableFee(amount);
        chargeSheet.setReceivedFee(BigDecimal.ZERO);
        chargeSheet.setDraftAdjustmentFee(expectedAdjustmentFee);
        ChargeUtils.insertChargeSheetAdditionalIfNeed(chargeSheet, operatorId);
        chargeSheet.getAdditional().setChargeVersion(chargeVersion);
        FillUtils.fillCreatedBy(chargeSheet, operatorId);

        ChargeForm chargeForm = createChargeFormFunction.apply(chargeSheet);
        if (chargeForm != null) {
            chargeSheet.getChargeForms().add(chargeForm);
        }

        return chargeSheet;
    }

    @Transactional(rollbackFor = Exception.class)
    public CreatePaidChargeSheetRsp createPaidChargeSheet(CreatePaidChargeSheetReq req) {

        int chargeSheetType = ChargeSheet.Type.UNKNOWN;

        Function<ChargeSheet, ChargeForm> createChargeFormFunction = null;

        switch (req.getType()) {
            case CreatePaidChargeSheetReq.Type.PROMOTION_CARD_OPEN:
                chargeSheetType = ChargeSheet.Type.PROMOTION_CARD_OPEN;
                createChargeFormFunction = chargeSheet -> ChargeFormFactory.insertPromotionCardOpenForm(chargeSheet, req.getAmount(), req.getOperatorId());
                break;
            case CreatePaidChargeSheetReq.Type.PROMOTION_CARD_RECHARGE:
                chargeSheetType = ChargeSheet.Type.PROMOTION_CARD_RECHARGE;
                createChargeFormFunction = chargeSheet -> ChargeFormFactory.insertPromotionCardRechargeForm(chargeSheet, req.getAmount(), req.getOperatorId());
                break;
        }

        if (chargeSheetType == ChargeSheet.Type.UNKNOWN) {
            throw new ParamNotValidException("type参数不合法");
        }

        PatientOrder patientOrder = patientOrderService.createPatientOrder(PatientOrder.SourceClientType.WECLINIC,
                ChargeUtils.convertCisPatientInfoFromPatientInfo(req.getPatient()),
                null, req.getClinicId(), req.getChainId(), req.getOperatorId(), chargeSheetType, null, null, null);
        if (patientOrder == null) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "insertOrUpdatePatientOrder... 创建就诊单失败");
            throw new ChargeServiceException(ChargeServiceError.CREATE_PATIENT_ORDER_FAILED);
        }

        ChargeSheet chargeSheet = new ChargeSheet();
        chargeSheet.setId(AbcIdUtils.getUUID());
        chargeSheet.setPatientOrderId(patientOrder.getId());
        chargeSheet.setChainId(patientOrder.getChainId());
        chargeSheet.setClinicId(patientOrder.getClinicId());
        chargeSheet.setPatientId(patientOrder.getPatientId());
        chargeSheet.setStatus(Constants.ChargeSheetStatus.CHARGED);
        chargeSheet.setSellerId(req.getSellerId());
        chargeSheet.setMemberId(req.getMemberId());
        chargeSheet.setTotalFee(req.getAmount());
        chargeSheet.setReceivedFee(req.getAmount());
        chargeSheet.setChargedTime(Instant.now());
        chargeSheet.setFirstChargedTime(Instant.now());
        chargeSheet.setType(chargeSheetType);
        chargeSheet.setChargeForms(new ArrayList<>());
        FillUtils.fillCreatedBy(chargeSheet, req.getOperatorId());

        ChargeForm chargeForm = createChargeFormFunction.apply(chargeSheet);
        if (chargeForm != null) {

            chargeForm.setStatus(Constants.ChargeFormStatus.CHARGED);

            if (chargeForm.getChargeFormItems() != null) {
                chargeForm.getChargeFormItems().forEach(chargeFormItem -> {
                    chargeFormItem.setStatus(Constants.ChargeFormItemStatus.CHARGED);
                    chargeFormItem.setV2Status(Constants.ChargeFormItemStatus.CHARGED);
                });
            }

            chargeSheet.getChargeForms().add(chargeForm);
        }


        BigDecimal amount = req.getAmount();
        //兼容线上逻辑
        if (amount.compareTo(BigDecimal.ZERO) < 0) {
            req.setPayType(CreatePaidChargeSheetReq.PayType.REFUND);
        } else if (amount.compareTo(BigDecimal.ZERO) > 0) {
            req.setPayType(CreatePaidChargeSheetReq.PayType.PAY);
        }
        int payType = req.getPayType();
        int payMode = req.getPayMode();

        CombinedPayItem payItem = new CombinedPayItem();
        payItem.setAmount(MathUtils.wrapBigDecimalOrZero(amount));
        payItem.setPayMode(payMode);
        payItem.setPaySubMode(0);
        payItem.setChange(BigDecimal.ZERO);
        //目前第三方支付回调没有卡项支付，如果以后要支持，这里肯定要在chargePayTransaction上存储一个名字，直接从上面取就行嘞
        payItem.bindPayActionInfo("", chargeConfigService.getChargePayModeConfigSimpleByChainId(chargeSheet.getChainId()));


        ChargeAction chargeAction = ChargeUtils.insertChargeAction(null, chargeSheet, payType == CreatePaidChargeSheetReq.PayType.REFUND ? ChargeAction.Type.REFUND : ChargeAction.Type.PAY, PayStatus.SUCCESS,
                amount, payItem.getPayActionInfo(), null, null, req.getOperatorId(), null);

        ThirdPartyPayInfo thirdPartyPayInfo = new ThirdPartyPayInfo();
        thirdPartyPayInfo.setTransactionId(req.getRecordId());

        ChargeTransaction chargeTransaction = ChargeUtils.insertChargeTransaction(null, chargeSheet, req.getPayMode(), Constants.ChargePaySubMode.ONLY_RECORD, amount, amount,
                BigDecimal.ZERO, amount, BigDecimal.ZERO, BigDecimal.ZERO, Constants.ChargeSource.MEMBER_CARD_RECHARGE, thirdPartyPayInfo, chargeAction, req.getOperatorId());

        save(chargeSheet, null);

        int chargeType = payType == CreatePaidChargeSheetReq.PayType.REFUND ? ChargeTransactionRecord.ChargeType.REFUND : ChargeTransactionRecord.ChargeType.CHARGED;
        ChargeFormItem chargeFormItem = Optional.ofNullable(chargeForm)
                .map(ChargeForm::getChargeFormItems)
                .orElse(new ArrayList<>())
                .stream()
                .findFirst()
                .orElse(null);

        ChargeTransactionRecord transactionRecord = ChargeUtils.createChargeTransactionRecord(chargeSheet, chargeTransaction.getId(), chargeFormItem, req.getAmount().abs(), chargeType, ChargeTransactionRecord.RecordType.PRODUCT, req.getOperatorId());
        chargeTransactionRecordService.saveAll(Collections.singletonList(transactionRecord));

        MQProducer.doAfterTransactionCommit(() -> {
            if (payType == CreatePaidChargeSheetReq.PayType.PAY) {
                hamqProducer.notifyChargeSheetMessage(chargeSheet, Collections.singletonList(chargeTransaction), Collections.singletonList(transactionRecord), Maps.newHashMap(), ChargeSheetMessage.MSG_TYPE_CHARGED, null, req.getOperatorId());
            } else {
                hamqProducer.notifyChargeSheetMessage(chargeSheet, Collections.singletonList(chargeTransaction), Collections.singletonList(transactionRecord), Maps.newHashMap(), ChargeSheetMessage.MSG_TYPE_REFUNDED, null, req.getOperatorId());
            }
        });

        CreatePaidChargeSheetRsp rsp = new CreatePaidChargeSheetRsp();
        rsp.setId(chargeSheet.getId());
        rsp.setChargeTransactions(new ArrayList<>());
        if (chargeSheet.getChargeTransactions() != null) {
            for (ChargeTransaction dbChargeTransaction : chargeSheet.getChargeTransactions()) {
                cn.abcyun.bis.rpc.sdk.cis.model.charge.ChargeTransaction rpcChargeTransaction = new cn.abcyun.bis.rpc.sdk.cis.model.charge.ChargeTransaction();
                BeanUtils.copyProperties(dbChargeTransaction, rpcChargeTransaction);
                rsp.getChargeTransactions().add(rpcChargeTransaction);
            }
        }
        rsp.setSellerId(chargeSheet.getSellerId());
        rsp.setPatientOrderId(chargeSheet.getPatientOrderId());
        rsp.setStatus(chargeSheet.getStatus());

        return rsp;
    }

    @Transactional(rollbackFor = Exception.class)
    public BaseSuccessRsp clearChargeSheetShebaoError(String chargeSheetId, String operatorId) {
        chargeSheetRepository.clearChargeSheetShebaoError(chargeSheetId, Constants.ChargeSheetQueryExceptionType.SHEBAO_PAY, operatorId);
        return new BaseSuccessRsp(200, "成功");
    }

    @Transactional(rollbackFor = Exception.class)
    public BaseSuccessRsp markChargeSheetShebaoError(String chargeSheetId, String operatorId) {
        chargeSheetRepository.markChargeSheetShebaoError(chargeSheetId, Constants.ChargeSheetQueryExceptionType.SHEBAO_PAY, operatorId);
        return new BaseSuccessRsp(200, "成功");
    }

    @Transactional(rollbackFor = Exception.class)
    public BaseSuccessRsp clearChargePayTransactionShebaoError(ShebaoErrorUpdateReq req) {

        if (org.apache.commons.lang3.StringUtils.isBlank(req.getChargePayTransactionId())) {
            return clearChargeSheetShebaoError(req.getChargeSheetId(), req.getOperatorId());
        }

        //查询是否还有异常，如果还有异常，则直接返回
        chargePayTransactionRepository.clearChargePayTransactionError(req.getChargePayTransactionId(), req.getClinicId(), req.getTaskId(), req.getOperatorId());

        int errorCount = chargePayTransactionRepository.queryChargePayTransactionErrorCount(req.getChargePayTransactionId(), Arrays.asList(Constants.ChargePayMode.HEALTH_CARD), req.getClinicId(), req.getChargeSheetId());
        if (errorCount == 0) {
            chargeSheetRepository.clearChargeSheetShebaoError(req.getChargeSheetId(), Constants.ChargeSheetQueryExceptionType.SHEBAO_PAY, req.getOperatorId());
        }
        return new BaseSuccessRsp(200, "成功");
    }

    @Transactional(rollbackFor = Exception.class)
    public BaseSuccessRsp batchClearChargePayTransactionShebaoError(cn.abcyun.cis.charge.api.model.BatchShebaoErrorUpdateReq req) {

        String clinicId = req.getClinicId();
        List<String> chargeSheetIds = req.getChargeSheetInfos().stream()
                .map(BatchShebaoErrorUpdateReq.ChargeSheetInfo::getChargeSheetId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(chargeSheetIds)) {
            return new BaseSuccessRsp(200, "成功");
        }

        //清除支付单的异常
        chargePayTransactionRepository.batchClearChargePayTransactionError(chargeSheetIds, clinicId, req.getOperatorId());
        //清除收费单的异常
        chargeSheetRepository.batchClearChargeSheetShebaoError(chargeSheetIds, Constants.ChargeSheetQueryExceptionType.SHEBAO_PAY, req.getOperatorId());

        return new BaseSuccessRsp(200, "成功");
    }

    @Transactional(rollbackFor = Exception.class)
    public BaseSuccessRsp markChargePayTransactionShebaoError(ShebaoErrorUpdateReq req) {

        //兼容历史逻辑
        if (org.apache.commons.lang3.StringUtils.isBlank(req.getChargePayTransactionId())) {
            return markChargeSheetShebaoError(req.getChargeSheetId(), req.getOperatorId());
        }

        chargePayTransactionRepository.markChargePayTransactionError(req.getChargePayTransactionId(), req.getClinicId(), req.getTaskId(), req.getOperatorId());
        chargeSheetRepository.markChargeSheetShebaoError(req.getChargeSheetId(), Constants.ChargeSheetQueryExceptionType.SHEBAO_PAY, req.getOperatorId());
        return new BaseSuccessRsp(200, "成功");
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateChargeSheetExceptionType(ChargeAbnormalTransaction abnormalTransaction) {

        ChargeSheet chargeSheet = findByIdContainIsDeleted(abnormalTransaction.getChargeSheetId());

        if (chargeSheet == null) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeSheet is null");
            return;
        }

        PayModeInfo payModeInfo = InnerPayModes.getPayModeInfo(abnormalTransaction.getPayMode(), abnormalTransaction.getPaySubMode());
        if (payModeInfo == null) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "payModeEnum is null, payMode: {}", abnormalTransaction.getPayMode());
            return;
        }

        chargeAbnormalTransactionRepository.updateStatusToErrorById(abnormalTransaction.getId());

        int thisTimeChargeSheetExceptionType = payModeInfo.getExceptionType();
        int exceptionType = chargeSheet.getQueryExceptionType();

        if ((exceptionType & thisTimeChargeSheetExceptionType) == 0) {
            chargeSheet.setQueryExceptionType(exceptionType | thisTimeChargeSheetExceptionType);
        }
    }

    /**
     * 保存异常入账数据
     *
     * @param payCallbackReq
     * @param chargePayTransaction
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void saveChargeSheetExceptionType(PayCallbackContainPayModeReq payCallbackReq, ChargePayTransaction chargePayTransaction) {
        if (chargePayTransaction == null) {
            return;
        }

        String chargeSheetId = chargePayTransaction.getChargeSheetId();
        ChargeSheet chargeSheet = findByIdContainIsDeleted(chargeSheetId);

        if (chargeSheet == null) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeSheet is null");
            return;
        }

        PayModeInfo payModeInfo = InnerPayModes.getPayModeInfo(chargePayTransaction.getPayMode(), chargePayTransaction.getPaySubMode());
        if (payModeInfo == null) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "payModeEnum is null, payMode: {}", chargePayTransaction.getPayMode());
            return;
        }

        int thisTimeChargeSheetExceptionType = payModeInfo.getExceptionType();
        int exceptionType = chargeSheet.getQueryExceptionType();

        exceptionType = exceptionType | thisTimeChargeSheetExceptionType;

        //不能直接修改chargeSheet的属性，因为可能会出现dataVersion发生变化的错误，用sql来改queryExceptionType
        chargeSheetRepository.updateQueryExceptionType(chargeSheet.getId(), exceptionType);
        chargePayTransaction.setPayStatus(cn.abcyun.cis.commons.rpc.pay.PayStatus.ERROR);
        chargePayTransactionRepository.save(chargePayTransaction);


        //获取payModeDisPlayName
        String payModeDisPlayName = getPayModeDisplayName(chargeSheet.getChainId(), payCallbackReq.getPayMode(), payCallbackReq.getPaySubMode());

        chargeAbnormalTransactionService.saveChargeAbnormalTransaction(chargePayTransaction.getChainId(), chargePayTransaction.getClinicId(), chargePayTransaction.getChargeSheetId(),
                chargePayTransaction.getId(), chargePayTransaction.getPayMode(), payCallbackReq.getPaySubMode(),
                payModeDisPlayName, chargePayTransaction.getPayReq(),
                payCallbackReq.calculateReceivedFee(), Instant.now(),
                ChargeAbnormalTransactionService.convertToChargeAbnormalTransactionType(chargePayTransaction.getPayType()),
                ChargeAbnormalTransaction.Status.IN_TRANSACTION_ERROR,
                payCallbackReq.getTaskId(),
                payCallbackReq.getOperatorId()
        );
    }

    public String getPayModeDisplayName(String chainId, int payMode, int paySubMode) {
        Map<Long, ChargePayModeConfigSimple> chargePayModeConfigSimpleMap = chargeConfigService.getChargePayModeConfigSimpleByChainId(chainId);

        if (MapUtils.isEmpty(chargePayModeConfigSimpleMap)) {
            return "";
        }

        String payModeName = Optional.ofNullable(chargePayModeConfigSimpleMap.getOrDefault((long) payMode, null))
                .map(chargePayModeConfigSimple -> chargePayModeConfigSimple.getName()).orElse("");

        if (payMode == Constants.ChargePayMode.ABC_PAY) {
            ChargeAction.PayActionInfo payActionInfo = new ChargeAction.PayActionInfo();
            payActionInfo.setPayMode(payMode);
            payActionInfo.setPaySubMode(paySubMode);
            payActionInfo.setPayModeName("ABC支付");
            ChargeUtils.buildAbcPaySubModeName(payActionInfo, paySubMode);

            ChargeUtils.buildAbcPaySubModeName(payActionInfo, paySubMode);
            return payActionInfo.getPayModeDisplayName();
        }

        return payModeName;
    }


    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public List<ChargeFormItem> listChargeFormItemsByIds(String chainId, String clinicId, List<String> chargeFormItemIds) {

        if (org.apache.commons.lang3.StringUtils.isAnyEmpty(chainId, clinicId)) {
            return new ArrayList<>();
        }

        if (CollectionUtils.isEmpty(chargeFormItemIds)) {
            return new ArrayList<>();
        }

        List<ChargeFormItem> chargeFormItems = mChargeFormItemRepository.findByIdInAndChainIdAndIsDeleted(chargeFormItemIds, chainId, 0);

        if (CollectionUtils.isEmpty(chargeFormItems)) {
            return new ArrayList<>();
        }

        mergeRefundChargeFormItems(chargeFormItems);

        return chargeFormItems;
    }

    private void mergeRefundChargeFormItems(List<ChargeFormItem> chargeFormItems) {

        if (CollectionUtils.isEmpty(chargeFormItems)) {
            return;
        }

        List<ChargeFormItem> containRefundPaidItems = chargeFormItems
                .stream()
                .filter(chargeFormItem -> chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED &&
                        (MathUtils.wrapBigDecimalCompare(chargeFormItem.getRefundUnitCount(), BigDecimal.ZERO) > 0 || MathUtils.wrapBigDecimalCompare(chargeFormItem.getRefundDoseCount(), BigDecimal.ZERO) > 0))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(containRefundPaidItems)) {
            return;
        }

        //查询退费的item
        List<ChargeFormItem> refundItems = chargeFormItemMapper.listRefundChargeFormItemsByChargeFormIdAndAssociateFormItemId(containRefundPaidItems);

        if (CollectionUtils.isEmpty(refundItems)) {
            return;
        }


        Map<String, List<ChargeFormItem>> groupByAssociateFormItemIdRefundItems = refundItems.stream()
                .filter(item -> !TextUtils.isEmpty(item.getAssociateFormItemId()))
                .collect(Collectors.groupingBy(ChargeFormItem::getAssociateFormItemId));

        //对部分退费和全退费的item进行更新
        containRefundPaidItems.forEach(chargeFormItem -> {

            List<ChargeFormItem> refundChargeFormItems = groupByAssociateFormItemIdRefundItems.getOrDefault(chargeFormItem.getId(), new ArrayList<>());

            if (CollectionUtils.isEmpty(refundChargeFormItems)) {
                return;
            }

            ChargeFormItem refundChargeFormItem = refundChargeFormItems.stream()
                    .collect(
                            Collectors.toMap(ChargeFormItem::getAssociateFormItemId, Function.identity(), (item1, item2) -> {
                                item1.setUnitCount(MathUtils.wrapBigDecimalAdd(item1.getUnitCount(), item2.getUnitCount()));
                                item1.setPromotionPrice(MathUtils.wrapBigDecimalAdd(item1.getPromotionPrice(), item2.getPromotionPrice()));
                                item1.setAdjustmentPrice(MathUtils.wrapBigDecimalAdd(item1.getAdjustmentPrice(), item2.getAdjustmentPrice()));
                                item1.setFractionPrice(MathUtils.wrapBigDecimalAdd(item1.getFractionPrice(), item2.getFractionPrice()));
                                item1.calculateDiscountedPrice();
                                return item1;
                            })
                    ).get(chargeFormItem.getId());

            BigDecimal totalCount = MathUtils.calculateTotalCount(chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount());
            BigDecimal refundTotalCount = MathUtils.wrapBigDecimalOrZero(refundChargeFormItem.getUnitCount()).multiply(MathUtils.wrapBigDecimalOrZero(refundChargeFormItem.getDoseCount()));

            if (totalCount.compareTo(refundTotalCount) <= 0) {
                chargeFormItem.setStatus(Constants.ChargeFormItemStatus.REFUNDED);
                return;
            }


            chargeFormItem.setUnitCount(MathUtils.wrapBigDecimal(chargeFormItem.getUnitCount(), BigDecimal.ZERO).subtract(MathUtils.wrapBigDecimal(refundChargeFormItem.getUnitCount(), BigDecimal.ZERO)));
            chargeFormItem.setDiscountPrice(MathUtils.wrapBigDecimal(chargeFormItem.getDiscountPrice(), BigDecimal.ZERO).subtract(MathUtils.wrapBigDecimal(refundChargeFormItem.getDiscountPrice(), BigDecimal.ZERO)));
            chargeFormItem.setPromotionPrice(MathUtils.wrapBigDecimal(chargeFormItem.getPromotionPrice(), BigDecimal.ZERO).subtract(MathUtils.wrapBigDecimal(refundChargeFormItem.getPromotionPrice(), BigDecimal.ZERO)));
            chargeFormItem.setAdjustmentPrice(MathUtils.wrapBigDecimal(chargeFormItem.getAdjustmentPrice(), BigDecimal.ZERO).subtract(MathUtils.wrapBigDecimal(refundChargeFormItem.getAdjustmentPrice(), BigDecimal.ZERO)));
            chargeFormItem.setFractionPrice(MathUtils.wrapBigDecimalSubtract(chargeFormItem.getFractionPrice(), refundChargeFormItem.getFractionPrice()));
            chargeFormItem.setTotalPrice(MathUtils.wrapBigDecimalSubtract(chargeFormItem.getTotalPrice(), refundChargeFormItem.getTotalPrice()));
        });

    }

    /**
     * 开放平台查询患者收费单
     */
    public AbcListPage<OpenApiPatientChargeSheetView> queryPatientChargeSheetForOpenApi(OpenApiQueryPatientChargeSheetReq req) {
        LocalDateTime beginTime = cn.abcyun.cis.commons.util.DateUtils.beginOfDay(req.getBeginDate());
        LocalDateTime endTime = cn.abcyun.cis.commons.util.DateUtils.endOfDay(req.getEndDate());
        req.setBeginTime(beginTime);
        req.setEndTime(endTime);

        List<OpenApiPatientChargeSheetView> patientChargeSheets = mChargeMapper.queryPatientChargeSheetForOpenApi(req);
        int total = mChargeMapper.countPatientChargeSheetForOpenApi(req);
        bindEmployeeName(req.getChainId(), patientChargeSheets);
        AbcListPage<OpenApiPatientChargeSheetView> listPage = new AbcListPage<>();
        listPage.setTotal(total);
        listPage.setRows(patientChargeSheets);
        return listPage;
    }

    private void bindEmployeeName(String chainId, List<OpenApiPatientChargeSheetView> chargeSheets) {
        if (CollectionUtils.isEmpty(chargeSheets)) {
            return;
        }

        //查询医生的当前名称
        Set<String> employeeIds = new HashSet<>();
        employeeIds.addAll(cn.abcyun.cis.commons.util.ListUtils.extractUniqueProperty(chargeSheets, OpenApiPatientChargeSheetView::getSellerId));
        employeeIds.addAll(cn.abcyun.cis.commons.util.ListUtils.extractUniqueProperty(chargeSheets, OpenApiPatientChargeSheetView::getChargedBy));

        Map<String, String> employeeIdNameMap = employeeService
                .findEmployeeList(chainId, new ArrayList<>(employeeIds))
                .stream()
                .collect(Collectors.toMap(Employee::getId, Employee::getName));

        chargeSheets.forEach(chargeSheet -> {
            chargeSheet.setSellerName(employeeIdNameMap.get(chargeSheet.getSellerId()));
            chargeSheet.setChargedByName(employeeIdNameMap.get(chargeSheet.getChargedBy()));
        });

        //查询医生的快照名称
        Instant now = Instant.now();

        BatchQueryEmployeeSnapshotReq queryEmployeeSnapshotReq = new BatchQueryEmployeeSnapshotReq().setChainId(chainId);

        chargeSheets.stream()
                .filter(chargeSheet -> org.apache.commons.lang3.StringUtils.isNotBlank(chargeSheet.getDoctorId()))
                .forEach(chargeSheet -> queryEmployeeSnapshotReq.addBusinessTimeEmployeeSnap(chargeSheet.getDoctorId(), Optional.ofNullable(chargeSheet.getDiagnosedDate()).orElse(now)));

        BatchQueryEmployeeSnapshotRsp queryEmployeeSnap = scClinicService.queryEmployeeSnap(queryEmployeeSnapshotReq);

        chargeSheets.stream()
                .filter(chargeSheet -> org.apache.commons.lang3.StringUtils.isNotBlank(chargeSheet.getDoctorId()))
                .forEach(chargeSheet -> chargeSheet.setDoctorName(Optional.ofNullable(queryEmployeeSnap.getEmployeeByBusTime(chargeSheet.getDoctorId(), Optional.ofNullable(chargeSheet.getDiagnosedDate()).orElse(now)))
                        .map(Employee::getName)
                        .orElse(null)
                ));

    }

    public void saveTraceableCode(SaveTraceableCodeReq clientReq) {
        List<SaveTraceableCodeReq.TraceableCodeItem> traceCodeListReq = clientReq.getList();
        if (CollectionUtils.isEmpty(traceCodeListReq)) {
            return;
        }
        ChargeSheet chargeSheet = findByIdAndClinicIdAndIsDeleted(clientReq.getId(), clientReq.getClinicId());
        if (chargeSheet == null) {
            throw new NotFoundException();
        }
        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.CHARGED && chargeSheet.getStatus() != Constants.ChargeSheetStatus.PART_CHARGED) {
            throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_STATUS_ERROR);
        }
        // 查询发药单状态
        List<DispensingSheet> dispensingSheets = dispensingService.getDispensingSheets(clientReq.getId());
        Map<String, DispensingSheet> sheetIdToSheet = dispensingSheets.stream().collect(Collectors.toMap(DispensingSheet::getId, Function.identity(), (a, b) -> a));
//        Map<String, DispensingFormItem> sourceFormItemIdToFormItem = dispensingSheets.stream()
//                .filter(it -> !CollectionUtils.isEmpty(it.getDispensingForms())).flatMap(it -> it.getDispensingForms().stream())
//                .filter(it -> !CollectionUtils.isEmpty(it.getDispensingFormItems())).flatMap(it -> it.getDispensingFormItems().stream())
//                .collect(Collectors.toMap(DispensingFormItem::getSourceFormItemId, Function.identity(), (a, b) -> a));
        Map<String, DispensingFormItem> sourceFormItemIdToFormItem = ChargeUtils.createSourceFormItemIdToDispensingFormItemMap(dispensingSheets);

        Map<String, ChargeFormItem> chargeFormItemIdToFormItem = chargeSheet.getChargeForms()
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(it -> !CollectionUtils.isEmpty(it.getChargeFormItems()))
                .flatMap(it -> it.getChargeFormItems().stream())
                .filter(item -> item.getIsDeleted() == 0)
                .collect(Collectors.toMap(ChargeFormItem::getId, Function.identity(), (a, b) -> a));
        List<ChargeFormItem> updateFormItems = new ArrayList<>();
        List<UpdateTraceableCodeReq.UpdateTraceableCodeItem> updateDispenseTraceableCodeItems = new ArrayList<>();
        boolean needSendDispense = false;
        //通知SCGoods
        GoodsUseTraceCodeReq goodsUseTraceCodeReq = new GoodsUseTraceCodeReq();
        goodsUseTraceCodeReq.setChainId(clientReq.getChainId());
        goodsUseTraceCodeReq.setClinicId(clientReq.getClinicId());
        goodsUseTraceCodeReq.setEmployeeId(clientReq.getEmployeeId());
        goodsUseTraceCodeReq.setPatientOrderId(chargeSheet.getPatientOrderId());
        goodsUseTraceCodeReq.setList(new ArrayList<>());

        for (SaveTraceableCodeReq.TraceableCodeItem traceCodeItem : traceCodeListReq) {
            ChargeFormItem chargeFormItem = chargeFormItemIdToFormItem.get(traceCodeItem.getId());
            if (chargeFormItem == null) {
                throw new NotFoundException();
            }
            if (!CollectionUtils.isEmpty(traceCodeItem.getComposeChildren())) {
                for (SaveTraceableCodeReq.TraceableCodeItem childTraceCodeItem : traceCodeItem.getComposeChildren()) {
                    needSendDispense = needSendDispense || checkFormItemHasDispense(childTraceCodeItem.getId(), sourceFormItemIdToFormItem, sheetIdToSheet);
                    updateFormItemTraceableCode(childTraceCodeItem.getId(),
                            childTraceCodeItem.getTraceableCodeList(),
                            chargeFormItemIdToFormItem, updateFormItems, updateDispenseTraceableCodeItems,goodsUseTraceCodeReq);
                }
            } else {
                needSendDispense = needSendDispense || checkFormItemHasDispense(traceCodeItem.getId(), sourceFormItemIdToFormItem, sheetIdToSheet);
                updateFormItemTraceableCode(traceCodeItem.getId(),
                        traceCodeItem.getTraceableCodeList(),
                        chargeFormItemIdToFormItem, updateFormItems, updateDispenseTraceableCodeItems,goodsUseTraceCodeReq);
            }
        }
        if (updateFormItems.isEmpty()) {
            return;
        }
        List<GoodsUseTraceCodeRsp.GoodsUseTraceCodeItem> rspList = goodsService.traceCodeUse(goodsUseTraceCodeReq);
        if (!org.springframework.util.CollectionUtils.isEmpty(rspList)) {
            for (GoodsUseTraceCodeRsp.GoodsUseTraceCodeItem goodsUseTraceCodeItem : rspList) {

                ChargeFormItem formItem = chargeFormItemIdToFormItem.get(goodsUseTraceCodeItem.getKeyId());
                if(formItem == null) {
                    continue;
                }
                //找到extendData里面的no，修改id
                if (formItem.getAdditional() != null && !org.springframework.util.CollectionUtils.isEmpty(formItem.getAdditional().getTraceableCodeList())) {
                    formItem.getAdditional().getTraceableCodeList().stream()
                            .filter(it -> ChargeUtils.compareStrEqual(it.getNo(), goodsUseTraceCodeItem.getNo()))
                            .findFirst()
                            .ifPresent(it -> it.setId(goodsUseTraceCodeItem.getId()));
                }
                //通知给charge也要把id通知过去
                for (UpdateTraceableCodeReq.UpdateTraceableCodeItem updateTraceableCodeItem : updateDispenseTraceableCodeItems) {
                    if (CollectionUtils.isEmpty(updateTraceableCodeItem.getTraceableCodeList())) {
                        continue;
                    }
                    updateTraceableCodeItem.getTraceableCodeList().stream()
                            .filter(it -> ChargeUtils.compareStrEqual(it.getNo(), goodsUseTraceCodeItem.getNo()))
                            .findFirst().ifPresent(it -> it.setId(goodsUseTraceCodeItem.getId()));
                }
            }
        }

        mChargeFormItemRepository.saveAll(updateFormItems);
        if (needSendDispense) {
            doUpdateDispensingSheetTraceableCode(chargeSheet.getId(), clientReq.getChainId(), clientReq.getClinicId(),
                    clientReq.getEmployeeId(), updateDispenseTraceableCodeItems);
        }
    }

    private boolean checkFormItemHasDispense(String chargeFormItemId,
                                             Map<String, DispensingFormItem> sourceFormItemIdToFormItem,
                                             Map<String, DispensingSheet> sheetIdToSheet
                                            ) {
        DispensingFormItem dispensingFormItem = sourceFormItemIdToFormItem.get(chargeFormItemId);
        if (dispensingFormItem != null) {
            DispensingSheet dispensingSheet = sheetIdToSheet.get(dispensingFormItem.getDispensingSheetId());
            if (dispensingSheet != null) {
                if (dispensingSheet.getStatus() == DispenseConst.Status.DISPENSED) {
                    throw new ChargeServiceException(ChargeServiceError.NEED_REQUIRED_PARAMETER.getCode(), "已发药的收费项不支持采集追溯码");
                }
                return true;
            }
        }
        return false;
    }

    private void updateFormItemTraceableCode(
            String chargeFormItemId,
            List<TraceableCode> traceableCodeList,
            Map<String, ChargeFormItem> chargeFormItemIdToFormItem,
            List<ChargeFormItem> updateFormItems,
            List<UpdateTraceableCodeReq.UpdateTraceableCodeItem> updateTraceableCodeItems,
            GoodsUseTraceCodeReq goodsUseTraceCodeReq) {
        ChargeFormItem chargeFormItem = chargeFormItemIdToFormItem.get(chargeFormItemId);
        if (chargeFormItem == null) {
            throw new NotFoundException();
        }
        /**
         * 无码不要落地，前端会根据goods类型，自动填充无码
         * 后台只需要吧无码过滤掉即可
         * */
        List<TraceableCode> normalCodeList = null;
        if (!CollectionUtils.isEmpty(traceableCodeList)) {
            normalCodeList = traceableCodeList.stream().filter(it -> it.getType() == null || it.getType() != GoodsConst.DrugIdentificationCodeType.NO_CODE).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(normalCodeList)) {
            return;
        }
        // 中西成药和医疗器械支持
        if (!(chargeFormItem.getProductType() == GoodsConst.GoodsType.MEDICINE)
                && !(chargeFormItem.getProductType() == GoodsConst.GoodsType.MATERIAL
                && chargeFormItem.getProductSubType() == GoodsConst.GoodsMaterialSubType.MEDICINE_MATERIAL)) {
            throw new ChargeServiceException(ChargeServiceError.NEED_REQUIRED_PARAMETER.getCode(), "非药品/器械不支持采集追溯码");
        }
        if (chargeFormItem.getAdditional() == null) {
            chargeFormItem.setAdditional(new ChargeFormItemAdditional());
        }
        if(goodsUseTraceCodeReq != null){
            for (TraceableCode traceableCode : traceableCodeList) {
                GoodsUseTraceCodeReq.GoodsUseTraceCodeItem goodsUseTraceCodeItem = new GoodsUseTraceCodeReq.GoodsUseTraceCodeItem();
                goodsUseTraceCodeItem.setNo(traceableCode.getNo());
                goodsUseTraceCodeItem.setGoodsId( chargeFormItem.getProductId());
                goodsUseTraceCodeItem.setPharmacyType(chargeFormItem.getPharmacyType());
                goodsUseTraceCodeItem.setPharmacyNo(chargeFormItem.getPharmacyNo());
                goodsUseTraceCodeItem.setChangePackageCount(traceableCode.getHisPackageCount());
                goodsUseTraceCodeItem.setChangePieceCount(traceableCode.getHisPieceCount());
                goodsUseTraceCodeItem.setDismountingSn(traceableCode.getDismountingSn());
                goodsUseTraceCodeItem.setOptType(0);
                goodsUseTraceCodeItem.setOrderId(chargeFormItem.getChargeSheetId());
                goodsUseTraceCodeItem.setOrderDetailId(chargeFormItem.getId());
                goodsUseTraceCodeItem.setKeyId(chargeFormItem.getId());
                goodsUseTraceCodeReq.getList().add(goodsUseTraceCodeItem);
            }
        }
        chargeFormItem.getAdditional().setTraceableCodeList(normalCodeList);
        updateFormItems.add(chargeFormItem);

        UpdateTraceableCodeReq.UpdateTraceableCodeItem updateTraceableCodeItem = new UpdateTraceableCodeReq.UpdateTraceableCodeItem();
        updateTraceableCodeItem.setChargeFormItemId(chargeFormItem.getId());
        updateTraceableCodeItem.setTraceableCodeList(normalCodeList);
        updateTraceableCodeItems.add(updateTraceableCodeItem);
    }

    private void doUpdateDispensingSheetTraceableCode(String id, String chainId, String clinicId, String employeeId,
                                                      List<UpdateTraceableCodeReq.UpdateTraceableCodeItem> traceableCodeItemList) {
        if (CollectionUtils.isEmpty(traceableCodeItemList)) {
            return;
        }
        UpdateTraceableCodeReq req = new UpdateTraceableCodeReq();
        req.setChargeSheetId(id);
        req.setChainId(chainId);
        req.setClinicId(clinicId);
        req.setEmployeeId(employeeId);
        req.setList(traceableCodeItemList);
        dispensingService.updateDispensingSheetTraceableCode(req);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateChargeSheetTraceableCode(UpdateTraceableCodeReq req) {
        if (CollectionUtils.isEmpty(req.getList())) {
            return;
        }

        ChargeSheet chargeSheet = findByIdAndClinicIdAndIsDeleted(req.getChargeSheetId(), req.getClinicId());
        if (chargeSheet == null) {
            return;
        }
        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.CHARGED && chargeSheet.getStatus() != Constants.ChargeSheetStatus.PART_CHARGED) {
            return;
        }
        Map<String, ChargeFormItem> chargeFormItemIdToFormItem = chargeSheet.getChargeForms()
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(it -> !CollectionUtils.isEmpty(it.getChargeFormItems()))
                .flatMap(it -> it.getChargeFormItems().stream())
                .filter(item -> item.getIsDeleted() == 0)
                .collect(Collectors.toMap(ChargeFormItem::getId, Function.identity(), (a, b) -> a));
        List<ChargeFormItem> updateFormItems = new ArrayList<>();
        List<UpdateTraceableCodeReq.UpdateTraceableCodeItem> updateTraceableCodeItems = new ArrayList<>();
        for (UpdateTraceableCodeReq.UpdateTraceableCodeItem traceCodeItem : req.getList()) {
            updateFormItemTraceableCode(traceCodeItem.getChargeFormItemId(),
                    traceCodeItem.getTraceableCodeList(),
                    chargeFormItemIdToFormItem, updateFormItems, updateTraceableCodeItems,null);
        }
        if (updateFormItems.isEmpty()) {
            return;
        }
        mChargeFormItemRepository.saveAll(updateFormItems);
    }
}
