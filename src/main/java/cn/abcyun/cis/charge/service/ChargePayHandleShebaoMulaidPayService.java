package cn.abcyun.cis.charge.service;

import cn.abcyun.bis.rpc.sdk.cis.model.pay.shebao.PayForShebaoReq;
import cn.abcyun.cis.charge.base.PayModeInfo;
import cn.abcyun.cis.charge.base.InnerPayModes;
import cn.abcyun.cis.charge.rpc.client.ShebaoClient;
import cn.abcyun.cis.charge.service.rpc.CisScClinicService;
import cn.abcyun.cis.charge.service.rpc.CisShebaoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ChargePayHandleShebaoMulaidPayService extends ChargeShebaoPayHandleAbstractService {

    @Autowired
    public ChargePayHandleShebaoMulaidPayService(ChargeAbnormalTransactionService chargeAbnormalTransactionService,
                                                 ChargePayTransactionService chargePayTransactionService,
                                                 ChargeSheetService chargeSheetService,
                                                 EmployeeService employeeService,
                                                 PatientService patientService,
                                                 ShebaoClient shebaoClient,
                                                 CisScClinicService scClinicService,
                                                 CisShebaoService cisShebaoService) {
        super(chargeSheetService, chargePayTransactionService, chargeAbnormalTransactionService, employeeService, patientService, shebaoClient, scClinicService, cisShebaoService);
    }

    @Override
    public PayModeInfo getPayModeInfo() {
        return InnerPayModes.getPayModeByName(InnerPayModes.InnerPayModeName.SHEBAO_MULAID_PAY);
    }

    @Override
    public int getShebaoPayMethod(int paySubMode) {
        return PayForShebaoReq.PayMethod.MULAIDPAY;
    }

}
