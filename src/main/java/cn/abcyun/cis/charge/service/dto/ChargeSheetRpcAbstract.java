package cn.abcyun.cis.charge.service.dto;

import cn.abcyun.cis.commons.model.CisPatientInfo;
import lombok.Data;

import java.time.Instant;

@Data
public class ChargeSheetRpcAbstract {
    private String id;
    private String patientOrderId;

    /**
     * 这个创建时间是收费单的创建时间，但是不是收费台看到的时间，比如：挂号挂了一个周之后的号，会生成一个待诊的门诊单，同时会一个收费单，创建时间是当前时间，此时收费台看不到这个收费单，
     * 一周后接诊完成，此时收费台才看得到门诊对应的收费单，显示的是一周后的时间，但是这个收费单是一周前生成的，创建时间也是一周前
     */
    private Instant created;
    private String chainId;
    private String clinicId;
    private String doctorName;

    private int isDraft;
    private int status;
    /**
     * 欠费状态
     */
    private int owedStatus;
    private int executeStatus;
    private int outpatientStatus;
    private CisPatientInfo patient;
    private String statusName;
    /**
     * 收费单类型
     */
    private int type;

    private int isClosed;

}
