package cn.abcyun.cis.charge.service.dto;


import cn.abcyun.cis.commons.rpc.outpatient.PrescriptionForm;
import cn.abcyun.cis.commons.rpc.outpatient.PrescriptionFormItem;
import cn.abcyun.cis.commons.util.ItemParentIdSourceCell;
import cn.abcyun.cis.commons.util.TextUtils;

public class OutpatientPrescriptionFormSourceDto implements ItemParentIdSourceCell {

    private final PrescriptionForm prescriptionForm;

    public OutpatientPrescriptionFormSourceDto(PrescriptionForm prescriptionForm) {
        this.prescriptionForm = prescriptionForm;
    }

    @Override
    public String getParentSourceItemId() {
        return prescriptionForm.getPrescriptionFormDelivery().getPrimaryPrescriptionFormId();
    }

    @Override
    public String getSourceItemId() {
        return prescriptionForm.getId();
    }

    @Override
    public boolean isChildItem() {
        return prescriptionForm.getType() == cn.abcyun.cis.commons.rpc.outpatient.PrescriptionForm.TYPE_CHINESE
                && prescriptionForm.getPharmacyType() == cn.abcyun.cis.commons.rpc.outpatient.PrescriptionForm.PharmacyType.AIR && prescriptionForm.getPrescriptionFormDelivery() != null;
    }
}
