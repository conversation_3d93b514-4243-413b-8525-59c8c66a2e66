package cn.abcyun.cis.charge.service.dto;

import cn.abcyun.bis.rpc.sdk.cis.model.promotion.Promotion;
import cn.abcyun.cis.charge.config.BigDecimalExcludeZeroFilter;
import cn.abcyun.cis.charge.processor.discount.ItemDeductedDetail;
import cn.abcyun.cis.charge.processor.discount.PromotionSimple;
import cn.abcyun.cis.charge.util.MathUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Data
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ChargeDiscountInfo {

    /**
     * 标记为过了社保支付的项目
     */
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private int isMarkedByHealthCardPay;

    /**
     * 议价，可能是加价，也可能是减价
     * 在加价时ChargeTransactionRecord表的discountPrice不包含adjustmentPrice，
     * 在减价时ChargeTransactionRecord表的discountPrice包含adjustmentPrice，
     */
    @JsonInclude(value = JsonInclude.Include.CUSTOM, valueFilter = BigDecimalExcludeZeroFilter.class)
    private BigDecimal adjustmentFee;

    /**
     * 单项金额议价值 = 折扣前金额 - sourceTotalPrice
     * 折扣前金额 = MathUtils.wrapBigDecimalAdd(MathUtils.calculateTotalPrice(unitPrice, unitCount, doseCount, 2), fractionPrice)
     * sourceTotalPrice = sourceUnitPrice * unitCount * doseCount
     */
    @JsonInclude(value = JsonInclude.Include.CUSTOM, valueFilter = BigDecimalExcludeZeroFilter.class)
    private BigDecimal unitAdjustmentFee;

    /**
     * 被抵扣的单项议价值
     */
    @JsonInclude(value = JsonInclude.Include.CUSTOM, valueFilter = BigDecimalExcludeZeroFilter.class)
    private BigDecimal deductedUnitAdjustmentFee;


    /**
     * 被核销抵扣的单项议价值
     */
    @JsonInclude(value = JsonInclude.Include.CUSTOM, valueFilter = BigDecimalExcludeZeroFilter.class)
    private BigDecimal verifyUnitAdjustmentFee;

    /**
     * 排除掉抵扣的单项议价值
     */
    @JsonInclude(value = JsonInclude.Include.CUSTOM, valueFilter = BigDecimalExcludeZeroFilter.class)
    private BigDecimal unitAdjustmentFeeIgnoreDeduct;

    /**
     * 服务抵扣list
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<DeductDiscountInfo> deductDiscountInfos;

    /**
     * 折扣list
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<PromotionInfo> discountPromotionInfos;

    /**
     * 满减满赠list
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<PromotionInfo> giftRulePromotionInfos;

    /**
     * 优惠券list
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<CouponInfo> couponInfos;

    /**
     * 核销抵扣list
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<VerifyDeductInfo> verifyDeductInfos;

    /**
     * 积分抵扣金额
     */
    @JsonInclude(value = JsonInclude.Include.CUSTOM, valueFilter = BigDecimalExcludeZeroFilter.class)
    private BigDecimal patientPointPromotionFee = BigDecimal.ZERO;


    /**
     * 限价折扣金额
     */
    @JsonInclude(value = JsonInclude.Include.CUSTOM, valueFilter = BigDecimalExcludeZeroFilter.class)
    private BigDecimal limitFee = BigDecimal.ZERO;

    /**
     * 挂网价限价折扣金额
     */
    @JsonInclude(value = JsonInclude.Include.CUSTOM, valueFilter = BigDecimalExcludeZeroFilter.class)
    private BigDecimal listingDiscountFee = BigDecimal.ZERO;

    public List<DeductDiscountInfo> getDeductDiscountInfos() {
        if (deductDiscountInfos == null) {
            deductDiscountInfos = new ArrayList<>();
        }
        return deductDiscountInfos;
    }

    @JsonIgnore
    public BigDecimal getDeductTotalCount() {
        return getDeductDiscountInfos()
                .stream()
                .map(DeductDiscountInfo::getDeductedCount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @JsonIgnore
    public BigDecimal getVerifyTotalCount() {
        return getVerifyDeductInfos()
                .stream()
                .map(VerifyDeductInfo::getDeductedCount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public List<PromotionInfo> getDiscountPromotionInfos() {
        if (discountPromotionInfos == null) {
            discountPromotionInfos = new ArrayList<>();
        }
        return discountPromotionInfos;
    }

    public List<PromotionInfo> getGiftRulePromotionInfos() {
        if (giftRulePromotionInfos == null) {
            giftRulePromotionInfos = new ArrayList<>();
        }
        return giftRulePromotionInfos;
    }

    public List<CouponInfo> getCouponInfos() {
        if (couponInfos == null) {
            couponInfos = new ArrayList<>();
        }
        return couponInfos;
    }

    public List<VerifyDeductInfo> getVerifyDeductInfos() {
        if (verifyDeductInfos == null) {
            verifyDeductInfos = new ArrayList<>();
        }
        return verifyDeductInfos;
    }

    /**
     * 计算整单优惠的值
     *
     * @return
     */
    public BigDecimal calculatePackagePromotionPrice() {
        return getGiftRulePromotionInfos()
                .stream()
                .map(ChargeDiscountInfo.PromotionInfo::getDiscountPrice)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .add(getCouponInfos()
                        .stream()
                        .map(ChargeDiscountInfo.CouponInfo::getDiscountPrice)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                )
                .add(MathUtils.wrapBigDecimalOrZero(getPatientPointPromotionFee()));
    }

    //计算单项优惠的值
    public BigDecimal calculateSinglePromotionPrice() {
        return getDeductPromotionPrice().add(
                getDiscountPromotionInfos()
                        .stream()
                        .map(ChargeDiscountInfo.PromotionInfo::getDiscountPrice)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
        );
    }

    /**
     * 所有优惠的和
     *
     * @return
     */
    public BigDecimal calculateAllPromotionPrice() {
        return getDiscountPromotionInfos()
                .stream()
                .map(ChargeDiscountInfo.PromotionInfo::getDiscountPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .add(
                        getDeductPromotionPrice()
                )
                .add(
                        getGiftRulePromotionInfos()
                                .stream()
                                .map(ChargeDiscountInfo.PromotionInfo::getDiscountPrice)
                                .filter(Objects::nonNull)
                                .reduce(BigDecimal.ZERO, BigDecimal::add)
                )
                .add(
                        getCouponInfos()
                                .stream()
                                .map(ChargeDiscountInfo.CouponInfo::getDiscountPrice)
                                .filter(Objects::nonNull)
                                .reduce(BigDecimal.ZERO, BigDecimal::add)
                )
                .add(MathUtils.wrapBigDecimalOrZero(getPatientPointPromotionFee()));
    }

    public ChargeDiscountInfo addVerifyDeductInfos(List<VerifyDeductInfo> verifyDeductInfos) {
        if (CollectionUtils.isNotEmpty(verifyDeductInfos)) {
            getVerifyDeductInfos().addAll(verifyDeductInfos);
        }
        return this;
    }

    public ChargeDiscountInfo addDeductDiscountInfos(List<DeductDiscountInfo> deductDiscountInfos) {
        if (CollectionUtils.isNotEmpty(deductDiscountInfos)) {
            getDeductDiscountInfos().addAll(deductDiscountInfos);
        }
        return this;
    }

    public ChargeDiscountInfo addDiscountPromotionInfos(List<PromotionInfo> discountPromotionInfos) {
        if (CollectionUtils.isNotEmpty(discountPromotionInfos)) {
            getDiscountPromotionInfos().addAll(discountPromotionInfos);
        }
        return this;
    }

    public ChargeDiscountInfo addGiftRulePromotionInfos(List<PromotionInfo> giftRulePromotionInfos) {
        if (CollectionUtils.isNotEmpty(giftRulePromotionInfos)) {
            getGiftRulePromotionInfos().addAll(giftRulePromotionInfos);
        }
        return this;
    }

    public ChargeDiscountInfo addCouponInfos(List<CouponInfo> couponInfos) {
        if (CollectionUtils.isNotEmpty(couponInfos)) {
            getCouponInfos().addAll(couponInfos);
        }
        return this;
    }

    @JsonIgnore
    public boolean isDiscountPriceEmpty() {

        //如果是议价加价，则不在折扣里面，如果是议价减价，则在折扣里面
        BigDecimal discountAdjustmentFee = MathUtils.wrapBigDecimalOrZero(MathUtils.wrapBigDecimalCompare(adjustmentFee, BigDecimal.ZERO) > 0 ? BigDecimal.ZERO : adjustmentFee);

        BigDecimal deductDiscountTotalFee = getDeductDiscountInfos()
                .stream()
                .map(DeductDiscountInfo::getDiscountPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal discountPromotionTotalFee = getDiscountPromotionInfos()
                .stream()
                .map(PromotionInfo::getDiscountPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal giftRulePromotionTotalFee = getGiftRulePromotionInfos()
                .stream()
                .map(PromotionInfo::getDiscountPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal couponTotalFee = getCouponInfos()
                .stream()
                .map(CouponInfo::getDiscountPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return MathUtils.compareZero(adjustmentFee) == 0
                && MathUtils.compareZero(discountPromotionTotalFee) == 0
                && MathUtils.compareZero(giftRulePromotionTotalFee) == 0
                && MathUtils.compareZero(unitAdjustmentFee) == 0
                && MathUtils.compareZero(couponTotalFee) == 0
                && MathUtils.compareZero(patientPointPromotionFee) == 0
                && MathUtils.compareZero(listingDiscountFee) == 0;
    }

    @Data
    @Accessors(chain = true)
    @EqualsAndHashCode(callSuper = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DeductDiscountInfo extends PromotionInfo {
        /**
         * 抵扣次数  正值
         */
        private BigDecimal deductedCount;

        /**
         * 抵扣的单价
         */
        private BigDecimal unitPrice;


        /**
         * 赠送表的id
         */
        private String presentId;

    }

    @Data
    @Accessors(chain = true)
    @EqualsAndHashCode(callSuper = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class VerifyDeductInfo extends PromotionInfo {
        /**
         * 抵扣次数  正值
         */
        private BigDecimal deductedCount;

        /**
         * 抵扣的单价
         */
        private BigDecimal unitPrice;


    }

    @Data
    @Accessors(chain = true)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PromotionInfo {

        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private String parentId;

        /**
         * 折扣id，（也可能是卡项id，因为卡项也有折扣）
         */
        private String id;

        /**
         * 折扣名称
         */
        private String name;

        private BigDecimal discountPrice;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Integer parentType;

        /**
         * 折扣类型 {@link PromotionSimple.Type}
         */
        private int type;

        /**
         * {@link PromotionSimple.SubType}
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Integer subType;

        /**
         * 0折扣 1特价 2满赠
         * {@link Promotion.PromotionDetail.DiscountWay}
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Integer discountWay;

        /**
         * 优惠方式
         * {@link Promotion.PromotionDetail.RuleType}
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Integer ruleType;

        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private List<ItemDeductedDetail.BatchInfoDeductedDetail> batchInfos;

        /**
         * 被消耗掉的折扣金额
         */
        @JsonIgnore
        private BigDecimal expendDiscountPrice;

        public BigDecimal getDiscountPrice() {
            return MathUtils.wrapBigDecimalOrZero(discountPrice);
        }
    }

    @Data
    @Accessors(chain = true)
    @EqualsAndHashCode(callSuper = true)
    public static class CouponInfo extends PromotionInfo {
        //已使用的券id列表
        private List<String> couponIds;
    }

    @JsonIgnore
    public BigDecimal getTotalDiscountPrice(boolean containUnitAdjustmentFee) {

        //如果是议价加价，则不在折扣里面，如果是议价减价，则在折扣里面
        BigDecimal discountAdjustmentFee = MathUtils.wrapBigDecimalOrZero(MathUtils.wrapBigDecimalCompare(adjustmentFee, BigDecimal.ZERO) > 0 ? BigDecimal.ZERO : adjustmentFee);

        BigDecimal deductDiscountTotalFee = getDeductDiscountInfos()
                .stream()
                .map(DeductDiscountInfo::getDiscountPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal discountPromotionTotalFee = getDiscountPromotionInfos()
                .stream()
                .map(PromotionInfo::getDiscountPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal giftRulePromotionTotalFee = getGiftRulePromotionInfos()
                .stream()
                .map(PromotionInfo::getDiscountPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal couponTotalFee = getCouponInfos()
                .stream()
                .map(CouponInfo::getDiscountPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal patientPointPromotionFee = MathUtils.wrapBigDecimalOrZero(this.patientPointPromotionFee);

        return MathUtils.wrapBigDecimalAdd(discountAdjustmentFee,
                deductDiscountTotalFee,
                discountPromotionTotalFee,
                giftRulePromotionTotalFee,
                couponTotalFee,
                patientPointPromotionFee,
                containUnitAdjustmentFee ? unitAdjustmentFeeIgnoreDeduct : BigDecimal.ZERO);
    }

    public BigDecimal calculateAllPrice() {
        BigDecimal deductDiscountTotalFee = getDeductDiscountInfos()
                .stream()
                .map(DeductDiscountInfo::getDiscountPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal discountPromotionTotalFee = getDiscountPromotionInfos()
                .stream()
                .map(PromotionInfo::getDiscountPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal giftRulePromotionTotalFee = getGiftRulePromotionInfos()
                .stream()
                .map(PromotionInfo::getDiscountPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal couponTotalFee = getCouponInfos()
                .stream()
                .map(CouponInfo::getDiscountPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal patientPointPromotionFee = MathUtils.wrapBigDecimalOrZero(this.patientPointPromotionFee);


        return MathUtils.wrapBigDecimalAdd(adjustmentFee, unitAdjustmentFee, deductDiscountTotalFee, discountPromotionTotalFee, giftRulePromotionTotalFee, couponTotalFee, patientPointPromotionFee, listingDiscountFee);
    }


    public BigDecimal calculatePromotionPrice() {
        return calculateSinglePromotionPrice().add(calculatePackagePromotionPrice());
    }

    @JsonIgnore
    public BigDecimal getExcludeDeductPromotionPrice() {

        BigDecimal discountPromotionTotalFee = getDiscountPromotionInfos()
                .stream()
                .map(PromotionInfo::getDiscountPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal giftRulePromotionTotalFee = getGiftRulePromotionInfos()
                .stream()
                .map(PromotionInfo::getDiscountPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal couponTotalFee = getCouponInfos()
                .stream()
                .map(CouponInfo::getDiscountPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return discountPromotionTotalFee.add(giftRulePromotionTotalFee).add(couponTotalFee);
    }

    @JsonIgnore
    public BigDecimal getDeductPromotionPrice() {
        return getDeductDiscountInfos()
                .stream()
                .map(DeductDiscountInfo::getDiscountPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @JsonIgnore
    public BigDecimal getVerifyDeductPrice() {
        return getVerifyDeductInfos()
                .stream()
                .map(VerifyDeductInfo::getDiscountPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @JsonIgnore
    public BigDecimal getAllDeductPrice() {

      return MathUtils.wrapBigDecimalAdd(getDeductPromotionPrice(), getVerifyDeductPrice());
    }

    @JsonIgnore
    public BigDecimal getPointDeductPromotionPrice() {
        return getDiscountPromotionInfos()
                .stream()
                .filter(promotionInfo -> promotionInfo.getType() == PromotionSimple.Type.PATIENT_POINT_DEDUCT)
                .map(PromotionInfo::getDiscountPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public void clearPromotionAndAdjustmentFee() {

        this.setAdjustmentFee(null);
//        this.setUnitAdjustmentFee(null);
        this.setDeductedUnitAdjustmentFee(null);
        this.setUnitAdjustmentFeeIgnoreDeduct(null);
        this.setDeductDiscountInfos(null);
        this.setDiscountPromotionInfos(null);
        this.setGiftRulePromotionInfos(null);
        this.setCouponInfos(null);
        this.setPatientPointPromotionFee(null);

    }

    /**
     * 清空处理积分抵扣的其他营销金额
     */
    public void clearPromotionWithOutPoint() {
        deductedUnitAdjustmentFee = null;
        unitAdjustmentFeeIgnoreDeduct = unitAdjustmentFee;
        deductDiscountInfos = null;
        discountPromotionInfos = null;
        giftRulePromotionInfos = null;
        couponInfos = null;
        patientPointPromotionFee = null;
    }
}
