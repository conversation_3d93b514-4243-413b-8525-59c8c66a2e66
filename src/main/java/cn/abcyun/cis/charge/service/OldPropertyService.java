package cn.abcyun.cis.charge.service;

import cn.abcyun.cis.charge.rpc.client.OldPropertyClient;
import cn.abcyun.cis.charge.rpc.model.DeliveryScopeRsp;
import cn.abcyun.cis.charge.rpc.model.RefundCouponConfigRsp;
import cn.abcyun.cis.commons.CisServiceResponseBody;
import cn.abcyun.cis.commons.exception.FeignRuntimeException;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.cis.core.util.JsonUtils;
import cn.abcyun.common.log.marker.AbcLogMarker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class OldPropertyService {

    @Autowired
    private OldPropertyClient oldPropertyClient;


    public List<DeliveryScopeRsp.DistributionScopeView> getDistributionScopeListByClinicId(String clinicId){
        if (TextUtils.isEmpty(clinicId)) {
            return new ArrayList<>();
        }

        List<DeliveryScopeRsp.DistributionScopeView> distributionScopeViews = new ArrayList<>();

        DeliveryScopeRsp rsp = findDeliveryScopePropertyByClinicId(clinicId);

        if (rsp != null && rsp.getDispensing() != null && !CollectionUtils.isEmpty(rsp.getDispensing().getDistributionScope())) {
            distributionScopeViews = rsp.getDispensing().getDistributionScope();
        }
        return distributionScopeViews;

    }

    public int getIsToHomeByClinicId(String clinicId){
        int isToHome = 0;
        if (TextUtils.isEmpty(clinicId)) {
            return isToHome;
        }
        DeliveryScopeRsp rsp = findDeliveryScopePropertyByClinicId(clinicId);
        if (rsp != null && rsp.getDispensing() != null) {
            isToHome = rsp.getDispensing().getToHome();
        }
        return isToHome;
    }

    public boolean isNeedPatientConfirmSwitch(String clinicId){

        if (StringUtils.isEmpty(clinicId)) {
            return false;
        }

        DeliveryScopeRsp rsp = findDeliveryScopePropertyByClinicId(clinicId);

        if (rsp != null && rsp.getDispensing() != null) {
            return rsp.getDispensing().getToHome() == 1 || rsp.getDispensing().getIsDecoction() == 1;
        }
        return false;
    }

    public boolean isOpenDecoctionSwitch(String clinicId){
        if (StringUtils.isEmpty(clinicId)) {
            return false;
        }

        DeliveryScopeRsp rsp = findDeliveryScopePropertyByClinicId(clinicId);

        if (rsp != null && rsp.getDispensing() != null) {
            return rsp.getDispensing().getIsDecoction() == 1;
        }
        return false;
    }

    private DeliveryScopeRsp findDeliveryScopePropertyByClinicId(String clinicId) {
        if (TextUtils.isEmpty(clinicId)) {
            return null;
        }

        DeliveryScopeRsp deliveryScopeRsp = null;
        try {
            long startRequestTime = System.currentTimeMillis();
            CisServiceResponseBody<DeliveryScopeRsp> rspBody = oldPropertyClient.findDeliveryScopePropertyByClinicId(clinicId, OldPropertyClient.TREAT_ONLINE_DISPENSING_KEY);
            if (rspBody != null && rspBody.getData() != null) {
                deliveryScopeRsp = rspBody.getData();
                log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX,"findDeliveryScopePropertyByChainId rpc cost time:{}ms, result:{}", (System.currentTimeMillis() - startRequestTime), JsonUtils.dump(rspBody.getData()));
            }
        } catch (FeignRuntimeException e) {
            log.error("findDeliveryScopePropertyByChainId feign error", e);
            throw e;
        } catch (Exception e) {
            log.error("findDeliveryScopePropertyByChainId error", e);
            throw e;
        }
        return deliveryScopeRsp;
    }

    public RefundCouponConfigRsp searchRefundCouponConfig(String chainId) {
        if (TextUtils.isEmpty(chainId)) {
            return null;
        }

        RefundCouponConfigRsp refundCouponConfigRsp = null;
        try {
            long startRequestTime = System.currentTimeMillis();
            CisServiceResponseBody<RefundCouponConfigRsp> rspBody = oldPropertyClient.searchRefundCouponConfig(chainId, OldPropertyClient.CHAINBASIC_PROMOTION_REFUNDCOUPON_KEY);
            if (rspBody != null && rspBody.getData() != null) {
                refundCouponConfigRsp = rspBody.getData();
                log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX,"searchRefundCouponConfig rpc cost time:{}ms, result:{}", (System.currentTimeMillis() - startRequestTime), JsonUtils.dump(rspBody.getData()));
            }
        } catch (FeignRuntimeException e) {
            log.error("searchRefundCouponConfig feign error", e);
            throw e;
        } catch (Exception e) {
            log.error("searchRefundCouponConfig error", e);
            throw e;
        }
        return refundCouponConfigRsp;
    }

}
