package cn.abcyun.cis.charge.service;

import cn.abcyun.bis.rpc.sdk.cis.model.wechatpay.CloseOrderReq;
import cn.abcyun.bis.rpc.sdk.cis.model.wechatpay.WeChatMobileShebaoRefundReq;
import cn.abcyun.bis.rpc.sdk.cis.model.wechatpay.WeChatMobileShebaoRefundRsp;
import cn.abcyun.cis.charge.base.ChargeServiceError;
import cn.abcyun.cis.charge.base.ChargeServiceException;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.base.InnerPayModes;
import cn.abcyun.cis.charge.model.ChargePayTransaction;
import cn.abcyun.cis.charge.model.ChargeTransaction;
import cn.abcyun.cis.charge.model.ThirdPartyPayInfo;
import cn.abcyun.cis.charge.processor.*;
import cn.abcyun.cis.charge.service.rpc.CisWechatPayService;
import cn.abcyun.cis.charge.util.AbcIdUtils;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.cis.commons.exception.ServiceInternalException;
import cn.abcyun.cis.commons.rpc.pay.PayStatus;
import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.common.log.marker.AbcLogMarker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 医保移动支付现金部分，只有退款
 */
@Service
@Slf4j
public class ChargePayHandleWechatMobileShebaoCashPayService extends ChargePayHandleAbstractService {

    private final CisWechatPayService wechatPayService;

    @Autowired
    public ChargePayHandleWechatMobileShebaoCashPayService(ChargeAbnormalTransactionService chargeAbnormalTransactionService,
                                                           ChargePayTransactionService chargePayTransactionService,
                                                           ChargeSheetService chargeSheetService,
                                                           CisWechatPayService wechatPayService) {
        super(chargeSheetService, chargePayTransactionService, chargeAbnormalTransactionService);
        this.wechatPayService = wechatPayService;
    }

    @Override
    public String getPayModeKey() {
        return InnerPayModes.getPayModeByName(InnerPayModes.InnerPayModeName.WECHAT_MOBILE_SHEBAO_CASH).getPayModeKey();
    }

    @Override
    public ChargePayResult pay(ChargePayInfo payInfo) {
        log.error("医保移动支付现金部分，不能单独发起支付");
        throw new ServiceInternalException(500, "医保移动支付现金部分不能单独发起支付");
    }

    @Override
    public ChargePayRefundResult refund(ChargePayRefundInfo refundInfo) {
        return refundForWxMobile(refundInfo, false);
    }

    @Override
    public ChargePayRefundResult paidback(ChargePayRefundInfo refundInfo) {
        return refundForWxMobile(refundInfo, true);
    }

    @Override
    public CloseOrderRsp closeOrder(ChargePayTransaction chargePayTransaction, String operatorId) {
        if (StringUtils.isEmpty(chargePayTransaction.getPayTransactionId())) {
            return CloseOrderRsp.fail("chargePayTransaction is null");
        }

        CloseOrderReq closeOrderReq = new CloseOrderReq();
        closeOrderReq.setChainId(chargePayTransaction.getChainId())
                .setClinicId(chargePayTransaction.getClinicId())
                .setOutTradeNo(chargePayTransaction.getPayTransactionId());

        wechatPayService.closeMobileShebaoPayOrder(closeOrderReq);
        return CloseOrderRsp.success();
    }

    public ChargePayRefundResult refundForWxMobile(ChargePayRefundInfo refundInfo, boolean isPaidback) {
        if (refundInfo == null) {
            return null;
        }

        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "refundForWxMobile:{}", JsonUtils.dump(refundInfo));

        //校验医保是否已退，如果医保没退，不允许退现金部分

        ChargeTransaction payChargeTransaction = null;
        ChargeTransaction wxMobileShebaoInsuranceTransaction = null;
        if (refundInfo.getChargeTransactions() != null) {

            //从支付记录中找到医保移动支付医保部分
            wxMobileShebaoInsuranceTransaction = refundInfo.getChargeTransactions()
                    .stream()
                    .sorted((a, b) -> ObjectUtils.compare(a.getCreated(), b.getCreated()))
                    .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.HEALTH_CARD
                            && chargeTransaction.getPaySubMode() == Constants.ChargePaySubMode.WECHAT_MOBILE_SHEBAO_INSURANCE
                            && !TextUtils.isEmpty(chargeTransaction.getThirdPartyPayTransactionId())
                            && chargeTransaction.getIsPaidback() == 0
                            && chargeTransaction.getIsDeleted() == 0
                    )
                    .findFirst().orElse(null);

            payChargeTransaction = refundInfo.getChargeTransactions()
                    .stream()
                    .sorted((a, b) -> ObjectUtils.compare(a.getCreated(), b.getCreated()))
                    .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.WECHAT_PAY
                            && chargeTransaction.getPaySubMode() == Constants.ChargePaySubMode.WECHAT_MOBILE_SHEBAO_CASH)
                    .filter(chargeTransaction -> !TextUtils.isEmpty(chargeTransaction.getThirdPartyPayTransactionId()))
                    .filter(chargeTransaction -> MathUtils.wrapBigDecimalCompare(MathUtils.wrapBigDecimalAdd(chargeTransaction.getAmount(), chargeTransaction.getRefundedAmount()),
                            MathUtils.wrapBigDecimalOrZero(refundInfo.getRefundFee()).negate()) >= 0)
                    .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                    .filter(chargeTransaction -> chargeTransaction.getIsDeleted() == 0)
                    .filter(chargeTransaction -> TextUtils.isEmpty(chargeTransaction.getAssociateTransactionId()))
                    .findFirst().orElse(null);
        }

        if (payChargeTransaction == null) {
            throw new ChargeServiceException(ChargeServiceError.REFUND_RECEIVED_TRANSACTION_NOT_EXISTED);
        }

        if (Objects.isNull(wxMobileShebaoInsuranceTransaction)) {
            log.info("wxMobileShebaoInsuranceTransaction is null");
            throw new ChargeServiceException(ChargeServiceError.REFUND_RECEIVED_TRANSACTION_NOT_EXISTED);
        }

        //判断医保部分是否已退
        BigDecimal insuranceCanRefundAmount = MathUtils.wrapBigDecimalAdd(wxMobileShebaoInsuranceTransaction.getAmount(), wxMobileShebaoInsuranceTransaction.getRefundedAmount());
        if (insuranceCanRefundAmount.compareTo(BigDecimal.ZERO) != 0 || StringUtils.isEmpty(wxMobileShebaoInsuranceTransaction.getAssociateTransactionId())) {
            log.info("医保部分还未退，不能退现金部分");
            throw new ChargeServiceException(ChargeServiceError.WECHAT_MOBILE_CASH_REFUND_NEED_AFTER_INSURANCE);
        }
        String transactionId = AbcIdUtils.getUUID();

        WeChatMobileShebaoRefundReq weChatMobileShebaoRefundReq = new WeChatMobileShebaoRefundReq();
        weChatMobileShebaoRefundReq.setClinicId(refundInfo.getClinicId());
        weChatMobileShebaoRefundReq.setChainId(refundInfo.getChainId());
        weChatMobileShebaoRefundReq.setPatientId(refundInfo.getPatientId());
        weChatMobileShebaoRefundReq.setOutTradeNo(payChargeTransaction.getThirdPartyPayTransactionId());
        weChatMobileShebaoRefundReq.setBusinessRefundNo(transactionId);
        weChatMobileShebaoRefundReq.setRefundFee(MathUtils.convertBigDecimalToFenInt(MathUtils.wrapBigDecimalOrZero(refundInfo.getRefundFee()).abs()));
        weChatMobileShebaoRefundReq.setBusinessCallBackUrl(Constants.BusinessCallBackUrl.THIRD_PART_REFUND_CALLBACK_URL);
        weChatMobileShebaoRefundReq.setOperatorId(refundInfo.getOperatorId());

        WeChatMobileShebaoRefundRsp weChatMobileShebaoRefundRsp = wechatPayService.wxMobileShebaoRefund(weChatMobileShebaoRefundReq);

        int payType;
        if (isPaidback) {
            payType = ChargePayTransaction.PayType.PAIDBACK;
        } else {
            payType = ChargePayTransaction.PayType.REFUND;
        }

        ThirdPartyPayInfo thirdPartyPayInfo = new ThirdPartyPayInfo();
        thirdPartyPayInfo.setTransactionId(weChatMobileShebaoRefundRsp.getOutRefundNo());

        int payStatus;
        if (weChatMobileShebaoRefundRsp.getStatus() == WeChatMobileShebaoRefundRsp.Status.SUCCESS) {
            payStatus = PayStatus.SUCCESS;
            thirdPartyPayInfo.setThirdPartyTransactionId(weChatMobileShebaoRefundRsp.getMedRefundId());
        } else {
            payStatus = PayStatus.WAITING;
        }

        ChargePayTransaction chargePayTransaction = saveChargePayTransactionForRefund(transactionId, refundInfo, refundInfo.getRefundFee(), refundInfo.getRefundFee(),
                BigDecimal.ZERO, payStatus, payType, weChatMobileShebaoRefundRsp.getOutRefundNo(),
                payChargeTransaction.getThirdPartyPayTransactionId(),
                null, thirdPartyPayInfo);

        ChargePayRefundResult chargePayRefundResult = new ChargePayRefundResult();
        chargePayRefundResult.setPayStatus(payStatus);
        chargePayRefundResult.setChargePayTransactionId(transactionId);
        chargePayRefundResult.setThirdPartyPayTaskId(weChatMobileShebaoRefundRsp.getOutRefundNo());
        chargePayRefundResult.setRefundedPrincipal(chargePayRefundResult.getPayStatus() == PayStatus.SUCCESS ? MathUtils.convertFenIntToBigDecimal(weChatMobileShebaoRefundReq.getRefundFee()).negate() : BigDecimal.ZERO);
        chargePayRefundResult.setPayInfo(chargePayTransaction.getPayInfo());
        chargePayRefundResult.setAssociateThirdPartyPayTaskId(payChargeTransaction.getThirdPartyPayTransactionId());
        chargePayRefundResult.setExpireTime(chargePayTransaction.getExpireTime());
        chargePayRefundResult.setLockPatientOrderExpireTime(getLockPatientOrderExpireTimeForRefund());
        return chargePayRefundResult;
    }
}
