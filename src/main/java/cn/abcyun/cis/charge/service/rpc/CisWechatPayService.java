package cn.abcyun.cis.charge.service.rpc;

import cn.abcyun.bis.rpc.sdk.cis.client.AbcCisWechatPayFeignClient;
import cn.abcyun.bis.rpc.sdk.cis.model.wechatpay.*;
import cn.abcyun.bis.rpc.sdk.common.model.BasicCommonRsp;
import cn.abcyun.cis.charge.rpc.client.WeChatPayClient;
import cn.abcyun.cis.commons.exception.ServiceInternalException;
import cn.abcyun.cis.commons.rpc.wechat.*;
import cn.abcyun.cis.commons.rpc.wechat.WeChatPayReq;
import cn.abcyun.cis.commons.rpc.wechat.WeChatPayRsp;
import cn.abcyun.cis.commons.rpc.wechat.WeChatRefundReq;
import cn.abcyun.cis.commons.rpc.wechat.WeChatRefundRsp;
import cn.abcyun.cis.core.util.FeignClientRpcTemplate;
import feign.RetryableException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@Slf4j
public class CisWechatPayService {

    @Autowired
    private WeChatPayClient weChatPayClient;

    @Autowired
    private AbcCisWechatPayFeignClient client;

    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public boolean isWechatPayJsapiEnable(String chainId, String clinicId, boolean isRefund) {

        WeChatPayConfigRsp rsp = getWeChatPayConfig(chainId, clinicId);

        //这里区分是否为退费的原因是：一个店有可能从微信支付改为了abc支付，这时，查询config，判断的方式只需要判断是否支持jsapi支付就够了
        if (isRefund) {
            return rsp != null && ObjectUtils.equals(rsp.getJsapiPayStatus(), WeChatPayConfigRsp.JsapiPayStatus.AVAILABLE);
        }

        return rsp != null && ObjectUtils.equals(rsp.getJsapiPayStatus(), WeChatPayConfigRsp.JsapiPayStatus.AVAILABLE)
                && rsp.getType() == WeChatPayConfigRsp.Type.WECHAT_PAY_CONFIG;
    }

    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public boolean isClinicWechatPayEnable(String chainId, String clinicId) throws ServiceInternalException {
        WeChatPayConfigRsp rsp = getWeChatPayConfig(chainId, clinicId);
        return rsp != null && ObjectUtils.equals(rsp.getWeChatPaySwitch(), WeChatPayConfigRsp.WeChatPaySwitch.AVAILABLE);
    }


    public WeChatPayConfigRsp getWeChatPayConfig(String chainId, String clinicId) throws ServiceInternalException {

        if (org.apache.commons.lang3.StringUtils.isAnyEmpty(chainId, clinicId)) {
            return null;
        }

        WeChatPayConfigRsp rsp = FeignClientRpcTemplate.dealRpcClientMethod("getWeChatPayConfig", true, () -> weChatPayClient.getClinicConfig(chainId, clinicId), chainId, clinicId);

        return Optional.ofNullable(rsp).orElse(new WeChatPayConfigRsp());
    }

    public WeChatPayRsp abcPay(WeChatPayReq abcPayReq) {
        return FeignClientRpcTemplate.dealRpcClientMethod("payForAbcPay", true, () -> weChatPayClient.allinPay(abcPayReq), abcPayReq);
    }

    public WeChatRefundRsp abcRefund(WeChatRefundReq abcRefundReq) {
        return FeignClientRpcTemplate.dealRpcClientMethod("refundForAbcPay", true, () -> weChatPayClient.allinPayRefund(abcRefundReq), abcRefundReq);
    }

    public BasicCommonRsp closeAllinPayOrder(CloseOrderReq closeOrderReq) {
        return FeignClientRpcTemplate.dealRpcClientMethod("closeAllinPayOrder", true, () -> client.closeAllinPayOrder(closeOrderReq), closeOrderReq);
    }

    public BasicCommonRsp closeMobileShebaoPayOrder(CloseOrderReq closeOrderReq) {
        return FeignClientRpcTemplate.dealRpcClientMethod("closeMobileShebaoPayOrder", true, () -> client.closeMobileShebaoOrder(closeOrderReq), closeOrderReq);
    }

    public BasicCommonRsp closeWechatPayOrder(CloseOrderReq closeOrderReq) {
        return FeignClientRpcTemplate.dealRpcClientMethod("closeWechatPayOrder", true, () -> client.closeWechatPayOrder(closeOrderReq), closeOrderReq);
    }

    public WeChatPayRsp wechatPay(WeChatPayReq weChatPayReq) {
        return FeignClientRpcTemplate.dealRpcClientMethod("wechatPay", true, () -> weChatPayClient.wechatPay(weChatPayReq), weChatPayReq);
    }

    public WeChatRefundRsp wechatRefund(WeChatRefundReq weChatRefundReq) {
        return FeignClientRpcTemplate.dealRpcClientMethod("refundForWechatPay", true, () -> weChatPayClient.wechatRefund(weChatRefundReq), weChatRefundReq);
    }

    /**
     * 微信移动医保支付
     * @param req 请求参数
     * @return AbcServiceResponseBody<WeChatMobileShebaoPayRsp>
     */
    public WeChatMobileShebaoPayRsp wxMobileShebaoPay(WeChatMobileShebaoPayReq req){
        return FeignClientRpcTemplate.dealRpcClientMethod("wxMobileShebaoPay", true, () -> client.wxMobileShebaoPay(req), req);
    }

    /**
     * 微信移动医保退款-仅支持退现金部分
     * @param req 请求参数
     * @return AbcServiceResponseBody<WeChatMobileShebaoPayRsp>
     */
    public WeChatMobileShebaoRefundRsp wxMobileShebaoRefund(WeChatMobileShebaoRefundReq req){
        return FeignClientRpcTemplate.dealRpcClientMethod("wxMobileShebaoPay", true, () -> client.wxMobileShebaoRefund(req), req);
    }
}
