package cn.abcyun.cis.charge.service.dto;

import cn.abcyun.cis.charge.model.ChargeSheetAdditionalExtendInfo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;

/**
 * <AUTHOR>
 * @create 2024-01-27 20:13
 * @Description
 */

@Data
public class SalesOrderAbstract {
    private String id;

    private String patientOrderId;

    private String chainId;

    private String clinicId;

    @ApiModelProperty(value = "销售单号")
    private String sellNo;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "应收金额")
    private BigDecimal receivableFee;

    @ApiModelProperty(value = "实收金额")
    private BigDecimal receivedFee;

    private BigDecimal refundFee;

    @ApiModelProperty(value = "患者ID")
    private String patientId;

    @ApiModelProperty(value = "销售员id")
    private String sellerId;

    @ApiModelProperty(value = "收费员id")
    private String chargedBy;

    @ApiModelProperty(value = "创建时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Instant created;
    @ApiModelProperty(value = "收费时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Instant orderByDate;

    @ApiModelProperty(value = "是否已关闭，null/0：否；1：是")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer isClosed;

    @ApiModelProperty(value = "收费单类型")
    private int type;

    /**
     * 合作诊所处方来源诊所id
     */
    @ApiModelProperty(value = "合作诊所处方来源诊所id")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String coSourceClinicId;

    /**
     * 合作诊所处方来源诊所名称
     */
    @ApiModelProperty(value = "合作诊所处方来源诊所名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String coSourceClinicName;

    private ChargeSheetAdditionalExtendInfo additionalExtendedInfo;
}
