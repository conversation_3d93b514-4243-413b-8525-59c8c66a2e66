package cn.abcyun.cis.charge.service.dto;

import cn.abcyun.bis.rpc.sdk.cis.model.shorturl.UploadAttachment;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.model.ChargeDeliveryInfo;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.util.ChargeUtils;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.cis.commons.model.CisPatientInfo;
import cn.abcyun.cis.commons.rpc.charge.DiagnosisInfo;
import cn.abcyun.cis.commons.rpc.outpatient.ExtendDiagnosisInfo;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class ChargeSheetExtend extends ChargeSheet {
    private CisPatientInfo patient;
    private String statusName;
    private ChargeSheetSummary chargeSheetSummary;
    private String diagnosis;
    private String chiefComplaint;
    private ChargeDeliveryInfo deliveryInfo;
    private Instant firstChargedTime;

    /**
     * 转录医生的科室id
     */
    private String departmentId;

    /**
     * 诊断的医保目录编码及名称
     */
    private List<DiagnosisInfo> diagnosisInfos;

    /**
     * 诊断V2
     */
    private List<ExtendDiagnosisInfo> extendDiagnosisInfos;

    /**
     * 收费告知书
     */
    private List<UploadAttachment> notificationInfos;

    /**
     * 是否要上传收费告知书
     */
    private int isCanUploadNotification;

    /**
     * 是否为患者自己支付的
     */
    private int isPatientSelfPay;

    /**
     * 药房登记id
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String registerInfoId;

    /**
     * 转录医生id
     */
    private String transcribeDoctorId;

    /**
     * 开票状态标志位（位模式） 1:存在待开票的数据 2:存在已开票的数据 4:开票金额异常
     * {@link Constants.ChargeSheetInvoiceStatusFlag}
     */
    private Integer invoiceStatusFlag;

    public int getIsPatientSelfPay() {
        return ChargeUtils.isPatientSelfPay(getChargeTransactions()) ? 1 : 0;
    }

    public int getIsCanUploadNotification() {
        if (ChargeUtils.isCanUploadNotification(getStatus(), getType(), MathUtils.wrapBigDecimalAdd(getReceivedFee(), getRefundFee())).getLeft().booleanValue()) {
            return 1;
        }
        return 0;
    }

    public List<DiagnosisInfo> getDiagnosisInfos() {
        return ChargeUtils.generateDiagnosisInfos(diagnosisInfos, extendDiagnosisInfos);
    }

    public List<ExtendDiagnosisInfo> getExtendDiagnosisInfos() {
        return ChargeUtils.generateExtendDiagnosisInfos(diagnosisInfos, extendDiagnosisInfos);
    }
}
