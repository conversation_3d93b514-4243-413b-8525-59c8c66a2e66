package cn.abcyun.cis.charge.service;

import cn.abcyun.bis.rpc.sdk.cis.model.pay.shebao.PayForShebaoReq;
import cn.abcyun.cis.charge.base.InnerPayModes;
import cn.abcyun.cis.charge.base.PayModeInfo;
import cn.abcyun.cis.charge.rpc.client.ShebaoClient;
import cn.abcyun.cis.charge.service.rpc.CisScClinicService;
import cn.abcyun.cis.charge.service.rpc.CisShebaoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 铁保支付
 */
@Service
@Slf4j
public class ChargePayHandleShebaoRailwayPayService extends ChargeShebaoPayHandleAbstractService {

    @Autowired
    public ChargePayHandleShebaoRailwayPayService(ChargeAbnormalTransactionService chargeAbnormalTransactionService,
                                                  ChargePayTransactionService chargePayTransactionService,
                                                  ChargeSheetService chargeSheetService,
                                                  EmployeeService employeeService,
                                                  PatientService patientService,
                                                  ShebaoClient shebaoClient,
                                                  CisScClinicService scClinicService,
                                                  CisShebaoService cisShebaoService) {
        super(chargeSheetService, chargePayTransactionService, chargeAbnormalTransactionService, employeeService, patientService, shebaoClient, scClinicService, cisShebaoService);
    }


    @Override
    public PayModeInfo getPayModeInfo() {
        return InnerPayModes.getPayModeByName(InnerPayModes.InnerPayModeName.SHEBAO_RAILWAY_PAY);
    }

    @Override
    public int getShebaoPayMethod(int paySubMode) {
        return PayForShebaoReq.PayMethod.RAILWAY_YB_PAY;
    }

    @Override
    public Long getLockPatientOrderExpireTimeForPay() {
        //铁保支付超时时间设置为20分钟
        return 20 * 60 * 1000L;
    }

}
