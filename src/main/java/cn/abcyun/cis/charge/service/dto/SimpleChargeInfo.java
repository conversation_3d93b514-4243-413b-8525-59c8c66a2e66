package cn.abcyun.cis.charge.service.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

@Data
public class SimpleChargeInfo {
    private BigDecimal netIncomeFee;        //净收入
    private String chargedByName;
    private Instant chargedTime;
    /**
     * 开单人id
     */
    private String sellerId;
    /**
     * 开单人名称
     */
    private String sellerName;
    /**
     * 医生id
     */
    private String doctorId;
    /**
     * 医生名称
     */
    private String doctorName;
    private ChargeDeliveryInfoView deliveryInfo;

    /**
     * 虚拟药房的form信息，快递和加工
     */
    private List<ChargeFormSimpleView> virtualPharmacyChargeForms;

    /**
     * 医生科室名称
     */
    private String departmentName;
    /**
     * 医生科室id
     */
    private String departmentId;

    /**
     * 开单人科室id
     */
    private String sellerDepartmentId;
    /**
     * 开单人科室名称
     */
    private String sellerDepartmentName;

    /**
     * 诊断
     */
    private String diagnose;

    /**
     * 医嘱
     */
    private String doctorAdvice;

    private List<ChargeFormItemSimpleView> chargeFormItems;

    @Data
    @Accessors(chain = true)
    public static class ChargeFormItemSimpleView {

        private String id;

        private int productType;

        private int productSubType;

        private BigDecimal receivedPrice;

    }

    @Data
    @Accessors(chain = true)
    public static class ChargeFormSimpleView {

        private String id;

        /**
         * 药房号
         */
        private int pharmacyNo;

        /**
         * 药房类型
         */
        private int pharmacyType;

        /**
         * 快递信息
         */
        private ChargeDeliveryInfoView deliveryInfo;

        /**
         * 快递费
         */
        private BigDecimal deliveryFee = BigDecimal.ZERO;

        /**
         * 是否有快递费
         */
        private int isDelivery;

        /**
         * 加工费
         */
        private BigDecimal processFee = BigDecimal.ZERO;

        /**
         * 是否有加工费
         */
        private int isProcess;

        /**
         * 9自煎 10代煎
         */
        private String medicineStateScopeId;
    }
}
