package cn.abcyun.cis.charge.service.dto;

import cn.abcyun.cis.charge.api.model.EmployeeView;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

@Data
@Accessors(chain = true)
public class ChargeExecuteActionView {
    private String id;
    private BigDecimal count;
    private String executeItemId;
    private String name;
    private int productType;
    private int productSubType;
    /**
     * 保留字段，新接口使用executors字段
     */
    @Deprecated
    private String executorName;
    private Instant executeDate;
    private Instant lastModifiedDate;
    private String lastModifiedByName;
    private int status;
    /**
     * 创建人
     */
    private String createdByName;
    /**
     * 执行效果
     */
    private String executeEffect;
    /**
     * 撤销人名称
     */
    private String canceledEmployeeName;
    /**
     * 执行人集合
     */
    private List<EmployeeView> executors;
}
