package cn.abcyun.cis.charge.service.dto;

import cn.abcyun.bis.rpc.sdk.cis.model.shebao.QueryChargeSheetReverseListResBody;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.model.ChargePayTransaction;
import cn.abcyun.cis.commons.rpc.pay.shebao.PayForShebaoReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Objects;

@Data
@Accessors(chain = true)
public class LockPayTransactionView {

    @ApiModelProperty(value = "第三方支付id")
    private String id;

    @ApiModelProperty(value = "渠道的支付单id，比如是shebao渠道，就是taskId，如果是wechatpay渠道，就是wechatPay服务生成的订单号，如果是chargeCenter渠道，就是chargeCenter生成的订单号")
    private String payTransactionId;

    @ApiModelProperty(value = "支付方式")
    private int payMode;

    @ApiModelProperty(value = "支付子方式")
    private int paySubMode;

    /**
     * {@link ChargePayTransaction.PayType}
     */
    @ApiModelProperty(value = "支付类型，1:支付，2：收费单支付完成后再退费，3：部分支付状态下退费，（2和3都表示退费，只是收费单的原始状态不同）")
    private int payType;

    @ApiModelProperty(value = "异常数据来源，0 charge本身，1 社保服务")
    private int source;

    public static class Source {

        public static final int CHARGE_PAY_TRANSACTION = 0;

        public static final int SHEBAO = 1;

    }

    public static LockPayTransactionView of(ChargePayTransaction chargePayTransaction) {
        if (Objects.isNull(chargePayTransaction)) {
            return null;
        }

        return new LockPayTransactionView()
                .setId(chargePayTransaction.getId())
                .setPayTransactionId(chargePayTransaction.getPayTransactionId())
                .setPayMode(chargePayTransaction.getPayMode())
                .setPaySubMode(chargePayTransaction.getPaySubMode())
                .setPayType(chargePayTransaction.getPayType())
                .setSource(Source.CHARGE_PAY_TRANSACTION);
    }

    public static LockPayTransactionView ofFromShebaoReverseItem(QueryChargeSheetReverseListResBody.ChargeSheetItem.ReverseItem reverseItem) {
        if (Objects.isNull(reverseItem)) {
            return null;
        }

        Integer payMode = null;
        int paySubMode = 0;
        if (reverseItem.getPayMethod() == PayForShebaoReq.PayMethod.NORMAL) {
            payMode = Constants.ChargePayMode.HEALTH_CARD;
        } else if (reverseItem.getPayMethod() == PayForShebaoReq.PayMethod.MULAIDPAY) {
            payMode = Constants.ChargePayMode.SHEBAO_MULAID_PAY;
        } else if (reverseItem.getPayMethod() == PayForShebaoReq.PayMethod.MOBILEPAY) {
            payMode = Constants.ChargePayMode.HEALTH_CARD;
            paySubMode = Constants.ChargePaySubMode.WECHAT_MOBILE_SHEBAO;
        } else if (reverseItem.getPayMethod() == PayForShebaoReq.PayMethod.OUTPATIENT_CENTER_PAY) {
            payMode = Constants.ChargePayMode.OUTPATIENT_CENTER_PAY;
        }

        if (payMode == null) {
            return null;
        }

        return new LockPayTransactionView()
                .setPayTransactionId(reverseItem.getTaskId())
                .setPayMode(payMode)
                .setPaySubMode(paySubMode)
                .setPayType(ChargePayTransaction.PayType.PAY)
                .setSource(Source.SHEBAO);
    }
}
