package cn.abcyun.cis.charge.service;

import cn.abcyun.bis.rpc.sdk.cis.model.charge.ThirdPartyCommonPayConfig;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.ClinicBusinessPermissionItem;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.ClinicBusinessPermissionValue;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.ClinicConstants;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Employee;
import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.bis.rpc.sdk.cis.model.message.ToBMessage;
import cn.abcyun.bis.rpc.sdk.cis.model.message.WebMessageBody;
import cn.abcyun.bis.rpc.sdk.property.base.PropertyKey;
import cn.abcyun.bis.rpc.sdk.property.base.UpdatePropertyItemReq;
import cn.abcyun.bis.rpc.sdk.property.model.ChargeSheetAutoClose;
import cn.abcyun.bis.rpc.sdk.property.model.ClinicBasicCharge;
import cn.abcyun.bis.rpc.sdk.property.model.Dispensing;
import cn.abcyun.bis.rpc.sdk.property.model.WechatAutoPay;
import cn.abcyun.bis.rpc.sdk.property.service.PropertyService;
import cn.abcyun.cis.charge.amqp.MQProducer;
import cn.abcyun.cis.charge.amqp.RocketMqProducer;
import cn.abcyun.cis.charge.api.model.*;
import cn.abcyun.cis.charge.base.ChargeServiceError;
import cn.abcyun.cis.charge.base.ChargeServiceException;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.mapper.ChargePayModeConfigMapper;
import cn.abcyun.cis.charge.model.ChargeCalculateConfig;
import cn.abcyun.cis.charge.model.ChargePayModeConfig;
import cn.abcyun.cis.charge.model.ChargePayModeRelation;
import cn.abcyun.cis.charge.processor.ChargePayModeConfigSimple;
import cn.abcyun.cis.charge.processor.provider.ChargePayModeProvider;
import cn.abcyun.cis.charge.repository.ChargeCalculateConfigRepository;
import cn.abcyun.cis.charge.repository.ChargePayModeConfigRepository;
import cn.abcyun.cis.charge.repository.ChargePayModeRelationRepository;
import cn.abcyun.cis.charge.service.rpc.CisScClinicService;
import cn.abcyun.cis.charge.util.FillUtils;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.abcyun.cis.commons.model.CisClinicType;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.cis.core.db.UseReadOnlyDB;
import cn.abcyun.cis.core.util.JsonUtils;
import cn.abcyun.cis.idgenerator.AbcIdGenerator;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Transactional
@Slf4j
@EnableAspectJAutoProxy(exposeProxy = true)
public class ChargeConfigService implements ChargePayModeProvider {
    private static final Logger sLogger = LoggerFactory.getLogger(ChargeConfigService.class);
    @Autowired
    private ChargeCalculateConfigRepository chargeCalculateConfigRepository;

    @Autowired
    private ChargePayModeRelationRepository chargePayModeRelationRepository;

    @Autowired
    private ChargePayModeConfigRepository chargePayModeConfigRepository;

    @Autowired
    private ChargePayModeConfigMapper chargePayModeConfigMapper;

    @Autowired
    private AbcIdGenerator abcIdGenerator;

    @Autowired
    private MQProducer mqProducer;

    @Autowired
    private RocketMqProducer rocketMqProducer;

    @Autowired
    private McService mcService;

    @Autowired
    private PropertyService propertyService;

    @Autowired
    private CisScClinicService cisScClinicService;

    private static List<ChargePayModeConfigSimple> SYSTEM_WITH_CONDITIONAL_PAY_MODES = null;

    @Transactional(rollbackFor = Exception.class)
    @Caching(evict = {
            @CacheEvict(cacheNames = "cis_charge:charge-pay-mode", key = "#chainId"),
            @CacheEvict(cacheNames = "cis_charge:charge-pay-mode-config-view", key = "#chainId")
    })
    public void updateChargeConfig(UpdateChargeConfigReq updateChargeConfigReq, String chainId, String clinicId, String employeeId, int clinicType, String viewMode) throws ChargeServiceException {

        checkRefundRestriction(updateChargeConfigReq, clinicId);

        ChargeCalculateConfig chargeCalculateConfig = chargeCalculateConfigRepository.findByChainIdAndClinicIdAndIsDeleted(chainId, chainId, 0);

        insertOrUpdateChargeCalculateConfig(chargeCalculateConfig, updateChargeConfigReq, chainId, chainId, employeeId);

        updatePayModeConfig(updateChargeConfigReq.getPayMode(), chainId, chainId, employeeId);

        /**
         * 如果是单店或者是子店（子店包含了特殊的单店判断，就不用分成三个判断了），就可以改自助支付的配置
         */
        if (clinicType == CisClinicType.CHAIN_BRANCH_CLINIC
                || (clinicType == CisClinicType.INDEPENDENT_CLINIC && Objects.equals(CisJWTUtils.CIS_VIEW_MODE_NORMAL, viewMode))) {
            updateBranchClinicSendOrderConfig(updateChargeConfigReq, clinicId, employeeId);
        }

        /**
         * 自动关单
         */
        updateClinicChargeSheetAutoClose(updateChargeConfigReq, clinicId, employeeId);
        updateClinicBasicChargeConfig(updateChargeConfigReq, chainId, clinicId, employeeId);

        sendPushMessageToWeb(chainId, clinicId);

    }

    private void checkRefundRestriction(UpdateChargeConfigReq updateChargeConfigReq, String clinicId) {
        if (updateChargeConfigReq.getRefundRestriction() != ChargeCalculateConfig.RefundRestriction.AUDIT && updateChargeConfigReq.getRefundRestriction() != ChargeCalculateConfig.RefundRestriction.COMPOUND) {
            return;
        }
        Dispensing dispensingKey = propertyService.getPropertyValueByKey(PropertyKey.DISPENSING, clinicId, Dispensing.class);
        if (dispensingKey == null) {
            return;
        }
        if (updateChargeConfigReq.getRefundRestriction() == ChargeCalculateConfig.RefundRestriction.AUDIT && dispensingKey.getPrescriptionReview() == YesOrNo.NO) {
            throw new ChargeServiceException(ChargeServiceError.NEED_REQUIRED_PARAMETER.getCode(), "需先至发药设置中开启处方审核");
        }
        if (updateChargeConfigReq.getRefundRestriction() == ChargeCalculateConfig.RefundRestriction.COMPOUND && dispensingKey.getPrescriptionCompound() == YesOrNo.NO) {
            throw new ChargeServiceException(ChargeServiceError.NEED_REQUIRED_PARAMETER.getCode(), "需先至发药设置中开启处方调配");
        }
    }

    private void sendPushMessageToWeb(String chainId, String clinicId) {
        WebMessageBody.WebPushMessage webPushMessage = new WebMessageBody.WebPushMessage();
        webPushMessage.setEvent(WebMessageBody.WebMessageEvent.CHARGE_UPDATE_PAY_MODE);
        webPushMessage.setScope(WebMessageBody.WebMessageScope.CHAIN);
        webPushMessage.setScopeId(chainId);

        WebMessageBody webMessageBody = new WebMessageBody();
        webMessageBody.setTrigger(webPushMessage);
        webMessageBody.setMemoryFlag(WebMessageBody.MemoryLogType.NO_WRITE_LOG);

        ToBMessage toBMessage = new ToBMessage();
        toBMessage.setChainId(chainId);
        toBMessage.setClinicId(clinicId);
        toBMessage.setChannel(ToBMessage.MessageChannel.Web);
        toBMessage.setMsgId(abcIdGenerator.getUUID());
        toBMessage.setBody(JsonUtils.dumpAsJsonNode(webMessageBody));
        rocketMqProducer.sendTobMessage(toBMessage);
    }

    private void updatePayModeConfig(UpdatePayModeReq updatePayModeReq, String chainId, String clinicId, String employeeId) {

        Map<Long, UpdatePayModeReq.PayModeReq> needUpdatePayModeMap = new HashMap<>();
        List<UpdatePayModeReq.PayModeReq> needInsertPayModes = new ArrayList<>();
        List<UpdatePayModeReq.PayModeReq> needRecoverPayModes = new ArrayList<>();

        if (!CollectionUtils.isEmpty(updatePayModeReq.getOptionalPayModes())) {
            updatePayModeReq.getOptionalPayModes().removeIf(payModeReq ->
                    !Constants.ChargePayMode.SYSTEM_OPTIONAL_PAY_MODES.contains(payModeReq.getPayModeId().intValue())
                            && !Constants.ChargePayMode.SYSTEM_REQUIRED_PAY_MODES.contains(payModeReq.getPayModeId().intValue())
                            && !Constants.ChargePayMode.SYSTEM_WITH_CONDITIONAL_PAY_MODES.contains(payModeReq.getPayModeId().intValue())
                            && !Objects.equals(Integer.valueOf(Constants.ChargePayModeConfigType.THIRD_PARTY_COMMON_PAY), payModeReq.getType())
            );
            Map<Long, UpdatePayModeReq.PayModeReq> optionalPayModeIdMap = updatePayModeReq.getOptionalPayModes().stream().collect(Collectors.toMap(UpdatePayModeReq.PayModeReq::getPayModeId, Function.identity(), (a, b) -> a));
            needUpdatePayModeMap.putAll(optionalPayModeIdMap);
        }

        if (!CollectionUtils.isEmpty(updatePayModeReq.getCustomizedPayModes())) {

            Map<Long, UpdatePayModeReq.PayModeReq> customizedPayModeIdMap = updatePayModeReq.getCustomizedPayModes()
                    .stream()
                    .filter(payModeReq -> payModeReq.getPayModeId() != null)
                    .filter(payModeReq -> payModeReq.getPayModeId() >= Constants.ChargePayMode.SYSTEM_PAY_MODE_MAX)
                    .collect(Collectors.toMap(UpdatePayModeReq.PayModeReq::getPayModeId, Function.identity(), (a, b) -> a));
            needUpdatePayModeMap.putAll(customizedPayModeIdMap);

            needInsertPayModes = updatePayModeReq.getCustomizedPayModes()
                    .stream()
                    .filter(payModeReq -> payModeReq.getPayModeId() == null)
                    .collect(Collectors.toList());

        }

        if (!CollectionUtils.isEmpty(needInsertPayModes)) {

            List<String> payModeNames = needInsertPayModes.stream().map(payModeReq -> payModeReq.getName()).collect(Collectors.toList());

            List<ChargePayModeConfig> chargePayModeConfigList = chargePayModeConfigMapper.findAllByChainIdAndClinicIdAndName(chainId, clinicId, payModeNames, null);
            if (!CollectionUtils.isEmpty(chargePayModeConfigList)) {
                //处理被删除的收费方式又添加进来的逻辑
                Map<String, ChargePayModeConfig> chargePayModeConfigMap = chargePayModeConfigList.stream().collect(Collectors.toMap(ChargePayModeConfig::getName, Function.identity(), (a, b) -> a));

                needInsertPayModes.removeIf(payModeReq -> {
                    ChargePayModeConfig chargePayModeConfig = chargePayModeConfigMap.getOrDefault(payModeReq.getName(), null);
                    if (chargePayModeConfig != null && chargePayModeConfig.getIsDeleted() == 1) {
                        UpdatePayModeReq.PayModeReq recoverPayModeReq = new UpdatePayModeReq.PayModeReq();
                        BeanUtils.copyProperties(payModeReq, recoverPayModeReq);
                        recoverPayModeReq.setPayModeId(chargePayModeConfig.getId());
                        needRecoverPayModes.add(recoverPayModeReq);
                        return true;
                    } else if (chargePayModeConfig != null && chargePayModeConfig.getIsDeleted() == 0) {
                        UpdatePayModeReq.PayModeReq updatePayModeReqFlag = new UpdatePayModeReq.PayModeReq();
                        BeanUtils.copyProperties(payModeReq, updatePayModeReqFlag);
                        updatePayModeReqFlag.setPayModeId(chargePayModeConfig.getId());
                        needUpdatePayModeMap.put(chargePayModeConfig.getId(), updatePayModeReqFlag);
                        return true;
                    } else {
                        return false;
                    }
                });

                long deletedCount = chargePayModeConfigList.stream()
                        .filter(chargePayModeConfig -> !needUpdatePayModeMap.containsKey(chargePayModeConfig.getId().longValue()))
                        .filter(chargePayModeConfig -> chargePayModeConfig.getIsDeleted() == 0)
                        .count();
                if (deletedCount > 0) {
                    log.error("收费方式名字不可重复添加");
                    throw new ChargeServiceException(ChargeServiceError.PAY_MODE_NAME_REPEAT);
                }
            }


        }

        Set<Long> payModeConfigIds = new HashSet<>();
        if (!CollectionUtils.isEmpty(needUpdatePayModeMap)) {

            List<Long> needUpdatePayModeIds = new ArrayList<>(needUpdatePayModeMap.keySet());
            List<Long> needRecoverPayModeIds = needRecoverPayModes.stream().map(UpdatePayModeReq.PayModeReq::getPayModeId).collect(Collectors.toList());
            Map<Long, UpdatePayModeReq.PayModeReq> needRecoverPayModeMap = needRecoverPayModes.stream().collect(Collectors.toMap(UpdatePayModeReq.PayModeReq::getPayModeId, Function.identity(), (a, b) -> a));


            payModeConfigIds.addAll(needUpdatePayModeIds);
            payModeConfigIds.addAll(needRecoverPayModeIds);

            List<ChargePayModeRelation> chargePayModeRelations = chargePayModeRelationRepository.findAllByChainIdAndClinicIdAndPayModeConfigIdIn(chainId, clinicId, payModeConfigIds);

            if (CollectionUtils.isEmpty(chargePayModeRelations)) {
                throw new ChargeServiceException(ChargeServiceError.PAY_MODE_NOT_EXIST);
            }

            //更新自定义收费方式
            chargePayModeRelations
                    .stream()
                    .filter(chargePayModeRelation -> needUpdatePayModeIds.contains(chargePayModeRelation.getPayModeConfigId()))
                    .forEach(chargePayModeRelation -> {
                        UpdatePayModeReq.PayModeReq optionalPayMode = needUpdatePayModeMap.getOrDefault(chargePayModeRelation.getPayModeConfigId(), null);
                        if (optionalPayMode != null) {
                            chargePayModeRelation.setIsEnable(optionalPayMode.getIsEnable());
                            chargePayModeRelation.setSort(optionalPayMode.getSort());
                            FillUtils.fillLastModifiedBy(chargePayModeRelation, employeeId);
                        }
                    });

            //恢复被删除的收费方式
            if (!CollectionUtils.isEmpty(needRecoverPayModeIds)) {
                List<ChargePayModeConfig> chargePayModeConfigs = chargePayModeConfigRepository.findAllByIdIn(needRecoverPayModeIds);
                if (!CollectionUtils.isEmpty(chargePayModeConfigs)) {
                    chargePayModeConfigs
                            .stream()
                            .forEach(chargePayModeConfig -> chargePayModeConfig.setIsDeleted(0));
                }
            }

            chargePayModeRelations
                    .stream()
                    .filter(chargePayModeRelation -> needRecoverPayModeIds.contains(chargePayModeRelation.getPayModeConfigId()))
                    .forEach(chargePayModeRelation -> {
                        UpdatePayModeReq.PayModeReq payModeReq = needRecoverPayModeMap.getOrDefault(chargePayModeRelation.getPayModeConfigId(), null);
                        if (payModeReq != null) {
                            chargePayModeRelation.setIsDeleted(0);
                            chargePayModeRelation.setIsEnable(payModeReq.getIsEnable());
                            chargePayModeRelation.setSort(payModeReq.getSort());
                            FillUtils.fillLastModifiedBy(chargePayModeRelation, employeeId);
                        }
                    });
        }

        //删除没有回传的自定义收费方式
        chargePayModeConfigMapper.deleteChargePayModeRelationByPayModeConfigIdsNotIN(clinicId, chainId, new ArrayList<>(payModeConfigIds));


        if (!CollectionUtils.isEmpty(needInsertPayModes)) {
            List<ChargePayModeRelation> chargePayModeRelations = new ArrayList<>();

            needInsertPayModes.forEach(payModeReq -> {

                ChargePayModeConfig chargePayModeConfig = new ChargePayModeConfig();
                chargePayModeConfig.setName(payModeReq.getName());
                chargePayModeConfig.setType(Constants.ChargePayModeConfigType.CUSTOM);
                FillUtils.fillCreatedBy(chargePayModeConfig, employeeId);

                chargePayModeConfigRepository.saveAndFlush(chargePayModeConfig);

                ChargePayModeRelation chargePayModeRelation = new ChargePayModeRelation();
                chargePayModeRelation.setId(abcIdGenerator.getUUID());
                chargePayModeRelation.setPayModeConfigId(chargePayModeConfig.getId());
                chargePayModeRelation.setClinicId(clinicId);
                chargePayModeRelation.setChainId(chainId);
                chargePayModeRelation.setIsEnable(payModeReq.getIsEnable());
                chargePayModeRelation.setSort(payModeReq.getSort());

                FillUtils.fillCreatedBy(chargePayModeRelation, employeeId);
                chargePayModeRelations.add(chargePayModeRelation);

            });

            chargePayModeRelationRepository.saveAll(chargePayModeRelations);
        }
    }

    public void insertOrUpdateChargeCalculateConfig(ChargeCalculateConfig chargeCalculateConfig, UpdateChargeConfigReq updateChargeConfigReq, String chainId, String clinicId, String employeeId) {

        boolean isInsert = false;

        if (chargeCalculateConfig == null) {
            isInsert = true;
            chargeCalculateConfig = new ChargeCalculateConfig();
            chargeCalculateConfig.setId(abcIdGenerator.getUID());
            chargeCalculateConfig.setClinicId(clinicId);
            chargeCalculateConfig.setChainId(chainId);
            chargeCalculateConfig.setDoctorRegisteredBargainSwitch(1);
            chargeCalculateConfig.setReservationRegisteredBargainSwitch(1);

            FillUtils.fillCreatedBy(chargeCalculateConfig, employeeId);
        } else {
            FillUtils.fillLastModifiedBy(chargeCalculateConfig, employeeId);
        }
        chargeCalculateConfig.setRoundingType(updateChargeConfigReq.getRoundingType());
        chargeCalculateConfig.setBargainSwitch(updateChargeConfigReq.getBargainSwitch());
        chargeCalculateConfig.setSingleBargainSwitch(updateChargeConfigReq.getSingleBargainSwitch());
        chargeCalculateConfig.setDoctorBargainSwitch(updateChargeConfigReq.getDoctorBargainSwitch());
        chargeCalculateConfig.setDoctorSingleBargainSwitch(updateChargeConfigReq.getDoctorSingleBargainSwitch());
        chargeCalculateConfig.setDoctorRegisteredBargainSwitch(updateChargeConfigReq.getDoctorRegisteredBargainSwitch());
        chargeCalculateConfig.setChargeNeedAstPassSwitch(updateChargeConfigReq.getChargeNeedAstPassSwitch());
        chargeCalculateConfig.setNurseBargainSwitch(updateChargeConfigReq.getNurseBargainSwitch());
        chargeCalculateConfig.setOweSheetSwitch(updateChargeConfigReq.getOweSheetSwitch());
        chargeCalculateConfig.setInspectBargainSwitch(updateChargeConfigReq.getInspectBargainSwitch());
        chargeCalculateConfig.setReservationRegisteredBargainSwitch(updateChargeConfigReq.getReservationRegisteredBargainSwitch());
        chargeCalculateConfig.setUsedDiscountNotAllowShebaoSwitch(updateChargeConfigReq.getUsedDiscountNotAllowShebaoSwitch());
        chargeCalculateConfig.setOpenPromotionCardUseMemberSwitch(updateChargeConfigReq.getOpenPromotionCardUseMemberSwitch());
        chargeCalculateConfig.setRechargePromotionCardUseMemberSwitch(updateChargeConfigReq.getRechargePromotionCardUseMemberSwitch());
        chargeCalculateConfig.setOddFeeDealPayModes(updateChargeConfigReq.getOddFeeDealPayModes());
        chargeCalculateConfig.setOddFeeDealType(updateChargeConfigReq.getOddFeeDealType());
        if (isInsert) {
            chargeCalculateConfigRepository.save(chargeCalculateConfig);
        }
    }


    public void updateConfigClinicScope(UpdateChargeConfigReq updateChargeConfigReq, String chainId, String clinicId, String employeeId) {
        checkRefundRestriction(updateChargeConfigReq, clinicId);
        updateBranchClinicSendOrderConfig(updateChargeConfigReq, clinicId, employeeId);
        updateClinicChargeSheetAutoClose(updateChargeConfigReq, clinicId, employeeId);
        updateClinicBasicChargeConfig(updateChargeConfigReq, chainId, clinicId, employeeId);
    }

    private void updateClinicBasicChargeConfig(UpdateChargeConfigReq updateChargeConfigReq, String chainId, String clinicId, String employeeId) {
        ClinicBasicCharge clinicBasicCharge = getClinicBasicCharge(clinicId);
        if (clinicBasicCharge != null) {
            clinicBasicCharge.setDirectSaleEnable(updateChargeConfigReq.getDirectSaleEnable());
            clinicBasicCharge.setRefundRestriction(updateChargeConfigReq.getRefundRestriction());
            clinicBasicCharge.setRefundCheck(updateChargeConfigReq.getRefundCheck());
            clinicBasicCharge.setWholeSheetOperateEnable(updateChargeConfigReq.getWholeSheetOperateEnable());
            UpdatePropertyItemReq<ClinicBasicCharge> updatePropertyItemReq = new UpdatePropertyItemReq<>();
            updatePropertyItemReq.setValue(clinicBasicCharge);
            updatePropertyItemReq.setPropertyKey(PropertyKey.CLINIC_BASIC_CHARGE);
            updatePropertyItemReq.setScopeId(clinicId);
            updatePropertyItemReq.setOperatorId(employeeId);
            propertyService.updatePropertyValueByKey(updatePropertyItemReq);

            if (clinicBasicCharge.getRefundCheck() == YesOrNo.YES) {
                ClinicBusinessPermissionItem clinicBusinessPermissionItemReq = new ClinicBusinessPermissionItem();
                clinicBusinessPermissionItemReq.setKey(ClinicConstants.BusinessPermissionKey.CHARGE_REFUND_CHECK);
                clinicBusinessPermissionItemReq.setBusinessId(String.format("%s:%s", clinicId, ClinicConstants.BusinessPermissionKey.CHARGE_REFUND_CHECK));
                ClinicBusinessPermissionValue permissionValue = new ClinicBusinessPermissionValue();
                permissionValue.setScopePath(ClinicConstants.BusinessPermissionScope.CLINIC);
                permissionValue.setScopeValues(Optional.ofNullable(updateChargeConfigReq.getRefundCheckEmployeeIds()).orElseGet(Collections::emptyList));
                clinicBusinessPermissionItemReq.setPermissionValues(Collections.singletonList(permissionValue));
                cisScClinicService.upsertBusinessPermission(chainId, clinicId, employeeId, clinicBusinessPermissionItemReq);
            }
        }
    }

    /**
     * 更新诊所的redis开关
     *
     * @return 是否更新成功
     */
    public boolean updateBranchClinicSendOrderConfig(UpdateChargeConfigReq updateChargeConfigReq, String clinicId, String employeeId) {

        WechatAutoPay wechatAutoPay = propertyService.getPropertyValueByKey(PropertyKey.WECHAT_AUTO_PAY_AUTO_SEND_ORDER_INFO_SWITCH, clinicId, WechatAutoPay.class);
        if (wechatAutoPay != null
                && (!org.apache.commons.lang3.StringUtils.equals(
                Optional.ofNullable(updateChargeConfigReq.getSendOrderInfoModes())
                        .orElse(Optional.ofNullable(wechatAutoPay.getSendOrderInfoModes()).orElse(new ArrayList<>()))
                        .stream()
                        .sorted(Comparator.comparing(Integer::intValue))
                        .map(String::valueOf)
                        .collect(Collectors.joining(",")),
                Optional.ofNullable(wechatAutoPay.getSendOrderInfoModes())
                        .orElse(Lists.newArrayList())
                        .stream()
                        .sorted(Comparator.comparing(Integer::intValue))
                        .map(String::valueOf)
                        .collect(Collectors.joining(",")))
                || !Objects.equals(wechatAutoPay.getDoctorCanPushChargeSheetSwitch(), updateChargeConfigReq.getDoctorCanPushChargeSheetSwitch())
        )) {
            WechatAutoPay wechatAutoPayUpdate = new WechatAutoPay();
            BeanUtils.copyProperties(wechatAutoPay, wechatAutoPayUpdate);
            wechatAutoPayUpdate.setSendOrderInfoModes(Optional.ofNullable(updateChargeConfigReq.getSendOrderInfoModes()).orElse(Optional.ofNullable(wechatAutoPay.getSendOrderInfoModes()).orElse(new ArrayList<>())));
            wechatAutoPayUpdate.setDoctorCanPushChargeSheetSwitch(updateChargeConfigReq.getDoctorCanPushChargeSheetSwitch());
            UpdatePropertyItemReq<WechatAutoPay> updatePropertyItemReq = new UpdatePropertyItemReq<>();
            updatePropertyItemReq.setValue(wechatAutoPayUpdate);
            updatePropertyItemReq.setPropertyKey(PropertyKey.WECHAT_AUTO_PAY_AUTO_SEND_ORDER_INFO_SWITCH);
            updatePropertyItemReq.setScopeId(clinicId);
            updatePropertyItemReq.setOperatorId(employeeId);
            propertyService.updatePropertyValueByKey(updatePropertyItemReq);
            return true;
        }
        return false;
    }

    public boolean updateClinicChargeSheetAutoClose(UpdateChargeConfigReq updateChargeConfigReq, String clinicId, String employeeId) {

        ChargeSheetAutoClose chargeSheetAutoClose = propertyService.getPropertyValueByKey(PropertyKey.CHARGE_SHEET_AUTO_CLOSED_SWITCH, clinicId, ChargeSheetAutoClose.class);
        if (chargeSheetAutoClose != null && (chargeSheetAutoClose.getAutoClosedSwitch() != updateChargeConfigReq.getAutoClosedSwitch()
                || MathUtils.wrapBigDecimalCompare(chargeSheetAutoClose.getAutoClosedTime(), updateChargeConfigReq.getAutoClosedTime()) != 0
                || chargeSheetAutoClose.getAutoClosedSwitchV2() != updateChargeConfigReq.getAutoClosedSwitchV2()
                || chargeSheetAutoClose.getAutoClosedTimeV2() != updateChargeConfigReq.getAutoClosedTimeV2())) {
            ChargeSheetAutoClose ChargeSheetAutoCloseUpdate = new ChargeSheetAutoClose();
            BeanUtils.copyProperties(chargeSheetAutoClose, ChargeSheetAutoCloseUpdate);
            ChargeSheetAutoCloseUpdate.setAutoClosedSwitch(updateChargeConfigReq.getAutoClosedSwitch());
            ChargeSheetAutoCloseUpdate.setAutoClosedTime(updateChargeConfigReq.getAutoClosedTime());
            ChargeSheetAutoCloseUpdate.setAutoClosedSwitchV2(updateChargeConfigReq.getAutoClosedSwitchV2());
            ChargeSheetAutoCloseUpdate.setAutoClosedTimeV2(updateChargeConfigReq.getAutoClosedTimeV2());
            UpdatePropertyItemReq<ChargeSheetAutoClose> updatePropertyItemReq = new UpdatePropertyItemReq<>();
            updatePropertyItemReq.setValue(ChargeSheetAutoCloseUpdate);
            updatePropertyItemReq.setPropertyKey(PropertyKey.CHARGE_SHEET_AUTO_CLOSED_SWITCH);
            updatePropertyItemReq.setScopeId(clinicId);
            updatePropertyItemReq.setOperatorId(employeeId);
            propertyService.updatePropertyValueByKey(updatePropertyItemReq);
            return true;
        }
        return false;
    }

    /**
     * 诊所是否开启了自动推送
     */
    public boolean isClinicEnableAutoSendOrder(String clinicId) {
        WechatAutoPay wechatAutoPay = propertyService.getPropertyValueByKey(PropertyKey.WECHAT_AUTO_PAY_AUTO_SEND_ORDER_INFO_SWITCH, clinicId, WechatAutoPay.class);
        if (wechatAutoPay != null && Optional.ofNullable(wechatAutoPay.getSendOrderInfoModes()).orElse(Lists.newArrayList()).contains(ChargeConfigDetailView.SelfPayPushOrderStatus.AUTO_PUSH_SELF_PAY)) {
            return true;
        }
        return false;
    }

    /**
     * 药房配置：总店的配置
     */
    public ChargeConfigDetailView getHeadClinicConfigDetail(String chainId, String clinicId, Integer isEnable) {
        ChargeConfigDetailView rsp = new ChargeConfigDetailView();
        ChargeCalculateConfig chargeCalculateConfig = chargeCalculateConfigRepository.findByChainIdAndClinicIdAndIsDeleted(chainId, chainId, 0);
        if (chargeCalculateConfig != null) {
            BeanUtils.copyProperties(chargeCalculateConfig, rsp);
        }

        ChargeConfigService chargeConfigService = (ChargeConfigService) AopContext.currentProxy();

        rsp.setChargePayModeConfigs(Optional.ofNullable(chargeConfigService.queryChargePayModeConfigViews(chainId))
                .orElse(new ArrayList<>())
                .stream()
                .filter(chargePayModeConfigView -> {
                    if (isEnable == null) {
                        return true;
                    }
                    return isEnable == chargePayModeConfigView.getIsEnable();
                }).collect(Collectors.toList()));

        getChargeSheetAutoClosedConfig(clinicId, rsp);
        return rsp;
    }

    /**
     * 只包含charge库的配置
     */
    @Cacheable(cacheNames = "cis_charge:charge-calculate-config", key = "#chainId")
    @Override
    public ChargeConfigDetailView getHeadClinicConfigDetailCache(String chainId) {
        ChargeConfigDetailView rsp = new ChargeConfigDetailView();
        ChargeCalculateConfig chargeCalculateConfig = chargeCalculateConfigRepository.findByChainIdAndClinicIdAndIsDeleted(chainId, chainId, 0);
        if (chargeCalculateConfig != null) {
            BeanUtils.copyProperties(chargeCalculateConfig, rsp);
        }
        return rsp;
    }



    @Override
    @Cacheable(cacheNames = "cis_charge:charge-pay-mode-config-view", key = "#chainId", unless = "#result == null")
    public List<ChargeConfigDetailView.ChargePayModeConfigView> queryChargePayModeConfigViews (String chainId) {
        return chargePayModeConfigMapper.findChargePayModeConfigViewByChainIdAndClinicId(chainId, chainId, null, null);
    }

    /**
     * 药房配置：需要人工确认
     */
    private void getBranchClinicSendOrderConfig(String clinicId, ChargeConfigDetailView rsp) {
        WechatAutoPay wechatAutoPay = propertyService.getPropertyValueByKey(PropertyKey.WECHAT_AUTO_PAY_AUTO_SEND_ORDER_INFO_SWITCH, clinicId, WechatAutoPay.class);
        if (wechatAutoPay != null) {
            rsp.setSendOrderInfoModes(wechatAutoPay.getSendOrderInfoModes());
            rsp.setDoctorCanPushChargeSheetSwitch(wechatAutoPay.getDoctorCanPushChargeSheetSwitch());
        }
    }

    /**
     * 自动关单配置
     */
    private void getChargeSheetAutoClosedConfig(String clinicId, ChargeConfigDetailView rsp) {
        ChargeSheetAutoClose chargeSheetAutoClose = propertyService.getPropertyValueByKey(PropertyKey.CHARGE_SHEET_AUTO_CLOSED_SWITCH, clinicId, ChargeSheetAutoClose.class);
        if (chargeSheetAutoClose != null) {
            rsp.setAutoClosedSwitch(chargeSheetAutoClose.getAutoClosedSwitch());
            rsp.setAutoClosedTime(chargeSheetAutoClose.getAutoClosedTime());
            rsp.setAutoClosedSwitchV2(chargeSheetAutoClose.getAutoClosedSwitchV2());
            rsp.setAutoClosedTimeV2(chargeSheetAutoClose.getAutoClosedTimeV2());
        }
    }


    @Transactional(readOnly = true)
    public ChargeConfigDetailView getBranchConfigDetail(String chainId) {
        ChargeConfigDetailView rsp = getBranchConfigDetail(chainId, chainId, false);
        rsp.setAutoSendOrderInfoSwitch(ChargeConfigDetailView.SelfPayPushOrderStatus.NOT_INIT);
        return rsp;
    }

    /**
     * 【收费台/RPC】子店/单点来拿的收费配置
     * 子店也会来拿的配置，子店拿的是总店配置
     */
    @Transactional(readOnly = true)
    public ChargeConfigDetailView getBranchConfigDetail(String chainId, String clinicId, boolean needAutoSendOrderInfoSwitch) {

        //2、从数据库中查询
        ChargeConfigDetailView rsp = getHeadClinicConfigDetail(chainId, chainId, 1);
        rsp.setAutoSendOrderInfoSwitch(ChargeConfigDetailView.SelfPayPushOrderStatus.NOT_INIT);

        ClinicBasicCharge clinicBasicCharge = getClinicBasicCharge(clinicId);
        if (clinicBasicCharge != null) {
            rsp.setDirectSaleEnable(clinicBasicCharge.getDirectSaleEnable());
            rsp.setRefundRestriction(clinicBasicCharge.getRefundRestriction());
            rsp.setRefundCheck(clinicBasicCharge.getRefundCheck());
            rsp.setWholeSheetOperateEnable(clinicBasicCharge.getWholeSheetOperateEnable());
        }
        rsp.setRefundCheckEmployees(getChargeRefundCheckEmployees(chainId, clinicId, rsp.getRefundCheck()));


        if (needAutoSendOrderInfoSwitch) {
            boolean canSupportSelfPay = mcService.canSupportSelfPay(chainId, clinicId);
            if (canSupportSelfPay) {
                getBranchClinicSendOrderConfig(clinicId, rsp);
            }
        }

        return rsp;
    }

    /**
     * 【业务场景配置中心】总店拿总店：药房配置
     * 确认收费从总店移到了子店，其他还是总店配置.子店只获取微信自助开关并展示
     */
    @Transactional(readOnly = true)
    public ChargeConfigDetailView getChainConfigDetail(String chainId, String clinicId, Integer isEnable, int clinicType, String viewMode) {
        ChargeConfigDetailView rsp = new ChargeConfigDetailView();
        /**药房配置：总店的配置*/
        if (clinicType == CisClinicType.CHAIN_HEAD_CLINIC
                || (clinicType == CisClinicType.INDEPENDENT_CLINIC && Objects.equals(CisJWTUtils.CIS_VIEW_MODE_NORMAL, viewMode))
                || Objects.equals(CisJWTUtils.CIS_VIEW_MODE_SINGLE, viewMode)) {
            rsp = getHeadClinicConfigDetail(chainId, chainId, isEnable);
        }

        //初始化
        rsp.setAutoSendOrderInfoSwitch(ChargeConfigDetailView.SelfPayPushOrderStatus.NOT_INIT);
        getChargeSheetAutoClosedConfig(clinicId, rsp);
        /**收费配置：需要人工确认*/
        if (clinicType == CisClinicType.CHAIN_BRANCH_CLINIC
                || (clinicType == CisClinicType.INDEPENDENT_CLINIC && Objects.equals(CisJWTUtils.CIS_VIEW_MODE_NORMAL, viewMode))
        ) {
            boolean canSupportSelfPay = mcService.canSupportSelfPay(chainId, clinicId);
            if (canSupportSelfPay) {
                getBranchClinicSendOrderConfig(clinicId, rsp);
            }
        }
        //获取零售开关设置
        ClinicBasicCharge clinicBasicCharge = getClinicBasicCharge(clinicId);
        if (clinicBasicCharge != null) {
            rsp.setDirectSaleEnable(clinicBasicCharge.getDirectSaleEnable());
            rsp.setRefundRestriction(clinicBasicCharge.getRefundRestriction());
            rsp.setRefundCheck(clinicBasicCharge.getRefundCheck());
            rsp.setWholeSheetOperateEnable(clinicBasicCharge.getWholeSheetOperateEnable());
        }
        rsp.setRefundCheckEmployees(getChargeRefundCheckEmployees(chainId, clinicId, rsp.getRefundCheck()));
        return rsp;
    }

    public ClinicBasicCharge getClinicBasicCharge(String clinicId) {
        return propertyService.getPropertyValueByKey(PropertyKey.CLINIC_BASIC_CHARGE, clinicId, ClinicBasicCharge.class);
    }

    public List<String> getChargeRefundCheckEmployeeIds(String chainId, String clinicId, int refundCheck) {
        //if (refundCheck != YesOrNo.YES) {
        //    return Collections.emptyList();
        //}
        // 查询退费审核权限
        return cisScClinicService.batchQueryBusinessPermissionValues(chainId, clinicId, Collections.singletonList(ClinicConstants.BusinessPermissionKey.CHARGE_REFUND_CHECK), Collections.singletonList(String.format("%s:%s", clinicId, ClinicConstants.BusinessPermissionKey.CHARGE_REFUND_CHECK)))
                .stream()
                .map(ClinicBusinessPermissionValue::getScopeValues)
                .filter(org.apache.commons.collections.CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
    }

    public List<Employee> getChargeRefundCheckEmployees(String chainId, String clinicId, int refundCheck) {
        return cisScClinicService.queryEmployeeListById(chainId, getChargeRefundCheckEmployeeIds(chainId, clinicId, refundCheck), YesOrNo.YES);
    }

    @Override
    public Map<Long, ChargePayModeConfigSimple> getChargePayModeConfigSimpleByChainId(String chainId) {
        Map<Long, ChargePayModeConfigSimple> chargePayModeConfigSimpleMap = new HashMap<>();
        List<ChargePayModeConfigSimple> chargePayModeConfigSimples = getChargePayModeConfigSimpleListByChainId(chainId);

        if (!CollectionUtils.isEmpty(chargePayModeConfigSimples)) {
            chargePayModeConfigSimpleMap = chargePayModeConfigSimples.stream().collect(Collectors.toMap(ChargePayModeConfigSimple::getId, Function.identity(), (a, b) -> a));
        }

        return chargePayModeConfigSimpleMap;
    }

    @Cacheable(cacheNames = "cis_charge:charge-pay-mode", key = "#chainId")
    public List<ChargePayModeConfigSimple> getChargePayModeConfigSimpleListByChainId(String chainId) {
        if (StringUtils.isEmpty(chainId)) {
            return new ArrayList<>();
        }

        //先查询
        List<ChargePayModeConfigSimple> chargePayModeConfigSimples = new ArrayList<>();
        List<Long> payModeIds = chargePayModeConfigMapper.findChargePayModeIdsByChainId(chainId);

        if (CollectionUtils.isEmpty(payModeIds)) {
            return new ArrayList<>();
        }

        List<ChargePayModeConfig> payModeConfigs = chargePayModeConfigRepository.findAllByIdIn(payModeIds);

        if (CollectionUtils.isEmpty(payModeConfigs)) {
            return new ArrayList<>();
        }


        chargePayModeConfigSimples.addAll(payModeConfigs.stream().map(payModeConfig -> {
            ChargePayModeConfigSimple payModeConfigSimple = new ChargePayModeConfigSimple();
            BeanUtils.copyProperties(payModeConfig, payModeConfigSimple);
            return payModeConfigSimple;
        }).collect(Collectors.toList()));
        chargePayModeConfigSimples.addAll(findSystemWithConditionalPayModes());

        return new ArrayList<>(chargePayModeConfigSimples.stream()
                .collect(Collectors.toMap(ChargePayModeConfigSimple::getId, Function.identity(), (a, b) -> a))
                .values());
    }

    /**
     * 查询内置的需要条件才开启的支付方式列表
     */
    private List<ChargePayModeConfigSimple> findSystemWithConditionalPayModes() {

        if (SYSTEM_WITH_CONDITIONAL_PAY_MODES != null) {
            return SYSTEM_WITH_CONDITIONAL_PAY_MODES;
        }

        List<ChargePayModeConfig> payModeConfigs = chargePayModeConfigRepository.findAllSystemWithConditionalPayModes();

        if (CollectionUtils.isEmpty(payModeConfigs)) {
            return new ArrayList<>();
        }

        SYSTEM_WITH_CONDITIONAL_PAY_MODES = payModeConfigs.stream().map(payModeConfig -> {
            ChargePayModeConfigSimple payModeConfigSimple = new ChargePayModeConfigSimple();
            BeanUtils.copyProperties(payModeConfig, payModeConfigSimple);
            return payModeConfigSimple;
        }).collect(Collectors.toList());

        return SYSTEM_WITH_CONDITIONAL_PAY_MODES;
    }

    public SendOrderConfigResult getSendOrderConfig(String chainId) {
        ChargeCalculateConfig chargeCalculateConfig = chargeCalculateConfigRepository.findByChainIdAndClinicIdAndIsDeleted(chainId, chainId, 0);
        SendOrderConfigResult sendOrderConfigResult = new SendOrderConfigResult();
        if (chargeCalculateConfig == null) {
            return sendOrderConfigResult;
        }
        sendOrderConfigResult.setAutoSendOrderInfoSwitch(chargeCalculateConfig.getAutoSendOrderInfoSwitch());
        sendOrderConfigResult.setAutoPaySwitch(
                (sendOrderConfigResult.getAutoSendOrderInfoSwitch() == 1 && sendOrderConfigResult.getSellerSendOrderInfoSwitch() == 1) ? 1 : 0
        );
        return sendOrderConfigResult;
    }

    /**
     * 查询连锁所有的支付方式（包含已删除的）
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public List<ChargeConfigDetailView.ChargePayModeConfigView> getChainPayMode(String chainId) {
        return chargePayModeConfigMapper.findChargePayModeConfigViewByChainIdAndClinicId(chainId, chainId, null, 1);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public PayModeDetailRsp getPayModeDetailById(String chainId, Long payModeId) {
        ChargeConfigDetailView.ChargePayModeConfigView config = chargePayModeConfigMapper.findChargeChainPayModeConfigByChainIdAndPayModeId(chainId, chainId, payModeId);
        if (config == null) {
            throw new NotFoundException("没有找到支付方式");
        }

        PayModeDetailRsp payModeDetailRsp = new PayModeDetailRsp();
        payModeDetailRsp.setName(config.getName());
        payModeDetailRsp.setPayModeId(config.getPayModeId());
        payModeDetailRsp.setType(config.getType());
        if (config.getType() == Constants.ChargePayModeConfigType.THIRD_PARTY_COMMON_PAY && !TextUtils.isEmpty(config.getConfig())) {
            payModeDetailRsp.setThirdPartyCommonPayConfig(cn.abcyun.cis.commons.util.JsonUtils.readValue(config.getConfig(), ThirdPartyCommonPayConfig.class));
        }
        return payModeDetailRsp;
    }
}
