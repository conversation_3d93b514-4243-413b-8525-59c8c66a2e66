package cn.abcyun.cis.charge.service;

import cn.abcyun.bis.rpc.sdk.cis.model.goodslocking.GoodsLockingForm;
import cn.abcyun.cis.charge.model.ChargeSheetLockingTask;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
public class ChargeSheetLockingTaskService {

    public List<GoodsLockingForm> doMergelockingTask(List<ChargeSheetLockingTask> lockingTasks) {
        if (CollectionUtils.isEmpty(lockingTasks)) {
            return null;
        }

       // lockingTasks.stream().flatMap(lockingTask -> lockingTask.getBatchInfo().getLockingForms().stream())


        return null;
    }
}
