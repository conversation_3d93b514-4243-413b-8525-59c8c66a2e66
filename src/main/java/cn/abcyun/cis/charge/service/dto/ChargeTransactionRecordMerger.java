package cn.abcyun.cis.charge.service.dto;

import cn.abcyun.cis.charge.model.ChargeTransactionRecord;
import cn.abcyun.cis.charge.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
public class ChargeTransactionRecordMerger {

    public static List<ChargeTransactionRecord> merge(List<ChargeTransactionRecord> records, List<cn.abcyun.bis.rpc.sdk.cis.model.charge.record.ChargeTransactionRecord> rpcRecords) {

        List<ChargeTransactionRecord> result = Optional.ofNullable(records).orElse(new ArrayList<>())
                .stream()
                .map(record -> JsonUtils.readValue(JsonUtils.dump(record), ChargeTransactionRecord.class))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(rpcRecords)) {
            return result;
        }

        List<String> existedIds = records.stream()
                .map(ChargeTransactionRecord::getId)
                .collect(Collectors.toList());

        rpcRecords.removeIf(rpcRecord -> existedIds.contains(rpcRecord.getId()));

        result.addAll(rpcRecords.stream()
                .map(rpcRecord -> JsonUtils.readValue(JsonUtils.dump(rpcRecord), ChargeTransactionRecord.class))
                .collect(Collectors.toList()));
        result = result.stream()
                .sorted(Comparator.comparing(ChargeTransactionRecord::getCreated)
                        .thenComparing(ChargeTransactionRecord::getId))
                .collect(Collectors.toList());
        return result;
    }

}
