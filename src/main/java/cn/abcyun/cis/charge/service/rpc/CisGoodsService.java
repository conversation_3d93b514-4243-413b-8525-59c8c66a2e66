package cn.abcyun.cis.charge.service.rpc;

import cn.abcyun.bis.rpc.sdk.cis.client.AbcCisGoodsFeignClient;
import cn.abcyun.bis.rpc.sdk.cis.client.AbcCisScGoodsFeignClient;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.QueryVendorClinicGoodsReq;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.QueryVendorClinicGoodsRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.config.GoodsConfigView;
import cn.abcyun.cis.charge.processor.provider.CisGoodsProvider;
import cn.abcyun.cis.charge.util.GoodsLockingUtils;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.exception.ServiceInternalException;
import cn.abcyun.cis.core.util.FeignClientRpcTemplate;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import feign.RetryableException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class CisGoodsService implements CisGoodsProvider {

    @Autowired
    private AbcCisGoodsFeignClient client;

    @Autowired
    private AbcCisScGoodsFeignClient goodsFeignClient;

    /**
     * Goods的配置本机缓存下，目前没多少个医院 缓存最多100个够了
     * 1分钟
     */
    private Cache<String, GoodsConfigView> clinicIdToGoodsConfigViewCache = Caffeine.newBuilder()
            .maximumSize(100)
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .build();

    public List<QueryVendorClinicGoodsRsp.VendorGoodsList> queryVendorClinicGoods(String clinicId, List<QueryVendorClinicGoodsReq.VendorGoodsIds> vendorGoodsIds, boolean isWithStock) throws ServiceInternalException {
        List<QueryVendorClinicGoodsRsp.VendorGoodsList> vendorGoodsList = new ArrayList<>();
        if (StringUtils.isEmpty(clinicId) || CollectionUtils.isEmpty(vendorGoodsIds)) {
            return vendorGoodsList;
        }

        QueryVendorClinicGoodsReq req = new QueryVendorClinicGoodsReq();
        req.setClinicId(clinicId);
        req.setList(vendorGoodsIds);
        req.setWithStock(isWithStock ? 1 : 0);

        QueryVendorClinicGoodsRsp rsp = FeignClientRpcTemplate.dealRpcClientMethod("queryVendorClinicGoods", () -> client.queryVendorClinicGoods(req), req);
        return Optional.ofNullable(rsp).map(QueryVendorClinicGoodsRsp::getList).orElse(new ArrayList<>());
    }

    /**
     * GoodsRpc接口 获取诊所配置
     *
     * @param clinicId 门店id
     * @return 响应结果
     */
    @Override
    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public GoodsConfigView getGoodsConfig(String clinicId) {
        if (org.apache.commons.lang3.StringUtils.isBlank(clinicId)) {
            throw new ParamRequiredException("clinicId");
        }
        return clinicIdToGoodsConfigViewCache.get(clinicId, this::getGoodsConfigNoCache);
    }

    /**
     * GoodsRpc接口 获取诊所配置 没有缓存
     *
     * @param clinicId 门店id
     * @return 响应结果
     */
    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public GoodsConfigView getGoodsConfigNoCache(String clinicId) {
        return FeignClientRpcTemplate.dealRpcClientMethod("getGoodsConfig",
                () -> goodsFeignClient.getGoodsConfig(clinicId),
                clinicId);
    }

    public GoodsConfigView openClinicBatchLock(String chainId, String clinicId, Integer sceneType, Integer lockBatch) {

        GoodsConfigView goodsConfigView = FeignClientRpcTemplate.dealRpcClientMethod("openClinicBatchLock",
                () -> goodsFeignClient.openClinicBatchLock(chainId, clinicId, sceneType, lockBatch),
                chainId, clinicId, sceneType, lockBatch);

        return goodsConfigView;
    }


    @Override
    public boolean isLockByBatch(GoodsConfigView getGoodsConfig) {
        return GoodsLockingUtils.isLockByBatch(getGoodsConfig);
    }
}
