package cn.abcyun.cis.charge.service;

import cn.abcyun.bis.rpc.sdk.cis.model.charge.ChargeStatus;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Employee;
import cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientInfo;
import cn.abcyun.bis.rpc.sdk.cis.model.search.CdssQLSearchResultItem;
import cn.abcyun.bis.rpc.sdk.cis.model.search.SearchResultRsp;
import cn.abcyun.cis.charge.amqp.model.ChargeCoPharmacyOrderMessage;
import cn.abcyun.cis.charge.api.model.ChargeCooperationOrderAbstractView;
import cn.abcyun.cis.charge.api.model.ChargeCooperationOrderView;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.factory.ChargeCooperationOrderFactory;
import cn.abcyun.cis.charge.mapper.ChargeCooperationOrderMapper;
import cn.abcyun.cis.charge.model.ChargeCooperationOrder;
import cn.abcyun.cis.charge.model.ChargeCooperationOrderItem;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.processor.provider.ChargeCooperationOrderProvider;
import cn.abcyun.cis.charge.repository.ChargeCooperationOrderItemRepository;
import cn.abcyun.cis.charge.repository.ChargeCooperationOrderRepository;
import cn.abcyun.cis.charge.service.rpc.CisSearchService;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.cis.core.db.UseReadOnlyDB;
import cn.abcyun.cis.core.util.Validators;
import cn.abcyun.common.model.AbcListPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Service
@Slf4j
@RequiredArgsConstructor
public class ChargeCooperationOrderService implements ChargeCooperationOrderProvider {

    private final ChargeCooperationOrderRepository chargeCooperationOrderRepository;
    private final ChargeCooperationOrderItemRepository chargeCooperationOrderItemRepository;
    private final ChargeCooperationOrderMapper chargeCooperationOrderMapper;
    private final ClinicService clinicService;
    private final TobMessageService tobMessageService;
    private final CisSearchService cisSearchService;


    public List<ChargeCooperationOrder> listChargeCooperationOrderByClinicIdAndSourceSheetId(String clinicId, String sourceSheetId) {

        if (StringUtils.isAnyBlank(clinicId, sourceSheetId)) {
            log.info("clinicId or sourceSheetId is empty, clinicId: {}, sourceSheetId: {}", clinicId, sourceSheetId);
            return new ArrayList<>();
        }

        List<ChargeCooperationOrder> chargeCooperationOrders = chargeCooperationOrderRepository.findAllByClinicIdAndSourceSheetIdAndIsDeleted(clinicId, sourceSheetId, 0);

        if (CollectionUtils.isEmpty(chargeCooperationOrders)) {
            return new ArrayList<>();
        }

        bindChargeCooperationOrderItems(chargeCooperationOrders);
        return chargeCooperationOrders;
    }

    public ChargeCooperationOrder findById(String id, String clinicId) {
        return findById(id, clinicId, true);
    }

    public ChargeCooperationOrder findById(String id, String clinicId, boolean withItems) {
        ChargeCooperationOrder chargeCooperationOrder = chargeCooperationOrderRepository.findByIdAndClinicIdAndIsDeleted(id, clinicId, 0);

        if (Objects.isNull(chargeCooperationOrder)) {
            return null;
        }

        if (withItems) {
            bindChargeCooperationOrderItems(Collections.singletonList(chargeCooperationOrder));
        }
        return chargeCooperationOrder;
    }

    public ChargeCooperationOrder findByChargeSheetId(String chargeSheetId, String clinicId) {
        return findByChargeSheetId(chargeSheetId, clinicId, true);
    }

    public ChargeCooperationOrder findByChargeSheetId(String chargeSheetId, String clinicId, boolean withItems) {

        ChargeCooperationOrder chargeCooperationOrder = chargeCooperationOrderRepository.findByRelateChargeSheetIdAndClinicIdAndIsDeleted(chargeSheetId, clinicId, 0);

        if (Objects.isNull(chargeCooperationOrder)) {
            return null;
        }

        if (withItems) {
            bindChargeCooperationOrderItems(Collections.singletonList(chargeCooperationOrder));
        }

        return chargeCooperationOrder;
    }

    private void bindChargeCooperationOrderItems(List<ChargeCooperationOrder> chargeCooperationOrders) {
        if (CollectionUtils.isEmpty(chargeCooperationOrders)) {
            return;
        }

        Set<String> chargeCooperationOrderIds = chargeCooperationOrders.stream()
                .map(ChargeCooperationOrder::getId)
                .collect(Collectors.toSet());


        List<ChargeCooperationOrderItem> orderItems = chargeCooperationOrderItemRepository.findAllByOrderIdInAndIsDeleted(chargeCooperationOrderIds, 0);

        Map<String, List<ChargeCooperationOrderItem>> orderIdOrderItemMap = Optional.ofNullable(orderItems)
                .orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.groupingBy(ChargeCooperationOrderItem::getOrderId));

        chargeCooperationOrders.forEach(chargeCooperationOrder -> chargeCooperationOrder.setOrderItems(orderIdOrderItemMap.getOrDefault(chargeCooperationOrder.getId(), new ArrayList<>())));
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public AbcListPage<ChargeCooperationOrderAbstractView> pageListChargeCooperationOrders(String chainId,
                                                                                           String clinicId,
                                                                                           String keyword,
                                                                                           int offset,
                                                                                           int limit,
                                                                                           Instant beginDate,
                                                                                           Instant endDate) {

        AbcListPage<ChargeCooperationOrderAbstractView> rsp = new AbcListPage<>();
        rsp.setRows(new ArrayList<>());
        rsp.setTotal(0);
        rsp.setLimit(limit);
        rsp.setOffset(offset);
        rsp.setKeyword(keyword);

        List<ChargeCooperationOrderAbstractView> results = new ArrayList<>();
        int totalCount = 0;
        if (TextUtils.isEmpty(keyword)) {
            results = chargeCooperationOrderMapper.pageListChargeCooperationOrders(offset, limit, clinicId, beginDate, endDate);
            totalCount = chargeCooperationOrderMapper.countChargeCooperationOrders(clinicId, beginDate, endDate);
        } else {
            SearchResultRsp searchResultRsp = cisSearchService.searchChargeCooperationOrder(chainId, clinicId, keyword);
            if (Objects.nonNull(searchResultRsp)) {
                totalCount = searchResultRsp.getTotal();
                if (!org.springframework.util.CollectionUtils.isEmpty(searchResultRsp.getHits())) {
                    List<String> ids = searchResultRsp.getHits()
                            .stream()
                            .map(jsonNode -> JsonUtils.readValue(jsonNode, CdssQLSearchResultItem.class))
                            .filter(Objects::nonNull)
                            .map(CdssQLSearchResultItem::getId)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.toList());
                    if (!org.springframework.util.CollectionUtils.isEmpty(ids)) {
                        results = chargeCooperationOrderMapper.listChargeCooperationOrdersByIds(clinicId, ids);
                        Map<String, Integer> idIndexMap = IntStream.range(0, ids.size()).boxed().collect(Collectors.toMap(ids::get, Function.identity(), (a, b) -> a));
                        results.sort((a, b) -> ObjectUtils.compare(idIndexMap.getOrDefault(a.getId(), 0), idIndexMap.getOrDefault(b.getId(), 0)));
                    }
                }
            }
        }

        if (!CollectionUtils.isEmpty(results)) {
            fillDoctorName(results);
        }

        rsp.setRows(results);
        rsp.setTotal(totalCount);
        return rsp;
    }

    public void fillDoctorName(List<ChargeCooperationOrderAbstractView> results) {
        // 获取results中不为空的sourceDoctorId
        Set<String> doctorIds = results.stream()
                .filter(result -> StringUtils.isNotEmpty(result.getSourceDoctorId()))
                .filter(result -> ChargeCooperationOrder.Status.unchargedStatuses().contains(result.getStatus()))
                .map(ChargeCooperationOrderAbstractView::getSourceDoctorId)
                .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(doctorIds)) {
            return;
        }

        Map<String, Employee> employeeMap = clinicService.findEmployeeByIds(doctorIds);
        if (employeeMap == null || employeeMap.isEmpty()) {
            return;
        }

        List<String> doctorNameChangedIds = new ArrayList<>();

        results.forEach(result -> {
            Employee employee = employeeMap.get(result.getSourceDoctorId());
            if (employee != null && !TextUtils.equals(result.getSourceDoctorName(), employee.getName())) {
                result.setSourceDoctorName(employee.getName());
                doctorNameChangedIds.add(result.getId());
            }
        });

        if (!CollectionUtils.isEmpty(doctorNameChangedIds)) {
            updateDoctorName(doctorNameChangedIds, employeeMap);
        }
    }

    @Async
    public void updateDoctorName(List<String> ids, Map<String, Employee> employeeMap) {
        log.info("updateDoctorName ids: {}", ids);
        List<ChargeCooperationOrder> orders = chargeCooperationOrderRepository.findAllByIdIn(ids);
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }

        orders.forEach(order -> {
            Employee employee = employeeMap.get(order.getSourceDoctorId());
            if (employee != null) {
                order.setSourceDoctorName(employee.getName());
            }
        });

        chargeCooperationOrderRepository.saveAll(orders);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public ChargeCooperationOrderView getChargeCooperationOrderViewById(String id, String clinicId) {

        ChargeCooperationOrder chargeCooperationOrder = findById(id, clinicId);

        return ChargeCooperationOrderView.of(chargeCooperationOrder);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public ChargeCooperationOrderView getChargeCooperationOrderViewByChargeSheetId(String chargeSheetId, String clinicId) {

        ChargeCooperationOrder chargeCooperationOrder = findByChargeSheetId(chargeSheetId, clinicId);

        return ChargeCooperationOrderView.of(chargeCooperationOrder);
    }


    public void insertOrUpdateChargeCooperationOrderForChargeCoPharmacyOrderMessage(ChargeCoPharmacyOrderMessage message, int sourceType) {

        if (Objects.isNull(message)) {
            log.info("message is null");
            return;
        }

        Validators.validateEntity(message);

        List<ChargeCooperationOrder> chargeCooperationOrders = listChargeCooperationOrderByClinicIdAndSourceSheetId(message.getClinicId(), message.getSourceSheetId());

        //chargeCooperationOrders为被修改过的list，已经把删除的内容全部移除掉了
        ChargeCooperationOrderFactory.insertOrUpdateChargeCooperationOrder(message,
                sourceType,
                chargeCooperationOrders,
                chargeCooperationOrderRepository,
                chargeCooperationOrderItemRepository);

        if (CollectionUtils.isNotEmpty(chargeCooperationOrders)) {
            chargeCooperationOrderRepository.saveAll(chargeCooperationOrders);

            List<ChargeCooperationOrderItem> orderItems = chargeCooperationOrders.stream()
                    .flatMap(chargeCooperationOrder -> Optional.ofNullable(chargeCooperationOrder).map(ChargeCooperationOrder::getOrderItems).orElse(new ArrayList<>()).stream())
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(orderItems)) {
                chargeCooperationOrderItemRepository.saveAll(orderItems);
            }

            tobMessageService.pushChargeCooperationTodoMessage(message.getChainId(), message.getClinicId());
        }

    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public int findUnchargedCount(String chainId, String clinicId, Instant startTime, Instant endTime) {
        return chargeCooperationOrderRepository.getUnchargedTodoCount(chainId, clinicId, startTime, endTime);
    }

    public List<ChargeCooperationOrder> queryOrGenerateChargeCooperationOrdersForCoPharmacyOutpatientSheet(ChargeCoPharmacyOrderMessage chargeCoPharmacyOrderMessage) {

        if (Objects.isNull(chargeCoPharmacyOrderMessage)) {
            return new ArrayList<>();
        }

        Validators.validateEntity(chargeCoPharmacyOrderMessage);


        List<ChargeCooperationOrder> chargeCooperationOrders = listChargeCooperationOrderByClinicIdAndSourceSheetId(chargeCoPharmacyOrderMessage.getClinicId(), chargeCoPharmacyOrderMessage.getSourceSheetId());

        List<ChargeCooperationOrder> chargedOrders = Optional.ofNullable(chargeCooperationOrders).orElse(new ArrayList<>())
                .stream()
                .filter(chargeCooperationOrder -> chargeCooperationOrder.getStatus() != ChargeCooperationOrder.Status.UNCHARGED && chargeCooperationOrder.getStatus() != ChargeCooperationOrder.Status.CLOSED)
                .collect(Collectors.toList());

        ChargeCooperationOrderFactory.insertOrUpdateChargeCooperationOrderCore(chargeCoPharmacyOrderMessage, ChargeCooperationOrder.SourceType.OUTPATIENT, chargeCooperationOrders);

        List<ChargeCooperationOrder> allChargeCooperationOrders = new ArrayList<>();
        allChargeCooperationOrders.addAll(chargedOrders);
        allChargeCooperationOrders.addAll(chargeCooperationOrders);

        return allChargeCooperationOrders;
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public List<ChargeStatus> queryChargeStatusBatch(String clinicId, List<String> patientOrderIds) {

        if (StringUtils.isEmpty(clinicId)) {
            return new ArrayList<>();
        }

        if (CollectionUtils.isEmpty(patientOrderIds)) {
            return new ArrayList<>();
        }

        List<ChargeStatus> chargeStatusList = chargeCooperationOrderMapper.findChargeSheetStatusBatch(clinicId, patientOrderIds);

        Map<String, ChargeStatus> chargeStatusMap = chargeStatusList.stream().collect(Collectors.toMap(
                ChargeStatus::getPatientOrderId,
                Function.identity(),
                (a, b) -> a.getStatus() < b.getStatus() ? a : b)
        );

        return patientOrderIds.stream().map(patientOrderId -> {
            ChargeStatus chargeStatus = chargeStatusMap.getOrDefault(patientOrderId, null);
            if (chargeStatus == null) {
                chargeStatus = new ChargeStatus();
                chargeStatus.setId(patientOrderId);
                chargeStatus.setPatientOrderId(patientOrderId);
                chargeStatus.setStatus(Constants.ChargeSheetStatus.UNCHARGED);
            }
            return chargeStatus;
        }).collect(Collectors.toList());
    }

    @Override
    public void updateChargeCooperationOrderStatus(ChargeSheet chargeSheet, String operatorId) {
        if (Objects.isNull(chargeSheet)) {
            return;
        }

        int chargeSheetStatus = chargeSheet.getStatus();
        String chargeSheetId = chargeSheet.getId();
        String clinicId = chargeSheet.getClinicId();

        ChargeCooperationOrder chargeCooperationOrder = chargeCooperationOrderRepository.findByRelateChargeSheetIdAndClinicIdAndIsDeleted(chargeSheetId, clinicId, 0);

        if (Objects.isNull(chargeCooperationOrder)) {
            throw new NotFoundException();
        }
        chargeCooperationOrder.setStatus(ChargeCooperationOrder.convertStatusFromChargeSheetStatus(chargeSheetStatus, chargeSheet.getIsClosed(), CollectionUtils.isNotEmpty(chargeSheet.getChargeTransactions())))
                .setLastModified(Instant.now())
                .setLastModifiedBy(operatorId);

        tobMessageService.pushChargeCooperationTodoMessage(chargeCooperationOrder.getChainId(), clinicId);
    }

    /**
     * 重置订单上的关联收费单id为最后一条收费单的信息
     *
     * @param lastChargeSheet 最后一个收费单
     */
    public void resetCooperationOrderRelateChargeSheetInfoFromLastChargeSheet(String cooperationOrderId, ChargeSheet lastChargeSheet, String operatorId) {

        if (StringUtils.isEmpty(cooperationOrderId)) {
            return;
        }

        String relateChargeSheetId, relatePatientOrderId;
        int status, extractStatus;
        if (Objects.isNull(lastChargeSheet)) {
            relateChargeSheetId = null;
            relatePatientOrderId = null;
            status = ChargeCooperationOrder.Status.UNCHARGED;
            extractStatus = ChargeCooperationOrder.ExtractStatus.NON_EXTRACT;
        } else {
            relateChargeSheetId = lastChargeSheet.getId();
            relatePatientOrderId = lastChargeSheet.getPatientOrderId();
            status = ChargeCooperationOrder.convertStatusFromChargeSheetStatus(lastChargeSheet.getStatus(), lastChargeSheet.getIsClosed(), CollectionUtils.isNotEmpty(lastChargeSheet.getChargeTransactions()));
            extractStatus = ChargeCooperationOrder.ExtractStatus.EXTRACTED;
        }


        chargeCooperationOrderRepository.updateRelateChargeSheetInfo(cooperationOrderId,
                relateChargeSheetId,
                relatePatientOrderId,
                status,
                extractStatus,
                operatorId);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateOrderPatientInfoBySourcePatientId(String chainId, String sourcePatientId, PatientInfo patientInfo) {

        if (StringUtils.isAnyBlank(chainId, sourcePatientId) || Objects.isNull(patientInfo)) {
            return;
        }

        String patientInfoStr = JsonUtils.dump(patientInfo);

        chargeCooperationOrderRepository.updatePatientInfo(chainId, sourcePatientId, patientInfoStr);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateOrderPatientIdByPatientMerge(String chainId, List<String> oldSourcePatientIds, String mergedPatientId, PatientInfo mergedPatientInfo) {

        String patientInfoStr = JsonUtils.dump(mergedPatientInfo);

        chargeCooperationOrderRepository.updateOrderPatientIdByPatientMerge(chainId, oldSourcePatientIds, mergedPatientId, patientInfoStr);
    }
}
