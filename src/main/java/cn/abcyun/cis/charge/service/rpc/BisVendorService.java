package cn.abcyun.cis.charge.service.rpc;

import cn.abcyun.bis.rpc.sdk.bis.model.vendor.BusinessScopeListRsp;
import cn.abcyun.bis.rpc.sdk.bis.model.vendor.BusinessScopeVO;
import cn.abcyun.bis.rpc.sdk.bis.model.vendor.mall.AirPharmacyApplyReq;
import cn.abcyun.bis.rpc.sdk.bis.model.vendor.mall.AirPharmacyApplyVo;
import cn.abcyun.cis.commons.exception.ServiceInternalException;
import cn.abcyun.cis.core.util.FeignClientRpcTemplate;
import cn.abcyun.region.rpc.sdk.client.bis.AbcBisVendorFeignClient;
import feign.RetryableException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class BisVendorService {

    @Autowired
    private AbcBisVendorFeignClient client;


    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public List<BusinessScopeVO> listBusinessScopeTreeByExistAvailableOrgan(String provinceId, String cityId, Integer isOnlyInner) throws ServiceInternalException {
        BusinessScopeListRsp businessScopeListRsp = FeignClientRpcTemplate.dealRpcClientMethod("listBusinessScopeTreeByExistAvailableOrgan",
                () -> client.listBusinessScopeTreeByExistAvailableOrgan(provinceId, cityId, isOnlyInner), provinceId, cityId, isOnlyInner);
        return Optional.ofNullable(businessScopeListRsp).map(BusinessScopeListRsp::getBusinessScopes).orElse(new ArrayList<>());

    }

    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public List<BusinessScopeVO> listMedicineStateScopeByType() {
        return listBusinessScopeByType(BusinessScopeVO.BusinessScopeType.MEDICINE_STATE);
    }

    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    private List<BusinessScopeVO> listBusinessScopeByType(int type) {
        BusinessScopeListRsp businessScopeListRsp = FeignClientRpcTemplate.dealRpcClientMethod("listBusinessScopeByType",
                () -> client.listBusinessScopeByType(type), type);

        return Optional.ofNullable(businessScopeListRsp).map(BusinessScopeListRsp::getBusinessScopes).orElse(new ArrayList<>());
    }

    public String createAirPharmacyApply(AirPharmacyApplyReq req) {

       return FeignClientRpcTemplate.dealRpcClientMethod("createAirPharmacyApply",
                () -> client.create(req), req);

    }

    public AirPharmacyApplyVo getAirPharmacyApply(Long chainId, Long clinicId) {

        return FeignClientRpcTemplate.dealRpcClientMethod("getAirPharmacyApply",
                () -> client.getAirPharmacyApply(clinicId, chainId), clinicId, chainId);

    }

}
