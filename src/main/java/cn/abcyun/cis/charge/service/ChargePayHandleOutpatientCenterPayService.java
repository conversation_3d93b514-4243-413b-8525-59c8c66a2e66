package cn.abcyun.cis.charge.service;

import cn.abcyun.bis.rpc.sdk.cis.model.pay.shebao.PayForShebaoReq;
import cn.abcyun.cis.charge.base.*;
import cn.abcyun.cis.charge.model.ChargeTransaction;
import cn.abcyun.cis.charge.processor.ChargePayRefundInfo;
import cn.abcyun.cis.charge.rpc.client.ShebaoClient;
import cn.abcyun.cis.charge.service.rpc.CisScClinicService;
import cn.abcyun.cis.charge.service.rpc.CisShebaoService;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.common.log.marker.AbcLogMarker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 沈阳诊间支付
 */
@Service
@Slf4j
public class ChargePayHandleOutpatientCenterPayService extends ChargeShebaoPayHandleAbstractService {

    @Autowired
    public ChargePayHandleOutpatientCenterPayService(ChargeAbnormalTransactionService chargeAbnormalTransactionService,
                                                     ChargePayTransactionService chargePayTransactionService,
                                                     ChargeSheetService chargeSheetService,
                                                     EmployeeService employeeService,
                                                     PatientService patientService,
                                                     ShebaoClient shebaoClient,
                                                     CisScClinicService scClinicService,
                                                     CisShebaoService cisShebaoService) {
        super(chargeSheetService, chargePayTransactionService, chargeAbnormalTransactionService, employeeService, patientService, shebaoClient, scClinicService, cisShebaoService);
    }


    @Override
    public PayModeInfo getPayModeInfo() {
        return InnerPayModes.getPayModeByName(InnerPayModes.InnerPayModeName.OUTPATIENT_CENTER);
    }

    @Override
    public int getShebaoPayMethod(int paySubMode) {
        return PayForShebaoReq.PayMethod.OUTPATIENT_CENTER_PAY;
    }

    protected String checkBeforeRefundAndReturn(ChargePayRefundInfo refundInfo) {
        //校验退费金额是否与收费金额一致
        BigDecimal refundFee = refundInfo.getRefundFee().abs();

        List<ChargeTransaction> payChargeTransactions = checkCanRefundAndReturnPayTransactions(refundInfo.getChargeTransactions(), refundFee);

        return payChargeTransactions
                .stream()
                .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.OUTPATIENT_CENTER_PAY
                        && chargeTransaction.getPaySubMode() == Constants.ChargePaySubMode.OUTPATIENT_CENTER_PAY_INSURANCE)
                .findFirst()
                .map(ChargeTransaction::getThirdPartyPayTransactionId)
                .orElse(null);
    }

    private static List<ChargeTransaction> filterOutpatientCenterPayTransactions(List<ChargeTransaction> allChargeTransactions) {
        return Optional.ofNullable(allChargeTransactions)
                .orElse(new ArrayList<>())
                .stream()
                .sorted((a, b) -> -ObjectUtils.compare(a.getCreated(), b.getCreated()))
                .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.OUTPATIENT_CENTER_PAY)
                .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                .filter(chargeTransaction -> chargeTransaction.getIsDeleted() == 0)
                .filter(chargeTransaction -> TextUtils.isEmpty(chargeTransaction.getAssociateTransactionId()))
                .collect(Collectors.toList());
    }

    public static List<ChargeTransaction> checkCanRefundAndReturnPayTransactions(List<ChargeTransaction> chargeTransactions, BigDecimal refundFee) {
        List<ChargeTransaction> payChargeTransactions = filterOutpatientCenterPayTransactions(chargeTransactions);

        BigDecimal paidAmount = payChargeTransactions.stream()
                .map(ChargeTransaction::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (CollectionUtils.isEmpty(payChargeTransactions) || paidAmount.compareTo(refundFee) != 0) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "诊间支付退费金额与收费金额不一致");
            throw new ChargeServiceException(ChargeServiceError.REFUND_RECEIVED_HEALTH_CARD_NOT_MATCH.getCode(), String.format(ChargeServiceError.REFUND_RECEIVED_HEALTH_CARD_NOT_MATCH.getMessage(), "诊间支付"));
        }

        return payChargeTransactions;
    }


}
