package cn.abcyun.cis.charge.service.dto;

import cn.abcyun.bis.rpc.sdk.cis.model.patient.MemberInfo;
import cn.abcyun.cis.charge.util.JsonUtils;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

@Data
@NoArgsConstructor
public class ChargeSheetIdAndMemberIdDto {
    private String id;

    private String memberId;

    private String memberInfoJson;

    private int useMemberFlag;

    public ChargeSheetIdAndMemberIdDto(String id, String memberId, String memberInfoJson, Integer useMemberFlag) {
        this.id = id;
        this.memberId = memberId;
        this.memberInfoJson = memberInfoJson;
        this.useMemberFlag = Objects.nonNull(useMemberFlag) ? useMemberFlag : 0;
    }

    public MemberInfo getMemberInfo() {

        if (StringUtils.isEmpty(memberInfoJson)) {
            return null;
        }
        return JsonUtils.readValue(memberInfoJson, MemberInfo.class);
    }
}
