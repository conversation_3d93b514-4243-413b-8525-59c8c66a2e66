package cn.abcyun.cis.charge.service.dto.print;

import cn.abcyun.cis.charge.service.dto.ExecuteRecordGroupView;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 执行记录处方分组展示
 *
 * <AUTHOR>
 * @version ExecuteRecordGroupView.java, 2022/4/8 下午3:22
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class ExecuteRecordGroupPrintView extends ExecuteRecordGroupView {
    @ApiModelProperty("本次执行金额")
    private BigDecimal executedAmount;
}
