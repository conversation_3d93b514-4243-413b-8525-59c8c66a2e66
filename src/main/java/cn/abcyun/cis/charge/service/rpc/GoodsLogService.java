package cn.abcyun.cis.charge.service.rpc;

import cn.abcyun.bis.rpc.sdk.cis.client.AbcCisGoodsLogFeignClient;
import cn.abcyun.bis.rpc.sdk.cis.model.goodslog.GoodsStockLogItem;
import cn.abcyun.bis.rpc.sdk.cis.model.goodslog.QueryGoodsStockLogBySourceOrderReq;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.cis.core.util.FeignClientRpcTemplate;
import cn.abcyun.common.model.AbcListPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 进销存服务
 *
 * <AUTHOR>
 * @since 2024/12/2 17:05
 **/
@Service
public class GoodsLogService {

    @Autowired
    private AbcCisGoodsLogFeignClient abcCisGoodsLogFeignClient;

    public Map<String, BigDecimal> getCostPriceMapByChargeSheets(String chainId, String clinicId, List<QueryGoodsStockLogBySourceOrderReq.QueryGoodsStockLogItem> queryItems) {
        if (CollectionUtils.isEmpty(queryItems)) {
            return new HashMap<>();
        }

        QueryGoodsStockLogBySourceOrderReq req = new QueryGoodsStockLogBySourceOrderReq();
        req.setChainId(chainId);
        req.setClinicId(clinicId);
        req.setReturnRealCostPrice(1);
        req.setItems(queryItems);
        AbcListPage<GoodsStockLogItem> listPage = FeignClientRpcTemplate.dealRpcClientMethod("queryGoodsStockLogBySourceOrder", () -> abcCisGoodsLogFeignClient.queryGoodsStockLogBySourceOrder(req), req);
        if (listPage == null || CollectionUtils.isEmpty(listPage.getRows())) {
            return new HashMap<>();
        }

        List<GoodsStockLogItem> goodStockLogItems = listPage.getRows();
        Map<String, BigDecimal> chargeSheetIdToCostPrice = new HashMap<>();
        goodStockLogItems.forEach(item -> {
            if (item.getStockChangeCost() == null) {
                return;
            }

            BigDecimal totalCost = chargeSheetIdToCostPrice.computeIfAbsent(item.getSourceOrderId(), k -> BigDecimal.ZERO);
            chargeSheetIdToCostPrice.put(item.getSourceOrderId(), MathUtils.wrapBigDecimalAdd(totalCost, item.getStockChangeCost()));
        });

        chargeSheetIdToCostPrice.forEach((chargeSheetId, costPrice) -> chargeSheetIdToCostPrice.put(chargeSheetId, costPrice.abs()));

        return chargeSheetIdToCostPrice;
    }

}
