package cn.abcyun.cis.charge.service;

import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ;
import cn.abcyun.cis.charge.api.model.*;
import cn.abcyun.cis.charge.base.ChargeServiceError;
import cn.abcyun.cis.charge.base.ChargeServiceException;
import cn.abcyun.cis.charge.base.ChargeVersionConstants;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.base.pay.CombinedPayItem;
import cn.abcyun.cis.charge.controller.StatusNameTranslator;
import cn.abcyun.cis.charge.factory.chargeform.ChargeFormFactory;
import cn.abcyun.cis.charge.model.ChargeForm;
import cn.abcyun.cis.charge.model.ChargeFormItem;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.model.ChargeTransactionRecord;
import cn.abcyun.cis.charge.processor.SheetProcessorDispatcher;
import cn.abcyun.cis.charge.processor.pay.BaseChargeSheetPay;
import cn.abcyun.cis.charge.processor.pay.ChargeSheetImportPay;
import cn.abcyun.cis.charge.processor.pay.ChargeSheetOwePay;
import cn.abcyun.cis.charge.processor.pay.ChargeSheetPayRsp;
import cn.abcyun.cis.charge.processor.stat.StatRecordResult;
import cn.abcyun.cis.charge.repository.ChargePayTransactionRepository;
import cn.abcyun.cis.charge.repository.ChargeSheetRepository;
import cn.abcyun.cis.charge.repository.ChargeTransactionRecordRepository;
import cn.abcyun.cis.charge.service.dto.PayChargeSheetInfo;
import cn.abcyun.cis.charge.service.rpc.CisPatientOrderService;
import cn.abcyun.cis.charge.service.rpc.CisScClinicService;
import cn.abcyun.cis.charge.util.AbcIdUtils;
import cn.abcyun.cis.charge.util.ChargeUtils;
import cn.abcyun.cis.charge.util.FillUtils;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.cis.commons.amqp.message.PatientOrderMessage;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.exception.ParamNotValidException;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrder;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrderReq;
import cn.abcyun.common.log.marker.AbcLogMarker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.Instant;
import java.util.*;

@Service
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class ChargeImportService {

    private final PatientOrderService patientOrderService;
    private final ChargeExecuteService chargeExecuteService;
    private final ChargeSheetService chargeSheetService;
    private final ChargeService chargeService;
    private final SheetProcessorService sheetProcessorService;
    private final ChargeTransactionRecordRepository chargeTransactionRecordRepository;
    private final ChargeTransactionRecordService chargeTransactionRecordService;
    private final CisPatientOrderService cisPatientOrderService;
    private final TransactionTemplate transactionTemplate;
    private final ChargeSheetRepository chargeSheetRepository;
    private final CisScClinicService cisScClinicService;
    private final ChargePayTransactionRepository chargePayTransactionRepository;

    @Transactional(rollbackFor = Exception.class)
    public CreateChargeSheetRsp importChargeSheet(CreateChargeSheetReq req, String chainId, String clinicId, String operatorId) {
        if (req == null || req.getChargeForms() == null) {
            throw new ParamRequiredException("chargeForms");
        }

        if (req.getImportFlag() == null || req.getImportFlag().equals(0)) {
            throw new ParamRequiredException("importFlag");
        }

        long chargeFormItemCount = req.getChargeForms()
                .stream()
                .filter(chargeFormReq -> chargeFormReq.getChargeFormItems() != null)
                .flatMap(chargeFormReq -> chargeFormReq.getChargeFormItems().stream())
                .filter(Objects::nonNull)
                .count();
        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "importChargeSheet, chargeFormItemCount:" + chargeFormItemCount);

        if (chargeFormItemCount == 0) {
            throw new ChargeServiceException(ChargeServiceError.NO_CHARGE_ITEMS);
        }
        PatientOrder patientOrder = patientOrderService.createPatientOrder(PatientOrderReq.SourceClientType.PC_WEB, req.getPatient(), null, clinicId, chainId, operatorId, PatientOrderReq.Source.CHARGE_RETAIL, null, null, null);
        if (patientOrder == null) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "insertOrUpdatePatientOrder... 创建就诊单失败");
            throw new ChargeServiceException(ChargeServiceError.CREATE_PATIENT_ORDER_FAILED);
        }

        ChargeSheet chargeSheet = new ChargeSheet();
        chargeSheet.setId(AbcIdUtils.getUUID());
        chargeSheet.setPatientOrderId(patientOrder.getId());
        chargeSheet.setChainId(patientOrder.getChainId());
        chargeSheet.setClinicId(patientOrder.getClinicId());
        chargeSheet.setPatientId(patientOrder.getPatientId());
        chargeSheet.setReceivableFee(MathUtils.wrapBigDecimalOrZero(req.getReceivableFee()));
        chargeSheet.setReceivedFee(MathUtils.wrapBigDecimalOrZero(req.getReceivableFee()));
        chargeSheet.setStatus(Constants.ChargeSheetStatus.CHARGED);
        chargeSheet.setDoctorId(req.getDoctorId());
        chargeSheet.setType(ChargeSheet.Type.DIRECT_SALE);
        if (req.getDispensing() != null) {
            chargeSheet.setIsDispensing(req.getDispensing() ? 1 : 0);
        }
        chargeSheet.setSellerId(req.getSellerId());
        chargeSheet.setMemberId(req.getMemberId());
        chargeSheet.setImportFlag(req.getImportFlag());

        List<ChargeForm> chargeForms = new ArrayList<>();
        for (ChargeFormReq chargeFormReq : req.getChargeForms()) {
            ChargeForm chargeForm = ChargeFormFactory.createChargeFormFromCalculate(chargeSheet.getId(), chargeSheet.getPatientOrderId(), chargeSheet.getClinicId(), chargeSheet.getChainId(), chargeFormReq, ChargeUtils.isCanUpdateUsageInfo(chargeSheet.getType(), chargeSheet.getOutpatientStatus(), chargeFormReq.getSourceFormType(), chargeSheet.getClonePrescriptionType()), true, false, operatorId);
            chargeForms.add(chargeForm);
        }

        chargeForms
                .forEach(chargeForm -> {
                    chargeForm.setStatus(Constants.ChargeFormStatus.CHARGED);
                    if (CollectionUtils.isNotEmpty(chargeForm.getChargeFormItems())) {
                        chargeForm.getChargeFormItems()
                                .forEach(chargeFormItem -> {
                                    chargeFormItem.setV2Status(Constants.ChargeFormItemStatus.CHARGED);
                                    chargeFormItem.setStatus(Constants.ChargeFormItemStatus.CHARGED);
                                });
                    }
                });

        chargeSheet.setChargeForms(chargeForms);

        chargeExecuteService.createOrUpdateChargeExecuteItems(chargeSheet, true, operatorId);

        chargeSheetService.saveImport(chargeSheet, operatorId, req.getCreated(), null);

        chargeService.notifyChargeSheetStatusChanged(chargeSheet, operatorId, PatientOrderMessage.MSG_TYPE_CHARGE_CREATED);
        if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.CHARGED) {
            chargeService.notifyChargeSheetStatusChanged(chargeSheet, operatorId, PatientOrderMessage.MSG_TYPE_CHARGE_UPDATED);
        }

        CreateChargeSheetRsp rsp = new CreateChargeSheetRsp();
        rsp.setStatusName(StatusNameTranslator.translateChargeSheetStatus(chargeSheet.getOwedStatus(), chargeSheet.getStatus()));
        rsp.setStatus(chargeSheet.getStatus());
        rsp.setPatientOrderId(chargeSheet.getPatientOrderId());
        rsp.setId(chargeSheet.getId());
        return rsp;
    }

    @Transactional(rollbackFor = Exception.class)
    public CreateChargeSheetRsp importChargeSheetWithOweSheet(CreateChargeSheetReq req, String chainId, String clinicId, String operatorId) {
        if (req == null || req.getChargeForms() == null) {
            throw new ParamRequiredException("chargeForms");
        }

        if (req.getImportFlag() == null || !req.getImportFlag().equals(2)) {
            throw new ParamRequiredException("importFlag");
        }

        long chargeFormItemCount = req.getChargeForms()
                .stream()
                .filter(chargeFormReq -> chargeFormReq.getChargeFormItems() != null)
                .flatMap(chargeFormReq -> chargeFormReq.getChargeFormItems().stream())
                .filter(Objects::nonNull)
                .count();
        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "importChargeSheet, chargeFormItemCount:" + chargeFormItemCount);

        if (chargeFormItemCount == 0) {
            throw new ChargeServiceException(ChargeServiceError.NO_CHARGE_ITEMS);
        }
        PatientOrder patientOrder = patientOrderService.createPatientOrder(PatientOrderReq.SourceClientType.PC_WEB, req.getPatient(), null, clinicId, chainId, operatorId, PatientOrderReq.Source.CHARGE_RETAIL, null, null, null);
        if (patientOrder == null) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "insertOrUpdatePatientOrder... 创建就诊单失败");
            throw new ChargeServiceException(ChargeServiceError.CREATE_PATIENT_ORDER_FAILED);
        }

        ChargeSheet chargeSheet = new ChargeSheet();
        chargeSheet.setId(AbcIdUtils.getUUID());
        chargeSheet.setPatientOrderId(patientOrder.getId());
        chargeSheet.setChainId(patientOrder.getChainId());
        chargeSheet.setClinicId(patientOrder.getClinicId());
        chargeSheet.setPatientId(patientOrder.getPatientId());
        chargeSheet.setDoctorId(req.getDoctorId());
        chargeSheet.setType(ChargeSheet.Type.DIRECT_SALE);
        if (req.getDispensing() != null) {
            chargeSheet.setIsDispensing(req.getDispensing() ? 1 : 0);
        }
        chargeSheet.setSellerId(req.getSellerId());
        chargeSheet.setMemberId(req.getMemberId());
        chargeSheet.setImportFlag(req.getImportFlag());
        ChargeUtils.insertChargeSheetAdditionalIfNeed(chargeSheet, operatorId);
        chargeSheet.getAdditional().setChargeVersion(ChargeVersionConstants.convertChargeVersion(chargeSheet.getClinicId(), cisScClinicService));

        List<ChargeForm> chargeForms = new ArrayList<>();
        for (ChargeFormReq chargeFormReq : req.getChargeForms()) {
            ChargeForm chargeForm = ChargeFormFactory.createChargeFormFromCalculate(chargeSheet.getId(), chargeSheet.getPatientOrderId(), chargeSheet.getClinicId(), chargeSheet.getChainId(), chargeFormReq, ChargeUtils.isCanUpdateUsageInfo(chargeSheet.getType(), chargeSheet.getOutpatientStatus(), chargeFormReq.getSourceFormType(), chargeSheet.getClonePrescriptionType()), true, false, operatorId);
            chargeForms.add(chargeForm);
        }
        chargeSheet.setChargeForms(chargeForms);

        Organ organ = Optional.ofNullable(cisScClinicService.getOrgan(clinicId)).orElseThrow(NotFoundException::new);
        Constants.CalculateMethod calculateMethod = Constants.CalculateMethod.convertMethod(organ.getHisType());

        PayChargeSheetInfo payChargeSheetInfo = new PayChargeSheetInfo();
        payChargeSheetInfo.setClinicId(chargeSheet.getClinicId());
        payChargeSheetInfo.setId(chargeSheet.getId());
        payChargeSheetInfo.setOperatorId(operatorId);
        payChargeSheetInfo.setExpectedAdjustmentFee(req.getExpectedAdjustmentFee());
        payChargeSheetInfo.setPayType(PayChargeSheetInfo.PayType.PAY_FOR_EXISTED_UNCHARGE_SHEET);
        payChargeSheetInfo.setPayMode(Constants.ChargePayMode.OWE_PAY);
        payChargeSheetInfo.setChargeComment(req.getChargeComment());
        CombinedPayItem payItem = new CombinedPayItem();
        payItem.setPayMode(Constants.ChargePayMode.OWE_PAY);
        payChargeSheetInfo.setCombinedPayItems(Arrays.asList(payItem));

        BaseChargeSheetPay chargeSheetPay = new ChargeSheetOwePay(sheetProcessorService, chargeSheet, payChargeSheetInfo, patientOrder, chargeTransactionRecordService, Constants.ChargeSource.CHARGE, payItem, cisPatientOrderService, chargePayTransactionRepository, operatorId);
        ChargeSheetPayRsp chargeSheetPayRsp = chargeSheetPay.pay();
        SheetProcessorDispatcher.ISheetProcessor sheetProcessor = chargeSheetPayRsp.getSheetProcessor();

        chargeExecuteService.createOrUpdateChargeExecuteItems(chargeSheet, true, operatorId);

        chargeSheetService.saveImport(chargeSheet, operatorId, req.getCreated(), null);
        StatRecordResult statRecordResult = sheetProcessor.generateChargeTransactionRecords();
        saveImportTransactionRecords(statRecordResult.getToSaveRecords(), chargeSheet.getImportFlag(), operatorId, req.getCreated());
        chargeService.notifyChargeSheetStatusChanged(chargeSheet, operatorId, PatientOrderMessage.MSG_TYPE_CHARGE_CREATED);
        if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.CHARGED) {
            chargeService.notifyChargeSheetStatusChanged(chargeSheet, operatorId, PatientOrderMessage.MSG_TYPE_CHARGE_UPDATED);
        }

        CreateChargeSheetRsp rsp = new CreateChargeSheetRsp();
        rsp.setStatusName(StatusNameTranslator.translateChargeSheetStatus(chargeSheet.getOwedStatus(), chargeSheet.getStatus()));
        rsp.setStatus(chargeSheet.getStatus());
        rsp.setPatientOrderId(chargeSheet.getPatientOrderId());
        rsp.setId(chargeSheet.getId());
        return rsp;
    }

    private void saveImportTransactionRecords(List<ChargeTransactionRecord> chargeTransactionRecords, int importFlag, String operatorId, Instant created) {

        if (CollectionUtils.isEmpty(chargeTransactionRecords)) {
            return;
        }

        if (created == null) {
            created = Instant.now();
        }
        Instant finalCreated = created;
        Optional.ofNullable(chargeTransactionRecords)
                .orElse(new ArrayList<>())
                .forEach(chargeTransactionRecord -> {
                    chargeTransactionRecord.setImportFlag(importFlag);
                    FillUtils.fillCreatedBy(chargeTransactionRecord, operatorId, finalCreated);
                });
        chargeTransactionRecordService.saveAll(chargeTransactionRecords);
    }

    @Transactional(rollbackFor = Exception.class)
    public CreateChargeSheetRsp postImportPaidChargeSheet(ImportChargeSheetReq req, String chainId, String clinicId, String operatorId) {

        if (req == null || req.getChargeForms() == null) {
            throw new ParamRequiredException("chargeForms");
        }

        if (req.getImportFlag() == null || !req.getImportFlag().equals(3)) {
            throw new ParamRequiredException("importFlag");
        }

        if (CollectionUtils.isEmpty(req.getCombinedPayItems())) {
            throw new ParamRequiredException("combinedPayItems");
        }

        long chargeFormItemCount = req.getChargeForms()
                .stream()
                .filter(chargeFormReq -> chargeFormReq.getChargeFormItems() != null)
                .flatMap(chargeFormReq -> chargeFormReq.getChargeFormItems().stream())
                .filter(Objects::nonNull)
                .count();
        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "importChargeSheet, chargeFormItemCount:" + chargeFormItemCount);

        if (chargeFormItemCount == 0) {
            throw new ChargeServiceException(ChargeServiceError.NO_CHARGE_ITEMS);
        }

        PatientOrder patientOrder = patientOrderService.createPatientOrder(PatientOrderReq.SourceClientType.PC_WEB, req.getPatient(), null, clinicId, chainId, operatorId, PatientOrderReq.Source.CHARGE_RETAIL, null, null, null);
        if (patientOrder == null) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "insertOrUpdatePatientOrder... 创建就诊单失败");
            throw new ChargeServiceException(ChargeServiceError.CREATE_PATIENT_ORDER_FAILED);
        }

        ChargeSheet chargeSheet = new ChargeSheet();
        chargeSheet.setId(AbcIdUtils.getUUID());
        chargeSheet.setPatientOrderId(patientOrder.getId());
        chargeSheet.setChainId(patientOrder.getChainId());
        chargeSheet.setClinicId(patientOrder.getClinicId());
        chargeSheet.setPatientId(patientOrder.getPatientId());
        chargeSheet.setDoctorId(req.getDoctorId());
        chargeSheet.setType(ChargeSheet.Type.DIRECT_SALE);
        chargeSheet.setSellerId(req.getSellerId());
        chargeSheet.setMemberId(req.getMemberId());
        chargeSheet.setImportFlag(req.getImportFlag());
        ChargeUtils.insertChargeSheetAdditionalIfNeed(chargeSheet, operatorId);
        chargeSheet.getAdditional().setChargeVersion(ChargeVersionConstants.convertChargeVersion(chargeSheet.getClinicId(), cisScClinicService));

        List<ChargeForm> chargeForms = new ArrayList<>();
        for (ChargeFormReq chargeFormReq : req.getChargeForms()) {
            ChargeForm chargeForm = ChargeFormFactory.createChargeFormFromCalculate(chargeSheet.getId(), chargeSheet.getPatientOrderId(), chargeSheet.getClinicId(), chargeSheet.getChainId(), chargeFormReq, ChargeUtils.isCanUpdateUsageInfo(chargeSheet.getType(), chargeSheet.getOutpatientStatus(), chargeFormReq.getSourceFormType(), chargeSheet.getClonePrescriptionType()), true, false, operatorId);
            chargeForms.add(chargeForm);
        }
        chargeSheet.setChargeForms(chargeForms);

        List<ChargeFormItem> chargeFormItems = ChargeUtils.getChargeSheetItems(chargeSheet);

        if (chargeFormItems.size() <= 0) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeFormItem不能为空");
            throw new ParamNotValidException("chargeFormItem不能为空");
        }

        PayChargeSheetInfo payChargeSheetInfo = new PayChargeSheetInfo();
        BeanUtils.copyProperties(req, payChargeSheetInfo, "chargeForms");
        payChargeSheetInfo.setClinicId(clinicId);
        payChargeSheetInfo.setId(chargeSheet.getId());
        payChargeSheetInfo.setOperatorId(operatorId);
        payChargeSheetInfo.setPayType(PayChargeSheetInfo.PayType.PAY_FOR_CREATE_CHARGE_SHEET);

        CombinedPayItem payItem = payChargeSheetInfo.getCombinedPayItems().get(0);

        ChargeSheetImportPay baseChargeSheetPay = new ChargeSheetImportPay(sheetProcessorService, chargeSheet, payChargeSheetInfo, patientOrder, chargeTransactionRecordService, Constants.ChargeSource.CHARGE, payItem, cisPatientOrderService, chargePayTransactionRepository, operatorId);

        ChargeSheetPayRsp chargeSheetPayRsp = baseChargeSheetPay.pay();
        SheetProcessorDispatcher.ISheetProcessor sheetProcessor = chargeSheetPayRsp.getSheetProcessor();

        chargeSheetService.saveImport(chargeSheet, operatorId, req.getCreated(), req.getChargeTime());
        StatRecordResult statRecordResult = sheetProcessor.generateChargeTransactionRecords();
        saveImportTransactionRecords(statRecordResult.getToSaveRecords(), chargeSheet.getImportFlag(), operatorId, req.getCreated());

        CreateChargeSheetRsp rsp = new CreateChargeSheetRsp();
        rsp.setStatusName(StatusNameTranslator.translateChargeSheetStatus(chargeSheet.getOwedStatus(), chargeSheet.getStatus()));
        rsp.setStatus(chargeSheet.getStatus());
        rsp.setPatientOrderId(chargeSheet.getPatientOrderId());
        rsp.setId(chargeSheet.getId());
        return rsp;
    }

    public RefreshDataCommonRsp refreshImportSheetTransactionRecord(List<String> chargeSheetIds, List<String> chainIds) {
//        RefreshDataCommonRsp rsp = new RefreshDataCommonRsp();
//        if (CollectionUtils.isEmpty(chainIds)) {
//            return rsp.setIsSuccess(0)
//                    .setMessage("chainIds为空，不需要刷");
//        }
//
//        StringBuilder sql = new StringBuilder("select id from v2_charge_sheet where import_flag in (2, 3) and status = 2 ");
//        if (CollectionUtils.isNotEmpty(chargeSheetIds)) {
//            sql.append(" and id in ");
//            sql.append("(")
//                    .append(chargeSheetIds.stream().map(chargeSheetId -> "\"" + chargeSheetId + "\"").collect(Collectors.joining(",")))
//                    .append(")");
//        } else {
//            sql.append(" and chain_id in ");
//            sql.append("(")
//                    .append(chainIds.stream().map(chainId -> "\"" + chainId + "\"").collect(Collectors.joining(",")))
//                    .append(")");
//        }
//
//        List<Map<String, Object>> list = adbMysqlClient.queryForList(sql.toString(), null);
//
//        if (CollectionUtils.isEmpty(list)) {
//            return rsp;
//        }
//
//        List<String> needRefreshChargeSheetIds = list.stream().map(map -> (String) map.get("id")).collect(Collectors.toList());
//
//        if (CollectionUtils.isEmpty(needRefreshChargeSheetIds)) {
//            return rsp.setMessage("needRefreshChargeSheetIds为空，没有可刷的数据");
//        }
//
//        AtomicInteger count = new AtomicInteger();
//        for (String chargeSheetId : needRefreshChargeSheetIds) {
//            try {
//                transactionTemplate.execute(status -> {
////                    count.addAndGet(refreshImportSheetTransactionRecord(chargeSheetId));
//                    return true;
//                });
//            } catch (Exception e) {
//                log.info("refreshImportSheetTransactionRecord error, chargeSheetId: {}, errorMessage: {}", chargeSheetId, e.getMessage());
//            }
//        }
//        return rsp.setIsSuccess(count.get());
        return new RefreshDataCommonRsp();
    }

//    private int refreshImportSheetTransactionRecord(String chargeSheetId) {
//
//        ChargeSheet chargeSheet = chargeSheetRepository.findByIdAndIsDeleted(chargeSheetId, 0).orElse(null);
//
//        if (Objects.isNull(chargeSheet)) {
//            log.info("chargeSheet is null, id: {}", chargeSheetId);
//            return 0;
//        }
//
//        int recordCount = chargeTransactionRecordRepository.countByChargeSheetId(chargeSheetId);
//
//        if (recordCount > 0) {
//            log.info("已经刷过了，不用再刷了，chargeSheetId: {}", chargeSheetId);
//            return 0;
//        }
//
//        ChargeTransaction chargeTransaction = Optional.ofNullable(chargeSheet.getChargeTransactions())
//                .orElse(new ArrayList<>())
//                .stream()
//                .filter(transaction -> MathUtils.wrapBigDecimalCompare(transaction.getAmount(), BigDecimal.ZERO) >= 0)
//                .findFirst()
//                .orElse(null);
//
//
//        if (Objects.isNull(chargeTransaction)) {
//            log.info("chargeTransaction is null, chargeSheetId: {}", chargeSheetId);
//            return 0;
//        }
//
//        StatRecordProcessor statRecordProcessor = new StatRecordProcessor(chargeSheet.getId(), chargeSheet.getChargeVersion(), chargeTransaction.getCreatedBy());
//        statRecordProcessor.setChargeTransactionRecordService(chargeTransactionRecordService);
//        int statPayType = StatRecordProcessor.PayType.PAID;
//
//        Organ organ = cisScClinicService.getOrgan(chargeSheet.getClinicId());
//        boolean isHospital = Objects.equals(Organ.HisType.CIS_HIS_TYPE_HOSPITAL, Optional.ofNullable(organ)
//                .map(Organ::getHisType)
//                .orElse(Organ.HisType.CIS_HIS_TYPE_NORMAL));
//
//        statRecordProcessor.setChargeInfo(
//                statPayType,
//                Constants.TransactionRecordHandleMode.RECORD_COUNT_BY_CHOOSE,
//                StatRecordProcessor.RefundFlatType.ALL_FLAT,
//                isHospital,
//                Arrays.asList(chargeTransaction),
//                ChargeUtils.getChargeSheetItems(chargeSheet),
//                null,
//                null,
//                null,
//                null,
//                new ArrayList<>(),
//                new HashMap<>()
//        );
//
//        List<ChargeTransactionRecord> chargeTransactionRecords = statRecordProcessor.generateImportChargeTransactionRecords();
//
//        if (CollectionUtils.isNotEmpty(chargeTransactionRecords)) {
//            chargeTransactionRecords.forEach(chargeTransactionRecord -> chargeTransactionRecord.setImportFlag(chargeSheet.getImportFlag()));
//            chargeTransactionRecordService.saveAll(chargeTransactionRecords);
//        }
//
//        return 1;
//    }
}
