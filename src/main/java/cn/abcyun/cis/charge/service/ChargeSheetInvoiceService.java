package cn.abcyun.cis.charge.service;

import cn.abcyun.bis.rpc.sdk.cis.model.charge.GoodsFeeType;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Employee;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsSystemType;
import cn.abcyun.bis.rpc.sdk.cis.model.invoice.*;
import cn.abcyun.bis.rpc.sdk.cis.model.invoice.message.AutoInvoiceMessage;
import cn.abcyun.bis.rpc.sdk.cis.model.invoice.message.PaidFeeChangeMessage;
import cn.abcyun.bis.rpc.sdk.property.base.PropertyKey;
import cn.abcyun.bis.rpc.sdk.property.model.InvoiceManagement;
import cn.abcyun.bis.rpc.sdk.property.service.PropertyService;
import cn.abcyun.cis.charge.amqp.MQProducer;
import cn.abcyun.cis.charge.amqp.RocketMqProducer;
import cn.abcyun.cis.charge.api.model.*;
import cn.abcyun.cis.charge.api.model.invoice.*;
import cn.abcyun.cis.charge.base.ChargeServiceError;
import cn.abcyun.cis.charge.base.ChargeServiceException;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.base.GoodsFeeTypeConstants;
import cn.abcyun.cis.charge.digitalinvoice.DigitalInvoiceService;
import cn.abcyun.cis.charge.model.*;
import cn.abcyun.cis.charge.processor.ChargeSheetFeeProtocol;
import cn.abcyun.cis.charge.processor.SheetProcessorDispatcher;
import cn.abcyun.cis.charge.processor.provider.ChargeSheetInvoiceProvider;
import cn.abcyun.cis.charge.repository.ChargeSheetAdditionalRepository;
import cn.abcyun.cis.charge.service.dto.print.ChargeFormItemPrintView;
import cn.abcyun.cis.charge.service.dto.print.ChargeSheetPrintView;
import cn.abcyun.cis.charge.service.rpc.CisInvoiceService;
import cn.abcyun.cis.charge.service.rpc.CisScClinicService;
import cn.abcyun.cis.charge.service.rpc.CisScGoodsService;
import cn.abcyun.cis.charge.util.ChargeUtils;
import cn.abcyun.cis.charge.util.FillUtils;
import cn.abcyun.cis.charge.util.JsonUtils;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.exception.ParamNotValidException;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.rpc.charge.ClinicForCharge;
import cn.abcyun.cis.commons.rpc.crm.PatientInfo;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrder;
import cn.abcyun.cis.commons.rpc.shebao.QueryChargeSheetShebaoInfoReq;
import cn.abcyun.cis.commons.rpc.shebao.QueryChargeSheetShebaoInfoRsp;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.cis.core.db.UseReadOnlyDB;
import cn.abcyun.cis.core.redis.RedisLock;
import cn.abcyun.cis.core.util.ExecutorUtils;
import cn.abcyun.common.log.marker.AbcLogMarker;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ChargeSheetInvoiceService implements ChargeSheetInvoiceProvider {

    @Autowired
    private ChargeSheetService chargeSheetService;

    @Autowired
    private CisInvoiceService cisInvoiceService;

    @Autowired
    private ChargeSheetAdditionalRepository chargeSheetAdditionalRepository;

    @Autowired
    private EmployeeService employeeService;

    @Autowired
    private ShebaoService shebaoService;

    @Autowired
    private CrmService crmService;

    @Autowired
    private PropertyService propertyService;

    @Autowired
    private PatientOrderService patientOrderService;

    @Autowired
    private SheetProcessorService sheetProcessorService;

    @Autowired
    private ChargePrintService chargePrintService;

    @Autowired
    private DigitalInvoiceService digitalInvoiceService;

    @Autowired
    private RocketMqProducer rocketMqProducer;

    @Autowired
    private CisScClinicService cisScClinicService;

    @Autowired
    private ClinicService clinicService;

    @Autowired
    private OrganProductService organProductService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private CisScGoodsService scGoodsService;

    /**
     * 查看收费单的收据信息
     *
     * @param chainId           连锁id
     * @param clinicId          门店id
     * @param chargeSheetId     收费单id
     * @return 收据信息
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public ChargeSheetPrintView getChargeSheetReceipt(String chainId, String clinicId, String chargeSheetId) {
        ChargeSheet chargeSheet = chargeSheetService.findByIdAndClinicId(chargeSheetId, clinicId);
        if (Objects.isNull(chargeSheet)) {
            throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_NOT_EXISTED);
        }
        if (!Arrays.asList(Constants.ChargeSheetStatus.CHARGED, Constants.ChargeSheetStatus.PART_REFUNDED).contains(chargeSheet.getStatus())) {
            throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_STATUS_IS_NOT_CHARGED);
        }
        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.CHARGED && chargeSheet.getStatus() != Constants.ChargeSheetStatus.PART_REFUNDED) {
            throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_STATUS_IS_NOT_CHARGED);
        }
        return this.findChargeSheetPrintView(chargeSheet);
    }

    /**
     * 收费单发票打印信息
     *
     * @param chargeSheetId           收费单id
     * @param clinicId                门店id
     * @param chainId                 连锁id
     * @param operatorId              开票人id
     * @param invoiceType             发票类型 {@link cn.abcyun.bis.rpc.sdk.cis.model.invoice.InvoiceConst.InvoiceType}
     * @param invoiceCategory         发票分类 {@link cn.abcyun.bis.rpc.sdk.cis.model.invoice.InvoiceConst.Category}
     * @param businessScene           业务场景 {@link cn.abcyun.bis.rpc.sdk.cis.model.invoice.InvoiceConst.BusinessScene}
     * @param registrationInvoiceType 挂号发票类型 0:普通的挂号发票，1:杭州特殊的挂号发票 {@link cn.abcyun.bis.rpc.sdk.cis.model.invoice.InvoiceConst.RegistrationInvoiceType}
     * @param invoiceSupplierId       发票供应商id {@link cn.abcyun.bis.rpc.sdk.cis.model.invoice.InvoiceConst.SupplierId}
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public GetChargeSheetInvoicePrintInfoRsp getChargeSheetInvoicePrintInfo(String chargeSheetId,
                                                                            String chainId,
                                                                            String clinicId,
                                                                            String operatorId,
                                                                            Integer invoiceType,
                                                                            int invoiceCategory,
                                                                            String businessScene,
                                                                            int registrationInvoiceType,
                                                                            Integer invoiceSupplierId) {
        // 如果非普通发票，但是发票供应商为空
        if (invoiceCategory != InvoiceConst.Category.NORMAL_INVOICE && Objects.isNull(invoiceSupplierId)) {
            throw new ParamRequiredException("invoiceSupplierId");
        }
        // 校验参数
        if (!Arrays.asList(InvoiceConst.InvoiceType.NORMAL_GENERAL_INVOICE, InvoiceConst.InvoiceType.NORMAL_OUTPATIENT_INVOICE,
                InvoiceConst.InvoiceType.NORMAL_REGISTRATION_INVOICE, InvoiceConst.InvoiceType.NORMAL_HOSPITAL_INVOICE).contains(invoiceType)) {
            throw new ParamNotValidException("不支持的发票类型");
        }
        if (StringUtils.isEmpty(businessScene)) {
            businessScene = InvoiceConst.BusinessScene.CHARGE;
        }
        if (!Objects.equals(InvoiceConst.BusinessScene.CHARGE, businessScene) && !Objects.equals(InvoiceConst.BusinessScene.REGISTRATION, businessScene)) {
            throw new ParamNotValidException("不支持的businessScene类型");
        }
        // 1、获取chargeSheet信息
        ChargeSheet chargeSheet = chargeSheetService.findByIdAndClinicId(chargeSheetId, clinicId);
        if (chargeSheet == null) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeSheet is null, chargeSheetId: {}", chargeSheetId);
            throw new NotFoundException();
        }
        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.CHARGED && chargeSheet.getStatus() != Constants.ChargeSheetStatus.PART_REFUNDED) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeSheet.status is not charged, status: {}", chargeSheet.getStatus());
            throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_STATUS_IS_NOT_CHARGED);
        }

        // 2、校验发票状态
        ChargeSheetAdditional chargeSheetAdditional = chargeSheet.getAdditional();
        if (chargeSheetAdditional == null) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeSheet.additional is null");
            throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_INVOICE_STATUS_IS_NONE);
        }
        if (Objects.isNull(chargeSheetAdditional.getInvoiceStatusFlag())
                && !Arrays.asList(Constants.ChargeSheetInvoiceStatus.INVOICE_WAITING, Constants.ChargeSheetInvoiceStatus.INVOICE_INVALID, Constants.ChargeSheetInvoiceStatus.INVOICE_REFUND).contains(chargeSheetAdditional.getInvoiceStatus())) {
            throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_INVOICE_STATUS_IS_NONE);
        }
        // 收费单已开票的判断逻辑
        if (Objects.nonNull(chargeSheetAdditional.getInvoiceStatusFlag()) && (chargeSheetAdditional.getInvoiceStatusFlag() & Constants.ChargeSheetInvoiceStatusFlag.HAS_INVOICED_ITEM) != 0) {
            throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_INVOICE_STATUS_IS_SUCCESS);
        }

        String patientId = chargeSheet.getPatientId();
        // 3、响应结果
        GetChargeSheetInvoicePrintInfoRsp rsp = new GetChargeSheetInvoicePrintInfoRsp();
        rsp.setClinicId(clinicId);
        rsp.setChainId(chainId);
        rsp.setBusinessId(chargeSheetId);
        rsp.setPatientId(patientId);
        rsp.setBusinessScene(businessScene);
        rsp.setBusinessCallbackUrl(Constants.BusinessCallBackUrl.INVOICE_STATUS_FLAG_CALLBACK_URL);
        rsp.setInvoiceType(invoiceType);
        rsp.setInvoiceCategory(invoiceCategory);
        rsp.setInvoiceSupplierId(invoiceSupplierId);

        BigDecimal invoiceFee = MathUtils.wrapBigDecimalAdd(chargeSheet.getReceivedFee(), chargeSheet.getRefundFee());
        rsp.setInvoiceFee(invoiceFee);

        // 4、非普通发票
        if (invoiceCategory != InvoiceConst.Category.NORMAL_INVOICE) {
            ChargeSheet memoryChargeSheet = ChargeUtils.deepCopyChargeSheet(chargeSheet);
            ChargeSheetInvoiceContext chargeSheetInvoiceContext = this.createChargeSheetInvoiceContext(memoryChargeSheet);
            rsp.setDigitalInvoiceInfo(this.generateDigitalInvoiceInfoJsonNode(memoryChargeSheet, invoiceSupplierId, operatorId, chargeSheetInvoiceContext));
            if (!CollectionUtils.isEmpty(chargeSheetInvoiceContext.getInvoiceChargeFormItems())) {
                BigDecimal itemsTotalPrice = chargeSheetInvoiceContext.getInvoiceChargeFormItems().stream()
                        .map(InvoiceChargeFormItem::getInvoiceInfo)
                        .map(InvoiceChargeFormItem.InvoiceInfo::getDiscountedTotalPrice)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                rsp.setInvoiceFee(itemsTotalPrice);
            }
            if (invoiceSupplierId == InvoiceConst.SupplierId.NUONUO || invoiceSupplierId == InvoiceConst.SupplierId.ABC_ISV) {
                CreateInvoiceReq.DigitalInvoice digitalInvoice = JsonUtils.readValue(rsp.getDigitalInvoiceInfo(), CreateInvoiceReq.DigitalInvoice.class);
                rsp.setGoodsNum(Optional.ofNullable(digitalInvoice)
                        .map(CreateInvoiceReq.DigitalInvoice::getDetailItems)
                        .orElse(new ArrayList<>())
                        .size());
            } else if (invoiceSupplierId == InvoiceConst.SupplierId.FUJIAN_BOSI) {
                CreateBosiInVoiceReq digitalInvoice = JsonUtils.readValue(rsp.getDigitalInvoiceInfo(), CreateBosiInVoiceReq.class);
                rsp.setGoodsNum(Optional.ofNullable(digitalInvoice)
                        .map(CreateBosiInVoiceReq::getListDetails)
                        .orElse(new ArrayList<>())
                        .size());
            } else if (invoiceSupplierId == InvoiceConst.SupplierId.NANJING_WUAI) {
                CreateNanjingWuaiInvoiceReq digitalInvoice = JsonUtils.readValue(rsp.getDigitalInvoiceInfo(), CreateNanjingWuaiInvoiceReq.class);
                rsp.setGoodsNum(Optional.ofNullable(digitalInvoice)
                        .map(CreateNanjingWuaiInvoiceReq::getListDetails)
                        .orElse(new ArrayList<>())
                        .size());
            } else if (invoiceSupplierId == InvoiceConst.SupplierId.HEBEI_XINHE) {
                CreateHebeiXinheInvoiceReq digitalInvoice = JsonUtils.readValue(rsp.getDigitalInvoiceInfo(), CreateHebeiXinheInvoiceReq.class);
                rsp.setGoodsNum(Optional.ofNullable(digitalInvoice)
                        .map(CreateHebeiXinheInvoiceReq::getItemList)
                        .orElse(new ArrayList<>())
                        .size());
            } else if (invoiceCategory == InvoiceConst.Category.VAT_DIGITAL_INVOICE && invoiceSupplierId == InvoiceConst.SupplierId.MANUAL_UPLOAD_VAT) {
                CreateInvoiceReq.DigitalInvoice digitalInvoice = JsonUtils.readValue(rsp.getDigitalInvoiceInfo(), CreateInvoiceReq.DigitalInvoice.class);
                rsp.setGoodsNum(Optional.ofNullable(digitalInvoice)
                        .map(CreateInvoiceReq.DigitalInvoice::getDetailItems)
                        .orElse(new ArrayList<>())
                        .size());
            } else if (invoiceCategory == InvoiceConst.Category.MEDICAL_DIGITAL_INVOICE && invoiceSupplierId == InvoiceConst.SupplierId.MANUAL_UPLOAD_MED) {
                CreateUploadInvoiceReq digitalInvoice = JsonUtils.readValue(rsp.getDigitalInvoiceInfo(), CreateUploadInvoiceReq.class);
                rsp.setGoodsNum(Optional.ofNullable(digitalInvoice)
                        .map(CreateUploadInvoiceReq::getItemList)
                        .orElse(new ArrayList<>())
                        .size());
            }
        }

        // 7、普通发票
        if (invoiceCategory == InvoiceConst.Category.NORMAL_INVOICE &&
                (invoiceType == InvoiceConst.InvoiceType.NORMAL_OUTPATIENT_INVOICE
                        || invoiceType == InvoiceConst.InvoiceType.NORMAL_REGISTRATION_INVOICE
                        || invoiceType == InvoiceConst.InvoiceType.NORMAL_GENERAL_INVOICE
                )) {
            if (invoiceType == InvoiceConst.InvoiceType.NORMAL_REGISTRATION_INVOICE && registrationInvoiceType == CreateChargeSheetInvoiceReq.RegistrationInvoiceType.HANGZHOU) {
                RegistrationInvoicePrintView registrationInvoicePrintView = chargePrintService.generateRegistrationInvoicePrintView(chargeSheet);
                rsp.setGoodsNum(1);
                rsp.setHzRegistrationNormalInvoicePrintView(registrationInvoicePrintView);
            } else {
                ChargeSheetPrintView chargeSheetPrintView = findChargeSheetPrintView(chargeSheet);
                rsp.setGoodsNum(Optional.ofNullable(chargeSheetPrintView)
                        .map(ChargeSheetPrintView::getChargeForms)
                        .orElse(new ArrayList<>())
                        .stream()
                        .filter(chargeFormPrintView -> chargeFormPrintView.getChargeFormItems() != null)
                        .flatMap(chargeFormPrintView -> chargeFormPrintView.getChargeFormItems().stream())
                        .mapToInt(chargeFormItemPrintView -> 1)
                        .sum()
                );
                rsp.setNormalInvoicePrintView(chargeSheetPrintView);
            }
        }

        rsp.setOperatorId(operatorId);
        return rsp;
    }

    private int getInvoiceSupplierId(String clinicId, int invoiceCategory) {
        // 查询当前门店的电子发票配置
        Map<Integer, Integer> categorySupplierIdMap = Optional.ofNullable(cisInvoiceService.getInvoiceConfigAll(clinicId))
                .orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.toMap(InvoiceConfigView::getInvoiceCategory, InvoiceConfigView::getInvoiceSupplierId, (a, b) -> a));
        return categorySupplierIdMap.getOrDefault(invoiceCategory, 0);
    }

    private JsonNode generateDigitalInvoiceInfoJsonNode(ChargeSheet chargeSheet, int invoiceSupplierId, String operatorId, ChargeSheetInvoiceContext chargeSheetInvoiceContext) {
        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.CHARGED) {
            boolean enableInvoice = false;
            if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_REFUNDED) {
                Organ organ = cisScClinicService.getOrgan(chargeSheet.getClinicId());
                if (organ.getHisType() == Organ.HisType.CIS_HIS_TYPE_NORMAL || organ.getHisType() == Organ.HisType.CIS_HIS_TYPE_HOSPITAL || organ.getHisType() == Organ.HisType.CIS_HIS_TYPE_PHARMACY) {
                    enableInvoice = true;
                }
            }
            if (!enableInvoice) {
                log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeSheet.status is not charged, status: {}", chargeSheet.getStatus());
                throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_STATUS_IS_NOT_CHARGED);
            }
        }
        return digitalInvoiceService.generateDigitalInvoiceJsonNode(chargeSheet, invoiceSupplierId, operatorId, chargeSheetInvoiceContext);
    }


    /**
     * 开冲红发票
     *
     * @param req
     * @param chargeSheetId
     * @param chainId
     * @param clinicId
     * @param operatorId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ChonghongChargeSheetInvoiceRsp chonghongInvoice(ChonghongChargeInvoiceReq req, String chargeSheetId, String chainId, String clinicId, String operatorId) {
        ChargeSheet chargeSheet = chargeSheetService.findByIdAndClinicId(chargeSheetId, clinicId);

        if (chargeSheet == null) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeSheet is null, chargeSheetId: {}", chargeSheetId);
            throw new NotFoundException();
        }

        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.CHARGED && chargeSheet.getStatus() != Constants.ChargeSheetStatus.PART_REFUNDED && chargeSheet.getStatus() != Constants.ChargeSheetStatus.REFUNDED) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeSheet.status is not charged, status: {}", chargeSheet.getStatus());
            throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_STATUS_IS_NOT_CHARGED);
        }

        int invoiceStatus = Optional.ofNullable(chargeSheet.getAdditional()).map(ChargeSheetAdditional::getInvoiceStatus).orElse(Constants.ChargeSheetInvoiceStatus.NONE);

        if (!Constants.ChargeSheetInvoiceStatus.getInvoicedStatues().contains(invoiceStatus) && invoiceStatus != Constants.ChargeSheetInvoiceStatus.INVOICE_INVALING) {
            throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_INVOICE_STATUS_ERROR);
        }

        InvoiceView invoiceView = getInvoiceByBusinessId(chargeSheet.getClinicId(), chargeSheet.getId(), chargeSheet.getType());

        if (invoiceView == null) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "invoiceView is null");
            throw new NotFoundException();
        }

        cn.abcyun.bis.rpc.sdk.cis.model.invoice.ChonghongInvoiceReq chonghongInvoiceReq = new cn.abcyun.bis.rpc.sdk.cis.model.invoice.ChonghongInvoiceReq();
        chonghongInvoiceReq.setClinicId(clinicId);
        chonghongInvoiceReq.setBusinessId(chargeSheetId);
        chonghongInvoiceReq.setBusinessScene(chargeSheet.getType() == ChargeSheet.Type.REGISTRATION ? CreateInvoiceReq.BusinessScene.REGISTRATION : CreateInvoiceReq.BusinessScene.CHARGE);
        chonghongInvoiceReq.setOperatorId(operatorId);
        chonghongInvoiceReq.setInvoiceType(req.getInvoiceType());
        chonghongInvoiceReq.setOperatorName(Optional.ofNullable(employeeService.findById(chainId, operatorId)).map(Employee::getName).orElse(null));
        chonghongInvoiceReq.setBusinessCallbackUrl(Constants.BusinessCallBackUrl.CREATE_RED_INVOICE_CALLBACK_URL);

        if (invoiceView.getInvoiceType() != InvoiceView.InvoiceType.DIGITAL_INVOICE) {
            if (req == null || StringUtils.isEmpty(req.getInvoiceNumber())) {
                throw new ParamRequiredException("invoiceNumber");
            }
            if (StringUtils.isEmpty(req.getInvoiceCode())) {
                throw new ParamRequiredException("invoiceCode");
            }
            if (StringUtils.isEmpty(req.getMac())) {
                throw new ParamRequiredException("mac");
            }

            if (req.getRegistrationInvoiceType() == CreateChargeSheetInvoiceReq.RegistrationInvoiceType.HANGZHOU) {
                RegistrationInvoicePrintView registrationInvoicePrintView = JsonUtils.readValue(invoiceView.getDetail(), RegistrationInvoicePrintView.class);
                RegistrationInvoicePrintView registrationChonghongInvoicePrintView = negateRegistrationInvoicePrintView(registrationInvoicePrintView);
                chonghongInvoiceReq.setDetail(JsonUtils.dumpAsJsonNode(registrationChonghongInvoicePrintView));
            } else {
                ChargeSheetPrintView chargeSheetPrintView = JsonUtils.readValue(invoiceView.getDetail(), ChargeSheetPrintView.class);
                ChargeSheetPrintView chargeSheetChonghongPrintView = negateChargeSheetPrintView(chargeSheet.getChainId(), chargeSheet.getClinicId(), chargeSheet.getDoctorId(), chargeSheetPrintView);
                chonghongInvoiceReq.setDetail(JsonUtils.dumpAsJsonNode(chargeSheetChonghongPrintView));
            }


            CreateInvoiceReq.NormalInvoice normalInvoice = new CreateInvoiceReq.NormalInvoice();
            BeanUtils.copyProperties(req, normalInvoice);
            chonghongInvoiceReq.setNormalInvoice(normalInvoice);
        }

        ChonghongInvoiceRsp chonghongInvoiceRsp = cisInvoiceService.chonghongInvoice(chonghongInvoiceReq);

        Integer status = Optional.ofNullable(chonghongInvoiceRsp).map(c -> c.getStatus()).orElse(CreateInvoiceRsp.Status.FAILED);

        invoiceStatus = translateInvoiceStatus(InvoiceView.Type.RED, invoiceView.getInvoiceType(), status);

        if (invoiceStatus != Constants.ChargeSheetInvoiceStatus.INVOICE_INVALID && invoiceStatus != Constants.ChargeSheetInvoiceStatus.INVOICE_INVALING) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "createInvoiceRsp.status error");
            throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_INVOICE_DESTROY_ERROR);
        }

        chargeSheet.getAdditional().setInvoiceStatus(invoiceStatus);
        FillUtils.fillLastModifiedBy(chargeSheet.getAdditional(), operatorId);

        ChonghongChargeSheetInvoiceRsp rsp = new ChonghongChargeSheetInvoiceRsp();
        rsp.setChargeSheetId(chargeSheetId)
                .setInvoiceStatus(chargeSheet.getAdditional().getInvoiceStatus())
                .setInvoiceRsp(chonghongInvoiceRsp);

        return rsp;
    }

    /**
     * 将金额取反
     *
     * @param chargeSheetPrintView
     * @return
     */
    private ChargeSheetPrintView negateChargeSheetPrintView(String chainId, String clinicId, String doctorId, ChargeSheetPrintView chargeSheetPrintView) {
        if (chargeSheetPrintView == null) {
            return null;
        }

        Optional.ofNullable(chargeSheetPrintView.getTotalFee()).ifPresent(fee -> chargeSheetPrintView.setTotalFee(fee.negate()));
        Optional.ofNullable(chargeSheetPrintView.getDiscountFee()).ifPresent(fee -> chargeSheetPrintView.setDiscountFee(fee.negate()));
        Optional.ofNullable(chargeSheetPrintView.getReceivableFee()).ifPresent(fee -> chargeSheetPrintView.setReceivableFee(fee.negate()));
        Optional.ofNullable(chargeSheetPrintView.getNetIncomeFee()).ifPresent(fee -> chargeSheetPrintView.setNetIncomeFee(fee.negate()));
        Optional.ofNullable(chargeSheetPrintView.getRefundFee()).ifPresent(fee -> chargeSheetPrintView.setRefundFee(fee.negate()));
        Optional.ofNullable(chargeSheetPrintView.getPersonalPaymentFee()).ifPresent(fee -> chargeSheetPrintView.setPersonalPaymentFee(fee.negate()));

        chargeSheetPrintView.setMemberCardBeginningBalance(null);
        chargeSheetPrintView.setMemberCardBalance(null);
        chargeSheetPrintView.setMemberCardMobile(null);
        chargeSheetPrintView.setHealthCardBeginningBalance(null);
        chargeSheetPrintView.setHealthCardBalance(null);
        chargeSheetPrintView.setHealthCardNo(null);
        chargeSheetPrintView.setHealthCardOwner(null);
        chargeSheetPrintView.setHealthCardOwnerRelationToPatient(null);
        chargeSheetPrintView.setHealthCardId(null);
        chargeSheetPrintView.setHealthCardAccountPaymentFee(null);
        chargeSheetPrintView.setHealthCardFundPaymentFee(null);
        chargeSheetPrintView.setHealthCardOtherPaymentFee(null);
        chargeSheetPrintView.setHealthCardCardOwnerType(null);
        chargeSheetPrintView.setHealthCardSelfConceitFee(null);
        chargeSheetPrintView.setHealthCardSelfPayFee(null);

        Optional.ofNullable(chargeSheetPrintView.getSubTotals()).ifPresent(subTotal -> {

            Optional.ofNullable(subTotal.getRegistrationFee()).ifPresent(fee -> subTotal.setRegistrationFee(fee.negate()));
            Optional.ofNullable(subTotal.getWesternMedicineFee()).ifPresent(fee -> subTotal.setWesternMedicineFee(fee.negate()));
            Optional.ofNullable(subTotal.getChineseMedicineFee()).ifPresent(fee -> subTotal.setChineseMedicineFee(fee.negate()));
            Optional.ofNullable(subTotal.getExaminationFee()).ifPresent(fee -> subTotal.setExaminationFee(fee.negate()));
            Optional.ofNullable(subTotal.getTreatmentFee()).ifPresent(fee -> subTotal.setTreatmentFee(fee.negate()));
            Optional.ofNullable(subTotal.getMaterialFee()).ifPresent(fee -> subTotal.setMaterialFee(fee.negate()));
            Optional.ofNullable(subTotal.getOnlineConsultationFee()).ifPresent(fee -> subTotal.setOnlineConsultationFee(fee.negate()));
            Optional.ofNullable(subTotal.getExpressDeliveryFee()).ifPresent(fee -> subTotal.setExpressDeliveryFee(fee.negate()));
            Optional.ofNullable(subTotal.getDecoctionFee()).ifPresent(fee -> subTotal.setDecoctionFee(fee.negate()));
            Optional.ofNullable(subTotal.getOtherFee()).ifPresent(fee -> subTotal.setOtherFee(fee.negate()));
            Optional.ofNullable(subTotal.getComposeProductFee()).ifPresent(fee -> subTotal.setComposeProductFee(fee.negate()));

        });

        if (CollectionUtils.isNotEmpty(chargeSheetPrintView.getMedicalBills())) {
            chargeSheetPrintView.getMedicalBills()
                    .forEach(medicalBillPrintView -> Optional.ofNullable(medicalBillPrintView.getTotalFee()).ifPresent(fee -> medicalBillPrintView.setTotalFee(fee.negate())));
        }

        if (CollectionUtils.isNotEmpty(chargeSheetPrintView.getChargeForms())) {
            chargeSheetPrintView.getChargeForms()
                    .forEach(chargeFormPrintView -> {
                        Optional.ofNullable(chargeFormPrintView.getTotalPrice()).ifPresent(fee -> chargeFormPrintView.setTotalPrice(fee.negate()));
                        // 对chargeFormItems取反
                        negateChargeFormItemPrintViews(chargeFormPrintView.getChargeFormItems());
                    });
        }

        chargeSheetPrintView.setShebaoPayment(queryChonghongInvoiceShebaoPayment(chainId, clinicId, doctorId, QueryChargeSheetShebaoInfoReq.PayTaskType.REFUND, chargeSheetPrintView));

        return chargeSheetPrintView;
    }

    private void negateChargeFormItemPrintViews(List<ChargeFormItemPrintView> chargeFormItemPrintViews) {
        if (CollectionUtils.isEmpty(chargeFormItemPrintViews)) {
            return;
        }

        chargeFormItemPrintViews
                .forEach(chargeFormItemPrintView -> {
                    Optional.ofNullable(chargeFormItemPrintView.getTotalPrice()).ifPresent(fee -> chargeFormItemPrintView.setTotalPrice(fee.negate()));
                    Optional.ofNullable(chargeFormItemPrintView.getDiscountedPrice()).ifPresent(fee -> chargeFormItemPrintView.setDiscountedPrice(fee.negate()));
                    // 如果composeChildren不为空
                    if (CollectionUtils.isNotEmpty(chargeFormItemPrintView.getComposeChildren())) {
                        negateChargeFormItemPrintViews(chargeFormItemPrintView.getComposeChildren());
                    }
                });
    }

    public static int translateInvoiceStatus(int type, int invoiceType, int status) {
        int invoiceStatus = Constants.ChargeSheetInvoiceStatus.NONE;

        if (type == InvoiceView.Type.BLUE) {
            if (status == CreateInvoiceRsp.Status.WAITING) {
                invoiceStatus = Constants.ChargeSheetInvoiceStatus.INVOICE_BEING;
            } else if (status == CreateInvoiceRsp.Status.SUCCESS) {
                invoiceStatus = Constants.ChargeSheetInvoiceStatus.INVOICE_SUCCESS;
            } else if (status == CreateInvoiceRsp.Status.FAILED) {
                invoiceStatus = Constants.ChargeSheetInvoiceStatus.INVOICE_FAIL;
            } else if (status == CreateInvoiceRsp.Status.REFUND) {
                if (invoiceType == InvoiceView.InvoiceType.DIGITAL_INVOICE) {
                    invoiceStatus = Constants.ChargeSheetInvoiceStatus.INVOICE_INVALID;
                } else {
                    invoiceStatus = Constants.ChargeSheetInvoiceStatus.INVOICE_REFUND;
                }
            }
            return invoiceStatus;
        }

        if (type == InvoiceView.Type.RED) {
            if (status == CreateInvoiceRsp.Status.WAITING) {
                invoiceStatus = Constants.ChargeSheetInvoiceStatus.INVOICE_INVALING;
            } else if (status == CreateInvoiceRsp.Status.SUCCESS) {
                invoiceStatus = Constants.ChargeSheetInvoiceStatus.INVOICE_INVALID;
            } else if (status == CreateInvoiceRsp.Status.FAILED) {
                invoiceStatus = Constants.ChargeSheetInvoiceStatus.INVOICE_FAIL;
            }
        }

        return invoiceStatus;
    }

    /**
     * 收费单发票状态回调
     *
     * @param req 发票状态回调请求参数
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @RedisLock(key = "'charge.sheet:invoice.status.flag:' + #req.businessId", waitTime = 20)
    public InvoiceStatusCallbackRsp chargeSheetInvoiceStatusFlagCallback(InvoiceStatusCallbackReq req) {
        InvoiceStatusCallbackRsp rsp = new InvoiceStatusCallbackRsp();
        String chargeSheetId = req.getBusinessId();
        ChargeSheet chargeSheet = chargeSheetService.findById(chargeSheetId);
        if (chargeSheet == null) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeSheet is null, chargeSheetId: {}", chargeSheetId);
            throw new NotFoundException();
        }
        AtomicBoolean hasInvoicableItem = new AtomicBoolean(false);
        AtomicBoolean hasInvoicedItem = new AtomicBoolean(false);
        AtomicBoolean hasInvoiceFailure = new AtomicBoolean(false);
        AtomicBoolean hasInvoiceFeeChanger = new AtomicBoolean(false);
        Optional.ofNullable(req.getFeeTypeInvoiceResults())
                .orElse(Lists.newArrayList())
                .forEach(feeTypeInvoiceResult -> {
                    int status = feeTypeInvoiceResult.getStatus();
                    if (Objects.equals(status, InvoiceConst.Status.WAITING)
                            || Objects.equals(status, InvoiceConst.Status.CREATING)
                            || Objects.equals(status, InvoiceConst.Status.DESTROYED)) {
                        hasInvoicableItem.set(true);
                    }
                    if (Objects.equals(status, InvoiceConst.Status.CREATE_FAILED)) {
                        hasInvoicableItem.set(true);
                        hasInvoiceFailure.set(true);
                    }
                    if (Objects.equals(status, InvoiceConst.Status.CREATED) || Objects.equals(status, InvoiceConst.Status.WAITING_DESTROY)) {
                        hasInvoicedItem.set(true);
                        if (MathUtils.wrapBigDecimalCompare(
                                MathUtils.wrapBigDecimalOrZero(feeTypeInvoiceResult.getInvoiceFee()),
                                MathUtils.wrapBigDecimalOrZero(feeTypeInvoiceResult.getReceivedFee())) != 0) {
                            hasInvoiceFeeChanger.set(true);
                        }
                    }
                });
        ChargeUtils.insertChargeSheetAdditionalIfNeed(chargeSheet, Constants.DEFAULT_OPERATORID);
        Integer dbInvoiceStatusFlag = chargeSheet.getAdditional().getInvoiceStatusFlag();
        boolean alreadyInvoiced = dbInvoiceStatusFlag != null && (dbInvoiceStatusFlag & Constants.ChargeSheetInvoiceStatusFlag.HAS_INVOICED_ITEM) != 0;
        // 当前收费单是否已经开过票
        int invoiceStatusFlag = 0;
        if (hasInvoicableItem.get()) { // 开票结果中存在待开票的数据,此时数据库中只可能存在 (待开票 | 金额异常 | 开票异常)
            if (hasInvoiceFailure.get()) {
                invoiceStatusFlag = Constants.ChargeSheetInvoiceStatusFlag.INVOICE_FAIL;
                if (alreadyInvoiced) {
                    invoiceStatusFlag = invoiceStatusFlag | Constants.ChargeSheetInvoiceStatusFlag.HAS_INVOICED_ITEM;
                } else {
                    invoiceStatusFlag = invoiceStatusFlag | Constants.ChargeSheetInvoiceStatusFlag.HAS_INVOICABLE_ITEM;
                }
            } else {
                invoiceStatusFlag = Constants.ChargeSheetInvoiceStatusFlag.HAS_INVOICABLE_ITEM;
            }
            if (hasInvoiceFeeChanger.get()) {
                invoiceStatusFlag = invoiceStatusFlag | Constants.ChargeSheetInvoiceStatusFlag.INVOICE_FEE_CHANGER;
            }
        } else if (hasInvoicedItem.get()) { // 开票结果中存在已开票的数据,且没有待开票+开票失败的数据,此时数据库中只可能存在 (已开票 | 金额异常)
            invoiceStatusFlag = Constants.ChargeSheetInvoiceStatusFlag.HAS_INVOICED_ITEM;
            if (hasInvoiceFeeChanger.get()) {
                invoiceStatusFlag = invoiceStatusFlag | Constants.ChargeSheetInvoiceStatusFlag.INVOICE_FEE_CHANGER;
            }
        }
        chargeSheet.getAdditional().setInvoiceStatusFlag(invoiceStatusFlag);
        FillUtils.fillLastModifiedBy(chargeSheet.getAdditional(), Constants.DEFAULT_OPERATORID);

        rsp.setCode(CreateInvoiceCallbackRsp.Code.OK);
        rsp.setMessage("成功");
        return rsp;
    }

    @Transactional(rollbackFor = Exception.class)
    public CreateInvoiceCallbackRsp callback(CreateInvoiceCallbackReq req) {

        CreateInvoiceCallbackRsp rsp = new CreateInvoiceCallbackRsp();

        String chargeSheetId = req.getBusinessId();

        ChargeSheet chargeSheet = chargeSheetService.findById(chargeSheetId);

        if (chargeSheet == null) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeSheet is null, chargeSheetId: {}", chargeSheetId);
            throw new NotFoundException();
        }

        int invoiceStatus = chargeSheet.getAdditional().getInvoiceStatus();
        if (Constants.ChargeSheetInvoiceStatus.getInvoicedStatues().contains(invoiceStatus)) {
            rsp.setCode(CreateInvoiceCallbackRsp.Code.OK);
            rsp.setMessage("成功");
            return rsp;
        }

        if (invoiceStatus != Constants.ChargeSheetInvoiceStatus.INVOICE_BEING && invoiceStatus != Constants.ChargeSheetInvoiceStatus.INVOICE_FAIL) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "invoiceStatus is not being or not fail, invoiceStatus: {}", invoiceStatus);
            rsp.setCode(CreateInvoiceCallbackRsp.Code.ERROR);
            rsp.setMessage("收费单发票状态错误");
            return rsp;
        }

        chargeSheet.getAdditional().setInvoiceStatus(translateInvoiceStatus(InvoiceView.Type.BLUE, InvoiceView.InvoiceType.DIGITAL_INVOICE, req.getStatus()));

        FillUtils.fillLastModifiedBy(chargeSheet.getAdditional(), Constants.ANONYMOUS_PATIENT_ID);

        rsp.setCode(CreateInvoiceCallbackRsp.Code.OK);
        rsp.setMessage("成功");
        return rsp;
    }

    @Transactional(rollbackFor = Exception.class)
    public ChonghongInvoiceCallbackRsp redCallback(ChonghongInvoiceCallbackReq req) {

        ChonghongInvoiceCallbackRsp rsp = new ChonghongInvoiceCallbackRsp();

        String chargeSheetId = req.getBusinessId();

        ChargeSheet chargeSheet = chargeSheetService.findById(chargeSheetId);

        if (chargeSheet == null) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeSheet is null, chargeSheetId: {}", chargeSheetId);
            throw new NotFoundException();
        }

        int invoiceStatus = chargeSheet.getAdditional().getInvoiceStatus();
        if (invoiceStatus == Constants.ChargeSheetInvoiceStatus.INVOICE_INVALID) {
            rsp.setCode(CreateInvoiceCallbackRsp.Code.OK);
            rsp.setMessage("成功");
            return rsp;
        }

        if (invoiceStatus != Constants.ChargeSheetInvoiceStatus.INVOICE_INVALING) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "invoiceStatus is not being or not fail, invoiceStatus: {}", invoiceStatus);
            rsp.setCode(CreateInvoiceCallbackRsp.Code.ERROR);
            rsp.setMessage("收费单发票状态错误");
            return rsp;
        }

        chargeSheet.getAdditional().setInvoiceStatus(translateInvoiceStatus(InvoiceView.Type.RED, InvoiceView.InvoiceType.DIGITAL_INVOICE, req.getStatus()));

        FillUtils.fillLastModifiedBy(chargeSheet.getAdditional(), Constants.ANONYMOUS_PATIENT_ID);

        rsp.setCode(CreateInvoiceCallbackRsp.Code.OK);
        rsp.setMessage("成功");
        return rsp;
    }

    @Transactional(rollbackFor = Exception.class)
    public void destroyInvoice(ChargeSheet chargeSheet, String operatorId) {
        InvoiceView invoiceView = getInvoiceByBusinessId(chargeSheet.getClinicId(), chargeSheet.getId(), chargeSheet.getType());

        if (invoiceView == null) {
            return;
        }

        //如果是南京吾爱发票则不自动冲红
        if (invoiceView.getInvoiceSupplierId() == InvoiceConfigView.InvoiceSupplierId.NANJING_WUAI) {
            return;
        }

        if ((invoiceView.getInvoiceCategory() == InvoiceConfigView.InvoiceCategory.NORMAL_INVOICE)) {

            InvoiceManagement invoiceManagement = propertyService.getPropertyValueByKey(PropertyKey.INVOICE_MANAGEMENT, chargeSheet.getClinicId(), InvoiceManagement.class);

            int registrationRefundAutoDestroy = 0, outpatientRefundAutoDestroy = 0;
            if (invoiceManagement != null) {
                registrationRefundAutoDestroy = invoiceManagement.getRegistrationRefundAutoDestroy();
                outpatientRefundAutoDestroy = invoiceManagement.getOutpatientRefundAutoDestroy();
            }

            if (chargeSheet.getType() == ChargeSheet.Type.REGISTRATION && registrationRefundAutoDestroy == 0) {
                return;
            }

            if (chargeSheet.getType() != ChargeSheet.Type.REGISTRATION && outpatientRefundAutoDestroy == 0) {
                return;
            }
        }

        destroyInvoiceCore(chargeSheet, invoiceView, operatorId);
    }

    @Override
    public InvoiceView getInvoiceByBusinessId(String clinicId, String chargeSheetId, int chargeSheetType) {
        String businessScene = CreateInvoiceReq.BusinessScene.CHARGE;
        if (chargeSheetType == ChargeSheet.Type.REGISTRATION) {
            businessScene = CreateInvoiceReq.BusinessScene.REGISTRATION;
        }

        return cisInvoiceService.getInvoiceByBusinessId(clinicId, businessScene, chargeSheetId);
    }

    /**
     * 事务提交后异步发送自动开票消息
     *
     * @param chargeSheet         收费单
     * @param chargeTransactionId 收费单流水id
     * @param operatorId          操作人id
     */
    public void asyncSendAutoInvoiceMessageAfterTransactionCommit(ChargeSheet chargeSheet, String chargeTransactionId, String operatorId) {
        MQProducer.doAfterTransactionCommit(
                () -> ExecutorUtils.futureRunAsync(() -> transactionTemplate.executeWithoutResult(
                        transactionStatus -> {
                            try {
                                transactionStatus.setRollbackOnly();
                                sendAutoInvoiceMessage(chargeSheet, chargeTransactionId, operatorId);
                            } catch (Exception e) {
                                log.error(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "asyncSendAutoInvoiceMessageAfterTransactionCommit error, {}", e.getMessage());
                            }
                        })
                ));
    }

    private void sendAutoInvoiceMessage(ChargeSheet chargeSheet, String chargeTransactionId, String operatorId) {
        // 1、获取chargeSheet信息
        if (chargeSheet == null) {
            return;
        }
        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.CHARGED) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "sendAutoInvoiceMessage chargeSheet.status is not charged, status: {}", chargeSheet.getStatus());
            return;
        }
        // 过滤不需要打印发票的类型
        if (!ChargeSheet.Type.enableInvoiceTypes().contains(chargeSheet.getType())) {
            return;
        }

        // 2、校验发票状态
        Integer invoiceStatusFlag = Optional.ofNullable(chargeSheet.getAdditional()).map(ChargeSheetAdditional::getInvoiceStatusFlag).orElse(null);
        // 判断是否有待开票状态发票
        if (Objects.isNull(invoiceStatusFlag) || (invoiceStatusFlag & Constants.ChargeSheetInvoiceStatusFlag.HAS_INVOICABLE_ITEM) == 0) {
            return;
        }

        // 3、查询自动开票设置
        Integer enableAutoBillInvoiceCategory = Optional.ofNullable(cisInvoiceService.getInvoiceBillConfigByClinicId(chargeSheet.getChainId(), chargeSheet.getClinicId()))
                .map(invoiceBillConfigView -> Objects.equals(invoiceBillConfigView.getEnableAutoBill(), 1) ? invoiceBillConfigView.getInvoiceCategory() : null)
                .orElse(null);
        if (Objects.isNull(enableAutoBillInvoiceCategory)) {
            return;
        }
        // 4、获取发票配置
        Integer autoInvoiceSupplierId = cisInvoiceService.getInvoiceConfigAll(chargeSheet.getClinicId())
                .stream()
                .filter(invoiceConfigView -> Objects.equals(invoiceConfigView.getInvoiceCategory(), enableAutoBillInvoiceCategory))
                .map(InvoiceConfigView::getInvoiceSupplierId)
                .findFirst()
                .orElse(null);
        if (Objects.isNull(autoInvoiceSupplierId)) {
            return;
        }

        // 为了避免影响数据库中chargeSheet信息，序列化和反序列化chargeSheet
        chargeSheet = JsonUtils.readValue(JsonUtils.dumpAsJsonNode(chargeSheet), ChargeSheet.class);

        // 5、发送消息
        AutoInvoiceMessage autoInvoiceMessage = new AutoInvoiceMessage();
        autoInvoiceMessage.setMsgId(chargeTransactionId);
        autoInvoiceMessage.setChainId(chargeSheet.getChainId());
        autoInvoiceMessage.setClinicId(chargeSheet.getClinicId());
        autoInvoiceMessage.setPatientId(chargeSheet.getPatientId());
        autoInvoiceMessage.setBusinessId(chargeSheet.getId());
        autoInvoiceMessage.setBusinessScene(chargeSheet.getType() == ChargeSheet.Type.REGISTRATION ? CreateInvoiceReq.BusinessScene.REGISTRATION : CreateInvoiceReq.BusinessScene.CHARGE);
        autoInvoiceMessage.setBusinessCallbackUrl(Constants.BusinessCallBackUrl.INVOICE_STATUS_FLAG_CALLBACK_URL);
        autoInvoiceMessage.setInvoiceFee(MathUtils.wrapBigDecimalAdd(chargeSheet.getReceivedFee(), chargeSheet.getRefundFee()));
        autoInvoiceMessage.setInvoiceCategory(enableAutoBillInvoiceCategory);
        autoInvoiceMessage.setInvoiceSupplierId(autoInvoiceSupplierId);
        ChargeSheet memoryChargeSheet = ChargeUtils.deepCopyChargeSheet(chargeSheet);
        ChargeSheetInvoiceContext chargeSheetInvoiceContext = createChargeSheetInvoiceContext(memoryChargeSheet);
        autoInvoiceMessage.setInvoiceInfo(generateDigitalInvoiceInfoJsonNode(memoryChargeSheet, autoInvoiceSupplierId, operatorId, chargeSheetInvoiceContext));
        autoInvoiceMessage.setOperatorId(operatorId);
        rocketMqProducer.syncSendAutoInvoiceMessage(autoInvoiceMessage);
    }

    /**
     * 事务提交后异步发送发票费用变更消息
     *
     * @param chargeSheet         收费单
     * @param chargeTransactionId 收费单流水id
     * @param operatorId          操作人id
     */
    public void asyncSendInvoicePaidFeeChangeMessageAfterTransactionCommit(ChargeSheet chargeSheet, String chargeTransactionId, String operatorId) {
        MQProducer.doAfterTransactionCommit(
                () -> ExecutorUtils.futureRunAsync(() -> transactionTemplate.executeWithoutResult(
                                transactionStatus -> {
                                    try {
                                        transactionStatus.setRollbackOnly();
                                        sendInvoicePaidFeeChangeMessage(chargeSheet, chargeTransactionId, operatorId);
                                    } catch (Exception e) {
                                        log.error(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "asyncSendInvoicePaidFeeChangeMessageAfterTransactionCommit error: {}", e.getMessage());
                                    }

                                }
                        )
                ));
    }

    private void sendInvoicePaidFeeChangeMessage(ChargeSheet chargeSheet, String chargeTransactionId, String operatorId) {
        // 1、获取chargeSheet
        if (chargeSheet == null) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "sendPaidFeeChangeMessage chargeSheet is null");
            return;
        }

        // 2023年11月23日11:19:03 在余林峰位置处，开发和产品讨论，退费是否需要强制作废已开发票，以保证合规，产品（徐彩云）决定将这个作废交给诊所自己决定，不做强制限制
//        if (!isRenewPaid
//                && chargeSheet.getStatus() != Constants.ChargeSheetStatus.PART_REFUNDED
//                && chargeSheet.getStatus() != Constants.ChargeSheetStatus.REFUNDED) {
//            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "not sendPaidFeeChangeMessage chargeSheetStatus:{}", chargeSheet.getStatus());
//            return;
//        }

        // 过滤不需要打印发票的类型
        if (!ChargeSheet.Type.enableInvoiceTypes().contains(chargeSheet.getType())) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "not sendPaidFeeChangeMessage chargeSheetType:{}", chargeSheet.getType());
            return;
        }

        // 2、校验发票状态，不存在已开发票
        Integer invoiceStatusFlag = Optional.ofNullable(chargeSheet.getAdditional()).map(ChargeSheetAdditional::getInvoiceStatusFlag).orElse(null);
        // 判断是否有待开票状态发票
        if (Objects.isNull(invoiceStatusFlag)) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "not sendPaidFeeChangeMessage invoiceStatusFlag:null");
            return;
        }
        // 为了避免影响数据库中chargeSheet信息，序列化和反序列化chargeSheet
        chargeSheet = JsonUtils.readValue(JsonUtils.dumpAsJsonNode(chargeSheet), ChargeSheet.class);

        // 2、获取患者信息
        PatientInfo patientInfo = null;
        if (!Objects.equals(Constants.ANONYMOUS_PATIENT_ID, chargeSheet.getPatientId())) {
            patientInfo = crmService.searchPatientInfoByPatientIdFromCrm(chargeSheet.getChainId(), chargeSheet.getClinicId(), chargeSheet.getPatientId(), false, false);
        }

        // 3、获取employee信息
        Employee employee = null;
        if (StringUtils.isNotBlank(operatorId) && !StringUtils.equals(operatorId, Constants.DEFAULT_OPERATORID)) {
            employee = employeeService.findById(chargeSheet.getChainId(), operatorId);
        }

        // 4、获取发票配置
        Map<Integer, Integer> categorySupplierIdMap = Optional.ofNullable(cisInvoiceService.getInvoiceConfigAll(chargeSheet.getClinicId()))
                .orElse(new ArrayList<>())
                .stream()
                .filter(invoiceConfigView -> Objects.equals(invoiceConfigView.getStatus(), InvoiceConst.ConfigStatus.ENABLE))
                .collect(Collectors.toMap(InvoiceConfigView::getInvoiceCategory, InvoiceConfigView::getInvoiceSupplierId, (a, b) -> a));

        ChargeSheetInvoicePrintPreviewInfo printPreviewInfo = new ChargeSheetInvoicePrintPreviewInfo();
        // 5、生成增值税电子发票预览数据
        if (categorySupplierIdMap.containsKey(InvoiceConst.Category.VAT_DIGITAL_INVOICE)) {
            ChargeSheet memoryChargeSheet = ChargeUtils.deepCopyChargeSheet(chargeSheet);
            ChargeSheetInvoiceContext chargeSheetInvoiceContext = createChargeSheetInvoiceContext(memoryChargeSheet);
            printPreviewInfo.setDigitalInvoicePrintView(JsonUtils.readValue(digitalInvoiceService.generateDigitalInvoicePrintView(InvoiceConst.Category.VAT_DIGITAL_INVOICE, memoryChargeSheet, patientInfo, chargeSheet.getPatientId(), employee, operatorId, categorySupplierIdMap.get(InvoiceConst.Category.VAT_DIGITAL_INVOICE), chargeSheetInvoiceContext), DigitalInvoicePrintPreview.class));
        }
        // 6、生成 医疗电子票据预览数据
        if (categorySupplierIdMap.containsKey(InvoiceConst.Category.MEDICAL_DIGITAL_INVOICE)) {
            ChargeSheet memoryChargeSheet = ChargeUtils.deepCopyChargeSheet(chargeSheet);
            ChargeSheetInvoiceContext chargeSheetInvoiceContext = createChargeSheetInvoiceContext(memoryChargeSheet);
            printPreviewInfo.setMedicalDigitalInvoicePrintView(JsonUtils.readValue(digitalInvoiceService.generateDigitalInvoicePrintView(InvoiceConst.Category.MEDICAL_DIGITAL_INVOICE, memoryChargeSheet, patientInfo, chargeSheet.getPatientId(), employee, operatorId, categorySupplierIdMap.get(InvoiceConst.Category.MEDICAL_DIGITAL_INVOICE), chargeSheetInvoiceContext), MedicalDigitalInvoicePrintView.class));
        }
        // 7、生成 普通发票数据
        int registrationInvoiceType = InvoiceConst.RegistrationInvoiceType.NORMAL;
        // 使用特殊挂号发票的是。天津：120100，杭州：330100
        List<String> hzRegistrationCityIds = Arrays.asList("120100", "330100");
        boolean isHzRegistrationInvoice = Objects.equals(chargeSheet.getType(), ChargeSheet.Type.REGISTRATION)
                && hzRegistrationCityIds.contains(Optional.ofNullable(clinicService.getClinicById(chargeSheet.getClinicId())).map(ClinicForCharge::getAddressCityId).orElse(null));
        // 判断是否是杭州挂号
        if (isHzRegistrationInvoice) {
            registrationInvoiceType = InvoiceConst.RegistrationInvoiceType.HANGZHOU;
            printPreviewInfo.setHzRegistrationNormalInvoicePrintView(chargePrintService.generateRegistrationInvoicePrintView(chargeSheet));
        } else {
            printPreviewInfo.setNormalInvoicePrintView(findChargeSheetPrintView(chargeSheet));
        }

        // 8、发送消息
        PaidFeeChangeMessage paidFeeChangeMessage = new PaidFeeChangeMessage();
        paidFeeChangeMessage.setMsgId(chargeTransactionId);
        paidFeeChangeMessage.setChainId(chargeSheet.getChainId());
        paidFeeChangeMessage.setClinicId(chargeSheet.getClinicId());
        paidFeeChangeMessage.setPatientId(chargeSheet.getPatientId());
        paidFeeChangeMessage.setEmployeeId(operatorId);
        paidFeeChangeMessage.setBusinessId(chargeSheet.getId());
        paidFeeChangeMessage.setBusinessScene(chargeSheet.getType() == ChargeSheet.Type.REGISTRATION ? CreateInvoiceReq.BusinessScene.REGISTRATION : CreateInvoiceReq.BusinessScene.CHARGE);
        paidFeeChangeMessage.setRegistrationInvoiceType(registrationInvoiceType);
        paidFeeChangeMessage.setBusinessCallbackUrl(Constants.BusinessCallBackUrl.INVOICE_STATUS_FLAG_CALLBACK_URL);
        paidFeeChangeMessage.setInvoiceInfo(JsonUtils.dumpAsJsonNode(printPreviewInfo));
        paidFeeChangeMessage.setMsgId(
                Optional.ofNullable(chargeSheet.getChargeTransactions())
                        .orElse(Lists.newArrayList())
                        .stream()
                        .max(Comparator.comparing(ChargeTransaction::getOperateNo))
                        .map(ChargeTransaction::getId)
                        .orElse(null)
        );
        rocketMqProducer.syncSendInvoicePaidFeeChangeMessage(paidFeeChangeMessage);
    }

    public void destroyInvoiceCore(ChargeSheet chargeSheet, InvoiceView invoiceView, String operatorId) {

        if (invoiceView.getInvoiceCategory() != InvoiceConfigView.InvoiceCategory.NORMAL_INVOICE) {
            cn.abcyun.bis.rpc.sdk.cis.model.invoice.ChonghongInvoiceReq chonghongInvoiceReq = new cn.abcyun.bis.rpc.sdk.cis.model.invoice.ChonghongInvoiceReq();
            chonghongInvoiceReq.setClinicId(chargeSheet.getClinicId());
            chonghongInvoiceReq.setBusinessId(chargeSheet.getId());
            chonghongInvoiceReq.setBusinessScene(chargeSheet.getType() == ChargeSheet.Type.REGISTRATION ? CreateInvoiceReq.BusinessScene.REGISTRATION : CreateInvoiceReq.BusinessScene.CHARGE);
            chonghongInvoiceReq.setOperatorId(operatorId);
            chonghongInvoiceReq.setRemark("退费冲红");
            chonghongInvoiceReq.setOperatorName(Optional.ofNullable(employeeService.findById(chargeSheet.getChainId(), operatorId)).map(Employee::getName).orElse(null));


            ChonghongInvoiceRsp chonghongInvoiceRsp = cisInvoiceService.chonghongInvoice(chonghongInvoiceReq);

            if (chonghongInvoiceRsp == null || (chonghongInvoiceRsp.getStatus() != CreateInvoiceRsp.Status.WAITING && chonghongInvoiceRsp.getStatus() != CreateInvoiceRsp.Status.SUCCESS)) {
                log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "destroyInvoiceRsp.status error");
                throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_INVOICE_DESTROY_ERROR);
            }

            chargeSheet.getAdditional().setInvoiceStatus(Constants.ChargeSheetInvoiceStatus.INVOICE_INVALID);
            FillUtils.fillLastModifiedBy(chargeSheet.getAdditional(), operatorId);

        } else {
            DestroyInvoiceReq req = new DestroyInvoiceReq();
            req.setClinicId(chargeSheet.getClinicId());
            req.setBusinessId(chargeSheet.getId());
            req.setBusinessScene(chargeSheet.getType() == ChargeSheet.Type.REGISTRATION ? CreateInvoiceReq.BusinessScene.REGISTRATION : CreateInvoiceReq.BusinessScene.CHARGE);
            req.setOperatorId(operatorId);
            DestroyInvoiceRsp destroyInvoiceRsp = cisInvoiceService.destroyInvoice(req);
            if (destroyInvoiceRsp == null || (destroyInvoiceRsp.getStatus() != CreateInvoiceRsp.Status.REFUND && destroyInvoiceRsp.getStatus() != CreateInvoiceRsp.Status.FAILED)) {
                log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "destroyInvoiceRsp.status error");
                throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_INVOICE_DESTROY_ERROR);
            }

            chargeSheet.getAdditional().setInvoiceStatus(translateInvoiceStatus(invoiceView.getType(), invoiceView.getInvoiceType(), destroyInvoiceRsp.getStatus()));
            FillUtils.fillLastModifiedBy(chargeSheet.getAdditional(), operatorId);
        }
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public ChargeSheetInvoiceStatusRsp getChargeSheetInvoiceStatus(String chargeSheetId, String clinicId) {

        ChargeSheetAdditional chargeSheetAdditional = chargeSheetAdditionalRepository.findByIdAndClinicIdAndIsDeleted(chargeSheetId, clinicId, 0);

        if (chargeSheetAdditional == null) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeSheetAdditional is null, chargeSheetId: {}", chargeSheetId);
            throw new NotFoundException();
        }

        ChargeSheetInvoiceStatusRsp rsp = new ChargeSheetInvoiceStatusRsp();
        rsp.setChargeSheetId(chargeSheetId)
                .setInvoiceStatus(chargeSheetAdditional.getInvoiceStatus());

        return rsp;
    }

//    @Transactional(rollbackFor = Exception.class)
//    public CreateChargeSheetInvoiceRsp destroyInvoice(String chargeSheetId, String clinicId, String employeeId) {
//
//        ChargeSheet chargeSheet = chargeSheetService.findByIdAndClinicId(chargeSheetId, clinicId);
//
//        if (chargeSheet == null) {
//            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeSheet is null, chargeSheetId: {}", chargeSheetId);
//            throw new NotFoundException();
//        }
//
//        InvoiceView invoiceView = getInvoiceByBusinessId(clinicId, chargeSheetId, chargeSheet.getType());
//
//        if (invoiceView == null) {
//            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "invoiceView is null");
//            throw new NotFoundException();
//        }
//
//        destroyInvoiceCore(chargeSheet, invoiceView, employeeId);
//
//        CreateChargeSheetInvoiceRsp rsp = new CreateChargeSheetInvoiceRsp();
//        rsp.setChargeSheetId(chargeSheetId)
//                .setInvoiceStatus(chargeSheet.getAdditional().getInvoiceStatus());
//
//        return rsp;
//    }

//    @Transactional(readOnly = true)
//    @UseReadOnlyDB
//    public DigitalInvoicePrintPreview getChargeSheetDigitalInvoicePrintPreview(String chargeSheetId, String clinicId, String employeeId) {
//        ChargeSheet chargeSheet = chargeSheetService.findByIdAndClinicId(chargeSheetId, clinicId);
//
//        if (chargeSheet == null) {
//            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX,"chargeSheet is null, chargeSheetId: {}", chargeSheetId);
//            throw new NotFoundException();
//        }
//        InvoiceConfigView invoiceConfig = null;
//
//
//    }

    private ChargeSheetPrintView findChargeSheetPrintView(ChargeSheet chargeSheet) {
        PatientOrder patientOrder = patientOrderService.findPatientOrder(chargeSheet.getPatientOrderId(), PatientOrderService.getAgeLockTime(chargeSheet));
        SheetProcessorDispatcher.ISheetProcessor sheetProcessor = SheetProcessorDispatcher.newSheetProcessor(chargeSheet);
        sheetProcessor.setSheetProcessorInfoProvider(sheetProcessorService);
        sheetProcessor.setPatientOrder(patientOrder);
        sheetProcessor.build();
        return sheetProcessor.generatePrintView();
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public ChargeSheetInvoicePrintPreviewRsp getChargeSheetInvoicePrintPreview(String chargeSheetId,
                                                                               String clinicId,
                                                                               int registrationInvoiceType,
                                                                               String employeeId,
                                                                               int invoiceCategory,
                                                                               Integer invoiceSupplierId) {
        // 如果非普通发票，但是发票供应商为空
        if (invoiceCategory != InvoiceConst.Category.NORMAL_INVOICE && Objects.isNull(invoiceSupplierId)) {
            throw new ParamRequiredException("invoiceSupplierId");
        }
        ChargeSheetInvoicePrintPreviewRsp rsp = new ChargeSheetInvoicePrintPreviewRsp();
        rsp.setChargeSheetId(chargeSheetId);
        rsp.setRegistrationInvoiceType(registrationInvoiceType);
        rsp.setClinicId(clinicId);
        rsp.setEmployeeId(employeeId);
        rsp.setInvoiceSupplierId(invoiceSupplierId);
        // 1、获取chargeSheet
        ChargeSheet chargeSheet = chargeSheetService.findByIdAndClinicId(chargeSheetId, clinicId);
        if (chargeSheet == null) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeSheet is null, chargeSheetId: {}", chargeSheetId);
            throw new NotFoundException();
        }
        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.CHARGED && chargeSheet.getStatus() != Constants.ChargeSheetStatus.PART_REFUNDED && chargeSheet.getStatus() != Constants.ChargeSheetStatus.REFUNDED) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeSheet status error, status: {}", chargeSheet.getStatus());
            throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_STATUS_IS_NOT_CHARGED);
        }

        // 2、获取患者信息
        PatientInfo patientInfo = null;
        if (!Objects.equals(Constants.ANONYMOUS_PATIENT_ID, chargeSheet.getPatientId())) {
            patientInfo = crmService.searchPatientInfoByPatientIdFromCrm(chargeSheet.getChainId(), chargeSheet.getClinicId(), chargeSheet.getPatientId(), false, false);
        }

        // 3、获取employee信息
        Employee employee = employeeService.findById(chargeSheet.getChainId(), employeeId);

        // 4、生成增值税电子发票预览数据
        switch (invoiceCategory) {
            case InvoiceConst.Category.NORMAL_INVOICE:
                boolean isHzRegistrationInvoice = Objects.equals(registrationInvoiceType, CreateChargeSheetInvoiceReq.RegistrationInvoiceType.HANGZHOU);
                // 4.1、判断是否是杭州挂号
                if (isHzRegistrationInvoice) {
                    rsp.setHzRegistrationNormalInvoicePrintView(chargePrintService.generateRegistrationInvoicePrintView(chargeSheet));
                } else {
                    rsp.setNormalInvoicePrintView(findChargeSheetPrintView(chargeSheet));
                }
                return rsp;
            case InvoiceConst.Category.VAT_DIGITAL_INVOICE:
                // 增值税电子发票
                ChargeSheet memoryChargeSheet = ChargeUtils.deepCopyChargeSheet(chargeSheet);
                ChargeSheetInvoiceContext chargeSheetInvoiceContext = this.createChargeSheetInvoiceContext(memoryChargeSheet);
                rsp.setDigitalInvoicePrintView(JsonUtils.readValue(digitalInvoiceService.generateDigitalInvoicePrintView(InvoiceConst.Category.VAT_DIGITAL_INVOICE, memoryChargeSheet, patientInfo, chargeSheet.getPatientId(), employee, employeeId, invoiceSupplierId, chargeSheetInvoiceContext), DigitalInvoicePrintPreview.class));
                return rsp;
            case InvoiceConst.Category.MEDICAL_DIGITAL_INVOICE:
                // 医疗电子发票
                ChargeSheet memoryChargeSheet2 = ChargeUtils.deepCopyChargeSheet(chargeSheet);
                ChargeSheetInvoiceContext invoiceContext = this.createChargeSheetInvoiceContext(memoryChargeSheet2);
                rsp.setMedicalDigitalInvoicePrintView(JsonUtils.readValue(digitalInvoiceService.generateDigitalInvoicePrintView(InvoiceConst.Category.MEDICAL_DIGITAL_INVOICE, memoryChargeSheet2, patientInfo, chargeSheet.getPatientId(), employee, employeeId, invoiceSupplierId, invoiceContext), MedicalDigitalInvoicePrintView.class));
                return rsp;
            case InvoiceConst.Category.TAX_FULLY_DIGITAL_INVOICE:
                ChargeSheet _memoryChargeSheet = ChargeUtils.deepCopyChargeSheet(chargeSheet);
                ChargeSheetInvoiceContext _chargeSheetInvoiceContext = this.createChargeSheetInvoiceContext(_memoryChargeSheet);
                rsp.setDigitalInvoicePrintView(JsonUtils.readValue(digitalInvoiceService.generateDigitalInvoicePrintView(InvoiceConst.Category.TAX_FULLY_DIGITAL_INVOICE, _memoryChargeSheet, patientInfo, chargeSheet.getPatientId(), employee, employeeId, invoiceSupplierId, _chargeSheetInvoiceContext), DigitalInvoicePrintPreview.class));
                return rsp;
            default:
                return rsp;
        }
    }

    private RegistrationInvoicePrintView negateRegistrationInvoicePrintView(RegistrationInvoicePrintView registrationInvoicePrintView) {
        if (registrationInvoicePrintView == null) {
            return null;
        }
        Optional.ofNullable(registrationInvoicePrintView.getRegistrationFee()).ifPresent(fee -> registrationInvoicePrintView.setRegistrationFee(fee.negate()));
        return registrationInvoicePrintView;
    }

    private ChargeSheetPrintView.ShebaoPaymentExtend queryChonghongInvoiceShebaoPayment(String chainId, String clinicId, String doctorId, int payTaskType, ChargeSheetPrintView chargeSheetPrintView) {
        QueryChargeSheetShebaoInfoReq req = new QueryChargeSheetShebaoInfoReq();
        req.setChainId(chainId);
        req.setClinicId(clinicId);
        req.setDoctorId(doctorId);
        req.setChargeSheetId(chargeSheetPrintView.getId());
        req.setPayTaskType(payTaskType);
        req.setIsCharged(QueryChargeSheetShebaoInfoReq.IsCharged.CHARGED);
        req.setSheetReceivableFee(MathUtils.wrapBigDecimalOrZero(chargeSheetPrintView.getNetIncomeFee()).abs());
        if (chargeSheetPrintView.getChargeForms() != null) {
            req.setGoodsItems(chargeSheetPrintView.getChargeForms()
                    .stream()
                    .filter(chargeFormPrintView -> chargeFormPrintView.getChargeFormItems() != null)
                    .flatMap(chargeFormPrintView -> chargeFormPrintView.getChargeFormItems().stream())
                    .filter(chargeFormItemPrintView -> !TextUtils.isEmpty(chargeFormItemPrintView.getProductId()))
                    .map(chargeFormItemPrintView -> {
                        List<QueryChargeSheetShebaoInfoReq.GoodsItem> goodsItems = new ArrayList<>();
                        ChargeSheetFeeProtocol.addChildrenShebaoInfoGoodsItem(chargeFormItemPrintView, goodsItems, true);
                        return goodsItems;
                    })
                    .flatMap(Collection::stream)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList()));
        }

        QueryChargeSheetShebaoInfoRsp rsp = shebaoService.queryChargeSheetShebaoInfo(req);

        QueryChargeSheetShebaoInfoRsp.ShebaoPayment shebaoPayment = Optional.ofNullable(rsp).map(QueryChargeSheetShebaoInfoRsp::getShebaoPayment).orElse(null);

        if (shebaoPayment == null) {
            return null;
        }

        ChargeSheetPrintView.ShebaoPaymentExtend shebaoPaymentExtend = new ChargeSheetPrintView.ShebaoPaymentExtend();
        BeanUtils.copyProperties(shebaoPayment, shebaoPaymentExtend);

        return shebaoPaymentExtend;
    }

    /**
     * 发票信息取反
     *
     * @param chargeSheetId 收费单id
     * @param req           请求参数
     * @return 结果
     */
    public NegateChargeSheetInvoicePrintPreviewRsp negateChargeSheetInvoicePrintInfo(String chargeSheetId, NegateChargeSheetInvoicePrintPreviewReq req) {
        String clinicId = req.getClinicId();
        if (StringUtils.isBlank(clinicId)) {
            throw new ParamRequiredException("clinicId");
        }
        // 1、获取chargeSheet
        ChargeSheet chargeSheet = chargeSheetService.findByIdAndClinicId(chargeSheetId, clinicId);
        if (chargeSheet == null) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeSheet is null, chargeSheetId: {}", chargeSheetId);
            throw new NotFoundException();
        }
        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.CHARGED && chargeSheet.getStatus() != Constants.ChargeSheetStatus.PART_REFUNDED && chargeSheet.getStatus() != Constants.ChargeSheetStatus.REFUNDED) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeSheet status error, status: {}", chargeSheet.getStatus());
            throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_STATUS_IS_NOT_CHARGED);
        }
        // 2、取反
        NegateChargeSheetInvoicePrintPreviewRsp negateChargeSheetInvoicePrintInfoRsp = new NegateChargeSheetInvoicePrintPreviewRsp()
                .setChargeSheetId(chargeSheetId)
                .setClinicId(clinicId);
        // 增值税电子发票取反
        negateChargeSheetInvoicePrintInfoRsp.setDigitalInvoicePrintView(
                Optional.ofNullable(req.getDigitalInvoicePrintView())
                        .map(digitalInvoicePrintPreview -> {
                            Optional.ofNullable(digitalInvoicePrintPreview.getInvoiceFee())
                                    .ifPresent(fee -> digitalInvoicePrintPreview.setInvoiceFee(fee.abs().negate()));
                            List<CreateInvoiceReq.InvoiceDetailItem> itemList = digitalInvoicePrintPreview.getItems();
                            if (CollectionUtils.isNotEmpty(itemList)) {
                                itemList.forEach(item -> {
                                    Optional.ofNullable(item.getNum()).ifPresent(num -> item.setNum(num.negate()));
                                    Optional.ofNullable(item.getTaxRate()).ifPresent(taxRate -> item.setTaxRate(taxRate.negate()));
                                    Optional.ofNullable(item.getTaxIncludedAmount()).ifPresent(fee -> item.setTaxIncludedAmount(fee.negate()));
                                });
                            }
                            return digitalInvoicePrintPreview;
                        })
                        .orElse(null)
        );
        // 医疗电子发票取反
        negateChargeSheetInvoicePrintInfoRsp.setMedicalDigitalInvoicePrintView(
                Optional.ofNullable(req.getMedicalDigitalInvoicePrintView())
                        .map(medicalDigitalInvoicePrintView -> {
                            Optional.ofNullable(medicalDigitalInvoicePrintView.getInvoiceFee())
                                    .ifPresent(fee -> medicalDigitalInvoicePrintView.setInvoiceFee(fee.abs().negate()));

                            if (CollectionUtils.isNotEmpty(medicalDigitalInvoicePrintView.getItems())) {
                                medicalDigitalInvoicePrintView.getItems()
                                        .forEach(item -> {
                                            Optional.ofNullable(item.getAmt()).ifPresent(fee -> item.setAmt(fee.negate()));
                                            Optional.ofNullable(item.getSelfAmt()).ifPresent(fee -> item.setSelfAmt(fee.negate()));
                                        });
                            }
                            if (CollectionUtils.isNotEmpty(medicalDigitalInvoicePrintView.getChargeDetails())) {
                                medicalDigitalInvoicePrintView.getChargeDetails()
                                        .forEach(chargeDetail -> {
                                            Optional.ofNullable(chargeDetail.getAmt()).ifPresent(fee -> chargeDetail.setAmt(fee.negate()));
                                            Optional.ofNullable(chargeDetail.getSelfAmt()).ifPresent(fee -> chargeDetail.setSelfAmt(fee.negate()));
                                        });
                            }
                            return medicalDigitalInvoicePrintView;
                        })
                        .orElse(null)
        );
        // 杭州挂号普通发票取反
        negateChargeSheetInvoicePrintInfoRsp.setHzRegistrationNormalInvoicePrintViews(
                Optional.ofNullable(req.getHzRegistrationNormalInvoicePrintViews())
                        .orElse(Lists.newArrayList())
                        .stream()
                        .map(this::negateRegistrationInvoicePrintView)
                        .collect(Collectors.toList())
        );
        // 非杭州挂号（普通收费单、非杭州挂号单）普通发票 取反
        negateChargeSheetInvoicePrintInfoRsp.setNormalInvoicePrintViews(
                Optional.ofNullable(req.getNormalInvoicePrintViews())
                        .orElse(Lists.newArrayList())
                        .stream()
                        .map(normalInvoicePrintView -> negateChargeSheetPrintView(chargeSheet.getChainId(), chargeSheet.getClinicId(), chargeSheet.getDoctorId(), normalInvoicePrintView.setId(chargeSheetId)))
                        .collect(Collectors.toList())
        );
        return negateChargeSheetInvoicePrintInfoRsp;
    }

    private ChargeSheetInvoiceContext createChargeSheetInvoiceContext(ChargeSheet memoryChargeSheet) {
        ChargeUtils.loadChargeSheetProductInfo(memoryChargeSheet, scGoodsService);

        Organ organ = organProductService.findOrganById(memoryChargeSheet.getClinicId());
        // 过滤有效的发票项
        List<ChargeFormItem> filterChargeFormItems = this.filterEffectiveInvoiceChargeFormItems(memoryChargeSheet, organ.getHisType());

        // 生成发票项基本发票信息
        List<InvoiceChargeFormItem> invoiceChargeFormItems = new ArrayList<>();
        if (!CollectionUtils.isEmpty(filterChargeFormItems)) {
            Map<String, List<ChargeFormItem>> refundChargeFormItemsMap = ChargeUtils.generateRefundChargeFormItemsMap(memoryChargeSheet);
            filterChargeFormItems.forEach(chargeFormItem -> {
                InvoiceChargeFormItem invoiceChargeFormItem = new InvoiceChargeFormItem();
                invoiceChargeFormItem.setChargeFormItem(chargeFormItem);
                invoiceChargeFormItem.setInvoiceInfo(generateChargeFormItemInvoiceBaseInfo(chargeFormItem, memoryChargeSheet.getChargeVersion(), refundChargeFormItemsMap));
                invoiceChargeFormItems.add(invoiceChargeFormItem);
            });
        }

        ChargeSheetInvoiceContext invoiceContext = new ChargeSheetInvoiceContext();
        invoiceContext.setInvoiceChargeFormItems(invoiceChargeFormItems);
        List<GoodsSystemType> systemTypes = scGoodsService.getSystemTypes();
        Map<String, GoodsSystemType> goodsTypeKeyToGoodsTypeMap = Optional.ofNullable(systemTypes).orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.toMap(it -> GoodsFeeTypeConstants.goodsTypeKey(it.getType(), it.getSubType(), it.getCMSpec()), Function.identity(), (a, b) -> a));
        invoiceContext.setGoodsTypeKeyToGoodsTypeMap(goodsTypeKeyToGoodsTypeMap);
        return invoiceContext;
    }

    /**
     * 过滤出有效的需要上传发票的item列表
     *
     * @param chargeSheet
     * @return
     */
    protected List<ChargeFormItem> filterEffectiveInvoiceChargeFormItems(ChargeSheet chargeSheet, int hisType) {

        //更新子项的productType改为与母项一致
        updateFeeChildProductType(chargeSheet, hisType);

        return ChargeUtils.getChargeSheetItems(chargeSheet)
                .stream()
                .filter(chargeFormItem -> chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED)
                .filter(chargeFormItem -> !chargeFormItem.isParentItem())
                .filter(chargeFormItem -> chargeFormItem.getSourceItemType() != Constants.SourceItemType.SELF_PROVIDED)
                .collect(Collectors.toList());
    }


    private void updateFeeChildProductType(ChargeSheet chargeSheet, int hisType) {
        if (hisType != Organ.HisType.CIS_HIS_TYPE_NORMAL
                && hisType != Organ.HisType.CIS_HIS_TYPE_DENTISTRY
                && hisType != Organ.HisType.CIS_HIS_TYPE_EYE) {
            return;
        }

        if (Objects.isNull(chargeSheet)) {
            return;
        }

        Map<String, ChargeFormItem> feeParentItemIdMap = ChargeUtils.getChargeSheetItems(chargeSheet)
                .stream()
                .filter(chargeFormItem -> chargeFormItem.getGoodsFeeType() == GoodsFeeType.FEE_PARENT)
                .collect(Collectors.toMap(ChargeFormItem::getId, Function.identity(), (a, b) -> a));

        if (MapUtils.isEmpty(feeParentItemIdMap)) {
            return;
        }

        ChargeUtils.getChargeSheetItems(chargeSheet)
                .stream()
                .filter(chargeFormItem -> chargeFormItem.getGoodsFeeType() == GoodsFeeType.FEE_CHILD)
                .filter(chargeFormItem -> StringUtils.isNotEmpty(chargeFormItem.getComposeParentFormItemId()))
                .forEach(chargeFormItem -> {

                    ChargeFormItem feeParentItem = feeParentItemIdMap.get(chargeFormItem.getComposeParentFormItemId());

                    if (Objects.isNull(feeParentItem)) {
                        return;
                    }

                    chargeFormItem.setProductType(feeParentItem.getProductType());
                    chargeFormItem.setProductSubType(feeParentItem.getProductSubType());
                    chargeFormItem.setFeeTypeId(feeParentItem.getFeeTypeId());
                });

    }


    protected static InvoiceChargeFormItem.InvoiceInfo generateChargeFormItemInvoiceBaseInfo(ChargeFormItem chargeFormItem, int chargeVersion, Map<String, List<ChargeFormItem>> refundChargeFormItemsMap) {
        InvoiceChargeFormItem.InvoiceInfo baseInfo = new InvoiceChargeFormItem.InvoiceInfo();
        BigDecimal totalCount = cn.abcyun.cis.charge.util.MathUtils.calculateTotalCount(chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount());
        if (cn.abcyun.cis.charge.util.MathUtils.wrapBigDecimalCompare(totalCount, BigDecimal.ZERO) <= 0) {
            return baseInfo;
        }

        BigDecimal refundCount = MathUtils.calculateTotalCount(chargeFormItem.getRefundUnitCount(), chargeFormItem.getRefundDoseCount());
        BigDecimal realCount = MathUtils.wrapBigDecimalSubtract(totalCount, refundCount);
        if (MathUtils.wrapBigDecimalCompare(realCount, BigDecimal.ZERO) <= 0) {
            return baseInfo;
        }

        BigDecimal discountedTotalPrice = chargeFormItem.calculateDiscountedPrice(chargeVersion);
        BigDecimal refundDiscountedTotalPrice = BigDecimal.ZERO;
        BigDecimal refundTotalPrice = BigDecimal.ZERO;
        List<ChargeFormItem> refundChargeItems = refundChargeFormItemsMap.get(chargeFormItem.getId());
        if (!CollectionUtils.isEmpty(refundChargeItems)) {
            refundDiscountedTotalPrice = refundChargeItems.stream()
                    .map(refundChargeItem -> refundChargeItem.calculateDiscountedPrice(chargeVersion))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            refundTotalPrice = refundChargeItems.stream()
                    .map(refundChargeItem -> MathUtils.calculateTotalPrice(refundChargeItem.getUnitPrice(), MathUtils.calculateTotalCount(refundChargeItem.getUnitCount(), refundChargeItem.getDoseCount()), 6))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        BigDecimal realDiscountedTotalPrice = MathUtils.wrapBigDecimalSubtract(discountedTotalPrice, refundDiscountedTotalPrice);
        if (MathUtils.wrapBigDecimalCompare(realDiscountedTotalPrice, BigDecimal.ZERO) <= 0) {
            return baseInfo;
        }

        BigDecimal unitPrice = chargeFormItem.getUnitPrice();
        BigDecimal totalPrice = MathUtils.wrapBigDecimalSubtract(MathUtils.calculateTotalPrice(chargeFormItem.getUnitPrice(), totalCount, 2), refundTotalPrice);
        if (MathUtils.wrapBigDecimalCompare(discountedTotalPrice, totalPrice) != 0) {
            unitPrice = discountedTotalPrice.divide(totalCount, 8, RoundingMode.DOWN);
        }

        baseInfo.setCount(realCount);
        baseInfo.setUnitPrice(unitPrice);
        baseInfo.setDiscountedTotalPrice(realDiscountedTotalPrice);

        return baseInfo;
    }

}
