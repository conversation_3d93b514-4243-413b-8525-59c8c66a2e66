package cn.abcyun.cis.charge.service;

import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ;
import cn.abcyun.bis.rpc.sdk.cis.model.common.BitFlagUtils;
import cn.abcyun.cis.charge.api.model.CreateChargeSheetForExaminationInspectionReq;
import cn.abcyun.cis.charge.api.model.CreateChargeSheetForExaminationInspectionRsp;
import cn.abcyun.cis.charge.api.model.CreateChargeSheetReq;
import cn.abcyun.cis.charge.base.ChargeVersionConstants;
import cn.abcyun.cis.charge.base.GoodsLockScene;
import cn.abcyun.cis.charge.controller.StatusNameTranslator;
import cn.abcyun.cis.charge.controller.api.ChargeFormItemMerger;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.model.ChargeSheetAdditional;
import cn.abcyun.cis.charge.processor.ChargeSheetFeeProtocol;
import cn.abcyun.cis.charge.processor.PharmacySheetBatchExtractProcessor;
import cn.abcyun.cis.charge.processor.SheetProcessor;
import cn.abcyun.cis.charge.processor.SheetProcessorDispatcher;
import cn.abcyun.cis.charge.service.dto.ChargeSheetExtend;
import cn.abcyun.cis.charge.service.dto.CreateChargeSheetAndPatientOrderInfo;
import cn.abcyun.cis.charge.service.dto.CreateChargeSheetAndPatientOrderResult;
import cn.abcyun.cis.charge.service.dto.DTOConverter;
import cn.abcyun.cis.charge.service.rpc.CisScClinicService;
import cn.abcyun.cis.charge.util.ChargeSheetBatchExtractUtils;
import cn.abcyun.cis.charge.util.ChargeUtils;
import cn.abcyun.cis.charge.util.JsonUtils;
import cn.abcyun.cis.commons.amqp.message.ChargeSheetMessage;
import cn.abcyun.cis.commons.amqp.message.PatientOrderMessage;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Objects;
import java.util.Optional;

@Service
@Slf4j
public class ChargeExaminationService {

    @Autowired
    private ChargeService chargeService;

    @Autowired
    private SheetProcessorService sheetProcessorService;

    @Autowired
    private ChargeExecuteService chargeExecuteService;

    @Autowired
    private ChargeSheetService chargeSheetService;

    @Autowired
    private TobMessageService tobMessageService;

    @Autowired
    private CisScClinicService scClinicService;

    /**
     * 检查站开单
     *
     * @param examinationInspectionReq 请求参数
     * @return 结构
     */
    @Transactional(rollbackFor = Exception.class)
    public CreateChargeSheetForExaminationInspectionRsp createChargeSheetForExaminationInspection(CreateChargeSheetForExaminationInspectionReq examinationInspectionReq) {

        String clinicId = examinationInspectionReq.getClinicId();
        String operatorId = examinationInspectionReq.getOperatorId();

        CreateChargeSheetReq createChargeSheetReq = convertToBaseCreateChargeSheetReq(examinationInspectionReq);

        Organ organ = Optional.ofNullable(scClinicService.getOrgan(clinicId))
                .orElseThrow(() -> new NotFoundException("诊所未找到"));

        CreateChargeSheetAndPatientOrderInfo createChargeSheetAndPatientOrderInfo = new CreateChargeSheetAndPatientOrderInfo();
        createChargeSheetAndPatientOrderInfo.setCreateType(CreateChargeSheetAndPatientOrderInfo.CreateType.CREATE_FOR_EXAMINATION_INSPECTION);
        createChargeSheetAndPatientOrderInfo.setCreateChargeSheetReq(createChargeSheetReq);
        createChargeSheetAndPatientOrderInfo.setClinicId(clinicId);
        createChargeSheetAndPatientOrderInfo.setChainId(examinationInspectionReq.getChainId());
        createChargeSheetAndPatientOrderInfo.setHisType(organ.getHisType());
        createChargeSheetAndPatientOrderInfo.setChargeVersion(ChargeVersionConstants.convertChargeVersion(organ.getHisType()));
        createChargeSheetAndPatientOrderInfo.setOperatorId(operatorId);

        CreateChargeSheetAndPatientOrderResult createChargeSheetAndPatientOrderResult = chargeService.createChargeSheetAndPatientOrder(createChargeSheetAndPatientOrderInfo);

        ChargeSheet chargeSheet = createChargeSheetAndPatientOrderResult.getChargeSheet();
        PatientOrder patientOrder = createChargeSheetAndPatientOrderResult.getPatientOrder();
        ChargeSheetBatchExtractUtils.MergeChargeSheetResult batchExtractChargeSheetInfo = createChargeSheetAndPatientOrderResult.getBatchExtractChargeSheetInfo();

        ChargeUtils.insertChargeSheetAdditionalIfNeed(chargeSheet, operatorId);

        chargeExecuteService.createOrUpdateChargeExecuteItems(chargeSheet, true, operatorId);

        SheetProcessorDispatcher.ISheetProcessor sheetProcessor = null;
        if (chargeSheet.isBatchExtractDirectChargeSheet()) {
            PharmacySheetBatchExtractProcessor batchExtractProcessor = SheetProcessorDispatcher.newPharmacySheetBatchExtractProcessor(chargeSheet);
            batchExtractProcessor.setOperatorId(operatorId);
            batchExtractProcessor.setSheetProcessorInfoProvider(sheetProcessorService);
            batchExtractProcessor.setPatientOrder(patientOrder);
            batchExtractProcessor.build(Optional.ofNullable(batchExtractChargeSheetInfo)
                    .map(ChargeSheetBatchExtractUtils.MergeChargeSheetResult::getChargeFormItemIdDtoMap)
                    .orElse(new HashMap<>())
            );
            sheetProcessor = batchExtractProcessor;
        } else {
            sheetProcessor = SheetProcessorDispatcher.newSheetProcessor(chargeSheet);
            sheetProcessor.setOperatorId(operatorId);
            sheetProcessor.setSheetProcessorInfoProvider(sheetProcessorService);
            sheetProcessor.setPatientOrder(patientOrder);
            sheetProcessor.build();
        }

        chargeSheet = sheetProcessor.updateProductInfoAndBindMemberId();


        if (chargeSheet.isBatchExtractDirectChargeSheet() && Objects.nonNull(batchExtractChargeSheetInfo) && CollectionUtils.isNotEmpty(batchExtractChargeSheetInfo.getOriginalChargeSheets())) {
            for (ChargeSheet originalChargeSheet : batchExtractChargeSheetInfo.getOriginalChargeSheets()) {
                chargeService.goodsLockAndSaveBatchInfo(originalChargeSheet, GoodsLockScene.RELEASE_LOCK, false, operatorId);
                originalChargeSheet.deleteModel(operatorId);
                chargeService.notifyChargeSheetStatusChanged(chargeSheet, operatorId, PatientOrderMessage.MSG_TYPE_CHARGE_DELETED);
                chargeService.doAfterChargeSheetChange(chargeSheet, Lists.newArrayList(), Lists.newArrayList(), Maps.newHashMap(), ChargeSheetMessage.MSG_TYPE_DELETE, null,null, operatorId);
                chargeSheetService.save(originalChargeSheet, null);
            }
        }
        chargeService.goodsLockAndSaveBatchInfo(chargeSheet, GoodsLockScene.ONLY_LOCK_BILLING, false, operatorId);

        chargeSheetService.save(chargeSheet, null);

        chargeService.notifyChargeSheetStatusChanged(chargeSheet, operatorId, PatientOrderMessage.MSG_TYPE_CHARGE_CREATED);
        tobMessageService.pushChargeTodoMessage(chargeSheet.getChainId(), chargeSheet.getClinicId(), chargeSheet.getType());
        chargeService.doAfterChargeSheetChange(chargeSheet, Lists.newArrayList(), Lists.newArrayList(), Maps.newHashMap(), ChargeSheetMessage.MSG_TYPE_CREATED, null,null, operatorId);
        SheetProcessorDispatcher.ISheetProcessor finalSheetProcessor = sheetProcessor;
        chargeService.sendPushAutoPrintMessageToWeb(chargeSheet.getId(), chargeSheet.getChainId(), chargeSheet.getClinicId(), chargeSheet.getPatientOrderId(), chargeSheet.getType(), ChargeSheetFeeProtocol.generatePrintable(chargeSheet, patientOrder, () -> finalSheetProcessor.getSheetProcessorInfoProvider().getPropertyProvider().getPrintMedicalDocumentsInfusionAndTreatmentContent(patientOrder.getClinicId())));

        CreateChargeSheetForExaminationInspectionRsp rsp = new CreateChargeSheetForExaminationInspectionRsp();

        rsp.setStatusName(StatusNameTranslator.translateChargeSheetStatus(chargeSheet.getOwedStatus(), chargeSheet.getStatus()));
        rsp.setStatus(chargeSheet.getStatus());
        rsp.setPatientOrderId(chargeSheet.getPatientOrderId());
        rsp.setId(chargeSheet.getId());

        ChargeSheet memoryChargeSheet = ChargeUtils.deepCopyChargeSheet(chargeSheet);
        ChargeSheetExtend chargeSheetExtend = DTOConverter.convertToChargeSheetExtend(memoryChargeSheet);
        ChargeFormItemMerger.mergeForChargeSheet(chargeSheetExtend);
        ChargeSheetMessage.ChargeSheet messageChargeSheet = ChargeUtils.convertToMessageChargeSheet(chargeSheetExtend, new ArrayList<>(), Lists.newArrayList(), Maps.newHashMap(), Maps.newHashMap());
        rsp.setMessageChargeSheet(JsonUtils.dumpAsJsonNode(messageChargeSheet));

        return rsp;
    }

    private static CreateChargeSheetReq convertToBaseCreateChargeSheetReq(CreateChargeSheetForExaminationInspectionReq examinationInspectionReq) {

        if (Objects.isNull(examinationInspectionReq)) {
            return null;
        }

        CreateChargeSheetReq req = new CreateChargeSheetReq();
        BeanUtils.copyProperties(examinationInspectionReq, req);
        req.setPatient(ChargeUtils.convertCommonCisPatientInfoFromSdkCisPatientInfo(examinationInspectionReq.getPatient()));

        return req;
    }

}
