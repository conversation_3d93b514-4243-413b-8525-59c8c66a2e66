package cn.abcyun.cis.charge.service;

import cn.abcyun.bis.rpc.sdk.cis.model.pay.PayStatus;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.model.ChargeAbnormalTransaction;
import cn.abcyun.cis.charge.model.ChargePayTransaction;
import cn.abcyun.cis.charge.model.ThirdPartyPayInfo;
import cn.abcyun.cis.charge.processor.ChargePayInfo;
import cn.abcyun.cis.charge.processor.ChargePayRefundInfo;
import cn.abcyun.cis.charge.processor.provider.ChargePayHandleProvider;
import cn.abcyun.cis.charge.service.dto.SaveChargePayTransactionResult;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.Optional;

@Slf4j
public abstract class ChargePayHandleAbstractService implements ChargePayHandleProvider {

    protected final ChargeSheetService chargeSheetService;
    protected final ChargePayTransactionService chargePayTransactionService;
    protected final ChargeAbnormalTransactionService chargeAbnormalTransactionService;

    public ChargePayHandleAbstractService(ChargeSheetService chargeSheetService,
                                          ChargePayTransactionService chargePayTransactionService,
                                          ChargeAbnormalTransactionService chargeAbnormalTransactionService) {
        this.chargeAbnormalTransactionService = chargeAbnormalTransactionService;
        this.chargeSheetService = chargeSheetService;
        this.chargePayTransactionService = chargePayTransactionService;
    }


    public ChargePayTransaction saveChargePayTransactionForPay(String transactionId, ChargePayInfo payInfo, BigDecimal amount,
                                                               BigDecimal principalAmount, BigDecimal presentAmount, int payStatus,
                                                               int payType, String payTransactionId, BigDecimal accountBalance,
                                                               ThirdPartyPayInfo thirdPartyPayInfo,
                                                               JsonNode payReq,
                                                                String groupId) {

        SaveChargePayTransactionResult result = chargePayTransactionService.saveChargePayTransaction(transactionId, payInfo.getChainId(), payInfo.getClinicId(), payInfo.getChargeSheetId(),
                null, payInfo.getChargeActionId(), payInfo.getPatientId(), payInfo.getPatientOrderId(),
                payInfo.getPaySource(), payInfo.getPayMode(), payInfo.getPaySubMode(), payInfo.getPayModeName(), payInfo.getIsCannotCalculateRounding(),
                amount, principalAmount, presentAmount, payStatus,
                payType, payTransactionId, null, accountBalance, thirdPartyPayInfo, payReq, payInfo.getChargeComment(), payInfo.getSpecifiedChargedTime(), payInfo.getOperatorId(), groupId, getChargePayTransactionExpireTime(true));

        if (payInfo.getPayMode() != Constants.ChargePayMode.HEALTH_CARD && payStatus == PayStatus.SUCCESS) {
            handleExceptionAfterTransaction(result.getAbnormalTransaction());
        }

        return result.getChargePayTransaction();
    }

    public ChargePayTransaction saveChargePayTransactionForRefund(String transactionId, ChargePayRefundInfo refundPayInfo, BigDecimal amount,
                                                                  BigDecimal principalAmount, BigDecimal presentAmount, int payStatus,
                                                                  int payType, String payTransactionId, String associatePayTransactionId, BigDecimal accountBalance, ThirdPartyPayInfo thirdPartyPayInfo) {

        SaveChargePayTransactionResult result = chargePayTransactionService.saveChargePayTransaction(transactionId, refundPayInfo.getChainId(), refundPayInfo.getClinicId(), refundPayInfo.getChargeSheetId(),
                refundPayInfo.getRefundSheetId(), refundPayInfo.getChargeActionId(), refundPayInfo.getPatientId(), refundPayInfo.getPatientOrderId(),
                refundPayInfo.getPaySource(), refundPayInfo.getPayMode(), refundPayInfo.getPaySubMode(), refundPayInfo.getPayModeName(), 0,
                amount, principalAmount, presentAmount, payStatus,
                payType, payTransactionId, associatePayTransactionId, accountBalance, thirdPartyPayInfo, null, refundPayInfo.getChargeComment(), null, refundPayInfo.getOperatorId(), null, getChargePayTransactionExpireTime(false));

        return result.getChargePayTransaction();
    }


    public void handleExceptionAfterTransaction(ChargeAbnormalTransaction abnormalTransaction) {
        if (abnormalTransaction == null) {
            return;
        }
        if (TransactionSynchronizationManager.isActualTransactionActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCompletion(int status) {
                    if (status == TransactionSynchronization.STATUS_COMMITTED) {
                        chargeAbnormalTransactionService.deleteById(abnormalTransaction.getId());
                        return;
                    }

                    if (status == TransactionSynchronization.STATUS_ROLLED_BACK) {
                        chargeSheetService.updateChargeSheetExceptionType(abnormalTransaction);
                        return;
                    }
                }
            });
        }
    }

    public Instant getChargePayTransactionExpireTime(boolean isForPay) {
        if (isForPay) {
            return Optional.ofNullable(getLockPatientOrderExpireTimeForPay())
                    .map(expireTime -> Instant.now().plusMillis(expireTime))
                    .orElse(null);
        } else {
            return Optional.ofNullable(getLockPatientOrderExpireTimeForRefund())
                    .map(expireTime -> Instant.now().plusMillis(expireTime))
                    .orElse(null);
        }
    }

    @Override
    public Long getLockPatientOrderExpireTimeForPay() {
        //默认都是5分钟
        return 5 * 60 * 1000L;
    }

    @Override
    public Long getLockPatientOrderExpireTimeForRefund() {
        //理论上是没有过期概念的，但是为了不要锁太久，先锁两个小时
        return 2 * 60 * 60 * 1000L;
    }

}
