package cn.abcyun.cis.charge.service.dto;

import cn.abcyun.cis.charge.model.ChargeTransactionRecord;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Data
@Accessors(chain = true)
public class ChargeTransactionRecordPaidView {

    private String productId;
    private String name;
    private int productType;
    private int productSubType;
    private int type;
    private int composeType;
    private String productUnit;
    private BigDecimal productUnitCount;
    private BigDecimal doseCount;
    private BigDecimal totalPrice;
    private BigDecimal discountPrice;

    private List<ChargeTransactionRecordView> records;

    public void addRecords(ChargeTransactionRecordView recordView) {
        if (Objects.isNull(recordView)) {
            return;
        }
        if (records == null) {
            records = new ArrayList<>();
        }

        records.add(recordView);
    }

    public static ChargeTransactionRecordPaidView ofChargeTransactionRecordPaidView(ChargeTransactionRecord record) {

        ChargeTransactionRecordView recordView = ChargeTransactionRecordView.ofChargeTransactionRecordView(record);

        if (recordView == null) {
            return null;
        }

        ChargeTransactionRecordPaidView recordPaidView = new ChargeTransactionRecordPaidView();
        BeanUtils.copyProperties(recordView, recordPaidView);
        recordPaidView.addRecords(recordView);
        return recordPaidView;
    }
}
