package cn.abcyun.cis.charge.service.dto.print;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GetDispenseGoodsStockInfoRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsSystemType;
import cn.abcyun.cis.charge.base.GoodsFeeTypeConstants;
import cn.abcyun.cis.charge.service.dto.SinglePromotionView;
import cn.abcyun.cis.commons.util.TextUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Data
public class ChargeFormItemPrintView {
    private String id;
    private String name;
    private String unit;
    private BigDecimal count;
    private BigDecimal unitCount;
    private BigDecimal doseCount;
    private BigDecimal totalPrice;
    private BigDecimal discountedPrice;
    private BigDecimal discountedUnitPrice;
    /**
     * 单价
     */
    private BigDecimal unitPrice;
    private int composeType = 0;

    private Integer goodsTypeId;

    private int feeComposeType;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long feeTypeId;

    private String feeTypeName;

    /**
     * 0费用本身，1费用母项，2费用子项
     */
    private int goodsFeeType;

    /**
     * 检查检验是否要拆开打印
     */
    @JsonIgnore
    private int isExaminationSplitPrint;
    private List<ChargeFormItemPrintView> composeChildren;
    private String position;
    /**
     * 规格
     */
    private String displaySpec;

    private String cmSpec;

    private int sourceItemType;

    //深圳社保相关
    private String socialCode;
    private String hisCode;
    private String socialUnit;
    private String socialName;
    private Integer medicalFeeGrade;        //医疗费用等级 0.空（未知） 1.甲类  2.乙类  3.丙类
    private BigDecimal ownExpenseRatio;     //医疗保障范围外个人承担比例
    private BigDecimal ownExpenseFee;       //医保保障范围外个人承担金额
    private BigDecimal inscpScpAmt;     // 符合政策范围金额
    private BigDecimal overlmtAmt;     // 超限价金额

    /**
     * 药品发药的厂家，批号，效期等信息
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<GetDispenseGoodsStockInfoRsp.DispensingGoodsStockInfo> goodsStockInfos;

    @JsonIgnore
    private String productId;
    private int productType;
    private int productSubType;

    private JsonNode productInfo;

    @JsonIgnore
    private Type type;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String specialRequirement;

    /**
     * 牙位
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Integer> toothNos;

    /**
     * 单项优惠
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<SinglePromotionView> singlePromotions;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal singlePromotionFee; //单项优惠
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal unitAdjustmentFee; //单项议价

    public Long getFeeTypeIdOrDefaultByGoodsTypeId(Map<String, GoodsSystemType> goodsTypeKeyToGoodsTypeMap) {

        if (feeTypeId != null) {
            return feeTypeId;
        }
        Integer goodsTypeId = getGoodsTypeIdOrDefault(goodsTypeKeyToGoodsTypeMap);

        return Objects.nonNull(goodsTypeId) ? GoodsFeeTypeConstants.defaultFeeTypeIdByGoodsTypeId(goodsTypeId) : null;
    }

    private Integer getGoodsTypeIdOrDefault(Map<String, GoodsSystemType> goodsTypeKeyToGoodsTypeMap) {
        if (Objects.nonNull(goodsTypeId)) {
            return goodsTypeId;
        }

        return GoodsFeeTypeConstants.convertGoodsTypeIdByGoodsTypeAndSubType(productType, productSubType, cmSpec, goodsTypeKeyToGoodsTypeMap);
    }

    public ChargeFormItemPrintView() {
        type = Type.OTHER;
    }

    public String getSocialUnit() {
        return !TextUtils.isEmpty(socialUnit) ? socialUnit : unit;
    }

    public String getSocialName() {
        return !TextUtils.isEmpty(socialName) ? socialName : name;
    }

    public enum Type {
        // 1：西药费，2：中药饮片：3：中成药，4：检查费，5：化验费，6：治疗费，7：挂号费，8：卫生材料费，9：一般诊疗费 10:眼镜费
        /**
         * 挂号
         */
        REGISTRATION(7, MedicalBillPrintView.Name.REGISTRATION),
        /**
         * 西药
         */
        WESTERN_MEDICINE(1, MedicalBillPrintView.Name.WESTERN_MEDICINE),
        /**
         * 中药饮片
         */
        CHINESE_MEDICINE_DRINKS_PIECE(2, MedicalBillPrintView.Name.CHINESE_MEDICINE_DRINKS_PIECE),
        /**
         * 中成药
         */
        CHINESE_COMPOSE_MEDICINE(3, MedicalBillPrintView.Name.CHINESE_COMPOSE_MEDICINE),
        /**
         * 中药颗粒
         */
        CHINESE_MEDICINE_PARTICLE(2, MedicalBillPrintView.Name.CHINESE_MEDICINE_DRINKS_PIECE),
        /**
         * 检验
         */
        EXAMINATION_EXAMINATION(5, MedicalBillPrintView.Name.EXAMINATION_EXAMINATION),
        /**
         * 检查
         */
        EXAMINATION_INSPECTION(4, MedicalBillPrintView.Name.EXAMINATION_INSPECTION),

        /**
         * 治疗理疗
         */
        TREATMENT(6, MedicalBillPrintView.Name.TREATMENT),
        /**
         * 物资
         */
        MATERIAL(8, MedicalBillPrintView.Name.MATERIAL),
        /**
         * 线上咨询
         */
        ONLINE_CONSULTATION(9, MedicalBillPrintView.Name.OTHER),
        /**
         * 快递
         */
        EXPRESS_DELIVERY(9, MedicalBillPrintView.Name.OTHER),
        /**
         * 加工
         */
        PROCESS(9, MedicalBillPrintView.Name.OTHER),
        /**
         * 商品
         */
        SALE_PRODUCT(8, MedicalBillPrintView.Name.MATERIAL),
        /**
         * 套餐
         */
        COMPOSE_PRODUCT(9, MedicalBillPrintView.Name.OTHER),
        /**
         * 其他
         */
        OTHER(9, MedicalBillPrintView.Name.OTHER),

        /**
         * 眼镜费
         */
        EYE_PRODUCT(10, MedicalBillPrintView.Name.MATERIAL),

        NURSING(11, MedicalBillPrintView.Name.NURSING_NAME),
        SURGERY(12,MedicalBillPrintView.Name.SURGERY_NAME);

        private int printType;

        private String printName;

        Type(int printType, String printName) {
            this.printType = printType;
            this.printName = printName;
        }

        public String getPrintName() {
            return printName;
        }

        public void setPrintName(String printName) {
            this.printName = printName;
        }

        public int getPrintType() {
            return printType;
        }

        public void setPrintType(int printType) {
            this.printType = printType;
        }
    }
}
