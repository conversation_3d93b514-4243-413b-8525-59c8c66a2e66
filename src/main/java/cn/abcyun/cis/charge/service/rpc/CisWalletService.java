package cn.abcyun.cis.charge.service.rpc;

import cn.abcyun.bis.rpc.sdk.bis.model.order.QueryPayOrderRsp;
import cn.abcyun.bis.rpc.sdk.cis.client.AbcCisWalletFeignClient;
import cn.abcyun.bis.rpc.sdk.cis.model.wallet.CreatePayOrderReq;
import cn.abcyun.bis.rpc.sdk.cis.model.wallet.CreatePayOrderRsp;
import cn.abcyun.cis.commons.exception.ServiceInternalException;
import cn.abcyun.cis.core.util.FeignClientRpcTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CisWalletService {

    @Autowired
    private AbcCisWalletFeignClient client;


    public CreatePayOrderRsp createPayOrder(String clinicId, BigDecimal needPay, List<QueryPayOrderRsp.OrderSimple> orders, String callbackUrl, int airPharmacyVendorType, String operatorId) throws ServiceInternalException {
        CreatePayOrderReq req = new CreatePayOrderReq();
        req.setAccountOwnerId(clinicId);
        req.setTotalFee(needPay);
        req.setScene(CreatePayOrderReq.Scene.AIR_PHARMACY);
        req.setItems(orders.stream().map(CisWalletService::convertToPayOrderItem).collect(Collectors.toList()));
        req.setCallbackUrl(callbackUrl);
        req.setOperatorId(operatorId);
        req.setAirPharmacyVendorType(airPharmacyVendorType);

        return FeignClientRpcTemplate.dealRpcClientMethod("createPayOrder", true, () -> client.createPayOrder(req), req);
    }

    public static CreatePayOrderReq.PayOrderItem convertToPayOrderItem(QueryPayOrderRsp.OrderSimple orderSimple) {
        if (orderSimple == null) {
            return null;
        }

        CreatePayOrderReq.PayOrderItem payOrderItem = new CreatePayOrderReq.PayOrderItem();

        payOrderItem.setBusinessId(orderSimple.getOrderId());
        payOrderItem.setOppositeAccountOwnerId(orderSimple.getVendorId());
        payOrderItem.setFee(orderSimple.getClinicTotalPrice());
        payOrderItem.setServiceFee(orderSimple.getServicePrice());
        return payOrderItem;
    }

}
