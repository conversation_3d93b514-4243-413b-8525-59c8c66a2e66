package cn.abcyun.cis.charge.service.thirtparty;

import cn.abcyun.cis.charge.base.InnerPayModes;
import cn.abcyun.cis.charge.repository.ChargePayTransactionRepository;
import cn.abcyun.cis.charge.repository.ChargeTransactionRecordRepository;
import cn.abcyun.cis.charge.service.*;
import cn.abcyun.cis.charge.service.rpc.CisScClinicService;
import cn.abcyun.cis.charge.service.rpc.CisShebaoService;
import cn.abcyun.cis.idgenerator.AbcIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 医保移动支付-现金部分
 */
@Service
@Slf4j
public class ChargeWechatMobileShebaoCashPayModeCallbackService extends ChargeWechatPayModeCallbackService {

    @Autowired
    public ChargeWechatMobileShebaoCashPayModeCallbackService(ChargeSheetService chargeSheetService,
                                                              ChargePayTransactionRepository chargePayTransactionRepository,
                                                              ChargeService chargeService,
                                                              AbcIdGenerator abcIdGenerator,
                                                              SheetProcessorService sheetProcessorService,
                                                              ChargeTransactionRecordRepository chargeTransactionRecordRepository,
                                                              ChargeAbnormalTransactionService chargeAbnormalTransactionService,
                                                              ChargeConfigService chargeConfigService,
                                                              CisScClinicService scClinicService,
                                                              CisShebaoService shebaoService) {
        super(chargeSheetService, chargePayTransactionRepository, chargeService, abcIdGenerator, sheetProcessorService, chargeTransactionRecordRepository, chargeAbnormalTransactionService, chargeConfigService, scClinicService, shebaoService);
    }

    @Override
    public String getPayModeKey() {
        return InnerPayModes.getPayModeByName(InnerPayModes.InnerPayModeName.WECHAT_MOBILE_SHEBAO_CASH).getPayModeKey();
    }

}
