package cn.abcyun.cis.charge.service.dto;

import cn.abcyun.cis.charge.model.ChargeAbnormalTransaction;
import cn.abcyun.cis.charge.model.ChargePayTransaction;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class SaveChargePayTransactionResult {
    private ChargePayTransaction chargePayTransaction;
    private ChargeAbnormalTransaction abnormalTransaction;
}
