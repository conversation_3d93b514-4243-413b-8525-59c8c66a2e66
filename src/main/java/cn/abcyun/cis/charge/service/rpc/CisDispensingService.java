package cn.abcyun.cis.charge.service.rpc;

import cn.abcyun.bis.rpc.sdk.cis.client.AbcCisDispensingFeignClient;
import cn.abcyun.bis.rpc.sdk.cis.model.charge.UpdateTraceableCodeReq;
import cn.abcyun.bis.rpc.sdk.cis.model.dispensing.*;
import cn.abcyun.bis.rpc.sdk.cis.model.goodslog.GoodsStockLogItem;
import cn.abcyun.cis.charge.service.dto.DispensingFormInfo;
import cn.abcyun.cis.charge.service.dto.DispensingInfo;
import cn.abcyun.cis.charge.service.dto.DispensingSheetInfo;
import cn.abcyun.cis.charge.util.ListUtils;
import cn.abcyun.cis.charge.util.MergeTool;
import cn.abcyun.cis.commons.model.ComposeType;
import cn.abcyun.cis.commons.util.MathUtils;
import cn.abcyun.cis.core.util.FeignClientRpcTemplate;
import cn.abcyun.common.log.marker.AbcLogMarker;
import feign.RetryableException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CisDispensingService {

    @Autowired
    private AbcCisDispensingFeignClient client;

    public List<DispensingSheet> getDispensingSheetListBySourceSheetId(String chargeSheetId){
        if (StringUtils.isEmpty(chargeSheetId)) {
            return new ArrayList<>();
        }
        DispensingSheetListRsp dispensingSheetListRsp = null;
        try {
            dispensingSheetListRsp = FeignClientRpcTemplate.dealRpcClientMethod("getDispensingSheetListBySourceSheetId", true,
                    () -> client.getDispensingSheetListBySourceSheetId(chargeSheetId, 1),
                    chargeSheetId);
        }catch (Exception e) {
            log.error(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "feign getDispensingSheetListBySourceSheetId error");
        }
        return Optional.ofNullable(dispensingSheetListRsp).map(DispensingSheetListRsp::getDispensingSheets).orElse(new ArrayList<>());
    }

    public static DispensingSheetInfo generateDispensingSheetInfoByDispensingSheet(DispensingSheet dispensingSheet){
        DispensingSheetInfo dispensingSheetInfo = new DispensingSheetInfo();
        // 这里发药 发多次 退多次 item只会有两条 一条发药 一条退药
        Map<String, Integer> dispensingFormEnableRefundFeeMap = dispensingSheet.getDispensingForms().stream()
                .filter(dispensingForm -> Objects.nonNull(dispensingForm.getEnableRefundFee()))
                .collect(Collectors.toMap(DispensingForm::getId, DispensingForm::getEnableRefundFee));

        // 特殊处理套餐在多个sheet中被拆分发药的情况，只保留一个套餐的item
        Set<String> composeChargeFormItemIds = new HashSet<>();
        List<DispensingInfo> dispensingInfos = new ArrayList<>(dispensingSheet.getDispensingForms()
                .stream()
                .filter(dispensingForm -> dispensingForm.getDispensingFormItems() != null)
                .flatMap(dispensingForm -> dispensingForm.getDispensingFormItems().stream()
                        .map(dispensingFormItem -> {
                            List<DispensingFormItem> dispensingFormItems = new ArrayList<>();
                            if (dispensingFormItem.getComposeType() == ComposeType.COMPOSE) {
                                dispensingFormItems.add(dispensingFormItem);
                                if (!CollectionUtils.isEmpty(dispensingFormItem.getComposeChildren())) {
                                    dispensingFormItems.addAll(dispensingFormItem.getComposeChildren());
                                }
                            }else {
                                dispensingFormItems.add(dispensingFormItem);
                            }
                            return dispensingFormItems;
                        })
                        .filter(dispensingFormItems -> !CollectionUtils.isEmpty(dispensingFormItems))
                        .flatMap(Collection::stream))
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(DispensingFormItem::getSourceFormItemId, dispensingItem -> {
                    DispensingInfo dispensingInfo = new DispensingInfo();
                    dispensingInfo.setBatchInfoList(new ArrayList<>());
                    dispensingInfo.setPharmacyDispenseFlag(dispensingSheet.getPharmacyDispenseFlag());
                    if (dispensingSheet.getStatus() == DispenseConst.Status.WAITING) {
                        dispensingInfo.setEnableRefundFee(dispensingFormEnableRefundFeeMap.getOrDefault(dispensingItem.getDispensingFormId(), null));
                    }
                    dispensingInfo.setChargeFormItemId(dispensingItem.getSourceFormItemId());
                    if (dispensingItem.getStatus() == DispensingFormItem.Status.DISPENSED) {
                        dispensingInfo.setDispensedUnitCount(MathUtils.wrapBigDecimalOrZero(dispensingItem.getUnitCount()));
                        dispensingInfo.setDispensedDoseCount(MathUtils.wrapBigDecimal(dispensingItem.getDoseCount(), BigDecimal.ONE));
                    }  else if (dispensingItem.getStatus() == DispensingFormItem.Status.PART_DISPENSE) {
                        if (CollectionUtils.isNotEmpty(dispensingItem.getOperationLogs())) {
                            DispensingFormItem dispensingFormItem = dispensingItem.getOperationLogs().stream().findFirst().orElse(null);
                            Optional.ofNullable(dispensingFormItem).ifPresent(item -> {
                                dispensingInfo.setDispensedUnitCount(MathUtils.wrapBigDecimalOrZero(item.getUnitCount()));
                                dispensingInfo.setDispensedDoseCount(MathUtils.wrapBigDecimal(item.getDoseCount(), BigDecimal.ONE));
                            });
                        }
//                        dispensingInfo.setDispensedUnitCount(MathUtils.wrapBigDecimalSubtract(dispensingItem.getUnitCount(), dispensingItem.getRemainingUnitCount()));
//                        dispensingInfo.setDispensedDoseCount(MathUtils.wrapBigDecimalSubtract(dispensingItem.getDoseCount(), dispensingItem.getRemainingDoseCount()));
//
////                        dispensingInfo.setUndispensedUnitCount(MathUtils.wrapBigDecimalOrZero(dispensingItem.getRemainingUnitCount()));
////                        dispensingInfo.setUndispensedDoseCount(MathUtils.wrapBigDecimal(dispensingItem.getRemainingDoseCount(), BigDecimal.ONE));
                    } else if (dispensingItem.getStatus() == DispensingFormItem.Status.UNDISPENSED) {
                        dispensingInfo.setUndispensedUnitCount(MathUtils.wrapBigDecimalOrZero(dispensingItem.getUnitCount()));
                        dispensingInfo.setUndispensedDoseCount(MathUtils.wrapBigDecimal(dispensingItem.getDoseCount(), BigDecimal.ONE));
                    }
                    dispensingInfo.setStatus(dispensingItem.getStatus());
                    // 设置追溯码
                    DispensingFormItemExtendData extendData = dispensingItem.getExtendData();
                    if (Objects.nonNull(extendData) && CollectionUtils.isNotEmpty(extendData.getTraceableCodeList())) {
                        dispensingInfo.setTraceableCodeList(extendData.getTraceableCodeList());
                    }
                    if (dispensingItem.getProductSnap() != null) {
                        dispensingInfo.setTraceableCodeNoInfoList(dispensingItem.getProductSnap().getTraceableCodeNoInfoList());
                    }
                    // 组装发药退药批次信息
                    if (CollectionUtils.isNotEmpty(dispensingItem.getGoodsStockLogItems())) {
                        Map<Long, List<GoodsStockLogItem>> logItemMap = ListUtils.groupByKey(dispensingItem.getGoodsStockLogItems()
                                .stream()
                                .filter(goodsStockLogItem -> Objects.nonNull(goodsStockLogItem.getBatchId()))
                                .collect(Collectors.toList()), GoodsStockLogItem::getBatchId);
                        List<DispensingInfo.BatchInfo> batchInfos = new ArrayList<>();
                        if (dispensingItem.getStatus() != DispensingFormItem.Status.CANCELED) {
                            logItemMap.forEach((key, goodsStockLogItems) -> {
                                DispensingInfo.BatchInfo batchInfo = new DispensingInfo.BatchInfo();
                                batchInfo.setBatchId(key + "");
                                for (GoodsStockLogItem goodsStockLogItem : goodsStockLogItems) {
                                    BigDecimal count;
                                    if (dispensingItem.getUseDismounting() == 1) {
                                        count = goodsStockLogItem.getChangePieceCount().abs();
                                    } else {
                                        count = goodsStockLogItem.getChangePackageCount().abs();
                                    }
                                    if (dispensingItem.getStatus() == DispensingFormItem.Status.DISPENSED || dispensingItem.getStatus() == DispensingFormItem.Status.PART_DISPENSE) {
                                        batchInfo.setUnitCount(MathUtils.wrapBigDecimalAdd(batchInfo.getUnitCount(), count));
                                    } else {
                                        batchInfo.setRefundUnitCount(MathUtils.wrapBigDecimalAdd(batchInfo.getRefundUnitCount(), count));
                                    }
                                }
                                batchInfos.add(batchInfo);
                            });
                        }
                        dispensingInfo.setBatchInfoList(batchInfos);
                    }
                    return dispensingInfo;
                }, (a, b) -> {
                    a.setDispensedUnitCount(a.getDispensedUnitCount().add(b.getDispensedUnitCount()));
//                    a.setDispensedDoseCount(a.getDispensedDoseCount().add(b.getDispensedDoseCount()));
                    a.setUndispensedUnitCount(a.getUndispensedUnitCount().add(b.getUndispensedUnitCount()));
                    a.setUndispensedDoseCount(a.getUndispensedDoseCount().add(b.getUndispensedDoseCount()));

                    // 将批次信息做merge
                    MergeTool<DispensingInfo.BatchInfo, DispensingInfo.BatchInfo> mergeTool = new MergeTool<>();
                    BiFunction<DispensingInfo.BatchInfo, DispensingInfo.BatchInfo, Boolean> isEqualKeyFunc = (batchA, batchB) -> batchA.getBatchId().equals(batchB.getBatchId());
                    BiConsumer<DispensingInfo.BatchInfo, DispensingInfo.BatchInfo> updateFunc =  (batchB, batchA) -> {
                        batchA.setUnitCount(MathUtils.wrapBigDecimalAdd(batchA.getUnitCount(),batchB.getUnitCount()));
                        batchA.setRefundUnitCount(MathUtils.wrapBigDecimalAdd(batchA.getRefundUnitCount(),batchB.getRefundUnitCount()));
                    };
                    Function<DispensingInfo.BatchInfo, Boolean> deleteFunc = batchA->false;
                    Function<DispensingInfo.BatchInfo, DispensingInfo.BatchInfo> insertFunc = (batchB) -> batchB;
                    mergeTool.setSrc(b.getBatchInfoList())
                            .setDst(a.getBatchInfoList())
                            .setIsEqualKeyFunc(isEqualKeyFunc)
                            .setUpdateFunc(updateFunc)
                            .setInsertFunc(insertFunc)
                            .setDeleteFunc(deleteFunc);
                    mergeTool.doMerge();
                    return a;
                }))
                .values());
        List<DispensingFormInfo> formInfoList = dispensingSheet.getDispensingForms().stream().map(form -> {
            DispensingFormInfo formInfo = new DispensingFormInfo();
            formInfo.setId(form.getId());
            formInfo.setDispensingSheetId(form.getDispensingSheetId());
            formInfo.setSourceFormId(form.getSourceFormId());
            formInfo.setAuditedStatus(form.getAuditedStatus());
            formInfo.setCompoundedStatus(form.getCompoundedStatus());
            return formInfo;
        }).collect(Collectors.toList());

        dispensingSheetInfo.setStatus(dispensingSheet.getStatus());
        dispensingSheetInfo.setDispensingInfos(dispensingInfos);
        dispensingSheetInfo.setDispensedBy(dispensingSheet.getDispensedBy());
        dispensingSheetInfo.setDispensedTime(dispensingSheet.getDispensedTime());
        dispensingSheetInfo.setPharmacyNo(dispensingSheet.getPharmacyNo());
        dispensingSheetInfo.setPharmacyType(dispensingSheet.getPharmacyType());
        dispensingSheetInfo.setDispensingFormInfos(formInfoList);
        return dispensingSheetInfo;
    }

    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public List<DispensingFormItemQuantityInfo> queryDispensingFormItemQuantityInfos(String chainId, String clinicId, List<String> chargeSheetIds) {
        if (StringUtils.isAnyEmpty(chainId, clinicId)) {
            return new ArrayList<>();
        }
        if (CollectionUtils.isEmpty(chargeSheetIds)) {
            return new ArrayList<>();
        }
        DispensingBatchQueryWithClinicReq req = new DispensingBatchQueryWithClinicReq();
        req.setClinicId(clinicId);
        req.setChainId(chainId);
        req.setSourceSheetIds(chargeSheetIds);

        DispensingFormItemQuantityBatchRsp rsp = FeignClientRpcTemplate.dealRpcClientMethod("queryDispensingFormItemQuantityInfos", true,
                () -> client.queryDispensingFormItemQuantityInfos(req), req
        );
        return Optional.ofNullable(rsp).map(DispensingFormItemQuantityBatchRsp::getDispensingFormItems).orElse(new ArrayList<>());
    }

    public void updateDispensingSheetTraceableCode(UpdateTraceableCodeReq req) {
        if (Objects.isNull(req) || CollectionUtils.isEmpty(req.getList())) {
            return;
        }
        try {
            FeignClientRpcTemplate.dealRpcClientMethod("updateDispensingSheetTraceableCode", true,
                    () -> client.updateDispensingSheetTraceableCode(req), req
            );
        } catch (Exception e) {
            log.error(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "feign updateDispensingSheetTraceableCode error,", e);
        }
    }

    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public DispensingSheetOperationRsp findDispensingSheetOperation(String patientOrderId) {
        return FeignClientRpcTemplate.dealRpcClientMethod("findDispensingSheetOperation",
                () -> client.findDispensingSheetOperation(patientOrderId),
                patientOrderId);
    }
}
