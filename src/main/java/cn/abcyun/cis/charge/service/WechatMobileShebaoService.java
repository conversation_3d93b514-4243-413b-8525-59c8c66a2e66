package cn.abcyun.cis.charge.service;

import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ;
import cn.abcyun.bis.rpc.sdk.cis.model.shebao.MobilePayOrderInfoRspBody;
import cn.abcyun.bis.rpc.sdk.cis.model.wechatpay.WeChatMobileShebaoPayReq;
import cn.abcyun.bis.rpc.sdk.cis.model.wechatpay.WeChatMobileShebaoPayRsp;
import cn.abcyun.cis.charge.base.ChargeServiceError;
import cn.abcyun.cis.charge.base.ChargeServiceException;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.model.ChargePayTransaction;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.service.dto.CalculateChargeResult;
import cn.abcyun.cis.charge.service.interfaces.ChargePayByTaskIdInterface;
import cn.abcyun.cis.charge.service.rpc.CisScClinicService;
import cn.abcyun.cis.charge.service.rpc.CisWechatPayService;
import cn.abcyun.cis.charge.util.AbcIdUtils;
import cn.abcyun.cis.charge.util.IdCardUtils;
import cn.abcyun.cis.commons.exception.ParamNotValidException;
import cn.abcyun.cis.commons.util.DateUtils;
import cn.abcyun.cis.commons.util.MD5Utils;
import cn.abcyun.cis.commons.util.MathUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.Optional;

/**
 * 类说明
 *
 * <AUTHOR>
 * @version WechatMobileShebaoService.java, 2023/7/17 2:27 PM
 */
@Service
@Slf4j
public class WechatMobileShebaoService implements ChargePayByTaskIdInterface {

    @Autowired
    private CisWechatPayService cisWechatPayService;
    @Autowired
    private ShebaoService shebaoService;
    @Autowired
    private CisScClinicService scClinicService;

    @Override
    public WeChatMobileShebaoPayRsp chargePayByTaskId(WeChatMobileShebaoPayReq weChatMobileShebaoPayReq) {
        return cisWechatPayService.wxMobileShebaoPay(weChatMobileShebaoPayReq);
    }

    @Override
    public WeChatMobileShebaoPayReq genWxMobileShebaoReq(ChargePayTransaction wechatPayTransaction,
                                                         CalculateChargeResult calculateChargeResult,
                                                         ChargeSheet chargeSheet,
                                                         String patientOpenId,
                                                         String clientIp,
                                                         String returnUrl,
                                                         String patientGps,
                                                         String shebaoTaskId) {
        // 1、查询微信医保订单信息
        MobilePayOrderInfoRspBody mobilePayOrderInfoRspBody = shebaoService.getMobilePayOrderInfo(shebaoTaskId, wechatPayTransaction.getChainId(), wechatPayTransaction.getClinicId());
        if (Objects.isNull(mobilePayOrderInfoRspBody)) {
            throw new ChargeServiceException(ChargeServiceError.MOBILE_PAY_ORDER_NOT_EXIST);
        }
        if (!Objects.equals(mobilePayOrderInfoRspBody.getIsCompleted(), 1)) {
            throw new ChargeServiceException(ChargeServiceError.MOBILE_PAY_ORDER_NOT_COMPLETED);
        }
        MobilePayOrderInfoRspBody.MobilePayOrderInfo mobilePayOrderInfo = mobilePayOrderInfoRspBody.getMobilePayOrderInfo();


        /**
         * 整个单子的现金支付
         */
        BigDecimal cashPayFee = mobilePayOrderInfo.getCashPayFee();
        /**
         * 社保的现金支付
         */
        BigDecimal psnCashFee = mobilePayOrderInfo.getPsnCashPay();
        /**
         * 医疗费总金额 = fundPaySumamt + acctPay + psnCashPay
         */
        BigDecimal medfeeSumamt = mobilePayOrderInfo.getMedfeeSumamt();
        /**
         * 总应收 = 医疗费总金额 + (cashPayFee - psnCashPay);
         */
        BigDecimal receivableFee = mobilePayOrderInfo.getReceivableFee();

        //校验医保数据是否正确
        if (MathUtils.wrapBigDecimalCompare(mobilePayOrderInfo.getMedfeeSumamt(), MathUtils.wrapBigDecimalAdd(mobilePayOrderInfo.getFundPaySumamt(), mobilePayOrderInfo.getAcctPay(), mobilePayOrderInfo.getPsnCashPay())) != 0) {
            log.error("医保数据不正确，medfeeSumamt:{},fundPaySumamt:{},acctPay:{},psnCashPay:{}", mobilePayOrderInfo.getMedfeeSumamt(), mobilePayOrderInfo.getFundPaySumamt(), mobilePayOrderInfo.getAcctPay(), mobilePayOrderInfo.getPsnCashPay());
            throw new ChargeServiceException(ChargeServiceError.MOBILE_PAY_ORDER_TOTAL_FEE_NOT_MATCH);
        }

        if (MathUtils.wrapBigDecimalCompare(receivableFee, MathUtils.wrapBigDecimalAdd(mobilePayOrderInfo.getMedfeeSumamt(), MathUtils.wrapBigDecimalSubtract(mobilePayOrderInfo.getCashPayFee(), mobilePayOrderInfo.getPsnCashPay()))) != 0) {
            log.error("医保数据不正确，receivableFee:{},medfeeSumamt:{},cashPayFee:{},psnCashPay:{}", mobilePayOrderInfo.getReceivableFee(), mobilePayOrderInfo.getMedfeeSumamt(), mobilePayOrderInfo.getCashPayFee(), mobilePayOrderInfo.getPsnCashPay());
            throw new ChargeServiceException(ChargeServiceError.MOBILE_PAY_ORDER_TOTAL_FEE_NOT_MATCH);
        }

        if (MathUtils.wrapBigDecimalCompare(calculateChargeResult.getNeedPayFee(), receivableFee) != 0) {
            throw new ChargeServiceException(ChargeServiceError.MOBILE_PAY_ORDER_TOTAL_FEE_NOT_MATCH);
        }
        String medOrgOrd = Optional.ofNullable(mobilePayOrderInfoRspBody.getMobilePayOrderInfo())
                .map(MobilePayOrderInfoRspBody.MobilePayOrderInfo::getMedOrgOrd)
                .orElse(null);

        if (StringUtils.isEmpty(medOrgOrd)) {
            throw new ParamNotValidException("医疗机构订单号不能为空");
        }

        // 2、查询门店信息
        Organ clinic = scClinicService.getOrgan(wechatPayTransaction.getClinicId());
        if (Objects.isNull(clinic)) {
            throw new ChargeServiceException(ChargeServiceError.ORGAN_NOT_EXIST);
        }

        boolean isRegChargeSheet = Objects.equals(chargeSheet.getType(), ChargeSheet.Type.REGISTRATION);
        String body = isRegChargeSheet
                ? Constants.SystemProductId.REGISTRATION_NAME
                : (Objects.equals(chargeSheet.getType(), ChargeSheet.Type.MEMBER_RECHARGE) ? "会员充值" : "收费单：" + AbcIdUtils.convertUUidToLongString(chargeSheet.getId()));

        Integer payType = null;
        boolean isCashBiggerThanZero = MathUtils.wrapBigDecimalCompare(cashPayFee, BigDecimal.ZERO) > 0;
        boolean isInsuranceBiggerThanZero = MathUtils.wrapBigDecimalCompare(medfeeSumamt, BigDecimal.ZERO) > 0;
        if (isCashBiggerThanZero && isInsuranceBiggerThanZero) {
            payType = WeChatMobileShebaoPayReq.MobileShebaoPayType.CASH_INSURANCE;
        } else if (isInsuranceBiggerThanZero) {
            payType = WeChatMobileShebaoPayReq.MobileShebaoPayType.INSURANCE;
        } else if (isCashBiggerThanZero) {
            payType = WeChatMobileShebaoPayReq.MobileShebaoPayType.CASH;
        }

        /**
         * total_fee = receivableFee;
         * cash_fee = cashPayFee;
         * cash_add_fee = cashPayFee - psnCashPay;
         * insurance_fee = medfeeSumamt;
         */
        return new WeChatMobileShebaoPayReq()
                .setClinicId(wechatPayTransaction.getClinicId())
                .setChainId(wechatPayTransaction.getChainId())
                .setPatientId(wechatPayTransaction.getPatientId())
                .setBusinessTradeNo(wechatPayTransaction.getId())
                .setShebaoTaskId(shebaoTaskId)
                .setOrderType(isRegChargeSheet ? WeChatMobileShebaoPayReq.MobileShebaoOrderType.REG_PAY : WeChatMobileShebaoPayReq.MobileShebaoOrderType.DIAG_PAY)
                .setSource(isRegChargeSheet ? WeChatMobileShebaoPayReq.MobileShebaoSource.CHARGE_FOR_REGISTRATION : WeChatMobileShebaoPayReq.MobileShebaoSource.CHARGE)
                .setSubOpenid(patientOpenId)
                .setHospitalName(StringUtils.isBlank(clinic.getName()) ? clinic.getShortName() : clinic.getName())
                .setTotalFee(MathUtils.convertBigDecimalToFenInt(receivableFee))
                .setCashFee(MathUtils.convertBigDecimalToFenInt(cashPayFee))
                .setCashAddFee(MathUtils.convertBigDecimalToFenInt(MathUtils.wrapBigDecimalSubtract(cashPayFee, psnCashFee)))
                .setSpbillCreateIp(clientIp)
                .setBody(body)
                .setReturnUrl(returnUrl)
                .setPayType(payType)
                .setCityId(mobilePayOrderInfoRspBody.getCityCode())
                .setInsuranceFee(MathUtils.convertBigDecimalToFenInt(MathUtils.max(BigDecimal.ZERO, MathUtils.wrapBigDecimalSubtract(medfeeSumamt, psnCashFee))))
                .setUserCardType(WeChatMobileShebaoPayReq.UserCardType.ID_CARD)
                .setUserCardNo(MD5Utils.sign(IdCardUtils.covert15To18(mobilePayOrderInfo.getCertno())))
                .setUserName(mobilePayOrderInfo.getPsnName())
                .setSerialNo(medOrgOrd)
                .setOrgNo(mobilePayOrderInfo.getOrgCodg())
                .setTimeExpire(chargeSheet.getExpireTime())
                .setGmtOutCreate(
                        DateUtils.formatLocalDateTime(DateUtils.toLocalDateTime(wechatPayTransaction.getCreated()), "yyyyMMddHHmmss")
                )
                .setPayAuthNo(mobilePayOrderInfo.getPayAuthNo())
                .setPayOrdId(mobilePayOrderInfo.getPayOrdId())
                .setSetlLatlnt(patientGps)
                .setBusinessCallBackUrl(String.format(Constants.BusinessCallBackUrl.WECHAT_PAY_MOBILE_SHEBAO_BUSINESS_CALLBACK_URL, shebaoTaskId))
                .setOperatorId(Constants.ANONYMOUS_PATIENT_ID);

    }
}
