package cn.abcyun.cis.charge.service.dto;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.TraceableCode;
import cn.abcyun.cis.charge.processor.limitprice.BaseLimitPriceInfo;
import cn.abcyun.cis.charge.util.MathUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class CalculateChargeFormItemView {
    private String id;
    private String sourceFormItemId;
    private String keyId;
    private String sourceFormItemKeyId;
    private String unit;
    private String name;
    private BigDecimal unitCount;
    private BigDecimal doseCount;
    private BigDecimal unitPrice;
    private BigDecimal sourceUnitPrice;
    private BigDecimal sourceTotalPrice;
    private BigDecimal discountPrice;
    private BigDecimal promotionPrice;
    private BigDecimal totalPrice;  //总价不算折扣 unitPrice * unitCount * packageCount;
    private BigDecimal discountedTotalPrice; //折后价
    private BigDecimal adjustmentPrice;
    private BigDecimal actualUnitPrice;
    private BigDecimal actualTotalPrice;

    private BigDecimal expectedUnitPrice;
    private BigDecimal expectedTotalPrice;

    private BigDecimal totalPriceRatio;
    private BigDecimal expectedTotalPriceRatio;
    private BigDecimal unitAdjustmentFee;

    private String unitAdjustmentFeeLastModifiedBy;

    /**
     * 最后议价人名称
     */
    private String unitAdjustmentFeeLastModifiedByName;

    /**
     * 应收（平摊了议价之后的总值）
     */
    private BigDecimal receivableTotalFee;

    /**
     * 社保应收
     */
    private BigDecimal sheBaoReceivableFee;
    /**
     * 是否期望指定批次
     */
    private int isExpectedBatch;

    /**
     * 单项优惠后的单价
     */
    private BigDecimal singlePromotionedUnitPrice;

    /**
     * 单项优惠后的总价
     */
    private BigDecimal singlePromotionedTotalPrice;

    private boolean isUseLimitPrice;
    private int useDismounting;
    private int productType;
    private int productSubType;
    private String productId;
    private int isGift;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<SinglePromotionView> singlePromotions;

    private int composeType = 0;
    private List<CalculateChargeFormItemView> composeChildren = new ArrayList<>();

    /**
     * 批次列表
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<ChargeFormItemBatchInfoView> chargeFormItemBatchInfos;


    @Deprecated
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<BatchInfoView> batchInfoViewList = new ArrayList<>();

    /**
     * 产生赠品的promotionId
     * 赠品时，记录由哪个promotion活动赠送的
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String giftGoodsPromotionId;

    /**
     * 触发的限价规则快照
     */
    private BaseLimitPriceInfo.LimitInfo limitInfo;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private JsonNode productInfo;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal stockPieceCount;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal stockPackageCount;

    public BigDecimal getSourceTotalPrice() {
        if (sourceTotalPrice != null) {
            return sourceTotalPrice;
        }

        return MathUtils.calculateTotalPrice(sourceUnitPrice, unitCount, doseCount,2);
    }

    /**
     * 追溯码列表
     */
    private List<TraceableCode> traceableCodeList;

    /**
     * 批量提单时的原始chargeFormItemId列表
     */
    private List<String> originalChargeFormItemIds;

}
