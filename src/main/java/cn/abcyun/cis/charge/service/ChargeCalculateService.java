package cn.abcyun.cis.charge.service;

import cn.abcyun.bis.rpc.sdk.bis.model.order.CreateOrderView;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Employee;
import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.medicalplan.MedicalPlanSheet;
import cn.abcyun.cis.charge.api.model.*;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.controller.api.ChargeFormItemMerger;
import cn.abcyun.cis.charge.handler.ChargeSheetConvertHandler;
import cn.abcyun.cis.charge.handler.dto.MedicalPlanSheetConvertChargeSheetRsp;
import cn.abcyun.cis.charge.handler.dto.OutpatientSheetConvertChargeSheetRsp;
import cn.abcyun.cis.charge.model.*;
import cn.abcyun.cis.charge.processor.ChargeSheetFeeProtocol;
import cn.abcyun.cis.charge.processor.FlatReceivedPriceHelper;
import cn.abcyun.cis.charge.processor.SheetProcessor;
import cn.abcyun.cis.charge.processor.StatChargeOweRecordProcessor;
import cn.abcyun.cis.charge.processor.calculate.CalculateSheetProcessor;
import cn.abcyun.cis.charge.repository.ChargeOweCombineTransactionRecordDetailRepository;
import cn.abcyun.cis.charge.repository.ChargeOweSheetRepository;
import cn.abcyun.cis.charge.service.dto.BusinessSheetCalculateChargeSheetDto;
import cn.abcyun.cis.charge.service.dto.ChargeSheetView;
import cn.abcyun.cis.charge.util.*;
import cn.abcyun.cis.commons.model.ComposeType;
import cn.abcyun.cis.commons.rpc.charge.ChargeDeliveryCompanyView;
import cn.abcyun.cis.commons.rpc.charge.ChargeDeliveryInfoView;
import cn.abcyun.cis.commons.rpc.outpatient.*;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrder;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.cis.core.db.UseReadOnlyDB;
import cn.abcyun.common.log.marker.AbcLogMarker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ChargeCalculateService {

    @Autowired
    private ChargeSheetService chargeSheetService;

    @Autowired
    private PatientOrderService patientOrderService;

    @Autowired
    private ChargeExecuteService chargeExecuteService;

    @Autowired
    private ChargeConfigService chargeConfigService;

    @Autowired
    private EmployeeService employeeService;

    @Autowired
    private ChargeOweSheetRepository chargeOweSheetRepository;

    @Autowired
    private ChargeAirPharmacyService airPharmacyService;

    @Autowired
    private ChargeSheetConvertHandler chargeSheetConvertHandler;

    @Autowired
    private SheetProcessorService sheetProcessorService;

    @Autowired
    private ChargeTransactionRecordService chargeTransactionRecordService;

    @Autowired
    private ChargeOweCombineTransactionRecordDetailRepository chargeOweCombineTransactionRecordDetailRepository;



    /**
     * 批量算费摊费工具纯算费接口，将议价平摊到单项上
     *
     * @param req
     * @return
     */
    public BatchBasicCalculateSheetRsp batchCalculateSheet(BatchBasicCalculateSheetReq req) {

        BatchBasicCalculateSheetRsp rsp = new BatchBasicCalculateSheetRsp();
        rsp.setSheets(new ArrayList<>());
        if (CollectionUtils.isEmpty(req.getSheets())) {
            return rsp;
        }

        rsp.setSheets(req.getSheets()
                .stream().map(sheet -> new CalculateSheetProcessor(sheet).calculate())
                .collect(Collectors.toList()));
        return rsp;
    }

    /**
     * 纯算费接口，将议价平摊到单项上
     *
     * @param calculateSheetReq
     * @return
     */
    public BasicCalculateSheetRsp calculateSheet(BasicCalculateSheetReq calculateSheetReq) {
        BatchBasicCalculateSheetReq req = new BatchBasicCalculateSheetReq();
        req.setSheets(new ArrayList<>());
        req.getSheets().add(calculateSheetReq);

        BatchBasicCalculateSheetRsp batchBasicCalculateSheetRsp = batchCalculateSheet(req);
        return batchBasicCalculateSheetRsp.getSheets().get(0);
    }

    public BusinessSheetCalculateChargeSheetDto businessSheetCalculateChargeSheet(List<ChargeSheet> chargeSheets,
                                                                                   String clinicId,
                                                                                   String patientOrderId,
                                                                                   boolean withDeliveryAndProcess,
                                                                                   boolean withExecutedCount) {

        BusinessSheetCalculateChargeSheetDto rsp = new BusinessSheetCalculateChargeSheetDto();
        rsp.setChargeSheets(new ArrayList<>());

        if (CollectionUtils.isEmpty(chargeSheets)) {
            return rsp;
        }

        if (withDeliveryAndProcess) {
            chargeSheets.forEach(chargeSheet -> ClientReqUtils.updatePrescriptionChineseFormUsageInfoByProcessForm(chargeSheet.getChargeForms(), null));
        }


        rsp.setChargeSheets(new ArrayList<>());

        //查询收费单的欠费明细和还款明细，计算出每个item的当前欠费值
        Map<String, BigDecimal> chargeFormItemOwedFeeIdMap = queryChargeFormItemOweFee(chargeSheets, clinicId);

        //查询收费单的执行明细
        Map<String, BigDecimal> executedIdCountMap = new HashMap<>();
        if (withExecutedCount) {
            List<ChargeExecuteItem> executeItems = chargeExecuteService.findByPatientOrderIdAndIsDeleted(patientOrderId);
            executedIdCountMap = executeItems.stream().collect(Collectors.toMap(ChargeExecuteItem::getChargeFormItemId, ChargeExecuteItem::getExecutedCount, (a, b) -> a));
        }

        //查询收费单的收费员
        Map<String, String> chargeSheetChargedByMap = chargeSheets.stream()
                .filter(chargeSheet -> chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED)
                .filter(chargeSheet -> StringUtils.isNotEmpty(chargeSheet.getChargedBy()))
                .collect(Collectors.toMap(ChargeSheet::getId, ChargeSheet::getChargedBy));

        List<String> chargedByIds = chargeSheetChargedByMap.values()
                .stream()
                .distinct()
                .collect(Collectors.toList());
        String chainId = chargeSheets.get(0).getChainId();

        Map<String, Employee> employeeMap = Optional.ofNullable(employeeService.findEmployeeList(chainId, chargedByIds)).orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.toMap(Employee::getId, Function.identity()));

        //补充门诊form和收费处直接加的form
        Map<String, BigDecimal> finalExecutedIdCountMap = executedIdCountMap;
        return rsp.setChargeSheets(chargeSheets.stream()
                .map(chargeSheet -> generateBusinessCalculateChargeSheet(chargeSheet,
                        chargeFormItemOwedFeeIdMap,
                        finalExecutedIdCountMap,
                        employeeMap,
                        withExecutedCount))
                .collect(Collectors.toList()));
    }

    private BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeSheet generateBusinessCalculateChargeSheet(ChargeSheet chargeSheet,
                                                                                                                   Map<String, BigDecimal> chargeFormItemOwedFeeIdMap,
                                                                                                                   Map<String, BigDecimal> executedIdCountMap,
                                                                                                                   Map<String, Employee> employeeMap,
                                                                                                                   boolean withExecutedCount) {


        //将退费的chargeFormItem进行合并，合并为一个退费item
        Map<String, ChargeFormItem> mergedRefundChargeFormItemIdMap = Optional.ofNullable(chargeSheet.getChargeForms())
                .orElse(new ArrayList<>())
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getSourceFormType() != Constants.SourceFormType.AIR_PHARMACY)
                .filter(chargeForm -> CollectionUtils.isNotEmpty(chargeForm.getChargeFormItems()))
                .map(chargeForm -> {
                    List<ChargeFormItem> refundItems = ChargeFormItemMerger.collectChargeFormItemsByStatus(chargeForm.getChargeFormItems(), Constants.ChargeFormItemStatus.REFUNDED);

                    Map<String, List<ChargeFormItem>> groupByAssociateFormItemIdRefundItems = refundItems.stream()
                            .filter(item -> !TextUtils.isEmpty(item.getAssociateFormItemId()))
                            .collect(Collectors.groupingBy(ChargeFormItem::getAssociateFormItemId));

                    return chargeForm.getChargeFormItems()
                            .stream()
                            .filter(item -> item.getIsDeleted() == 0)
                            .map(chargeFormItem -> {
                                List<ChargeFormItem> refundChargeFormItems = groupByAssociateFormItemIdRefundItems.getOrDefault(chargeFormItem.getId(), new ArrayList<>());
                                if (CollectionUtils.isEmpty(refundChargeFormItems)) {
                                    return null;
                                }

                                //判断是按剂退的还是按unit退的
                                boolean refundByDose = ChargeFormItemMerger.isRefundByDose(chargeForm, chargeFormItem, refundChargeFormItems);

                                ChargeFormItem refundChargeFormItem = ChargeFormItemUtils.mergeRefundChargeFormItems(refundChargeFormItems, refundByDose);
                                chargeFormItem.setIsRefundByDose(refundByDose ? 1 : 0);
                                return refundChargeFormItem;
                            })
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                })
                .flatMap(Collection::stream)
                .filter(chargeFormItem -> StringUtils.isNotEmpty(chargeFormItem.getAssociateFormItemId()))
                .collect(Collectors.toMap(ChargeFormItem::getAssociateFormItemId, Function.identity(), (a, b) -> a));


        List<BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeForm> businessCalculateChargeForms = Optional.ofNullable(chargeSheet.getChargeForms())
                .orElse(new ArrayList<>())
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .map(chargeForm -> {
                    List<ChargeFormItem> needConvertChargeFormItems = chargeForm.getChargeFormItems()
                            .stream()
                            .filter(Objects::nonNull)
                            .filter(item -> item.getIsDeleted() == 0)
                            .filter(chargeFormItem -> {
                                //空中药房由于没有把实收摊到每个子项上，只能特殊判断，只要是空中药房，都返回所有子项
                                if (chargeForm.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY) {
                                    return true;
                                } else {
                                    return chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.REFUNDED;
                                }
                            }).collect(Collectors.toList());

                    return generateBusinessCalculateChargeForm(chargeForm,
                            chargeSheet,
                            employeeMap,
                            chargeFormItemOwedFeeIdMap,
                            mergedRefundChargeFormItemIdMap,
                            needConvertChargeFormItems
                    );
                })
                .filter(Objects::nonNull)
                .peek(businessCalculateChargeForm -> {
                    //将子项全部移除掉
                    if (businessCalculateChargeForm.getChargeFormItems() != null) {
                        businessCalculateChargeForm.buildAllPrice();

                        //绑定执行数量
                        if (withExecutedCount) {
                            businessCalculateChargeForm.getChargeFormItems().forEach(formItem -> {
                                BigDecimal executedCount = executedIdCountMap.getOrDefault(formItem.getId(), null);
                                formItem.setExecutedCount(executedCount);
                                formItem.setNeedExecutive(Objects.nonNull(executedCount) ? 1 : 0);
                            });
                        }
                    }
                })
                .collect(Collectors.toList());

        BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeSheet businessCalculateChargeSheet = new BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeSheet();
        BeanUtils.copyProperties(chargeSheet, businessCalculateChargeSheet, "chargeForms");
        businessCalculateChargeSheet.setChargeForms(businessCalculateChargeForms);
        businessCalculateChargeSheet.buildAllPrice();
        return businessCalculateChargeSheet;
    }

    private BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeForm generateBusinessCalculateChargeForm(ChargeForm chargeForm,
                                                                                                                 ChargeSheet chargeSheet,
                                                                                                                 Map<String, Employee> employeeMap,
                                                                                                                 Map<String, BigDecimal> chargeFormItemOwedFeeIdMap,
                                                                                                                 Map<String, ChargeFormItem> mergedRefundChargeFormItemIdMap,
                                                                                                                 List<ChargeFormItem> needConvertChargeFormItems) {
        //处理空中药房退费的实收问题
        Map<String, BigDecimal> airPharmacyRefundItemIdReceivedFeeMap = generateAirPharmacyRefundItemIdReceivedFeeMap(chargeForm);
        Map<String, BigDecimal> airPharmacyRefundItemIdRefundedFeeMap = generateAirPharmacyRefundItemIdRefundedFeeMap(chargeForm);

        BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeForm businessCalculateChargeForm = new BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeForm();
        businessCalculateChargeForm.setStatus(chargeForm.getStatus());
        businessCalculateChargeForm.setId(chargeForm.getId());
        businessCalculateChargeForm.setChargeSheetId(chargeSheet.getId());
        businessCalculateChargeForm.setSourceFormId(chargeForm.getSourceFormId());
        businessCalculateChargeForm.setSourceFormType(chargeForm.getSourceFormType());
        businessCalculateChargeForm.setProcessInfo(chargeForm.getProcessInfo());
        businessCalculateChargeForm.setChargeAirPharmacyLogistics(chargeForm.getChargeAirPharmacyLogistics());
        businessCalculateChargeForm.setUsageInfoJson(chargeForm.getUsageInfoJson());
        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED && StringUtils.isNotEmpty(chargeSheet.getChargedBy())) {
            Optional.ofNullable(employeeMap.get(chargeSheet.getChargedBy()))
                    .ifPresent(employee -> {
                        businessCalculateChargeForm.setChargedBy(employee.getId());
                        businessCalculateChargeForm.setChargedByName(employee.getName());
                        businessCalculateChargeForm.setChargedByHandSign(employee.getHandSign());
                    });
        }
        businessCalculateChargeForm.setPharmacyType(chargeForm.getPharmacyType());
        businessCalculateChargeForm.setPharmacyNo(chargeForm.getPharmacyNo());

        /**
         * 目前配镜处方没有收费项，如果是配镜处方在这直接return
         */
        if (chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_TYPE_EYE_GLASS) {
            return businessCalculateChargeForm;
        }

        //将chargeFormItems转换成outpatientFormItems
        List<BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeFormItem> formItems = needConvertChargeFormItems.stream()
                .map(chargeFormItem -> {
                    BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeFormItem formItem = generateOutpatientFormItemForChargeFormItem(chargeFormItem,
                            chargeSheet.getStatus(),
                            chargeSheet.getChargeVersion(),
                            chargeForm.getSourceFormType(),
                            chargeFormItemOwedFeeIdMap,
                            mergedRefundChargeFormItemIdMap,
                            airPharmacyRefundItemIdReceivedFeeMap,
                            airPharmacyRefundItemIdRefundedFeeMap);

                    if (Objects.isNull(formItem)) {
                        return null;
                    }

                    return formItem;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        //如果套餐子项有退费，那么母项也要记录退费
        updateComposeAndFeeParentItemRefundedFee(formItems, needConvertChargeFormItems, mergedRefundChargeFormItemIdMap);

        businessCalculateChargeForm.setChargeFormItems(formItems);

        boolean isChinesePrescription = chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE;


        if (CollectionUtils.isEmpty(businessCalculateChargeForm.getChargeFormItems())) {
            return null;
        }

        //处理中药处方的剂量
        if (isChinesePrescription) {

            //绑定处方剂量
            if (CollectionUtils.isNotEmpty(formItems)) {

                BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeFormItem formItem = formItems.stream()
                        .filter(item -> item.getProductType() == Constants.ProductType.MEDICINE)
                        .findFirst()
                        .orElse(null);

                if (formItem != null) {
                    businessCalculateChargeForm.setDoseCount(formItem.getDoseCount());

                    if (businessCalculateChargeForm.getStatus() == Constants.ChargeFormStatus.REFUNDED) {
                        businessCalculateChargeForm.setRefundDoseCount(formItem.getDoseCount());
                    } else {

                        BigDecimal refundDoseCount = formItems.stream()
                                .filter(item -> item.getProductType() == Constants.ProductType.MEDICINE)
                                .filter(outpatientFormItem -> outpatientFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED)
                                .findFirst().map(BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeFormItem::getRefundDoseCount).orElse(BigDecimal.ZERO);

                        //判断是否退的剂量都是一样的
                        boolean refundByDose = formItems.stream()
                                .filter(outpatientFormItem -> outpatientFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED)
                                .allMatch(outpatientFormItem -> MathUtils.wrapBigDecimalOrZero(outpatientFormItem.getRefundDoseCount()).compareTo(refundDoseCount) == 0);
                        businessCalculateChargeForm.setRefundDoseCount(refundByDose ? refundDoseCount : BigDecimal.ZERO);
                    }
                } else {
                    businessCalculateChargeForm.setDoseCount(BigDecimal.ONE);
                    businessCalculateChargeForm.setRefundDoseCount(BigDecimal.ZERO);
                }
            } else {
                businessCalculateChargeForm.setDoseCount(BigDecimal.ZERO);
                businessCalculateChargeForm.setRefundDoseCount(BigDecimal.ZERO);
            }
        }

        businessCalculateChargeForm.buildAllPrice();
        return businessCalculateChargeForm;

    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    @Deprecated
    public OutpatientSheetCalculateRsp calculateOutpatientSheet(OutpatientSheetCalculateReq outpatientSheetCalculateReq) {
        OutpatientSheetCalculateRsp rsp = new OutpatientSheetCalculateRsp();

        if (outpatientSheetCalculateReq.getOutpatientForms() == null) {
            outpatientSheetCalculateReq.setOutpatientForms(new ArrayList<>());
        }

        boolean withDeliveryAndProcess = outpatientSheetCalculateReq.getWithDeliveryAndProcess() == 1;
        String patientOrderId = outpatientSheetCalculateReq.getPatientOrderId();
        List<ChargeSheet> allChargeSheets = new ArrayList<>();
        if (StringUtils.isNotEmpty(patientOrderId)) {
            allChargeSheets = chargeSheetService.findAllChargeSheetByPatientOrderId(patientOrderId, withDeliveryAndProcess);
        }

        List<ChargeSheet> chargeSheets = allChargeSheets.stream()
                .filter(chargeSheet -> chargeSheet.getType() == ChargeSheet.Type.OUTPATIENT || chargeSheet.getType() == ChargeSheet.Type.REGISTRATION)
                .collect(Collectors.toList());

        if (withDeliveryAndProcess) {
            chargeSheets.stream().forEach(chargeSheet -> ClientReqUtils.updatePrescriptionChineseFormUsageInfoByProcessForm(chargeSheet.getChargeForms(), null));
        }

        rsp.setOutpatientForms(new ArrayList<>());
        rsp.setChargeSheets(chargeSheets.stream().map(chargeSheet -> {
            OutpatientSheetCalculateRsp.ChargeSheet rpcChargeSheet = new OutpatientSheetCalculateRsp.ChargeSheet();
            BeanUtils.copyProperties(chargeSheet, rpcChargeSheet);
            return rpcChargeSheet;
        }).collect(Collectors.toList()));

        Map<String, List<ChargeOweSheet>> chargeOweSheetsMap = chargeOweSheetRepository.findAllByChargeSheetIdInAndClinicIdAndIsDeleted(allChargeSheets.stream().map(item -> item.getId()).collect(Collectors.toList()), outpatientSheetCalculateReq.getClinicId(), 0)
                .stream().collect(Collectors.groupingBy(ChargeOweSheet::getChargeSheetId));
        List<ChargeTransaction> chargeTransactionList = new ArrayList<>();
        for (ChargeSheet allChargeSheet : allChargeSheets) {
            chargeTransactionList.addAll(
                    ChargeSheetFeeProtocol.getChargeTransactionAndChargeActionContainOwe(allChargeSheet, chargeOweSheetsMap.get(allChargeSheet.getId()))
                            .getChargeTransactions()
            );
        }


        if (StringUtils.isNotEmpty(patientOrderId)) {
            // 设置费别
            PatientOrder patientOrder = patientOrderService.findPatientOrder(patientOrderId, null);
            Integer shebaoChargeType = null;
            if (patientOrder != null) {
                shebaoChargeType = patientOrder.getShebaoChargeType();
            }

            rsp.setHealthCardPayLevel(ChargeUtils.generateHealthCardPayLevel(chargeTransactionList, shebaoChargeType));
            rsp.setChargedOutpatientAdjustmentFee(chargeSheets.stream()
                    .filter(chargeSheet -> chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED)
                    .map(ChargeSheet::getOutpatientAdjustmentFee)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
        }

        chargeSheets.forEach(ChargeFormItemMerger::mergeForChargeSheet);

        // sheet status
        if (chargeSheets.stream().filter(chargeSheet -> chargeSheet.getType() != ChargeSheet.Type.REGISTRATION).count() == 0 || chargeSheets.stream().filter(chargeSheet -> chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED || chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_CHARGED).count() > 0) {
            rsp.setStatus(Constants.ChargeSheetStatus.UNCHARGED);
        } else if (chargeSheets.stream().filter(chargeSheet -> chargeSheet.getStatus() == Constants.ChargeSheetStatus.CHARGED || chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_REFUNDED).count() > 0) {
            rsp.setStatus(Constants.ChargeSheetStatus.CHARGED);
        } else {
            rsp.setStatus(Constants.ChargeSheetStatus.REFUNDED);
        }

        List<OutpatientSheetCalculateRsp.OutpatientForm> outpatientForms = generateOutpatientForm(chargeSheets, outpatientSheetCalculateReq, withDeliveryAndProcess);
        rsp.setOutpatientForms(outpatientForms)
                .setTotalPrice(outpatientForms.stream()
                        .map(outpatientForm -> MathUtils.wrapBigDecimalOrZero(outpatientForm.getTotalPrice()))
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                );
        rsp.setRegistrationInfo(generateRegistrationInfo(chargeSheets, outpatientSheetCalculateReq.getRegistrationInfo()));

        rsp.setReceivedPrice(outpatientForms.stream()
                .flatMap(outpatientForm -> outpatientForm.getFormItems().stream())
                .map(outpatientForm -> MathUtils.wrapBigDecimalOrZero(outpatientForm.getReceivedPrice()))
                .reduce(BigDecimal.ZERO, BigDecimal::add));

        if (rsp.getRegistrationInfo() != null &&
                (rsp.getRegistrationInfo().getChargeSheetStatus() == Constants.ChargeSheetStatus.UNCHARGED || rsp.getRegistrationInfo().getChargeSheetType() == ChargeSheet.Type.OUTPATIENT)
        ) {
            rsp.setTotalPrice(MathUtils.wrapBigDecimalAdd(rsp.getTotalPrice(), rsp.getRegistrationInfo().getRegistrationFormItem().getTotalPrice()));
            rsp.setIsTotalPriceContainRegistrationPrice(1);
            rsp.setReceivedPrice(MathUtils.wrapBigDecimalAdd(rsp.getReceivedPrice(), rsp.getRegistrationInfo().getRegistrationFormItem().getReceivedPrice()));
        }

        //对totalPrice进行四舍五入进行填充
        appendAfterRoundingTotalPrice(chargeSheets, rsp);

        return rsp;
    }

    private void appendAfterRoundingTotalPrice(List<ChargeSheet> chargeSheets, OutpatientSheetCalculateRsp rsp) {

        if (Objects.isNull(rsp)) {
            return;
        }

        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "before rounding totalPrice: {}", rsp.getTotalPrice());

        if (MathUtils.wrapBigDecimalCompare(rsp.getTotalPrice(), BigDecimal.ZERO) == 0) {
            return;
        }

        if (CollectionUtils.isEmpty(chargeSheets)) {
            return;
        }

        boolean isAllUncharged = chargeSheets.stream()
                .allMatch(chargeSheet -> chargeSheet.getStatus() == Constants.ChargeSheetStatus.CHARGED || chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED);

        if (!isAllUncharged) {
            return;
        }

        String chainId = chargeSheets.get(0).getChainId();
        ChargeConfigDetailView chargeConfigDetailView = chargeConfigService.getBranchConfigDetail(chainId);
        int currentRoundingType = Optional.ofNullable(chargeConfigDetailView).map(ChargeConfigDetailView::getRoundingType).orElse(0);

        rsp.setTotalPrice(MathUtils.calculateRoundingResult(rsp.getTotalPrice(), currentRoundingType));
    }

    @Deprecated
    private OutpatientSheetCalculateRsp.RegistrationInfo generateRegistrationInfo(List<ChargeSheet> chargeSheets, OutpatientSheetCalculateReq.RegistrationInfoReq registrationInfoReq) {

        if (registrationInfoReq == null) {
            return null;
        }

        AtomicReference<String> chargeSheetIdAtomic = new AtomicReference<>();
        OutpatientSheetCalculateRsp.OutpatientFormItem registrationChargeFormItem = chargeSheets
                .stream()
                .filter(chargeSheet -> chargeSheet.getChargeForms() != null)
                .flatMap(chargeSheet -> chargeSheet.getChargeForms().stream())
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getChargeFormItems() != null)
                .filter(item -> item.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.REGISTRATION)
                .findFirst()
                .map(chargeForm -> chargeForm.getChargeFormItems()
                        .stream()
                        .filter(Objects::nonNull)
                        .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.REGISTRATION)
                        .filter(chargeFormItem -> !TextUtils.isEmpty(chargeFormItem.getSourceFormItemId()))
                        .collect(Collectors.toMap(ChargeFormItem::getSourceFormItemId, Function.identity(), (a, b) -> {
                            // 前面已经合并了一次，这里会同时出现的只有部分退费的情况
                            if (a.getIsRefundByDose() == 1) {
                                BigDecimal doseCountSum = MathUtils.wrapBigDecimalAdd(a.getDoseCount(), b.getDoseCount());
                                a.setDoseCount(doseCountSum);
                                a.setRefundDoseCount(MathUtils.wrapBigDecimalAdd(a.getRefundDoseCount(), b.getRefundDoseCount()));
                            } else {
                                BigDecimal unitCountSum = MathUtils.wrapBigDecimalAdd(a.getUnitCount(), b.getUnitCount());
                                a.setUnitCount(unitCountSum);
                                a.setRefundUnitCount(MathUtils.wrapBigDecimalAdd(a.getRefundUnitCount(), b.getRefundUnitCount()));
                            }
                            a.setStatus(Constants.ChargeFormItemStatus.CHARGED);
                            return a;
                        }))
                        .values().stream()
                        .findFirst()
                        .map(chargeFormItem -> {
                            chargeSheetIdAtomic.set(chargeFormItem.getChargeSheetId());
                            OutpatientSheetCalculateRsp.OutpatientFormItem formItem = new OutpatientSheetCalculateRsp.OutpatientFormItem();
                            formItem.setId(chargeFormItem.getSourceFormItemId());
                            formItem.setStatus(chargeFormItem.getStatus());
                            formItem.setUnitCount(chargeFormItem.getUnitCount());
                            formItem.setProductType(chargeFormItem.getProductType());
                            formItem.setProductSubType(chargeFormItem.getProductSubType());
                            formItem.setChargeFormItemId(chargeFormItem.getId());
                            if (chargeFormItem.getIsRefundByDose() == 1) {
                                formItem.setRefundDoseCount(MathUtils.wrapBigDecimalOrZero(chargeFormItem.getRefundDoseCount()));
                                formItem.setRefundUnitCount(BigDecimal.ZERO);
                            } else {
                                formItem.setRefundDoseCount(BigDecimal.ZERO);
                                formItem.setRefundUnitCount(chargeFormItem.getRefundUnitCount());
                            }

                            formItem.setDoseCount(chargeFormItem.getDoseCount());
                            formItem.setReceivedPrice(chargeFormItem.getReceivedPrice());
                            formItem.setUnitPrice(chargeFormItem.getUnitPrice());
                            formItem.setTotalPrice(MathUtils.wrapBigDecimalAdd(MathUtils.calculateTotalPrice(chargeFormItem.getUnitPrice(), chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount(), 2), chargeFormItem.getFractionPrice()));
                            formItem.setFractionPrice(chargeFormItem.getFractionPrice());

                            if (chargeForm.getStatus() != Constants.ChargeFormStatus.REFUNDED && chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED) {
                                BigDecimal refundDoseCount = BigDecimal.ZERO;
                                BigDecimal refundUnitCount = BigDecimal.ZERO;

                                if (chargeFormItem.getIsRefundByDose() == 1) {
                                    refundDoseCount = MathUtils.wrapBigDecimalOrZero(chargeFormItem.getRefundDoseCount());
                                    refundUnitCount = chargeFormItem.getUnitCount();
                                } else {
                                    refundDoseCount = chargeFormItem.getDoseCount();
                                    refundUnitCount = chargeFormItem.getRefundUnitCount();
                                }

                                BigDecimal refundTotalPrice = MathUtils.calculateTotalPrice(chargeFormItem.getUnitPrice(), refundUnitCount, refundDoseCount, 2);
                                BigDecimal totalPrice = MathUtils.wrapBigDecimalAdd(MathUtils.calculateTotalPrice(chargeFormItem.getUnitPrice(), chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount(), 2),
                                        chargeFormItem.getFractionPrice());
                                formItem.setUnitPrice(chargeFormItem.getUnitPrice());
                                formItem.setTotalPrice(MathUtils.max(MathUtils.wrapBigDecimalSubtract(totalPrice, refundTotalPrice), BigDecimal.ZERO));
                                formItem.setFractionPrice(chargeFormItem.getFractionPrice());
                            }

                            if (chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.UNSELECTED) {
                                formItem.setUnitPrice(BigDecimal.ZERO);
                                formItem.setTotalPrice(BigDecimal.ZERO);
                                formItem.setFractionPrice(BigDecimal.ZERO);
                            }
                            return formItem;
                        })
                        .orElse(null)
                )
                .orElse(null);

        if (Objects.isNull(registrationChargeFormItem)) {
            return null;
        }

        if (registrationChargeFormItem.getStatus() == Constants.ChargeFormItemStatus.UNCHARGED && registrationInfoReq != null && registrationInfoReq.getUnitPrice() != null) {
            registrationChargeFormItem.setUnitPrice(registrationInfoReq.getUnitPrice())
                    .setTotalPrice(registrationInfoReq.getUnitPrice());
        }

        String containRegistrationChargeSheetId = chargeSheetIdAtomic.get();

        OutpatientSheetCalculateRsp.RegistrationInfo registrationInfo = new OutpatientSheetCalculateRsp.RegistrationInfo();
        registrationInfo.setRegistrationFormItem(registrationChargeFormItem);

        chargeSheets.stream()
                .filter(chargeSheet -> TextUtils.equals(chargeSheet.getId(), containRegistrationChargeSheetId))
                .findFirst()
                .ifPresent(chargeSheet -> registrationInfo.setChargeSheetId(chargeSheet.getId())
                        .setChargeSheetType(chargeSheet.getType())
                        .setChargeSheetStatus(chargeSheet.getStatus()));

        return registrationInfo;
    }

    @Deprecated
    private List<OutpatientSheetCalculateRsp.OutpatientForm> generateOutpatientForm(List<ChargeSheet> chargeSheets, OutpatientSheetCalculateReq outpatientSheetCalculateReq, boolean withDeliveryAndProcess) {

        List<String> outpatientFormItemIds = outpatientSheetCalculateReq.getOutpatientForms().stream()
                .filter(outpatientFormCalculateReq -> CollectionUtils.isNotEmpty(outpatientFormCalculateReq.getOutpatientFormItems()))
                .flatMap(outpatientFormCalculateReq -> outpatientFormCalculateReq.getOutpatientFormItems().stream())
                .filter(outpatientFormItemCalculateReq -> StringUtils.isNotEmpty(outpatientFormItemCalculateReq.getId()))
                .map(OutpatientFormItemCalculateReq::getId)
                .collect(Collectors.toList());

        Map<String, OutpatientFormCalculateReq> outpatientFormCalculateReqMap = outpatientSheetCalculateReq.getOutpatientForms().stream().collect(Collectors.toMap(OutpatientFormCalculateReq::getId, item -> item));

        boolean withExecutedCount = outpatientSheetCalculateReq.getWithExecutedCount() == 1;
        String patientOrderId = outpatientSheetCalculateReq.getPatientOrderId();
        Map<String, BigDecimal> executedIdCountMap = new HashMap<>();
        if (withExecutedCount) {
            List<ChargeExecuteItem> executeItems = chargeExecuteService.findByPatientOrderIdAndIsDeleted(patientOrderId);
            executedIdCountMap = executeItems.stream().collect(Collectors.toMap(ChargeExecuteItem::getChargeFormItemId, ChargeExecuteItem::getExecutedCount, (a, b) -> a));
        }

        //查询收费单的收费员
        Map<String, String> chargeSheetChargedByMap = chargeSheets.stream()
                .filter(chargeSheet -> chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED)
                .filter(chargeSheet -> StringUtils.isNotEmpty(chargeSheet.getChargedBy()))
                .collect(Collectors.toMap(ChargeSheet::getId, ChargeSheet::getChargedBy));

        List<String> chargedByIds = chargeSheetChargedByMap.values()
                .stream()
                .distinct()
                .collect(Collectors.toList());
        String chainId = chargeSheets.get(0).getChainId();

        Map<String, Employee> employeeMap = Optional.ofNullable(employeeService.findEmployeeList(chainId, chargedByIds)).orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.toMap(Employee::getId, Function.identity()));

        //查询绑定空中药房快递单号
        List<ChargeForm> chargeForms = chargeSheets.stream().flatMap(chargeSheet -> chargeSheet.getChargeForms().stream())
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY).collect(Collectors.toList());
        Map<String, CreateOrderView> createOrderViewMap = airPharmacyService.findAirPharmacyOrderStatusByChargeFormIdsAndClinicId(chargeForms.stream().map(ChargeForm::getId).collect(Collectors.toList()), outpatientSheetCalculateReq.getClinicId());

        Map<String, BigDecimal> finalExecutedIdCountMap = executedIdCountMap;
        List<String> composeChildrenItemIds = new ArrayList<>();
        List<OutpatientSheetCalculateRsp.OutpatientForm> existedForms = chargeSheets
                .stream()
                .filter(chargeSheet -> chargeSheet.getChargeForms() != null)
                .map(chargeSheet -> {
                            Map<String, ChargeForm> processFormMap = chargeSheet.getChargeForms().stream()
                                    .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                                    .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.PROCESS)
                                    .filter(chargeForm -> chargeForm.getProcessInfo() != null)
                                    .filter(chargeForm -> org.apache.commons.lang3.StringUtils.isNotEmpty(chargeForm.getProcessInfo().getChargeFormId()))
                                    .collect(Collectors.toMap(chargeForm -> chargeForm.getProcessInfo().getChargeFormId(), Function.identity()));

                            ChargeForm deliveryForm = chargeSheet.getChargeForms().stream()
                                    .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                                    .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.EXPRESS_DELIVERY)
                                    .findFirst().orElse(null);

                            return chargeSheet.getChargeForms().stream()
                                    .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                                    .filter(chargeForm -> chargeForm.getChargeFormItems() != null)
                                    .filter(chargeForm -> chargeForm.getSourceFormType() != Constants.SourceFormType.REGISTRATION)
                                    .filter(chargeForm -> org.apache.commons.lang3.StringUtils.isNotEmpty(chargeForm.getSourceFormId()))
                                    .map(chargeForm -> {
                                        OutpatientSheetCalculateRsp.OutpatientForm outpatientForm = new OutpatientSheetCalculateRsp.OutpatientForm();
                                        outpatientForm.setStatus(chargeForm.getStatus());
                                        outpatientForm.setId(chargeForm.getSourceFormId());
                                        outpatientForm.setChargeSheetId(chargeSheet.getId());
                                        outpatientForm.setSourceFormType(chargeForm.getSourceFormType());
                                        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED && StringUtils.isNotEmpty(chargeSheet.getChargedBy())) {
                                            Optional.ofNullable(employeeMap.get(chargeSheet.getChargedBy()))
                                                    .ifPresent(employee -> {
                                                        outpatientForm.setChargedBy(employee.getId());
                                                        outpatientForm.setChargedByName(employee.getName());
                                                        outpatientForm.setChargedByHandSign(employee.getHandSign());
                                                        outpatientForm.setChargedTime(chargeSheet.getChargedTime());
                                                    });
                                        }
                                        outpatientForm.setPharmacyType(chargeForm.getPharmacyType());
                                        outpatientForm.setPharmacyNo(chargeForm.getPharmacyNo());
                                        if (chargeForm.getUsageInfo() != null && chargeSheet.getStatus() == Constants.ChargeSheetStatus.CHARGED) {
                                            UsageInfo usageInfoReq = JsonUtils.readValue(chargeForm.getUsageInfo(), UsageInfo.class);
                                            outpatientForm.setProcessRemark(usageInfoReq.getProcessRemark());
                                            outpatientForm.setProcessBagUnitCountDecimal(usageInfoReq.getProcessBagUnitCountDecimal());
                                            outpatientForm.setTotalProcessCount(usageInfoReq.getTotalProcessCount());
                                            outpatientForm.setProcessBagUnit(usageInfoReq.getProcessBagUnit());
                                        } else {
                                            OutpatientFormCalculateReq outpatientFormCalculateReq = outpatientFormCalculateReqMap.get(chargeForm.getSourceFormId());
                                            if (outpatientFormCalculateReq != null) {
                                                outpatientForm.setProcessRemark(outpatientFormCalculateReq.getProcessRemark());
                                                outpatientForm.setProcessBagUnitCountDecimal(outpatientFormCalculateReq.getProcessBagUnitCountDecimal());
                                                outpatientForm.setTotalProcessCount(outpatientFormCalculateReq.getTotalProcessCount());
                                                outpatientForm.setProcessBagUnit(outpatientFormCalculateReq.getProcessBagUnit());
                                            }
                                        }
                                        List<OutpatientSheetCalculateRsp.OutpatientFormItem> formItems = chargeForm.getChargeFormItems()
                                                .stream()
                                                .filter(Objects::nonNull)
                                                .filter(item -> item.getIsDeleted() == 0)
                                                .filter(chargeFormItem -> !TextUtils.isEmpty(chargeFormItem.getSourceFormItemId()))
                                                .filter(chargeFormItem -> outpatientFormItemIds.contains(chargeFormItem.getSourceFormItemId()))
                                                .collect(Collectors.toMap(ChargeFormItem::getSourceFormItemId, Function.identity(), (a, b) -> {
                                                    // 前面已经合并了一次，这里会同时出现的只有部分退费的情况
                                                    if (a.getIsRefundByDose() == 1) {
                                                        BigDecimal doseCountSum = MathUtils.wrapBigDecimalAdd(a.getDoseCount(), b.getDoseCount());
                                                        a.setDoseCount(doseCountSum);
                                                        a.setRefundDoseCount(MathUtils.wrapBigDecimalAdd(a.getRefundDoseCount(), b.getRefundDoseCount()));
                                                    } else {
                                                        BigDecimal unitCountSum = MathUtils.wrapBigDecimalAdd(a.getUnitCount(), b.getUnitCount());
                                                        a.setUnitCount(unitCountSum);
                                                        a.setRefundUnitCount(MathUtils.wrapBigDecimalAdd(a.getRefundUnitCount(), b.getRefundUnitCount()));
                                                    }
                                                    a.setStatus(Constants.ChargeFormItemStatus.CHARGED);
                                                    return a;
                                                }))
                                                .values()
                                                .stream()
                                                .map(chargeFormItem -> {

                                                    OutpatientSheetCalculateRsp.OutpatientFormItem formItem = new OutpatientSheetCalculateRsp.OutpatientFormItem();
                                                    formItem.setId(chargeFormItem.getSourceFormItemId());
                                                    formItem.setStatus(chargeFormItem.getStatus());
                                                    formItem.setUnitCount(chargeFormItem.getUnitCount());
                                                    formItem.setProductType(chargeFormItem.getProductType());
                                                    formItem.setProductSubType(chargeFormItem.getProductSubType());
                                                    formItem.setChargeFormItemId(chargeFormItem.getId());
                                                    formItem.setPharmacyType(chargeFormItem.getPharmacyType());
                                                    formItem.setPharmacyNo(chargeFormItem.getPharmacyNo());
                                                    formItem.setComposeType(chargeFormItem.getComposeType());
                                                    formItem.setFeeComposeType(chargeFormItem.getFeeComposeType());
                                                    formItem.setFeeTypeId(chargeFormItem.getFeeTypeId());
                                                    formItem.setGoodsFeeType(chargeFormItem.getGoodsFeeType());
                                                    if (chargeFormItem.getIsRefundByDose() == 1) {
                                                        formItem.setRefundDoseCount(MathUtils.wrapBigDecimalOrZero(chargeFormItem.getRefundDoseCount()));
                                                        formItem.setRefundUnitCount(BigDecimal.ZERO);
                                                    } else {
                                                        formItem.setRefundDoseCount(BigDecimal.ZERO);
                                                        formItem.setRefundUnitCount(chargeFormItem.getRefundUnitCount());
                                                    }
                                                    formItem.setReceivedPrice(chargeFormItem.getReceivedPrice());

                                                    formItem.setDoseCount(chargeFormItem.getDoseCount());
                                                    if (withExecutedCount) {
                                                        BigDecimal executedCount = finalExecutedIdCountMap.getOrDefault(chargeFormItem.getId(), null);
                                                        formItem.setExecutedCount(executedCount);
                                                    }

                                                    formItem.setUnitPrice(chargeFormItem.getUnitPrice());
                                                    formItem.setTotalPrice(MathUtils.wrapBigDecimalAdd(MathUtils.calculateTotalPrice(chargeFormItem.getUnitPrice(), chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount(), 2), chargeFormItem.getFractionPrice()));
                                                    formItem.setFractionPrice(chargeFormItem.getFractionPrice());

                                                    if (chargeForm.getStatus() != Constants.ChargeFormStatus.REFUNDED && chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED) {
                                                        BigDecimal refundDoseCount = BigDecimal.ZERO;
                                                        BigDecimal refundUnitCount = BigDecimal.ZERO;

                                                        if (chargeFormItem.getIsRefundByDose() == 1) {
                                                            refundDoseCount = MathUtils.wrapBigDecimalOrZero(chargeFormItem.getRefundDoseCount());
                                                            refundUnitCount = chargeFormItem.getUnitCount();
                                                        } else {
                                                            refundDoseCount = chargeFormItem.getDoseCount();
                                                            refundUnitCount = chargeFormItem.getRefundUnitCount();
                                                        }

                                                        BigDecimal refundTotalPrice = MathUtils.calculateTotalPrice(chargeFormItem.getUnitPrice(), refundUnitCount, refundDoseCount, 2);
                                                        BigDecimal totalPrice = MathUtils.wrapBigDecimalAdd(MathUtils.calculateTotalPrice(chargeFormItem.getUnitPrice(), chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount(), 2),
                                                                chargeFormItem.getFractionPrice());
                                                        formItem.setUnitPrice(chargeFormItem.getUnitPrice());
                                                        formItem.setTotalPrice(MathUtils.max(MathUtils.wrapBigDecimalSubtract(totalPrice, refundTotalPrice), BigDecimal.ZERO));
                                                        formItem.setFractionPrice(chargeFormItem.getFractionPrice());
                                                    }

                                                    if (chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.UNSELECTED) {
                                                        formItem.setUnitPrice(BigDecimal.ZERO);
                                                        formItem.setTotalPrice(BigDecimal.ZERO);
                                                        formItem.setFractionPrice(BigDecimal.ZERO);
                                                    }

                                                    if (chargeFormItem.getComposeType() == ComposeType.COMPOSE_SUB_ITEM) {
                                                        composeChildrenItemIds.add(formItem.getId());
                                                    }
                                                    return formItem;
                                                }).collect(Collectors.toList());

                                        if (CollectionUtils.isEmpty(formItems)) {
                                            return null;
                                        }

                                        outpatientForm.setFormItems(formItems);
                                        outpatientForm.setTotalPrice(Optional.ofNullable(formItems).orElse(new ArrayList<>())
                                                .stream()
                                                .filter(outpatientFormItem -> !composeChildrenItemIds.contains(outpatientFormItem.getId()))
                                                .map(OutpatientSheetCalculateRsp.OutpatientFormItem::getTotalPrice)
                                                .filter(Objects::nonNull)
                                                .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        );

                                        boolean isChinesePrescription = chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE;

                                        if (isChinesePrescription) {
                                            if (CollectionUtils.isNotEmpty(formItems)) {

                                                OutpatientSheetCalculateRsp.OutpatientFormItem formItem = formItems.get(0);

                                                outpatientForm.setDoseCount(formItem.getDoseCount());

                                                if (outpatientForm.getStatus() == Constants.ChargeFormStatus.REFUNDED) {
                                                    outpatientForm.setRefundDoseCount(formItem.getDoseCount());
                                                } else {

                                                    BigDecimal refundDoseCount = formItems.stream().filter(outpatientFormItem -> outpatientFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED)
                                                            .findFirst().map(OutpatientSheetCalculateRsp.OutpatientFormItem::getRefundDoseCount).orElse(BigDecimal.ZERO);

                                                    //判断是否退的剂量都是一样的
                                                    boolean refundByDose = formItems.stream()
                                                            .filter(outpatientFormItem -> outpatientFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED)
                                                            .allMatch(outpatientFormItem -> MathUtils.wrapBigDecimalOrZero(outpatientFormItem.getRefundDoseCount()).compareTo(refundDoseCount) == 0);
                                                    outpatientForm.setRefundDoseCount(refundByDose ? refundDoseCount : BigDecimal.ZERO);
                                                }

                                            } else {
                                                outpatientForm.setDoseCount(BigDecimal.ZERO);
                                                outpatientForm.setRefundDoseCount(BigDecimal.ZERO);
                                            }
                                        }

                                        if (withDeliveryAndProcess) {
                                            ChargeUtils.appendDeliveryAndProcessInfo(outpatientForm, chargeSheet.getDeliveryInfo(), deliveryForm, chargeForm, processFormMap.getOrDefault(chargeForm.getId(), null), isChinesePrescription, createOrderViewMap.get(chargeForm.getId()));
                                        }
                                        return outpatientForm;
                                    })
                                    .filter(Objects::nonNull)
                                    .collect(Collectors.toList());
                        }
                )
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        List<String> existedUnchargedIds = new ArrayList<>();
        List<String> existedNotUnchargedIds = new ArrayList<>();
        Map<String, BigDecimal> executedOutpatientFormItemIdCountMap = new HashMap<>();
        Map<String, String> existedOutpatientFormItemIdMap = new HashMap<>();
        existedForms.stream()
                .filter(outpatientForm -> CollectionUtils.isNotEmpty(outpatientForm.getFormItems()))
                .flatMap(outpatientForm -> outpatientForm.getFormItems().stream())
                .forEach(outpatientFormItem -> {
                    if (outpatientFormItem.getStatus() == Constants.ChargeFormItemStatus.UNCHARGED) {
                        existedUnchargedIds.add(outpatientFormItem.getId());
                    } else {
                        existedNotUnchargedIds.add(outpatientFormItem.getId());
                    }
                    executedOutpatientFormItemIdCountMap.put(outpatientFormItem.getId(), outpatientFormItem.getExecutedCount());
                    existedOutpatientFormItemIdMap.put(outpatientFormItem.getId(), outpatientFormItem.getChargeFormItemId());
                });


        //计算门诊单上未收费的收费项
        List<OutpatientSheetCalculateRsp.OutpatientForm> unchargedForms = calculateOutpatientUnChargedForms(outpatientSheetCalculateReq, existedNotUnchargedIds);

        BiFunction<OutpatientSheetCalculateRsp.OutpatientForm, OutpatientSheetCalculateRsp.OutpatientForm, Boolean> isEqualKeyFunc = (existedForm, unchargedForm) -> TextUtils.equals(existedForm.getId(), unchargedForm.getId());
        Function<OutpatientSheetCalculateRsp.OutpatientForm, OutpatientSheetCalculateRsp.OutpatientForm> insertFunc = Function.identity();
        Function<OutpatientSheetCalculateRsp.OutpatientForm, Boolean> deleteFunc = chargeFormItem -> false;
        BiConsumer<OutpatientSheetCalculateRsp.OutpatientForm, OutpatientSheetCalculateRsp.OutpatientForm> updateFunc = (unchargedForm, existedForm) -> {
            existedForm.setFormItems(unchargedForm.getFormItems())
                    .setDoseCount(unchargedForm.getDoseCount())
                    .setTotalProcessCount(unchargedForm.getTotalProcessCount())
                    .setProcessBagUnitCountDecimal(unchargedForm.getProcessBagUnitCountDecimal())
                    .setProcessBagUnit(unchargedForm.getProcessBagUnit())
                    .setProcessRemark(unchargedForm.getProcessRemark())
                    .setPharmacyNo(unchargedForm.getPharmacyNo())
                    .setPharmacyType(unchargedForm.getPharmacyType());
        };

        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "existedForms before merge: {}, unchargedForms: {}", JsonUtils.dump(existedForms), JsonUtils.dump(unchargedForms));
        //merge之后existedForms就是最终的数据
        MergeTool.doMerge(unchargedForms, existedForms, isEqualKeyFunc, insertFunc, deleteFunc, updateFunc);

        existedForms.forEach(outpatientForm -> outpatientForm.setTotalPrice(Optional.ofNullable(outpatientForm.getFormItems()).orElse(new ArrayList<>())
                .stream()
                .filter(outpatientFormItem -> !composeChildrenItemIds.contains(outpatientFormItem.getId()))
                .map(OutpatientSheetCalculateRsp.OutpatientFormItem::getTotalPrice)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add)));

        //bind executedCount、chargeFormItemId
        existedForms.stream()
                .filter(outpatientForm -> outpatientForm.getFormItems() != null)
                .flatMap(outpatientForm -> outpatientForm.getFormItems().stream())
                .filter(outpatientFormItem -> StringUtils.isNotEmpty(outpatientFormItem.getId()))
                .forEach(outpatientFormItem -> {
                    outpatientFormItem.setExecutedCount(executedOutpatientFormItemIdCountMap.getOrDefault(outpatientFormItem.getId(), null))
                            .setChargeFormItemId(existedOutpatientFormItemIdMap.getOrDefault(outpatientFormItem.getId(), null));
                });

        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "existedForms after merge: {}", JsonUtils.dump(existedForms));

        return existedForms;
    }

    private List<OutpatientSheetCalculateRsp.OutpatientForm> calculateOutpatientUnChargedForms(OutpatientSheetCalculateReq outpatientSheetCalculateReq, List<String> existedNotUnchargedIds) {

        List<BasicCalculateFormReq> basicCalculateFormReqs = outpatientSheetCalculateReq.getOutpatientForms()
                .stream()
                .filter(formReq -> CollectionUtils.isNotEmpty(formReq.getOutpatientFormItems()))
                .map(formReq -> {

                    BasicCalculateFormReq basicCalculateFormReq = new BasicCalculateFormReq();
                    BeanUtils.copyProperties(formReq, basicCalculateFormReq);

                    basicCalculateFormReq.setItems(formReq.getOutpatientFormItems().stream()
                            .filter(itemReq -> !existedNotUnchargedIds.contains(itemReq.getId()))
                            .map(itemReq -> {
                                BasicCalculateItemReq basicCalculateItemReq = new BasicCalculateItemReq();
                                BeanUtils.copyProperties(itemReq, basicCalculateItemReq);
                                if (itemReq.getSourceItemType() == Constants.SourceItemType.SELF_PROVIDED) {
                                    basicCalculateItemReq.setUnitPrice(BigDecimal.ZERO)
                                            .setTotalPrice(BigDecimal.ZERO);
                                }
                                return basicCalculateItemReq;
                            }).collect(Collectors.toList())
                    );

                    if (CollectionUtils.isEmpty(basicCalculateFormReq.getItems())) {
                        return null;
                    }
                    return basicCalculateFormReq;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(basicCalculateFormReqs)) {
            return new ArrayList<>();
        }

        Map<String, OutpatientFormCalculateReq> outpatientFormCalculateReqMap = outpatientSheetCalculateReq.getOutpatientForms().stream().collect(Collectors.toMap(OutpatientFormCalculateReq::getId, Function.identity()));
        Map<String, OutpatientFormItemCalculateReq> outpatientFormItemCalculateReqMap = outpatientSheetCalculateReq.getOutpatientForms()
                .stream()
                .filter(outpatientFormCalculateReq -> CollectionUtils.isNotEmpty(outpatientFormCalculateReq.getOutpatientFormItems()))
                .flatMap(outpatientFormCalculateReq -> outpatientFormCalculateReq.getOutpatientFormItems().stream())
                .collect(Collectors.toMap(OutpatientFormItemCalculateReq::getId, item -> item));

        BasicCalculateSheetReq basicCalculateSheetReq = new BasicCalculateSheetReq();
        basicCalculateSheetReq.setChainId(outpatientSheetCalculateReq.getChainId())
                .setClinicId(outpatientSheetCalculateReq.getClinicId())
                .setForms(basicCalculateFormReqs)
                .setExpectedTotalPrice(null);

        BasicCalculateSheetRsp basicCalculateSheetRsp = calculateSheet(basicCalculateSheetReq);

        return basicCalculateSheetRsp.getForms()
                .stream()
                .map(basicCalculateFormRsp -> {
                    OutpatientSheetCalculateRsp.OutpatientForm outpatientForm = new OutpatientSheetCalculateRsp.OutpatientForm();
                    OutpatientFormCalculateReq outpatientFormCalculateReq = outpatientFormCalculateReqMap.get(basicCalculateFormRsp.getId());
                    outpatientForm.setTotalPrice(basicCalculateFormRsp.getTotalPrice());
                    outpatientForm.setDoseCount(BigDecimal.ONE);
                    outpatientForm.setId(basicCalculateFormRsp.getId());
                            outpatientForm.setSourceFormType(basicCalculateFormRsp.getSourceFormType());
                            outpatientForm.setTotalProcessCount(outpatientFormCalculateReq.getTotalProcessCount());
                            outpatientForm.setProcessBagUnitCountDecimal(outpatientFormCalculateReq.getProcessBagUnitCountDecimal());
                            outpatientForm.setProcessBagUnit(outpatientFormCalculateReq.getProcessBagUnit());
                            outpatientForm.setProcessRemark(outpatientFormCalculateReq.getProcessRemark());
                            outpatientForm.setPharmacyType(outpatientFormCalculateReq.getPharmacyType());
                            outpatientForm.setPharmacyNo(outpatientFormCalculateReq.getPharmacyNo());
                            outpatientForm.setFormItems(basicCalculateFormRsp.getItems()
                                    .stream()
                                    .map(basicCalculateItemRsp -> {
                                        OutpatientSheetCalculateRsp.OutpatientFormItem outpatientFormItem = new OutpatientSheetCalculateRsp.OutpatientFormItem();
                                        OutpatientFormItemCalculateReq outpatientFormItemCalculateReq = outpatientFormItemCalculateReqMap.get(basicCalculateItemRsp.getId());
                                        outpatientFormItem.setTotalPrice(basicCalculateItemRsp.getTotalPrice());
                                        outpatientFormItem.setId(basicCalculateItemRsp.getId())
                                                .setProductType(basicCalculateItemRsp.getProductType())
                                                .setProductSubType(basicCalculateItemRsp.getProductSubType())
                                                .setUnitCount(basicCalculateItemRsp.getUnitCount())
                                                .setDoseCount(basicCalculateItemRsp.getDoseCount())
                                                .setUnitPrice(basicCalculateItemRsp.getUnitPrice())
                                                .setFractionPrice(basicCalculateItemRsp.getFractionPrice())
                                                .setPharmacyType(outpatientFormItemCalculateReq.getPharmacyType())
                                                .setPharmacyNo(outpatientFormItemCalculateReq.getPharmacyNo());
                                        return outpatientFormItem;
                                    })
                                    .collect(Collectors.toList())
                            );

                    if (CollectionUtils.isEmpty(outpatientForm.getFormItems())) {
                        return null;
                    }

                    if (outpatientForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE || outpatientForm.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY) {
                        BigDecimal doseCount = outpatientForm.getFormItems()
                                .stream()
                                .filter(outpatientFormItem -> outpatientFormItem.getProductType() == Constants.ProductType.MEDICINE)
                                .filter(outpatientFormItem -> outpatientFormItem.getDoseCount() != null)
                                .findFirst()
                                .map(OutpatientSheetCalculateRsp.OutpatientFormItem::getDoseCount)
                                .orElse(BigDecimal.ONE);
                        outpatientForm.setDoseCount(doseCount);
                    }
                    return outpatientForm;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public OutpatientSheetCalculateRsp calculateOutpatientSheetDetail(OutpatientSheetDetailCalculateReq req) {
        OutpatientSheet outpatientSheet = req.getOutpatientSheet();
        boolean withDeliveryAndProcess = req.getWithDeliveryAndProcess() == 1;
        boolean withExecutedCount = req.getWithExecutedCount() == 1;
        boolean withAllItems = req.getWithAllItems() == 1;
        String patientOrderId = outpatientSheet.getPatientOrderId();

        List<ChargeSheet> existedChargeSheetList = Optional.ofNullable(chargeSheetService.findAllByPatientOrderId(outpatientSheet.getPatientOrderId())).orElse(new ArrayList<>());

        //这里用内存中的门诊单时因为下面两个方法会修改门诊单的内容
        OutpatientSheet memoryOutpatientSheet = JsonUtils.readValue(JsonUtils.dump(outpatientSheet), OutpatientSheet.class);
        //新增或修改挂号收费单
        chargeSheetConvertHandler.insertOrUpdateRegistrationChargeSheet(existedChargeSheetList, memoryOutpatientSheet, req.getRegistrationInfo(), "");

        //新增或修改门诊收费单
        OutpatientSheetConvertChargeSheetRsp convertChargeSheetRsp = chargeSheetConvertHandler.insertOrUpdateOutpatientChargeSheetCore(existedChargeSheetList, memoryOutpatientSheet, 0, null, false,"");

        //删除了也要chargeSheetId
        String unchargedChargeSheetId = Optional.ofNullable(convertChargeSheetRsp)
                .map(OutpatientSheetConvertChargeSheetRsp::getUnchargedChargeSheet)
                .map(ChargeSheet::getId)
                .orElse(null);

        ChargeSheet unchargedChargeSheet = Optional.ofNullable(convertChargeSheetRsp)
                .map(OutpatientSheetConvertChargeSheetRsp::getUnchargedChargeSheet)
                .filter(chargeSheet -> chargeSheet.getIsDeleted() == 0)
                .orElse(null);

        ChargeSheet registrationChargeSheet = Optional.ofNullable(convertChargeSheetRsp).map(OutpatientSheetConvertChargeSheetRsp::getRegistrationChargeSheet).orElse(null);
        unchargedChargeSheet = mergeRegistrationSheetAndOutpatientSheet(unchargedChargeSheet, registrationChargeSheet);

        existedChargeSheetList.removeIf(chargeSheet -> chargeSheet.getIsDeleted() == 1);
        if (StringUtils.isNotEmpty(unchargedChargeSheetId)) {
            existedChargeSheetList.removeIf(chargeSheet -> Objects.equals(chargeSheet.getId(), unchargedChargeSheetId));
        }
        if (Objects.nonNull(unchargedChargeSheet)) {
            ChargeSheet finalUnchargedChargeSheet = unchargedChargeSheet;
            existedChargeSheetList.removeIf(chargeSheet -> Objects.equals(chargeSheet.getId(), finalUnchargedChargeSheet.getId()));
            existedChargeSheetList.add(unchargedChargeSheet);
        }


        List<ChargeSheet> availableChargeSheets = existedChargeSheetList.stream()
                .filter(chargeSheet -> chargeSheet.getType() == ChargeSheet.Type.OUTPATIENT
                        || chargeSheet.getType() == ChargeSheet.Type.REGISTRATION
                        || chargeSheet.getType() == ChargeSheet.Type.DIRECT_OUTPATIENT_ADDITIONAL
                )
                .collect(Collectors.toList());

        //计算收费单，得到每个item的各个维度的价格信息
        BusinessSheetCalculateChargeSheetDto businessSheetCalculateChargeSheetDto = businessSheetCalculateChargeSheet(availableChargeSheets,
                outpatientSheet.getClinicId(),
                outpatientSheet.getPatientOrderId(),
                withDeliveryAndProcess,
                withExecutedCount);

        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "businessSheetCalculateChargeSheetDto: {}", JsonUtils.dump(businessSheetCalculateChargeSheetDto));

        List<BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeSheet> chargeSheets = businessSheetCalculateChargeSheetDto.getChargeSheets();

        OutpatientSheetCalculateRsp rsp = new OutpatientSheetCalculateRsp();

        rsp.setOutpatientForms(new ArrayList<>());
        rsp.setChargeSheets(businessSheetCalculateChargeSheetDto.getChargeSheets()
                .stream()
                .map(chargeSheet -> {
                    OutpatientSheetCalculateRsp.ChargeSheet rpcChargeSheet = new OutpatientSheetCalculateRsp.ChargeSheet();
                    BeanUtils.copyProperties(chargeSheet, rpcChargeSheet);
                    return rpcChargeSheet;
                }).collect(Collectors.toList()));

        //查询欠费单
        Map<String, List<ChargeOweSheet>> chargeOweSheetsMap = chargeOweSheetRepository.findAllByChargeSheetIdInAndClinicIdAndIsDeleted(existedChargeSheetList.stream()
                                .map(ChargeSheet::getId)
                                .collect(Collectors.toList()),
                        outpatientSheet.getClinicId(),
                        0)
                .stream()
                .collect(Collectors.groupingBy(ChargeOweSheet::getChargeSheetId));
        List<ChargeTransaction> chargeTransactionList = new ArrayList<>();
        //将收费单和对应的欠费单的收退费流水组合出来，为了判断收费流水中是否有医保支付
        for (ChargeSheet allChargeSheet : existedChargeSheetList) {
            chargeTransactionList.addAll(
                    ChargeSheetFeeProtocol.getChargeTransactionAndChargeActionContainOwe(allChargeSheet, chargeOweSheetsMap.get(allChargeSheet.getId()))
                            .getChargeTransactions()
            );
        }

        if (StringUtils.isNotEmpty(patientOrderId)) {
            // 设置费别
            PatientOrder patientOrder = patientOrderService.findPatientOrder(patientOrderId, null);
            Integer shebaoChargeType = null;
            if (patientOrder != null) {
                shebaoChargeType = patientOrder.getShebaoChargeType();
            }

            rsp.setHealthCardPayLevel(ChargeUtils.generateHealthCardPayLevel(chargeTransactionList, shebaoChargeType));
            rsp.setChargedOutpatientAdjustmentFee(chargeSheets
                    .stream()
                    .filter(chargeSheet -> chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED)
                    .map(BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeSheet::getOutpatientAdjustmentFee)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
        }

        // sheet status
        if (chargeSheets.stream().filter(chargeSheet -> chargeSheet.getType() != ChargeSheet.Type.REGISTRATION).count() == 0
                || chargeSheets.stream().filter(chargeSheet -> chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED || chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_CHARGED).count() > 0) {
            rsp.setStatus(Constants.ChargeSheetStatus.UNCHARGED);
        } else if (chargeSheets.stream().filter(chargeSheet -> chargeSheet.getStatus() == Constants.ChargeSheetStatus.CHARGED || chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_REFUNDED).count() > 0) {
            rsp.setStatus(Constants.ChargeSheetStatus.CHARGED);
        } else {
            rsp.setStatus(Constants.ChargeSheetStatus.REFUNDED);
        }

        //补充门诊form和收费处直接加的form
        fillOutpatientFormsAndChargeForms(rsp, chargeSheets, outpatientSheet, withAllItems);

        rsp.setRegistrationInfo(generateRegistrationInfo(chargeSheets));

        if (rsp.getRegistrationInfo() != null &&
                (rsp.getRegistrationInfo().getChargeSheetStatus() == Constants.ChargeSheetStatus.UNCHARGED || rsp.getRegistrationInfo().getChargeSheetType() == ChargeSheet.Type.OUTPATIENT)
        ) {
            rsp.setIsTotalPriceContainRegistrationPrice(1);
            BusinessSheetBasicPriceInfo.mergeAllPrice(rsp, rsp.getRegistrationInfo().getRegistrationFormItem());
        }

        //对displayTotalPrice进行凑整抹零
        calculateAfterRoundingDisplayTotalPrice(chargeSheets, rsp);

        return rsp;
    }

    private void calculateAfterRoundingDisplayTotalPrice(List<BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeSheet> chargeSheets, OutpatientSheetCalculateRsp rsp) {

        if (Objects.isNull(rsp)) {
            return;
        }

        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "before rounding totalPrice: {}", rsp.getTotalPrice());

        if (MathUtils.wrapBigDecimalCompare(rsp.getTotalPrice(), BigDecimal.ZERO) == 0) {
            return;
        }

        if (CollectionUtils.isEmpty(chargeSheets)) {
            return;
        }

        boolean containUnchargedSheet = chargeSheets.stream()
                .anyMatch(chargeSheet -> chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED
                        || chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_CHARGED);

        if (!containUnchargedSheet) {
            return;
        }

        String chainId = chargeSheets.get(0).getChainId();
        ChargeConfigDetailView chargeConfigDetailView = chargeConfigService.getBranchConfigDetail(chainId);
        int currentRoundingType = Optional.ofNullable(chargeConfigDetailView).map(ChargeConfigDetailView::getRoundingType).orElse(0);

        rsp.setDisplayTotalPrice(MathUtils.calculateRoundingResult(rsp.getDisplayTotalPrice(), currentRoundingType));
    }

    private void fillOutpatientFormsAndChargeForms(OutpatientSheetCalculateRsp rsp,
                                                   List<BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeSheet> chargeSheets,
                                                   OutpatientSheet outpatientSheet,
                                                   boolean withAllItems) {

        List<String> outpatientFormItemIds = collectOutpatientFormItemIds(outpatientSheet);
        Map<String, Boolean> prescriptionFormIdIsDecoctionIdMap = Optional.ofNullable(outpatientSheet.getPrescriptionForms())
                .orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.toMap(PrescriptionForm::getId, PrescriptionForm::getIsDecoction));

        //查询绑定空中药房快递单号
        List<BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeForm> chargeForms = chargeSheets.stream().flatMap(chargeSheet -> Optional.ofNullable(chargeSheet.getChargeForms()).orElse(new ArrayList<>()).stream()).filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY).collect(Collectors.toList());
        Map<String, CreateOrderView> createOrderViewMap = airPharmacyService.findAirPharmacyOrderStatusByChargeFormIdsAndClinicId(chargeForms.stream().map(BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeForm::getId).collect(Collectors.toList()), outpatientSheet.getClinicId());


        List<OutpatientSheetCalculateRsp.OutpatientForm> withoutOutpatientChargeForms = new ArrayList<>();
        List<OutpatientSheetCalculateRsp.OutpatientForm> outpatientForms = new ArrayList<>();
        chargeSheets
                .stream()
                .filter(chargeSheet -> chargeSheet.getChargeForms() != null)
                .forEach(chargeSheet -> {
                    Map<String, BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeForm> processFormMap = chargeSheet.getChargeForms().stream()
                            .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.PROCESS)
                            .filter(chargeForm -> chargeForm.getProcessInfo() != null)
                            .filter(chargeForm -> org.apache.commons.lang3.StringUtils.isNotEmpty(chargeForm.getProcessInfo().getChargeFormId()))
                            .collect(Collectors.toMap(chargeForm -> chargeForm.getProcessInfo().getChargeFormId(), Function.identity()));

                    BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeForm deliveryForm = chargeSheet.getChargeForms().stream()
                            .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.EXPRESS_DELIVERY)
                            .findFirst().orElse(null);

                    List<OutpatientSheetCalculateRsp.OutpatientForm> outpatientFormsFlag = chargeSheet.getChargeForms().stream()
                            .filter(chargeForm -> {

                                if (chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_TYPE_EYE_GLASS) {
                                    return true;
                                }
                                return chargeForm.getChargeFormItems() != null;
                            })
                            .filter(chargeForm -> chargeForm.getSourceFormType() != Constants.SourceFormType.REGISTRATION)
                            .filter(chargeForm -> org.apache.commons.lang3.StringUtils.isNotEmpty(chargeForm.getSourceFormId()))
                            .map(chargeForm -> {

                                List<BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeFormItem> needConvertChargeFormItems = cn.abcyun.cis.commons.util.ListUtils.alwaysList(chargeForm.getChargeFormItems())
                                        .stream()
                                        .filter(Objects::nonNull)
                                        .filter(chargeFormItem -> !TextUtils.isEmpty(chargeFormItem.getSourceFormItemId()) && outpatientFormItemIds.contains(chargeFormItem.getSourceFormItemId()))
                                        .filter(chargeFormItem -> chargeFormItem.getProductType() != Constants.ProductType.PROCESS
                                                && chargeFormItem.getProductType() != Constants.ProductType.EXPRESS_DELIVERY
                                                && chargeFormItem.getProductType() != Constants.ProductType.INGREDIENT)
                                        .collect(Collectors.toList());

                                return generateOutpatientFormForChargeForm(chargeForm,
                                        chargeSheet,
                                        createOrderViewMap,
                                        processFormMap,
                                        deliveryForm,
                                        prescriptionFormIdIsDecoctionIdMap,
                                        needConvertChargeFormItems,
                                        true
                                );
                            })
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());

                    outpatientForms.addAll(outpatientFormsFlag);

                    List<String> outpatientChargeFormItemIds = outpatientFormsFlag
                            .stream()
                            .flatMap(form -> {
                                List<OutpatientSheetCalculateRsp.OutpatientFormItem> items = new ArrayList<>();
                                Optional.ofNullable(form.getFormItems()).ifPresent(items::addAll);
                                Optional.ofNullable(form.getDeliveryItemPriceInfo())
                                        .filter(item -> item.getIsSupplementary() == YesOrNo.NO)
                                        .ifPresent(items::add);
                                Optional.ofNullable(form.getProcessItemPriceInfo())
                                        .filter(item -> item.getIsSupplementary() == YesOrNo.NO)
                                        .ifPresent(items::add);
                                Optional.ofNullable(form.getIngredientItemPriceInfo())
                                        .filter(item -> item.getIsSupplementary() == YesOrNo.NO)
                                        .ifPresent(items::add);
                                return items.stream();
                            })
                            .map(OutpatientSheetCalculateRsp.OutpatientFormItem::getChargeFormItemId)
                            .filter(StringUtils::isNotEmpty)
                            .collect(Collectors.toList());

                    if (withAllItems) {
                        List<OutpatientSheetCalculateRsp.OutpatientForm> withoutOutpatientChargeFormsFlag = chargeSheet.getChargeForms().stream()
                                .filter(chargeForm -> chargeForm.getChargeFormItems() != null)
                                .filter(chargeForm -> chargeForm.getSourceFormType() != Constants.SourceFormType.REGISTRATION)
                                .map(chargeForm -> {

                                    List<BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeFormItem> needConvertChargeFormItems = chargeForm.getChargeFormItems()
                                            .stream()
                                            .filter(Objects::nonNull)
                                            .filter(chargeFormItem -> !outpatientChargeFormItemIds.contains(chargeFormItem.getId()))
                                            .filter(chargeFormItem -> TextUtils.isEmpty(chargeFormItem.getSourceFormItemId()))
                                            .filter(chargeFormItem -> chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED)
                                            .filter(chargeFormItem -> {
                                                //空中药房由于没有把实收摊到每个子项上，只能特殊判断，只要是空中药房，都返回所有子项
                                                if (chargeForm.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY) {
                                                    //如果空中药房是门诊处开的，快递费加工费辅料费不能在补单中出现
                                                    if (StringUtils.isNotEmpty(chargeForm.getSourceFormId())) {
                                                        return chargeFormItem.getProductType() != Constants.ProductType.PROCESS
                                                                && chargeFormItem.getProductType() != Constants.ProductType.EXPRESS_DELIVERY
                                                                && chargeFormItem.getProductType() != Constants.ProductType.INGREDIENT;
                                                    }
                                                    return true;
                                                }
                                                return true;
                                            }).collect(Collectors.toList());

                                    return generateOutpatientFormForChargeForm(chargeForm,
                                            chargeSheet,
                                            createOrderViewMap,
                                            processFormMap,
                                            deliveryForm,
                                            prescriptionFormIdIsDecoctionIdMap,
                                            needConvertChargeFormItems,
                                            false
                                    );
                                })
                                .filter(Objects::nonNull)
                                .peek(outpatientForm -> {
                                    //将子项全部移除掉
                                    if (outpatientForm.getFormItems() != null) {
                                        outpatientForm.getFormItems().removeIf(item -> !ChargeFormItemUtils.isTopItem(item.getComposeType(), item.getGoodsFeeType()));
                                        outpatientForm.buildAllPrice();
                                    }
                                })
                                .collect(Collectors.toList());
                        withoutOutpatientChargeForms.addAll(withoutOutpatientChargeFormsFlag);
                    }
                });

        rsp.setOutpatientForms(outpatientForms);
        rsp.setChargeForms(withoutOutpatientChargeForms);
        rsp.buildAllPrice();
    }

    private List<String> collectOutpatientFormItemIds(OutpatientSheet outpatientSheet) {

        if (Objects.isNull(outpatientSheet)) {
            return new ArrayList<>();
        }

        List<String> outpatientFormItemIds = Optional.ofNullable(outpatientSheet.getPrescriptionForms())
                .orElse(new ArrayList<>())
                .stream()
                .filter(prescriptionForm -> CollectionUtils.isNotEmpty(prescriptionForm.getPrescriptionFormItems()))
                .flatMap(prescriptionForm -> prescriptionForm.getPrescriptionFormItems().stream())
                .filter(prescriptionFormItem -> StringUtils.isNotEmpty(prescriptionFormItem.getId()))
                .map(PrescriptionFormItem::getId)
                .collect(Collectors.toList());
        outpatientFormItemIds.addAll(Optional.ofNullable(outpatientSheet.getProductForms())
                .orElse(new ArrayList<>())
                .stream()
                .filter(productForm -> CollectionUtils.isNotEmpty(productForm.getProductFormItems()))
                .flatMap(productForm -> productForm.getProductFormItems().stream())
                .filter(productFormItem -> StringUtils.isNotEmpty(productFormItem.getId()))
                .map(ProductFormItem::getId)
                .collect(Collectors.toList())
        );

        return outpatientFormItemIds;
    }

    private OutpatientSheetCalculateRsp.RegistrationInfo generateRegistrationInfo(List<BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeSheet> chargeSheets) {

        if (CollectionUtils.isEmpty(chargeSheets)) {
            return null;
        }


        AtomicReference<String> chargeSheetIdAtomic = new AtomicReference<>();

        OutpatientSheetCalculateRsp.OutpatientFormItem registrationChargeFormItem = chargeSheets
                .stream()
                .filter(chargeSheet -> chargeSheet.getChargeForms() != null)
                .flatMap(chargeSheet -> chargeSheet.getChargeForms().stream())
                .filter(chargeForm -> chargeForm.getChargeFormItems() != null)
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.REGISTRATION)
                .findFirst()
                .map(chargeForm -> chargeForm.getChargeFormItems()
                        .stream()
                        .filter(Objects::nonNull)
                        .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.REGISTRATION)
                        .findFirst()
                        .map(chargeFormItem -> {
                            chargeSheetIdAtomic.set(chargeFormItem.getChargeSheetId());
                            return chargeFormItem.convertToOutpatientFormItem();
                        })
                        .orElse(null)
                )
                .orElse(null);

        if (Objects.isNull(registrationChargeFormItem)) {
            return null;
        }

        String containRegistrationChargeSheetId = chargeSheetIdAtomic.get();

        OutpatientSheetCalculateRsp.RegistrationInfo registrationInfo = new OutpatientSheetCalculateRsp.RegistrationInfo();
        registrationInfo.setRegistrationFormItem(registrationChargeFormItem);

        chargeSheets.stream()
                .filter(chargeSheet -> TextUtils.equals(chargeSheet.getId(), containRegistrationChargeSheetId))
                .findFirst()
                .ifPresent(chargeSheet -> registrationInfo.setChargeSheetId(chargeSheet.getId())
                        .setChargeSheetType(chargeSheet.getType())
                        .setChargeSheetStatus(chargeSheet.getStatus()));

        return registrationInfo;
    }

    private OutpatientSheetCalculateRsp.OutpatientForm generateOutpatientFormForChargeForm(BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeForm chargeForm,
                                                                                           BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeSheet chargeSheet,
                                                                                           Map<String, CreateOrderView> createOrderViewMap,
                                                                                           Map<String, BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeForm> processFormMap,
                                                                                           BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeForm deliveryForm,
                                                                                           Map<String, Boolean> prescriptionFormIdIsDecoctionIdMap,
                                                                                           List<BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeFormItem> needConvertChargeFormItems,
                                                                                           boolean needBindProcessAndDelivery) {

        OutpatientSheetCalculateRsp.OutpatientForm outpatientForm = new OutpatientSheetCalculateRsp.OutpatientForm();
        outpatientForm.setStatus(chargeForm.getStatus());
        outpatientForm.setId(chargeForm.getSourceFormId());
        outpatientForm.setChargeSheetId(chargeForm.getChargeSheetId());
        outpatientForm.setChargeFormId(chargeForm.getId());
        outpatientForm.setSourceFormType(chargeForm.getSourceFormType());
        outpatientForm.setChargedBy(chargeForm.getChargedBy());
        outpatientForm.setChargedByName(chargeForm.getChargedByName());
        outpatientForm.setChargedByHandSign(chargeForm.getChargedByHandSign());
        outpatientForm.setChargedTime(chargeSheet.getChargedTime());
        outpatientForm.setPharmacyType(chargeForm.getPharmacyType());
        outpatientForm.setPharmacyNo(chargeForm.getPharmacyNo());
        outpatientForm.setDoseCount(chargeForm.getDoseCount());
        outpatientForm.setRefundDoseCount(chargeForm.getRefundDoseCount());

        if (Objects.nonNull(chargeForm.getUsageInfoJson())) {
            UsageInfo usageInfo = JsonUtils.readValue(chargeForm.getUsageInfoJson(), UsageInfo.class);
            outpatientForm.setRequirement(Optional.ofNullable(usageInfo).map(UsageInfo::getRequirement).orElse(null));
        }


        /**
         * 目前配镜处方没有收费项，如果是配镜处方在这直接return
         */
        if (chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_TYPE_EYE_GLASS) {
            return outpatientForm;
        }

        //将chargeFormItems转换成outpatientFormItems
        List<OutpatientSheetCalculateRsp.OutpatientFormItem> formItems = needConvertChargeFormItems.stream()
                .map(BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeFormItem::convertToOutpatientFormItem)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        outpatientForm.setFormItems(formItems);

        if (needBindProcessAndDelivery) {
            //绑定快递费、加工费、辅料费
            bindProcessAndDeliveryAndIngredientItem(outpatientForm,
                    chargeSheet.getDeliveryInfo(),
                    deliveryForm,
                    chargeForm,
                    processFormMap,
                    prescriptionFormIdIsDecoctionIdMap.getOrDefault(chargeForm.getSourceFormId(), false),
                    createOrderViewMap.get(chargeForm.getId()));
        }

        if (CollectionUtils.isEmpty(outpatientForm.getFormItems())) {
            return null;
        }

        outpatientForm.buildAllPrice();
        return outpatientForm;

    }

    private void updateComposeAndFeeParentItemRefundedFee(List<BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeFormItem> businessCalculateChargeFormItems,
                                                          List<ChargeFormItem> needConvertChargeFormItems,
                                                          Map<String, ChargeFormItem> mergedRefundChargeFormItemIdMap) {

        if (CollectionUtils.isEmpty(businessCalculateChargeFormItems) || CollectionUtils.isEmpty(needConvertChargeFormItems)) {
            return;
        }

        Map<String, List<ChargeFormItem>> parentChargeFormItemIdMap = needConvertChargeFormItems.stream()
                .filter(chargeFormItem -> StringUtils.isNotEmpty(chargeFormItem.getComposeParentFormItemId()))
                .collect(Collectors.groupingBy(ChargeFormItem::getComposeParentFormItemId, Collectors.toList()));

        //先处理费用母项
        businessCalculateChargeFormItems.stream()
                .filter(outpatientFormItem -> StringUtils.isNotEmpty(outpatientFormItem.getId())
                        && ChargeFormItemUtils.isTopItem(outpatientFormItem.getComposeType(), outpatientFormItem.getGoodsFeeType())
                        && ChargeFormItemUtils.isParentItem(outpatientFormItem.getComposeType(), outpatientFormItem.getGoodsFeeType())
                )
                .filter(outpatientFormItem -> outpatientFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED)
                .forEach(outpatientFormItem -> {
                    String chargeFormItemId = outpatientFormItem.getId();
                    List<ChargeFormItem> children = parentChargeFormItemIdMap.getOrDefault(chargeFormItemId, new ArrayList<>());
                    List<ChargeFormItem> childrenRefundItems = children.stream()
                            .map(childId -> mergedRefundChargeFormItemIdMap.getOrDefault(childId.getId(), null))
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());

                    //有子项退过费，那么母项的退费金额就直接用应收减实收
                    if (CollectionUtils.isNotEmpty(childrenRefundItems)) {
                        if (outpatientFormItem.getComposeType() == ComposeType.COMPOSE) {
                            outpatientFormItem.setStatus(Constants.ChargeFormItemStatus.COMPOSE_SUB_REFUNDED);
                        }
                        outpatientFormItem.setRefundedFee(MathUtils.wrapBigDecimalSubtract(outpatientFormItem.getReceivableFee(), outpatientFormItem.getReceivedPrice()).negate());
                    }
                });

    }

    private List<ChargeFormItem> filterConvertOutpatientFormItemChargeFormItems(ChargeForm chargeForm,
                                                                                List<String> filterChargeFormItemIds,
                                                                                boolean withOutpatientForm,
                                                                                List<String> outpatientFormItemIds) {

        if (Objects.isNull(chargeForm)) {
            return new ArrayList<>();
        }

        return chargeForm.getChargeFormItems()
                .stream()
                .filter(Objects::nonNull)
                .filter(item -> item.getIsDeleted() == 0)
                .filter(chargeFormItem -> !filterChargeFormItemIds.contains(chargeFormItem.getId()))
                .filter(chargeFormItem -> {
                    if (withOutpatientForm) {
                        return !TextUtils.isEmpty(chargeFormItem.getSourceFormItemId()) && outpatientFormItemIds.contains(chargeFormItem.getSourceFormItemId());
                    }
                    return TextUtils.isEmpty(chargeFormItem.getSourceFormItemId());
                })
                .filter(chargeFormItem -> chargeFormItem.getProductType() != Constants.ProductType.PROCESS
                        && chargeFormItem.getProductType() != Constants.ProductType.EXPRESS_DELIVERY
                        && chargeFormItem.getProductType() != Constants.ProductType.INGREDIENT)
                .filter(chargeFormItem -> {
                    //空中药房由于没有把实收摊到每个子项上，只能特殊判断，只要是空中药房，都返回所有子项
                    if (chargeForm.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY) {
                        return true;
                    } else {
                        return chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.REFUNDED;
                    }
                }).collect(Collectors.toList());
    }

    private Map<String, BigDecimal> generateAirPharmacyRefundItemIdReceivedFeeMap(ChargeForm chargeForm) {
        //处理空中药房退费的实收问题
        if (chargeForm.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY && chargeForm.getStatus() == Constants.ChargeFormStatus.REFUNDED) {
            //将form的实收平摊到每个item上
            List<FlatReceivedPriceHelper.FlatCell> flatCells = Optional.ofNullable(chargeForm.getChargeFormItems())
                    .orElse(new ArrayList<>())
                    .stream()
                    .filter(item -> item.getIsDeleted() == 0)
                    .filter(chargeFormItem -> chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED
                            || chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.REFUNDED)
                    .filter(chargeFormItem -> chargeFormItem.getSourceItemType() == Constants.SourceItemType.NORMAL)
                    .map(chargeFormItem -> new FlatReceivedPriceHelper.FlatCell()
                            .setId(chargeFormItem.getId())
                            .setTotalPrice(chargeFormItem.calculateTotalPrice())
                            .setMaxFlatPrice(chargeFormItem.calculateTotalPrice()))
                    .collect(Collectors.toList());
            FlatReceivedPriceHelper flatReceivedPriceHelper = new FlatReceivedPriceHelper(chargeForm.getReceivedPrice());
            try {
                //catch掉，不要影响门诊单的打开
                flatReceivedPriceHelper.flat(flatCells);
            } catch (Exception e) {
                log.error("flatReceivedPriceHelper.flat error, chargeFormId:{}, error:{}", chargeForm.getId(), e.getMessage());
            }
            return flatCells.stream()
                    .collect(Collectors.toMap(FlatReceivedPriceHelper.FlatCell::getId, cell -> MathUtils.wrapBigDecimalOrZero(cell.getFlatPrice())));

        }

        return new HashMap<>();
    }

    private Map<String, BigDecimal> generateAirPharmacyRefundItemIdRefundedFeeMap(ChargeForm chargeForm) {
        //处理空中药房退费的实收问题
        if (chargeForm.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY && chargeForm.getStatus() == Constants.ChargeFormStatus.REFUNDED) {
            //将form的实收平摊到每个item上
            List<FlatReceivedPriceHelper.FlatCell> flatCells = Optional.ofNullable(chargeForm.getChargeFormItems())
                    .orElse(new ArrayList<>())
                    .stream()
                    .filter(item -> item.getIsDeleted() == 0)
                    .filter(chargeFormItem -> chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED
                            || chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.REFUNDED)
                    .filter(chargeFormItem -> chargeFormItem.getSourceItemType() == Constants.SourceItemType.NORMAL)
                    .map(chargeFormItem -> new FlatReceivedPriceHelper.FlatCell()
                            .setId(chargeFormItem.getId())
                            .setTotalPrice(chargeFormItem.calculateTotalPrice())
                            .setMaxFlatPrice(chargeFormItem.calculateTotalPrice()))
                    .collect(Collectors.toList());
            FlatReceivedPriceHelper flatReceivedPriceHelper = new FlatReceivedPriceHelper(MathUtils.wrapBigDecimalAdd(chargeForm.getTotalPrice(), chargeForm.getDiscountPrice()).subtract(MathUtils.wrapBigDecimalOrZero(chargeForm.getReceivedPrice())));
            try {
                //catch掉，不要影响门诊单的打开
                flatReceivedPriceHelper.flat(flatCells);
            } catch (Exception e) {
                log.error("flatReceivedPriceHelper.flat error, chargeFormId:{}, error:{}", chargeForm.getId(), e.getMessage());
            }
            return flatCells.stream()
                    .collect(Collectors.toMap(FlatReceivedPriceHelper.FlatCell::getId, cell -> MathUtils.wrapBigDecimalOrZero(cell.getFlatPrice())));

        }

        return new HashMap<>();
    }


    private void bindProcessAndDeliveryAndIngredientItem(OutpatientSheetCalculateRsp.OutpatientForm outpatientForm,
                                                         ChargeDeliveryInfo deliveryInfo,
                                                         BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeForm deliveryForm,
                                                         BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeForm chargeForm,
                                                         Map<String, BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeForm> processFormMap,
                                                         boolean isDecoction,
                                                         CreateOrderView airPharmacyOrderView) {

        if (Objects.isNull(outpatientForm) || Objects.isNull(chargeForm)) {
            return;
        }

        /**
         * 快递费，加工费，辅料费
         */
        if (chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE && chargeForm.getPharmacyType() == GoodsConst.PharmacyType.LOCAL_PHARMACY) {
            //加工费
            BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeForm processForm= processFormMap.getOrDefault(chargeForm.getId(), null);
            Optional.ofNullable(processForm)
                    .map(BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeForm::getChargeFormItems)
                    .orElse(new ArrayList<>())
                    .stream()
                    .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.PROCESS)
                    .findFirst()
                    .map(BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeFormItem::convertToOutpatientFormItem)
                    .ifPresent(item -> {
                        OutpatientSheetCalculateRsp.ProcessItemPriceInfo processItemPriceInfo = new OutpatientSheetCalculateRsp.ProcessItemPriceInfo();
                        BeanUtils.copyProperties(item, processItemPriceInfo);
                        processItemPriceInfo.initProcessInfo(JsonUtils.readValue(chargeForm.getUsageInfoJson(), UsageInfo.class));
                        processItemPriceInfo.setIsSupplementary(isDecoction ? YesOrNo.NO : YesOrNo.YES);
                        if (Objects.nonNull(processForm.getProcessInfo()) && Objects.nonNull(processForm.getProcessInfo().getTakeMedicationTime())) {
                            processItemPriceInfo.setTakeMedicationTime(processForm.getProcessInfo().getTakeMedicationTime());
                        }
                        outpatientForm.setProcessItemPriceInfo(processItemPriceInfo);
                    });


            if (Objects.nonNull(deliveryInfo)) {
                //快递费
                Optional.ofNullable(deliveryForm)
                        .map(BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeForm::getChargeFormItems)
                        .orElse(new ArrayList<>())
                        .stream()
                        .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.EXPRESS_DELIVERY)
                        .findFirst()
                        .map(BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeFormItem::convertToOutpatientFormItem)
                        .ifPresent(item -> {
                            ChargeDeliveryInfoView chargeDeliveryInfoView = new ChargeDeliveryInfoView();
                            BeanUtils.copyProperties(deliveryInfo, chargeDeliveryInfoView);

                            if (deliveryInfo.getDeliveryCompany() != null) {
                                ChargeDeliveryCompanyView chargeDeliveryCompanyVo = new ChargeDeliveryCompanyView();
                                BeanUtils.copyProperties(deliveryInfo.getDeliveryCompany(), chargeDeliveryCompanyVo);
                                chargeDeliveryInfoView.setDeliveryCompany(chargeDeliveryCompanyVo);
                            }
                            OutpatientSheetCalculateRsp.DeliveryItemPriceInfo deliveryItemPriceInfo = new OutpatientSheetCalculateRsp.DeliveryItemPriceInfo();
                            BeanUtils.copyProperties(item, deliveryItemPriceInfo);
                            deliveryItemPriceInfo.setDeliveryInfo(chargeDeliveryInfoView);
                            deliveryItemPriceInfo.setIsSupplementary(YesOrNo.YES);
                            outpatientForm.setDeliveryItemPriceInfo(deliveryItemPriceInfo);
                        });
            }

        } else if (chargeForm.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY ||
                (chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE && chargeForm.getPharmacyType() == GoodsConst.PharmacyType.VIRTUAL_PHARMACY)
        ) {
            //加工费
            Optional.ofNullable(chargeForm.getChargeFormItems())
                    .orElse(new ArrayList<>())
                    .stream()
                    .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.PROCESS)
                    .findFirst()
                    .map(BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeFormItem::convertToOutpatientFormItem)
                    .ifPresent(item -> {
                        OutpatientSheetCalculateRsp.ProcessItemPriceInfo processItemPriceInfo = new OutpatientSheetCalculateRsp.ProcessItemPriceInfo();
                        BeanUtils.copyProperties(item, processItemPriceInfo);
                        processItemPriceInfo.initProcessInfo(JsonUtils.readValue(chargeForm.getUsageInfoJson(), UsageInfo.class));

                        if (org.apache.commons.lang3.StringUtils.isNotEmpty(chargeForm.getMedicineStateScopeId())
                                && Constants.MedicineStateScopeId.PROCESS_USAGE_MAP.keySet().contains(Long.parseLong(chargeForm.getMedicineStateScopeId()))) {
                            processItemPriceInfo.setProcessUsageInfo(Constants.MedicineStateScopeId.PROCESS_USAGE_MAP.getOrDefault(Long.parseLong(chargeForm.getMedicineStateScopeId()), null));
                            processItemPriceInfo.setContactMobile(Optional.ofNullable(chargeForm.getChargeAirPharmacyLogistics()).map(ChargeAirPharmacyLogistics::getDeliveryMobile).orElse(""));
                        }

                        outpatientForm.setProcessItemPriceInfo(processItemPriceInfo);
                    });

            //快递费
            Optional.ofNullable(chargeForm.getChargeFormItems())
                    .orElse(new ArrayList<>())
                    .stream()
                    .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.EXPRESS_DELIVERY)
                    .findFirst()
                    .map(BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeFormItem::convertToOutpatientFormItem)
                    .ifPresent(item -> {
                        OutpatientSheetCalculateRsp.DeliveryItemPriceInfo deliveryItemPriceInfo = new OutpatientSheetCalculateRsp.DeliveryItemPriceInfo();
                        BeanUtils.copyProperties(item, deliveryItemPriceInfo);

                        if (Objects.nonNull(chargeForm.getChargeAirPharmacyLogistics())) {
                            ChargeAirPharmacyLogistics chargeAirPharmacyLogistics = chargeForm.getChargeAirPharmacyLogistics();
                            ChargeDeliveryInfoView chargeDeliveryInfoView = new ChargeDeliveryInfoView();
                            BeanUtils.copyProperties(chargeAirPharmacyLogistics, chargeDeliveryInfoView);
                            if (airPharmacyOrderView != null && airPharmacyOrderView.getLogistics() != null) {
                                chargeDeliveryInfoView.setDeliveryOrderNo(airPharmacyOrderView.getLogistics().getLogisticsNo());
                            }

                            if (chargeAirPharmacyLogistics.getDeliveryCompany() != null) {
                                ChargeDeliveryCompanyView chargeDeliveryCompanyVo = new ChargeDeliveryCompanyView();
                                BeanUtils.copyProperties(chargeAirPharmacyLogistics.getDeliveryCompany(), chargeDeliveryCompanyVo);
                                chargeDeliveryInfoView.setDeliveryCompany(chargeDeliveryCompanyVo);
                            }
                            deliveryItemPriceInfo.setDeliveryInfo(chargeDeliveryInfoView);
                        }
                        outpatientForm.setDeliveryItemPriceInfo(deliveryItemPriceInfo);
                    });

            //辅料费
            Optional.ofNullable(chargeForm.getChargeFormItems())
                    .orElse(new ArrayList<>())
                    .stream()
                    .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.INGREDIENT)
                    .findFirst()
                    .map(BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeFormItem::convertToOutpatientFormItem)
                    .ifPresent(item -> {
                        OutpatientSheetCalculateRsp.IngredientItemPriceInfo ingredientItemPriceInfo = new OutpatientSheetCalculateRsp.IngredientItemPriceInfo();
                        BeanUtils.copyProperties(item, ingredientItemPriceInfo);
                        outpatientForm.setIngredientItemPriceInfo(ingredientItemPriceInfo);
                    });
        }

    }


    private BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeFormItem generateOutpatientFormItemForChargeFormItem(ChargeFormItem chargeFormItem,
                                                                                                                             int chargeSheetStatus,
                                                                                                                             int chargeVersion,
                                                                                                                             int sourceFormType,
                                                                                                                             Map<String, BigDecimal> chargeFormItemOwedFeeIdMap,
                                                                                                                             Map<String, ChargeFormItem> mergedRefundChargeFormItemIdMap,
                                                                                                                             Map<String, BigDecimal> airPharmacyRefundItemIdReceivedFeeMap,
                                                                                                                             Map<String, BigDecimal> airPharmacyRefundItemIdRefundedFeeMap) {


        if (Objects.isNull(chargeFormItem)) {
            return null;
        }

        BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeFormItem formItem = new BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeFormItem();
        formItem.setId(chargeFormItem.getId());
        formItem.setChargeSheetId(chargeFormItem.getChargeSheetId());
        formItem.setChargeFormId(chargeFormItem.getChargeFormId());
        formItem.setStatus(chargeFormItem.getStatus());
        formItem.setUnitCount(chargeFormItem.getUnitCount());
        formItem.setProductType(chargeFormItem.getProductType());
        formItem.setProductSubType(chargeFormItem.getProductSubType());
        formItem.setSourceFormItemId(chargeFormItem.getSourceFormItemId());
        formItem.setTotalPriceRatio(chargeFormItem.getTotalPriceRatio());
        formItem.setName(chargeFormItem.getName());
        formItem.setProductId(chargeFormItem.getProductId());
        formItem.setUnit(chargeFormItem.getUnit());
        formItem.setUseDismounting(chargeFormItem.getUseDismounting());
        formItem.setPharmacyType(chargeFormItem.getPharmacyType());
        formItem.setPharmacyNo(chargeFormItem.getPharmacyNo());
        formItem.setComposeType(chargeFormItem.getComposeType());
        formItem.setFeeComposeType(chargeFormItem.getFeeComposeType());
        formItem.setFeeTypeId(chargeFormItem.getFeeTypeId());
        formItem.setGoodsFeeType(chargeFormItem.getGoodsFeeType());
        formItem.setDoseCount(chargeFormItem.getDoseCount());
        formItem.setUnitPrice(chargeFormItem.getUnitPrice());
        formItem.setFractionPrice(chargeFormItem.getFractionPrice());
        formItem.setPromotionPrice(chargeFormItem.getPromotionPrice());
        formItem.setAdjustmentPrice(chargeFormItem.getAdjustmentPrice());
        formItem.setUnitAdjustmentFee(chargeFormItem.getUnitAdjustmentFee());
        formItem.setUsageInfoJson(chargeFormItem.getUsageInfoJson());
        formItem.setLockId(chargeFormItem.getLockId());
        formItem.setAdditional(chargeFormItem.getAdditional());
        formItem.setUnitAdjustmentFeeLastModifiedBy(Optional.ofNullable(chargeFormItem.getAdditional())
                .map(ChargeFormItemAdditional::getUnitAdjustmentFeeLastModifiedBy)
                .orElse("")
        );
        if (CollectionUtils.isNotEmpty(chargeFormItem.getChargeFormItemBatchInfos())) {
            formItem.setBatchInfos(chargeFormItem.getChargeFormItemBatchInfos()
                    .stream()
                    .filter(chargeFormItemBatchInfo -> chargeFormItemBatchInfo.getIsDeleted() == 0)
                    .map(BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeFormItemBatchInfo::of)
                    .collect(Collectors.toList())
            );
        }

        formItem.setOwedFee(chargeFormItemOwedFeeIdMap.getOrDefault(chargeFormItem.getId(), BigDecimal.ZERO));
        //折扣前价，单项议价后的总价
        formItem.setTotalPrice(MathUtils.wrapBigDecimalAdd(MathUtils.calculateTotalPrice(chargeFormItem.getUnitPrice(), chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount(), 2), chargeFormItem.getFractionPrice()));

        //goods原价
        formItem.setSourceTotalPrice(chargeFormItem.getSourceTotalPrice());
        //实收
        formItem.setReceivedPrice(MathUtils.wrapBigDecimalOrZero(chargeFormItem.getReceivedPrice()));
        //应收
        if (chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.UNCHARGED) {
            formItem.setReceivableFee(chargeFormItem.calculateTotalPrice());
        } else {
            formItem.setReceivableFee(chargeFormItem.calculateDiscountedPrice(chargeVersion));
        }
        ChargeFormItem refundChargeFormItem = mergedRefundChargeFormItemIdMap.get(chargeFormItem.getId());
        if (chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.UNCHARGED) {
            //应付
            formItem.setNeedPayFee(formItem.getReceivableFee());
            formItem.setPrintTotalPrice(chargeFormItem.calculateDiscountedPrice(chargeVersion));
        } else if (chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED) {



            //判断是否有退费记录，如果有退费记录，处理退费的情况

            if (Objects.nonNull(refundChargeFormItem)) {
                BigDecimal totalCount = MathUtils.calculateTotalCount(chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount());
                BigDecimal refundTotalCount = MathUtils.wrapBigDecimalOrZero(refundChargeFormItem.getUnitCount())
                        .multiply(MathUtils.wrapBigDecimalOrZero(refundChargeFormItem.getDoseCount()))
                        //加上抵扣的数量
                        .add(MathUtils.wrapBigDecimalOrZero(refundChargeFormItem.getDeductTotalCount()));

                formItem.setPrintTotalPrice(MathUtils.wrapBigDecimalSubtract(chargeFormItem.calculateDiscountedPrice(chargeVersion), refundChargeFormItem.calculateDiscountedPrice(chargeVersion)));
                //判断是否全退了
                if (totalCount.compareTo(refundTotalCount) <= 0) {
                    formItem.setStatus(Constants.ChargeFormItemStatus.REFUNDED);
                    formItem.setPrintTotalPrice(BigDecimal.ZERO);
                }

                formItem.setRefundTotalPrice(refundChargeFormItem.getTotalPrice().negate());
                formItem.setRefundDiscountedPrice(refundChargeFormItem.calculateDiscountedPrice(chargeVersion).negate());
                formItem.setRefundDoseCount(MathUtils.wrapBigDecimalOrZero(refundChargeFormItem.getDoseCount()));
                formItem.setRefundUnitCount(refundChargeFormItem.getUnitCount());
                //计算实退金额 = 应收-实收
                formItem.setRefundedFee(MathUtils.wrapBigDecimalSubtract(formItem.getReceivableFee(), formItem.getReceivedPrice()).negate());

            } else {
                formItem.setRefundedFee(BigDecimal.ZERO);
                formItem.setPrintTotalPrice(chargeFormItem.calculateDiscountedPrice(chargeVersion));
            }

            if (chargeSheetStatus == Constants.ChargeSheetStatus.PART_CHARGED) {
                formItem.setNeedPayFee(MathUtils.wrapBigDecimalSubtract(formItem.getReceivableFee(), formItem.getReceivedPrice()));
                formItem.setPrintTotalPrice(chargeFormItem.getTotalPrice());
            } else {
                formItem.setNeedPayFee(BigDecimal.ZERO);
            }

        } else if (chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.REFUNDED) {
            formItem.setRefundTotalPrice(chargeFormItem.calculateTotalPrice());
            formItem.setRefundDiscountedPrice(chargeFormItem.calculateDiscountedPrice(chargeVersion).negate());
            formItem.setNeedPayFee(BigDecimal.ZERO);
            if (sourceFormType == Constants.SourceFormType.AIR_PHARMACY) {
                formItem.setReceivedPrice(airPharmacyRefundItemIdReceivedFeeMap.getOrDefault(chargeFormItem.getId(), BigDecimal.ZERO));
                formItem.setRefundedFee(airPharmacyRefundItemIdRefundedFeeMap.getOrDefault(chargeFormItem.getId(), BigDecimal.ZERO));
            }
            formItem.setPrintTotalPrice(BigDecimal.ZERO);
        } else if (chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.UNSELECTED) {
            //退单的情况
            formItem.setReceivedPrice(BigDecimal.ZERO);
            formItem.setReceivableFee(BigDecimal.ZERO);
            formItem.setUnselectedFee(formItem.getTotalPrice().negate());
            formItem.setNeedPayFee(BigDecimal.ZERO);
            formItem.setPrintTotalPrice(BigDecimal.ZERO);
        }

        //展示价格计算，待收和部分收展示原价，其他状态都展示实收
        if (formItem.getStatus() == Constants.ChargeFormItemStatus.UNCHARGED
                || (formItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED && MathUtils.wrapBigDecimalCompare(formItem.getNeedPayFee(), BigDecimal.ZERO) > 0)
        ) {
            //部分收，展示原价
            formItem.setDisplayTotalPrice(formItem.getTotalPrice());
        } else {
            //全收，展示实收
            formItem.setDisplayTotalPrice(formItem.getReceivedPrice());
        }

        //将欠费从实收里面移除掉
        formItem.setReceivedIgnoreOwePrice(MathUtils.max(BigDecimal.ZERO, MathUtils.wrapBigDecimalSubtract(formItem.getReceivedPrice(), formItem.getOwedFee())));

        //处理打印单价
        BigDecimal count = MathUtils.wrapBigDecimalSubtract(MathUtils.wrapBigDecimalMultiply(chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount()),
                MathUtils.wrapBigDecimalMultiply(Optional.ofNullable(chargeFormItem).map(ChargeFormItem::getRefundDoseCount).orElse(BigDecimal.ZERO), Optional.ofNullable(refundChargeFormItem).map(ChargeFormItem::getRefundUnitCount).orElse(BigDecimal.ZERO)));


        int scale = 2;
        if (chargeFormItem.getProductType() == Constants.ProductType.MEDICINE && (chargeFormItem.getProductSubType() == Constants.ProductType.SubType.MEDICINE_CHINESE || chargeFormItem.getProductSubType() == Constants.ProductType.SubType.MEDICINE_WESTERN)) {
            scale = 4;
        }


        if (MathUtils.wrapBigDecimalCompare(count, BigDecimal.ZERO) > 0 ) {
            formItem.setPrintUnitPrice(formItem.getPrintTotalPrice().divide(count, scale, RoundingMode.HALF_UP));
        }
        return formItem;
    }

    private Map<String, BigDecimal> queryChargeFormItemOweFee(List<ChargeSheet> chargeSheets, String clinicId) {
        if (CollectionUtils.isEmpty(chargeSheets)) {
            return new HashMap<>();
        }

        Map<String, ChargeSheet> chargeSheetIdMap = chargeSheets.stream()
                .collect(Collectors.toMap(ChargeSheet::getId, Function.identity()));

        //获取欠费支付方式的transactionIds
        List<String> owePayTransactionIds = chargeSheets.stream()
                .filter(chargeSheet -> chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED && chargeSheet.getOwedStatus() == ChargeSheet.OwedStatus.OWING)
                .flatMap(chargeSheet -> Optional.ofNullable(chargeSheet.getChargeTransactions()).orElse(new ArrayList<>()).stream())
                .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0 && chargeTransaction.getPayMode() == Constants.ChargePayMode.OWE_PAY)
                .map(ChargeTransaction::getId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(owePayTransactionIds)) {
            return new HashMap<>();
        }

        //查询欠费的统计明细
        Map<String, List<ChargeTransactionRecord>> chargeSheetIdTransactionRecordMap = Optional.ofNullable(chargeTransactionRecordService.listByClinicIdAndTransactionIdsAndIsOldRecord(clinicId, owePayTransactionIds, 0))
                .orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.groupingBy(ChargeTransactionRecord::getChargeSheetId));


        if (MapUtils.isEmpty(chargeSheetIdTransactionRecordMap)) {
            return new HashMap<>();
        }

        //查询还款的统计明细
        Map<String, List<ChargeOweCombineTransactionRecordDetail>> oweCombinsTransactionRecordDetailChargeSheetIdMap = Optional.ofNullable(chargeOweCombineTransactionRecordDetailRepository.findAllByClinicIdAndChargeSheetIdInAndIsOldRecordAndIsDeleted(clinicId, new ArrayList<>(chargeSheetIdTransactionRecordMap.keySet()), 0, 0))
                .orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.groupingBy(ChargeOweCombineTransactionRecordDetail::getChargeSheetId));

        return chargeSheetIdTransactionRecordMap.entrySet().stream()
                .map(entry -> {
                    String chargeSheetId = entry.getKey();
                    List<ChargeTransactionRecord> chargeTransactionRecords = entry.getValue();

                    ChargeSheet chargeSheet = chargeSheetIdMap.getOrDefault(chargeSheetId, null);

                    if (Objects.isNull(chargeSheet)) {
                        return null;
                    }

                    List<ChargeOweCombineTransactionRecordDetail> oweCombineTransactionRecordDetails = oweCombinsTransactionRecordDetailChargeSheetIdMap.getOrDefault(chargeSheetId, new ArrayList<>());


                    Map<String, BigDecimal> itemIdRepaymentFeeMap = oweCombineTransactionRecordDetails.stream()
                            .filter(recordDetail -> StringUtils.isNotEmpty(recordDetail.getExpenseItemId()))
                            .collect(Collectors.toMap(ChargeOweCombineTransactionRecordDetail::getExpenseItemId,
                                    recordDetail -> MathUtils.wrapBigDecimalAdd(recordDetail.getTotalPrice(), recordDetail.getDiscountPrice()),
                                    MathUtils::wrapBigDecimalAdd
                            ));

                    Map<String, ChargeTransactionRecord> originalItemIdRecordMap = StatChargeOweRecordProcessor.generateItemOwePayOriginalDataMap(chargeTransactionRecords, ChargeUtils.getChargeSheetItems(chargeSheet), chargeSheet.getTransactionRecordHandleMode());

                    //计算每个item的当前欠费值
                    return originalItemIdRecordMap.entrySet()
                            .stream()
                            .collect(Collectors.toMap(Map.Entry::getKey, recordEntry -> {
                                String chargeFormItemId = recordEntry.getKey();
                                ChargeTransactionRecord record = recordEntry.getValue();
                                BigDecimal totalOwedFee = MathUtils.wrapBigDecimalAdd(record.getTotalPrice(), record.getDiscountPrice());
                                return MathUtils.wrapBigDecimalSubtract(totalOwedFee, itemIdRepaymentFeeMap.getOrDefault(chargeFormItemId, BigDecimal.ZERO));
                            }, (a, b) -> a));
                })
                .flatMap(map -> map.entrySet().stream())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (a, b) -> a));
    }

    private ChargeSheet calculateChargeSheetForOutpatient(ChargeSheet unchargedChargeSheet, ChargeSheet registrationChargeSheet) {

        if (Objects.isNull(unchargedChargeSheet)) {
            registrationChargeSheet = calculateChargeSheet(registrationChargeSheet);
            return null;
        }

        //将挂号费移到门诊收费单中进行算费
        if (registrationChargeSheet != null && registrationChargeSheet.getType() == ChargeSheet.Type.REGISTRATION && registrationChargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED) {
            ChargeService.moveRegistrationChargeSheetToOutpatientChargeSheet(registrationChargeSheet, unchargedChargeSheet);
            registrationChargeSheet.setIsDeleted(1);
            unchargedChargeSheet.setRegistrationChargeSheetId(null);
        }
        return calculateChargeSheet(unchargedChargeSheet);
    }

    private ChargeSheet mergeRegistrationSheetAndOutpatientSheet(ChargeSheet unchargedChargeSheet, ChargeSheet registrationChargeSheet) {
        if (Objects.isNull(unchargedChargeSheet)) {
            return null;
        }

        //将挂号费移到门诊收费单中进行算费
        if (registrationChargeSheet != null && registrationChargeSheet.getType() == ChargeSheet.Type.REGISTRATION && registrationChargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED) {
            ChargeService.moveRegistrationChargeSheetToOutpatientChargeSheet(registrationChargeSheet, unchargedChargeSheet);
            registrationChargeSheet.setIsDeleted(1);
            unchargedChargeSheet.setRegistrationChargeSheetId(null);
        }

        return unchargedChargeSheet;
    }


    public ChargeSheet calculateChargeSheet(ChargeSheet chargeSheet) {
        if (Objects.isNull(chargeSheet) || chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED) {
            return null;
        }

        SheetProcessor sheetProcessor = new SheetProcessor(chargeSheet);
        sheetProcessor.setSheetProcessorInfoProvider(sheetProcessorService);
        sheetProcessor.setPayMode(Constants.ChargePayMode.NONE);
        sheetProcessor.build();

        return sheetProcessor.calculateAndGenerateChargeSheet();
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public ChargeSheetView outpatientSheetConvertToChargeSheetView(cn.abcyun.bis.rpc.sdk.cis.model.outpatient.OutpatientSheet sdkOutpatientSheet) {

        if (Objects.isNull(sdkOutpatientSheet)) {
            return null;
        }
        OutpatientSheetDetailCalculateReq.RegistrationInfoReq registrationInfo = new OutpatientSheetDetailCalculateReq.RegistrationInfoReq();
        registrationInfo.setUnitPrice(MathUtils.wrapBigDecimalOrZero(sdkOutpatientSheet.getRegistrationFee()));

        //将sdk的outpatient转化为common包的outpatient
        OutpatientSheet outpatientSheet = JsonUtils.readValue(JsonUtils.dump(sdkOutpatientSheet), OutpatientSheet.class);

        if (Objects.isNull(outpatientSheet)) {
            return null;
        }
        List<ChargeSheet> existedChargeSheetList = new ArrayList<>();
        if (StringUtils.isNotEmpty(outpatientSheet.getPatientOrderId())) {
            existedChargeSheetList = Optional.ofNullable(chargeSheetService.findAllByPatientOrderId(outpatientSheet.getPatientOrderId())).orElse(new ArrayList<>());
        }

        //这里用内存中的门诊单时因为下面两个方法会修改门诊单的内容
        OutpatientSheet memoryOutpatientSheet = JsonUtils.readValue(JsonUtils.dump(outpatientSheet), OutpatientSheet.class);
        //新增或修改挂号收费单
        chargeSheetConvertHandler.insertOrUpdateRegistrationChargeSheet(existedChargeSheetList, memoryOutpatientSheet, registrationInfo, "");

        //新增或修改门诊收费单
        OutpatientSheetConvertChargeSheetRsp convertChargeSheetRsp = chargeSheetConvertHandler.insertOrUpdateOutpatientChargeSheetCore(existedChargeSheetList, memoryOutpatientSheet, 0, null, false, "");

        ChargeSheet unchargedChargeSheet = Optional.ofNullable(convertChargeSheetRsp)
                .map(OutpatientSheetConvertChargeSheetRsp::getUnchargedChargeSheet)
                .filter(chargeSheet -> chargeSheet.getIsDeleted() == 0)
                .orElse(null);

        if (unchargedChargeSheet == null) {
            return null;
        }

        SheetProcessor sheetProcessor = new SheetProcessor(unchargedChargeSheet);
        sheetProcessor.setSheetProcessorInfoProvider(sheetProcessorService);
        sheetProcessor.setPayMode(Constants.ChargePayMode.NONE);
        sheetProcessor.build();

        ChargeSheet registrationChargeSheet = Optional.ofNullable(convertChargeSheetRsp).map(OutpatientSheetConvertChargeSheetRsp::getRegistrationChargeSheet).orElse(null);

        //将挂号费移到门诊收费单中进行算费
        if (registrationChargeSheet != null && registrationChargeSheet.getType() == ChargeSheet.Type.REGISTRATION && registrationChargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED) {
            ChargeService.moveRegistrationChargeSheetToOutpatientChargeSheet(registrationChargeSheet, unchargedChargeSheet);
            registrationChargeSheet.setIsDeleted(1);
            unchargedChargeSheet.setRegistrationChargeSheetId(null);
        }

        return sheetProcessor.generateSheetDetail(false, true, Constants.ChargeSource.CHARGE, true);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public MedicalPlanSheetDetailCalculateRsp calculateMedicalPlanSheetDetail(MedicalPlanSheetDetailCalculateReq req) {
        MedicalPlanSheet medicalPlanSheet = req.getMedicalPlanSheet();

        if (Objects.isNull(medicalPlanSheet)) {
            return null;
        }

        String patientOrderId = medicalPlanSheet.getPatientOrderId();

        List<ChargeSheet> existedChargeSheetList = Optional.ofNullable(chargeSheetService.findAllByPatientOrderId(patientOrderId)).orElse(new ArrayList<>());

        //这里用内存中的门诊单时因为下面两个方法会修改门诊单的内容
        MedicalPlanSheet memoryMedicalPlanSheet = JsonUtils.readValue(JsonUtils.dump(medicalPlanSheet), MedicalPlanSheet.class);

        //新增或修改门诊收费单
        MedicalPlanSheetConvertChargeSheetRsp convertChargeSheetRsp = chargeSheetConvertHandler.insertOrUpdateMedicalPlanChargeSheetCore(existedChargeSheetList, memoryMedicalPlanSheet, null, null);

        //删除了也要chargeSheetId
        String unchargedChargeSheetId = Optional.ofNullable(convertChargeSheetRsp)
                .map(MedicalPlanSheetConvertChargeSheetRsp::getUnchargedChargeSheet)
                .map(ChargeSheet::getId)
                .orElse(null);

        ChargeSheet unchargedChargeSheet = Optional.ofNullable(convertChargeSheetRsp)
                .map(MedicalPlanSheetConvertChargeSheetRsp::getUnchargedChargeSheet)
                .filter(chargeSheet -> chargeSheet.getIsDeleted() == 0)
                .orElse(null);

//        unchargedChargeSheet = calculateChargeSheet(unchargedChargeSheet);

        existedChargeSheetList.removeIf(chargeSheet -> chargeSheet.getIsDeleted() == 1);
        if (StringUtils.isNotEmpty(unchargedChargeSheetId)) {
            existedChargeSheetList.removeIf(chargeSheet -> Objects.equals(chargeSheet.getId(), unchargedChargeSheetId));
        }
        if (Objects.nonNull(unchargedChargeSheet)) {
            ChargeSheet finalUnchargedChargeSheet = unchargedChargeSheet;
            existedChargeSheetList.removeIf(chargeSheet -> Objects.equals(chargeSheet.getId(), finalUnchargedChargeSheet.getId()));
            existedChargeSheetList.add(unchargedChargeSheet);
        }


        List<ChargeSheet> availableChargeSheets = existedChargeSheetList.stream()
                .filter(chargeSheet -> chargeSheet.getType() == ChargeSheet.Type.MEDICAL_PLAN)
                .collect(Collectors.toList());

        //计算收费单，得到每个item的各个维度的价格信息
        BusinessSheetCalculateChargeSheetDto businessSheetCalculateChargeSheetDto = businessSheetCalculateChargeSheet(availableChargeSheets,
                medicalPlanSheet.getClinicId(),
                medicalPlanSheet.getPatientOrderId(),
                false,
                false);

        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "businessSheetCalculateChargeSheetDto: {}", JsonUtils.dump(businessSheetCalculateChargeSheetDto));

        List<BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeSheet> chargeSheets = businessSheetCalculateChargeSheetDto.getChargeSheets();

        MedicalPlanSheetDetailCalculateRsp rsp = new MedicalPlanSheetDetailCalculateRsp();

        rsp.setMedicalPlanForms(chargeSheets.stream()
                .flatMap(chargeSheet -> Optional.ofNullable(chargeSheet.getChargeForms()).orElse(new ArrayList<>()).stream())
                .map(chargeForm -> {
                    MedicalPlanSheetDetailCalculateRsp.MedicalPlanForm medicalPlanForm = new MedicalPlanSheetDetailCalculateRsp.MedicalPlanForm();
                    BeanUtils.copyProperties(chargeForm, medicalPlanForm);
                    medicalPlanForm.setId(chargeForm.getSourceFormId());
                    medicalPlanForm.setChargeFormId(chargeForm.getId());
                    medicalPlanForm.setFormItems(Optional.ofNullable(chargeForm.getChargeFormItems()).orElse(new ArrayList<>())
                            .stream()
                            .map(BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeFormItem::convertToMedicalPlanFormItem)
                            .collect(Collectors.toList())
                    );
                    medicalPlanForm.buildAllPrice();
                    return medicalPlanForm;
                })
                .collect(Collectors.toList()));

        // sheet status
        if (chargeSheets.stream().filter(chargeSheet -> chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED || chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_CHARGED).count() > 0) {
            rsp.setStatus(Constants.ChargeSheetStatus.UNCHARGED);
        } else if (chargeSheets.stream().filter(chargeSheet -> chargeSheet.getStatus() == Constants.ChargeSheetStatus.CHARGED || chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_REFUNDED).count() > 0) {
            rsp.setStatus(Constants.ChargeSheetStatus.CHARGED);
        } else {
            rsp.setStatus(Constants.ChargeSheetStatus.REFUNDED);
        }

        rsp.buildAllPrice();
        return rsp;
    }
}
