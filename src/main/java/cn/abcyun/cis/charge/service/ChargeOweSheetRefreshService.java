package cn.abcyun.cis.charge.service;

import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ;
import cn.abcyun.cis.charge.api.model.RefreshDataCommonRsp;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.cold.ChargeFormItemDaoProxy;
import cn.abcyun.cis.charge.combinorder.model.ChargeCombineOrderTransaction;
import cn.abcyun.cis.charge.model.*;
import cn.abcyun.cis.charge.processor.StatChargeOweRecordProcessor;
import cn.abcyun.cis.charge.repository.*;
import cn.abcyun.cis.charge.service.rpc.CisScClinicService;
import cn.abcyun.cis.charge.util.MathUtils;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ChargeOweSheetRefreshService {
    private final ChargeSheetRepository chargeSheetRepository;
    private final ChargeOweSheetRepository chargeOweSheetRepository;
    private final ChargeOweCombineTransactionRecordDetailRepository oweCombineTransactionRecordDetailRepository;
    private final ChargeOweCombineTransactionRecordDetailService oweCombineTransactionRecordDetailService;
    private final ChargeFormItemDaoProxy chargeFormItemRepository;
    private final ChargeTransactionRepository chargeTransactionRepository;
    private final ChargeTransactionRecordService chargeTransactionRecordService;
    private final CisScClinicService scClinicService;

//    private ExecutorService executorService = Executors.newFixedThreadPool(5,
//            new ThreadFactoryBuilder()
//                    .setNameFormat("charge-owe-sheet-refresh-thread-%d")
//                    .build());

    @Autowired
    public ChargeOweSheetRefreshService(ChargeSheetRepository chargeSheetRepository,
                                        ChargeOweSheetRepository chargeOweSheetRepository,
                                        ChargeOweCombineTransactionRecordDetailRepository oweCombineTransactionRecordDetailRepository,
                                        ChargeOweCombineTransactionRecordDetailService oweCombineTransactionRecordDetailService,
                                        ChargeFormItemDaoProxy chargeFormItemRepository,
                                        ChargeTransactionRepository chargeTransactionRepository,
                                        ChargeTransactionRecordService chargeTransactionRecordService,
                                        CisScClinicService scClinicService) {
        this.chargeSheetRepository = chargeSheetRepository;
        this.chargeOweSheetRepository = chargeOweSheetRepository;
        this.oweCombineTransactionRecordDetailRepository = oweCombineTransactionRecordDetailRepository;
        this.oweCombineTransactionRecordDetailService = oweCombineTransactionRecordDetailService;
        this.chargeFormItemRepository = chargeFormItemRepository;
        this.chargeTransactionRepository = chargeTransactionRepository;
        this.chargeTransactionRecordService = chargeTransactionRecordService;
        this.scClinicService = scClinicService;
    }

    public RefreshDataCommonRsp refreshPartedPaidOweSheetRecordDetail(List<String> oweSheetIds) {
        for (String oweSheetId : oweSheetIds) {
            refreshChargeOweSheetRecordDetail(Long.parseLong(oweSheetId));
        }
        return new RefreshDataCommonRsp();
    }

    public int refreshChargeOweSheetRecordDetail(Long chargeOweSheetId) {

        ChargeOweSheet chargeOweSheet = chargeOweSheetRepository.findById(chargeOweSheetId).orElse(null);

        if (Objects.isNull(chargeOweSheet)) {
            log.info("chargeOweSheet is null");
            return 0;
        }

        //查询收费单的记录明细的标记
        List<ChargeSheet> chargeSheets = Optional.ofNullable(chargeSheetRepository.findChargeSheetsTransactionRecordHandleModeByIds(Arrays.asList(chargeOweSheet.getChargeSheetId()))).orElse(new ArrayList<>());

        if (CollectionUtils.isEmpty(chargeSheets)) {
            log.info("chargeSheets is null");
            return 0;
        }

        int transactionRecordHandleMode = chargeSheets.get(0).getTransactionRecordHandleMode();

        int count = oweCombineTransactionRecordDetailRepository.countByClinicIdAndOweSheetIdAndIsDeleted(chargeOweSheet.getClinicId(), String.valueOf(chargeOweSheet.getId()), 0);

        if (count > 0) {
            log.info("已经刷过了，不需要再刷了");
            return 1;
        }


        if (CollectionUtils.isEmpty(chargeOweSheet.getTransactionRecords())) {
            log.info("没有收费记录，不需要刷");
            return 0;
        }

        //查询欠费单对应的收费单当时的欠费记录列表
        List<ChargeTransaction> chargeTransactions = Optional.ofNullable(chargeTransactionRepository.findAllByClinicIdAndChargeSheetIdInAndPayModeAndIsDeleted(chargeOweSheet.getClinicId(), Arrays.asList(chargeOweSheet.getChargeSheetId()), Constants.ChargePayMode.OWE_PAY, 0)).orElse(new ArrayList<>());

        //查询欠费单对应的收费单当时欠费的统计明细
        List<String> chargeTransactionIds = chargeTransactions.stream()
                .map(ChargeTransaction::getId)
                .collect(Collectors.toList());
        List<ChargeTransactionRecord> chargeTransactionRecords = Optional.ofNullable(chargeTransactionRecordService.listByClinicIdAndTransactionIds(chargeOweSheet.getClinicId(), chargeTransactionIds)).orElse(new ArrayList<>());

        List<ChargeOweCombineTransactionRecord> transactionRecords = chargeOweSheet.getTransactionRecords()
                .stream()
                .filter(chargeOweCombineTransactionRecord -> chargeOweCombineTransactionRecord.getType() == ChargeCombineOrderTransaction.Type.PAY)
                .sorted(Comparator.comparing(ChargeOweCombineTransactionRecord::getCreated))
                .collect(Collectors.toList());


        List<ChargeOweCombineTransactionRecordDetail> thisTimeWriteRecordDetails = new ArrayList<>();
        BigDecimal totalPrice = chargeOweSheet.getTotalPrice();

        for (int i = 0; i < transactionRecords.size(); i++) {
            boolean isFirstPay = i == 0;
            int payType;
            if (chargeOweSheet.getStatus() == ChargeOweSheet.Status.PART_CHARGED) {
                payType = StatChargeOweRecordProcessor.PayType.PARTED_PAID;
            } else {
                payType = i == transactionRecords.size() - 1 ? StatChargeOweRecordProcessor.PayType.PAID : StatChargeOweRecordProcessor.PayType.PARTED_PAID;
            }

            ChargeOweCombineTransactionRecord transactionRecord = transactionRecords.get(i);

            //更新transactionRecord的needPay字段
            totalPrice = MathUtils.max(BigDecimal.ZERO, totalPrice.subtract(transactionRecord.getAmount()));
            transactionRecord.setNeedPay(totalPrice);

            thisTimeWriteRecordDetails.addAll(writeChargeOweCombineTransactionRecordDetail(chargeOweSheet,
                    isFirstPay,
                    payType,
                    transactionRecordHandleMode,
                    transactionRecord,
                    chargeTransactionRecords,
                    thisTimeWriteRecordDetails,
                    transactionRecord.getCreatedBy()));
        }

        if (CollectionUtils.isNotEmpty(thisTimeWriteRecordDetails)) {
            oweCombineTransactionRecordDetailRepository.saveAll(thisTimeWriteRecordDetails);
        }

        return 1;
    }

    private List<ChargeOweCombineTransactionRecordDetail> writeChargeOweCombineTransactionRecordDetail(ChargeOweSheet chargeOweSheet,
                                                                                                       boolean isFirstPay,
                                                                                                       int payType,
                                                                                                       int transactionRecordHandleMode,
                                                                                                       ChargeOweCombineTransactionRecord chargeOweCombineTransactionRecord,
                                                                                                       List<ChargeTransactionRecord> chargeTransactionRecords,
                                                                                                       List<ChargeOweCombineTransactionRecordDetail> historyRecordDetails,
                                                                                                       String operatorId) {

        boolean isHospital = Optional.ofNullable(scClinicService.getOrganAlwaysReturn(chargeOweSheet.getClinicId()))
                .map(scOrgan -> Objects.equals(scOrgan.getHisType(), Organ.HisType.CIS_HIS_TYPE_HOSPITAL))
                .orElse(false);

        StatChargeOweRecordProcessor chargeOweRecordProcessor = new StatChargeOweRecordProcessor();
        chargeOweRecordProcessor.setOweCombineTransactionRecordDetailService(oweCombineTransactionRecordDetailService);
        chargeOweRecordProcessor.setChargeFormItemRepository(chargeFormItemRepository);
        chargeOweRecordProcessor.setChargeInfo(chargeOweSheet,
                isFirstPay,
                payType,
                transactionRecordHandleMode,
                isHospital,
                chargeOweCombineTransactionRecord,
                chargeTransactionRecords,
                operatorId);

        List<ChargeOweCombineTransactionRecordDetail> recordDetails = chargeOweRecordProcessor.generateOweCombineTransactionRecordDetails(historyRecordDetails);

        recordDetails.forEach(recordDetail -> recordDetail.setCreated(chargeOweCombineTransactionRecord.getCreated())
                .setLastModified(chargeOweCombineTransactionRecord.getLastModified())
                .setIsOldRecord(chargeOweSheet.getIsOldRecord())
        );
        return recordDetails;
    }


}
