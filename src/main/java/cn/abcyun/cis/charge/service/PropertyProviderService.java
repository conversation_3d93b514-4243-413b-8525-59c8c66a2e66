package cn.abcyun.cis.charge.service;

import cn.abcyun.bis.rpc.sdk.property.base.PropertyInfo;
import cn.abcyun.bis.rpc.sdk.property.base.PropertyKey;
import cn.abcyun.bis.rpc.sdk.property.model.*;
import cn.abcyun.bis.rpc.sdk.property.service.PropertyService;
import cn.abcyun.bis.rpc.sdk.property.service.model.PropertyConfigItem;
import cn.abcyun.cis.charge.processor.provider.PropertyProvider;
import cn.abcyun.cis.charge.service.dto.PrintMedicalDocumentsInfusionAndTreatmentProperty;
import cn.abcyun.cis.charge.util.CaffeineCacheUtils;
import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.common.log.marker.AbcLogMarker;
import com.github.benmanes.caffeine.cache.Cache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class PropertyProviderService implements PropertyProvider {

    @Autowired
    private PropertyService propertyService;
    @Autowired
    private Cache<String, ClinicBasicFeeNameDisplay> clinicIdFeeNameDisplayCache;

    @Override
    public PrintPrescriptionInfusionExecute getPrintPrescriptionInfusionExecute(String clinicId) {

        if (StringUtils.isEmpty(clinicId)) {
            return null;
        }
        return propertyService.getPropertyValueByKey(PropertyKey.PRINT_PRESCRIPTION_INFUSIONEXECUTE, clinicId, PrintPrescriptionInfusionExecute.class);
    }

    @Override
    public CrmPatientFamily.SharedRights getPatientSharedRightsProperty(String chainId) {
        if (StringUtils.isEmpty(chainId)) {
            return null;
        }

        return Optional.ofNullable(propertyService.getPropertyValueByKey(PropertyKey.CRM_PATIENT_FAMILY, chainId, CrmPatientFamily.class))
                .map(CrmPatientFamily::getSharedRights)
                .orElse(null);
    }

    @Override
    public String getRegistrationFeeNameDisplay(String clinicId) {
        ClinicBasicFeeNameDisplay clinicBasicFeeNameDisplay = getFeeNameDisplay(clinicId);
        if (clinicBasicFeeNameDisplay != null && !StringUtils.isEmpty(clinicBasicFeeNameDisplay.getRegistrationFee())) {
            return clinicBasicFeeNameDisplay.getRegistrationFee();
        }
        return "挂号费";
    }

    private ClinicBasicFeeNameDisplay getFeeNameDisplay(String clinicId) {
        return CaffeineCacheUtils.findOneFromCacheOrOtherSource("getPropertyValueByKeyFromCache", clinicIdFeeNameDisplayCache, clinicId,
                (notInCacheIn) -> {
                    log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "未命中内存缓存, notInCacheId: {}", JsonUtils.dump(notInCacheIn));
                    return propertyService.getPropertyValueByKey(PropertyKey.CLINIC_BASIC_FEE_NAME_DISPLAY, clinicId, ClinicBasicFeeNameDisplay.class);
                });
    }

    @Override
    public PrintMedicalDocumentsInfusionAndTreatmentProperty getPrintMedicalDocumentsInfusionAndTreatmentContent(String clinicId) {
        List<PropertyInfo> propertyInfos = new ArrayList<>();
        PropertyInfo infusionPropertyInfo = new PropertyInfo();
        infusionPropertyInfo.setScopeId(clinicId);
        infusionPropertyInfo.setPropertyKey(PropertyKey.PRINT_MEDICAL_DOCUMENTS_INFUSION);
        propertyInfos.add(infusionPropertyInfo);
        PropertyInfo treatmentContentPropertyInfo = new PropertyInfo();
        treatmentContentPropertyInfo.setScopeId(clinicId);
        treatmentContentPropertyInfo.setPropertyKey(PropertyKey.MEDICAL_DOCUMENTS_TREATMENT_CONTENT);
        propertyInfos.add(treatmentContentPropertyInfo);
        List<PropertyConfigItem> propertyConfigItems = null;
        try {
            propertyConfigItems = propertyService.batchGetConfigItems(propertyInfos);
        }catch (Exception e) {
            log.error("propertyService.batchGetConfigItems error: {}", e.getMessage());
        }

        if (CollectionUtils.isEmpty(propertyConfigItems)) {
            return null;
        }

        PrintMedicalDocumentsInfusionAndTreatmentProperty property = new PrintMedicalDocumentsInfusionAndTreatmentProperty();

        propertyConfigItems.forEach(propertyConfigItem -> {

            if (TextUtils.equals(PropertyKey.PRINT_MEDICAL_DOCUMENTS_INFUSION.getKey(), propertyConfigItem.getKey())) {
                property.setInfusion(JsonUtils.readValue(propertyConfigItem.getValue(), PrintMedicalDocumentsInfusion.class));
            }

            if (TextUtils.equals(PropertyKey.MEDICAL_DOCUMENTS_TREATMENT_CONTENT.getKey(), propertyConfigItem.getKey())) {
                property.setTreatmentContent(JsonUtils.readValue(propertyConfigItem.getValue(), MedicalDocumentsTreatmentContent.class));
            }
        });

        return property;
    }

    public AntimicrobialDrugManagementConfig getAntimicrobialDrugManagementConfig(String clinicId) {
        return propertyService.getPropertyValueByKey(PropertyKey.ANTIMICROBIAL_DRUGS_MANAGEMENT, clinicId, AntimicrobialDrugManagementConfig.class);
    }
}
