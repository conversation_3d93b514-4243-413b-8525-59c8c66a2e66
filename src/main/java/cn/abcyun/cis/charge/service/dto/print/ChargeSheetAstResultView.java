package cn.abcyun.cis.charge.service.dto.print;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-10-20 16:05:02
 */
@Data
public class ChargeSheetAstResultView {

    private List<AstResultItem> list;
    @Data
    public static class AstResultItem {
        private String formItemId;
        private String goodsId;
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private Integer ast;
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private JsonNode astResult;
    }
}
