package cn.abcyun.cis.charge.service;

import cn.abcyun.bis.rpc.sdk.cis.model.patient.*;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.controller.api.ChargeFormItemMerger;
import cn.abcyun.cis.charge.model.ChargeFormItemBatchInfo;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.model.ChargeTransaction;
import cn.abcyun.cis.charge.model.ChargeTransactionRecord;
import cn.abcyun.cis.charge.processor.provider.PatientInfoProvider;
import cn.abcyun.cis.charge.service.dto.ChargeSheetExtend;
import cn.abcyun.cis.charge.service.dto.DTOConverter;
import cn.abcyun.cis.charge.service.rpc.CisCrmService;
import cn.abcyun.cis.charge.util.ChargeUtils;
import cn.abcyun.cis.commons.amqp.message.ChargeSheetMessage;
import cn.abcyun.cis.commons.exception.ServiceInternalException;
import cn.abcyun.cis.commons.rpc.crm.PatientInfo;
import cn.abcyun.cis.commons.rpc.patient.MemberInfo;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.common.log.marker.AbcLogMarker;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class PatientService implements PatientInfoProvider {
    private static final Logger sLogger = LoggerFactory.getLogger(PatientService.class);

    @Autowired
    private CrmService crmService;

    @Autowired
    private CisCrmService cisCrmService;

    @Autowired
    private ChargeTransactionRecordService chargeTransactionRecordService;


    @Override
    @Transactional(readOnly = true)
    public PatientInfo findPatientInfoById(String chainId, String clinicId, String patientId, boolean needMember, boolean needPointsInfo) {
        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX,"findPatientInfoById:" + patientId);
        if (TextUtils.isEmpty(patientId) || Constants.ANONYMOUS_PATIENT_ID.equals(patientId)) {
            return null;
        }
        PatientInfo patientInfo = null;
        try {
            patientInfo = crmService.searchPatientInfoByPatientIdFromCrm(chainId, clinicId, patientId, needMember, needPointsInfo);
        } catch (Exception e) {
            sLogger.error("find patientInfo error");
        }
        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX,"findPatientInfoByPatientOrderId:" + patientId + ", patientName:" + (patientInfo != null ? patientInfo.getName() : "NOT FOUND"));
        return patientInfo;
    }

    @Override
    @Retryable(value = {ServiceInternalException.class}, maxAttempts = 2)
    public MemberInfo findMemberInfo(String memberId, String clinicId, String chainId) throws ServiceInternalException {
        PatientMemberInfo patientMemberInfo = cisCrmService.getPatientMemberByPatientId(chainId, memberId);
        if (patientMemberInfo == null) {
            return null;
        }
        return ChargeUtils.convertPatientMemberInfoToMemberInfo(patientMemberInfo);
    }

    @Override
    @Retryable(value = {ServiceInternalException.class}, maxAttempts = 2)
    public PatientRelateMemberRsp getPatientRelateMember(String patientId, String chainId) {
        return cisCrmService.getPatientRelateMember(patientId, chainId);
    }

    @Override
    public PointPromotionRsp fetchPatientPointPromotions(String chainId, String patientId, List<PointPromotionReq.GoodsInfo> goodsInfos) {
        return cisCrmService.fetchPatientPointAvailablePromotion(chainId, patientId, goodsInfos);
    }

    @Override
    public PatientPointsChargeCalculateRsp calculatePatientPointsForCharge(ChargeSheet chargeSheet) {

        ChargeSheetExtend chargeSheetExtend = DTOConverter.convertToChargeSheetExtend(chargeSheet);
        ChargeFormItemMerger.mergeForChargeSheet(chargeSheetExtend);

        List<ChargeTransaction> currentAddedTransactions = Arrays.asList(ChargeUtils.getLastChargeTransaction(chargeSheet.getChargeTransactions()));
        //查询统计明细
        List<ChargeTransactionRecord> allChargeTransactionRecords = chargeTransactionRecordService.listByClinicIdAndTransactionIdsAndIsOldRecord(chargeSheet.getClinicId(), currentAddedTransactions.stream()
                        .map(ChargeTransaction::getId)
                        .collect(Collectors.toList()),
                null);

        Map<String, String> formItemIdAssociateFormItemIdMap = Optional.ofNullable(chargeSheet.getChargeForms()).orElse(Lists.newArrayList())
                .stream()
                .filter(chargeForm -> !org.springframework.util.CollectionUtils.isEmpty(chargeForm.getChargeFormItems()))
                .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                .filter(chargeFormItem -> !StringUtils.isEmpty(chargeFormItem.getAssociateFormItemId()))
                .collect(Collectors.toMap(cn.abcyun.cis.charge.model.ChargeFormItem::getId, cn.abcyun.cis.charge.model.ChargeFormItem::getAssociateFormItemId));

        Map<String, List<ChargeFormItemBatchInfo>> currentRefundBatchInfoMap = new HashMap<>();

        //将chargeSheet转换为chargeSheetMessage
        ChargeSheetMessage chargeSheetMessage = new ChargeSheetMessage();
        ChargeSheetMessage.ChargeSheet messageChargeSheet = ChargeUtils.convertToMessageChargeSheet(chargeSheetExtend, currentAddedTransactions, allChargeTransactionRecords, formItemIdAssociateFormItemIdMap, currentRefundBatchInfoMap);

        int msgType = 0;
        if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_CHARGED) {
            msgType = ChargeSheetMessage.MSG_TYPE_PARTY_CHARGED;
        } else if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.CHARGED) {
            msgType = ChargeSheetMessage.MSG_TYPE_CHARGED;
        } else if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.PART_REFUNDED) {
            msgType = ChargeSheetMessage.MSG_TYPE_PARTY_REFUND;
        } else if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.REFUNDED) {
            msgType = ChargeSheetMessage.MSG_TYPE_REFUNDED;
        }

        chargeSheetMessage.setType(msgType);
        chargeSheetMessage.setOperatorId(Constants.DEFAULT_OPERATORID);
        chargeSheetMessage.setChargeSheet(messageChargeSheet);
        chargeSheetMessage.setTimestamp(Instant.now());

        return cisCrmService.calculatePatientPointsForCharge(chargeSheetMessage);
    }

    public PatientMemberBillDetailVO findMemberCardRechargeTransaction(String chainId, String memberCardRechargeTransactionId) throws ServiceInternalException {
        return cisCrmService.getPatientMemberBillsById(chainId, memberCardRechargeTransactionId);
    }
}
