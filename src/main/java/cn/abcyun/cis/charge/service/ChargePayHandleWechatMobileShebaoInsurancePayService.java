package cn.abcyun.cis.charge.service;

import cn.abcyun.bis.rpc.sdk.cis.model.pay.shebao.PayForShebaoReq;
import cn.abcyun.cis.charge.base.PayModeInfo;
import cn.abcyun.cis.charge.base.InnerPayModes;
import cn.abcyun.cis.charge.processor.ChargePayInfo;
import cn.abcyun.cis.charge.processor.ChargePayResult;
import cn.abcyun.cis.charge.rpc.client.ShebaoClient;
import cn.abcyun.cis.charge.service.rpc.CisScClinicService;
import cn.abcyun.cis.charge.service.rpc.CisShebaoService;
import cn.abcyun.cis.commons.exception.ServiceInternalException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 医保移动支付医保部分，仅支持退款
 */
@Service
@Slf4j
public class ChargePayHandleWechatMobileShebaoInsurancePayService extends ChargeShebaoPayHandleAbstractService {
    @Autowired
    public ChargePayHandleWechatMobileShebaoInsurancePayService(ChargeAbnormalTransactionService chargeAbnormalTransactionService,
                                                                ChargePayTransactionService chargePayTransactionService,
                                                                ChargeSheetService chargeSheetService,
                                                                EmployeeService employeeService,
                                                                PatientService patientService,
                                                                ShebaoClient shebaoClient,
                                                                CisScClinicService scClinicService,
                                                                CisShebaoService cisShebaoService) {
        super(chargeSheetService, chargePayTransactionService, chargeAbnormalTransactionService, employeeService, patientService, shebaoClient, scClinicService, cisShebaoService);
    }

    @Override
    public PayModeInfo getPayModeInfo() {
        return InnerPayModes.getPayModeByName(InnerPayModes.InnerPayModeName.WECHAT_MOBILE_SHEBAO_INSURANCE);
    }

    @Override
    public int getShebaoPayMethod(int paySubMode) {
        return PayForShebaoReq.PayMethod.MOBILEPAY;
    }

    @Override
    public ChargePayResult pay(ChargePayInfo payInfo) {
        log.error("医保移动支付医保部分，不能单独发起支付");
        throw new ServiceInternalException(500, "医保移动支付医保部分不能单独发起支付");
    }

}
