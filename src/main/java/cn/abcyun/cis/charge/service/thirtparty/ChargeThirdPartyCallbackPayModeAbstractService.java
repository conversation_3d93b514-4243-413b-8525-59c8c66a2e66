package cn.abcyun.cis.charge.service.thirtparty;

import cn.abcyun.bis.rpc.sdk.cis.model.pay.PayCallbackRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.pay.PayStatus;
import cn.abcyun.bis.rpc.sdk.cis.model.shebao.NationalYimaPayAliNotifyReqBody;
import cn.abcyun.cis.charge.amqp.MQProducer;
import cn.abcyun.cis.charge.amqp.RocketMqProducer;
import cn.abcyun.cis.charge.api.model.PayCallbackContainPayModeReq;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.model.ChargePayTransaction;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.model.ChargeTransaction;
import cn.abcyun.cis.charge.model.ThirdPartyPayInfo;
import cn.abcyun.cis.charge.processor.ChargePayModeConfigSimple;
import cn.abcyun.cis.charge.processor.SheetProcessorDispatcher;
import cn.abcyun.cis.charge.processor.callback.IChargeThirdPartyPaidBackCallbackProvider;
import cn.abcyun.cis.charge.processor.callback.IChargeThirdPartyPayCallbackProvider;
import cn.abcyun.cis.charge.processor.callback.IChargeThirdPartyRefundCallbackProvider;
import cn.abcyun.cis.charge.repository.ChargePayTransactionRepository;
import cn.abcyun.cis.charge.repository.ChargeTransactionRecordRepository;
import cn.abcyun.cis.charge.service.*;
import cn.abcyun.cis.charge.service.dto.CalculateChargeResult;
import cn.abcyun.cis.charge.service.dto.PayCallBackActionReq;
import cn.abcyun.cis.charge.service.rpc.CisScClinicService;
import cn.abcyun.cis.charge.service.rpc.CisShebaoService;
import cn.abcyun.cis.charge.util.ChargeUtils;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.cis.idgenerator.AbcIdGenerator;
import cn.abcyun.common.log.marker.AbcLogMarker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.weaver.ast.Var;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 回调抽象类，处理回调公共的逻辑，非公共的交给实现类自己实现
 */
@Service
@Slf4j
public abstract class ChargeThirdPartyCallbackPayModeAbstractService implements IChargeThirdPartyPayCallbackProvider, IChargeThirdPartyRefundCallbackProvider, IChargeThirdPartyPaidBackCallbackProvider {

    protected final ChargeSheetService chargeSheetService;

    protected final ChargePayTransactionRepository chargePayTransactionRepository;

    protected final ChargeService chargeService;

    protected final AbcIdGenerator abcIdGenerator;

    protected final SheetProcessorService sheetProcessorService;

    protected final ChargeTransactionRecordRepository chargeTransactionRecordRepository;

    protected final ChargeAbnormalTransactionService chargeAbnormalTransactionService;

    protected final ChargeConfigService chargeConfigService;

    protected final CisScClinicService scClinicService;

    protected final CisShebaoService shebaoService;

    public ChargeThirdPartyCallbackPayModeAbstractService(ChargeSheetService chargeSheetService,
                                                          ChargePayTransactionRepository chargePayTransactionRepository,
                                                          ChargeService chargeService,
                                                          AbcIdGenerator abcIdGenerator,
                                                          SheetProcessorService sheetProcessorService,
                                                          ChargeTransactionRecordRepository chargeTransactionRecordRepository,
                                                          ChargeAbnormalTransactionService chargeAbnormalTransactionService,
                                                          ChargeConfigService chargeConfigService,
                                                          CisScClinicService scClinicService,
                                                          CisShebaoService shebaoService) {
        this.chargeSheetService = chargeSheetService;
        this.chargePayTransactionRepository = chargePayTransactionRepository;
        this.chargeService = chargeService;
        this.abcIdGenerator = abcIdGenerator;
        this.sheetProcessorService = sheetProcessorService;
        this.chargeTransactionRecordRepository = chargeTransactionRecordRepository;
        this.chargeAbnormalTransactionService = chargeAbnormalTransactionService;
        this.chargeConfigService = chargeConfigService;
        this.scClinicService = scClinicService;
        this.shebaoService = shebaoService;
    }

    protected List<PayCallBackActionReq> doGenerateOncePayCallBackReqs(PayCallbackContainPayModeReq payCallbackReq, ChargePayTransaction chargePayTransaction) {
        List<PayCallBackActionReq> payCallBackActionReqs = new ArrayList<>();
        payCallBackActionReqs.add(new PayCallBackActionReq()
                .setChargePayTransaction(chargePayTransaction)
                .setOncePayCallBackReqs(Collections.singletonList(payCallbackReq.generateMainOncePayCallBackReq())));
        return payCallBackActionReqs;
    }

    @Override
    public PayCallbackRsp payCallback(PayCallbackContainPayModeReq payCallbackReq, ChargeSheet chargeSheet, ChargePayTransaction chargePayTransaction) {
        return payCallbackCore(payCallbackReq, chargeSheet, chargePayTransaction);
    }

    protected abstract boolean needSendPaySuccessMessage(PayCallbackContainPayModeReq payCallbackReq);

    private void sendPaySuccessMessage(ChargeSheet chargeSheet, PayCallbackContainPayModeReq payCallbackReq) {
        if (ChargeSheet.Type.ONLINE_CONSULTATION == chargeSheet.getType()
                || ChargeSheet.Type.REGISTRATION == chargeSheet.getType()
                || ChargeSheet.Type.MEMBER_RECHARGE == chargeSheet.getType()) {
            return;
        }
        if (!needSendPaySuccessMessage(payCallbackReq)) {
            return;
        }
        //发送微信支付成功通知
        chargeService.sendTocMessage(chargeSheet, payCallbackReq);
    }


    public PayCallbackRsp payCallbackCore(PayCallbackContainPayModeReq payCallbackReq, ChargeSheet chargeSheet, ChargePayTransaction chargePayTransaction) {
        PayCallbackRsp payCallbackRsp = new PayCallbackRsp();
        payCallbackRsp.setRecordCode(PayCallbackRsp.RecordCode.REJECT);

        ChargeSheet registrationChargeSheet = null;
        if (!TextUtils.isEmpty(chargeSheet.getRegistrationChargeSheetId())) {
            registrationChargeSheet = chargeSheetService.findById(chargeSheet.getRegistrationChargeSheetId());
        }
        if (registrationChargeSheet != null && (registrationChargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED)) {
            ChargeService.moveRegistrationChargeSheetToOutpatientChargeSheet(registrationChargeSheet, chargeSheet);
            registrationChargeSheet.setIsDeleted(1);
            chargeSheet.setRegistrationChargeSheetId(null);
        }

        ChargeSheet memoryChargeSheet = ChargeUtils.deepCopyChargeSheet(chargeSheet);
        // 校验是否可以入账
        if (this.checkPayCallBackCanExecute(memoryChargeSheet, payCallbackReq, chargePayTransaction)) {
            this.doUpdateChargeSheetInfo(chargeSheet, payCallbackReq);
            return payCallbackCoreExecute(chargeSheet, chargePayTransaction, payCallbackReq);
        }

        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "checkPayCallBackCanExecute: false， 不允许入账");
        return payCallbackRsp;
    }

    /**
     * 校验是否可以入账
     */
    protected abstract boolean checkPayCallBackCanExecute(ChargeSheet chargeSheet, PayCallbackContainPayModeReq payCallbackReq, ChargePayTransaction chargePayTransaction);

    /**
     * 生成追溯码
     */
    protected abstract void doUpdateChargeSheetInfo(ChargeSheet chargeSheet, PayCallbackContainPayModeReq payCallbackReq);

    private List<PayCallBackActionReq> generateOncePayCallBackReqs(PayCallbackContainPayModeReq payCallbackReq, ChargePayTransaction chargePayTransaction) {
        List<PayCallBackActionReq> payCallBackActionReqs = doGenerateOncePayCallBackReqs(payCallbackReq, chargePayTransaction);
        // 绑定payActionInfo
        Map<Long, ChargePayModeConfigSimple> chargePayModeConfigSimpleMap = chargeConfigService.getChargePayModeConfigSimpleByChainId(chargePayTransaction.getChainId());
        payCallBackActionReqs.forEach(payCallBackActionReq ->
                payCallBackActionReq.setPayActionInfos(payCallBackActionReq.getOncePayCallBackReqs()
                        .stream()
                        .map(oncePayCallBackReq -> oncePayCallBackReq.generatePayActionInfo(chargePayModeConfigSimpleMap))
                        .collect(Collectors.toList())
                ));
        return payCallBackActionReqs;
    }

    protected void updateChargeSheetPatientId(ChargeSheet chargeSheet, ChargePayTransaction chargePayTransaction, PayCallbackContainPayModeReq payCallbackReq) {

    }

    protected BigDecimal calculateUnChargedSheetNeedPayFee(ChargeSheet chargeSheet, int payMode, int paySource, String operatorId) {
        // 收费单已解锁，算一次费，比较金额是否一致，如果不一致，走退款
        SheetProcessorDispatcher.ISheetProcessor sheetProcessor = SheetProcessorDispatcher.newSheetProcessor(chargeSheet);
        sheetProcessor.setSheetProcessorInfoProvider(sheetProcessorService);
        sheetProcessor.setPayMode(payMode);
        sheetProcessor.setOperatorId(operatorId);
        sheetProcessor.build();
        CalculateChargeResult calculateChargeResult = sheetProcessor.calculateSheetFee(null, null, new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), null, paySource, false, false);
        return calculateChargeResult != null ? calculateChargeResult.getNeedPayFee() : BigDecimal.ZERO;
    }

    @Override
    public PayCallbackRsp refundCallback(PayCallbackContainPayModeReq payCallbackReq, ChargeSheet chargeSheet, ChargePayTransaction chargePayTransaction) {
        chargeService.refundCallback(chargeSheet, payCallbackReq, chargePayTransaction);
        PayCallbackRsp payCallbackRsp = new PayCallbackRsp();
        payCallbackRsp.setChargeSheetId(chargeSheet.getId());
        payCallbackRsp.setStatus(chargeSheet.getStatus());
        payCallbackRsp.setNeedPay(chargeSheet.calculateNeedPay());
        return payCallbackRsp;
    }

    @Override
    public PayCallbackRsp paidBackCallback(PayCallbackContainPayModeReq payCallbackReq, ChargeSheet chargeSheet, ChargePayTransaction chargePayTransaction) {
        PayCallbackRsp payCallbackRsp = new PayCallbackRsp();
        chargeService.paidbackCallback(chargeSheet, payCallbackReq, chargePayTransaction);


        payCallbackRsp.setChargeSheetId(chargeSheet.getId());
        payCallbackRsp.setStatus(chargeSheet.getStatus());
        payCallbackRsp.setNeedPay(chargeSheet.calculateNeedPay());
        return payCallbackRsp;
    }


    public PayCallbackRsp payCallbackCoreExecute(ChargeSheet chargeSheet, ChargePayTransaction chargePayTransaction, PayCallbackContainPayModeReq payCallbackReq) {
        // 构建支付chargeActions
        List<PayCallBackActionReq> payCallBackActionReqs = generateOncePayCallBackReqs(payCallbackReq, chargePayTransaction);
        // 社保支付回调更新匿名患者的id
        updateChargeSheetPatientId(chargeSheet, chargePayTransaction, payCallbackReq);
        // 支付回调入账
        List<ChargeTransaction> chargeTransactions = chargeService.payCallBackExecute(payCallbackReq,
                payCallBackActionReqs,
                chargePayTransaction,
                chargeSheet,
                payCallbackReq.getOperatorId());

        // 需要通知业务收费单异步支付成功
        ChargeTransaction chargeTransaction = chargeTransactions.get(0);
        String thirdPartyTransactionId = Optional.ofNullable(chargeTransaction.getThirdPartyPayInfo())
                .filter(payInfo -> !StringUtils.isEmpty(payInfo.getThirdPartyTransactionId()))
                .map(ThirdPartyPayInfo::getThirdPartyTransactionId).orElse(null);
        chargeService.sendChargeSheetPaidSuccessNeedInformBusinessMessage(chargeSheet,
                payCallbackReq.getPayMode(),
                payCallbackReq.getPaySubMode(),
                payCallbackReq.calculateReceivedFee(),
                chargeTransaction.getId(),
                thirdPartyTransactionId,
                payCallbackReq.getOperatorId());

        try {
            if (payCallbackReq.getPayMode() == Constants.ChargePayMode.ABC_PAY && payCallbackReq.getPaySubMode() == Constants.ChargePaySubMode.ABC_PAY_SCAN_ONE_QR_CODE) {
                //如果是ABC 当面一码付，则需要通知社保支付完成
                MQProducer.doAfterTransactionCommit(() -> {
                    shebaoService.nationalYimaPayAliNotify(chargeSheet.getChainId(), chargeSheet.getClinicId(), chargeSheet.getId(), payCallbackReq.getOperatorId(),
                            chargeSheet.getReceivableFee(), chargeTransaction.getThirdPartyPayInfo().getChannelTransactionId(), chargeTransaction.getAmount(),
                            NationalYimaPayAliNotifyReqBody.BizType.OUTPATIENT_PAYMENT, Instant.now(), NationalYimaPayAliNotifyReqBody.PaymentPlace.CASHIER);
                });
            }

        } catch (Exception e) {
            log.error("通知社保支付完成失败", e);
        }


        // 发送支付成功消息
        sendPaySuccessMessage(chargeSheet, payCallbackReq);
        PayCallbackRsp payCallbackRsp = new PayCallbackRsp();
        payCallbackRsp.setChargeSheetId(chargeSheet.getId());
        payCallbackRsp.setStatus(chargeSheet.getStatus());
        payCallbackRsp.setNeedPay(chargeSheet.calculateNeedPay());
        return payCallbackRsp;
    }

}