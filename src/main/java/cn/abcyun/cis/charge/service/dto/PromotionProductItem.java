package cn.abcyun.cis.charge.service.dto;

import cn.abcyun.cis.charge.util.MathUtils;

import java.math.BigDecimal;

public class PromotionProductItem {
    private String id;
    private String name;
    private BigDecimal discountPrice;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BigDecimal getDiscountPrice() {
        return MathUtils.wrapBigDecimalOrZero(discountPrice);
    }

    public void setDiscountPrice(BigDecimal discountPrice) {
        this.discountPrice = discountPrice;
    }
}
