package cn.abcyun.cis.charge.service.dto;

import cn.abcyun.cis.charge.model.ThirdPartyPayInfo;
import cn.abcyun.cis.charge.util.ChargePayModeUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

@Data
@Accessors(chain = true)
public class ChargeTransactionView {
    private String id;
    private String thirdPartyPayTransactionId;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String thirdPartyPayCardId;
    private ThirdPartyPayInfo thirdPartyPayInfo;
    private int payMode;
    private int payModeType;
    private String payModeName;
    private int paySubMode;
    private String paySubModeName;
    private String payModeDisplayName;

    private BigDecimal amount;

    private BigDecimal refundedAmount;

    private BigDecimal presentAmount;   //赠金支付金额

    private BigDecimal principalAmount; //本金支付金额

    private BigDecimal income;

    private BigDecimal change;

    private BigDecimal needPay;

    private int changePayMode;

    private String changePayModeName;

    private int paySource;

    private int isPaidback;

    private int isRefunded;

    private Instant created;

    private int chargeType;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<ChargeTransactionRecordView> chargeTransactionRecords;

    public String getPayModeDisplayName() {
        return ChargePayModeUtils.convertPayModeDisplayNameForTransaction(payMode, paySubMode, payModeName, paySubModeName);
    }
}
