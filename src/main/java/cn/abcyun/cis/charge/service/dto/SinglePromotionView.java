package cn.abcyun.cis.charge.service.dto;

import cn.abcyun.bis.rpc.sdk.cis.model.promotion.Promotion;
import cn.abcyun.cis.charge.processor.discount.DiscountRulePromotionGoodsCalculator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SinglePromotionView implements Serializable {

    @ApiModelProperty("营销折扣父id")
    private String parentId;

    @ApiModelProperty("营销折扣id")
    private String id;
    @ApiModelProperty("营销名称id")
    private String name;

//    @ApiModelProperty("营销规则名称")
//    private String ruleName;

    @ApiModelProperty("营销规则详情")
    private DiscountRulePromotionGoodsCalculator.PromotionGoodsCalculateResult.HitRuleDetail hitRuleDetail;

    @ApiModelProperty("是否选中")
    private boolean checked;

    @ApiModelProperty("期望是否选中")
    private Boolean expectedChecked;

    @ApiModelProperty("折扣金额，负值")
    private BigDecimal discountPrice;

    private BigDecimal displayDiscountPrice;

    @ApiModelProperty("参与折扣的数量")
    private BigDecimal participationDiscountCount;

    /**
     * 剩余限购数量
     * null  为不限购
     */
    private BigDecimal leftSaleCount;

    @ApiModelProperty("折扣类型")
    private Promotion.PromotionDetail.PromotionGoods promotionGoods;

    /**
     * 赠送的goods
     */
    @ApiModelProperty("赠送的商品信息")
    @JsonIgnore
    private transient List<DiscountRulePromotionGoodsCalculator.PromotionGiftGoodsResult> giftGoodsList;


    @ApiModelProperty("赠送的优惠券信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Promotion.PromotionDetail.Coupon coupon;

    @ApiModelProperty("营销类型")
    private int type;

    @ApiModelProperty("营销父类型")
    private Integer parentType;

    @ApiModelProperty("会员信息")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Promotion.PromotionDetail.MemberInfo memberInfo;

    public boolean getChecked() {
        return checked;
    }

}
