package cn.abcyun.cis.charge.service.rpc;

import cn.abcyun.bis.rpc.sdk.cis.client.AbcCisScGoodsFeignClient;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.*;
import cn.abcyun.cis.charge.base.GoodsFeeTypeConstants;
import cn.abcyun.cis.charge.processor.provider.GoodsScProvider;
import cn.abcyun.cis.commons.exception.FeignRuntimeException;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.exception.ServiceInternalException;
import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.cis.core.util.FeignClientRpcTemplate;
import cn.abcyun.common.log.marker.AbcLogMarker;
import cn.abcyun.common.model.AbcListPage;
import com.google.common.collect.Lists;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixProperty;
import feign.RetryableException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CisScGoodsService implements GoodsScProvider {

    @Autowired
    private AbcCisScGoodsFeignClient client;

    /**
     * 已不再使用，查询goods接口统一迁移到 {@link CisScGoodsService#queryGoodsInPharmacyByIds(String, String, boolean, int, List)}
     *
     * @param clinicId
     * @param ids
     * @param isWithStock
     * @param notQuerySheBaoInfo
     * @return
     */
    @Deprecated
    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    @Override
    public List<GoodsItem> fetchGoodsItems(String clinicId, List<String> ids, boolean isWithStock, int notQuerySheBaoInfo) {
        List<GoodsItem> goodsItems = new ArrayList<>();
        if (ids == null || ids.size() == 0) {
            return goodsItems;
        }

        QueryGoodsByIdsReq goodsListReq = new QueryGoodsByIdsReq();
        goodsListReq.setClinicId(clinicId);
        goodsListReq.setGoodsIds(ids);
        goodsListReq.setWithStock(isWithStock);
        goodsListReq.setWithDeleted(1);
        goodsListReq.setNotQuerySheBaoInfo(notQuerySheBaoInfo);

        QueryGoodsByIdsRsp rsp = FeignClientRpcTemplate.dealRpcClientMethod("fetchGoodsItems", true, () -> client.queryGoodsByIds(goodsListReq), goodsItems);
        return Optional.ofNullable(rsp)
                .map(QueryGoodsByIdsRsp::getList)
                .orElse(new ArrayList<>())
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    @Override
    public List<GoodsItem> findChineseMedicineGoodsListByNames(List<QueryChineseMedicinesInPharmacyByCadnsReq.QueryCadnsGoodsItems> cadns, String spec, String clinicId, String chainId) {
        List<GoodsItem> goodsItems = new ArrayList<>();
        if (CollectionUtils.isEmpty(cadns) || StringUtils.isEmpty(spec) || StringUtils.isEmpty(clinicId)) {
            return goodsItems;
        }
        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "findChineseMedicineGoodsListByNames cadns:{}, spec: {}", JsonUtils.dump(cadns), spec);

        QueryChineseMedicinesInPharmacyByCadnsReq.QueryPharmacyGoodsReq queryPharmacyGoodsReq = new QueryChineseMedicinesInPharmacyByCadnsReq.QueryPharmacyGoodsReq();
        queryPharmacyGoodsReq.setPharmacyNo(0);
        queryPharmacyGoodsReq.setCMSpec(spec);
        queryPharmacyGoodsReq.setList(cadns);

        List<QueryChineseMedicinesInPharmacyByCadnsRsp.QueryPharmacyGoodsRsp> queryPharmacyGoodsRsps = queryChineseMedicinesInPharmacyByMedicineCadns(chainId, clinicId, 0, Arrays.asList(queryPharmacyGoodsReq));

        return Optional.ofNullable(queryPharmacyGoodsRsps).orElse(new ArrayList<>())
                .stream()
                .filter(queryPharmacyGoodsRsp -> queryPharmacyGoodsRsp.getPharmacyNo() == 0)
                .filter(queryPharmacyGoodsRsp -> CollectionUtils.isNotEmpty(queryPharmacyGoodsRsp.getGoodsList()))
                .findFirst()
                .map(QueryChineseMedicinesInPharmacyByCadnsRsp.QueryPharmacyGoodsRsp::getGoodsList)
                .orElse(new ArrayList<>())
                .stream()
                .map(QueryChineseMedicinesInPharmacyByCadnsRsp.QueryChineseMedcineResultItem::getGoods)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public List<QueryVendorClinicGoodsRsp.VendorGoodsList> queryVendorClinicGoods(String clinicId, List<QueryVendorClinicGoodsReq.VendorGoodsIds> vendorGoodsIds, boolean isWithStock) throws ServiceInternalException {
        List<QueryVendorClinicGoodsRsp.VendorGoodsList> vendorGoodsList = new ArrayList<>();
        if (StringUtils.isEmpty(clinicId) || CollectionUtils.isEmpty(vendorGoodsIds)) {
            return vendorGoodsList;
        }

        QueryVendorClinicGoodsReq req = new QueryVendorClinicGoodsReq();
        req.setClinicId(clinicId);
        req.setList(vendorGoodsIds);
        req.setWithStock(isWithStock ? 1 : 0);

        QueryVendorClinicGoodsRsp rsp = FeignClientRpcTemplate.dealRpcClientMethod("queryVendorClinicGoods", true, () -> client.queryVendorClinicGoods(req), req);

        return Optional.ofNullable(rsp).map(QueryVendorClinicGoodsRsp::getList).orElse(new ArrayList<>());
    }

    /**
     * 根据不同药房查询goods信息
     *
     * @param clinicId
     * @param chainId
     * @param isWithStock
     * @param notQuerySheBaoInfo
     * @param pharmacyGoodsList
     * @return
     */
    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    @Override
    public List<QueryGoodsInPharmacyByIdsRsp.QueryPharmacyGoodsRsp> queryGoodsInPharmacyByIds(String clinicId, String chainId, boolean isWithStock, int notQuerySheBaoInfo, List<QueryGoodsInPharmacyByIdsReq.QueryPharmacyGoodsReq> pharmacyGoodsList) {
        if (CollectionUtils.isEmpty(pharmacyGoodsList) || org.apache.commons.lang3.StringUtils.isAnyEmpty(clinicId, chainId)) {
            return new ArrayList<>();
        }

        QueryGoodsInPharmacyByIdsReq req = new QueryGoodsInPharmacyByIdsReq();
        req.setClinicId(clinicId);
        req.setChainId(chainId);
        req.setWithStock(isWithStock);
        req.setWithDeleted(1);
        req.setNotQuerySheBaoInfo(notQuerySheBaoInfo);
        req.setLoadRepoPrice(1);
        req.setQueryPharmacyGoodsList(pharmacyGoodsList);

        QueryGoodsInPharmacyByIdsRsp rsp = FeignClientRpcTemplate.dealRpcClientMethod("queryGoodsInPharmacyByIds", true,
                () -> client.queryGoodsInPharmacyByIds(req), req);

        return Optional.ofNullable(rsp).map(QueryGoodsInPharmacyByIdsRsp::getList).orElse(new ArrayList<>());
    }

    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    @Override
    public List<QueryGoodsInPharmacyByIdsRsp.QueryPharmacyGoodsRsp> queryGoodsInPharmacyByIdsAndStockCount(String clinicId, String chainId, boolean isWithStock, int notQuerySheBaoInfo, int withShebaoPayLimitPrice, List<QueryGoodsInPharmacyByIdsAndStockCountReq.QueryPharmacyGoodsReq> pharmacyGoodsList) {
        if (CollectionUtils.isEmpty(pharmacyGoodsList) || org.apache.commons.lang3.StringUtils.isAnyEmpty(clinicId, chainId)) {
            return new ArrayList<>();
        }

        QueryGoodsInPharmacyByIdsAndStockCountReq req = new QueryGoodsInPharmacyByIdsAndStockCountReq();
        req.setClinicId(clinicId);
        req.setChainId(chainId);
        req.setWithStock(isWithStock);
        req.setWithDeleted(1);
        req.setLoadRepoPrice(1);
        req.setWithShebaoPayLimitPrice(withShebaoPayLimitPrice);
        req.setNotQuerySheBaoInfo(notQuerySheBaoInfo);
        req.setQueryPharmacyGoodsList(pharmacyGoodsList);

        QueryGoodsInPharmacyByIdsRsp rsp = FeignClientRpcTemplate.dealRpcClientMethod("queryGoodsInPharmacyByIdsAndStockCount", true,
                () -> client.queryGoodsInPharmacyByIdsAndStockCount(req), req);

        return Optional.ofNullable(rsp).map(QueryGoodsInPharmacyByIdsRsp::getList).orElse(new ArrayList<>());
    }

    @Override
    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public Map<String, GetDispenseGoodsStockInfoRsp.DispensingGoodsStockItem> getDispensingGoodsStockItemMap(GetDispenseGoodsStockInfoReq req) {

        if (req == null) {
            return new HashMap<>();
        }

        GetDispenseGoodsStockInfoRsp rsp = FeignClientRpcTemplate.dealRpcClientMethod("getDispensingGoodsStockItemMap", true, () -> client.getDispenseGoodsStockInfo(req), req);
        return Optional.ofNullable(rsp).map(GetDispenseGoodsStockInfoRsp::getList)
                .orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.toMap(GetDispenseGoodsStockInfoRsp.DispensingGoodsStockItem::getDispenseItemId, Function.identity(), (a, b) -> a));
    }

    public static List<QueryGoodsInPharmacyByIdsReq.QueryPharmacyGoodsReq> generateLocalPharmacyQueryPharmacyGoodsReq(List<String> goodsIds) {

        if (CollectionUtils.isEmpty(goodsIds)) {
            return new ArrayList<>();
        }

        return new ArrayList<QueryGoodsInPharmacyByIdsReq.QueryPharmacyGoodsReq>() {{
            QueryGoodsInPharmacyByIdsReq.QueryPharmacyGoodsReq pharmacyGoodsReq = new QueryGoodsInPharmacyByIdsReq.QueryPharmacyGoodsReq();
            pharmacyGoodsReq.setPharmacyNo(0);
            pharmacyGoodsReq.setGoodsIds(new ArrayList<>(goodsIds));
            add(pharmacyGoodsReq);
        }};
    }

    /**
     * 查询诊所的药房列表
     *
     * @param chainId
     * @param clinicId
     * @return
     */
    @Override
    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public List<GoodsPharmacyView> findPharmacyByClinic(String chainId, String clinicId) {
        AbcListPage<GoodsPharmacyView> goodsPharmacyViewListPage = FeignClientRpcTemplate.dealRpcClientMethod("findPharmacyByClinic",
                () -> client.findPharmacyByClinic(chainId, clinicId),
                chainId, clinicId);
        return Optional.ofNullable(goodsPharmacyViewListPage).map(AbcListPage::getRows).orElse(new ArrayList<>());
    }

    /**
     * 匹配默认药房
     *
     * @param chainId
     * @param clinicId
     * @return
     */
    @Override
    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public List<QueryGoodsRulePharmacyRsp> queryGoodsRulePharmacy(String chainId, String clinicId, List<GoodsRulePharmacyItem> goodsRulePharmacyItems) {
        if (CollectionUtils.isEmpty(goodsRulePharmacyItems)) {
            return new ArrayList<>();
        }

        QueryGoodsRulePharmacyReq req = new QueryGoodsRulePharmacyReq();
        req.setChainId(chainId);
        req.setClinicId(clinicId);
        req.setRuleItemList(goodsRulePharmacyItems);
        AbcListPage<QueryGoodsRulePharmacyRsp> goodsPharmacyViewListPage = FeignClientRpcTemplate.dealRpcClientMethod("queryGoodsRulePharmacy",
                () -> client.queryGoodsRulePharmacy(req), req);
        return Optional.ofNullable(goodsPharmacyViewListPage).map(AbcListPage::getRows).orElse(new ArrayList<>());
    }


    /**
     * 查询诊所的药房列表
     *
     * @param chainId
     * @param clinicId
     * @return
     */
    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public List<QueryChineseMedicinesInPharmacyByCadnsRsp.QueryPharmacyGoodsRsp> queryChineseMedicinesInPharmacyByMedicineCadns(String chainId, String clinicId, int withDisable, List<QueryChineseMedicinesInPharmacyByCadnsReq.QueryPharmacyGoodsReq> pharmacyGoodsList) {
        if (CollectionUtils.isEmpty(pharmacyGoodsList) || StringUtils.isAnyEmpty(clinicId, chainId)) {
            return new ArrayList<>();
        }
        QueryChineseMedicinesInPharmacyByCadnsReq req = new QueryChineseMedicinesInPharmacyByCadnsReq();
        req.setChainId(chainId);
        req.setClinicId(clinicId);
        req.setWithStock(false);
        req.setWithDeleted(0);
        req.setDisable(withDisable);
        req.setNotQuerySheBaoInfo(0);
        req.setWithShebaoPayLimitPrice(1);
        req.setQueryPharmacyGoodsList(pharmacyGoodsList);

        QueryChineseMedicinesInPharmacyByCadnsRsp rsp = FeignClientRpcTemplate.dealRpcClientMethod("findPharmacyByClinic",
                () -> client.queryChineseMedicinesInPharmacyByMedicineCadns(req), req);

        return Optional.ofNullable(rsp).map(QueryChineseMedicinesInPharmacyByCadnsRsp::getList).orElse(new ArrayList<>());
    }


    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public boolean isOpenVirtualPharmacy(String chainId, String clinicId) {
        if (StringUtils.isAnyEmpty(chainId, clinicId)) {
            return false;
        }
        GoodsPharmacyOpenRsp goodsPharmacyOpenRsp = FeignClientRpcTemplate.dealRpcClientMethod("checkPharmacyOpen",
                () -> client.checkPharmacyOpen(chainId, clinicId),
                chainId, clinicId);
        return Optional.ofNullable(goodsPharmacyOpenRsp).map(GoodsPharmacyOpenRsp::getIsVirtualPharmacyOpen).orElse(0) == 1;
    }

    /**
     * 获取供应商列表
     *
     * @param chainId      连锁id
     * @param pharmacyType 可选 {@link GoodsConst.PharmacyType} 拉取指定药房类型下的供应商,不填为拉取整个连锁下供应商
     * @return List<GoodsSupplierView>
     */
    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public List<GoodsSupplierView> getGoodsSuppliers(String chainId, String clinicId, Integer pharmacyType) {
        if (StringUtils.isEmpty(chainId)) {
            return new ArrayList<>();
        }

        AbcListPage<GoodsSupplierView> rps = FeignClientRpcTemplate.dealRpcClientMethod("getGoodsSuppliers",
                () -> client.getGoodsSuppliers(chainId, clinicId, pharmacyType, 0, 1, null),
                chainId, pharmacyType);

        if (Objects.isNull(rps) || CollectionUtils.isEmpty(rps.getRows())) {
            return new ArrayList<>();
        }

        return rps.getRows();
    }

    /**
     * 返回Goods 单位
     *
     * @param goodsType {@link GoodsConst.GoodsType}
     */
    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public List<GoodsCustomUnitRsp.ClientCustomUnitItem> getGoodsSysUnit(String chainId,
                                                                         Integer goodsType) {
        if (StringUtils.isBlank(chainId)) {
            throw new ParamRequiredException("连锁id");
        }

        return Optional.ofNullable(
                        FeignClientRpcTemplate.dealRpcClientMethod("getGoodsCustomUnit",
                                () -> client.getGoodsCustomUnit(chainId, goodsType, 1),
                                chainId, goodsType, 1)
                ).map(GoodsCustomUnitRsp::getSysUnitList)
                .orElse(Lists.newArrayList());
    }

    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public List<GoodsSystemType> getSystemTypes() {
        Integer isLeaf = null;
        AbcListPage<GoodsSystemType> goodsSystemTypeList = FeignClientRpcTemplate.dealRpcClientMethod("getSystemTypes",
                () -> client.getSystemTypes(isLeaf),
                isLeaf);

        return Optional.ofNullable(goodsSystemTypeList)
                .map(AbcListPage::getRows)
                .orElse(new ArrayList<>());
    }


    @Override
    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public List<GoodsFeeTypeView> getCustomFeeTypeList(String chainId) {
        if (StringUtils.isEmpty(chainId)) {
            return new ArrayList<>();
        }

        AbcListPage<GoodsFeeTypeView> rsp = cn.abcyun.cis.core.util.FeignClientRpcTemplate.dealRpcClientMethod("getFeeTypeList",
                () -> client.getFeeTypeList(chainId, null, 1), chainId);

        return Optional.ofNullable(rsp).map(AbcListPage::getRows)
                .orElse(new ArrayList<>());
    }

    @Override
    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public Map<Long, GoodsFeeTypeConstants.GoodsFeeTypeSortDto> queryGoodsFeeTypeNames(String chainId, Set<Long> feeTypeIds) {
        if (CollectionUtils.isEmpty(feeTypeIds)) {
            return new HashMap<>();
        }
        Map<Long, GoodsFeeTypeConstants.GoodsFeeTypeSortDto> result = new HashMap<>(GoodsFeeTypeConstants.SYSTEM_GOODS_FEE_TYPE_MAP);

        List<String> customFeeTypeIds = feeTypeIds.stream()
                .filter(feeTypeId -> Objects.isNull(result.get(feeTypeId)))
                .map(String::valueOf)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(customFeeTypeIds)) {
            return result;
        }

        Optional.ofNullable(getCustomFeeTypeList(chainId))
                .orElse(new ArrayList<>())
                .stream()
                .filter(customFeeType -> StringUtils.isNotEmpty(customFeeType.getFeeTypeId()))
                .forEach(customFeeType -> {
                    long feeTypeId = Long.parseLong(customFeeType.getFeeTypeId());
                    result.put(feeTypeId,
                            new GoodsFeeTypeConstants.GoodsFeeTypeSortDto(feeTypeId,
                                    customFeeType.getName(), feeTypeId));
                });

        return result;
    }

    @Override
    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public List<BatchesGoodsStockItem> getGoodsStockBatches(String clinicId, List<GetGoodsStockBatchesReq.GetGoodsStockBatch> goodsStockBatchesReq) {

        if (StringUtils.isEmpty(clinicId) || CollectionUtils.isEmpty(goodsStockBatchesReq)) {
            return new ArrayList<>();
        }

        GetGoodsStockBatchesReq req = new GetGoodsStockBatchesReq();
        req.setClinicId(clinicId);
        req.setGoodsList(goodsStockBatchesReq);
        GetGoodsStockBatchesRsp rsp = FeignClientRpcTemplate.dealRpcClientMethod("getGoodsStockBatches", () -> client.getGoodsStockBatches(req), req);

        return Optional.ofNullable(rsp).map(GetGoodsStockBatchesRsp::getList).orElse(new ArrayList<>());
    }

    /**
     * 使用追溯码
     *
     * @param req
     * @return
     */
    public List<GoodsUseTraceCodeRsp.GoodsUseTraceCodeItem> traceCodeUse(GoodsUseTraceCodeReq req) {
        if (req == null || CollectionUtils.isEmpty(req.getList())) {
            return null;
        }
        try {
            GoodsUseTraceCodeRsp rsp = FeignClientRpcTemplate.dealRpcClientMethod("tracecodeUseLog", true, () -> client.tracecodeUseLog(req), req);
            if (rsp != null && rsp.getList() != null) {
                return rsp.getList();
            }
        } catch (Exception exp) {

        }
        return null;
    }

    @HystrixCommand(fallbackMethod = "traceCodeUseFallback",
            commandProperties = {
                    @HystrixProperty(name = "execution.isolation.thread.timeoutInMilliseconds", value = "3000"),
                    @HystrixProperty(name = "execution.isolation.strategy", value = "THREAD")
            },
            ignoreExceptions = {FeignRuntimeException.class}
    )
    public List<GoodsUseTraceCodeRsp.GoodsUseTraceCodeItem> traceCodeUseWithHystrix(GoodsUseTraceCodeReq req) {
        return traceCodeUse(req);
    }

    public List<GoodsUseTraceCodeRsp.GoodsUseTraceCodeItem> traceCodeUseFallback(GoodsUseTraceCodeReq req) {
        log.error("traceCodeUseFallback, req: {}", JsonUtils.dump(req));
        throw new ServiceInternalException("traceCodeUseFallback");
    }

}
