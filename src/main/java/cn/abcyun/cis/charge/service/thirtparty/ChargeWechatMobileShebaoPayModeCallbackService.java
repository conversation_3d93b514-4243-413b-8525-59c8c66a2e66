package cn.abcyun.cis.charge.service.thirtparty;

import cn.abcyun.cis.charge.api.model.PayCallbackContainPayModeReq;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.base.InnerPayModes;
import cn.abcyun.cis.charge.model.ChargePayTransaction;
import cn.abcyun.cis.charge.model.ThirdPartyPayInfo;
import cn.abcyun.cis.charge.repository.ChargePayTransactionRepository;
import cn.abcyun.cis.charge.repository.ChargeTransactionRecordRepository;
import cn.abcyun.cis.charge.service.*;
import cn.abcyun.cis.charge.service.dto.OncePayCallBackReq;
import cn.abcyun.cis.charge.service.dto.PayCallBackActionReq;
import cn.abcyun.cis.charge.service.rpc.CisScClinicService;
import cn.abcyun.cis.charge.service.rpc.CisShebaoService;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.cis.commons.exception.ParamNotValidException;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.rpc.pay.PayCallbackReq;
import cn.abcyun.cis.commons.rpc.pay.PayStatus;
import cn.abcyun.cis.idgenerator.AbcIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 医保移动支付
 */
@Service
@Slf4j
public class ChargeWechatMobileShebaoPayModeCallbackService extends ChargeShebaoPayModeCallbackService {

    @Autowired
    public ChargeWechatMobileShebaoPayModeCallbackService(ChargeSheetService chargeSheetService,
                                                          ChargePayTransactionRepository chargePayTransactionRepository,
                                                          ChargeService chargeService,
                                                          AbcIdGenerator abcIdGenerator,
                                                          SheetProcessorService sheetProcessorService,
                                                          ChargeTransactionRecordRepository chargeTransactionRecordRepository,
                                                          ChargeAbnormalTransactionService chargeAbnormalTransactionService,
                                                          ChargeConfigService chargeConfigService,
                                                          CisScClinicService scClinicService,
                                                          CisShebaoService shebaoService) {
        super(chargeSheetService, chargePayTransactionRepository, chargeService, abcIdGenerator, sheetProcessorService, chargeTransactionRecordRepository, chargeAbnormalTransactionService, chargeConfigService, scClinicService, shebaoService);
    }


    @Override
    public String getPayModeKey() {
        return InnerPayModes.getPayModeByName(InnerPayModes.InnerPayModeName.WECHAT_MOBILE_SHEBAO).getPayModeKey();
    }


    @Override
    protected List<PayCallBackActionReq> doGenerateOncePayCallBackReqs(PayCallbackContainPayModeReq payCallbackReq, ChargePayTransaction chargePayTransaction) {
        List<PayCallBackActionReq> payCallBackActionReqs = new ArrayList<>();
        //从action层面分成两笔，分开退费
        //医保移动支付-医保部分，单独设置paySubMode
        payCallBackActionReqs.add(new PayCallBackActionReq()
                .setSort(0)
                .setChargePayTransaction(chargePayTransaction)
                .setOncePayCallBackReqs(Arrays.asList(payCallbackReq.generateMainOncePayCallBackReq()
                        .setPaySubMode(Constants.ChargePaySubMode.WECHAT_MOBILE_SHEBAO_INSURANCE))));

        PayCallbackReq.ThirdPartPayInfo callbackThirdPartPayInfo = payCallbackReq.getThirdPartPayInfo();
        // 医保移动支付-现金部分
        if (callbackThirdPartPayInfo != null && MathUtils.wrapBigDecimalCompare(callbackThirdPartPayInfo.getReceivedFee(), BigDecimal.ZERO) > 0) {
            if (StringUtils.isEmpty(callbackThirdPartPayInfo.getOutTradeNo())) {
                throw new ParamRequiredException("thirdPartPayInfo.outTradeNo");
            }
            if (callbackThirdPartPayInfo.getThirdPartPayType() != PayCallbackReq.ThirdPartPayType.WECHAT_PAY) {
                throw new ParamNotValidException("医保移动支付现金部分暂不支持非微信渠道入账");
            }

            ThirdPartyPayInfo thirdPartyPayInfo = new ThirdPartyPayInfo();
            thirdPartyPayInfo.setTransactionId(callbackThirdPartPayInfo.getOutTradeNo());

            OncePayCallBackReq wechatMobileShebaoCashReq = new OncePayCallBackReq();
            wechatMobileShebaoCashReq.setPayMode(Constants.ChargePayMode.WECHAT_PAY)
                    .setPaySubMode(Constants.ChargePaySubMode.WECHAT_MOBILE_SHEBAO_CASH)
                    .setReceivedFee(callbackThirdPartPayInfo.getReceivedFee())
                    .setThirdPartyPayInfo(thirdPartyPayInfo)
                    .setSort(1);

            PayCallBackActionReq payCallBackActionReq = new PayCallBackActionReq()
                    .setSort(1)
                    .setOncePayCallBackReqs(Arrays.asList(wechatMobileShebaoCashReq));

            if (StringUtils.isNotEmpty(chargePayTransaction.getGroupId())) {
                ChargePayTransaction mobileShebaoCashTransaction = Optional.ofNullable(chargePayTransactionRepository.findAllByChargeSheetIdAndGroupIdAndIsDeleted(chargePayTransaction.getChargeSheetId(), chargePayTransaction.getGroupId(), 0))
                        .orElse(new ArrayList<>())
                        .stream()
                        .filter(c -> c.getPayStatus() == PayStatus.WAITING || c.getPayStatus() == PayStatus.CANCELED)
                        .filter(c -> c.getPayMode() == Constants.ChargePayMode.WECHAT_PAY && c.getPaySubMode() == Constants.ChargePaySubMode.WECHAT_MOBILE_SHEBAO)
                        .findFirst()
                        .orElse(null);
                chargePayTransaction.setGroupBranchPayTransactions(Arrays.asList(mobileShebaoCashTransaction));
                payCallBackActionReq.setChargePayTransaction(mobileShebaoCashTransaction);
            }

            payCallBackActionReqs.add(payCallBackActionReq);
        }

        payCallBackActionReqs.forEach(p -> p.getOncePayCallBackReqs().sort(Comparator.comparing(OncePayCallBackReq::getSort)));
        payCallBackActionReqs.sort(Comparator.comparing(PayCallBackActionReq::getSort));

        return payCallBackActionReqs;
    }
}
