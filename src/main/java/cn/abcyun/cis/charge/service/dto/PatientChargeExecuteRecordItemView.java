package cn.abcyun.cis.charge.service.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.Instant;

/**
 * 患者护士站执行记录子项
 *
 * <AUTHOR>
 * @version PatientChargeExecuteRecordItemView.java, 2020/7/9 下午8:57
 */
@Data
@Accessors(chain = true)
public class PatientChargeExecuteRecordItemView {
    /**
     * 执行记录子项id
     */
    private String     id;
    /**
     * 执行项id
     */
    private String     executeItemId;
    /**
     * 患者id
     */
    private String     patientId;
    /**
     * 商品id
     */
    private String     productId;
    /**
     * 商品名称
     */
    private String     productName;
    /**
     * 本次执行次数
     */
    private BigDecimal executeCount;
    /**
     * 本次执行单位
     */
    private String     executeUnit;
    /**
     * 执行时间
     */
    private Instant    executeDate;
}
