package cn.abcyun.cis.charge.service;

import cn.abcyun.bis.rpc.sdk.cis.model.clinic.BatchQueryEmployeeSnapshotReq;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Employee;
import cn.abcyun.bis.rpc.sdk.cis.model.shebao.ShebaoPayTaskClosedRspBody;
import cn.abcyun.cis.charge.base.ChargeServiceError;
import cn.abcyun.cis.charge.base.ChargeServiceException;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.base.PayModeInfo;
import cn.abcyun.cis.charge.model.ChargePayTransaction;
import cn.abcyun.cis.charge.model.ChargeTransaction;
import cn.abcyun.cis.charge.model.ThirdPartyPayInfo;
import cn.abcyun.cis.charge.processor.*;
import cn.abcyun.cis.charge.rpc.client.ShebaoClient;
import cn.abcyun.cis.charge.service.rpc.CisScClinicService;
import cn.abcyun.cis.charge.service.rpc.CisShebaoService;
import cn.abcyun.cis.charge.util.AbcIdUtils;
import cn.abcyun.cis.commons.CisServiceResponseBody;
import cn.abcyun.cis.commons.exception.FeignRuntimeException;
import cn.abcyun.cis.commons.exception.ServiceInternalException;
import cn.abcyun.cis.commons.rpc.crm.PatientInfo;
import cn.abcyun.cis.commons.rpc.pay.PayExtraInfo;
import cn.abcyun.cis.commons.rpc.pay.PayStatus;
import cn.abcyun.cis.commons.rpc.pay.shebao.PayForShebaoReq;
import cn.abcyun.cis.commons.rpc.pay.shebao.PayForShebaoRsp;
import cn.abcyun.cis.commons.rpc.pay.shebao.RefundForShebaoReq;
import cn.abcyun.cis.commons.rpc.pay.shebao.RefundForShebaoRsp;
import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.cis.commons.util.MathUtils;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.common.log.marker.AbcLogMarker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;

@Slf4j
public abstract class ChargeShebaoPayHandleAbstractService extends ChargePayHandleAbstractService{

    protected final EmployeeService employeeService;

    protected final PatientService patientService;
    protected final ShebaoClient shebaoClient;
    private final CisScClinicService scClinicService;
    private final CisShebaoService cisShebaoService;

    public ChargeShebaoPayHandleAbstractService(ChargeSheetService chargeSheetService,
                                                ChargePayTransactionService chargePayTransactionService,
                                                ChargeAbnormalTransactionService chargeAbnormalTransactionService,
                                                EmployeeService employeeService,
                                                PatientService patientService,
                                                ShebaoClient shebaoClient,
                                                CisScClinicService scClinicService, CisShebaoService cisShebaoService) {
        super(chargeSheetService, chargePayTransactionService, chargeAbnormalTransactionService);
        this.employeeService = employeeService;
        this.patientService = patientService;
        this.shebaoClient = shebaoClient;
        this.scClinicService = scClinicService;
        this.cisShebaoService = cisShebaoService;
    }

    public abstract int getShebaoPayMethod(int paySubMode);

    public abstract PayModeInfo getPayModeInfo();

    protected void checkBeforePay(ChargePayInfo payInfo) {
    }

    @Override
    public String getPayModeKey() {
        return getPayModeInfo().getPayModeKey();
    }

    @Override
    public ChargePayResult pay(ChargePayInfo payInfo) {
        if (payInfo == null) {
            return null;
        }

        Set<String> employeeIds = new HashSet<>();

        if (!TextUtils.isEmpty(payInfo.getSellerId())) {
            employeeIds.add(payInfo.getSellerId());
        }

        checkBeforePay(payInfo);

        List<Employee> employeeList = employeeService.findEmployeeList(payInfo.getChainId(), new ArrayList<>(employeeIds));

        PatientInfo patientInfo = patientService.findPatientInfoById(payInfo.getChainId(), payInfo.getClinicId(), payInfo.getPatientId(), false, false);

        PayForShebaoReq payForShebaoReq = new PayForShebaoReq();
        payForShebaoReq.setChargeSheetId(payInfo.getChargeSheetId());
        payForShebaoReq.setClinicId(payInfo.getClinicId());
        payForShebaoReq.setChainId(payInfo.getChainId());
        if (!TextUtils.isEmpty(payInfo.getDoctorId())) {
            //医生名称用快照
            Instant doctorIdSnapTime = Optional.ofNullable(payInfo.getDiagnosedDate()).orElse(Instant.now());
            Optional.ofNullable(scClinicService.queryEmployeeSnap(new BatchQueryEmployeeSnapshotReq()
                    .setChainId(payInfo.getChainId())
                    .addBusinessTimeEmployeeSnap(payInfo.getDoctorId(), doctorIdSnapTime)
            ).getEmployeeByBusTime(payInfo.getDoctorId(), doctorIdSnapTime))
                            .ifPresent(employee -> payForShebaoReq.setDoctorName(employee.getName()));
        }
        payForShebaoReq.setDoctorName(EmployeeService.findNameInEmployeeList(employeeList, payInfo.getDoctorId()));
        payForShebaoReq.setSellerName(EmployeeService.findNameInEmployeeList(employeeList, payInfo.getSellerId()));
        payForShebaoReq.setPatientOrderNo(payInfo.getPatientOrderNo());
        payForShebaoReq.setPatientOrderId(payInfo.getPatientOrderId());
        payForShebaoReq.setOperatorId(payInfo.getOperatorId());
        payForShebaoReq.setPatientName(patientInfo != null ? patientInfo.getName() : null);
        payForShebaoReq.setSheetReceivableFee(payInfo.getSheetReceivableFee());
        payForShebaoReq.setReceivableFee(payInfo.getReceivableFee());
        payForShebaoReq.setPayMethod(getShebaoPayMethod(payInfo.getPaySubMode()));

        String transactionId = AbcIdUtils.getUUID();
        payForShebaoReq.setRequestTransactionId(transactionId);

        PayForShebaoRsp payForShebaoRsp = null;
        try {
            long startRequestTime = System.currentTimeMillis();
            CisServiceResponseBody<PayForShebaoRsp> rspBody = shebaoClient.pay(payForShebaoReq);
            if (rspBody != null) {
                payForShebaoRsp = rspBody.getData();
                log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX,"payForShebao cost time:{}ms, rsp:{}", (System.currentTimeMillis() - startRequestTime), JsonUtils.dump(payForShebaoRsp));
            }
        } catch (FeignRuntimeException e) {
            log.error("payForShebao feign error", e);
            throw e;
        } catch (Exception e) {
            log.error("payForShebao error", e);
            throw new ServiceInternalException(500, "payForShebao error");
        }

        if (payForShebaoRsp == null) {
            throw new ServiceInternalException(500, "payForShebao error");
        }

        BigDecimal amount = null;
        switch (payForShebaoRsp.getPayStatus()) {
            case PayStatus.SUCCESS:
                amount = MathUtils.wrapBigDecimalOrZero(payForShebaoRsp.getReceivedFee());
                break;
            case PayStatus.WAITING:
                amount = MathUtils.wrapBigDecimalOrZero(payInfo.getReceivableFee());
                break;
            default:
                amount = BigDecimal.ZERO;
        }

        PayExtraInfo payExtraInfo = payForShebaoRsp.getExtraInfo();
        ThirdPartyPayInfo thirdPartyPayInfo = null;
        BigDecimal accountBalance = null;
        if (payExtraInfo != null) {
            accountBalance = payExtraInfo.getCardBalance();
            thirdPartyPayInfo = new ThirdPartyPayInfo();
            thirdPartyPayInfo.setCardId(payExtraInfo.getCardId());
            thirdPartyPayInfo.setIdCardNum(payExtraInfo.getIdCardNum());
            thirdPartyPayInfo.setCardBalance(payExtraInfo.getCardBalance());
            thirdPartyPayInfo.setCardOwner(payExtraInfo.getCardOwner());
            thirdPartyPayInfo.setTransactionId(payForShebaoRsp.getTaskId());
        }

        // 如果是微信医保支付，设置payTransaction的groupId
        String groupId = null;
        if (Objects.equals(payInfo.getPayMode(), Constants.ChargePayMode.HEALTH_CARD) && Objects.equals(payInfo.getPaySubMode(), Constants.ChargePaySubMode.WECHAT_MOBILE_SHEBAO)){
            groupId = transactionId;
        }
        ChargePayTransaction chargePayTransaction = saveChargePayTransactionForPay(transactionId, payInfo, amount, amount, BigDecimal.ZERO, payForShebaoRsp.getPayStatus(),
                ChargePayTransaction.PayType.PAY, payForShebaoRsp.getTaskId(), accountBalance, thirdPartyPayInfo, JsonUtils.dumpAsJsonNode(payForShebaoReq), groupId);

        ChargePayResult chargePayResult = new ChargePayResult();
        chargePayResult.setChargePayTransactionId(transactionId);
        chargePayResult.setPayStatus(payForShebaoRsp.getPayStatus());
        chargePayResult.setThirdPayTaskId(payForShebaoRsp.getTaskId());
        chargePayResult.setReceivedPrincipal(payForShebaoRsp.getPayStatus() == PayStatus.SUCCESS ? payForShebaoRsp.getReceivedFee() : BigDecimal.ZERO);
        chargePayResult.setPayInfo(thirdPartyPayInfo);
        chargePayResult.setExpireTime(chargePayTransaction.getExpireTime());
        return chargePayResult;
    }

    @Override
    public CloseOrderRsp closeOrder(ChargePayTransaction chargePayTransaction, String operatorId) {
        ShebaoPayTaskClosedRspBody shebaoPayTaskClosedRspBody = cisShebaoService.closePayTask(chargePayTransaction.getChainId(), chargePayTransaction.getClinicId(), chargePayTransaction.getId(), chargePayTransaction.getPayTransactionId(), operatorId);

        if (shebaoPayTaskClosedRspBody == null) {
            return CloseOrderRsp.fail("shebaoPayTaskClosedRspBody is null");
        }

        if (shebaoPayTaskClosedRspBody.getIsClosed() == 1) {
            return CloseOrderRsp.success()
                    .setIsHaveAbnormalByRefund(shebaoPayTaskClosedRspBody.getIsHaveAbnormalByRefund())
                    .setErrorMessage(shebaoPayTaskClosedRspBody.getErrorMessage());
        } else {
            return new CloseOrderRsp()
                    .setIsCloseSuccess(0)
                    .setIsHaveAbnormalByRefund(shebaoPayTaskClosedRspBody.getIsHaveAbnormalByRefund())
                    .setErrorMessage(shebaoPayTaskClosedRspBody.getErrorMessage());
        }
    }


    @Override
    public ChargePayRefundResult refund(ChargePayRefundInfo payRefundInfo) {
        return refundForShebao(payRefundInfo, false);
    }

    @Override
    public ChargePayRefundResult paidback(ChargePayRefundInfo payRefundInfo) {
        return refundForShebao(payRefundInfo, true);
    }

    protected ChargePayRefundResult refundForShebao(ChargePayRefundInfo refundInfo, boolean isPaidback) throws ServiceInternalException, ChargeServiceException {
        if (refundInfo == null) {
            return null;
        }

        log.info("refundForShebao {}", JsonUtils.dump(refundInfo));

        String thirdPartyPayTransactionId = checkBeforeRefundAndReturn(refundInfo);

        RefundForShebaoReq refundForShebaoReq = new RefundForShebaoReq();
        refundForShebaoReq.setChargeSheetId(refundInfo.getChargeSheetId());
        refundForShebaoReq.setPatientOrderId(refundInfo.getPatientOrderId());
        refundForShebaoReq.setClinicId(refundInfo.getClinicId());
        refundForShebaoReq.setChainId(refundInfo.getChainId());
        refundForShebaoReq.setOperatorId(refundInfo.getOperatorId());
        refundForShebaoReq.setReceivableFee(refundInfo.getRefundFee());
        refundForShebaoReq.setPayAuthNo(refundInfo.getAuthCode());

        String transactionId = AbcIdUtils.getUUID();
        refundForShebaoReq.setRequestTransactionId(transactionId);
        refundForShebaoReq.setPaidTaskId(thirdPartyPayTransactionId);
        RefundForShebaoRsp refundForShebaoRsp = null;
        try {
            long startRequestTime = System.currentTimeMillis();
            CisServiceResponseBody<RefundForShebaoRsp> rspBody = shebaoClient.refund(refundForShebaoReq);
            if (rspBody != null) {
                refundForShebaoRsp = rspBody.getData();
                log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX,"refundForShebao cost time:{}ms, rsp:{}", (System.currentTimeMillis() - startRequestTime), JsonUtils.dump(refundForShebaoRsp));
            }
        } catch (FeignRuntimeException e) {
            log.error("refundForShebao feign error", e);
            throw e;
        } catch (Exception e) {
            log.error("refundForShebao error", e);
            throw new ServiceInternalException(500, "refundForShebao error");
        }

        if (refundForShebaoRsp == null) {
            throw new ServiceInternalException(500, "refundForShebao error");
        }

        BigDecimal amount = null;
        switch (refundForShebaoRsp.getPayStatus()) {
            case PayStatus.SUCCESS:
                amount = MathUtils.wrapBigDecimalOrZero(refundForShebaoRsp.getReceivedFee());
                break;
            case PayStatus.WAITING:
                amount = MathUtils.wrapBigDecimalOrZero(refundInfo.getRefundFee());
                break;
            default:
                amount = BigDecimal.ZERO;
        }

        PayExtraInfo payExtraInfo = refundForShebaoRsp.getExtraInfo();
        ThirdPartyPayInfo thirdPartyPayInfo = null;
        BigDecimal accountBalance = null;
        if (payExtraInfo != null) {
            accountBalance = payExtraInfo.getCardBalance();
            thirdPartyPayInfo = new ThirdPartyPayInfo();
            thirdPartyPayInfo.setCardId(payExtraInfo.getCardId());
            thirdPartyPayInfo.setIdCardNum(payExtraInfo.getIdCardNum());
            thirdPartyPayInfo.setCardBalance(payExtraInfo.getCardBalance());
            thirdPartyPayInfo.setCardOwner(payExtraInfo.getCardOwner());
            thirdPartyPayInfo.setTransactionId(refundForShebaoRsp.getTaskId());
        }

        ChargePayTransaction chargePayTransaction = saveChargePayTransactionForRefund(transactionId, refundInfo, amount, amount, BigDecimal.ZERO, refundForShebaoRsp.getPayStatus(),
                isPaidback ? ChargePayTransaction.PayType.PAIDBACK : ChargePayTransaction.PayType.REFUND, refundForShebaoRsp.getTaskId(),
                thirdPartyPayTransactionId, accountBalance, thirdPartyPayInfo);

        ChargePayRefundResult chargePayRefundResult = new ChargePayRefundResult();
        chargePayRefundResult.setPayStatus(refundForShebaoRsp.getPayStatus());
        chargePayRefundResult.setChargePayTransactionId(transactionId);
        chargePayRefundResult.setThirdPartyPayTaskId(refundForShebaoRsp.getTaskId());
        chargePayRefundResult.setRefundedPrincipal(refundForShebaoRsp.getPayStatus() == PayStatus.SUCCESS ? MathUtils.wrapBigDecimalOrZero(refundForShebaoRsp.getReceivedFee()) : BigDecimal.ZERO);
        chargePayRefundResult.setPayInfo(chargePayTransaction.getPayInfo());
        chargePayRefundResult.setAssociateThirdPartyPayTaskId(thirdPartyPayTransactionId);
        chargePayRefundResult.setExpireTime(chargePayTransaction.getExpireTime());
        chargePayRefundResult.setLockPatientOrderExpireTime(getLockPatientOrderExpireTimeForRefund());

        return chargePayRefundResult;
    }

    /**
     * 退费前校验，可被重写
     * @param refundInfo
     */
    protected String checkBeforeRefundAndReturn(ChargePayRefundInfo refundInfo) {

        ChargeTransaction payChargeTransaction = null;
        if (refundInfo.getChargeTransactions() != null) {
            payChargeTransaction = refundInfo.getChargeTransactions()
                    .stream()
                    .sorted((a, b) -> -ObjectUtils.compare(a.getCreated(), b.getCreated()))
                    .filter(chargeTransaction -> chargeTransaction.getPayMode() == getPayModeInfo().getPayMode())
                    .filter(chargeTransaction -> MathUtils.wrapBigDecimalCompare(chargeTransaction.getAmount(), MathUtils.wrapBigDecimalOrZero(refundInfo.getRefundFee()).negate()) == 0)
                    .filter(chargeTransaction -> chargeTransaction.getIsPaidback() == 0)
                    .filter(chargeTransaction -> chargeTransaction.getIsDeleted() == 0)
                    .filter(chargeTransaction -> TextUtils.isEmpty(chargeTransaction.getAssociateTransactionId()))
                    .findFirst().orElse(null);
        }

        if (payChargeTransaction == null) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX,"医保卡退费金额与收费金额不一致");
            throw new ChargeServiceException(ChargeServiceError.REFUND_RECEIVED_HEALTH_CARD_NOT_MATCH.getCode(), String.format(ChargeServiceError.REFUND_RECEIVED_HEALTH_CARD_NOT_MATCH.getMessage(), "医保卡"));
        }

        return payChargeTransaction.getThirdPartyPayTransactionId();
    }

    @Override
    public Long getLockPatientOrderExpireTimeForRefund() {
        //社保退费默认25秒
        return 25 * 1000L;
    }

}
