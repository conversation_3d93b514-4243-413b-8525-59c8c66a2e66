package cn.abcyun.cis.charge.service.dto;

import cn.abcyun.cis.charge.model.ChargeAdditionalFee;
import cn.abcyun.cis.charge.model.ChargeForm;
import cn.abcyun.cis.charge.model.ChargeTransaction;
import cn.abcyun.cis.charge.model.ThirdPartyPayInfo;
import cn.abcyun.cis.commons.rpc.patient.MemberDiscountInfo;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

@Data
public class PayChargeSheetResult {
    private String id;
    private String patientOrderId;

    private int status;
    private int owedStatus;
    private String statusName;

    private BigDecimal needPay;
    private BigDecimal receivedFee;
    private BigDecimal netIncomeFee;

    private String dataSignature;
    private MemberDiscountInfo memberDiscountInfo;

    private List<ChargeForm> chargeForms;

    private List<ChargeAdditionalFee> additionalFees;

    private List<ChargeTransactionView> chargeTransactions;

    private Instant created;

    private String chargeActionId;

    private String chargePayTransactionId;
    private String chargeTransactionId;

    private int payStatus;

    private String thirdPartyPayTaskId;
    private Integer thirdPartyPayTaskType;   //0:pay 1:refund

    private ThirdPartyPayInfo.WeChatPayInfo weChatPayInfo;


    private int isNotDispensed;

    private int isContainAirPharmacy;

    private List<String> airPharmacyOrderIds;

    private int isAirPharmacyCanPay;

    /**
     * 取号机，把锁单状态独立出来，新增加的返回字段
     */
    private int lockStatus;//锁单状态：0：未锁单，非0：锁单【10-30：锁单中(10-20是患者锁单，10:患者在微诊所锁的单，11患者在取号机锁的单，20-30是收费台锁单，20收费员锁的单）】

    private int canUnLockChargeSheet = 0;//在锁单状态下，是否可以解锁 0 不可以，1可以

    private Instant autoUnlockTime;//锁单状态自动解锁时间

    /**
     * 是否能够完善收费单患者信息(0:不能 1:能)
     */
    private int canImprovePatientFlag;

    public int getIsContainAirPharmacy() {
        return CollectionUtils.isEmpty(airPharmacyOrderIds) ? 0 : 1;
    }

}
