package cn.abcyun.cis.charge.service;

import cn.abcyun.cis.charge.amqp.RocketMqProducer;
import cn.abcyun.cis.charge.model.ChargeTransactionRecord;
import cn.abcyun.cis.charge.model.ChargeTransactionRecordAdditional;
import cn.abcyun.cis.charge.model.ChargeTransactionRecordBatchInfo;
import cn.abcyun.cis.charge.repository.ChargeTransactionRecordAdditionalRepository;
import cn.abcyun.cis.charge.repository.ChargeTransactionRecordBatchInfoRepository;
import cn.abcyun.cis.charge.repository.ChargeTransactionRecordRepository;
import cn.abcyun.cis.charge.service.dto.ChargeTransactionRecordMerger;
import cn.abcyun.cis.charge.service.rpc.CisChargeRecordService;
import cn.abcyun.cis.charge.util.ListUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ChargeTransactionRecordService {

    private final ChargeTransactionRecordRepository chargeTransactionRecordRepository;
    private final ChargeTransactionRecordAdditionalRepository chargeTransactionRecordAdditionalRepository;
    private final ChargeTransactionRecordBatchInfoRepository chargeTransactionRecordBatchInfoRepository;
    private final CisChargeRecordService cisChargeRecordService;
    private final RocketMqProducer rocketMqProducer;

    private void bindBatchInfos(List<ChargeTransactionRecord> chargeTransactionRecords) {

        if (CollectionUtils.isEmpty(chargeTransactionRecords)) {
            return;
        }

        List<String> chargeTransactionIds = chargeTransactionRecords.stream()
                .map(ChargeTransactionRecord::getTransactionId)
                .collect(Collectors.toList());

        List<ChargeTransactionRecordBatchInfo> recordBatchInfos = chargeTransactionRecordBatchInfoRepository.findAllByTransactionIdInAndIsDeleted(chargeTransactionIds, 0);

        if (CollectionUtils.isEmpty(recordBatchInfos)) {
            return;
        }

        Map<String, List<ChargeTransactionRecordBatchInfo>> groupByRecordIdMap = ListUtils.groupByKey(recordBatchInfos, ChargeTransactionRecordBatchInfo::getTransactionRecordId);

        chargeTransactionRecords.forEach(record -> record.setBatchInfos(groupByRecordIdMap.get(record.getId())));
    }

    public void saveAll(List<ChargeTransactionRecord> chargeTransactionRecords) {
        if (CollectionUtils.isEmpty(chargeTransactionRecords)) {
            return;
        }

        List<ChargeTransactionRecordBatchInfo> recordBatchInfos = chargeTransactionRecords.stream()
                .flatMap(chargeTransactionRecord -> Optional.ofNullable(chargeTransactionRecord.getBatchInfos()).orElse(new ArrayList<>()).stream())
                .collect(Collectors.toList());


        rocketMqProducer.sendChargeTransactionRecordMessage(chargeTransactionRecords);

        chargeTransactionRecordRepository.saveAll(chargeTransactionRecords);
        if (CollectionUtils.isNotEmpty(recordBatchInfos)) {
            chargeTransactionRecordBatchInfoRepository.saveAll(recordBatchInfos);
        }
    }

    public List<ChargeTransactionRecord> listByChargeSheetIds(List<String> chargeSheetIds) {

        if (CollectionUtils.isEmpty(chargeSheetIds)) {
            return new ArrayList<>();
        }

        //1、查询chargeStat rpc
        List<cn.abcyun.bis.rpc.sdk.cis.model.charge.record.ChargeTransactionRecord> rpcChargeTransactionRecords = cisChargeRecordService.listByChargeSheetIds(chargeSheetIds);

        //2、查询当前数据库
        List<ChargeTransactionRecord> chargeTransactionRecords = chargeTransactionRecordRepository.findByChargeSheetIdInAndIsDeleted(chargeSheetIds, 0);
        bindAdditionals(chargeTransactionRecords);
        bindBatchInfos(chargeTransactionRecords);

        //3、合并数据
        return ChargeTransactionRecordMerger.merge(chargeTransactionRecords, rpcChargeTransactionRecords);
    }


    public void markRecordsAsOldRecord(String chargeSheetId) {

        if (StringUtils.isEmpty(chargeSheetId)) {
            return;
        }

        cisChargeRecordService.markRecordsAsOldRecord(chargeSheetId);

        chargeTransactionRecordRepository.markRecordsAsOldRecord(chargeSheetId);
    }

    public void updatePatientIdForAnonymousPatient(String patientOrderId, String chainId, String newPatientId) {
        chargeTransactionRecordRepository.updatePatientIdForAnonymousPatient(patientOrderId, chainId, newPatientId);
    }

    public List<ChargeTransactionRecord> listByClinicIdAndTransactionIds(String clinicId, List<String> chargeTransactionIds) {
        return listByClinicIdAndTransactionIdsAndIsOldRecord(clinicId, chargeTransactionIds, null);
    }

    public List<ChargeTransactionRecord> listByChargeSheetIdAndIsOldRecord(String chargeSheetId, int isOldRecord) {
        if (StringUtils.isEmpty(chargeSheetId)) {
            return new ArrayList<>();
        }

        //1、查询chargeStat rpc
        List<cn.abcyun.bis.rpc.sdk.cis.model.charge.record.ChargeTransactionRecord> rpcChargeTransactionRecords = cisChargeRecordService.listByChargeSheetIdAndIsOldRecord(chargeSheetId, isOldRecord);

        //2、查询当前数据库
        List<ChargeTransactionRecord> chargeTransactionRecords = chargeTransactionRecordRepository.findByChargeSheetIdAndIsDeletedAndIsOldRecord(chargeSheetId, 0, isOldRecord);
        bindAdditionals(chargeTransactionRecords);
        bindBatchInfos(chargeTransactionRecords);

        //3、合并数据
        return ChargeTransactionRecordMerger.merge(chargeTransactionRecords, rpcChargeTransactionRecords);
    }

    private void bindAdditionals(List<ChargeTransactionRecord> chargeTransactionRecords) {

        if (CollectionUtils.isEmpty(chargeTransactionRecords)) {
            return;
        }

        List<String> recordIds = chargeTransactionRecords.stream()
                .map(ChargeTransactionRecord::getId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(recordIds)) {
            return;
        }

        Map<String, ChargeTransactionRecordAdditional> additionalIdMap = cn.abcyun.cis.commons.util.ListUtils.alwaysList(chargeTransactionRecordAdditionalRepository.findAllByIdInAndIsDeleted(recordIds, 0))
                .stream()
                .collect(Collectors.toMap(ChargeTransactionRecordAdditional::getId, Function.identity(), (a, b) -> a));

        if (MapUtils.isEmpty(additionalIdMap)) {
            return;
        }

        chargeTransactionRecords.forEach(chargeTransactionRecord -> chargeTransactionRecord.setAdditional(additionalIdMap.get(chargeTransactionRecord.getId())));

    }

    public List<ChargeTransactionRecord> listByClinicIdAndTransactionIdsAndIsOldRecord(String clinicId, List<String> transactionIds, Integer isOldRecord) {

        if (CollectionUtils.isEmpty(transactionIds)) {
            return new ArrayList<>();
        }

        if (StringUtils.isEmpty(clinicId)) {
            return new ArrayList<>();
        }

        //1、查询chargeStat rpc
        List<cn.abcyun.bis.rpc.sdk.cis.model.charge.record.ChargeTransactionRecord> rpcChargeTransactionRecords = cisChargeRecordService.listByClinicIdAndTransactionIdsAndIsOldRecord(clinicId, transactionIds, isOldRecord);

        //2、查询当前数据库
        List<ChargeTransactionRecord> chargeTransactionRecords;
        if (isOldRecord == null) {
            chargeTransactionRecords = chargeTransactionRecordRepository.findAllByClinicIdAndTransactionIdInAndIsDeleted(clinicId, transactionIds, 0);
        } else {
            chargeTransactionRecords = chargeTransactionRecordRepository.findAllByClinicIdAndTransactionIdInAndIsDeletedAndIsOldRecord(clinicId, transactionIds, 0, isOldRecord);
        }
        bindAdditionals(chargeTransactionRecords);
        bindBatchInfos(chargeTransactionRecords);

        //3、合并数据
        return ChargeTransactionRecordMerger.merge(chargeTransactionRecords, rpcChargeTransactionRecords);
    }
}
