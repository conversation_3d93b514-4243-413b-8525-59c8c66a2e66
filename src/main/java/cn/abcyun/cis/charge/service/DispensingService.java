package cn.abcyun.cis.charge.service;

import cn.abcyun.bis.rpc.sdk.cis.model.charge.UpdateTraceableCodeReq;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.processor.provider.DispensingInfoProvider;
import cn.abcyun.cis.charge.rpc.client.DispensingServiceClient;
import cn.abcyun.cis.charge.service.dto.DispensingInfo;
import cn.abcyun.cis.charge.service.dto.DispensingSheetInfo;
import cn.abcyun.cis.charge.service.rpc.CisDispensingService;
import cn.abcyun.cis.charge.util.LogUtils;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.cis.commons.CisServiceResponseBody;
import cn.abcyun.cis.commons.exception.FeignRuntimeException;
import cn.abcyun.cis.commons.exception.ServiceInternalException;
import cn.abcyun.cis.commons.model.ComposeType;
import cn.abcyun.cis.commons.rpc.dispensing.DispensingBatchQueryReq;
import cn.abcyun.cis.commons.rpc.dispensing.DispensingBatchQueryRsp;
import cn.abcyun.cis.commons.rpc.dispensing.DispensingFormItem;
import cn.abcyun.cis.commons.rpc.dispensing.DispensingSheet;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.common.log.marker.AbcLogMarker;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class DispensingService implements DispensingInfoProvider {
    private static final Logger sLogger = LoggerFactory.getLogger(DispensingService.class);

    @Autowired
    private DispensingServiceClient mDispensingServiceClient;

    @Autowired
    private CisDispensingService cisDispensingService;

    @Autowired
    private ChargeService mChargeService;


    @Override
    public DispensingSheetInfo findDispensingInfo(String chargeSheetId) throws ServiceInternalException {

        DispensingSheet dispensingSheet = rpcGetDispensingSheetBySourceSheetId(chargeSheetId);
        if (dispensingSheet == null || dispensingSheet.getDispensingForms() == null) {
            return null;
        }

        DispensingSheetInfo dispensingSheetInfo = new DispensingSheetInfo();

        List<DispensingInfo> dispensingInfos = new ArrayList<>(dispensingSheet.getDispensingForms()
                .stream()
                .filter(dispensingForm -> dispensingForm.getDispensingFormItems() != null)
                .flatMap(dispensingForm -> dispensingForm.getDispensingFormItems().stream()
                        .map(dispensingFormItem -> {
                            List<DispensingFormItem> dispensingFormItems = new ArrayList<>();
                            if (dispensingFormItem.getComposeType() == ComposeType.COMPOSE) {
                                dispensingFormItems.add(dispensingFormItem);
                                if (!CollectionUtils.isEmpty(dispensingFormItem.getComposeChildren())) {
                                    dispensingFormItems.addAll(dispensingFormItem.getComposeChildren());
                                }
                            } else {
                                dispensingFormItems.add(dispensingFormItem);
                            }
                            return dispensingFormItems;
                        })
                        .filter(dispensingFormItems -> !CollectionUtils.isEmpty(dispensingFormItems))
                        .flatMap(Collection::stream))
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(DispensingFormItem::getSourceFormItemId, dispensingItem -> {
                    DispensingInfo dispensingInfo = new DispensingInfo();
                    dispensingInfo.setChargeFormItemId(dispensingItem.getSourceFormItemId());
                    if (dispensingItem.getStatus() == DispensingFormItem.Status.DISPENSED) {
                        dispensingInfo.setDispensedUnitCount(MathUtils.wrapBigDecimalOrZero(dispensingItem.getUnitCount()));
                        dispensingInfo.setDispensedDoseCount(MathUtils.wrapBigDecimal(dispensingItem.getDoseCount(), BigDecimal.ONE));
                    } else if (dispensingItem.getStatus() == DispensingFormItem.Status.PART_DISPENSE){
                        dispensingInfo.setDispensedUnitCount(MathUtils.wrapBigDecimalSubtract(dispensingItem.getUnitCount(), dispensingItem.getRemainingUnitCount()));
                        dispensingInfo.setDispensedDoseCount(MathUtils.wrapBigDecimal(dispensingItem.getDoseCount(), BigDecimal.ONE));
//
//                        dispensingInfo.setUndispensedUnitCount(MathUtils.wrapBigDecimalOrZero(dispensingItem.getRemainingUnitCount()));
//                        dispensingInfo.setUndispensedDoseCount(MathUtils.wrapBigDecimal(dispensingItem.getRemainingDoseCount(), BigDecimal.ONE));

                    } else {
                        dispensingInfo.setUndispensedUnitCount(MathUtils.wrapBigDecimalOrZero(dispensingItem.getUnitCount()).negate());
                        dispensingInfo.setUndispensedDoseCount(MathUtils.wrapBigDecimal(dispensingItem.getDoseCount(), BigDecimal.ONE));
                    }
                    return dispensingInfo;
                }, (a, b) -> {
                    a.setDispensedUnitCount(a.getDispensedUnitCount().add(b.getDispensedUnitCount()));
                    a.setUndispensedUnitCount(a.getUndispensedUnitCount().add(b.getUndispensedUnitCount()));
                    return a;
                })).values());
        dispensingSheetInfo.setStatus(dispensingSheet.getStatus());
        dispensingSheetInfo.setDispensingInfos(dispensingInfos);
        dispensingSheetInfo.setDispensedBy(dispensingSheet.getDispensedBy());
        dispensingSheetInfo.setDispensedTime(dispensingSheet.getDispensedTime());
        return dispensingSheetInfo;
    }

    @Override
    public List<DispensingSheetInfo> getDispensingSheetInfoListBySourceSheetId(String chargeSheetId) {
        if (StringUtils.isEmpty(chargeSheetId)) {
            return new ArrayList<>();
        }
        Set<String> composeChargeFormItemIds = new HashSet<>();
        return this.getDispensingSheets(chargeSheetId).stream()
                .filter(Objects::nonNull)
                .filter(dispensingSheet -> CollectionUtils.isNotEmpty(dispensingSheet.getDispensingForms()))
                .map(dispensingSheet -> {
                    DispensingSheetInfo dispensingSheetInfo = CisDispensingService.generateDispensingSheetInfoByDispensingSheet(dispensingSheet);
                    List<DispensingInfo> dispensingInfos = dispensingSheetInfo.getDispensingInfos();
                    if (CollectionUtils.isEmpty(dispensingInfos)) {
                        return dispensingSheetInfo;
                    }
                    // 移除套餐被药房拆分成多个发药单导致chargeFormItemId重复的数据，只保留一条数据
                    dispensingInfos.removeIf(dispensingInfo -> composeChargeFormItemIds.contains(dispensingInfo.getChargeFormItemId()));
                    Set<String> composeItemIds = Optional.ofNullable(dispensingSheet.getDispensingForms())
                            .orElse(new ArrayList<>())
                            .stream()
                            .filter(dispensingForm -> dispensingForm.getDispensingFormItems() != null)
                            .flatMap(dispensingForm -> dispensingForm.getDispensingFormItems().stream())
                            .filter(dispensingFormItem -> dispensingFormItem.getComposeType() == ComposeType.COMPOSE)
                            .map(cn.abcyun.bis.rpc.sdk.cis.model.dispensing.DispensingFormItem::getSourceFormItemId)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.toSet());
                    composeChargeFormItemIds.addAll(composeItemIds);
                    return dispensingSheetInfo;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<cn.abcyun.bis.rpc.sdk.cis.model.dispensing.DispensingSheet> getDispensingSheets(String chargeSheetId) {
        return cisDispensingService.getDispensingSheetListBySourceSheetId(chargeSheetId);
    }


    public DispensingSheet rpcGetDispensingSheetBySourceSheetId(String chargeSheetId) throws ServiceInternalException {
        if (TextUtils.isEmpty(chargeSheetId)) {
            return null;
        }

        DispensingSheet dispensingSheet = null;
        try {
            long startRequestTime = System.currentTimeMillis();
            CisServiceResponseBody<DispensingSheet> rspBody = mDispensingServiceClient.getDispensingSheetBySourceSheetId(chargeSheetId);
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX,"feign getDispensingSheetBySourceSheetId cost time:" + (System.currentTimeMillis() - startRequestTime) + "ms");
            if (rspBody != null) {
                dispensingSheet = rspBody.getData();
                LogUtils.infoObjectToJson(sLogger, "getDispensingSheetBySourceSheetId", dispensingSheet);
            }
        } catch (FeignRuntimeException e) {
            sLogger.error("getDispensingSheetBySourceSheetId feign error", e);
//            throw new ServiceInternalException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            sLogger.error("getDispensingSheetBySourceSheetId error", e);
//            throw new ServiceInternalException(500, "getDispensingSheet error");
        }
        return dispensingSheet;
    }


    public void rpcPutRenewChargeSheet(String chargeSheetId, String operatorId) throws ServiceInternalException {
        if (TextUtils.isEmpty(operatorId) || TextUtils.isEmpty(chargeSheetId)) {
            return;
        }

        try {
            long startRequestTime = System.currentTimeMillis();
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX,"rpcPutRenewChargeSheet chargeSheetId:{}", chargeSheetId);
            mDispensingServiceClient.putRenewBySourceSheetId(chargeSheetId, operatorId);
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX,"feign rpcPutRenewChargeSheet cost time:" + (System.currentTimeMillis() - startRequestTime) + "ms");
        } catch (FeignRuntimeException e) {
            sLogger.error("rpcPutRenewChargeSheet feign error", e);
            throw e;
        } catch (Exception e) {
            sLogger.error("rpcPutRenewChargeSheet error", e);
            throw new ServiceInternalException(500, "rpcPutRenewChargeSheet error");
        }
        return;
    }

    public Map<String, DispensingBatchQueryRsp.DispensingSimpleInfo> rpcQueryDispensingSimpleInfo(String chainId, List<String> chargeSheetIds) {
        if (TextUtils.isEmpty(chainId) || chargeSheetIds == null || chargeSheetIds.size() == 0) {
            return new HashMap<>();
        }

        Map<String, DispensingBatchQueryRsp.DispensingSimpleInfo> resultsMap = chargeSheetIds.stream().collect(Collectors.toMap(Function.identity(), a -> new DispensingBatchQueryRsp.DispensingSimpleInfo(), (a, b) -> a));

        try {
            long startRequestTime = System.currentTimeMillis();
            DispensingBatchQueryReq req = new DispensingBatchQueryReq();
            req.setChainId(chainId);
            req.setSourceSheetIds(chargeSheetIds);
            CisServiceResponseBody<DispensingBatchQueryRsp> rspBody = mDispensingServiceClient.queryDispensingSimpleInfos(req);
            if (rspBody != null && rspBody.getData() != null) {
                DispensingBatchQueryRsp rsp = rspBody.getData();
                if (rsp.getResults() != null) {
                    rsp.getResults().forEach(dispensingSimpleInfo -> {
                        if (resultsMap.containsKey(dispensingSimpleInfo.getSourceSheetId())) {
                            resultsMap.put(dispensingSimpleInfo.getSourceSheetId(), dispensingSimpleInfo);
                        }
                    });
                }
            }
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX,"rpcQueryDispensingSimpleInfo cost time:" + (System.currentTimeMillis() - startRequestTime) + "ms");
        } catch (FeignRuntimeException e) {
            sLogger.error("rpcQueryDispensingSimpleInfo feign error", e);
            throw e;
        } catch (Exception e) {
            sLogger.error("rpcQueryDispensingSimpleInfo error", e);
            throw e;
        }

        return resultsMap;
    }

    /**
     * 初始化空中药房的发药状态
     * 消息生成，create，update
     */
    public void initAirChargeSheetDispensingStatus(ChargeSheet chargeSheet) {
        if (chargeSheet == null) {
            return;
        }
        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED) {
            return;
        }

        long airChargeFormCount = chargeSheet.getChargeForms().stream()
                .filter(cf -> cf.getIsDeleted() == 0)
                .filter(cf -> cf.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY).count();
        if (chargeSheet.getAdditional() != null && airChargeFormCount > 0) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX,"initAirChargeSheetDispensingStatus airChargeFormCount={}", airChargeFormCount);
            chargeSheet.getAdditional().setAirDispensingStatus(Constants.AirDispensingStatus.WAITING);
        }
    }

    /**
     * 处理成chargeSheet的总的空中药房的发药状态
     * 消息生成，create，update
     */
    public void updateAirChargeSheetDispensingStatus(ChargeSheet chargeSheet) {
        if (chargeSheet == null) {
            return;
        }


        long airChargeFormCount = chargeSheet.getChargeForms().stream()
                .filter(cf -> cf.getIsDeleted() == 0)
                .filter(cf -> cf.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY).count();
        if (chargeSheet.getAdditional() != null && airChargeFormCount > 0) {
            int oldSendMedcineChargeForms = chargeSheet.getAdditional().getAirDispensingStatus();
            int newSendMedcineChargeForms = oldSendMedcineChargeForms;
            if (oldSendMedcineChargeForms == Constants.AirDispensingStatus.NONE) {
                newSendMedcineChargeForms = Constants.AirDispensingStatus.WAITING;
            }

            newSendMedcineChargeForms++; //当前已经发了
            if (newSendMedcineChargeForms >= airChargeFormCount) { //全部发完
                newSendMedcineChargeForms = Constants.AirDispensingStatus.ALL_DISPENSED;
            }
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX,"updateAirChargeSheetDispensingStatus {} airChargeFormCount={},oldAirChargeFormCount={}-->newSendMedcineChargeForms={}", chargeSheet.getId(), airChargeFormCount, oldSendMedcineChargeForms, newSendMedcineChargeForms);
            chargeSheet.getAdditional().setAirDispensingStatus(newSendMedcineChargeForms);
        }
    }

    public void updateDispensingSheetTraceableCode(UpdateTraceableCodeReq req) {
        if (CollectionUtils.isEmpty(req.getList())) {
            return;
        }
        cisDispensingService.updateDispensingSheetTraceableCode(req);
    }
}
