package cn.abcyun.cis.charge.service.dto.print;

import cn.abcyun.bis.rpc.sdk.cis.model.copharmacy.CoOutpatientSheetPrintInfoRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.outpatient.print.OutpatientSheetPrintView;
import cn.abcyun.bis.rpc.sdk.property.service.model.ConfigItemMapReq;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2024-08-12 11:27
 * @Description
 */

@Data
public class CoOutpatientSheetPrintView {

    @ApiModelProperty("药店ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String clinicId;

    @ApiModelProperty("药店名")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String clinicName;

    @ApiModelProperty("门诊单打印信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private OutpatientSheetPrintView printInfo;

    @ApiModelProperty("打印设置")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ConfigItemMapReq printSetting;

    @ApiModelProperty("基础设置")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ConfigItemMapReq clinicBasicSetting;
}
