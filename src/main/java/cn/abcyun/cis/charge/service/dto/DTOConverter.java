package cn.abcyun.cis.charge.service.dto;

import cn.abcyun.bis.rpc.sdk.bis.model.order.CalculateLogisticsReq;
import cn.abcyun.bis.rpc.sdk.bis.model.order.CalculatePriceReq;
import cn.abcyun.bis.rpc.sdk.bis.model.order.OrderItemReq;
import cn.abcyun.bis.rpc.sdk.cis.model.charge.ChargeConstants;
import cn.abcyun.bis.rpc.sdk.cis.model.charge.GoodsFeeType;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsItem;
import cn.abcyun.bis.rpc.sdk.cis.model.outpatient.UsageInfo;
import cn.abcyun.cis.charge.api.model.ChargeDeliveryReq;
import cn.abcyun.cis.charge.api.model.ChargeRuleExpressDeliveryAddressVo;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.model.*;
import cn.abcyun.cis.charge.processor.discount.GiftCouponView;
import cn.abcyun.cis.charge.util.AbcIdUtils;
import cn.abcyun.cis.charge.util.BisOrderUtils;
import cn.abcyun.cis.charge.util.FillUtils;
import cn.abcyun.cis.charge.util.JsonUtils;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.cis.commons.model.ComposeType;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

public class DTOConverter {
    public static ChargeSheetExtend convertToChargeSheetExtend(ChargeSheet chargeSheet) {
        ChargeSheetExtend chargeSheetExtend = new ChargeSheetExtend();
        if (chargeSheet.getChargeForms() == null) {
            chargeSheet.setChargeForms(new ArrayList<>());
        }
        BeanUtils.copyProperties(chargeSheet, chargeSheetExtend);
        chargeSheetExtend.setDoctorId(chargeSheetExtend.getChargeSheetDoctorId());
        ChargeSheetAdditional additional = chargeSheet.getAdditional();
        if (additional != null) {
            chargeSheetExtend.setDepartmentId(additional.getDepartmentId());
            chargeSheetExtend.setDiagnosisInfos(additional.getDiagnosisInfos());
            chargeSheetExtend.setNotificationInfos(additional.getNotificationInfos());
            chargeSheetExtend.setExtendDiagnosisInfos(additional.getExtendDiagnosisInfos());
            chargeSheetExtend.setTranscribeDoctorId(additional.getTranscribeDoctorId());
            chargeSheetExtend.setInvoiceStatusFlag(additional.getInvoiceStatusFlag());
            ChargeSheetAdditionalExtendInfo extendedInfo = additional.getExtendedInfo();
            if (extendedInfo != null) {
                chargeSheetExtend.setRegisterInfoId(extendedInfo.getRegisterInfoId());
            }
        }

        return chargeSheetExtend;
    }

    public static ChargeFormItem generateComposeSubFormItem(ChargeFormItem composeChargeFormItem, GoodsItem subGoodsItem, String operatorId) {
        ChargeFormItem chargeFormItem = new ChargeFormItem();
        chargeFormItem.setId(AbcIdUtils.getUUID());
        chargeFormItem.setChargeFormId(composeChargeFormItem.getChargeFormId());
        chargeFormItem.setChargeSheetId(composeChargeFormItem.getChargeSheetId());
        chargeFormItem.setPatientOrderId(composeChargeFormItem.getPatientOrderId());
        chargeFormItem.setClinicId(composeChargeFormItem.getClinicId());
        chargeFormItem.setChainId(composeChargeFormItem.getChainId());
        chargeFormItem.setComposeParentFormItemId(composeChargeFormItem.getId());
        chargeFormItem.setComposeType(ComposeType.COMPOSE_SUB_ITEM);
        chargeFormItem.setProductId(subGoodsItem.getId());
        chargeFormItem.setProductType(subGoodsItem.getType());
        chargeFormItem.setProductSubType(subGoodsItem.getSubType());
        chargeFormItem.setName(subGoodsItem.getName());
        chargeFormItem.setStatus(Constants.ChargeFormItemStatus.UNCHARGED);
        chargeFormItem.setV2Status(Constants.ChargeFormItemStatus.UNCHARGED);
        chargeFormItem.setDoseCount(composeChargeFormItem.getDoseCount());
        chargeFormItem.setUnit(subGoodsItem.getPackageUnit());
        chargeFormItem.setUseDismounting(subGoodsItem.getComposeUseDismounting());
        chargeFormItem.setFeeComposeType(subGoodsItem.getFeeComposeType());
        chargeFormItem.setFeeTypeId(subGoodsItem.getFeeTypeId());
        chargeFormItem.setSort(subGoodsItem.getComposeSort());
        chargeFormItem.setPharmacyNo(subGoodsItem.getPharmacyNo());
        chargeFormItem.setPharmacyType(subGoodsItem.getPharmacyType());
        appendShebaoPayType(composeChargeFormItem, chargeFormItem, subGoodsItem);
        appendComposeSubFormItemPrice(composeChargeFormItem, subGoodsItem, chargeFormItem);
        FillUtils.fillCreatedBy(chargeFormItem, operatorId);
        return chargeFormItem;
    }

    public static void appendShebaoPayType(ChargeFormItem composeChargeFormItem, ChargeFormItem chargeFormItem, GoodsItem subGoodsItem) {

        if (Objects.isNull(composeChargeFormItem) || Objects.isNull(chargeFormItem) || Objects.isNull(subGoodsItem)) {
            return;
        }

        //先用母项的payType
        UsageInfo parentUsageInfo = JsonUtils.readValue(composeChargeFormItem.getUsageInfo(), UsageInfo.class);
        Integer parentShebaPayType = Optional.ofNullable(parentUsageInfo).map(UsageInfo::getPayType).orElse(null);

        if (Objects.nonNull(parentShebaPayType)) {
            UsageInfo childUsageInfo = Optional.ofNullable(chargeFormItem.getUsageInfoJson())
                    .map(usageInfoJson -> JsonUtils.readValue(usageInfoJson, UsageInfo.class))
                    .orElse(new UsageInfo());
            childUsageInfo.setPayType(parentShebaPayType);
            chargeFormItem.setUsageInfo(JsonUtils.dumpAsJsonNode(childUsageInfo));
            chargeFormItem.setUsageInfoJson(JsonUtils.dump(childUsageInfo));
            return;
        }

        //再用goods上的payType
        Integer shebaoPayType = convertPayTypeFromShebaoPayMode(subGoodsItem.getShebaoPayMode());
        Optional.ofNullable(shebaoPayType)
                .ifPresent(payType -> {
                    UsageInfo childUsageInfo = Optional.ofNullable(chargeFormItem.getUsageInfoJson())
                            .map(usageInfoJson -> JsonUtils.readValue(usageInfoJson, UsageInfo.class))
                            .orElse(new UsageInfo());
                    childUsageInfo.setPayType(payType);
                    chargeFormItem.setUsageInfo(JsonUtils.dumpAsJsonNode(childUsageInfo));
                    chargeFormItem.setUsageInfoJson(JsonUtils.dump(childUsageInfo));
                });
    }

    private static Integer convertPayTypeFromShebaoPayMode(Integer shebaoPayMode) {

        if (Objects.isNull(shebaoPayMode)) {
            return null;
        }

        return Objects.equals(GoodsConst.SheBaoPayMode.SELF_PAY, shebaoPayMode) ? ChargeConstants.ChargeFormItemPayType.SHEBAO_PERSONAL_PAYMENT : null;
    }

    public static void appendComposeSubFormItemPrice(ChargeFormItem composeChargeFormItem, GoodsItem subGoodsItem, ChargeFormItem chargeFormItem) {
        int childComposeType = ComposeType.NOT_COMPOSE;
        int childGoodsFeeType = GoodsFeeType.FEE_OWN;
        //判断母项是什么类型
        if (composeChargeFormItem.getComposeType() == ComposeType.COMPOSE) {
            childComposeType = ComposeType.COMPOSE_SUB_ITEM;
            childGoodsFeeType = subGoodsItem.getFeeComposeType() == GoodsConst.FeeComposeType.FEE_TYPE_COMPOSE_FEE ? GoodsFeeType.FEE_PARENT : GoodsFeeType.FEE_OWN;
        } else if (composeChargeFormItem.getComposeType() == ComposeType.NOT_COMPOSE) {
            childComposeType = ComposeType.NOT_COMPOSE;
            childGoodsFeeType = GoodsFeeType.FEE_CHILD;
        } else if (composeChargeFormItem.getComposeType() == ComposeType.COMPOSE_SUB_ITEM) {
            childComposeType = ComposeType.COMPOSE_SUB_ITEM_FEE_CHILD;
            childGoodsFeeType = GoodsFeeType.FEE_CHILD;
        }

        chargeFormItem.setComposeType(childComposeType);
        chargeFormItem.setGoodsFeeType(childGoodsFeeType);

        if (chargeFormItem.getUseDismounting() == GoodsConst.DismountingStatus.PACKAGE) {
            chargeFormItem.setUnitCount(MathUtils.wrapBigDecimalMultiply(subGoodsItem.getComposePackageCount(), composeChargeFormItem.getUnitCount()));
            chargeFormItem.setUnitPrice(subGoodsItem.getComposePackagePrice());
        } else {
            chargeFormItem.setUnitCount(MathUtils.wrapBigDecimalMultiply(subGoodsItem.getComposePieceCount(), composeChargeFormItem.getUnitCount()));
            chargeFormItem.setUnitPrice(subGoodsItem.getComposePiecePrice());
        }

        //计算套餐子项的零头总额，goods上的零头是一个套餐的零头，这里要乘以套餐的总数量
        BigDecimal composeChildFractionTotalPrice = MathUtils.calculateTotalPrice(composeChargeFormItem.getUnitCount(), subGoodsItem.getComposeFractionPrice());
        chargeFormItem.setFractionPrice(composeChildFractionTotalPrice);

        chargeFormItem.setTotalPrice(MathUtils.calculateTotalPrice(chargeFormItem.getUnitPrice(), chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount(), 2));
        updateAdditionalTraceableCode(chargeFormItem, composeChargeFormItem);

    }

    /**
     * 套餐母项没有traceableCode，子项重新设置下
     */
    private static void updateAdditionalTraceableCode(ChargeFormItem childChargeFormItem, ChargeFormItem composeChargeFormItem) {
        if (childChargeFormItem == null || composeChargeFormItem == null) {
            return;
        }
        ChargeFormItemAdditional childAdditional = null;
        if (childChargeFormItem.getAdditional() != null) {
            childAdditional = childChargeFormItem.getAdditional();
        }
        //TODO 这里确认要深拷贝
        ChargeFormItemAdditional composeChargeFromItemAdditional = JsonUtils.readValue(JsonUtils.dump(composeChargeFormItem.getAdditional()), ChargeFormItemAdditional.class);
        childChargeFormItem.setAdditional(composeChargeFromItemAdditional);
        if (childAdditional != null) {
            if (childChargeFormItem.getAdditional() != null) {
                childChargeFormItem.getAdditional().setTraceableCodeList(childAdditional.getTraceableCodeList());
            } else {
                childChargeFormItem.setAdditional(childAdditional);
            }
        }
    }


    public static int parseProductType(int sourceFormType, Integer productType) {
        int type;
        if (sourceFormType == Constants.SourceFormType.REGISTRATION) {
            type = productType == null ? Constants.ProductType.REGISTRATION : productType;
        } else if (sourceFormType == Constants.SourceFormType.TREATMENT) {
            type = productType == null ? Constants.ProductType.TREATMENT : productType;
        } else if (sourceFormType == Constants.SourceFormType.EXAMINATION) {
            type = productType == null ? Constants.ProductType.EXAMINATION : productType;
        } else if (sourceFormType == Constants.SourceFormType.PRESCRIPTION_CHINESE
                || sourceFormType == Constants.SourceFormType.PRESCRIPTION_WESTERN
                || sourceFormType == Constants.SourceFormType.PRESCRIPTION_INFUSION) {
            type = productType == null ? Constants.ProductType.MEDICINE : productType;
        } else {
            type = productType == null ? 0 : productType;
        }
        return type;
    }

    public static int parseProductSubType(int sourceFormType, Integer productSubType) {
        int type;
        if (sourceFormType == Constants.SourceFormType.REGISTRATION) {
            type = productSubType == null ? 0 : productSubType;
        } else if (sourceFormType == Constants.SourceFormType.TREATMENT) {
            type = productSubType == null ? 1 : productSubType;
        } else if (sourceFormType == Constants.SourceFormType.EXAMINATION) {
            type = productSubType == null ? 1 : productSubType;
        } else if (sourceFormType == Constants.SourceFormType.PRESCRIPTION_CHINESE) {
            type = productSubType == null ? Constants.ProductType.SubType.MEDICINE_CHINESE : productSubType;
        } else if (sourceFormType == Constants.SourceFormType.PRESCRIPTION_WESTERN
                || sourceFormType == Constants.SourceFormType.PRESCRIPTION_INFUSION) {
            type = productSubType == null ? Constants.ProductType.SubType.MEDICINE_WESTERN : productSubType;
        } else {
            type = productSubType == null ? 1 : productSubType;
        }
        return type;
    }

    public static GiftRulePromotionView convertToGiftRulePromotionView(ChargeGiftRulePromotionInfo giftRulePromotionInfo) {

        GiftRulePromotionView giftRulePromotionView = new GiftRulePromotionView();

        giftRulePromotionView.setId(giftRulePromotionInfo.getPromotionId());
        giftRulePromotionView.setName(giftRulePromotionInfo.getName());
        giftRulePromotionView.setChecked(!StringUtils.isEmpty(giftRulePromotionInfo.getChecked()) && Boolean.parseBoolean(giftRulePromotionInfo.getChecked()));
        giftRulePromotionView.setOrderThresholdPrice(giftRulePromotionInfo.getOrderThresholdPrice());
        giftRulePromotionView.setDiscountPrice(giftRulePromotionInfo.getDiscountPrice());
        giftRulePromotionView.setExpectedChecked(!StringUtils.isEmpty(giftRulePromotionInfo.getExpectedChecked()) ? Boolean.parseBoolean(giftRulePromotionInfo.getExpectedChecked()) : null);
        giftRulePromotionView.setGiftCoupons(JsonUtils.getList(giftRulePromotionInfo.getGiftCoupons(), GiftCouponView.class));
        giftRulePromotionView.setGiftGoodItems(JsonUtils.getList(giftRulePromotionInfo.getGiftGoodItems(), GiftRulePromotionView.GiftGoodsItemView.class));
        giftRulePromotionView.setProductItems(JsonUtils.getList(giftRulePromotionInfo.getProductItems(), PromotionProductItem.class));
        giftRulePromotionView.setIsCanBeUsed(giftRulePromotionInfo.getIsCanBeUsed());

        return giftRulePromotionView;

    }

    public static ChargeGiftRulePromotionInfo convertToGiftRulePromotionView(ChargeSheet chargeSheet, GiftRulePromotionView giftRulePromotionView, String operatorId) {

        ChargeGiftRulePromotionInfo giftRulePromotionInfo = new ChargeGiftRulePromotionInfo();
        giftRulePromotionInfo.setId(AbcIdUtils.getUID());
        giftRulePromotionInfo.setPromotionId(giftRulePromotionView.getId());
        giftRulePromotionInfo.setChargeSheetId(chargeSheet.getId());
        giftRulePromotionInfo.setChainId(chargeSheet.getChainId());
        giftRulePromotionInfo.setClinicId(chargeSheet.getClinicId());
        giftRulePromotionInfo.setName(giftRulePromotionView.getName());
        giftRulePromotionInfo.setChecked(String.valueOf(giftRulePromotionView.getChecked()));
        giftRulePromotionInfo.setOrderThresholdPrice(giftRulePromotionView.getOrderThresholdPrice());
        giftRulePromotionInfo.setDiscountPrice(giftRulePromotionView.getDiscountPrice());
        giftRulePromotionInfo.setExpectedChecked(giftRulePromotionView.getExpectedChecked() != null ? String.valueOf(giftRulePromotionView.getExpectedChecked()) : null);
        giftRulePromotionInfo.setGiftCoupons(JsonUtils.dump(giftRulePromotionView.getGiftCoupons()));
        giftRulePromotionInfo.setGiftGoodItems(JsonUtils.dump(giftRulePromotionView.getGiftGoodItems()));
        giftRulePromotionInfo.setProductItems(JsonUtils.dump(giftRulePromotionView.getProductItems()));
        giftRulePromotionInfo.setIsCanBeUsed(giftRulePromotionView.getIsCanBeUsed());
        giftRulePromotionInfo.setCreated(Instant.now());
        giftRulePromotionInfo.setLastModified(Instant.now());

        return giftRulePromotionInfo;

    }

    public static CouponPromotionView convertToCouponPromotionView(ChargeCouponPromotionInfo couponPromotionInfo) {

        CouponPromotionView couponPromotionView = new CouponPromotionView();

        couponPromotionView.setId(couponPromotionInfo.getPromotionId());
        couponPromotionView.setName(couponPromotionInfo.getName());
        couponPromotionView.setChecked(!StringUtils.isEmpty(couponPromotionInfo.getChecked()) && Boolean.parseBoolean(couponPromotionInfo.getChecked()));
        couponPromotionView.setCouponIds(JsonUtils.getList(couponPromotionInfo.getCouponIds(), String.class));
        couponPromotionView.setExpectedChecked(!StringUtils.isEmpty(couponPromotionInfo.getExpectedChecked()) ? Boolean.parseBoolean(couponPromotionInfo.getExpectedChecked()) : null);
        couponPromotionView.setDiscountPrice(couponPromotionInfo.getDiscountPrice());
        couponPromotionView.setTotalCount(couponPromotionInfo.getTotalCount());
        couponPromotionView.setAvailableCount(couponPromotionInfo.getAvailableCount());
        couponPromotionView.setCurrentCount(couponPromotionInfo.getCurrentCount());
        couponPromotionView.setProductItems(JsonUtils.getList(couponPromotionInfo.getProductItems(), PromotionProductItem.class));
        couponPromotionView.setIsCanBeUsed(couponPromotionInfo.getIsCanBeUsed());
        couponPromotionView.setPromotionId(couponPromotionInfo.getPromotionId());

        return couponPromotionView;

    }

    public static ChargeCouponPromotionInfo convertToChargeCouponPromotionInfo(ChargeSheet chargeSheet, CouponPromotionView couponPromotionView, String operatorId) {
        ChargeCouponPromotionInfo couponPromotionInfo = new ChargeCouponPromotionInfo();
        couponPromotionInfo.setId(AbcIdUtils.getUID());
        couponPromotionInfo.setChainId(chargeSheet.getChainId());
        couponPromotionInfo.setChargeSheetId(chargeSheet.getId());
        couponPromotionInfo.setClinicId(chargeSheet.getClinicId());
        couponPromotionInfo.setPromotionId(couponPromotionView.getId());
//        couponPromotionInfo.setCouponIds(JsonUtils.dump(couponPromotionView.getCouponIds()));
        couponPromotionInfo.setCouponIds(JsonUtils.dump(Collections.emptyList()));
        couponPromotionInfo.setName(couponPromotionView.getName());
        couponPromotionInfo.setChecked(String.valueOf(couponPromotionView.getChecked()));
        couponPromotionInfo.setExpectedChecked(couponPromotionView.getExpectedChecked() != null ? String.valueOf(couponPromotionView.getExpectedChecked()) : null);
        couponPromotionInfo.setDiscountPrice(couponPromotionView.getDiscountPrice());
        couponPromotionInfo.setTotalCount(couponPromotionView.getTotalCount());
        couponPromotionInfo.setAvailableCount(couponPromotionView.getAvailableCount());
        couponPromotionInfo.setCurrentCount(couponPromotionView.getCurrentCount());
        couponPromotionInfo.setProductItems(JsonUtils.dump(couponPromotionView.getProductItems()));
        couponPromotionInfo.setIsCanBeUsed(couponPromotionView.getIsCanBeUsed());
        couponPromotionInfo.setCreated(Instant.now());
        couponPromotionInfo.setLastModified(Instant.now());
        return couponPromotionInfo;
    }

    public static ChargeRuleExpressDeliveryAddress convertToChargeRuleExpressDeliveryAddress(ChargeRuleExpressDeliveryAddressVo info, ChargeRuleExpressDelivery chargeRuleExpressDelivery, String operatorId) {
        ChargeRuleExpressDeliveryAddress address = new ChargeRuleExpressDeliveryAddress();
        BeanUtils.copyProperties(info, address);
        address.setId(AbcIdUtils.getUID());
        address.setChainId(chargeRuleExpressDelivery.getChainId());
        address.setClinicId(chargeRuleExpressDelivery.getClinicId());
        address.setRuleId(chargeRuleExpressDelivery.getId());
        FillUtils.fillCreatedBy(address, operatorId);
        return address;
    }

    public static ChargeDeliveryInfo convertToChargeDeliveryInfo(ChargeDeliveryReq deliveryInfoReq, String chainId, String clinicId) {
        ChargeDeliveryInfo chargeDeliveryInfo = new ChargeDeliveryInfo();
        BeanUtils.copyProperties(deliveryInfoReq, chargeDeliveryInfo, "deliveryCompany");
        chargeDeliveryInfo.setId(AbcIdUtils.getUID());
        chargeDeliveryInfo.setChainId(chainId);
        chargeDeliveryInfo.setClinicId(clinicId);

        ChargeDeliveryCompany deliveryCompany = null;
        if (deliveryInfoReq.getDeliveryCompany() != null) {
            deliveryCompany = new ChargeDeliveryCompany();
            ChargeDeliveryReq.DeliveryCompanyReq deliveryCompanyReq = deliveryInfoReq.getDeliveryCompany();
            BeanUtils.copyProperties(deliveryCompanyReq, deliveryCompany);

            deliveryCompany.setChainId(chainId);
            deliveryCompany.setClinicId(clinicId);
            if (StringUtils.isEmpty(deliveryCompany.getId())) {
                deliveryCompany.setId(AbcIdUtils.getUID());
            }
        }

        chargeDeliveryInfo.setDeliveryCompany(deliveryCompany);
        chargeDeliveryInfo.setDeliveryCompanyId(deliveryCompany != null ? deliveryCompany.getId() : null);
        return chargeDeliveryInfo;
    }

    public static CalculatePriceReq.ChargeFormReq convertToBisChargeFormReq(ChargeForm chargeForm) {
        if (chargeForm == null) {
            return null;
        }

        CalculatePriceReq.ChargeFormReq chargeFormReq = new CalculatePriceReq.ChargeFormReq();
        chargeFormReq.setOrderItems(new ArrayList<>());
        chargeFormReq.setId(chargeForm.getId());
        chargeFormReq.setMedicineStateScopeId(chargeForm.getMedicineStateScopeId());
        chargeFormReq.setUsageScopeId(chargeForm.getUsageScopeId());
        chargeFormReq.setVendorId(chargeForm.getVendorId());
        chargeFormReq.setGoodsTypeId(BisOrderUtils.convertToGoodsTypeId(chargeForm.getSpecification()) + "");
        chargeFormReq.setDeliveryPrimaryFormId(chargeForm.getDeliveryPrimaryFormId());
        if (!StringUtils.isEmpty(chargeForm.getUsageInfoJson())) {
            chargeFormReq.setUsageInfo(JsonUtils.readValue(chargeForm.getUsageInfoJson(), UsageInfo.class));
        } else {
            chargeFormReq.setUsageInfo(JsonUtils.readValue(chargeForm.getUsageInfo(), UsageInfo.class));
        }


        if (chargeForm.getChargeAirPharmacyLogistics() != null) {
            CalculateLogisticsReq logisticsReq = new CalculateLogisticsReq();

            BeanUtils.copyProperties(chargeForm.getChargeAirPharmacyLogistics(), logisticsReq);
            logisticsReq.setLogisticsCompanyId(chargeForm.getChargeAirPharmacyLogistics().getDeliveryCompanyId());
            chargeFormReq.setLogistics(logisticsReq);
        }

        if (!CollectionUtils.isEmpty(chargeForm.getChargeFormItems())) {

            List<OrderItemReq> orderItemReqs = chargeForm.getChargeFormItems()
                    .stream()
                    .filter(item -> item.getIsDeleted() == 0)
                    .filter(chargeFormItem -> chargeFormItem.getProductType() != Constants.ProductType.PROCESS
                            && chargeFormItem.getProductType() != Constants.ProductType.EXPRESS_DELIVERY
                            && chargeFormItem.getProductType() != Constants.ProductType.INGREDIENT)
                    .filter(chargeFormItem -> chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.UNCHARGED)
                    .filter(chargeFormItem -> chargeFormItem.getIsAirPharmacy() == 1)
                    .map(chargeFormItem -> {
                        OrderItemReq orderItemReq = new OrderItemReq();

                        orderItemReq.setSourceItemId(chargeFormItem.getId());
                        orderItemReq.setGoodsId(chargeFormItem.getProductId());
                        orderItemReq.setGoodsName(chargeFormItem.getName());
                        orderItemReq.setDoseCount(chargeFormItem.getDoseCount().intValue());
                        orderItemReq.setUnitPrice(chargeFormItem.getUnitPrice());
                        orderItemReq.setUnit(chargeFormItem.getUnit());
                        orderItemReq.setUnitCount(chargeFormItem.getUnitCount());
                        orderItemReq.setTotalPrice(chargeFormItem.getTotalPrice());

                        return orderItemReq;
                    }).collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(orderItemReqs)) {
                chargeFormReq.setOrderItems(orderItemReqs);
            }

        }


        return chargeFormReq;
    }

    public static PatientPointsInfoView convertToPatientPointsInfoView(ChargePatientPointsPromotionInfo patientPointsPromotionInfo) {

        if (patientPointsPromotionInfo == null || !patientPointsPromotionInfo.getChecked() || patientPointsPromotionInfo.getIsDeleted() == 1) {
            return null;
        }

        PatientPointsInfoView patientPointsInfoView = new PatientPointsInfoView();
        return patientPointsInfoView.setChecked(patientPointsPromotionInfo.getChecked())
                .setTotalPoints(patientPointsPromotionInfo.getTotalPoints())
                .setMaxDeductionPrice(patientPointsPromotionInfo.getMaxDeductionPrice())
                .setCheckedDeductionPrice(patientPointsPromotionInfo.getCheckedDeductionPrice())
                .setPointsDeductionRat(patientPointsPromotionInfo.getPointsDeductionRat());

    }

    public static void convertToChargeFormItemExtend(ChargeSheet chargeSheet) {
        if (chargeSheet == null || CollectionUtils.isEmpty(chargeSheet.getChargeForms())) {
            return;
        }

        chargeSheet.getChargeForms()
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .forEach(chargeForm -> {
                    if (chargeForm == null || CollectionUtils.isEmpty(chargeForm.getChargeFormItems())) {
                        return;
                    }

                    List<ChargeFormItem> chargeFormItems = chargeForm.getChargeFormItems();
                    chargeForm.setChargeFormItems(chargeFormItems.stream()
                            .filter(item -> item.getIsDeleted() == 0)
                            .map(chargeFormItem -> {
                                if (chargeFormItem == null) {
                                    return null;
                                }
                                ChargeFormItemExtend chargeFormItemExtend = new ChargeFormItemExtend();
                                BeanUtils.copyProperties(chargeFormItem, chargeFormItemExtend);
                                return chargeFormItemExtend;
                            }).filter(Objects::nonNull).collect(Collectors.toList()));
                });
    }

    public static ChargeFormItem generateGoodsFeeTypeChildRefundItem(ChargeFormItem parentToRefundChargeFormItem, BigDecimal parentUnitCount, ChargeFormItem childItem) {
        if (Objects.isNull(parentToRefundChargeFormItem) || Objects.isNull(childItem)) {
            return null;
        }

        ChargeFormItem childToRefundChargeFormItem = new ChargeFormItem();
        childToRefundChargeFormItem.setId(childItem.getId());
        childToRefundChargeFormItem.setPatientOrderId(childItem.getPatientOrderId());
        childToRefundChargeFormItem.setComposeParentFormItemId(childItem.getComposeParentFormItemId());
        childToRefundChargeFormItem.setChainId(childItem.getChainId());
        childToRefundChargeFormItem.setClinicId(childItem.getClinicId());
        childToRefundChargeFormItem.setDoseCount(childItem.getDoseCount());
        childToRefundChargeFormItem.setName(childItem.getName());
        //计算费用子项要退的数量
        BigDecimal parentToRefundUnitCount = parentToRefundChargeFormItem.getUnitCount();

        if (MathUtils.wrapBigDecimalCompare(parentToRefundUnitCount, BigDecimal.ZERO) == 0 || MathUtils.wrapBigDecimalCompare(parentUnitCount, BigDecimal.ZERO) == 0) {
            childToRefundChargeFormItem.setUnitCount(BigDecimal.ZERO);
        } else {
            BigDecimal childToRefundUnitCount = childItem.getUnitCount().divide(parentUnitCount, 4, RoundingMode.HALF_UP).multiply(parentToRefundUnitCount).setScale(2, RoundingMode.HALF_UP);
            childToRefundUnitCount = MathUtils.min(childToRefundUnitCount, childItem.getUnitCount());
            childToRefundChargeFormItem.setUnitCount(childToRefundUnitCount);
        }

        BigDecimal parentToRefundDeductCount = parentToRefundChargeFormItem.getDeductTotalCount();
        if (MathUtils.wrapBigDecimalCompare(parentToRefundDeductCount, BigDecimal.ZERO) == 0 || MathUtils.wrapBigDecimalCompare(childItem.getDeductTotalCount(), BigDecimal.ZERO) == 0) {
            childToRefundChargeFormItem.setDeductTotalCount(BigDecimal.ZERO);
        } else {
            BigDecimal childToRefundDeductCount = childItem.getUnitCount().divide(parentUnitCount, 4, RoundingMode.HALF_UP).multiply(parentToRefundDeductCount).setScale(2, RoundingMode.HALF_UP);
            childToRefundDeductCount = MathUtils.min(childToRefundDeductCount, childItem.getDeductTotalCount());
            childToRefundChargeFormItem.setDeductTotalCount(childToRefundDeductCount);
        }

        BigDecimal parentToRefundVerifyCount = parentToRefundChargeFormItem.getVerifyTotalCount();
        if (MathUtils.wrapBigDecimalCompare(parentToRefundVerifyCount, BigDecimal.ZERO) == 0 || MathUtils.wrapBigDecimalCompare(childItem.getVerifyTotalCount(), BigDecimal.ZERO) == 0) {
            childToRefundChargeFormItem.setVerifyTotalCount(BigDecimal.ZERO);
        } else {
            BigDecimal childToRefundVerifyCount = childItem.getUnitCount().divide(parentUnitCount, 4, RoundingMode.HALF_UP).multiply(parentToRefundVerifyCount).setScale(2, RoundingMode.HALF_UP);
            childToRefundVerifyCount = MathUtils.min(childToRefundVerifyCount, childItem.getVerifyTotalCount());
            childToRefundChargeFormItem.setVerifyTotalCount(childToRefundVerifyCount);
        }

        return childToRefundChargeFormItem;
    }
}
