package cn.abcyun.cis.charge.service.dto;

import cn.abcyun.bis.rpc.sdk.property.model.MedicalDocumentsTreatmentContent;
import cn.abcyun.bis.rpc.sdk.property.model.PrintMedicalDocumentsInfusion;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class PrintMedicalDocumentsInfusionAndTreatmentProperty {
    public PrintMedicalDocumentsInfusion infusion;

    public MedicalDocumentsTreatmentContent treatmentContent;

}
