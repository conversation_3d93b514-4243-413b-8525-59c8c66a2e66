package cn.abcyun.cis.charge.service;

import cn.abcyun.bis.rpc.sdk.cis.message.gsp.GspRegisterInfoUpdateMessage;
import cn.abcyun.bis.rpc.sdk.cis.message.medicalplan.MedicalPlanSheetSyncMessage;
import cn.abcyun.bis.rpc.sdk.cis.message.wechatpay.WechatChainOpenAllInPayMessage;
import cn.abcyun.bis.rpc.sdk.cis.model.charge.ChargeSheetRegisterInfoUpdateMessage;
import cn.abcyun.bis.rpc.sdk.cis.model.charge.ChargeSheetRegisterInfoView;
import cn.abcyun.bis.rpc.sdk.cis.model.copharmacy.CoPharmacyOutpatientSheet;
import cn.abcyun.bis.rpc.sdk.cis.model.gsp.register.GspRegisterInfoView;
import cn.abcyun.bis.rpc.sdk.cis.model.medicalplan.MedicalPlan;
import cn.abcyun.bis.rpc.sdk.cis.model.medicalplan.MedicalPlanConstant;
import cn.abcyun.bis.rpc.sdk.cis.model.medicalplan.MedicalPlanSheet;
import cn.abcyun.bis.rpc.sdk.cis.model.outpatient.MedicalRecord;
import cn.abcyun.bis.rpc.sdk.cis.model.pay.PayStatus;
import cn.abcyun.bis.rpc.sdk.cis.model.promotion.CardPatientPaidSuccessReq;
import cn.abcyun.bis.rpc.sdk.cis.model.promotion.CardPatientPaidSuccessRsp;
import cn.abcyun.cis.charge.amqp.HAMQProducer;
import cn.abcyun.cis.charge.amqp.RocketMqProducer;
import cn.abcyun.cis.charge.amqp.model.*;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.base.InnerPayModes;
import cn.abcyun.cis.charge.model.*;
import cn.abcyun.cis.charge.processor.CloseOrderRsp;
import cn.abcyun.cis.charge.processor.provider.ChargePayHandleProvider;
import cn.abcyun.cis.charge.repository.ChargePayModeRelationRepository;
import cn.abcyun.cis.charge.repository.ChargePayTransactionRepository;
import cn.abcyun.cis.charge.repository.ChargeSheetAdditionalRepository;
import cn.abcyun.cis.charge.repository.ChargeSheetRepository;
import cn.abcyun.cis.charge.service.dto.RefundByChargeRefundItemResultDto;
import cn.abcyun.cis.charge.service.rpc.CisPromotionService;
import cn.abcyun.cis.charge.service.rpc.CisShebaoService;
import cn.abcyun.cis.charge.util.ChargeUtils;
import cn.abcyun.cis.charge.util.SendMessageUtils;
import cn.abcyun.cis.commons.amqp.message.ChargeMessage;
import cn.abcyun.cis.commons.amqp.message.CoPatientMessage;
import cn.abcyun.cis.commons.amqp.message.charge.ChargeSheetPaidSuccessNeedInformBusinessMessage;
import cn.abcyun.cis.commons.amqp.message.charge.OnlineChargeSheetPushAgainDelayMessage;
import cn.abcyun.cis.commons.amqp.message.patient.merge.CoPatientMergeTaskMessage;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.exception.ServiceInternalException;
import cn.abcyun.cis.commons.message.ToCMessage;
import cn.abcyun.cis.commons.rpc.crm.PatientInfo;
import cn.abcyun.cis.commons.rpc.crm.PatientMemberRechargeReq;
import cn.abcyun.cis.commons.rpc.crm.PatientMemberRechargeRsp;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrder;
import cn.abcyun.cis.commons.util.FillUtils;
import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.cis.commons.util.MathUtils;
import cn.abcyun.cis.idgenerator.AbcIdGenerator;
import cn.abcyun.common.log.marker.AbcLogMarker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@DependsOn("chargePayModeConfiguration")
public class MQMessageHandleService {

    private final ChargeSheetService chargeSheetService;
    private final TocMessageService tocMessageService;
    private final CrmService crmService;
    private final RocketMqProducer rocketMqProducer;
    private final CisPromotionService cisPromotionService;
    private final ChargePayModeRelationRepository chargePayModeRelationRepository;
    private final AbcIdGenerator abcIdGenerator;
    private final CisShebaoService cisShebaoService;
    private final ChargePayTransactionRepository chargePayTransactionRepository;
    private final ChargeService chargeService;
    private final HAMQProducer hamqProducer;
    private final ChargeRefundTaskService chargeRefundTaskService;
    private final ChargeRefundTaskAssembleService chargeRefundTaskAssembleService;
    private final ChargeSheetAdditionalRepository chargeSheetAdditionalRepository;
    private final ChargeMedicalPlanService chargeMedicalPlanService;
    private final ChargeService mChargeService;
    private final ChargeCooperationOrderService chargeCooperationOrderService;
    private Map<String, ChargePayHandleProvider> chargePayHandleProviderMap = new HashMap<>();
    private final List<ChargePayHandleProvider> chargePayHandleProviders;
    private final ChargeSheetRepository chargeSheetRepository;

    @PostConstruct
    public void initChargePayHandleProviderMap() {

        if (org.springframework.util.CollectionUtils.isEmpty(chargePayHandleProviders)) {
            return;
        }

        chargePayHandleProviderMap = chargePayHandleProviders.stream().collect(Collectors.toMap(ChargePayHandleProvider::getPayModeKey, Function.identity(), (a, b) -> a));
    }
    @Autowired
    public MQMessageHandleService(ChargeSheetService chargeSheetService,
                                  TocMessageService tocMessageService,
                                  CrmService crmService,
                                  RocketMqProducer rocketMqProducer, CisPromotionService cisPromotionService,
                                  ChargePayModeRelationRepository chargePayModeRelationRepository,
                                  AbcIdGenerator abcIdGenerator,
                                  CisShebaoService cisShebaoService,
                                  ChargePayTransactionRepository chargePayTransactionRepository,
                                  ChargeService chargeService,
                                  ChargeSheetAdditionalRepository chargeSheetAdditionalRepository,
                                  HAMQProducer hamqProducer,
                                  ChargeRefundTaskService chargeRefundTaskService,
                                  ChargeRefundTaskAssembleService chargeRefundTaskAssembleService,
                                  ChargeMedicalPlanService chargeMedicalPlanService,
                                  ChargeService mChargeService,
                                  ChargeCooperationOrderService chargeCooperationOrderService,
                                  List<ChargePayHandleProvider> chargePayHandleProviders,
                                  ChargeSheetRepository chargeSheetRepository) {
        this.chargeSheetService = chargeSheetService;
        this.tocMessageService = tocMessageService;
        this.crmService = crmService;
        this.rocketMqProducer = rocketMqProducer;
        this.cisPromotionService = cisPromotionService;
        this.chargePayModeRelationRepository = chargePayModeRelationRepository;
        this.abcIdGenerator = abcIdGenerator;
        this.cisShebaoService = cisShebaoService;
        this.chargePayTransactionRepository = chargePayTransactionRepository;
        this.chargeService = chargeService;
        this.chargeSheetAdditionalRepository = chargeSheetAdditionalRepository;
        this.hamqProducer = hamqProducer;
        this.chargeRefundTaskService = chargeRefundTaskService;
        this.chargeRefundTaskAssembleService = chargeRefundTaskAssembleService;
        this.chargeMedicalPlanService = chargeMedicalPlanService;
        this.mChargeService = mChargeService;
        this.chargeCooperationOrderService = chargeCooperationOrderService;
        this.chargePayHandleProviders = chargePayHandleProviders;
        this.chargeSheetRepository = chargeSheetRepository;
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleOnlineChargeSheetPushAgainDelayMessage(OnlineChargeSheetPushAgainDelayMessage message) {

        ChargeSheet chargeSheet = chargeSheetService.findByIdAndClinicId(message.getChargeSheetId(), message.getClinicId());

        if (chargeSheet == null) {
            log.info("chargeSheet is null, chargeSheetId: {}", message.getChargeSheetId());
        }

        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED) {
            log.info("chargeSheet.status != 0, status: {}", chargeSheet.getStatus());
            return;
        }

        if (chargeSheet.getIsOnline() != 1) {
            log.info("chargeSheet.isOnline != 1");
            return;
        }

        if (chargeSheet.getCheckStatus() != ChargeSheet.CheckStatus.NEED_PATIENT_CHECK) {
            log.info("chargeSheet.checkStatus != 1, checkStatus: {}", chargeSheet.getCheckStatus());
            return;
        }

        PatientInfo patientInfo = crmService.searchPatientInfoByPatientIdFromCrm(chargeSheet.getChainId(), chargeSheet.getClinicId(), chargeSheet.getPatientId(), false, false);

        ToCMessage toCMessage = tocMessageService.generateConsultationAddDeliveryInfoMessage(chargeSheet, Optional.ofNullable(patientInfo).map(PatientInfo::getName).orElse(""), message.getOperatorId());
        rocketMqProducer.sendToCMessage(toCMessage);
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleChargeSheetPaidSuccessNeedInformBusinessMessage(ChargeSheetPaidSuccessNeedInformBusinessMessage messageBody) {
        switch (messageBody.getType()) {
            case ChargeSheet.Type.MEMBER_RECHARGE:
                postCrmMemberRechargePaySuccess(messageBody);
                return;
            case ChargeSheet.Type.PROMOTION_CARD_OPEN:
                postPromotionOpenCardPaySuccess(messageBody);
                return;
            case ChargeSheet.Type.PROMOTION_CARD_RECHARGE:
                postPromotionCardRechargePaySuccess(messageBody);
                return;
        }
    }

    /**
     * 通知promotion服务卡项充值成功
     *
     * @param messageBody
     */
    private void postPromotionCardRechargePaySuccess(ChargeSheetPaidSuccessNeedInformBusinessMessage messageBody) {
        CardPatientPaidSuccessRsp rsp = null;

        try {
            CardPatientPaidSuccessReq req = generateCardPatientPaidSuccessReq(messageBody);
            rsp = cisPromotionService.rechargePaySuccess(req);
        } catch (Exception e) {
            log.error("cisPromotionService.rechargePaySuccess 处理失败, message: {}", e.getMessage());
        }

        int code = Optional.ofNullable(rsp).map(CardPatientPaidSuccessRsp::getCode).orElse(CardPatientPaidSuccessRsp.Code.FAILED);

        if (code != CardPatientPaidSuccessRsp.Code.SUCCESS) {
            log.error("promotion卡充值回调处理失败");
            String content = String.format("chargeSheetId: %s", messageBody.getId());
            String title = "卡项充值promotion处理失败";
            rocketMqProducer.sendServiceAlertMessage(title, content, JsonUtils.dumpAsJsonNode(messageBody));
        }
    }

    /**
     * 通知promotion服务会员开卡成功
     *
     * @param messageBody
     */
    private void postPromotionOpenCardPaySuccess(ChargeSheetPaidSuccessNeedInformBusinessMessage messageBody) {
        CardPatientPaidSuccessRsp rsp = null;
        try {
            CardPatientPaidSuccessReq req = generateCardPatientPaidSuccessReq(messageBody);
            rsp = cisPromotionService.openCardPaySuccess(req);
        } catch (Exception e) {
            log.error("cisPromotionService.openCardPaySuccess 处理失败, message: {}", e.getMessage());
        }
        int code = Optional.ofNullable(rsp).map(CardPatientPaidSuccessRsp::getCode).orElse(CardPatientPaidSuccessRsp.Code.FAILED);

        if (code != CardPatientPaidSuccessRsp.Code.SUCCESS) {
            log.error("promotion开卡支付回调处理失败");

            String content = String.format("chargeSheetId: %s", messageBody.getId());
            String title = "患者开卡promotion处理失败";
            rocketMqProducer.sendServiceAlertMessage(title, content, JsonUtils.dumpAsJsonNode(messageBody));
        }
    }

    private CardPatientPaidSuccessReq generateCardPatientPaidSuccessReq(ChargeSheetPaidSuccessNeedInformBusinessMessage messageBody) {
        CardPatientPaidSuccessReq req = new CardPatientPaidSuccessReq();
        req.setChainId(messageBody.getChainId());
        req.setClinicId(messageBody.getClinicId());
        req.setChargeSheetId(messageBody.getId());
        req.setTransactionId(messageBody.getChargeTransactionId());
        req.setThirdPartTransactionId(messageBody.getThirdPartTransactionId());
        req.setPayMode(messageBody.getPayMode());
        req.setAmount(messageBody.getAmount());
        req.setPatientId(messageBody.getPatientId());
        req.setPayStatus(CardPatientPaidSuccessReq.PayStatus.SUCCESS);

        return req;
    }

    /**
     * 通知crm服务会员卡充值成功
     *
     * @param messageBody
     */
    private void postCrmMemberRechargePaySuccess(ChargeSheetPaidSuccessNeedInformBusinessMessage messageBody) {
        PatientMemberRechargeRsp patientMemberRechargeRsp = null;
        try {
            PatientMemberRechargeReq patientMemberRechargeReq = new PatientMemberRechargeReq();

            patientMemberRechargeReq.setChainId(messageBody.getChainId());
            patientMemberRechargeReq.setClinicId(messageBody.getClinicId());
            patientMemberRechargeReq.setPatientId(messageBody.getPatientId());
            patientMemberRechargeReq.setPatientOrderId(messageBody.getPatientOrderId());

            patientMemberRechargeReq.setPayMode(messageBody.getPayMode());
            patientMemberRechargeReq.setPaySubMode(messageBody.getPaySubMode());
            patientMemberRechargeReq.setPayStatus(PatientMemberRechargeReq.PayStatus.SUCCESS);
            patientMemberRechargeReq.setChargeTransactionId(messageBody.getChargeTransactionId());

            patientMemberRechargeReq.setAmount(messageBody.getAmount());
            patientMemberRechargeReq.setThirdPartTransactionId(messageBody.getThirdPartTransactionId());

            patientMemberRechargeRsp = crmService.rpcPostCrmMemberRechargePaySuccess(messageBody.getId(), patientMemberRechargeReq);
        } catch (Exception e) {
            log.error("crmService.rpcPostCrmMemberRechargePaySuccess 处理失败, message: {}", e.getMessage());
        }

        if (patientMemberRechargeRsp == null || patientMemberRechargeRsp.getRet() != PatientMemberRechargeRsp.RetCode.SUCCESS) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "crm会员充值处理失败");
            String content = String.format("chargeSheetId: %s", messageBody.getId());
            String title = "会员充值crm处理失败";
            rocketMqProducer.sendServiceAlertMessage(title, content, JsonUtils.dumpAsJsonNode(messageBody));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleWechatChainOpenAllInPayMessage(WechatChainOpenAllInPayMessage message) {

        if (message == null || StringUtils.isEmpty(message.getChainId())) {
            return;
        }

        //判断总店是否已经有了abc支付方式的配置
        boolean isExistedAbcPayMode = chargePayModeRelationRepository.existsByChainIdAndPayModeConfigIdAndIsDeleted(message.getChainId(), (long) Constants.ChargePayMode.ABC_PAY, 0);

        if (isExistedAbcPayMode) {
            return;
        }

        ChargePayModeRelation chargePayModeRelation = new ChargePayModeRelation();
        chargePayModeRelation.setId(abcIdGenerator.getUUID());
        chargePayModeRelation.setPayModeConfigId((long) Constants.ChargePayMode.ABC_PAY);
        chargePayModeRelation.setClinicId(message.getChainId());
        chargePayModeRelation.setChainId(message.getChainId());
        chargePayModeRelation.setIsEnable(1);
        chargePayModeRelation.setSort(-10);
        FillUtils.fillCreatedBy(chargePayModeRelation, Constants.ANONYMOUS_PATIENT_ID);

        chargePayModeRelationRepository.save(chargePayModeRelation);
    }

//    @Transactional(rollbackFor = Exception.class)
//    public void updateChargeSheetShebaoExceptionType(ChargeSheetMessage.ChargeSheet messageChargeSheet, String operatorId) {
//
//        if (messageChargeSheet == null) {
//            return;
//        }
//
//        ChargeSheet chargeSheet = chargeSheetService.findByIdContainIsDeleted(messageChargeSheet.getId());
//
//        if (chargeSheet == null) {
//            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeSheet is null");
//            return;
//        }
//
//        int queryExceptionType = chargeSheet.getQueryExceptionType();
//
//        if ((queryExceptionType & Constants.ChargeSheetQueryExceptionType.SHEBAO_PAY) == 0) {
//            return;
//        }
//
//        //判断是否支付过社保，如果没有过支付记录，就不用去查社保了
//        boolean existsShebaoPayTransaction = chargePayTransactionRepository.existsByClinicIdAndChargeSheetIdAndPayModeAndIsDeleted(chargeSheet.getClinicId(), chargeSheet.getId(), Constants.ChargePayMode.HEALTH_CARD, 0);
//
//        if (!existsShebaoPayTransaction) {
//            chargeSheet.setQueryExceptionType(queryExceptionType - Constants.ChargeSheetQueryExceptionType.SHEBAO_PAY);
//            FillUtils.fillLastModifiedBy(chargeSheet, operatorId);
//            return;
//        }
//
//        //查询是否有社保异常
//        List<String> containShebaoExceptionIds = cisShebaoService.queryContainShebaoExceptionList(chargeSheet.getId(), chargeSheet.getId(), Arrays.asList(chargeSheet.getId()));
//
//        if (CollectionUtils.isEmpty(containShebaoExceptionIds)) {
//            chargeSheet.setQueryExceptionType(queryExceptionType - Constants.ChargeSheetQueryExceptionType.SHEBAO_PAY);
//            FillUtils.fillLastModifiedBy(chargeSheet, operatorId);
//        }
//    }

    /**
     * 更新收费单的诊断信息
     *
     * @param medicalRecord 病例信息
     * @param operatorId
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateChargeSheetForMedicalRecordUpdate(MedicalRecord medicalRecord, String operatorId) {

        if (Objects.isNull(medicalRecord)) {
            return;
        }

        operatorId = StringUtils.isNotBlank(operatorId) ? operatorId : Constants.ANONYMOUS_PATIENT_ID;

        String chainId = medicalRecord.getChainId();
        String patientOrderId = medicalRecord.getPatientOrderId();

        String diagnosisInfosStr = CollectionUtils.isNotEmpty(medicalRecord.getDiagnosisInfos()) ? JsonUtils.dump(medicalRecord.getDiagnosisInfos()) : null;
        String extendDiagnosisInfosStr = CollectionUtils.isNotEmpty(medicalRecord.getExtendDiagnosisInfos()) ? JsonUtils.dump(medicalRecord.getExtendDiagnosisInfos()) : null;

        chargeSheetAdditionalRepository.updateForMedicalRecord(medicalRecord.getChiefComplaint(),
                medicalRecord.getDiagnosis(),
                diagnosisInfosStr,
                extendDiagnosisInfosStr,
                patientOrderId,
                chainId,
                operatorId);

    }

    @Transactional(rollbackFor = Exception.class)
    public void handleChargeRefundTaskStartMessage(ChargeRefundTaskStartMessage message) {

        if (Objects.isNull(message) || StringUtils.isAnyEmpty(message.getClinicId(), message.getId(), message.getTaskItemId())) {
            return;
        }

        ChargeRefundTask chargeRefundTask = chargeRefundTaskAssembleService.findById(message.getClinicId(), message.getId());

        if (Objects.isNull(chargeRefundTask)) {
            log.info("chargeRefundTask is null");
            throw new NotFoundException();
        }

        //校验状态是否正确
        if (chargeRefundTask.getStatus() != ChargeRefundTask.Status.REFUNDING && chargeRefundTask.getStatus() != ChargeRefundTask.Status.PART_REFUNDED_AND_NEXT_REFUNDING) {
            log.info("chargeRefundTask status not need handle, status: {}", chargeRefundTask.getStatus());
            return;
        }

        //找到本次需要退费的子任务
        ChargeRefundTaskItem chargeRefundTaskItem = Optional.ofNullable(chargeRefundTask.getRefundTaskItems())
                .orElse(new ArrayList<>())
                .stream()
                .filter(item -> StringUtils.equals(item.getId(), message.getTaskItemId()))
                .findFirst()
                .orElse(null);

        if (Objects.isNull(chargeRefundTaskItem)) {
            log.info("chargeRefundTaskItem is null");
            throw new NotFoundException();
        }

        if (chargeRefundTaskItem.getStatus() != ChargeRefundTaskItem.Status.WAIT_REFUND) {
            log.info("chargeRefundTaskItem status not need handle, status: {}", chargeRefundTaskItem.getStatus());
            return;
        }

        //校验该子任务之前的所有任务是否都处理完成
        if (chargeRefundTaskItem.getSort() > 0) {
            boolean frontItemIsAllRefund = Optional.ofNullable(chargeRefundTask.getRefundTaskItems())
                    .orElse(new ArrayList<>())
                    .stream()
                    .filter(item -> item.getSort() < chargeRefundTaskItem.getSort())
                    .allMatch(item -> item.getStatus() == ChargeRefundTaskItem.Status.REFUND_SUCCESS);

            if (!frontItemIsAllRefund) {
                log.info("有前置任务未完成，不允许退费");
                throw new ServiceInternalException("有前置任务未完成，不允许退费");
            }
        }

        //根据子任务发起退费
        RefundByChargeRefundItemResultDto refundResult = chargeRefundTaskService.doRefundByTaskItem(chargeRefundTaskItem, message.getOperatorId());

        if (refundResult.getChargeRefundTaskItemStatus() == ChargeRefundTaskItem.Status.REFUND_SUCCESS) {
            chargeRefundTask.setRefundedAmount(MathUtils.wrapBigDecimalAdd(chargeRefundTask.getRefundedAmount(), chargeRefundTaskItem.getAmount()));
            boolean allRefunded = chargeRefundTask.getRefundTaskItems()
                    .stream()
                    .allMatch(item -> item.getStatus() == ChargeRefundTaskItem.Status.REFUND_SUCCESS);
            chargeRefundTask.setStatus(allRefunded ? ChargeRefundTask.Status.REFUND_SUCCESS : ChargeRefundTask.Status.PART_REFUNDED_AND_NEXT_REFUNDING);
            sendNextRefundTaskItemMessage(chargeRefundTask, chargeRefundTaskItem, message.getOperatorId());
        } else if (refundResult.getChargeRefundTaskItemStatus() == ChargeRefundTaskItem.Status.REFUNDING) {
            if (chargeRefundTask.getStatus() == ChargeRefundTask.Status.PART_REFUNDED_AND_NEXT_REFUNDING) {
                chargeRefundTask.setStatus(ChargeRefundTask.Status.PART_REFUNDED_AND_NEXT_REFUNDING);
            } else {
                chargeRefundTask.setStatus(ChargeRefundTask.Status.REFUNDING);
            }
        } else if (refundResult.getChargeRefundTaskItemStatus() == ChargeRefundTask.Status.REFUND_FAIL) {
            if (chargeRefundTask.getStatus() == ChargeRefundTask.Status.PART_REFUNDED_AND_NEXT_REFUNDING) {
                chargeRefundTask.setStatus(ChargeRefundTask.Status.PART_REFUND_SUCCESS_PART_REFUND_FAIL);
            } else {
                chargeRefundTask.setStatus(ChargeRefundTask.Status.REFUND_FAIL);
            }
            chargeRefundTask.setErrorMessage(refundResult.getChargeRefundTaskItemErrorMsg());
        }
        FillUtils.fillLastModifiedBy(chargeRefundTaskItem, message.getOperatorId());
    }

    private void sendNextRefundTaskItemMessage(ChargeRefundTask chargeRefundTask, ChargeRefundTaskItem chargeRefundTaskItem, String operatorId) {
        if (Objects.isNull(chargeRefundTask) || Objects.isNull(chargeRefundTaskItem)) {
            return;
        }

        if (StringUtils.isEmpty(chargeRefundTaskItem.getNextRefundItemId())) {
            return;
        }
        hamqProducer.sendRefundTaskStartMessageAfterTransactionCommit(chargeRefundTask, chargeRefundTaskItem.getNextRefundItemId(), operatorId);
    }

    /**
     * 咨询单转成收费单
     *
     * @param message
     */
    @Retryable(include = Exception.class, backoff = @Backoff(delay = 500))
    public void handleMedicalPlanMessage(ChargeMessage message) {

        if (message == null || message.getBusinessInfo() == null) {
            return;
        }

        MedicalPlanSheetSyncMessage medicalPlanSheetSyncMessage = JsonUtils.readValue(message.getBusinessInfo(), MedicalPlanSheetSyncMessage.class);
        MedicalPlan medicalPlan = Optional.ofNullable(medicalPlanSheetSyncMessage).map(MedicalPlanSheetSyncMessage::getMedicalPlan).orElse(null);
        PatientOrder patientOrder = message.getPatientOrder();

        chargeMedicalPlanService.handleMedicalPlanMessage(medicalPlan, patientOrder, message.getIsOutpatientDeleted() == 1, message.getOperatorId());
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateChargeSheetForMedicalPlanSheetSync(MedicalPlanSheetSyncMessage message) {

        if (message == null || message.getMedicalPlan() == null) {
            return;
        }

        MedicalPlan medicalPlan = message.getMedicalPlan();

        MedicalPlanSheet medicalPlanSheet = Optional.ofNullable(medicalPlan.getMedicalPlanSheets())
                .orElse(new ArrayList<>())
                .stream()
                .filter(m -> m.getStatus() == MedicalPlanConstant.MedicalPlanSheetStatus.NORMAL)
                .findFirst()
                .orElse(null);


        chargeMedicalPlanService.updateChargeSheetForMedicalPlanSheetSync(medicalPlanSheet, medicalPlan, message.getOperatorId());

    }

    @Transactional(rollbackFor = Exception.class)
    public void handleChargeSheetRegisterInfoUpdateMessage(ChargeSheetRegisterInfoUpdateMessage message) {

        if (Objects.isNull(message) || StringUtils.isAnyEmpty(message.getClinicId(), message.getChargeSheetId())) {
            log.info("message is null or clinicId,chargeSheetId is null");
            return;
        }

        Long registerInfoId = Optional.ofNullable(message.getRegisterInfoView())
                .map(ChargeSheetRegisterInfoView::getRegisterInfoId)
                .orElse(null);

        String operatorId = StringUtils.isNotEmpty(message.getOperatorId()) ? message.getOperatorId() : Constants.ANONYMOUS_PATIENT_ID;

        if (Objects.isNull(registerInfoId)) {
            log.info("registerInfoId is null");
            return;
        }

        ChargeSheetAdditional chargeSheetAdditional = chargeSheetAdditionalRepository.findByIdAndClinicIdAndIsDeleted(message.getChargeSheetId(), message.getClinicId(), 0);

        if (Objects.isNull(chargeSheetAdditional)) {
            log.info("chargeSheetAdditional is null");
            return;
        }

        ChargeSheetAdditionalExtendInfo extendInfo = Optional.ofNullable(chargeSheetAdditional.getExtendedInfo()).orElse(new ChargeSheetAdditionalExtendInfo());

        extendInfo.setRegisterInfoId(String.valueOf(registerInfoId));

        chargeSheetAdditional.setExtendedInfo(extendInfo);

        FillUtils.fillLastModifiedBy(chargeSheetAdditional, operatorId);
    }

    /**
     * 门诊单转成收费单
     *
     * @param message
     */
    @Retryable(include = Exception.class, backoff = @Backoff(delay = 500))
    public void handleOutpatientMessage(ChargeMessage message) {

        PatientOrder patientOrder = message.getPatientOrder();
        if (message.getType() == ChargeMessage.MSG_TYPE_CREATE_CHARGE || message.getType() == ChargeMessage.MSG_TYPE_UPDATE_CHARGE) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "handle event create of update charge...");
            if (message.getType() == ChargeMessage.MSG_TYPE_UPDATE_CHARGE && message.getIsOutpatientDeleted() == 1) {
                mChargeService.deleteOutpatientChargeSheet(patientOrder, message.getOperatorId());
            } else {
                mChargeService.insertOrUpdateChargeSheetByPatientOrder(patientOrder, message.getBusinessInfo(), message.getOperatorId());
            }
        } else {
            log.info("patientOrder == null");
        }
    }

    /**
     * 合作诊所生成药店的合作诊所处方消息
     */
    @Retryable(include = Exception.class, backoff = @Backoff(delay = 500))
    @Transactional(rollbackFor = Exception.class)
    public void handleCoPharmacyOutpatientSheet(ChargeMessage message) {

        CoPharmacyOutpatientSheet coPharmacyOutpatientSheet = JsonUtils.readValue(message.getBusinessInfo(), CoPharmacyOutpatientSheet.class);

        ChargeCoPharmacyOrderMessage chargeCoPharmacyOrderMessage = MessageConvertor.convertToChargeCoPharmacyOrderMessage(coPharmacyOutpatientSheet);

        chargeCooperationOrderService.insertOrUpdateChargeCooperationOrderForChargeCoPharmacyOrderMessage(chargeCoPharmacyOrderMessage, ChargeCooperationOrder.SourceType.OUTPATIENT);
    }

    public void handleCoPharmacyMedicalPlanSheet(CoPharmacyOutpatientSheet coPharmacyOutpatientSheet) {

        //todo

        chargeCooperationOrderService.insertOrUpdateChargeCooperationOrderForChargeCoPharmacyOrderMessage(null, ChargeCooperationOrder.SourceType.MEDICAL_PLAN);
    }

    public void handleCoPatientMessage(CoPatientMessage message) {
        //药店的chainId
        String chainId = message.getChainId();
        String sourcePatientId = message.getPatientId();

        if (StringUtils.isAnyBlank(chainId, sourcePatientId)) {
            log.info("chainId or sourcePatientId is empty, chainId: {}, sourcePatientId: {}", chainId, sourcePatientId);
            return;
        }

        cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientInfo patientInfo = ChargeUtils.concertToSdkPatientInfoFromCommonCisPatientInfo(message.getNewVal());

        if (Objects.isNull(patientInfo)) {
            log.info("patientInfo is null");
            return;
        }

        chargeCooperationOrderService.updateOrderPatientInfoBySourcePatientId(chainId, sourcePatientId, patientInfo);
    }

    public void handleCoPatientMergeTaskMessage(CoPatientMergeTaskMessage message) {

        String chainId = message.getChainId();

        if (StringUtils.isEmpty(chainId)) {
            throw new ParamRequiredException("chainId");
        }

        List<String> oldSourcePatientIds = message.getSourceIds();
        cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientInfo mergedPatientInfo = JsonUtils.readValue(JsonUtils.dump(message.getMergedPatientInfo()), cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientInfo.class);

        if (CollectionUtils.isEmpty(oldSourcePatientIds)) {
            log.info("oldSourcePatientIds is empty");
            throw new ParamRequiredException("sourceIds");
        }

        if (Objects.isNull(mergedPatientInfo)) {
            throw new ParamRequiredException("mergedPatientInfo");
        }

        String mergedPatientId = mergedPatientInfo.getId();

        if (StringUtils.isEmpty(mergedPatientId)) {
            throw new ParamRequiredException("mergedPatientInfo.id");
        }

        chargeCooperationOrderService.updateOrderPatientIdByPatientMerge(chainId, oldSourcePatientIds, mergedPatientId, mergedPatientInfo);
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleChargeRefundSuccessMessage(ChargeRefundSuccessMessage message) {
        chargeService.onRefundSuccessCallback(message.getChargeSheet(), message.getChargePayTransaction(), message.getOperatorId());
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleChargePayTransactionAutoCancelMessage(ChargePayTransactionAutoCancelMessage message) {

        String chargePayTransactionId = message.getChargePayTransactionId();
        String chargeSheetId = message.getChargeSheetId();
        String operatorId = message.getOperatorId();


        ChargePayTransaction chargePayTransaction = chargePayTransactionRepository.findByIdAndChargeSheetIdAndIsDeleted(chargePayTransactionId, chargeSheetId, 0);

        if (chargePayTransaction == null) {
            log.info("chargePayTransaction is null, chargePayTransactionId: {}", chargePayTransactionId);
            return;
        }

        if (chargePayTransaction.getPayType() == ChargePayTransaction.PayType.PAY) {
            ChargeSheet chargeSheet = chargeSheetService.findById(chargeSheetId);

            if (chargeSheet == null) {
                log.info("chargeSheet is null, chargeSheetId: {}", chargeSheetId);
                return;
            }

            mChargeService.unLockAndReleaseLockSheetSourceV2(chargeSheet, chargePayTransaction, true, operatorId);
        } else if (chargePayTransaction.getPayType() == ChargePayTransaction.PayType.REFUND || chargePayTransaction.getPayType() == ChargePayTransaction.PayType.PAIDBACK) {
            unLockRefundChargePayTransaction(chargePayTransaction, message, operatorId);
        }

    }

    public void unLockRefundChargePayTransaction(ChargePayTransaction chargePayTransaction, ChargePayTransactionAutoCancelMessage message, String operatorId) {

        if (chargePayTransaction.getExpireTime() == null) {
            log.info("没有过期时间，不解锁");
            return;
        }

        if (chargePayTransaction.getPayStatus() != PayStatus.WAITING) {
            log.info("支付状态不是等待支付，不解锁");
            return;
        }

        //这里时间比较要转换成秒，因为数据库中的时间字段只能存储到秒，但是内存中的Instant对象有毫秒值，如果直接compare，一定不相等
        if (message.getExpireTime() == null || chargePayTransaction.getExpireTime().getEpochSecond() < message.getExpireTime().getEpochSecond()) {
            log.info("过期时间比消息里面的过期时间小，不解锁");
            return;
        }

        if (!Constants.ChargePayMode.SHEBAO_PAY_MODES.contains(chargePayTransaction.getPayMode())) {
            log.info("非社保支付不处理关闭订单");
            return;
        }

        //这里不能直接比较大小，应该给5秒的容错，如果差值在5秒之类，都可以认为是正常的解锁
        if (chargePayTransaction.getExpireTime().getEpochSecond() - message.getExpireTime().getEpochSecond() > 5) {
            log.info("过期时间和消息里面的过期时间不一致，且时间超过5秒，说明被续期过，重新发送delay消息，messageExpireTime: {}, chargePayTransactionExpireTime: {}", message.getExpireTime(), chargePayTransaction.getExpireTime());
            message.setExpireTime(chargePayTransaction.getExpireTime());
            //用过期时间减去当前时间得到需要delay的秒数
            SendMessageUtils.sendChargePayTransactionCancelMessage(message, chargePayTransaction.getExpireTime().getEpochSecond() - Instant.now().getEpochSecond());
            return;
        }

        //通过chargePayMode或者实现类，调用实现类的关闭订单
        ChargePayHandleProvider chargePayHandleProvider = chargePayHandleProviderMap.getOrDefault(InnerPayModes.getPayModeKeyByPayMode(chargePayTransaction.getPayMode(), chargePayTransaction.getPaySubMode()), null);

        if (chargePayHandleProvider == null) {
            throw new ServiceInternalException("chargePayHandleProvider is null");
        }

        CloseOrderRsp closeOrderRsp = null;
        try {
            closeOrderRsp = chargePayHandleProvider.closeOrder(chargePayTransaction, operatorId);
        } catch (Exception e) {
            log.error("chargePayHandleProvider.closeOrder fail.", e);
        }

        if (closeOrderRsp == null) {
            closeChargePayTransaction(chargePayTransaction, operatorId);
            return;
        }

        //不管关闭是否成功，有异常标记异常
        if (closeOrderRsp.getIsHaveAbnormalByRefund() == 1) {
            chargePayTransaction.setPayStatus(cn.abcyun.cis.commons.rpc.pay.PayStatus.ERROR);
            FillUtils.fillLastModifiedBy(chargePayTransaction, operatorId);
            chargePayTransactionRepository.save(chargePayTransaction);
            chargeSheetRepository.markChargeSheetShebaoError(chargePayTransaction.getChargeSheetId(), Constants.ChargeSheetQueryExceptionType.SHEBAO_PAY, operatorId);
        } else {
            closeChargePayTransaction(chargePayTransaction, operatorId);
        }
    }

    private void closeChargePayTransaction(ChargePayTransaction chargePayTransaction, String operatorId) {
        //走解锁流程，更新payStatus
        chargePayTransaction.setPayStatus(PayStatus.CANCELED);
        FillUtils.fillLastModifiedBy(chargePayTransaction, operatorId);
        chargePayTransactionRepository.save(chargePayTransaction);
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleGspRegisterInfoUpdateMessage(GspRegisterInfoUpdateMessage message) {

        if (Objects.isNull(message) || StringUtils.isAnyEmpty(message.getClinicId(), message.getChargeSheetId())) {
            log.info("message is null or clinicId,chargeSheetId is null");
            return;
        }

        Long registerInfoId = Optional.ofNullable(message.getRegisterInfoView())
                .map(GspRegisterInfoView::getRegisterInfoId)
                .orElse(null);

        String operatorId = StringUtils.isNotEmpty(message.getOperatorId()) ? message.getOperatorId() : Constants.ANONYMOUS_PATIENT_ID;

        if (StringUtils.isEmpty(message.getChargeSheetId())) {
            log.info("chargeSheetId is null");
            return;
        }

        if (Objects.isNull(registerInfoId)) {
            log.info("registerInfoId is null");
            return;
        }

        ChargeSheetAdditional chargeSheetAdditional = chargeSheetAdditionalRepository.findByIdAndClinicIdAndIsDeleted(message.getChargeSheetId(), message.getClinicId(), 0);

        if (Objects.isNull(chargeSheetAdditional)) {
            log.info("chargeSheetAdditional is null");
            return;
        }

        ChargeSheetAdditionalExtendInfo extendInfo = Optional.ofNullable(chargeSheetAdditional.getExtendedInfo()).orElse(new ChargeSheetAdditionalExtendInfo());

        extendInfo.setRegisterInfoId(String.valueOf(registerInfoId));

        chargeSheetAdditional.setExtendedInfo(extendInfo);

        FillUtils.fillLastModifiedBy(chargeSheetAdditional, operatorId);
    }
}
