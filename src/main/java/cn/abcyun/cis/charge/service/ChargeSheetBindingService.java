package cn.abcyun.cis.charge.service;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.cold.ChargeFormItemDaoProxy;
import cn.abcyun.cis.charge.model.*;
import cn.abcyun.cis.charge.repository.*;
import cn.abcyun.cis.charge.service.dto.ChargeSheetRelationDto;
import cn.abcyun.cis.charge.service.rpc.BisOrderService;
import cn.abcyun.cis.charge.util.ChargeSheetBindingUtils;
import cn.abcyun.cis.charge.util.ChargeUtils;
import cn.abcyun.cis.charge.util.ListUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.abcyun.cis.charge.util.ChargeSheetBindingUtils.fillChargeSheetFieldCore;
import static cn.abcyun.cis.charge.util.ChargeSheetBindingUtils.fillChargeSheetListFieldCore;

@Service
@Slf4j
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class ChargeSheetBindingService {

    private final ChargeFormRepository chargeFormRepository;

    private final ChargeFormItemDaoProxy chargeFormItemRepository;

    private final ChargeFormItemBatchInfoRepository chargeFormItemBatchInfoRepository;

    private final ChargeSheetAdditionalRepository chargeSheetAdditionalRepository;

    private final ChargeAdditionalFeeRepository chargeAdditionalFeeRepository;

    private final ChargeTransactionRepository chargeTransactionRepository;

    private final ChargeActionRepository chargeActionRepository;

    private final ChargeSheetAttachmentRepository chargeSheetAttachmentRepository;

    private final ChargeCouponPromotionInfoRepository chargeCouponPromotionInfoRepository;

    private final ChargeGiftRulePromotionInfoRepository chargeGiftRulePromotionInfoRepository;

    private final ChargePatientCardPromotionInfoRepository chargePatientCardPromotionInfoRepository;

    private final ChargeVerifyInfoRepository chargeVerifyInfoRepository;

    private final ChargePatientPointsPromotionInfoRepository chargePatientPointsPromotionInfoRepository;

    private final ChargePatientPointsDeductProductPromotionInfoRepository chargePatientPointsDeductProductPromotionInfoRepository;

    private final ChargeDeliveryTraceRepository chargeDeliveryTraceRepository;

    private final ChargeDeliveryInfoRepository chargeDeliveryInfoRepository;

    private final ChargeAirPharmacyLogisticsRepository chargeAirPharmacyLogisticsRepository;

    private final ChargeAirPharmacyMedicalRecordRepository chargeAirPharmacyMedicalRecordRepository;

    private final ChargeDeliveryCompanyRepository chargeDeliveryCompanyRepository;

    private final BisOrderService bisOrderService;

    private final ChargeSheetProcessInfoRepository chargeSheetProcessInfoRepository;

    private final ChargeChangePayModeRecordRepository chargeChangePayModeRecordRepository;

    public void bindFieldsForOne(ChargeSheet chargeSheet) {

        if (Objects.isNull(chargeSheet)) {
            return;
        }

        List<ChargeSheet> chargeSheets = Collections.singletonList(chargeSheet);
        appendChargeSheetField(chargeSheets);
    }

    public void bindFieldsForMany(List<ChargeSheet> chargeSheets) {
        appendChargeSheetField(chargeSheets);
    }

    private void appendChargeSheetField(List<ChargeSheet> chargeSheets) {

        if (CollectionUtils.isEmpty(chargeSheets)) {
            return;
        }

        //拿出收费单上的关联数据标记，有数据才去查，没有就不去查了
        Map<String, Integer> chargeSheetIdRelateDataFlagMap = new HashMap<>();
        chargeSheets.forEach(chargeSheet -> chargeSheetIdRelateDataFlagMap.put(chargeSheet.getId(), chargeSheet.getDataFlag()));

        bindChargeSheetAdditional(chargeSheets);

        bindChargeForms(chargeSheets, chargeSheetIdRelateDataFlagMap);
        bindAdditionalFees(chargeSheets, chargeSheetIdRelateDataFlagMap);
        bindChargeTransactions(chargeSheets);
        bindChargeActions(chargeSheets);
        bindAttachments(chargeSheets, chargeSheetIdRelateDataFlagMap);
        bindCouponPromotionInfos(chargeSheets, chargeSheetIdRelateDataFlagMap);
        bindGiftRulePromotionInfos(chargeSheets, chargeSheetIdRelateDataFlagMap);
        bindPatientCardPromotionInfos(chargeSheets, chargeSheetIdRelateDataFlagMap);
        bindPatientPointsPromotionInfo(chargeSheets, chargeSheetIdRelateDataFlagMap);
        bindPatientPointsDeductProductPromotionInfo(chargeSheets, chargeSheetIdRelateDataFlagMap);
        bindDeliveryInfo(chargeSheets);
        bindChangePayModeRecords(chargeSheets);
        bindVerifyInfos(chargeSheets, chargeSheetIdRelateDataFlagMap);
    }

    private void bindVerifyInfos(List<ChargeSheet> chargeSheets, Map<String, Integer> chargeSheetIdRelateDataFlagMap) {

        cn.abcyun.cis.commons.util.ListUtils.alwaysList(chargeSheets)
                .forEach(chargeSheet -> chargeSheet.setChargeVerifyInfos(new ArrayList<>()));

        if (!ChargeSheetBindingUtils.needQueryRelateData(chargeSheetIdRelateDataFlagMap, ChargeSheet.DataFlag.HAS_CHARGE_VERIFY_INFO)) {
            return;
        }

        fillChargeSheetListFieldCore(chargeSheets,
                chargeSheetIds -> Optional.ofNullable(chargeVerifyInfoRepository.findAllByChargeSheetIdIn(chargeSheetIds))
                        .orElse(new ArrayList<>())
                        .stream()
                        .collect(Collectors.groupingBy(ChargeVerifyInfo::getChargeSheetId)),
                ChargeSheet::setChargeVerifyInfos);
    }

    private void bindChangePayModeRecords(List<ChargeSheet> chargeSheets) {
        cn.abcyun.cis.commons.util.ListUtils.alwaysList(chargeSheets)
                .forEach(chargeSheet -> chargeSheet.setChangePayModeRecords(new ArrayList<>()));

        fillChargeSheetListFieldCore(chargeSheets,
                chargeSheetIds -> Optional.ofNullable(chargeChangePayModeRecordRepository.findAllByBusinessIdIn(chargeSheetIds))
                        .orElse(new ArrayList<>())
                        .stream()
                        .collect(Collectors.groupingBy(ChargeChangePayModeRecord::getBusinessId)),
                ChargeSheet::setChangePayModeRecords);
    }

    private void bindDeliveryInfo(List<ChargeSheet> chargeSheets) {
        if (CollectionUtils.isEmpty(chargeSheets)) {
            return;
        }

        String chainId = chargeSheets.get(0).getChainId();
        String clinicId = chargeSheets.get(0).getClinicId();

        List<String> needBindChargeSheetIds = chargeSheets.stream()
                .filter(chargeSheet -> chargeSheet.getDeliveryType() == ChargeSheet.DeliveryType.DELIVERY_TO_HOME)
                .map(ChargeSheet::getId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(needBindChargeSheetIds)) {
            return;
        }

        List<ChargeDeliveryInfo> chargeDeliveryInfos = chargeDeliveryInfoRepository.findAllByChainIdAndClinicIdAndChargeSheetIdInAndIsDeleted(chainId, clinicId, needBindChargeSheetIds, 0);

        if (CollectionUtils.isEmpty(chargeDeliveryInfos)) {
            return;
        }

        ChargeSheetBindingUtils.bindChargeDeliveryTrace(chainId, chargeDeliveryInfos, chargeDeliveryTraceRepository);
        Map<String, List<ChargeDeliveryInfo>> chargeDeliveryInfoMap = ListUtils.groupByKey(chargeDeliveryInfos, ChargeDeliveryInfo::getChargeSheetId);

        chargeSheets.stream()
                .filter(chargeSheet -> needBindChargeSheetIds.contains(chargeSheet.getId()))
                .forEach(chargeSheet -> ChargeSheetBindingUtils.bindChargeDeliveryInfoCore(chargeSheet, chargeDeliveryInfoMap.getOrDefault(chargeSheet.getId(), null)));

    }

    private void bindPatientPointsDeductProductPromotionInfo(List<ChargeSheet> chargeSheets, Map<String, Integer> chargeSheetIdRelateDataFlagMap) {

        if (!ChargeSheetBindingUtils.needQueryRelateData(chargeSheetIdRelateDataFlagMap, ChargeSheet.DataFlag.HAS_PATIENT_POINTS_DEDUCT_PRODUCT_PROMOTION_INFO)) {
            return;
        }

        String clinicId = CollectionUtils.isNotEmpty(chargeSheets) ? chargeSheets.get(0).getClinicId() : null;

        fillChargeSheetFieldCore(chargeSheets,
                chargeSheetIds -> Optional.ofNullable(chargePatientPointsDeductProductPromotionInfoRepository.findAllByClinicIdAndChargeSheetIdInAndIsDeleted(clinicId, chargeSheetIds, 0))
                        .orElse(new ArrayList<>())
                        .stream()
                        .collect(Collectors.toMap(ChargePatientPointsDeductProductPromotionInfo::getChargeSheetId, Function.identity(), (a, b) -> {
                            if (a.getCreated().isAfter(b.getCreated())) {
                                return a;
                            }
                            return b;
                        })),
                ChargeSheet::setPatientPointsDeductProductPromotionInfo);
    }

    private void bindPatientPointsPromotionInfo(List<ChargeSheet> chargeSheets, Map<String, Integer> chargeSheetIdRelateDataFlagMap) {

        if (!ChargeSheetBindingUtils.needQueryRelateData(chargeSheetIdRelateDataFlagMap, ChargeSheet.DataFlag.HAS_PATIENT_POINTS_PROMOTION_INFO)) {
            return;
        }

        fillChargeSheetFieldCore(chargeSheets,
                chargeSheetIds -> Optional.ofNullable(chargePatientPointsPromotionInfoRepository.findAllByChargeSheetIdInAndIsDeleted(chargeSheetIds, 0))
                        .orElse(new ArrayList<>())
                        .stream()
                        .collect(Collectors.toMap(ChargePatientPointsPromotionInfo::getChargeSheetId,
                                Function.identity(),
                                (a, b) -> {
                                    if (a.getCreated().isAfter(b.getCreated())) {
                                        return a;
                                    }
                                    return b;
                                })),
                ChargeSheet::setPatientPointsPromotionInfo);
    }

    private void bindPatientCardPromotionInfos(List<ChargeSheet> chargeSheets, Map<String, Integer> chargeSheetIdRelateDataFlagMap) {

        cn.abcyun.cis.commons.util.ListUtils.alwaysList(chargeSheets)
                .forEach(chargeSheet -> chargeSheet.setPatientCardPromotionInfos(new ArrayList<>()));

        if (!ChargeSheetBindingUtils.needQueryRelateData(chargeSheetIdRelateDataFlagMap, ChargeSheet.DataFlag.HAS_PATIENT_CARD_PROMOTION_INFO)) {
            return;
        }

        fillChargeSheetListFieldCore(chargeSheets,
                chargeSheetIds -> Optional.ofNullable(chargePatientCardPromotionInfoRepository.findAllByChargeSheetIdIn(chargeSheetIds))
                        .orElse(new ArrayList<>())
                        .stream()
                        .collect(Collectors.groupingBy(ChargePatientCardPromotionInfo::getChargeSheetId)),
                ChargeSheet::setPatientCardPromotionInfos);
    }


    private void bindGiftRulePromotionInfos(List<ChargeSheet> chargeSheets, Map<String, Integer> chargeSheetIdRelateDataFlagMap) {

        cn.abcyun.cis.commons.util.ListUtils.alwaysList(chargeSheets)
                .forEach(chargeSheet -> chargeSheet.setGiftRulePromotionInfos(new ArrayList<>()));

        if (!ChargeSheetBindingUtils.needQueryRelateData(chargeSheetIdRelateDataFlagMap, ChargeSheet.DataFlag.HAS_GIFT_RULE_PROMOTION_INFO)) {
            return;
        }

        fillChargeSheetListFieldCore(chargeSheets,
                chargeSheetIds -> Optional.ofNullable(chargeGiftRulePromotionInfoRepository.findAllByChargeSheetIdIn(chargeSheetIds))
                        .orElse(new ArrayList<>())
                        .stream()
                        .collect(Collectors.groupingBy(ChargeGiftRulePromotionInfo::getChargeSheetId)),
                ChargeSheet::setGiftRulePromotionInfos);
    }

    private void bindCouponPromotionInfos(List<ChargeSheet> chargeSheets, Map<String, Integer> chargeSheetIdRelateDataFlagMap) {

        cn.abcyun.cis.commons.util.ListUtils.alwaysList(chargeSheets)
                .forEach(chargeSheet -> chargeSheet.setCouponPromotionInfos(new ArrayList<>()));

        if (!ChargeSheetBindingUtils.needQueryRelateData(chargeSheetIdRelateDataFlagMap, ChargeSheet.DataFlag.HAS_COUPON_PROMOTION_INFO)) {
            return;
        }

        fillChargeSheetListFieldCore(chargeSheets,
                chargeSheetIds -> Optional.ofNullable(chargeCouponPromotionInfoRepository.findAllByChargeSheetIdIn(chargeSheetIds))
                        .orElse(new ArrayList<>())
                        .stream()
                        .collect(Collectors.groupingBy(ChargeCouponPromotionInfo::getChargeSheetId)),
                ChargeSheet::setCouponPromotionInfos);
    }

    private void bindAttachments(List<ChargeSheet> chargeSheets, Map<String, Integer> chargeSheetIdRelateDataFlagMap) {

        cn.abcyun.cis.commons.util.ListUtils.alwaysList(chargeSheets)
                .forEach(chargeSheet -> chargeSheet.setAttachments(new ArrayList<>()));

        if (!ChargeSheetBindingUtils.needQueryRelateData(chargeSheetIdRelateDataFlagMap, ChargeSheet.DataFlag.HAS_ATTACHMENT)) {
            return;
        }

        fillChargeSheetListFieldCore(chargeSheets,
                chargeSheetIds -> Optional.ofNullable(chargeSheetAttachmentRepository.findAllByChargeSheetIdInAndIsDeleted(chargeSheetIds, 0))
                        .orElse(new ArrayList<>())
                        .stream()
                        .collect(Collectors.groupingBy(ChargeSheetAttachment::getChargeSheetId)),
                ChargeSheet::setAttachments);
    }

    private void bindChargeActions(List<ChargeSheet> chargeSheets) {
        cn.abcyun.cis.commons.util.ListUtils.alwaysList(chargeSheets)
                .forEach(chargeSheet -> chargeSheet.setChargeActions(new ArrayList<>()));

        fillChargeSheetListFieldCore(chargeSheets,
                chargeSheetIds -> Optional.ofNullable(chargeActionRepository.findAllByChargeSheetIdInAndIsDeleted(chargeSheetIds, 0))
                        .orElse(new ArrayList<>())
                        .stream()
                        .collect(Collectors.groupingBy(ChargeAction::getChargeSheetId)),
                ChargeSheet::setChargeActions);
    }

    private void bindChargeTransactions(List<ChargeSheet> chargeSheets) {
        cn.abcyun.cis.commons.util.ListUtils.alwaysList(chargeSheets)
                .forEach(chargeSheet -> chargeSheet.setChargeTransactions(new ArrayList<>()));

        fillChargeSheetListFieldCore(chargeSheets,
                chargeSheetIds -> Optional.ofNullable(chargeTransactionRepository.findAllByChargeSheetIdInAndIsDeleted(chargeSheetIds, 0))
                        .orElse(new ArrayList<>())
                        .stream()
                        .collect(Collectors.groupingBy(ChargeTransaction::getChargeSheetId)),
                ChargeSheet::setChargeTransactions);
    }

    private void bindAdditionalFees(List<ChargeSheet> chargeSheets, Map<String, Integer> chargeSheetIdRelateDataFlagMap) {

        cn.abcyun.cis.commons.util.ListUtils.alwaysList(chargeSheets)
                .forEach(chargeSheet -> chargeSheet.setAdditionalFees(new ArrayList<>()));

        if (!ChargeSheetBindingUtils.needQueryRelateData(chargeSheetIdRelateDataFlagMap, ChargeSheet.DataFlag.HAS_ADDITIONAL_FEE)) {
            return;
        }

        fillChargeSheetListFieldCore(chargeSheets,
                chargeSheetIds -> Optional.ofNullable(chargeAdditionalFeeRepository.findAllByChargeSheetIdInAndIsDeleted(chargeSheetIds, 0))
                        .orElse(new ArrayList<>())
                        .stream()
                        .collect(Collectors.groupingBy(ChargeAdditionalFee::getChargeSheetId)),
                ChargeSheet::setAdditionalFees);

    }

    public void bindChargeSheetAdditional(List<ChargeSheet> chargeSheets) {
        fillChargeSheetFieldCore(chargeSheets,
                chargeSheetIds -> Optional.ofNullable(chargeSheetAdditionalRepository.findByIdInAndIsDeleted(chargeSheetIds, 0))
                        .orElse(new ArrayList<>())
                        .stream()
                        .collect(Collectors.toMap(ChargeSheetAdditional::getId, Function.identity(), (a, b) -> a)),
                ChargeSheet::setAdditional);
    }

    public void bindChargeForms(List<ChargeSheet> chargeSheets, Map<String, Integer> chargeSheetIdRelateDataFlagMap) {
        if (CollectionUtils.isEmpty(chargeSheets)) {
            return;
        }

        cn.abcyun.cis.commons.util.ListUtils.alwaysList(chargeSheets)
                .forEach(chargeSheet -> chargeSheet.setChargeForms(new ArrayList<>()));

        List<String> chargeSheetIds = chargeSheets.stream().map(ChargeSheet::getId).distinct().collect(Collectors.toList());

        List<ChargeForm> chargeForms = chargeFormRepository.findByChargeSheetIdInAndIsDeleted(chargeSheetIds, 0);

        if (CollectionUtils.isEmpty(chargeForms)) {
            return;
        }

        String chainId = chargeSheets.get(0).getChainId();
        String clinicId = chargeSheets.get(0).getClinicId();

        Map<String, List<ChargeForm>> chargeFormsSheetIdMap = chargeForms
                .stream()
                .collect(Collectors.groupingBy(ChargeForm::getChargeSheetId));

        chargeSheets.forEach(chargeSheet -> chargeSheet.setChargeForms(chargeFormsSheetIdMap.getOrDefault(chargeSheet.getId(), new ArrayList<>())));

        bindAirPharmacyForChargeSheet(chargeForms, chainId, clinicId);
        bindChargeFormProcessInfo(chargeForms);
        bindChargeFormItems(chargeForms, chargeSheetIdRelateDataFlagMap);
    }

    private void bindAirPharmacyForChargeSheet(List<ChargeForm> chargeForms, String chainId, String clinicId) {

        if (CollectionUtils.isEmpty(chargeForms)) {
            return;
        }

        List<String> airPharmacyChargeFormIds = chargeForms.stream()
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY)
                .map(ChargeForm::getId)
                .collect(Collectors.toList());

        List<String> virtualPharmacyChargeFormIds = chargeForms.stream()
                .filter(chargeForm -> chargeForm.getPharmacyType() == GoodsConst.PharmacyType.VIRTUAL_PHARMACY)
                .map(ChargeForm::getId)
                .collect(Collectors.toList());

        List<String> needAirPharmacyLogisticsFormIds = new ArrayList<>();

        needAirPharmacyLogisticsFormIds.addAll(airPharmacyChargeFormIds);
        needAirPharmacyLogisticsFormIds.addAll(virtualPharmacyChargeFormIds);

        if (CollectionUtils.isEmpty(needAirPharmacyLogisticsFormIds)) {
            return;
        }

        List<ChargeAirPharmacyLogistics> airPharmacyLogisticsList = new ArrayList<>();


        if (CollectionUtils.isNotEmpty(needAirPharmacyLogisticsFormIds)) {
            airPharmacyLogisticsList = chargeAirPharmacyLogisticsRepository.findAllByChargeFormIdInAndIsDeleted(needAirPharmacyLogisticsFormIds, 0);
        }
        if (airPharmacyLogisticsList == null) {
            airPharmacyLogisticsList = new ArrayList<>();
        }

        List<ChargeAirPharmacyMedicalRecord> airPharmacyMedicalRecords = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(airPharmacyChargeFormIds)) {
            airPharmacyMedicalRecords = chargeAirPharmacyMedicalRecordRepository.findAllByChargeFormIdInAndIsDeleted(airPharmacyChargeFormIds, 0);
        }
        if (airPharmacyMedicalRecords == null) {
            airPharmacyMedicalRecords = new ArrayList<>();
        }

        ChargeSheetBindingUtils.fillVirtualPharmacyLogisticsDeliveryCompany(airPharmacyLogisticsList, chainId, clinicId, chargeDeliveryCompanyRepository);
        ChargeSheetBindingUtils.fillAirPharmacyLogisticsDeliveryCompany(airPharmacyLogisticsList.stream().filter(airPharmacyLogistics -> airPharmacyChargeFormIds.contains(airPharmacyLogistics.getChargeFormId())).collect(Collectors.toList()), bisOrderService);

        Map<String, ChargeAirPharmacyLogistics> airPharmacyLogisticsMap = airPharmacyLogisticsList.stream()
                .sorted(Comparator.comparing(ChargeAirPharmacyLogistics::getCreated).reversed()) // 排序取最新
                .collect(Collectors.toMap(ChargeAirPharmacyLogistics::getChargeFormId, Function.identity(), (a, b) -> a));

        Map<String, ChargeAirPharmacyMedicalRecord> airPharmacyMedicalRecordMap = airPharmacyMedicalRecords.stream()
                .collect(Collectors.toMap(ChargeAirPharmacyMedicalRecord::getChargeFormId, Function.identity(), (a, b) -> a));


        chargeForms
                .forEach(chargeForm -> ChargeSheetBindingUtils.bindAirPharmacyForChargeSheetCore(chargeForm, airPharmacyLogisticsMap, airPharmacyMedicalRecordMap));
    }


    private void bindChargeFormProcessInfo(List<ChargeForm> chargeForms) {

        List<String> processChargeFormIds = chargeForms.stream()
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.PROCESS)
                .map(ChargeForm::getId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(processChargeFormIds)) {
            return;
        }

        List<ChargeSheetProcessInfo> chargeSheetProcessInfos = chargeSheetProcessInfoRepository.findAllByProcessFormIdInAndIsDeleted(processChargeFormIds, 0);

        if (CollectionUtils.isEmpty(chargeSheetProcessInfos)) {
            return;
        }

        Map<String, ChargeSheetProcessInfo> processInfoMap = chargeSheetProcessInfos.stream().collect(Collectors.toMap(ChargeSheetProcessInfo::getProcessFormId, Function.identity(), (a, b) -> a.getCreated().isAfter(b.getCreated()) ? a : b));

        bindChargeFormProcessInfoCore(chargeForms, processInfoMap);

    }

    private void bindChargeFormProcessInfoCore(List<ChargeForm> chargeForms, Map<String, ChargeSheetProcessInfo> processInfoMap) {
        chargeForms.stream()
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.PROCESS)
                .forEach(chargeForm -> chargeForm.setProcessInfo(processInfoMap.getOrDefault(chargeForm.getId(), null)));
    }

    private void bindChargeFormItems(List<ChargeForm> chargeForms, Map<String, Integer> chargeSheetIdRelateDataFlagMap) {

        if (CollectionUtils.isEmpty(chargeForms)) {
            return;
        }

        Map<String, List<ChargeForm>> chargeFormChargeSheetIdMap = chargeForms.stream()
                .collect(Collectors.groupingBy(ChargeForm::getChargeSheetId));

        List<String> chargeFormIds = chargeForms.stream().map(ChargeForm::getId).distinct().collect(Collectors.toList());

        List<ChargeFormItem> chargeFormItems = chargeFormItemRepository.findByChargeFormIdInAndIsDeleted(chargeFormIds, 0);

        Map<String, Map<String, List<ChargeFormItem>>> chargeFormItemFormIdMap = Optional.ofNullable(chargeFormItems)
                .orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.groupingBy(ChargeFormItem::getChargeSheetId, Collectors.groupingBy(ChargeFormItem::getChargeFormId)));

        chargeFormChargeSheetIdMap.forEach((chargeSheetId, chargeFormsFlag) -> {
            Map<String, List<ChargeFormItem>> chargeFormItemMap = chargeFormItemFormIdMap.getOrDefault(chargeSheetId, new HashMap<>());
            chargeFormsFlag.forEach(chargeForm -> chargeForm.setChargeFormItems(chargeFormItemMap.getOrDefault(chargeForm.getId(), new ArrayList<>())));
        });

        bindChargeFormItemBatchInfos(chargeFormItems, chargeSheetIdRelateDataFlagMap);
    }

    private void bindChargeFormItemBatchInfos(List<ChargeFormItem> chargeFormItems, Map<String, Integer> chargeSheetIdRelateDataFlagMap) {

        if (CollectionUtils.isEmpty(chargeFormItems)) {
            return;
        }

        //判断是否要查batchInfo
        if (!ChargeSheetBindingUtils.needQueryRelateData(chargeSheetIdRelateDataFlagMap, ChargeSheet.DataFlag.HAS_ITEM_BATCH_INFO)) {
            return;
        }

        List<String> chargeFormItemIds = chargeFormItems.stream().map(ChargeFormItem::getId).collect(Collectors.toList());

        List<ChargeFormItemBatchInfo> chargeFormItemBatchInfos = chargeFormItemBatchInfoRepository.findAllByChargeFormItemIdInAndIsDeleted(chargeFormItemIds, 0);

        if (CollectionUtils.isEmpty(chargeFormItemBatchInfos)) {
            return;
        }

        Map<String, List<ChargeFormItemBatchInfo>> chargeFormItemBatchInfoGroupMap = ListUtils.groupByKey(chargeFormItemBatchInfos, ChargeFormItemBatchInfo::getChargeFormItemId);


        chargeFormItems.forEach(chargeFormItem -> chargeFormItem.setChargeFormItemBatchInfos(chargeFormItemBatchInfoGroupMap.getOrDefault(chargeFormItem.getId(), new ArrayList<>())));
    }

    public void saveAllBindingData(ChargeSheet chargeSheet, ChargeSheetRelationDto deletedDataDto) {

        if (chargeSheet == null) {
            return;
        }
        int relateDataFlag = 0;


        //给收费单打关联关系表的标记
        if (chargeSheet.getIsDeleted() == 0) {
//            relateDataFlag = relateDataFlag | ChargeSheet.DataFlag.HAS_FORM;
//            relateDataFlag = relateDataFlag | ChargeSheet.DataFlag.HAS_ITEM;
        }

        Consumer<List<?>> doBeforeSaveConsumer = list -> {};

        ChargeSheetRelationDto toSaveDto = Objects.nonNull(deletedDataDto) ? deletedDataDto : new ChargeSheetRelationDto();
        ChargeUtils.addChargeSheetRelationData(chargeSheet, toSaveDto, null);

        if (CollectionUtils.isNotEmpty(chargeSheet.getChargeTransactions())) {
            chargeTransactionRepository.saveAll(chargeSheet.getChargeTransactions());
        }

        if (CollectionUtils.isNotEmpty(chargeSheet.getChargeActions())) {
            chargeActionRepository.saveAll(chargeSheet.getChargeActions());
        }

        if (CollectionUtils.isNotEmpty(toSaveDto.getChargeForms())) {
            doBeforeSaveConsumer.accept(toSaveDto.getChargeForms());
            chargeFormRepository.saveAll(toSaveDto.getChargeForms());
        }

        if (CollectionUtils.isNotEmpty(toSaveDto.getChargeFormItems())) {
            doBeforeSaveConsumer.accept(toSaveDto.getChargeFormItems());
            chargeFormItemRepository.saveAll(toSaveDto.getChargeFormItems());
        }

        if (CollectionUtils.isNotEmpty(toSaveDto.getChargeFormItemBatchInfos())) {
            if (toSaveDto.getChargeFormItemBatchInfos().stream().anyMatch(c -> c.getIsDeleted() == 0)) {
                relateDataFlag = relateDataFlag | ChargeSheet.DataFlag.HAS_ITEM_BATCH_INFO;
            }
            doBeforeSaveConsumer.accept(toSaveDto.getChargeFormItemBatchInfos());
            chargeFormItemBatchInfoRepository.saveAll(toSaveDto.getChargeFormItemBatchInfos());
        }

        if (CollectionUtils.isNotEmpty(toSaveDto.getChargeAirPharmacyMedicalRecords())) {
            doBeforeSaveConsumer.accept(toSaveDto.getChargeAirPharmacyMedicalRecords());
            chargeAirPharmacyMedicalRecordRepository.saveAll(toSaveDto.getChargeAirPharmacyMedicalRecords());
        }

        if (CollectionUtils.isNotEmpty(toSaveDto.getChargeAirPharmacyLogisticsList())) {
            doBeforeSaveConsumer.accept(toSaveDto.getChargeAirPharmacyLogisticsList());
            chargeAirPharmacyLogisticsRepository.saveAll(toSaveDto.getChargeAirPharmacyLogisticsList());
        }

        if (CollectionUtils.isNotEmpty(toSaveDto.getProcessInfos())) {
            if (toSaveDto.getProcessInfos().stream().anyMatch(c -> c.getIsDeleted() == 0)) {
                relateDataFlag = relateDataFlag | ChargeSheet.DataFlag.HAS_PROCESS_INFO;
            }
            doBeforeSaveConsumer.accept(toSaveDto.getProcessInfos());
            chargeSheetProcessInfoRepository.saveAll(toSaveDto.getProcessInfos());
        }

        if (CollectionUtils.isNotEmpty(toSaveDto.getAdditionalFees())) {
            if (toSaveDto.getAdditionalFees().stream().anyMatch(c -> c.getIsDeleted() == 0)) {
                relateDataFlag = relateDataFlag | ChargeSheet.DataFlag.HAS_ADDITIONAL_FEE;
            }
            doBeforeSaveConsumer.accept(toSaveDto.getAdditionalFees());
            chargeAdditionalFeeRepository.saveAll(toSaveDto.getAdditionalFees());
        }

        if (CollectionUtils.isNotEmpty(toSaveDto.getAttachments())) {
            if (toSaveDto.getAttachments().stream().anyMatch(c -> c.getIsDeleted() == 0)) {
                relateDataFlag = relateDataFlag | ChargeSheet.DataFlag.HAS_ATTACHMENT;
            }
            doBeforeSaveConsumer.accept(toSaveDto.getAttachments());
            chargeSheetAttachmentRepository.saveAll(toSaveDto.getAttachments());
        }

        if (CollectionUtils.isNotEmpty(toSaveDto.getCouponPromotionInfos())) {
            List<ChargeCouponPromotionInfo> needSaveList = new ArrayList<>();
            Set<String> needDeleteIds = new HashSet<>();

            toSaveDto.getCouponPromotionInfos()
                    .forEach(c -> {
                        if (c.getIsDeleted() == 1 && StringUtils.isNotBlank(c.getId())) {
                            needDeleteIds.add(c.getId());
                        } else {
                            needSaveList.add(c);
                        }
                    });


            if (CollectionUtils.isNotEmpty(needSaveList)) {
                doBeforeSaveConsumer.accept(needSaveList);
                relateDataFlag = relateDataFlag | ChargeSheet.DataFlag.HAS_COUPON_PROMOTION_INFO;
                chargeCouponPromotionInfoRepository.saveAll(toSaveDto.getCouponPromotionInfos());
            }
            if (CollectionUtils.isNotEmpty(needDeleteIds)) {
                chargeCouponPromotionInfoRepository.deleteByIds(needDeleteIds);
            }

        }

        if (CollectionUtils.isNotEmpty(toSaveDto.getGiftRulePromotionInfos())) {

            List<ChargeGiftRulePromotionInfo> needSaveList = new ArrayList<>();
            Set<String> needDeleteIds = new HashSet<>();

            toSaveDto.getGiftRulePromotionInfos()
                    .forEach(c -> {
                        if (c.getIsDeleted() == 1 && StringUtils.isNotBlank(c.getId())) {
                            needDeleteIds.add(c.getId());
                        } else {
                            needSaveList.add(c);
                        }
                    });


            if (CollectionUtils.isNotEmpty(needSaveList)) {
                doBeforeSaveConsumer.accept(needSaveList);
                relateDataFlag = relateDataFlag | ChargeSheet.DataFlag.HAS_GIFT_RULE_PROMOTION_INFO;
                doBeforeSaveConsumer.accept(needSaveList);
                chargeGiftRulePromotionInfoRepository.saveAll(needSaveList);
            }
            if (CollectionUtils.isNotEmpty(needDeleteIds)) {
                chargeGiftRulePromotionInfoRepository.deleteByIds(needDeleteIds);
            }
        }

        if (CollectionUtils.isNotEmpty(toSaveDto.getPatientCardPromotionInfos())) {

            List<ChargePatientCardPromotionInfo> needSaveList = new ArrayList<>();
            Set<String> needDeleteIds = new HashSet<>();

            toSaveDto.getPatientCardPromotionInfos()
                    .forEach(c -> {
                        if (c.getIsDeleted() == 1 && StringUtils.isNotBlank(c.getId())) {
                            needDeleteIds.add(c.getId());
                        } else {
                            needSaveList.add(c);
                        }
                    });


            if (CollectionUtils.isNotEmpty(needSaveList)) {
                doBeforeSaveConsumer.accept(needSaveList);
                relateDataFlag = relateDataFlag | ChargeSheet.DataFlag.HAS_PATIENT_CARD_PROMOTION_INFO;
                doBeforeSaveConsumer.accept(needSaveList);
                chargePatientCardPromotionInfoRepository.saveAll(needSaveList);
            }
            if (CollectionUtils.isNotEmpty(needDeleteIds)) {
                chargePatientCardPromotionInfoRepository.deleteByIds(needDeleteIds);
            }
        }

        if (CollectionUtils.isNotEmpty(toSaveDto.getChargeVerifyInfos())) {

            List<ChargeVerifyInfo> needSaveList = new ArrayList<>();
            Set<String> needDeleteIds = new HashSet<>();

            toSaveDto.getChargeVerifyInfos()
                    .forEach(c -> {
                        if (c.getIsDeleted() == 1 && StringUtils.isNotBlank(c.getId())) {
                            needDeleteIds.add(c.getId());
                        } else {
                            needSaveList.add(c);
                        }
                    });


            if (CollectionUtils.isNotEmpty(needSaveList)) {
                doBeforeSaveConsumer.accept(needSaveList);
                relateDataFlag = relateDataFlag | ChargeSheet.DataFlag.HAS_CHARGE_VERIFY_INFO;
                doBeforeSaveConsumer.accept(needSaveList);
                chargeVerifyInfoRepository.saveAll(needSaveList);
            }
            if (CollectionUtils.isNotEmpty(needDeleteIds)) {
                chargeVerifyInfoRepository.deleteByIds(needDeleteIds);
            }
        }

        if (CollectionUtils.isNotEmpty(toSaveDto.getPatientPointsPromotionInfos())) {
            if (toSaveDto.getPatientPointsPromotionInfos().stream().anyMatch(c -> c.getIsDeleted() == 0)) {
                relateDataFlag = relateDataFlag | ChargeSheet.DataFlag.HAS_PATIENT_POINTS_PROMOTION_INFO;
            }
            doBeforeSaveConsumer.accept(toSaveDto.getPatientPointsPromotionInfos());
            chargePatientPointsPromotionInfoRepository.saveAll(toSaveDto.getPatientPointsPromotionInfos());
        }

        if (CollectionUtils.isNotEmpty(toSaveDto.getPatientPointsDeductProductPromotionInfos())) {
            if (toSaveDto.getPatientPointsDeductProductPromotionInfos().stream().anyMatch(c -> c.getIsDeleted() == 0)) {
                relateDataFlag = relateDataFlag | ChargeSheet.DataFlag.HAS_PATIENT_POINTS_DEDUCT_PRODUCT_PROMOTION_INFO;
            }
            doBeforeSaveConsumer.accept(toSaveDto.getPatientPointsDeductProductPromotionInfos());
            chargePatientPointsDeductProductPromotionInfoRepository.saveAll(toSaveDto.getPatientPointsDeductProductPromotionInfos());
        }

        if (CollectionUtils.isNotEmpty(toSaveDto.getChargeDeliveryInfos())) {
            doBeforeSaveConsumer.accept(toSaveDto.getChargeDeliveryInfos());
            chargeDeliveryInfoRepository.saveAll(toSaveDto.getChargeDeliveryInfos());
        }

        if (CollectionUtils.isNotEmpty(toSaveDto.getAdditionals())) {
            doBeforeSaveConsumer.accept(toSaveDto.getAdditionals());
            chargeSheetAdditionalRepository.saveAll(toSaveDto.getAdditionals());
        }

        chargeSheet.setDataFlag(relateDataFlag);
    }

}
