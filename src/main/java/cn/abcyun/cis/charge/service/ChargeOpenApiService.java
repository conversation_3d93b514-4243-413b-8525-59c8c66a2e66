package cn.abcyun.cis.charge.service;

import cn.abcyun.cis.charge.api.model.ChargeSheetAbstractListRsp;
import cn.abcyun.cis.charge.api.model.ChargeSheetExecuteRecordListRsp;
import cn.abcyun.cis.charge.api.model.EmployeeView;
import cn.abcyun.cis.charge.api.model.ExecuteRecordCreateReq;
import cn.abcyun.cis.charge.api.model.openapi.OpenApiPatientChargeSheetView;
import cn.abcyun.cis.charge.api.model.openapi.OpenApiQueryPatientChargeSheetReq;
import cn.abcyun.cis.charge.api.model.openapi.nurse.*;
import cn.abcyun.cis.charge.base.ChargeServiceError;
import cn.abcyun.cis.charge.base.ChargeServiceException;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.processor.SheetProcessor;
import cn.abcyun.cis.charge.rpc.model.ChargeSheetViewList;
import cn.abcyun.cis.charge.service.dto.*;
import cn.abcyun.cis.charge.util.ChargeUtils;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.rpc.outpatient.MedicalRecord;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrder;
import cn.abcyun.cis.commons.util.DateUtils;
import cn.abcyun.cis.core.db.UseReadOnlyDB;
import cn.abcyun.common.log.marker.AbcLogMarker;
import cn.abcyun.common.model.AbcListPage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 开放平台 service
 *
 * <AUTHOR>
 * @date 2023-08-14 22:50:52
 **/
@Slf4j
@Service
public class ChargeOpenApiService {

    @Autowired
    private OutpatientService outpatientService;

    @Autowired
    private ChargeService chargeService;

    @Autowired
    private ChargeSheetService chargeSheetService;

    @Autowired
    private PatientOrderService patientOrderService;

    @Autowired
    private ChargeNurseService chargeNurseService;

    @Autowired
    private ChargeExecuteService chargeExecuteService;

    @Autowired
    private ChargeExecuteRecordService chargeExecuteRecordService;

    /**
     * 按天查询执行列表
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public OpenApiChargeNurseListRsp listChargeNurseByDate(String chainId, String clinicId, LocalDate date) {
        Instant createBegin = DateUtils.toInstant(DateUtils.beginOfDay(date));
        Instant createEnd = DateUtils.toInstant(DateUtils.endOfDay(date));
        ChargeSheetAbstractListRsp chargeSheetAbstractListRsp = chargeNurseService.findChargeSheetAbstractList(-1, -1, null, chainId, clinicId,
                null,null, createBegin, createEnd, 0, null, null);
        return toOpenApiChargeNurseListRsp(chargeSheetAbstractListRsp);
    }

    private OpenApiChargeNurseListRsp toOpenApiChargeNurseListRsp(ChargeSheetAbstractListRsp chargeSheetAbstractListRsp) {
        OpenApiChargeNurseListRsp openApiChargeNurseListRsp = new OpenApiChargeNurseListRsp();
        if (chargeSheetAbstractListRsp == null || CollectionUtils.isEmpty(chargeSheetAbstractListRsp.getResult())) {
            openApiChargeNurseListRsp.setChargeNurses(Collections.emptyList());
            return openApiChargeNurseListRsp;
        }

        List<OpenApiChargeNurseListView> chargeNurses = chargeSheetAbstractListRsp.getResult().stream()
                .map(this::toOpenApiChargeNurseListView).filter(Objects::nonNull).collect(Collectors.toList());
        openApiChargeNurseListRsp.setChargeNurses(chargeNurses);
        return openApiChargeNurseListRsp;
    }

    private OpenApiChargeNurseListView toOpenApiChargeNurseListView(ChargeSheetAbstract chargeSheetAbstract) {
        if (chargeSheetAbstract == null) {
            return null;
        }

        OpenApiChargeNurseListView openApiChargeNurseListView = new OpenApiChargeNurseListView();
        openApiChargeNurseListView.setId(chargeSheetAbstract.getId());
        openApiChargeNurseListView.setPatientOrderId(chargeSheetAbstract.getPatientOrderId());
        openApiChargeNurseListView.setStatus(chargeSheetAbstract.getExecuteStatus());
        openApiChargeNurseListView.setPatient(chargeSheetAbstract.getPatient());
        openApiChargeNurseListView.setAbstractInfo(chargeSheetAbstract.getNurseAbstractInfo());
        openApiChargeNurseListView.setCreated(chargeSheetAbstract.getCreated());
        openApiChargeNurseListView.setIsClosed(chargeSheetAbstract.getIsClosed());
        return openApiChargeNurseListView;
    }


    /**
     * 查询执行详情
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public ChargeSheetView getChargeNurseDetail(String chainId, String clinicId, String chargeSheetId) {
        return chargeNurseService.findChargeSheetById(chargeSheetId, clinicId, chainId);
    }

    /**
     * 执行
     */
    @Transactional(rollbackFor = Exception.class)
    public void createExecuteRecord(String chargeSheetId, OpenApiCreateExecuteRecordReq openApiCreateExecuteRecordReq) {
        // 1、先补偿插入治疗理疗项收费子项对应的执行项不存在的数据
        List<String> chargeFormItemIds = openApiCreateExecuteRecordReq.getItems().stream().map(OpenApiChargeNurseExecuteItemReq::getId).collect(Collectors.toList());
        String chainId = openApiCreateExecuteRecordReq.getChainId();
        String operatorId = openApiCreateExecuteRecordReq.getOperatorId();
        chargeExecuteService.addNotExistedExecuteItemsByChargeFormItemIds(chargeFormItemIds, chainId, operatorId);

        // 2. 创建执行记录
        String clinicId = openApiCreateExecuteRecordReq.getClinicId();
        ExecuteRecordCreateReq executeRecordCreateReq = toExecuteRecordCreateReq(openApiCreateExecuteRecordReq);
        chargeExecuteRecordService.createExecuteRecord(executeRecordCreateReq, chargeSheetId, chainId, clinicId, operatorId);
    }

    private ExecuteRecordCreateReq toExecuteRecordCreateReq(OpenApiCreateExecuteRecordReq openApiCreateExecuteRecordReq) {
        if (openApiCreateExecuteRecordReq == null) {
            return null;
        }

        ExecuteRecordCreateReq executeRecordCreateReq = new ExecuteRecordCreateReq();
        executeRecordCreateReq.setItems(openApiCreateExecuteRecordReq.getItems().stream().map(this::toExecuteRecordCreateItem).collect(Collectors.toList()));
        executeRecordCreateReq.setExecutorIds(new HashSet<>(openApiCreateExecuteRecordReq.getExecutorIds()));
        executeRecordCreateReq.setComment(openApiCreateExecuteRecordReq.getComment());
        return executeRecordCreateReq;
    }

    private ExecuteRecordCreateReq.ExecuteRecordCreateItem toExecuteRecordCreateItem(OpenApiChargeNurseExecuteItemReq req) {
        if (req == null) {
            return null;
        }

        ExecuteRecordCreateReq.ExecuteRecordCreateItem executeRecordCreateItem = new ExecuteRecordCreateReq.ExecuteRecordCreateItem();
        executeRecordCreateItem.setChargeFormItemId(req.getId());
        executeRecordCreateItem.setCount(req.getCount());
        return executeRecordCreateItem;
    }

    public OpenApiExecuteRecordListRsp listExecuteRecordByChargeSheetId(String chargeSheetId, String clinicId, String chainId) {
        ChargeSheetExecuteRecordListRsp chargeSheetExecuteRecordListRsp = chargeExecuteRecordService.listExecuteRecordByChargeSheetId(chargeSheetId, clinicId, chainId, null);
        return toOpenApiExecuteRecordListRsp(chargeSheetExecuteRecordListRsp);
    }

    private OpenApiExecuteRecordListRsp toOpenApiExecuteRecordListRsp(ChargeSheetExecuteRecordListRsp chargeSheetExecuteRecordListRsp) {
        OpenApiExecuteRecordListRsp openApiChargeNurseListRsp = new OpenApiExecuteRecordListRsp();
        if (chargeSheetExecuteRecordListRsp == null || CollectionUtils.isEmpty(chargeSheetExecuteRecordListRsp.getExecuteRecords())) {
            openApiChargeNurseListRsp.setExecuteRecords(Collections.emptyList());
            return openApiChargeNurseListRsp;
        }

        List<OpenApiExecuteRecordView> executeRecords = chargeSheetExecuteRecordListRsp.getExecuteRecords().stream()
                .map(this::toOpenApiExecuteRecordView).filter(Objects::nonNull).collect(Collectors.toList());
        openApiChargeNurseListRsp.setExecuteRecords(executeRecords);
        return openApiChargeNurseListRsp;
    }

    private OpenApiExecuteRecordView toOpenApiExecuteRecordView(ChargeExecuteRecordView chargeExecuteRecordView) {
        if (chargeExecuteRecordView == null) {
            return null;
        }

        OpenApiExecuteRecordView openApiExecuteRecordView = new OpenApiExecuteRecordView();
        openApiExecuteRecordView.setId(chargeExecuteRecordView.getId());
        openApiExecuteRecordView.setExecuteClinicId(chargeExecuteRecordView.getExecuteClinicId());
        openApiExecuteRecordView.setExecuteClinicName(chargeExecuteRecordView.getExecuteClinicName());
        openApiExecuteRecordView.setStatus(chargeExecuteRecordView.getStatus());
        openApiExecuteRecordView.setComment(chargeExecuteRecordView.getComment());
        openApiExecuteRecordView.setExecuteDate(chargeExecuteRecordView.getExecuteDate());
        openApiExecuteRecordView.setOperator(buildEmployeeView(chargeExecuteRecordView.getCreatedBy(), chargeExecuteRecordView.getCreatedByName()));
        openApiExecuteRecordView.setCancelBy(buildEmployeeView(chargeExecuteRecordView.getCancelBy(), chargeExecuteRecordView.getCanceledEmployeeName()));
        openApiExecuteRecordView.setExecutors(chargeExecuteRecordView.getExecutors());
        openApiExecuteRecordView.setExecuteItems(toOpenApiExecuteRecordViews(chargeExecuteRecordView.getItems()));
        return openApiExecuteRecordView;
    }

    private List<OpenApiExecuteRecordItemView> toOpenApiExecuteRecordViews(List<ChargeExecuteRecordItemView> chargeExecuteRecordItemViews) {
        if (CollectionUtils.isEmpty(chargeExecuteRecordItemViews)) {
            return Collections.emptyList();
        }

        return chargeExecuteRecordItemViews.stream().map(this::toOpenApiExecuteRecordItemView).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private OpenApiExecuteRecordItemView toOpenApiExecuteRecordItemView(ChargeExecuteRecordItemView chargeExecuteRecordItemView) {
        if (chargeExecuteRecordItemView == null) {
            return null;
        }

        OpenApiExecuteRecordItemView openApiExecuteRecordItemView = new OpenApiExecuteRecordItemView();
        openApiExecuteRecordItemView.setId(chargeExecuteRecordItemView.getId());
        openApiExecuteRecordItemView.setChargeFormItemId(chargeExecuteRecordItemView.getChargeFormItemId());
        openApiExecuteRecordItemView.setExecuteItemId(chargeExecuteRecordItemView.getExecuteItemId());
        openApiExecuteRecordItemView.setExecuteItemName(chargeExecuteRecordItemView.getExecuteItemName());
        openApiExecuteRecordItemView.setExecutedCount(chargeExecuteRecordItemView.getCount());
        return openApiExecuteRecordItemView;
    }


    private EmployeeView buildEmployeeView(String id, String name) {
        if (StringUtils.isBlank(id)) {
            return null;
        }

        EmployeeView employeeView = new EmployeeView();
        employeeView.setId(id);
        employeeView.setName(name);
        return employeeView;
    }

    /**
     * 撤销执行记录
     */
    public void cancelExecuteRecord(String chargeSheetId, String executeRecordId, OpenApiCancelExecuteRecordReq req) {
        String chainId = req.getChainId();
        String clinicId = req.getClinicId();
        String operatorId = req.getOperatorId();
        chargeExecuteRecordService.cancelChargeExecuteRecord(chargeSheetId, executeRecordId, clinicId, chainId, operatorId);
    }

    /**
     * 通过就诊单ID查询收费单
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public ChargeSheetViewList getChargeSheetViewListByPatientOrderId(String chainId, String clinicId, String patientOrderId) {
        ChargeSheetViewList chargeSheetViewList = new ChargeSheetViewList();

        List<ChargeSheet> chargeSheets = chargeSheetService.findAllByPatientOrderId(patientOrderId, null);
        chargeSheets.removeIf(chargeSheet -> chargeSheet.getType() == ChargeSheet.Type.OUTPATIENT && chargeSheet.getOutpatientStatus() == ChargeSheet.OutpatientStatus.WAITING);
        if (chargeSheets.isEmpty()) {
            chargeSheetViewList.setChargeSheets(Collections.emptyList());
            return chargeSheetViewList;
        }

        ChargeSheet firstChargeSheet = chargeSheets.get(0);
        if (!Objects.equals(firstChargeSheet.getChainId(), chainId) || !Objects.equals(firstChargeSheet.getClinicId(), clinicId)) {
            throw new NotFoundException();
        }

        PatientOrder patientOrder = patientOrderService.findPatientOrder(patientOrderId, null);
        if (patientOrder == null) {
            return chargeSheetViewList;
        }

        MedicalRecord medicalRecord = null;
        if (patientOrder.getSource() == PatientOrder.Source.REGISTRATION || patientOrder.getSource() == PatientOrder.Source.OUTPATIENT
                || patientOrder.getSource() == PatientOrder.Source.COPYWRITING_PRESCRIPTION || patientOrder.getSource() == PatientOrder.Source.ONLINE_CONSULTATION) {
            medicalRecord = outpatientService.findMedicalRecord(patientOrderId);
        }

        // 将挂号单生成的收费单合并到门诊生成的收费单上
        ChargeSheet outpatientChargeSheet = chargeSheets.stream()
                .filter(chargeSheet -> StringUtils.isNotEmpty(chargeSheet.getRegistrationChargeSheetId()) && chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED)
                .findFirst().orElse(null);
        if (outpatientChargeSheet != null) {
            String registrationChargeSheetId = outpatientChargeSheet.getRegistrationChargeSheetId();
            ChargeSheet registrationChargeSheet = chargeSheets.stream()
                    .filter(chargeSheet -> Objects.equals(chargeSheet.getId(), registrationChargeSheetId)).findFirst().orElse(null);
            if (registrationChargeSheet != null && registrationChargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED) {
                ChargeService.moveRegistrationChargeSheetToOutpatientChargeSheet(registrationChargeSheet, outpatientChargeSheet);
                chargeSheets.remove(registrationChargeSheet);
            }
        }

        List<ChargeSheetView> chargeSheetViews = new ArrayList<>(chargeSheets.size());
        for (ChargeSheet chargeSheet : chargeSheets) {
            SheetProcessor sheetProcessor = new SheetProcessor(chargeSheet);
            sheetProcessor.setSheetProcessorInfoProvider(chargeService);
            sheetProcessor.setMedicalRecord(medicalRecord);
            sheetProcessor.setPatientOrder(patientOrder);
            sheetProcessor.build();

            ChargeSheetView chargeSheetView = sheetProcessor.generateSheetDetail(false, false, Constants.ChargeSource.CHARGE, true);
            chargeService.updateChargeActionCreateByNameAndSort(chargeSheetView.getChainId(), chargeSheetView.getChargeActions());

            chargeSheetViews.add(chargeSheetView);
        }

        chargeService.bindChargeTransactionRecords(chargeSheetViews, chargeSheets);

        // 将 chargeTransactionRecords 转换为父子结构
        chargeSheetViews.forEach(chargeSheetView -> convertChargeTransactionViews(chargeSheetView.getChargeTransactions()));

        chargeSheetViewList.setChargeSheets(chargeSheetViews);
        return chargeSheetViewList;
    }

    public ChargeSheetView findChargeSheetById(String chargeSheetId) {
        ChargeSheetView chargeSheetView = chargeService.findChargeSheetById(chargeSheetId, Constants.ChargeSource.WE_CLINIC, true, true, true, Constants.ChargeSheetDispensingQueryCheckType.DISPENSING_QUERY_NOT_CHECK, null);
        if (chargeSheetView == null) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "getChargeSheetById:{} not found", chargeSheetId);
            throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_DETAIL_NOT_EXISTED);
        }

        // 将 chargeTransactionRecords 转换为父子结构
        convertChargeTransactionViews(chargeSheetView.getChargeTransactions());

        chargeSheetView.setCanUnLockChargeSheet(ChargeUtils.isChargerLockStatus(chargeSheetView.getLockStatus()));
        return chargeSheetView;
    }

    private void convertChargeTransactionViews(List<ChargeTransactionView> chargeTransactions) {
        if (CollectionUtils.isEmpty(chargeTransactions)) {
            return;
        }

        chargeTransactions.forEach(this::convertChargeTransactionView);
    }

    private void convertChargeTransactionView(ChargeTransactionView chargeTransactionView) {
        if (chargeTransactionView == null || CollectionUtils.isEmpty(chargeTransactionView.getChargeTransactionRecords())) {
            return;
        }

        chargeTransactionView.setChargeTransactionRecords(buildChargeTransactionRecordViewTree(null, chargeTransactionView.getChargeTransactionRecords()));
    }

    private List<ChargeTransactionRecordView> buildChargeTransactionRecordViewTree(String parentId, List<ChargeTransactionRecordView> chargeTransactionRecords) {
        if (CollectionUtils.isEmpty(chargeTransactionRecords)) {
            return new ArrayList<>();
        }
        return chargeTransactionRecords.stream()
                .filter(record -> Objects.equals(record.getComposeParentRecordId(), parentId))
                .peek(record -> record.setComposeChildren(buildChargeTransactionRecordViewTree(record.getId(), chargeTransactionRecords)))
                .collect(Collectors.toList());
    }

    /**
     * 开放平台查询患者的收费单
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public AbcListPage<OpenApiPatientChargeSheetView> queryPatientChargeSheets(OpenApiQueryPatientChargeSheetReq req) {
        return chargeSheetService.queryPatientChargeSheetForOpenApi(req);
    }
}