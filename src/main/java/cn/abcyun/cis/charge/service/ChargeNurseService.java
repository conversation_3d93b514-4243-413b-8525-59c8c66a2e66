package cn.abcyun.cis.charge.service;

import cn.abcyun.bis.rpc.sdk.cis.model.clinic.*;
import cn.abcyun.bis.rpc.sdk.cis.model.search.CdssQLSearchResultItem;
import cn.abcyun.bis.rpc.sdk.cis.model.search.CdssSearchResultRsp;
import cn.abcyun.bis.rpc.sdk.property.base.PropertyInfo;
import cn.abcyun.bis.rpc.sdk.property.base.PropertyKey;
import cn.abcyun.bis.rpc.sdk.property.model.ClinicNurseSettings;
import cn.abcyun.bis.rpc.sdk.property.model.NurseExecute;
import cn.abcyun.bis.rpc.sdk.property.service.PropertyService;
import cn.abcyun.bis.rpc.sdk.property.service.model.PropertyConfigItem;
import cn.abcyun.cis.charge.amqp.RocketMqProducer;
import cn.abcyun.cis.charge.api.model.ChargeSheetAbstractListRsp;
import cn.abcyun.cis.charge.api.model.ChargeSheetPrivilegeForNurseCheckRsp;
import cn.abcyun.cis.charge.api.model.CreateChargeSheetForTherapyRsp;
import cn.abcyun.cis.charge.api.model.CreateChargeSheetReq;
import cn.abcyun.cis.charge.base.*;
import cn.abcyun.cis.charge.base.pay.CombinedPayItem;
import cn.abcyun.cis.charge.cold.ChargeFormItemDaoProxy;
import cn.abcyun.cis.charge.controller.StatusNameTranslator;
import cn.abcyun.cis.charge.mapper.ChargeExecuteRecordExecutorMapper;
import cn.abcyun.cis.charge.mapper.ChargeMapper;
import cn.abcyun.cis.charge.model.*;
import cn.abcyun.cis.charge.processor.ChargeSheetFeeProtocol;
import cn.abcyun.cis.charge.processor.SheetProcessor;
import cn.abcyun.cis.charge.processor.SheetProcessorDispatcher;
import cn.abcyun.cis.charge.processor.push.ChargeSheetTherapyPushOpsPatient;
import cn.abcyun.cis.charge.repository.*;
import cn.abcyun.cis.charge.service.dto.*;
import cn.abcyun.cis.charge.service.rpc.CisCrmService;
import cn.abcyun.cis.charge.service.rpc.CisScClinicService;
import cn.abcyun.cis.charge.service.rpc.CisSearchService;
import cn.abcyun.cis.charge.util.ChargeUtils;
import cn.abcyun.cis.charge.util.JsonUtils;
import cn.abcyun.cis.charge.util.ListUtils;
import cn.abcyun.cis.commons.amqp.message.ChargeSheetMessage;
import cn.abcyun.cis.commons.amqp.message.PatientOrderMessage;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.exception.ServiceInternalException;
import cn.abcyun.cis.commons.rpc.outpatient.OutpatientSheetExtend;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrder;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.cis.core.db.UseReadOnlyDB;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 收费单执行站 service
 *
 * <AUTHOR>
 * @version ChargeNurseService.java, 2022/3/28 上午10:29
 */
@Service
public class ChargeNurseService {
    @Autowired
    private ChargeSheetService chargeSheetService;
    @Autowired
    private PropertyService propertyService;
    @Autowired
    private ClinicService clinicService;
    @Autowired
    private PatientOrderService patientOrderService;
    @Autowired
    private OutpatientService outpatientService;
    @Autowired
    private OrganProductService organProductService;
    @Autowired
    private NurseService nurseService;
    @Autowired
    private CisScClinicService cisScClinicService;
    @Autowired
    private ChargeService chargeService;
    @Autowired
    private ChargeMapper chargeMapper;
    @Autowired
    private CisSearchService cisSearchService;
    @Autowired
    private ChargeExecuteItemRepository chargeExecuteItemRepository;
    @Autowired
    private ChargeSheetRepository chargeSheetRepository;
    @Autowired
    private ChargeSheetAdditionalRepository chargeSheetAdditionalRepository;
    @Autowired
    private ChargeExecuteService chargeExecuteService;
    @Autowired
    private ChargeTransactionRecordService chargeTransactionRecordService;
    @Autowired
    private RocketMqProducer rocketMqProducer;
    @Autowired
    private WeClinicChargeService weClinicChargeService;
    @Autowired
    private CisCrmService crmService;
    @Autowired
    private ChargeExecuteRecordExecutorRepository executeRecordExecutorRepository;
    @Autowired
    private ChargeExecuteRecordExecutorMapper chargeExecuteRecordExecutorMapper;
    @Autowired
    private SheetProcessorService sheetProcessorService;
    @Autowired
    private ChargeFormItemDaoProxy chargeFormItemRepository;
    @Autowired
    private ChargeFormRepository chargeFormRepository;
    @Autowired
    private TobMessageService tobMessageService;

    /**
     * 执行站收费单详情
     *
     * @param chargeSheetId 收费单id
     * @param clinicId      门店id
     * @param chainId       连锁id
     * @return 收费单详情
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public ChargeSheetView findChargeSheetById(String chargeSheetId, String clinicId, String chainId) {
        if (TextUtils.isEmpty(chargeSheetId)) {
            return null;
        }

        //1、查询收费单信息
        ChargeSheet chargeSheet = chargeSheetService.findByIdAndChainId(chargeSheetId, chainId);

        if (chargeSheet == null) {
            return null;
        }

        // 2、判断是否是跨店查询
        String createClinicName = null;
        if (!StringUtils.equals(chainId, clinicId) && !Objects.equals(clinicId, chargeSheet.getClinicId())) {
            boolean enableCross = propertyService.getPropertyValueByKey(PropertyKey.NURSE_EXECUTE, chainId, NurseExecute.class).getEnableCross() == 1;
            if (!enableCross) {
                throw new ChargeServiceException(ChargeServiceError.CROSS_CLINIC_EXECUTE_UNAVAILABLE);
            }
            createClinicName = Optional.ofNullable(clinicService.getClinicById(chargeSheet.getClinicId()))
                    .map(clinicForCharge -> StringUtils.isEmpty(clinicForCharge.getShortName()) ? clinicForCharge.getName() : clinicForCharge.getShortName())
                    .orElse(null);

        }

        // 3、查询patientOrder
        PatientOrder patientOrder = patientOrderService.findPatientOrder(chargeSheet.getPatientOrderId(), PatientOrderService.getAgeLockTime(chargeSheet));
        if (patientOrder == null) {
            return null;
        }

        OutpatientSheetExtend outpatientSheetExtend = null;
        if (patientOrder.getSource() == PatientOrder.Source.REGISTRATION || patientOrder.getSource() == PatientOrder.Source.OUTPATIENT
                || patientOrder.getSource() == PatientOrder.Source.COPYWRITING_PRESCRIPTION || patientOrder.getSource() == PatientOrder.Source.ONLINE_CONSULTATION) {
            outpatientSheetExtend = outpatientService.findOutpatientSheetExtend(patientOrder.getId());
        }

        SheetProcessor sheetProcessor = new SheetProcessor(chargeSheet);
        sheetProcessor.setSheetProcessorInfoProvider(sheetProcessorService);
        sheetProcessor.setMedicalRecord(outpatientSheetExtend != null ? outpatientSheetExtend.getMedicalRecord() : null);
        sheetProcessor.setPatientOrder(patientOrder);
        sheetProcessor.build();

        ChargeSheetView chargeSheetView = sheetProcessor.generateSheetDetail(false, true, Constants.ChargeSource.CHARGE, true);
        chargeSheetView.setClinicName(createClinicName);
        chargeSheetView.setOnlyExecuteAfterPaid(propertyService.getPropertyValueByKey(PropertyKey.CLINIC_NURSE_SETTINGS, chargeSheet.getClinicId(), ClinicNurseSettings.class).getOnlyExecuteAfterPaid());
        return nurseService.patchChargeSheetViewForNurse(chargeSheetView, outpatientSheetExtend);
    }

    /**
     * 执行站列表查询
     *
     * @param offset              起始下标
     * @param limit               分页条数
     * @param keyword             查询关键字
     * @param chainId             连锁id
     * @param clinicId            门店id
     * @param filterExecuteStatus 执行状态条件
     * @param createdBegin        起始日期
     * @param createdEnd          结束日期
     * @param queryClinicScope    查询门店范围，针对跨店执行（0：本店；1：其它门店）
     * @param ownerId             开单人Id
     * @return 查询结果
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public ChargeSheetAbstractListRsp findChargeSheetAbstractList(int offset,
                                                                  int limit,
                                                                  String keyword,
                                                                  String chainId,
                                                                  String clinicId,
                                                                  String employeeId,
                                                                  String filterExecuteStatus,
                                                                  Instant createdBegin,
                                                                  Instant createdEnd,
                                                                  int queryClinicScope,
                                                                  String ownerId,
                                                                  List<Integer> sourceTypeList) {
        Integer countInSelfClinic = null;
        Integer countInOtherClinics = null;

        int totalCount = 0;
        int withUndiagnosed = organProductService.isShowUndiagnosedChargeSheet(chainId) ? 1 : 0;
        // onlyShowContainsExecute保留兼容chargeSheet老数据，如果includeItemType里面包含了执行项，需要把老数据中包含执行项的查出来，其他的不查出来
        boolean onlyShowContainsExecute = false;
        Integer includeItemType = null;
        ClinicNurseSettings clinicNurseSettings = propertyService.getPropertyValueByKey(PropertyKey.CLINIC_NURSE_SETTINGS, clinicId, ClinicNurseSettings.class);
        boolean onlyExecuteAfterPaid = clinicNurseSettings.getOnlyExecuteAfterPaid() == 1;
        List<Integer> enterListItemTypes = clinicNurseSettings.getEnterListSettings().getItemType();
        List<Integer> typeList = ChargeSheet.SourceType.getTypesMap(sourceTypeList);

        for (Integer itemType : enterListItemTypes) {
            includeItemType = Objects.isNull(includeItemType) ? itemType : (includeItemType | itemType);
        }
        if (enterListItemTypes.contains(Constants.ChargeSheetIncludeItemType.NEED_EXECUTE_TREATMENT)) {
            onlyShowContainsExecute = true;
        } else if (enterListItemTypes.contains(0)) {
            // 展示全部单据
            includeItemType = null;
        }
        // 进入执行站收费单状态设置. 0：开单后即可进入执行站，1：收费后才可进入执行站
        boolean onlyShowAfterPaid = Objects.equals(clinicNurseSettings.getEnterListSettings().getChargeStatus(), 1);

        List<ChargeSheetAbstract> results = new ArrayList<>();
        if (TextUtils.isEmpty(keyword)) {
            // 查询历史单据查看数据权限设置，如果是开单人及执行人可看，需要获取开单人和助理医生信息
            List<String> ownerIds = new ArrayList<>();
            boolean isNeedFilterExecutor = false;
            String filterExecutorId = null;
            // 如果前端已经指定了ownerId，不用查询数据查看权限
            if (StringUtils.isNotBlank(ownerId)) {
                ownerIds.add(ownerId);
            } else if (StringUtils.isNotBlank(employeeId)) {
                // 如果当前人是管理员，也不用执行人去过滤
                // 3、当前人在开单门店是管理员,所有的都可以看见
                boolean isAdmin = Objects.equals(Optional.ofNullable(cisScClinicService.queryEmployeeClinicInfoById(employeeId, clinicId))
                                .map(ClinicEmployee::getClinicInfo)
                                .map(EmployeeClinicInfo::getRoleId)
                                .orElse(null),
                        EmployeeClinicInfo.RoleId.ADMIN);
                // 非管理员，查询查看历史单据的数据权限设置
                if (!isAdmin) {
                    Integer showHistorySheet = Optional.ofNullable(clinicService.getDataPermission(chainId, clinicId))
                            .map(ClinicDataPermissionView::getNurse)
                            .map(ClinicDataPermissionNurse::getHistorySheet)
                            .orElse(0);
                    // 如果是开单人或执行人可看
                    if (Objects.equals(showHistorySheet, 1)) {
                        // 查看助理医生
                        QueryAssistDoctorRsp queryAssistDoctorRsp = clinicService.getAssistDoctors(clinicId, employeeId);
                        if (Objects.nonNull(queryAssistDoctorRsp)){
                            // 如果助理的全部医生，不用过滤，过滤字段不生效
                            if (!Objects.equals(queryAssistDoctorRsp.getIsAssistAll(), 1)){
                                if(StringUtils.isNotBlank(employeeId)) {
                                    // 本人开单的支持查看
                                    ownerIds.add(employeeId);
                                    isNeedFilterExecutor = true;
                                    // 只能看自己执行的
                                    filterExecutorId = employeeId;
                                }
                                // 将助力医生添加到开单人集合中
                                Optional.ofNullable(queryAssistDoctorRsp.getAssistDoctors()).orElse(Lists.newArrayList())
                                        .forEach(assistDoctor -> ownerIds.add(assistDoctor.getId()));
                            }
                        } else if(StringUtils.isNotBlank(employeeId)) {
                            // 本人开单的支持查看
                            ownerIds.add(employeeId);
                            isNeedFilterExecutor = true;
                            // 只能看自己执行的
                            filterExecutorId = employeeId;
                        }
                    }
                }
            }

            results = chargeMapper.findChargeSheetAbstractListForNurse(offset, limit, clinicId, filterExecuteStatus, createdBegin, createdEnd, ownerIds, onlyShowContainsExecute, includeItemType, onlyExecuteAfterPaid, onlyShowAfterPaid, typeList, isNeedFilterExecutor, filterExecutorId);
            if (offset >= 0 && limit >= 0) {
                totalCount = chargeMapper.findChargeSheetAbstractListCountForNurse(clinicId, filterExecuteStatus, createdBegin, createdEnd, ownerIds, onlyShowContainsExecute, includeItemType, onlyShowAfterPaid, typeList, isNeedFilterExecutor, filterExecutorId);
            } else {
                totalCount = results.size();
            }
        } else {
            // 00：所有的；01：不看收费状态，包含执行项的； 10：已收费，所有的； 11：已收费，包含执行项的；
            int filterFlag = (onlyShowContainsExecute ? 1 : 0) | (onlyShowAfterPaid ? 2 : 0);
            CdssSearchResultRsp<CdssQLSearchResultItem> esSearchRsp = cisSearchService.searchCharge(chainId, clinicId, keyword, "nurse", withUndiagnosed,
                    queryClinicScope, filterFlag, includeItemType, offset, limit, null, typeList, null, null);
            if (Objects.nonNull(esSearchRsp)) {
                totalCount = (int) esSearchRsp.getTotal();
                if (!CollectionUtils.isEmpty(esSearchRsp.getHits())) {
                    List<String> ids = esSearchRsp.getHits()
                            .stream()
                            .filter(Objects::nonNull)
                            .map(CdssQLSearchResultItem::getId)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(ids)) {
                        results = chargeMapper.findChargeSheetAbstractListForNurseByIdsAndChainIdAndClinicId(ids, chainId, queryClinicScope == 0 ? clinicId : null, withUndiagnosed, onlyExecuteAfterPaid);
                    }
                }
            }

            // 判断是否支持跨店执行
            boolean enableCross = propertyService.getPropertyValueByKey(PropertyKey.NURSE_EXECUTE, chainId, NurseExecute.class).getEnableCross() == 1;
            if (enableCross) {
                // 如果queryClinicScope=0，这个地方查询queryClinicScope=1的数据；如果queryClinicScope=1，这个地方查询queryClinicScope=0的数据
                long anotherSearchResult = cisSearchService.searchChargeCount(chainId, clinicId, keyword, "nurse", withUndiagnosed, queryClinicScope ^ 1,
                        filterFlag, includeItemType, null, typeList, null, null);
                if (queryClinicScope == 0) {
                    countInSelfClinic = totalCount;
                    countInOtherClinics = Math.toIntExact(anotherSearchResult);
                } else if (queryClinicScope == 1) {
                    countInSelfClinic = Math.toIntExact(anotherSearchResult);
                    countInOtherClinics = totalCount;
                }
            }
        }

        // 添加额外信息
        appendExtraInfoToChargeSheetAbstract(chainId, results);

        ChargeSheetAbstractListRsp rspData = new ChargeSheetAbstractListRsp();
        rspData.setResult(results);
        rspData.setKeyword(keyword);
        rspData.setLimit(limit);
        rspData.setOffset(offset);
        rspData.setTotalCount(totalCount);
        rspData.setCountInSelfClinic(countInSelfClinic);
        rspData.setCountInOtherClinics(countInOtherClinics);
        return rspData;
    }

    /**
     * 执行站开单
     *
     * @param req        请求参数
     * @param clinicId   门店id
     * @param operatorId 操作人id
     * @return 结构
     */
    @Transactional(rollbackFor = Exception.class)
    public CreateChargeSheetForTherapyRsp createChargeSheetForTherapy(CreateChargeSheetReq req, String chainId, String clinicId, String operatorId, int hisType) {
        CreateChargeSheetAndPatientOrderInfo createChargeSheetAndPatientOrderInfo = new CreateChargeSheetAndPatientOrderInfo();
        createChargeSheetAndPatientOrderInfo.setCreateType(CreateChargeSheetAndPatientOrderInfo.CreateType.CREATE_FOR_THERAPY);
        createChargeSheetAndPatientOrderInfo.setCreateChargeSheetReq(req);
        createChargeSheetAndPatientOrderInfo.setClinicId(clinicId);
        createChargeSheetAndPatientOrderInfo.setChainId(chainId);
        createChargeSheetAndPatientOrderInfo.setChargeVersion(ChargeVersionConstants.convertChargeVersion(hisType));
        createChargeSheetAndPatientOrderInfo.setOperatorId(operatorId);

        CreateChargeSheetAndPatientOrderResult createChargeSheetAndPatientOrderResult = chargeService.createChargeSheetAndPatientOrder(createChargeSheetAndPatientOrderInfo);

        ChargeSheet chargeSheet = createChargeSheetAndPatientOrderResult.getChargeSheet();
        PatientOrder patientOrder = createChargeSheetAndPatientOrderResult.getPatientOrder();

        SheetProcessorDispatcher.ISheetProcessor sheetProcessor = SheetProcessorDispatcher.newSheetProcessor(chargeSheet);
        sheetProcessor.setOperatorId(operatorId);
        sheetProcessor.setSheetProcessorInfoProvider(sheetProcessorService);
        sheetProcessor.setPatientOrder(patientOrder);
        if (!CollectionUtils.isEmpty(req.getCombinedPayItems())) {
            CombinedPayItem payItem = req.getCombinedPayItems().get(0);
            sheetProcessor.setPayMode(payItem.getPayMode());
        }
        sheetProcessor.setChargeTransactionRecordService(chargeTransactionRecordService);
        sheetProcessor.build();

        chargeSheet = sheetProcessor.updateProductInfoAndBindMemberId();
        chargeExecuteService.createOrUpdateChargeExecuteItems(chargeSheet, true, operatorId);

        // 推送自助支付公众号消息
        new ChargeSheetTherapyPushOpsPatient(chargeSheet, operatorId, rocketMqProducer, weClinicChargeService, crmService, propertyService, chargeService, cisScClinicService, outpatientService).doPushToPatient();

        chargeService.goodsLockAndSaveBatchInfo(chargeSheet, GoodsLockScene.ONLY_LOCK_BILLING, false, operatorId);

        chargeSheetService.save(chargeSheet, null);

        chargeService.notifyChargeSheetStatusChanged(chargeSheet, operatorId, PatientOrderMessage.MSG_TYPE_CHARGE_CREATED);
        tobMessageService.pushChargeTodoMessage(chargeSheet.getChainId(), chargeSheet.getClinicId(), chargeSheet.getType());
        chargeService.doAfterChargeSheetChange(chargeSheet, Lists.newArrayList(), Lists.newArrayList(), Maps.newHashMap(), ChargeSheetMessage.MSG_TYPE_CREATED, null, null, operatorId);
        chargeService.sendPushAutoPrintMessageToWeb(chargeSheet.getId(), chargeSheet.getChainId(), chargeSheet.getClinicId(), chargeSheet.getPatientOrderId(), chargeSheet.getType(), ChargeSheetFeeProtocol.generatePrintable(chargeSheet, patientOrder, () -> sheetProcessor.getSheetProcessorInfoProvider().getPropertyProvider().getPrintMedicalDocumentsInfusionAndTreatmentContent(patientOrder.getClinicId())));
        CreateChargeSheetForTherapyRsp rsp = new CreateChargeSheetForTherapyRsp();

        rsp.setStatusName(StatusNameTranslator.translateChargeSheetStatus(chargeSheet.getOwedStatus(), chargeSheet.getStatus()));
        rsp.setStatus(chargeSheet.getStatus());
        rsp.setPatientOrderId(chargeSheet.getPatientOrderId());
        rsp.setId(chargeSheet.getId());

        return rsp;
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public List<ChargeSheetAbstract> findChargeSheetAbstractTodayList(String clinicId, String ownerId) throws ServiceInternalException {
        ZonedDateTime now = ZonedDateTime.now();
        LocalDateTime startOfDay = now.toLocalDate().atStartOfDay();
        Instant startOfDayInstant = startOfDay.atOffset(now.getOffset()).toInstant();
        // onlyShowContainsExecute保留兼容chargeSheet老数据，如果includeItemType里面包含了执行项，需要把老数据中包含执行项的查出来，其他的不查出来
        boolean onlyShowContainsExecute = false;
        boolean onlyExecuteAfterPaid = false;
        Integer includeItemType = null;
        ClinicNurseSettings clinicNurseSettings = propertyService.getPropertyValueByKey(PropertyKey.CLINIC_NURSE_SETTINGS, clinicId, ClinicNurseSettings.class);
        onlyExecuteAfterPaid = clinicNurseSettings.getOnlyExecuteAfterPaid() == 1;
        List<Integer> newListDisplaySetting = clinicNurseSettings.getNewListDisplaySetting();

        for (Integer itemType : newListDisplaySetting) {
            includeItemType = Objects.isNull(includeItemType) ? itemType : (includeItemType | itemType);
        }
        if (newListDisplaySetting.contains(Constants.ChargeSheetIncludeItemType.NEED_EXECUTE_TREATMENT)) {
            onlyShowContainsExecute = true;
        } else if (newListDisplaySetting.contains(0)) {
            // 展示全部单据
            includeItemType = null;
        }
        return chargeMapper.findChargeSheetAbstractListForNurse(-1, -1, clinicId, null, startOfDayInstant, null, StringUtils.isNotBlank(ownerId) ? Lists.newArrayList(ownerId) : null, onlyShowContainsExecute, includeItemType, onlyExecuteAfterPaid, false, null, false, null);
    }

    /**
     * 已执行单据详情权限校验
     *
     * @param chainId       门店id
     * @param employeeId    用户id
     * @param chargeSheetId 收费单id
     * @return 结果
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public ChargeSheetPrivilegeForNurseCheckRsp checkChargeSheetPrivilegeForNurse(String chainId, String employeeId, String chargeSheetId) {
        // 1、获取收费单信息
        ChargeSheet chargeSheet = chargeSheetRepository.findByIdAndChainIdAndIsDeleted(chargeSheetId, chainId, 0).orElse(null);
        if (Objects.isNull(chargeSheet)) {
            throw new NotFoundException();
        }

        ChargeSheetPrivilegeForNurseCheckRsp privilegeCheckRsp = new ChargeSheetPrivilegeForNurseCheckRsp();

        // 3、当前人在开单门店是管理员,可以看见
        boolean isAdmin = Objects.equals(Optional.ofNullable(cisScClinicService.queryEmployeeClinicInfoById(employeeId, chargeSheet.getClinicId()))
                        .map(ClinicEmployee::getClinicInfo)
                        .map(EmployeeClinicInfo::getRoleId)
                        .orElse(null),
                EmployeeClinicInfo.RoleId.ADMIN);

        // 4、获取执行站权限设置
        ClinicDataPermissionView clinicDataPermissionView = clinicService.getDataPermission(chainId, chargeSheet.getClinicId());
        Integer showExecutedRecords = Optional.ofNullable(clinicDataPermissionView)
                .map(ClinicDataPermissionView::getNurse)
                .map(ClinicDataPermissionNurse::getExecutedSheetDetail)
                .orElse(0);
        Integer showHistorySheet = Optional.ofNullable(clinicDataPermissionView)
                .map(ClinicDataPermissionView::getNurse)
                .map(ClinicDataPermissionNurse::getHistorySheet)
                .orElse(0);
        // 是否是开单人
        boolean isOwner = StringUtils.equals(chargeSheet.getSellerId(), employeeId) || StringUtils.equals(chargeSheet.getDoctorId(), employeeId);
        return privilegeCheckRsp
                .setEnableShowExecutedRecords(
                        genShowExecutedRecords(chargeSheet, showExecutedRecords, employeeId, isAdmin, isOwner)
                ).setEnableShowHistorySheet(
                        genShowHistorySheet(chargeSheet, showHistorySheet, employeeId, isAdmin, isOwner)
                );
    }

    private int genShowHistorySheet(ChargeSheet chargeSheet, Integer showHistorySheet, String employeeId, boolean isAdmin, boolean isOwner) {
        // 如果是管理员
        if (isAdmin) {
            return 1;
        }
        // 所有人可看
        if (Objects.equals(showHistorySheet, 0)) {
            return 1;
        } else if (Objects.equals(showHistorySheet, 2)) {
            // 不能查看
            return 0;
        }
        // 是开单人
        if (isOwner) {
            return 1;
        }
        // 查询是否是助理医生
        QueryAssistDoctorRsp queryAssistDoctorRsp = clinicService.getAssistDoctors(chargeSheet.getClinicId(), employeeId);
        if (Objects.nonNull(queryAssistDoctorRsp)) {
            // 助理全部医生
            if (Objects.equals(queryAssistDoctorRsp.getIsAssistAll(), 1)
                    || Optional.ofNullable(queryAssistDoctorRsp.getAssistDoctors())
                    .orElse(Lists.newArrayList())
                    .stream()
                    .anyMatch(assistDoctor -> StringUtils.equals(assistDoctor.getId(), chargeSheet.getDoctorId()))) {
                return 1;
            }
        }
        // 判断是否是执行人
        return executeRecordExecutorRepository.existsByChainIdAndChargeSheetIdAndExecutorIdAndExecuteRecordStatus(chargeSheet.getChainId(), chargeSheet.getId(), employeeId, ChargeExecuteRecord.ExecuteRecordStatus.EXECUTED) ? 1 : 0;
    }

    private int genShowExecutedRecords(ChargeSheet chargeSheet, Integer showExecutedRecords, String employeeId, boolean isAdmin, boolean isOwner) {
        // 1、获取执行状态，不是已执行状态
        if (!Objects.equals(chargeSheet.getExecuteStatus(), ChargeSheet.ExecuteStatus.FINISHED)) {
            return 1;
        }
        // 如果是管理员
        if (isAdmin) {
            return 1;
        }
        // 所有人可看
        if (Objects.equals(showExecutedRecords, 0)) {
            return 1;
        } else if (Objects.equals(showExecutedRecords, 2)) {
            // 不能查看
            return 0;
        }
        // 是开单人
        if (isOwner) {
            return 1;
        }
        // 判断是否是执行人
        return executeRecordExecutorRepository.existsByChainIdAndChargeSheetIdAndExecutorIdAndExecuteRecordStatus(chargeSheet.getChainId(), chargeSheet.getId(), employeeId, ChargeExecuteRecord.ExecuteRecordStatus.EXECUTED) ? 1 : 0;
    }

    /**
     * 查询我的执行列表
     *
     * @param chainId    连锁id
     * @param clinicId   门店id
     * @param employeeId 用户id
     * @param beginTime  开始时间
     * @param endTime    结束时间
     * @param offset     分页下标
     * @param limit      分页条数
     * @return 结果
     */
    public ChargeSheetAbstractListRsp findMyExecutedChargeSheetAbstractList(String chainId,
                                                                            String clinicId,
                                                                            String employeeId,
                                                                            Instant beginTime,
                                                                            Instant endTime,
                                                                            Integer offset,
                                                                            Integer limit) {
        ChargeSheetAbstractListRsp rsp = new ChargeSheetAbstractListRsp()
                .setLimit(limit)
                .setOffset(offset)
                .setResult(Lists.newArrayList());
        // 1、统计总条数
        int total = chargeExecuteRecordExecutorMapper.countMyExecutedChargeSheet(clinicId, employeeId, beginTime, endTime);
        rsp.setTotalCount(total);
        if (total == 0) {
            return rsp;
        }

        // 2、查询指定时间范围内用户参与过的有效执行记录的收费单信息
        List<ExecuteRecordExecutorChargeSheetDTO> executorChargeSheets = chargeExecuteRecordExecutorMapper.pageMyExecutedChargeSheet(clinicId, employeeId, beginTime, endTime, offset, limit);
        if (CollectionUtils.isEmpty(executorChargeSheets)) {
            return rsp;
        }
        List<String> chargeSheetIds = executorChargeSheets.stream().map(ExecuteRecordExecutorChargeSheetDTO::getChargeSheetId).collect(Collectors.toList());
        Map<String, Instant> chargeSheetIdLastExecuteTime = executorChargeSheets.stream()
                .collect(Collectors.toMap(ExecuteRecordExecutorChargeSheetDTO::getChargeSheetId, ExecuteRecordExecutorChargeSheetDTO::getMaxCreated));

        // 3、查询收费单信息
        List<ChargeSheetAbstract> chargeSheetAbstracts = chargeMapper.listChargeSheetAbstractListByIds(chainId, chargeSheetIds)
                .stream()
                .peek(chargeSheetAbstract -> chargeSheetAbstract.setLastExecuteTime(chargeSheetIdLastExecuteTime.get(chargeSheetAbstract.getId())))
                .sorted(Comparator.comparing(chargeSheetAbstract -> chargeSheetIds.indexOf(chargeSheetAbstract.getId())))
                .collect(Collectors.toList());

        // 4、为收费单添加额外信息
        appendExtraInfoToChargeSheetAbstract(chainId, chargeSheetAbstracts);

        return rsp.setResult(chargeSheetAbstracts);
    }

    private void appendExtraInfoToChargeSheetAbstract(String chainId, List<ChargeSheetAbstract> results) {
        // 绑定患者和医生信息
        chargeService.bindPatientInfoAndDoctorNameForChargeSheetAbstract(results, chainId);

        // 追加门店是否需要收费后才可执行 设置
        List<PropertyInfo> propertyInfos = results
                .stream()
                .map(ChargeSheetAbstract::getClinicId)
                .distinct()
                .map(chargeSheetClinicId -> {
                    PropertyInfo propertyInfo = new PropertyInfo();
                    propertyInfo.setScopeId(chargeSheetClinicId);
                    propertyInfo.setPropertyKey(PropertyKey.CLINIC_NURSE_SETTINGS);
                    return propertyInfo;
                })
                .collect(Collectors.toList());
        Map<String, Integer> clinicIdExecuteNeedPayMap = propertyService.batchGetConfigItems(propertyInfos)
                .stream()
                .collect(Collectors.toMap(
                                PropertyConfigItem::getScopeId,
                                configItem -> Optional.ofNullable(JsonUtils.readValue(configItem.getValue(), ClinicNurseSettings.class))
                                        .map(ClinicNurseSettings::getOnlyExecuteAfterPaid)
                                        .orElse(0)
                        )
                );
        results.forEach(chargeSheetAbstract -> chargeSheetAbstract.setOnlyExecuteAfterPaid(clinicIdExecuteNeedPayMap.get(chargeSheetAbstract.getClinicId())));
        StatusNameTranslator.translateForNurse(results);
        // 护士站收费单摘要
        if (!CollectionUtils.isEmpty(results)) {
            List<String> chargeSheetIds = results.stream().map(ChargeSheetAbstract::getId).collect(Collectors.toList());
            Map<String, List<ChargeExecuteItem>> chargeSheetExecuteItemsMap = ListUtils.groupByKey(chargeExecuteItemRepository.findByChargeSheetIdInAndIsDeleted(chargeSheetIds, 0), ChargeExecuteItem::getChargeSheetId);
            // 存在chargeForm 删除，但是chargeFormItem未删除的数据，所以先查从chargeForm
            Map<String, List<ChargeFormItem>> chargeSheetIdItemsMap = ListUtils.groupByKey(
                    chargeFormItemRepository.findByChargeFormIdInAndIsDeleted(
                            chargeFormRepository.findByChargeSheetIdInAndIsDeleted(chargeSheetIds, 0)
                                    .stream()
                                    .map(ChargeForm::getId)
                                    .collect(Collectors.toList()),
                            0),
                    ChargeFormItem::getChargeSheetId
            );
            Map<String, ChargeSheetAdditional> chargeSheetAdditionalMap = ListUtils.toMap(chargeSheetAdditionalRepository.findByIdInAndIsDeleted(chargeSheetIds, 0), ChargeSheetAdditional::getId);

            results.forEach(result -> {
                String resultId = result.getId();
                result.setNurseAbstractInfo(
                        ChargeUtils.generateNurseAbstractInfo(result.getType(),
                                chargeSheetExecuteItemsMap.getOrDefault(resultId, Lists.newArrayList()),
                                chargeSheetIdItemsMap.getOrDefault(resultId, Lists.newArrayList()),
                                chargeSheetAdditionalMap.getOrDefault(resultId, new ChargeSheetAdditional()))
                );
            });
        }
    }
}
