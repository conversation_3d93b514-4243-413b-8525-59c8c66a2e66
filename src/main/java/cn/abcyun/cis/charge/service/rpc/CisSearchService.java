package cn.abcyun.cis.charge.service.rpc;

import cn.abcyun.bis.rpc.sdk.cis.client.AbcCisSearchFeignClient;
import cn.abcyun.bis.rpc.sdk.cis.model.search.CdssQLSearchResultItem;
import cn.abcyun.bis.rpc.sdk.cis.model.search.CdssSearchResultRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.search.SearchResultRsp;
import cn.abcyun.cis.charge.util.DateUtils;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.core.util.FeignClientRpcTemplate;
import feign.RetryableException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

/**
 * search client service
 *
 * <AUTHOR>
 * @version CisSearchService.java, 2021/9/10 下午2:17
 */
@Service
public class CisSearchService {
    private final AbcCisSearchFeignClient searchFeignClient;

    @Autowired
    public CisSearchService(AbcCisSearchFeignClient searchFeignClient) {
        this.searchFeignClient = searchFeignClient;
    }

    /**
     * 搜索收费单
     *
     * @param chainId         连锁id
     * @param clinicId        门店id
     * @param keyword         搜索关键次
     * @param source          查询源头 收费单/执行单
     * @param withUndiagnosed 是否查询未诊的
     * @param scope           0 本门店 1兄弟门店 2所有门店【暂时无需求】
     * @param filterFlag      00：所有的；01：不看收费状态，包含执行项的； 10：已收费，所有的； 11：已收费，包含执行项的；100：挂单
     * @param includeItemType 收费单包含了哪些收费项，使用位运算来存储，example：包含诊疗项目和输注和皮试 1|2|8 = 11，数据库中存储的就是11
     * @param offset          分页下标
     * @param limit           分页条数
     * @return 结果
     */
    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public CdssSearchResultRsp<CdssQLSearchResultItem> searchCharge(String chainId,
                                                                    String clinicId,
                                                                    String keyword,
                                                                    String source,
                                                                    Integer withUndiagnosed,
                                                                    Integer scope,
                                                                    Integer filterFlag,
                                                                    Integer includeItemType,
                                                                    Integer offset,
                                                                    Integer limit,
                                                                    List<Integer> statusList,
                                                                    List<Integer> typeList,
                                                                    Instant beginTime,
                                                                    Instant endTime) {
        if (StringUtils.isBlank(chainId)) {
            throw new ParamRequiredException("chainId");
        }
        if (StringUtils.isBlank(clinicId)) {
            throw new ParamRequiredException("clinicId");
        }
        if (StringUtils.isBlank(keyword)) {
            throw new ParamRequiredException("keyword");
        }
        if (StringUtils.isBlank(source)) {
            throw new ParamRequiredException("source");
        }

        String beginTimeStr = beginTime != null ? DateUtils.convertInstantToString(beginTime, "yyyy-MM-dd HH:mm:ss") : null;
        String endTimeStr = endTime != null ? DateUtils.convertInstantToString(endTime, "yyyy-MM-dd HH:mm:ss") : null;

        return FeignClientRpcTemplate.dealRpcClientMethod("searchCharge",
                () -> searchFeignClient.searchCharge(chainId, clinicId, keyword, source, withUndiagnosed, scope, filterFlag,
                        includeItemType, offset, limit, statusList, typeList, beginTimeStr, endTimeStr),
                chainId, clinicId, keyword, source, withUndiagnosed, scope, filterFlag, includeItemType, offset, limit
                , beginTimeStr, endTimeStr);
    }

//    public CdssSearchResultRsp<CdssQLSearchResultItem> searchCharge(String chainId,
//                                                                    String clinicId,
//                                                                    String keyword,
//                                                                    String source,
//                                                                    Integer withUndiagnosed,
//                                                                    Integer scope,
//                                                                    Integer filterFlag,
//                                                                    Integer includeItemType,
//                                                                    String goodsId,
//                                                                    Integer invoiceStatus,
//                                                                    Integer offset,
//                                                                    Integer limit) {
//        if (StringUtils.isBlank(chainId)) {
//            throw new ParamRequiredException("chainId");
//        }
//        if (StringUtils.isBlank(clinicId)) {
//            throw new ParamRequiredException("clinicId");
//        }
//        if (StringUtils.isBlank(keyword)) {
//            throw new ParamRequiredException("keyword");
//        }
//        if (StringUtils.isBlank(source)) {
//            throw new ParamRequiredException("source");
//        }
//
//        return FeignClientRpcTemplate.dealRpcClientMethod("searchCharge",
//                () -> searchFeignClient.searchCharge(chainId, clinicId, keyword, source, withUndiagnosed, scope, filterFlag, includeItemType, offset, limit),
//                chainId, clinicId, keyword, source, withUndiagnosed, scope, filterFlag, includeItemType, offset, limit);
//    }

    /**
     * 搜索收费单数量
     *
     * @param chainId         连锁id
     * @param clinicId        门店id
     * @param keyword         搜索关键次
     * @param source          查询源头 收费单/执行单
     * @param withUndiagnosed 是否查询未诊的
     * @param scope           0 本门店 1兄弟门店 2所有门店【暂时无需求】
     * @param filterFlag      0 所有的  1 包含执行项的
     * @param includeItemType 收费单包含了哪些收费项，使用位运算来存储，example：包含诊疗项目和输注和皮试 1|2|8 = 11，数据库中存储的就是11
     * @return 结果
     */
    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public long searchChargeCount(String chainId,
                                  String clinicId,
                                  String keyword,
                                  String source,
                                  Integer withUndiagnosed,
                                  Integer scope,
                                  Integer filterFlag,
                                  Integer includeItemType,
                                  List<Integer> statusList,
                                  List<Integer> typeList,
                                  Instant beginTime,
                                  Instant endTime) {
        if (StringUtils.isBlank(chainId)) {
            throw new ParamRequiredException("chainId");
        }
        if (StringUtils.isBlank(clinicId)) {
            throw new ParamRequiredException("clinicId");
        }
        if (StringUtils.isBlank(keyword)) {
            throw new ParamRequiredException("keyword");
        }
        if (StringUtils.isBlank(source)) {
            throw new ParamRequiredException("source");
        }

        String beginTimeStr = beginTime != null ? DateUtils.convertInstantToString(beginTime, "yyyy-MM-dd HH:mm:ss") : null;
        String endTimeStr = endTime != null ? DateUtils.convertInstantToString(endTime, "yyyy-MM-dd HH:mm:ss") : null;

        // @TODO 接口兼容问题
        CdssSearchResultRsp<CdssQLSearchResultItem> searchChargeCountRsp = FeignClientRpcTemplate.dealRpcClientMethod("searchChargeCount",
                () -> searchFeignClient.searchChargeCount(chainId, clinicId, keyword, source, withUndiagnosed, scope,
                        filterFlag, includeItemType, statusList, typeList, beginTimeStr, endTimeStr),
                chainId, clinicId, keyword, source, withUndiagnosed, scope, filterFlag, includeItemType, statusList, beginTimeStr, endTimeStr);

        return Optional.ofNullable(searchChargeCountRsp)
                .map(CdssSearchResultRsp::getTotal)
                .orElse(0L);

    }

    /**
     * 搜索收费单数量
     *
     * @param chainId         连锁id
     * @param clinicId        门店id
     * @param keyword         搜索关键词
     * @return 结果
     */
    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public SearchResultRsp searchChargeCooperationOrder(String chainId,
                                  String clinicId,
                                  String keyword) {
        if (StringUtils.isBlank(chainId)) {
            throw new ParamRequiredException("chainId");
        }
        if (StringUtils.isBlank(clinicId)) {
            throw new ParamRequiredException("clinicId");
        }
        if (StringUtils.isBlank(keyword)) {
            throw new ParamRequiredException("keyword");
        }

        String clientName = "v1-cis-charge-cooperation-order";

        return FeignClientRpcTemplate.dealRpcClientMethod("searchChargeCooperationOrder",
                () -> searchFeignClient.doSearch(chainId, clinicId, null, clientName, keyword),
                chainId, clinicId, null, clientName, keyword);

    }
}
