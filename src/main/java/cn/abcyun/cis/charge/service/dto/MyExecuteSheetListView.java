package cn.abcyun.cis.charge.service.dto;

import cn.abcyun.cis.commons.rpc.charge.ChargeFormItemExecuteView;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.Instant;
import java.util.List;

/**
 * 微诊所患者我的治疗理疗单
 *
 * <AUTHOR>
 * @version WeClinicMyExecuteSheetListView.java, 2020/8/13 下午2:15
 */
@Data
@Accessors(chain = true)
public class MyExecuteSheetListView {
    /**
     * 收费单id
     */
    private String                          chargeSheetId;
    /**
     * 患者id
     */
    private String                          patientId;
    /**
     * 患者姓名
     */
    private String                          patientName;
    /**
     * 收费单创建时间
     */
    private Instant                         created;
    /**
     * 展示状态
     */
    private String                          displayStatusName;
    /**
     * 收费单执行项集合
     */
    private List<ChargeFormItemExecuteView> executeItems;
}
