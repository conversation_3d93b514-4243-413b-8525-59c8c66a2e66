package cn.abcyun.cis.charge.service;

import cn.abcyun.bis.rpc.sdk.cis.client.AbcCisShebaoFeignClient;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsSheBaoMatchedCodesInfo;
import cn.abcyun.bis.rpc.sdk.cis.model.pay.shebao.ShebaoInvoiceRemarkReq;
import cn.abcyun.bis.rpc.sdk.cis.model.pay.shebao.ShebaoInvoiceRemarkRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.shebao.*;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.processor.provider.ShebaoInfoProvider;
import cn.abcyun.cis.charge.rpc.client.ShebaoClient;
import cn.abcyun.cis.charge.util.JsonUtils;
import cn.abcyun.cis.charge.util.ListUtils;
import cn.abcyun.cis.commons.CisServiceResponseBody;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.exception.ServiceInternalException;
import cn.abcyun.cis.commons.rpc.shebao.QueryChargeSheetShebaoInfoReq;
import cn.abcyun.cis.commons.rpc.shebao.QueryChargeSheetShebaoInfoRsp;
import cn.abcyun.cis.core.util.FeignClientRpcTemplate;
import cn.abcyun.common.log.marker.AbcLogMarker;
import cn.abcyun.common.model.AbcServiceResponseBody;
import com.google.common.collect.Lists;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixProperty;
import feign.Request;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
@EnableAspectJAutoProxy(exposeProxy = true)
public class ShebaoService implements ShebaoInfoProvider {
    @Autowired
    private ShebaoClient shebaoClient;
    @Autowired
    private AbcCisShebaoFeignClient cisShebaoFeignClient;

    @Override
    @Retryable
    public QueryChargeSheetShebaoInfoRsp queryChargeSheetShebaoInfo(QueryChargeSheetShebaoInfoReq req) {
        QueryChargeSheetShebaoInfoRsp rsp = null;
        log.info("queryChargeSheetShebaoInfo req: {}", JsonUtils.dump(req));
        try {
            CisServiceResponseBody<QueryChargeSheetShebaoInfoRsp> rspBody = shebaoClient.queryChargeSheetShebaoInfo(req);
            if (rspBody != null) {
                rsp = rspBody.getData();
            }
            log.info("queryChargeSheetShebaoInfo rsp: {}", JsonUtils.dump(rsp));
        } catch (Exception e) {
            log.error("queryChargeSheetShebaoInfo", e);
            throw e;
        }

        return rsp;
    }

    @HystrixCommand(fallbackMethod = "getInvoiceRemarkFallback",
            commandProperties = {
                    @HystrixProperty(name = "execution.isolation.thread.timeoutInMilliseconds", value = "2000"),
                    @HystrixProperty(name = "execution.isolation.strategy", value = "THREAD")
            }
    )
    public ShebaoInvoiceRemarkRsp getInvoiceRemark(String chainId, String clinicId, String taskId, BigDecimal receivableFee) {
        ShebaoInvoiceRemarkReq req = new ShebaoInvoiceRemarkReq();
        req.setChainId(chainId);
        req.setClinicId(clinicId);
        req.setTaskId(taskId);
        req.setChargeSheetReceivableFee(receivableFee);
        return FeignClientRpcTemplate.dealRpcClientMethod("getInvoiceRemark", () -> cisShebaoFeignClient.getInvoiceRemarkV2(taskId, req), req);
    }

    public ShebaoInvoiceRemarkRsp getInvoiceRemarkFallback(String chainId, String clinicId, String taskId, BigDecimal receivableFee) {
        log.error(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "getInvoiceRemarkFallback chainId: {} clinicId: {}, taskId: {}, receivableFee: {}", chainId, clinicId, taskId, receivableFee);
        return new ShebaoInvoiceRemarkRsp();
    }

    public boolean isShebaoPayEnable(String clinicId) {
        boolean ret = false;

        if (org.apache.commons.lang3.StringUtils.isEmpty(clinicId)) {
            return ret;
        }

        ShebaoService shebaoService = (ShebaoService) AopContext.currentProxy();
        ClinicConfig shebaoClinicConfig = shebaoService.getShebaoClinicConfig(clinicId);

        if (Objects.isNull(shebaoClinicConfig)) {
            return ret;
        }

        return shebaoClinicConfig.getStatus() == ClinicConfig.Status.ENABLE_PAY;
    }

    /**
     * 是否支持挂网价限价
     * @param clinicId
     * @return
     */
    @Override
    public boolean isEnableListingPrice(String clinicId) {
        boolean ret = false;

        if (org.apache.commons.lang3.StringUtils.isEmpty(clinicId)) {
            return ret;
        }

        ShebaoService shebaoService = (ShebaoService) AopContext.currentProxy();
        ClinicConfig shebaoClinicConfig = shebaoService.getShebaoClinicConfig(clinicId);

        if (Objects.isNull(shebaoClinicConfig)) {
            return ret;
        }

        return shebaoClinicConfig.getLimitListingPriceSwitch() == 1;
    }

    @HystrixCommand(fallbackMethod = "getShebaoClinicConfigFallback",
            commandProperties = {
                    @HystrixProperty(name = "execution.isolation.thread.timeoutInMilliseconds", value = "3000"),
                    @HystrixProperty(name = "execution.isolation.strategy", value = "THREAD")
            }
    )
    public ClinicConfig getShebaoClinicConfig(String clinicId) {
        return FeignClientRpcTemplate.dealRpcClientMethod("getClinicConfig", () -> cisShebaoFeignClient.getClinicConfig(clinicId), clinicId);
    }

    public ClinicConfig getShebaoClinicConfigFallback(String clinicId) {
        log.error(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "getShebaoClinicConfigFallback clinicId: {}", clinicId);
        throw new ServiceInternalException("社保服务异常");
    }


    public boolean isShebaoPayEnableWithHystrix(String chainId, String clinicId, int paySubMode) {
        // 如果是微信社保
        if (Objects.equals(paySubMode, Constants.ChargePaySubMode.WECHAT_MOBILE_SHEBAO)) {
            return isHealthOpenMobileShebao(chainId, clinicId);
        }
        return isShebaoPayEnable(clinicId);
    }

    /**
     * 校验门店是否开通移动医保
     *
     * @param chainId  连锁id
     * @param clinicId 门店id
     * @return 结果
     */
    public boolean isHealthOpenMobileShebao(String chainId, String clinicId) {
        if (StringUtils.isBlank(chainId)) {
            throw new ParamRequiredException("chainId");
        }
        if (StringUtils.isBlank(clinicId)) {
            throw new ParamRequiredException("clinicId");
        }

        return Optional
                .ofNullable(
                        cn.abcyun.cis.core.util.FeignClientRpcTemplate.dealRpcClientMethod("getClinicsMobilePayAuthParams",
                                () -> cisShebaoFeignClient.getMobilePayAuthParams(chainId),
                                chainId)
                )
                .map(MobilePayAuthParamsRspBody::getPayAuthParamsList)
                .orElse(Lists.newArrayList())
                .stream()
                .anyMatch(payAuthParams -> payAuthParams.getClinicId().equals(clinicId));
    }

    /**
     * 查询微信医保订单信息
     *
     * @param taskId   第三方支付taskId
     * @param chainId  连锁id
     * @param clinicId 门店id
     * @return 结果
     */
    public MobilePayOrderInfoRspBody getMobilePayOrderInfo(String taskId,
                                                           String chainId,
                                                           String clinicId) {
        if (StringUtils.isBlank(taskId)) {
            throw new ParamRequiredException("taskId");
        }
        if (StringUtils.isBlank(chainId)) {
            throw new ParamRequiredException("chainId");
        }
        if (StringUtils.isBlank(clinicId)) {
            throw new ParamRequiredException("clinicId");
        }

        return cn.abcyun.cis.core.util.FeignClientRpcTemplate.dealRpcClientMethod("getMobilePayOrderInfo",
                () -> cisShebaoFeignClient.getMobilePayOrderInfo(taskId, chainId, clinicId),
                taskId, chainId, clinicId
        );
    }

    @Override
    @Retryable
    public List<GoodsSheBaoMatchedCodesInfo> getMatchedSheBaoCodes(String chainId,
                                                                   String clinicId,
                                                                   boolean notQueryGoodsFlag,
                                                                   List<RpcGetShebaoMatchedCodesReq.GetSmartMatchedReqItem> items) {

        if (CollectionUtils.isEmpty(items)) {
            return new ArrayList<>();
        }

        RpcGetShebaoMatchedCodesReq req = new RpcGetShebaoMatchedCodesReq();
        req.setChainId(chainId);
        req.setClinicId(clinicId);
        req.setNotQueryGoodsFlag(notQueryGoodsFlag);
        req.setItemList(items);
        RpcGetShebaoMatchedCodesRsp getMatchedSheBaoCodes = FeignClientRpcTemplate.dealRpcClientMethod("getMatchedSheBaoCodes", true,
                () -> cisShebaoFeignClient.getMatchedSheBaoCodes(req, new Request.Options(25, TimeUnit.SECONDS, 25, TimeUnit.SECONDS, true)),
                req);

        return Optional.ofNullable(getMatchedSheBaoCodes).map(RpcGetShebaoMatchedCodesRsp::getItemList).orElse(new ArrayList<>());
    }

    @Override
    public Map<String, QueryRegistrationFeesMatchCodeResBody.RegistrationFeesMatchCode> queryRegistrationFeesMatchCode(String chainId, String clinicId, List<QueryRegistrationFeesMatchCodeReqBody.DoctorInfo> doctorInfoList) {
        QueryRegistrationFeesMatchCodeReqBody req = new QueryRegistrationFeesMatchCodeReqBody();
        req.setChainId(chainId);
        req.setClinicId(clinicId);
        req.setDoctorInfos(doctorInfoList);
        req.setIsFillDefaultMatchCode(true);


        QueryRegistrationFeesMatchCodeResBody registrationFeesMatchCode = FeignClientRpcTemplate.dealRpcClientMethod("queryRegistrationFeesMatchCode", () -> cisShebaoFeignClient.queryRegistrationFeesMatchCode(req), req);

        if (CollectionUtils.isEmpty(registrationFeesMatchCode.getMatchCodes())) {
            return new HashMap<>();
        }

        Map<String, QueryRegistrationFeesMatchCodeResBody.RegistrationFeesMatchCode> feesMatchCodeMap = ListUtils.toMap(registrationFeesMatchCode.getMatchCodes(), me -> generateRegistrationFeesMatchCodeKey(me.getDoctorId(), me.getDepartmentId()));


        return feesMatchCodeMap;
    }

    public static String generateRegistrationFeesMatchCodeKey(String doctorId, String departmentId) {
        if (StringUtils.isEmpty(doctorId) && StringUtils.isEmpty(departmentId)) {
            return null;
        }
        return String.format("%s-%s", doctorId, departmentId);
    }


    @Override
    public QueryProcessFeesMatchCodeResBody queryProcessFeesMatchCode(QueryProcessFeesMatchCodeReqBody req) {

        return FeignClientRpcTemplate.dealRpcClientMethod("queryProcessFeesMatchCode", () -> cisShebaoFeignClient.queryProcessFeesMatchCode(req), req);
    }

}
