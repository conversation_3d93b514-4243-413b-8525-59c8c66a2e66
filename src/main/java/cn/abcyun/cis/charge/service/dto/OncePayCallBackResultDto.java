package cn.abcyun.cis.charge.service.dto;

import cn.abcyun.cis.charge.model.ChargeAction;
import cn.abcyun.cis.charge.model.ChargeTransaction;
import cn.abcyun.cis.charge.model.ChargeTransactionRecord;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class OncePayCallBackResultDto {

    private ChargeAction chargeAction;

    private ChargeTransaction chargeTransaction;

    private List<ChargeTransactionRecord> addedChargeTransactionRecords;

    private List<ChargeTransactionRecord> toSaveChargeTransactionRecords;

    private ChargeSheetRelationDto chargeSheetRelationDto;

    private boolean isFirstPay;
}
