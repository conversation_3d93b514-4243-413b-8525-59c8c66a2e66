package cn.abcyun.cis.charge.service;

import cn.abcyun.bis.rpc.sdk.cis.message.charge.ChargePatientOweAmountChangedMessage;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ;
import cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientMemberInfo;
import cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientRelateMemberRsp;
import cn.abcyun.cis.charge.amqp.HAMQProducer;
import cn.abcyun.cis.charge.amqp.MQProducer;
import cn.abcyun.cis.charge.amqp.RocketMqProducer;
import cn.abcyun.cis.charge.api.model.PatientOweAmountInfo;
import cn.abcyun.cis.charge.api.model.PatientOweAmountInfoListRsp;
import cn.abcyun.cis.charge.api.model.owe.*;
import cn.abcyun.cis.charge.base.ChargeServiceError;
import cn.abcyun.cis.charge.base.ChargeServiceException;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.base.pay.CombinedPayItem;
import cn.abcyun.cis.charge.cold.ChargeFormItemDaoProxy;
import cn.abcyun.cis.charge.combinorder.dto.*;
import cn.abcyun.cis.charge.combinorder.model.ChargeCombineOrder;
import cn.abcyun.cis.charge.combinorder.service.CombineOrderService;
import cn.abcyun.cis.charge.mapper.ChargeOweSheetMapper;
import cn.abcyun.cis.charge.model.*;
import cn.abcyun.cis.charge.processor.StatChargeOweRecordProcessor;
import cn.abcyun.cis.charge.processor.provider.ChargeOweSheetProvider;
import cn.abcyun.cis.charge.repository.*;
import cn.abcyun.cis.charge.service.dto.ChargeSheetIdAndHospitalSheetIdDto;
import cn.abcyun.cis.charge.service.dto.ChargeSheetIdAndMemberIdDto;
import cn.abcyun.cis.charge.service.dto.ClinicPatientOweAmountInfoDto;
import cn.abcyun.cis.charge.service.dto.OweSheetCombinePayExtraInfo;
import cn.abcyun.cis.charge.service.rpc.CisCrmService;
import cn.abcyun.cis.charge.service.rpc.CisScClinicService;
import cn.abcyun.cis.charge.util.*;
import cn.abcyun.cis.commons.amqp.message.charge.ChargeOweSheetMessage;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.exception.ParamNotValidException;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.exception.ServiceInternalException;
import cn.abcyun.cis.core.db.UseReadOnlyDB;
import cn.abcyun.cis.core.redis.RedisLock;
import cn.abcyun.cis.core.util.ExecutorUtils;
import cn.abcyun.cis.idgenerator.AbcIdGenerator;
import cn.abcyun.common.log.marker.AbcLogMarker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ChargeOweSheetService implements ChargeOweSheetProvider {

    private final ChargeOweSheetRepository chargeOweSheetRepository;
    private final AbcIdGenerator abcIdGenerator;
    private final ChargeOweSheetLogRepository chargeOweSheetLogRepository;
    private final CombineOrderService combineOrderService;
    private final ChargeOweCombineTransactionRepository chargeOweCombineTransactionRepository;
    private final ChargeOweCombineTransactionRecordRepository chargeOweCombineTransactionRecordRepository;
    private final ChargeSheetRepository chargeSheetRepository;
    private final ChargeSheetService chargeSheetService;
    private final HAMQProducer hamqProducer;
    private final CisCrmService cisCrmService;
    private final ChargeTransactionRepository chargeTransactionRepository;
    private final ChargeTransactionRecordRepository chargeTransactionRecordRepository;

    private final ChargeTransactionRecordService chargeTransactionRecordService;
    private final ChargeOweCombineTransactionRecordDetailRepository oweCombineTransactionRecordDetailRepository;
    private final ChargeOweCombineTransactionRecordDetailService oweCombineTransactionRecordDetailService;
    private final ChargeFormItemDaoProxy chargeFormItemRepository;

    private final ChargeOweSheetMapper chargeOweSheetMapper;

    private final MQProducer mqProducer;
    private final CisScClinicService scClinicService;
    private final RocketMqProducer rocketMqProducer;

    @Autowired
    public ChargeOweSheetService(ChargeOweSheetRepository chargeOweSheetRepository,
                                 AbcIdGenerator abcIdGenerator,
                                 ChargeOweSheetLogRepository chargeOweSheetLogRepository,
                                 CombineOrderService combineOrderService,
                                 ChargeOweCombineTransactionRepository chargeOweCombineTransactionRepository,
                                 ChargeOweCombineTransactionRecordRepository chargeOweCombineTransactionRecordRepository,
                                 ChargeSheetRepository chargeSheetRepository,
                                 ChargeSheetService chargeSheetService,
                                 HAMQProducer hamqProducer,
                                 CisCrmService cisCrmService,
                                 ChargeTransactionRepository chargeTransactionRepository,
                                 ChargeTransactionRecordRepository chargeTransactionRecordRepository,
                                 ChargeTransactionRecordService chargeTransactionRecordService,
                                 ChargeOweCombineTransactionRecordDetailRepository oweCombineTransactionRecordDetailRepository,
                                 ChargeOweCombineTransactionRecordDetailService oweCombineTransactionRecordDetailService,
                                 ChargeFormItemDaoProxy chargeFormItemRepository,
                                 ChargeOweSheetMapper chargeOweSheetMapper,
                                 MQProducer mqProducer,
                                 CisScClinicService scClinicService,
                                 RocketMqProducer rocketMqProducer) {
        this.chargeOweSheetRepository = chargeOweSheetRepository;
        this.abcIdGenerator = abcIdGenerator;
        this.chargeOweSheetLogRepository = chargeOweSheetLogRepository;
        this.combineOrderService = combineOrderService;
        this.chargeOweCombineTransactionRepository = chargeOweCombineTransactionRepository;
        this.chargeOweCombineTransactionRecordRepository = chargeOweCombineTransactionRecordRepository;
        this.chargeSheetRepository = chargeSheetRepository;
        this.chargeSheetService = chargeSheetService;
        this.hamqProducer = hamqProducer;
        this.cisCrmService = cisCrmService;
        this.chargeTransactionRepository = chargeTransactionRepository;
        this.chargeTransactionRecordRepository = chargeTransactionRecordRepository;
        this.chargeTransactionRecordService = chargeTransactionRecordService;
        this.oweCombineTransactionRecordDetailRepository = oweCombineTransactionRecordDetailRepository;
        this.oweCombineTransactionRecordDetailService = oweCombineTransactionRecordDetailService;
        this.chargeFormItemRepository = chargeFormItemRepository;
        this.chargeOweSheetMapper = chargeOweSheetMapper;
        this.mqProducer = mqProducer;
        this.scClinicService = scClinicService;
        this.rocketMqProducer = rocketMqProducer;
    }

    /**
     * 新增或修改chargeOweSheet
     *
     * @param chargeSheet
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void insertOrUpdateChargeOweSheet(ChargeSheet chargeSheet, ChargeTransaction chargeTransaction, String operatorId) {

        if (Objects.isNull(chargeSheet) || Objects.isNull(chargeTransaction)) {
            return;
        }

        int payMode = chargeTransaction.getPayMode();

        if (payMode != Constants.ChargePayMode.OWE_PAY || MathUtils.wrapBigDecimalCompare(chargeTransaction.getAmount(), BigDecimal.ZERO) == 0) {
            return;
        }

        ChargeOweSheet chargeOweSheet = chargeOweSheetRepository.findByChargeSheetIdAndClinicIdAndIsOldRecordAndIsDeleted(chargeSheet.getId(), chargeSheet.getClinicId(), 0, 0);
        boolean isUpdate = false;
        if (chargeOweSheet == null) {
            //新建
            chargeOweSheet = new ChargeOweSheet();
            chargeOweSheet.setId(abcIdGenerator.getUIDLong())
                    .setChargeSheetId(chargeSheet.getId())
                    .setChainId(chargeSheet.getChainId())
                    .setClinicId(chargeSheet.getClinicId())
                    .setPatientId(chargeSheet.getPatientId())
                    .setReceivedPrice(BigDecimal.ZERO)
                    .setRefundedPrice(BigDecimal.ZERO)
                    .setTotalPrice(MathUtils.max(chargeTransaction.getAmount(), BigDecimal.ZERO))
                    .setStatus(ChargeOweSheet.Status.UNCHARGED);

            FillUtils.fillCreatedBy(chargeOweSheet, operatorId);
        } else {
            isUpdate = true;
            if (chargeOweSheet.getStatus() != ChargeOweSheet.Status.UNCHARGED) {
                log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "欠费单不是待收费状态，不能进行再次欠费");
                throw new ChargeServiceException(ChargeServiceError.CHARGE_OWE_SHEET_STATUS_ERROR);
            }

            chargeOweSheet.setTotalPrice(MathUtils.max(MathUtils.wrapBigDecimalAdd(chargeOweSheet.getTotalPrice(), chargeTransaction.getAmount()), BigDecimal.ZERO));
            FillUtils.fillLastModifiedBy(chargeOweSheet, operatorId);
        }

        insertChargeOweSheetLog(chargeOweSheet, chargeTransaction, operatorId);

        //更新收费单的欠费状态
        if (MathUtils.wrapBigDecimalCompare(chargeOweSheet.getTotalPrice(), BigDecimal.ZERO) <= 0) {
            chargeSheet.setOwedStatus(ChargeSheet.OwedStatus.NO_OWED);
            chargeOweSheet.setIsDeleted(1)
                    .setObsoleteMillis(System.currentTimeMillis());
        } else {
            chargeSheet.setOwedStatus(ChargeSheet.OwedStatus.OWING);
        }

        chargeOweSheetRepository.save(chargeOweSheet);
        //发送患者欠费消息
        notifyPatientOweAmountMessage(chargeOweSheet.getChainId(), chargeOweSheet.getClinicId(), Collections.singletonList(chargeOweSheet.getPatientId()), operatorId);

        if (!isUpdate) {
            if (chargeOweSheet.getIsDeleted() == 1) {
                return;
            }

            hamqProducer.notifyChargeOweSheetMessage(Arrays.asList(chargeOweSheet), ChargeOweSheetMessage.Type.MSG_TYPE_CREATED, operatorId);
        }

    }

    private void notifyPatientOweAmountMessage(String chainId, String clinicId, List<String> patientIds, String operatorId) {
        if (StringUtils.isAnyEmpty(chainId, clinicId, operatorId)) {
            return;
        }

        if (CollectionUtils.isEmpty(patientIds)) {
            return;
        }

        MQProducer.doAfterTransactionCommit(() -> ExecutorUtils.futureRunAsync(() -> {
            //1、查询患者的欠费金额
            List<PatientOweAmountInfo> patientOweAmountInfos = Optional.ofNullable(getPatientOweAmountInfoListByPatientIds(chainId, clinicId, patientIds))
                    .map(PatientOweAmountInfoListRsp::getPatientOweAmountInfos)
                    .orElse(new ArrayList<>());

            if (CollectionUtils.isEmpty(patientOweAmountInfos)) {
                return;
            }

            //2、发送患者欠费消息
            patientOweAmountInfos.forEach(patientOweAmountInfo -> {
                ChargePatientOweAmountChangedMessage patientOweAmountMessage = new ChargePatientOweAmountChangedMessage();
                BeanUtils.copyProperties(patientOweAmountInfo, patientOweAmountMessage);
                patientOweAmountMessage.setChainId(chainId);
                patientOweAmountMessage.setClinicId(clinicId);
                patientOweAmountMessage.setOperatorId(operatorId);
                rocketMqProducer.sendChargePatientOweAmountChangedName(patientOweAmountMessage);
            });
        }));
    }

    @Override
    public List<ChargeOweSheet> findChargeOweSheetsByChargeSheetId(String clinicId, String chargeSheetId) {
        if (StringUtils.isAnyEmpty(clinicId, chargeSheetId)) {
            return new ArrayList<>();
        }
        List<ChargeOweSheet> chargeOweSheets = chargeOweSheetRepository.findAllByChargeSheetIdAndClinicIdAndIsDeleted(chargeSheetId, clinicId, 0);

        return Optional.ofNullable(chargeOweSheets).orElse(new ArrayList<>());
    }

    @Override
    public ChargeOweSheet findCurrentChargeOweSheetByChargeSheetId(String clinicId, String chargeSheetId) {

        if (StringUtils.isAnyEmpty(clinicId, chargeSheetId)) {
            return null;
        }

        return chargeOweSheetRepository.findByChargeSheetIdAndClinicIdAndIsOldRecordAndIsDeleted(chargeSheetId, clinicId, 0, 0);
    }


    public void insertChargeOweSheetLog(ChargeOweSheet chargeOweSheet, ChargeTransaction chargeTransaction, String operatorId) {
        if (Objects.isNull(chargeOweSheet) || Objects.isNull(chargeTransaction)) {
            return;
        }

        ChargeOweSheetLog chargeOweSheetLog = new ChargeOweSheetLog();
        chargeOweSheetLog.setId(abcIdGenerator.getUIDLong())
                .setChainId(chargeOweSheet.getChainId())
                .setClinicId(chargeOweSheet.getClinicId())
                .setOweSheetId(chargeOweSheet.getId())
                .setChargeTransactionId(chargeTransaction.getId())
                .setChargeSheetId(chargeOweSheet.getChargeSheetId())
                .setAmount(chargeTransaction.getAmount());


        FillUtils.fillCreatedBy(chargeOweSheetLog, operatorId);

        chargeOweSheetLogRepository.save(chargeOweSheetLog);
    }

    /**
     * 单个欠费单支付
     *
     * @param payChargeOweSheetReq
     * @param clinicId
     * @param employeeId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @RedisLock(key = "'charge.oweSheet.paid:' + #payChargeOweSheetReq.patientId", waitTime = 5)
    public PayChargeOweSheetRsp payForChargeOweSheetById(String chargeOweSheetId, PayChargeOweSheetReq payChargeOweSheetReq, String chainId, String clinicId, String employeeId) {

        payChargeOweSheetReq.getOweSheetItems().removeIf(item -> !StringUtils.equals(chargeOweSheetId, item.getOweSheetId()));

        List<Long> oweSheetIds = Arrays.asList(Long.parseLong(chargeOweSheetId));

        List<ChargeOweSheet> chargeOweSheets = chargeOweSheetRepository.findAllByIdInAndClinicIdAndIsDeleted(oweSheetIds, clinicId, 0);

        if (CollectionUtils.isEmpty(chargeOweSheets)) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeOweSheets is null");
            throw new NotFoundException();
        }

        ChargeOweSheet chargeOweSheet = chargeOweSheets.get(0);

        String chargeSheetId = chargeOweSheet.getChargeSheetId();

        ChargeSheet chargeSheet = chargeSheetService.findByIdCore(chargeSheetId);

        if (Objects.isNull(chargeSheet)) {
            log.info("chargeSheet is null, chargeSheetId: {}", chargeSheetId);
            throw new NotFoundException();
        }

        if (Objects.nonNull(chargeSheet.getHospitalSheetId())) {
            throw new ChargeServiceException(ChargeServiceError.HOSPITAL_OWE_SHEET_CANNOT_PAID_ALONE);
        }

        if (payChargeOweSheetReq.getPayItem().getPayMode() == Constants.ChargePayMode.HEALTH_CARD) {

            //医保卡还款时传递的patientOrderId是收费单的patientOrderId
            payChargeOweSheetReq.getOweSheetItems().get(0).setPatientOrderId(chargeSheet.getPatientOrderId());

            if (!ChargeUtils.checkOweSheetCanPayForShebao(chargeSheet)) {
                throw new ChargeServiceException(ChargeServiceError.CHARGE_OWE_SHEET_CAN_NOT_PAY_FOR_SHEBAO);
            }
        }


        CombinePayOrderPayResult combinePayOrderPayResult = payForChargeOweSheetCore(chargeOweSheets, oweSheetIds, payChargeOweSheetReq,
                chainId, clinicId, ChargeCombineOrder.Source.OWE_SHEET, Long.parseLong(chargeOweSheetId), employeeId);

        PayChargeOweSheetRsp rsp = new PayChargeOweSheetRsp();
        BeanUtils.copyProperties(combinePayOrderPayResult, rsp);
        rsp.setId(chargeOweSheet.getId())
                .setNeedPay(chargeOweSheet.calculateReceivableFee())
                .setReceivedFee(chargeOweSheet.calculateReceivedFee());
        return rsp;
    }

    /**
     * 欠费单组合支付
     *
     * @param payChargeOweSheetReq
     * @param clinicId
     * @param employeeId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @RedisLock(key = "'charge.oweSheet.paid:' + #payChargeOweSheetReq.patientId", waitTime = 5)
    public CombinePayOrderPayResult combinePayForChargeOweSheet(PayChargeOweSheetReq payChargeOweSheetReq, String chainId, String clinicId, String employeeId) {
        List<Long> oweSheetIds = new ArrayList<>();

        for (PayChargeOweSheetReq.CombineOweSheetItem oweSheetItem : payChargeOweSheetReq.getOweSheetItems()) {

            if (!NumberUtils.isDigits(oweSheetItem.getOweSheetId())) {
                throw new ParamNotValidException("oweSheetId错误");
            }

            if (MathUtils.wrapBigDecimalCompare(oweSheetItem.getReceivableFee(), oweSheetItem.getAmount()) < 0) {
                log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "receivableFee小于amount, oweSheetId: {}, receivableFee: {}, amount: {}", oweSheetItem.getOweSheetId(), oweSheetItem.getReceivableFee(), oweSheetItem.getAmount());
                throw new ParamNotValidException("收费金额不能大于应收金额");
            }

            long oweSheetId = Long.parseLong(oweSheetItem.getOweSheetId());

            if (oweSheetIds.contains(oweSheetId)) {
                log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "oweSheetId参数重复, oweSheetId: {}", oweSheetId);
                throw new ParamNotValidException("oweSheetId错误");
            }

            oweSheetIds.add(oweSheetId);
        }

        List<ChargeOweSheet> chargeOweSheets = chargeOweSheetRepository.findAllByIdInAndClinicIdAndIsDeleted(oweSheetIds, clinicId, 0);

        List<String> chargeSheetIds = Optional.ofNullable(chargeOweSheets)
                .orElse(new ArrayList<>())
                .stream()
                .map(ChargeOweSheet::getChargeSheetId)
                .collect(Collectors.toList());

        long hospitalSheetCount = chargeSheetRepository.countByIdInAndHospitalSheetIdNotNullAndIsDeleted(chargeSheetIds, 0);

        if (hospitalSheetCount > 0) {
            throw new ChargeServiceException(ChargeServiceError.HOSPITAL_OWE_SHEET_CANNOT_PAID_ALONE);
        }

        CombinePayOrderPayResult combinePayOrderPayResult = payForChargeOweSheetCore(chargeOweSheets, oweSheetIds, payChargeOweSheetReq,
                chainId, clinicId, ChargeCombineOrder.Source.OWE_SHEET, null, employeeId);

        return combinePayOrderPayResult;

    }


    /**
     * 欠费单支付
     *
     * @param chargeOweSheets      欠费单列表
     * @param oweSheetIds          欠费单ids
     * @param payChargeOweSheetReq
     * @param chainId
     * @param clinicId
     * @param operatorId
     * @return
     */
    public CombinePayOrderPayResult payForChargeOweSheetCore(List<ChargeOweSheet> chargeOweSheets, List<Long> oweSheetIds, PayChargeOweSheetReq payChargeOweSheetReq,
                                                             String chainId, String clinicId, int source, Long businessId, String operatorId) {
        if (Objects.isNull(chargeOweSheets)) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeOweSheet is null, ids: {}, clinicId: {}", JsonUtils.dump(oweSheetIds), clinicId);
            throw new NotFoundException();
        }

        if (chargeOweSheets.size() != oweSheetIds.size()) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeOweSheets.size() != oweSheetIds.size()");
            throw new NotFoundException();
        }

        //校验欠费单是否可以进行收费
        checkChargeOweSheetCanPaid(payChargeOweSheetReq.getPayItem(), chargeOweSheets, payChargeOweSheetReq.getOweSheetItems());

        //构造组合支付的参数
        CombinePayOrderPayInfo payInfo = generateCombineOrderPayInfo(chainId, clinicId, payChargeOweSheetReq, source, businessId, operatorId);
        CombinePayOrderPayResult combinePayOrderPayResult = combineOrderService.pay(payInfo);

        if (Objects.isNull(combinePayOrderPayResult)) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "combinePayOrderPayResult is null");
            throw new ServiceInternalException("支付失败");
        }

        HandleUtils.isTrue(combinePayOrderPayResult.getPayStatus() == CombinePayOrderPayResult.PayStatus.SUCCESS)
                .handle(() -> {
                    //支付成功入账
                    handlePaySuccess(chainId, clinicId, chargeOweSheets, combinePayOrderPayResult.getCombineOrderTransaction(), source, businessId, operatorId);

                    //发送患者欠费金额发生变更消息
                    List<String> patientIds = chargeOweSheets.stream()
                            .map(ChargeOweSheet::getPatientId)
                            .distinct()
                            .collect(Collectors.toList());
                    notifyPatientOweAmountMessage(chainId, clinicId, patientIds, operatorId);
                });

        return combinePayOrderPayResult;

    }

    /**
     * 欠费单退费
     *
     * @param chargeOweSheets
     * @param refundChargeOweSheetReq
     * @param chainId
     * @param clinicId
     * @param operatorId
     * @return
     */
    public ChargeOweSheetRefundResult refundChargeOweSheetCore(List<ChargeOweSheet> chargeOweSheets, ChargeOweCombineTransaction oweCombineTransaction,
                                                               List<ChargeOweCombineTransactionRecord> oweCombineTransactionRecords,
                                                               @Valid RefundChargeOweSheetReq refundChargeOweSheetReq,
                                                               String chainId, String clinicId, String operatorId) {

        log.info("refundChargeOweSheetCore req:{}", JsonUtils.dump(refundChargeOweSheetReq));

        if (CollectionUtils.isEmpty(chargeOweSheets)) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeOweCombineTransaction is null, clinicId: {}", clinicId);
            throw new NotFoundException();
        }

        if (Objects.isNull(oweCombineTransaction)) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "oweCombineTransaction is null");
            throw new ParamRequiredException("oweCombineTransaction");
        }

        if (CollectionUtils.isEmpty(oweCombineTransactionRecords)) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "oweCombineTransactionRecords is empty");
            throw new ParamRequiredException("oweCombineTransactionRecords");
        }

        //校验欠费单是否可以进行退费
        checkChargeOweSheetCanRefund(chargeOweSheets, oweCombineTransaction, oweCombineTransactionRecords, refundChargeOweSheetReq);

        CombinedPayItem payItem = new CombinedPayItem();
        CombinedPayItem payItemReq = refundChargeOweSheetReq.getPayItem();
        BeanUtils.copyProperties(payItemReq, payItem);
        payItem.setTransactionIds(Arrays.asList(String.valueOf(oweCombineTransaction.getCombineOrderTransactionId())));


        Map<Long, ChargeOweCombineTransactionRecord> oweCombineTransactionRecordMap = ListUtils.toMap(oweCombineTransactionRecords, ChargeOweCombineTransactionRecord::getOweSheetId);

        List<ChargeOweCombineTransactionRecord> payTransactionRecords = new ArrayList<>();
        List<CombineRefundOrderItem> combineRefundOrderItems = refundChargeOweSheetReq.getOweSheetItems()
                .stream()
                .map(item -> {

                    ChargeOweCombineTransactionRecord payTransactionRecord = oweCombineTransactionRecordMap.getOrDefault(item.getOweSheetId(), null);

                    if (Objects.isNull(payTransactionRecord)) {
                        return null;
                    }

                    payTransactionRecords.add(payTransactionRecord);
                    CombineRefundOrderItem combineRefundOrderItem = new CombineRefundOrderItem();
                    combineRefundOrderItem.setCombineOrderItemId(payTransactionRecord.getCombineOrderItemId())
                            .setBusinessId(Objects.toString(payTransactionRecord.getOweSheetId(), ""))
                            .setPrice(item.getPrice());
                    return combineRefundOrderItem;
                }).collect(Collectors.toList());

        CombinePayOrderItemRefundInfo combinePayOrderItemRefundInfo = new CombinePayOrderItemRefundInfo();
        combinePayOrderItemRefundInfo.setChainId(chainId)
                .setClinicId(clinicId)
                .setCombineOrderId(oweCombineTransaction.getCombineOrderId())
                .setOrderItems(combineRefundOrderItems)
                .setPayItem(payItem)
                .setPatientId(refundChargeOweSheetReq.getPatientId())
                .setMemberId(refundChargeOweSheetReq.getMemberId())
                .setSource(refundChargeOweSheetReq.getSource())
                .setBusinessId(refundChargeOweSheetReq.getBusinessId())
                .setBusinessPayTransactionId(refundChargeOweSheetReq.getBusinessPayTransactionId())
                .setOperatorId(operatorId);


        CombinePayOrderPayResult combinePayOrderPayResult = combineOrderService.refund(combinePayOrderItemRefundInfo);

        if (Objects.isNull(combinePayOrderPayResult)) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "combinePayOrderPayResult is null");
            throw new ServiceInternalException("退款失败");
        }

        ChargeOweSheetRefundResult result = new ChargeOweSheetRefundResult();
        BeanUtils.copyProperties(combinePayOrderPayResult, result);

        HandleUtils.isTrue(combinePayOrderPayResult.getPayStatus() == CombinePayOrderPayResult.PayStatus.SUCCESS)
                .handle(() -> {
                    ChargeOweCombineTransaction chargeOweCombineTransaction = handleRefundSuccess(chainId, clinicId, chargeOweSheets,
                            oweCombineTransaction, payTransactionRecords,
                            combinePayOrderPayResult.getCombineOrderTransaction(), refundChargeOweSheetReq.getSource(),
                            refundChargeOweSheetReq.getBusinessId(), operatorId);

                    result.setChargeOweCombineTransaction(chargeOweCombineTransaction);
                });

        return result;
    }

    private void checkChargeOweSheetCanRefund(List<ChargeOweSheet> chargeOweSheets, ChargeOweCombineTransaction oweCombineTransaction, List<ChargeOweCombineTransactionRecord> oweCombineTransactionRecords, RefundChargeOweSheetReq refundChargeOweSheetReq) {

        if (CollectionUtils.isEmpty(chargeOweSheets) || Objects.isNull(refundChargeOweSheetReq) || Objects.isNull(oweCombineTransaction)) {
            return;
        }

        CombinedPayItem payItem = refundChargeOweSheetReq.getPayItem();
        List<RefundChargeOweSheetReq.CombineOweSheetItem> oweSheetItems = refundChargeOweSheetReq.getOweSheetItems();

        BigDecimal canRefundFee = MathUtils.wrapBigDecimalAdd(oweCombineTransaction.getAmount(), oweCombineTransaction.getRefundedAmount());

        if (MathUtils.wrapBigDecimalCompare(canRefundFee, payItem.getAmount()) < 0) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "退费金额超过可退金额, canRefundFee: {}, thisTimeRefundFee: {}", canRefundFee, payItem.getAmount());
            throw new ChargeServiceException(ChargeServiceError.CHARGE_REFUND_OVERFLOW);
        }


        Map<Long, RefundChargeOweSheetReq.CombineOweSheetItem> oweSheetItemMap = oweSheetItems.stream().collect(Collectors.toMap(RefundChargeOweSheetReq.CombineOweSheetItem::getOweSheetId, Function.identity()));

        for (ChargeOweSheet chargeOweSheet : chargeOweSheets) {

            if (chargeOweSheet.getStatus() != ChargeOweSheet.Status.CHARGED && chargeOweSheet.getStatus() != ChargeOweSheet.Status.PART_REFUNDED) {
                log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "欠费单状态不是已收或部分退，不能进行退费");
                throw new ChargeServiceException(ChargeServiceError.CHARGE_OWE_SHEET_STATUS_ERROR);
            }

            RefundChargeOweSheetReq.CombineOweSheetItem combineOweSheetItem = oweSheetItemMap.get(chargeOweSheet.getId());

            if (Objects.isNull(combineOweSheetItem)) {
                throw new NotFoundException();
            }

            BigDecimal receivedFee = chargeOweSheet.calculateReceivedFee();

            BigDecimal frontRefundFee = combineOweSheetItem.getPrice();

            if (receivedFee.compareTo(MathUtils.wrapBigDecimalOrZero(frontRefundFee)) < 0) {
                log.info("退费金额大于欠费单可退金额， oweSheetId: {}, receivedFee: {}, frontRefundFee: {}", chargeOweSheet.getId(), receivedFee, frontRefundFee);
                throw new ChargeServiceException(ChargeServiceError.CHARGE_REFUND_OVERFLOW);
            }
        }

        for (ChargeOweCombineTransactionRecord oweCombineTransactionRecord : oweCombineTransactionRecords) {
            RefundChargeOweSheetReq.CombineOweSheetItem combineOweSheetItem = oweSheetItemMap.get(oweCombineTransactionRecord.getOweSheetId());

            if (Objects.isNull(combineOweSheetItem)) {
                throw new NotFoundException();
            }

            BigDecimal transactionRecordCanRefundFee = MathUtils.wrapBigDecimalAdd(oweCombineTransactionRecord.getAmount(), oweCombineTransactionRecord.getRefundedAmount());

            BigDecimal frontRefundFee = combineOweSheetItem.getPrice();

            if (transactionRecordCanRefundFee.compareTo(MathUtils.wrapBigDecimalOrZero(frontRefundFee)) < 0) {
                log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "退费金额大于欠费单可退金额， oweCombineTransactionRecordId: {}, transactionRecordCanRefundFee: {}, frontRefundFee: {}", oweCombineTransactionRecord.getId(), transactionRecordCanRefundFee, frontRefundFee);
                throw new ChargeServiceException(ChargeServiceError.CHARGE_REFUND_OVERFLOW);
            }
        }
    }

    /**
     * 支付成功之后的处理逻辑
     *
     * @param chainId
     * @param clinicId
     * @param chargeOweSheets
     * @param combineOrderTransaction
     * @param operatorId
     */
    public void handlePaySuccess(String chainId, String clinicId, List<ChargeOweSheet> chargeOweSheets,
                                 CombineOrderTransactionView combineOrderTransaction, int source, Long businessId,
                                 String operatorId) {

        if (CollectionUtils.isEmpty(chargeOweSheets) || Objects.isNull(combineOrderTransaction) || CollectionUtils.isEmpty(combineOrderTransaction.getTransactionRecords())) {
            return;
        }

        Map<String, CombineOrderTransactionRecordView> businessIdMap = Optional.ofNullable(combineOrderTransaction.getTransactionRecords())
                .orElse(new ArrayList<>())
                .stream()
                .filter(recordView -> StringUtils.isNotEmpty(recordView.getBusinessId()))
                .collect(Collectors.toMap(CombineOrderTransactionRecordView::getBusinessId, Function.identity(), (a, b) -> a));


        List<String> chargeSheetIds = chargeOweSheets.stream()
                .map(chargeOweSheet -> chargeOweSheet.getChargeSheetId())
                .collect(Collectors.toList());

        //查询收费单的记录明细的标记
        List<ChargeSheet> chargeSheets = Optional.ofNullable(chargeSheetRepository.findChargeSheetsTransactionRecordHandleMode(clinicId, chargeSheetIds)).orElse(new ArrayList<>());
        Map<String, Integer> chargeSheetIdTransactionRecordHandleModeMap = chargeSheets.stream().collect(Collectors.toMap(ChargeSheet::getId, ChargeSheet::getTransactionRecordHandleMode, (a, b) -> a));


        //查询欠费单对应的收费单当时的欠费记录列表
        List<ChargeTransaction> chargeTransactions = Optional.ofNullable(chargeTransactionRepository.findAllByClinicIdAndChargeSheetIdInAndPayModeAndIsPaidbackAndIsDeleted(clinicId, chargeSheetIds, Constants.ChargePayMode.OWE_PAY, 0, 0)).orElse(new ArrayList<>());
        Map<String, BigDecimal> chargeSheetIdOweAmountMap = chargeTransactions.stream()
                .collect(Collectors.toMap(ChargeTransaction::getChargeSheetId, chargeTransaction -> chargeTransaction.getAmount(), MathUtils::wrapBigDecimalAdd));

        //查询欠费单对应的收费单当时欠费的统计明细
        List<String> chargeTransactionIds = chargeTransactions.stream()
                .map(ChargeTransaction::getId)
                .collect(Collectors.toList());

        List<ChargeTransactionRecord> chargeTransactionRecords = Optional.ofNullable(chargeTransactionRecordService.listByClinicIdAndTransactionIdsAndIsOldRecord(clinicId, chargeTransactionIds, 0)).orElse(new ArrayList<>());
        Map<String, List<ChargeTransactionRecord>> chargeSheetIdTransactionRecordMap = chargeTransactionRecords.stream()
                .collect(Collectors.groupingBy(ChargeTransactionRecord::getChargeSheetId));

        String chargeComment = Optional.ofNullable(combineOrderTransaction.getExtra())
                .map(extra -> JsonUtils.readValue(extra, OweSheetCombinePayExtraInfo.class))
                .map(OweSheetCombinePayExtraInfo::getChargeComment)
                .orElse(null);

        ChargeOweCombineTransaction oweCombineTransaction = ChargeOweSheetUtils.generateChargeOweCombineTransaction(chainId, clinicId, combineOrderTransaction, source, businessId, chargeComment, operatorId);

        //查询诊所是医院版本还是其他版本

        boolean isHospital = Optional.ofNullable(scClinicService.getOrganAlwaysReturn(clinicId))
                .map(scOrgan -> scOrgan.getHisType() == Organ.HisType.CIS_HIS_TYPE_HOSPITAL)
                .orElse(false);

        for (ChargeOweSheet chargeOweSheet : chargeOweSheets) {
            BigDecimal totalOweAmount = chargeSheetIdOweAmountMap.getOrDefault(chargeOweSheet.getChargeSheetId(), BigDecimal.ZERO);

            if (MathUtils.wrapBigDecimalCompare(totalOweAmount, chargeOweSheet.getTotalPrice()) != 0) {
                log.error("收费单的欠费金额与欠费单的欠费金额不相等, chargeSheetId: {}, chargeSheet totalOweAmount: {}, chargeOweSheet totalPrice: {}", chargeOweSheet.getChargeSheetId(), totalOweAmount, chargeOweSheet.getTotalPrice());
                throw new IllegalStateException("收费单的欠费金额与欠费单的欠费金额不相等");
            }

            List<ChargeTransactionRecord> records = chargeSheetIdTransactionRecordMap.getOrDefault(chargeOweSheet.getChargeSheetId(), new ArrayList<>());

            if (CollectionUtils.isEmpty(records)) {
                log.error("ChargeTransactionRecords is Empty, chargeSheetId: {}", chargeOweSheet.getChargeSheetId());
                throw new IllegalStateException("ChargeTransactionRecords is Empty");
            }
            int transactionRecordHandleMode = chargeSheetIdTransactionRecordHandleModeMap.get(chargeOweSheet.getChargeSheetId());

            CombineOrderTransactionRecordView recordView = businessIdMap.getOrDefault(Objects.toString(chargeOweSheet.getId(), ""), null);
            writeChargeOweSheetCombineTransactionRecordForPay(chargeOweSheet, recordView, oweCombineTransaction, records, transactionRecordHandleMode, isHospital, operatorId);
        }

        //修改收费单为已还
        updateChargeSheetOweStatusToNoOwed(chargeOweSheets);

        ChargeOweSheet firstChargeOweSheet = chargeOweSheets.get(0);
        if (firstChargeOweSheet.getStatus() == ChargeOweSheet.Status.PART_CHARGED) {
            hamqProducer.notifyChargeOweSheetMessage(chargeOweSheets, ChargeOweSheetMessage.Type.MSG_TYPE_PARTY_CHARGED, operatorId);
        } else if (firstChargeOweSheet.getStatus() == ChargeOweSheet.Status.CHARGED) {
            hamqProducer.notifyChargeOweSheetMessage(chargeOweSheets, ChargeOweSheetMessage.Type.MSG_TYPE_CHARGED, operatorId);
        }

        List<ChargeOweCombineTransactionRecord> transactionRecords = oweCombineTransaction.getTransactionRecords();

        chargeOweCombineTransactionRepository.save(oweCombineTransaction);
        if (CollectionUtils.isNotEmpty(transactionRecords)) {
            chargeOweCombineTransactionRecordRepository.saveAll(transactionRecords);
        }
    }

    /**
     * 退款成功之后的处理逻辑
     *
     * @param chargeOweSheets
     * @param combineOrderTransaction
     * @param operatorId
     */
    public ChargeOweCombineTransaction handleRefundSuccess(String chainId, String clinicId, List<ChargeOweSheet> chargeOweSheets, ChargeOweCombineTransaction payTransaction, List<ChargeOweCombineTransactionRecord> payTransactionRecords, CombineOrderTransactionView combineOrderTransaction, int source, Long businessId, String operatorId) {

        if (CollectionUtils.isEmpty(chargeOweSheets) || Objects.isNull(combineOrderTransaction) || CollectionUtils.isEmpty(combineOrderTransaction.getTransactionRecords())) {
            return null;
        }

        Map<Long, ChargeOweCombineTransactionRecord> payTransactionRecordMap = ListUtils.toMap(Optional.ofNullable(payTransactionRecords).orElse(new ArrayList<>()), ChargeOweCombineTransactionRecord::getOweSheetId);

        Map<String, CombineOrderTransactionRecordView> businessIdMap = Optional.ofNullable(combineOrderTransaction.getTransactionRecords())
                .orElse(new ArrayList<>())
                .stream()
                .filter(recordView -> StringUtils.isNotEmpty(recordView.getBusinessId()))
                .collect(Collectors.toMap(CombineOrderTransactionRecordView::getBusinessId, Function.identity(), (a, b) -> a));

        String chargeComment = Optional.ofNullable(combineOrderTransaction.getExtra())
                .map(extra -> JsonUtils.readValue(extra, OweSheetCombinePayExtraInfo.class))
                .map(OweSheetCombinePayExtraInfo::getChargeComment)
                .orElse(null);

        ChargeOweCombineTransaction refundTransaction = ChargeOweSheetUtils.generateChargeOweCombineTransaction(chainId, clinicId, combineOrderTransaction, source, businessId, chargeComment, operatorId);

        //更新支付流水的退费金额
        payTransaction.setRefundedAmount(MathUtils.wrapBigDecimalAdd(payTransaction.getRefundedAmount(), refundTransaction.getAmount()));

        for (ChargeOweSheet chargeOweSheet : chargeOweSheets) {
            CombineOrderTransactionRecordView recordView = businessIdMap.getOrDefault(Objects.toString(chargeOweSheet.getId(), ""), null);
            ChargeOweCombineTransactionRecord payTransactionRecord = payTransactionRecordMap.getOrDefault(chargeOweSheet.getId(), null);
            writeChargeOweSheetCombineTransactionRecordForRefund(chargeOweSheet, recordView, refundTransaction, payTransactionRecord, operatorId);
        }

        List<ChargeOweSheet> partRefundChargeOweSheets = chargeOweSheets.stream()
                .filter(chargeOweSheet -> chargeOweSheet.getStatus() == ChargeOweSheet.Status.PART_REFUNDED)
                .collect(Collectors.toList());

        List<ChargeOweSheet> refundedChargeOweSheets = chargeOweSheets.stream()
                .filter(chargeOweSheet -> chargeOweSheet.getStatus() == ChargeOweSheet.Status.REFUNDED)
                .collect(Collectors.toList());

        hamqProducer.notifyChargeOweSheetMessage(partRefundChargeOweSheets, ChargeOweSheetMessage.Type.MSG_TYPE_PARTY_REFUND, operatorId);
        hamqProducer.notifyChargeOweSheetMessage(refundedChargeOweSheets, ChargeOweSheetMessage.Type.MSG_TYPE_REFUNDED, operatorId);

        List<ChargeOweCombineTransactionRecord> transactionRecords = refundTransaction.getTransactionRecords();


        chargeOweCombineTransactionRepository.save(refundTransaction);
        if (CollectionUtils.isNotEmpty(transactionRecords)) {
            chargeOweCombineTransactionRecordRepository.saveAll(transactionRecords);
        }

        return refundTransaction;
    }


    /**
     * 将收费单改为未欠费
     *
     * @param chargeOweSheets
     */
    private void updateChargeSheetOweStatusToNoOwed(List<ChargeOweSheet> chargeOweSheets) {

        List<String> chargeSheetIds = Optional.ofNullable(chargeOweSheets).orElse(new ArrayList<>())
                .stream()
                .filter(chargeOweSheet -> chargeOweSheet.getStatus() == ChargeOweSheet.Status.CHARGED)
                .map(ChargeOweSheet::getChargeSheetId)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(chargeSheetIds)) {
            return;
        }

        chargeSheetRepository.updateChargeSheetOwedStatusToNoOwedByIds(chargeSheetIds);
    }

    public ChargeOweCombineTransaction writeChargeOweSheetCombineTransactionRecordForPay(ChargeOweSheet chargeOweSheet,
                                                                                         CombineOrderTransactionRecordView recordView,
                                                                                         ChargeOweCombineTransaction oweCombineTransaction,
                                                                                         List<ChargeTransactionRecord> chargeTransactionRecords,
                                                                                         int transactionRecordHandleMode,
                                                                                         boolean isHospital,
                                                                                         String operatorId) {
        if (Objects.isNull(chargeOweSheet) || Objects.isNull(oweCombineTransaction) || Objects.isNull(recordView)) {
            return null;
        }

        BigDecimal receivableFee = chargeOweSheet.calculateReceivableFee();

        if (MathUtils.wrapBigDecimalCompare(receivableFee, recordView.getAmount()) < 0) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "欠费单的应收金额小于支付金额， receivableFee: {}, amount: {}", receivableFee, recordView.getAmount());
            throw new ChargeServiceException(ChargeServiceError.CHARGE_OWE_SHEET_RECEIVABLE_FEE_CHANGED);
        }

        boolean isFirstPay = CollectionUtils.isEmpty(chargeOweSheet.getTransactionRecords());

        chargeOweSheet.setReceivedPrice(MathUtils.wrapBigDecimalAdd(chargeOweSheet.getReceivedPrice(), recordView.getAmount()));
        receivableFee = chargeOweSheet.calculateReceivableFee();

        //写入combineTransactionRecord
        ChargeOweCombineTransactionRecord chargeOweCombineTransactionRecord = ChargeOweSheetUtils.insertChargeOweCombineTransactionRecord(chargeOweSheet, oweCombineTransaction, null, recordView, receivableFee, operatorId);


        int payType;
        if (MathUtils.wrapBigDecimalCompare(receivableFee, BigDecimal.ZERO) == 0) {
            chargeOweSheet.setStatus(ChargeOweSheet.Status.CHARGED);
            payType = StatChargeOweRecordProcessor.PayType.PAID;
        } else {
            chargeOweSheet.setStatus(ChargeOweSheet.Status.PART_CHARGED);
            payType = StatChargeOweRecordProcessor.PayType.PARTED_PAID;
        }
        chargeOweSheet.setLatestPayTime(Instant.now());
        FillUtils.fillLastModifiedBy(chargeOweSheet, operatorId);

        writeChargeOweCombineTransactionRecordDetail(chargeOweSheet, isFirstPay, payType, isHospital, transactionRecordHandleMode, chargeOweCombineTransactionRecord, chargeTransactionRecords, operatorId);

        return oweCombineTransaction;

    }

    private void writeChargeOweCombineTransactionRecordDetail(ChargeOweSheet chargeOweSheet, boolean isFirstPay, int payType, boolean isHospital, int transactionRecordHandleMode, ChargeOweCombineTransactionRecord chargeOweCombineTransactionRecord, List<ChargeTransactionRecord> chargeTransactionRecords, String operatorId) {

        StatChargeOweRecordProcessor chargeOweRecordProcessor = new StatChargeOweRecordProcessor();
        chargeOweRecordProcessor.setOweCombineTransactionRecordDetailService(oweCombineTransactionRecordDetailService);
        chargeOweRecordProcessor.setChargeFormItemRepository(chargeFormItemRepository);
        chargeOweRecordProcessor.setChargeInfo(chargeOweSheet,
                isFirstPay,
                payType,
                transactionRecordHandleMode,
                isHospital,
                chargeOweCombineTransactionRecord,
                chargeTransactionRecords,
                operatorId);

        List<ChargeOweCombineTransactionRecordDetail> oweCombineTransactionRecordDetails = chargeOweRecordProcessor.generateOweCombineTransactionRecordDetails();

        if (CollectionUtils.isNotEmpty(oweCombineTransactionRecordDetails)) {
            oweCombineTransactionRecordDetailRepository.saveAll(oweCombineTransactionRecordDetails);
        }
    }

    public ChargeOweCombineTransactionRecord writeChargeOweSheetCombineTransactionRecordForRefund(ChargeOweSheet chargeOweSheet, CombineOrderTransactionRecordView recordView, ChargeOweCombineTransaction refundTransaction, ChargeOweCombineTransactionRecord payTransactionRecord, String operatorId) {
        if (Objects.isNull(chargeOweSheet) || Objects.isNull(refundTransaction) || Objects.isNull(recordView)) {
            return null;
        }

        BigDecimal amount = recordView.getAmount();
        BigDecimal canRefundFee = chargeOweSheet.calculateReceivedFee();

        if (canRefundFee.add(amount).compareTo(BigDecimal.ZERO) < 0) {
            log.info("退费金额大于欠费单实收金额， oweSheetId: {}, canRefundFee: {}, amount: {}", chargeOweSheet.getId(), canRefundFee, amount);
            throw new ChargeServiceException(ChargeServiceError.CHARGE_OWE_SHEET_RECEIVABLE_FEE_CHANGED);
        }

        //写入combineTransactionRecord
        ChargeOweCombineTransactionRecord refundTransactionRecord = ChargeOweSheetUtils.insertChargeOweCombineTransactionRecord(chargeOweSheet, refundTransaction, payTransactionRecord.getId(), recordView, BigDecimal.ZERO, operatorId);

        chargeOweSheet.setRefundedPrice(MathUtils.wrapBigDecimalAdd(chargeOweSheet.getRefundedPrice(), recordView.getAmount()));
        //更新支付流水的退费金额
        payTransactionRecord.setRefundedAmount(MathUtils.wrapBigDecimalAdd(payTransactionRecord.getRefundedAmount(), refundTransactionRecord.getAmount()));

        canRefundFee = chargeOweSheet.calculateReceivedFee();

        if (MathUtils.wrapBigDecimalCompare(canRefundFee, BigDecimal.ZERO) <= 0) {
            chargeOweSheet.setStatus(ChargeOweSheet.Status.REFUNDED);
        } else {
            chargeOweSheet.setStatus(ChargeOweSheet.Status.PART_REFUNDED);
        }
        FillUtils.fillLastModifiedBy(chargeOweSheet, operatorId);

        return refundTransactionRecord;

    }

    private CombinePayOrderPayInfo generateCombineOrderPayInfo(String chainId, String clinicId, PayChargeOweSheetReq payChargeOweSheetReq, int source, Long businessId, String operatorId) {

        OweSheetCombinePayExtraInfo extraInfo = HandleUtils.isTrueOrFalseReturn(StringUtils.isNotEmpty(payChargeOweSheetReq.getChargeComment()))
                .handleAndReturn(() -> new OweSheetCombinePayExtraInfo()
                                .setChargeComment(payChargeOweSheetReq.getChargeComment()),
                        () -> null);


        CombinedPayItem frontPayItem = payChargeOweSheetReq.getPayItem();

        CombinedPayItem payItem = new CombinedPayItem();
        BeanUtils.copyProperties(frontPayItem, payItem);
        payItem.setAmount(frontPayItem.getNetIncome());
        payItem.setChange(BigDecimal.ZERO);

        CombinePayOrderPayInfo combinePayOrderPayInfo = new CombinePayOrderPayInfo();
        combinePayOrderPayInfo.setChainId(chainId)
                .setClinicId(clinicId)
                .setTotalPrice(payChargeOweSheetReq.getOweSheetItems().stream().map(PayChargeOweSheetReq.CombineOweSheetItem::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add))
                .setPayItem(payItem)
                .setSource(source)
                .setBusinessId(businessId)
                .setMemberId(payChargeOweSheetReq.getMemberId())
                .setOrderItems(payChargeOweSheetReq.getOweSheetItems().stream()
                        .map(itemReq -> new CombinePayOrderItem()
                                .setBusinessId(itemReq.getOweSheetId())
                                .setPrice(itemReq.getAmount())
                                .setPatientOrderId(itemReq.getPatientOrderId())
                        )
                        .collect(Collectors.toList())
                )
                .setPatientId(payChargeOweSheetReq.getPatientId())
                .setExtra(Optional.ofNullable(extraInfo).map(JsonUtils::dumpAsJsonNode).orElse(null))
                .setOperatorId(operatorId);

        return combinePayOrderPayInfo;

    }

    private void checkChargeOweSheetCanPaid(CombinedPayItem payItem, List<ChargeOweSheet> chargeOweSheets, List<PayChargeOweSheetReq.CombineOweSheetItem> oweSheetItems) {

        if (CollectionUtils.isEmpty(chargeOweSheets) || CollectionUtils.isEmpty(oweSheetItems)) {
            return;
        }

        Map<Long, PayChargeOweSheetReq.CombineOweSheetItem> oweSheetItemMap = oweSheetItems.stream().collect(Collectors.toMap(oweSheetItem -> Long.parseLong(oweSheetItem.getOweSheetId()), Function.identity(), (a, b) -> a));

        BigDecimal totalReceivableFee = chargeOweSheets.stream()
                .map(ChargeOweSheet::calculateReceivableFee)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        payItem.dealChangeFee(totalReceivableFee, new ChargeServiceException(ChargeServiceError.CHARGE_FEE_MORE_THAN_RECEIVABLE));

        for (ChargeOweSheet chargeOweSheet : chargeOweSheets) {

            if (chargeOweSheet.getStatus() != ChargeOweSheet.Status.UNCHARGED && chargeOweSheet.getStatus() != ChargeOweSheet.Status.PART_CHARGED) {
                log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "欠费单状态不是待收或部分收，不能进行收费");
                throw new ChargeServiceException(ChargeServiceError.CHARGE_OWE_SHEET_STATUS_ERROR);
            }

            PayChargeOweSheetReq.CombineOweSheetItem combineOweSheetItem = oweSheetItemMap.get(chargeOweSheet.getId());

            if (Objects.isNull(combineOweSheetItem)) {
                continue;
            }

            //有找零的情况，item的amount重新赋值
            if (payItem.getPayMode() == Constants.ChargePayMode.CASH && MathUtils.wrapBigDecimalCompare(payItem.getChange(), BigDecimal.ZERO) > 0 && combineOweSheetItem.getReceivableFee().compareTo(combineOweSheetItem.getAmount()) < 0) {
                combineOweSheetItem.setAmount(combineOweSheetItem.getReceivableFee());
            }

            BigDecimal receivableFee = chargeOweSheet.calculateReceivableFee();

            totalReceivableFee = MathUtils.wrapBigDecimalAdd(totalReceivableFee, receivableFee);

            BigDecimal frontReceivableFee = combineOweSheetItem.getReceivableFee();

            if (receivableFee.compareTo(frontReceivableFee) != 0) {
                log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "应收金额发生变化, oweSheetId: {}, receivableFee: {}, frontReceivableFee: {}", chargeOweSheet.getId(), receivableFee, frontReceivableFee);
                throw new ChargeServiceException(ChargeServiceError.CHARGE_OWE_SHEET_RECEIVABLE_FEE_CHANGED);
            }

            if (receivableFee.compareTo(MathUtils.wrapBigDecimalOrZero(combineOweSheetItem.getAmount())) < 0) {
                log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "收费金额大于欠费单应收金额， oweSheetId: {}, receivableFee: {}, amount: {}", chargeOweSheet.getId(), receivableFee, combineOweSheetItem.getAmount());
                throw new ChargeServiceException(ChargeServiceError.CHARGE_OWE_SHEET_RECEIVABLE_FEE_CHANGED);
            }

        }
    }

    public void oweSheetPayCallbackCore(CombineOrderBusinessPayCallbackReq req) {
        //查询需要入账的欠费单
        List<Long> oweSheetIds = Optional.ofNullable(req.getCombineOrderTransaction())
                .map(CombineOrderTransactionView::getTransactionRecords)
                .orElse(new ArrayList<>())
                .stream()
                .filter(payItem -> StringUtils.isNotEmpty(payItem.getBusinessId()))
                .map(payItem -> Long.parseLong(payItem.getBusinessId()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(oweSheetIds)) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "oweSheetIds is null");
            throw new NotFoundException();
        }

        List<ChargeOweSheet> chargeOweSheets = chargeOweSheetRepository.findAllByIdInAndClinicIdAndIsDeleted(oweSheetIds, req.getClinicId(), 0);

        //校验是否可以入账
        checkPayCallbackCanExecute(chargeOweSheets, oweSheetIds, req);

        //入账
        handlePaySuccess(req.getChainId(), req.getClinicId(), chargeOweSheets, req.getCombineOrderTransaction(), req.getSource(), req.getBusinessId(), req.getOperatorId());
        //发送患者欠费金额发生变更消息
        List<String> patientIds = chargeOweSheets.stream()
                .map(ChargeOweSheet::getPatientId)
                .distinct()
                .collect(Collectors.toList());
        notifyPatientOweAmountMessage(req.getChainId(), req.getClinicId(), patientIds, req.getOperatorId());
    }

    public CombineOrderBusinessPayCallbackRsp oweSheetRefundCallBackCore(CombineOrderBusinessPayCallbackReq req) {

        //查询需要入账的欠费单
        List<Long> oweSheetIds = Optional.ofNullable(req.getCombineOrderTransaction())
                .map(CombineOrderTransactionView::getTransactionRecords)
                .orElse(new ArrayList<>())
                .stream()
                .filter(payItem -> StringUtils.isNotEmpty(payItem.getBusinessId()))
                .map(payItem -> Long.parseLong(payItem.getBusinessId()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(oweSheetIds)) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "oweSheetIds is null");
            return new CombineOrderBusinessPayCallbackRsp().setCode(CombineOrderBusinessPayCallbackRsp.Code.FAIL);
        }

        List<ChargeOweSheet> chargeOweSheets = chargeOweSheetRepository.findAllByIdInAndClinicIdAndIsDeleted(oweSheetIds, req.getClinicId(), 0);


        //校验是否可以入账
        checkRefundCallbackCanExecute(chargeOweSheets, oweSheetIds, req);

        //找到退费对应的的支付ChargeOweCombineTransaction
        ChargeOweCombineTransaction payTransaction = chargeOweSheets.stream()
                .filter(chargeOweSheet -> CollectionUtils.isNotEmpty(chargeOweSheet.getTransactionRecords()))
                .flatMap(chargeOweSheet -> chargeOweSheet.getTransactionRecords().stream())
                .filter(chargeOweCombineTransactionRecord -> Objects.nonNull(chargeOweCombineTransactionRecord.getOweCombineTransaction()))
                .map(ChargeOweCombineTransactionRecord::getOweCombineTransaction)
                .filter(oweCombineTransaction -> Objects.equals(oweCombineTransaction.getCombineOrderId(), req.getOrderId()))
                .findFirst().orElse(null);

        //找到退费对应的的支付ChargeOweCombineTransactionRecord
        Set<String> refundOweSheetIds = req.getCombineOrderTransaction().getTransactionRecords()
                .stream()
                .filter(transactionRecordView -> StringUtils.isNotEmpty(transactionRecordView.getBusinessId()))
                .map(CombineOrderTransactionRecordView::getBusinessId)
                .collect(Collectors.toSet());

        List<ChargeOweCombineTransactionRecord> payTransactionRecord = chargeOweSheets.stream()
                .filter(chargeOweSheet -> CollectionUtils.isNotEmpty(chargeOweSheet.getTransactionRecords()))
                .flatMap(chargeOweSheet -> chargeOweSheet.getTransactionRecords().stream())
                .filter(transactionRecord -> Objects.equals(transactionRecord.getOweCombineTransactionId(), payTransaction.getId()))
                .filter(transactionRecord -> refundOweSheetIds.contains(Objects.toString(transactionRecord.getOweSheetId(), "")))
                .collect(Collectors.toList());

        if (Objects.isNull(payTransaction)) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "payTransaction is null");
            throw new NotFoundException();
        }

        //入账
        handleRefundSuccess(req.getChainId(), req.getClinicId(), chargeOweSheets, payTransaction, payTransactionRecord, req.getCombineOrderTransaction(), req.getSource(), req.getBusinessId(), req.getOperatorId());

        return new CombineOrderBusinessPayCallbackRsp();
    }


    private void checkPayCallbackCanExecute(List<ChargeOweSheet> chargeOweSheets, List<Long> oweSheetIds, CombineOrderBusinessPayCallbackReq req) {

        if (Objects.isNull(chargeOweSheets)) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeOweSheet is null, ids: {}", JsonUtils.dump(oweSheetIds));
            throw new NotFoundException();
        }

        if (chargeOweSheets.size() != oweSheetIds.size()) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeOweSheets.size() != oweSheetIds.size()");
            throw new NotFoundException();
        }

        Map<String, BigDecimal> oweSheetIdMap = Optional.ofNullable(req.getCombineOrderTransaction())
                .map(CombineOrderTransactionView::getTransactionRecords)
                .orElse(new ArrayList<>())
                .stream()
                .filter(item -> StringUtils.isNotEmpty(item.getBusinessId()))
                .collect(Collectors.toMap(CombineOrderTransactionRecordView::getBusinessId, CombineOrderTransactionRecordView::getAmount));

        for (ChargeOweSheet chargeOweSheet : chargeOweSheets) {

            if (chargeOweSheet.getStatus() != ChargeOweSheet.Status.UNCHARGED && chargeOweSheet.getStatus() != ChargeOweSheet.Status.PART_CHARGED) {
                log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "欠费单状态不是待收或部分收，不能进行收费");
                throw new ChargeServiceException(ChargeServiceError.CHARGE_OWE_SHEET_STATUS_ERROR);
            }

            BigDecimal amount = oweSheetIdMap.getOrDefault(chargeOweSheet.getId(), BigDecimal.ZERO);

            BigDecimal receivableFee = chargeOweSheet.calculateReceivableFee();

            if (receivableFee.compareTo(amount) < 0) {
                log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "收费金额大于欠费单应收金额， oweSheetId: {}, receivableFee: {}, amount: {}", chargeOweSheet.getId(), receivableFee, amount);
                throw new ChargeServiceException(ChargeServiceError.CHARGE_OWE_SHEET_RECEIVABLE_FEE_CHANGED);
            }

        }

    }

    private void checkRefundCallbackCanExecute(List<ChargeOweSheet> chargeOweSheets, List<Long> oweSheetIds, CombineOrderBusinessPayCallbackReq req) {

        if (Objects.isNull(chargeOweSheets)) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeOweSheet is null, ids: {}", JsonUtils.dump(oweSheetIds));
            throw new NotFoundException();
        }

        if (chargeOweSheets.size() != oweSheetIds.size()) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeOweSheets.size() != oweSheetIds.size()");
            throw new NotFoundException();
        }

        Map<String, BigDecimal> oweSheetIdMap = Optional.ofNullable(req.getCombineOrderTransaction())
                .map(CombineOrderTransactionView::getTransactionRecords)
                .orElse(new ArrayList<>())
                .stream()
                .filter(item -> StringUtils.isNotEmpty(item.getBusinessId()))
                .collect(Collectors.toMap(CombineOrderTransactionRecordView::getBusinessId, CombineOrderTransactionRecordView::getAmount));

        for (ChargeOweSheet chargeOweSheet : chargeOweSheets) {

            if (chargeOweSheet.getStatus() != ChargeOweSheet.Status.CHARGED && chargeOweSheet.getStatus() != ChargeOweSheet.Status.PART_REFUNDED) {
                log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "欠费单状态不是已收或部分退，不能进行退费入账");
                throw new ChargeServiceException(ChargeServiceError.CHARGE_OWE_SHEET_STATUS_ERROR);
            }

            BigDecimal amount = oweSheetIdMap.getOrDefault(chargeOweSheet.getId(), BigDecimal.ZERO);

            BigDecimal canRefundFee = chargeOweSheet.calculateReceivedFee();

            if (canRefundFee.add(amount).compareTo(BigDecimal.ZERO) < 0) {
                log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "退费金额大于欠费单实收金额， oweSheetId: {}, canRefundFee: {}, amount: {}", chargeOweSheet.getId(), canRefundFee, amount);
                throw new ChargeServiceException(ChargeServiceError.CHARGE_OWE_SHEET_RECEIVABLE_FEE_CHANGED);
            }

        }

    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public ChargeOweSheetListRsp listWaitPayChargeOweSheetByPatientId(String patientId, String clinicId) {
        ChargeOweSheetListRsp rsp = new ChargeOweSheetListRsp();
        rsp.setChargeOweSheets(new ArrayList<>());

        if (StringUtils.isAnyEmpty(patientId, clinicId)) {
            return rsp;
        }

        if (Objects.equals(Constants.ANONYMOUS_PATIENT_ID, patientId)) {
            return rsp;
        }

        List<Integer> statuses = Arrays.asList(ChargeOweSheet.Status.UNCHARGED, ChargeOweSheet.Status.PART_CHARGED);

        List<ChargeOweSheet> chargeOweSheets = chargeOweSheetRepository.findAllByClinicIdAndPatientIdAndStatusInAndIsOldRecordAndIsDeletedOrderByCreatedDesc(clinicId, patientId, statuses, 0, 0);

        chargeOweSheets = filterChargeOweSheets(chargeOweSheets);

        if (CollectionUtils.isEmpty(chargeOweSheets)) {
            return rsp;
        }

        List<ChargeOweSheetView> chargeOweSheetViews = chargeOweSheets
                .stream()
                .map(ChargeOweSheetView::ofChargeOweSheetView)
                .collect(Collectors.toList());

        bindChargeOweSheetViewMemberId(chargeOweSheetViews);
        return rsp.setChargeOweSheets(chargeOweSheetViews);
    }

    private List<ChargeOweSheet> filterChargeOweSheets(List<ChargeOweSheet> chargeOweSheets) {

        if (CollectionUtils.isEmpty(chargeOweSheets)) {
            return new ArrayList<>();
        }

        List<String> chargeSheetIds = chargeOweSheets.stream()
                .map(ChargeOweSheet::getChargeSheetId)
                .distinct()
                .collect(Collectors.toList());

        List<ChargeSheetIdAndHospitalSheetIdDto> chargeSheetIdAndHospitalSheetIdDtos = chargeSheetRepository.findChargeSheetIdAndHospitalSheetIdDtosByIds(chargeSheetIds);

        List<String> availableChargeSheetIds = Optional.ofNullable(chargeSheetIdAndHospitalSheetIdDtos)
                .orElse(new ArrayList<>())
                .stream()
                .filter(d -> Objects.isNull(d.getHospitalSheetId()))
                .map(ChargeSheetIdAndHospitalSheetIdDto::getId)
                .collect(Collectors.toList());

        return chargeOweSheets.stream()
                .filter(chargeOweSheet -> availableChargeSheetIds.contains(chargeOweSheet.getChargeSheetId()))
                .collect(Collectors.toList());

    }

    /**
     * 绑定每个欠费单可使用的会员memberId
     *
     * @param chargeOweSheetViews
     */
    private void bindChargeOweSheetViewMemberId(List<ChargeOweSheetView> chargeOweSheetViews) {

        List<String> chargeSheetIds = Optional.ofNullable(chargeOweSheetViews).orElse(new ArrayList<>())
                .stream()
                .filter(chargeOweSheetView -> StringUtils.isNotEmpty(chargeOweSheetView.getChargeSheetId()))
                .map(ChargeOweSheetView::getChargeSheetId)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(chargeSheetIds)) {
            return;
        }

        List<ChargeSheetIdAndMemberIdDto> chargeSheetIdAndMemberIdDtos = chargeSheetRepository.findChargeSheetIdAndMemberIdByIds(chargeSheetIds);

        if (CollectionUtils.isEmpty(chargeSheetIdAndMemberIdDtos)) {
            return;
        }

        String chainId = chargeOweSheetViews.get(0).getChainId();
        String patientId = chargeOweSheetViews.get(0).getPatientId();

        List<String> needFetchDefaultMemberChargeSheetIds = chargeSheetIdAndMemberIdDtos.stream()
                .filter(chargeSheetIdAndMemberIdDto -> StringUtils.isEmpty(chargeSheetIdAndMemberIdDto.getMemberId())
                        && chargeSheetIdAndMemberIdDto.getUseMemberFlag() == ChargeSheetAdditional.UseMemberFlag.DEFAULT_MEMBER)
                .map(ChargeSheetIdAndMemberIdDto::getId)
                .collect(Collectors.toList());

        PatientMemberInfo defaultPatientMemberInfo = CollectionUtils.isNotEmpty(needFetchDefaultMemberChargeSheetIds) ? queryPatientDefaultMemberInfo(patientId, chainId) : null;

        Map<String, ChargeSheetIdAndMemberIdDto> chargeSheetIdChargeSheetIdAndMemberIdDtoMap = chargeSheetIdAndMemberIdDtos.stream()
                .peek(chargeSheetIdAndMemberIdDto -> {
                    if (StringUtils.isEmpty(chargeSheetIdAndMemberIdDto.getMemberId())
                            && chargeSheetIdAndMemberIdDto.getUseMemberFlag() == ChargeSheetAdditional.UseMemberFlag.DEFAULT_MEMBER) {
                        Optional.ofNullable(defaultPatientMemberInfo).ifPresent(
                                patientMemberInfo -> {
                                    chargeSheetIdAndMemberIdDto.setMemberId(patientMemberInfo.getPatientId());
                                    chargeSheetIdAndMemberIdDto.setMemberInfoJson(JsonUtils.dump(ChargeUtils.convertPatientMemberInfoToMemberInfo(patientMemberInfo)));
                                }
                        );
                    }
                })
                .collect(Collectors.toMap(ChargeSheetIdAndMemberIdDto::getId, Function.identity(), (a, b) -> a));

        chargeOweSheetViews.forEach(chargeOweSheetView -> {
            ChargeSheetIdAndMemberIdDto chargeSheetIdAndMemberIdDto = chargeSheetIdChargeSheetIdAndMemberIdDtoMap.get(chargeOweSheetView.getChargeSheetId());
            Optional.ofNullable(chargeSheetIdAndMemberIdDto)
                    .ifPresent(c -> chargeOweSheetView.setMemberId(c.getMemberId())
                            .setMemberInfo(c.getMemberInfo()));
        });
    }

    private PatientMemberInfo queryPatientDefaultMemberInfo(String patientId, String chainId) {

        PatientRelateMemberRsp patientRelateMemberRsp = cisCrmService.getPatientRelateMember(patientId, chainId);

        List<PatientMemberInfo> relatePatientMembers = Optional.ofNullable(patientRelateMemberRsp)
                .map(PatientRelateMemberRsp::getRelatePatientMembers)
                .orElse(new ArrayList<>());

        return relatePatientMembers.stream()
                .filter(patientMemberInfo -> org.apache.commons.lang3.StringUtils.equals(patientId, patientMemberInfo.getPatientId()))
                .findFirst()
                .orElse(relatePatientMembers.size() > 0 ? relatePatientMembers.get(0) : null);

    }


    public PatientOweAmountInfo getPatientOweAmountInfoByPatientId(String patientId, String chainId, String clinicId) {
        PatientOweAmountInfo patientOweAmountInfo = new PatientOweAmountInfo()
                .setPatientId(patientId)
                .setTotalOweAmount(BigDecimal.ZERO);

        if (StringUtils.isAnyEmpty(chainId, patientId)) {
            return patientOweAmountInfo;
        }

        return Optional.ofNullable(getPatientOweAmountInfoListByPatientIds(chainId, clinicId, Arrays.asList(patientId)))
                .map(PatientOweAmountInfoListRsp::getPatientOweAmountInfos)
                .orElse(new ArrayList<>())
                .stream()
                .filter(p -> StringUtils.equals(patientId, p.getPatientId()))
                .findFirst()
                .orElse(patientOweAmountInfo);
    }

    /**
     * 根据患者id查询待还的收费单列表
     *
     * @param patientIdReqs
     * @param chainId
     * @return
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public PatientOweAmountInfoListRsp getPatientOweAmountInfoListByPatientIds(String chainId, String clinicId, List<String> patientIdReqs) {
        PatientOweAmountInfoListRsp rsp = new PatientOweAmountInfoListRsp();
        rsp.setPatientOweAmountInfos(new ArrayList<>());
        if (org.springframework.util.CollectionUtils.isEmpty(patientIdReqs) || StringUtils.isEmpty(chainId)) {
            return rsp;
        }

        //查询患者的欠费总金额
        List<ClinicPatientOweAmountInfoDto> clinicPatientOweAmountInfoDtos = Optional.ofNullable(chargeOweSheetMapper.getPatientOweAmountInfoByPatientIds(chainId, clinicId, patientIdReqs)).orElse(new ArrayList<>());

        //将门店维度的欠费金额转化为连锁维度的欠费金额
        List<PatientOweAmountInfo> patientOweAmountInfos = clinicPatientOweAmountInfoDtos.stream()
                .collect(Collectors.groupingBy(ClinicPatientOweAmountInfoDto::getPatientId))
                .entrySet()
                .stream()
                .map(entry -> {
                    String patientId = entry.getKey();
                    List<ClinicPatientOweAmountInfoDto> clinicPatientOweAmountInfoDtoList = entry.getValue();
                    BigDecimal totalOweAmount = clinicPatientOweAmountInfoDtoList.stream()
                            .map(ClinicPatientOweAmountInfoDto::getTotalOweAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    return new PatientOweAmountInfo()
                            .setPatientId(patientId)
                            .setTotalOweAmount(totalOweAmount)
                            .setClinicPatientOweAmountInfos(new ArrayList<>(clinicPatientOweAmountInfoDtoList.stream()
                                    .collect(Collectors.toMap(ClinicPatientOweAmountInfoDto::getClinicId, clinicPatientOweAmountInfoDto -> new PatientOweAmountInfo.ClinicPatientOweAmountInfo()
                                            .setClinicId(clinicPatientOweAmountInfoDto.getClinicId())
                                            .setTotalOweAmount(clinicPatientOweAmountInfoDto.getTotalOweAmount()), (a, b) -> {
                                        a.setTotalOweAmount(a.getTotalOweAmount().add(b.getTotalOweAmount()));
                                        return a;
                                    }))
                                    .values())
                            );
                })
                .collect(Collectors.toList());

        //填充所有的patientId对应的对象，因为查询数据库时可能患者没有欠费，就没有实体对象，但是需要告诉crm患者已经没有欠费了
        List<String> existedPatientIds = patientOweAmountInfos.stream()
                .map(PatientOweAmountInfo::getPatientId)
                .collect(Collectors.toList());
        patientOweAmountInfos.addAll(patientIdReqs.stream()
                .filter(patientIdReq -> !existedPatientIds.contains(patientIdReq))
                .map(patientIdReq -> PatientOweAmountInfo.defaultPatientOweAmountInfo(patientIdReq, clinicId))
                .collect(Collectors.toList())
        );

        return rsp.setPatientOweAmountInfos(patientOweAmountInfos);
    }

}
