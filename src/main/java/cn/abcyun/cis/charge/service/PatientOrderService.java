package cn.abcyun.cis.charge.service;

import cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientConstant;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.rpc.client.PatientOrderClient;
import cn.abcyun.cis.charge.util.JsonUtils;
import cn.abcyun.cis.commons.CisServiceResponseBody;
import cn.abcyun.cis.commons.amqp.message.PatientOrderMessage;
import cn.abcyun.cis.commons.model.CisPatientInfo;
import cn.abcyun.cis.commons.model.ShebaoCardInfo;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrder;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrderListReq;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrderListRsp;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrderReq;
import cn.abcyun.cis.commons.util.ListUtils;
import cn.abcyun.common.log.marker.AbcLogMarker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class PatientOrderService {

    @Autowired
    private PatientOrderClient mPatientOrderClient;

    public static Instant getAgeLockTime(ChargeSheet chargeSheet) {
        if (Objects.isNull(chargeSheet)) {
            return null;
        }

        if (chargeSheet.getType() != ChargeSheet.Type.OUTPATIENT) {
            return chargeSheet.getCreated();
        }

        if (chargeSheet.getOutpatientStatus() == ChargeSheet.OutpatientStatus.DIAGNOSED) {
            return null;
        } else {
            return Instant.now();
        }
    }

    public static Instant getAgeLockTimeForMessage(ChargeSheet chargeSheet, int msgType) {
        Instant ageLockTime = null;

        if (Objects.isNull(chargeSheet)) {
            return ageLockTime;
        }

        if (chargeSheet.getType() != ChargeSheet.Type.OUTPATIENT && msgType == PatientOrderMessage.MSG_TYPE_CHARGE_CREATED) {
            ageLockTime = Instant.now();
        }
        return ageLockTime;
    }

    public PatientOrder findPatientOrder(String patientOrderId, Instant ageLockTime) {
        PatientOrder patientOrder = null;
        try {
            CisServiceResponseBody<PatientOrder> rsp = mPatientOrderClient.findPatientOrder(patientOrderId, ageLockTime);
            if (rsp != null && rsp.getError() == null && rsp.getData() != null) {
                patientOrder = rsp.getData();
            }
        } catch (Exception e) {
            log.error("findPatientOrder orderId:" + patientOrderId + ", error:" + e.getMessage());
        }
        return patientOrder;
    }

    public PatientOrder updatePatientOrder(String patientOrderId, CisPatientInfo patient, ShebaoCardInfo shebaoCardInfo, String clinicId, String chainId, String operatorId, boolean isMemberCardRecharge) {

        //更新的时候这个source是没有更新的，先不管这里
        int source = isMemberCardRecharge ? PatientOrderReq.Source.MEMBER_CARD_RECHARGE : PatientOrderReq.Source.CHARGE_RETAIL;

        PatientOrderReq patientOrderReq = createPatientOrderReq(null, patient, shebaoCardInfo, clinicId, chainId, operatorId, source, null, null, null);

        PatientOrder patientOrder = null;

        try {
            CisServiceResponseBody<PatientOrder> rsp = mPatientOrderClient.updatePatientOrder(patientOrderId, patientOrderReq);
            if (rsp != null && rsp.getError() == null) {
                patientOrder = rsp.getData();
            }
        } catch (Exception e) {
            log.error("rpcUpdatePatientOrder error:", e);
            throw e;
        }

        return patientOrder;
    }

    public PatientOrder createPatientOrder(int sourceClientType, CisPatientInfo patient, ShebaoCardInfo shebaoCardInfo, String clinicId, String chainId, String operatorId, int source,
                                           String visitSourceId, String visitSourceFrom, String visitSourceRemark) {
        PatientOrderReq patientOrderReq = createPatientOrderReq(sourceClientType, patient, shebaoCardInfo, clinicId, chainId, operatorId, source, visitSourceId, visitSourceFrom, visitSourceRemark);
        PatientOrder patientOrder = null;

        try {
            CisServiceResponseBody<PatientOrder> rsp = mPatientOrderClient.createPatientOrder(patientOrderReq);
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "rsp: {}", JsonUtils.dump(rsp));
            if (rsp != null && rsp.getError() == null) {
                patientOrder = rsp.getData();
            }
        } catch (Exception e) {
            log.error("rpcCreatePatientOrder error:", e);
            throw e;
        }

        return patientOrder;
    }

    public List<PatientOrder> findListByIds(List<String> patientOrderIds, String chainId) {
        if (CollectionUtils.isEmpty(patientOrderIds) || StringUtils.isEmpty(chainId)) {
            return new ArrayList<>();
        }

        PatientOrderListReq req = new PatientOrderListReq();
        req.setChainId(chainId);
        req.setIds(patientOrderIds);

        List<PatientOrder> patientOrders = new ArrayList<>();

        try {
            CisServiceResponseBody<PatientOrderListRsp> rsp = mPatientOrderClient.findListByIds(req);
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "rsp: {}", JsonUtils.dump(rsp));
            if (rsp != null && rsp.getError() == null && rsp.getData() != null && rsp.getData().getPatientOrders() != null) {
                patientOrders = rsp.getData().getPatientOrders();
            }
        } catch (Exception e) {
            log.error("findListByIds error:", e);
            throw e;
        }

        return patientOrders;
    }

    public Map<String, Instant> findPatientOrderIdCreatedMap(List<String> patientOrderIds, String chainId) {
        List<PatientOrder> patientOrders = findListByIds(patientOrderIds, chainId);
        return generatePatientOrderIdCreatedMap(patientOrders);
    }

    public static Map<String, Instant> generatePatientOrderIdCreatedMap (List<PatientOrder> patientOrders) {
        return ListUtils.alwaysList(patientOrders)
                .stream()
                .collect(Collectors.toMap(PatientOrder::getId, PatientOrder::getCreated, (a, b) -> a));
    }


    private PatientOrderReq createPatientOrderReq(Integer sourceClientType, CisPatientInfo patient, ShebaoCardInfo shebaoCardInfo, String clinicId, String chainId, String operatorId, int source,
                                                  String visitSourceId, String visitSourceFrom, String visitSourceRemark) {
        PatientOrderReq patientOrderReq = new PatientOrderReq();
        patientOrderReq.setChainId(chainId);
        patientOrderReq.setClinicId(clinicId);
        if (patient != null) {
            patientOrderReq.setPatientId(patient.getId());
            patientOrderReq.setPatientSex(patient.getSex());
            patientOrderReq.setPatientMobile(patient.getMobile());
            patientOrderReq.setPatientAge(patient.getAge());
            patientOrderReq.setPatientBirthday(patient.getBirthday());
            patientOrderReq.setPatientName(patient.getName());
            patientOrderReq.setPatientSn(patient.getSn());
            patientOrderReq.setPatientRemark(patient.getRemark());
            patientOrderReq.setPatientProfession(patient.getProfession());
            patientOrderReq.setPatientCompany(patient.getCompany());
            //为了不扩大影响范围，只对社保创建的就诊单才填充身份证号，如果这里要全部放开，需要考虑零售开单的各种场景是否适合
            if (source == PatientOrderReq.Source.SHEBAO
                    // 证件类型是身份证(包含港澳台)的才填充
                    && Stream.of(PatientConstant.IdCardType.IDENTITY_CARD,
                            PatientConstant.IdCardType.IDENTITY_CARD_HK,
                            PatientConstant.IdCardType.IDENTITY_CARD_MACAO,
                            PatientConstant.IdCardType.IDENTITY_CARD_TAIWAN)
                    .anyMatch(idCardType -> org.apache.commons.lang3.StringUtils.equals(idCardType, patient.getIdCardType()))) {
                patientOrderReq.setPatientIdCardType(patient.getIdCardType());
                patientOrderReq.setPatientIdCard(patient.getIdCard());
            }
        }
        patientOrderReq.setShebaoCardInfo(shebaoCardInfo);
        patientOrderReq.setIsDoNotInsertOrUpdatePatient(0);
        patientOrderReq.setCreatedBy(operatorId);
        patientOrderReq.setSource(source);
        patientOrderReq.setLastModifiedBy(operatorId);
        patientOrderReq.setVisitSourceId(visitSourceId);
        patientOrderReq.setVisitSourceFrom(visitSourceFrom);
        patientOrderReq.setVisitSourceRemark(visitSourceRemark);
        if (sourceClientType != null) {
            patientOrderReq.setSourceClientType(sourceClientType);
        }
        return patientOrderReq;
    }
}
