package cn.abcyun.cis.charge.service.dto;


import cn.abcyun.cis.charge.api.model.*;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.service.dto.print.MedicalBillPrintView;
import cn.abcyun.cis.charge.util.ChargeUtils;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.cis.commons.rpc.patient.MemberInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class CalculateChargeResult {
    private String chargeSheetId;
    private BigDecimal totalFee;            //总费用
    private BigDecimal adjustmentFee;       //议价【扣除】【负数】（不包含系统议价值） adjustmentFee = draftAdjustmentFee + outpatientAdjustmentFee + oddFee
    private BigDecimal discountFee;         //折扣【扣除】【负数】
    private BigDecimal receivableFee;       //应收
    private BigDecimal needPayFee;          //需支付金额(收费时)
    private BigDecimal needRefundFee;       //需退费金额(退费时)
    private BigDecimal owedRefundFee;       //欠退金
    private BigDecimal netIncomeFee;        //净收金额
    private BigDecimal draftAdjustmentFee;  //收费处整单议价的值
    private BigDecimal outpatientAdjustmentFee; //门诊处整单议价的值
    private BigDecimal oddFee;  //零头处理值（四舍五入的值）
    private Integer roundingType;  //四舍五入的规则类型
    private BigDecimal afterRoundingDiscountedTotalFee;  //totalFee减去折扣并且四舍五入之后的值
    private BigDecimal excludeDraftAndOddFee; //不包含议价和零头的金额
    private BigDecimal sourceTotalPrice; //原价总金额
    private BigDecimal discountTotalFee; //优惠总金额 = 单项优惠总金额 + 整单优惠总金额
    private BigDecimal singleDiscountTotalFee; //单项优惠总金额 = 单项优惠 + 单项议价，可能结果为正值
    private BigDecimal packageDiscountTotalFee; //整单优惠总金额 = 整单优惠 + 整单议价，可能结果为正值

    private BigDecimal unitAdjustmentFee; //单项议价总值

    private BigDecimal promotionDiscountTotalFee;// 折扣总数【负数】包含挂号费已经收了的折扣总数

    @JsonIgnore
    private BigDecimal receivedMemberCardFee;

    @JsonIgnore
    private BigDecimal receivedHealthCardFee;

    private BigDecimal registrationDiscountFee;
    private BigDecimal westernMedicineDiscountFee;
    private BigDecimal chineseMedicineDiscountFee;
    private BigDecimal treatmentDiscountFee;
    private BigDecimal examinationDiscountFee;
    private BigDecimal materialDiscountFee;
    private boolean isUseLimitPrice = false;
    /**
     * 是否触发挂网价限价
     */
    private boolean isUseListingPrice = false;

    private List<CalculateChargeFormView> chargeForms;
    private List<PromotionView> promotions;
    private List<CouponPromotionView> couponPromotions;
    private List<GiftRulePromotionView> giftRulePromotions;
    //即将达到的满减满赠活动
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private OncomingGiftRulePromotionView oncomingGiftRulePromotion;
    private PatientPointsInfoView patientPointsInfo;
    private List<PatientCardPromotionView> patientCardPromotions;
    private List<PatientPointDeductProductPromotionView> patientPointDeductProductPromotions;
    private List<ProcessInfoView> processInfos;
    private List<VerifyInfoView> verifyInfoViews;
    private BigDecimal patientTotalFee;
    private String memberId;
    private MemberInfo memberInfo;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<PatientCardView> canPaidPatientCards;

    private List<MedicalBillPrintView> medicalBill;

    private ChargeSheet sourceChargeSheet;
    /**
     * 社保应收
     */
    private BigDecimal sheBaoReceivableFee;

    /**
     * 总成本价
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal totalCostPrice;

    /**
     * 零售开单的类型
     */
    private Integer retailType;

    /**
     * 批量提单时选中的收费单id列表
     */
    private List<String> batchExtractChargeSheetIds;

    public BigDecimal getExcludeDraftAndOddFee() {
        return MathUtils.wrapBigDecimalOrZero(needPayFee).subtract(MathUtils.wrapBigDecimalOrZero(draftAdjustmentFee)).subtract(MathUtils.wrapBigDecimalOrZero(oddFee));
    }

    public void mergeRegistrationResult(CalculateChargeResult regResult) {
        if (regResult == null) {
            return;
        }
        totalFee = MathUtils.wrapBigDecimalAdd(totalFee, regResult.getTotalFee());
        adjustmentFee = MathUtils.wrapBigDecimalAdd(adjustmentFee, regResult.getAdjustmentFee());
        discountFee = MathUtils.wrapBigDecimalAdd(discountFee, regResult.getDiscountFee());
        receivableFee = MathUtils.wrapBigDecimalAdd(receivableFee, regResult.getReceivableFee());
        needPayFee = MathUtils.wrapBigDecimalAdd(needPayFee, regResult.getNeedPayFee());
        needRefundFee = MathUtils.wrapBigDecimalAdd(needRefundFee, regResult.getNeedRefundFee());
        owedRefundFee = MathUtils.wrapBigDecimalAdd(owedRefundFee, regResult.getOwedRefundFee());
        netIncomeFee = MathUtils.wrapBigDecimalAdd(netIncomeFee, regResult.getNetIncomeFee());

        if (chargeForms == null) {
            chargeForms = new ArrayList<>();
        }
        if (regResult.getChargeForms() != null) {
            chargeForms.addAll(0, regResult.getChargeForms());
        }

        this.promotions = ChargeUtils.mergePromotionViewList(regResult.getPromotions(), this.promotions);
    }

    public void mergePromotions(List<PromotionView> promotions) {
        this.promotions = ChargeUtils.mergePromotionViewList(promotions, this.promotions);
    }

    public void summaryPromotionDiscountTotalFee() {
        if (!CollectionUtils.isEmpty(this.promotions)) {
            promotionDiscountTotalFee = this.promotions.stream().map(PromotionView::getDiscountPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        }else {
            promotionDiscountTotalFee = BigDecimal.ZERO;
        }
    }

    public void setSheBaoReceivableFee(BigDecimal sheBaoReceivableFee) {
        this.sheBaoReceivableFee = sheBaoReceivableFee;
    }
}
