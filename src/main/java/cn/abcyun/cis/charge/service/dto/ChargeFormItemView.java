package cn.abcyun.cis.charge.service.dto;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.TraceableCode;
import cn.abcyun.bis.rpc.sdk.his.model.surgery.SurgeryDetailView;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.processor.limitprice.BaseLimitPriceInfo;
import cn.abcyun.cis.charge.util.ChargeFormItemUtils;
import cn.abcyun.cis.charge.util.JsonUtils;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.cis.commons.rpc.outpatient.PrescriptionAcupoint;
import cn.abcyun.cis.commons.rpc.outpatient.PrescriptionFormGoodsItem;
import cn.abcyun.cis.commons.rpc.outpatient.UsageInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Setter;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Objects;

@Data
public class ChargeFormItemView {

    private String id;

    private String clinicId;

    private String chainId;

    private String patientOrderId;

    private String chargeSheetId;

    private String chargeFormId;

    private String sourceFormItemId;

    private int payStatus;

    private int status;

    private String unit;

    private String name;

    private BigDecimal unitCostPrice;

    private BigDecimal unitCount;

    private BigDecimal doseCount;

    private BigDecimal unitPrice;

    private BigDecimal expectedUnitPrice;

    private BigDecimal sourceUnitPrice;

    private BigDecimal discountPrice;

    private BigDecimal promotionPrice;

    private BigDecimal totalPrice;  //总价不算折扣 unitPrice * unitCount * packageCount;

    private BigDecimal expectedTotalPrice;

    private BigDecimal totalPriceRatio;

    private BigDecimal expectedTotalPriceRatio;

    private BigDecimal unitAdjustmentFee;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String unitAdjustmentFeeLastModifiedBy;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String unitAdjustmentFeeLastModifiedByName;

    private BigDecimal sourceTotalPrice;

    private BigDecimal adjustmentPrice;

    private BigDecimal discountedTotalPrice; //折后价

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal displayDiscountedTotalPrice; //用于展示的折后总价

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal displayDiscountedUnitPrice; //用于展示的折后单价

    private int useDismounting;

    private int productType;

    private int productSubType;

    private String productId;

    private Integer groupId;

    private int sort;

    private int composeType = 0;

    private int feeComposeType;

    private Long feeTypeId;

    /**
     * 0费用本身，1费用母项，2费用子项
     */
    private int goodsFeeType;

    private List<ChargeFormItemView> composeChildren;

    private int paySource;

    private int isUseLimitPrice;

    private int isProductDeleted;

    private int isGift;

    private int isAirPharmacy;

    private int sourceItemType;

    /**
     * 应收总值（平摊了议价之后的总值）
     */
    private BigDecimal receivableTotalFee;

    /**
     * 社保应收总金额
     */
    private BigDecimal sheBaoReceivableTotalFee;

    /**
     * 应收单价（平摊了议价之后的单价）
     */
    private BigDecimal receivableUnitFee;

    /**
     * 社保应收单价（平摊了议价之后的单价）
     */
    private BigDecimal sheBaoReceivableUnitFee;

    /**
     * 医生id
     */
    private String doctorId;

    private String doctorName;

    /**
     * 医生科室id
     */
    private String departmentId;

    private String departmentName;

    /**
     * 护士id
     */
    private String nurseId;

    private String nurseName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 药房号：默认为0
     */
    private int pharmacyNo;

    /**
     * 药房类型：0：实体药房，1：虚拟药房，2：空中药房
     */
    private int pharmacyType;

    /**
     * 退费的标记
     * {@link cn.abcyun.cis.charge.processor.ItemProcessor.ItemRefundFlag}
     */
    private int itemRefundFlag;

    /**
     * 是否期望指定批次
     */
    private int isExpectedBatch;
    @ApiModelProperty("手术申请单信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private SurgeryDetailView surgeryDetail;

    public BigDecimal getReceivableUnitFee() {

        return ChargeFormItemUtils.calculateReceivableUnitFee(receivableTotalFee, unitPrice, unitCount, doseCount, deductedTotalCount);
    }

    public BigDecimal getSheBaoReceivableUnitFee() {
        return ChargeFormItemUtils.calculateReceivableUnitFee(sheBaoReceivableTotalFee, unitPrice, unitCount, doseCount, deductedTotalCount);
    }

    //煎法
    private String specialRequirement;

    /**
     * 标记为过了社保支付的项目
     */
    private int isMarkedByHealthCardPay;

    @JsonIgnore
    private String createdBy;

    private Instant created;
    @JsonIgnore
    private String lastModifiedBy;
    @JsonIgnore
    private Instant lastModified;

    // 业务附加字段，不存入数据库

    private JsonNode productInfo;
    private JsonNode usageInfo;
    private JsonNode promotionInfo;

    private int isOutOfStock;

    /**
     * 是否可以被删除，0：可以，1：不可以
     */
    private int isCanNotDelete;

    /**
     * 门诊原单价
     */
    private BigDecimal doctorSourceUnitPrice;
    /**
     * 门诊原总价
     */
    private BigDecimal doctorSourceTotalPrice;
    /**
     * 是否需要执行。执行站获取收费单详情时需要
     */
    private int needExecutive;

    /**
     * 单项优惠
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<SinglePromotionView> singlePromotions;

    // for nurse
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer ast;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private JsonNode astResult;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private JsonNode examinationResult;

    /**
     * 如果是治疗理疗，就表示治疗理疗的执行次数，如果是检查检验，就表示检查检验的完成次数，用一个字段表示
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private BigDecimal executedUnitCount;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer executedActionCount;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer executeStatus;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String executeStatusName;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String lastExecutedByName;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Instant lastExecutedDate;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<PrescriptionAcupoint> acupoints;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<PrescriptionFormGoodsItem> externalGoodsItems;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String outpatientRemark; // 门诊备注

    /**
     * 自负比例
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal shebaoSelfPayRatio;

    private BigDecimal stockPieceCount = BigDecimal.ZERO; //库存pieceCount
    private BigDecimal stockPackageCount = BigDecimal.ZERO; //库存packageCount
    private BigDecimal canRefundUnitCount = BigDecimal.ZERO;
    private BigDecimal canRefundDoseCount = BigDecimal.ZERO;

    /**
     * 可退的抵扣数量
     */
    private BigDecimal canRefundDeductCount = BigDecimal.ZERO;


    /**
     * 总共抵扣的数量
     */
    private BigDecimal deductedTotalCount = BigDecimal.ZERO;


    /**
     * 可退的核销数量
     */
    private BigDecimal canRefundVerifyCount = BigDecimal.ZERO;


    /**
     * 总共核销的数量
     */
    private BigDecimal verifyTotalCount = BigDecimal.ZERO;


    /**
     * 牙位
     */
    private List<Integer> toothNos;

    /**
     * 追溯码是否展示(0是 1否)
     */
    private int isShowTraceableCode;

    /**
     * 追溯码集合
     */
    private List<TraceableCode> traceableCodeList;


    /**
     * 批量提单时的原始chargeFormItemId列表
     */
    private List<String> originalChargeFormItemIds;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<ChargeFormItemBatchInfoView> chargeFormItemBatchInfos;

    /**
     * 触发的限价规则快照
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BaseLimitPriceInfo.LimitInfo limitInfo;

    /**
     * 是否存在已退药
     */
    @JsonIgnore
    private boolean hasUndispense;

//    public BigDecimal getDiscountedTotalPrice() {
//        discountedTotalPrice = MathUtils.wrapBigDecimalOrZero(totalPrice).add(MathUtils.wrapBigDecimalOrZero(discountPrice));
//        return discountedTotalPrice;
//    }

    public BigDecimal getSourceTotalPrice() {

        if (sourceTotalPrice == null) {
            return MathUtils.calculateTotalPrice(sourceUnitPrice, unitCount, doseCount, 2);
        }

        return sourceTotalPrice;
    }

    // 对于展示层，这里特殊处理为，把议价加价从原价中扣除
//    public BigDecimal getTotalPrice() {
//        if (MathUtils.wrapBigDecimalCompare(adjustmentPrice, BigDecimal.ZERO) > 0) {
//            return MathUtils.wrapBigDecimalSubtract(totalPrice, adjustmentPrice);
//        }
//        return totalPrice;
//    }

    public String getSpecialRequirement() {

        if (usageInfo != null) {

            UsageInfo obj = JsonUtils.readValue(usageInfo, UsageInfo.class);
            if (obj != null) {
                specialRequirement = obj.getSpecialRequirement();
            }
        }
        return specialRequirement;
    }

    public void setSpecialRequirement(String specialRequirement) {
        this.specialRequirement = specialRequirement;
    }

    public BigDecimal calculateUnitPriceView(boolean isGoodsItemMakeUp) {

        if (!isGoodsItemMakeUp) {
            return unitPrice;
        }

        MathUtils.CalculateExpectedUnitPriceResult calculateExpectedUnitPriceResult = ChargeFormItemUtils.calculateRefundUnitPrice(chargeFormItemBatchInfos, unitPrice, unitCount, doseCount, productType, productSubType, totalPrice);

        return calculateExpectedUnitPriceResult.expectedUnitPrice;
    }

    /**
     * 是否显示追溯码
     */
    public boolean showTraceableCode() {
        return (Objects.equals(Constants.ProductType.MEDICINE, productType))
                || (Objects.equals(Constants.ProductType.MATERIAL, productType) && Objects.equals(Constants.ProductType.SubType.MEDICAL_MATERIAL, productSubType));
    }

    public boolean isHasUndispense() {
        if (CollectionUtils.isEmpty(composeChildren)) {
            return ChargeFormItemUtils.isCanDispensing(goodsFeeType, productType, productId) && getStatus() == Constants.ChargeFormItemStatus.REFUNDED;
        }
        return composeChildren.stream().anyMatch(ChargeFormItemView::isHasUndispense);
    }
}
