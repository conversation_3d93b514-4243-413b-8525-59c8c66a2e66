package cn.abcyun.cis.charge.service.rpc;

import cn.abcyun.bis.rpc.sdk.cis.client.AbcCisMcFeignClient;
import cn.abcyun.bis.rpc.sdk.cis.model.mc.GetChainWeClinicOpenedStatusReq;
import cn.abcyun.bis.rpc.sdk.cis.model.mc.GetChainWeClinicOpenedStatusRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.mc.GetQrCodeRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.mc.QrCodeSceneType;
import cn.abcyun.cis.core.util.FeignClientRpcTemplate;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixProperty;
import feign.RetryableException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@Slf4j
public class CisMcService {

    @Autowired
    private AbcCisMcFeignClient client;

    public GetChainWeClinicOpenedStatusRsp getChainsWeClinicOpenedStatus(String chainId){

        if (StringUtils.isEmpty(chainId)) {
            return null;
        }

        GetChainWeClinicOpenedStatusReq req = new GetChainWeClinicOpenedStatusReq();
        req.setChainId(chainId);

        return FeignClientRpcTemplate.dealRpcClientMethod("getChainsWeClinicOpenedStatus", () -> client.getChainsWeClinicOpenedStatus(req), req);
    }

    /**
     * 获取订单支付的场景二维码信息
     * @param chainId
     * @param chargeSheetId
     * @param operatorId
     * @param clinicId
     * @param patientId
     * @return
     */
    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public GetQrCodeRsp getOrderPayQrCode(String chainId, String chargeSheetId, String operatorId, String clinicId, String patientId) {

        if (StringUtils.isAnyEmpty(chainId, chargeSheetId)) {
            return null;
        }

        String scene = QrCodeSceneType.ORDER_PAY;
        return FeignClientRpcTemplate.dealRpcClientMethod("getOrderPayQrCode",
                () -> client.getSceneQrCode(scene, chainId, chargeSheetId, operatorId, clinicId, patientId),
                scene, chainId, chargeSheetId, operatorId, clinicId, patientId);
    }

    /**
     * 是否开通微诊所或者小程序
     * @param chainId
     * @return
     */
    @HystrixCommand(fallbackMethod = "isOpenWeClinicOrWeAppFallback",
            commandProperties = {
                    @HystrixProperty(name = "execution.isolation.thread.timeoutInMilliseconds", value = "3000"),
                    @HystrixProperty(name = "execution.isolation.strategy", value = "THREAD")
            }
    )
    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public boolean isOpenWeClinicOrWeApp(String chainId){

        if (StringUtils.isEmpty(chainId)) {
            return false;
        }

        return Optional.ofNullable(getChainsWeClinicOpenedStatus(chainId)).map(GetChainWeClinicOpenedStatusRsp::getIsOpened).orElse(0) == 1;
    }

    public boolean isOpenWeClinicOrWeAppFallback(String chainId){
        log.info("isOpenWeClinicOrWeAppFallback chainId: {}", chainId);
        return false;
    }
}
