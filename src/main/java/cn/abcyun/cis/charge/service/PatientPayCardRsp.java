package cn.abcyun.cis.charge.service;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
public class PatientPayCardRsp {
    private List<PatientCardView> patientCards;

    @Data
    @Accessors(chain = true)
    public static class PatientCardView {

        private String id;

        private String name;
        // 卡费
        private BigDecimal cardFee;
        // 卡余额
        private BigDecimal cardBalance;
        // 卡本金
        private BigDecimal principal;
        // 卡赠金
        private BigDecimal present;

    }
}
