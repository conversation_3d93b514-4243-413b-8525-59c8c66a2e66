package cn.abcyun.cis.charge.service.rpc;

import cn.abcyun.bis.rpc.sdk.cis.client.AbcCisShebaoFeignClient;
import cn.abcyun.bis.rpc.sdk.cis.model.charge.DiagnosisInfo;
import cn.abcyun.bis.rpc.sdk.cis.model.shebao.*;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.core.util.FeignClientRpcTemplate;
import cn.abcyun.cis.core.util.JsonUtils;
import cn.abcyun.common.log.marker.AbcLogMarker;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixProperty;
import feign.RetryableException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class CisShebaoService {

    private final AbcCisShebaoFeignClient client;

    @Autowired
    public CisShebaoService(AbcCisShebaoFeignClient client) {
        this.client = client;
    }

    @HystrixCommand(fallbackMethod = "queryReversalWaitForByChargeSheetIdsFallback",
            commandProperties = {
                    @HystrixProperty(name = "execution.isolation.thread.timeoutInMilliseconds", value = "2000"),
                    @HystrixProperty(name = "execution.isolation.strategy", value = "THREAD")
            }
    )
    public List<QueryChargeSheetReverseListResBody.ChargeSheetItem> queryReversalWaitForByChargeSheetIds(String chainId, String clinicId, List<String> chargeSheetIds) {

        if (StringUtils.isAnyEmpty(chainId, clinicId)) {
            return new ArrayList<>();
        }

        if (CollectionUtils.isEmpty(chargeSheetIds)) {
            return new ArrayList<>();
        }

        QueryChargeSheetReverseListReq req = new QueryChargeSheetReverseListReq();
        req.setChainId(chainId);
        req.setClinicId(clinicId);
        req.setChargeSheetIds(chargeSheetIds);
        QueryChargeSheetReverseListResBody rsp = null;
        try {
            rsp = FeignClientRpcTemplate.dealRpcClientMethod("queryReversalWaitForByChargeSheetIds", () -> client.queryReversalWaitForByChargeSheetIds(req), req);
        } catch (Exception e) {
            log.error("queryReversalWaitForByChargeSheetIds error: {}", e.getMessage());
        }

        return Optional.ofNullable(rsp).map(QueryChargeSheetReverseListResBody::getChargeSheetList).orElse(new ArrayList<>());

    }

    public List<QueryChargeSheetReverseListResBody.ChargeSheetItem> queryReversalWaitForByChargeSheetIdsFallback(String chainId, String clinicId, List<String> chargeSheetIds) {
        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "queryReversalWaitForByChargeSheetIdsFallback, chainId: {}, clinicId: {}, chargeSheetIds: {}", chainId, clinicId, JsonUtils.dump(chargeSheetIds));
        return new ArrayList<>();
    }

//    public List<String> queryContainShebaoExceptionList(String chainId, String clinicId, List<String> chargeSheetIds) {
//
//        List<QueryChargeSheetReverseListResBody.ChargeSheetItem> chargeSheets = queryReversalWaitForByChargeSheetIds(chainId, clinicId, chargeSheetIds);
//
//        return Optional.ofNullable(chargeSheets).orElse(new ArrayList<>()).stream().map(QueryChargeSheetReverseListResBody.ChargeSheetItem::getChargeSheetId).collect(Collectors.toList());
//    }

    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public QueryProcessFeesMatchCodeResBody queryProcessFeesMatchCode(QueryProcessFeesMatchCodeReqBody req) {

        return FeignClientRpcTemplate.dealRpcClientMethod("queryProcessFeesMatchCode", () -> client.queryProcessFeesMatchCode(req), req);
    }

    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public MatchCodeCommonResp operateProcessFeesMatchCode(ProcessFeesMatchCodeOperatorReqBody req) {

        return FeignClientRpcTemplate.dealRpcClientMethod("operateProcessFeesMatchCode", () -> client.operateProcessFeesMatchCode(req), req);
    }

    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public QueryChargeSheetLongCareInfoRsp queryLoneCareInvoiceByHospitalOrderId(String chainId, String clinicId, String hospitalOrderId) {

        if (StringUtils.isAnyEmpty(chainId, clinicId, hospitalOrderId)) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chainId or clinicId or hospitalSheetId is null, chainId: {}, clinicId: {}, hospitalOrderId: {}", chainId, clinicId, hospitalOrderId);
            return null;
        }
        return FeignClientRpcTemplate.dealRpcClientMethod("queryLoneCareInvoiceByHospitalOrderId",
                () -> client.queryLoneCareInvoiceByHospitalOrderId(chainId, clinicId, hospitalOrderId),
                chainId, clinicId, hospitalOrderId);
    }

    /**
     * 查询长护单的外诊列表
     * @param chainId
     * @param clinicId
     * @param hospitalOrderId
     * @return
     */
    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public QueryLongCareOutpatientChargeSheetRsp queryLongCareOutpatientChargeSheetItems(String chainId, String clinicId, String hospitalOrderId) {

        if (StringUtils.isAnyEmpty(chainId, clinicId, hospitalOrderId)) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chainId or clinicId or hospitalSheetId is null, chainId: {}, clinicId: {}, hospitalOrderId: {}", chainId, clinicId, hospitalOrderId);
            return null;
        }
        return FeignClientRpcTemplate.dealRpcClientMethod("queryLongCareOutpatientChargeSheetItems",
                () -> client.queryLongCareOutpatientChargeSheetItems(chainId, clinicId, hospitalOrderId),
                chainId, clinicId, hospitalOrderId);
    }

    /**
     * 查询长护单的自付比例
     * @param chainId
     * @param clinicId
     * @param hospitalOrderId
     * @return
     */
    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public List<LongCareChargeResultFormItemView> queryLongCarePaymentResultItems(String chainId, String clinicId, String hospitalOrderId) {

        if (StringUtils.isAnyEmpty(chainId, clinicId, hospitalOrderId)) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chainId or clinicId or hospitalSheetId is null, chainId: {}, clinicId: {}, hospitalOrderId: {}", chainId, clinicId, hospitalOrderId);
            return null;
        }
        QueryLongCarePaymentItemsRsp rsp = FeignClientRpcTemplate.dealRpcClientMethod("queryLongCarePaymentResultItems",
                () -> client.queryLongCarePaymentResultItems(chainId, clinicId, hospitalOrderId),
                chainId, clinicId, hospitalOrderId);

        return Optional.ofNullable(rsp).map(QueryLongCarePaymentItemsRsp::getLongCarePaymentItems)
                .orElse(new ArrayList<>());
    }

    /**
     * 校验收费单是否可以进行医保支付
     * @param chargeSheetId
     * @param chainId
     * @param clinicId
     * @param diagnosisInfos
     * @param doctorId
     * @param departmentId
     * @param type
     * @return
     */
    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public MobilePayPreCheckRspBody preCheckMobilePay(String chargeSheetId,
                                                      String chainId,
                                                      String clinicId,
                                                      List<DiagnosisInfo> diagnosisInfos,
                                                      String doctorId,
                                                      String departmentId,
                                                      int type) {
        MobilePayPreCheckReqBody req = new MobilePayPreCheckReqBody();
        req.setChainId(chainId);
        req.setClinicId(clinicId);
        req.setDiagnosisInfos(diagnosisInfos);
        req.setDoctorId(doctorId);
        req.setDepartmentId(departmentId);
        req.setType(type);
        return FeignClientRpcTemplate.dealRpcClientMethod("queryLongCareOutpatientChargeSheetItems",
                () -> client.preCheckMobilePay(req, chargeSheetId),
                req, chargeSheetId);
    }

    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public ShebaoPayTaskClosedRspBody closePayTask(String chainId, String clinicId, String chargePayTransactionId, String taskId, String operatorId) {

        if (StringUtils.isAnyBlank(chainId, clinicId, taskId, chargePayTransactionId)) {
            return null;
        }

        if (StringUtils.isEmpty(operatorId)) {
            operatorId = Constants.DEFAULT_OPERATORID;
        }

        ShebaoPayTaskClosedReqBody req = new ShebaoPayTaskClosedReqBody();
        req.setChainId(chainId);
        req.setClinicId(clinicId);
        req.setTransactionId(chargePayTransactionId);
        req.setTaskId(taskId);
        req.setOperateId(operatorId);

        return FeignClientRpcTemplate.dealRpcClientMethod("closedPayTask", true, () -> client.closedPayTask(req));
    }

    public void nationalYimaPayAliNotify(String chainId, String clinicId, String chargeSheetId, String employeeId,
    BigDecimal chargeSheetReceivable, String alipayTradeNo, BigDecimal alipayAmount, NationalYimaPayAliNotifyReqBody.BizType bizType, Instant gmtPaid, NationalYimaPayAliNotifyReqBody.PaymentPlace paymentPlace) {

        NationalYimaPayAliNotifyReqBody req = new NationalYimaPayAliNotifyReqBody();
        req.setChainId(chainId);
        req.setClinicId(clinicId);
        req.setChargeSheetId(chargeSheetId);
        req.setEmployeeId(employeeId);
        req.setChargeSheetReceivable(chargeSheetReceivable);
        req.setAlipayTradeNo(alipayTradeNo);
        req.setAlipayAmount(alipayAmount);
        req.setBizType(bizType);
        req.setGmtPaid(gmtPaid);
        req.setPaymentPlace(paymentPlace);

        FeignClientRpcTemplate.dealRpcClientMethod("closedPayTask", true, () -> client.nationalYimaPayAliNotify(chargeSheetId, req), chargeSheetId, req);
    }







}
