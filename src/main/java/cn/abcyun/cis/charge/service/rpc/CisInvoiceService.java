package cn.abcyun.cis.charge.service.rpc;


import cn.abcyun.bis.rpc.sdk.cis.client.AbcCisInvoiceFeignClient;
import cn.abcyun.bis.rpc.sdk.cis.model.invoice.*;
import cn.abcyun.cis.commons.exception.FeignRuntimeException;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.exception.ServiceInternalException;
import cn.abcyun.cis.core.util.FeignClientRpcTemplate;
import cn.abcyun.cis.core.util.JsonUtils;
import cn.abcyun.common.model.AbcListPage;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixProperty;
import feign.Request;
import feign.RetryableException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import springfox.documentation.spring.web.json.Json;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class CisInvoiceService {

    @Autowired
    private AbcCisInvoiceFeignClient client;

    @HystrixCommand(fallbackMethod = "createInvoiceFallback",
            commandProperties = {
                    @HystrixProperty(name = "execution.isolation.thread.timeoutInMilliseconds", value = "3000"),
                    @HystrixProperty(name = "execution.isolation.strategy", value = "THREAD")
            },
            ignoreExceptions = {FeignRuntimeException.class}
    )
    public CreateInvoiceRsp createInvoice(CreateInvoiceReq req) {

        CreateInvoiceRsp createInvoiceRsp = FeignClientRpcTemplate.dealRpcClientMethod("createInvoice", true,
                () -> client.createInvoice(req, new Request.Options(60, TimeUnit.SECONDS, 60, TimeUnit.SECONDS, true)),
                req);
        return createInvoiceRsp;
    }

    public CreateInvoiceRsp createInvoiceFallback(CreateInvoiceReq req) {
        throw new ServiceInternalException("发票服务异常，请稍后重试");
    }

    /**
     * 开冲红发票
     *
     * @param req
     * @return
     */
    @HystrixCommand(fallbackMethod = "chonghongInvoiceFallback",
            commandProperties = {
                    @HystrixProperty(name = "execution.isolation.thread.timeoutInMilliseconds", value = "3000"),
                    @HystrixProperty(name = "execution.isolation.strategy", value = "THREAD")
            },
            ignoreExceptions = {FeignRuntimeException.class}
    )
    public ChonghongInvoiceRsp chonghongInvoice(ChonghongInvoiceReq req) {

        ChonghongInvoiceRsp chonghongInvoiceRsp = FeignClientRpcTemplate.dealRpcClientMethod("chonghongInvoice",
                () -> client.chonghongInvoice(req),
                req);
        return chonghongInvoiceRsp;
    }

    public ChonghongInvoiceRsp chonghongInvoiceFallback(ChonghongInvoiceReq req) {
        throw new ServiceInternalException("发票服务异常，请稍后重试");
    }

    @HystrixCommand(fallbackMethod = "destroyInvoiceFallback",
            commandProperties = {
                    @HystrixProperty(name = "execution.isolation.thread.timeoutInMilliseconds", value = "3000"),
                    @HystrixProperty(name = "execution.isolation.strategy", value = "THREAD")
            },
            ignoreExceptions = {FeignRuntimeException.class}
    )
    public DestroyInvoiceRsp destroyInvoice(DestroyInvoiceReq req) {
        return FeignClientRpcTemplate.dealRpcClientMethod("destroyInvoice",
                () -> client.destroyInvoice(req),
                req);
    }

    public DestroyInvoiceRsp destroyInvoiceFallback(DestroyInvoiceReq req) {
        log.warn("请求参数: {}", JsonUtils.dump(req));
        throw new ServiceInternalException("发票服务异常，请稍后重试");
    }

    /**
     * 查询发票详情
     *
     * @param clinicId
     * @param businessId
     * @return
     */
    @HystrixCommand(fallbackMethod = "getInvoiceByBusinessIdFallback",
            commandProperties = {
                    @HystrixProperty(name = "execution.isolation.thread.timeoutInMilliseconds", value = "3000"),
                    @HystrixProperty(name = "execution.isolation.strategy", value = "THREAD")
            },
            ignoreExceptions = {FeignRuntimeException.class}
    )
    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public InvoiceView getInvoiceByBusinessId(String clinicId, String businessScene, String businessId) {
        if (StringUtils.isAnyEmpty(clinicId, businessId)) {
            return null;
        }
        return FeignClientRpcTemplate.dealRpcClientMethod("getInvoiceByBusinessId",
                () -> client.getInvoiceByBusinessId(clinicId, businessScene, businessId),
                clinicId, businessScene, businessId);
    }

    public InvoiceView getInvoiceByBusinessIdFallback(String clinicId, String businessScene, String businessId) {
        log.error("getInvoiceByBusinessIdFallback, clinicId: {}, businessScene: {}, businessId: {}", clinicId, businessScene, businessId);
        return null;
    }


    /**
     * 查询发票配置
     *
     * @param clinicId
     * @return
     */
    @HystrixCommand(fallbackMethod = "getInvoiceConfigAllFallback",
            commandProperties = {
                    @HystrixProperty(name = "execution.isolation.thread.timeoutInMilliseconds", value = "3000"),
                    @HystrixProperty(name = "execution.isolation.strategy", value = "THREAD")
            },
            ignoreExceptions = {FeignRuntimeException.class}
    )
    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public List<InvoiceConfigView> getInvoiceConfigAll(String clinicId) {

        if (StringUtils.isEmpty(clinicId)) {
            return new ArrayList<>();
        }

        AbcListPage<InvoiceConfigView> invoiceConfigListPage = FeignClientRpcTemplate.dealRpcClientMethod("getInvoiceConfig", true,
                () -> client.getInvoiceConfigAll(clinicId),
                clinicId);

        return Optional.ofNullable(invoiceConfigListPage).map(AbcListPage::getRows).orElse(new ArrayList<>());
    }

    public List<InvoiceConfigView> getInvoiceConfigAllFallback(String clinicId) {
        log.error("getInvoiceConfigAllFallback, clinicId: {}", clinicId);
        return new ArrayList<>();
    }

    /**
     * 查看开票配置详情
     *
     * @param chainId  连锁ID
     * @param clinicId 门店ID
     * @return 开票配置
     */
    @HystrixCommand(fallbackMethod = "getInvoiceBillConfigByClinicIdFallback",
            commandProperties = {
                    @HystrixProperty(name = "execution.isolation.thread.timeoutInMilliseconds", value = "3000"),
                    @HystrixProperty(name = "execution.isolation.strategy", value = "THREAD")
            },
            ignoreExceptions = {FeignRuntimeException.class}
    )
    @Retryable(value = {RetryableException.class}, backoff = @Backoff(delay = 100L))
    public InvoiceBillConfigView getInvoiceBillConfigByClinicId(String chainId,
                                                                String clinicId) {
        if (StringUtils.isBlank(chainId)){
            throw new ParamRequiredException("chainId");
        }
        if (StringUtils.isBlank(clinicId)){
            throw new ParamRequiredException("clinicId");
        }

        return FeignClientRpcTemplate.dealRpcClientMethod("getInvoiceBillConfigByClinicId",
                () -> client.getInvoiceBillConfig(chainId, clinicId),
                chainId, clinicId);
    }

    public InvoiceBillConfigView getInvoiceBillConfigByClinicIdFallback(String chainId, String clinicId) {
        log.error("getInvoiceBillConfigByClinicIdFallback, chainId: {}, clinicId: {}", chainId, clinicId);
        return null;
    }
}
