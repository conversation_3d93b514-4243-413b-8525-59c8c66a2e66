package cn.abcyun.cis.charge.service.rpc;

import cn.abcyun.bis.rpc.sdk.cis.client.AbcCisGspClient;
import cn.abcyun.bis.rpc.sdk.cis.model.gsp.register.AddGspRegisterPrescriptionReq;
import cn.abcyun.bis.rpc.sdk.cis.model.gsp.register.GspRegisterInfoView;
import cn.abcyun.cis.core.util.FeignClientRpcTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-04-03 16:51:03
 */
@Slf4j
@Service
public class CisGspService {
    @Autowired
    private AbcCisGspClient abcCisGspClient;

    /**
     * 处方登记
     */
    public GspRegisterInfoView addGspRegisterPrescription(AddGspRegisterPrescriptionReq req) {
        if (req == null) {
            return null;
        }
        return FeignClientRpcTemplate.dealRpcClientMethod("addGspPrescriptionInfo", () -> abcCisGspClient.addGspPrescriptionInfo(req), req);
    }
}
