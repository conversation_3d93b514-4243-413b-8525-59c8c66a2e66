package cn.abcyun.cis.charge.service;

import cn.abcyun.cis.charge.service.rpc.CisMcService;
import cn.abcyun.cis.charge.service.rpc.CisWechatPayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class McService {

    @Autowired
    private CisMcService cisMcService;

    @Autowired
    private CisWechatPayService wechatPayService;

    /**检查诊所是否开启了微信支付和开启了微诊所*/
    public boolean canSupportSelfPay(String chainId, String clinicId) {
        boolean isOpenWechatPay = wechatPayService.isClinicWechatPayEnable(chainId, clinicId);
        boolean isOpenWeClinic = cisMcService.isOpenWeClinicOrWeApp(chainId);
        if (isOpenWechatPay && isOpenWeClinic) {
            return true;
        }
//        sLogger.error("chainId={},clinicId={},isOpenWechatPay={} isOpenWeClinic ={}", chainId,clinicId,isOpenWechatPay,isOpenWeClinic);
        return false;
    }

}
