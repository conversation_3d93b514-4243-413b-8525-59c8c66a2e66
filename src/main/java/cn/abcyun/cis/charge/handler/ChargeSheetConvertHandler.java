package cn.abcyun.cis.charge.handler;

import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.medicalplan.MedicalPlan;
import cn.abcyun.bis.rpc.sdk.cis.model.medicalplan.MedicalPlanSheet;
import cn.abcyun.cis.charge.api.model.OutpatientSheetDetailCalculateReq;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.factory.ChargeFormItemFactory;
import cn.abcyun.cis.charge.factory.ChargeSheetFactory;
import cn.abcyun.cis.charge.factory.chargeform.ChargeFormFactory;
import cn.abcyun.cis.charge.handler.dto.MedicalPlanSheetConvertChargeSheetRsp;
import cn.abcyun.cis.charge.handler.dto.OutpatientSheetConvertChargeSheetRsp;
import cn.abcyun.cis.charge.model.ChargeForm;
import cn.abcyun.cis.charge.model.ChargeFormItem;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.model.ChargeSheetAdditional;
import cn.abcyun.cis.charge.processor.calculate.CalculateModel;
import cn.abcyun.cis.charge.repository.ChargeSheetAdditionalRepository;
import cn.abcyun.cis.charge.service.ChargeAirPharmacyService;
import cn.abcyun.cis.charge.service.ChargeSheetResourceService;
import cn.abcyun.cis.charge.service.SheetProcessorService;
import cn.abcyun.cis.charge.service.rpc.CisScClinicService;
import cn.abcyun.cis.charge.util.*;
import cn.abcyun.cis.commons.rpc.outpatient.MedicalRecord;
import cn.abcyun.cis.commons.rpc.outpatient.OutpatientSheet;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.common.log.marker.AbcLogMarker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;

/**
 * chargeSheet转换器
 */
@Service
@Slf4j
public class ChargeSheetConvertHandler {

    @Autowired
    private ChargeSheetResourceService chargeSheetResourceService;

    @Autowired
    private SheetProcessorService sheetProcessorService;

    @Autowired
    private ChargeAirPharmacyService chargeAirPharmacyService;

    @Autowired
    private CisScClinicService cisScClinicService;

    @Autowired
    private ChargeSheetAdditionalRepository chargeSheetAdditionalRepository;

    public OutpatientSheetConvertChargeSheetRsp insertOrUpdateOutpatientChargeSheet(List<ChargeSheet> existedChargeSheetList, OutpatientSheet outpatientSheet, int isMember, String airPharmacyOrderId, String operatorId) {
        OutpatientSheetConvertChargeSheetRsp convertChargeSheetRsp = insertOrUpdateOutpatientChargeSheetCore(existedChargeSheetList, outpatientSheet, isMember, airPharmacyOrderId, true, operatorId);

        //已收费的门诊单，要更新最新的主诉诊断
        updateChargedChargeSheetAdditional(existedChargeSheetList, outpatientSheet, operatorId);

        if (convertChargeSheetRsp == null || convertChargeSheetRsp.getUnchargedChargeSheet() == null) {
            return null;
        }

        ChargeSheet chargeSheet = convertChargeSheetRsp.getUnchargedChargeSheet();
        boolean isUpdate = convertChargeSheetRsp.isUpdate();
//        if (chargeSheet.getLockStatus() != Constants.ChargeSheetLockStatus.LOCK_STATUS_UNLOCK) {
//            chargeSheetResourceService.unLockAndReleaseLockSheetSource(chargeSheet, null, operatorId);
//        }

        if (isUpdate) {
            FillUtils.fillLastModifiedBy(chargeSheet, operatorId);
        }

        return convertChargeSheetRsp;
    }

    private void updateChargedChargeSheetAdditional(List<ChargeSheet> existedChargeSheetList, OutpatientSheet outpatientSheet, String operatorId) {

        List<String> needUpdateChargeSheetIds = new ArrayList<>();

        existedChargeSheetList
                .stream()
                .filter(cs -> cs.getType() == ChargeSheet.Type.OUTPATIENT || cs.getType() == ChargeSheet.Type.DIRECT_OUTPATIENT_ADDITIONAL)
                .filter(cs -> cs.getStatus() != Constants.ChargeSheetStatus.UNCHARGED)
                .filter(cs -> cs.getClonePrescriptionType() != ChargeSheetAdditional.ClonePrescriptionType.FROM_PHOTO_BY_OUTPATIENT_DOCTOR)
                .forEach(chargedChargeSheet -> {

                    /**
                     * 门诊补录的收费单要更新医生信息
                     */
                    if (chargedChargeSheet.getType() == ChargeSheet.Type.DIRECT_OUTPATIENT_ADDITIONAL) {
                        chargedChargeSheet.setDoctorId(outpatientSheet.getDoctorId());
                        chargedChargeSheet.setCopywriterId(outpatientSheet.getCopywriterId());
                    }

                    needUpdateChargeSheetIds.add(chargedChargeSheet.getId());
                });

        if (CollectionUtils.isEmpty(needUpdateChargeSheetIds)) {
            return;
        }

        if (outpatientSheet.getMedicalRecord() == null) {
            return;
        }

        MedicalRecord medicalRecord = outpatientSheet.getMedicalRecord();

        String diagnosisInfosStr = CollectionUtils.isNotEmpty(medicalRecord.getDiagnosisInfos()) ? cn.abcyun.cis.commons.util.JsonUtils.dump(medicalRecord.getDiagnosisInfos()) : null;
        String extendDiagnosisInfosStr = CollectionUtils.isNotEmpty(medicalRecord.getExtendDiagnosisInfos()) ? cn.abcyun.cis.commons.util.JsonUtils.dump(medicalRecord.getExtendDiagnosisInfos()) : null;

        chargeSheetAdditionalRepository.updateForOutpatientSheet(needUpdateChargeSheetIds,
                outpatientSheet.getDepartmentId(),
                outpatientSheet.getConsultantId(),
                medicalRecord.getChiefComplaint(),
                ChargeUtils.convertDiagnosisInfo(medicalRecord.getDiagnosisInfos(), medicalRecord.getExtendDiagnosisInfos()),
                diagnosisInfosStr,
                extendDiagnosisInfosStr,
                operatorId);

    }

    public MedicalPlanSheetConvertChargeSheetRsp insertOrUpdateMedicalPlanChargeSheet(List<ChargeSheet> existedChargeSheetList, MedicalPlanSheet medicalPlanSheet, MedicalPlan medicalPlan, String operatorId) {

        MedicalPlanSheetConvertChargeSheetRsp convertChargeSheetRsp = insertOrUpdateMedicalPlanChargeSheetCore(existedChargeSheetList, medicalPlanSheet, medicalPlan, operatorId);

        if (convertChargeSheetRsp == null || convertChargeSheetRsp.getUnchargedChargeSheet() == null) {
            return null;
        }

        ChargeSheet chargeSheet = convertChargeSheetRsp.getUnchargedChargeSheet();
        boolean isUpdate = convertChargeSheetRsp.isUpdate();

//        if (chargeSheet.getLockStatus() != Constants.ChargeSheetLockStatus.LOCK_STATUS_UNLOCK) {
//            chargeSheetResourceService.unLockAndReleaseLockSheetSource(chargeSheet, null, operatorId);
//        }

        if (isUpdate) {
            FillUtils.fillLastModifiedBy(chargeSheet, operatorId);
        }

        return convertChargeSheetRsp;
    }

    public MedicalPlanSheetConvertChargeSheetRsp insertOrUpdateMedicalPlanChargeSheetCore(List<ChargeSheet> existedChargeSheetList, MedicalPlanSheet medicalPlanSheet, MedicalPlan medicalPlan, String operatorId) {
        Pair<Boolean, ChargeSheet> chargeSheetPair = MedicalPlanSheetUtils.createOrFindChargeSheet(existedChargeSheetList, medicalPlanSheet, medicalPlan, operatorId);
        ChargeSheet chargeSheet = chargeSheetPair.getRight();
        boolean isUpdate = chargeSheetPair.getLeft();

        String preUpdateKey = ChargeUtils.signForOutpatient(chargeSheet);

        //根据门诊单新增或修改收费单
        insertOrUpdateChargeSheetForMedicalPlan(chargeSheet, medicalPlanSheet, medicalPlan, operatorId);

        String afterUpdateKey = ChargeUtils.signForOutpatient(chargeSheet);
        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "preUpdateKey: {}, afterUpdateKey: {}", preUpdateKey, afterUpdateKey);


        //比较本次门诊修改的消息是否对收费单真正有影响
        if (isUpdate && !Objects.equals(preUpdateKey, afterUpdateKey)) {
            chargeSheet.setIsClosed(0);
        }

        long chargeItemCount = chargeSheet.getChargeForms()
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getChargeFormItems() != null)
                .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                .filter(Objects::nonNull)
                .filter(chargeFormItem -> chargeFormItem.getIsDeleted() == 0)
                .count();


        if (chargeItemCount == 0) {

            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeItemCount == 0, chargeSheetId:{}, isUpdate:{}", chargeSheet.getId(), isUpdate);
            if (isUpdate) {
                log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeSheet delete, id : {}", chargeSheet.getId());
                chargeSheet.setIsDeleted(1);
            } else {
                //new and no items and no bind return null
                chargeSheet = null;
            }
        }

        return new MedicalPlanSheetConvertChargeSheetRsp()
                .setUpdate(isUpdate)
                .setUnchargedChargeSheet(chargeSheet);
    }

    private void insertOrUpdateChargeSheetForMedicalPlan(ChargeSheet chargeSheet, MedicalPlanSheet medicalPlanSheet, MedicalPlan medicalPlan, String operatorId) {
        if (medicalPlanSheet == null) {
            return;
        }

        ChargeUtils.insertChargeSheetAdditionalIfNeed(chargeSheet, operatorId);
        ChargeSheetFactory.insertOrUpdateChargeFormsForMedicalPlanForm(chargeSheet, medicalPlanSheet.getMedicalPlanForms(), operatorId);
        ChargeSheetFactory.insertOrUpdateChargeSheetAdditionalForMedicalPlan(chargeSheet, medicalPlan, operatorId);
    }

    public void insertOrUpdateRegistrationChargeSheet(List<ChargeSheet> existedChargeSheetList,
                                                      OutpatientSheet outpatientSheet,
                                                      OutpatientSheetDetailCalculateReq.RegistrationInfoReq registrationInfoReq,
                                                      String operatorId) {

        if (registrationInfoReq == null) {
            return;
        }

        ChargeSheet chargeSheet = ChargeUtils.pickRegistrationChargeSheet(existedChargeSheetList);
        if (chargeSheet == null) {
            chargeSheet = new ChargeSheet();
            chargeSheet.setId(AbcIdUtils.getUUID());
            chargeSheet.setPatientOrderId(outpatientSheet.getPatientOrderId());
            chargeSheet.setChainId(outpatientSheet.getChainId());
            chargeSheet.setClinicId(outpatientSheet.getClinicId());
            chargeSheet.setPatientId(outpatientSheet.getPatientId());
            if (!StringUtils.isEmpty(outpatientSheet.getDoctorId())) {
                chargeSheet.setDoctorId(outpatientSheet.getDoctorId());
            }
            chargeSheet.setStatus(Constants.ChargeSheetStatus.UNCHARGED);
            chargeSheet.setType(ChargeSheet.Type.REGISTRATION);
            chargeSheet.setOrderByDate(Instant.now());
            FillUtils.fillCreatedBy(chargeSheet, operatorId);

            ChargeForm chargeForm = ChargeFormFactory.generateRegistrationForm(chargeSheet, null, operatorId);
            chargeForm.setChargeFormItems(new ArrayList<>());
            chargeForm.getChargeFormItems().add(ChargeFormItemFactory.generateRegistrationFormItem(chargeForm, registrationInfoReq, operatorId));
            chargeSheet.setChargeForms(Arrays.asList(chargeForm));
            existedChargeSheetList.add(chargeSheet);
        } else {

            if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED) {
                return;
            }

            Optional.ofNullable(chargeSheet.getChargeForms())
                    .orElse(new ArrayList<>())
                    .stream()
                    .filter(chargeForm -> chargeForm.getIsDeleted() == 0 && chargeForm.getSourceFormType() == Constants.SourceFormType.REGISTRATION)
                    .findFirst()
                    .ifPresent(chargeForm -> ChargeFormItemFactory.updateRegistrationFormItemByRegistrationInfoReq(chargeForm, registrationInfoReq, operatorId));
        }
    }

    public OutpatientSheetConvertChargeSheetRsp insertOrUpdateOutpatientChargeSheetCore(List<ChargeSheet> existedChargeSheetList, OutpatientSheet outpatientSheet,
                                                                                        int isMember,
                                                                                        String airPharmacyOrderId,
                                                                                        boolean checkSourceVersion,
                                                                                        String operatorId) {

        ChargeSheet registrationChargeSheet = ChargeUtils.pickRegistrationChargeSheet(existedChargeSheetList);

        //移除合作药房的处方信息
        Optional.ofNullable(outpatientSheet)
                .ifPresent(o -> {
                    if (CollectionUtils.isNotEmpty(o.getPrescriptionForms())) {
                        o.getPrescriptionForms().removeIf(prescriptionForm -> prescriptionForm.getPharmacyType() == GoodsConst.PharmacyType.CO_PHARMACY);
                    }
                });


        Pair<Boolean, ChargeSheet> chargeSheetPair = OutpatientSheetUtils.createOrFindChargeSheet(existedChargeSheetList,
                outpatientSheet,
                isMember,
                registrationChargeSheet,
                cisScClinicService,
                operatorId);
        ChargeSheet chargeSheet = chargeSheetPair.getRight();
        boolean isUpdate = chargeSheetPair.getLeft();


        String preUpdateKey = ChargeUtils.signForOutpatient(chargeSheet);

        if (checkSourceVersion && isUpdate && chargeSheet.getSourceDataVersion() != null && chargeSheet.getSourceDataVersion() > outpatientSheet.getVersion()) {
            log.info("门诊单的版本号小于或等于收费单存储的来源版本号，不做任何处理");
            return new OutpatientSheetConvertChargeSheetRsp()
                    .setUpdate(false)
                    .setUnchargedChargeSheet(null)
                    .setRegistrationChargeSheet(registrationChargeSheet);
        }

        //查询organ的hisType
        Organ organ = cisScClinicService.getOrgan(chargeSheet.getClinicId());

        CalculateModel calculateModel = CalculateModel.translateCalculateModel(organ.getHisType());

        //根据门诊单新增或修改收费单
        insertOrUpdateChargeSheetForOutpatient(chargeSheet, outpatientSheet, calculateModel, airPharmacyOrderId, operatorId);

        String afterUpdateKey = ChargeUtils.signForOutpatient(chargeSheet);
        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "preUpdateKey: {}, afterUpdateKey: {}", preUpdateKey, afterUpdateKey);

        int oldOutpatientStatus = chargeSheet.getOutpatientStatus();

        if (outpatientSheet != null) {
            int outpatientStatus = ChargeSheet.OutpatientStatus.WAITING;
            switch (outpatientSheet.getStatus()) {
                case OutpatientSheet.Status.DIAGNOSED:
                    outpatientStatus = ChargeSheet.OutpatientStatus.DIAGNOSED;
                    break;
                case OutpatientSheet.Status.WAITING:
                    outpatientStatus = ChargeSheet.OutpatientStatus.WAITING;
                    break;
            }
            chargeSheet.setIsOnline(outpatientSheet.getIsOnline());
            chargeSheet.setOutpatientStatus(outpatientStatus);
            chargeSheet.setReserveDate(outpatientSheet.getReserveDate());

            if (outpatientStatus == ChargeSheet.OutpatientStatus.DIAGNOSED) {
                chargeSheet.setDiagnosedDate(outpatientSheet.getDiagnosedDate());
                if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED) {
                    if (isUpdate && !Objects.equals(preUpdateKey, afterUpdateKey)) {
                        chargeSheet.setOrderByDate(Instant.now());
                    }
                }

                if (chargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED
                        && Objects.nonNull(outpatientSheet.getDiagnosedDate())
                        && ObjectUtils.compare(outpatientSheet.getDiagnosedDate(), chargeSheet.getOrderByDate()) > 0) {
                    chargeSheet.setOrderByDate(outpatientSheet.getDiagnosedDate());
                }
            }
        } else if (isChargeSheetHasValidRegistration(chargeSheet)) {
            chargeSheet.setOutpatientStatus(ChargeSheet.OutpatientStatus.WAITING);
        }

        //比较本次门诊修改的消息是否对收费单真正有影响
        if (isUpdate && !Objects.equals(preUpdateKey, afterUpdateKey)) {
            chargeSheet.setIsClosed(0);
        }

        boolean isDecoction = chargeSheet.getChargeForms()
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .anyMatch(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.PROCESS);
        chargeSheet.setIsDecoction(isDecoction ? 1 : chargeSheet.getIsDecoction());

        long chargeItemCount = chargeSheet.getChargeForms()
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getChargeFormItems() != null)
                .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                .filter(Objects::nonNull)
                .filter(chargeFormItem -> chargeFormItem.getIsDeleted() == 0)
                .count();

        int hasTypeEyeClass = chargeSheet.getChargeForms()
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_TYPE_EYE_GLASS)
                .findFirst().map(o -> 1).orElse(0);


        if (chargeItemCount == 0 && hasTypeEyeClass == 0) {

            BigDecimal registrationFee = Optional.ofNullable(registrationChargeSheet).map(ChargeSheet::getChargeForms)
                    .orElse(new ArrayList<>())
                    .stream()
                    .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                    .filter(registrationForm -> registrationForm.getChargeFormItems() != null)
                    .flatMap(registrationForm -> registrationForm.getChargeFormItems().stream())
                    .filter(item -> item.getIsDeleted() == 0)
                    .findFirst()
                    .map(ChargeFormItem::getUnitPrice)
                    .orElse(BigDecimal.ZERO);

            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeItemCount == 0, sheetId:{}, bindRegistrationSheetId:{}, isUpdate:{}, registrationFee: {}", chargeSheet.getId(), chargeSheet.getRegistrationChargeSheetId(), isUpdate, registrationFee);
            //分情况
            if (!TextUtils.isEmpty(chargeSheet.getRegistrationChargeSheetId())
                    && registrationChargeSheet != null
                    && ((registrationChargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED && MathUtils.wrapBigDecimalCompare(registrationFee, BigDecimal.ZERO) > 0)
                    || (!isUpdate && MathUtils.wrapBigDecimalCompare(registrationFee, BigDecimal.ZERO) > 0) && outpatientSheet.getStatus() != OutpatientSheet.Status.DIAGNOSED)
            ) {
                //保留
            } else if (isUpdate) {
                if (outpatientSheet.getStatus() == OutpatientSheet.Status.DIAGNOSED) {
                    log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeSheet delete, id : {}", chargeSheet.getId());
                    chargeSheet.setIsDeleted(1);
                }
            } else {
                //new and no items and no bind return null
                chargeSheet = null;
            }
        }

        return new OutpatientSheetConvertChargeSheetRsp()
                .setUpdate(isUpdate)
                .setUnchargedChargeSheet(chargeSheet)
                .setOldOutpatientStatus(oldOutpatientStatus)
                .setRegistrationChargeSheet(registrationChargeSheet);
    }


    private static boolean isChargeSheetHasValidRegistration(ChargeSheet chargeSheet) {
        if (chargeSheet == null || chargeSheet.getChargeForms() == null) {
            return false;
        }
        long registrationCount = chargeSheet.getChargeForms()
                .stream()
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getSourceFormType() == Constants.SourceFormType.REGISTRATION)
                .filter(chargeForm -> chargeForm.getChargeFormItems() != null)
                .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                .filter(chargeFormItem -> chargeFormItem.getIsDeleted() == 0)
                .filter(chargeFormItem -> chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED ||
                        chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.UNCHARGED)
                .count();
        return registrationCount > 0;
    }

    /**
     * 把门诊单里面的信息初始化到收费单里面，不要被函数意思迷惑，其实就是把chargeSheet这个对象初始化好，用outpationSheet的参数值
     *
     * @param chargeSheet 本次需要生成的收费单
     *                    outpatientSheet 病人门诊单
     *                    operatorId    操作人
     */
    public void insertOrUpdateChargeSheetForOutpatient(ChargeSheet chargeSheet, OutpatientSheet outpatientSheet, CalculateModel calculateModel, String airPharmacyOrderId, String operatorId) {
        if (outpatientSheet == null) {
            return;
        }

        /**
         * 门店单预处理
         */
        OutpatientSheetUtils.preHandleOutpatientSheet(outpatientSheet);

        ChargeUtils.insertChargeSheetAdditionalIfNeed(chargeSheet, operatorId);
        chargeSheet.setDoctorId(outpatientSheet.getDoctorId());
        chargeSheet.setCopywriterId(outpatientSheet.getCopywriterId());
        chargeSheet.setSourceDataVersion(outpatientSheet.getVersion());
        //记录代录来源
        ChargeSheetFactory.updateCopyWriteSource(chargeSheet, outpatientSheet.getCopywriterId(), ChargeSheetAdditional.CopyWriteSource.OUTPATIENT);

        ChargeSheetFactory.insertOrUpdateChargeFormsForProductForm(chargeSheet, outpatientSheet.getProductForms(), calculateModel, operatorId); //商品治疗理疗之类的，其他商品
        ChargeSheetFactory.insertOrUpdateChargeFormsForPrescription(chargeSheet, outpatientSheet.getPrescriptionForms(), calculateModel, outpatientSheet.getMedicalRecord(), outpatientSheet.getDoctorId(), outpatientSheet.getDepartmentId(), operatorId);//处方
        ChargeSheetFactory.insertOrUpdateChargeSheetAdditionalForOutpatient(chargeSheet, outpatientSheet, airPharmacyOrderId, operatorId);//填充门诊信息
        ChargeSheetFactory.insertOrUpdateProcessChargeForm(chargeSheet, sheetProcessorService, outpatientSheet.getPrescriptionForms(), airPharmacyOrderId, operatorId); //处理本地药房加工费
//        initAirPharmacyDeliveryFees(chargeSheet, operatorId);
        ChargeUtils.updateConcatMobileForOutpatient(chargeSheet, operatorId);
    }

}
