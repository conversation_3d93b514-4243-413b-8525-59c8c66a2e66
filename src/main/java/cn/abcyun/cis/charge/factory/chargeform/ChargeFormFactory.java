package cn.abcyun.cis.charge.factory.chargeform;

import cn.abcyun.bis.rpc.sdk.cis.model.charge.GoodsFeeType;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.medicalplan.MedicalPlanForm;
import cn.abcyun.bis.rpc.sdk.cis.model.medicalplan.MedicalPlanFormItem;
import cn.abcyun.cis.charge.api.model.ChargeDeliveryReq;
import cn.abcyun.cis.charge.api.model.ChargeFormReq;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.factory.ChargeFormItemBatchInfoFactory;
import cn.abcyun.cis.charge.factory.ChargeFormItemFactory;
import cn.abcyun.cis.charge.factory.chargeform.chineseprescription.AirChinesePrescriptionFactory;
import cn.abcyun.cis.charge.factory.chargeform.chineseprescription.LocalChinesePrescriptionFactory;
import cn.abcyun.cis.charge.factory.chargeform.chineseprescription.VirtualChinesePrescriptionFactory;
import cn.abcyun.cis.charge.factory.logistics.LogisticsFactory;
import cn.abcyun.cis.charge.model.*;
import cn.abcyun.cis.charge.processor.calculate.CalculateModel;
import cn.abcyun.cis.charge.service.dto.ChargeFormItemTargetDto;
import cn.abcyun.cis.charge.service.dto.OutpatientProductFormItemSourceDto;
import cn.abcyun.cis.charge.util.*;
import cn.abcyun.cis.commons.model.ComposeType;
import cn.abcyun.cis.commons.rpc.outpatient.*;
import cn.abcyun.cis.commons.rpc.registration.RegistrationForm;
import cn.abcyun.cis.commons.rpc.registration.RegistrationFormItem;
import cn.abcyun.cis.commons.util.ItemParentIdConvertTool;
import cn.abcyun.cis.commons.util.ItemParentIdSourceCell;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.common.log.marker.AbcLogMarker;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 工厂方法，构造chargeForm
 * 第一期目标：1.把chargeFrom的构造的代码从DTOConverter类里面移动过来
 * 2.chargeForm有太多的历史逻辑，暂时通过Factory的继承关系来隔离普通chargeForm和空中药房chargeForm的构造
 */
@Slf4j
public class ChargeFormFactory {
    //只是用类关系的多台，把各种chargeform的构造代码封装到不同的ChargeFormFactory里面
    private final static ChargeFormFactoryAirPharmacy CHARGE_FORM_FACTORY_AIR_PHARMACY = new ChargeFormFactoryAirPharmacy();
    private final static ChargeFormFactoryVirtualPharmacy CHARGE_FORM_FACTORY_VIRTUAL_PHARMACY = new ChargeFormFactoryVirtualPharmacy();
    private final static ChargeFormFactoryBase CHARGE_FORM_FACTORY_BASE = new ChargeFormFactoryBase();
    private final static ChargeFormFactoryProcess CHARGE_FORM_FACTORY_PROCESS = new ChargeFormFactoryProcess();
    private final static ChargeFormFactoryBase REGISTRATION_FORM_FACTORY = new ChargeFormFactoryRegistration();

    private final static ChargeFormFactoryBase ONLINE_CONSULTATION_FORM_FACTORY = new ChargeFormFactoryOnlineConsultation();

    public static ChargeFormFactoryBase chooseChargeFormFactory(ChargeFormReq chargeFormReq) {
        if (chargeFormReq.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY) {
            return CHARGE_FORM_FACTORY_AIR_PHARMACY;
        } else if (chargeFormReq.getSourceFormType() == Constants.SourceFormType.PROCESS) {
            return CHARGE_FORM_FACTORY_PROCESS;
        } else if (chargeFormReq.getPharmacyType() == GoodsConst.PharmacyType.VIRTUAL_PHARMACY) {
            return CHARGE_FORM_FACTORY_VIRTUAL_PHARMACY;
        } else if (chargeFormReq.getSourceFormType() == Constants.SourceFormType.REGISTRATION) {
            return REGISTRATION_FORM_FACTORY;
        } else if (chargeFormReq.getSourceFormType() == Constants.SourceFormType.ONLINE_CONSULTATION) {
            return ONLINE_CONSULTATION_FORM_FACTORY;
        }
        return CHARGE_FORM_FACTORY_BASE;
    }

    /**
     * 更新chargeForm，原来和现在只要一个是AIR_PHARMACY都要特殊处理
     * 三种情况：
     * 1.1、门诊是本地药房
     * 1.1.1 收费处是本地药房
     * 1.1.2 收费处是虚拟药房
     * 1.1.3 收费处是空中药房
     * 1.2、门诊是虚拟药房
     * 1.2.1 收费处是本地药房
     * 1.2.2 收费处是虚拟药房
     * 1.2.3 收费处是空中药房
     * 1.3、门诊是空中药房
     * 1.3.1 收费处是本地药房
     * 1.3.2 收费处是虚拟药房
     * 1.3.3 收费处是空中药房
     *
     * @return 更新的内容会设置到输入参数chargeForm 上
     */

    private static final Map<Integer, ChargeFormFactoryBase> CHINESE_PRESCRIPTION_FORM_FACTORY_MAP = new HashMap<Integer, ChargeFormFactoryBase>() {{
        put(GoodsConst.PharmacyType.LOCAL_PHARMACY, new LocalChinesePrescriptionFactory());
        put(GoodsConst.PharmacyType.AIR_PHARMACY, new AirChinesePrescriptionFactory());
        put(GoodsConst.PharmacyType.VIRTUAL_PHARMACY, new VirtualChinesePrescriptionFactory());
    }};

    /**
     * 把端上要求算费的算费单转化成后台的算费数据结构ChargeForm
     *
     * @param chargeSheetId 收费单id
     *                      patientOrderId 病人门诊订单号
     *                      clinicId，chainId 诊所id
     *                      chargeFormReq 终端请求要算费的chargeFormReq
     *                      generateIdIfNotExisted，forceGenerateNewId 指定是否为chargeFrom，chargeFormItem等生成新的id
     * @return ChargeFrom ChargeForm是后台算费的依据，算费逻辑封装到对应FormProcessor和FormItemProcessor里面
     */
    public static ChargeForm createChargeFormFromCalculate(String chargeSheetId,
                                                           String patientOrderId,
                                                           String clinicId,
                                                           String chainId,
                                                           ChargeFormReq chargeFormReq,
                                                           boolean isCanUpdateUsageInfo,
                                                           boolean generateIdIfNotExisted,
                                                           boolean forceGenerateNewId,
                                                           String operatorId) {
        return chooseChargeFormFactory(chargeFormReq).createChargeFormFromCalculate(chargeSheetId, patientOrderId, clinicId, chainId, chargeFormReq, isCanUpdateUsageInfo, generateIdIfNotExisted, forceGenerateNewId, operatorId);
    }

    /**
     * 从门诊过来的chargeForm的构造[费]
     */
    public static ChargeForm createChargeFormFromPatientPrescription(String chargeSheetId, String patientOrderId, String clinicId, String chainId,
                                                                     PrescriptionForm prescriptionForm, CalculateModel calculateModel, MedicalRecord outpatientMedicalRecord,
                                                                     String doctorId,
                                                                     String doctorDepartmentId,
                                                                     String operatorId) {
        if (prescriptionForm.getType() == PrescriptionForm.TYPE_CHINESE
                && prescriptionForm.getPharmacyType() == PrescriptionForm.PharmacyType.AIR) {
            return CHARGE_FORM_FACTORY_AIR_PHARMACY.createChargeFormFromPatientPrescription(chargeSheetId, patientOrderId, clinicId, chainId,
                    prescriptionForm, calculateModel, outpatientMedicalRecord, doctorId, doctorDepartmentId,
                    operatorId);
        } else if (prescriptionForm.getType() == PrescriptionForm.TYPE_CHINESE
                && prescriptionForm.getPharmacyType() == PrescriptionForm.PharmacyType.VIRTUAL) {
            return CHARGE_FORM_FACTORY_VIRTUAL_PHARMACY.createChargeFormFromPatientPrescription(chargeSheetId, patientOrderId, clinicId, chainId,
                    prescriptionForm, calculateModel, outpatientMedicalRecord, doctorId, doctorDepartmentId,
                    operatorId);
        } else {
            return CHARGE_FORM_FACTORY_BASE.createChargeFormFromPatientPrescription(chargeSheetId, patientOrderId, clinicId, chainId,
                    prescriptionForm, calculateModel, outpatientMedicalRecord, doctorId, doctorDepartmentId,
                    operatorId);
        }
    }

    /**
     * 根据门诊处方更新chargeForm
     * 两种情况：
     * 1、中药处方
     * 1.1、门诊是本地药房
     * 1.1.1 收费处是本地药房
     * 1.1.2 收费处是虚拟药房
     * 1.1.3 收费处是空中药房
     * 1.2、门诊是虚拟药房
     * 1.2.1 收费处是本地药房
     * 1.2.2 收费处是虚拟药房
     * 1.2.3 收费处是空中药房
     * 1.3、门诊是空中药房
     * 1.3.1 收费处是本地药房
     * 1.3.2 收费处是虚拟药房
     * 1.3.3 收费处是空中药房
     * 2、非中药处方
     * 走baseUpdate逻辑
     *
     * @return 更新的内容会设置到输入参数chargeForm 上
     */
    public static void updateChargeFormFromPatientPrescription(ChargeForm chargeForm,
                                                               PrescriptionForm prescriptionForm,
                                                               CalculateModel calculateModel,
                                                               MedicalRecord outpatientMedicalRecord,
                                                               String doctorId, String doctorDepartmentId, String operatorId) {
        ChargeFormFactoryBase chargeFormFactoryBase = CHARGE_FORM_FACTORY_BASE;
        if (prescriptionForm.getType() == PrescriptionForm.TYPE_CHINESE) {
            chargeFormFactoryBase = CHINESE_PRESCRIPTION_FORM_FACTORY_MAP.getOrDefault(prescriptionForm.getPharmacyType(), CHARGE_FORM_FACTORY_BASE);
        }
        chargeFormFactoryBase.updateChargeFormFromPatientPrescription(chargeForm, prescriptionForm, calculateModel, outpatientMedicalRecord, doctorId, doctorDepartmentId, operatorId);
    }

    public static void initChargeFormDeliveryInfo(ChargeForm chargeForm) {
        if (chargeForm.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY) {
            CHARGE_FORM_FACTORY_AIR_PHARMACY.bindProductInfoForAirPharmacyExpressDelivery(chargeForm);
        } else if (chargeForm.getPharmacyType() == GoodsConst.PharmacyType.VIRTUAL_PHARMACY) {
            CHARGE_FORM_FACTORY_VIRTUAL_PHARMACY.bindProductInfoForAirPharmacyExpressDelivery(chargeForm);
        } else {
            CHARGE_FORM_FACTORY_BASE.bindProductInfoForAirPharmacyExpressDelivery(chargeForm);
        }
    }


    public static ChargeForm generateRegistrationForm(ChargeSheet chargeSheet, String sourceFormId, String operatorId) {
        ChargeForm chargeForm = new ChargeForm();
        chargeForm.setId(AbcIdUtils.getUUID());
        chargeForm.setChargeSheetId(chargeSheet.getId());
        chargeForm.setPatientOrderId(chargeSheet.getPatientOrderId());
        chargeForm.setSourceFormId(sourceFormId);
        chargeForm.setSourceFormType(Constants.SourceFormType.REGISTRATION);
        chargeForm.setStatus(Constants.ChargeFormStatus.UNCHARGED);
        chargeForm.setClinicId(chargeSheet.getClinicId());
        chargeForm.setChainId(chargeSheet.getChainId());

        FillUtils.fillCreatedBy(chargeForm, operatorId);
        return chargeForm;
    }

    public static ChargeForm insertRegistrationForm(ChargeSheet chargeSheet, RegistrationForm registrationForm, String operatorId) {
        if (registrationForm == null || CollectionUtils.isEmpty(registrationForm.getRegistrationFormItems())) {
            return null;
        }
        ChargeForm chargeForm = generateRegistrationForm(chargeSheet, registrationForm.getId(), operatorId);

        List<ChargeFormItem> chargeFormItems = new ArrayList<>();
        registrationForm.getRegistrationFormItems().forEach(registrationFormItem -> {
            ChargeFormItem formItem = ChargeFormItemFactory.insertRegistrationFormItem(chargeForm, registrationFormItem, chargeFormItems, operatorId);
            if (Objects.nonNull(formItem)) {
                chargeFormItems.add(formItem);
            }
        });
        chargeForm.setChargeFormItems(chargeFormItems);

        return chargeForm;
    }

    public static ChargeForm insertOnlineConsultationForm(ChargeSheet chargeSheet, String doctorId, String doctorName, BigDecimal fee, String operatorId) {
        ChargeForm chargeForm = new ChargeForm();
        chargeForm.setId(AbcIdUtils.getUUID());
        chargeForm.setChargeSheetId(chargeSheet.getId());
        chargeForm.setPatientOrderId(chargeSheet.getPatientOrderId());
        chargeForm.setSourceFormId("");
        chargeForm.setSourceFormType(Constants.SourceFormType.ONLINE_CONSULTATION);
        chargeForm.setStatus(Constants.ChargeFormStatus.UNCHARGED);
        chargeForm.setClinicId(chargeSheet.getClinicId());
        chargeForm.setChainId(chargeSheet.getChainId());

        FillUtils.fillCreatedBy(chargeForm, operatorId);

        List<ChargeFormItem> chargeFormItems = new ArrayList<>();
        chargeFormItems.add(ChargeFormItemFactory.insertOnlineConsultationFormItem(chargeForm, doctorId, doctorName, fee, operatorId));
        chargeForm.setChargeFormItems(chargeFormItems);

        return chargeForm;
    }

    public static ChargeForm insertFamilyDoctorSignForm(ChargeSheet chargeSheet, BigDecimal fee, String servicePackageName, JsonNode familyDoctorInfo, String operatorId) {
        ChargeForm chargeForm = new ChargeForm();
        chargeForm.setId(AbcIdUtils.getUUID());
        chargeForm.setChargeSheetId(chargeSheet.getId());
        chargeForm.setPatientOrderId(chargeSheet.getPatientOrderId());
        chargeForm.setSourceFormId("");
        chargeForm.setSourceFormType(Constants.SourceFormType.FAMILY_DOCTOR_SIGN);
        chargeForm.setStatus(Constants.ChargeFormStatus.UNCHARGED);
        chargeForm.setClinicId(chargeSheet.getClinicId());
        chargeForm.setChainId(chargeSheet.getChainId());

        FillUtils.fillCreatedBy(chargeForm, operatorId);

        List<ChargeFormItem> chargeFormItems = new ArrayList<>();
        chargeFormItems.add(ChargeFormItemFactory.insertFamilyDoctorSignFormItem(chargeForm, fee, servicePackageName, familyDoctorInfo, operatorId));
        chargeForm.setChargeFormItems(chargeFormItems);

        return chargeForm;
    }

    public static ChargeForm insertPromotionCardOpenForm(ChargeSheet chargeSheet, BigDecimal fee, String operatorId) {
        ChargeForm chargeForm = new ChargeForm();
        chargeForm.setId(AbcIdUtils.getUUID());
        chargeForm.setChargeSheetId(chargeSheet.getId());
        chargeForm.setPatientOrderId(chargeSheet.getPatientOrderId());
        chargeForm.setSourceFormId("");
        chargeForm.setSourceFormType(Constants.SourceFormType.PROMOTION_CARD_OPEN);
        chargeForm.setStatus(Constants.ChargeFormStatus.UNCHARGED);
        chargeForm.setClinicId(chargeSheet.getClinicId());
        chargeForm.setChainId(chargeSheet.getChainId());

        FillUtils.fillCreatedBy(chargeForm, operatorId);

        List<ChargeFormItem> chargeFormItems = new ArrayList<>();
        chargeFormItems.add(ChargeFormItemFactory.insertPromotionCardOpenFormItem(chargeForm, fee, operatorId));
        chargeForm.setChargeFormItems(chargeFormItems);

        return chargeForm;
    }

    public static ChargeForm insertMemberCardRechargeForm(ChargeSheet chargeSheet, BigDecimal fee, String operatorId) {
        ChargeForm chargeForm = new ChargeForm();
        chargeForm.setId(AbcIdUtils.getUUID());
        chargeForm.setChargeSheetId(chargeSheet.getId());
        chargeForm.setPatientOrderId(chargeSheet.getPatientOrderId());
        chargeForm.setSourceFormId("");
        chargeForm.setSourceFormType(Constants.SourceFormType.MEMBER_CARD_RECHARGE);
        chargeForm.setStatus(Constants.ChargeFormStatus.UNCHARGED);
        chargeForm.setClinicId(chargeSheet.getClinicId());
        chargeForm.setChainId(chargeSheet.getChainId());

        FillUtils.fillCreatedBy(chargeForm, operatorId);

        List<ChargeFormItem> chargeFormItems = new ArrayList<>();
        chargeFormItems.add(ChargeFormItemFactory.insertMemberCardRechargeFormItem(chargeForm, fee, operatorId));
        chargeForm.setChargeFormItems(chargeFormItems);

        return chargeForm;
    }

    public static ChargeForm insertPromotionCardRechargeForm(ChargeSheet chargeSheet, BigDecimal fee, String operatorId) {
        ChargeForm chargeForm = new ChargeForm();
        chargeForm.setId(AbcIdUtils.getUUID());
        chargeForm.setChargeSheetId(chargeSheet.getId());
        chargeForm.setPatientOrderId(chargeSheet.getPatientOrderId());
        chargeForm.setSourceFormId("");
        chargeForm.setSourceFormType(Constants.SourceFormType.PROMOTION_CARD_RECHARGE);
        chargeForm.setStatus(Constants.ChargeFormStatus.UNCHARGED);
        chargeForm.setClinicId(chargeSheet.getClinicId());
        chargeForm.setChainId(chargeSheet.getChainId());

        FillUtils.fillCreatedBy(chargeForm, operatorId);

        List<ChargeFormItem> chargeFormItems = new ArrayList<>();
        chargeFormItems.add(ChargeFormItemFactory.insertPromotionCardRechargeFormItem(chargeForm, fee, operatorId));
        chargeForm.setChargeFormItems(chargeFormItems);

        return chargeForm;
    }

    public static ChargeForm insertExpressDeliveryForm(ChargeSheet chargeSheet, ChargeDeliveryReq deliveryInfo, String operatorId) {
        ChargeForm chargeForm = new ChargeForm();
        chargeForm.setId(AbcIdUtils.getUUID());
        chargeForm.setKeyId(AbcIdUtils.getUUID());
        chargeForm.setChargeSheetId(chargeSheet.getId());
        chargeForm.setPatientOrderId(chargeSheet.getPatientOrderId());
        chargeForm.setSourceFormId("");
        chargeForm.setSourceFormType(Constants.SourceFormType.EXPRESS_DELIVERY);
        chargeForm.setStatus(Constants.ChargeFormStatus.UNCHARGED);
        chargeForm.setClinicId(chargeSheet.getClinicId());
        chargeForm.setChainId(chargeSheet.getChainId());
        FillUtils.fillCreatedBy(chargeForm, operatorId);

        List<ChargeFormItem> chargeFormItems = new ArrayList<>();
        chargeFormItems.add(ChargeFormItemFactory.insertExpressDeliveryFormItem(chargeForm, deliveryInfo, operatorId));
        chargeForm.setChargeFormItems(chargeFormItems);

        return chargeForm;
    }

    public static ChargeForm createProcessForm(ChargeSheet chargeSheet, ChargeSheetProcessInfo processInfo, String operatorId) {

        ChargeForm chargeForm = new ChargeForm();
        chargeForm.setId(AbcIdUtils.getUUID());
        chargeForm.setChargeSheetId(chargeSheet.getId());
        chargeForm.setPatientOrderId(chargeSheet.getPatientOrderId());
        chargeForm.setSourceFormId("");
        chargeForm.setSourceFormType(Constants.SourceFormType.PROCESS);
        chargeForm.setStatus(Constants.ChargeFormStatus.UNCHARGED);
        chargeForm.setClinicId(chargeSheet.getClinicId());
        chargeForm.setChainId(chargeSheet.getChainId());
        chargeForm.setProcessInfo(generateChargeSheetProcessInfo(processInfo, chargeForm.getId(), operatorId));

        FillUtils.fillCreatedBy(chargeForm, operatorId);

        List<ChargeFormItem> chargeFormItems = new ArrayList<>();
        chargeFormItems.add(ChargeFormItemFactory.insertProcessFormItem(chargeForm, operatorId));
        chargeForm.setChargeFormItems(chargeFormItems);

        return chargeForm;
    }

    private static ChargeSheetProcessInfo generateChargeSheetProcessInfo(ChargeSheetProcessInfo processInfo, String processFormId, String operatorId) {
        if (processInfo == null) {
            return null;
        }
        ChargeSheetProcessInfo saveProcessInfo = new ChargeSheetProcessInfo();
        BeanUtils.copyProperties(processInfo, saveProcessInfo);
        processInfo.setProcessFormId(processFormId);
        FillUtils.fillCreatedBy(processInfo, operatorId);
        return processInfo;
    }

    public static ChargeForm updateRegistrationForm(ChargeSheet chargeSheet, ChargeForm chargeForm, RegistrationForm registrationForm, String operatorId) {
        if (chargeForm.getChargeFormItems() == null) {
            chargeForm.setChargeFormItems(new ArrayList<>());
        }

        if (registrationForm.getRegistrationFormItems() == null) {
            registrationForm.setRegistrationFormItems(new ArrayList<>());
        }

        List<ChargeFormItem> formItems = chargeForm.getChargeFormItems().stream().filter(chargeFormItem -> chargeFormItem.getIsDeleted() == 0).filter(item -> item.getGoodsFeeType() == GoodsFeeType.FEE_PARENT || item.getGoodsFeeType() == GoodsFeeType.FEE_OWN).collect(Collectors.toList());
        List<ChargeFormItem> childrenFormItems = chargeForm.getChargeFormItems().stream().filter(chargeFormItem -> chargeFormItem.getIsDeleted() == 0).filter(item -> item.getGoodsFeeType() == GoodsFeeType.FEE_CHILD).collect(Collectors.toList());
        Map<String, List<ChargeFormItem>> childMap = ListUtils.groupByKey(childrenFormItems, ChargeFormItem::getComposeParentFormItemId);

        List<ChargeFormItem> resultChildFormItems = new ArrayList<>();
        BiFunction<RegistrationFormItem, ChargeFormItem, Boolean> isEqualKeyFunc = (RegistrationFormItem registrationFormItem, ChargeFormItem chargeFormItem) -> TextUtils.equals(chargeFormItem.getSourceFormItemId(), registrationFormItem.getId());
        Function<RegistrationFormItem, ChargeFormItem> insertFunc = registrationFormItem -> ChargeFormItemFactory.insertRegistrationFormItem(chargeForm, registrationFormItem, resultChildFormItems, operatorId);

        Function<ChargeFormItem, Boolean> deleteFunc = chargeFormItem -> ChargeFormItemFactory.deleteFeeParentChargeFormItem(chargeFormItem, chargeForm.getChargeFormItems(), operatorId);

        BiConsumer<RegistrationFormItem, ChargeFormItem> updateFunc = (registrationFormItem, chargeFormItem) -> {
            if (registrationFormItem.getStatus() != null && registrationFormItem.getStatus() == 2) { //退号情况下删除
                ChargeFormItemFactory.deleteFeeParentChargeFormItem(chargeFormItem, chargeForm.getChargeFormItems(), operatorId);
            } else {
                List<ChargeFormItem> chargeFormItemList = childMap.get(chargeFormItem.getId());

                if (CollectionUtils.isEmpty(chargeFormItemList)) {
                    chargeFormItemList = new ArrayList<>();
                }

                ChargeFormItemFactory.updateRegistrationFormItem(chargeForm, chargeFormItem, registrationFormItem, chargeFormItemList, operatorId);
                resultChildFormItems.addAll(chargeFormItemList);
            }
        };

        MergeTool<RegistrationFormItem, ChargeFormItem> mergeTool = new MergeTool<>();
        mergeTool.setSrc(registrationForm.getRegistrationFormItems())
                .setDst(formItems)
                .setIsEqualKeyFunc(isEqualKeyFunc)
                .setInsertFunc(insertFunc)
                .setDeleteFunc(deleteFunc)
                .setUpdateFunc(updateFunc);
        mergeTool.doMerge();
        formItems.addAll(resultChildFormItems);
        chargeForm.setChargeFormItems(formItems);

        return chargeForm;
    }


    /**
     * 端上请求的参数里面的医疗记录去更新服务端的chargeForm的记录
     * 如果服务端没有，应该是新增
     */
    private static void updateChargeAirPharmacyMedicalRecordForCharge(ChargeForm svrChargeForm, ChargeAirPharmacyMedicalRecord updateMedicalRecord, String operatorId) {


        if (svrChargeForm == null || updateMedicalRecord == null) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "【基本不可能进来】updateChargeAirPharmacyMedicalRecordForCharge Before{},${}", JsonUtils.dump(svrChargeForm), JsonUtils.dump(updateMedicalRecord));
            return;
        }
        ChargeAirPharmacyMedicalRecord medicalRecord = svrChargeForm.getChargeAirPharmacyMedicalRecord();
        if (medicalRecord != null) { //更新
            medicalRecord.setComment(updateMedicalRecord.getComment());
            FillUtils.fillLastModifiedBy(medicalRecord, operatorId);
        } else { //新增
            medicalRecord = new ChargeAirPharmacyMedicalRecord();
            BeanUtils.copyProperties(updateMedicalRecord, medicalRecord);
            FillUtils.fillLastModifiedBy(medicalRecord, operatorId);
            medicalRecord.setIsNeedInsert(1);//要新增
            svrChargeForm.setChargeAirPharmacyMedicalRecord(medicalRecord);
        }
    }

    public static ChargeForm insertAdditionalForm(ChargeSheet chargeSheet, ChargeForm additionalChargeForm, boolean isCanUpdateUsageInfo, String operatorId) {
        if (additionalChargeForm == null || (CollectionUtils.isEmpty(additionalChargeForm.getChargeFormItems()) && additionalChargeForm.getSourceFormType() != Constants.SourceFormType.PRESCRIPTION_TYPE_EYE_GLASS)) {
            return null;
        }

        ChargeForm chargeForm = new ChargeForm();
        chargeForm.setId(AbcIdUtils.getUUID());
        chargeForm.setClinicId(chargeSheet.getClinicId());
        chargeForm.setChainId(chargeSheet.getChainId());
        chargeForm.setChargeSheetId(chargeSheet.getId());
        chargeForm.setPatientOrderId(chargeSheet.getPatientOrderId());
        chargeForm.setSourceFormType(additionalChargeForm.getSourceFormType());
        chargeForm.setStatus(Constants.ChargeFormStatus.UNCHARGED);
        chargeForm.setSort(additionalChargeForm.getSort());
        chargeForm.setKeyId(additionalChargeForm.getKeyId());
        chargeForm.setPharmacyNo(additionalChargeForm.getPharmacyNo());
        chargeForm.setPharmacyType(additionalChargeForm.getPharmacyType());

        if ((isCanUpdateUsageInfo || (chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_TYPE_EYE_GLASS && StringUtils.isEmpty(chargeForm.getSourceFormId())))
                && additionalChargeForm.getUsageInfo() != null) {
            chargeForm.setUsageInfo(JsonUtils.dumpAsJsonNode(additionalChargeForm.getUsageInfo()));
            chargeForm.setUsageInfoJson(JsonUtils.dump(additionalChargeForm.getUsageInfo()));
        }

        if (chargeForm.getSourceFormType() == Constants.SourceFormType.PROCESS) {
            chargeForm.setProcessInfo(insertChargeSheetProcessInfo(chargeForm, additionalChargeForm.getProcessInfo(), operatorId));
        }

        FillUtils.fillCreatedBy(chargeForm, operatorId);

        // 增加空中药房的快递加工信息
        insertAirAdditionalFormImpl(chargeForm, chargeSheet, additionalChargeForm);

        // 增加chargeFormItem
        List<ChargeFormItem> chargeFormItems = new ArrayList<>();
        additionalChargeForm.getChargeFormItems().forEach(additionalChargeFormItem -> {
            ChargeFormItem formItem = ChargeFormItemFactory.insertChargeFormItem(chargeForm, additionalChargeFormItem, operatorId);
            chargeFormItems.add(formItem);
        });
        chargeForm.setChargeFormItems(chargeFormItems);
        return chargeForm;
    }

    public static ChargeSheetProcessInfo insertChargeSheetProcessInfo(ChargeForm chargeForm, ChargeSheetProcessInfo processInfoReq, String operatorId) {

        if (processInfoReq == null) {
            return null;
        }
        ChargeSheetProcessInfo processInfo = new ChargeSheetProcessInfo();
        processInfo.setId(AbcIdUtils.getUID())
                .setKeyId(processInfoReq.getKeyId())
                .setChainId(chargeForm.getChainId())
                .setClinicId(chargeForm.getClinicId())
                .setChargeSheetId(chargeForm.getChargeSheetId())
                .setChargeFormId(processInfoReq.getChargeFormId())
                .setProcessFormId(chargeForm.getId())
                .setType(processInfoReq.getType())
                .setSubType(processInfoReq.getSubType())
                .setBagUnitCount(processInfoReq.getBagUnitCount())
                .setChecked(processInfoReq.getChecked())
                .setBagUnitCountDecimal(processInfoReq.getBagUnitCountDecimal())
                .setTotalProcessCount(processInfoReq.getTotalProcessCount())
                .setProcessRemark(processInfoReq.getProcessRemark())
                .setTakeMedicationTime(processInfoReq.getTakeMedicationTime());

        FillUtils.fillCreatedBy(processInfo, operatorId);

        return processInfo;
    }

    /**
     * 为空中药房chargeForm增加快递和治疗信息
     */
    public static ChargeForm insertAirAdditionalFormImpl(ChargeForm chargeForm, ChargeSheet chargeSheet, ChargeForm additionalChargeForm) {
        if (chargeForm.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY) {
            chargeForm.setSpecification(additionalChargeForm.getSpecification());
            ChargeAirPharmacyLogistics chargeAirPharmacyLogistics = additionalChargeForm.getChargeAirPharmacyLogistics();
            if (chargeAirPharmacyLogistics != null) {
                chargeAirPharmacyLogistics
                        .setChargeFormId(chargeForm.getId())
                        .setChargeSheetId(chargeSheet.getId())
                        .setChainId(chargeForm.getChainId())
                        .setClinicId(chargeForm.getClinicId());
                chargeAirPharmacyLogistics.setIsNeedInsert(1);//新的chargeForm上的要设置为可插入
                //TODO robinsli 浅拷贝，BeanUtils.copy也是浅拷贝。拷贝复杂/可变成员变量有出问题风险
                chargeForm.setChargeAirPharmacyLogistics(chargeAirPharmacyLogistics);
            }
            ChargeAirPharmacyMedicalRecord medicalRecord = additionalChargeForm.getChargeAirPharmacyMedicalRecord();
            if (medicalRecord != null) {
                medicalRecord.setChargeFormId(chargeForm.getId())
                        .setChargeSheetId(chargeSheet.getId())
                        .setChainId(chargeForm.getChainId())
                        .setClinicId(chargeForm.getClinicId());
                medicalRecord.setIsNeedInsert(1);//新的chargeForm上的要设置为可插入
                //TODO robinsli 浅拷贝，BeanUtils.copy也是浅拷贝。拷贝复杂/可变成员变量有出问题风险
                chargeForm.setChargeAirPharmacyMedicalRecord(medicalRecord);
            }

            chargeForm.setVendorId(additionalChargeForm.getVendorId());
            chargeForm.setVendorName(additionalChargeForm.getVendorName());
            chargeForm.setMedicineStateScopeId(additionalChargeForm.getMedicineStateScopeId());
            chargeForm.setUsageScopeId(additionalChargeForm.getUsageScopeId());
            chargeForm.setExpectedPriceFlag(additionalChargeForm.getExpectedPriceFlag());

            chargeForm.setProcessRule(additionalChargeForm.getProcessRule());
            chargeForm.setDeliveryRule(additionalChargeForm.getDeliveryRule());

            ChargeFormFactory.initChargeFormDeliveryInfo(chargeForm);
        }
        return chargeForm;
    }

    /**
     * 函数抽离
     */
    public static void doUpdateAirAdditionalFormImpl(ChargeForm svrAdditionalChargeForm, ChargeForm clientUpdatedAdditionalChargeForm, boolean isCanUpdateChiefComplaint, String operatorId) {
        LogisticsFactory.airPharmacyDeliveryInfoUpdate(svrAdditionalChargeForm, clientUpdatedAdditionalChargeForm.getChargeAirPharmacyLogistics(), operatorId, false);
        updateChargeAirPharmacyMedicalRecordForCharge(svrAdditionalChargeForm, clientUpdatedAdditionalChargeForm.getChargeAirPharmacyMedicalRecord(), operatorId);

        if (isCanUpdateChiefComplaint) {
            svrAdditionalChargeForm.setSpecification(clientUpdatedAdditionalChargeForm.getSpecification());
            svrAdditionalChargeForm.setVendorId(clientUpdatedAdditionalChargeForm.getVendorId());
            svrAdditionalChargeForm.setVendorName(clientUpdatedAdditionalChargeForm.getVendorName());
            svrAdditionalChargeForm.setMedicineStateScopeId(clientUpdatedAdditionalChargeForm.getMedicineStateScopeId());
            svrAdditionalChargeForm.setUsageScopeId(clientUpdatedAdditionalChargeForm.getUsageScopeId());
        }
        svrAdditionalChargeForm.setDeliveryRule(clientUpdatedAdditionalChargeForm.getDeliveryRule());
        svrAdditionalChargeForm.setProcessRule(clientUpdatedAdditionalChargeForm.getProcessRule());
        ChargeFormFactory.initChargeFormDeliveryInfo(svrAdditionalChargeForm);
    }

    //updatedAdditionalChargeForm端上上来的， additionalChargeForm本地服务器需要更新的

    /**
     * 调用关系updateAdditionalForm->insertOrUpdateChargeFormsForAdditionalForm---|calculate*（要谨慎修改）
     * |convertChargeSheet  ----(最外围ChargeAirPharmacyMedicalRecordService.saveNew会把isNeedInsert的过滤掉，Update通过JPA更新后自动插入)
     */
    public static ChargeForm updateAdditionalForm(int chargeSheetType, ChargeForm svrAdditionalChargeForm, ChargeForm clientUpdatedAdditionalChargeForm, boolean removeUnselected, boolean isCanUpdateChiefComplaint, boolean isCanUpdateUsageInfo, String operatorId) {
        if (svrAdditionalChargeForm == null || clientUpdatedAdditionalChargeForm == null) {
            return null;
        }
        svrAdditionalChargeForm.setSort(clientUpdatedAdditionalChargeForm.getSort());
        svrAdditionalChargeForm.setKeyId(clientUpdatedAdditionalChargeForm.getKeyId());
        FillUtils.fillLastModifiedBy(svrAdditionalChargeForm, operatorId);
        svrAdditionalChargeForm.setPharmacyNo(clientUpdatedAdditionalChargeForm.getPharmacyNo());
        svrAdditionalChargeForm.setPharmacyType(clientUpdatedAdditionalChargeForm.getPharmacyType());

        if ((isCanUpdateUsageInfo || (clientUpdatedAdditionalChargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_TYPE_EYE_GLASS && StringUtils.isEmpty(svrAdditionalChargeForm.getSourceFormId()))) && clientUpdatedAdditionalChargeForm.getUsageInfo() != null) {
            if (org.apache.commons.lang3.StringUtils.isEmpty(svrAdditionalChargeForm.getUsageInfoJson())) {
                svrAdditionalChargeForm.setUsageInfo(clientUpdatedAdditionalChargeForm.getUsageInfo());
                svrAdditionalChargeForm.setUsageInfoJson(clientUpdatedAdditionalChargeForm.getUsageInfoJson());
            } else {
                UsageInfo usageInfo = JsonUtils.readValue(svrAdditionalChargeForm.getUsageInfoJson(), UsageInfo.class);

                UsageInfo usageInfoReq = JsonUtils.readValue(clientUpdatedAdditionalChargeForm.getUsageInfo(), UsageInfo.class);
                usageInfo.setUsage(usageInfoReq.getUsage());
                usageInfo.setDailyDosage(usageInfoReq.getDailyDosage());
                usageInfo.setFreq(usageInfoReq.getFreq());
                usageInfo.setUsageLevel(usageInfoReq.getUsageLevel());
                usageInfo.setRequirement(usageInfoReq.getRequirement());
                usageInfo.setSpecialRequirement(usageInfoReq.getSpecialRequirement());
                usageInfo.setTotalProcessCount(usageInfoReq.getTotalProcessCount());
                usageInfo.setProcessRemark(usageInfoReq.getProcessRemark());
                usageInfo.setProcessBagUnitCountDecimal(usageInfoReq.getProcessBagUnitCountDecimal());
                usageInfo.setProcessBagUnitCount(usageInfoReq.getProcessBagUnitCount());
                usageInfo.setGlassesParams(usageInfoReq.getGlassesParams());
                usageInfo.setGlassesType(usageInfoReq.getGlassesType());
                usageInfo.setOptometristId(usageInfoReq.getOptometristId());

                svrAdditionalChargeForm.setUsageInfo(JsonUtils.dumpAsJsonNode(usageInfo));
                svrAdditionalChargeForm.setUsageInfoJson(JsonUtils.dump(usageInfo));
            }
        } else if (org.apache.commons.lang3.StringUtils.isNotEmpty(svrAdditionalChargeForm.getUsageInfoJson()) && clientUpdatedAdditionalChargeForm.getUsageInfo() != null) {
            UsageInfo usageInfoReq = JsonUtils.readValue(clientUpdatedAdditionalChargeForm.getUsageInfo(), UsageInfo.class);
            UsageInfo usageInfo = JsonUtils.readValue(svrAdditionalChargeForm.getUsageInfoJson(), UsageInfo.class);
            usageInfo.setRequirement(Optional.ofNullable(clientUpdatedAdditionalChargeForm.getUsageInfo().get("requirement")).map(JsonNode::asText).orElse(""));
            usageInfo.setProcessBagUnitCountDecimal(usageInfoReq.getProcessBagUnitCountDecimal());
            usageInfo.setProcessRemark(usageInfoReq.getProcessRemark());
            usageInfo.setTotalProcessCount(usageInfoReq.getTotalProcessCount());
            usageInfo.setProcessBagUnitCount(usageInfoReq.getProcessBagUnitCount());
            usageInfo.setGlassesParams(usageInfoReq.getGlassesParams());
            usageInfo.setGlassesType(usageInfoReq.getGlassesType());
            usageInfo.setOptometristId(usageInfoReq.getOptometristId());
            svrAdditionalChargeForm.setUsageInfo(JsonUtils.dumpAsJsonNode(usageInfo));
            svrAdditionalChargeForm.setUsageInfoJson(JsonUtils.dump(usageInfo));
        }

        if (svrAdditionalChargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE || svrAdditionalChargeForm.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY) {
            svrAdditionalChargeForm.setSourceFormType(clientUpdatedAdditionalChargeForm.getSourceFormType());
        }

        if (svrAdditionalChargeForm.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY) {
            doUpdateAirAdditionalFormImpl(svrAdditionalChargeForm, clientUpdatedAdditionalChargeForm, isCanUpdateChiefComplaint, operatorId);
        } else if (svrAdditionalChargeForm.getSourceFormType() == Constants.SourceFormType.PROCESS) {
            updateChargeSheetProcessInfo(svrAdditionalChargeForm, clientUpdatedAdditionalChargeForm, operatorId);
        } else if (svrAdditionalChargeForm.getPharmacyType() == GoodsConst.PharmacyType.VIRTUAL_PHARMACY && svrAdditionalChargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE) {
            doUpdateVirtualPharmacyFormImpl(svrAdditionalChargeForm, clientUpdatedAdditionalChargeForm, isCanUpdateChiefComplaint, operatorId);
        }

        BiFunction<ChargeFormItem, ChargeFormItem, Boolean> isEqualKeyFunc = (a, b) -> TextUtils.equals(a.getId(), b.getId());
        Function<ChargeFormItem, ChargeFormItem> insertFunc = toInsertChargeFormItem -> ChargeFormItemFactory.insertChargeFormItem(svrAdditionalChargeForm, toInsertChargeFormItem, operatorId);

        Function<ChargeFormItem, Boolean> deleteFunc = chargeFormItem -> {
            if (removeUnselected) {
                ChargeFormItemFactory.deleteChargeFormItem(chargeFormItem, operatorId);
            }
            return false;
        };

        BiConsumer<ChargeFormItem, ChargeFormItem> updateFunc = (updatedChargeFormItem, chargeFormItem) -> {

            if (updatedChargeFormItem.getIsAirPharmacy() == 1) {
                chargeFormItem.setProductId(updatedChargeFormItem.getProductId());
                chargeFormItem.setName(updatedChargeFormItem.getName());
                chargeFormItem.setIsAirPharmacy(updatedChargeFormItem.getIsAirPharmacy());
            }

            chargeFormItem.setKeyId(updatedChargeFormItem.getKeyId());
            chargeFormItem.setUnitCount(MathUtils.wrapBigDecimalOrZero(updatedChargeFormItem.getUnitCount()));
            chargeFormItem.setDoseCount(MathUtils.wrapBigDecimal(updatedChargeFormItem.getDoseCount(), BigDecimal.ONE));
            chargeFormItem.setUseDismounting(updatedChargeFormItem.getUseDismounting());
            chargeFormItem.setUnit(updatedChargeFormItem.getUnit());
            chargeFormItem.setSort(updatedChargeFormItem.getSort());
            chargeFormItem.setPharmacyType(updatedChargeFormItem.getPharmacyType());
            chargeFormItem.setPharmacyNo(updatedChargeFormItem.getPharmacyNo());
            chargeFormItem.setIsExpectedBatch(updatedChargeFormItem.getIsExpectedBatch());
            chargeFormItem.setIsFixedData(updatedChargeFormItem.getIsFixedData());
            chargeFormItem.setIsGift(updatedChargeFormItem.getIsGift());

            if (chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.UNSELECTED) {
                chargeFormItem.setStatus(Constants.ChargeFormItemStatus.UNCHARGED);
            }
            if (svrAdditionalChargeForm.getSourceFormType() == Constants.SourceFormType.GIFT_PRODUCT) {
                chargeFormItem.setSourceFormItemId(updatedChargeFormItem.getSourceFormItemId());
            }
            if (updatedChargeFormItem.needUpdateBatch()) {
                if (chargeFormItem.getChargeFormItemBatchInfos() == null) {
                    chargeFormItem.setChargeFormItemBatchInfos(new ArrayList<>());
                }
                ChargeFormItemBatchInfoFactory.insertOrUpdateChargeFormItemBatchInfo(chargeFormItem.getChargeFormItemBatchInfos(), updatedChargeFormItem.getChargeFormItemBatchInfos(), operatorId);
            }
            ChargeFormItemFactory.insertChargeFormItemAdditionalIfNeed(chargeFormItem);
            updateChargeFormItemAdditional(chargeFormItem.getAdditional(), updatedChargeFormItem.getAdditional());

            ChargeFormItemAdditional additionalReq = updatedChargeFormItem.getAdditional();
            // 门诊开的收费项不支持修改医生护士信息
            if (!(chargeSheetType == ChargeSheet.Type.OUTPATIENT && org.apache.commons.lang3.StringUtils.isNotBlank(chargeFormItem.getSourceFormItemId()))) {
                ChargeFormItemAdditional additional = chargeFormItem.getAdditional();
                if(additionalReq != null){
                    additional.setDoctorId(additional.getDoctorId());
                    additional.setNurseId(additional.getNurseId());
                    additional.setDepartmentId(additional.getDepartmentId());
                    additional.setToothNos(additional.getToothNos());
                    additional.setTraceableCodeList( additional.getTraceableCodeList());
                }
                else{
                    additional.setDoctorId(null);
                    additional.setNurseId(null);
                    additional.setDepartmentId(null);
                    additional.setToothNos(null);
                    additional.setTraceableCodeList(null);
                }
            }

            if (updatedChargeFormItem.getUsageInfo() != null) {
                if (StringUtils.isEmpty(chargeFormItem.getUsageInfoJson())) {
                    chargeFormItem.setUsageInfoJson(updatedChargeFormItem.getUsageInfoJson());
                    chargeFormItem.setUsageInfo(updatedChargeFormItem.getUsageInfo());
                } else {
                    UsageInfo usageInfo = JsonUtils.readValue(chargeFormItem.getUsageInfoJson(), UsageInfo.class);
                    UsageInfo usageInfoReq = JsonUtils.readValue(updatedChargeFormItem.getUsageInfo(), UsageInfo.class);

                    if (chargeFormItem.getProductType() == Constants.ProductType.MEDICINE) {
                        usageInfo.setSpecialRequirement(usageInfoReq.getSpecialRequirement());
                    }

                    chargeFormItem.setUsageInfo(JsonUtils.dumpAsJsonNode(usageInfo));
                    chargeFormItem.setUsageInfoJson(JsonUtils.dump(usageInfo));
                }
            } else {
                if (!StringUtils.isEmpty(chargeFormItem.getUsageInfoJson())) {
                    UsageInfo usageInfo = JsonUtils.readValue(chargeFormItem.getUsageInfoJson(), UsageInfo.class);

                    if (chargeFormItem.getProductType() == Constants.ProductType.MEDICINE
                            && chargeFormItem.getProductSubType() == Constants.ProductType.SubType.MEDICINE_CHINESE) {
                        usageInfo.setSpecialRequirement("");
                    }

                    chargeFormItem.setUsageInfo(JsonUtils.dumpAsJsonNode(usageInfo));
                    chargeFormItem.setUsageInfoJson(JsonUtils.dump(usageInfo));
                }
            }

        };

        MergeTool<ChargeFormItem, ChargeFormItem> mergeTool = new MergeTool<>();
        mergeTool.setSrc(clientUpdatedAdditionalChargeForm.getChargeFormItems())
                .setDst(svrAdditionalChargeForm.getChargeFormItems())
                .setIsEqualKeyFunc(isEqualKeyFunc)
                .setInsertFunc(insertFunc)
                .setDeleteFunc(deleteFunc)
                .setUpdateFunc(updateFunc);
        mergeTool.doMerge();

        // 确保子项item的status与母项item的status保持一致
        Map<String, ChargeFormItem> parentItemMap = ListUtils.toMap(svrAdditionalChargeForm.getChargeFormItems(), ChargeFormItem::getId);

        /**
         * 这里要跑两次  第一次先处理套餐子项  第二次处理套餐子项的子项
         */
        svrAdditionalChargeForm.getChargeFormItems().stream()
                .filter(item -> !StringUtils.isEmpty(item.getComposeParentFormItemId()))
                .forEach(childItem -> {
                    ChargeFormItem parentItem = parentItemMap.get(childItem.getComposeParentFormItemId());
                    if (parentItem != null) {
                        childItem.setStatus(parentItem.getStatus());
                    }
                });

        svrAdditionalChargeForm.getChargeFormItems().stream()
                .filter(item -> !StringUtils.isEmpty(item.getComposeParentFormItemId()))
                .forEach(childItem -> {
                    ChargeFormItem parentItem = parentItemMap.get(childItem.getComposeParentFormItemId());
                    if (parentItem != null) {
                        childItem.setStatus(parentItem.getStatus());
                    }
                });

        return svrAdditionalChargeForm;
    }

    private static void updateChargeFormItemAdditional(ChargeFormItemAdditional additional, ChargeFormItemAdditional additionalReq) {
        if (Objects.isNull(additional) || Objects.isNull(additionalReq)) {
            return;
        }
        additional.setRemark(additionalReq.getRemark())
                .setUnitAdjustmentFeeLastModifiedBy(additionalReq.getUnitAdjustmentFeeLastModifiedBy())
                .setSinglePromotions(additionalReq.getSinglePromotions())
                .setTraceableCodeList(additionalReq.getTraceableCodeList())
                .setGiftGoodsPromotionId(additionalReq.getGiftGoodsPromotionId());
    }

    private static void doUpdateVirtualPharmacyFormImpl(ChargeForm svrAdditionalChargeForm, ChargeForm clientUpdatedAdditionalChargeForm, boolean isCanUpdateChiefComplaint, String operatorId) {
        LogisticsFactory.airPharmacyDeliveryInfoUpdate(svrAdditionalChargeForm, clientUpdatedAdditionalChargeForm.getChargeAirPharmacyLogistics(), operatorId, false);
        if (isCanUpdateChiefComplaint) {
            svrAdditionalChargeForm.setVendorId(clientUpdatedAdditionalChargeForm.getVendorId());
            svrAdditionalChargeForm.setVendorName(clientUpdatedAdditionalChargeForm.getVendorName());
            svrAdditionalChargeForm.setSpecification(clientUpdatedAdditionalChargeForm.getSpecification());
            svrAdditionalChargeForm.setMedicineStateScopeId(clientUpdatedAdditionalChargeForm.getMedicineStateScopeId());
            svrAdditionalChargeForm.setUsageScopeId(clientUpdatedAdditionalChargeForm.getUsageScopeId());
        }
        svrAdditionalChargeForm.setDeliveryRule(clientUpdatedAdditionalChargeForm.getDeliveryRule());
        ChargeFormFactory.initChargeFormDeliveryInfo(svrAdditionalChargeForm);
    }

    private static void updateChargeSheetProcessInfo(ChargeForm svrAdditionalChargeForm, ChargeForm clientUpdatedAdditionalChargeForm, String operatorId) {
        if (svrAdditionalChargeForm == null || clientUpdatedAdditionalChargeForm == null) {
            return;
        }
        ChargeSheetProcessInfo processInfoReq = clientUpdatedAdditionalChargeForm.getProcessInfo();
        ChargeSheetProcessInfo processInfo = svrAdditionalChargeForm.getProcessInfo();
        if (processInfoReq == null) {
            svrAdditionalChargeForm.setProcessInfo(null);
            return;
        }
        if (processInfo != null) {
            processInfo.setType(processInfoReq.getType())
                    .setSubType(processInfoReq.getSubType())
                    .setChecked(processInfoReq.getChecked())
                    .setBagUnitCount(processInfoReq.getBagUnitCount())
                    .setBagUnitCountDecimal(processInfoReq.getBagUnitCountDecimal())
                    .setProcessFormId(svrAdditionalChargeForm.getId())
                    .setChargeFormId(processInfoReq.getChargeFormId())
                    .setProcessFee(processInfoReq.getProcessFee())
                    .setTotalProcessCount(processInfoReq.getTotalProcessCount())
                    .setProcessRemark(processInfoReq.getProcessRemark())
                    .setTakeMedicationTime(processInfoReq.getTakeMedicationTime());
            FillUtils.fillLastModifiedBy(processInfo, operatorId);
            return;
        }
        svrAdditionalChargeForm.setProcessInfo(insertChargeSheetProcessInfo(svrAdditionalChargeForm, processInfoReq, operatorId));
    }

    public static boolean deleteChargeForm(ChargeForm chargeForm, String operatorId) {
        if (chargeForm.getStatus() == Constants.ChargeFormStatus.UNCHARGED) {
            chargeForm.deleteModel(operatorId);
        }
        return false;
    }


    /**
     * 门诊的productForm  转化成 ChargeForm （chargeSheet 只是传入参数）
     *
     * @param chargeSheet 生成的chargeSheet
     *                    productForm  输入参数
     */
    public static ChargeForm insertProductForm(ChargeSheet chargeSheet, ProductForm productForm, CalculateModel calculateModel, String operatorId) {
        if (productForm == null || CollectionUtils.isEmpty(productForm.getProductFormItems())) {
            return null;
        }
        // new a chargeform
        ChargeForm chargeForm = new ChargeForm();
        chargeForm.setId(AbcIdUtils.getUUID());
        chargeForm.setClinicId(chargeSheet.getClinicId());
        chargeForm.setChainId(chargeSheet.getChainId());
        chargeForm.setChargeSheetId(chargeSheet.getId());
        chargeForm.setPatientOrderId(chargeSheet.getPatientOrderId());
        chargeForm.setSourceFormId(productForm.getId());
        chargeForm.setSourceFormType(productForm.getSourceFormType());
        chargeForm.setStatus(Constants.ChargeFormStatus.UNCHARGED);
        chargeForm.setSort(productForm.getSort());

        FillUtils.fillCreatedBy(chargeForm, operatorId);
        //new all chargeFormItems
        List<ChargeFormItem> chargeFormItems = new ArrayList<>();
        productForm.getProductFormItems()
                .stream().filter(productFormItem -> !StringUtils.isEmpty(productFormItem.getProductId()))
                .forEach(productFormItem -> {
                    chargeFormItems.add(ChargeFormItemFactory.insertProductFormItem(chargeForm, productFormItem, calculateModel, operatorId));
                });

        correctChargeFormItemsFromProductFormItem(chargeFormItems, productForm.getProductFormItems());
        chargeForm.setChargeFormItems(chargeFormItems);
        return chargeForm;
    }

    public static ChargeForm insertMedicalPlanForm(ChargeSheet chargeSheet, MedicalPlanForm medicalPlanForm, String operatorId) {
        if (medicalPlanForm == null || CollectionUtils.isEmpty(medicalPlanForm.getMedicalPlanFormItems())) {
            return null;
        }
        // new a chargeform
        ChargeForm chargeForm = new ChargeForm();
        chargeForm.setId(AbcIdUtils.getUUID());
        chargeForm.setClinicId(chargeSheet.getClinicId());
        chargeForm.setChainId(chargeSheet.getChainId());
        chargeForm.setChargeSheetId(chargeSheet.getId());
        chargeForm.setPatientOrderId(chargeSheet.getPatientOrderId());

        if (org.apache.commons.lang3.StringUtils.isEmpty(medicalPlanForm.getSourceFormId())) {
            chargeForm.setSourceFormId(medicalPlanForm.getId());
        }
        chargeForm.setSourceFormType(medicalPlanForm.getSourceFormType());
        chargeForm.setStatus(Constants.ChargeFormStatus.UNCHARGED);
        chargeForm.setSort(medicalPlanForm.getSort());

        FillUtils.fillCreatedBy(chargeForm, operatorId);
        //new all chargeFormItems
        List<ChargeFormItem> chargeFormItems = new ArrayList<>();
        medicalPlanForm.getMedicalPlanFormItems()
                .stream()
                .filter(medicalPlanFormItem -> !StringUtils.isEmpty(medicalPlanFormItem.getProductId()))
                .forEach(medicalPlanFormItem -> {
                    chargeFormItems.add(ChargeFormItemFactory.insertMedicalPlanFormItem(chargeForm, medicalPlanFormItem, operatorId));
                });

        correctChargeFormItemsFromMedicalPlanFormItem(chargeFormItems, medicalPlanForm.getMedicalPlanFormItems());
        chargeForm.setChargeFormItems(chargeFormItems);
        return chargeForm;
    }

    public static ChargeForm updateProductForm(ChargeForm chargeForm, ProductForm productForm, CalculateModel calculateModel, String operatorId) {
        if (chargeForm.getChargeFormItems() == null) {
            chargeForm.setChargeFormItems(new ArrayList<>());
        }
        if (productForm.getProductFormItems() == null) {
            productForm.setProductFormItems(new ArrayList<>());
        }
        chargeForm.setSort(productForm.getSort());

        BiFunction<ProductFormItem, ChargeFormItem, Boolean> isEqualKeyFunc = (productFormItem, chargeFormItem) -> TextUtils.equals(chargeFormItem.getSourceFormItemId(), productFormItem.getId());

        Function<ProductFormItem, ChargeFormItem> insertFunc = productFormItem -> ChargeFormItemFactory.insertProductFormItem(chargeForm, productFormItem, calculateModel, operatorId);

        Function<ChargeFormItem, Boolean> deleteFunc = chargeFormItem -> ChargeFormItemFactory.deleteChargeFormItem(chargeFormItem, operatorId);

        BiConsumer<ProductFormItem, ChargeFormItem> updateFunc = (productFormItem, chargeFormItem) -> ChargeFormItemFactory.updateProductFormItem(chargeFormItem, productFormItem, calculateModel, operatorId);

        List<ChargeFormItem> chargeFormItems = chargeForm.getChargeFormItems();
        MergeTool.doMerge(productForm.getProductFormItems(), chargeFormItems, isEqualKeyFunc, insertFunc, deleteFunc, updateFunc);
        correctChargeFormItemsFromProductFormItem(chargeFormItems, productForm.getProductFormItems());
        return chargeForm;
    }

    private static void correctChargeFormItemsFromProductFormItem(List<ChargeFormItem> chargeFormItems, List<ProductFormItem> productFormItems) {
        correctChargeFormItemsFromMedicalPlanFormItemCore(chargeFormItems, productFormItems
                .stream()
                .map(OutpatientProductFormItemSourceDto::new)
                .collect(Collectors.toList()));
    }

    public static ChargeForm updateMedicalPlanForm(ChargeForm chargeForm, MedicalPlanForm medicalPlanForm, String operatorId) {
        if (chargeForm.getChargeFormItems() == null) {
            chargeForm.setChargeFormItems(new ArrayList<>());
        }

        if (medicalPlanForm.getMedicalPlanFormItems() == null) {
            medicalPlanForm.setMedicalPlanFormItems(new ArrayList<>());
        }
        chargeForm.setSort(medicalPlanForm.getSort());

        BiFunction<MedicalPlanFormItem, ChargeFormItem, Boolean> isEqualKeyFunc = (medicalPlanFormItem, chargeFormItem) ->
                TextUtils.equals(chargeFormItem.getSourceFormItemId(), medicalPlanFormItem.getId())
                        || TextUtils.equals(chargeForm.getId(), medicalPlanForm.getSourceFormId());

        Function<MedicalPlanFormItem, ChargeFormItem> insertFunc = medicalPlanFormItem -> ChargeFormItemFactory.insertMedicalPlanFormItem(chargeForm, medicalPlanFormItem, operatorId);

        Function<ChargeFormItem, Boolean> deleteFunc = chargeFormItem -> ChargeFormItemFactory.deleteChargeFormItem(chargeFormItem, operatorId);

        BiConsumer<MedicalPlanFormItem, ChargeFormItem> updateFunc = (medicalPlanFormItem, chargeFormItem) -> ChargeFormItemFactory.updateMedicalPlanFormItem(chargeFormItem, medicalPlanFormItem, operatorId);

        List<ChargeFormItem> chargeFormItems = chargeForm.getChargeFormItems();

        MergeTool.doMerge(medicalPlanForm.getMedicalPlanFormItems(), chargeFormItems, isEqualKeyFunc, insertFunc, deleteFunc, updateFunc);

        correctChargeFormItemsFromMedicalPlanFormItem(chargeFormItems, medicalPlanForm.getMedicalPlanFormItems());

        return chargeForm;
    }

    /**
     * 修正chargeFormItem
     *
     * @param chargeFormItems
     * @param medicalPlanFormItems
     */
    private static void correctChargeFormItemsFromMedicalPlanFormItem(List<ChargeFormItem> chargeFormItems, List<MedicalPlanFormItem> medicalPlanFormItems) {

        List<ChargeFormItem> toUpdateChargeFormItems = ChargeFormItemUtils.filterIsDeletedItems(chargeFormItems);

        correctChargeFormItemsFromMedicalPlanFormItemCore(toUpdateChargeFormItems, medicalPlanFormItems
                .stream()
                .map(medicalPlanFormItem -> new ItemParentIdSourceCell() {
                    @Override
                    public String getParentSourceItemId() {
                        return medicalPlanFormItem.getComposeParentFormItemId();
                    }

                    @Override
                    public String getSourceItemId() {
                        return medicalPlanFormItem.getId();
                    }

                    @Override
                    public boolean isChildItem() {
                        return !TextUtils.isEmpty(medicalPlanFormItem.getComposeParentFormItemId());
                    }
                })
                .collect(Collectors.toList()));
    }

    private static void correctChargeFormItemsFromChargeCooperationOrderItem(List<ChargeFormItem> chargeFormItems, List<ChargeCooperationOrderItem> chargeCooperationOrderItems) {
        correctChargeFormItemsFromMedicalPlanFormItemCore(chargeFormItems, chargeCooperationOrderItems
                .stream()
                .map(medicalPlanFormItem -> new ItemParentIdSourceCell() {
                    @Override
                    public String getParentSourceItemId() {
                        return medicalPlanFormItem.getComposeParentFormItemId();
                    }

                    @Override
                    public String getSourceItemId() {
                        return medicalPlanFormItem.getId();
                    }

                    @Override
                    public boolean isChildItem() {
                        return !TextUtils.isEmpty(medicalPlanFormItem.getComposeParentFormItemId());
                    }
                })
                .collect(Collectors.toList()));
    }


    /**
     * 修正chargeFormItem
     *
     * @param chargeFormItems
     * @param itemParentIdSourceCells
     */
    private static void correctChargeFormItemsFromMedicalPlanFormItemCore(List<ChargeFormItem> chargeFormItems, List<ItemParentIdSourceCell> itemParentIdSourceCells) {

        List<ChargeFormItem> toUpdateChargeFormItems = ChargeFormItemUtils.filterIsDeletedItems(chargeFormItems);

        //绑定收费项的父子关系
        ItemParentIdConvertTool.convert(itemParentIdSourceCells,
                toUpdateChargeFormItems.stream()
                        .map(ChargeFormItemTargetDto::new)
                        .collect(Collectors.toList())
        );
        ChargeFormItemFactory.updateChargeFormItemComposeTypeAndGoodsFeeType(toUpdateChargeFormItems);
        //处理套餐子项的价格问题
        ChargeFormItemFactory.updateComposeItemPrice(toUpdateChargeFormItems);
    }

    public static ChargeForm cloneChargeForm(ChargeForm sourceChargeForm, ChargeSheet chargeSheet, String operatorId) {
        ChargeForm chargeForm = new ChargeForm();
        BeanUtils.copyProperties(sourceChargeForm, chargeForm, "chargeFormItems");
        chargeForm.setId(AbcIdUtils.getUUID());
        chargeForm.setStatus(Constants.ChargeFormStatus.UNCHARGED);
        chargeForm.setChainId(chargeSheet.getChainId());
        chargeForm.setClinicId(chargeSheet.getClinicId());
        chargeForm.setChargeSheetId(chargeSheet.getId());
        chargeForm.setPatientOrderId(chargeSheet.getPatientOrderId());
        chargeForm.setSourceFormId(null);
        chargeForm.setExpectedTotalPrice(null);
        chargeForm.setExpectedPriceFlag(0);
        chargeForm.setDiscountPrice(BigDecimal.ZERO);
        chargeForm.setExpectedTotalPrice(null);
        chargeForm.setAdjustmentPrice(BigDecimal.ZERO);
        chargeForm.setPromotionPrice(BigDecimal.ZERO);
        chargeForm.setRefundDiscountPrice(BigDecimal.ZERO);
        chargeForm.setRefundTotalPrice(BigDecimal.ZERO);
        chargeForm.setRefundUnitCount(BigDecimal.ZERO);
        chargeForm.setTotalCostPrice(BigDecimal.ZERO);
        chargeForm.setDispensingStatus(0);
        chargeForm.setPromotionInfoJson(null);
        chargeForm.setCouponPromotionInfoJson(null);
        chargeForm.setGiftRulePromotionInfoJson(null);
        chargeForm.setIsCanBeRefund(0);
        chargeForm.setChargeAirPharmacyLogistics(null);
        chargeForm.setChargeAirPharmacyMedicalRecord(null);
        chargeForm.setReceivedPrice(BigDecimal.ZERO);

        FillUtils.fillCreatedBy(chargeForm, operatorId);

        if (sourceChargeForm.getChargeFormItems() != null) {
            List<ChargeFormItem> newChargeFormItems = sourceChargeForm.getChargeFormItems()
                    .stream()
                    .filter(chargeFormItem -> chargeFormItem.getIsGift() == Constants.ChargeFormItemGiftType.NOT_GIFT)
                    .filter(chargeFormItem -> chargeFormItem.getComposeType() != ComposeType.COMPOSE_SUB_ITEM)
                    //需要把已退单的也续上
                    .filter(chargeFormItem -> chargeFormItem.getV2Status() == Constants.ChargeFormItemStatus.CHARGED || chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.UNSELECTED)
                    .map(chargeFormItem -> ChargeFormItemFactory.cloneChargeFormItem(chargeForm, chargeFormItem, operatorId))
                    .collect(Collectors.toList());

            chargeForm.setChargeFormItems(newChargeFormItems);
        }

        if (chargeForm.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY) {
            // 诊断信息
            chargeForm.setChargeAirPharmacyMedicalRecord(cloneChargeAirPharmacyMedicalRecord(chargeForm, sourceChargeForm.getChargeAirPharmacyMedicalRecord(), operatorId));
            ChargeFormFactory.initChargeFormDeliveryInfo(chargeForm);
            if (!CollectionUtils.isEmpty(chargeForm.getChargeFormItems())) {
                chargeForm.setTotalPrice(chargeForm.getChargeFormItems().stream().map(ChargeFormItem::getTotalPrice).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            } else {
                chargeForm.setTotalPrice(BigDecimal.ZERO);
            }
        }
        return chargeForm;
    }

    private static ChargeAirPharmacyMedicalRecord cloneChargeAirPharmacyMedicalRecord(ChargeForm chargeForm, ChargeAirPharmacyMedicalRecord sourceMedicalRecord, String operatorId) {
        if (chargeForm == null || sourceMedicalRecord == null) {
            return null;
        }
        ChargeAirPharmacyMedicalRecord medicalRecord = new ChargeAirPharmacyMedicalRecord();
        BeanUtils.copyProperties(sourceMedicalRecord, medicalRecord);
        medicalRecord.setId(AbcIdUtils.getUID());
        medicalRecord.setClinicId(chargeForm.getClinicId());
        medicalRecord.setChainId(chargeForm.getChainId());
        medicalRecord.setChargeSheetId(chargeForm.getChargeSheetId());
        medicalRecord.setChargeFormId(chargeForm.getId());
        medicalRecord.setIsNeedInsert(1);

        FillUtils.fillCreatedBy(medicalRecord, operatorId);
        return medicalRecord;
    }

    public static ChargeForm createChargeFormCore(ChargeSheet chargeSheet, int sourceFormType, String sourceFormId, int sort, String operatorId) {
        ChargeForm chargeForm = new ChargeForm();
        chargeForm.setId(AbcIdUtils.getUUID());
        chargeForm.setChainId(chargeSheet.getChainId());
        chargeForm.setClinicId(chargeSheet.getClinicId());
        chargeForm.setChargeSheetId(chargeSheet.getId());
        chargeForm.setPatientOrderId(chargeSheet.getPatientOrderId());
        chargeForm.setSourceFormId(sourceFormId);
        chargeForm.setSourceFormType(sourceFormType);
        chargeForm.setStatus(Constants.ChargeFormStatus.UNCHARGED);
        chargeForm.setSort(sort);

        FillUtils.fillCreatedBy(chargeForm, operatorId);

        chargeForm.setChargeFormItems(new ArrayList<>());

        return chargeForm;
    }

    public static ChargeForm insertChargeCooperationOrderForm(ChargeSheet chargeSheet, ChargeCooperationOrder chargeCooperationOrder, String operatorId) {
        if (CollectionUtils.isEmpty(chargeCooperationOrder.getOrderItems())) {
            return null;
        }
        // new a chargeform
        ChargeForm chargeForm = new ChargeForm();
        chargeForm.setId(AbcIdUtils.getUUID());
        chargeForm.setClinicId(chargeSheet.getClinicId());
        chargeForm.setChainId(chargeSheet.getChainId());
        chargeForm.setChargeSheetId(chargeSheet.getId());
        chargeForm.setPatientOrderId(chargeSheet.getPatientOrderId());
        chargeForm.setSourceFormId(chargeCooperationOrder.getId());
        chargeForm.setSourceFormType(chargeCooperationOrder.getSourceFormType());
        chargeForm.setStatus(Constants.ChargeFormStatus.UNCHARGED);
        chargeForm.setSort(0);
        chargeForm.setSpecification(chargeCooperationOrder.getSpecification());
        chargeForm.setPharmacyNo(chargeCooperationOrder.getPharmacyNo());
        chargeForm.setPharmacyType(chargeCooperationOrder.getPharmacyType());
        chargeForm.setUsageInfo(JsonUtils.dumpAsJsonNode(chargeCooperationOrder.getUsageInfo()));
        chargeForm.setUsageInfoJson(JsonUtils.dump(chargeCooperationOrder.getUsageInfo()));

        FillUtils.fillCreatedBy(chargeForm, operatorId);
        //new all chargeFormItems
        List<ChargeFormItem> chargeFormItems = new ArrayList<>();
        chargeCooperationOrder.getOrderItems()
                .stream()
                .filter(orderItem -> !StringUtils.isEmpty(orderItem.getGoodsId()))
                .forEach(orderItem -> chargeFormItems.add(ChargeFormItemFactory.insertChargeCooperationOrderItem(chargeForm, orderItem, operatorId)));
        correctChargeFormItemsFromChargeCooperationOrderItem(chargeFormItems, chargeCooperationOrder.getOrderItems());
        chargeForm.setChargeFormItems(chargeFormItems);
        return chargeForm;
    }

    public static ChargeForm updateChargeCooperationOrderForm(ChargeForm chargeForm, ChargeCooperationOrder cooperationOrder, String operatorId) {
        if (chargeForm.getChargeFormItems() == null) {
            chargeForm.setChargeFormItems(new ArrayList<>());
        }

        BiFunction<ChargeCooperationOrderItem, ChargeFormItem, Boolean> isEqualKeyFunc = (chargeCooperationOrderItem, chargeFormItem) -> TextUtils.equals(chargeFormItem.getSourceFormItemId(), chargeCooperationOrderItem.getId());

        Function<ChargeCooperationOrderItem, ChargeFormItem> insertFunc = chargeCooperationOrderItem -> ChargeFormItemFactory.insertChargeCooperationOrderItem(chargeForm, chargeCooperationOrderItem, operatorId);

        Function<ChargeFormItem, Boolean> deleteFunc = chargeFormItem -> ChargeFormItemFactory.deleteChargeFormItem(chargeFormItem, operatorId);

        BiConsumer<ChargeCooperationOrderItem, ChargeFormItem> updateFunc = (chargeCooperationOrderItem, chargeFormItem) -> ChargeFormItemFactory.updateChargeCooperationOrderItem(chargeFormItem, chargeCooperationOrderItem, operatorId);

        List<ChargeFormItem> chargeFormItems = chargeForm.getChargeFormItems();

        MergeTool.doMerge(cooperationOrder.getOrderItems(), chargeFormItems, isEqualKeyFunc, insertFunc, deleteFunc, updateFunc);

        correctChargeFormItemsFromChargeCooperationOrderItem(chargeFormItems, cooperationOrder.getOrderItems());

        return chargeForm;
    }
}
