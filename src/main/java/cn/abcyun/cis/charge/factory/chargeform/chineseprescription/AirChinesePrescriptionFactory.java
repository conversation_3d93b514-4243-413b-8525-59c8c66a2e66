package cn.abcyun.cis.charge.factory.chargeform.chineseprescription;

import cn.abcyun.cis.charge.api.model.ChargeAirPharmacyLogisticsReq;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.factory.chargeform.ChargeFormFactoryAirPharmacy;
import cn.abcyun.cis.charge.factory.chargeform.ChargeFormFactoryBase;
import cn.abcyun.cis.charge.factory.logistics.LogisticsFactory;
import cn.abcyun.cis.charge.model.ChargeAirPharmacyLogistics;
import cn.abcyun.cis.charge.model.ChargeForm;
import cn.abcyun.cis.charge.util.AbcIdUtils;
import cn.abcyun.cis.commons.rpc.outpatient.MedicalRecord;
import cn.abcyun.cis.commons.rpc.outpatient.PrescriptionForm;
import cn.abcyun.cis.commons.rpc.outpatient.PrescriptionFormDelivery;

public class AirChinesePrescriptionFactory extends ChargeFormFactoryBase {

    @Override
    protected void updateChargeFormFromPatientPrescriptionInitChargeFormImpl(ChargeForm chargeForm,
                                                                             PrescriptionForm prescriptionForm,
                                                                             MedicalRecord outpatientMedicalRecord,
                                                                             String doctorId, String operatorId) {
        super.updateChargeFormFromPatientPrescriptionInitChargeFormImpl(chargeForm, prescriptionForm, outpatientMedicalRecord, doctorId, operatorId);
        chargeForm.setSourceFormType(Constants.SourceFormType.AIR_PHARMACY);

        PrescriptionFormDelivery prescriptionFormDelivery = prescriptionForm.getPrescriptionFormDelivery();
        ChargeAirPharmacyLogistics chargeAirPharmacyLogistics = chargeForm.getChargeAirPharmacyLogistics();
        if (chargeAirPharmacyLogistics == null) {
            chargeForm.setChargeAirPharmacyMedicalRecord(ChargeFormFactoryAirPharmacy.initChargeAirPharmacyMedicalRecord(chargeForm, outpatientMedicalRecord, doctorId, operatorId));
        }else {
            ChargeFormFactoryAirPharmacy.updateChargeAirPharmacyMedicalRecordForOutpatient(chargeForm.getChargeAirPharmacyMedicalRecord(), outpatientMedicalRecord, doctorId, operatorId);
        }
        // 保存门诊过来的快递信息
        createOrUpdateAirPharmacyLogisticsFromPrescriptionDelivery(chargeForm, prescriptionFormDelivery, operatorId);

        chargeForm.setUsageScopeId(prescriptionForm.getUsageScopeId());
        chargeForm.setMedicineStateScopeId(prescriptionForm.getMedicineStateScopeId());
        chargeForm.setVendorUsageScopeId(prescriptionForm.getVendorUsageScopeId());
        //空中药房 部分议价的特殊处理
        chargeForm.setExpectedTotalPrice(prescriptionForm.getExpectedTotalPrice());
        chargeForm.setExpectedPriceFlag(ChargeForm.EXPECTED_PART);

        chargeForm.setVendorId(prescriptionForm.getVendorId());
        chargeForm.setVendorName(prescriptionForm.getVendorName());
        ChargeFormFactoryAirPharmacy.insertOriginalDeliveryAndProcessAndIngredientChargeFormItem(chargeForm, prescriptionForm, operatorId);
    }
}
