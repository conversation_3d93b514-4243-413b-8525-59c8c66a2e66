package cn.abcyun.cis.charge.factory.chargeform;

import cn.abcyun.bis.rpc.sdk.cis.model.charge.GoodsFeeType;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.cis.charge.api.model.ChargeAirPharmacyLogisticsReq;
import cn.abcyun.cis.charge.api.model.ChargeFormItemBatchInfoReq;
import cn.abcyun.cis.charge.api.model.ChargeFormItemReq;
import cn.abcyun.cis.charge.api.model.ChargeFormReq;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.factory.ChargeFormItemFactory;
import cn.abcyun.cis.charge.factory.logistics.LogisticsFactory;
import cn.abcyun.cis.charge.model.ChargeAirPharmacyLogistics;
import cn.abcyun.cis.charge.model.ChargeForm;
import cn.abcyun.cis.charge.model.ChargeFormItem;
import cn.abcyun.cis.charge.model.ChargeFormItemBatchInfo;
import cn.abcyun.cis.charge.processor.calculate.CalculateModel;
import cn.abcyun.cis.charge.service.dto.ChargeFormItemTargetDto;
import cn.abcyun.cis.charge.service.dto.DTOConverter;
import cn.abcyun.cis.charge.service.dto.OutpatientPrescriptionFormItemSourceDto;
import cn.abcyun.cis.charge.util.*;
import cn.abcyun.cis.commons.model.ComposeType;
import cn.abcyun.cis.commons.rpc.outpatient.*;
import cn.abcyun.cis.commons.util.ItemParentIdConvertTool;
import cn.abcyun.cis.commons.util.TextUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 普通chargeForm的构造
 */
public class ChargeFormFactoryBase implements IChargeFormCreate {

    private static final Logger sLogger = LoggerFactory.getLogger(ChargeFormFactoryBase.class);

//    @Autowired
//    private ChargeFormItemService mChargeFormItemService;

    @Override
    public ChargeForm createChargeFormFromCalculate(String chargeSheetId,
                                                    String patientOrderId,
                                                    String clinicId,
                                                    String chainId,
                                                    ChargeFormReq chargeFormReq,
                                                    boolean isCanUpdateUsageInfo,
                                                    boolean generateIdIfNotExisted,
                                                    boolean forceGenerateNewId,
                                                    String operatorId) {
        ChargeForm chargeForm = new ChargeForm();
        // 终端传上来的chargeSheetId
        String id = chargeFormReq.getId();
        if (forceGenerateNewId || (TextUtils.isEmpty(id) && generateIdIfNotExisted)) {
            id = AbcIdUtils.getUUID();
        }
        // 各种id的初始化
        chargeForm.setId(id);
        chargeForm.setKeyId(chargeFormReq.getKeyId());
        chargeForm.setChargeSheetId(chargeSheetId);
        chargeForm.setClinicId(clinicId);
        chargeForm.setChainId(chainId);
        chargeForm.setPatientOrderId(patientOrderId);
        chargeForm.setSourceFormType(chargeFormReq.getSourceFormType());
        chargeForm.setStatus(Constants.ChargeFormStatus.UNCHARGED);
        chargeForm.setSort(chargeFormReq.getSort());
        chargeForm.setChargeFormItems(new ArrayList<>());
        chargeForm.setPharmacyNo(chargeFormReq.getPharmacyNo());
        chargeForm.setPharmacyType(chargeFormReq.getPharmacyType());
        FillUtils.fillCreatedBy(chargeForm, operatorId);
        if (chargeFormReq.getUsageInfo() != null) {
            //修正usageInfo中的doseCount
            if (chargeForm.getSourceFormType() == Constants.SourceFormType.PRESCRIPTION_CHINESE || chargeForm.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY) {

                BigDecimal doseCount = chargeFormReq.getChargeFormItems()
                        .stream()
                        .filter(chargeFormItemReq -> Objects.equals(chargeFormItemReq.getProductType(), Constants.ProductType.MEDICINE))
                        //找到item的剂量的最大值
                        .map(chargeFormItemReq -> {
                            if (chargeFormItemReq.getExpectedDoseCount() != null) {
                                return MathUtils.max(chargeFormItemReq.getExpectedDoseCount(), BigDecimal.ONE);
                            }
                            return MathUtils.max(chargeFormItemReq.getDoseCount(), BigDecimal.ONE);
                        })
                        .max(Comparator.naturalOrder())
                        .orElse(BigDecimal.ONE);

                chargeFormReq.getUsageInfo().setDoseCount(doseCount.intValue());
            }
            chargeForm.setUsageInfoJson(JsonUtils.dump(chargeFormReq.getUsageInfo()));
            chargeForm.setUsageInfo(JsonUtils.parseString(chargeForm.getUsageInfoJson()));
        }



        // 收费子项目的初始化---这里才是算费的最小粒度。
        if (chargeFormReq.getChargeFormItems() != null) {
            List<ChargeFormItem> chargeFormItems = chargeFormReq.getChargeFormItems().stream()
                    .flatMap(chargeFormItemReq ->
                            convertToChargeFormItem(chargeForm,
                                    chargeFormReq.getPharmacyType(),
                                    chargeFormItemReq,
                                    generateIdIfNotExisted,
                                    forceGenerateNewId,
                                    operatorId).stream())
                    .collect(Collectors.toList());
            chargeForm.setChargeFormItems(chargeFormItems);
        }
        return chargeForm;
    }


    public ChargeForm createChargeFormFromPatientProcess(String chargeSheetId, String patientOrderId, String clinicId, String chainId,
                                                         BigDecimal processFee,
                                                         String operatorId) {
        ChargeForm chargeForm = new ChargeForm();
        chargeForm.setId(AbcIdUtils.getUUID());
        chargeForm.setChargeSheetId(chargeSheetId);
        chargeForm.setPatientOrderId(patientOrderId);
        chargeForm.setSourceFormId("");
        chargeForm.setSourceFormType(Constants.SourceFormType.PROCESS);
        chargeForm.setStatus(Constants.ChargeFormStatus.UNCHARGED);
        chargeForm.setClinicId(clinicId);
        chargeForm.setChainId(chainId);

        FillUtils.fillCreatedBy(chargeForm, operatorId);

        List<ChargeFormItem> chargeFormItems = new ArrayList<>();
        ChargeFormItem formItem = ChargeFormItemFactory.insertProcessFormItem(chargeForm, processFee, operatorId);
        if (formItem != null) {
            chargeFormItems.add(formItem);
        }
        chargeForm.setChargeFormItems(chargeFormItems);

        return chargeForm;
    }

    /**
     * 从门诊过来的chargeForm的构造[加工费]
     */

    public ChargeForm createChargeFormFromPatientPrescription(String chargeSheetId, String patientOrderId, String clinicId, String chainId,
                                                              PrescriptionForm prescriptionForm,
                                                              CalculateModel calculateModel,
                                                              MedicalRecord outpatientMedicalRecord,
                                                              String doctorId,
                                                              String doctorDepartmentId,
                                                              String operatorId) {
        if (prescriptionForm == null
                || ((prescriptionForm.getPrescriptionFormItems() == null
                || prescriptionForm.getPrescriptionFormItems().size() == 0) && prescriptionForm.getType() != PrescriptionForm.TYPE_EYE_GLASS)) {
            return null;
        }
        ChargeForm chargeForm = newChargeFormFromPatientPrescriptionImpl(chargeSheetId, patientOrderId, clinicId, chainId,
                prescriptionForm, outpatientMedicalRecord, doctorId,
                operatorId);

        //Robinsli ChargeFormItem的构造
        //Step 2 ChargeFormItem的构造
        newChargeFormFromPatientPrescriptionCreateItemsImpl(chargeForm, chargeSheetId, patientOrderId, clinicId, chainId,
                prescriptionForm, calculateModel, outpatientMedicalRecord, doctorId, doctorDepartmentId,
                operatorId);

        //计费
        initRawTotalPrice(chargeForm);

        /**
         * 更新item上的快递信息
         */
        ChargeFormFactory.initChargeFormDeliveryInfo(chargeForm);

        return chargeForm;
    }


    protected void newChargeFormFromPatientPrescriptionCreateItemsImpl(ChargeForm chargeForm, String chargeSheetId, String patientOrderId, String clinicId, String chainId,
                                                                       PrescriptionForm prescriptionForm,
                                                                       CalculateModel calculateModel,
                                                                       MedicalRecord outpatientMedicalRecord,
                                                                       String doctorId,
                                                                       String doctorDepartmentId,
                                                                       String operatorId) {

        List<ChargeFormItem> chargeFormItems = new ArrayList<>();
        prescriptionForm.getPrescriptionFormItems().forEach(prescriptionFormItem -> {
            ChargeFormItem formItem = ChargeFormItemFactory.insertPrescriptionFormItem(chargeForm, prescriptionForm, prescriptionFormItem, calculateModel, doctorId, doctorDepartmentId, operatorId);
            if (formItem != null) {
                chargeFormItems.add(formItem);
            }
        });
        chargeForm.setChargeFormItems(chargeFormItems);

        //绑定收费项的父子关系
        ItemParentIdConvertTool.convert(prescriptionForm.getPrescriptionFormItems()
                        .stream()
                        .map(OutpatientPrescriptionFormItemSourceDto::new)
                        .collect(Collectors.toList()),
                chargeFormItems.stream()
                        .map(ChargeFormItemTargetDto::new)
                        .collect(Collectors.toList())
        );
        ChargeFormItemFactory.updateChargeFormItemComposeTypeAndGoodsFeeType(chargeFormItems);
        //处理套餐子项的价格问题
        ChargeFormItemFactory.updateComposeItemPrice(chargeFormItems);
    }

    protected ChargeForm newChargeFormFromPatientPrescriptionImpl(String chargeSheetId, String patientOrderId, String clinicId, String chainId,
                                                                  PrescriptionForm prescriptionForm,
                                                                  MedicalRecord outpatientMedicalRecord,
                                                                  String doctorId,
                                                                  String operatorId) {
        //Step1 生成
        ChargeForm chargeForm = new ChargeForm();
        chargeForm.setId(AbcIdUtils.getUUID());
        chargeForm.setClinicId(clinicId);
        chargeForm.setChainId(chainId);
        chargeForm.setChargeSheetId(chargeSheetId);
        chargeForm.setPatientOrderId(patientOrderId);
        chargeForm.setSourceFormId(prescriptionForm.getId());
        chargeForm.setSort(prescriptionForm.getSort());
        chargeForm.setPharmacyNo(prescriptionForm.getPharmacyNo());
        chargeForm.setPharmacyType(prescriptionForm.getPharmacyType());
        chargeForm.setUsageScopeId(prescriptionForm.getUsageScopeId());
        chargeForm.setMedicineStateScopeId(prescriptionForm.getMedicineStateScopeId());
        int sourceFormType;
        if (prescriptionForm.getType() == PrescriptionForm.TYPE_CHINESE) {
            sourceFormType = Constants.SourceFormType.PRESCRIPTION_CHINESE;
            UsageInfo usageInfo = new UsageInfo();
            BeanUtils.copyProperties(prescriptionForm, usageInfo);
            usageInfo.setChecked(usageInfo.getIsDecoction() && !StringUtils.isEmpty(usageInfo.getProcessUsage()));
            chargeForm.setUsageInfoJson(JsonUtils.dump(usageInfo));
            chargeForm.setSpecification(prescriptionForm.getSpecification());
        } else if (prescriptionForm.getType() == PrescriptionForm.TYPE_INFUSION) {
            sourceFormType = Constants.SourceFormType.PRESCRIPTION_INFUSION;
        } else if (prescriptionForm.getType() == PrescriptionForm.TYPE_EXTERNAL) {
            sourceFormType = Constants.SourceFormType.PRESCRIPTION_EXTERNAL;
            UsageInfo usageInfo = new UsageInfo();
            BeanUtils.copyProperties(prescriptionForm, usageInfo);
            chargeForm.setUsageInfoJson(JsonUtils.dump(usageInfo));
        } else if (prescriptionForm.getType() == PrescriptionForm.TYPE_EYE_GLASS) {
            sourceFormType = Constants.SourceFormType.PRESCRIPTION_TYPE_EYE_GLASS;
            UsageInfo usageInfo = new UsageInfo();
            BeanUtils.copyProperties(prescriptionForm, usageInfo);

            if (prescriptionForm.getGlassesParams() != null) {
                EyeExamination glassesParams = new EyeExamination();
                BeanUtils.copyProperties(prescriptionForm.getGlassesParams(), glassesParams);
                usageInfo.setGlassesParams(glassesParams);
            }

            chargeForm.setUsageInfoJson(JsonUtils.dump(usageInfo));
        } else {
            sourceFormType = Constants.SourceFormType.PRESCRIPTION_WESTERN;
        }
        chargeForm.setSourceFormType(sourceFormType);
        chargeForm.setStatus(Constants.ChargeFormStatus.UNCHARGED);
        chargeForm.setVendorUsageScopeId(prescriptionForm.getVendorUsageScopeId());
        chargeForm.setCreated(Instant.now());
        chargeForm.setCreatedBy(operatorId);
        chargeForm.setLastModifiedBy(chargeForm.getCreatedBy());
        chargeForm.setLastModified(Instant.now());


        return chargeForm;
    }

    /**
     * 根据门诊中药处方快递信息
     * 更新chargeForm快递信息
     *
     * @param chargeForm chargeForm
     * @return ChargeAirPharmacyLogistics 快递信息
     */
    protected ChargeAirPharmacyLogistics createOrUpdateAirPharmacyLogisticsFromPrescriptionDelivery(ChargeForm chargeForm, PrescriptionFormDelivery prescriptionFormDelivery, String operatorId) {
        if (chargeForm == null) {
            sLogger.error("chargeForm is null");
            return null;
        }
        if (prescriptionFormDelivery == null) {
            sLogger.error("prescriptionFormDelivery is null");
            return null;
        }
        ChargeAirPharmacyLogistics chargeAirPharmacyLogistics = chargeForm.getChargeAirPharmacyLogistics();
        if (chargeAirPharmacyLogistics == null) {
            // 新增
            ChargeAirPharmacyLogisticsReq chargeAirPharmacyLogisticsReq = new ChargeAirPharmacyLogisticsReq();
            chargeAirPharmacyLogisticsReq.setId(AbcIdUtils.getUID());
            chargeAirPharmacyLogisticsReq.setAddressProvinceId(prescriptionFormDelivery.getAddressProvinceId());
            chargeAirPharmacyLogisticsReq.setAddressProvinceName(prescriptionFormDelivery.getAddressProvinceName());
            chargeAirPharmacyLogisticsReq.setAddressDistrictId(prescriptionFormDelivery.getAddressDistrictId());
            chargeAirPharmacyLogisticsReq.setAddressDistrictName(prescriptionFormDelivery.getAddressDistrictName());
            chargeAirPharmacyLogisticsReq.setAddressCityId(prescriptionFormDelivery.getAddressCityId());
            chargeAirPharmacyLogisticsReq.setAddressCityName(prescriptionFormDelivery.getAddressCityName());
            chargeAirPharmacyLogisticsReq.setAddressDetail(prescriptionFormDelivery.getAddressDetail());
            chargeAirPharmacyLogisticsReq.setDeliveryMobile(prescriptionFormDelivery.getDeliveryMobile());
            chargeAirPharmacyLogisticsReq.setDeliveryName(prescriptionFormDelivery.getDeliveryName());
            ChargeAirPharmacyLogisticsReq.LogisticsCompanyReq companyReq = new ChargeAirPharmacyLogisticsReq.LogisticsCompanyReq();
            companyReq.setId(prescriptionFormDelivery.getDeliveryCompanyId());
            chargeAirPharmacyLogisticsReq.setDeliveryCompany(companyReq);
            chargeAirPharmacyLogisticsReq.setDeliveryPayType(prescriptionFormDelivery.getDeliveryPayType());
            chargeAirPharmacyLogisticsReq.setDeliveryNo(prescriptionFormDelivery.getDeliveryOrderNo());

            chargeAirPharmacyLogistics = LogisticsFactory.createAirPharmacyLogisticsFromReq(
                    chargeForm.getChargeSheetId(),
                    chargeForm.getId(),
                    chargeForm.getClinicId(),
                    chargeForm.getChainId(),
                    chargeAirPharmacyLogisticsReq,
                    operatorId
            );
            chargeForm.setChargeAirPharmacyLogistics(chargeAirPharmacyLogistics);
        } else {
            // 更新
            chargeAirPharmacyLogistics.setAddressProvinceId(prescriptionFormDelivery.getAddressProvinceId());
            chargeAirPharmacyLogistics.setAddressProvinceName(prescriptionFormDelivery.getAddressProvinceName());
            chargeAirPharmacyLogistics.setAddressDistrictId(prescriptionFormDelivery.getAddressDistrictId());
            chargeAirPharmacyLogistics.setAddressDistrictName(prescriptionFormDelivery.getAddressDistrictName());
            chargeAirPharmacyLogistics.setAddressCityId(prescriptionFormDelivery.getAddressCityId());
            chargeAirPharmacyLogistics.setAddressCityName(prescriptionFormDelivery.getAddressCityName());
            chargeAirPharmacyLogistics.setAddressDetail(prescriptionFormDelivery.getAddressDetail());
            chargeAirPharmacyLogistics.setDeliveryMobile(prescriptionFormDelivery.getDeliveryMobile());
            chargeAirPharmacyLogistics.setDeliveryName(prescriptionFormDelivery.getDeliveryName());
            chargeAirPharmacyLogistics.setDeliveryNo(prescriptionFormDelivery.getDeliveryOrderNo());
            ChargeAirPharmacyLogisticsReq.LogisticsCompanyReq companyReq = new ChargeAirPharmacyLogisticsReq.LogisticsCompanyReq();
            companyReq.setId(prescriptionFormDelivery.getDeliveryCompanyId());
            chargeAirPharmacyLogistics.setDeliveryCompany(companyReq);
            chargeAirPharmacyLogistics.setDeliveryPayType(prescriptionFormDelivery.getDeliveryPayType());
        }

        return chargeAirPharmacyLogistics;
    }

    public ChargeForm updateChargeFormFromPatientPrescription(ChargeForm chargeForm,
                                                              PrescriptionForm prescriptionForm,
                                                              CalculateModel calculateModel,
                                                              MedicalRecord outpatientMedicalRecord,
                                                              String doctorId, String doctorDepartmentId, String operatorId) {
        if (chargeForm.getChargeFormItems() == null) {
            chargeForm.setChargeFormItems(new ArrayList<>());
        }
        if (prescriptionForm.getPrescriptionFormItems() == null) {
            prescriptionForm.setPrescriptionFormItems(new ArrayList<>());
        }
        //更新chargeForm
        updateChargeFormFromPatientPrescriptionInitChargeFormImpl(chargeForm, prescriptionForm, outpatientMedicalRecord, doctorId, operatorId);
        //合并算发
        updateChargeFormFromPatientPrescriptionMergeImpl(chargeForm, prescriptionForm, calculateModel, outpatientMedicalRecord, doctorId, doctorDepartmentId, operatorId);
        //计算总价
        initRawTotalPrice(chargeForm);

        /**
         * 更新item上的快递信息
         */
        ChargeFormFactory.initChargeFormDeliveryInfo(chargeForm);
        return chargeForm;
    }

    protected void updateChargeFormFromPatientPrescriptionInitChargeFormImpl(ChargeForm chargeForm,
                                                                             PrescriptionForm prescriptionForm,
                                                                             MedicalRecord outpatientMedicalRecord,
                                                                             String doctorId, String operatorId) {
        if (prescriptionForm.getType() == PrescriptionForm.TYPE_CHINESE || prescriptionForm.getType() == PrescriptionForm.TYPE_EXTERNAL) {
            UsageInfo usageInfo = new UsageInfo();
            BeanUtils.copyProperties(prescriptionForm, usageInfo);

            chargeForm.setUsageInfoJson(JsonUtils.dump(usageInfo));
            chargeForm.setSpecification(prescriptionForm.getSpecification());
            chargeForm.setPharmacyNo(prescriptionForm.getPharmacyNo());
            chargeForm.setPharmacyType(prescriptionForm.getPharmacyType());
        } else if (prescriptionForm.getType() == PrescriptionForm.TYPE_EYE_GLASS) {
            UsageInfo usageInfo = new UsageInfo();
            BeanUtils.copyProperties(prescriptionForm, usageInfo);

            if (prescriptionForm.getGlassesParams() != null) {
                EyeExamination glassesParams = new EyeExamination();
                BeanUtils.copyProperties(prescriptionForm.getGlassesParams(), glassesParams);
                usageInfo.setGlassesParams(glassesParams);
            }


            chargeForm.setUsageInfoJson(JsonUtils.dump(usageInfo));
        }
        chargeForm.setPharmacyType(prescriptionForm.getPharmacyType());
        chargeForm.setPharmacyNo(prescriptionForm.getPharmacyNo());
        chargeForm.setSort(prescriptionForm.getSort());
        chargeForm.setStatus(Constants.ChargeFormStatus.UNCHARGED);

    }

    private void updateChargeFormFromPatientPrescriptionMergeImpl(ChargeForm chargeForm,
                                                                  PrescriptionForm prescriptionForm,
                                                                  CalculateModel calculateModel,
                                                                  MedicalRecord outpatientMedicalRecord,
                                                                  String doctorId, String doctorDepartmentId, String operatorId) {

        BiFunction<PrescriptionFormItem, ChargeFormItem, Boolean> isEqualKeyFunc = (prescriptionFormItem, chargeFormItem) -> TextUtils.equals(chargeFormItem.getSourceFormItemId(), prescriptionFormItem.getId());
        Function<PrescriptionFormItem, ChargeFormItem> insertFunc = prescriptionFormItem -> {
            ChargeFormItem chargeFormItem = ChargeFormItemFactory.insertPrescriptionFormItem(chargeForm, prescriptionForm, prescriptionFormItem, calculateModel, doctorId, doctorDepartmentId, operatorId);
            return chargeFormItem;
        };

        Function<ChargeFormItem, Boolean> deleteFunc = chargeFormItem -> {
            if (chargeFormItem.getProductType() == Constants.ProductType.EXPRESS_DELIVERY || chargeFormItem.getProductType() == Constants.ProductType.PROCESS || chargeFormItem.getProductType() == Constants.ProductType.INGREDIENT) {
                return false;
            }
            return ChargeFormItemFactory.deleteChargeFormItem(chargeFormItem, operatorId);
        };

        BiConsumer<PrescriptionFormItem, ChargeFormItem> updateFunc = (prescriptionFormItem, chargeFormItem) -> {
            ChargeFormItemFactory.updatePrescriptionFormItem(chargeForm, prescriptionForm, calculateModel, chargeFormItem, prescriptionFormItem, doctorId, doctorDepartmentId, operatorId);
        };

        MergeTool.doMerge(prescriptionForm.getPrescriptionFormItems(), chargeForm.getChargeFormItems(), isEqualKeyFunc, insertFunc, deleteFunc, updateFunc);

        List<ChargeFormItem> chargeFormItems = chargeForm.getChargeFormItems();

        //绑定收费项的父子关系
        ItemParentIdConvertTool.convert(prescriptionForm.getPrescriptionFormItems()
                        .stream()
                        .map(OutpatientPrescriptionFormItemSourceDto::new)
                        .collect(Collectors.toList()),
                chargeFormItems.stream()
                        .filter(item -> item.getIsDeleted() == 0)
                        .map(ChargeFormItemTargetDto::new)
                        .collect(Collectors.toList())
        );
        ChargeFormItemFactory.updateChargeFormItemComposeTypeAndGoodsFeeType(chargeFormItems);
        //处理套餐子项的价格问题
        ChargeFormItemFactory.updateComposeItemPrice(chargeFormItems);
    }

    /**
     * 空中药房的计算方式覆盖这里
     */
    protected void initRawTotalPrice(ChargeForm chargeForm) {
        chargeForm.setTotalPrice(chargeForm.getChargeFormItems().stream().filter(item -> item.getIsDeleted() == 0).map(ChargeFormItem::getTotalPrice).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
    }

    //普通订单不需要初始化
    public void bindProductInfoForAirPharmacyExpressDelivery(ChargeForm chargeForm) {

    }

    /**
     * 普通chargeFormItem的的构造
     */
    public List<ChargeFormItem> convertToChargeFormItem(ChargeForm chargeForm,
                                                        int pharmacyType,
                                                        ChargeFormItemReq chargeFormItemReq,
                                                        boolean generateIdIfNotExisted,
                                                        boolean forceGenerateNewId,
                                                        String operatorId) {
        List<ChargeFormItem> chargeFormItems = new ArrayList<>();
        ChargeFormItem chargeFormItem = generateChargeFormItem(chargeForm, pharmacyType, chargeFormItemReq, generateIdIfNotExisted, forceGenerateNewId, operatorId);
        chargeFormItems.add(chargeFormItem);

        if (chargeFormItemReq.getComposeChildren() != null && chargeFormItemReq.getComposeChildren().size() != 0) {
            List<ChargeFormItem> children = chargeFormItemReq.getComposeChildren().stream().map(
                    subChargeFormItemReq -> {
                        subChargeFormItemReq.setIsFixedData(chargeFormItemReq.getIsFixedData());
                        ChargeFormItem subChargeFormItem = generateChargeFormItem(chargeForm, pharmacyType, subChargeFormItemReq, generateIdIfNotExisted, forceGenerateNewId, operatorId);
                        subChargeFormItem.setComposeParentFormItemId(chargeFormItem.getId());
                        return subChargeFormItem;
                    })
                    .collect(Collectors.toList());

            if (chargeFormItem.getProductType() == Constants.ProductType.COMPOSE_PRODUCT) {
                chargeFormItem.setComposeType(ComposeType.COMPOSE);
                chargeFormItem.setGoodsFeeType(GoodsFeeType.FEE_OWN);
                children.forEach(child -> child.setComposeType(ComposeType.COMPOSE_SUB_ITEM));
            } else {
                chargeFormItem.setComposeType(ComposeType.NOT_COMPOSE);
                chargeFormItem.setGoodsFeeType(GoodsFeeType.FEE_PARENT);
                children.forEach(child -> child.setGoodsFeeType(GoodsFeeType.FEE_CHILD));
            }

            chargeFormItems.addAll(children);
        }
        return chargeFormItems;
    }

    public ChargeFormItem generateChargeFormItem(ChargeForm chargeForm, int pharmacyType, ChargeFormItemReq chargeFormItemReq, boolean generateIdIfNotExisted, boolean forceGenerateNewId, String operatorId) {
        ChargeFormItem chargeFormItem = new ChargeFormItem();
        String id = chargeFormItemReq.getId();
        if (forceGenerateNewId || (org.apache.commons.lang3.StringUtils.isBlank(id) && generateIdIfNotExisted)) {
            id = AbcIdUtils.getUUID();
        }
        chargeFormItem.setId(id);
        chargeFormItem.setKeyId(chargeFormItemReq.getKeyId());
        chargeFormItem.setSourceFormItemKeyId(chargeFormItemReq.getSourceFormItemKeyId());
        chargeFormItem.setChargeFormId(chargeForm.getId());
        chargeFormItem.setChargeSheetId(chargeForm.getChargeSheetId());
        chargeFormItem.setPatientOrderId(chargeForm.getPatientOrderId());
        chargeFormItem.setClinicId(chargeForm.getClinicId());
        chargeFormItem.setChainId(chargeForm.getChainId());
        chargeFormItem.setProductType(DTOConverter.parseProductType(chargeForm.getSourceFormType(), chargeFormItemReq.getProductType()));
        chargeFormItem.setProductSubType(DTOConverter.parseProductSubType(chargeForm.getSourceFormType(), chargeFormItemReq.getProductSubType()));
        chargeFormItem.setPharmacyType(chargeFormItemReq.getPharmacyType());
        chargeFormItem.setPharmacyNo(chargeFormItemReq.getPharmacyNo());
        chargeFormItem.setIsGift(chargeFormItemReq.getIsGift());
        if (chargeForm.getSourceFormType() == Constants.SourceFormType.GIFT_PRODUCT) {
            chargeFormItem.setSourceFormItemId(chargeFormItemReq.getSourceFormItemId());
        }
        // 补偿系统费用的productId
        if (chargeFormItem.getProductType() == Constants.ProductType.REGISTRATION) {
            Pair<String, Integer> pair = RegistrationUtils.registrationCategoryConvertToGoodsInfo(Optional.ofNullable(chargeFormItemReq.getDoctorInfo()).map(ChargeFormItemReq.DoctorInfoReq::getRegistrationCategory).orElse(0));
            chargeFormItem.setProductId(!StringUtils.isEmpty(chargeFormItemReq.getProductId()) ? chargeFormItemReq.getProductId() : pair.getLeft());
            chargeFormItem.setProductSubType(pair.getRight());
        } else if (chargeFormItem.getProductType() == Constants.ProductType.EXPRESS_DELIVERY) {
            chargeFormItem.setProductId(!StringUtils.isEmpty(chargeFormItemReq.getProductId()) ? chargeFormItemReq.getProductId() : Constants.SystemProductId.EXPRESS_DELIVERY_PRODUCT_ID);
            chargeFormItem.setProductSubType(0);
        } else if (chargeFormItem.getProductType() == Constants.ProductType.PROCESS) {
            chargeFormItem.setProductId(!StringUtils.isEmpty(chargeFormItemReq.getProductId()) ? chargeFormItemReq.getProductId() : Constants.SystemProductId.PROCESS_PRODUCT_ID);
            if (pharmacyType != GoodsConst.PharmacyType.LOCAL_PHARMACY) {
                chargeFormItem.setProductSubType(0);
            }
        } else if (chargeFormItem.getProductType() == Constants.ProductType.ONLINE_CONSULTATION) {
            chargeFormItem.setProductId(!StringUtils.isEmpty(chargeFormItemReq.getProductId()) ? chargeFormItemReq.getProductId() : Constants.SystemProductId.ONLINE_CONSULTATION_PRODUCT_ID);
            chargeFormItem.setProductSubType(0);
        } else {
            chargeFormItem.setProductId(chargeFormItemReq.getProductId());
        }
        chargeFormItem.setName(chargeFormItemReq.getName());
        chargeFormItem.setStatus(Constants.ChargeFormItemStatus.UNCHARGED);
        chargeFormItem.setV2Status(Constants.ChargeFormItemStatus.UNCHARGED);
        chargeFormItem.setUnitCount(chargeFormItemReq.getUnitCount());
        chargeFormItem.setExecutedUnitCount(chargeFormItemReq.getExecutedUnitCount());
        chargeFormItem.setDoseCount(chargeFormItemReq.getDoseCount());
        chargeFormItem.setExpectedDoseCount(chargeFormItemReq.getExpectedDoseCount());
        chargeFormItem.setUnit(chargeFormItemReq.getUnit());
        chargeFormItem.setUseDismounting(chargeFormItemReq.getUseDismounting());
        chargeFormItem.setSort(chargeFormItemReq.getSort());
        chargeFormItem.setUnitPrice(chargeFormItemReq.getUnitPrice());
        chargeFormItem.setSourceUnitPrice(chargeFormItemReq.getSourceUnitPrice());
        chargeFormItem.setDiscountPrice(chargeFormItemReq.getDiscountPrice());
        chargeFormItem.setFrontEndUnitPrice(chargeFormItemReq.getUnitPrice());
        chargeFormItem.setFrontEndTotalPrice(chargeFormItemReq.getTotalPrice());
        chargeFormItem.setExpectedUnitPrice(chargeFormItemReq.getExpectedUnitPrice());
        chargeFormItem.setExpectedTotalPrice(chargeFormItemReq.getExpectedTotalPrice());
        chargeFormItem.setTotalPriceRatio(chargeFormItemReq.getTotalPriceRatio());
        chargeFormItem.setExpectedTotalPriceRatio(chargeFormItemReq.getExpectedTotalPriceRatio());
        chargeFormItem.setTotalPrice(MathUtils.calculateTotalPrice(chargeFormItem.getUnitPrice(), chargeFormItem.getUnitCount(), chargeFormItem.getDoseCount(), 2));
        chargeFormItem.setDeductTotalCount(MathUtils.wrapBigDecimalOrZero(chargeFormItemReq.getDeductTotalCount()));
        chargeFormItem.setVerifyTotalCount(MathUtils.wrapBigDecimalOrZero(chargeFormItemReq.getVerifyTotalCount()));
        chargeFormItem.setIsFixedData(chargeFormItemReq.getIsFixedData());
        chargeFormItem.setOriginalChargeFormItemIds(chargeFormItemReq.getOriginalChargeFormItemIds());
        FillUtils.fillCreatedBy(chargeFormItem, operatorId);

        // 如果是中药，直接走拆零逻辑
        boolean isChineseMedicine = chargeFormItem.getProductType() == Constants.ProductType.MEDICINE && chargeFormItem.getProductSubType() == Constants.ProductType.SubType.MEDICINE_CHINESE;
        if (isChineseMedicine) {
            chargeFormItem.setUseDismounting(1);
        }
        chargeFormItemReq.amendExpectedBatch();
        chargeFormItem.setIsExpectedBatch(chargeFormItemReq.getIsExpectedBatch());
        if (chargeFormItemReq.needUpdateBatch()) {
            chargeFormItem.setChargeFormItemBatchInfos(new ArrayList<>());
            chargeFormItem.getChargeFormItemBatchInfos()
                    .addAll(chargeFormItemReq.getChargeFormItemBatchInfos().stream()
                            .map(batchInfoReq -> generateChargeFormItemBatchInfo(chargeFormItem, batchInfoReq, forceGenerateNewId))
                            .collect(Collectors.toList()));
        }
        // 中药设置煎法
        if (chargeFormItem.getProductType() == Constants.ProductType.MEDICINE) {
            UsageInfo usageInfo = new UsageInfo();
            usageInfo.setSpecialRequirement(chargeFormItemReq.getSpecialRequirement());

            chargeFormItem.setUsageInfo(JsonUtils.dumpAsJsonNode(usageInfo));
            chargeFormItem.setUsageInfoJson(JsonUtils.dump(usageInfo));
        }

        if (Objects.nonNull(chargeFormItemReq.getUsageInfo())) {
            UsageInfo usageInfo = JsonUtils.readValue(chargeFormItem.getUsageInfoJson(), UsageInfo.class);
            usageInfo = Objects.nonNull(usageInfo) ? usageInfo : new UsageInfo();

            usageInfo.setPayType(chargeFormItemReq.getUsageInfo().getPayType());
            chargeFormItem.setUsageInfo(JsonUtils.dumpAsJsonNode(usageInfo));
            chargeFormItem.setUsageInfoJson(JsonUtils.dump(usageInfo));
        }

        ChargeFormItemFactory.insertChargeFormItemAdditionalIfNeed(chargeFormItem);
        // 组装扩展属性数据信息
        ChargeFormItemFactory.updateChargeFormItemAdditional(chargeFormItem.getAdditional(),
                chargeFormItemReq.getDoctorId(),
                chargeFormItemReq.getNurseId(),
                chargeFormItemReq.getDepartmentId(),
                chargeFormItemReq.getRemark(),
                chargeFormItemReq.getToothNos(),
                chargeFormItemReq.getTraceableCodeList(),
                chargeFormItemReq.getUnitAdjustmentFeeLastModifiedBy(),
                chargeFormItemReq.getGiftGoodsPromotionId());
        return chargeFormItem;
    }

    public ChargeFormItemBatchInfo generateChargeFormItemBatchInfo(ChargeFormItem chargeFormItem, ChargeFormItemBatchInfoReq batchInfoReq, boolean forceGenerateNewId) {
        String id = batchInfoReq.getId();
        if (forceGenerateNewId || org.apache.commons.lang3.StringUtils.isBlank(id)) {
            id = AbcIdUtils.getUID();
        }
        ChargeFormItemBatchInfo chargeFormItemBatchInfo = new ChargeFormItemBatchInfo();
        chargeFormItemBatchInfo.setId(id);
        chargeFormItemBatchInfo.setClinicId(chargeFormItem.getClinicId());
        chargeFormItemBatchInfo.setChainId(chargeFormItem.getChainId());
        chargeFormItemBatchInfo.setPatientOrderId(chargeFormItem.getPatientOrderId());
        chargeFormItemBatchInfo.setChargeSheetId(chargeFormItem.getChargeSheetId());
        chargeFormItemBatchInfo.setChargeFormId(chargeFormItem.getChargeFormId());
        chargeFormItemBatchInfo.setChargeFormItemId(chargeFormItem.getId());
        chargeFormItemBatchInfo.setAssociateItemBatchInfoId(null);
        chargeFormItemBatchInfo.setUnitCostPrice(BigDecimal.ZERO);
        chargeFormItemBatchInfo.setTotalPrice(BigDecimal.ZERO);
        chargeFormItemBatchInfo.setUnitCount(batchInfoReq.getUnitCount());
        chargeFormItemBatchInfo.setUnitPrice(chargeFormItem.getUnitPrice());
        chargeFormItemBatchInfo.setRefundUnitCount(null);
        chargeFormItemBatchInfo.setRefundTotalPrice(null);
        chargeFormItemBatchInfo.setProductId(chargeFormItem.getProductId());
        chargeFormItemBatchInfo.setIsUseLimitPrice(0);
        chargeFormItemBatchInfo.setStockId(batchInfoReq.getStockId());
        chargeFormItemBatchInfo.setBatchId(batchInfoReq.getBatchId());
        chargeFormItemBatchInfo.setBatchNo(batchInfoReq.getBatchNo());
        chargeFormItemBatchInfo.setExpiryDate(batchInfoReq.getExpiryDate());
        return chargeFormItemBatchInfo;
    }
}
