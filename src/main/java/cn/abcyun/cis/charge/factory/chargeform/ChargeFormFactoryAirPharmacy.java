package cn.abcyun.cis.charge.factory.chargeform;

import cn.abcyun.bis.rpc.sdk.cis.model.charge.GoodsFeeType;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.cis.charge.api.model.ChargeAirPharmacyMedicalRecordReq;
import cn.abcyun.cis.charge.api.model.ChargeFormItemReq;
import cn.abcyun.cis.charge.api.model.ChargeFormReq;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.factory.ChargeFormItemFactory;
import cn.abcyun.cis.charge.factory.logistics.LogisticsFactory;
import cn.abcyun.cis.charge.model.ChargeAirPharmacyLogistics;
import cn.abcyun.cis.charge.model.ChargeAirPharmacyMedicalRecord;
import cn.abcyun.cis.charge.model.ChargeForm;
import cn.abcyun.cis.charge.model.ChargeFormItem;
import cn.abcyun.cis.charge.processor.calculate.CalculateModel;
import cn.abcyun.cis.charge.util.AbcIdUtils;
import cn.abcyun.cis.charge.util.FillUtils;
import cn.abcyun.cis.charge.util.JsonUtils;
import cn.abcyun.cis.commons.model.ComposeType;
import cn.abcyun.cis.commons.rpc.outpatient.MedicalRecord;
import cn.abcyun.cis.commons.rpc.outpatient.PrescriptionForm;
import cn.abcyun.cis.commons.rpc.outpatient.PrescriptionFormDelivery;
import org.apache.commons.lang.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * 空中药房chargeForm的构造，全在这个类里面
 * 还有几个函数因为外部在chargeform构造后，在外部还在调用，暂时无法移动进来，散落在外面
 */
public class ChargeFormFactoryAirPharmacy extends ChargeFormFactoryBase {
    private static final Logger sLogger = LoggerFactory.getLogger(ChargeFormFactoryBase.class);

    /**
     * calculate 路径上来的chargeForm的生成
     */
    @Override
    public ChargeForm createChargeFormFromCalculate(String chargeSheetId, String patientOrderId, String clinicId, String chainId,
                                                    ChargeFormReq chargeFormReq,
                                                    boolean isCanUpdateUsageInfo,
                                                    boolean generateIdIfNotExisted,
                                                    boolean forceGenerateNewId,
                                                    String operatorId) {
        ChargeForm chargeForm = super.createChargeFormFromCalculate(chargeSheetId, patientOrderId, clinicId, chainId, chargeFormReq, isCanUpdateUsageInfo, generateIdIfNotExisted, forceGenerateNewId, operatorId);
        chargeForm.setSpecification(chargeFormReq.getSpecification());

        chargeForm.setChargeAirPharmacyLogistics(LogisticsFactory.createAirPharmacyLogisticsFromReq(chargeForm.getChargeSheetId(),
                chargeForm.getId(),
                chargeForm.getClinicId(),
                chargeForm.getChainId(),
                chargeFormReq.getDeliveryInfo(),
                operatorId));
        //诊断信息
        chargeForm.setChargeAirPharmacyMedicalRecord(convertToChargeAirPharmacyMedicalRecord(chargeForm, chargeFormReq.getMedicalRecord(), operatorId));
        chargeForm.setVendorId(chargeFormReq.getVendorId());
        chargeForm.setVendorName(chargeFormReq.getVendorName());
        chargeForm.setMedicineStateScopeId(chargeFormReq.getMedicineStateScopeId());
        chargeForm.setUsageScopeId(chargeFormReq.getUsageScopeId());
        chargeForm.setExpectedTotalPrice(chargeFormReq.getExpectedTotalPrice());
        chargeForm.setExpectedPriceFlag(chargeFormReq.getExpectedPriceFlag());
        chargeForm.setTotalPrice(chargeFormReq.getTotalPrice());

        chargeForm.setDeliveryRule(chargeFormReq.getDeliveryRule());
        chargeForm.setProcessRule(chargeFormReq.getProcessRule());
        chargeForm.setVendorUsageScopeId(chargeFormReq.getVendorUsageScopeId());
        bindProductInfoForAirPharmacyExpressDelivery(chargeForm);
        return chargeForm;
    }

    /**
     * 【门诊】 空中药房chargeForm的初始化代码
     */
    @Override
    protected ChargeForm newChargeFormFromPatientPrescriptionImpl(String chargeSheetId, String patientOrderId, String clinicId, String chainId,
                                                                  PrescriptionForm prescriptionForm,
                                                                  MedicalRecord outpatientMedicalRecord,
                                                                  String doctorId,
                                                                  String operatorId) {
        ChargeForm chargeForm = super.newChargeFormFromPatientPrescriptionImpl(chargeSheetId, patientOrderId, clinicId, chainId,
                prescriptionForm, outpatientMedicalRecord, doctorId,
                operatorId);

        //立即改成空中药房状态的chargeForm
        chargeForm.setSourceFormType(Constants.SourceFormType.AIR_PHARMACY);
        chargeForm.setPharmacyType(GoodsConst.PharmacyType.AIR_PHARMACY);
        //空中药房需要特殊构造的地方
        chargeForm.setChargeAirPharmacyMedicalRecord(initChargeAirPharmacyMedicalRecord(chargeForm, outpatientMedicalRecord, doctorId, operatorId));

        //空中药房 部分议价的特殊处理
        chargeForm.setExpectedTotalPrice(prescriptionForm.getExpectedTotalPrice());
        chargeForm.setExpectedPriceFlag(ChargeForm.EXPECTED_PART);

        chargeForm.setVendorId(prescriptionForm.getVendorId());
        chargeForm.setVendorName(prescriptionForm.getVendorName());

        //保存门诊过来的快递信息
        PrescriptionFormDelivery prescriptionFormDelivery = prescriptionForm.getPrescriptionFormDelivery();
        createOrUpdateAirPharmacyLogisticsFromPrescriptionDelivery(chargeForm, prescriptionFormDelivery, operatorId);

        return chargeForm;
    }

    /**
     * 【门诊】空中药房chargeForm需要额外再插入两条快递和加工费的chargeFormItem
     */
    @Override
    protected void newChargeFormFromPatientPrescriptionCreateItemsImpl(ChargeForm chargeForm, String chargeSheetId, String patientOrderId, String clinicId, String chainId,
                                                                       PrescriptionForm prescriptionForm,
                                                                       CalculateModel calculateModel,
                                                                       MedicalRecord outpatientMedicalRecord,
                                                                       String doctorId,
                                                                       String doctorDepartmentId,
                                                                       String operatorId) {
        super.newChargeFormFromPatientPrescriptionCreateItemsImpl(chargeForm, chargeSheetId, patientOrderId, clinicId, chainId,
                prescriptionForm,
                calculateModel,
                outpatientMedicalRecord,
                doctorId,
                doctorDepartmentId,
                operatorId);

        //空中药房chargeForm 插入快递和加工费记录
        insertOriginalDeliveryAndProcessAndIngredientChargeFormItem(chargeForm, prescriptionForm, operatorId);
    }

    /**
     * 空中药房总价的计算
     */
    @Override
    protected void initRawTotalPrice(ChargeForm chargeForm) {
        if (chargeForm.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY  //因为update那里chargeForm可以来回转化
                && chargeForm.getExpectedTotalPrice() != null) {
            chargeForm.setTotalPrice(chargeForm.getExpectedTotalPrice());
        } else {
            super.initRawTotalPrice(chargeForm);
        }
    }

    /**
     * 【空中药房ChargeFormItem】都特殊标记下
     */
    @Override
    public ChargeFormItem generateChargeFormItem(ChargeForm chargeForm, int pharmacyType, ChargeFormItemReq chargeFormItemReq, boolean generateIdIfNotExisted, boolean forceGenerateNewId, String operatorId) {
        ChargeFormItem chargeFormItem = super.generateChargeFormItem(chargeForm, pharmacyType, chargeFormItemReq, generateIdIfNotExisted, forceGenerateNewId, operatorId);
        chargeFormItem.setIsAirPharmacy(1);
        return chargeFormItem;
    }

    //构造空中药房的快递信息
    @Override
    public void bindProductInfoForAirPharmacyExpressDelivery(ChargeForm chargeForm) {
        super.bindProductInfoForAirPharmacyExpressDelivery(chargeForm);
        ChargeAirPharmacyLogistics deliveryInfo = chargeForm.getChargeAirPharmacyLogistics();
        if (CollectionUtils.isEmpty(chargeForm.getChargeFormItems()) || deliveryInfo == null) {
            return;
        }

        chargeForm.getChargeFormItems().stream()
                .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.EXPRESS_DELIVERY)
                .filter(item -> item.getIsDeleted() == 0)
                .forEach(chargeFormItem -> {
                    chargeFormItem.setProductSnapshot(JsonUtils.dump(deliveryInfo));
                    chargeFormItem.setProductInfo(JsonUtils.dumpAsJsonNode(deliveryInfo));
                });
    }

    //构造空中药房的治疗记录
    private ChargeAirPharmacyMedicalRecord convertToChargeAirPharmacyMedicalRecord(ChargeForm chargeForm, ChargeAirPharmacyMedicalRecordReq medicalRecordReq, String operatorId) {

        if (medicalRecordReq == null) {
            return null;
        }

        ChargeAirPharmacyMedicalRecord medicalRecord = new ChargeAirPharmacyMedicalRecord();
        BeanUtils.copyProperties(medicalRecordReq, medicalRecord);

        medicalRecord.setChainId(chargeForm.getChainId());
        medicalRecord.setClinicId(chargeForm.getClinicId());
        medicalRecord.setId(StringUtils.isEmpty(medicalRecordReq.getId()) ? AbcIdUtils.getUID() : medicalRecordReq.getId());
        medicalRecord.setChargeSheetId(chargeForm.getChargeSheetId());
        medicalRecord.setChargeFormId(chargeForm.getId());
        medicalRecord.setIsNeedInsert(1);
        FillUtils.fillCreatedBy(medicalRecord, operatorId);
        return medicalRecord;
    }

    /**
     * 这也是一个转化函数 outpatientMedicalRecord->ChargeAirPharmacyMedicalRecord ，不要被函数名字迷惑，不是什么插入操作函数
     */
    public static ChargeAirPharmacyMedicalRecord initChargeAirPharmacyMedicalRecord(ChargeForm chargeForm, MedicalRecord outpatientMedicalRecord, String doctorId, String operatorId) {

        if (chargeForm == null) {
            return null;
        }
        ChargeAirPharmacyMedicalRecord medicalRecord = new ChargeAirPharmacyMedicalRecord();

        medicalRecord.setId(AbcIdUtils.getUID());
        medicalRecord.setClinicId(chargeForm.getClinicId());
        medicalRecord.setChainId(chargeForm.getChainId());
        medicalRecord.setChargeSheetId(chargeForm.getChargeSheetId());
        medicalRecord.setChargeFormId(chargeForm.getId());
        medicalRecord.setDoctorAdvice(Optional.ofNullable(outpatientMedicalRecord).map(MedicalRecord::getDoctorAdvice).orElse(""));
        medicalRecord.setIsNeedInsert(1);

        FillUtils.fillCreatedBy(medicalRecord, operatorId);

        return medicalRecord;
    }

    public static void updateChargeAirPharmacyMedicalRecordForOutpatient(ChargeAirPharmacyMedicalRecord medicalRecord, MedicalRecord outpatientMedicalRecord, String doctorId, String operatorId) {

        if (medicalRecord == null || outpatientMedicalRecord == null) {
            return;
        }

        medicalRecord.setDoctorAdvice(outpatientMedicalRecord.getDoctorAdvice());

        FillUtils.fillLastModifiedBy(medicalRecord, operatorId);
    }

    /**
     * Code move from ChargeFormService
     * 这个函数会给chargeSheet里面的空中药房的chargeForm插入两条快递和加工费的chargeFormItem
     *
     * @param chargeForm 生成的收费单，里面有空中药房的chargeForm
     */
    //TODO robinsli 业务逻辑上将从门诊推过来的chargeSheet不需要急着去算加工费和快递费，只要插入两条记录就可以了
    public static void insertOriginalDeliveryAndProcessAndIngredientChargeFormItem(ChargeForm chargeForm, PrescriptionForm prescriptionForm, String operatorId) {
        if (chargeForm == null || CollectionUtils.isEmpty(chargeForm.getChargeFormItems())) {
            return;
        }
        //过滤空中药房的chargeForm
        BigDecimal deliveryPrice = Optional.ofNullable(prescriptionForm).map(PrescriptionForm::getPrescriptionFormDelivery)
                .map(PrescriptionFormDelivery::getDeliveryFee)
                .orElse(BigDecimal.ZERO);
        BigDecimal processPrice = Optional.ofNullable(prescriptionForm).map(PrescriptionForm::getProcessPrice).orElse(BigDecimal.ZERO);
        BigDecimal ingredientPrice = Optional.ofNullable(prescriptionForm).map(PrescriptionForm::getIngredientPrice).orElse(BigDecimal.ZERO);
        int ORDER_SORT_DELIVERY = 0; //排序
        int ORDER_SORT_PROCESS = 1;

        ChargeFormItem airPharmacyDeliveryCFI = null;
        ChargeFormItem airPharmacyProcessCFI = null;
        ChargeFormItem airPharmacyIngredientCFI = null;
        for (ChargeFormItem chargeFormItem : chargeForm.getChargeFormItems()) {

            if (chargeFormItem.getIsDeleted() == 1) {
                continue;
            }

            if (chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.UNCHARGED) {
                continue;
            }
            //找第一个
            if (chargeFormItem.getProductType() == Constants.ProductType.EXPRESS_DELIVERY
                    && airPharmacyDeliveryCFI == null) {
                airPharmacyDeliveryCFI = chargeFormItem;
            }
            //找第一个
            if (chargeFormItem.getProductType() == Constants.ProductType.PROCESS
                    && airPharmacyProcessCFI == null) {
                airPharmacyProcessCFI = chargeFormItem;
            }
            if (chargeFormItem.getProductType() == Constants.ProductType.INGREDIENT && airPharmacyIngredientCFI == null) {
                airPharmacyIngredientCFI = chargeFormItem;
            }

        }

        //快递费只有不存在的时候写入就行，存在时不需要更新
        if (airPharmacyDeliveryCFI == null) {
            airPharmacyDeliveryCFI = ChargeFormItemFactory.insertChargeFormItem(chargeForm, "", Constants.SystemProductId.EXPRESS_DELIVERY_PRODUCT_ID,
                    Constants.ProductType.EXPRESS_DELIVERY, 0, ComposeType.NOT_COMPOSE, null,
                    Constants.SystemProductId.EXPRESS_DELIVERY_NAME,  // 名字
                    "", BigDecimal.ONE,
                    deliveryPrice, deliveryPrice, deliveryPrice, //价格
                    null, BigDecimal.ZERO, null, null, null, BigDecimal.ONE, BigDecimal.ZERO, "", JsonUtils.dump(chargeForm.getChargeAirPharmacyLogistics()), null, 0,
                    ORDER_SORT_DELIVERY, null, null, Constants.SourceItemType.NORMAL, //快递费排前面
                    GoodsConst.PharmacyType.AIR_PHARMACY, 0, GoodsConst.FeeComposeType.FEE_TYPE_NORMAL, GoodsConst.FeeTypeId.FEE_TYPE_ID_MEDICINE_SERVICE, GoodsFeeType.FEE_OWN,
                    null, operatorId); //操作人
            chargeForm.getChargeFormItems().add(airPharmacyDeliveryCFI);
        } else {
            airPharmacyDeliveryCFI.setUnitPrice(deliveryPrice)
                    .setSourceUnitPrice(deliveryPrice)
                    .setSourceTotalPrice(deliveryPrice);
        }

        if (airPharmacyProcessCFI == null && !ObjectUtils.equals(Constants.MedicineStateScopeId.ziJian + "", chargeForm.getMedicineStateScopeId())) {
            airPharmacyProcessCFI = ChargeFormItemFactory.insertChargeFormItem(chargeForm, "", Constants.SystemProductId.PROCESS_PRODUCT_ID,
                    Constants.ProductType.PROCESS, 0, ComposeType.NOT_COMPOSE, null,
                    Constants.SystemProductId.PROCESS_NAME, "", BigDecimal.ONE,
                    processPrice, processPrice, processPrice,
                    null, BigDecimal.ZERO, null, null, null, BigDecimal.ONE, BigDecimal.ZERO, "", null, null, 0,
                    ORDER_SORT_PROCESS, null, null, Constants.SourceItemType.NORMAL,// 排序
                    GoodsConst.PharmacyType.AIR_PHARMACY, 0, GoodsConst.FeeComposeType.FEE_TYPE_NORMAL, GoodsConst.FeeTypeId.FEE_TYPE_ID_MEDICINE_SERVICE, GoodsFeeType.FEE_OWN,
                    null, operatorId);
            chargeForm.getChargeFormItems().add(airPharmacyProcessCFI);
        } else if (airPharmacyProcessCFI != null){
            airPharmacyProcessCFI.setUnitPrice(processPrice)
                    .setSourceUnitPrice(processPrice)
                    .setSourceTotalPrice(processPrice);
        }

        if (airPharmacyProcessCFI != null && ObjectUtils.equals(Constants.MedicineStateScopeId.ziJian + "", chargeForm.getMedicineStateScopeId())) {
            airPharmacyProcessCFI.deleteModel(operatorId);
        }

        if (airPharmacyIngredientCFI == null) {
            chargeForm.getChargeFormItems().add(ChargeFormItemFactory.insertIngredientFormItem(chargeForm, ingredientPrice, operatorId));
        } else {
            airPharmacyIngredientCFI.setUnitPrice(ingredientPrice)
                    .setSourceUnitPrice(ingredientPrice)
                    .setSourceTotalPrice(ingredientPrice);
        }
    }


}
