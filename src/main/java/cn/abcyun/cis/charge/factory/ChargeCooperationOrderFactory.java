package cn.abcyun.cis.charge.factory;

import cn.abcyun.cis.charge.amqp.model.ChargeCoPharmacyOrderMessage;
import cn.abcyun.cis.charge.base.ChargeServiceError;
import cn.abcyun.cis.charge.base.ChargeServiceException;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.model.ChargeCooperationOrder;
import cn.abcyun.cis.charge.model.ChargeCooperationOrderItem;
import cn.abcyun.cis.charge.repository.ChargeCooperationOrderItemRepository;
import cn.abcyun.cis.charge.repository.ChargeCooperationOrderRepository;
import cn.abcyun.cis.charge.util.*;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.common.log.marker.AbcLogMarker;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.http.util.Asserts;

import java.time.Instant;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class ChargeCooperationOrderFactory {


    public static void insertOrUpdateChargeCooperationOrder(ChargeCoPharmacyOrderMessage message,
                                                            int sourceType,
                                                            List<ChargeCooperationOrder> chargeCooperationOrders,
                                                            ChargeCooperationOrderRepository chargeCooperationOrderRepository,
                                                            ChargeCooperationOrderItemRepository chargeCooperationOrderItemRepository) {
        InsertOrUpdateChargeCooperationOrderResult result = insertOrUpdateChargeCooperationOrderCore(message, sourceType, chargeCooperationOrders);

        //保存已删除的数据，同时从已存在的列表中移除掉，外部使用不需要考虑已删除的数据
        List<ChargeCooperationOrderItem> removedOrderItems = result.getRemovedOrderItems();
        List<ChargeCooperationOrder> removedOrders = result.getRemovedOrders();
        if (CollectionUtils.isNotEmpty(removedOrders)) {
            chargeCooperationOrderRepository.saveAll(removedOrders);
        }
        if (CollectionUtils.isNotEmpty(removedOrderItems)) {
            chargeCooperationOrderItemRepository.saveAll(removedOrderItems);
        }
    }

    public static void checkChargeCooperationOrderChanged(ChargeCooperationOrder chargeCooperationOrder, String chargeSheetId) {
        if (Objects.isNull(chargeCooperationOrder)) {
            log.error("chargeCooperationOrder is null, chargeSheetId: {}", chargeSheetId);
            throw new ChargeServiceException(ChargeServiceError.COOPERATION_ORDER_IS_DELETED);
        }

        //校验数据是否发生变化
        String extractDataSignature = chargeCooperationOrder.getExtractDataSignature();

        String currentDataSignature = ChargeCooperationOrderFactory.generateCooperationOrderDataSignature(chargeCooperationOrder);

        if (!Objects.equals(extractDataSignature, currentDataSignature)) {
            throw new ChargeServiceException(ChargeServiceError.COOPERATION_ORDER_CHANGED);
        }
    }

    @Data
    @Accessors(chain = true)
    public static class InsertOrUpdateChargeCooperationOrderResult {

        private List<ChargeCooperationOrder> chargeCooperationOrders;

        private List<ChargeCooperationOrderItem> removedOrderItems;

        private List<ChargeCooperationOrder> removedOrders;

    }

    public static InsertOrUpdateChargeCooperationOrderResult insertOrUpdateChargeCooperationOrderCore(ChargeCoPharmacyOrderMessage message,
                                                                int sourceType,
                                                                List<ChargeCooperationOrder> chargeCooperationOrders) {
        Asserts.notNull(message, "message cannot be null");

        if (message.getCooperationOrders() == null) {
            message.setCooperationOrders(new ArrayList<>());
        }

        filterCannotBeModifiedOrder(message, chargeCooperationOrders);

        BiFunction<ChargeCoPharmacyOrderMessage.ChargeCooperationOrderMessage, ChargeCooperationOrder, Boolean> isEqualKeyFunc = (chargeCooperationOrderMessage, chargeCooperationOrder) -> Objects.equals(chargeCooperationOrderMessage.getSourceFormId(), chargeCooperationOrder.getSourceFormId());

        Function<ChargeCoPharmacyOrderMessage.ChargeCooperationOrderMessage, ChargeCooperationOrder> insertFunc = chargeCooperationOrderMessage -> generateChargeCooperationOrder(message, sourceType, chargeCooperationOrderMessage);

        Function<ChargeCooperationOrder, Boolean> deleteFunc = chargeCooperationOrder -> {
            chargeCooperationOrder.setIsDeleted(1)
                    .setSourceLastModifiedBy(message.getSourceOperatorId())
                    .setSourceLastModifiedName(message.getSourceOperatorName())
                    .setSourceLastModified(Instant.now())
                    .setLastModified(Instant.now())
                    .setLastModifiedBy(Constants.DEFAULT_OPERATORID);

            Optional.ofNullable(chargeCooperationOrder.getOrderItems())
                    .orElse(new ArrayList<>())
                    .forEach(chargeCooperationOrderItem -> chargeCooperationOrderItem.setIsDeleted(1)
                            .setSourceLastModifiedBy(message.getSourceOperatorId())
                            .setSourceLastModifiedName(message.getSourceOperatorName())
                            .setLastModified(Instant.now())
                            .setSourceLastModified(Instant.now())
                            .setLastModifiedBy(Constants.DEFAULT_OPERATORID));

            return false;
        };

        BiConsumer<ChargeCoPharmacyOrderMessage.ChargeCooperationOrderMessage, ChargeCooperationOrder> updateFunc = (chargeCooperationOrderMessage, chargeCooperationOrder) -> updateChargeCooperationOrder(message, chargeCooperationOrderMessage, chargeCooperationOrder);

        MergeTool.doMerge(message.getCooperationOrders(),
                chargeCooperationOrders,
                isEqualKeyFunc,
                insertFunc,
                deleteFunc,
                updateFunc);

        //保存已删除的数据，同时从已存在的列表中移除掉，外部使用不需要考虑已删除的数据
        List<ChargeCooperationOrderItem> removedOrderItems = new ArrayList<>();

        //将已删除的从chargeCooperationOrders中移除掉
        chargeCooperationOrders.forEach(chargeCooperationOrder -> {
            removedOrderItems.addAll(chargeCooperationOrder.getOrderItems()
                    .stream()
                    .filter(chargeCooperationOrderItem -> chargeCooperationOrderItem.getIsDeleted() == 1)
                    .collect(Collectors.toList()));

            chargeCooperationOrder.getOrderItems().removeIf(chargeCooperationOrderItem -> chargeCooperationOrderItem.getIsDeleted() == 1);
        });

        chargeCooperationOrders.forEach(chargeCooperationOrder -> {
            if (CollectionUtils.isEmpty(chargeCooperationOrder.getOrderItems())) {
                chargeCooperationOrder.setIsDeleted(1);
            }
        });

        List<ChargeCooperationOrder> removedOrders = chargeCooperationOrders.stream()
                .filter(chargeCooperationOrder -> chargeCooperationOrder.getIsDeleted() == 1)
                .collect(Collectors.toList());

        chargeCooperationOrders.removeIf(chargeCooperationOrder -> chargeCooperationOrder.getIsDeleted() == 1);

        return new InsertOrUpdateChargeCooperationOrderResult()
                .setChargeCooperationOrders(chargeCooperationOrders)
                .setRemovedOrders(removedOrders)
                .setRemovedOrderItems(removedOrderItems);
    }

    private static void updateChargeCooperationOrder(ChargeCoPharmacyOrderMessage message, ChargeCoPharmacyOrderMessage.ChargeCooperationOrderMessage chargeCooperationOrderMessage, ChargeCooperationOrder chargeCooperationOrder) {

        chargeCooperationOrder
                .setSourcePatientId(message.getSourcePatientId())
                .setSourcePatientInfo(message.getSourcePatientInfo())
                .setSourceClinicName(message.getSourceClinicName())
                .setSourceDoctorId(message.getSourceDoctorId())
                .setSourceDoctorName(chargeCooperationOrderMessage.getSourceDoctorName())
                .setExtendDiagnosisInfos(chargeCooperationOrderMessage.getExtendDiagnosisInfos())
                .setSpecification(chargeCooperationOrderMessage.getSpecification())
                .setPharmacyType(chargeCooperationOrderMessage.getPharmacyType())
                .setPharmacyNo(chargeCooperationOrderMessage.getPharmacyNo())
                .setSourceFormType(chargeCooperationOrder.getSourceFormType())
                .setUsageInfo(chargeCooperationOrderMessage.getUsageInfo())
                .setSourceLastModifiedName(message.getSourceOperatorName())
                .setSourceLastModifiedBy(message.getSourceOperatorId())
                .setSourceLastModified(Instant.now())
                .setLastModified(Instant.now())
                .setLastModifiedBy(Constants.DEFAULT_OPERATORID);

        if (chargeCooperationOrderMessage.getCooperationOrderItems() == null) {
            chargeCooperationOrderMessage.setCooperationOrderItems(new ArrayList<>());
        }

        if (chargeCooperationOrder.getOrderItems() == null) {
            chargeCooperationOrder.setOrderItems(new ArrayList<>());
        }

        Instant created = Instant.now();

        BiFunction<ChargeCoPharmacyOrderMessage.ChargeCooperationOrderItemMessage, ChargeCooperationOrderItem, Boolean> isEqualKeyFunc = (orderItemMessage, orderItem) -> Objects.equals(orderItemMessage.getSourceItemId(), orderItem.getSourceItemId());

        Function<ChargeCoPharmacyOrderMessage.ChargeCooperationOrderItemMessage, ChargeCooperationOrderItem> insertFunc = orderItemMessage -> ChargeCooperationOrderItemFactory.generateChargeCooperationOrderItem(chargeCooperationOrder, orderItemMessage, created, message.getSourceOperatorId(), message.getSourceOperatorName());

        Function<ChargeCooperationOrderItem, Boolean> deleteFunc = orderItem -> {
            orderItem.setIsDeleted(1)
                    .setSourceLastModifiedName(message.getSourceOperatorName())
                    .setSourceLastModifiedBy(message.getSourceOperatorId())
                    .setSourceLastModified(Instant.now())
                    .setLastModified(created)
                    .setLastModifiedBy(Constants.DEFAULT_OPERATORID);
            return false;
        };

        BiConsumer<ChargeCoPharmacyOrderMessage.ChargeCooperationOrderItemMessage, ChargeCooperationOrderItem> updateFunc = (orderItemMessage, orderItem) -> ChargeCooperationOrderItemFactory.updateChargeCooperationOrderItem(orderItemMessage, orderItem, created, message.getSourceOperatorId(), message.getSourceOperatorName());

        MergeTool.doMerge(chargeCooperationOrderMessage.getCooperationOrderItems(),
                chargeCooperationOrder.getOrderItems(),
                isEqualKeyFunc,
                insertFunc,
                deleteFunc,
                updateFunc);

        //如果item全部删除，则将order也标记为删除
        if (CollectionUtils.isEmpty(chargeCooperationOrder.getOrderItems())
                || chargeCooperationOrder.getOrderItems()
                .stream()
                .allMatch(orderItem -> orderItem.getIsDeleted() == 1)) {
            chargeCooperationOrder.setIsDeleted(1)
                    .setSourceLastModifiedName(message.getSourceOperatorName())
                    .setSourceLastModifiedBy(message.getSourceOperatorId())
                    .setSourceLastModified(Instant.now())
                    .setLastModified(created)
                    .setLastModifiedBy(Constants.DEFAULT_OPERATORID);
        }

        chargeCooperationOrder.buildAbstractInfo();
    }

    public static String generateCooperationOrderDataSignature(ChargeCooperationOrder chargeCooperationOrder) {
        Map<String, String> keyData = Optional.ofNullable(chargeCooperationOrder)
                .map(ChargeCooperationOrder::getOrderItems)
                .orElse(new ArrayList<>())
                .stream()
                .filter(cooperationOrderItem -> cooperationOrderItem.getIsDeleted() == 0)
                .collect(Collectors.toMap(ChargeCooperationOrderItem::getId,
                        orderItem -> String.format("%s-%d-%d-%d",
                                TextUtils.alwaysString(orderItem.getGoodsId()),
                                MathUtils.wrapBigDecimalOrZero(orderItem.getUnitCount()).intValue(),
                                MathUtils.wrapBigDecimalOrZero(orderItem.getDoseCount()).intValue(),
                                orderItem.getPharmacyNo()),
                        (a, b) -> a));
        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "generateCooperationOrderDataSignatureData: {}", JsonUtils.dump(keyData));
        return ChargeUtils.signMap(keyData);
    }

    /**
     * 先过滤掉已提单的数据和已付款的数据，不能被修改
     *
     * @param message
     * @param chargeCooperationOrders
     */
    private static void filterCannotBeModifiedOrder(ChargeCoPharmacyOrderMessage message, List<ChargeCooperationOrder> chargeCooperationOrders) {

        List<String> cannotBeModifiedOrderIds = chargeCooperationOrders.stream()
                //已收费的不能被修改
                .filter(chargeCooperationOrder -> chargeCooperationOrder.getStatus() != ChargeCooperationOrder.Status.UNCHARGED && chargeCooperationOrder.getStatus() != ChargeCooperationOrder.Status.CLOSED)
                .map(ChargeCooperationOrder::getSourceFormId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(cannotBeModifiedOrderIds)) {
            return;
        }

        chargeCooperationOrders.removeIf(chargeCooperationOrder -> cannotBeModifiedOrderIds.contains(chargeCooperationOrder.getSourceFormId()));
        message.getCooperationOrders().removeIf(chargeCooperationOrderMessage -> cannotBeModifiedOrderIds.contains(chargeCooperationOrderMessage.getSourceFormId()));
    }

    private static ChargeCooperationOrder generateChargeCooperationOrder(ChargeCoPharmacyOrderMessage message, int sourceType, ChargeCoPharmacyOrderMessage.ChargeCooperationOrderMessage chargeCooperationOrderMessage) {

        if (CollectionUtils.isEmpty(chargeCooperationOrderMessage.getCooperationOrderItems())) {
            return null;
        }

        Instant created = Instant.now();

        ChargeCooperationOrder chargeCooperationOrder = new ChargeCooperationOrder();
        chargeCooperationOrder.setId(AbcIdUtils.getUID())
                .setChainId(message.getChainId())
                .setClinicId(message.getClinicId())
                .setSourcePatientId(message.getSourcePatientId())
                .setSourcePatientInfo(message.getSourcePatientInfo())
                .setSourceChainId(message.getSourceChainId())
                .setSourceClinicId(message.getSourceClinicId())
                .setSourceFormId(chargeCooperationOrderMessage.getSourceFormId())
                .setSourceSheetId(message.getSourceSheetId())
                .setSourcePatientOrderId(message.getSourcePatientOrderId())
                .setSourcePatientOrder(message.getSourcePatientOrder())
                .setSourceClinicName(message.getSourceClinicName())
                .setSourceDoctorId(chargeCooperationOrderMessage.getSourceDoctorId())
                .setSourceDoctorName(chargeCooperationOrderMessage.getSourceDoctorName())
                .setSourceFormType(chargeCooperationOrderMessage.getSourceFormType())
                .setSpecification(chargeCooperationOrderMessage.getSpecification())
                .setSourceType(sourceType)
                .setPharmacyType(chargeCooperationOrderMessage.getPharmacyType())
                .setPharmacyNo(chargeCooperationOrderMessage.getPharmacyNo())
                .setExtendDiagnosisInfos(chargeCooperationOrderMessage.getExtendDiagnosisInfos())
                .setUsageInfo(chargeCooperationOrderMessage.getUsageInfo())
                .setRelateChargeSheetId(null)
                .setExtractStatus(ChargeCooperationOrder.ExtractStatus.NON_EXTRACT)
                .setStatus(ChargeCooperationOrder.Status.UNCHARGED)
                .setSourceCreatedBy(message.getSourceOperatorId())
                .setSourceCreatedName(message.getSourceOperatorName())
                .setSourceLastModified(created)
                .setSourceLastModifiedBy(message.getSourceOperatorId())
                .setSourceLastModifiedName(message.getSourceOperatorName());

        FillUtils.fillCreatedBy(chargeCooperationOrder, Constants.DEFAULT_OPERATORID, created);

        chargeCooperationOrder.setOrderItems(chargeCooperationOrderMessage.getCooperationOrderItems()
                .stream()
                .map(orderItemMessage -> ChargeCooperationOrderItemFactory.generateChargeCooperationOrderItem(chargeCooperationOrder, orderItemMessage, created, message.getSourceOperatorId(), message.getSourceOperatorName()))
                .collect(Collectors.toList())
        );

        chargeCooperationOrder.buildAbstractInfo();

        return chargeCooperationOrder;

    }
}
