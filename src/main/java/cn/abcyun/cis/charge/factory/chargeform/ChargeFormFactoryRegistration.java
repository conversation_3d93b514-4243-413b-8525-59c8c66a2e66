package cn.abcyun.cis.charge.factory.chargeform;

import cn.abcyun.cis.charge.api.model.ChargeFormItemReq;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.model.ChargeForm;
import cn.abcyun.cis.charge.model.ChargeFormItem;
import cn.abcyun.cis.charge.util.JsonUtils;
import cn.abcyun.cis.commons.rpc.registration.RegistrationFormItem;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class ChargeFormFactoryRegistration extends ChargeFormFactoryBase {

    /**
     * 挂号费构造item
     * @param chargeForm
     * @param pharmacyType
     * @param chargeFormItemReq
     * @param generateIdIfNotExisted
     * @param forceGenerateNewId
     * @return
     */
    @Override
    public List<ChargeFormItem> convertToChargeFormItem(ChargeForm chargeForm, int pharmacyType, ChargeFormItemReq chargeFormItemReq, boolean generateIdIfNotExisted, boolean forceGenerateNewId, String operatorId) {
        List<ChargeFormItem> chargeFormItems = super.convertToChargeFormItem(chargeForm, pharmacyType, chargeFormItemReq, generateIdIfNotExisted, forceGenerateNewId, operatorId);

        if (CollectionUtils.isEmpty(chargeFormItems)) {
            return new ArrayList<>();
        }

        //将挂号医生更新到productInfo上
        ChargeFormItem chargeFormItem = chargeFormItems
                .stream()
                .filter(item -> item.getProductType() == Constants.ProductType.REGISTRATION)
                .findFirst()
                .orElse(null);

        if (Objects.isNull(chargeFormItem)) {
            return chargeFormItems;
        }

        ChargeFormItemReq.DoctorInfoReq doctorInfoReq = chargeFormItemReq.getDoctorInfo();

        if (Objects.isNull(doctorInfoReq)) {
            return chargeFormItems;
        }

        RegistrationFormItem registrationFormItem = new RegistrationFormItem();
        registrationFormItem.setDoctorId(doctorInfoReq.getDoctorId());
        registrationFormItem.setDepartmentId(doctorInfoReq.getDepartmentId());
        registrationFormItem.setFee(chargeFormItemReq.getSourceUnitPrice());

        chargeFormItem.setProductInfo(JsonUtils.dumpAsJsonNode(registrationFormItem));
        chargeFormItem.setProductSnapshot(JsonUtils.dump(registrationFormItem));

        return chargeFormItems;
    }

}
