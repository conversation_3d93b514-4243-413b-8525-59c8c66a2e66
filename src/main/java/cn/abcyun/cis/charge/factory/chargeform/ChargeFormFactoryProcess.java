package cn.abcyun.cis.charge.factory.chargeform;

import cn.abcyun.cis.charge.api.model.ChargeFormReq;
import cn.abcyun.cis.charge.api.model.ProcessInfoReq;
import cn.abcyun.cis.charge.model.ChargeForm;
import cn.abcyun.cis.charge.model.ChargeSheetProcessInfo;
import cn.abcyun.cis.charge.util.AbcIdUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

@Slf4j
public class ChargeFormFactoryProcess extends ChargeFormFactoryBase {

    /**
     * calculate 路径上来的chargeForm的生成
     */
    @Override
    public ChargeForm createChargeFormFromCalculate(String chargeSheetId,
                                                    String patientOrderId,
                                                    String clinicId,
                                                    String chainId,
                                                    ChargeFormReq chargeFormReq,
                                                    boolean isCanUpdateUsageInfo,
                                                    boolean generateIdIfNotExisted,
                                                    boolean forceGenerateNewId,
                                                    String operatorId) {
        ChargeForm chargeForm = super.createChargeFormFromCalculate(chargeSheetId, patientOrderId, clinicId, chainId, chargeFormReq, isCanUpdateUsageInfo, generateIdIfNotExisted, forceGenerateNewId, operatorId);
        if (chargeFormReq.getProcessInfo() != null) {
            chargeForm.setProcessInfo(convertToChargeSheetProcessInfo(chargeForm, chargeFormReq.getProcessInfo()));
        }
        return chargeForm;
    }

    public static ChargeSheetProcessInfo convertToChargeSheetProcessInfo(ChargeForm chargeForm, ProcessInfoReq processInfoReq) {

        if (processInfoReq == null) {
            return null;
        }

        ChargeSheetProcessInfo processInfo = new ChargeSheetProcessInfo();
        String id = StringUtils.isNotEmpty(processInfoReq.getId()) ? processInfoReq.getId() : AbcIdUtils.getUID();
        return processInfo.setId(id)
                .setKeyId(processInfoReq.getKeyId())
                .setChainId(chargeForm.getChainId())
                .setClinicId(chargeForm.getClinicId())
                .setChargeSheetId(chargeForm.getChargeSheetId())
                .setChargeFormId(processInfoReq.getChargeFormId())
                .setProcessFormId(chargeForm.getId())
                .setType(processInfoReq.getType())
                .setSubType(processInfoReq.getSubType())
                .setBagUnitCount(processInfoReq.getBagUnitCount())
                .setBagUnitCountDecimal(processInfoReq.getProcessBagUnitCountDecimal())
                .setProcessRemark(processInfoReq.getProcessRemark())
                .setTotalProcessCount(processInfoReq.getTotalProcessCount())
                .setChecked(processInfoReq.getChecked())
                .setTakeMedicationTime(processInfoReq.getTakeMedicationTime());
    }
}
