package cn.abcyun.cis.charge.factory.logistics;

import cn.abcyun.cis.charge.api.model.ChargeAirPharmacyLogisticsReq;
import cn.abcyun.cis.charge.model.ChargeAirPharmacyLogistics;
import cn.abcyun.cis.charge.model.ChargeForm;
import cn.abcyun.cis.charge.util.AbcIdUtils;
import cn.abcyun.cis.commons.util.FillUtils;
import cn.abcyun.cis.commons.util.TextUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;

/**
 * 空中药房快递信息的构造，更新全部收敛到本类中
 *
 * @robins 2020-07
 */
public class LogisticsFactory {


    /**
     * DTOConverter.convertToChargeAirPharmacyLogistics
     */
    public static ChargeAirPharmacyLogistics createAirPharmacyLogisticsFromReq(String chargeSheetId,
                                                                               String chargeFormId,
                                                                               String clinicId,
                                                                               String chainId,
                                                                               ChargeAirPharmacyLogisticsReq req,
                                                                               String operatorId
    ) {
        if (req == null || (TextUtils.isEmpty(req.getAddressCityId()) && TextUtils.isEmpty(req.getAddressProvinceId()) && TextUtils.isEmpty(req.getAddressDistrictId()))) {
            return null;
        }

        ChargeAirPharmacyLogistics airPharmacyLogistics = new ChargeAirPharmacyLogistics();
        BeanUtils.copyProperties(req, airPharmacyLogistics, "id");
        airPharmacyLogistics.setId(StringUtils.isEmpty(req.getId()) ? AbcIdUtils.getUID() : req.getId());
        airPharmacyLogistics.setChainId(chainId);
        airPharmacyLogistics.setClinicId(clinicId);
        airPharmacyLogistics.setChargeSheetId(chargeSheetId);
        airPharmacyLogistics.setChargeFormId(chargeFormId);
        airPharmacyLogistics.setDeliveryNo(req.getDeliveryNo());
        if (req.getDeliveryCompany() != null) {
            airPharmacyLogistics.setDeliveryCompanyId(req.getDeliveryCompany().getId());
        }
        airPharmacyLogistics.setIsNeedInsert(1);
        FillUtils.fillCreatedBy(airPharmacyLogistics, operatorId);
        return airPharmacyLogistics;
    }

    /**
     * 空中药房快递信息的转化
     * 1整合ChargeFormService.insertOrUpdateChargeAirPharmacyLogistics 逻辑完全一样，update参数传false
     * 2整合 ChargeAirPharmacyLogisticsService.updateChargeAirPharmacyLogistics 57行左右的代码
     */
    public static void airPharmacyDeliveryInfoUpdate(ChargeForm chargeFom, ChargeAirPharmacyLogistics logisticsReq, String operatorId, boolean update) {
        ChargeAirPharmacyLogistics existLogistics = chargeFom.getChargeAirPharmacyLogistics();

        if (chargeFom == null || logisticsReq == null) {
            return;
        }

        //更新
        if (existLogistics != null && logisticsReq != null) {

            existLogistics.setAddressProvinceId(logisticsReq.getAddressProvinceId());
            existLogistics.setAddressProvinceName(logisticsReq.getAddressProvinceName());
            existLogistics.setAddressCityId(logisticsReq.getAddressCityId());
            existLogistics.setAddressCityName(logisticsReq.getAddressCityName());
            existLogistics.setAddressDistrictId(logisticsReq.getAddressDistrictId());
            existLogistics.setAddressDistrictName(logisticsReq.getAddressDistrictName());
            existLogistics.setAddressDetail(logisticsReq.getAddressDetail());
            existLogistics.setDeliveryName(logisticsReq.getDeliveryName());
            existLogistics.setDeliveryMobile(logisticsReq.getDeliveryMobile());
            existLogistics.setDeliveryCompanyId(logisticsReq.getDeliveryCompanyId());
            existLogistics.setDeliveryNo(logisticsReq.getDeliveryNo());
            //拷贝遗漏
            existLogistics.setDeliveryCompany(logisticsReq.getDeliveryCompany()); //加上了这一行就和
            FillUtils.fillLastModifiedBy(existLogistics, operatorId);
            return;
        }
        //构造拷贝，只拷贝部分
        if (!update) {
            ChargeAirPharmacyLogistics newLogistics = new ChargeAirPharmacyLogistics();
            BeanUtils.copyProperties(logisticsReq, newLogistics, "id");


            newLogistics.setId(AbcIdUtils.getUID());
            newLogistics.setChainId(chargeFom.getChainId());
            newLogistics.setClinicId(chargeFom.getClinicId());
            newLogistics.setChargeSheetId(chargeFom.getChargeSheetId());
            newLogistics.setChargeFormId(chargeFom.getId());
            newLogistics.setIsNeedInsert(1);

            cn.abcyun.cis.charge.util.FillUtils.fillCreatedBy(newLogistics, operatorId);

            chargeFom.setChargeAirPharmacyLogistics(newLogistics);
        }

    }

}
