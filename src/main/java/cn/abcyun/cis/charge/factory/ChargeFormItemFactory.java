package cn.abcyun.cis.charge.factory;

import cn.abcyun.bis.rpc.sdk.cis.model.charge.GoodsFeeType;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsItem;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.TraceableCode;
import cn.abcyun.bis.rpc.sdk.cis.model.medicalplan.MedicalPlanFormItem;
import cn.abcyun.cis.charge.api.model.ChargeDeliveryReq;
import cn.abcyun.cis.charge.api.model.OutpatientSheetDetailCalculateReq;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.model.*;
import cn.abcyun.cis.charge.processor.ExpectedPriceHelper;
import cn.abcyun.cis.charge.processor.calculate.CalculateModel;
import cn.abcyun.cis.charge.service.dto.OnlineConsultationItemInfo;
import cn.abcyun.cis.charge.util.*;
import cn.abcyun.cis.commons.model.ComposeType;
import cn.abcyun.cis.commons.rpc.goods.GoodsBatchInfo;
import cn.abcyun.cis.commons.rpc.outpatient.PrescriptionForm;
import cn.abcyun.cis.commons.rpc.outpatient.PrescriptionFormItem;
import cn.abcyun.cis.commons.rpc.outpatient.ProductFormItem;
import cn.abcyun.cis.commons.rpc.outpatient.UsageInfo;
import cn.abcyun.cis.commons.rpc.registration.RegistrationFormItem;
import cn.abcyun.cis.commons.rpc.registration.RegistrationFormItemFeeDetail;
import cn.abcyun.cis.commons.util.TextUtils;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

public class ChargeFormItemFactory {
    public static ChargeFormItem insertRegistrationFormItem(ChargeForm chargeForm, RegistrationFormItem registrationFormItem, List<ChargeFormItem> chargeFormItems, String operatorId) {

        List<ChargeFormItem> chargeFormItemList = new ArrayList<>();
        if (Objects.isNull(registrationFormItem)) {
            return null;
        }
        String name = TextUtils.isEmpty(registrationFormItem.getDoctorName()) ? Constants.SystemProductId.REGISTRATION_NAME : registrationFormItem.getDoctorName();
        BigDecimal expectedPrice = MathUtils.wrapBigDecimalCompare(registrationFormItem.getSourceFee(), registrationFormItem.getFee()) == 0 ? null : registrationFormItem.getFee();
        Pair<String, Integer> pair = RegistrationUtils.registrationCategoryConvertToGoodsInfo(registrationFormItem.getRegistrationCategory());
        ChargeFormItem chargeFormItem = insertChargeFormItem(chargeForm, registrationFormItem.getId(), pair.getLeft(),
                Constants.ProductType.REGISTRATION, pair.getRight(), ComposeType.NOT_COMPOSE, null, name, "次", BigDecimal.ONE, registrationFormItem.getSourceFee(), registrationFormItem.getSourceFee(), null,
                expectedPrice, BigDecimal.ZERO, expectedPrice, null, null, BigDecimal.ONE, BigDecimal.ZERO, "", JsonUtils.dump(registrationFormItem), null, 0, 0,
                null, null, Constants.SourceItemType.NORMAL, GoodsConst.PharmacyType.LOCAL_PHARMACY, 0, GoodsConst.FeeComposeType.FEE_TYPE_NORMAL, registrationFormItem.getFeeTypeId(), GoodsFeeType.FEE_OWN, null, operatorId);
        chargeFormItemList.add(chargeFormItem);

        if (CollectionUtils.isNotEmpty(registrationFormItem.getFeeDetails())) {
            chargeFormItem.setGoodsFeeType(GoodsFeeType.FEE_PARENT);
            chargeFormItem.setFeeComposeType(GoodsConst.FeeComposeType.FEE_TYPE_COMPOSE_FEE);


            List<ChargeFormItem> children = registrationFormItem.getFeeDetails().stream().map(itemFeeDetail -> {
                GoodsItem registrationChildGoodsItem = ChargeFormItemUtils.convertGoodsItemFromRegistrationChildGoodsSnap(itemFeeDetail);
                ChargeFormItem chargeFormItemChild = insertRegistrationFormItemFeeDetail(chargeForm, registrationFormItem, itemFeeDetail, registrationChildGoodsItem, operatorId);
                chargeFormItemChild.setComposeParentFormItemId(chargeFormItem.getId());
                chargeFormItemList.add(chargeFormItemChild);
                return chargeFormItemChild;
            }).collect(Collectors.toList());


            ExpectedPriceHelper.processCompose(chargeFormItem,
                    children,
                    chargeFormItem.getSourceUnitPrice(),
                    chargeFormItem.getSourceTotalPrice(),
                    null,
                    chargeFormItem.getExpectedUnitPrice(),
                    chargeFormItem.getExpectedTotalPrice(),
                    chargeFormItem.getExpectedTotalPriceRatio());
            chargeFormItems.addAll(children);
        } else {
            chargeFormItem.setGoodsFeeType(GoodsFeeType.FEE_OWN);
            chargeFormItem.setFeeComposeType(GoodsConst.FeeComposeType.FEE_TYPE_NORMAL);
        }

        return chargeFormItem;
    }


    public static ChargeFormItem insertRegistrationFormItemFeeDetail(ChargeForm chargeForm, RegistrationFormItem registrationFormItem, RegistrationFormItemFeeDetail detail, GoodsItem registrationChildGoodsItem, String operatorId) {
        ChargeFormItem chargeFormItem = insertChargeFormItem(chargeForm, detail.getId() + "", detail.getGoodsId(),
                detail.getGoodsType(), detail.getGoodsSubType(), ComposeType.NOT_COMPOSE, registrationFormItem.getId(), detail.getGoodsName(), detail.getUnit(), detail.getUnitCount(), detail.getUnitPrice(), detail.getUnitPrice(), null,
                null, BigDecimal.ZERO, null, null, null, BigDecimal.ONE, BigDecimal.ZERO, "", JsonUtils.dump(registrationChildGoodsItem), null, detail.getIsDismounting(), 0,
                null, null, Constants.SourceItemType.NORMAL, GoodsConst.PharmacyType.LOCAL_PHARMACY, 0, detail.getFeeComposeType(), detail.getFeeTypeId(), GoodsFeeType.FEE_CHILD, null, operatorId);

        chargeFormItem.setUnitCostPrice(Optional.ofNullable(detail).map(RegistrationFormItemFeeDetail::getCostPrice).orElse(BigDecimal.ZERO));
        chargeFormItem.calculateTotalCostPrice();
        return chargeFormItem;
    }

    public static ChargeFormItem generateRegistrationFormItem(ChargeForm chargeForm, OutpatientSheetDetailCalculateReq.RegistrationInfoReq registrationInfoReq, String operatorId) {

        RegistrationFormItem registrationFormItem = new RegistrationFormItem();
        registrationFormItem.setDoctorId(registrationInfoReq.getDoctorId());
        registrationFormItem.setDepartmentId(registrationInfoReq.getDepartmentId());
        registrationFormItem.setFee(registrationInfoReq.getUnitPrice());

        return insertChargeFormItem(chargeForm, null, Constants.SystemProductId.REGISTRATION_PRODUCT_ID,
                Constants.ProductType.REGISTRATION, 0, ComposeType.NOT_COMPOSE, null, "", "", BigDecimal.ONE, registrationInfoReq.getUnitPrice(), registrationInfoReq.getUnitPrice(), null,
                null, BigDecimal.ZERO, null, null, null, BigDecimal.ONE, BigDecimal.ZERO, "", JsonUtils.dump(registrationFormItem), null, 0, 0,
                null, null, Constants.SourceItemType.NORMAL, GoodsConst.PharmacyType.LOCAL_PHARMACY, 0, GoodsConst.FeeComposeType.FEE_TYPE_NORMAL, null, GoodsFeeType.FEE_OWN, null, operatorId);
    }

    public static void updateRegistrationFormItemByRegistrationInfoReq(ChargeForm chargeForm, OutpatientSheetDetailCalculateReq.RegistrationInfoReq registrationInfoReq, String operatorId) {

        if (Objects.isNull(chargeForm) || CollectionUtils.isEmpty(chargeForm.getChargeFormItems())) {
            return;
        }

        chargeForm.getChargeFormItems()
                        .forEach(chargeFormItem -> {
                            chargeFormItem.setStatus(Constants.ChargeFormItemStatus.UNCHARGED);
                            FillUtils.fillLastModifiedBy(chargeFormItem, operatorId);
                            if (chargeFormItem.getProductType() != Constants.ProductType.REGISTRATION) {
                                return;
                            }
                            chargeFormItem.setUnitPrice(registrationInfoReq.getUnitPrice());
                            chargeFormItem.setSourceUnitPrice(registrationInfoReq.getUnitPrice());
                            RegistrationFormItem registrationFormItem = JsonUtils.readValue(chargeFormItem.getProductSnapshot(), RegistrationFormItem.class);
                            if (registrationFormItem == null) {
                                registrationFormItem = new RegistrationFormItem();
                            }

                            chargeFormItem.setUnitCostPrice(registrationFormItem.getCostUnitPrice());
                            chargeFormItem.calculateTotalCostPrice();
                            if (StringUtils.isNotBlank(registrationInfoReq.getDoctorId())) {
                                registrationFormItem.setDoctorId(registrationInfoReq.getDoctorId());
                            }
                            if (StringUtils.isNotBlank(registrationInfoReq.getDepartmentId())) {
                                registrationFormItem.setDepartmentId(registrationInfoReq.getDepartmentId());
                            }
                            registrationFormItem.setFee(registrationInfoReq.getUnitPrice());
                            chargeFormItem.setProductSnapshot(JsonUtils.dump(registrationFormItem));
                            chargeFormItem.setProductInfo(JsonUtils.dumpAsJsonNode(registrationFormItem));


                        });


    }

    public static ChargeFormItem insertOnlineConsultationFormItem(ChargeForm chargeForm, String doctorId, String doctorName, BigDecimal fee, String operatorId) {
        String name = TextUtils.isEmpty(doctorName) ? "咨询费" : String.format("咨询费-%s", doctorName);

        OnlineConsultationItemInfo itemInfo = new OnlineConsultationItemInfo();
        itemInfo.setDoctorId(doctorId);
        itemInfo.setClinicId(chargeForm.getClinicId());

        return insertChargeFormItem(chargeForm, "", Constants.SystemProductId.ONLINE_CONSULTATION_PRODUCT_ID,
                Constants.ProductType.ONLINE_CONSULTATION, 0, ComposeType.NOT_COMPOSE, null, name, "", BigDecimal.ONE, fee, fee, null,
                null, BigDecimal.ZERO, null, null, null, BigDecimal.ONE, BigDecimal.ZERO, "", JsonUtils.dump(itemInfo), null, 0, 0,
                null, null, Constants.SourceItemType.NORMAL, GoodsConst.PharmacyType.LOCAL_PHARMACY, 0,
                GoodsConst.FeeComposeType.FEE_TYPE_NORMAL, GoodsConst.FeeTypeId.FEE_TYPE_ID_ONLINE_CONSULTATION, GoodsFeeType.FEE_OWN, null, operatorId);
    }

    public static ChargeFormItem insertFamilyDoctorSignFormItem(ChargeForm chargeForm, BigDecimal fee, String servicePackageName, JsonNode familyDoctorInfo, String operatorId) {
        String name = StringUtils.isNotEmpty(servicePackageName) ? String.format("%s-%s", Constants.SystemProductId.FAMILY_DOCTOR_SIGN_NAME, servicePackageName) : Constants.SystemProductId.FAMILY_DOCTOR_SIGN_NAME;
        return insertChargeFormItem(chargeForm, "", Constants.SystemProductId.FAMILY_DOCTOR_SIGN_ID,
                Constants.ProductType.FAMILY_DOCTOR_SIGN, 0, ComposeType.NOT_COMPOSE, null, name, "", BigDecimal.ONE, fee, fee, null,
                null, BigDecimal.ZERO, null, null, null, BigDecimal.ONE, BigDecimal.ZERO, "", JsonUtils.dump(familyDoctorInfo), null, 0, 0,
                null, null, Constants.SourceItemType.NORMAL, GoodsConst.PharmacyType.LOCAL_PHARMACY, 0,
                GoodsConst.FeeComposeType.FEE_TYPE_NORMAL, GoodsConst.FeeTypeId.FEE_TYPE_ID_FAMILY_DOCTOR_SIGN, GoodsFeeType.FEE_OWN, null, operatorId);
    }

    public static ChargeFormItem insertPromotionCardOpenFormItem(ChargeForm chargeForm, BigDecimal fee, String operatorId) {
        return insertChargeFormItem(chargeForm, "", Constants.SystemProductId.PROMOTION_CARD_OPEN_ID,
                Constants.ProductType.PROMOTION_CARD_OPEN, 0, ComposeType.NOT_COMPOSE, null, Constants.SystemProductId.PROMOTION_CARD_OPEN_NAME, "", BigDecimal.ONE, fee, fee, null,
                null, BigDecimal.ZERO, null, null, null, BigDecimal.ONE, BigDecimal.ZERO, "", null, null, 0, 0,
                null, null, Constants.SourceItemType.NORMAL, GoodsConst.PharmacyType.LOCAL_PHARMACY, 0,
                GoodsConst.FeeComposeType.FEE_TYPE_NORMAL, GoodsConst.FeeTypeId.FEE_TYPE_ID_PROMOTION_CARD_OPEN, GoodsFeeType.FEE_OWN, null, operatorId);
    }

    public static ChargeFormItem insertMemberCardRechargeFormItem(ChargeForm chargeForm, BigDecimal fee, String operatorId) {
        return insertChargeFormItem(chargeForm, "", Constants.SystemProductId.MEMBER_CARD_RECHARGE_ID,
                Constants.ProductType.MEMBER_CARD_RECHARGE, 0, ComposeType.NOT_COMPOSE, null, Constants.SystemProductId.MEMBER_CARD_RECHARGE_NAME, "", BigDecimal.ONE, fee, fee, null,
                null, BigDecimal.ZERO, null, null, null, BigDecimal.ONE, BigDecimal.ZERO, "", null, null, 0, 0,
                null, null, Constants.SourceItemType.NORMAL, GoodsConst.PharmacyType.LOCAL_PHARMACY, 0, GoodsConst.FeeComposeType.FEE_TYPE_NORMAL, GoodsConst.FeeTypeId.FEE_TYPE_ID_MEMBER_CARD_RECHARGE, GoodsFeeType.FEE_OWN, null, operatorId);
    }


    public static ChargeFormItem insertPromotionCardRechargeFormItem(ChargeForm chargeForm, BigDecimal fee, String operatorId) {
        return insertChargeFormItem(chargeForm, "", Constants.SystemProductId.PROMOTION_CARD_RECHARGE_ID,
                Constants.ProductType.PROMOTION_CARD_RECHARGE, 0, ComposeType.NOT_COMPOSE, null, Constants.SystemProductId.PROMOTION_CARD_RECHARGE_NAME, "", BigDecimal.ONE, fee, fee, null,
                null, BigDecimal.ZERO, null, null, null, BigDecimal.ONE, BigDecimal.ZERO, "", null, null, 0, 0,
                null, null, Constants.SourceItemType.NORMAL, GoodsConst.PharmacyType.LOCAL_PHARMACY, 0,
                GoodsConst.FeeComposeType.FEE_TYPE_NORMAL, GoodsConst.FeeTypeId.FEE_TYPE_ID_PROMOTION_CARD_RECHARGE, GoodsFeeType.FEE_OWN, null, operatorId);
    }

    public static ChargeFormItem insertExpressDeliveryFormItem(ChargeForm chargeForm, ChargeDeliveryReq deliveryInfo, String operatorId) {
        String name = Constants.SystemProductId.EXPRESS_DELIVERY_NAME;
        BigDecimal deliveryFee = Optional.ofNullable(deliveryInfo).map(ChargeDeliveryReq::getDeliveryFee).orElse(BigDecimal.ZERO);
        return insertChargeFormItem(chargeForm, "", Constants.SystemProductId.EXPRESS_DELIVERY_PRODUCT_ID,
                Constants.ProductType.EXPRESS_DELIVERY, 0, ComposeType.NOT_COMPOSE, null, name, "", BigDecimal.ONE, deliveryFee, deliveryFee, null,
                null, BigDecimal.ZERO, null, null, null, BigDecimal.ONE, BigDecimal.ZERO, "", JsonUtils.dump(deliveryInfo), null, 0, 0,
                null, null, Constants.SourceItemType.NORMAL, GoodsConst.PharmacyType.LOCAL_PHARMACY, 0,
                GoodsConst.FeeComposeType.FEE_TYPE_NORMAL, GoodsConst.FeeTypeId.FEE_TYPE_ID_MEDICINE_SERVICE, GoodsFeeType.FEE_OWN, null, operatorId);
    }

    /**
     * 本地药房的加工费
     */
    public static ChargeFormItem insertProcessFormItem(ChargeForm chargeForm, String operatorId) {
        String name = Constants.SystemProductId.PROCESS_NAME;
        return insertChargeFormItem(chargeForm, "", Constants.SystemProductId.PROCESS_PRODUCT_ID,
                Constants.ProductType.PROCESS, 0, ComposeType.NOT_COMPOSE, null, name, "", BigDecimal.ONE, BigDecimal.ZERO, BigDecimal.ZERO, null,
                null, BigDecimal.ZERO, null, null, null, BigDecimal.ONE, BigDecimal.ZERO, "", null, null, 0, 0,
                null, null, Constants.SourceItemType.NORMAL, GoodsConst.PharmacyType.LOCAL_PHARMACY, 0,
                GoodsConst.FeeComposeType.FEE_TYPE_NORMAL, GoodsConst.FeeTypeId.FEE_TYPE_ID_MEDICINE_SERVICE, GoodsFeeType.FEE_OWN, null, operatorId);
    }

    /**
     * 空中药房加工费
     */
    public static ChargeFormItem insertProcessFormItem(ChargeForm chargeForm, BigDecimal processFee, String operatorId) {
        String name = Constants.SystemProductId.PROCESS_NAME;
        return insertChargeFormItem(chargeForm, "", Constants.SystemProductId.PROCESS_PRODUCT_ID,
                Constants.ProductType.PROCESS, 0, ComposeType.NOT_COMPOSE, null, name, "", BigDecimal.ONE, processFee, processFee, null,
                null, BigDecimal.ZERO, null, null, null, BigDecimal.ONE, BigDecimal.ZERO, "", null, null, 0, 0,
                null, null, Constants.SourceItemType.NORMAL, GoodsConst.PharmacyType.AIR_PHARMACY, 0, GoodsConst.FeeComposeType.FEE_TYPE_NORMAL,
                GoodsConst.FeeTypeId.FEE_TYPE_ID_MEDICINE_SERVICE, GoodsFeeType.FEE_OWN, null, operatorId);
    }

    /**
     * 空中药房辅料费
     */
    public static ChargeFormItem insertIngredientFormItem(ChargeForm chargeForm, BigDecimal ingredientFee, String operatorId) {
        String name = Constants.SystemProductId.INGREDIENT_NAME;
        return insertChargeFormItem(chargeForm, "", Constants.SystemProductId.INGREDIENT_PRODUCT_ID,
                Constants.ProductType.INGREDIENT, 0, ComposeType.NOT_COMPOSE, null, name, "", BigDecimal.ONE, ingredientFee, ingredientFee, ingredientFee,
                null, BigDecimal.ZERO, null, null, null, BigDecimal.ONE, BigDecimal.ZERO, "", null, null, 0, 0,
                null, null, Constants.SourceItemType.NORMAL, GoodsConst.PharmacyType.AIR_PHARMACY, 0,
                GoodsConst.FeeComposeType.FEE_TYPE_NORMAL, GoodsConst.FeeTypeId.FEE_TYPE_ID_MEDICINE_SERVICE, GoodsFeeType.FEE_OWN, null, operatorId);
    }

    public static ChargeFormItem updateRegistrationFormItemFeeDetail(ChargeFormItem chargeFormItem, RegistrationFormItem registrationFormItem, RegistrationFormItemFeeDetail feeDetail, GoodsItem registrationChildGoodsItem, String operatorId) {
        if (chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.UNSELECTED) {
            chargeFormItem.setStatus(Constants.ChargeFormItemStatus.UNCHARGED);
        }
        updateChargeFormItem(chargeFormItem, feeDetail.getGoodsId(), feeDetail.getGoodsType(), feeDetail.getGoodsSubType(), feeDetail.getGoodsName(), feeDetail.getUnit(),
                feeDetail.getUnitCount(), feeDetail.getUnitPrice(), feeDetail.getUnitPrice(), null, null, BigDecimal.ZERO, null, null, null, BigDecimal.ONE,
                BigDecimal.ZERO, "", JsonUtils.dump(registrationChildGoodsItem), null, feeDetail.getIsDismounting(), 0, null, null,
                Constants.SourceItemType.NORMAL, GoodsConst.PharmacyType.LOCAL_PHARMACY, 0, feeDetail.getFeeComposeType(), feeDetail.getFeeTypeId(), GoodsFeeType.FEE_CHILD, null, operatorId);

        if (Objects.nonNull(chargeFormItem)) {
            chargeFormItem.setUnitCostPrice(Optional.ofNullable(feeDetail).map(RegistrationFormItemFeeDetail::getCostPrice).orElse(BigDecimal.ZERO));
            chargeFormItem.calculateTotalCostPrice();
        }

        return chargeFormItem;
    }

    public static ChargeFormItem updateRegistrationFormItem(ChargeForm chargeForm,
                                                            ChargeFormItem chargeFormItem,
                                                            RegistrationFormItem registrationFormItem,
                                                            List<ChargeFormItem> chargeFormItems,
                                                            String operatorId) {
        // 只有在未支付的情况下可以更新
        if (chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.UNCHARGED && chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.UNSELECTED) {
            return chargeFormItem;
        }

        //门诊过来的数据可能有变动，这里强制把状态设置为待收
        if (chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.UNSELECTED) {
            chargeFormItem.setStatus(Constants.ChargeFormItemStatus.UNCHARGED);
        }

        BigDecimal expectedPrice = MathUtils.wrapBigDecimalCompare(registrationFormItem.getSourceFee(), registrationFormItem.getFee()) == 0 ? null : registrationFormItem.getFee();
        String name = TextUtils.isEmpty(registrationFormItem.getDoctorName()) ? Constants.SystemProductId.REGISTRATION_NAME : registrationFormItem.getDoctorName();
        Pair<String, Integer> pair = RegistrationUtils.registrationCategoryConvertToGoodsInfo(registrationFormItem.getRegistrationCategory());
        updateChargeFormItem(chargeFormItem, pair.getLeft(), Constants.ProductType.REGISTRATION, pair.getRight(), name, "",
                BigDecimal.ONE, registrationFormItem.getSourceFee(), registrationFormItem.getSourceFee(), null, expectedPrice, BigDecimal.ZERO, expectedPrice, null, null, BigDecimal.ONE,
                BigDecimal.ZERO, "", JsonUtils.dump(registrationFormItem), null, 0, 0, null, null,
                Constants.SourceItemType.NORMAL, GoodsConst.PharmacyType.LOCAL_PHARMACY, 0, GoodsConst.FeeComposeType.FEE_TYPE_NORMAL, registrationFormItem.getFeeTypeId(), GoodsFeeType.FEE_OWN, null, operatorId);
        insertChargeFormItemAdditionalIfNeed(chargeFormItem);
        ChargeFormItemAdditional additional = chargeFormItem.getAdditional();
        updateChargeFormItemAdditional(additional,
                registrationFormItem.getDoctorId(),
                null,
                registrationFormItem.getDepartmentId(),
                null,
                null,
                Objects.nonNull(additional) ? additional.getTraceableCodeList() : null,
                null, null);

        /**
         * 如果含有子项则需要更新子项内容
         */
        Map<String, List<ChargeFormItem>> childrenMap = ListUtils.groupByKey(chargeForm.getChargeFormItems().stream()
                .filter(item -> item.getIsDeleted() == 0)
                .filter(item -> Objects.nonNull(item.getComposeParentFormItemId())).collect(Collectors.toList()), ChargeFormItem::getComposeParentFormItemId);
        List<ChargeFormItem> children = childrenMap.getOrDefault(chargeFormItem.getId(), new ArrayList<>());

        if (CollectionUtils.isNotEmpty(children) || CollectionUtils.isNotEmpty(registrationFormItem.getFeeDetails())) {
            chargeFormItem.setGoodsFeeType(GoodsFeeType.FEE_PARENT);
            chargeFormItem.setFeeComposeType(GoodsConst.FeeComposeType.FEE_TYPE_COMPOSE_FEE);
            BiFunction<RegistrationFormItemFeeDetail, ChargeFormItem, Boolean> isEqualKeyFunc = (RegistrationFormItemFeeDetail registrationFormItemFeeDetail, ChargeFormItem chargeFormItem1) -> TextUtils.equals(chargeFormItem1.getSourceFormItemId(), registrationFormItemFeeDetail.getId() + "");
            Function<RegistrationFormItemFeeDetail, ChargeFormItem> insertFunc = itemFeeDetail -> {
                GoodsItem registrationChildGoodsItem = ChargeFormItemUtils.convertGoodsItemFromRegistrationChildGoodsSnap(itemFeeDetail);
                ChargeFormItem chargeFormItemChild = insertRegistrationFormItemFeeDetail(chargeForm, registrationFormItem, itemFeeDetail, registrationChildGoodsItem, operatorId);
                chargeFormItemChild.setComposeParentFormItemId(chargeFormItem.getId());
                chargeFormItems.add(chargeFormItemChild);
                return chargeFormItemChild;
            };

            Function<ChargeFormItem, Boolean> deleteFunc = chargeFormItem1 -> ChargeFormItemFactory.deleteFeeParentChargeFormItem(chargeFormItem1, chargeFormItems, operatorId);

            BiConsumer<RegistrationFormItemFeeDetail, ChargeFormItem> updateFunc = (itemFeeDetail, chargeFormItem1) -> {
                GoodsItem registrationChildGoodsItem = ChargeFormItemUtils.convertGoodsItemFromRegistrationChildGoodsSnap(itemFeeDetail);
                updateRegistrationFormItemFeeDetail(chargeFormItem1, registrationFormItem, itemFeeDetail, registrationChildGoodsItem, operatorId);
            };

            MergeTool<RegistrationFormItemFeeDetail, ChargeFormItem> mergeTool = new MergeTool<>();
            mergeTool.setSrc(registrationFormItem.getFeeDetails())
                    .setDst(children)
                    .setIsEqualKeyFunc(isEqualKeyFunc)
                    .setInsertFunc(insertFunc)
                    .setDeleteFunc(deleteFunc)
                    .setUpdateFunc(updateFunc);
            mergeTool.doMerge();

            ExpectedPriceHelper.processCompose(chargeFormItem,
                    children,
                    chargeFormItem.getSourceUnitPrice(),
                    chargeFormItem.getSourceTotalPrice(),
                    null,
                    chargeFormItem.getExpectedUnitPrice(),
                    chargeFormItem.getExpectedTotalPrice(),
                    chargeFormItem.getExpectedTotalPriceRatio());
        }


        return chargeFormItem;
    }

    /**
     * 这也是一个转化函数 prescriptionFormItem->ChargeFormItem ，不要被函数名字迷惑，不是什么插入操作函数
     *
     * @param chargeForm ，prescriptionForm 为构造ChargeFormItem参数需要 而传入
     */
    /**
     * 这也是一个转化函数 prescriptionFormItem->ChargeFormItem ，不要被函数名字迷惑，不是什么插入操作函数
     *
     * @param chargeForm ，prescriptionForm 为构造ChargeFormItem参数需要 而传入
     */
    public static ChargeFormItem insertPrescriptionFormItem(ChargeForm chargeForm, PrescriptionForm prescriptionForm, PrescriptionFormItem prescriptionFormItem, CalculateModel calculateModel, String doctorId, String doctorDepartmentId, String operatorId) {
        BigDecimal doseCount = new BigDecimal(prescriptionForm.getDoseCount());
        BigDecimal packageCount = (prescriptionForm.getType() == PrescriptionForm.TYPE_CHINESE) && ObjectUtils.compare(doseCount, BigDecimal.ONE) > 0 ? doseCount : BigDecimal.ONE;
        String name = !TextUtils.isEmpty(prescriptionFormItem.getName()) ? prescriptionFormItem.getName() : prescriptionFormItem.getMedicineCadn();
        String productId = !TextUtils.isEmpty(prescriptionFormItem.getGoodsId()) ? prescriptionFormItem.getGoodsId() : "";

        UsageInfo usageInfo = new UsageInfo();
        BeanUtils.copyProperties(prescriptionFormItem, usageInfo);

        int sourceItemType = prescriptionFormItem.getChargeType() == PrescriptionFormItem.ChargeType.NO_CHARGE ? Constants.SourceItemType.SELF_PROVIDED : 0;

        BigDecimal doctorSourceUnitPrice = null;
        BigDecimal doctorSourceTotalPrice = null;
        String unitAdjustmentFeeLastModifiedBy = null;
//        if (calculateModel == CalculateModel.KEEP_CURRENT) {
//            BigDecimal totalPrice = MathUtils.calculateTotalPrice(prescriptionFormItem.getUnitPrice(), MathUtils.calculateTotalCount(prescriptionFormItem.getUnitCount(), doseCount), 2).add(MathUtils.wrapBigDecimalOrZero(prescriptionFormItem.getFractionPrice()));
//            DoctorSourcePriceCell doctorSourcePriceCell = translateDoctorSourcePrice(prescriptionFormItem.getUnitPrice(), prescriptionFormItem.getSourceUnitPrice(), prescriptionFormItem.getFractionPrice(), totalPrice);
//            unitAdjustmentFeeLastModifiedBy = doctorSourcePriceCell.getExpectedTotalPrice() != null ? prescriptionFormItem.getUnitAdjustmentFeeLastModifiedBy() : null;
//            prescriptionFormItem.setExpectedTotalPrice(doctorSourcePriceCell.getExpectedTotalPrice());
//            prescriptionFormItem.setExpectedUnitPrice(doctorSourcePriceCell.getExpectedUnitPrice());
//            doctorSourceUnitPrice = doctorSourcePriceCell.getDoctorSourceUnitPrice();
//            doctorSourceTotalPrice = doctorSourcePriceCell.getDoctorSourceTotalPrice();
//        } else if (calculateModel == CalculateModel.NORMAL_RESET) {
        unitAdjustmentFeeLastModifiedBy = prescriptionFormItem.getUnitAdjustmentFeeLastModifiedBy();
//        }


        ChargeFormItem chargeFormItem = ChargeFormItemFactory.insertChargeFormItem(chargeForm, prescriptionFormItem.getId(), productId, prescriptionFormItem.getType(), prescriptionFormItem.getSubType(), ComposeType.NOT_COMPOSE, null, name, prescriptionFormItem.getUnit(),
                prescriptionFormItem.getUnitCount(), prescriptionFormItem.getUnitPrice(), prescriptionFormItem.getSourceUnitPrice(), prescriptionFormItem.getSourceTotalPrice(), prescriptionFormItem.getExpectedUnitPrice(), prescriptionFormItem.getFractionPrice(), prescriptionFormItem.getExpectedTotalPrice(),
                prescriptionFormItem.getTotalPriceRatio(), prescriptionFormItem.getExpectedTotalPriceRatio(),
                packageCount, BigDecimal.ZERO, JsonUtils.dump(usageInfo), JsonUtils.dump(prescriptionFormItem.getProductInfo()), prescriptionFormItem.getGroupId(),
                prescriptionFormItem.getUseDismounting(), prescriptionFormItem.getSort(), doctorSourceUnitPrice, doctorSourceTotalPrice, sourceItemType, prescriptionFormItem.getPharmacyType(),
                prescriptionFormItem.getPharmacyNo(), prescriptionFormItem.getFeeComposeType(), prescriptionFormItem.getFeeTypeId(), prescriptionFormItem.getGoodsFeeType(), Objects.toString(prescriptionFormItem.getLockId(), null), operatorId);

        if (chargeFormItem.getSourceItemType() == Constants.SourceItemType.NORMAL && CollectionUtils.isNotEmpty(prescriptionFormItem.getBatchInfos())) {
            ChargeFormItemBatchInfoUtils.insertOrUpdateChargeFormItemBatchInfosForGoodsBatchInfo(chargeFormItem, Optional.ofNullable(prescriptionFormItem.getBatchInfos().get(0))
                            .map(GoodsBatchInfo::getPieceNum)
                            .map(BigDecimal::new)
                            .orElse(null),
                    prescriptionFormItem.getBatchInfos()
                            .stream()
                            .map(ChargeFormItemBatchInfoUtils::convertCommonBatchInfoToSdkBatchInfo)
                            .collect(Collectors.toList()), operatorId);
        }

        insertChargeFormItemAdditionalIfNeed(chargeFormItem);
        updateChargeFormItemAdditional(chargeFormItem.getAdditional(),
                doctorId,
                null,
                doctorDepartmentId,
                null,
                null,
                null,//门诊单同步，不需要设置追溯码
                unitAdjustmentFeeLastModifiedBy,
                null);

        chargeFormItem.setPharmacyType(chargeForm.getPharmacyType());
        return chargeFormItem;
    }

    public static ChargeFormItem updatePrescriptionFormItem(ChargeForm chargeForm, PrescriptionForm prescriptionForm, CalculateModel calculateModel, ChargeFormItem chargeFormItem, PrescriptionFormItem prescriptionFormItem, String doctorId, String doctorDepartmentId, String operatorId) {
        //只有在未支付的情况下可以更新
        if (chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.UNCHARGED && chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.UNSELECTED) {
            return chargeFormItem;
        }

        //门诊过来的数据可能有变动，这里强制把状态设置为待收
        if (chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.UNSELECTED) {
            chargeFormItem.setStatus(Constants.ChargeFormItemStatus.UNCHARGED);
        }

        BigDecimal doseCount = new BigDecimal(prescriptionForm.getDoseCount());
        BigDecimal packageCount = (prescriptionForm.getType() == PrescriptionForm.TYPE_CHINESE) && ObjectUtils.compare(doseCount, BigDecimal.ONE) > 0 ? doseCount : BigDecimal.ONE;
        String name = !TextUtils.isEmpty(prescriptionFormItem.getName()) ? prescriptionFormItem.getName() : prescriptionFormItem.getMedicineCadn();
        String productId = !TextUtils.isEmpty(prescriptionFormItem.getGoodsId()) ? prescriptionFormItem.getGoodsId() : "";

        UsageInfo usageInfo = new UsageInfo();
        BeanUtils.copyProperties(prescriptionFormItem, usageInfo);

        BigDecimal doctorSourceUnitPrice = null;
        BigDecimal doctorSourceTotalPrice = null;
        String unitAdjustmentFeeLastModifiedBy = null;
//        if (calculateModel == CalculateModel.KEEP_CURRENT) {
//            BigDecimal totalPrice = MathUtils.calculateTotalPrice(prescriptionFormItem.getUnitPrice(), MathUtils.calculateTotalCount(prescriptionFormItem.getUnitCount(), doseCount), 2).add(MathUtils.wrapBigDecimalOrZero(prescriptionFormItem.getFractionPrice()));
//            DoctorSourcePriceCell doctorSourcePriceCell = translateDoctorSourcePrice(prescriptionFormItem.getUnitPrice(), prescriptionFormItem.getSourceUnitPrice(), prescriptionFormItem.getFractionPrice(), totalPrice);
//            unitAdjustmentFeeLastModifiedBy = doctorSourcePriceCell.getExpectedTotalPrice() != null ? prescriptionFormItem.getUnitAdjustmentFeeLastModifiedBy() : null;
//            prescriptionFormItem.setExpectedTotalPrice(doctorSourcePriceCell.getExpectedTotalPrice());
//            prescriptionFormItem.setExpectedUnitPrice(doctorSourcePriceCell.getExpectedUnitPrice());
//            doctorSourceUnitPrice = doctorSourcePriceCell.getDoctorSourceUnitPrice();
//            doctorSourceTotalPrice = doctorSourcePriceCell.getDoctorSourceTotalPrice();
//        } else if (calculateModel == CalculateModel.NORMAL_RESET) {
        unitAdjustmentFeeLastModifiedBy = prescriptionFormItem.getUnitAdjustmentFeeLastModifiedBy();
//        }

        int sourceItemType = prescriptionFormItem.getChargeType() == PrescriptionFormItem.ChargeType.NO_CHARGE ? Constants.SourceItemType.SELF_PROVIDED : 0;

        updateChargeFormItem(chargeFormItem, productId, prescriptionFormItem.getType(),
                prescriptionFormItem.getSubType(), name, prescriptionFormItem.getUnit(),
                prescriptionFormItem.getUnitCount(), prescriptionFormItem.getUnitPrice(), prescriptionFormItem.getSourceUnitPrice(), prescriptionFormItem.getSourceTotalPrice(),
                prescriptionFormItem.getExpectedUnitPrice(), prescriptionFormItem.getFractionPrice(), prescriptionFormItem.getExpectedTotalPrice(),
                prescriptionFormItem.getTotalPriceRatio(), prescriptionFormItem.getExpectedTotalPriceRatio(), packageCount,
                BigDecimal.ZERO, JsonUtils.dump(usageInfo), JsonUtils.dump(prescriptionFormItem.getProductInfo()),
                prescriptionFormItem.getGroupId(), prescriptionFormItem.getUseDismounting(),
                prescriptionFormItem.getSort(), doctorSourceUnitPrice, doctorSourceTotalPrice,
                sourceItemType, prescriptionFormItem.getPharmacyType(), prescriptionFormItem.getPharmacyNo(),
                prescriptionFormItem.getFeeComposeType(), prescriptionFormItem.getFeeTypeId(), prescriptionFormItem.getGoodsFeeType(), Objects.toString(prescriptionFormItem.getLockId(), null),
                operatorId);

        if (chargeFormItem.getSourceItemType() == Constants.SourceItemType.NORMAL && CollectionUtils.isNotEmpty(prescriptionFormItem.getBatchInfos())) {
            ChargeFormItemBatchInfoUtils.insertOrUpdateChargeFormItemBatchInfosForGoodsBatchInfo(chargeFormItem, Optional.ofNullable(prescriptionFormItem.getBatchInfos().get(0))
                            .map(GoodsBatchInfo::getPieceNum)
                            .map(BigDecimal::new)
                            .orElse(null),
                    prescriptionFormItem.getBatchInfos()
                            .stream()
                            .map(ChargeFormItemBatchInfoUtils::convertCommonBatchInfoToSdkBatchInfo)
                            .collect(Collectors.toList()), operatorId);
        } else {
            Optional.ofNullable(chargeFormItem.getChargeFormItemBatchInfos())
                    .orElse(new ArrayList<>())
                    .forEach(chargeFormItemBatchInfo -> chargeFormItemBatchInfo.deleteModel(operatorId));
        }

        insertChargeFormItemAdditionalIfNeed(chargeFormItem);
        ChargeFormItemAdditional additional = chargeFormItem.getAdditional();
        updateChargeFormItemAdditional(additional,
                doctorId,
                null,
                doctorDepartmentId,
                null,
                null,
                Objects.nonNull(additional) ? additional.getTraceableCodeList() : null,
                unitAdjustmentFeeLastModifiedBy,
                null);
        chargeFormItem.setIsAirPharmacy(chargeForm.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY ? 1 : 0);
        chargeFormItem.setPharmacyType(chargeForm.getPharmacyType());
        return chargeFormItem;
    }

    public static ChargeFormItem insertChargeFormItem(ChargeForm chargeForm,
                                                      String sourceFormItemId,
                                                      String productId,
                                                      int productType,
                                                      int productSubType,
                                                      int composeType,
                                                      String sourceComposeParentFormItemId,
                                                      String name,
                                                      String unit,
                                                      BigDecimal unitCount,
                                                      BigDecimal unitPrice,
                                                      BigDecimal sourceUnitPrice,
                                                      BigDecimal sourceTotalPrice,
                                                      BigDecimal expectedUnitPrice,
                                                      BigDecimal fractionPrice,
                                                      BigDecimal expectedTotalPrice,
                                                      BigDecimal totalPriceRatio,
                                                      BigDecimal expectedTotalPriceRatio,
                                                      BigDecimal doseCount,
                                                      BigDecimal discountPrice,
                                                      String usageInfo,
                                                      String productInfo,
                                                      Integer groupId,
                                                      int isDismounting,
                                                      int sort,
                                                      BigDecimal doctorSourceUnitPrice,
                                                      BigDecimal doctorSourceTotalPrice,
                                                      int sourceItemType,
                                                      int pharmacyType,
                                                      int pharmacyNo,
                                                      int feeComposeType,
                                                      Long feeTypeId,
                                                      int goodsFeeType,
                                                      String lockId,
                                                      String operatorId) {
        ChargeFormItem chargeFormItem = new ChargeFormItem();
        chargeFormItem.setId(AbcIdUtils.getUUID());
        chargeFormItem.setChargeFormId(chargeForm.getId());
        chargeFormItem.setChargeSheetId(chargeForm.getChargeSheetId());
        chargeFormItem.setPatientOrderId(chargeForm.getPatientOrderId());
        chargeFormItem.setClinicId(chargeForm.getClinicId());
        chargeFormItem.setChainId(chargeForm.getChainId());
        chargeFormItem.setSourceFormItemId(sourceFormItemId);
        chargeFormItem.setProductId(productId);
        chargeFormItem.setProductType(productType);
        chargeFormItem.setProductSubType(productSubType);
        chargeFormItem.setComposeType(composeType);
        if (composeType == ComposeType.COMPOSE) {
            chargeFormItem.setProductType(Constants.ProductType.COMPOSE_PRODUCT);
        }

        chargeFormItem.setSourceComposeParentFormItemId(sourceComposeParentFormItemId);
        chargeFormItem.setName(name);
        chargeFormItem.setStatus(Constants.ChargeFormItemStatus.UNCHARGED);
        chargeFormItem.setV2Status(Constants.ChargeFormItemStatus.UNCHARGED);
        chargeFormItem.setGroupId(groupId);
        chargeFormItem.setUnitCount(unitCount);
        chargeFormItem.setDoseCount(doseCount);
        chargeFormItem.setUnit(unit);
        chargeFormItem.setUnitPrice(unitPrice);
        chargeFormItem.setSourceUnitPrice(sourceUnitPrice);
        chargeFormItem.setSourceTotalPrice(sourceTotalPrice);
        chargeFormItem.setExpectedUnitPrice(expectedUnitPrice);
        chargeFormItem.setExpectedTotalPrice(expectedTotalPrice);
        chargeFormItem.setTotalPriceRatio(totalPriceRatio);
        chargeFormItem.setExpectedTotalPriceRatio(expectedTotalPriceRatio);
        chargeFormItem.setFractionPrice(fractionPrice);
//        if (chargeFormItem.getExpectedTotalPrice() != null) {
//            chargeFormItem.setExpectedTotalPrice(chargeFormItem.getExpectedTotalPrice().multiply(MathUtils.wrapBigDecimal(doseCount, BigDecimal.ONE)));
//        }

        chargeFormItem.setIsAirPharmacy(chargeForm.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY ? 1 : 0);
        chargeFormItem.setDiscountPrice(discountPrice);
        chargeFormItem.setUsageInfoJson(usageInfo);
        chargeFormItem.setProductSnapshot(productInfo);
        chargeFormItem.setUseDismounting(isDismounting);
        chargeFormItem.setSort(sort);
        chargeFormItem.setDoctorSourceUnitPrice(doctorSourceUnitPrice);
        chargeFormItem.setDoctorSourceTotalPrice(doctorSourceTotalPrice);
        chargeFormItem.setSourceItemType(sourceItemType);
        chargeFormItem.setPharmacyType(pharmacyType);
        chargeFormItem.setPharmacyNo(pharmacyNo);
        chargeFormItem.setFeeComposeType(feeComposeType);
        chargeFormItem.setFeeTypeId(feeTypeId);
        chargeFormItem.setGoodsFeeType(goodsFeeType);
        chargeFormItem.setLockId(lockId);

        // 保障中药入库一定拆零
        if (productType == Constants.ProductType.MEDICINE && productSubType == Constants.ProductType.SubType.MEDICINE_CHINESE) {
            chargeFormItem.setUseDismounting(1);
        }

        if (sourceItemType == Constants.SourceItemType.SELF_PROVIDED) {
            chargeFormItem.setUnitPrice(BigDecimal.ZERO);
            chargeFormItem.setTotalPrice(BigDecimal.ZERO);
            chargeFormItem.setFractionPrice(BigDecimal.ZERO);
            chargeFormItem.setExpectedUnitPrice(null);
            chargeFormItem.setExpectedTotalPrice(null);
            chargeFormItem.setExpectedTotalPriceRatio(null);
            chargeFormItem.setDoctorSourceUnitPrice(null);
            chargeFormItem.setDoctorSourceUnitPrice(null);
            chargeFormItem.calculateTotalPrice();
        } else {
            ExpectedPriceHelper.process(chargeFormItem,
                    chargeFormItem.getSourceUnitPrice(),
                    chargeFormItem.getExpectedUnitPrice(),
                    chargeFormItem.getExpectedTotalPrice(),
                    chargeFormItem.getExpectedTotalPriceRatio());
        }

        FillUtils.fillCreatedBy(chargeFormItem, operatorId);
        return chargeFormItem;
    }

    public static ChargeFormItem insertChargeFormItem(ChargeForm chargeForm, ChargeFormItem toInsertFormItem, String operatorId) {
        ChargeFormItem chargeFormItem = new ChargeFormItem();
        BeanUtils.copyProperties(toInsertFormItem, chargeFormItem);
        chargeFormItem.setId(AbcIdUtils.getUUID());
        chargeFormItem.setChargeFormId(chargeForm.getId());
        chargeFormItem.setChargeSheetId(chargeForm.getChargeSheetId());
        chargeFormItem.setPatientOrderId(chargeForm.getPatientOrderId());
        chargeFormItem.setClinicId(chargeForm.getClinicId());
        chargeFormItem.setChainId(chargeForm.getChainId());
        chargeFormItem.setStatus(Constants.ChargeFormItemStatus.UNCHARGED);
        chargeFormItem.setV2Status(Constants.ChargeFormItemStatus.UNCHARGED);
        chargeFormItem.calculateTotalPrice();

        if (chargeForm.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY) {
            chargeFormItem.setIsAirPharmacy(1);
        }

        FillUtils.fillCreatedBy(chargeFormItem, operatorId);
        return chargeFormItem;
    }

    public static ChargeFormItem cloneChargeFormItem(ChargeForm chargeForm, ChargeFormItem sourceChargeFormItem, String operatorId) {
        ChargeFormItem chargeFormItem = new ChargeFormItem();
        BeanUtils.copyProperties(sourceChargeFormItem, chargeFormItem, "chargeFormItemBatchInfos");
        chargeFormItem.setId(AbcIdUtils.getUUID());
        chargeFormItem.setChargeFormId(chargeForm.getId());
        chargeFormItem.setChargeSheetId(chargeForm.getChargeSheetId());
        chargeFormItem.setPatientOrderId(chargeForm.getPatientOrderId());
        chargeFormItem.setClinicId(chargeForm.getClinicId());
        chargeFormItem.setChainId(chargeForm.getChainId());
        chargeFormItem.setStatus(Constants.ChargeFormItemStatus.UNCHARGED);
        chargeFormItem.setV2Status(Constants.ChargeFormItemStatus.UNCHARGED);
        chargeFormItem.setPayStatus(ChargeFormItem.PayStatus.UNPAID);
        chargeFormItem.setPaySource(0);
        chargeFormItem.setSourceFormItemId(null);
        chargeFormItem.setDiscountPrice(BigDecimal.ZERO);
        chargeFormItem.setExpectedUnitPrice(null);
        chargeFormItem.setExpectedTotalPrice(null);
        chargeFormItem.setExpectedTotalPriceRatio(null);
        chargeFormItem.setFractionPrice(null);
        chargeFormItem.setSourceUnitPrice(null);
        chargeFormItem.setAdjustmentPrice(BigDecimal.ZERO);
        chargeFormItem.setPromotionPrice(BigDecimal.ZERO);
        chargeFormItem.setRefundDiscountPrice(BigDecimal.ZERO);
        chargeFormItem.setRefundTotalPrice(BigDecimal.ZERO);
        chargeFormItem.setRefundUnitCount(BigDecimal.ZERO);
        chargeFormItem.setAssociateFormItemId(null);
        chargeFormItem.setIsUseLimitPrice(0);
        chargeFormItem.setPromotionInfoJson(null);
        chargeFormItem.setCouponPromotionInfoJson(null);
        chargeFormItem.setGiftRulePromotionInfoJson(null);
        chargeFormItem.setProductSnapshot(null);
        chargeFormItem.setProductInfo(null);
        chargeFormItem.setReceivedPrice(BigDecimal.ZERO);
        chargeFormItem.setLockId(null);
        Optional.ofNullable(chargeFormItem.getChargeFormItemBatchInfos())
                .ifPresent(List::clear);

        if (chargeForm.getSourceFormType() == Constants.SourceFormType.AIR_PHARMACY &&
                (chargeFormItem.getProductType() == Constants.ProductType.PROCESS || chargeFormItem.getProductType() == Constants.ProductType.EXPRESS_DELIVERY || chargeFormItem.getProductType() == Constants.ProductType.INGREDIENT)) {
            chargeFormItem.setUnitPrice(BigDecimal.ZERO);
        }
        chargeFormItem.calculateTotalPrice();
        FillUtils.fillCreatedBy(chargeFormItem, operatorId);
        return chargeFormItem;
    }

    public static ChargeFormItem updateChargeFormItem(ChargeFormItem chargeFormItem,
                                                      String productId,
                                                      int productType,
                                                      int productSubType,
                                                      String name,
                                                      String unit,
                                                      BigDecimal unitCount,
                                                      BigDecimal unitPrice,
                                                      BigDecimal sourceUnitPrice,
                                                      BigDecimal sourceTotalPrice,
                                                      BigDecimal expectedUnitPrice,
                                                      BigDecimal fractionPrice,
                                                      BigDecimal expectedTotalPrice,
                                                      BigDecimal totalPriceRatio,
                                                      BigDecimal expectedTotalPriceRatio,
                                                      BigDecimal doseCount,
                                                      BigDecimal discountPrice,
                                                      String usageInfo,
                                                      String productInfo,
                                                      Integer groupId,
                                                      int isDismounting,
                                                      int sort,
                                                      BigDecimal doctorSourceUnitPrice,
                                                      BigDecimal doctorSourceTotalPrice,
                                                      int sourceItemType,
                                                      int pharmacyType,
                                                      int pharmacyNo,
                                                      Integer feeComposeType,
                                                      Long feeTypeId,
                                                      int goodsFeeType,
                                                      String lockId,
                                                      String operatorId) {
        // 只有在未支付的情况下可以更新
        if (chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.UNCHARGED) {
            return chargeFormItem;
        }

        chargeFormItem.setName(name);
        chargeFormItem.setProductId(productId);
        chargeFormItem.setProductType(productType);
        if (chargeFormItem.getComposeType() == ComposeType.COMPOSE) {
            chargeFormItem.setProductType(Constants.ProductType.COMPOSE_PRODUCT);
        }
        chargeFormItem.setProductSubType(productSubType);
        chargeFormItem.setUnitCount(unitCount);
        chargeFormItem.setDoseCount(doseCount);
        chargeFormItem.setUnit(unit);
        chargeFormItem.setGroupId(groupId);
        chargeFormItem.setUnitPrice(unitPrice);
        chargeFormItem.setSourceUnitPrice(sourceUnitPrice);
        chargeFormItem.setSourceTotalPrice(sourceTotalPrice);
        chargeFormItem.setExpectedUnitPrice(expectedUnitPrice);
        chargeFormItem.setExpectedTotalPrice(expectedTotalPrice);
        chargeFormItem.setFractionPrice(fractionPrice);
        chargeFormItem.setTotalPriceRatio(totalPriceRatio);
        chargeFormItem.setExpectedTotalPriceRatio(expectedTotalPriceRatio);

//        if (chargeFormItem.getExpectedTotalPrice() != null) {
//            chargeFormItem.setExpectedTotalPrice(chargeFormItem.getExpectedTotalPrice().multiply(MathUtils.wrapBigDecimal(doseCount, BigDecimal.ONE)));
//        }

        chargeFormItem.setDiscountPrice(discountPrice);
        chargeFormItem.setUsageInfoJson(usageInfo);
        chargeFormItem.setUseDismounting(isDismounting);
        chargeFormItem.setProductSnapshot(productInfo);
        chargeFormItem.setSort(sort);
        chargeFormItem.setDoctorSourceUnitPrice(doctorSourceUnitPrice);
        chargeFormItem.setDoctorSourceTotalPrice(doctorSourceTotalPrice);
        chargeFormItem.setSourceItemType(sourceItemType);
        chargeFormItem.setPharmacyType(pharmacyType);
        chargeFormItem.setPharmacyNo(pharmacyNo);
        chargeFormItem.setFeeComposeType(feeComposeType);
        chargeFormItem.setFeeTypeId(feeTypeId);
        chargeFormItem.setGoodsFeeType(goodsFeeType);
        chargeFormItem.setLockId(lockId);

        //保障中药入库一定拆零
        if (productType == Constants.ProductType.MEDICINE && productSubType == Constants.ProductType.SubType.MEDICINE_CHINESE) {
            chargeFormItem.setUseDismounting(1);
        }

        if (sourceItemType == Constants.SourceItemType.SELF_PROVIDED) {
            chargeFormItem.setUnitPrice(BigDecimal.ZERO);
            chargeFormItem.setTotalPrice(BigDecimal.ZERO);
            chargeFormItem.setFractionPrice(BigDecimal.ZERO);
            chargeFormItem.setExpectedUnitPrice(null);
            chargeFormItem.setExpectedTotalPrice(null);
            chargeFormItem.setExpectedTotalPriceRatio(null);
            chargeFormItem.setDoctorSourceUnitPrice(null);
            chargeFormItem.setDoctorSourceUnitPrice(null);
            chargeFormItem.calculateTotalPrice();
        } else {
            ExpectedPriceHelper.process(chargeFormItem, chargeFormItem.getSourceUnitPrice(), chargeFormItem.getExpectedUnitPrice(), chargeFormItem.getExpectedTotalPrice(), chargeFormItem.getExpectedTotalPriceRatio());
        }

        chargeFormItem.setLastModified(Instant.now());
        chargeFormItem.setLastModifiedBy(operatorId);
        return chargeFormItem;
    }

    public static boolean deleteChargeFormItem(ChargeFormItem chargeFormItem, String operatorId) {
        //只有在未支付的情况下可以删除
        if (chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.UNCHARGED && chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.UNSELECTED) {
            return false;
        }
        /**
         * 【空中药房特殊逻辑】已经存在的空中药房收费单，已经有快递和门诊Item，不能删除
         * TapdBug:https://www.tapd.cn/22044681/bugtrace/bugs/view?bug_id=1122044681001017224&url_cache_key=291eb4a185c1470591eef2b3d00a987a
         * */
        if (chargeFormItem.getIsAirPharmacy() == 1 && (chargeFormItem.getProductType() == Constants.ProductType.PROCESS
                || chargeFormItem.getProductType() == Constants.ProductType.EXPRESS_DELIVERY)) {
            return false;
        }

        chargeFormItem.deleteModel(operatorId);
        return false;
    }

    public static boolean deleteFeeParentChargeFormItem(ChargeFormItem chargeFormItem, List<ChargeFormItem> chargeFormItems, String operatorId) {

        //只有在未支付的情况下可以删除
        if (chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.UNCHARGED) {
            return false;
        }
        /**
         * 【空中药房特殊逻辑】已经存在的空中药房收费单，已经有快递和门诊Item，不能删除
         * TapdBug:https://www.tapd.cn/22044681/bugtrace/bugs/view?bug_id=1122044681001017224&url_cache_key=291eb4a185c1470591eef2b3d00a987a
         * */
        if (chargeFormItem.getIsAirPharmacy() == 1 && (chargeFormItem.getProductType() == Constants.ProductType.PROCESS
                || chargeFormItem.getProductType() == Constants.ProductType.EXPRESS_DELIVERY)) {
            return false;
        }

        chargeFormItem.deleteModel(operatorId);
        /**
         * 删除母项时同时删除子项
         */
        Map<String, List<ChargeFormItem>> childMap = ListUtils.groupByKey(chargeFormItems.stream().filter(item -> Objects.nonNull(item.getComposeParentFormItemId())).collect(Collectors.toList()), ChargeFormItem::getComposeParentFormItemId);
        List<ChargeFormItem> children = childMap.get(chargeFormItem.getId());
        if (CollectionUtils.isNotEmpty(children)) {
            children.forEach(child -> child.deleteModel(operatorId));
        }
        return false;
    }

    public static void updateChargeFormItemComposeTypeAndGoodsFeeType(List<ChargeFormItem> chargeFormItems) {

        List<ChargeFormItem> toUpdateChargeFormItems = ChargeFormItemUtils.filterIsDeletedItems(chargeFormItems);

        if (CollectionUtils.isEmpty(toUpdateChargeFormItems)) {
            return;
        }

        //将composeType维护正确
        List<String> composeProductChargeFormItemIds = toUpdateChargeFormItems.stream()
                .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.COMPOSE_PRODUCT)
                .map(ChargeFormItem::getId)
                .collect(Collectors.toList());

        toUpdateChargeFormItems
                .forEach(chargeFormItem -> {
                    if (composeProductChargeFormItemIds.contains(chargeFormItem.getId())) {
                        chargeFormItem.setComposeType(ComposeType.COMPOSE);
                        chargeFormItem.setFeeComposeType(GoodsConst.FeeComposeType.FEE_TYPE_COMPOSE_GOODS);
                        return;
                    }

                    if (org.apache.commons.lang3.StringUtils.isNotEmpty(chargeFormItem.getComposeParentFormItemId())
                            && composeProductChargeFormItemIds.contains(chargeFormItem.getComposeParentFormItemId())) {
                        chargeFormItem.setComposeType(ComposeType.COMPOSE_SUB_ITEM);
                    }

                });

        //将goodsFeeType维护正确
        Map<String, ChargeFormItem> feeParentChargeFormItemMap = toUpdateChargeFormItems.stream()
                .filter(chargeFormItem -> chargeFormItem.getFeeComposeType() == GoodsConst.FeeComposeType.FEE_TYPE_COMPOSE_FEE)
                .collect(Collectors.toMap(ChargeFormItem::getId, Function.identity(), (a, b) -> a));

        Set<String> feeParentChargeFormItemIds = feeParentChargeFormItemMap.keySet();
        toUpdateChargeFormItems.forEach(chargeFormItem -> {
            if (feeParentChargeFormItemIds.contains(chargeFormItem.getId())) {
                chargeFormItem.setGoodsFeeType(GoodsFeeType.FEE_PARENT);
                return;
            }

            if (org.apache.commons.lang3.StringUtils.isNotEmpty(chargeFormItem.getComposeParentFormItemId())
                    && feeParentChargeFormItemIds.contains(chargeFormItem.getComposeParentFormItemId())) {
                ChargeFormItem parentItem = feeParentChargeFormItemMap.get(chargeFormItem.getComposeParentFormItemId());

                if (Objects.nonNull(parentItem) && parentItem.getComposeType() == ComposeType.COMPOSE_SUB_ITEM) {
                    chargeFormItem.setComposeType(ComposeType.COMPOSE_SUB_ITEM_FEE_CHILD);
                }

                chargeFormItem.setGoodsFeeType(GoodsFeeType.FEE_CHILD);
                return;
            }

            chargeFormItem.setGoodsFeeType(GoodsFeeType.FEE_OWN);
        });

    }

    public static void updateComposeItemPrice(List<ChargeFormItem> chargeFormItems) {

        List<ChargeFormItem> toUpdateChargeFormItems = ChargeFormItemUtils.filterIsDeletedItems(chargeFormItems);

        if (org.apache.commons.collections.CollectionUtils.isEmpty(toUpdateChargeFormItems)) {
            return;
        }

        Map<String, List<ChargeFormItem>> parentIdChargeFormItemsMap = toUpdateChargeFormItems.stream()
                .filter(chargeFormItem -> org.apache.commons.lang3.StringUtils.isNotEmpty(chargeFormItem.getComposeParentFormItemId()))
                .collect(Collectors.groupingBy(ChargeFormItem::getComposeParentFormItemId, Collectors.toList()));

        Consumer<ChargeFormItem> updateChildrenConsumer = parentItem -> {
            List<ChargeFormItem> children = parentIdChargeFormItemsMap.get(parentItem.getId());
            if (CollectionUtils.isEmpty(children)) {
                return;
            }

            //如果套餐母项的unitCount为0，那就把子项的unitCount也改为0
            if (MathUtils.wrapBigDecimalCompare(parentItem.getUnitCount(), BigDecimal.ZERO) <= 0) {
                children.forEach(chargeFormItem -> chargeFormItem.setUnitCount(BigDecimal.ZERO));
            }

            //将子项的议价全部清空
            children.forEach(chargeFormItem -> {
                chargeFormItem.setDoctorSourceTotalPrice(null);
                chargeFormItem.setDoctorSourceUnitPrice(null);
                chargeFormItem.setExpectedTotalPrice(null);
                chargeFormItem.setExpectedUnitPrice(null);
                chargeFormItem.setExpectedTotalPriceRatio(null);
                if (parentItem.getDoctorSourceUnitPrice() == null && parentItem.getDoctorSourceTotalPrice() == null) {
                    chargeFormItem.setFractionPrice(BigDecimal.ZERO);
                }
            });

            ExpectedPriceHelper.processCompose(parentItem,
                    children,
                    parentItem.getSourceUnitPrice(),
                    parentItem.getSourceTotalPrice(),
                    null,
                    parentItem.getExpectedUnitPrice(),
                    parentItem.getExpectedTotalPrice(),
                    parentItem.getExpectedTotalPriceRatio());

        };
        //先处理套餐母项，将套餐母项的金额平摊到套餐子项上
        List<ChargeFormItem> composeItems = toUpdateChargeFormItems.stream()
                .filter(chargeFormItem -> chargeFormItem.getComposeType() == ComposeType.COMPOSE)
                .collect(Collectors.toList());
        composeItems.forEach(updateChildrenConsumer);

        //再处理费用母项
        List<ChargeFormItem> feeParentItems = toUpdateChargeFormItems.stream()
                .filter(chargeFormItem -> chargeFormItem.getGoodsFeeType() == GoodsFeeType.FEE_PARENT)
                .collect(Collectors.toList());
        feeParentItems.forEach(updateChildrenConsumer);

    }

    /**
     * 把门诊病历单里面的 ProductFormItem 转成CharegeFormItem（chargeForm 构造chargeFormItem需要的参数）
     *
     * @param productFormItem 门诊病历输入参数
     *                        chargeForm 外层已经构造好的ChargeForm，chargeFormItem的部分构造参数需要从这里来
     *                        operatorId
     * @return ChargeFormItem 返回值其实最终也是作为输入参数chargeForm的孩子。
     */
    public static ChargeFormItem insertProductFormItem(ChargeForm chargeForm, ProductFormItem productFormItem, CalculateModel calculateModel, String operatorId) {
        UsageInfo usageInfo = new UsageInfo();
        BeanUtils.copyProperties(productFormItem, usageInfo);
        usageInfo.setDailyDosage(Objects.toString(productFormItem.getDailyDosage(), ""));


        BigDecimal doctorSourceUnitPrice = null;
        BigDecimal doctorSourceTotalPrice = null;
        String unitAdjustmentFeeLastModifiedBy = null;
//        if (calculateModel == CalculateModel.KEEP_CURRENT) {
//            BigDecimal totalPrice = MathUtils.calculateTotalPrice(productFormItem.getUnitPrice(), MathUtils.wrapBigDecimalOrZero(productFormItem.getUnitCount()), 2).add(MathUtils.wrapBigDecimalOrZero(productFormItem.getFractionPrice()));
//            DoctorSourcePriceCell doctorSourcePriceCell = translateDoctorSourcePrice(productFormItem.getUnitPrice(), productFormItem.getSourceUnitPrice(), productFormItem.getFractionPrice(), totalPrice);
//            unitAdjustmentFeeLastModifiedBy = doctorSourcePriceCell.getExpectedTotalPrice() != null ? productFormItem.getUnitAdjustmentFeeLastModifiedBy() : null;
//            productFormItem.setExpectedTotalPrice(doctorSourcePriceCell.getExpectedTotalPrice());
//            productFormItem.setExpectedUnitPrice(doctorSourcePriceCell.getExpectedUnitPrice());
//            doctorSourceUnitPrice = doctorSourcePriceCell.getDoctorSourceUnitPrice();
//            doctorSourceTotalPrice = doctorSourcePriceCell.getDoctorSourceTotalPrice();
//        } else if (calculateModel == CalculateModel.NORMAL_RESET) {
        unitAdjustmentFeeLastModifiedBy = productFormItem.getUnitAdjustmentFeeLastModifiedBy();
//        }

        ChargeFormItem chargeFormItem = insertChargeFormItem(chargeForm, productFormItem.getId(), productFormItem.getProductId(), productFormItem.getType(), productFormItem.getSubType(), productFormItem.getComposeType(), productFormItem.getComposeParentFormItemId(), productFormItem.getName(),
                productFormItem.getUnit(), productFormItem.getUnitCount(), productFormItem.getUnitPrice(), productFormItem.getSourceUnitPrice(), productFormItem.getSourceTotalPrice(), productFormItem.getExpectedUnitPrice(), productFormItem.getFractionPrice(), productFormItem.getExpectedTotalPrice(),
                productFormItem.getTotalPriceRatio(), productFormItem.getExpectedTotalPriceRatio(),
                BigDecimal.ONE, BigDecimal.ZERO, JsonUtils.dump(usageInfo),
                JsonUtils.dump(productFormItem.getProductInfo()), null, productFormItem.getUseDismounting(), productFormItem.getSort(), doctorSourceUnitPrice, doctorSourceTotalPrice, Constants.SourceItemType.NORMAL, productFormItem.getPharmacyType(), productFormItem.getPharmacyNo(),
                productFormItem.getFeeComposeType(), productFormItem.getFeeTypeId(), productFormItem.getGoodsFeeType(), Objects.toString(productFormItem.getLockId(), null), operatorId);

        if (chargeFormItem.getSourceItemType() == Constants.SourceItemType.NORMAL && CollectionUtils.isNotEmpty(productFormItem.getBatchInfos())) {
            ChargeFormItemBatchInfoUtils.insertOrUpdateChargeFormItemBatchInfosForGoodsBatchInfo(chargeFormItem, Optional.ofNullable(productFormItem.getBatchInfos().get(0))
                            .map(GoodsBatchInfo::getPieceNum)
                            .map(BigDecimal::new)
                            .orElse(null),
                    productFormItem.getBatchInfos()
                            .stream()
                            .map(ChargeFormItemBatchInfoUtils::convertCommonBatchInfoToSdkBatchInfo)
                            .collect(Collectors.toList()), operatorId);
        }

        insertProductFormItemAdditional(chargeFormItem, productFormItem, unitAdjustmentFeeLastModifiedBy);
        return chargeFormItem;
    }

    public static ChargeFormItem insertMedicalPlanFormItem(ChargeForm chargeForm, MedicalPlanFormItem medicalPlanFormItem, String operatorId) {
        UsageInfo usageInfo = new UsageInfo();
        BeanUtils.copyProperties(medicalPlanFormItem, usageInfo);

        String sourceFormItemId = null;
        if (org.apache.commons.lang3.StringUtils.isEmpty(medicalPlanFormItem.getSourceFormItemId())) {
            sourceFormItemId = medicalPlanFormItem.getId();
        }

        ChargeFormItem chargeFormItem = insertChargeFormItem(chargeForm, sourceFormItemId, medicalPlanFormItem.getProductId(),
                medicalPlanFormItem.getProductType(), medicalPlanFormItem.getProductSubType(), medicalPlanFormItem.getComposeType(),
                medicalPlanFormItem.getComposeParentFormItemId(), medicalPlanFormItem.getName(),
                medicalPlanFormItem.getUnit(), medicalPlanFormItem.getUnitCount(), medicalPlanFormItem.getUnitPrice(),
                medicalPlanFormItem.getSourceUnitPrice(), null, null, medicalPlanFormItem.getFractionPrice(),
                medicalPlanFormItem.getExpectedTotalPrice(), medicalPlanFormItem.getTotalPriceRatio(), medicalPlanFormItem.getExpectedTotalPriceRatio(),
                BigDecimal.ONE, BigDecimal.ZERO, JsonUtils.dump(usageInfo),
                null, null, medicalPlanFormItem.getIsDismounting(),
                medicalPlanFormItem.getSort(), null, null,
                Constants.SourceItemType.NORMAL, GoodsConst.PharmacyType.LOCAL_PHARMACY, 0,
                medicalPlanFormItem.getFeeComposeType(), medicalPlanFormItem.getFeeTypeId(), medicalPlanFormItem.getGoodsFeeType(),
                medicalPlanFormItem.getLockId(),
                operatorId);

        if (chargeFormItem.getSourceItemType() == Constants.SourceItemType.NORMAL && CollectionUtils.isNotEmpty(medicalPlanFormItem.getBatchInfos())) {
            ChargeFormItemBatchInfoUtils.insertOrUpdateChargeFormItemBatchInfosForGoodsBatchInfo(chargeFormItem, Optional.ofNullable(medicalPlanFormItem.getBatchInfos().get(0))
                            .map(cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsBatchInfo::getPieceNum)
                            .map(BigDecimal::new)
                            .orElse(null),
                    medicalPlanFormItem.getBatchInfos(), operatorId);
        }

        insertMedicalPlanFormItemAdditional(chargeFormItem, medicalPlanFormItem);
        return chargeFormItem;
    }

    public static void insertMedicalPlanFormItemAdditional(ChargeFormItem chargeFormItem, MedicalPlanFormItem medicalPlanFormItem) {
        if (Objects.isNull(chargeFormItem) || Objects.isNull(medicalPlanFormItem)) {
            return;
        }
        insertChargeFormItemAdditionalIfNeed(chargeFormItem);
        updateChargeFormItemAdditional(chargeFormItem.getAdditional(),
                medicalPlanFormItem.getDoctorId(),
                medicalPlanFormItem.getNurseId(),
                medicalPlanFormItem.getDepartmentId(),
                null,
                medicalPlanFormItem.getToothNos(),
                 null, //medical plan不需要
                medicalPlanFormItem.getUnitAdjustmentFeeLastModifiedBy(),
                null);
    }

    public static void insertProductFormItemAdditional(ChargeFormItem chargeFormItem, ProductFormItem productFormItem, String unitAdjustmentFeeLastModifiedBy) {
        if (Objects.isNull(chargeFormItem) || Objects.isNull(productFormItem)) {
            return;
        }
        insertChargeFormItemAdditionalIfNeed(chargeFormItem);
        updateChargeFormItemAdditional(chargeFormItem.getAdditional(),
                productFormItem.getDoctorId(),
                productFormItem.getNurseId(),
                productFormItem.getDepartmentId(),
                productFormItem.getRemark(),
                productFormItem.getToothNos(),
                null,
                unitAdjustmentFeeLastModifiedBy,
                null
        );
    }

    public static void updateChargeFormItemAdditional(ChargeFormItemAdditional additional,
                                                      String doctorId,
                                                      String nurseId,
                                                      String departmentId,
                                                      String remark,
                                                      List<Integer> toothNos,
                                                      List<TraceableCode> traceableCodeList,
                                                      String unitAdjustmentFeeLastModifiedBy,
                                                      String giftGoodsPromotionId) {
        if (Objects.isNull(additional)) {
            return;
        }
        additional.setDoctorId(doctorId)
                .setNurseId(nurseId)
                .setDepartmentId(departmentId)
                .setRemark(remark)
                .setToothNos(toothNos)
                .setUnitAdjustmentFeeLastModifiedBy(unitAdjustmentFeeLastModifiedBy)
                .setTraceableCodeList(traceableCodeList)
                .setGiftGoodsPromotionId(giftGoodsPromotionId);
    }

    public static void insertChargeFormItemAdditionalIfNeed(ChargeFormItem chargeFormItem) {
        if (Objects.isNull(chargeFormItem)) {
            return;
        }
        if (Objects.nonNull(chargeFormItem.getAdditional())) {
            return;
        }
        chargeFormItem.setAdditional(new ChargeFormItemAdditional());
    }

    public static ChargeFormItem updateMedicalPlanFormItem(ChargeFormItem chargeFormItem, MedicalPlanFormItem medicalPlanFormItem, String operatorId) {
        //只有在未支付的情况下可以更新
        if (chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.UNCHARGED) {
            return chargeFormItem;
        }
        //门诊过来的数据有可能是从套餐改为了非套餐，或者从非套餐改为套餐
        chargeFormItem.setComposeType(medicalPlanFormItem.getComposeType());

        UsageInfo usageInfo = new UsageInfo();
        BeanUtils.copyProperties(medicalPlanFormItem, usageInfo);

        updateChargeFormItem(chargeFormItem, medicalPlanFormItem.getProductId(), medicalPlanFormItem.getProductType(),
                medicalPlanFormItem.getProductSubType(), medicalPlanFormItem.getName(), medicalPlanFormItem.getUnit(),
                medicalPlanFormItem.getUnitCount(), medicalPlanFormItem.getUnitPrice(), medicalPlanFormItem.getSourceUnitPrice(), null,
                null, medicalPlanFormItem.getFractionPrice(), medicalPlanFormItem.getExpectedTotalPrice(),
                medicalPlanFormItem.getTotalPriceRatio(), medicalPlanFormItem.getExpectedTotalPriceRatio(), BigDecimal.ONE,
                BigDecimal.ZERO, JsonUtils.dump(usageInfo), null,
                null, medicalPlanFormItem.getIsDismounting(), medicalPlanFormItem.getSort(),
                null, null, Constants.SourceItemType.NORMAL,
                GoodsConst.PharmacyType.LOCAL_PHARMACY, 0, medicalPlanFormItem.getFeeComposeType(),
                medicalPlanFormItem.getFeeTypeId(), medicalPlanFormItem.getGoodsFeeType(), medicalPlanFormItem.getLockId(), operatorId);

        if (chargeFormItem.getSourceItemType() == Constants.SourceItemType.NORMAL && CollectionUtils.isNotEmpty(medicalPlanFormItem.getBatchInfos())) {
            ChargeFormItemBatchInfoUtils.insertOrUpdateChargeFormItemBatchInfosForGoodsBatchInfo(chargeFormItem, Optional.ofNullable(medicalPlanFormItem.getBatchInfos().get(0))
                            .map(cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsBatchInfo::getPieceNum)
                            .map(BigDecimal::new)
                            .orElse(null),
                    medicalPlanFormItem.getBatchInfos(), operatorId);
        } else {
            Optional.ofNullable(chargeFormItem.getChargeFormItemBatchInfos())
                    .orElse(new ArrayList<>())
                    .forEach(chargeFormItemBatchInfo -> chargeFormItemBatchInfo.deleteModel(operatorId));
        }

        insertChargeFormItemAdditionalIfNeed(chargeFormItem);
        ChargeFormItemAdditional additional = chargeFormItem.getAdditional();
        updateChargeFormItemAdditional(additional,
                medicalPlanFormItem.getDoctorId(),
                medicalPlanFormItem.getNurseId(),
                medicalPlanFormItem.getDepartmentId(),
                null,
                medicalPlanFormItem.getToothNos(),
                Objects.nonNull(additional) ? additional.getTraceableCodeList() : null,
                medicalPlanFormItem.getUnitAdjustmentFeeLastModifiedBy(),
                null);
        return chargeFormItem;
    }

    public static ChargeFormItem updateProductFormItem(ChargeFormItem chargeFormItem, ProductFormItem productFormItem, CalculateModel calculateModel, String operatorId) {
        // 只有在未支付的情况下可以更新
        if (chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.UNCHARGED && chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.UNSELECTED) {
            return chargeFormItem;
        }
        // 门诊过来的数据有可能是从套餐改为了非套餐，或者从非套餐改为套餐
        chargeFormItem.setComposeType(productFormItem.getComposeType());

        //门诊过来的数据可能有变动，这里强制把状态设置为待收
        if (chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.UNSELECTED) {
            chargeFormItem.setStatus(Constants.ChargeFormItemStatus.UNCHARGED);
        }

        UsageInfo usageInfo = new UsageInfo();
        BeanUtils.copyProperties(productFormItem, usageInfo);
        usageInfo.setDailyDosage(Objects.toString(productFormItem.getDailyDosage(), ""));

        BigDecimal doctorSourceUnitPrice = null;
        BigDecimal doctorSourceTotalPrice = null;
        String unitAdjustmentFeeLastModifiedBy = null;
//        if (calculateModel == CalculateModel.KEEP_CURRENT) {
//            BigDecimal totalPrice = MathUtils.calculateTotalPrice(productFormItem.getUnitPrice(), MathUtils.wrapBigDecimalOrZero(productFormItem.getUnitCount()), 2).add(MathUtils.wrapBigDecimalOrZero(productFormItem.getFractionPrice()));
//            DoctorSourcePriceCell doctorSourcePriceCell = translateDoctorSourcePrice(productFormItem.getUnitPrice(), productFormItem.getSourceUnitPrice(), productFormItem.getFractionPrice(), totalPrice);
//            unitAdjustmentFeeLastModifiedBy = doctorSourcePriceCell.getExpectedTotalPrice() != null ? productFormItem.getUnitAdjustmentFeeLastModifiedBy() : null;
//            productFormItem.setExpectedTotalPrice(doctorSourcePriceCell.getExpectedTotalPrice());
//            productFormItem.setExpectedUnitPrice(doctorSourcePriceCell.getExpectedUnitPrice());
//            doctorSourceUnitPrice = doctorSourcePriceCell.getDoctorSourceUnitPrice();
//            doctorSourceTotalPrice = doctorSourcePriceCell.getDoctorSourceTotalPrice();
//        } else if (calculateModel == CalculateModel.NORMAL_RESET) {
        unitAdjustmentFeeLastModifiedBy = productFormItem.getUnitAdjustmentFeeLastModifiedBy();
//        }

        updateChargeFormItem(chargeFormItem, productFormItem.getProductId(), productFormItem.getType(),
                productFormItem.getSubType(), productFormItem.getName(), productFormItem.getUnit(),
                productFormItem.getUnitCount(), productFormItem.getUnitPrice(), productFormItem.getSourceUnitPrice(), productFormItem.getSourceTotalPrice(),
                productFormItem.getExpectedUnitPrice(), productFormItem.getFractionPrice(), productFormItem.getExpectedTotalPrice(),
                productFormItem.getTotalPriceRatio(), productFormItem.getExpectedTotalPriceRatio(), BigDecimal.ONE,
                BigDecimal.ZERO, JsonUtils.dump(usageInfo), JsonUtils.dump(productFormItem.getProductInfo()),
                null, productFormItem.getUseDismounting(), productFormItem.getSort(),
                doctorSourceUnitPrice, doctorSourceTotalPrice, Constants.SourceItemType.NORMAL,
                productFormItem.getPharmacyType(), productFormItem.getPharmacyNo(), productFormItem.getFeeComposeType(),
                productFormItem.getFeeTypeId(), productFormItem.getGoodsFeeType(), Objects.toString(productFormItem.getLockId(), null), operatorId);

        if (chargeFormItem.getSourceItemType() == Constants.SourceItemType.NORMAL && CollectionUtils.isNotEmpty(productFormItem.getBatchInfos())) {
            ChargeFormItemBatchInfoUtils.insertOrUpdateChargeFormItemBatchInfosForGoodsBatchInfo(chargeFormItem, Optional.ofNullable(productFormItem.getBatchInfos().get(0))
                            .map(GoodsBatchInfo::getPieceNum)
                            .map(BigDecimal::new)
                            .orElse(null),
                    productFormItem.getBatchInfos().stream()
                            .map(ChargeFormItemBatchInfoUtils::convertCommonBatchInfoToSdkBatchInfo)
                            .collect(Collectors.toList()), operatorId);
        } else {
            Optional.ofNullable(chargeFormItem.getChargeFormItemBatchInfos())
                    .orElse(new ArrayList<>())
                    .forEach(chargeFormItemBatchInfo -> chargeFormItemBatchInfo.deleteModel(operatorId));
        }

        insertChargeFormItemAdditionalIfNeed(chargeFormItem);
        ChargeFormItemAdditional additional = chargeFormItem.getAdditional();
        updateChargeFormItemAdditional(additional,
                productFormItem.getDoctorId(),
                productFormItem.getNurseId(),
                productFormItem.getDepartmentId(),
                productFormItem.getRemark(),
                productFormItem.getToothNos(),
                Objects.nonNull(additional) ? additional.getTraceableCodeList() : null,
                unitAdjustmentFeeLastModifiedBy,
                null
        );
        return chargeFormItem;
    }

    public static DoctorSourcePriceCell translateDoctorSourcePrice(BigDecimal unitPrice, BigDecimal sourceUnitPrice, BigDecimal fractionPrice, BigDecimal totalPrice) {
        BigDecimal expectedTotalPrice = null;

        if (MathUtils.wrapBigDecimalCompare(sourceUnitPrice, unitPrice) != 0 || MathUtils.wrapBigDecimalCompare(fractionPrice, BigDecimal.ZERO) != 0) {
            expectedTotalPrice = totalPrice;
        }

        return new DoctorSourcePriceCell()
                // 产品不区分医生议价和收费员议价，只需要知道有没有议价
                .setDoctorSourceUnitPrice(null)
                .setDoctorSourceTotalPrice(null)
                .setExpectedTotalPrice(expectedTotalPrice)
                .setExpectedUnitPrice(null);
    }

    public static ChargeFormItem insertChargeCooperationOrderItem(ChargeForm chargeForm, ChargeCooperationOrderItem chargeCooperationOrderItem, String operatorId) {
        return insertChargeFormItem(chargeForm, chargeCooperationOrderItem.getId(), chargeCooperationOrderItem.getGoodsId(),
                chargeCooperationOrderItem.getGoodsType(), chargeCooperationOrderItem.getGoodsSubType(), chargeCooperationOrderItem.getComposeType(),
                chargeCooperationOrderItem.getComposeParentFormItemId(), chargeCooperationOrderItem.getName(),
                chargeCooperationOrderItem.getUnit(), chargeCooperationOrderItem.getUnitCount(), chargeCooperationOrderItem.getUnitPrice(),
                chargeCooperationOrderItem.getSourceUnitPrice(), chargeCooperationOrderItem.getSourceTotalPrice(), chargeCooperationOrderItem.getExpectedUnitPrice(), chargeCooperationOrderItem.getFractionPrice(),
                chargeCooperationOrderItem.getExpectedTotalPrice(), null, chargeCooperationOrderItem.getExpectedTotalPriceRatio(),
                chargeCooperationOrderItem.getDoseCount(), BigDecimal.ZERO, JsonUtils.dump(chargeCooperationOrderItem.getUsageInfo()),
                null, null, chargeCooperationOrderItem.getIsDismounting(),
                chargeCooperationOrderItem.getSort(), null, null,
                Constants.SourceItemType.NORMAL, chargeCooperationOrderItem.getPharmacyType(), chargeCooperationOrderItem.getPharmacyNo(),
                chargeCooperationOrderItem.getFeeComposeType(), chargeCooperationOrderItem.getFeeTypeId(), chargeCooperationOrderItem.getGoodsFeeType(),
                null,
                operatorId);
    }

    public static ChargeFormItem updateChargeCooperationOrderItem(ChargeFormItem chargeFormItem, ChargeCooperationOrderItem cooperationOrderItem, String operatorId) {
        //只有在未支付的情况下可以更新
        if (chargeFormItem.getStatus() != Constants.ChargeFormItemStatus.UNCHARGED) {
            return chargeFormItem;
        }
        chargeFormItem.setComposeType(cooperationOrderItem.getComposeType());

        updateChargeFormItem(chargeFormItem, cooperationOrderItem.getGoodsId(), cooperationOrderItem.getGoodsType(),
                cooperationOrderItem.getGoodsSubType(), cooperationOrderItem.getName(), cooperationOrderItem.getUnit(),
                cooperationOrderItem.getUnitCount(), cooperationOrderItem.getUnitPrice(), cooperationOrderItem.getSourceUnitPrice(), cooperationOrderItem.getSourceTotalPrice(),
                cooperationOrderItem.getExpectedUnitPrice(), cooperationOrderItem.getFractionPrice(), cooperationOrderItem.getExpectedTotalPrice(),
                null, cooperationOrderItem.getExpectedTotalPriceRatio(), cooperationOrderItem.getDoseCount(),
                BigDecimal.ZERO, JsonUtils.dump(cooperationOrderItem.getUsageInfo()), null,
                null, cooperationOrderItem.getIsDismounting(), cooperationOrderItem.getSort(),
                null, null, Constants.SourceItemType.NORMAL,
                cooperationOrderItem.getPharmacyType(), cooperationOrderItem.getPharmacyNo(), cooperationOrderItem.getFeeComposeType(),
                cooperationOrderItem.getFeeTypeId(), cooperationOrderItem.getGoodsFeeType(), chargeFormItem.getLockId(), operatorId);
        return chargeFormItem;
    }

    public static void updateSourceItemIdFromChargeCooperationOrder(ChargeSheet chargeSheet, ChargeCooperationOrder chargeCooperationOrder) {

        if (Objects.isNull(chargeSheet) || Objects.isNull(chargeCooperationOrder)) {
            return;
        }

        //已绑定关系的orderItemIds
        Set<String> cooperationOrderItemIds = ChargeUtils.getChargeSheetItems(chargeSheet)
                .stream()
                .filter(chargeFormItem -> StringUtils.isNotEmpty(chargeFormItem.getSourceFormItemId()))
                .map(ChargeFormItem::getSourceFormItemId)
                .collect(Collectors.toSet());


        Map<String, String> leftOrderItemIdGoodsIdMap = Optional.ofNullable(chargeCooperationOrder.getOrderItems())
                .orElse(new ArrayList<>())
                .stream()
                .filter(chargeCooperationOrderItem -> !cooperationOrderItemIds.contains(chargeCooperationOrderItem.getId()))
                .collect(Collectors.toMap(ChargeCooperationOrderItem::getGoodsId, ChargeCooperationOrderItem::getId, (a, b) -> a));

        if (MapUtils.isEmpty(leftOrderItemIdGoodsIdMap)) {
            return;
        }

        ChargeUtils.getChargeSheetItems(chargeSheet)
                .stream()
                .filter(chargeFormItem -> StringUtils.isEmpty(chargeFormItem.getSourceFormItemId())
                        && StringUtils.isNotEmpty(chargeFormItem.getProductId())
                        && chargeFormItem.getIsGift() == Constants.ChargeFormItemGiftType.NOT_GIFT)
                .forEach(chargeFormItem -> {
                    if (MapUtils.isEmpty(leftOrderItemIdGoodsIdMap)) {
                        return;
                    }

                    String orderItemId = leftOrderItemIdGoodsIdMap.get(chargeFormItem.getProductId());

                    if (StringUtils.isEmpty(orderItemId)) {
                        return;
                    }

                    chargeFormItem.setSourceFormItemId(orderItemId);
                    leftOrderItemIdGoodsIdMap.remove(chargeFormItem.getProductId());
                });

    }

    @Data
    @Accessors(chain = true)
    public static class DoctorSourcePriceCell {

        private BigDecimal doctorSourceUnitPrice;

        private BigDecimal doctorSourceTotalPrice;

        private BigDecimal expectedTotalPrice;

        private BigDecimal expectedUnitPrice;
    }
}
