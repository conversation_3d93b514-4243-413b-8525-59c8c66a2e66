package cn.abcyun.cis.charge.factory.chargeform;

import cn.abcyun.cis.charge.api.model.ChargeFormItemReq;
import cn.abcyun.cis.charge.api.model.ChargeFormReq;
import cn.abcyun.cis.charge.model.ChargeForm;
import cn.abcyun.cis.charge.model.ChargeFormItem;
import cn.abcyun.cis.charge.processor.calculate.CalculateModel;
import cn.abcyun.cis.commons.rpc.outpatient.MedicalRecord;
import cn.abcyun.cis.commons.rpc.outpatient.PrescriptionForm;

import java.math.BigDecimal;
import java.util.List;

/**
 * 定义构造chargeForm的接口
 * ChargeFormFactoryBase 实现基本的构造
 */
interface IChargeFormCreate {
    /*
     * 从计费过来的chargeForm的构造
     * */
    ChargeForm createChargeFormFromCalculate(String chargeSheetId, String patientOrderId, String clinicId, String chainId,
                                             ChargeFormReq chargeFormReq,
                                             boolean isCanUpdateUsageInfo,
                                             boolean generateIdIfNotExisted,
                                             boolean forceGenerateNewId,
                                             String operatorId);

    /**
     * 从门诊过来的chargeForm的构造[加工费]
     */
    ChargeForm createChargeFormFromPatientProcess(String chargeSheetId, String patientOrderId, String clinicId, String chainId,
                                                  BigDecimal processFee,
                                                  String operatorId);

    /**
     * 从门诊过来的chargeForm的构造[加工费]
     */
    ChargeForm createChargeFormFromPatientPrescription(String chargeSheetId, String patientOrderId, String clinicId, String chainId,
                                                       PrescriptionForm prescriptionForm, CalculateModel calculateModel, MedicalRecord outpatientMedicalRecord, String doctorId,
                                                       String doctorDepartmentId,
                                                       String operatorId);

    ChargeForm updateChargeFormFromPatientPrescription(ChargeForm chargeForm, PrescriptionForm prescriptionForm, CalculateModel calculateModel, MedicalRecord outpatientMedicalRecord,
                                                       String doctorId, String doctorDepartmentId, String operatorId);

    List<ChargeFormItem> convertToChargeFormItem(ChargeForm chargeForm,
                                                 int pharmacyType,
                                                 ChargeFormItemReq chargeFormItemReq,
                                                 boolean generateIdIfNotExisted,
                                                 boolean forceGenerateNewId,
                                                 String operatorId);

    ChargeFormItem generateChargeFormItem(ChargeForm chargeForm,
                                          int pharmacyType,
                                          ChargeFormItemReq chargeFormItemReq,
                                          boolean generateIdIfNotExisted,
                                          boolean forceGenerateNewId,
                                          String operatorId);

    //因为函数在chargeForm 构造的外部也在使用，先声明成接口
    void bindProductInfoForAirPharmacyExpressDelivery(ChargeForm chargeForm);


}
