package cn.abcyun.cis.charge.controller.rpc;

import cn.abcyun.cis.charge.api.model.PatientChargeSheetAbstractListRsp;
import cn.abcyun.cis.charge.api.model.scrm.*;
import cn.abcyun.cis.charge.service.ChargeScrmService;
import cn.abcyun.cis.charge.service.dto.ChargeExecuteRecordViewForScrm;
import cn.abcyun.common.model.AbcListPage;
import cn.abcyun.common.model.AbcServiceResponse;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 为scrm提供的rpc接口
 *
 * <AUTHOR>
 * @since 2024/7/19 17:44
 **/
@Api(value = "ChargeForScrmController", description = "为scrm提供的rpc接口")
@RestController
@RequestMapping("/rpc/charges/scrm")
public class ChargeForScrmController {

    @Autowired
    private ChargeScrmService chargeScrmService;

    /**
     * SCRM查询患者收费单
     */
    @PostMapping("/charge-sheets")
    public AbcServiceResponse<PatientChargeSheetAbstractListRsp> queryChargeSheets(@Valid @RequestBody QueryPatientChargeSheetForScrmReq req) {
        PatientChargeSheetAbstractListRsp rsp = chargeScrmService.findPatientChargeSheetAbstractList(req.getOffset(), req.getLimit(), req.getPatientId(), req.getChainId(), req.getClinicIds());
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * SCRM查询患者执行记录
     */
    @PostMapping("/nurse/execute-record")
    public AbcServiceResponse<AbcListPage<ChargeExecuteRecordViewForScrm>> queryPatientNurseExecuteRecord(@Valid @RequestBody QueryPatientNurseExecuteRecordForScrmReq req) {
        AbcListPage<ChargeExecuteRecordViewForScrm> listPage = chargeScrmService.queryPatientNurseExecuteRecord(req.getOffset(), req.getLimit(), req.getPatientId(), req.getChainId(), req.getClinicIds());
        return new AbcServiceResponse<>(listPage);
    }

    /**
     * SCRM查询患者待执行单
     */
    @PostMapping("/nurse/execute-sheet")
    public AbcServiceResponse<AbcListPage<PatientNurseWaitExecuteSheetView>> queryPatientNurseExecuteSheet(@Valid @RequestBody QueryPatientNurseExecuteSheetForScrmReq req) {
        AbcListPage<PatientNurseWaitExecuteSheetView> listPage = chargeScrmService.queryPatientNurseExecuteSheet(req.getChainId(), req.getClinicIds(), req.getPatientId(), req.getOffset(), req.getLimit());
        return new AbcServiceResponse<>(listPage);
    }

    /**
     * SCRM查询患者待执行项目
     */
    @PostMapping("/nurse/wait-execute")
    public AbcServiceResponse<AbcListPage<PatientNurseWaitExecuteView>> queryPatientNurseExecuteView(@Valid @RequestBody QueryPatientNurseWaitExecuteForScrmReq req) {
        List<PatientNurseWaitExecuteView> view  = chargeScrmService.queryPatientNurseExecuteView(req.getChainId(), req.getClinicIds(), req.getPatientIds());
        AbcListPage<PatientNurseWaitExecuteView> listPage = new AbcListPage<>();
        listPage.setRows(view);
        return new AbcServiceResponse<>(listPage);
    }

}
