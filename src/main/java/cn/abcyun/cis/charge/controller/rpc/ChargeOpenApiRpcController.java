package cn.abcyun.cis.charge.controller.rpc;

import cn.abcyun.cis.charge.api.model.openapi.OpenApiPatientChargeSheetView;
import cn.abcyun.cis.charge.api.model.openapi.OpenApiQueryPatientChargeSheetReq;
import cn.abcyun.cis.charge.api.model.openapi.OpenOpsCommonRsp;
import cn.abcyun.cis.charge.api.model.openapi.nurse.OpenApiCancelExecuteRecordReq;
import cn.abcyun.cis.charge.api.model.openapi.nurse.OpenApiChargeNurseListRsp;
import cn.abcyun.cis.charge.api.model.openapi.nurse.OpenApiCreateExecuteRecordReq;
import cn.abcyun.cis.charge.api.model.openapi.nurse.OpenApiExecuteRecordListRsp;
import cn.abcyun.cis.charge.rpc.model.ChargeSheetViewList;
import cn.abcyun.cis.charge.service.ChargeOpenApiService;
import cn.abcyun.cis.charge.service.dto.ChargeSheetView;
import cn.abcyun.cis.core.aop.LogReqAndRsp;
import cn.abcyun.common.model.AbcListPage;
import cn.abcyun.common.model.AbcServiceResponse;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDate;

/**
 * charge 开放平台接口
 *
 * <AUTHOR>
 * @date 2023/8/28 18:56
 **/
@Slf4j
@RestController
@RequestMapping("/rpc/charges/open-api")
@Api(tags = "开放平台接口")
public class ChargeOpenApiRpcController {

    @Autowired
    private ChargeOpenApiService chargeOpenApiService;

    /**
     * 查询收费单详情，详情中包含收费明细的子项信息
     */
    @GetMapping("/{chargeSheetId}/detail")
    @LogReqAndRsp(rpcCallStat = true)
    public AbcServiceResponse<ChargeSheetView> getChargeSheetWithTransactionRecordsViewById(@PathVariable("chargeSheetId") String chargeSheetId) {
        ChargeSheetView chargeSheetView = chargeOpenApiService.findChargeSheetById(chargeSheetId);
        return new AbcServiceResponse<>(chargeSheetView);
    }

    /**
     * 按天查询执行列表
     */
    @GetMapping("/nurse/query-by-date")
    @LogReqAndRsp(rpcCallStat = true)
    public AbcServiceResponse<OpenApiChargeNurseListRsp> listChargeNurseByDate(
            @RequestParam("chainId") String chainId,
            @RequestParam("clinicId") String clinicId,
            @RequestParam("date") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate date) {
        OpenApiChargeNurseListRsp rsp = chargeOpenApiService.listChargeNurseByDate(chainId, clinicId, date);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 查询执行单详情
     */
    @GetMapping("/nurse/{chargeSheetId}")
    @LogReqAndRsp(rpcCallStat = true)
    public AbcServiceResponse<ChargeSheetView> getChargeNurseDetail(
            @RequestParam("chainId") String chainId,
            @RequestParam("clinicId") String clinicId,
            @PathVariable("chargeSheetId") String chargeSheetId) {
        ChargeSheetView chargeSheetView = chargeOpenApiService.getChargeNurseDetail(chainId, clinicId, chargeSheetId);
        return new AbcServiceResponse<>(chargeSheetView);
    }

    /**
     * 创建执行记录
     */
    @PostMapping("/{chargeSheetId}/execute-records")
    @LogReqAndRsp
    public AbcServiceResponse<OpenOpsCommonRsp> createExecuteRecord(
            @PathVariable("chargeSheetId") String chargeSheetId,
            @Valid @RequestBody OpenApiCreateExecuteRecordReq req) {
        chargeOpenApiService.createExecuteRecord(chargeSheetId, req);
        return new AbcServiceResponse<>(OpenOpsCommonRsp.success());
    }

    /**
     * 查询执行记录
     */
    @GetMapping("/{chargeSheetId}/execute-records")
    @LogReqAndRsp(rpcCallStat = true)
    public AbcServiceResponse<OpenApiExecuteRecordListRsp> executeRecords(@PathVariable("chargeSheetId") String chargeSheetId,
                                                                          @RequestParam("chainId") String chainId,
                                                                          @RequestParam("clinicId") String clinicId) {
        OpenApiExecuteRecordListRsp openApiExecuteRecordListRsp = chargeOpenApiService.listExecuteRecordByChargeSheetId(chargeSheetId, clinicId, chainId);
        return new AbcServiceResponse<>(openApiExecuteRecordListRsp);
    }

    /**
     * 撤销执行记录
     */
    @PostMapping("/{chargeSheetId}/execute-records/{executeRecordId}/cancel")
    @LogReqAndRsp
    public AbcServiceResponse<OpenOpsCommonRsp> cancelExecuteRecord(@PathVariable("chargeSheetId") String chargeSheetId,
                                                                    @PathVariable("executeRecordId") String executeRecordId,
                                                                    @Valid @RequestBody OpenApiCancelExecuteRecordReq req) {
        chargeOpenApiService.cancelExecuteRecord(chargeSheetId, executeRecordId, req);
        return new AbcServiceResponse<>(OpenOpsCommonRsp.success());
    }

    /**
     * 通过就诊单ID获取收费单列表
     */
    @GetMapping("/by-patient-order-id/{patientOrderId}")
    @LogReqAndRsp(rpcCallStat = true)
    public AbcServiceResponse<ChargeSheetViewList> getChargeSheetViewListByPatientOrderId(@PathVariable("patientOrderId") String patientOrderId,
                                                                                          @RequestParam("chainId") String chainId,
                                                                                          @RequestParam("clinicId") String clinicId) {
        ChargeSheetViewList chargeSheetViewList = chargeOpenApiService.getChargeSheetViewListByPatientOrderId(chainId, clinicId, patientOrderId);
        return new AbcServiceResponse<>(chargeSheetViewList);
    }

    /**
     * 通过患者ID获取收费单列表
     */
    @PostMapping("/patient/query")
    @LogReqAndRsp(rpcCallStat = true)
    public AbcServiceResponse<AbcListPage<OpenApiPatientChargeSheetView>> queryPatientChargeSheets(@Valid @RequestBody OpenApiQueryPatientChargeSheetReq req) {
        AbcListPage<OpenApiPatientChargeSheetView> listPage = chargeOpenApiService.queryPatientChargeSheets(req);
        return new AbcServiceResponse<>(listPage);
    }

}
