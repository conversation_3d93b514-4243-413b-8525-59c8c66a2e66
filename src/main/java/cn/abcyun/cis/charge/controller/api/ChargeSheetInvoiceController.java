package cn.abcyun.cis.charge.controller.api;

import cn.abcyun.cis.charge.api.model.ChonghongChargeInvoiceReq;
import cn.abcyun.cis.charge.api.model.ChonghongChargeSheetInvoiceRsp;
import cn.abcyun.cis.charge.api.model.invoice.ChargeSheetInvoiceStatusRsp;
import cn.abcyun.cis.charge.service.ChargeSheetInvoiceService;
import cn.abcyun.cis.charge.service.dto.print.ChargeSheetPrintView;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.abcyun.cis.core.aop.LogReqAndRsp;
import cn.abcyun.common.model.AbcServiceResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v2/charges/invoice")
@Api(value = "ChargeSheetInvoiceController", description = "收费单发票相关接口", produces = "application/json")
public class ChargeSheetInvoiceController {

    @Autowired
    private ChargeSheetInvoiceService chargeSheetInvoiceService;

    /**
     * 收费单开冲红发票
     */
    @LogReqAndRsp
    @PutMapping("/{chargeSheetId}/chonghong")
    @ApiOperation(value = "发票冲红", produces = "application/json")
    public AbcServiceResponse<ChonghongChargeSheetInvoiceRsp> chonghongInvoice(
            @RequestBody ChonghongChargeInvoiceReq req,
            @PathVariable(value = "chargeSheetId") String chargeSheetId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) {
        return new AbcServiceResponse<>(chargeSheetInvoiceService.chonghongInvoice(req, chargeSheetId, chainId, clinicId, employeeId));
    }

    /**
     * 查看收费单的开票状态
     */
    @LogReqAndRsp
    @GetMapping("/{chargeSheetId}")
    @ApiOperation(value = "查看收费单的开票状态", produces = "application/json")
    public AbcServiceResponse<ChargeSheetInvoiceStatusRsp> getChargeSheetInvoiceStatus(
            @PathVariable(value = "chargeSheetId") String chargeSheetId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {
        return new AbcServiceResponse<>(chargeSheetInvoiceService.getChargeSheetInvoiceStatus(chargeSheetId, clinicId));
    }


    /**
     * 查看收费单的收据
     */
    @LogReqAndRsp
    @GetMapping("/{chargeSheetId}/receipt")
    @ApiOperation(value = "查看收费单的收据", produces = "application/json")
    public AbcServiceResponse<ChargeSheetPrintView> getChargeSheetReceipt(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @PathVariable(value = "chargeSheetId") String chargeSheetId) {
        return new AbcServiceResponse<>(chargeSheetInvoiceService.getChargeSheetReceipt(chainId, clinicId, chargeSheetId));
    }

}
