package cn.abcyun.cis.charge.controller;

import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.cis.charge.api.model.CreateChargeSheetRsp;
import cn.abcyun.cis.charge.api.model.RefundChargeSheetRsp;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.service.dto.ChargeSheetAbstract;
import org.apache.commons.lang3.text.translate.NumericEntityEscaper;

import java.util.List;
import java.util.Objects;

public class StatusNameTranslator {

//    public static void mergeStatus(List<ChargeSheetAbstract> abstractList) {
//        if (abstractList == null) {
//            return;
//        }
//
//        abstractList.forEach(sheetAbstract -> sheetAbstract.setStatus(ChargeUtils.mergeChargeSheetStatus(sheetAbstract.getStatus(), sheetAbstract.getRegistrationChargeSheetStatus())));
//    }

    public static void translate(List<ChargeSheetAbstract> abstractList) {
        if (abstractList == null) {
            return;
        }

        abstractList.forEach(sheetAbstract -> translate(sheetAbstract));
    }

    public static void translate(CreateChargeSheetRsp createChargeSheetRsp) {
        if (createChargeSheetRsp != null) {
            createChargeSheetRsp.setStatusName(translateChargeSheetStatus(createChargeSheetRsp.getOwedStatus(), createChargeSheetRsp.getStatus(),false));
        }
    }

    public static void translate(RefundChargeSheetRsp refundChargeSheetRsp) {
        if (refundChargeSheetRsp != null) {
            refundChargeSheetRsp.setStatusName(translateChargeSheetStatus(refundChargeSheetRsp.getOwedStatus(), refundChargeSheetRsp.getStatus(), false, null));
        }
    }

    public static void translate(ChargeSheetAbstract sheetAbstract) {
        if (sheetAbstract != null) {
            sheetAbstract.setStatusName(translateChargeSheetStatus(sheetAbstract.getOwedStatus(), sheetAbstract.getStatus(), sheetAbstract.getIsDraft() == 1, sheetAbstract.getOutpatientStatus(), sheetAbstract.getIsClosed()));
        }
    }

    public static String translateChargeSheetStatus(int owedStatus, int status) {
        return translateChargeSheetStatus(owedStatus, status, false, null);
    }

    public static String translateChargeSheetStatus(int owedStatus, int status, boolean isDraft) {
        return translateChargeSheetStatus(owedStatus, status, isDraft, null);
    }

    public static String translateChargeSheetStatus(int owedStatus, int status, boolean isDraft, Integer outpatientStatus) {
        return translateChargeSheetStatus(owedStatus, status, isDraft, outpatientStatus, 0);
    }

    public static String translateChargeSheetStatus(int owedStatus, int status, boolean isDraft, Integer outpatientStatus, int isClosed) {
        String statusName;

        if (isClosed == 1) {
            statusName = "关闭";
            return statusName;
        }

        if (owedStatus == ChargeSheet.OwedStatus.OWING) {
            statusName = "欠费";
            return statusName;
        }

        if (outpatientStatus != null && outpatientStatus == ChargeSheet.OutpatientStatus.WAITING) {
            statusName = "待诊";
            return statusName;
        }
        switch (status) {
            case Constants.ChargeSheetStatus.UNCHARGED:
                statusName = isDraft ? "挂单" : "待收";
                break;
            case Constants.ChargeSheetStatus.PART_CHARGED:
                statusName = "待收";
                break;
            case Constants.ChargeSheetStatus.CHARGED:
            case Constants.ChargeSheetStatus.PART_REFUNDED:
                statusName = "已收";
                break;
            case Constants.ChargeSheetStatus.REFUNDED:
                statusName = "已退";
                break;
            default:
                statusName = "";
        }
        return statusName;
    }


    public static void translateForNurse(List<ChargeSheetAbstract> abstractList) {
        if (abstractList == null) {
            return;
        }
        abstractList.forEach(StatusNameTranslator::translateForNurse);
    }

    public static void translateForNurse(ChargeSheetAbstract sheetAbstract) {
        if (sheetAbstract != null) {
            sheetAbstract.setStatusName(translateChargeSheetStatusForNurse(sheetAbstract.getIsClosed(), sheetAbstract.getStatus(), sheetAbstract.getExecuteStatus(), sheetAbstract.getOnlyExecuteAfterPaid()));
        }
    }

    public static String translateChargeSheetStatusForNurse(int isClosed, int chargeStatus, int executeStatus, Integer onlyExecuteAfterPaid) {
        if (isClosed == YesOrNo.YES) {
            return "已关闭";
        }
        String statusName;
        switch (chargeStatus) {
            case Constants.ChargeSheetStatus.UNCHARGED:
            case Constants.ChargeSheetStatus.PART_CHARGED:
                statusName = "未收费";
                if (!Objects.equals(onlyExecuteAfterPaid,1) && executeStatus != ChargeSheet.ExecuteStatus.NONE && executeStatus != ChargeSheet.ExecuteStatus.REFUNDED) {
                    statusName = executeStatus == ChargeSheet.ExecuteStatus.WAITING ? "待执行" : "已执行";
                }
                break;
            case Constants.ChargeSheetStatus.CHARGED:
            case Constants.ChargeSheetStatus.PART_REFUNDED:
                statusName = "已收费";
                if (executeStatus != ChargeSheet.ExecuteStatus.NONE && executeStatus != ChargeSheet.ExecuteStatus.REFUNDED) {
                    statusName = executeStatus == ChargeSheet.ExecuteStatus.WAITING ? "待执行" : "已执行";
                }
                break;
            case Constants.ChargeSheetStatus.REFUNDED:
                statusName = "已退费";
                break;
            default:
                statusName = "";
        }
        return statusName;
    }

    public static String translateChargeFormItemStatusName(int status) {
        String statusName;
        switch (status) {
            case Constants.ChargeFormItemStatus.UNCHARGED:
                statusName = "未收费";
                break;
            case Constants.ChargeFormItemStatus.CHARGED:
                statusName = "已收费";
                break;
            case Constants.ChargeFormItemStatus.REFUNDED:
                statusName = "已退费";
                break;
            case Constants.ChargeFormItemStatus.UNSELECTED:
                statusName = "已退单";
                break;
            default:
                statusName = "";
        }
        return statusName;
    }

    public static String translateChargeSheetInvoiceStatusName(int invoiceStatus) {
        String statusName;
        switch (invoiceStatus) {
            case Constants.ChargeSheetInvoiceStatus.NONE:
                statusName = "不可开票";
                break;
            case Constants.ChargeSheetInvoiceStatus.INVOICE_WAITING:
                statusName = "可开票";
                break;
            case Constants.ChargeSheetInvoiceStatus.INVOICE_BEING:
                statusName = "开票中";
                break;
            case Constants.ChargeSheetInvoiceStatus.INVOICE_SUCCESS:
                statusName = "开票成功";
                break;
            case Constants.ChargeSheetInvoiceStatus.INVOICE_FEE_INCONSISTENT:
                statusName = "开票金额异常";
                break;
            case Constants.ChargeSheetInvoiceStatus.INVOICE_FAIL:
                statusName = "开票失败";
                break;
            case Constants.ChargeSheetInvoiceStatus.INVOICE_INVALING:
                statusName = "冲红中";
                break;
            case Constants.ChargeSheetInvoiceStatus.INVOICE_INVALID:
                statusName = "已红冲";
                break;
            case Constants.ChargeSheetInvoiceStatus.INVOICE_REFUND:
                statusName = "已作废";
                break;
            default:
                statusName = "";
        }
        return statusName;
    }
}
