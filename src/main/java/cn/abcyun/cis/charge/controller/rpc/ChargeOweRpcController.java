package cn.abcyun.cis.charge.controller.rpc;

import cn.abcyun.cis.charge.api.model.PatientOweAmountInfoListReq;
import cn.abcyun.cis.charge.api.model.PatientOweAmountInfoListRsp;
import cn.abcyun.cis.charge.facade.ChargeOweRpcFacade;
import cn.abcyun.cis.charge.service.ChargeOweSheetService;
import cn.abcyun.cis.charge.service.dto.ChargeSheetView;
import cn.abcyun.cis.commons.CisServiceResponse;
import cn.abcyun.cis.core.aop.LogReqAndRsp;
import cn.abcyun.common.model.AbcServiceResponse;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
    @RequestMapping("/rpc/charges/owe")
public class ChargeOweRpcController {

    private final ChargeOweRpcFacade chargeOweRpcFacade;
    private final ChargeOweSheetService chargeOweSheetService;

    @Autowired
    public ChargeOweRpcController(ChargeOweRpcFacade chargeOweRpcFacade,
                                  ChargeOweSheetService chargeOweSheetService) {

        this.chargeOweRpcFacade = chargeOweRpcFacade;
        this.chargeOweSheetService = chargeOweSheetService;
    }

    @GetMapping("/{chargeOweSheetId}/charge-sheet-detail")
    @LogReqAndRsp
    public CisServiceResponse<ChargeSheetView> getChargeSheetViewByChargeOweSheetId(@PathVariable("chargeOweSheetId") String chargeOweSheetId,
                                                                                    @RequestParam("clinicId") String clinicId){
        ChargeSheetView chargeSheetView = chargeOweRpcFacade.getChargeSheetViewByChargeOweSheetId(clinicId, chargeOweSheetId);
        return new CisServiceResponse<>(chargeSheetView);
    }

    /**
     * 根据患者ids查询欠费总金额
     * @return
     */
    @PostMapping("/patient/owing-amount")
    @LogReqAndRsp
    @ApiOperation(value = "根据患者ids查询欠费总金额", produces = "application/json")
    public AbcServiceResponse<PatientOweAmountInfoListRsp> getChargeSheetOwingAmountByPatientIds(@RequestBody @Valid PatientOweAmountInfoListReq req) {


        PatientOweAmountInfoListRsp rsp = chargeOweSheetService.getPatientOweAmountInfoListByPatientIds(req.getChainId(), req.getClinicId(), req.getPatientIds());
        return new AbcServiceResponse<>(rsp);
    }
}
