package cn.abcyun.cis.charge.controller.api;

import cn.abcyun.cis.charge.api.model.ChargeSheetAbstractListRsp;
import cn.abcyun.cis.charge.api.model.CreateChargeSheetForTherapyRsp;
import cn.abcyun.cis.charge.api.model.CreateChargeSheetReq;
import cn.abcyun.cis.charge.api.model.DoExecuteReq;
import cn.abcyun.cis.charge.api.model.DoExecuteRsp;
import cn.abcyun.cis.charge.api.model.ExecuteActionListRsp;
import cn.abcyun.cis.charge.api.model.ChargeSheetPrivilegeForNurseCheckRsp;
import cn.abcyun.cis.charge.base.ChargeServiceError;
import cn.abcyun.cis.charge.base.ChargeServiceException;
import cn.abcyun.cis.charge.controller.StatusNameTranslator;
import cn.abcyun.cis.charge.service.ChargeExecuteService;
import cn.abcyun.cis.charge.service.ChargeNurseService;
import cn.abcyun.cis.charge.service.dto.ChargeExecuteActionView;
import cn.abcyun.cis.charge.service.dto.ChargeSheetAbstract;
import cn.abcyun.cis.charge.service.dto.ChargeSheetView;
import cn.abcyun.cis.charge.util.ChargeUtils;
import cn.abcyun.cis.commons.exception.CisCustomException;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.abcyun.cis.commons.util.DateUtils;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.cis.core.aop.LogReqAndRsp;
import cn.abcyun.common.model.AbcServiceResponse;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 执行站 前端控制器
 *
 * <AUTHOR>
 * @version ChargeNurseController.java, 2022/3/28 上午10:23
 */
@RestController
@RequestMapping("/api/v2/charges/nurse")
@Api(value = "ChargeNurseController", description = "执行站 前端控制器", produces = "application/json")
@Slf4j
public class ChargeNurseController {

    private final ChargeExecuteService chargeExecuteService;
    private final ChargeNurseService   chargeNurseService;

    @Autowired
    public ChargeNurseController(ChargeExecuteService chargeExecuteService,
                                 ChargeNurseService chargeNurseService) {
        this.chargeExecuteService = chargeExecuteService;
        this.chargeNurseService = chargeNurseService;
    }

    /**
     * 护士站支持批量执行后，业务逻辑进行修改，see {{@link ChargeExecuteRecordController}} 为了 app 老版本兼容，对该方法逻辑进行修改
     *
     * @param chargeFormItemId 收费单子项id
     * @param doExecuteReq     请求参数
     * @param employeeId       门店id
     * @param clinicId         门店id
     * @return 结果
     */
    @PutMapping("/execute/{chargeFormItemId}")
    @LogReqAndRsp
    @Deprecated
    @ApiOperation(value = "护士站增加执行记录 Deprecated", hidden = true)
    public AbcServiceResponse<DoExecuteRsp> putExecuteChargeFormItem(
            @PathVariable("chargeFormItemId") String chargeFormItemId,
            @RequestBody DoExecuteReq doExecuteReq,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) {

        DoExecuteRsp doExecuteRsp = chargeExecuteService.doExecuteChargeFormItem(chargeFormItemId, doExecuteReq.getCount(), doExecuteReq.getExecutorIds(),
                doExecuteReq.getExecuteEffect(), doExecuteReq.getExecuteDate(), doExecuteReq.getIsImport(), clinicId, chainId, employeeId);
        return new AbcServiceResponse<>(doExecuteRsp);
    }

    @GetMapping("/execute/{chargeFormItemId}/actions")
    @ApiOperation(value = "根据收费单子项对应的执行记录", produces = "application/json")
    @LogReqAndRsp
    public AbcServiceResponse<ExecuteActionListRsp> getExecuteActionList(
            @PathVariable("chargeFormItemId") String chargeFormItemId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {

        if (TextUtils.isEmpty(chainId)) {
            chainId = clinicId;
        }
        List<ChargeExecuteActionView> executeActionList = chargeExecuteService.getExecuteActionListByChargeFormItemId(chargeFormItemId, clinicId, chainId);
        ExecuteActionListRsp          rsp               = new ExecuteActionListRsp();
        rsp.setActions(executeActionList);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 护士站支持批量执行后，业务逻辑进行修改，see {{@link ChargeExecuteRecordController}} 为了 app 老版本兼容，对该方法逻辑进行修改
     *
     * @param executeActionId 执行记录id
     * @param employeeId      门店id
     * @param chainId         门店id
     * @param clinicId        门店id
     * @return 查询结果
     */
    @PutMapping("/execute/cancel/{actionId}")
    @LogReqAndRsp
    @ApiOperation(value = "取消执行记录 Deprecated", hidden = true)
    @Deprecated
    public AbcServiceResponse<ExecuteActionListRsp> putCancelExecuteAction(
            @PathVariable("actionId") String executeActionId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {

        chargeExecuteService.cancelChargeExecuteAction(executeActionId, clinicId, chainId, employeeId);

        ExecuteActionListRsp rsp = new ExecuteActionListRsp();
        rsp.setActions(Lists.newArrayList());
        return new AbcServiceResponse<>(rsp);
    }

    @GetMapping("/{chargeSheetId}")
    @LogReqAndRsp
    @ApiOperation(value = "执行站收费单详情", produces = "application/json")
    public AbcServiceResponse<ChargeSheetView> getChargeSheetForNurseById(@PathVariable("chargeSheetId") String chargeSheetId,
                                                                          @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                          @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId) {
        ChargeSheetView chargeSheetView = chargeNurseService.findChargeSheetById(chargeSheetId, clinicId, chainId);
        if (chargeSheetView == null) {
            throw new NotFoundException();
        }
        chargeSheetView.setCanUnLockChargeSheet(ChargeUtils.isChargerLockStatus(chargeSheetView.getLockStatus()));
        return new AbcServiceResponse<>(chargeSheetView);
    }

    @GetMapping("/my-executed")
    @LogReqAndRsp
    @ApiOperation(value = "我的执行列表", produces = "application/json")
    public AbcServiceResponse<ChargeSheetAbstractListRsp> getMyExecutedChargeSheetAbstractList(@RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                                               @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                               @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                               @RequestParam(value = "offset", required = false) Integer offset,
                                                                                               @RequestParam(value = "limit", required = false) Integer limit,
                                                                                               @RequestParam(value = "beginDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date beginDate,
                                                                                               @RequestParam(value = "endDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {

        if (offset == null || offset < 0) {
            offset = 0;
        }

        if (limit == null || limit < 0) {
            limit = 20;
        }

        Instant beginTime = Objects.nonNull(beginDate) ? DateUtils.getStartTime(beginDate).toInstant() : null;
        Instant endTime   = Objects.nonNull(endDate) ? DateUtils.getEndTime(endDate).toInstant() : null;

        ChargeSheetAbstractListRsp rspData = chargeNurseService.findMyExecutedChargeSheetAbstractList(chainId, clinicId, employeeId, beginTime, endTime, offset, limit);

        return new AbcServiceResponse<>(rspData);
    }

    @PostMapping("")
    @LogReqAndRsp
    @ApiOperation(value = "执行站开单", produces = "application/json")
    public AbcServiceResponse<CreateChargeSheetForTherapyRsp> createChargeSheet(@RequestBody CreateChargeSheetReq req,
                                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) int hisType) {

        if (req.getTherapySheet() != null) {
            log.info("therapySheet不为空，说明走的还是老协议，不让创建，需要更新前端版本再创建");
            throw new CisCustomException(HttpStatus.BAD_REQUEST.value(), "版本已更新，请刷新后再试");
        }

        CreateChargeSheetForTherapyRsp rsp = chargeNurseService.createChargeSheetForTherapy(req, chainId, clinicId, employeeId, hisType);

        return new AbcServiceResponse<>(rsp);
    }

    @GetMapping("/today")
    @ApiOperation(value = "今天的执行站收费单列表", produces = "application/json")
    @LogReqAndRsp
    public AbcServiceResponse<ChargeSheetAbstractListRsp> getChargeSheetAbstractTodayListForNurse(@RequestParam(value = "ownerId", required = false) String ownerId,
                                                                                                  @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {

        List<ChargeSheetAbstract> result = chargeNurseService.findChargeSheetAbstractTodayList(clinicId, ownerId);
        StatusNameTranslator.translateForNurse(result);
        ChargeSheetAbstractListRsp rspData = new ChargeSheetAbstractListRsp();
        rspData.setResult(result);
        return new AbcServiceResponse<>(rspData);
    }

    @GetMapping("")
    @LogReqAndRsp
    @ApiOperation(value = "执行站列表查询", produces = "application/json")
    public AbcServiceResponse<ChargeSheetAbstractListRsp> getChargeSheetAbstractListForNurse(@RequestParam(value = "offset", required = false) Integer offset,
                                                                                             @RequestParam(value = "limit", required = false) Integer limit,
                                                                                             @RequestParam(value = "keyword", required = false) String keyword,
                                                                                             @RequestParam(value = "executeStatus", required = false) String filterExecuteStatus,
                                                                                             @RequestParam(value = "beginDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date beginDate,
                                                                                             @RequestParam(value = "endDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
                                                                                             @RequestParam(value = "clinicScope", defaultValue = "0") Integer clinicScope,
                                                                                             @RequestParam(value = "ownerId", required = false) String ownerId,
                                                                                             @RequestParam(value = "sourceTypeList", required = false) String sourceTypeListStr,
                                                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) {
        if (offset == null || offset < 0) {
            offset = 0;
        }
        if (limit == null || limit < 0) {
            limit = 20;
        }
        if (endDate == null) {
            endDate = new Date();
        }
        if (beginDate == null) {
            beginDate = new Date();
        }
        beginDate = DateUtils.getStartTime(beginDate);
        endDate = DateUtils.getEndTime(endDate);
        //校验开始时间和结束时间不能超过100天
        if (endDate.getTime() - beginDate.getTime() > 100 * 24 * 60 * 60 * 1000L) {
            throw new ChargeServiceException(ChargeServiceError.CHARGE_NURSE_QUERY_TIME_OVERFLOW);
        }
        List<Integer> sourceTypeList = !StringUtils.isEmpty(sourceTypeListStr) ? Arrays.stream(sourceTypeListStr.split(","))
                .map(NumberUtils::toInt).collect(Collectors.toList()) : null;

        ChargeSheetAbstractListRsp rspData = chargeNurseService.findChargeSheetAbstractList(offset, limit,
                keyword, chainId, clinicId, employeeId, filterExecuteStatus,
                beginDate.toInstant(), endDate.toInstant(), clinicScope, ownerId, sourceTypeList);

        return new AbcServiceResponse<>(rspData);
    }

    @GetMapping(value = "/{chargeSheetId}/sheet-privilege/check")
    @LogReqAndRsp
    @ApiOperation(value = "收费单详情权限校验", produces = "application/json")
    public AbcServiceResponse<ChargeSheetPrivilegeForNurseCheckRsp> checkExecutedSheetDetailPrivilege(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                                      @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                                                      @PathVariable String chargeSheetId) {

        return new AbcServiceResponse<>(chargeNurseService.checkChargeSheetPrivilegeForNurse(chainId, employeeId, chargeSheetId));
    }

}
