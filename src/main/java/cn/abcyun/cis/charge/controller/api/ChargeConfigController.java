package cn.abcyun.cis.charge.controller.api;

import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Employee;
import cn.abcyun.bis.rpc.sdk.cis.model.common.YesOrNo;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.config.GoodsConfigView;
import cn.abcyun.cis.charge.api.model.BaseSuccessRsp;
import cn.abcyun.cis.charge.api.model.ChargeConfigDetailView;
import cn.abcyun.cis.charge.api.model.UpdateChargeConfigReq;
import cn.abcyun.cis.charge.api.model.UpdatePayModeReq;
import cn.abcyun.cis.charge.base.ChargeServiceError;
import cn.abcyun.cis.charge.base.ChargeServiceException;
import cn.abcyun.cis.charge.service.ChargeConfigService;
import cn.abcyun.cis.charge.service.rpc.CisGoodsService;
import cn.abcyun.cis.charge.util.EmojiUtils;
import cn.abcyun.cis.charge.util.GoodsLockingUtils;
import cn.abcyun.cis.commons.CisServiceError;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.abcyun.cis.commons.model.CisClinicType;
import cn.abcyun.cis.core.aop.LogReqAndRsp;
import cn.abcyun.common.model.AbcListPage;
import cn.abcyun.common.model.AbcServiceResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/api/v2/charges/config")
@Validated
@Slf4j
@Api(value = "ChargeConfigController", description = "收费设置相关接口", produces = "application/json")
public class ChargeConfigController {

    @Autowired
    private ChargeConfigService chargeConfigService;

    @Autowired
    private CisGoodsService cisGoodsService;

    /**
     * 查询收费设置
     */
    @GetMapping("")
    @LogReqAndRsp
    public AbcServiceResponse<ChargeConfigDetailView> getConfigDetail(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) String viewMode) {
        return new AbcServiceResponse<>(chargeConfigService.getChainConfigDetail(chainId, clinicId, null, clinicType, viewMode));
    }

    /**
     * 退费审核人列表
     *
     * @param chainId  连锁id
     * @param clinicId 诊所 ID
     * @return {@link AbcServiceResponse }<{@link AbcListPage }<{@link Employee }>>
     */
    @GetMapping("/refund-check-employees")
    @LogReqAndRsp
    public AbcServiceResponse<AbcListPage<Employee>> getChargeRefundCheckEmployees(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {
        return new AbcServiceResponse<>(AbcListPage.of(chargeConfigService.getChargeRefundCheckEmployees(chainId, clinicId, YesOrNo.YES)));
    }

    /**
     * 查询可用的收费方式
     *
     * @param chainId
     * @return
     */
    @GetMapping("/available")
    @LogReqAndRsp
    public AbcServiceResponse<ChargeConfigDetailView> getAvailableConfigDetail(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) String viewMode) {

        boolean needAutoSendOrderInfoSwitch = false;
        if (clinicType == CisClinicType.CHAIN_BRANCH_CLINIC || (clinicType == CisClinicType.INDEPENDENT_CLINIC && Objects.equals(CisJWTUtils.CIS_VIEW_MODE_NORMAL, viewMode))) {
            needAutoSendOrderInfoSwitch = true;
        }

        ChargeConfigDetailView rsp = chargeConfigService.getBranchConfigDetail(chainId, clinicId, needAutoSendOrderInfoSwitch);

        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 查询连锁所有的支付方式（包含了已删除的）
     */
    @GetMapping("/pay-mode")
    @LogReqAndRsp
    @ApiOperation(value = "查询连锁所有的支付方式（包含了已删除的）")
    public AbcServiceResponse<AbcListPage<ChargeConfigDetailView.ChargePayModeConfigView>> getChainPayMode(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId) {
        List<ChargeConfigDetailView.ChargePayModeConfigView> payModes = chargeConfigService.getChainPayMode(chainId);
        AbcListPage<ChargeConfigDetailView.ChargePayModeConfigView> listPage = new AbcListPage<>();
        listPage.setRows(payModes);
        return new AbcServiceResponse<>(listPage);
    }

//    @PostMapping("/init")
//    @LogReqAndRsp
//    public AbcServiceResponse init(
//            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
//            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
//            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId
//            ) throws ChargeServiceException {
//        if (!chainId.equals(clinicId)) {
//            log.info("无权限设置收费配置, chainId: {}, clinicId: {}", chainId, clinicId);
//            throw new ChargeServiceException(new CisServiceError(401, "无权限设置收费配置"));
//        }
//
//        chargePayModeRelationService.initPayModes(chainId, employeeId);
//
//        return new AbcServiceResponse<>(HttpStatus.OK);
//    }

    @PostMapping("")
    @LogReqAndRsp
    @ApiOperation(value = "修改收费设置")
    public AbcServiceResponse<BaseSuccessRsp> updateChargeConfig(
            @Valid @RequestBody UpdateChargeConfigReq updateChargeConfigReq,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) String viewMode) {
        updateChargeConfigReq.validate();
        if (Objects.isNull(updateChargeConfigReq.getDoctorRegisteredBargainSwitch())) {
            updateChargeConfigReq.setDoctorRegisteredBargainSwitch(1);
        }
        if (Objects.isNull(updateChargeConfigReq.getReservationRegisteredBargainSwitch())) {
            updateChargeConfigReq.setReservationRegisteredBargainSwitch(1);
        }
        // 判断整单收退费开关与退费条件限制开关是否冲突
        this.beforeCheckUpdateChargeConfig(clinicId, updateChargeConfigReq);
        //微信自助支付过后，子店也能拉到关于支付相关的配置
        if (clinicType == CisClinicType.CHAIN_BRANCH_CLINIC && Objects.equals(CisJWTUtils.CIS_VIEW_MODE_NORMAL, viewMode)) {
            chargeConfigService.updateConfigClinicScope(updateChargeConfigReq, chainId, clinicId, employeeId);
            BaseSuccessRsp rsp = new BaseSuccessRsp(200, "成功");
            return new AbcServiceResponse<>(rsp);
        }
        this.checkUpdateChargeConfigReq(updateChargeConfigReq);
        chargeConfigService.updateChargeConfig(updateChargeConfigReq, chainId, clinicId, employeeId, clinicType, viewMode);
        BaseSuccessRsp rsp = new BaseSuccessRsp(200, "成功");
        return new AbcServiceResponse<>(rsp);
    }

    /**
     *  配置保存之前的校验
     */
    private void beforeCheckUpdateChargeConfig(String clinicId, UpdateChargeConfigReq config) {
        int wholeSheetOperateEnable = config.getWholeSheetOperateEnable();
        // 开启过后需要校验开出设置
        if (wholeSheetOperateEnable == YesOrNo.YES) {
            GoodsConfigView goodsConfigView = cisGoodsService.getGoodsConfigNoCache(clinicId);
            boolean enableNoStockGoods = GoodsLockingUtils.isEnableNoStockGoods(goodsConfigView);
            if (enableNoStockGoods) {
                throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_WHOLE_OPERATE_NOT_ALLOW_WITH_NO_STOCK);
            }
        }
    }

    private void checkUpdateChargeConfigReq(UpdateChargeConfigReq updateChargeConfigReq) throws ParamRequiredException, ChargeServiceException {
        updateChargeConfigReq.preCheck();

        UpdatePayModeReq updatePayModeReq = updateChargeConfigReq.getPayMode();
        if (updatePayModeReq == null) {
            return;
        }
        int enableCount = 0;
        if (!CollectionUtils.isEmpty(updatePayModeReq.getOptionalPayModes())) {
            for (UpdatePayModeReq.PayModeReq optionalPayMode : updatePayModeReq.getOptionalPayModes()) {
                if (optionalPayMode.getPayModeId() == null) {
                    throw new ParamRequiredException("optionalPayModes.payModeId");
                }
                if (optionalPayMode.getIsEnable() == 1) {
                    enableCount += 1;
                }
            }
        }
        if (!CollectionUtils.isEmpty(updatePayModeReq.getCustomizedPayModes())) {
            for (UpdatePayModeReq.PayModeReq customizedPayMode : updatePayModeReq.getCustomizedPayModes()) {
                if (customizedPayMode.getPayModeId() == null && StringUtils.isEmpty(customizedPayMode.getName())) {
                    throw new ParamRequiredException("customizedPayModes.name");
                }
                if (!StringUtils.isEmpty(customizedPayMode.getName())) {
                    if (customizedPayMode.getName().length() > 6) {
                        throw new ChargeServiceException(new CisServiceError(HttpStatus.BAD_REQUEST.value(), "收费渠道名不能超过6个字"));
                    }
                    if (customizedPayMode.getSort() == null) {
                        throw new ChargeServiceException(new CisServiceError(HttpStatus.BAD_REQUEST.value(), "排序字段不能为空"));
                    }
                    if (EmojiUtils.containsEmoji(customizedPayMode.getName())) {
                        throw new ChargeServiceException(new CisServiceError(HttpStatus.BAD_REQUEST.value(), "收费方式不能包含表情包"));
                    }
                }
                if (customizedPayMode.getIsEnable() == 1) {
                    enableCount += 1;
                }
            }
        }
        if (enableCount < 1) {
            throw new ChargeServiceException(ChargeServiceError.PAY_MODE_LEAST_ONE);
        }
    }

}
