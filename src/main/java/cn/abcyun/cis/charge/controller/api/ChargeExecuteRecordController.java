package cn.abcyun.cis.charge.controller.api;

import cn.abcyun.cis.charge.api.model.*;
import cn.abcyun.cis.charge.base.ChargeServiceError;
import cn.abcyun.cis.charge.base.ChargeServiceException;
import cn.abcyun.cis.charge.cold.ChargeFormItemDaoProxy;
import cn.abcyun.cis.charge.model.ChargeExecuteEffectEmployeeHabits;
import cn.abcyun.cis.charge.model.ChargeExecuteRecord;
import cn.abcyun.cis.charge.model.ChargeFormItem;
import cn.abcyun.cis.charge.repository.ChargeExecuteRecordRepository;
import cn.abcyun.cis.charge.service.ChargeExecuteRecordService;
import cn.abcyun.cis.charge.service.dto.ChargeExecuteRecordView;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.abcyun.cis.core.aop.LogReqAndRsp;
import cn.abcyun.common.model.AbcServiceResponse;
import io.swagger.annotations.Api;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

/**
 * 执行记录控制器
 *
 * <AUTHOR>
 * @version ChargeExecuteRecordController.java, 2020/7/30 下午4:04
 */
@RestController
@RequestMapping("/api/v2/charges/execute-records")
@Api(value = "ChargeExecuteRecordController", description = "执行记录前端接口", produces = "application/json")
public class ChargeExecuteRecordController {

    private final ChargeExecuteRecordService  chargeExecuteRecordService;
    private final ChargeFormItemDaoProxy chargeFormItemRepository;
    private final ChargeExecuteRecordRepository chargeExecuteRecordRepository;

    @Autowired
    public ChargeExecuteRecordController(ChargeExecuteRecordService chargeExecuteRecordService,
                                         ChargeFormItemDaoProxy chargeFormItemRepository,
                                         ChargeExecuteRecordRepository chargeExecuteRecordRepository) {
        this.chargeExecuteRecordService = chargeExecuteRecordService;
        this.chargeFormItemRepository = chargeFormItemRepository;
        this.chargeExecuteRecordRepository = chargeExecuteRecordRepository;
    }

    /**
     * 新增执行记录
     *
     * @param req        请求参数
     * @param clinicId   门店id
     * @param chainId    连锁id
     * @param employeeId 操作人id
     * @return 新增结果
     * @throws ParamRequiredException 异常
     * @throws ChargeServiceException 异常
     */
    @PostMapping("")
    @LogReqAndRsp
    public AbcServiceResponse<ChargeExecuteRecordView> createExecuteRecord(@RequestBody ExecuteRecordCreateReq req,
                                                                           @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                           @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                           @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) {
        if (CollectionUtils.isEmpty(req.getItems())) {
            throw new ParamRequiredException("执行记录子项集合");
        }
        // 1、根据chargeFormItemId查询chargeSheetId
        ChargeFormItem chargeFormItem = chargeFormItemRepository.findByChainIdAndIdAndIsDeleted(chainId, req.getItems().get(0).getChargeFormItemId(), 0);

        String chargeSheetId = Optional.ofNullable(chargeFormItem).map(ChargeFormItem::getChargeSheetId).orElse(null);
        if (StringUtils.isBlank(chargeSheetId)) {
            throw new ChargeServiceException(ChargeServiceError.CHARGE_FORM_ITEM_CHANGED);
        }

        ChargeExecuteRecordView recordView = chargeExecuteRecordService.createExecuteRecord(req, chargeSheetId, chainId, clinicId, employeeId);
        return new AbcServiceResponse<>(recordView);
    }

    /**
     * 根据收费单id查询 执行记录 集合
     *
     * @param chargeSheetId 收费单id
     * @param recordStatus  执行记录状态
     * @param clinicId      门店id
     * @param chainId       连锁id
     * @return 执行记录集合
     * @throws NotFoundException 异常
     */
    @GetMapping(value = {"/charge-sheet-{chargeSheetId}", "/charge-sheet/{chargeSheetId}"})
    @LogReqAndRsp
    public AbcServiceResponse<ChargeSheetExecuteRecordListRsp> listExecuteRecordByChargeSheetId(@PathVariable("chargeSheetId") String chargeSheetId,
                                                                                                @RequestParam(value = "recordStatus", required = false) Integer recordStatus,
                                                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId) throws NotFoundException, ChargeServiceException {
        ChargeSheetExecuteRecordListRsp rsp = chargeExecuteRecordService.listExecuteRecordByChargeSheetId(chargeSheetId, clinicId, chainId, recordStatus);

        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 撤销执行记录
     *
     * @param executeRecordId 执行记录id
     * @param employeeId      操作人id
     * @param clinicId        门店id
     * @param chainId         连锁id
     * @return 结果
     * @throws NotFoundException      异常
     * @throws ChargeServiceException 异常
     */
    @PutMapping("/{executeRecordId}/cancel")
    @LogReqAndRsp
    public AbcServiceResponse<ChargeExecuteRecordView> cancelChargeExecuteRecord(@PathVariable("executeRecordId") String executeRecordId,
                                                                                 @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                                 @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                 @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId) throws NotFoundException, ChargeServiceException {
        // 1、根据executeRecordId查询chargeSheetId
        String chargeSheetId = chargeExecuteRecordRepository.findByIdAndChainId(executeRecordId, chainId)
                .map(ChargeExecuteRecord::getChargeSheetId)
                .orElseThrow(() -> new NotFoundException("执行记录不存在"));

        ChargeExecuteRecordView executeRecordView = chargeExecuteRecordService.cancelChargeExecuteRecord(chargeSheetId, executeRecordId, clinicId, chainId, employeeId);

        return new AbcServiceResponse<>(executeRecordView);
    }

    /**
     * 获取执行记录详情
     *
     * @param executeRecordId 执行记录id
     * @param clinicId        门店id
     * @return 修改结果
     * @throws NotFoundException 异常
     */
    @GetMapping("/{executeRecordId}")
    @LogReqAndRsp
    public AbcServiceResponse<ChargeExecuteRecordView> getExecuteRecordViewById(@PathVariable("executeRecordId") String executeRecordId,
                                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId) throws NotFoundException, ChargeServiceException {

        ChargeExecuteRecordView executeRecordView = chargeExecuteRecordService.getExecuteRecordViewById(executeRecordId, clinicId, chainId);
        return new AbcServiceResponse<>(executeRecordView);
    }

    /**
     * 修改执行效果模板
     *
     * @param executeRecordId 执行记录id
     * @param req             请求参数
     * @param clinicId        门店id
     * @param employeeId      操作人
     * @return 修改结果
     * @throws NotFoundException 异常
     */
    @PutMapping("/{executeRecordId}")
    @LogReqAndRsp
    public AbcServiceResponse<ChargeExecuteRecordView> updateExecuteRecordEffect(@PathVariable("executeRecordId") String executeRecordId,
                                                                                 @RequestBody UpdateExecuteEffectReq req,
                                                                                 @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                 @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                 @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) throws NotFoundException, ChargeServiceException {

        ChargeExecuteRecordView executeRecordView = chargeExecuteRecordService.updateExecuteRecordEffect(executeRecordId, req, clinicId, chainId, employeeId);
        return new AbcServiceResponse<>(executeRecordView);
    }

    /**
     * 获取当前用户的执行效果对患者是否可用的操作习惯
     *
     * @param chainId    连锁id
     * @param employeeId 操作人
     * @return 修改结果
     * @throws NotFoundException 异常
     */
    @GetMapping("/execute-effect/employee-habits")
    @LogReqAndRsp
    public AbcServiceResponse<ExecuteRecordEmployeeEffectVisibleRsp> getEmployeeExecuteEffectHabits(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                                    @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) throws NotFoundException, ChargeServiceException {

        ChargeExecuteEffectEmployeeHabits employeeHabits = chargeExecuteRecordService.getEmployeeExecuteEffectHabits(chainId, employeeId);
        return new AbcServiceResponse<>(new ExecuteRecordEmployeeEffectVisibleRsp()
                .setEffectVisibleForPatient(employeeHabits.getEffectVisibleForPatient())
                .setNeedExecuteEffect(employeeHabits.getNeedExecuteEffect())
        );
    }

    /**
     * 获取用户最近上门护理地址
     *
     * @param chainId   连锁id
     * @param patientId 患者id
     * @return 结果
     */
    @GetMapping("/{patientId}/nearest/home-care-address")
    @LogReqAndRsp
    public AbcServiceResponse<PatientNearestHomeCareAddressRsp> getPatientNearestHomeCareAddress(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                                 @PathVariable String patientId) {
        return new AbcServiceResponse<>(chargeExecuteRecordService.getPatientNearestHomeCareAddress(chainId, patientId));
    }
}
