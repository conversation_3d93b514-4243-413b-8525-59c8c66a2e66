package cn.abcyun.cis.charge.controller.rpc;


import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ;
import cn.abcyun.cis.charge.api.model.*;
import cn.abcyun.cis.charge.base.*;
import cn.abcyun.cis.charge.service.ChargeService;
import cn.abcyun.cis.charge.service.dto.CalculateChargeResult;
import cn.abcyun.cis.charge.service.dto.ChargeSheetView;
import cn.abcyun.cis.charge.service.rpc.CisScClinicService;
import cn.abcyun.cis.charge.util.ChargeUtils;
import cn.abcyun.cis.charge.util.LogUtils;
import cn.abcyun.cis.commons.CisServiceResponse;
import cn.abcyun.cis.commons.exception.CisCustomException;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.exception.ServiceInternalException;
import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.cis.core.aop.LogReqAndRsp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

/**
 * 为小程序提供的rpc接口
 */
@RestController
@RequestMapping("/rpc/charges/we-app")
public class ChargeForWeappController {
    private static final Logger sLogger = LoggerFactory.getLogger(ChargeForWeappController.class);

    @Autowired
    private ChargeService mChargeService;

    @Autowired
    private CisScClinicService scClinicService;

    @GetMapping("/{chargeSheetId}")
    @LogReqAndRsp(rpcCallStat = true)
    public CisServiceResponse<ChargeSheetView> getChargeSheetViewById(@PathVariable("chargeSheetId") String chargeSheetId) throws ParamRequiredException, ServiceInternalException, NotFoundException, ChargeServiceException {
        ChargeSheetView chargeSheetView = mChargeService.findChargeSheetById(chargeSheetId, Constants.ChargeSource.WE_APP, true, null);
        if (chargeSheetView == null) {
            sLogger.info("getChargeSheetById:{} not found", chargeSheetId);
            throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_DETAIL_NOT_EXISTED);
        }
        chargeSheetView.setCanUnLockChargeSheet(ChargeUtils.isPatientLockStatus(chargeSheetView.getLockStatus()));
        return new CisServiceResponse<>(chargeSheetView);
    }


    /**
     * 给微诊所封装的算费接口
     * 202009和普通算费接口的差异
     * */
    @PostMapping("/calculate")
    public CisServiceResponse<CalculateChargeSheetRsp> postSelfPayCalculateChargeSheet(@RequestBody WeClinicRpcCalculateChargeSheetReq calculateChargeSheetReq
    ) throws ServiceInternalException, ParamRequiredException,
            CisCustomException {

        LogUtils.infoObjectToJson(sLogger, "postSelfPayCalculateChargeSheet", calculateChargeSheetReq);

        CalculateChargeResult result;
        CalculateChargeSheetRsp rsp = new CalculateChargeSheetRsp();

        if (calculateChargeSheetReq.getPayType() == CalculateChargeSheetReq.PAY_TYPE_CHARGE) {
            Organ organ = Optional.ofNullable(scClinicService.getOrgan(calculateChargeSheetReq.getClinicId()))
                    .orElseThrow(() -> new NotFoundException("诊所未找到"));
            int hisType = organ.getHisType();
            result = mChargeService.calculateChargeSheet(null,
                    null,
                    calculateChargeSheetReq.getPayMode(),
                    calculateChargeSheetReq,
                    calculateChargeSheetReq.getChainId(),
                    calculateChargeSheetReq.getClinicId(),
                    Constants.ChargeSource.WE_APP,
                    hisType);//这个接口是微诊所的算费
            BeanUtils.copyProperties(result, rsp);
            LogUtils.infoObjectToJson(sLogger, "postCalculateChargeSheet result:", result);
        }
        return new CisServiceResponse<>(rsp);
    }

    /**
     * 小程序的锁单并支付
     * @param chargeSheetId
     * @param req
     * @return
     * @throws ServiceInternalException
     * @throws ChargeServiceException
     * @throws ParamRequiredException
     * @throws ProductInfoChangedException
     */
    @PutMapping("/{chargeSheetId}/paid")
    @LogReqAndRsp
    public CisServiceResponse<PayChargeSheetRsp> putChargeSheetPaid(@PathVariable("chargeSheetId") String chargeSheetId,
                                                                    @RequestBody ThirdPartPayForChargeSheetReq req) throws ServiceInternalException, ChargeServiceException, ParamRequiredException, ProductInfoChangedException {


        PayChargeSheetRsp rsp = mChargeService.thirdPartPayForChargeSheet(chargeSheetId, req, Constants.ChargeSource.WE_APP);
        return new CisServiceResponse<>(rsp);
    }


    /**
     * 取消锁单
     * @param chargeSheetId
     * @throws ServiceInternalException
     * @throws CisCustomException
     * @throws ParamRequiredException
     */
    @PutMapping("/{chargeSheetId}/un-lock")
    @LogReqAndRsp
    public CisServiceResponse<CancelChargeSheetRsp> unLockChargeSheet(@PathVariable("chargeSheetId") String chargeSheetId,
                                                                      @RequestBody RpcWeClinicUnlockReq req) throws ServiceInternalException, CisCustomException, ParamRequiredException, NotFoundException {

        CancelChargeSheetRsp rsp = mChargeService.unLockChargeSheet(chargeSheetId, null, req.getClinicId(), Constants.ChargeSource.WE_APP, Constants.ANONYMOUS_PATIENT_ID);
        sLogger.info("CancelChargeSheetRsp: {}", JsonUtils.dump(rsp));
        return new CisServiceResponse<>(rsp);
    }

}
