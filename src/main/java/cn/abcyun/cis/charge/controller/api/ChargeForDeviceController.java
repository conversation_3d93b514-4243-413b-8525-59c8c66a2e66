package cn.abcyun.cis.charge.controller.api;

import cn.abcyun.bis.rpc.sdk.bis.model.order.AvailableCompanyForDeliveryRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ;
import cn.abcyun.cis.charge.api.model.*;
import cn.abcyun.cis.charge.api.model.orderlist.DeviceSelfPayChargeOrderListReq;
import cn.abcyun.cis.charge.api.model.orderlist.DeviceSelfPayChargeOrderViewRsp;
import cn.abcyun.cis.charge.base.ChargeServiceException;
import cn.abcyun.cis.charge.base.ChargeVersionConstants;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.base.ProductInfoChangedException;
import cn.abcyun.cis.charge.factory.chargeform.ChargeFormFactory;
import cn.abcyun.cis.charge.model.ChargeDeliveryInfo;
import cn.abcyun.cis.charge.model.ChargeForm;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.rpc.model.PatientDeliveryInfoList;
import cn.abcyun.cis.charge.service.*;
import cn.abcyun.cis.charge.service.dto.*;
import cn.abcyun.cis.charge.service.dto.print.ChargeSheetPrintView;
import cn.abcyun.cis.charge.service.rpc.CisScClinicService;
import cn.abcyun.cis.charge.util.*;
import cn.abcyun.cis.commons.exception.CisCustomException;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.exception.ServiceInternalException;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.abcyun.cis.commons.rpc.crm.PatientInfo;
import cn.abcyun.cis.commons.util.DateUtils;
import cn.abcyun.cis.core.aop.LogReqAndRsp;
import cn.abcyun.cis.core.util.JsonUtils;
import cn.abcyun.common.model.AbcServiceResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api-device/charges")
@Slf4j
@Api(value = "ChargeController", description = "收费自助服务机相关接口", produces = "application/json")
public class ChargeForDeviceController {
    private static final Logger sLogger = LoggerFactory.getLogger(ChargeForDeviceController.class);
    @Autowired
    private ChargeService mChargeService;

    @Autowired
    private ChargePayService mChargePayService;

    @Autowired
    private ChargeSheetService chargeSheetService;

    @Autowired
    private ChargeRuleProcessUsageTypeService chargeRuleProcessUsageTypeService;

    @Autowired
    private CrmService crmService;

    @Autowired
    private DeliveryService mDeliveryService;

    @Autowired
    private ChargeAirPharmacyService airPharmacyService;

    @Autowired
    private ChargePrintService chargePrintService;

    @Autowired
    private ChargeRuleExpressDeliveryService chargeRuleExpressDeliveryService;

    @Autowired
    private ChargePatientOrderService chargePatientOrderService;

    @Autowired
    private CisScClinicService scClinicService;

    @GetMapping("/{chargeSheetId}")
    @LogReqAndRsp
    @ApiOperation(value = "", hidden = true)
    public AbcServiceResponse<ChargeSheetView> getChargeSheetById(@PathVariable("chargeSheetId") String chargeSheetId) throws NotFoundException, ServiceInternalException, ChargeServiceException {
        ChargeSheetView chargeSheetView = mChargeService.findChargeSheetById(chargeSheetId, Constants.ChargeSource.DEVICE, true, null);
        if (chargeSheetView == null) {
            log.info("getChargeSheetById:{} not found", chargeSheetId);
            throw new NotFoundException();
        }
        log.info("getChargeSheetById id:{}, rsp:{}", chargeSheetId, JsonUtils.dump(chargeSheetView));
        chargeSheetView.setCanUnLockChargeSheet(ChargeUtils.isPatientLockStatus(chargeSheetView.getLockStatus()));
        return new AbcServiceResponse<>(chargeSheetView);
    }

    /**
     * 自助服务机锁单并支付
     *
     * @param chargeSheetId
     * @param req
     * @param clinicId
     * @param httpServletRequest
     * @return
     * @throws ServiceInternalException
     * @throws ChargeServiceException
     * @throws ParamRequiredException
     * @throws ProductInfoChangedException
     * @throws NotFoundException
     */
    @PutMapping("/{chargeSheetId}/paid")
    @LogReqAndRsp
    @ApiOperation(value = "", hidden = true)
    public AbcServiceResponse<PayChargeSheetRsp> putChargeSheetPaid(@PathVariable("chargeSheetId") String chargeSheetId,
                                                                    @RequestBody ThirdPartPayForChargeSheetReq req,
                                                                    @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                    HttpServletRequest httpServletRequest) throws ServiceInternalException, ChargeServiceException, ParamRequiredException, ProductInfoChangedException, NotFoundException {

//        private BigDecimal receivableFee;
//        private String dataSignature;
//        private List<CombinedPayItem> combinedPayItems;

        req.setClientIp(HttpUtils.getClientIpAddress(httpServletRequest));
        req.setOperatorId(Constants.ANONYMOUS_PATIENT_ID);
        req.setClinicId(clinicId);

        ChargeUtils.checkPayModeOrThrowException(req.getCombinedPayItems());

        PayChargeSheetRsp rsp = mChargeService.thirdPartPayForChargeSheet(chargeSheetId, req, Constants.ChargeSource.DEVICE);
        return new AbcServiceResponse<>(rsp);
    }

    @GetMapping("/paystatus/{chargePayTransactionId}")
    @LogReqAndRsp
    @ApiOperation(value = "", hidden = true)
    public AbcServiceResponse<PayStatusRsp> getPayStatusByChargaPayTransactionId(@PathVariable("chargePayTransactionId") String chargePayTransactionId,
                                                                                 @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) throws ServiceInternalException {

        PayStatusRsp rsp = mChargePayService.getPayStatusByChargeTransactionId(chargePayTransactionId, clinicId);

        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 自助取号机自助支付拉去订单列表
     */
    @PostMapping("/orders")
    @ApiOperation(value = "", hidden = true)
    @LogReqAndRsp
    public AbcServiceResponse<DeviceSelfPayChargeOrderViewRsp> getChargeOrderListByPatientIds(
            @RequestBody DeviceSelfPayChargeOrderListReq chargeOrderListReq,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId) throws ParamRequiredException {
        chargeOrderListReq.setChainId(chainId);
        chargeOrderListReq.setClinicId(clinicId);
        sLogger.info("api-devices-selfpay-getorderlist req={}", JsonUtils.dump(chargeOrderListReq));
        if (chargeOrderListReq.getSearchBeginDate() != null) {
            chargeOrderListReq.setSearchBeginDate(DateUtils.getStartTime(chargeOrderListReq.getSearchBeginDate()));
        } else {
            chargeOrderListReq.setSearchBeginDate(DateUtils.getStartTime(new Date()));
        }

        DeviceSelfPayChargeOrderViewRsp rsp = null;
        List<PatientInfo> patientInfoList = null;
        //按patientId查询订单信息
        if (chargeOrderListReq.getQueryType() == DeviceSelfPayChargeOrderListReq.QueryType.QUERY_TYPE_PATIENT_ID) {

            patientInfoList = crmService.getPatientInfoList(chargeOrderListReq.getChainId(), chargeOrderListReq.getPatientIds());
            Map<String, PatientInfo> patientInfoMap = patientInfoList.stream().collect(Collectors.toMap(PatientInfo::getId, Function.identity(), (a, b) -> a));

            rsp = chargeSheetService.getDeviceSelfPayChargeOrderListByPatientId(chargeOrderListReq, patientInfoMap);

        } else if (chargeOrderListReq.getQueryType() == DeviceSelfPayChargeOrderListReq.QueryType.QUERY_TYPE_MOBILE) { //按手机号，先查患者信息，再查订单信息
            patientInfoList = crmService.searchPatientInfosByMobile(chargeOrderListReq.getChainId(), chargeOrderListReq.getMobile());
            chargeOrderListReq.setPatientIds(new ArrayList<>());
            for (PatientInfo patientInfo : patientInfoList) {
                chargeOrderListReq.getPatientIds().add(patientInfo.getId());
            }

            Map<String, PatientInfo> patientInfoMap = patientInfoList.stream().collect(Collectors.toMap(PatientInfo::getId, Function.identity(), (a, b) -> a));


            rsp = chargeSheetService.getDeviceSelfPayChargeOrderListByPatientId(chargeOrderListReq, patientInfoMap);

        }
        return new AbcServiceResponse<>(rsp);
    }

    @GetMapping("/process/usages/available")
    @LogReqAndRsp
    @ApiOperation(value = "", hidden = true)
    public AbcServiceResponse<List<UsageTypeNode>> getAvailableUsages(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId) {


        List<UsageTypeNode> rsp = chargeRuleProcessUsageTypeService.getAvailableUsages(clinicId, chainId);

        return new AbcServiceResponse<>(rsp);
    }

    @PostMapping("/calculate/process")
    @LogReqAndRsp
    @ApiOperation(value = "", hidden = true)
    public AbcServiceResponse<CalculateProcessChargeRsp> calculateProcessFee(@RequestBody CalculateProcessChargeSheetReq calculateProcessChargeSheetReq,
                                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId
    ) throws ServiceInternalException {

        CalculateProcessChargeRsp rsp = mChargeService.calculateProcessFee(calculateProcessChargeSheetReq, chainId, clinicId, Constants.ChargeSource.DEVICE);

        return new AbcServiceResponse<>(rsp);
    }

    @PostMapping("/calculate/delivery")
    @LogReqAndRsp
    @ApiOperation(value = "", hidden = true)
    public AbcServiceResponse<CalculateExpressDeliveryChargeSheetRsp> calculateExpressDeliveryFee(@RequestBody CalculateExpressDeliveryChargeSheetReq req,
                                                                                                  @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                                  @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId) {


        ChargeSheet chargeSheet = new ChargeSheet();
        chargeSheet.setId(req.getChargeSheetId());
        chargeSheet.setClinicId(clinicId);
        chargeSheet.setChainId(chainId);
        chargeSheet.setDeliveryType(ChargeSheet.DeliveryType.DELIVERY_TO_HOME);
        if (req.getChargeForms() != null) {
            List<ChargeForm> chargeForms = req.getChargeForms().stream()
                    .map(chargeFormReq -> ChargeFormFactory.createChargeFormFromCalculate(req.getChargeSheetId(), "", clinicId, "", chargeFormReq, false, true, false, ""))
                    .filter(Objects::nonNull).collect(Collectors.toList());
            chargeSheet.setChargeForms(chargeForms);
        }

        if (req.getDeliveryInfo() == null) {
            throw new ParamRequiredException("deliveryInfo");
        }

        ChargeDeliveryInfo chargeDeliveryInfo = DTOConverter.convertToChargeDeliveryInfo(req.getDeliveryInfo(), chainId, clinicId);

        chargeSheet.setDeliveryInfo(chargeDeliveryInfo);

        CalculateExpressDeliveryChargeSheetRsp rsp = mChargeService.calculateExpressDeliveryFee(chargeSheet, req.getScene(), req.getIsRenew() == 1, Constants.ChargeSource.DEVICE);

        return new AbcServiceResponse<>(rsp);
    }

    @PostMapping("/calculate")
    @ApiOperation(value = "", hidden = true)
    public AbcServiceResponse<CalculateChargeSheetRsp> postCalculateChargeSheet(@RequestBody CalculateChargeSheetReq calculateChargeSheetReq,
                                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID, required = false) String chainId) throws ServiceInternalException, ParamRequiredException, CisCustomException {


        LogUtils.infoObjectToJson(sLogger, "postCalculateChargeSheet", calculateChargeSheetReq);

        CalculateChargeResult result = null;

        CalculateChargeSheetRsp rsp = new CalculateChargeSheetRsp();
        if (calculateChargeSheetReq.getPayType() == CalculateChargeSheetReq.PAY_TYPE_CHARGE) {
            Organ organ = Optional.ofNullable(scClinicService.getOrgan(clinicId))
                    .orElseThrow(() -> new NotFoundException("诊所未找到"));
            int hisType = organ.getHisType();

            result = mChargeService.calculateChargeSheet(null,
                    null,
                    calculateChargeSheetReq.getPayMode(),
                    calculateChargeSheetReq,
                    chainId,
                    clinicId,
                    Constants.ChargeSource.DEVICE, //这个接口默认都是收费台的算费
                    hisType
            );
            BeanUtils.copyProperties(result, rsp);

        }
        LogUtils.infoObjectToJson(sLogger, "postCalculateChargeSheet result:", result);

        return new AbcServiceResponse<>(rsp);
    }

    @GetMapping("/delivery/address")
    @LogReqAndRsp
    @ApiOperation(value = "", hidden = true)
    public AbcServiceResponse<PatientDeliveryInfoList> getPatientDeliveryInfoList(
            @RequestParam("patientId") String patientId,
            @RequestParam(value = "type", required = false, defaultValue = "0") int type,
            @RequestParam(value = "vendorId", required = false) String vendorId,
            @RequestParam(value = "usageScopeId", required = false) String usageScopeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId
    ) throws ParamRequiredException {

        if (StringUtils.isEmpty(patientId)) {
            throw new ParamRequiredException("patientId");
        }

        PatientDeliveryInfoListReq patientDeliveryInfoListReq = new PatientDeliveryInfoListReq();
        patientDeliveryInfoListReq.setChainId(chainId);
        patientDeliveryInfoListReq.setClinicId(clinicId);
        patientDeliveryInfoListReq.setPatientIds(Arrays.asList(patientId));
        patientDeliveryInfoListReq.setType(type);
        patientDeliveryInfoListReq.setUsageScopeId(usageScopeId);
        patientDeliveryInfoListReq.setVendorId(vendorId);
        patientDeliveryInfoListReq.setIsCheckAddressScope(1);

        PatientDeliveryInfoList rsp = mDeliveryService.getPatientDeliveryInfoList(patientDeliveryInfoListReq);
        return new AbcServiceResponse<>(rsp);
    }

    @PostMapping("/air-pharmacy/available-company")
    @LogReqAndRsp
    @ApiOperation(value = "", hidden = true)
    public AbcServiceResponse<AvailableCompanyForDeliveryRsp> getAvailableCompanyForDelivery(@RequestBody AirPharmacyAvailableCompanyReq req,
                                                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) throws ServiceInternalException, ParamRequiredException, ChargeServiceException {
        AvailableCompanyForDeliveryRsp rsp = airPharmacyService.getAvailableCompanyForDelivery(clinicId, req);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 取消锁单
     *
     * @param chargeSheetId
     * @param clinicId
     * @return
     * @throws ServiceInternalException
     * @throws CisCustomException
     * @throws ParamRequiredException
     */
    @PutMapping("/{chargeSheetId}/un-lock")
    @LogReqAndRsp
    @ApiOperation(value = "", hidden = true)
    public AbcServiceResponse<CancelChargeSheetRsp> unLockChargeSheet(@PathVariable("chargeSheetId") String chargeSheetId, @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) throws ServiceInternalException, CisCustomException, ParamRequiredException, NotFoundException {

        CancelChargeSheetRsp rsp = mChargeService.unLockChargeSheet(chargeSheetId, null, clinicId, Constants.ChargeSource.DEVICE, Constants.ANONYMOUS_PATIENT_ID);
        sLogger.info("CancelChargeSheetRsp: {}", JsonUtils.dump(rsp));
        return new AbcServiceResponse<>(rsp);
    }

    @GetMapping("/{chargeSheetId}/print")
    @ApiOperation(value = "", hidden = true)
    public AbcServiceResponse<ChargeSheetPrintView> getChargeSheetPrintViewById(@PathVariable("chargeSheetId") String chargeSheetId) throws NotFoundException, ChargeServiceException, ServiceInternalException {
        sLogger.info("getChargeSheetPrintViewById:{}", chargeSheetId);
        ChargeSheetPrintView chargeSheetPrintView = chargePrintService.findChargeSheetPrintViewById(chargeSheetId);
        if (chargeSheetPrintView == null) {
            sLogger.info("getChargeSheetPrintViewById:{} not found", chargeSheetId);
            throw new NotFoundException();
        }
        sLogger.info("getChargeSheetPrintViewById id:{}, rsp:{}", chargeSheetId, cn.abcyun.cis.charge.util.JsonUtils.dump(chargeSheetPrintView));
        return new AbcServiceResponse<>(chargeSheetPrintView);
    }

    @PostMapping("/available-delivery-company")
    @ApiOperation(value = "根据地址信息查询可用的快递公司列表", produces = "application/json")
    @LogReqAndRsp
    public AbcServiceResponse<AvailableDeliveryPayTypeRsp> listAvailableDeliveryCompany(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestBody @Valid AvailableDeliveryPayTypeReq req) {

        req.setChainId(chainId);
        req.setClinicId(clinicId);
        req.checkParam();
        AvailableDeliveryPayTypeRsp rsp = chargeRuleExpressDeliveryService.listAvailableDeliveryPayTypeByAddress(req);

        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 查询patientOrderId的加锁信息
     *
     * @param patientOrderId 就诊单id
     * @param chainId
     * @return
     */
    @GetMapping("/patientorders/{patientOrderId}/lock-detail")
    @LogReqAndRsp
    @ApiOperation(value = "查询就诊单的加锁信息", produces = "application/json")
    public AbcServiceResponse<PatientOrderLockView> getPatientOrderLockDetail(@PathVariable(value = "patientOrderId") String patientOrderId,
                                                                              @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId) {
        if (StringUtils.isEmpty(patientOrderId)) {
            throw new ParamRequiredException("patientOrderId");
        }

        if (StringUtils.isEmpty(chainId)) {
            throw new ParamRequiredException("chainId");
        }

        PatientOrderLockView rsp = chargePatientOrderService.getPatientOrderLockDetail(patientOrderId, chainId);
        return new AbcServiceResponse<>(rsp);
    }

    @PostMapping("/process/bag-count/calculate")
    @LogReqAndRsp
    @ApiOperation(value = "加工袋数计算")
    public AbcServiceResponse<ProcessorUtils.CalProcessorBagCountResult> calProcessorBagCount(@RequestBody CalculateProcessorBagCountReq req) {

        ProcessorUtils.CalProcessorBagCountResult result = ProcessorUtils.calProcessorBagCount(req.getDailyDosage(), req.getFreq(), req.getUsageLevel(), MathUtils.wrapBigDecimal(req.getDoseCount(), new BigDecimal(3)), req.getBagUnitCount(), req.getPharmacyType(), req.getType());

        return new AbcServiceResponse<>(result);
    }

    /**
     * 根据chargePayTransactionId解锁
     * @param chargePayTransactionId
     * @return
     */
    @PutMapping(value = "/{chargePayTransactionId}/cancel-by-charge-pay-transaction-id")
    @ApiOperation(value = "根据chargePayTransactionId解锁")
    @LogReqAndRsp
    public AbcServiceResponse<CancelChargeSheetRsp> cancelByChargePayTransactionId(@PathVariable("chargePayTransactionId") String chargePayTransactionId,
                                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {

        CancelChargeSheetRsp rsp = mChargeService.cancelByChargePayTransactionId(chargePayTransactionId, clinicId, Constants.ChargeSource.DEVICE, Constants.DEFAULT_OPERATORID);
        return new AbcServiceResponse<>(rsp);
    }
}
