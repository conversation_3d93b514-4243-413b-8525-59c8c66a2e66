package cn.abcyun.cis.charge.controller.rpc;

import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ;
import cn.abcyun.cis.charge.api.model.*;
import cn.abcyun.cis.charge.base.*;
import cn.abcyun.cis.charge.controller.StatusNameTranslator;
import cn.abcyun.cis.charge.service.ChargeService;
import cn.abcyun.cis.charge.service.ChargeSheetService;
import cn.abcyun.cis.charge.service.dto.CalculateChargeResult;
import cn.abcyun.cis.charge.service.dto.ChargeSheetView;
import cn.abcyun.cis.charge.service.dto.PayChargeSheetInfo;
import cn.abcyun.cis.charge.service.dto.PayChargeSheetResult;
import cn.abcyun.cis.charge.service.rpc.CisScClinicService;
import cn.abcyun.cis.charge.util.ChargeUtils;
import cn.abcyun.cis.charge.util.JsonUtils;
import cn.abcyun.cis.charge.util.LogUtils;
import cn.abcyun.cis.commons.CisServiceResponse;
import cn.abcyun.cis.commons.exception.CisCustomException;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.exception.ServiceInternalException;
import cn.abcyun.cis.commons.rpc.charge.WeClinicListMyExecuteSheetsReq;
import cn.abcyun.cis.commons.rpc.charge.WeClinicMyExecuteSheetDetailView;
import cn.abcyun.cis.commons.rpc.charge.WeClinicMyExecuteSheetListView;
import cn.abcyun.cis.core.aop.LogReqAndRsp;
import cn.abcyun.common.model.AbcListPage;
import cn.abcyun.common.model.AbcServiceResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

@RestController
@RequestMapping("/rpc/charges/we-clinic")
public class ChargeForWeClinicRpcController {
    private static final Logger sLogger = LoggerFactory.getLogger(ChargeForWeClinicRpcController.class);

    @Autowired
    private ChargeService mChargeService;

    @Autowired
    private ChargeSheetService chargeSheetService;

    @Autowired
    private CisScClinicService scClinicService;


    @GetMapping("/{chargeSheetId}")
    @LogReqAndRsp(rpcCallStat = true)
    public CisServiceResponse<ChargeSheetView> getChargeSheetViewById(@PathVariable("chargeSheetId") String chargeSheetId) throws ParamRequiredException, ServiceInternalException, NotFoundException, ChargeServiceException {
        ChargeSheetView chargeSheetView = mChargeService.findChargeSheetById(chargeSheetId, Constants.ChargeSource.WE_CLINIC, true, null);
        if (chargeSheetView == null) {
            sLogger.info("getChargeSheetById:{} not found", chargeSheetId);
            throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_DETAIL_NOT_EXISTED);
        }
        chargeSheetView.setCanUnLockChargeSheet(ChargeUtils.isPatientLockStatus(chargeSheetView.getLockStatus()));
        return new CisServiceResponse<>(chargeSheetView);
    }

    /**
     * 微诊所患者我的治疗理疗单列表
     * @param req 查询请求参数
     * @return 查询结果
     */
    @PostMapping("/my-execute-sheets")
    @LogReqAndRsp
    public AbcServiceResponse<AbcListPage<WeClinicMyExecuteSheetListView>> listMyExecuteSheetsForWeClinic(@RequestBody WeClinicListMyExecuteSheetsReq req) throws ParamRequiredException {
        AbcListPage<WeClinicMyExecuteSheetListView> pageRsp = chargeSheetService.pageListMyExecuteSheetsForWeClinic(req.getChainId(), req.getClinicId(), req.getPatientIds(), req.getChargeSheetExecuteStatus(), req.getOffset(), req.getLimit(), req.getBeginDate(), req.getEndDate());

        return new AbcServiceResponse<>(pageRsp);
    }

    /**
     * 微诊所患者我的治疗理疗详情
     * @param chargeSheetId 收费单id
     * @param chainId 连锁id
     * @return 详情
     */
    @GetMapping("/my-execute-sheets/{chargeSheetId}")
    @LogReqAndRsp
    public AbcServiceResponse<WeClinicMyExecuteSheetDetailView> getMyExecuteSheetDetail(@PathVariable String chargeSheetId,
                                                                                        @RequestParam(value = "chainId") String chainId) throws NotFoundException, ChargeServiceException {
        WeClinicMyExecuteSheetDetailView detailView = chargeSheetService.getMyExecuteSheetDetail(chargeSheetId, chainId);

        return new AbcServiceResponse<>(detailView);
    }


    /**
     * 给微诊所封装的算费接口
     * 202009和普通算费接口的差异
     * */
    @PostMapping("/calculate")
    public CisServiceResponse<CalculateChargeSheetRsp> postSelfPayCalculateChargeSheet(@RequestBody WeClinicRpcCalculateChargeSheetReq calculateChargeSheetReq
                                                                                ) throws ServiceInternalException, ParamRequiredException,
            CisCustomException {

        LogUtils.infoObjectToJson(sLogger, "postSelfPayCalculateChargeSheet", calculateChargeSheetReq);

        CalculateChargeResult result;
        CalculateChargeSheetRsp rsp = new CalculateChargeSheetRsp();

        if (calculateChargeSheetReq.getPayType() == CalculateChargeSheetReq.PAY_TYPE_CHARGE) {
            Organ organ = Optional.ofNullable(scClinicService.getOrgan(calculateChargeSheetReq.getClinicId()))
                    .orElseThrow(() -> new NotFoundException("诊所未找到"));
            int hisType = organ.getHisType();
            result = mChargeService.calculateChargeSheet(null,
                    null,
                    calculateChargeSheetReq.getPayMode(),
                    calculateChargeSheetReq,
                    calculateChargeSheetReq.getChainId(),
                    calculateChargeSheetReq.getClinicId(),
                    Constants.ChargeSource.WE_CLINIC,
                    hisType);//这个接口是微诊所的算费
            BeanUtils.copyProperties(result, rsp);
            LogUtils.infoObjectToJson(sLogger, "postCalculateChargeSheet result:", result);
        }
        return new CisServiceResponse<>(rsp);
    }

    @PutMapping("/{chargeSheetId}/paid")
    public CisServiceResponse<PayChargeSheetRsp> putChargeSheetPaid(@PathVariable("chargeSheetId") String chargeSheetId,
                                                                      @RequestBody WeClinicRpcPayChargeSheetReq payChargeSheetReq) throws ServiceInternalException, ChargeServiceException, ParamRequiredException, ProductInfoChangedException {

        sLogger.info("putChargeSheetPaid id:{}, req:{}", chargeSheetId, JsonUtils.dump(payChargeSheetReq));

        PayChargeSheetInfo payChargeSheetInfo = new PayChargeSheetInfo();
        //用这个方法拷贝，真的不太好，虽然方便了，但是拷贝了哪些字段完全不好看
        BeanUtils.copyProperties(payChargeSheetReq, payChargeSheetInfo, "chargeForms");

        payChargeSheetInfo.setClinicId(payChargeSheetReq.getClinicId());
        payChargeSheetInfo.setId(chargeSheetId);
        payChargeSheetInfo.setOperatorId(payChargeSheetReq.getEmployeeId());
        payChargeSheetInfo.setPayType(PayChargeSheetInfo.PayType.PAY_FOR_EXISTED_UNCHARGE_SHEET);
        payChargeSheetInfo.setOpenId(payChargeSheetReq.getOpenId());
        payChargeSheetInfo.setClientIp(payChargeSheetReq.getClientIp());

        //微诊所没有议价功能
        payChargeSheetInfo.setExpectedAdjustmentFee(null);
        payChargeSheetInfo.setExpectedOddFee(null);

        PayChargeSheetResult result = mChargeService.payForChargeSheet(payChargeSheetInfo, Constants.ChargeSource.WE_CLINIC, null, payChargeSheetReq.getEmployeeId());
        PayChargeSheetRsp rsp = new PayChargeSheetRsp();
        BeanUtils.copyProperties(result, rsp);
        StatusNameTranslator.translate(rsp);

        sLogger.info("putChargeSheetRepaid id:{}, rsp:{}", chargeSheetId, JsonUtils.dump(rsp));

        return new CisServiceResponse<>(rsp);
    }

    /**
     * 取消锁单
     * @param chargeSheetId
     * @throws ServiceInternalException
     * @throws CisCustomException
     * @throws ParamRequiredException
     */
    @PutMapping("/{chargeSheetId}/un-lock")
    @LogReqAndRsp
    public CisServiceResponse<CancelChargeSheetRsp> unLockChargeSheet(@PathVariable("chargeSheetId") String chargeSheetId,
                                                                      @RequestBody RpcWeClinicUnlockReq req) throws ServiceInternalException, CisCustomException, ParamRequiredException, NotFoundException {

        CancelChargeSheetRsp rsp = mChargeService.unLockChargeSheet(chargeSheetId, null, req.getClinicId(), Constants.ChargeSource.WE_CLINIC, Constants.ANONYMOUS_PATIENT_ID);
        sLogger.info("CancelChargeSheetRsp: {}", JsonUtils.dump(rsp));
        return new CisServiceResponse<>(rsp);
    }

}
