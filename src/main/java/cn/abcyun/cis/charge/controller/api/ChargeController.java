package cn.abcyun.cis.charge.controller.api;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.OpsCommonRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.shorturl.AttachmentsQrCodeVO;
import cn.abcyun.bis.rpc.sdk.common.model.BasicCommonRsp;
import cn.abcyun.cis.charge.api.model.*;
import cn.abcyun.cis.charge.base.ChargeServiceException;
import cn.abcyun.cis.charge.base.ChargeVersionConstants;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.base.ProductInfoChangedException;
import cn.abcyun.cis.charge.controller.StatusNameTranslator;
import cn.abcyun.cis.charge.factory.chargeform.ChargeFormFactory;
import cn.abcyun.cis.charge.model.ChargeDeliveryInfo;
import cn.abcyun.cis.charge.model.ChargeForm;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.rpc.model.PatientDeliveryInfoList;
import cn.abcyun.cis.charge.service.*;
import cn.abcyun.cis.charge.service.dto.*;
import cn.abcyun.cis.charge.util.*;
import cn.abcyun.cis.commons.exception.*;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.abcyun.cis.commons.rpc.outpatient.OutpatientSheetExtend;
import cn.abcyun.cis.commons.util.DateUtils;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.cis.core.aop.LogReqAndRsp;
import cn.abcyun.common.model.AbcListPage;
import cn.abcyun.common.model.AbcServiceResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/v2/charges")
@Api(value = "ChargeController", description = "收费前端接口", produces = "application/json")
public class ChargeController {

    private static final Logger sLogger = LoggerFactory.getLogger(ChargeController.class);

    @Autowired
    private ChargeService mChargeService;

    @Autowired
    private ChargePayService mChargePayService;

    @Autowired
    private ChargeAbnormalTransactionService chargeAbnormalTransactionService;

    @Autowired
    private ChargeExecuteService mChargeExecuteService;

    @Autowired
    private ClinicService mClinicService;

    @Autowired
    private DeliveryService mDeliveryService;

    @Autowired
    private PromotionService promotionService;

    @Autowired
    private ChargePhysicalTherapyService chargePhysicalTherapyService;

    @Autowired
    private ChargePatientOrderService chargePatientOrderService;

    @Autowired
    private ChargeBusinessPrintService chargeBusinessPrintService;

    /**
     * 查询收费单列表
     */
    @GetMapping("")
    @ApiOperation(value = "", hidden = true)
    public AbcServiceResponse<ChargeSheetAbstractListRsp> getChargeSheetAbstractList(@RequestParam(value = "offset", required = false) Integer offset,
                                                                                     @RequestParam(value = "limit", required = false) Integer limit,
                                                                                     @RequestParam(value = "keyword", required = false) String keyword,
                                                                                     @RequestParam(value = "chargeStatus", required = false) String filterChargeStatus,
                                                                                     /**
                                                                                          *  {@link Constants.ChargeQLTab}
                                                                                          */
                                                                                     @RequestParam(value = "tab", required = false) Integer tab,  //1:待收，2：网诊，3：挂单，4：欠费
                                                                                     @RequestParam(value = "sourceTypeList", required = false) String sourceTypeListStr, // 1:门诊;2:零售;3:续方;4:咨询;5:其他
                                                                                     @RequestParam(value = "beginDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date beginDate,
                                                                                     @RequestParam(value = "endDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
                                                                                     @RequestParam(value = "invoiceStatusFlag", required = false) Integer invoiceStatusFlag,
                                                                                     @RequestParam(value = "sendToPatientStatus", required = false) Integer sendToPatientStatus,
                                                                                     @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                     @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                     @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) int hisType) {

        if (offset == null || offset < 0) {
            offset = 0;
        }

        if (limit == null || limit < 0) {
            limit = 20;
        }

        if (endDate == null) {
            endDate = new Date();
        }

        if (beginDate == null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(endDate);
            calendar.add(Calendar.DATE, -3);
            beginDate = calendar.getTime();
        }

        beginDate = DateUtils.getStartTime(beginDate);
        endDate = DateUtils.getEndTime(endDate);

        List<Integer> sourceTypeList = !StringUtils.isEmpty(sourceTypeListStr) ? Arrays.stream(sourceTypeListStr.split(","))
                .map(NumberUtils::toInt).collect(Collectors.toList()) : null;

        ChargeSheetAbstractListRsp rspData = mChargeService.findChargeSheetAbstractList(offset, limit,
                keyword, chainId, clinicId, filterChargeStatus, tab, sourceTypeList, beginDate != null ? beginDate.toInstant() : null,
                endDate != null ? endDate.toInstant() : null, invoiceStatusFlag, sendToPatientStatus, hisType);

        return new AbcServiceResponse<>(rspData);
    }

    /**
     * 社保查询收费单列表
     */
    @GetMapping("/search-for-shebao-report")
    @ApiOperation(value = "社保查询收费单列表")
    @LogReqAndRsp
    public AbcServiceResponse<AbcListPage<ChargeSheetAbstract>> getChargeSheetAbstractListForShebao(@RequestParam(value = "offset", required = false) Integer offset,
                                                                                                    @RequestParam(value = "limit", required = false) Integer limit,
                                                                                                    @RequestParam(value = "keyword", required = false) String keyword,
                                                                                                    @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                                    @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {


        if (offset == null || offset < 0) {
            offset = 0;
        }

        if (limit == null || limit < 0) {
            limit = 20;
        }

        AbcListPage<ChargeSheetAbstract> rspData = mChargeService.findChargeSheetAbstractListForShebao(offset, limit,
                keyword, chainId, clinicId);

        return new AbcServiceResponse<>(rspData);
    }

    @GetMapping("/today")
    @ApiOperation(value = "", hidden = true)
    public AbcServiceResponse<ChargeSheetAbstractListRsp> getChargeSheetAbstractTodayList(
            @RequestParam(value = "tab", required = false) Integer tab,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) throws ServiceInternalException {

        List<ChargeSheetAbstract> result = mChargeService.findChargeSheetAbstractTodayList(chainId, clinicId, tab);
        StatusNameTranslator.translate(result);
        ChargeSheetAbstractListRsp rspData = new ChargeSheetAbstractListRsp();
        rspData.setResult(result);
        return new AbcServiceResponse<>(rspData);
    }

    @PostMapping("")
    @ApiOperation(value = "零售收费接口")
    @LogReqAndRsp
    public AbcServiceResponse<CreateChargeSheetRsp> postChargeSheet(@RequestBody CreateChargeSheetReq createChargeSheetReq,
                                                                    @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                    @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                    @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                    @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) int hisType) {
        if (!CollectionUtils.isEmpty(createChargeSheetReq.getChargeForms())) {
            createChargeSheetReq.getChargeForms().forEach(ChargeFormReq::checkParam);
        }
        CreateChargeSheetRsp createChargeSheetRsp = mChargeService.createChargeSheet(createChargeSheetReq,
                hisType,
                chainId,
                clinicId,
                employeeId);
        if (createChargeSheetRsp == null) {
            throw new NotFoundException();
        }
        StatusNameTranslator.translate(createChargeSheetRsp);
        return new AbcServiceResponse<>(createChargeSheetRsp);
    }

    @PutMapping("/save")
    @LogReqAndRsp
    @ApiOperation(value = "保存收费单")
    public AbcServiceResponse<SaveChargeSheetRsp> insertOrUpdateChargeSheet(@RequestBody SaveChargeSheetDraftReq req,
                                                                            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId) throws ParamRequiredException, CisCustomException, ServiceInternalException, ProductInfoChangedException, NotFoundException {
        sLogger.info("insertOrUpdateChargeSheet, chargeSheetId: {}", req.getId());
        SaveChargeSheetRsp rsp = mChargeService.insertOrUpdateChargeSheet(req, chainId, clinicId, employeeId);
        return new AbcServiceResponse<>(rsp);
    }

    @GetMapping("/{chargeSheetId}")
    @ApiOperation(value = "收费单查询")
    @LogReqAndRsp
    public AbcServiceResponse<ChargeSheetView> getChargeSheetById(@PathVariable("chargeSheetId") String chargeSheetId) {
        ChargeSheetView chargeSheetView = mChargeService.findChargeSheetById(chargeSheetId, Constants.ChargeSource.CHARGE, false, null);
        if (chargeSheetView == null) {
            throw new NotFoundException();
        }
        chargeSheetView.setCanUnLockChargeSheet(ChargeUtils.isChargerLockStatus(chargeSheetView.getLockStatus()));
        return new AbcServiceResponse<>(chargeSheetView);
    }

    @GetMapping("/{chargeSheetId}/refund-detail")
    @ApiOperation(value = "退费时查询收费单发药/执行等详情数据信息")
    @LogReqAndRsp
    public AbcServiceResponse<ChargeSheetView> getRefundChargeSheetById(
            @PathVariable("chargeSheetId") String chargeSheetId,
            @RequestParam(value = "dispensingQueryCheck", defaultValue = "0") int dispensingQueryCheck) {
        ChargeSheetView chargeSheetView = mChargeService.findChargeSheetById(chargeSheetId, Constants.ChargeSource.CHARGE, false, false, false, dispensingQueryCheck, null);
        if (Objects.isNull(chargeSheetView)) {
            throw new NotFoundException();
        }
        chargeSheetView.setCanUnLockChargeSheet(ChargeUtils.isChargerLockStatus(chargeSheetView.getLockStatus()));
        return new AbcServiceResponse<>(chargeSheetView);
    }

    /**
     * 已退费重新收费会直接把收费单改为待收，不需要这个接口了
     *
     * @param chargeSheetId
     * @return
     * @throws NotFoundException
     * @throws ServiceInternalException
     * @throws ChargeServiceException
     */
    @Deprecated
    @GetMapping("/{chargeSheetId}/renew")
    @LogReqAndRsp
    public AbcServiceResponse<ChargeSheetView> getRenewChargeSheetById(@PathVariable("chargeSheetId") String chargeSheetId) {
        ChargeSheetView chargeSheetView = mChargeService.getRenewChargeSheetById(chargeSheetId);
        if (chargeSheetView == null) {
            throw new NotFoundException();
        }
        return new AbcServiceResponse<>(chargeSheetView);
    }

    @GetMapping("/{chargeSheetId}/copy-view")
    @LogReqAndRsp
    @ApiOperation(value = "获取复制收费单时展示信息")
    public AbcServiceResponse<ChargeSheetView> getChargeSheetCopyView(@RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) Integer hisType,
                                                                      @PathVariable("chargeSheetId") String chargeSheetId) {
        ChargeSheetView chargeSheetView = mChargeService.getChargeSheetCopyView(chainId, clinicId, employeeId, chargeSheetId, hisType);
        if (chargeSheetView == null) {
            throw new NotFoundException();
        }
        return new AbcServiceResponse<>(chargeSheetView);
    }

    /**
     * 已退费收费单重新收费
     *
     * @param chargeSheetId
     * @return
     * @throws NotFoundException
     * @throws ServiceInternalException
     * @throws ChargeServiceException
     */
    @PutMapping("/{chargeSheetId}/renew")
    @LogReqAndRsp
    @ApiOperation(value = "已退收费单重新收费")
    public AbcServiceResponse<ChargeSheetView> renewChargeSheetById(@PathVariable("chargeSheetId") String chargeSheetId,
                                                                    @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                    @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) {
        ChargeSheetView chargeSheetView = mChargeService.renewChargeSheetById(clinicId, chargeSheetId, employeeId);
        if (chargeSheetView == null) {
            throw new NotFoundException();
        }
        return new AbcServiceResponse<>(chargeSheetView);
    }


    /**
     * 已关闭收费单重新打开
     *
     * @param chargeSheetId
     * @return
     * @throws NotFoundException
     * @throws ServiceInternalException
     * @throws ChargeServiceException
     */
    @PutMapping("/{chargeSheetId}/open")
    @LogReqAndRsp
    @ApiOperation(value = "已关闭收费单重新收费")
    public AbcServiceResponse<ChargeSheetView> openChargeSheetById(@PathVariable("chargeSheetId") String chargeSheetId,
                                                                   @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                   @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) {
        ChargeSheetView chargeSheetView = mChargeService.openChargeSheetById(clinicId, chargeSheetId, employeeId);
        if (chargeSheetView == null) {
            throw new NotFoundException();
        }
        return new AbcServiceResponse<>(chargeSheetView);
    }


    @PutMapping("/{chargeSheetId}/paid")
    @ApiOperation(value = "收费的接口")
    @LogReqAndRsp
    public AbcServiceResponse<PayChargeSheetRsp> putChargeSheetPaid(@PathVariable("chargeSheetId") String chargeSheetId,
                                                                    @RequestBody PayChargeSheetReq payChargeSheetReq,
                                                                    @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                    @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                    HttpServletRequest httpServletRequest) {
        if (payChargeSheetReq != null) {
            payChargeSheetReq.preHandle(chargeSheetId);
        }
        PayChargeSheetInfo payChargeSheetInfo = new PayChargeSheetInfo();
        BeanUtils.copyProperties(payChargeSheetReq, payChargeSheetInfo, "chargeForms", "specifiedChargedTime");
        payChargeSheetInfo.setClinicId(clinicId);
        payChargeSheetInfo.setId(chargeSheetId);
        payChargeSheetInfo.setOperatorId(employeeId);
        payChargeSheetInfo.setPayType(PayChargeSheetInfo.PayType.PAY_FOR_EXISTED_UNCHARGE_SHEET);
        payChargeSheetInfo.setUpdateRegistrationItem(payChargeSheetReq.getRegistration());
        payChargeSheetInfo.setOpenId(payChargeSheetReq.getOpenId());
        payChargeSheetInfo.setPatient(payChargeSheetInfo.getPatient());
        payChargeSheetInfo.setClientIp(HttpUtils.getClientIpAddress(httpServletRequest));
        payChargeSheetInfo.setSpecifiedChargedTime(payChargeSheetReq.convertSpecifiedChargedTime());
        if (!CollectionUtils.isEmpty(payChargeSheetReq.getChargeForms())) {
            payChargeSheetReq.getChargeForms().forEach(ChargeFormReq::checkParam);
        }

        PayChargeSheetResult result = mChargeService.payForChargeSheet(payChargeSheetInfo,
                Constants.ChargeSource.CHARGE,
                payChargeSheetReq.getChargeForms(),
                employeeId);
        PayChargeSheetRsp rsp = new PayChargeSheetRsp();
        BeanUtils.copyProperties(result, rsp, "chargeForms");
        StatusNameTranslator.translate(rsp);
        rsp.setChargeForms(ChargeUtils.transChargeSheetFormRsp(result.getChargeForms()));
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 仅支付，不能修改收费项
     *
     * @param chargeSheetId
     * @param payChargeSheetReq
     * @param clinicId
     * @param employeeId
     * @return
     * @throws ServiceInternalException
     * @throws ChargeServiceException
     * @throws ParamRequiredException
     * @throws ProductInfoChangedException
     */
    @PutMapping("/{chargeSheetId}/pay")
    @LogReqAndRsp
    public AbcServiceResponse<PayChargeSheetRsp> putChargeSheetOnlyPaid(@PathVariable("chargeSheetId") String chargeSheetId,
                                                                        @RequestBody OnlyPayChargeSheetReq payChargeSheetReq,
                                                                        HttpServletRequest httpServletRequest,
                                                                        @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                        @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) {
        PayChargeSheetInfo payChargeSheetInfo = new PayChargeSheetInfo();
        BeanUtils.copyProperties(payChargeSheetReq, payChargeSheetInfo, "chargeForms");
        payChargeSheetInfo.setClinicId(clinicId);
        payChargeSheetInfo.setId(chargeSheetId);
        payChargeSheetInfo.setOperatorId(employeeId);
        payChargeSheetInfo.setClientIp(HttpUtils.getClientIpAddress(httpServletRequest));

        PayChargeSheetResult result = mChargeService.putChargeSheetOnlyPaid(payChargeSheetInfo);
        PayChargeSheetRsp rsp = new PayChargeSheetRsp();
        BeanUtils.copyProperties(result, rsp);
        StatusNameTranslator.translate(rsp);

        return new AbcServiceResponse<>(rsp);
    }

    @PutMapping("hospital-charge-sheet/{chargeSheetId}/renewpaid")
    @ApiOperation(value = "长护住院单对应的收费单退费重收")
    @LogReqAndRsp
    public AbcServiceResponse<PayChargeSheetRsp> putHospitalChargeSheetRenewPaid(@PathVariable("chargeSheetId") String chargeSheetId,
                                                                                 @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                                 @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) throws ServiceInternalException, ChargeServiceException, ParamRequiredException, ProductInfoChangedException {

        PayChargeSheetResult result = mChargeService.hospitalChargeSheetRenewPaid(chargeSheetId, clinicId, employeeId);
        PayChargeSheetRsp rsp = new PayChargeSheetRsp();
        BeanUtils.copyProperties(result, rsp);
        StatusNameTranslator.translate(rsp);
        return new AbcServiceResponse<>(rsp);
    }

    @PutMapping("/{chargeSheetId}/repaid")
    @ApiOperation(value = "", hidden = true)
    @LogReqAndRsp
    public AbcServiceResponse<PayChargeSheetRsp> putChargeSheetRepaid(@PathVariable("chargeSheetId") String chargeSheetId,
                                                                      @RequestBody PayChargeSheetReq payChargeSheetReq,
                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                      HttpServletRequest httpServletRequest) {
        mChargeService.dealPayMode(payChargeSheetReq, chainId, clinicId);
        PayChargeSheetInfo payChargeSheetInfo = new PayChargeSheetInfo();
        //用这个方法拷贝，真的不太好，虽然方便了，但是拷贝了哪些字段完全不好看
        BeanUtils.copyProperties(payChargeSheetReq, payChargeSheetInfo, "chargeForms", "specifiedChargedTime");

        payChargeSheetInfo.setClinicId(clinicId);
        payChargeSheetInfo.setId(chargeSheetId);
        payChargeSheetInfo.setOperatorId(employeeId);
        payChargeSheetInfo.setPayType(PayChargeSheetInfo.PayType.PAY_FOR_EXISTED_PARTCHARGE_SHEET);
        payChargeSheetInfo.setOpenId(payChargeSheetReq.getOpenId());
        payChargeSheetInfo.setClientIp(HttpUtils.getClientIpAddress(httpServletRequest));
        payChargeSheetInfo.setSpecifiedChargedTime(payChargeSheetReq.convertSpecifiedChargedTime());


        PayChargeSheetResult result = mChargeService.payForChargeSheet(payChargeSheetInfo, Constants.ChargeSource.CHARGE, null, employeeId);
        PayChargeSheetRsp rsp = new PayChargeSheetRsp();
        BeanUtils.copyProperties(result, rsp);
        StatusNameTranslator.translate(rsp);
        return new AbcServiceResponse<>(rsp);
    }


    @PutMapping("/{chargeSheetId}/refund")
    @ApiOperation(value = "", hidden = true)
    @LogReqAndRsp
    public AbcServiceResponse<RefundChargeSheetRsp> putChargeSheetRefund(@PathVariable("chargeSheetId") String chargeSheetId,
                                                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) int hisType,
                                                                         @RequestBody RefundChargeSheetReq refundChargeSheetReq) {

        RefundChargeSheetInfo refundChargeSheetInfo = new RefundChargeSheetInfo();
        BeanUtils.copyProperties(refundChargeSheetReq, refundChargeSheetInfo, "chargeForms");
        refundChargeSheetInfo.setClinicId(clinicId);
        refundChargeSheetInfo.setId(chargeSheetId);
        refundChargeSheetInfo.setOperatorId(employeeId);
        refundChargeSheetInfo.setChargeForms(new ArrayList<>());
        if (refundChargeSheetReq.getChargeForms() != null) {
            List<ChargeForm> chargeForms = refundChargeSheetReq.getChargeForms().stream()
                    .map(chargeFormReq -> ChargeFormFactory.createChargeFormFromCalculate(chargeSheetId, "", clinicId, chainId, chargeFormReq, false, false, false, employeeId))
                    .filter(Objects::nonNull).collect(Collectors.toList());
            refundChargeSheetInfo.setChargeForms(chargeForms);
        }
        RefundChargeSheetResult result = mChargeService.refundForChargeSheet(refundChargeSheetInfo, employeeId);
        RefundChargeSheetRsp rsp = new RefundChargeSheetRsp();
        BeanUtils.copyProperties(result, rsp);
        StatusNameTranslator.translate(rsp);
        return new AbcServiceResponse<>(rsp);
    }

    @PutMapping("/{chargeSheetId}/paidback")
    @ApiOperation(value = "", hidden = true)
    @LogReqAndRsp
    public AbcServiceResponse<RefundChargeSheetRsp> putChargeSheetPaidback(@PathVariable("chargeSheetId") String chargeSheetId,
                                                                           @RequestBody RefundChargeSheetReq refundChargeSheetReq,
                                                                           @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String operatorId,
                                                                           @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {
        RefundChargeSheetInfo refundChargeSheetInfo = new RefundChargeSheetInfo();
        BeanUtils.copyProperties(refundChargeSheetReq, refundChargeSheetInfo, "chargeForms");
        refundChargeSheetInfo.setClinicId(clinicId);
        refundChargeSheetInfo.setId(chargeSheetId);
        refundChargeSheetInfo.setOperatorId(operatorId);
        refundChargeSheetInfo.setChargeForms(new ArrayList<>());
        refundChargeSheetInfo.setPayMode(refundChargeSheetReq.getPayMode());
        RefundChargeSheetResult result = mChargeService.paidbackForChargeSheet(refundChargeSheetInfo, operatorId);
        RefundChargeSheetRsp rsp = new RefundChargeSheetRsp();
        BeanUtils.copyProperties(result, rsp);
        StatusNameTranslator.translate(rsp);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 取消锁单
     *
     * @param chargeSheetId
     * @param operatorId
     * @param clinicId
     * @return
     * @throws ServiceInternalException
     * @throws CisCustomException
     * @throws ParamRequiredException
     */
    @Deprecated
    @PutMapping(value = {"/{chargeSheetId}/cancel", "/{chargeSheetId}/un-lock"})
    @ApiOperation(value = "", hidden = true)
    @LogReqAndRsp
    public AbcServiceResponse<CancelChargeSheetRsp> unLockChargeSheet(@PathVariable("chargeSheetId") String chargeSheetId,
                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String operatorId,
                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) throws ServiceInternalException, CisCustomException, ParamRequiredException, NotFoundException {

        CancelChargeSheetRsp rsp = mChargeService.unLockChargeSheet(chargeSheetId, null, clinicId, Constants.ChargeSource.CHARGE, operatorId);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 根据chargePayTransactionId解锁
     *
     * @param chargePayTransactionId
     * @param operatorId
     * @param clinicId
     * @return
     */
    @PutMapping(value = "/{chargePayTransactionId}/cancel-by-charge-pay-transaction-id")
    @ApiOperation(value = "根据chargePayTransactionId解锁")
    @LogReqAndRsp
    public AbcServiceResponse<CancelChargeSheetRsp> cancelByChargePayTransactionId(@PathVariable("chargePayTransactionId") String chargePayTransactionId,
                                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String operatorId,
                                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {

        CancelChargeSheetRsp rsp = mChargeService.cancelByChargePayTransactionId(chargePayTransactionId, clinicId, Constants.ChargeSource.CHARGE, operatorId);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 根据chargePayTransactionId加锁续期
     *
     * @param chargePayTransactionId
     * @param operatorId
     * @param clinicId
     * @return
     */
    @PutMapping(value = "/{chargePayTransactionId}/lock-renew")
    @ApiOperation(value = "根据chargePayTransactionId解锁")
    @LogReqAndRsp
    public AbcServiceResponse<BasicCommonRsp> lockRenewByChargePayTransactionId(@PathVariable("chargePayTransactionId") String chargePayTransactionId,
                                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String operatorId,
                                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {

        BasicCommonRsp rsp = mChargeService.lockRenewByChargePayTransactionId(chargePayTransactionId, clinicId, Constants.ChargeSource.CHARGE, operatorId);
        return new AbcServiceResponse<>(rsp);
    }

    @GetMapping("/paystatus/{chargePayTransactionId}")
    @LogReqAndRsp
    @ApiOperation(value = "", hidden = true)
    public AbcServiceResponse<PayStatusRsp> getPayStatusByChargePayTransactionId(@PathVariable("chargePayTransactionId") String chargePayTransactionId,
                                                                                 @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) throws ServiceInternalException {

        PayStatusRsp rsp = mChargePayService.getPayStatusByChargeTransactionId(chargePayTransactionId, clinicId);
        return new AbcServiceResponse<>(rsp);
    }


    @PutMapping("/{chargeSheetId}/changepaymode")
    @ApiOperation(value = "修改支付方式的接口")
    @LogReqAndRsp
    public AbcServiceResponse<ChangePayModeRsp> putChargeSheetChangePayMode(@PathVariable("chargeSheetId") String chargeSheetId,
                                                                            @RequestBody ChangePayModeReq changePayModeReq,
                                                                            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID, required = true) String employeeId,
                                                                            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID, required = true) String clinicId) throws ServiceInternalException, CisCustomException, ParamRequiredException {
        ChangePayModeRsp rsp = mChargeService.changeChargeSheetPayMode(changePayModeReq, chargeSheetId, clinicId, employeeId);
        return new AbcServiceResponse<>(rsp);
    }

    @GetMapping("/{chargeSheetId}/outpatient")
    @ApiOperation(value = "", hidden = true)
    @LogReqAndRsp
    public AbcServiceResponse<OutpatientSheetExtend> getChargeSheetOutpatientSheet(@PathVariable("chargeSheetId") String chargeSheetId,
                                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) throws ParamRequiredException, NotFoundException {

        if (TextUtils.isEmpty(chargeSheetId)) {
            throw new ParamRequiredException("chargeSheetId");
        }
        OutpatientSheetExtend outpatientSheetExtend = mChargeService.getChargeSheetOutpatientSheet(chargeSheetId, clinicId);
        if (outpatientSheetExtend == null) {
            throw new NotFoundException();
        }

        return new AbcServiceResponse<>(outpatientSheetExtend);
    }

    @PostMapping("/calculate")
    @ApiOperation(value = "算费", produces = "application/json")
    @LogReqAndRsp
    public AbcServiceResponse<CalculateChargeSheetRsp> postCalculateChargeSheet(@RequestBody CalculateChargeSheetReq calculateChargeSheetReq,
                                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) int hisType) {


        CalculateChargeResult result;
        int chargeVersion = ChargeVersionConstants.convertChargeVersion(hisType);
        if (calculateChargeSheetReq.getPayType() == CalculateChargeSheetReq.PAY_TYPE_CHARGE) {
            result = mChargeService.calculateChargeSheet(calculateChargeSheetReq.getExpectedAdjustmentFee(),
                    calculateChargeSheetReq.getExpectedOddFee(),
                    calculateChargeSheetReq.getPayMode(),
                    calculateChargeSheetReq,
                    chainId, clinicId,
                    Constants.ChargeSource.CHARGE, //这个接口默认都是收费台的算费
                    hisType
            );
        } else if (calculateChargeSheetReq.getPayType() == CalculateChargeSheetReq.PAY_TYPE_REFUND) {
            ChargeSheet chargeSheet = ClientReqUtils.convertToCalculateChargeSheet(calculateChargeSheetReq, chargeVersion, clinicId, chainId, hisType, "");

            result = mChargeService.calculateRefundChargeSheet(chargeSheet,
                    MathUtils.wrapBigDecimalOrZero(calculateChargeSheetReq.getAdjustmentFee()),
                    clinicId);
        } else {
            ChargeSheet chargeSheet = ClientReqUtils.convertToCalculateChargeSheet(calculateChargeSheetReq, chargeVersion, clinicId, chainId, hisType, "");

            result = mChargeService.calculateRenewChargeSheet(chargeSheet,
                    calculateChargeSheetReq.getExpectedAdjustmentFee(),
                    calculateChargeSheetReq.getExpectedOddFee(),
                    clinicId,
                    calculateChargeSheetReq.getPayMode(),
                    calculateChargeSheetReq,
                    Constants.ChargeSource.CHARGE);//这个接口默认都是收费台的算费
        }
        CalculateChargeSheetRsp rsp = new CalculateChargeSheetRsp();
        BeanUtils.copyProperties(result, rsp);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 部分支付的收费单查询可支付的卡项
     *
     * @param chargeSheetId
     * @param clinicId
     * @return
     */
    @GetMapping("/{chargeSheetId}/patient-card")
    @LogReqAndRsp
    @ApiOperation(value = "部分支付的收费单查询可支付的卡项", produces = "application/json")
    public AbcServiceResponse<CanPaidPatientCardRsp> findCanPaidPatientCards(@ApiParam(value = "收费单id") @PathVariable("chargeSheetId") String chargeSheetId,
                                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {

        CanPaidPatientCardRsp rsp = mChargeService.findCanPaidPatientCards(clinicId, chargeSheetId);

        return new AbcServiceResponse<>(rsp);
    }


    @PostMapping("/calculate/process")
    @LogReqAndRsp
    @ApiOperation(value = "", hidden = true)
    public AbcServiceResponse<CalculateProcessChargeRsp> calculateProcessFee(@RequestBody CalculateProcessChargeSheetReq calculateProcessChargeSheetReq,
                                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId) {

        CalculateProcessChargeRsp rsp = mChargeService.calculateProcessFee(calculateProcessChargeSheetReq, chainId, clinicId, Constants.ChargeSource.CHARGE);

        return new AbcServiceResponse<>(rsp);
    }

    @PostMapping("/calculate/delivery")
    @LogReqAndRsp
    @ApiOperation(value = "", hidden = true)
    public AbcServiceResponse<CalculateExpressDeliveryChargeSheetRsp> calculateExpressDeliveryFee(@RequestBody CalculateExpressDeliveryChargeSheetReq req,
                                                                                                  @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                                  @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                                  @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) {


        ChargeSheet chargeSheet = new ChargeSheet();
        chargeSheet.setId(req.getChargeSheetId());
        chargeSheet.setClinicId(clinicId);
        chargeSheet.setChainId(chainId);
        chargeSheet.setDeliveryType(ChargeSheet.DeliveryType.DELIVERY_TO_HOME);
        if (req.getChargeForms() != null) {
            List<ChargeForm> chargeForms = req.getChargeForms().stream()
                    .map(chargeFormReq -> ChargeFormFactory.createChargeFormFromCalculate(req.getChargeSheetId(), "", clinicId, "", chargeFormReq, false, true, false, employeeId))
                    .filter(Objects::nonNull).collect(Collectors.toList());
            chargeSheet.setChargeForms(chargeForms);
        }

        if (req.getDeliveryInfo() == null) {
            throw new ParamRequiredException("deliveryInfo");
        }

        ChargeDeliveryInfo chargeDeliveryInfo = DTOConverter.convertToChargeDeliveryInfo(req.getDeliveryInfo(), chainId, clinicId);

        chargeSheet.setDeliveryInfo(chargeDeliveryInfo);

        CalculateExpressDeliveryChargeSheetRsp rsp = mChargeService.calculateExpressDeliveryFee(chargeSheet, req.getScene(), req.getIsRenew() == 1, Constants.ChargeSource.CHARGE);

        return new AbcServiceResponse<>(rsp);
    }

    @PostMapping("/virtual-pharmacy/calculate/delivery")
    @LogReqAndRsp
    @ApiOperation(value = "计算虚拟药房快递费")
    public AbcServiceResponse<AbcListPage<CalculateVirtualPharmacyExpressDeliveryChargeSheetRsp>> calculateVirtualPharmacyExpressDeliveryFee(@RequestBody CalculateVirtualPharmacyExpressDeliveryChargeSheetReq req,
                                                                                                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId) {


        ChargeSheet chargeSheet = new ChargeSheet();
        chargeSheet.setId(AbcIdUtils.getUUID());
        chargeSheet.setClinicId(clinicId);
        chargeSheet.setChainId(chainId);
        chargeSheet.setDeliveryType(ChargeSheet.DeliveryType.DELIVERY_TO_HOME);
        if (req.getChargeForms() != null) {
            List<ChargeForm> chargeForms = req.getChargeForms().stream()
                    .filter(chargeFormReq -> chargeFormReq.getDeliveryInfo() != null && !StringUtils.isEmpty(chargeFormReq.getKeyId()))
                    .map(chargeFormReq -> ChargeFormFactory.createChargeFormFromCalculate(chargeSheet.getId(), "", clinicId, "", chargeFormReq, false, true, false, ""))
                    .filter(Objects::nonNull).collect(Collectors.toList());
            chargeSheet.setChargeForms(chargeForms);
        }

        List<CalculateVirtualPharmacyExpressDeliveryChargeSheetRsp> rows = mChargeService.calculateVirtualPharmacyExpressDeliveryFee(chargeSheet);
        AbcListPage<CalculateVirtualPharmacyExpressDeliveryChargeSheetRsp> rsp = new AbcListPage<>();
        rsp.setRows(rows);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 患者治疗理疗列表
     *
     * @param patientId
     * @param offset
     * @param limit
     * @param chainId
     * @param clinicId
     * @return
     * @throws ServiceInternalException
     */
    @GetMapping("/patient/physical-therapy/list")
    @LogReqAndRsp
    @ApiOperation(value = "患者治疗理疗列表")
    public AbcServiceResponse<PhysicalTherapyListRsp> getPhysicalTherapyList(@RequestParam("patientId") String patientId,
                                                                             @RequestParam(value = "offset", required = false, defaultValue = "0") Integer offset,
                                                                             @RequestParam(value = "limit", required = false, defaultValue = "20") Integer limit,
                                                                             @RequestParam(value = "sellerId", required = false) String sellerId,
                                                                             @RequestParam(value = "createdDateBegin", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date createdDateBegin,
                                                                             @RequestParam(value = "createdDateEnd", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date createdDateEnd,
                                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) throws ServiceInternalException {


        if (Objects.nonNull(createdDateBegin)) {
            createdDateBegin = DateUtils.getStartTime(createdDateBegin);
        }

        if (Objects.nonNull(createdDateEnd)) {
            createdDateEnd = DateUtils.getEndTime(createdDateEnd);
        }


        return new AbcServiceResponse<>(chargePhysicalTherapyService.getPhysicalTherapyList(patientId,
                offset,
                limit,
                chainId,
                clinicId,
                sellerId,
                Optional.ofNullable(createdDateBegin).map(Date::toInstant).orElse(null),
                Optional.ofNullable(createdDateEnd).map(Date::toInstant).orElse(null)));
    }

    @GetMapping("/paymodes")
    @ApiOperation(value = "", hidden = true)
    public AbcServiceResponse<PayModeListRsp> getPayModeList(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {
        PayModeListRsp payModeListRsp = mClinicService.getPayModeList(chainId, clinicId);
        return new AbcServiceResponse<>(payModeListRsp);

    }

    /**
     * 查询患者可以支付的营销卡和已选中的别人的营销卡
     *
     * @param chainId
     * @param patientId
     * @param patientCardsIds
     * @return
     */
    @GetMapping("/list-patient-pay-cards")
    @LogReqAndRsp
    @ApiOperation(value = "", hidden = true)
    public AbcServiceResponse<PatientPayCardRsp> listPatientPayCards(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                     @RequestParam(value = "patientId") String patientId,
                                                                     @RequestParam(value = "patientCardIds", required = false) List<String> patientCardsIds) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(patientId)) {
            throw new ParamRequiredException("patientId");
        }

        PatientPayCardRsp rsp = promotionService.listPatientPayCards(chainId, patientId, patientCardsIds);
        return new AbcServiceResponse<>(rsp);
    }

    @GetMapping("/patient/{patientId}")
    @ApiOperation(value = "患者档案查询患者的收费记录")
    @LogReqAndRsp
    public AbcServiceResponse<PatientChargeSheetAbstractListRsp> getPatientChargeSheetAbstractList(@PathVariable("patientId") String patientId,
                                                                                                   @RequestParam(value = "offset", required = false) Integer offset,
                                                                                                   @RequestParam(value = "limit", required = false) Integer limit,
                                                                                                   @RequestParam(value = "sellerId", required = false) String sellerId,
                                                                                                   @RequestParam(value = "chargedDateBegin", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date chargedDateBegin,
                                                                                                   @RequestParam(value = "chargedDateEnd", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date chargedDateEnd,
                                                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {
        if (TextUtils.isEmpty(chainId)) {
            chainId = clinicId;
        }

        if (offset == null || offset < 0) {
            offset = 0;
        }

        if (limit == null || limit < 0) {
            limit = 20;
        }

        if (Objects.nonNull(chargedDateBegin)) {
            chargedDateBegin = DateUtils.getStartTime(chargedDateBegin);
        }

        if (Objects.nonNull(chargedDateEnd)) {
            chargedDateEnd = DateUtils.getEndTime(chargedDateEnd);
        }

        PatientChargeSheetAbstractListRsp rsp = mChargeService.findPatientChargeSheetAbstractList(offset, limit,
                patientId, chainId, null, sellerId,
                Optional.ofNullable(chargedDateBegin).map(Date::toInstant).orElse(null),
                Optional.ofNullable(chargedDateEnd).map(Date::toInstant).orElse(null));
        return new AbcServiceResponse<>(rsp);
    }

    @GetMapping("/patient/{patientId}/executes")
    @ApiOperation(value = "", hidden = true)
    public AbcServiceResponse<PatientExecuteItemListRsp> getPatientExecuteItemList(@PathVariable("patientId") String patientId,
                                                                                   @RequestParam(value = "offset", required = false) Integer offset,
                                                                                   @RequestParam(value = "limit", required = false) Integer limit,
                                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {
        if (TextUtils.isEmpty(chainId)) {
            chainId = clinicId;
        }

        if (offset == null || offset < 0) {
            offset = 0;
        }

        if (limit == null || limit < 0) {
            limit = 20;
        }

//        PatientExecuteItemListRsp rsp = mChargeExecuteService.findPatientExecuteItemList(offset, limit, patientId, chainId);
        return new AbcServiceResponse<>(null);
    }

    @GetMapping("/patientorder/{patientOrderId}/copy")
    @ApiOperation(value = "", hidden = true)
    @LogReqAndRsp
    public AbcServiceResponse<ChargeSheetView> copyChargeSheetByPatientOrderId(
            @PathVariable("patientOrderId") String patientOrderId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String operatorId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) int hisType
    ) throws NotFoundException, ServiceInternalException, ChargeServiceException {
        ChargeSheetView chargeSheetView = mChargeService.copyChargeSheetByPatientOrderId(patientOrderId, operatorId, chainId, clinicId, hisType);
        return new AbcServiceResponse<>(chargeSheetView);
    }

    @PostMapping("/draft/save")
    @ApiOperation(value = "", hidden = true)
    @LogReqAndRsp
    public AbcServiceResponse<SaveChargeSheetDraftRsp> saveChargeSheetDraft(@RequestBody SaveChargeSheetDraftReq chargeSheetDraftReq,
                                                                            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String operatorId,
                                                                            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                            @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) int hisType) throws ParamRequiredException, ChargeServiceException, ServiceInternalException, NotFoundException {
        sLogger.info("saveChargeSheetDraft start chargeSheetId:{}", chargeSheetDraftReq.getId());
        chargeSheetDraftReq.preHandle(chargeSheetDraftReq.getId());
        SaveChargeSheetDraftRsp rsp = mChargeService.saveChargeSheetDraft(chargeSheetDraftReq, chainId, clinicId, hisType, operatorId);
        return new AbcServiceResponse<>(rsp);
    }

    @PutMapping("/{chargeSheetId}/draft/cancel")
    @ApiOperation(value = "取消挂单")
    @LogReqAndRsp
    public AbcServiceResponse<BaseSuccessRsp> cancelDraftChargeSheet(@PathVariable(value = "chargeSheetId") String chargeSheetId,
                                                                     @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String operatorId,
                                                                     @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {
        BaseSuccessRsp rsp = mChargeService.cancelDraftChargeSheet(clinicId, chargeSheetId, operatorId);
        return new AbcServiceResponse<>(rsp);
    }

    @DeleteMapping({"/draft/{chargeSheetId}", "/{chargeSheetId}"})
    @ApiOperation(value = "删除收费单，后面/draft/{chargeSheetId}会删掉，保留/{chargeSheetId}")
    @LogReqAndRsp
    public AbcServiceResponse<DeleteChargeSheetDraftRsp> deleteChargeSheetDraft(@PathVariable String chargeSheetId,
                                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String operatorId,
                                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) int hisType) {
        mChargeService.deleteChargeSheetDraft(chargeSheetId, clinicId, hisType, operatorId);

        DeleteChargeSheetDraftRsp rsp = new DeleteChargeSheetDraftRsp(200, "成功");
        return new AbcServiceResponse<>(rsp);
    }

    @GetMapping("/delivery/address")
    @LogReqAndRsp
    @ApiOperation(value = "", hidden = true)
    public AbcServiceResponse<PatientDeliveryInfoList> getPatientDeliveryInfoList(
            @RequestParam("patientId") String patientId,
            @RequestParam(value = "type", required = false, defaultValue = "0") int type,
            @RequestParam(value = "vendorId", required = false) String vendorId,
            @RequestParam(value = "usageScopeId", required = false) String usageScopeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId
    ) throws ParamRequiredException {

        if (StringUtils.isEmpty(patientId)) {
            throw new ParamRequiredException("patientId");
        }
        PatientDeliveryInfoListReq patientDeliveryInfoListReq = new PatientDeliveryInfoListReq();
        patientDeliveryInfoListReq.setChainId(chainId);
        patientDeliveryInfoListReq.setClinicId(clinicId);
        patientDeliveryInfoListReq.setPatientIds(Arrays.asList(patientId));
        patientDeliveryInfoListReq.setType(type);
        patientDeliveryInfoListReq.setUsageScopeId(usageScopeId);
        patientDeliveryInfoListReq.setVendorId(vendorId);
        patientDeliveryInfoListReq.setIsCheckAddressScope(1);

        PatientDeliveryInfoList rsp = mDeliveryService.getPatientDeliveryInfoList(patientDeliveryInfoListReq);
        return new AbcServiceResponse<>(rsp);
    }


    @GetMapping("/delivery/companies")
    @LogReqAndRsp
    @ApiOperation(value = "查询诊所的快递公司列表", produces = "application/json")
    public AbcServiceResponse<DeliveryCompanyListRsp> getDeliveryCompanyList(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                             @ApiParam(value = "省id") @RequestParam(value = "addressProvinceId", required = false) String addressProvinceId,
                                                                             @ApiParam(value = "市id") @RequestParam(value = "addressCityId", required = false) String addressCityId,
                                                                             @ApiParam(value = "区id") @RequestParam(value = "addressDistrictId", required = false) String addressDistrictId,
                                                                             @ApiParam(value = "查询快递公司的场景，0：本地药房，1：虚拟药房") @RequestParam(value = "scene", required = false, defaultValue = "0") Integer scene
    ) {

        DeliveryCompanyListRsp rsp = mDeliveryService.getDeliveryCompanyList(chainId, clinicId, addressProvinceId, addressCityId, addressDistrictId, scene == null ? CalculateExpressDeliveryChargeSheetReq.Scene.CHARGE_SHEET : scene);
        return new AbcServiceResponse<>(rsp);
    }

    @PostMapping("/delivery/company")
    @LogReqAndRsp
    @ApiOperation(value = "", hidden = true)
    public AbcServiceResponse<DeliveryCompanyRsp> createDeliveryCompany(@RequestBody @Valid DeliveryCompanyReq deliveryCompanyReq,
                                                                        @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                        @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                        @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String operatorId) throws ChargeServiceException {

        DeliveryCompanyRsp rsp = mDeliveryService.createDeliveryCompany(deliveryCompanyReq, chainId, clinicId, operatorId);
        return new AbcServiceResponse<>(rsp);
    }


    @PutMapping("/delivery/company/{id}")
    @LogReqAndRsp
    @ApiOperation(value = "", hidden = true)
    public AbcServiceResponse<DeliveryCompanyRsp> updateDeliveryCompany(@RequestBody @Valid DeliveryCompanyReq deliveryCompanyReq,
                                                                        @PathVariable("id") String id,
                                                                        @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                        @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                        @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String operatorId) throws NotFoundException, ChargeServiceException {

        DeliveryCompanyRsp rsp = mDeliveryService.updateDeliveryCompany(id, deliveryCompanyReq, chainId, clinicId, operatorId);
        return new AbcServiceResponse<>(rsp);
    }

    @DeleteMapping("/delivery/company/{id}")
    @LogReqAndRsp
    @ApiOperation(value = "", hidden = true)
    public AbcServiceResponse<DeliveryCompanyRsp> deleteDeliveryCompany(@PathVariable("id") String id,
                                                                        @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                        @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                        @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String operatorId) throws NotFoundException, ChargeServiceException {

        DeliveryCompanyRsp rsp = mDeliveryService.deleteDeliveryCompany(id, chainId, clinicId, operatorId);
        return new AbcServiceResponse<>(rsp);
    }

    @PostMapping("/pushorder")
    @LogReqAndRsp
    @ApiOperation(value = "", hidden = true)
    public AbcServiceResponse<PushChargeSheetOrderRsp> pushChargeSheetOrder(@RequestBody SaveChargeSheetDraftReq req,
                                                                            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String operatorId) throws NotFoundException, ChargeServiceException, ServiceInternalException, ParamRequiredException, ProductInfoChangedException, InvocationTargetException, IllegalAccessException {
        ChargeSheet chargeSheet = mChargeService.pushChargeSheetOrder(req, chainId, clinicId, operatorId);
        PushChargeSheetOrderRsp rsp = new PushChargeSheetOrderRsp();
        rsp.setChargeSheetId(chargeSheet.getId());
        return new AbcServiceResponse<>(rsp);
    }

    @PostMapping("/orderdetail")
    @LogReqAndRsp
    @ApiOperation(value = "", hidden = true)
    public AbcServiceResponse<ChargeSheetPushOrderInfoRsp> getChargeSheetPushOrderDetail(@RequestBody SaveChargeSheetDraftReq req,
                                                                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String operatorId) throws ChargeServiceException, ServiceInternalException, NotFoundException, ParamRequiredException {

        ChargeSheetPushOrderInfoRsp rsp = mChargeService.getChargeSheetPushOrderDetail(req, chainId, clinicId, operatorId);

        return new AbcServiceResponse<>(rsp);
    }


    @PostMapping("/delivery/checkscope")
    @LogReqAndRsp
    @ApiOperation(value = "", hidden = true)
    public AbcServiceResponse<CheckDeliveryScopeRsp> checkDeliveryScope(@RequestBody ChargeDeliveryReq req,
                                                                        @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) throws ParamRequiredException {
        CheckDeliveryScopeRsp rsp = mDeliveryService.checkDeliveryScope(req, clinicId);

        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 关闭收费单
     *
     * @param chargeSheetId
     * @param clinicId
     * @return
     */
    @PutMapping("/{chargeSheetId}/close")
    @LogReqAndRsp
    @ApiOperation(value = "", hidden = true)
    public AbcServiceResponse<CloseChargeSheetRsp> closeChargeSheetById(@PathVariable("chargeSheetId") String chargeSheetId,
                                                                        @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                        @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) int hisType,
                                                                        @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String operatorId) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(chargeSheetId)) {
            throw new ParamRequiredException("chargeSheetId");
        }

        CloseChargeSheetRsp rsp = mChargeService.closeChargeSheetById(chargeSheetId, clinicId, hisType, operatorId);

        return new AbcServiceResponse<>(rsp);
    }


    /**
     * 查询收费单已收的支付方式及相关金额信息
     *
     * @param chargeSheetId
     * @param clinicId
     * @return
     */
    @GetMapping("/{chargeSheetId}/paid-modes")
    @LogReqAndRsp
    @ApiOperation(value = "查询收费单已收费的收费方式及金额", produces = "application/json")
    public AbcServiceResponse<ChargeSheetPaidModeRsp> queryChargeSheetPaidModes(@ApiParam(value = "收费单id") @PathVariable("chargeSheetId") String chargeSheetId,
                                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(chargeSheetId)) {
            throw new ParamRequiredException("chargeSheetId");
        }

        ChargeSheetPaidModeRsp rsp = mChargeService.queryChargeSheetPaidModes(chargeSheetId, chainId, clinicId);

        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 开启上传收费告知书任务
     *
     * @param chargeSheetId
     * @param clinicId
     * @return
     */
    @PutMapping("/{chargeSheetId}/find-upload-notification-qr-code")
    @LogReqAndRsp
    @ApiOperation(value = "开启上传收费告知书任务（已作废）", produces = "application/json")
    public AbcServiceResponse<AttachmentsQrCodeVO> startUploadChargeNotificationTask(@ApiParam(value = "收费单id") @PathVariable("chargeSheetId") String chargeSheetId,
                                                                                     @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                     @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String operatorId) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(chargeSheetId)) {
            throw new ParamRequiredException("chargeSheetId");
        }

        AttachmentsQrCodeVO rsp = mChargeService.startUploadChargeNotificationTask(chargeSheetId, clinicId, operatorId);

        return new AbcServiceResponse<>(rsp);
    }

    /**
     * pc上传收费告知书
     *
     * @param chargeSheetId
     * @param clinicId
     * @return
     */
    @PutMapping("/{chargeSheetId}/upload-notification")
    @LogReqAndRsp
    @ApiOperation(value = "上传收费告知书(已作废)", produces = "application/json")
    public AbcServiceResponse<UploadChargeNotificationRsp> uploadChargeNotification(@ApiParam(value = "收费单id") @PathVariable("chargeSheetId") String chargeSheetId,
                                                                                    @RequestBody @Valid UploadChargeNotificationReq req,
                                                                                    @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                    @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String operatorId) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(chargeSheetId)) {
            throw new ParamRequiredException("chargeSheetId");
        }

        UploadChargeNotificationRsp rsp = mChargeService.uploadChargeNotification(chargeSheetId, req, clinicId, operatorId);

        return new AbcServiceResponse<>(rsp);
    }


    /**
     * 查询收费单入账异常列表
     *
     * @param chargeSheetId
     * @param clinicId
     * @return
     */
    @GetMapping("/{chargeSheetId}/list-abnormal-transactions")
    @LogReqAndRsp
    @ApiOperation(value = "查询收费单入账异常列表", produces = "application/json")
    public AbcServiceResponse<ChargeSheetAbnormalTransactionsRsp> listAbnormalTransactions(@ApiParam(value = "收费单id") @PathVariable("chargeSheetId") String chargeSheetId,
                                                                                           @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(chargeSheetId)) {
            throw new ParamRequiredException("chargeSheetId");
        }

        ChargeSheetAbnormalTransactionsRsp rsp = chargeAbnormalTransactionService.listAbnormalTransactions(chargeSheetId, clinicId);

        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 锁单且保存收费单
     *
     * @param req
     * @param employeeId
     * @param clinicId
     * @return
     */
    @PostMapping("/lock-save")
    @LogReqAndRsp
    @ApiOperation(value = "锁单且保存收费单")
    public AbcServiceResponse<SaveChargeSheetRsp> lockAndSaveChargeSheet(@RequestBody CreateChargeSheetReq req,
                                                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) int hisType) {
        SaveChargeSheetRsp rsp = mChargeService.lockAndSaveChargeSheet(req, hisType, chainId, clinicId, employeeId);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 异常退费
     *
     * @param chargeSheetId
     * @param clinicId
     * @return
     */
    @PutMapping("/{chargeSheetId}/{abnormalTransactionId}/abnormal-refund")
    @LogReqAndRsp
    @ApiOperation(value = "异常退费", produces = "application/json")
    public AbcServiceResponse<RefundChargeAbnormalTransactionRsp> refundChargeAbnormalTransactionIdById(@ApiParam(value = "收费单id") @PathVariable("chargeSheetId") String chargeSheetId,
                                                                                                        @PathVariable("abnormalTransactionId") String abnormalTransactionId,
                                                                                                        @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                                        @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(chargeSheetId)) {
            throw new ParamRequiredException("chargeSheetId");
        }

        if (org.apache.commons.lang3.StringUtils.isEmpty(abnormalTransactionId)) {
            throw new ParamRequiredException("abnormalTransactionId");
        }

        if (!NumberUtils.isDigits(abnormalTransactionId)) {
            throw new ParamNotValidException("abnormalTransactionId错误");
        }

        RefundChargeAbnormalTransactionRsp rsp = chargeAbnormalTransactionService.refundChargeAbnormalTransactionIdById(chargeSheetId, Long.parseLong(abnormalTransactionId), clinicId, employeeId);

        return new AbcServiceResponse<>(rsp);
    }


    /**
     * 根据patientOrderId查询推送支付需要的订单详情
     *
     * @param patientOrderId
     * @param clinicId
     * @param type           {@link Constants.ChargeSheetPushScanCodeType}
     * @return
     */
    @GetMapping("/patientorder/{patientOrderId}/push-scan-code")
    @LogReqAndRsp
    @ApiOperation(value = "根据patientOrderId查询推送支付需要的订单详情", produces = "application/json")
    public AbcServiceResponse<ChargeSheetPushScanCodeRsp> getChargeSheetPushScanCodeByPatientOrderId(@ApiParam(value = "就诊单id") @PathVariable("patientOrderId") String patientOrderId,
                                                                                                     @ApiParam(value = "场景类型：1：执行站，2：门诊") @RequestParam("type") Integer type,
                                                                                                     @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                                     @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                                     @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(patientOrderId)) {
            throw new ParamRequiredException("patientOrderId");
        }

        if (type == null) {
            throw new ParamRequiredException("type");
        }

        if (type != Constants.ChargeSheetPushScanCodeType.NURSE && type != Constants.ChargeSheetPushScanCodeType.OUTPATIENT) {
            throw new ParamNotValidException("场景类型不支持");
        }

        ChargeSheetPushScanCodeRsp rsp = chargePatientOrderService.getChargeSheetPushScanCodeByPatientOrderId(patientOrderId, chainId, clinicId, type, employeeId);

        return new AbcServiceResponse<>(rsp);
    }


    @PutMapping("/{chargeSheetId}/push-order")
    @LogReqAndRsp
    @ApiOperation(value = "推送订单给患者", produces = "application/json")
    public AbcServiceResponse<PushChargeSheetOrderRsp> pushChargeSheetOrder(@ApiParam(value = "收费单id") @PathVariable("chargeSheetId") String chargeSheetId,
                                                                            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String operatorId) throws NotFoundException, ChargeServiceException, ServiceInternalException, ParamRequiredException, ProductInfoChangedException, InvocationTargetException, IllegalAccessException {

        if (StringUtils.isEmpty(chargeSheetId)) {
            throw new ParamRequiredException("chargeSheetId");
        }

        ChargeSheet chargeSheet = mChargeService.chargeSheetPush(chargeSheetId, clinicId, operatorId);
        PushChargeSheetOrderRsp rsp = new PushChargeSheetOrderRsp();
        rsp.setChargeSheetId(chargeSheet.getId());
        return new AbcServiceResponse<>(rsp);
    }

    @PostMapping("/outpatient/calculate/process")
    @LogReqAndRsp
    @ApiOperation(value = "门诊处给处方计算加工费", produces = "application/json")
    public AbcServiceResponse<CalculateOutpatientProcessFeeRsp> calculateProcessFeeForOutpatient(@RequestBody CalculateOutpatientProcessFeeReq req,
                                                                                                 @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                                 @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {
        CalculateOutpatientProcessFeeRsp rsp = mChargeService.calculateProcessFeeForOutpatient(req, chainId, clinicId);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 根据患者id查询待还的单据数量
     *
     * @param patientId 患者id
     * @param clinicId  诊所id
     * @return
     */
    @PostMapping("/patient/{patientId}/owing-count")
    @LogReqAndRsp
    @ApiOperation(value = "根据患者id查询待还的单据数量", produces = "application/json")
    public AbcServiceResponse<ChargeSheetOweCountRsp> listOwingChargeSheetCountByPatientId(@PathVariable(value = "patientId") String patientId,
                                                                                           @RequestBody ChargeSheetOweCountReq req,
                                                                                           @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {

        if (org.apache.commons.lang3.StringUtils.isEmpty(patientId)) {
            throw new ParamRequiredException("patientId");
        }

        List<String> excludeChargeSheetIds = req.getExcludeChargeSheetIds();

        ChargeSheetOweCountRsp rsp = mChargeService.listOwingChargeSheetCountByPatientId(patientId, clinicId, excludeChargeSheetIds);
        return new AbcServiceResponse<>(rsp);
    }


    @PutMapping("/{chargeSheetId}/{chargeActionId}/charge-action")
    @LogReqAndRsp
    @ApiOperation(value = "修改收费备注", produces = "application/json")
    public AbcServiceResponse<OpsCommonRsp> chargeAcitonCommentUpdate(@PathVariable("chargeSheetId") String chargeSheetId,
                                                                      @PathVariable("chargeActionId") String chargeActionId,
                                                                      @RequestBody ChargeActionUpdateReq req,
                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) {
        mChargeService.updateChargeActionComment(clinicId, chargeSheetId, chargeActionId, req.getComment(), employeeId);
        return new AbcServiceResponse<>(new OpsCommonRsp(OpsCommonRsp.SUCC, "success"));
    }

    @GetMapping("/sales-order/list")
    @LogReqAndRsp
    @ApiOperation(value = "获取销售单列表", produces = "application/json")
    public AbcServiceResponse<SalesOrderAbstractRsp> getSalesOrderAbstractList(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                               @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                               @Validated SalesOrderAbstractReq req) {
        SalesOrderAbstractRsp rsp = mChargeService.getSalesOrderAbstractList(chainId, clinicId, req);
        return new AbcServiceResponse<>(rsp);
    }


    @PutMapping("/only-remark-update")
    @LogReqAndRsp
    @ApiOperation(value = "更新备注", produces = "application/json")
    public AbcServiceResponse<String> updateRemark(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                   @RequestBody @Valid OnlyUpdateRemarkReq req) {
        req.checkParam();
        mChargeService.updateRemark(chainId, clinicId, req.getSceneType(), req.getBusinessId(), req.getRemark(), req.getChargeSheetId(), employeeId);
        return new AbcServiceResponse<>("success");
    }

    /**
     * 根据patientOrderId查询推送支付需要的订单详情
     *
     * @param chargeSheetId
     * @param clinicId
     * @return
     */
    @GetMapping("/{chargeSheetId}/push-scan-code")
    @LogReqAndRsp
    @ApiOperation(value = "根据chargeSheetId查询推送支付需要的订单详情", produces = "application/json")
    public AbcServiceResponse<ChargeSheetPushScanCodeRsp> getChargeSheetPushScanCodeByChargeSheetId(@ApiParam(value = "收费单id") @PathVariable("chargeSheetId") String chargeSheetId,
                                                                                                    @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                                    @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                                    @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(chargeSheetId)) {
            throw new ParamRequiredException("chargeSheetId");
        }


        ChargeSheetPushScanCodeRsp rsp = chargePatientOrderService.getChargeSheetPushScanCodeByChargeSheetId(chargeSheetId, chainId, clinicId, employeeId);

        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 追溯码保存
     * 1.草稿收费单 --  /api/v2/charges 直接收费保存追溯码
     * 2.待收收费单 --  /api/v2/charges/{chargeSheetId}/save
     * 3.完成收费收费单 ----  /api/v2/charges/{chargeSheetId}/save-traceable-code
     * */
    @PutMapping("/{chargeSheetId}/save-traceable-code")
    @ApiOperation(value = "保存追溯码", produces = "application/json")
    @LogReqAndRsp
    public AbcServiceResponse<OpsCommonRsp> saveTraceableCode(
            @ApiParam(value = "收费单id") @PathVariable("chargeSheetId") String chargeSheetId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
            @RequestBody @Valid SaveTraceableCodeReq req
    ) {
        req.checkParam();
        req.setChainId(chainId);
        req.setClinicId(clinicId);
        req.setEmployeeId(employeeId);
        req.setId(chargeSheetId);
        mChargeService.saveTraceableCode(req);
        return new AbcServiceResponse<>(new OpsCommonRsp(OpsCommonRsp.SUCC, "sucess"));
    }

    @GetMapping("/filter-type")
    @ApiOperation(value = "获取收费单过滤类型", produces = "application/json")
    @LogReqAndRsp
    public AbcServiceResponse<ChargeSheetFilterTypeRsp> getChargeSheetFilterType(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                 @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                 @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) Integer hisType) {
        ChargeSheetFilterTypeRsp rsp = mChargeService.getChargeSheetFilterType(chainId, clinicId, hisType);
        return new AbcServiceResponse<>(rsp);
    }

    @LogReqAndRsp
    @PutMapping("/direct/improve/{chargeSheetId}")
    @ApiOperation(value = "完善零售收费单的患者信息", produces = "application/json")
    public AbcServiceResponse<OpsCommonRsp> improveDirectPatient(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
            @PathVariable(value = "chargeSheetId") String chargeSheetId,
            @RequestBody @Valid ChargeImproveDirectPatientReq req) {
        return new AbcServiceResponse<>(mChargeService.improveDirectPatient(chainId, clinicId, chargeSheetId, employeeId, req));
    }

    /**
     * 挂单的收费单批量提单
     *
     * @param clinicId
     * @param employeeId
     * @param req
     * @return
     */
    @LogReqAndRsp
    @PostMapping("/draft/batch-extract")
    @ApiOperation(value = "挂单的收费单批量提单", produces = "application/json")
    public AbcServiceResponse<ChargeSheetDraftBatchExtractRsp> chargeSheetDraftBatchExtract (
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) int hisType,
            @RequestBody @Valid ChargeSheetDraftBatchExtractReq req) {

        return new AbcServiceResponse<>(mChargeService.chargeSheetDraftBatchExtract(clinicId, hisType, employeeId, req));
    }

    /**
     * 收费业务的收费类型对应的打印记录
     */
    @LogReqAndRsp
    @GetMapping("/business/{businessId}/type/print-record")
    @ApiOperation(value = "收费业务的收费类型对应的打印记录", produces = "application/json")
    public AbcServiceResponse<ChargeBusinessPrintRecordView> getChargeBusinessPrintRecord (
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @PathVariable(value = "businessId") String businessId,
            @RequestParam(value = "businessType") Integer businessType) {
        return new AbcServiceResponse<>(chargeBusinessPrintService.getChargeBusinessPrintRecord(chainId, clinicId, businessId, businessType));
    }

    /**
     * 新增或者更新收费业务的打印记录
     */
    @LogReqAndRsp
    @PostMapping("/business/print-record")
    @ApiOperation(value = "新增或者更新收费业务的打印记录", produces = "application/json")
    public AbcServiceResponse<ChargeBusinessPrintRecordView> insertOrUpdateChargeBusinessPrintRecord (
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String operatorId,
            @RequestBody @Valid ChargeBusinessPrintRecordReq req) {
        return new AbcServiceResponse<>(chargeBusinessPrintService.insertOrUpdateChargeBusinessPrintRecord(chainId, clinicId, operatorId, req));
    }

    /**
     * 获取收费单的打印按钮对应的数据信息
     */
    @LogReqAndRsp
    @GetMapping("/{chargeSheetId}/print-button-data")
    @ApiOperation(value = "获取收费单的打印按钮对应的数据信息", produces = "application/json")
    public AbcServiceResponse<ChargeBusinessPrintView> getChargeBusinessPrintInfo(
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) Integer hisType,
            @PathVariable(value = "chargeSheetId") String chargeSheetId) {
        return new AbcServiceResponse<>(mChargeService.getChargeBusinessPrintInfo(chainId, clinicId, hisType, chargeSheetId));
    }

}
