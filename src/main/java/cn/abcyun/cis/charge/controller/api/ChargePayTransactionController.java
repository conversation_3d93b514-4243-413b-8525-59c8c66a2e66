package cn.abcyun.cis.charge.controller.api;

import cn.abcyun.bis.rpc.sdk.common.model.BasicCommonRsp;
import cn.abcyun.cis.charge.api.model.CancelChargeSheetRsp;
import cn.abcyun.cis.charge.api.model.PayStatusRsp;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.service.ChargePayTransactionService;
import cn.abcyun.cis.charge.service.ChargeService;
import cn.abcyun.cis.charge.service.dto.LockPayTransactionView;
import cn.abcyun.cis.commons.exception.ServiceInternalException;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.abcyun.cis.core.aop.LogReqAndRsp;
import cn.abcyun.common.model.AbcListPage;
import cn.abcyun.common.model.AbcServiceResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v2/charges/charge-pay-transaction")
@Api(value = "ChargeController", description = "收费单第三方支付单相关接口", produces = "application/json")
public class ChargePayTransactionController {

    @Autowired
    private ChargePayTransactionService chargePayTransactionService;

    /**
     * 根据收费单id查询第三方支付的异常订单
     * @param clinicId
     * @return
     */
    @GetMapping(value = "/list-error-pay-transactions-by-charge-sheet-id/{chargeSheetId}")
    @ApiOperation(value = "根据chargePayTransactionId解锁")
    @LogReqAndRsp
    public AbcServiceResponse<AbcListPage<LockPayTransactionView>> listErrorPayTransactionsByChargeSheetId(@PathVariable("chargeSheetId") String chargeSheetId,
                                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {

        AbcListPage<LockPayTransactionView> rsp = chargePayTransactionService.listErrorPayTransactionsByChargeSheetId(chainId, clinicId, chargeSheetId);
        return new AbcServiceResponse<>(rsp);
    }


}
