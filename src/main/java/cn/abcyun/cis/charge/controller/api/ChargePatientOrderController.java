package cn.abcyun.cis.charge.controller.api;

import cn.abcyun.cis.charge.api.model.BaseSuccessRsp;
import cn.abcyun.cis.charge.service.ChargePatientOrderService;
import cn.abcyun.cis.charge.service.dto.ChargeSheetAbstract;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.abcyun.cis.core.aop.LogReqAndRsp;
import cn.abcyun.common.model.AbcListPage;
import cn.abcyun.common.model.AbcServiceResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api/v2/charges/patientorders")
@Api(value = "ChargePatientOrderController", produces = "application/json")
public class ChargePatientOrderController {

    private final ChargePatientOrderService chargePatientOrderService;

    @Autowired
    public ChargePatientOrderController(ChargePatientOrderService chargePatientOrderService) {
        this.chargePatientOrderService = chargePatientOrderService;
    }

    /**
     * 给patientOrder加锁
     *
     * @param patientOrderId 就诊单id
     * @param clinicId
     * @return
     */
    @PutMapping("/{patientOrderId}/lock")
    @LogReqAndRsp
    @ApiOperation(value = "就诊单加锁", produces = "application/json")
    public AbcServiceResponse<BaseSuccessRsp> lockPatientOrderId(@PathVariable(value = "patientOrderId") String patientOrderId,
                                                                 @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                 @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                 @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) {
        if (StringUtils.isEmpty(patientOrderId)) {
            throw new ParamRequiredException("patientOrderId");
        }
        BaseSuccessRsp rsp = chargePatientOrderService.lockPatientOrderId(patientOrderId, chainId, employeeId);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 给patientOrder解锁
     *
     * @param patientOrderId 就诊单id
     * @param clinicId
     * @return
     */
    @PutMapping("/{patientOrderId}/un-lock")
    @LogReqAndRsp
    @ApiOperation(value = "就诊单解锁", produces = "application/json")
    public AbcServiceResponse<BaseSuccessRsp> unLockPatientOrderId(@PathVariable(value = "patientOrderId") String patientOrderId,
                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) {
        if (StringUtils.isEmpty(patientOrderId)) {
            throw new ParamRequiredException("patientOrderId");
        }
        BaseSuccessRsp rsp = chargePatientOrderService.unLockPatientOrderId(patientOrderId, chainId, clinicId, employeeId);
        return new AbcServiceResponse<>(rsp);
    }

    @GetMapping("/{patientOrderId}/charge-sheets")
    @ApiOperation(value = "根据patientOrderId查询收费单列表")
    public AbcServiceResponse<AbcListPage<ChargeSheetAbstract>> getChargeSheetAbstractListByPatientOrderId(@PathVariable(value = "patientOrderId") String patientOrderId,
                                                                                                           @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId){


        if (StringUtils.isEmpty(patientOrderId)) {
            throw new ParamRequiredException("patientOrderId");
        }

        List<ChargeSheetAbstract> chargeSheetAbstractList = chargePatientOrderService.getChargeSheetAbstractListByPatientOrderId(chainId, patientOrderId);
        AbcListPage<ChargeSheetAbstract> rsp = new AbcListPage<>();
        rsp.setRows(new ArrayList<>());

        if (CollectionUtils.isNotEmpty(chargeSheetAbstractList)){
            rsp.setRows(chargeSheetAbstractList);
        }

        return new AbcServiceResponse<>(rsp);
    }


}
