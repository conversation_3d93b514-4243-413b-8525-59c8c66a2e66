package cn.abcyun.cis.charge.controller.api;

import cn.abcyun.bis.rpc.sdk.cis.model.charge.ChargeSheetRegisterInfoView;
import cn.abcyun.cis.charge.api.model.ChargeSheetRegisterIdentityReq;
import cn.abcyun.cis.charge.api.model.ChargeSheetRegisterPrescriptionReq;
import cn.abcyun.cis.charge.service.ChargeSheetRegisterService;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.abcyun.cis.core.aop.LogReqAndRsp;
import cn.abcyun.common.model.AbcServiceResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @create 2024-01-20 17:45
 * @Description
 */

@RestController
@RequestMapping("/api/v2/charges/register")
@Api(value = "ChargeSheetRegisterController", description = "收费单登记信息相关接口", produces = "application/json")
@Slf4j
public class ChargeSheetRegisterController {

    @Autowired
    private ChargeSheetRegisterService chargeSheetRegisterService;

    @PostMapping("/add/identity")
    @LogReqAndRsp
    @ApiOperation(value = "添加实名认证信息", produces = "application/json")
    public AbcServiceResponse<ChargeSheetRegisterInfoView> addRegisterIdentity(@RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                               @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                               @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                               @RequestBody @Validated ChargeSheetRegisterIdentityReq req) {
        ChargeSheetRegisterInfoView rsp = chargeSheetRegisterService.addRegisterIdentity(chainId, clinicId, employeeId, req);
        return new AbcServiceResponse<>(rsp);
    }

    @PostMapping("/add/prescription")
    @LogReqAndRsp
    @ApiOperation(value = "添加处方登记信息", produces = "application/json")
    public AbcServiceResponse<ChargeSheetRegisterInfoView> addRegisterPrescriptionInfo(@RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                           @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                           @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                           @RequestBody @Validated ChargeSheetRegisterPrescriptionReq req) {
        ChargeSheetRegisterInfoView rsp = chargeSheetRegisterService.addRegisterPrescriptionInfo(chainId, clinicId, employeeId, req);
        return new AbcServiceResponse<>(rsp);
    }

    @GetMapping("/{registerInfoId}")
    @LogReqAndRsp
    @ApiOperation(value = "获取登记信息", produces = "application/json")
    public AbcServiceResponse<ChargeSheetRegisterInfoView> getRegisterInfo(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                           @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                           @PathVariable Long registerInfoId) {
        ChargeSheetRegisterInfoView infoView = chargeSheetRegisterService.getRegisterInfo(chainId, clinicId, registerInfoId);
        return new AbcServiceResponse<>(infoView);
    }


}
