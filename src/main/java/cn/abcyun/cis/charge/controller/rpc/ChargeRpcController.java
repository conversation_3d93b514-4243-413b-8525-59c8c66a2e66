package cn.abcyun.cis.charge.controller.rpc;

import cn.abcyun.bis.rpc.sdk.cis.model.charge.*;
import cn.abcyun.bis.rpc.sdk.cis.model.charge.MemberCardRechargeReq;
import cn.abcyun.bis.rpc.sdk.cis.model.charge.MemberCardRechargeRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.charge.QueryChargeStatusBatchReq;
import cn.abcyun.bis.rpc.sdk.cis.model.charge.QueryRegistrationChargeInfoRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.charge.openapi.charge.OpenApiCancelDeliveryReq;
import cn.abcyun.bis.rpc.sdk.cis.model.charge.print.ChargeSheetAstResultRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.OpsCommonRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.outpatient.OutpatientSheet;
import cn.abcyun.bis.rpc.sdk.cis.model.outpatient.OutpatientSheetShebaoSimpleInfo;
import cn.abcyun.bis.rpc.sdk.cis.model.pay.PayCallbackRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.shebao.MobilePayPreCheckRspBody;
import cn.abcyun.bis.rpc.sdk.cis.model.wechatpay.WeChatMobileShebaoPayRsp;
import cn.abcyun.bis.rpc.sdk.common.model.BasicCommonRsp;
import cn.abcyun.cis.charge.amqp.HAMQConsumer;
import cn.abcyun.cis.charge.api.model.*;
import cn.abcyun.cis.charge.api.model.BaseSuccessRsp;
import cn.abcyun.cis.charge.api.model.BasicCalculateSheetReq;
import cn.abcyun.cis.charge.api.model.BasicCalculateSheetRsp;
import cn.abcyun.cis.charge.api.model.BatchBasicCalculateSheetReq;
import cn.abcyun.cis.charge.api.model.BatchBasicCalculateSheetRsp;
import cn.abcyun.cis.charge.api.model.BatchDeleteChargeSheetReq;
import cn.abcyun.cis.charge.api.model.BatchDeleteChargeSheetRsp;
import cn.abcyun.cis.charge.api.model.CalculateProcessorBagCountReq;
import cn.abcyun.cis.charge.api.model.CancelChargeSheetRsp;
import cn.abcyun.cis.charge.api.model.ChargeRefundedItemView;
import cn.abcyun.cis.charge.api.model.ChargeSheetReplenishReq;
import cn.abcyun.cis.charge.api.model.ChargeSheetReplenishRsp;
import cn.abcyun.cis.charge.api.model.ChargeSheetSimpleListRsp;
import cn.abcyun.cis.charge.api.model.CreateChargeSheetForExaminationInspectionReq;
import cn.abcyun.cis.charge.api.model.CreateChargeSheetForExaminationInspectionRsp;
import cn.abcyun.cis.charge.api.model.CreateChargeSheetForFamilyDoctorReq;
import cn.abcyun.cis.charge.api.model.CreateChargeSheetForFamilyDoctorRsp;
import cn.abcyun.cis.charge.api.model.CreateChargeSheetForShebaoOutsourcePrescriptionReq;
import cn.abcyun.cis.charge.api.model.CreateChargeSheetForShebaoOutsourcePrescriptionRsp;
import cn.abcyun.cis.charge.api.model.CreateChargeSheetForThirdPartyReq;
import cn.abcyun.cis.charge.api.model.CreateChargeSheetForThirdPartyRsp;
import cn.abcyun.cis.charge.api.model.CreatePaidChargeSheetReq;
import cn.abcyun.cis.charge.api.model.CreatePaidChargeSheetRsp;
import cn.abcyun.cis.charge.api.model.DeleteChargeSheetForFamilyDoctorCancelRsp;
import cn.abcyun.cis.charge.api.model.DoctorPatientDiagnosedChargedInfoReq;
import cn.abcyun.cis.charge.api.model.DoctorPatientDiagnosedChargedInfoRsp;
import cn.abcyun.cis.charge.api.model.MedicalPlanSheetDetailCalculateReq;
import cn.abcyun.cis.charge.api.model.MedicalPlanSheetDetailCalculateRsp;
import cn.abcyun.cis.charge.api.model.OutpatientSheetCalculateReq;
import cn.abcyun.cis.charge.api.model.OutpatientSheetCalculateRsp;
import cn.abcyun.cis.charge.api.model.OutpatientSheetDetailCalculateReq;
import cn.abcyun.cis.charge.api.model.PatientDeliveryInfoListReq;
import cn.abcyun.cis.charge.api.model.PayChargeSheetByChargePayTransactionIdReq;
import cn.abcyun.cis.charge.api.model.PayChargeSheetRsp;
import cn.abcyun.cis.charge.api.model.PayModeDetailRsp;
import cn.abcyun.cis.charge.api.model.PayModeListRsp;
import cn.abcyun.cis.charge.api.model.PayStatusRsp;
import cn.abcyun.cis.charge.api.model.QueryChargeRefundItemReq;
import cn.abcyun.cis.charge.api.model.QueryChargeSheetByPatientOrderIdsReq;
import cn.abcyun.cis.charge.api.model.QueryChargeSheetByPatientOrderIdsRsp;
import cn.abcyun.cis.charge.api.model.QueryChargeSheetExceptionStatusListReq;
import cn.abcyun.cis.charge.api.model.QueryChargeSheetExceptionStatusListRsp;
import cn.abcyun.cis.charge.api.model.QueryPatientOrderNotificationsReq;
import cn.abcyun.cis.charge.api.model.QueryPatientOrderNotificationsRsp;
import cn.abcyun.cis.charge.api.model.QuerySimpleChargeSheetsReq;
import cn.abcyun.cis.charge.api.model.QueryUnchargedChargeSheetsByPatientIdsReq;
import cn.abcyun.cis.charge.api.model.RefundChargeSheetRsp;
import cn.abcyun.cis.charge.api.model.RpcCalculateShebaoLimitPriceReq;
import cn.abcyun.cis.charge.api.model.RpcCalculateShebaoLimitPriceRsp;
import cn.abcyun.cis.charge.api.model.RpcOnlyPayChargeSheetReq;
import cn.abcyun.cis.charge.api.model.RpcPushChargeSheetReq;
import cn.abcyun.cis.charge.api.model.RpcQueryChargeFormItemsReq;
import cn.abcyun.cis.charge.api.model.ShebaoErrorUpdateReq;
import cn.abcyun.cis.charge.api.model.ThirdPartPayForChargeSheetReq;
import cn.abcyun.cis.charge.api.model.UpdateDeliveryTraceDataReq;
import cn.abcyun.cis.charge.api.model.WeClinicChargeHistoryListReq;
import cn.abcyun.cis.charge.api.model.WeClinicChargeHistoryListRsp;
import cn.abcyun.cis.charge.api.model.WeClinicCreateChargeSheetByCloneReq;
import cn.abcyun.cis.charge.api.model.WeClinicCreateChargeSheetByCloneRsp;
import cn.abcyun.cis.charge.api.model.WeClinicCreateChargeSheetByPhotoReq;
import cn.abcyun.cis.charge.api.model.WeClinicCreateChargeSheetByPhotoRsp;
import cn.abcyun.cis.charge.api.model.openapi.OpenApiDeliveryReq;
import cn.abcyun.cis.charge.api.model.openapi.OpenApiPaidChargeSheetReq;
import cn.abcyun.cis.charge.api.model.openapi.OpenApiRefundChargeSheetReq;
import cn.abcyun.cis.charge.api.model.orderlist.ChargeOrderListReq;
import cn.abcyun.cis.charge.api.model.orderlist.ChargeOrderViewRsp;
import cn.abcyun.cis.charge.base.ChargeServiceError;
import cn.abcyun.cis.charge.base.ChargeServiceException;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.base.ProductInfoChangedException;
import cn.abcyun.cis.charge.controller.StatusNameTranslator;
import cn.abcyun.cis.charge.controller.api.ChargeFormItemMerger;
import cn.abcyun.cis.charge.facade.ChargeCooperationOrderFacade;
import cn.abcyun.cis.charge.facade.ChargeMedicareLimitPriceFacade;
import cn.abcyun.cis.charge.model.ChargeFormItem;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.processor.ChargePayModeConfigSimple;
import cn.abcyun.cis.charge.rpc.model.*;
import cn.abcyun.cis.charge.rpc.model.ChargeSheetTraceCodesRsp;
import cn.abcyun.cis.charge.rpc.model.ChargeSheetViewList;
import cn.abcyun.cis.charge.rpc.model.ChargeTodo;
import cn.abcyun.cis.charge.rpc.model.PatientDeliveryInfoList;
import cn.abcyun.cis.charge.service.*;
import cn.abcyun.cis.charge.service.dto.*;
import cn.abcyun.cis.charge.service.dto.ChargeSheetAbstract;
import cn.abcyun.cis.charge.service.dto.ChargeSheetExtend;
import cn.abcyun.cis.charge.service.dto.ChargeSheetExtendListRsp;
import cn.abcyun.cis.charge.service.dto.ChargeSheetRpcAbstract;
import cn.abcyun.cis.charge.service.dto.ChargeSheetView;
import cn.abcyun.cis.charge.service.dto.PayChargeSheetResult;
import cn.abcyun.cis.charge.service.dto.RefundChargeSheetResult;
import cn.abcyun.cis.charge.service.dto.RegistrationChargeSheetView;
import cn.abcyun.cis.charge.service.dto.SimpleChargeInfo;
import cn.abcyun.cis.charge.service.dto.UsageTypeNode;
import cn.abcyun.cis.charge.service.rpc.CisScGoodsService;
import cn.abcyun.cis.charge.util.ChargeUtils;
import cn.abcyun.cis.charge.util.JsonUtils;
import cn.abcyun.cis.charge.util.ProcessorUtils;
import cn.abcyun.cis.charge.util.RedisUtils;
import cn.abcyun.cis.commons.CisServiceResponse;
import cn.abcyun.cis.commons.amqp.message.ChargeMessage;
import cn.abcyun.cis.commons.exception.*;
import cn.abcyun.cis.commons.rpc.charge.*;
import cn.abcyun.cis.commons.rpc.charge.ChargeDeliveryInfoView;
import cn.abcyun.cis.commons.rpc.charge.ChargeStatus;
import cn.abcyun.cis.commons.rpc.charge.QueryChargeStatusBatchRsp;
import cn.abcyun.cis.commons.rpc.pay.PayCallbackReq;
import cn.abcyun.cis.commons.util.DateUtils;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.cis.core.aop.LogReqAndRsp;
import cn.abcyun.common.log.marker.AbcLogMarker;
import cn.abcyun.common.model.AbcListPage;
import cn.abcyun.common.model.AbcServiceResponse;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/rpc/charges")
@Api(value = "ChargeRpcController", description = "收费单Rpc相关接口", produces = "application/json")
public class ChargeRpcController {
    private static final Logger sLogger = LoggerFactory.getLogger(ChargeRpcController.class);

    @Autowired
    private ChargeSheetService mChargeSheetService;
    @Autowired
    private ChargePayService mChargePayService;
    @Autowired
    private ChargeService mChargeService;
    @Autowired
    private ChargeStatService chargeStatService;
    @Autowired
    private CisScGoodsService cisScGoodsService;
    @Autowired
    private ClinicService mClinicService;
    @Autowired
    private DeliveryService mDeliveryService;
    @Autowired
    private ChargeSheetService chargeSheetService;
    @Autowired
    private ChargeConfigService chargeConfigService;
    @Autowired
    private WeClinicChargeService weClinicChargeService;
    @Autowired
    private ChargeRuleProcessUsageTypeService chargeRuleProcessUsageTypeService;
    @Autowired
    private ChargeExecuteRecordService chargeExecuteRecordService;
    @Autowired
    private ChargeRefreshDataService chargeRefreshDataService;
    @Autowired
    private ChargeThirdPartService chargeThirdPartService;
    @Autowired
    private ChargePatientOrderNotificationService chargePatientOrderNotificationService;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private ChargeCalculateService chargeCalculateService;
    @Autowired
    private ChargeRuleExpressDeliveryService chargeRuleExpressDeliveryService;
    @Autowired
    private ChargeExaminationService chargeExaminationService;
    @Autowired
    private ChargeDeliveryTraceService chargeDeliveryTraceService;
    @Autowired
    private ChargeImportService chargeImportService;
    @Autowired
    private ChargePatientOrderService chargePatientOrderService;

    @Autowired
    private ChargePrintService chargePrintService;

    @Autowired
    private ChargeShebaoService chargeShebaoService;
    @Autowired
    private ChargePhysicalTherapyService chargePhysicalTherapyService;

    @Autowired
    private ChargeMedicareLimitPriceFacade chargeMedicareLimitPriceFacade;

    @Autowired
    private ChargeSheetProcessInfoService chargeSheetProcessInfoService;

    @Autowired
    private ChargeCooperationOrderFacade chargeCooperationOrderFacade;

    @Autowired
    private HAMQConsumer hamqConsumer;

    @GetMapping("/patientorders/{patientOrderId}")
    @LogReqAndRsp(rpcCallStat = true)
    public CisServiceResponse<ChargeSheetExtendList> getChargeSheetListByPatientOrderId(@PathVariable("patientOrderId") String patientOrderId) throws ParamRequiredException {
        if (TextUtils.isEmpty(patientOrderId)) {
            throw new ParamRequiredException("patientOrderId");
        }
        List<ChargeSheet> chargeSheets = mChargeSheetService.findAllByPatientOrderId(patientOrderId);
        List<ChargeSheetExtend> chargeSheetExtends = new ArrayList<>();
        if (chargeSheets != null) {
            for (ChargeSheet chargeSheet : chargeSheets) {
                ChargeSheetExtend chargeSheetExtend = DTOConverter.convertToChargeSheetExtend(chargeSheet);
                ChargeFormItemMerger.mergeForChargeSheet(chargeSheetExtend);
                chargeSheetExtend.setChargeSheetSummary(mChargeService.generateChargeSheetSummary(chargeSheet));
                ChargeUtils.loadChargeSheetProductInfo(chargeSheetExtend, cisScGoodsService);
                chargeSheetExtends.add(chargeSheetExtend);
            }
        }
        ChargeSheetExtendList rsp = new ChargeSheetExtendList();
        rsp.setChargeSheets(chargeSheetExtends);
        return new CisServiceResponse<>(rsp);
    }

    @GetMapping("/patientorders/{patientOrderId}/sheetview")
    @LogReqAndRsp(rpcCallStat = true)
    public CisServiceResponse<ChargeSheetViewList> getChargeSheetViewListByPatientOrderId(@PathVariable("patientOrderId") String patientOrderId,
                                                                                          @RequestParam(value = "withExaminationResult", required = false) Integer withExaminationResult,
                                                                                          @RequestParam(value = "chargeType", required = false) Integer chargeType,
                                                                                          @RequestParam(value = "withTransactionRecord", required = false) Integer withTransactionRecord) throws ParamRequiredException, ServiceInternalException {
        if (TextUtils.isEmpty(patientOrderId)) {
            throw new ParamRequiredException("patientOrderId");
        }
        boolean withTransactionRecordBool = withTransactionRecord != null && withTransactionRecord == 1;
        List<ChargeSheetView> chargeSheets = mChargeService.findChargeSheetByPatientOrderId(patientOrderId, withExaminationResult != null && withExaminationResult == 1, Constants.ChargeSource.CHARGE, chargeType, withTransactionRecordBool);
        ChargeSheetViewList rsp = new ChargeSheetViewList();
        rsp.setChargeSheets(chargeSheets);
        return new CisServiceResponse<>(rsp);
    }

    @GetMapping("/{id}")
    @LogReqAndRsp(rpcCallStat = true)
    public CisServiceResponse<ChargeSheetExtend> getChargeSheetById(@PathVariable("id") String id) throws ParamRequiredException {
        if (TextUtils.isEmpty(id)) {
            sLogger.info("required id...");
            throw new ParamRequiredException("id");
        }
        ChargeSheet chargeSheet = mChargeSheetService.findById(id);
        if (chargeSheet == null) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "getChargeSheetById NOT FOUND id:{}", id);
            return new CisServiceResponse<>(HttpStatus.OK, null);
        }
        DTOConverter.convertToChargeFormItemExtend(chargeSheet);
        ChargeSheetExtend chargeSheetExtend = DTOConverter.convertToChargeSheetExtend(chargeSheet);
        ChargeFormItemMerger.mergeForChargeSheet(chargeSheetExtend);
        chargeSheetExtend.setChargeSheetSummary(mChargeService.generateChargeSheetSummary(chargeSheet));
        ChargeUtils.loadChargeSheetProductInfo(chargeSheetExtend, cisScGoodsService);
        return new CisServiceResponse<>(chargeSheetExtend);
    }

    @GetMapping("/patientorders/{patientOrderId}/charge-sheets")
    @LogReqAndRsp(rpcCallStat = true)
    public CisServiceResponse<ChargeSheetExtendListRsp> getChargeSheetByPatientOrderId(@PathVariable("patientOrderId") String patientOrderId) throws ParamRequiredException {
        ChargeSheetExtendListRsp chargeSheetExtendListRsp = new ChargeSheetExtendListRsp();
        if (TextUtils.isEmpty(patientOrderId)) {
            throw new ParamRequiredException("patientOrderId");
        }
        List<ChargeSheet> chargeSheets = mChargeSheetService.findAllByPatientOrderId(patientOrderId);
        if (ObjectUtils.isEmpty(chargeSheets)) {
            return new CisServiceResponse<>(chargeSheetExtendListRsp);
        }
        chargeSheetExtendListRsp.setChargeSheets(chargeSheets
                .stream()
                .map(chargeSheet -> {
                    ChargeSheetExtend chargeSheetExtend = DTOConverter.convertToChargeSheetExtend(chargeSheet);
                    ChargeFormItemMerger.mergeForChargeSheet(chargeSheetExtend);
                    chargeSheetExtend.setChargeSheetSummary(mChargeService.generateChargeSheetSummary(chargeSheet));
                    return chargeSheetExtend;
                }).collect(Collectors.toList()));
        return new CisServiceResponse<>(chargeSheetExtendListRsp);
    }

    @GetMapping(value = {"/{chargeSheetId}/detail", "/{chargeSheetId}/detail-by-scene"})
    @LogReqAndRsp(rpcCallStat = true)
    public CisServiceResponse<ChargeSheetView> getChargeSheetViewByIdAndScene(@PathVariable("chargeSheetId") String chargeSheetId,
                                                                              /**
                                                                               * 场景 {@link ChargeConstants.QueryChargeSheetScene}
                                                                               */
                                                                              @RequestParam(value = "scene", required = false) Integer scene) {
        ChargeSheetView chargeSheetView = mChargeService.findChargeSheetById(chargeSheetId, Constants.ChargeSource.WE_CLINIC, true, scene);
        if (chargeSheetView == null) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "getChargeSheetById:{} not found", chargeSheetId);
            throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_DETAIL_NOT_EXISTED);
        }
        chargeSheetView.setCanUnLockChargeSheet(ChargeUtils.isChargerLockStatus(chargeSheetView.getLockStatus()));
        return new CisServiceResponse<>(chargeSheetView);
    }

    /**
     * 查询收费单详情，详情中包含收费明细的子项信息
     *
     * @param chargeSheetId
     * @return
     */
    @GetMapping("/{chargeSheetId}/detail-with-records")
    @LogReqAndRsp(rpcCallStat = true)
    public CisServiceResponse<ChargeSheetView> getChargeSheetWithTransactionRecordsViewById(@PathVariable("chargeSheetId") String chargeSheetId) {
        ChargeSheetView chargeSheetView = mChargeService.findChargeSheetById(chargeSheetId, Constants.ChargeSource.WE_CLINIC, true, true, false, Constants.ChargeSheetDispensingQueryCheckType.DISPENSING_QUERY_NOT_CHECK, null);
        if (chargeSheetView == null) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "getChargeSheetById:{} not found", chargeSheetId);
            throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_DETAIL_NOT_EXISTED);
        }
        chargeSheetView.setCanUnLockChargeSheet(ChargeUtils.isChargerLockStatus(chargeSheetView.getLockStatus()));
        return new CisServiceResponse<>(chargeSheetView);
    }

    @PostMapping("/registration")
    @LogReqAndRsp
    public CisServiceResponse<RegistrationChargeSheetView> postChargeForRegistration(@RequestBody ChargeForRegistrationReq registrationReq) {
        RegistrationChargeSheetView rsp = mChargeService.payForRegistration(registrationReq);
        return new CisServiceResponse<>(rsp);
    }

    @Deprecated
    @PostMapping("/registration/refund")
    @LogReqAndRsp
    public CisServiceResponse<RefundForRegistrationRsp> postRefundForRegistration(@RequestBody RefundForRegistrationReq refundForRegistrationReq) {
        RefundChargeSheetResult result = mChargeService.refundForRegistration(refundForRegistrationReq);

        RefundForRegistrationRsp rsp = new RefundForRegistrationRsp();
        rsp.setRefundedFee(result.getRefundedFee());
        return new CisServiceResponse<>(rsp);
    }

    @GetMapping("/query/registration/{patientOrderId}")
    @LogReqAndRsp(rpcCallStat = true)
    public CisServiceResponse<ChargeFormItem> getQueryRegistrationChargeStatusRsp(@PathVariable("patientOrderId") String patientOrderId) {
        ChargeFormItem result = mChargeService.queryRegistrationChargeItem(patientOrderId);
        if (result == null) {
            return new CisServiceResponse<>(HttpStatus.OK, null);
        }
        return new CisServiceResponse<>(result);
    }

    @PostMapping("/query/registrations")
    @LogReqAndRsp(rpcCallStat = true)
    public CisServiceResponse<QueryRegistrationChargeInfoRsp> queryRegistrationChargeInfoBatchByPatientOrderIds(@RequestBody QueryChargeSheetByPatientOrderIdsReq req) {
        QueryRegistrationChargeInfoRsp rsp = mChargeService.queryRegistrationChargeInfoBatchByPatientOrderIds(req);
        return new CisServiceResponse<>(rsp);
    }

    @GetMapping("/registration/{patientOrderId}/chargesheet")
    @LogReqAndRsp(rpcCallStat = true)
    public AbcServiceResponse<RegistrationChargeSheetView> getRegistrationChargeSheetView(@PathVariable("patientOrderId") String patientOrderId,
                                                                                          @RequestParam(value = "withShebaoPayInfo", required = false, defaultValue = "0") int withShebaoPayInfo) throws NotFoundException, ServiceInternalException {
        RegistrationChargeSheetView result = mChargeService.getRegistrationChargeSheetView(patientOrderId, withShebaoPayInfo);
        return new AbcServiceResponse<>(result);
    }


    @GetMapping("/query/outpatient/{patientOrderId}")
    @LogReqAndRsp(rpcCallStat = true)
    public CisServiceResponse<QueryOutpatientSheetChargeStatusRsp> getQueryOutpatientSheetChargeStatusRsp(@PathVariable("patientOrderId") String patientOrderId,
                                                                                                          @RequestParam(value = "withExecutedCount", required = false) Integer withExecutedCount,
                                                                                                          @RequestParam(value = "withDeliveryAndProcess", required = false) Integer withDeliveryAndProcess) {
        QueryOutpatientSheetChargeStatusRsp rsp = mChargeSheetService.queryChargeSheetStatus(patientOrderId, withExecutedCount != null && withExecutedCount == 1, withDeliveryAndProcess != null && withDeliveryAndProcess == 1);
        return new CisServiceResponse<>(rsp);
    }

    //批量接口，含义同 @GetMapping("/query/outpatient/{patientOrderId}")
    @PostMapping("/query/outpatient")
    @LogReqAndRsp(rpcCallStat = true)
    public CisServiceResponse<QueryOutpatientChargeStatusBatchRsp> queryOutpatientChargeStatuses(@RequestBody QueryChargeStatusBatchReq req) throws NotFoundException {
        QueryOutpatientChargeStatusBatchRsp rsp = new QueryOutpatientChargeStatusBatchRsp();
        List<OutpatientChargeStatus> chargeStatusList = mChargeSheetService.queryOutpatientChargeStatuses(req.getPatientOrderIds());
        rsp.setChargeStatusList(chargeStatusList);
        return new CisServiceResponse<>(rsp);
    }

    @PostMapping("/query")
    public CisServiceResponse<QueryChargeStatusBatchRsp> queryChargeStatusBatch(@RequestBody QueryChargeStatusBatchReq req) {
        QueryChargeStatusBatchRsp rsp = new QueryChargeStatusBatchRsp();
        List<ChargeStatus> chargeStatusList = mChargeSheetService.queryChargeStatusByPatientOrderIds(req.getPatientOrderIds());
        rsp.setChargeStatusList(chargeStatusList);
        return new CisServiceResponse<>(rsp);
    }

    @GetMapping("/stat/info")
    public CisServiceResponse<ChargeStatInfo> getChargeStatInfo(@RequestParam(value = "clinicId") String clinicId,
                                                                @RequestParam(value = "beginDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date beginDate,
                                                                @RequestParam(value = "endDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
        ChargeStatInfo chargeStatInfo = new ChargeStatInfo();
        if (beginDate != null) {
            beginDate = DateUtils.getStartTime(beginDate);
        }
        if (endDate != null) {
            endDate = DateUtils.getEndTime(endDate);
        }

        chargeStatInfo.setChargedCount(mChargeService.findChargedCount(clinicId, beginDate, endDate));
        chargeStatInfo.setChargedPrice(mChargeService.findChargedAmount(clinicId, beginDate, endDate));
        chargeStatInfo.setChargedOutpatientPrice(mChargeService.findChargedOutpatientAmount(clinicId, null, beginDate, endDate));

        return new CisServiceResponse<>(chargeStatInfo);
    }

    @GetMapping("/stat/info/doctor")
    public CisServiceResponse<DoctorChargeStatInfo> getChargeStatInfo(@RequestParam(value = "clinicId") String clinicId,
                                                                      @RequestParam(value = "doctorId") String doctorId,
                                                                      @RequestParam(value = "beginDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date beginDate,
                                                                      @RequestParam(value = "endDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
        DoctorChargeStatInfo doctorChargeStatInfo = new DoctorChargeStatInfo();
        if (beginDate != null) {
            beginDate = DateUtils.getStartTime(beginDate);
        }
        if (endDate != null) {
            endDate = DateUtils.getEndTime(endDate);
        }

        doctorChargeStatInfo.setIncomeFee(mChargeService.findChargedOutpatientAmount(clinicId, doctorId, beginDate, endDate));
        return new CisServiceResponse<>(doctorChargeStatInfo);
    }


    @GetMapping("/stat/info/employee")
    public CisServiceResponse<EmployeeChargeStatInfo> getEmployeeChargeStatInfo(@RequestParam(value = "clinicId") String clinicId,
                                                                                @RequestParam(value = "employeeId") String employeeId,
                                                                                @RequestParam(value = "beginDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date beginDate,
                                                                                @RequestParam(value = "endDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {

        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "getEmployeeChargeStatInfo, clinicId:{}, employeeId:{}, beginDate:{}, endDate:{}, start...", clinicId, employeeId, beginDate, endDate);
        if (beginDate != null) {
            beginDate = DateUtils.getStartTime(beginDate);
        }
        if (endDate != null) {
            endDate = DateUtils.getEndTime(endDate);
        }

        EmployeeChargeStatInfo employeeChargeStatInfo = mChargeService.findEmployeeChargeStatInfo(clinicId, employeeId, beginDate, endDate);
        sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "getEmployeeChargeStatInfo, clinicId:{}, employeeId:{}, beginDate:{}, endDate:{}, rsp:{}", clinicId, employeeId, beginDate, endDate, JsonUtils.dump(employeeChargeStatInfo));
        return new CisServiceResponse<>(employeeChargeStatInfo);
    }


    @GetMapping("/todo")
    @LogReqAndRsp
    public CisServiceResponse<ChargeTodo> getChargeTodo(
            @RequestParam(value = "chainId") String chainId,
            @RequestParam(value = "clinicId") String clinicId) {
        ChargeTodo chargeTodo = new ChargeTodo();

        chargeTodo.setCount(chargeStatService.findUnchargedCount(chainId, clinicId));
        return new CisServiceResponse<>(chargeTodo);
    }

    @GetMapping("/cooperation/todo")
    @LogReqAndRsp
    public CisServiceResponse<ChargeTodo> getCooperationOrderChargeTodo(
            @RequestParam(value = "chainId") String chainId,
            @RequestParam(value = "clinicId") String clinicId) {
        ChargeTodo chargeTodo = new ChargeTodo();
        chargeTodo.setCount(chargeCooperationOrderFacade.findUnchargedCount(chainId, clinicId));
        return new CisServiceResponse<>(chargeTodo);
    }

    @PostMapping("/member/recharge")
    @LogReqAndRsp
    public CisServiceResponse<MemberCardRechargeRsp> memberCardRecharge(@RequestBody MemberCardRechargeReq rechargeReq) throws ParamRequiredException, ChargeServiceException {
        ChargeSheet chargeSheet = mChargeService.memberCardRecharge(rechargeReq);

        MemberCardRechargeRsp rechargeRsp = new MemberCardRechargeRsp();
        rechargeRsp.setId(chargeSheet.getId());
        rechargeRsp.setChargeTransactions(new ArrayList<>());
        if (chargeSheet.getChargeTransactions() != null) {
            for (cn.abcyun.cis.charge.model.ChargeTransaction chargeTransaction : chargeSheet.getChargeTransactions()) {
                cn.abcyun.bis.rpc.sdk.cis.model.charge.ChargeTransaction rpcChargeTransaction = new cn.abcyun.bis.rpc.sdk.cis.model.charge.ChargeTransaction();
                BeanUtils.copyProperties(chargeTransaction, rpcChargeTransaction);
                rechargeRsp.getChargeTransactions().add(rpcChargeTransaction);
            }
        }
        rechargeRsp.setSellerId(chargeSheet.getSellerId());
        rechargeRsp.setMemberId(chargeSheet.getMemberId());
        rechargeRsp.setPatientOrderId(chargeSheet.getPatientOrderId());
        rechargeRsp.setStatus(chargeSheet.getStatus());
        return new CisServiceResponse<>(rechargeRsp);
    }

    /**
     * 已作废，迁移到{@link #createChargeSheetForThirdParty(CreateChargeSheetForThirdPartyReq)}
     *
     * @param memberRechargePayReq
     * @return
     * @throws ParamRequiredException
     * @throws ChargeServiceException
     * @throws ProductInfoChangedException
     * @throws ServiceInternalException
     */
    @Deprecated
    @PostMapping("/member/recharge/pay")
    public CisServiceResponse<MemberRechargePayRsp> payForMemberRecharge(@RequestBody MemberRechargePayReq memberRechargePayReq) throws ParamRequiredException, ChargeServiceException, ProductInfoChangedException, ServiceInternalException {

        sLogger.info("payForMemberRecharge req:{}", JsonUtils.dump(memberRechargePayReq));

        ChargeSheet chargeSheet = mChargeService.payForMemberRecharge(memberRechargePayReq);

        MemberRechargePayRsp memberRechargePayRsp = new MemberRechargePayRsp();
        memberRechargePayRsp.setChargeSheetId(chargeSheet.getId());
        memberRechargePayRsp.setPatientOrderId(chargeSheet.getPatientOrderId());
        memberRechargePayRsp.setStatus(chargeSheet.getStatus());

        sLogger.info("payForMemberRecharge rsp:{}", JsonUtils.dump(memberRechargePayRsp));

        return new CisServiceResponse<>(memberRechargePayRsp);
    }

    @PostMapping("/consultation/pay")
    @LogReqAndRsp
    public CisServiceResponse<ChargeForOnlineConsultationRsp> chargeForOnlineConsultation(@RequestBody ChargeForOnlineConsultationReq req) throws ParamRequiredException, ProductInfoChangedException, ServiceInternalException, ChargeServiceException {
        ChargeForOnlineConsultationRsp rsp = mChargeService.chargeForOnlineConsultation(req);
        return new CisServiceResponse<>(rsp);
    }

    @PostMapping("/consultation/refund")
    @LogReqAndRsp
    public CisServiceResponse<RefundForOnlineConsultationRsp> refundForOnlineConsultation(@RequestBody RefundForOnlineConsultationReq req) throws ParamRequiredException, ServiceInternalException, CisCustomException {
        RefundForOnlineConsultationRsp rsp = mChargeService.refundForOnlineConsultation(req);
        return new CisServiceResponse<>(rsp);
    }

    @GetMapping("/{id}/simple")
    @LogReqAndRsp(rpcCallStat = true)
    public CisServiceResponse<SimpleChargeInfo> getSimpleChargeInfo(@PathVariable("id") String id) throws ParamRequiredException {
        if (TextUtils.isEmpty(id)) {
            throw new ParamRequiredException("id");
        }
        SimpleChargeInfo simpleChargeInfo = mChargeService.getSimpleChargeInfo(id);
        if (simpleChargeInfo == null) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "getSimpleChargeInfo NOT FOUND id:{}", id);
            return new CisServiceResponse<>(HttpStatus.OK, null);
        }
        return new CisServiceResponse<>(simpleChargeInfo);
    }


    @PostMapping("/pay/callback")
    public CisServiceResponse<PayCallbackRsp> postPayCallback(@RequestBody PayCallbackReq payCallbackReq) throws ServiceInternalException, CisCustomException, NotFoundException {
        sLogger.info("postPayCallback req:{}", JsonUtils.dump(payCallbackReq));
        PayCallbackContainPayModeReq payCallbackContainPayModeReq = new PayCallbackContainPayModeReq();
        BeanUtils.copyProperties(payCallbackReq, payCallbackContainPayModeReq);

        if (TextUtils.isEmpty(payCallbackReq.getRequestTransactionId())) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "payCallback empty requestTransactionId");
            throw new ParamRequiredException("requestTransactionId");
        }

        PayCallbackRsp payCallbackRsp = chargeThirdPartService.callback(payCallbackContainPayModeReq);

        sLogger.info("postPayCallback rsp:{}", JsonUtils.dump(payCallbackRsp));
        return new CisServiceResponse<>(payCallbackRsp);
    }

    /**
     * 根据chargePayTransactionId解锁
     *
     * @param chargePayTransactionId
     * @param operatorId
     * @param clinicId
     * @return
     */
    @PutMapping(value = "/{chargePayTransactionId}/cancel-by-charge-pay-transaction-id")
    @ApiOperation(value = "根据chargePayTransactionId解锁")
    @LogReqAndRsp
    public AbcServiceResponse<CancelChargeSheetRsp> cancelByChargePayTransactionId(@PathVariable("chargePayTransactionId") String chargePayTransactionId,
                                                                                   @RequestParam("clinicId") String clinicId,
                                                                                   @RequestParam("operatorId") String operatorId) {

        CancelChargeSheetRsp rsp = mChargeService.cancelByChargePayTransactionId(chargePayTransactionId, clinicId, Constants.ChargeSource.WE_APP, operatorId);
        return new AbcServiceResponse<>(rsp);
    }


    @GetMapping("/paymodes")
    @LogReqAndRsp(rpcCallStat = true)
    public CisServiceResponse<PayModeListRsp> getPayModeList(@RequestParam(value = "chainId") String chainId,
                                                             @RequestParam(value = "clinicId", required = false) String clinicId) {
        PayModeListRsp payModeListRsp = mClinicService.getPayModeList(chainId, clinicId);
        return new CisServiceResponse<>(payModeListRsp);
    }

    @GetMapping("/paymode/detail")
    @LogReqAndRsp(rpcCallStat = true)
    public CisServiceResponse<PayModeDetailRsp> getPayModeDetail(@RequestParam(value = "chainId") String chainId,
                                                                 @RequestParam(value = "payModeId") Long payModeId) {
        PayModeDetailRsp payModeDetailRsp = chargeConfigService.getPayModeDetailById(chainId, payModeId);
        return new CisServiceResponse<>(payModeDetailRsp);
    }

    @GetMapping("/calculate/config")
    @LogReqAndRsp
    public CisServiceResponse<ChargeConfigDetailView> getCalculateConfig(@RequestParam(value = "chainId") String chainId) throws ParamRequiredException {

        if (StringUtils.isEmpty(chainId)) {
            throw new ParamRequiredException("chainId");
        }
        ChargeConfigDetailView chargeConfigDetailView = chargeConfigService.getBranchConfigDetail(chainId);
        return new CisServiceResponse<>(chargeConfigDetailView);
    }


    @PostMapping("/dashboard/status")
    @LogReqAndRsp
    public CisServiceResponse<DashboardStatusListRsp> getDashboardStatusList(@RequestBody QueryChargeStatusBatchReq req) {
        List<DashboardStatus> dashboardStatusList = mChargeService.getDashboardStatusList(req.getPatientOrderIds());
        Map<String, DashboardStatus> dashboardStatusMap = dashboardStatusList.stream()
                .filter(dashboardStatus -> !TextUtils.isEmpty(dashboardStatus.getPatientOrderId()))
                .collect(Collectors.toMap(DashboardStatus::getPatientOrderId, Function.identity(), (a, b) -> a));

        DashboardStatusListRsp rsp = new DashboardStatusListRsp();
        rsp.setList(new ArrayList<>());
        if (req.getPatientOrderIds() != null) {
            rsp.setList(req.getPatientOrderIds().stream().map(patientOrderId -> {
                DashboardStatus dashboardStatus = dashboardStatusMap.getOrDefault(patientOrderId, null);
                if (dashboardStatus == null) {
                    dashboardStatus = new DashboardStatus();
                    dashboardStatus.setPatientOrderId(patientOrderId);
                }
                return dashboardStatus;
            }).collect(Collectors.toList()));
        }
        return new CisServiceResponse<>(rsp);
    }


    @GetMapping("/patients/chargedinfo")
    @LogReqAndRsp
    public CisServiceResponse<DoctorPatientDiagnosedChargedInfo> getDoctorPatientDiagnosedChargedInfo(
            @RequestParam("chainId") String chainId,
            @RequestParam("doctorId") String doctorId,
            @RequestParam("patientId") String patientId
    ) {
        DoctorPatientDiagnosedChargedInfo rsp = mChargeService.getDoctorPatientDiagnosedChargedInfo(chainId, doctorId, patientId);
        return new CisServiceResponse<>(rsp);
    }

    @PostMapping("/patients/chargedinfo-by-patientids")
    @LogReqAndRsp
    public AbcServiceResponse<DoctorPatientDiagnosedChargedInfoRsp> getDoctorPatientDiagnosedChargedInfo(@RequestBody @Valid DoctorPatientDiagnosedChargedInfoReq req) {
        DoctorPatientDiagnosedChargedInfoRsp rsp = mChargeService.getDoctorPatientDiagnosedChargedInfoByPatientIds(req.getChainId(), req.getDoctorId(), req.getPatientIds());
        return new AbcServiceResponse<>(rsp);
    }


    @PostMapping("/delivery/address")
    @LogReqAndRsp
    public CisServiceResponse<PatientDeliveryInfoList> getPatientDeliveryInfoList(@RequestBody @Valid PatientDeliveryInfoListReq patientDeliveryInfoListReq) throws ParamRequiredException {


        PatientDeliveryInfoList rsp = mDeliveryService.getPatientDeliveryInfoList(patientDeliveryInfoListReq);
        return new CisServiceResponse<>(rsp);
    }

    @PutMapping("/delivery/{chargeSheetId}")
    @LogReqAndRsp
    public CisServiceResponse<UpdateDeliveryInfoRsp> updateDeliveryByChargeSheetId(@PathVariable("chargeSheetId") String chargeSheetId,
                                                                                   @RequestBody UpdateDeliveryInfoReq updateDeliveryInfoReq) throws ParamRequiredException, NotFoundException {

        ChargeDeliveryInfoView chargeDeliveryInfoView = mDeliveryService.updateDeliveryByChargeSheetId(chargeSheetId, updateDeliveryInfoReq, null, null);

        UpdateDeliveryInfoRsp rsp = new UpdateDeliveryInfoRsp(UpdateDeliveryInfoRsp.Code.SUCCESS, "成功");
        rsp.setChargeDeliveryInfoView(chargeDeliveryInfoView);
        return new CisServiceResponse<>(rsp);
    }

    @PutMapping("/delivery/{chargeSheetId}/chargeForm/{chargeFormId}")
    @LogReqAndRsp
    public AbcServiceResponse<UpdateDeliveryInfoRsp> updateDeliveryByChargeFormId(@PathVariable("chargeSheetId") String chargeSheetId,
                                                                                  @PathVariable("chargeFormId") String chargeFormId,
                                                                                  @RequestBody UpdateDeliveryInfoReq updateDeliveryInfoReq) throws ParamRequiredException, NotFoundException {
        ChargeDeliveryInfoView chargeDeliveryInfoView = mDeliveryService.updateDeliveryByChargeFormId(chargeSheetId, chargeFormId, updateDeliveryInfoReq);
        UpdateDeliveryInfoRsp rsp = new UpdateDeliveryInfoRsp(UpdateDeliveryInfoRsp.Code.SUCCESS, "成功");
        rsp.setChargeDeliveryInfoView(chargeDeliveryInfoView);
        return new AbcServiceResponse<>(rsp);
    }


    @PostMapping("/orders")
    @LogReqAndRsp(rpcCallStat = true)
    public CisServiceResponse<ChargeOrderViewRsp> getChargeOrderListByPatientIds(
            @RequestBody ChargeOrderListReq chargeOrderListReq
    ) throws ParamRequiredException {

        ChargeOrderViewRsp rsp = chargeSheetService.getChargeOrderListByPatientId(chargeOrderListReq);
        return new CisServiceResponse<>(rsp);
    }

    @PostMapping("/orders/count")
    @LogReqAndRsp(rpcCallStat = true)
    public CisServiceResponse<ChargeOrderCountViewRsp> getChargeOrderCountByPatientId(
            @RequestBody ChargeOrderCountViewReq req
    ) throws ParamRequiredException {

        ChargeOrderCountViewRsp rsp = chargeSheetService.getChargeOrderCountByPatientId(req);
        return new CisServiceResponse<>(rsp);
    }

    /**
     * 微诊所的锁单并支付
     *
     * @param chargeSheetId
     * @param req
     * @return
     * @throws ServiceInternalException
     * @throws ChargeServiceException
     * @throws ParamRequiredException
     * @throws ProductInfoChangedException
     */
    @PostMapping("/{chargeSheetId}/thirdpartpay")
    @LogReqAndRsp
    public CisServiceResponse<PayChargeSheetRsp> thirdPartPayForChargeSheet(@PathVariable("chargeSheetId") String chargeSheetId,
                                                                            @RequestBody ThirdPartPayForChargeSheetReq req) throws ServiceInternalException, ChargeServiceException, ParamRequiredException, ProductInfoChangedException, NotFoundException {

        mChargeService.buildThirdPartPayForChargeSheetPayMode(req);
        PayChargeSheetRsp rsp = mChargeService.thirdPartPayForChargeSheet(chargeSheetId, req, Constants.ChargeSource.WE_CLINIC);
        return new CisServiceResponse<>(rsp);
    }

    @GetMapping("/sendorderconfig")
    @LogReqAndRsp
    @Deprecated
    public CisServiceResponse<SendOrderConfigResult> getSendOrderConfig(@RequestParam("chainId") String chainId) throws ServiceInternalException, ChargeServiceException, ParamRequiredException, ProductInfoChangedException, NotFoundException {

        if (StringUtils.isEmpty(chainId)) {

            throw new ParamRequiredException("chainId");
        }
        SendOrderConfigResult rsp = chargeConfigService.getSendOrderConfig(chainId);

        return new CisServiceResponse<>(rsp);
    }

    @PostMapping("/{chargeSheetId}/replenish")
    @LogReqAndRsp
    public CisServiceResponse<ChargeSheetReplenishRsp> replenishForChargeSheet(@PathVariable("chargeSheetId") String chargeSheetId,
                                                                               @RequestBody @Valid ChargeSheetReplenishReq chargeSheetReplenishReq
    ) throws ParamRequiredException, NotFoundException, ChargeServiceException, ServiceInternalException {

        if (StringUtils.isEmpty(chargeSheetId)) {
            throw new ParamRequiredException("chargeSheetId");
        }

        if (StringUtils.isEmpty(chargeSheetReplenishReq.getPatientId())) {
            throw new ParamRequiredException("patientId");
        }

        if (chargeSheetReplenishReq.getDeliveryType() == ChargeSheet.DeliveryType.DELIVERY_TO_HOME) {
            if (chargeSheetReplenishReq.getDeliveryInfo() == null) {
                throw new ParamRequiredException("deliveryInfo");
            }

            if (StringUtils.isEmpty(chargeSheetReplenishReq.getDeliveryInfo().getAddressCityName())) {
                throw new ParamRequiredException("deliveryInfo.addressCityName");
            }

            if (StringUtils.isEmpty(chargeSheetReplenishReq.getDeliveryInfo().getAddressProvinceName())) {
                throw new ParamRequiredException("deliveryInfo.addressProvinceName");
            }

            if (StringUtils.isEmpty(chargeSheetReplenishReq.getDeliveryInfo().getAddressDistrictName())) {
                throw new ParamRequiredException("deliveryInfo.addressDistrictName");
            }

        }

        ChargeSheet chargeSheet = mChargeService.replenishForChargeSheet(chargeSheetId, chargeSheetReplenishReq);

        ChargeSheetReplenishRsp rsp = new ChargeSheetReplenishRsp();
        BeanUtils.copyProperties(chargeSheet, rsp);
        rsp.setChargeSheetId(chargeSheet.getId());

        return new CisServiceResponse<>(rsp);
    }


    @GetMapping("/process/usages/available")
    @LogReqAndRsp
    public CisServiceResponse<List<UsageTypeNode>> getAvailableUsages(@RequestParam("clinicId") String clinicId,
                                                                      @RequestParam(value = "chainId", required = false) String chainId) throws ParamRequiredException, NotFoundException {


        if (StringUtils.isEmpty(clinicId)) {
            throw new ParamRequiredException("clinicId");
        }


        List<UsageTypeNode> rsp = chargeRuleProcessUsageTypeService.getAvailableUsages(clinicId, chainId);

        return new CisServiceResponse<>(rsp);
    }


    //    @PostMapping("/rule/expresses")
    //    @LogReqAndRsp
    //    public CisServiceResponse<ChargeRuleProcessResult> getChargeRuleProcesses(@RequestBody ChargeRuleProcessReq req) throws ParamRequiredException {
    //
    //        if (StringUtils.isEmpty(req.getChainId())){
    //            throw new ParamRequiredException("chainId");
    //        }
    //
    //        if (StringUtils.isEmpty(req.getClinicId())){
    //            throw new ParamRequiredException("clinicId");
    //        }
    //
    //        if (CollectionUtils.isEmpty(req.getUsages())) {
    //            throw new ParamRequiredException("usage");
    //        }
    //
    //        ChargeRuleProcessResult result = chargeRuleService.findChargeRuleProcesses(req.getChainId(), req.getClinicId(), req.getUsages());
    //
    //        return new CisServiceResponse<>(result);
    //    }

    @PostMapping("/history-for-mc")
    @LogReqAndRsp
    public CisServiceResponse<WeClinicChargeHistoryListRsp> getChargeHistoryForWeClinic(@RequestBody @Valid WeClinicChargeHistoryListReq reqBody) {

        if (reqBody.getOffset() == null || reqBody.getOffset() < 0) {
            reqBody.setOffset(0);
        }

        if (reqBody.getLimit() == null || reqBody.getLimit() < 0) {
            reqBody.setLimit(10);
        }

        WeClinicChargeHistoryListRsp rsp = weClinicChargeService.getChargeHistoryForWeClinic(reqBody);

        return new CisServiceResponse<>(rsp);
    }

    @PostMapping("/create-by-clone")
    @LogReqAndRsp
    public CisServiceResponse<WeClinicCreateChargeSheetByCloneRsp> createChargeSheetByClone(@RequestBody @Valid WeClinicCreateChargeSheetByCloneReq reqBody) throws NotFoundException, ChargeServiceException {
        WeClinicCreateChargeSheetByCloneRsp rsp = weClinicChargeService.createChargeSheetByClone(reqBody, mChargeService, Constants.ANONYMOUS_PATIENT_ID);
        return new CisServiceResponse<>(rsp);
    }

    @PostMapping("/create-by-photo")
    @LogReqAndRsp
    public CisServiceResponse<WeClinicCreateChargeSheetByPhotoRsp> createChargeSheetByPhoto(@RequestBody @Valid WeClinicCreateChargeSheetByPhotoReq reqBody) throws NotFoundException, ChargeServiceException {
        WeClinicCreateChargeSheetByPhotoRsp rsp = weClinicChargeService.createChargeSheetByPhoto(reqBody, mChargeService, Constants.ANONYMOUS_PATIENT_ID);
        return new CisServiceResponse<>(rsp);
    }

    /**
     * 根据请求参数查询执行记录集合
     *
     * @param req 请求参数
     * @return 查询结果
     */
    @PostMapping("/nurse/execute-actions")
    @LogReqAndRsp(rpcCallStat = true)
    public CisServiceResponse<NurseExecuteActionListRsp> listExecuteRecordItemsByPatientAndCreated(@RequestBody NurseExecuteActionListReq req) {
        List<PatientChargeExecuteRecordItemView> list = chargeExecuteRecordService.listExecuteRecordItemsByPatientAndCreated(
                req.getClinicId(),
                req.getPatientIds(),
                req.getStartCreated().toInstant(),
                req.getEndCreated().toInstant());

        return new CisServiceResponse<>(new NurseExecuteActionListRsp()
                .setExecuteActionList(
                        list.stream()
                                .map(patientChargeExecuteRecordItemView -> new NurseExecuteAction()
                                        .setId(patientChargeExecuteRecordItemView.getId())
                                        .setPatientId(patientChargeExecuteRecordItemView.getPatientId())
                                        .setProductId(patientChargeExecuteRecordItemView.getProductId())
                                        .setProductName(patientChargeExecuteRecordItemView.getProductName())
                                        .setExecuteCount(patientChargeExecuteRecordItemView.getExecuteCount())
                                        .setExecuteDate(patientChargeExecuteRecordItemView.getExecuteDate())
                                        .setExecuteUnit(patientChargeExecuteRecordItemView.getExecuteUnit())
                                )
                                .collect(Collectors.toList())
                ));
    }

    /**
     * 根据时间查询
     *
     * @param clinicId     诊所id
     * @param beginDateStr 开始时间 yyyy-MM-dd HH:mm:ss
     * @param endDateStr   结束时间 yyyy-MM-dd HH:mm:ss
     * @return
     */
    @GetMapping("/query-paid-by-date")
    @LogReqAndRsp(rpcCallStat = true)
    public AbcServiceResponse<ChargeSheetSimpleListRsp> queryPaidChargeSheetsByDate(@RequestParam(value = "clinicId") String clinicId, @RequestParam(value = "beginDate") String beginDateStr, @RequestParam(value = "endDate") String endDateStr) {
        Date beginDate = cn.abcyun.cis.charge.util.DateUtils.parseToDate(beginDateStr, "yyyy-MM-dd HH:mm:ss");
        Date endDate = cn.abcyun.cis.charge.util.DateUtils.parseToDate(endDateStr, "yyyy-MM-dd HH:mm:ss");
        if (beginDate == null || endDate == null) {
            throw new ParamNotValidException("beginDate or endDate");
        }
        return new AbcServiceResponse<>(chargeSheetService.queryPaidChargeSheetsByDate(clinicId, beginDate.toInstant(), endDate.toInstant()));
    }

    /**
     * 查询第三方支付单的支付状态
     *
     * @param chargePayTransactionId
     * @param clinicId
     * @return
     * @throws ServiceInternalException
     */
    @GetMapping("/paystatus/{clinicId}/{chargePayTransactionId}")
    @LogReqAndRsp(rpcCallStat = true)
    public CisServiceResponse<PayStatusRsp> getPayStatusByChargePayTransactionId(@PathVariable("clinicId") String clinicId, @PathVariable("chargePayTransactionId") String chargePayTransactionId) throws ServiceInternalException {
        PayStatusRsp rsp = mChargePayService.getPayStatusByChargeTransactionId(chargePayTransactionId, clinicId);
        return new CisServiceResponse<>(rsp);
    }

    /**
     * 家庭医生生成收费单
     *
     * @param req
     * @return
     */
    @PostMapping("/crm/family-doctor")
    @LogReqAndRsp
    public AbcServiceResponse<CreateChargeSheetForFamilyDoctorRsp> createChargeSheetForFamilyDoctor(@RequestBody @Valid CreateChargeSheetForFamilyDoctorReq req) {
        CreateChargeSheetForFamilyDoctorRsp rsp = mChargeService.createChargeSheetForFamilyDoctor(req);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 家庭医生解约删除收费单
     *
     * @return
     */
    @DeleteMapping("/crm/family-doctor/{chargeSheetId}")
    @LogReqAndRsp
    public AbcServiceResponse<DeleteChargeSheetForFamilyDoctorCancelRsp> deleteChargeSheetForFamilyDoctorCancel(@PathVariable(value = "chargeSheetId") String chargeSheetId, @RequestParam("operatorId") String operatorId) {

        if (org.apache.commons.lang3.StringUtils.isEmpty(chargeSheetId)) {
            throw new ParamRequiredException("chargeSheetId");
        }

        if (org.apache.commons.lang3.StringUtils.isEmpty(operatorId)) {
            throw new ParamRequiredException("operatorId");
        }

        DeleteChargeSheetForFamilyDoctorCancelRsp rsp = mChargeSheetService.deleteChargeSheetForFamilyDoctorCancel(chargeSheetId, operatorId);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 查询简单的收费单信息
     *
     * @return
     */
    @GetMapping("/simple/{chargeSheetId}")
    @LogReqAndRsp(rpcCallStat = true)
    public AbcServiceResponse<cn.abcyun.bis.rpc.sdk.cis.model.charge.ChargeSheetSimpleListRsp.ChargeSheetSimple> findChargeSheetSimpleById(@PathVariable("chargeSheetId") String chargeSheetId) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(chargeSheetId)) {
            throw new NotFoundException();
        }

        cn.abcyun.bis.rpc.sdk.cis.model.charge.ChargeSheetSimpleListRsp.ChargeSheetSimple chargeSheetSimple = mChargeSheetService.findChargeSheetSimpleById(chargeSheetId);
        return new AbcServiceResponse<>(chargeSheetSimple);
    }

    @PostMapping("/list-simple-charge-sheets")
    @LogReqAndRsp(rpcCallStat = true)
    public AbcServiceResponse<ChargeSheetSimpleListRsp> listSimpleChargeSheets(@RequestBody @Valid QuerySimpleChargeSheetsReq req) {

        ChargeSheetSimpleListRsp rsp = new ChargeSheetSimpleListRsp();
        List<ChargeSheetSimpleListRsp.ChargeSheetSimple> chargeSheetSimples = mChargeSheetService.listSimpleChargeSheets(req.getChainId(), req.getChargeSheetIds());
        rsp.setChargeSheets(chargeSheetSimples);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 业务生成待支付的收费单
     *
     * @return
     */
    @PostMapping("/create-for-third-party")
    @LogReqAndRsp
    public AbcServiceResponse<CreateChargeSheetForThirdPartyRsp> createChargeSheetForThirdParty(@RequestBody @Valid CreateChargeSheetForThirdPartyReq req) {

        CreateChargeSheetForThirdPartyRsp rsp = mChargeSheetService.createChargeSheetForThirdParty(req);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 业务生成已支付的收费单(eg:营销卡开卡、退开卡费记账，营销卡充值、卡退费记账)
     * 退费时调用
     *
     * @return
     */
    @Deprecated
    @PostMapping("/create-and-paid")
    @LogReqAndRsp
    public AbcServiceResponse<CreatePaidChargeSheetRsp> createPaidChargeSheet(@RequestBody @Valid CreatePaidChargeSheetReq req) {
        CreatePaidChargeSheetRsp rsp = mChargeSheetService.createPaidChargeSheet(req);
        return new AbcServiceResponse<>(rsp);
    }


    /**
     * 营销卡项需求刷数据rpc接口，只用于需求上线时jenkins调用刷数据，没有其他用途
     *
     * @return
     */
    @PostMapping("/refresh-received-price")
    @LogReqAndRsp
    public AbcServiceResponse<RefreshDataCommonRsp> refreshReceivedPrice(@RequestBody @Valid RefreshReceivedPriceReq req) {
        req.checkParam();
        RefreshDataCommonRsp rsp = chargeRefreshDataService.refreshReceivedPrice(req);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 清除收费单上社保的异常状态
     * 已作废
     *
     * @return
     */
    @PutMapping("/{chargeSheetId}/clear-shebao-error")
    @LogReqAndRsp
    @Deprecated
    public AbcServiceResponse<BaseSuccessRsp> clearChargeSheetShebaoError(@PathVariable("chargeSheetId") String chargeSheetId, @RequestParam("operatorId") String operatorId) {
        BaseSuccessRsp rsp = mChargeSheetService.clearChargeSheetShebaoError(chargeSheetId, operatorId);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 标记收费单上社保的异常状态
     * 已作废
     *
     * @return
     */
    @PutMapping("/{chargeSheetId}/mark-shebao-error")
    @LogReqAndRsp
    @Deprecated
    public AbcServiceResponse<BaseSuccessRsp> markChargeSheetShebaoError(@PathVariable("chargeSheetId") String chargeSheetId, @RequestParam("operatorId") String operatorId) {
        BaseSuccessRsp rsp = mChargeSheetService.markChargeSheetShebaoError(chargeSheetId, operatorId);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 清除收费单上社保的异常状态
     *
     * @return
     */
    @Deprecated
    @PostMapping("/charge-pay-transaction/clear-shebao-error")
    @LogReqAndRsp
    public AbcServiceResponse<BaseSuccessRsp> clearChargePayTransactionShebaoError(@RequestBody @Valid ShebaoErrorUpdateReq req) {
        BaseSuccessRsp rsp = mChargeSheetService.clearChargePayTransactionShebaoError(req);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 批量清除收费单上社保的异常状态
     *
     * @return
     */
    @PostMapping("/charge-pay-transaction/batch-clear-shebao-error")
    @LogReqAndRsp
    public AbcServiceResponse<BaseSuccessRsp> clearChargePayTransactionShebaoError(@RequestBody @Valid cn.abcyun.cis.charge.api.model.BatchShebaoErrorUpdateReq req) {
        BaseSuccessRsp rsp = mChargeSheetService.batchClearChargePayTransactionShebaoError(req);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 标记收费单上社保的异常状态
     *
     * @return
     */
    @PostMapping("/charge-pay-transaction/mark-shebao-error")
    @LogReqAndRsp
    public AbcServiceResponse<BaseSuccessRsp> markChargePayTransactionShebaoError(@RequestBody @Valid ShebaoErrorUpdateReq req) {
        BaseSuccessRsp rsp = mChargeSheetService.markChargePayTransactionShebaoError(req);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 通过patientOrderIds查询收费单是否有异常
     *
     * @return
     */
    @PostMapping("/registration/list-charge-sheet-exception-status")
    @LogReqAndRsp(rpcCallStat = true)
    public AbcServiceResponse<QueryChargeSheetExceptionStatusListRsp> listChargeSheetExceptionStatus(@RequestBody QueryChargeSheetExceptionStatusListReq req) {
        QueryChargeSheetExceptionStatusListRsp rsp = mChargeService.listChargeSheetExceptionStatus(req);
        return new AbcServiceResponse<>(rsp);
    }


    /**
     * 微诊所锁单支付(公众号-小程序公用)
     *
     * @param chargeSheetId
     * @param req
     * @return
     * @throws ServiceInternalException
     * @throws ChargeServiceException
     * @throws ParamRequiredException
     * @throws ProductInfoChangedException
     */
    @PutMapping("/{chargeSheetId}/paid")
    @LogReqAndRsp
    public CisServiceResponse<PayChargeSheetRsp> putChargeSheetPaidV2(@PathVariable("chargeSheetId") String chargeSheetId,
                                                                      @RequestBody ThirdPartPayForChargeSheetReq req) throws ServiceInternalException, ChargeServiceException, ParamRequiredException, ProductInfoChangedException {

        if (org.apache.commons.lang3.StringUtils.isEmpty(chargeSheetId)) {
            throw new ParamRequiredException("chargeSheetId");
        }

        String mcChargeSheetPaidRedisKey = RedisUtils.generateMcChargeSheetPaidRedisKey(chargeSheetId);

        Long leftLiveTime = RedisUtils.getLeftLiveTime(mcChargeSheetPaidRedisKey);

        if (Objects.nonNull(leftLiveTime) && leftLiveTime > 0) {
            sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "leftLiveTime大于0");
            throw new ChargeServiceException(ChargeServiceError.WECLINIC_CHARGE_SHEET_PAID_FREQUENT);
        }

        PayChargeSheetRsp rsp = null;
        //限制改接口只能10秒钟内调用一次，调用成功了才算一次
        try {
            String redisKey = String.format("charge.sheet.paid:%s", chargeSheetId);
            RLock lock = redissonClient.getLock(redisKey);
            // 尝试加锁，最多等待30秒，上锁以后15秒自动解锁
            boolean res = lock.tryLock(3, 15, TimeUnit.SECONDS);
            if (res) {
                sLogger.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "acquired locker:{}, timestamp:{}, threadId:{}", redisKey, Instant.now(), Thread.currentThread().getId());
                try {
                    mChargeService.buildThirdPartPayForChargeSheetPayMode(req);
                    rsp = mChargeService.thirdPartPayForChargeSheet(chargeSheetId, req, req.getSource());
                    RedisUtils.set(mcChargeSheetPaidRedisKey, "", 10, TimeUnit.SECONDS);
                } finally {
                    lock.unlock();
                }
            } else {
                sLogger.error("获取的redis锁失败");
                throw new ServiceInternalException();
            }
        } catch (InterruptedException e) {
            sLogger.error("thirdPartyCallback acquire lock failed error:", e);
            throw new ServiceInternalException();
        }

        return new CisServiceResponse<>(rsp);
    }

    /**
     * 仅支付，不能修改收费项
     *
     * @param chargeSheetId
     * @param payChargeSheetReq
     * @return
     */
    @PutMapping("/{chargeSheetId}/only-pay")
    @LogReqAndRsp
    public AbcServiceResponse<PayChargeSheetRsp> putChargeSheetOnlyPaid(@PathVariable("chargeSheetId") String chargeSheetId,
                                                                        @RequestBody @Valid RpcOnlyPayChargeSheetReq payChargeSheetReq) {
        PayChargeSheetInfo payChargeSheetInfo = new PayChargeSheetInfo();
        BeanUtils.copyProperties(payChargeSheetReq, payChargeSheetInfo);
        payChargeSheetInfo.setId(chargeSheetId);

        PayChargeSheetResult result = mChargeService.putChargeSheetOnlyPaid(payChargeSheetInfo);
        PayChargeSheetRsp rsp = new PayChargeSheetRsp();
        BeanUtils.copyProperties(result, rsp);
        StatusNameTranslator.translate(rsp);

        return new AbcServiceResponse<>(rsp);
    }

    @PutMapping("/charge-pay-transaction/{chargePayTransactionId}/paid")
    @LogReqAndRsp
    @ApiOperation(value = "根据chargePayTransactionId支付收费单", produces = "application/json")
    public AbcServiceResponse<WeChatMobileShebaoPayRsp> putChargeSheetPaidByChargePayTransactionId(@PathVariable("chargePayTransactionId") String chargePayTransactionId,
                                                                                                   @RequestBody PayChargeSheetByChargePayTransactionIdReq req) {

        return new AbcServiceResponse<>(mChargeService.putChargeSheetPaidByChargePayTransactionId(chargePayTransactionId, req));
    }

    @PostMapping("/mobile-shebao/{clinicId}/{chargeSheetId}/pay-pre-check")
    @LogReqAndRsp
    @ApiOperation(value = "校验收费单是否满足医保移动支付的条件", produces = "application/json")
    public AbcServiceResponse<MobilePayPreCheckRspBody> preCheckMobileShebaoCanPay(@PathVariable("clinicId") String clinicId, @PathVariable("chargeSheetId") String chargeSheetId) {

        return new AbcServiceResponse<>(mChargeService.preCheckMobileShebaoCanPay(chargeSheetId, clinicId));
    }

    /**
     * 批量查询就诊单的告知书
     *
     * @param req
     * @return
     */
    @PostMapping("/patientorders/list-patient-order-notifications")
    @LogReqAndRsp(rpcCallStat = true)
    public AbcServiceResponse<QueryPatientOrderNotificationsRsp> listPatientOrderNotifications(@RequestBody @Valid QueryPatientOrderNotificationsReq req) {

        if (req.getPatientOrderIds().size() > 100) {
            throw new ParamNotValidException("patientOrderIds最多100个");
        }

        QueryPatientOrderNotificationsRsp rsp = chargePatientOrderNotificationService.listPatientOrderNotifications(req.getChainId(), req.getClinicId(), req.getPatientOrderIds());
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 议价算费摊费
     *
     * @param calculateSheetReq
     * @return
     */
    @PostMapping("/outpatient/calculate")
    @ApiOperation(value = "算费", produces = "application/json")
    @LogReqAndRsp(rpcCallStat = true)
    public AbcServiceResponse<BasicCalculateSheetRsp> calculateSheet(@RequestBody @Valid BasicCalculateSheetReq calculateSheetReq) {


        BasicCalculateSheetRsp rsp = chargeCalculateService.calculateSheet(calculateSheetReq);

        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 基础的议价算费摊费工具
     *
     * @param calculateSheetReq
     * @return
     */
    @PostMapping("/calculate-tool/batch-calculate")
    @ApiOperation(value = "算费摊费工具", produces = "application/json")
    @LogReqAndRsp(rpcCallStat = true)
    public AbcServiceResponse<BatchBasicCalculateSheetRsp> batchCalculateSheet(@RequestBody @Valid BatchBasicCalculateSheetReq calculateSheetReq) {


        BatchBasicCalculateSheetRsp rsp = chargeCalculateService.batchCalculateSheet(calculateSheetReq);

        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 门诊单算费
     *
     * @param outpatientSheetCalculateReq
     * @return
     */
    @PostMapping("/outpatient-sheet/calculate")
    @ApiOperation(value = "算费", produces = "application/json")
    @LogReqAndRsp(rpcCallStat = true)
    @Deprecated
    public AbcServiceResponse<OutpatientSheetCalculateRsp> calculateOutpatientSheet(
            @RequestBody @Valid OutpatientSheetCalculateReq outpatientSheetCalculateReq) {

        OutpatientSheetCalculateRsp rsp = chargeCalculateService.calculateOutpatientSheet(outpatientSheetCalculateReq);

        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 门诊单详情算费
     *
     * @param req
     * @return
     */
    @PostMapping("/outpatient-sheet/detail-calculate")
    @ApiOperation(value = "门诊单详情算费", produces = "application/json")
    @LogReqAndRsp(rpcCallStat = true)
    public AbcServiceResponse<OutpatientSheetCalculateRsp> calculateOutpatientSheetDetail(
            @RequestBody @Valid OutpatientSheetDetailCalculateReq req) {

        OutpatientSheetCalculateRsp rsp = chargeCalculateService.calculateOutpatientSheetDetail(req);

        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 咨询计划单详情算费
     *
     * @param req
     * @return
     */
    @PostMapping("/medical-plan-sheet/detail-calculate")
    @ApiOperation(value = "咨询计划单详情算费", produces = "application/json")
    @LogReqAndRsp(rpcCallStat = true)
    public AbcServiceResponse<MedicalPlanSheetDetailCalculateRsp> calculateMedicalPlanSheetDetail(
            @RequestBody @Valid MedicalPlanSheetDetailCalculateReq req) {

        MedicalPlanSheetDetailCalculateRsp rsp = chargeCalculateService.calculateMedicalPlanSheetDetail(req);

        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 门诊单转收费单详情
     *
     * @param outpatientSheet 门诊单对象
     * @return chargeSheetView
     */
    @PostMapping("/converted/outpatient-sheet-to-charge-sheet-view")
    @LogReqAndRsp(rpcCallStat = true)
    public AbcServiceResponse<ChargeSheetView> outpatientSheetConvertToChargeSheetView(@RequestBody OutpatientSheet outpatientSheet) {
        ChargeSheetView chargeSheetView = chargeCalculateService.outpatientSheetConvertToChargeSheetView(outpatientSheet);
        return new AbcServiceResponse<>(chargeSheetView);
    }


    @PostMapping("/available-delivery-company")
    @ApiOperation(value = "根据地址信息查询可用的快递公司列表", produces = "application/json")
    @LogReqAndRsp
    public AbcServiceResponse<AvailableDeliveryPayTypeRsp> listAvailableDeliveryCompany(@RequestBody @Valid AvailableDeliveryPayTypeReq req) {

        req.checkParam();
        AvailableDeliveryPayTypeRsp rsp = chargeRuleExpressDeliveryService.listAvailableDeliveryPayTypeByAddress(req);

        return new AbcServiceResponse<>(rsp);
    }

    @PostMapping("/examinations/examination-inspection")
    @LogReqAndRsp
    @ApiOperation(value = "检查站开单", produces = "application/json")
    public AbcServiceResponse<CreateChargeSheetForExaminationInspectionRsp> createChargeSheetForExaminationInspection(@RequestBody @Valid CreateChargeSheetForExaminationInspectionReq req) {


        CreateChargeSheetForExaminationInspectionRsp rsp = chargeExaminationService.createChargeSheetForExaminationInspection(req);

        return new AbcServiceResponse<>(rsp);
    }

    @PutMapping("/delivery/trace/{deliveryOrderNo}")
    @LogReqAndRsp
    @ApiOperation(value = "更新快递物流信息", produces = "application/json")
    public AbcServiceResponse<BaseSuccessRsp> updateDeliveryTraceData(@PathVariable("deliveryOrderNo") String deliveryOrderNo,
                                                                      @RequestBody @Valid UpdateDeliveryTraceDataReq req) {


        BaseSuccessRsp rsp = chargeDeliveryTraceService.updateDeliveryTraceData(req, deliveryOrderNo);

        return new AbcServiceResponse<>(rsp);
    }

    @GetMapping("/process/usages")
    @LogReqAndRsp
    @ApiOperation(value = "查询所有的加工方式列表")
    public AbcServiceResponse<AbcListPage<UsageTypeNode>> getAllUsages() {
        AbcListPage<UsageTypeNode> rsp = new AbcListPage<>();
        List<UsageTypeNode> allUsages = chargeRuleProcessUsageTypeService.getAllUsages();
        rsp.setRows(allUsages);
        return new AbcServiceResponse<>(rsp);
    }

    @PostMapping("/import/owe-sheet")
    @ApiOperation(value = "导入欠费收费单")
    @LogReqAndRsp(logToIndex = true)
    public AbcServiceResponse<CreateChargeSheetRsp> postImportChargeSheetWithOweSheet(@RequestBody ImportChargeSheetReq importChargeSheetReq) {
        CreateChargeSheetRsp createChargeSheetRsp = chargeImportService.importChargeSheetWithOweSheet(importChargeSheetReq, importChargeSheetReq.getChainId(), importChargeSheetReq.getClinicId(), importChargeSheetReq.getEmployeeId());
        if (createChargeSheetRsp == null) {
            throw new NotFoundException();
        }
        return new AbcServiceResponse<>(createChargeSheetRsp);
    }

    @PostMapping("/import/paid-sheet")
    @ApiOperation(value = "导入欠费收费单")
    @LogReqAndRsp(logToIndex = true)
    public AbcServiceResponse<CreateChargeSheetRsp> postImportPaidChargeSheet(@RequestBody ImportChargeSheetReq importChargeSheetReq) {
        CreateChargeSheetRsp createChargeSheetRsp = chargeImportService.postImportPaidChargeSheet(importChargeSheetReq, importChargeSheetReq.getChainId(), importChargeSheetReq.getClinicId(), importChargeSheetReq.getEmployeeId());
        if (createChargeSheetRsp == null) {
            throw new NotFoundException();
        }
        return new AbcServiceResponse<>(createChargeSheetRsp);
    }

    @GetMapping("/list-charge-sheets")
    @ApiOperation(value = "查询指定日期的收费单列表")
    @LogReqAndRsp(rpcCallStat = true)
    public AbcServiceResponse<AbcListPage<ChargeSheetRpcAbstract>> listChargeSheetAbstractListByDate(@RequestParam(value = "date")
                                                                                                     @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
                                                                                                     @RequestParam(value = "chainId") String chainId,
                                                                                                     @RequestParam(value = "clinicId") String clinicId) {


        Date beginDate = DateUtils.getStartTime(date);
        Date endDate = DateUtils.getEndTime(date);

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.MONTH, 1);
        Date oneMonthLater = calendar.getTime();

        if (DateUtils.getStartTime(date).after(DateUtils.getStartTime(oneMonthLater))) {
            AbcListPage<ChargeSheetRpcAbstract> emptyRsp = new AbcListPage<>();
            emptyRsp.setRows(Lists.newArrayList());
            return new AbcServiceResponse<>(emptyRsp);
        }

        AbcListPage<ChargeSheetRpcAbstract> rsp = mChargeService.listChargeSheetAbstractListByDate(chainId, clinicId, beginDate.toInstant(), endDate.toInstant());
        return new AbcServiceResponse<>(rsp);
    }

    @GetMapping("/config/pay-mode/{chainId}")
    @ApiOperation(value = "查询当前门店的支付方式名称")
    @LogReqAndRsp(logToIndex = true, rpcCallStat = true)
    public AbcServiceResponse<AbcListPage<ChargePayModeConfigSimple>> getChargePayModeConfigSimpleByChainId(@PathVariable("chainId") String chainId) {
        List<ChargePayModeConfigSimple> chargePayModeConfigSimples = chargeConfigService.getChargePayModeConfigSimpleListByChainId(chainId);
        AbcListPage<ChargePayModeConfigSimple> rsp = new AbcListPage<>();
        rsp.setRows(chargePayModeConfigSimples);

        return new AbcServiceResponse<>(rsp);
    }

    @PostMapping("/list-charge-form-items")
    @ApiOperation(value = "根据chargeFormItemIds查询收费项明细")
    @LogReqAndRsp(rpcCallStat = true)
    public AbcServiceResponse<AbcListPage<ChargeFormItem>> listChargeFormItemsByIds(@RequestBody @Valid RpcQueryChargeFormItemsReq req) {
        List<ChargeFormItem> chargeFormItems = chargeSheetService.listChargeFormItemsByIds(req.getChainId(), req.getClinicId(), req.getChargeFormItemIds());
        AbcListPage<ChargeFormItem> rsp = new AbcListPage<>();
        rsp.setRows(chargeFormItems);
        return new AbcServiceResponse<>(rsp);
    }


    @PostMapping("/latest-uncharged-sheet")
    @ApiOperation(value = "查询指定日期的收费单列表")
    @LogReqAndRsp(rpcCallStat = true)
    public AbcServiceResponse<ChargeSheetSimpleListRsp.ChargeSheetSimple> findLatestUnchargedChargeSheetByPatientIds(@RequestBody @Valid QueryUnchargedChargeSheetsByPatientIdsReq req) {
        ChargeSheetSimpleListRsp.ChargeSheetSimple chargeSheetSimple = mChargeService.findLatestUnchargedChargeSheetByPatientIds(req);
        return new AbcServiceResponse<>(chargeSheetSimple);
    }


    @PostMapping("/push")
    @ApiOperation(value = "推送收费单")
    @LogReqAndRsp
    public AbcServiceResponse<BaseSuccessRsp> pushChargeSheet(@RequestBody @Valid RpcPushChargeSheetReq req) {
        mChargeService.onlyPushChargeSheet(req);
        return new AbcServiceResponse<>(BaseSuccessRsp.success());
    }

    @PostMapping("/process/bag-count/calculate")
    @LogReqAndRsp
    public AbcServiceResponse<ProcessorUtils.CalProcessorBagCountResult> calProcessorBagCount(@RequestBody CalculateProcessorBagCountReq req) {

        ProcessorUtils.CalProcessorBagCountResult result = ProcessorUtils.calProcessorBagCount(req.getDailyDosage(), req.getFreq(), req.getUsageLevel(), req.getDoseCount(), req.getBagUnitCount(), req.getPharmacyType(), req.getType());

        return new AbcServiceResponse<>(result);
    }

    @PutMapping("/{chargeSheetId}/simple-paid")
    @LogReqAndRsp
    @Deprecated
    public AbcServiceResponse<PayChargeSheetResult> chargeSheetSimplePaid(@RequestBody OpenApiPaidChargeSheetReq req,
                                                                          @PathVariable("chargeSheetId") String chargeSheetId) {


        PayChargeSheetResult rsp = mChargeService.chargeSheetSimplePaid(chargeSheetId, req, req.getOperatorId());
        return new AbcServiceResponse<>(rsp);
    }

    @PutMapping("/{chargeSheetId}/open-paid")
    @LogReqAndRsp
    public AbcServiceResponse<PayChargeSheetResult> chargeSheetOpenPaid(@RequestBody OpenApiPaidChargeSheetReq req,
                                                                        @PathVariable("chargeSheetId") String chargeSheetId) {


        PayChargeSheetResult rsp = mChargeService.chargeSheetOpenPaid(chargeSheetId, req, req.getOperatorId());
        return new AbcServiceResponse<>(rsp);
    }

    @PutMapping("/{chargeSheetId}/simple-refund")
    @LogReqAndRsp
    public AbcServiceResponse<RefundChargeSheetResult> chargeSheetSimpleRefund(
            @RequestBody OpenApiRefundChargeSheetReq req,
            @PathVariable("chargeSheetId") String chargeSheetId) {


        RefundChargeSheetResult result = mChargeService.chargeSheetSimpleRefund(chargeSheetId, req);
        return new AbcServiceResponse<>(result);
    }


    @PutMapping("/{chargeSheetId}/delivery")
    @LogReqAndRsp
    public AbcServiceResponse<ChargeSheetView> updateDelivery(@RequestBody OpenApiDeliveryReq req,
                                                              @PathVariable("chargeSheetId") String chargeSheetId) {


        ChargeSheetView rsp = mChargeService.updateChargeSheetDelivery(chargeSheetId, req);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 取消快递费
     */
    @PutMapping("/{chargeSheetId}/delivery/cancel")
    @LogReqAndRsp
    public AbcServiceResponse<ChargeSheetView> cancelChargeSheetDelivery(@PathVariable("chargeSheetId") String chargeSheetId,
                                                                         @RequestBody OpenApiCancelDeliveryReq req) {
        ChargeSheetView rsp = mChargeService.cancelChargeSheetDelivery(chargeSheetId, req);
        return new AbcServiceResponse<>(rsp);
    }

    @GetMapping("/{chargeSheetId}/ast-result")
    @LogReqAndRsp
    @ApiOperation(value = "获取皮试结果rpc")
    public AbcServiceResponse<ChargeSheetAstResultRsp> getChargeSheetAstResult(@PathVariable("chargeSheetId") String chargeSheetId,
                                                                               @RequestParam("clinicId") String clinicId) {
        return new AbcServiceResponse<>(chargePrintService.getChargeSheetAstResult(chargeSheetId, clinicId));
    }

    @GetMapping("/patientorders/{patientOrderId}/simple-charge-sheets")
    @ApiOperation(value = "根据patientOrderId查询收费单列表")
    @LogReqAndRsp(rpcCallStat = true)
    public AbcServiceResponse<AbcListPage<ChargeSheetAbstract>> getChargeSheetAbstractListByPatientOrderId(@PathVariable(value = "patientOrderId") String patientOrderId,
                                                                                                           @RequestParam(value = "chainId") String chainId) {


        if (org.apache.commons.lang3.StringUtils.isEmpty(patientOrderId)) {
            throw new ParamRequiredException("patientOrderId");
        }

        if (org.apache.commons.lang3.StringUtils.isEmpty(chainId)) {
            throw new ParamRequiredException("chainId");
        }

        List<ChargeSheetAbstract> chargeSheetAbstractList = chargePatientOrderService.getChargeSheetAbstractListByPatientOrderId(chainId, patientOrderId);
        AbcListPage<ChargeSheetAbstract> rsp = new AbcListPage<>();
        rsp.setRows(new ArrayList<>());

        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(chargeSheetAbstractList)) {
            rsp.setRows(chargeSheetAbstractList);
        }

        return new AbcServiceResponse<>(rsp);
    }

    @GetMapping("/charge-sheets/{chargeSheetId}/uncharged-order/push-info")
    @ApiOperation(value = "获取收费单待支付订单推送信息")
    @LogReqAndRsp(rpcCallStat = true)
    public AbcServiceResponse<UnchargedOrderPushInfo> getUnchargedOrderPushInfo(@PathVariable(value = "chargeSheetId") String chargeSheetId,
                                                                                @RequestParam(value = "chainId") String chainId) {

        return new AbcServiceResponse<>(mChargeService.getUnchargedOrderPushInfo(chargeSheetId, chainId));
    }

    @PostMapping("/list-charge-sheets-by-patient-order-ids")
    @LogReqAndRsp(rpcCallStat = true)
    @ApiOperation(value = "根据patientOrderIds查询收费单简要信息")
    public AbcServiceResponse<QueryChargeSheetByPatientOrderIdsRsp> listChargeSheetSimpleByPatientOrderIds(@Valid @RequestBody QueryChargeSheetByPatientOrderIdsReq req) {
        req.checkParam();
        return new AbcServiceResponse<>(mChargeService.listChargeSheetSimpleByPatientOrderIds(req));
    }

    @PostMapping("/list-charge-sheet-view-by-ids")
    @LogReqAndRsp(rpcCallStat = true)
    @ApiOperation(value = "根据patientOrderIds查询收费单简要信息")
    public AbcServiceResponse<ChargeSheetViewList> listChargeSheetViewByIds(@RequestBody QueryChargeSheetViewListReq req) {
        req.checkParam();
        return new AbcServiceResponse<>(mChargeService.listChargeSheetViewByIds(req));
    }

    /**
     * 社保创建外购处方收费单
     */
    @PostMapping("/shebao/outsource-prescription")
    @LogReqAndRsp
    public AbcServiceResponse<CreateChargeSheetForShebaoOutsourcePrescriptionRsp> createOutsourceChargeSheet(
            @RequestBody @Valid CreateChargeSheetForShebaoOutsourcePrescriptionReq req) {
        CreateChargeSheetForShebaoOutsourcePrescriptionRsp rsp = chargeShebaoService.createChargeSheetForOutsourcePrescription(req);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 计算医保限价
     *
     * @return
     */
    @PostMapping("/calculate-shebao-limit-price")
    @LogReqAndRsp
    public AbcServiceResponse<RpcCalculateShebaoLimitPriceRsp> calculateShebaoLimitPrice(@RequestBody @Valid RpcCalculateShebaoLimitPriceReq req) {
        return new AbcServiceResponse<>(chargeMedicareLimitPriceFacade.calculateShebaoLimitPrice(req));
    }

    @GetMapping("/patient/{patientId}/count")
    @ApiOperation(value = "查询患者收费统计数据")
    public AbcServiceResponse<PatientChargeCount> getPatientChargeCount(@PathVariable("patientId") String patientId,
                                                                        @RequestParam(value = "chainId") String chainId,
                                                                        @RequestParam(value = "withChargeSheetCount", required = false, defaultValue = "0") int withChargeSheetCount) {
        PatientChargeCount patientChargeCount = new PatientChargeCount();
        patientChargeCount.setPatientId(patientId);
        int physicalTherapyCount = chargePhysicalTherapyService.countPatientPhysicalTherapy(patientId, chainId);
        if (withChargeSheetCount == 1) {
            int chargeSheetCount = mChargeService.findPatientChargeSheetCount(patientId, chainId);
            patientChargeCount.setChargeSheetCount(chargeSheetCount);
        }
        patientChargeCount.setPhysicalTherapyCount(physicalTherapyCount);
        return new AbcServiceResponse<>(patientChargeCount);
    }

    @GetMapping("/query-latest-charge-sheets")
    @ApiOperation(value = "查询最近修改的收费单列表")
    @LogReqAndRsp(rpcCallStat = true)
    public AbcServiceResponse<AbcListPage<ChargeSheetSimpleListRsp.ChargeSheetSimple>> pageLatestChargeSheetList(@RequestParam(value = "offset") Integer offset,
                                                                                                                 @RequestParam(value = "limit") Integer limit,
                                                                                                                 @RequestParam(value = "beginDate") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date beginDate,
                                                                                                                 @RequestParam(value = "endDate") @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endDate,
                                                                                                                 @RequestParam(value = "clinicId") String clinicId) {
        if (offset == null || offset < 0) {
            offset = 0;
        }
        if (limit == null || limit < 0) {
            limit = 20;
        }
        limit = limit > 100 ? 100 : limit;

        if (beginDate == null) {
            throw new ParamRequiredException("beginDate");
        }
        if (endDate == null) {
            throw new ParamRequiredException("endDate");
        }
        if (StringUtils.isEmpty(clinicId)) {
            throw new ParamRequiredException("clinicId");
        }
        AbcListPage<ChargeSheetSimpleListRsp.ChargeSheetSimple> rspData = mChargeService.pageLatestChargeSheetList(offset, limit, beginDate, endDate, clinicId);

        return new AbcServiceResponse<>(rspData);
    }

    /**
     * 获取最后一次患者的收费单信息
     */
    @GetMapping("/query-latest-charge-sheet/{patientId}")
    @ApiOperation(value = "获取最后一次患者的收费单信息")
    @LogReqAndRsp(rpcCallStat = true)
    public AbcServiceResponse<cn.abcyun.cis.charge.api.model.LastChargeSheetSimpleRsp> getLatestChargeSheet(@PathVariable("patientId") String patientId,
                                                                                                            @RequestParam(value = "chainId") String chainId,
                                                                                                            @RequestParam(value = "clinicId") String clinicId) {
        cn.abcyun.cis.charge.api.model.LastChargeSheetSimpleRsp rspData = mChargeService.getLatestChargeSheet(patientId, chainId, clinicId);
        return new AbcServiceResponse<>(rspData);
    }

    /**
     * 查询已退费的项目
     */
    @PostMapping("/sheet/refund-item")
    @ApiOperation(value = "查询已退费的项目")
    @LogReqAndRsp(rpcCallStat = true)
    public AbcServiceResponse<AbcListPage<ChargeRefundedItemView>> queryChargeRefundItem(@RequestBody QueryChargeRefundItemReq req) {
        List<ChargeRefundedItemView> chargeRefundedItemViews = mChargeService.queryChargeRefundedItemViews(req);
        AbcListPage<ChargeRefundedItemView> listPage = new AbcListPage<>();
        listPage.setRows(chargeRefundedItemViews);
        return new AbcServiceResponse<>(listPage);
    }

    @PostMapping("/process/update")
    @ApiOperation(value = "更新加工信息")
    @LogReqAndRsp
    AbcServiceResponse<OpsCommonRsp> updateChargeSheetProcessInfo(@RequestBody ChargeSheetProcessInfoListReq req) {
        return new AbcServiceResponse<>(chargeSheetProcessInfoService.updateChargeSheetProcessInfo(req));
    }

    /**
     * 更新追溯码
     */
    @PutMapping("/update-traceable-code")
    @LogReqAndRsp(rpcCallStat = true)
    public AbcServiceResponse<OpsCommonRsp> updateChargeSheetTraceableCode(@RequestBody UpdateTraceableCodeReq req) {
        chargeSheetService.updateChargeSheetTraceableCode(req);
        return new AbcServiceResponse<>(new OpsCommonRsp(OpsCommonRsp.SUCC, "success"));
    }

    @LogReqAndRsp
    @PutMapping("/direct/improve")
    @ApiOperation(value = "完善零售收费单的患者信息", produces = "application/json")
    public AbcServiceResponse<OpsCommonRsp> improveDirectPatient(@RequestBody ChargeImproveDirectPatientRpcReq rpcReq) {
        ChargeImproveDirectPatientReq req = new ChargeImproveDirectPatientReq();
        req.setDoctorId(rpcReq.getDoctorId());
        req.setDepartmentId(rpcReq.getDoctorDepartmentId());
        req.setDepartmentName(rpcReq.getDoctorDepartmentName());
        String patientIdCard = rpcReq.getPatientIdCard();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(patientIdCard)) {
            req.setPatientIdCard(patientIdCard);
        }
        req.setExtendDiagnosisInfos(rpcReq.getExtendDiagnosisInfos());
        return new AbcServiceResponse<>(mChargeService.improveDirectPatient(rpcReq.getChainId(), rpcReq.getClinicId(), rpcReq.getChargeSheetId(), rpcReq.getOperatorId(), req));
    }

    /**
     * 查询可原路退回的收费单列表
     */
    @LogReqAndRsp
    @PostMapping("/recharge-refund/available-list")
    public AbcServiceResponse<AbcListPage<ChargeRechargeRefundAvailableItem>> queryRechargeRefundableList(@RequestBody ChargeRechargeRefundAvailableReq req) {
        AbcListPage<ChargeRechargeRefundAvailableItem> list = mChargeService.queryRechargeRefundableList(req);
        return new AbcServiceResponse<>(list);
    }

    /**
     * ABC支付充值退费
     */
    @LogReqAndRsp
    @PostMapping("/recharge-refund/refund")
    public AbcServiceResponse<RefundChargeSheetRsp> rechargeRefund(@RequestBody ChargeRechargeRefundReq req) {
        RefundChargeSheetRsp rsp = mChargeService.rechargeRefund(req);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 批量删除收费单
     */
    @PostMapping("/charge-sheets/batch-delete")
    @LogReqAndRsp
    @ApiOperation(value = "批量删除收费单")
    public AbcServiceResponse<BatchDeleteChargeSheetRsp> batchDeleteChargeSheets(@Valid @RequestBody BatchDeleteChargeSheetReq req) {
        BatchDeleteChargeSheetRsp rsp = new BatchDeleteChargeSheetRsp();
        List<String> successChargeSheetIds = new ArrayList<>();
        Map<String, String> failedChargeSheetIds = new HashMap<>();

        for (String chargeSheetId : req.getChargeSheetIds()) {
            try {
                mChargeService.deleteChargeSheetDraft(chargeSheetId, req.getClinicId(), req.getHisType(), req.getOperatorId());
                successChargeSheetIds.add(chargeSheetId);
            } catch (ChargeServiceException be) {
                failedChargeSheetIds.put(chargeSheetId, be.getMessage());
            } catch (Exception e) {
                sLogger.error("删除收费单异常, chargeSheetId: {}, error: {}", chargeSheetId, e.getMessage(), e);
                failedChargeSheetIds.put(chargeSheetId, e.getMessage());
            }
        }

        rsp.setSuccessChargeSheetIds(successChargeSheetIds);
        rsp.setFailedChargeSheetIds(failedChargeSheetIds);
        return new AbcServiceResponse<>(rsp);
    }

    @GetMapping("/charge-sheet/{chargeSheetId}/trace-codes")
    @LogReqAndRsp
    public AbcServiceResponse<ChargeSheetTraceCodesRsp> getChargeSheetTraceCodes(@PathVariable String chargeSheetId,
                                                                                 @RequestParam String chainId) {
        return new AbcServiceResponse<>(mChargeService.getChargeSheetTraceCodes(chargeSheetId, chainId));
    }

    @GetMapping("/{chargeSheetId}/query-cooperation-order-shebao-simple-info")
    @LogReqAndRsp
    public AbcServiceResponse<OutpatientSheetShebaoSimpleInfo> queryCooperationOrderShebaoSimpleInfo(@PathVariable(value = "chargeSheetId") String chargeSheetId,
                                                                                                    @RequestParam(value = "clinicId") String clinicId) {
        return new AbcServiceResponse<>(chargeCooperationOrderFacade.queryCooperationOrderShebaoSimpleInfo(chargeSheetId, clinicId));
    }

    @PostMapping("/ha-rabbitmq-retry")
    @LogReqAndRsp
    public AbcServiceResponse<BasicCommonRsp> haRabbitMqRetry(@RequestBody ChargeMessage chargeMessage) {
        sLogger.info("haRabbitMqRetry messageBody:{}", chargeMessage);
        hamqConsumer.receive(chargeMessage);
        return new AbcServiceResponse<>(new BasicCommonRsp());
    }
}
