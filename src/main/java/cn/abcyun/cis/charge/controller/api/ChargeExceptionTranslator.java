package cn.abcyun.cis.charge.controller.api;

import cn.abcyun.cis.charge.base.AirPharmacyDeliveryOrProcessChangedException;
import cn.abcyun.cis.charge.base.ChargeHospitalSheetCanNotSettleException;
import cn.abcyun.cis.charge.base.ChargeServiceError;
import cn.abcyun.cis.charge.base.ExpressDeliveryAddressRepeatException;
import cn.abcyun.cis.charge.base.ProductInfoChangedException;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.commons.CisServiceError;
import cn.abcyun.cis.commons.CisServiceResponseBody;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.cis.core.base.ExceptionTranslator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

@ControllerAdvice
public class ChargeExceptionTranslator extends ExceptionTranslator {
    private static final Logger sLogger = LoggerFactory.getLogger(ChargeExceptionTranslator.class);

    @ExceptionHandler(ProductInfoChangedException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    public CisServiceResponseBody<Object> processProductInfoChangedException(ProductInfoChangedException ex) {
        CisServiceError error = new CisServiceError(ex.getCode(), ex.getMessage());
        error.setDetail(ex.getChargeFormItemView());
        return new CisServiceResponseBody<>(null, error);
    }

    @ExceptionHandler(ExpressDeliveryAddressRepeatException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    public CisServiceResponseBody<Object> processExpressDeliveryAddressRepeatException(ExpressDeliveryAddressRepeatException ex) {
        CisServiceError error = ex.getError();
        error.setDetail(ex.getAddressVos());
        return new CisServiceResponseBody<>(null, error);
    }

    @ExceptionHandler(ChargeHospitalSheetCanNotSettleException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    public CisServiceResponseBody<Object> processChargeHospitalSheetCanNotSettleException(ChargeHospitalSheetCanNotSettleException ex) {
        CisServiceError error = new CisServiceError(ex.getCode(), ex.getMessage());
        error.setDetail(ex.getChargeHospitalItemNotValidViews());
        return new CisServiceResponseBody<>(null, error);
    }

    @ExceptionHandler(ObjectOptimisticLockingFailureException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    public CisServiceResponseBody<Object> processObjectOptimisticLockingFailureException(ObjectOptimisticLockingFailureException ex) {
        sLogger.error("processObjectOptimisticLockingFailureException", ex);
        CisServiceError error;
        if (TextUtils.equals(ChargeSheet.class.getName(), ex.getPersistentClassName())) {
            error = ChargeServiceError.CHARGE_SHEET_CHANGED;
        } else {
            error = new CisServiceError(400, "重复修改数据，请刷新后再试");
        }
        return new CisServiceResponseBody<>(null, error);
    }

    @ExceptionHandler(AirPharmacyDeliveryOrProcessChangedException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    public CisServiceResponseBody<Object> processAirPharmacyDeliveryOrProcessChangedException(AirPharmacyDeliveryOrProcessChangedException ex) {
        sLogger.error("airPharmacyDeliveryOrProcessChangedException", ex);
        CisServiceError error = new CisServiceError(ex.getCode(), ex.getMessage());
        return new CisServiceResponseBody<>(400, error);
    }

}
