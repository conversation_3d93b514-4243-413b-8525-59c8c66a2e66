package cn.abcyun.cis.charge.repository;

import cn.abcyun.cis.charge.model.ChargeOweCombineTransactionRecordDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface ChargeOweCombineTransactionRecordDetailRepository extends JpaRepository<ChargeOweCombineTransactionRecordDetail, String> {

    List<ChargeOweCombineTransactionRecordDetail> findAllByClinicIdAndOweSheetIdAndIsOldRecordAndIsDeleted(String clinicId, String oweSheetId, int isOldRecord, int isDeleted);

    List<ChargeOweCombineTransactionRecordDetail> findAllByClinicIdAndChargeSheetIdInAndIsOldRecordAndIsDeleted(String clinicId, List<String> chargeSheetIds, int isOldRecord, int isDeleted);

    int countByClinicIdAndOweSheetIdAndIsDeleted(String clinicId, String oweSheetId, int isDeleted);

    /**
     * 修改患者id
     *
     * @param chainId          连锁id
     * @param targetPatientId  目标患者id
     * @param sourcePatientIds 源患者id集合
     */
    @Modifying
    @Transactional(rollbackFor = Exception.class)
    @Query(value = "UPDATE v2_charge_owe_combine_transaction_record_detail SET patient_id=:targetPatientId WHERE chain_id = :chainId and patient_id in (:sourcePatientIds)", nativeQuery = true)
    void updatePatientId(@Param("chainId") String chainId, @Param("targetPatientId") String targetPatientId, @Param("sourcePatientIds") List<String> sourcePatientIds);

}
