package cn.abcyun.cis.charge.repository;

import cn.abcyun.cis.charge.model.ChargeOweCombineTransactionRecordDetailBatchInfo;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface ChargeOweCombineTransactionRecordDetailBatchInfoRepository extends JpaRepository<ChargeOweCombineTransactionRecordDetailBatchInfo, String> {

    List<ChargeOweCombineTransactionRecordDetailBatchInfo> findAllByClinicIdAndTransactionRecordIdInAndIsOldRecordAndIsDeleted(String clinicId, List<String> transactionRecordIds, int isOldRecord, int isDeleted);

}
