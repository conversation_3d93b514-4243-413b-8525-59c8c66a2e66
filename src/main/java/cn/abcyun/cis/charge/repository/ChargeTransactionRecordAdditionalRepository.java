package cn.abcyun.cis.charge.repository;

import cn.abcyun.cis.charge.model.ChargeTransactionRecordAdditional;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface ChargeTransactionRecordAdditionalRepository extends JpaRepository<ChargeTransactionRecordAdditional, String> {

    List<ChargeTransactionRecordAdditional> findAllByIdInAndIsDeleted(List<String> ids, int isDeleted);

}
