package cn.abcyun.cis.charge.repository;

import cn.abcyun.cis.charge.model.ChargeMedicareLimitPriceProduct;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface ChargeMedicareLimitPriceProductItemRepository extends JpaRepository<ChargeMedicareLimitPriceProduct, Long> {

    List<ChargeMedicareLimitPriceProduct> findByChainIdAndClinicIdAndIsDeleted(String chainId, String clinicId, int isDeleted);

    List<ChargeMedicareLimitPriceProduct> findAllByChainIdAndTypeAndIsDeleted(String chainId, int type, int isDeleted);
}
