package cn.abcyun.cis.charge.repository;

import cn.abcyun.cis.charge.model.ChargeTransactionRecordBatchInfo;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface ChargeTransactionRecordBatchInfoRepository extends JpaRepository<ChargeTransactionRecordBatchInfo, String> {

    List<ChargeTransactionRecordBatchInfo> findAllByTransactionIdInAndIsDeleted(List<String> chargeTransactionId, int isDeleted);


}
