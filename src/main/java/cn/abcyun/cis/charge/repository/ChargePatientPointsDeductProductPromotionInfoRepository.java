package cn.abcyun.cis.charge.repository;

import cn.abcyun.cis.charge.model.ChargePatientPointsDeductProductPromotionInfo;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface ChargePatientPointsDeductProductPromotionInfoRepository extends JpaRepository<ChargePatientPointsDeductProductPromotionInfo, String> {

    ChargePatientPointsDeductProductPromotionInfo findFirstByChainIdAndClinicIdAndChargeSheetIdAndIsDeletedOrderByCreatedDesc(String chainId, String clinicId, String chargeSheetId, int isDeleted);

    List<ChargePatientPointsDeductProductPromotionInfo> findAllByClinicIdAndChargeSheetIdInAndIsDeleted(String clinicId, List<String> chargeSheetId, int isDeleted);

}
