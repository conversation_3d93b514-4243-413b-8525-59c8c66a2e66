package cn.abcyun.cis.charge.repository;

import cn.abcyun.cis.charge.model.ChargeExecuteRecordEffect;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * 执行记录效果 jpa
 *
 * <AUTHOR>
 * @version ChargeExecuteRecordEffect.java, 2020/11/30 下午5:44
 */
public interface ChargeExecuteRecordEffectRepository extends JpaRepository<ChargeExecuteRecordEffect, Long> {

    /**
     * 根据执行记录id集合和删除标识查询执行效果集合
     * @param executeRecordIds 执行记录id集合
     * @param isDeleted 删除标识
     * @return 结果
     */
    List<ChargeExecuteRecordEffect> findByExecuteRecordIdInAndIsDeleted(List<String> executeRecordIds, int isDeleted);
}
