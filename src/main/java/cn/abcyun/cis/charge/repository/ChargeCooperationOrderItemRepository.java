package cn.abcyun.cis.charge.repository;

import cn.abcyun.cis.charge.model.ChargeCooperationOrderItem;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Collection;
import java.util.List;

public interface ChargeCooperationOrderItemRepository extends JpaRepository<ChargeCooperationOrderItem, String> {

    List<ChargeCooperationOrderItem> findAllByOrderIdInAndIsDeleted(Collection<String> orderIds, int isDeleted);

}
