package cn.abcyun.cis.charge.repository;

import cn.abcyun.cis.charge.model.ChargeForm;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;

public interface ChargeFormRepository extends JpaRepository<ChargeForm, String> {

    @Modifying
    @Transactional
    @Query(value = "UPDATE v2_charge_form SET is_deleted=1, last_modified_by= :lastModifiedBy, last_modified= :lastModified WHERE id = :id", nativeQuery = true)
    void setIsDeletedById(@Param("id") String id, @Param("lastModifiedBy") String lastModifiedBy, @Param("lastModified") Instant lastModified);

    @Modifying
    @Transactional
    @Query(value = "UPDATE v2_charge_form SET is_can_be_refund = 1, last_modified_by= :lastModifiedBy, last_modified= :lastModified WHERE id = :id", nativeQuery = true)
    void setIsCanBeRefundById(@Param("id") String id, @Param("lastModifiedBy") String lastModifiedBy, @Param("lastModified") Instant lastModified);

    @Modifying
    @Transactional
    @Query(value = "update v2_charge_form set dispensing_status = 1 where is_deleted = 0 and id = :chargeFormId and last_modified = :lastModified", nativeQuery = true)
    void updateDispensingStatus(@Param("chargeFormId")String chargeFormId, @Param("lastModified") Instant lastModified);

    /**
     * 根据chargeSheetId查询包含已删除的chargeForm集合
     * @param chargeSheetId 收费单id
     * @return 结果
     */
    List<ChargeForm> findByChargeSheetId(String chargeSheetId);

    List<ChargeForm> findByChargeSheetIdIn(List<String> chargeSheetIds);

    List<ChargeForm> findByChargeSheetIdInAndIsDeleted(List<String> chargeSheetIds, int isDeleted);

    /**
     * 根据连锁id、主键id集合查询chargeForm
     * @param chainId 连锁id
     * @param ids 主键id集合
     * @return 结果
     */
    List<ChargeForm> findByChainIdAndIdIn(String chainId, List<String> ids);


    ChargeForm findByClinicIdAndSourceFormIdAndIsDeleted(String clinicId, String sourceFormId, int isDeleted);
}
