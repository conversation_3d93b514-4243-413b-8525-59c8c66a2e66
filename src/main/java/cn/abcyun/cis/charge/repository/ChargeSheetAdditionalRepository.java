package cn.abcyun.cis.charge.repository;

import cn.abcyun.cis.charge.model.ChargeSheetAdditional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * charge sheet additional jpa
 *
 * <AUTHOR>
 */
public interface ChargeSheetAdditionalRepository extends JpaRepository<ChargeSheetAdditional, String> {
    /**
     * 根据主键id 查询
     *
     * @param chargeSheetId 主键
     * @param isDeleted     删除标识
     * @return 查询结果
     */
    ChargeSheetAdditional findByIdAndClinicIdAndIsDeleted(String chargeSheetId, String clinicId, int isDeleted);

    /**
     * 根据主键id 查询
     *
     * @param chargeSheetId 主键
     * @param isDeleted     删除标识
     * @return 查询结果
     */
    ChargeSheetAdditional findByIdAndIsDeleted(String chargeSheetId, int isDeleted);

    /**
     * 根据主键id集合、删除标识查询集合
     *
     * @param ids       主键id集合
     * @param isDeleted 删除标识
     * @return 查询结果
     */
    List<ChargeSheetAdditional> findByIdInAndIsDeleted(List<String> ids, int isDeleted);

    @Modifying
    @Transactional
    @Query(value = "update v2_charge_sheet_additional a inner join v2_charge_sheet b on a.id = b.id " +
            "set a.chief_complaint = :chiefComplaint, a.diagnosis = :diagnosis, a.diagnosis_infos = :diagnosisInfos," +
            " a.extend_diagnosis_infos = :extendDiagnosisInfos, a.last_modified_by = :operatorId, a.last_modified = now()  " +
            "where b.patient_order_id = :patientOrderId and b.chain_id = :chainId and b.type in (2, 13) and a.clone_prescription_type != 1 and a.is_deleted = 0 and b.is_deleted = 0", nativeQuery = true)
    void updateForMedicalRecord(@Param("chiefComplaint") String chiefComplaint,
                                @Param("diagnosis") String diagnosis,
                                @Param("diagnosisInfos") String diagnosisInfos,
                                @Param("extendDiagnosisInfos") String extendDiagnosisInfos,
                                @Param("patientOrderId") String patientOrderId,
                                @Param("chainId") String chainId,
                                @Param("operatorId") String operatorId);

    @Modifying
    @Transactional
    @Query(value = "update v2_charge_sheet_additional " +
            " set department_id = :departmentId, consultant_id = :consultantId, chief_complaint = :chiefComplaint," +
            " diagnosis = :diagnosis, diagnosis_infos = :diagnosisInfos," +
            " extend_diagnosis_infos = :extendDiagnosisInfos, last_modified_by = :operatorId, last_modified = now()  " +
            " where id in (:ids) and is_deleted = 0", nativeQuery = true)
    void updateForOutpatientSheet(
            @Param("ids") List<String> ids,
            @Param("departmentId") String departmentId,
            @Param("consultantId") String consultantId,
            @Param("chiefComplaint") String chiefComplaint,
            @Param("diagnosis") String diagnosis,
            @Param("diagnosisInfos") String diagnosisInfos,
            @Param("extendDiagnosisInfos") String extendDiagnosisInfos,
            @Param("operatorId") String operatorId);


}
