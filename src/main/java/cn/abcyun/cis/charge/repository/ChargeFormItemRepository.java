package cn.abcyun.cis.charge.repository;

import cn.abcyun.cis.charge.model.ChargeFormItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Collection;
import java.util.List;

public interface ChargeFormItemRepository extends JpaRepository<ChargeFormItem, String> {

//    @Modifying
//    @Transactional
//    @Query(value = "UPDATE v2_charge_form_item SET is_deleted=1, last_modified_by= :lastModifiedBy, last_modified= :lastModified WHERE charge_form_id = :chargeFormId", nativeQuery = true)
//    void setAllIsDeletedByChargeFormId(@Param("chargeFormId") String chargeFormId, @Param("lastModifiedBy") String lastModifiedBy, @Param("lastModified") Instant lastModified);


//    List<ChargeFormItem> findByPatientOrderIdAndProductTypeAndIsDeleted(String patientOrderId, int productType, int isDeleted);

    /**
     * 根据收费单id集合、删除标识查询收费单子项集合
     *
     * @param chargeSheetIds 收费单id集合
     * @param isDeleted      删除标识
     * @return 查询结果
     */
    List<ChargeFormItem> findByChargeSheetIdInAndIsDeleted(List<String> chargeSheetIds, int isDeleted);

    /**
     * 根据收费单form id集合、删除标识查询收费单子项集合
     * @param chargeFormIds 收费单form id集合
     * @param isDeleted 删除标识
     * @return 结果
     */
    List<ChargeFormItem> findByChargeFormIdInAndIsDeleted(List<String> chargeFormIds, int isDeleted);
    /**
     * 根据收费单id查询包括已删除的收费单子项
     * @param chargeSheetId 收费单id
     * @return 结果
     */
    List<ChargeFormItem> findByChargeSheetId(String chargeSheetId);
    /**
     * 根据门店id、主键id集合、删除标识查询收费单子项集合
     *
     * @param chainId   连锁id
     * @param ids       主键id集合
     * @param isDeleted 删除标识
     * @return 结果
     */
    List<ChargeFormItem> findByIdInAndChainIdAndIsDeleted(List<String> ids, String chainId, int isDeleted);
    List<ChargeFormItem> findByChargeSheetIdIn(List<String> chargeSheetIds);
    List<ChargeFormItem> findAllByClinicIdAndChargeSheetIdAndIdInAndIsDeleted(String clinicId, String chargeSheetId, Collection<String> ids, int isDeleted);

    /**
     * 根据chargeId查询当前收费单有治疗理疗检查检验项的数据
     *
     * @param chargeSheetIds
     * @param type
     * @param offset
     * @param limit
     * @return
     */
    @Query(value = "select distinct(charge_sheet_id) from v2_charge_form_item where " +
            " charge_sheet_id in ( :chargeSheetIds) and product_type= :type and status != 3 and is_deleted = 0 order by created desc limit :offset,:limit", nativeQuery = true)
    List<String> findChargeSheetIdByPatientId(@Param("chargeSheetIds") List<String> chargeSheetIds, @Param("type") int type, @Param("offset") Integer offset, @Param("limit") Integer limit);

    /**
     * 查询总数接口
     * @param chargeSheetIds
     * @param type
     * @return
     */
    @Query(value = "select count(distinct(charge_sheet_id)) from v2_charge_form_item where " +
            " charge_sheet_id in ( :chargeSheetIds) and product_type= :type and status != 3 and is_deleted = 0", nativeQuery = true)
    int countChargeSheetIdByPatientId(@Param("chargeSheetIds") List<String> chargeSheetIds, @Param("type") int type);

    ChargeFormItem findByChainIdAndIdAndIsDeleted(String chainId, String id, int isDeleted);

    ChargeFormItem findFirstChargeFormItemByPatientOrderIdAndProductTypeAndIsDeleted(String patientOrderId, int productType, int isDeleted);

//    List<ChargeFormItem> findAllByIdInAndIsDeleted(List<String> ids, int isDeleted);

//    ChargeFormItem findByIdAndIsDeleted(String id, int isDeleted);

//    @Modifying
//    @Transactional
//    @Query(value = "update v2_charge_form_item set product_snapshot = :productSnapshot, last_modified = now(), last_modified_by = :operatorId where id = :id", nativeQuery = true)
//    void updateProductSnapshotById(@Param("id") String id, @Param("productSnapshot") String productSnapshot, @Param("operatorId") String operatorId);
}
