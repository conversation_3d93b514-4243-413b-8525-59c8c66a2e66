package cn.abcyun.cis.charge.repository;

import cn.abcyun.cis.charge.model.ChargeSheetRegisterIdentity;
import cn.abcyun.cis.charge.model.ChargeSheetRegisterInfo;
import org.springframework.data.jpa.repository.JpaRepository;

/**
 * <AUTHOR>
 * @create 2024-01-20 17:41
 * @Description
 */

public interface ChargeSheetRegisterIdentityRepository extends JpaRepository<ChargeSheetRegisterIdentity, Long> {
    ChargeSheetRegisterIdentity findFirstByChainIdAndVersionAndIsDeleted(String chainId, String version, Integer isDeleted);

    ChargeSheetRegisterIdentity findByChainIdAndIdAndIsDeleted(String chainId, Long id, Integer isDeleted);
}
