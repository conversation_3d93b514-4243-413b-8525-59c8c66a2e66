package cn.abcyun.cis.charge.repository;

import cn.abcyun.cis.charge.model.ChargeFormItemBatchInfo;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface ChargeFormItemBatchInfoRepository extends JpaRepository<ChargeFormItemBatchInfo, String> {

    List<ChargeFormItemBatchInfo> findAllByChargeFormItemIdInAndIsDeleted(List<String> chargeFormItemIds, int isDeleted);
}
