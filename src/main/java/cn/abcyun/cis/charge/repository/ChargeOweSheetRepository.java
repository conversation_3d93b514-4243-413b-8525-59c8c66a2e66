package cn.abcyun.cis.charge.repository;

import cn.abcyun.cis.charge.model.ChargeOweSheet;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface ChargeOweSheetRepository extends JpaRepository<ChargeOweSheet, Long> {

    ChargeOweSheet findByChargeSheetIdAndClinicIdAndIsOldRecordAndIsDeleted(String chargeSheetId, String clinicId, int isOldRecord, int isDeleted);

    List<ChargeOweSheet> findAllByChargeSheetIdInAndIsOldRecordAndIsDeleted(List<String> chargeSheetIds, int isOldRecord, int isDeleted);

    List<ChargeOweSheet> findAllByChargeSheetIdAndClinicIdAndIsDeleted(String chargeSheetId, String clinicId, int isDeleted);

    List<ChargeOweSheet> findAllByIdInAndClinicIdAndIsDeleted(List<Long> ids, String clinicId, int isDeleted);

    List<ChargeOweSheet> findAllByChargeSheetIdInAndClinicIdAndIsDeleted(List<String> chargeSheetIds, String clinicId, int isDeleted);

    List<ChargeOweSheet> findAllByChargeSheetIdInAndPatientIdAndIsDeleted(List<String> chargeSheetIds, String PatientId, int isDeleted);

    ChargeOweSheet findByIdAndClinicIdAndIsDeleted(Long id, String clinicId, int isDeleted);

    List<ChargeOweSheet> findAllByClinicIdAndPatientIdAndStatusInAndIsOldRecordAndIsDeletedOrderByCreatedDesc(String clinicId, String patientId, List<Integer> statuses, int isOldRecord, int isDeleted);

    List<ChargeOweSheet> findAllByChainIdInAndStatusAndIsDeleted(List<String> chainIds, int status, int isDeleted);

    ChargeOweSheet findFirstByClinicIdAndChargeSheetIdAndIsOldRecordAndIsDeletedOrderByCreatedDesc(String clinicId, String chargeSheetId, int isOldRecord, int isDeleted);


    @Modifying
    @Transactional
    @Query(value = "UPDATE v2_charge_owe_sheet SET is_old_record = 1, obsolete_millis = REPLACE(unix_timestamp(current_timestamp(3)), '.', '') WHERE charge_sheet_id = :chargeSheetId and is_old_record = 0 and is_deleted = 0", nativeQuery = true)
    void markOweSheetAsOldRecord(@Param("chargeSheetId") String chargeSheetId);

    @Modifying
    @Transactional
    @Query(value = "UPDATE v2_charge_owe_sheet SET is_old_record = 1, obsolete_millis = REPLACE(unix_timestamp(current_timestamp(3)), '.', '') where is_deleted = 0 and is_old_record = 0 and id in (:ids)", nativeQuery = true)
    void markOweSheetAsOldRecordForHospital(@Param("ids") List<Long> ids);

    @Query(value = "SELECT b.* from `v2_charge_sheet` a inner join `v2_charge_owe_sheet` b on a.id = b.`charge_sheet_id` and b.clinic_id = :clinicId " +
            " WHERE b.is_deleted = 0 AND b.is_old_record = 0 AND a.is_deleted = 0 " +
            "  AND a.hospital_sheet_id = :hospitalSheetId" +
            "  AND a.clinic_id = :clinicId", nativeQuery = true)
    List<ChargeOweSheet> findAllByChargeHospitalSheetIdAndClinicId(@Param("hospitalSheetId") Long hospitalSheetId, @Param("clinicId") String clinicId);

    /**
     * 修改患者id
     *
     * @param chainId          连锁id
     * @param targetPatientId  目标患者id
     * @param sourcePatientIds 源患者id集合
     */
    @Modifying
    @Transactional(rollbackFor = Exception.class)
    @Query(value = "UPDATE v2_charge_owe_sheet SET patient_id=:targetPatientId WHERE chain_id = :chainId and patient_id in (:sourcePatientIds)", nativeQuery = true)
    void updatePatientId(@Param("chainId") String chainId, @Param("targetPatientId") String targetPatientId, @Param("sourcePatientIds") List<String> sourcePatientIds);

}
