package cn.abcyun.cis.charge.repository;

import cn.abcyun.cis.charge.model.ChargeRuleExpressDeliveryAddress;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface ChargeRuleExpressDeliveryAddressRepository extends JpaRepository<ChargeRuleExpressDeliveryAddress, String> {

    @Query(value = "select a.* from v2_charge_rule_express_delivery_address a left join v2_charge_rule_express_delivery b" +
            " on a.rule_id = b.id and a.chain_id = b.chain_id and a.clinic_id = b. clinic_id" +
            " where a.is_deleted = 0 and b.is_deleted = 0 and b.status = 1 and a.chain_id = :chainId and a.clinic_id = :clinicId", nativeQuery = true)
    List<ChargeRuleExpressDeliveryAddress> findAllByChainIdAndClinicId(String chainId, String clinicId);

    @Query(value = "select a.* from v2_charge_rule_express_delivery_address a left join v2_charge_rule_express_delivery b" +
            " on a.rule_id = b.id and a.chain_id = b.chain_id and a.clinic_id = b. clinic_id" +
            " where a.is_deleted = 0 and b.is_deleted = 0 and b.status = 1 and a.chain_id = :chainId and a.clinic_id = :clinicId and a.rule_id <> :ruleId", nativeQuery = true)
    List<ChargeRuleExpressDeliveryAddress> findAllByChainIdAndClinicIdAndRuleIdNot(String chainId, String clinicId, String ruleId);

}
