package cn.abcyun.cis.charge.repository;

import cn.abcyun.cis.charge.model.ChargeTransaction;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

public interface ChargeTransactionRepository extends JpaRepository<ChargeTransaction, String> {

    ChargeTransaction findByIdAndAmountGreaterThanAndIsDeleted(String id, BigDecimal amount, int isDeleted);

    List<ChargeTransaction> findAllByChainIdAndChargeSheetIdInAndIsDeleted(String chainId, List<String> chargeSheetIds, int isDeleted);

    List<ChargeTransaction> findAllByChainIdAndChargeSheetIdInAndIsPaidbackAndIsDeleted(String chainId, List<String> chargeSheetIds, int isPaidBack, int isDeleted);


    List<ChargeTransaction> findAllByClinicIdAndChargeSheetIdInAndPayModeAndIsPaidbackAndIsDeleted(String clinicId, List<String> chargeSheetIds, int payMode, int isPaidBack, int isDeleted);

    List<ChargeTransaction> findAllByClinicIdAndChargeSheetIdInAndPayModeAndIsDeleted(String clinicId, List<String> chargeSheetIds, int payMode, int isDeleted);

    @Modifying
    @Transactional
    @Query(value = "UPDATE v2_charge_transaction SET patient_id=:patientId WHERE chain_id = :chainId and patient_id in (:patientIds)", nativeQuery = true)
    void updatePatientId(@Param("chainId")String chainId, @Param("patientId") String patientId, @Param("patientIds") List<String> patientIds);

    @Modifying
    @Transactional
    @Query(value = "UPDATE v2_charge_transaction SET member_card_id =:patientId WHERE chain_id = :chainId and pay_mode = 6 and member_card_id is not null and member_card_id in (:patientIds)", nativeQuery = true)
    void updateMemberCardId(@Param("chainId")String chainId, @Param("patientId") String patientId, @Param("patientIds") List<String> patientIds);

    @Modifying
    @Transactional
    @Query(value = "UPDATE v2_charge_transaction SET patient_id = :newPatientId WHERE chain_id = :chainId and patient_order_id = :patientOrderId and patient_id = '00000000000000000000000000000000'", nativeQuery = true)
    void updatePatientIdForAnonymousPatient(@Param("patientOrderId") String patientOrderId, @Param("chainId") String chainId, @Param("newPatientId") String newPatientId);

    List<ChargeTransaction> findAllByChargeSheetIdInAndIsDeleted(List<String> chargeSheetIds, int isDeleted);


}
