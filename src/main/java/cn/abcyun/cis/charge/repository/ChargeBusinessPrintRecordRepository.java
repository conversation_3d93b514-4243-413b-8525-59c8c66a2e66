package cn.abcyun.cis.charge.repository;

import cn.abcyun.cis.charge.model.ChargeBusinessPrintRecord;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface ChargeBusinessPrintRecordRepository extends JpaRepository<ChargeBusinessPrintRecord, String> {

    /**
     * 根据业务id和类型查询打印记录
     *
     * @param chainId           链店id
     * @param clinicId          门店id
     * @param businessId        业务id
     * @param businessType      业务类型
     * @param isDeleted         是否删除
     * @return 打印记录
     */
    Optional<ChargeBusinessPrintRecord> findByChainIdAndClinicIdAndBusinessIdAndBusinessTypeAndIsDeleted(String chainId, String clinicId,String businessId, Integer businessType, int isDeleted);

    /**
     * 根据业务id查询打印记录
     *
     * @param chainId           链店id
     * @param clinicId          门店id
     * @param businessId        业务id
     * @param isDeleted         是否删除
     * @return 打印记录
     */
    List<ChargeBusinessPrintRecord> findByChainIdAndClinicIdAndBusinessIdAndIsDeleted(String chainId, String clinicId, String businessId, int isDeleted);

}
