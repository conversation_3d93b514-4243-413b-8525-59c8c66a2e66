package cn.abcyun.cis.charge.hospital.controller.api;

import cn.abcyun.cis.charge.api.model.ChonghongChargeInvoiceReq;
import cn.abcyun.cis.charge.api.model.invoice.CreateChargeSheetInvoiceReq;
import cn.abcyun.cis.charge.hospital.api.model.ChonghongHospitalSheetInvoiceRsp;
import cn.abcyun.cis.charge.hospital.api.model.CreateHospitalSheetInvoiceRsp;
import cn.abcyun.cis.charge.hospital.api.model.HospitalSheetInvoicePrintPreviewRsp;
import cn.abcyun.cis.charge.hospital.service.ChargeHospitalInvoiceService;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.abcyun.cis.core.aop.LogReqAndRsp;
import cn.abcyun.common.model.AbcServiceResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping("/api/v2/charges/hospital/invoice/")
@Api(value = "ChargeHospitalInvoiceController", description = "住院结算单发票相关接口", produces = "application/json")
@Slf4j
public class ChargeHospitalInvoiceController {

    private final ChargeHospitalInvoiceService chargeHospitalInvoiceService;

    @Autowired
    public ChargeHospitalInvoiceController(ChargeHospitalInvoiceService chargeHospitalInvoiceService) {
        this.chargeHospitalInvoiceService = chargeHospitalInvoiceService;
    }

    /**
     * 住院单申请开发票
     * @param req
     * @param clinicId
     * @param employeeId
     * @return
     */
    @PutMapping("/{hospitalSheetId}")
    @LogReqAndRsp
    @ApiOperation(value = "住院单申请开发票", produces = "application/json")
    public AbcServiceResponse<CreateHospitalSheetInvoiceRsp> createInvoice(@RequestBody @Valid CreateChargeSheetInvoiceReq req,
                                                                         @PathVariable(value = "hospitalSheetId") String hospitalSheetId,
                                                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) {
        if (StringUtils.isEmpty(hospitalSheetId)) {
            throw new ParamRequiredException("hospitalSheetId");
        }

        if (!NumberUtils.isDigits(hospitalSheetId)) {
            throw new NotFoundException();
        }

        req.checkParam();


        CreateHospitalSheetInvoiceRsp rsp = chargeHospitalInvoiceService.createInvoice(req, Long.parseLong(hospitalSheetId), chainId, clinicId, employeeId);

        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 发票作废
     * @param clinicId
     * @param employeeId
     * @return
     */
    @PutMapping("/{hospitalSheetId}/destroy")
    @LogReqAndRsp
    @ApiOperation(value = "发票作废", produces = "application/json")
    public AbcServiceResponse<CreateHospitalSheetInvoiceRsp> destroyInvoice(@PathVariable(value = "hospitalSheetId") String hospitalSheetId,
                                                                            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) {
        if (StringUtils.isEmpty(hospitalSheetId)) {
            throw new ParamRequiredException("hospitalSheetId");
        }

        if (!NumberUtils.isDigits(hospitalSheetId)) {
            throw new NotFoundException();
        }

        CreateHospitalSheetInvoiceRsp rsp = chargeHospitalInvoiceService.destroyInvoice(Long.parseLong(hospitalSheetId), clinicId, employeeId);

        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 收费单开冲红发票
     * @param req
     * @param clinicId
     * @param employeeId
     * @return
     */
    @PutMapping("/{hospitalSheetId}/chonghong")
    @LogReqAndRsp
    @ApiOperation(value = "发票冲红", produces = "application/json")
    public AbcServiceResponse<ChonghongHospitalSheetInvoiceRsp> chonghongInvoice(@RequestBody ChonghongChargeInvoiceReq req,
                                                                                 @PathVariable(value = "hospitalSheetId") String hospitalSheetId,
                                                                                 @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                 @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                 @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) {
        if (StringUtils.isEmpty(hospitalSheetId)) {
            throw new ParamRequiredException("hospitalSheetId");
        }

        if (!NumberUtils.isDigits(hospitalSheetId)) {
            throw new NotFoundException();
        }

        ChonghongHospitalSheetInvoiceRsp rsp = chargeHospitalInvoiceService.chonghongInvoice(req, Long.parseLong(hospitalSheetId), chainId, clinicId, employeeId);

        return new AbcServiceResponse<>(rsp);
    }

//    /**
//     * 查看收费单的开票状态
//     * @param clinicId
//     * @return
//     */
//    @GetMapping("/{chargeSheetId}")
//    @LogReqAndRsp
//    public AbcServiceResponse<ChargeSheetInvoiceStatusRsp> getChargeSheetInvoiceStatus(@PathVariable(value = "chargeSheetId") String chargeSheetId,
//                                                                                       @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {
//
//        ChargeSheetInvoiceStatusRsp rsp = chargeSheetInvoiceService.getChargeSheetInvoiceStatus(chargeSheetId, clinicId);
//
//        return new AbcServiceResponse<>(rsp);
//    }

    /**
     * 打印发票预览
     * @param clinicId
     * @param type 0:开票，1：冲红，2：已冲红需要重新开票 {@link cn.abcyun.cis.charge.base.Constants.InvoicePrintPreviewType}
     * @return
     */
    @GetMapping("/{hospitalSheetId}/print/preview")
    @LogReqAndRsp
    @ApiOperation(value = "打印住院单发票预览", produces = "application/json")
    public AbcServiceResponse<HospitalSheetInvoicePrintPreviewRsp> getHospitalSheetNormalInvoicePrintPreview(@PathVariable(value = "hospitalSheetId") String hospitalSheetId,
                                                                                                             @RequestParam(value = "type", required = false, defaultValue = "0") int type,
                                                                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {
        if (StringUtils.isEmpty(hospitalSheetId)) {
            throw new ParamRequiredException("hospitalSheetId");
        }

        if (!NumberUtils.isDigits(hospitalSheetId)) {
            throw new NotFoundException();
        }

        HospitalSheetInvoicePrintPreviewRsp rsp = chargeHospitalInvoiceService.getHospitalSheetNormalInvoicePrintPreview(Long.parseLong(hospitalSheetId), clinicId, type);

        return new AbcServiceResponse<>(rsp);
    }
}
