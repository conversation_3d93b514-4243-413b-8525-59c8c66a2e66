package cn.abcyun.cis.charge.hospital.service;

import cn.abcyun.cis.charge.base.ChargeServiceError;
import cn.abcyun.cis.charge.base.ChargeServiceException;
import cn.abcyun.cis.charge.base.InnerPayModes;
import cn.abcyun.cis.charge.hospital.api.model.HospitalSheetPayCallbackReq;
import cn.abcyun.cis.charge.hospital.api.model.HospitalSheetPayCallbackRsp;
import cn.abcyun.cis.charge.hospital.interfaces.thirdparty.IHospitalSheetThirdPartyCallbackProvider;
import cn.abcyun.cis.charge.hospital.interfaces.thirdparty.IHospitalSheetThirdPartyRefundCallbackProvider;
import cn.abcyun.cis.charge.hospital.model.ChargeHospitalPayTransaction;
import cn.abcyun.cis.charge.hospital.model.ChargeHospitalSheet;
import cn.abcyun.cis.charge.service.ChargePayModeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class HospitalSheetThirdPartyRefundCallbackService implements IHospitalSheetThirdPartyCallbackProvider {

    private final List<IHospitalSheetThirdPartyRefundCallbackProvider> hospitalSheetThirdPartyRefundCallbackProviders;
    private Map<String, IHospitalSheetThirdPartyRefundCallbackProvider> hospitalSheetThirdPartyRefundCallbackProviderMap;
    private final ChargePayModeService chargePayModeService;

    @Autowired
    public HospitalSheetThirdPartyRefundCallbackService(List<IHospitalSheetThirdPartyRefundCallbackProvider> hospitalSheetThirdPartyRefundCallbackProviders, ChargePayModeService chargePayModeService) {

        this.hospitalSheetThirdPartyRefundCallbackProviders = hospitalSheetThirdPartyRefundCallbackProviders;
        this.chargePayModeService = chargePayModeService;
    }

    @PostConstruct
    public void initHospitalSheetThirdPartyRefundCallbackProviderMap() {

        if (CollectionUtils.isEmpty(hospitalSheetThirdPartyRefundCallbackProviders)) {
            return;
        }

        hospitalSheetThirdPartyRefundCallbackProviderMap = hospitalSheetThirdPartyRefundCallbackProviders.stream()
                .collect(Collectors.toMap(IHospitalSheetThirdPartyRefundCallbackProvider::getPayModeKey, Function.identity(), (a, b) -> a));
    }

    @Override
    public int getPayType() {
        return ChargeHospitalPayTransaction.PayType.REFUND;
    }

    @Override
    public HospitalSheetPayCallbackRsp callback(HospitalSheetPayCallbackReq payCallbackReq, ChargeHospitalSheet chargeHospitalSheet, ChargeHospitalPayTransaction payTransaction) {

        IHospitalSheetThirdPartyRefundCallbackProvider hospitalSheetThirdPartyRefundCallbackProvider = hospitalSheetThirdPartyRefundCallbackProviderMap.getOrDefault(InnerPayModes.getPayModeKeyByPayMode(payTransaction.getPayMode(), payTransaction.getPaySubMode()), null);

        if (Objects.isNull(hospitalSheetThirdPartyRefundCallbackProvider)) {
            throw new ChargeServiceException(ChargeServiceError.CHARGE_THIRD_PART_CALLBACK_PAY_MODE_NOT_FOUND);
        }
        return hospitalSheetThirdPartyRefundCallbackProvider.refundCallback(payCallbackReq, chargeHospitalSheet, payTransaction);
    }
}
