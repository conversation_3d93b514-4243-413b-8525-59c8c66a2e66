package cn.abcyun.cis.charge.hospital.api.model;

import cn.abcyun.cis.charge.base.pay.CombinedPayItem;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class PayChargeHospitalSheetReq {

    @NotNull(message = "payItem不能为空")
    private CombinedPayItem payItem;

    private BigDecimal receivableFee;
}
