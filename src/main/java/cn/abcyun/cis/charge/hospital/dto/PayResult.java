package cn.abcyun.cis.charge.hospital.dto;

import cn.abcyun.cis.charge.combinorder.dto.CombineOrderTransactionView;
import cn.abcyun.cis.charge.combinorder.dto.ThirdPartyPayItem;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
public class PayResult {
    private int status;
    private BigDecimal needPay;
    private String thirdPartyPayTaskId;
    private String combineOrderPayTransactionId;

    private int payStatus;

    private CombineOrderTransactionView combineOrderTransaction;

    private List<ThirdPartyPayItem> items;

}
