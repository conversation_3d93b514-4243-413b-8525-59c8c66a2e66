package cn.abcyun.cis.charge.hospital.service;

import cn.abcyun.cis.charge.base.ChargeServiceError;
import cn.abcyun.cis.charge.base.ChargeServiceException;
import cn.abcyun.cis.charge.base.InnerPayModes;
import cn.abcyun.cis.charge.hospital.api.model.HospitalSheetPayCallbackReq;
import cn.abcyun.cis.charge.hospital.api.model.HospitalSheetPayCallbackRsp;
import cn.abcyun.cis.charge.hospital.interfaces.thirdparty.IHospitalSheetThirdPartyCallbackProvider;
import cn.abcyun.cis.charge.hospital.interfaces.thirdparty.IHospitalSheetThirdPartyPayCallbackProvider;
import cn.abcyun.cis.charge.hospital.model.ChargeHospitalPayTransaction;
import cn.abcyun.cis.charge.hospital.model.ChargeHospitalSheet;
import cn.abcyun.cis.charge.hospital.repository.ChargeHospitalSheetRepository;
import cn.abcyun.cis.charge.service.ChargePayModeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class HospitalSheetThirdPartyPayCallbackService implements IHospitalSheetThirdPartyCallbackProvider {

    private final ChargeHospitalSheetRepository chargeHospitalSheetRepository;
    private final HospitalSheetProcessorProvider hospitalSheetProcessorProvider;
    private final List<IHospitalSheetThirdPartyPayCallbackProvider> hospitalSheetThirdPartyPayCallbackProviders;
    private Map<String, IHospitalSheetThirdPartyPayCallbackProvider> hospitalSheetThirdPartyPayCallbackProviderMap;
    private final ChargePayModeService chargePayModeService;

    @Autowired
    public HospitalSheetThirdPartyPayCallbackService(ChargeHospitalSheetRepository chargeHospitalSheetRepository,
                                                     HospitalSheetProcessorProvider hospitalSheetProcessorProvider,
                                                     List<IHospitalSheetThirdPartyPayCallbackProvider> hospitalSheetThirdPartyPayCallbackProviders, ChargePayModeService chargePayModeService) {

        this.chargeHospitalSheetRepository = chargeHospitalSheetRepository;
        this.hospitalSheetProcessorProvider = hospitalSheetProcessorProvider;
        this.hospitalSheetThirdPartyPayCallbackProviders = hospitalSheetThirdPartyPayCallbackProviders;
        this.chargePayModeService = chargePayModeService;
    }

    @PostConstruct
    public void initHospitalSheetThirdPartyPayCallbackProviderMap() {

        if (CollectionUtils.isEmpty(hospitalSheetThirdPartyPayCallbackProviders)) {
            return;
        }

        hospitalSheetThirdPartyPayCallbackProviderMap = hospitalSheetThirdPartyPayCallbackProviders.stream()
                .collect(Collectors.toMap(IHospitalSheetThirdPartyPayCallbackProvider::getPayModeKey, Function.identity(), (a, b) -> a));
    }

    @Override
    public int getPayType() {
        return ChargeHospitalPayTransaction.PayType.PAY;
    }

    @Override
    public HospitalSheetPayCallbackRsp callback(HospitalSheetPayCallbackReq payCallbackReq, ChargeHospitalSheet chargeHospitalSheet, ChargeHospitalPayTransaction payTransaction) {

        IHospitalSheetThirdPartyPayCallbackProvider hospitalSheetThirdPartyPayCallbackProvider = hospitalSheetThirdPartyPayCallbackProviderMap.getOrDefault(InnerPayModes.getPayModeKeyByPayMode(payTransaction.getPayMode(), payTransaction.getPaySubMode()), null);

        if (Objects.isNull(hospitalSheetThirdPartyPayCallbackProvider)) {
            throw new ChargeServiceException(ChargeServiceError.CHARGE_THIRD_PART_CALLBACK_PAY_MODE_NOT_FOUND);
        }
        return hospitalSheetThirdPartyPayCallbackProvider.payCallback(payCallbackReq, chargeHospitalSheet, payTransaction);
    }
}
