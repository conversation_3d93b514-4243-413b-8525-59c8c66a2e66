package cn.abcyun.cis.charge.hospital.api.model;

import cn.abcyun.bis.rpc.sdk.cis.model.patientorder.HospitalPatientOrderVO;
import cn.abcyun.cis.charge.service.dto.ChargeSheetExtend;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HospitalSheetView extends HospitalSheetSimpleView {

    private List<ChargeSheetExtend> chargeSheets;

    private HospitalPatientOrderVO hospitalPatientOrder;

}
