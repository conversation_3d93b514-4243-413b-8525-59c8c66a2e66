package cn.abcyun.cis.charge.hospital.interfaces.thirdparty;

import cn.abcyun.cis.charge.hospital.api.model.HospitalSheetPayCallbackReq;
import cn.abcyun.cis.charge.hospital.api.model.HospitalSheetPayCallbackRsp;
import cn.abcyun.cis.charge.hospital.model.ChargeHospitalPayTransaction;
import cn.abcyun.cis.charge.hospital.model.ChargeHospitalSheet;

public interface IHospitalSheetThirdPartyCallbackProvider {
    int getPayType();

    /**
     * 回调处理：
     * 分为支付回调，退款回调，部分退费退款回调
     * @param payCallbackReq
     * @param payTransaction
     * @return
     */
    HospitalSheetPayCallbackRsp callback(HospitalSheetPayCallbackReq payCallbackReq, ChargeHospitalSheet chargeHospitalSheet, ChargeHospitalPayTransaction payTransaction);
}
