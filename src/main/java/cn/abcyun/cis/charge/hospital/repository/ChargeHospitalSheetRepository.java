package cn.abcyun.cis.charge.hospital.repository;

import cn.abcyun.cis.charge.hospital.model.ChargeHospitalSheet;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

public interface ChargeHospitalSheetRepository extends JpaRepository<ChargeHospitalSheet, Long> {

    ChargeHospitalSheet findByChainIdAndClinicIdAndHospitalOrderIdAndIsDeleted(String chainId, String clinicId, Long hospitalOrderId, int isDeleted);

    ChargeHospitalSheet findByIdAndClinicIdAndIsDeleted(Long id, String clinicId, int isDeleted);

    ChargeHospitalSheet findByIdAndIsDeleted(Long id, int isDeleted);

    ChargeHospitalSheet findByHospitalOrderIdAndIsDeleted(Long hospitalOrderId, int isDeleted);

    List<ChargeHospitalSheet> findAllByClinicIdAndHospitalOrderIdInAndIsDeleted(String clinicId, List<Long> hospitalOrderIds, int isDeleted);


    @Modifying
    @Transactional
    @Query(value = "update v2_charge_hospital_sheet set total_price = total_price + :amount " +
            "where hospital_order_id = :hospitalOrderId and chain_id = :chainId and clinic_id = :clinicId and is_deleted = 0 and settle_status in (0, 10) and status = 0", nativeQuery = true)
    int updateChargeHospitalSheetTotalPrice(@Param("amount") BigDecimal amount, @Param("hospitalOrderId") Long hospitalOrderId, @Param("chainId") String chainId, @Param("clinicId") String clinicId);

    /**
     * 修改患者id
     *
     * @param chainId          连锁id
     * @param targetPatientId  目标患者id
     * @param sourcePatientIds 源患者id集合
     */
    @Modifying
    @Transactional(rollbackFor = Exception.class)
    @Query(value = "UPDATE v2_charge_hospital_sheet SET patient_id=:targetPatientId WHERE chain_id = :chainId and patient_id in (:sourcePatientIds)", nativeQuery = true)
    void updatePatientId(@Param("chainId") String chainId, @Param("targetPatientId") String targetPatientId, @Param("sourcePatientIds")List<String> sourcePatientIds);
}
