package cn.abcyun.cis.charge.hospital.api.model;

import cn.abcyun.cis.charge.service.dto.PaymentSummaryInfo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
public class RefundChargeHospitalSheetRsp {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    private int status;

    private String statusName;

    private BigDecimal refundFee;           //本次已退金额

    private BigDecimal refundedFee;         //累计已退金额

    private String chargePayTransactionId;

    private int payStatus;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String thirdPartyPayTaskId;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer thirdPartyPayTaskType;   //0:pay 1:refund

    private List<PaymentSummaryInfo> paymentSummaryInfos;
}
