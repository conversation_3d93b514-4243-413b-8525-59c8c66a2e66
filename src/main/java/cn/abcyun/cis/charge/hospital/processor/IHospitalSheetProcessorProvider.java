package cn.abcyun.cis.charge.hospital.processor;

import cn.abcyun.cis.charge.processor.provider.ChargePayModeProvider;
import cn.abcyun.cis.charge.processor.provider.GoodsScProvider;
import cn.abcyun.cis.charge.processor.provider.HospitalInvoiceProvider;

public interface IHospitalSheetProcessorProvider {

    HospitalSheetPayProvider getHospitalSheetPayProvider();

    ChargePayModeProvider getChargePayModeProvider();

    GoodsScProvider getGoodsScProvider();

    HospitalInvoiceProvider getHospitalInvoiceProvider();
}
