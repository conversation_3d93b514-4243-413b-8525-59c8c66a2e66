package cn.abcyun.cis.charge.hospital.api.model;

import cn.abcyun.cis.charge.util.MathUtils;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.Instant;

@Data
@Accessors(chain = true)
public class HospitalSheetSimpleView {

    /**
     * 主键id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 连锁id
     */
    private String chainId;

    /**
     * 诊所id
     */
    private String clinicId;

    /**
     * 患者id
     */
    private String patientId;

    /**
     * 医院登记id
     */
    private Long hospitalOrderId;

    /**
     * 已收金额
     */
    private BigDecimal receivedPrice;

    /**
     * 已退金额
     */
    private BigDecimal refundedPrice;

    /**
     * 总金额
     */
    private BigDecimal totalPrice;

    /**
     * 外诊总金额
     */
    private BigDecimal externalTotalPrice;

    /**
     * 包含外诊的总金额
     */
    private BigDecimal totalPriceContainExternal;

    /**
     * 结算状态：0：未发起结算，10：结算中，20：结算完成
     */
    private int settleStatus;

    /**
     * 支付状态：0：待收，10：部分收，20：已收，30：部分退费，40：已退费
     */
    private int status;

    /**
     * 创建时间
     */
    private Instant created;

    /**
     * 创建人id
     */
    private String createdBy;

    /**
     * 最后修改时间
     */
    private Instant lastModified;

    /**
     * 最后修改时间
     */
    private String lastModifiedBy;

    private String statusName;

    private String settleStatusName;

    public BigDecimal getTotalPriceContainExternal() {
        return MathUtils.wrapBigDecimalAdd(totalPrice, externalTotalPrice);
    }

    public String getSettleStatusName() {
        return StatusTranslator.translateSettleStatus(settleStatus);
    }

    public String getStatusName() {
        return StatusTranslator.translateStatus(status);
    }

}
