package cn.abcyun.cis.charge.hospital.processor;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsItem;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.QueryGoodsInPharmacyByIdsReq;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.QueryGoodsInPharmacyByIdsRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.shebao.QueryChargeSheetLongCareInfoRsp;
import cn.abcyun.cis.charge.base.ChargeServiceError;
import cn.abcyun.cis.charge.base.ChargeServiceException;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.base.pay.CombinedPayItem;
import cn.abcyun.cis.charge.combinorder.dto.CombinePayOrderPayResult;
import cn.abcyun.cis.charge.hospital.dto.PayResult;
import cn.abcyun.cis.charge.hospital.dto.RefundResult;
import cn.abcyun.cis.charge.hospital.dto.ThirdPartyRefundResultDto;
import cn.abcyun.cis.charge.hospital.model.ChargeHospitalSheet;
import cn.abcyun.cis.charge.model.ChargeAction;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.processor.ChargePayModeConfigSimple;
import cn.abcyun.cis.charge.processor.ChargeSheetFeeProtocol;
import cn.abcyun.cis.charge.processor.FormProcessor;
import cn.abcyun.cis.charge.processor.SheetProcessor;
import cn.abcyun.cis.charge.service.dto.print.ChargeFormItemPrintView;
import cn.abcyun.cis.charge.service.dto.print.ChargeFormPrintView;
import cn.abcyun.cis.charge.util.ChargeUtils;
import cn.abcyun.cis.charge.util.FillUtils;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.cis.commons.exception.ServiceInternalException;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.common.log.marker.AbcLogMarker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class HospitalSheetProcessor {

    private ChargeHospitalSheet chargeHospitalSheet;
    private IHospitalSheetProcessorProvider hospitalSheetProcessorProvider;
    /**
     * 应收
     */
    private BigDecimal realReceivablePrice;
    /**
     * 可退金额
     */
    private BigDecimal canRefundFee;
    private String operatorId;

    public HospitalSheetProcessor(ChargeHospitalSheet chargeHospitalSheet, IHospitalSheetProcessorProvider hospitalSheetProcessorProvider, String operatorId) {
        this.chargeHospitalSheet = chargeHospitalSheet;
        this.hospitalSheetProcessorProvider = hospitalSheetProcessorProvider;
        this.operatorId = operatorId;
        build();
    }

    private void build() {

        realReceivablePrice = MathUtils.wrapBigDecimalSubtract(chargeHospitalSheet.getTotalPrice(), chargeHospitalSheet.getReceivedPrice()).add(chargeHospitalSheet.getRefundedPrice());
        canRefundFee = MathUtils.wrapBigDecimalAdd(chargeHospitalSheet.getReceivedPrice(), chargeHospitalSheet.getRefundedPrice());

    }

    public List<ChargeFormPrintView> generateChargeFormPrintView(List<ChargeSheet> chargeSheets, List<QueryChargeSheetLongCareInfoRsp.GoodsItem> shebaoGoodsItems, int hisType) {

        if (CollectionUtils.isEmpty(chargeSheets)) {
            return new ArrayList<>();
        }

        List<SheetProcessor> sheetProcessors = chargeSheets.stream()
                .map(chargeSheet -> {
                    SheetProcessor sheetProcessor = new SheetProcessor(chargeSheet);
                    sheetProcessor.build();
                    return sheetProcessor;
                }).collect(Collectors.toList());

        setChargeFormItemGoodsItem(sheetProcessors);

        List<FormProcessor> formProcessors = sheetProcessors.stream()
                .flatMap(sheetProcessor -> sheetProcessor.getFormProcessors().stream())
                .collect(Collectors.toList());

        List<ChargeFormPrintView> chargeFormPrintViews = ChargeSheetFeeProtocol.generateChargeFormPrintViews(formProcessors, BigDecimal.ZERO, BigDecimal.ZERO, Collections.singletonList(Constants.ChargeFormItemStatus.CHARGED), hisType);

        bindShebaoPrintInfo(chargeFormPrintViews, shebaoGoodsItems);

        return chargeFormPrintViews;
    }

    private void bindShebaoPrintInfo(List<ChargeFormPrintView> chargeFormPrintViews, List<QueryChargeSheetLongCareInfoRsp.GoodsItem> shebaoGoodsItems) {

        if (CollectionUtils.isEmpty(chargeFormPrintViews) || CollectionUtils.isEmpty(shebaoGoodsItems)) {
            return;
        }

        Map<String, QueryChargeSheetLongCareInfoRsp.GoodsItem> shebaoGoodsItemMap = shebaoGoodsItems.stream()
                .filter(goodsItem -> StringUtils.isNotEmpty(goodsItem.getChargeFormItemId()))
                .collect(Collectors.toMap(QueryChargeSheetLongCareInfoRsp.GoodsItem::getChargeFormItemId, Function.identity(), (a, b) -> a));

        chargeFormPrintViews.stream()
                .filter(chargeFormPrintView -> chargeFormPrintView.getChargeFormItems() != null)
                .flatMap(chargeFormPrintView -> chargeFormPrintView.getChargeFormItems().stream())
                .forEach(chargeFormItemPrintView -> bindShebaoPrintInfo(chargeFormItemPrintView, shebaoGoodsItemMap.getOrDefault(chargeFormItemPrintView.getId(), null)));

    }

    private static void bindShebaoPrintInfo(ChargeFormItemPrintView chargeFormItemPrintView, QueryChargeSheetLongCareInfoRsp.GoodsItem rspItem) {

        if (chargeFormItemPrintView == null || rspItem == null) {
            return;
        }

        chargeFormItemPrintView.setSocialCode(rspItem.getSocialCode());
        chargeFormItemPrintView.setHisCode(rspItem.getHisCode());
        chargeFormItemPrintView.setMedicalFeeGrade(rspItem.getMedicalFeeGrade());
        chargeFormItemPrintView.setOwnExpenseRatio(rspItem.getOwnExpenseRatio());
        chargeFormItemPrintView.setOwnExpenseFee(rspItem.getOwnExpenseFee());
        if (chargeFormItemPrintView.getProductType() == Constants.ProductType.EXAMINATION ||
                chargeFormItemPrintView.getProductType() == Constants.ProductType.TREATMENT ||
                chargeFormItemPrintView.getProductType() == Constants.ProductType.REGISTRATION) {
            if (!TextUtils.isEmpty(rspItem.getSpec())) {
                chargeFormItemPrintView.setSocialUnit(rspItem.getSpec());
            }

            if (!TextUtils.isEmpty(rspItem.getSocialName())) {
                chargeFormItemPrintView.setSocialName(rspItem.getSocialName());
            }
        }
    }

    private void setChargeFormItemGoodsItem(List<SheetProcessor> sheetProcessors) {
        List<QueryGoodsInPharmacyByIdsReq.QueryPharmacyGoodsReq> pharmacyGoodsReqs = generateQueryPharmacyGoodsReq(sheetProcessors);

        List<QueryGoodsInPharmacyByIdsRsp.QueryPharmacyGoodsRsp> queryPharmacyGoodsRsps = hospitalSheetProcessorProvider.getGoodsScProvider().queryGoodsInPharmacyByIds(chargeHospitalSheet.getClinicId(), chargeHospitalSheet.getChainId(), false, 1, pharmacyGoodsReqs);

        Map<Integer, Map<String, GoodsItem>> limitPriceGoodsItemMap = Optional.ofNullable(queryPharmacyGoodsRsps)
                .orElse(new ArrayList<>())
                .stream()
                .flatMap(queryPharmacyGoodsRsp -> queryPharmacyGoodsRsp.getList().stream())
                .collect(Collectors.groupingBy(GoodsItem::getPharmacyNo, Collectors.toMap(GoodsItem::getId, goodsItem -> {

                    return goodsItem;
                }, (a, b) -> a)));

        sheetProcessors.forEach(sheetProcessor -> {
            List<FormProcessor> formProcessors = sheetProcessor.getFormProcessors();
            formProcessors.stream()
                    .filter(formProcessor -> !formProcessor.isAirPharmacy())
                    .forEach(formProcessor -> formProcessor.bindGoodsItems(limitPriceGoodsItemMap, new HashMap<>(), false, operatorId));
        });
    }

    /**
     * 将多个收费单的goods收集起来
     *
     * @param sheetProcessors
     * @return
     */
    private List<QueryGoodsInPharmacyByIdsReq.QueryPharmacyGoodsReq> generateQueryPharmacyGoodsReq(List<SheetProcessor> sheetProcessors) {
        /**
         * key: pharmacyType-pharmacyNo
         */
        Map<String, QueryGoodsInPharmacyByIdsReq.QueryPharmacyGoodsReq> result = new HashMap<>();

        sheetProcessors
                .forEach(sheetProcessor -> sheetProcessor.generateQueryPharmacyGoodsReqs()
                        .forEach(queryGoodsWithStockPharmacyGoodsReq -> {
                            if (CollectionUtils.isEmpty(queryGoodsWithStockPharmacyGoodsReq.getList())) {
                                return;
                            }

                            List<String> goodsIds = result.computeIfAbsent(String.format("%d-%d", queryGoodsWithStockPharmacyGoodsReq.getPharmacyType(), queryGoodsWithStockPharmacyGoodsReq.getPharmacyNo()), key -> {
                                QueryGoodsInPharmacyByIdsReq.QueryPharmacyGoodsReq queryPharmacyGoodsReq = new QueryGoodsInPharmacyByIdsReq.QueryPharmacyGoodsReq();
                                queryPharmacyGoodsReq.setPharmacyNo(queryGoodsWithStockPharmacyGoodsReq.getPharmacyNo());
                                queryPharmacyGoodsReq.setPharmacyType(queryGoodsWithStockPharmacyGoodsReq.getPharmacyType());
                                queryPharmacyGoodsReq.setGoodsIds(new ArrayList<>());
                                return queryPharmacyGoodsReq;
                            }).getGoodsIds();

                            queryGoodsWithStockPharmacyGoodsReq.getList()
                                    .forEach(queryGoodsWithStock -> {
                                        if (!goodsIds.contains(queryGoodsWithStock.getGoodsId())) {
                                            goodsIds.add(queryGoodsWithStock.getGoodsId());
                                        }
                                    });
                        })

                );

        return new ArrayList<>(result.values());
    }

    /**
     * 住院结算单收费
     *
     * @param payItem
     */
    public PayResult pay(CombinedPayItem payItem, BigDecimal frontReceivablePrice) {


        if (realReceivablePrice.compareTo(BigDecimal.ZERO) < 0) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "realReceivablePrice <= 0:" + realReceivablePrice);
            throw new ChargeServiceException(ChargeServiceError.CHARGE_NO_VALID_RECEIVABLE);
        }

        BigDecimal needPayFee = payItem.dealChangeFee(realReceivablePrice, new ChargeServiceException(ChargeServiceError.CHARGE_FEE_MORE_THAN_RECEIVABLE));

        BigDecimal netIncome = payItem.getNetIncome();

        if (MathUtils.wrapBigDecimalCompare(netIncome, BigDecimal.ZERO) < 0) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "住院结算收费金额错误, netIncome: {}", netIncome);
            throw new ChargeServiceException(ChargeServiceError.CHARGE_HOSPITAL_PAID_AMOUNT_ERROR);
        }

        if (realReceivablePrice.compareTo(netIncome) < 0) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "收费金额大于了应收，realReceivablePrice: {}, netIncome: {}", realReceivablePrice, netIncome);
            throw new ChargeServiceException(ChargeServiceError.CHARGE_HOSPITAL_PAID_AMOUNT_ERROR);
        }

        if (MathUtils.wrapBigDecimalCompare(frontReceivablePrice, realReceivablePrice) != 0) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "收费金额发生了变化，frontReceivablePrice: {}, realReceivablePrice: {}", frontReceivablePrice, realReceivablePrice);
            throw new ChargeServiceException(ChargeServiceError.CHARGE_HOSPITAL_PAID_AMOUNT_CHANGED);
        }

        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "[hospitalSheetProcessor.pay] realReceivablePrice: {}, netIncome: {}", realReceivablePrice, netIncome);


        //调用组合支付
        CombinePayOrderPayResult combinePayOrderPayResult = hospitalSheetProcessorProvider.getHospitalSheetPayProvider().pay(chargeHospitalSheet, payItem, operatorId);

        if (combinePayOrderPayResult == null) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "combinePayOrderPayResult is null");
            throw new ServiceInternalException("pay error");
        }

        if (combinePayOrderPayResult.getPayStatus() == CombinePayOrderPayResult.PayStatus.SUCCESS) {
            writePayTransaction(payItem, needPayFee, operatorId);
        }

        PayResult payResult = new PayResult();
        payResult.setStatus(chargeHospitalSheet.getStatus());
        payResult.setNeedPay(needPayFee);
        payResult.setThirdPartyPayTaskId(combinePayOrderPayResult.getThirdPartyPayTaskId());
        payResult.setCombineOrderPayTransactionId(combinePayOrderPayResult.getCombineOrderPayTransactionId());
        payResult.setPayStatus(combinePayOrderPayResult.getPayStatus());
        payResult.setCombineOrderTransaction(combinePayOrderPayResult.getCombineOrderTransaction());
        payResult.setItems(combinePayOrderPayResult.getItems());
        return payResult;
    }


    /**
     * 住院结算单退款
     *
     * @param payItem
     * @return
     */
    public RefundResult refund(CombinedPayItem payItem) {

        BigDecimal thisTimeRefundFee = payItem.getAmount();

        if (thisTimeRefundFee.compareTo(BigDecimal.ZERO) < 0) { //退费
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "本次退费金额小于0, thisTimeRefundFee: {}", thisTimeRefundFee);
            throw new ChargeServiceException(ChargeServiceError.CHARGE_HOSPITAL_REFUND_AMOUNT_ERROR);
        }


        if (MathUtils.wrapBigDecimalSubtract(canRefundFee, thisTimeRefundFee).compareTo(BigDecimal.ZERO) < 0) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "退费金额超过可退金额, canRefundFee: {}, thisTimeRefundFee: {}", canRefundFee, thisTimeRefundFee);
            throw new ChargeServiceException(ChargeServiceError.CHARGE_HOSPITAL_REFUND_OVERFLOW);
        }

        ThirdPartyRefundResultDto thirdPartyRefundResultDto = hospitalSheetProcessorProvider.getHospitalSheetPayProvider().refund(chargeHospitalSheet, payItem, operatorId);
        if (thirdPartyRefundResultDto.getPayStatus() == CombinePayOrderPayResult.PayStatus.SUCCESS) {
            destroyInvoice();
            writeRefundTransaction(thirdPartyRefundResultDto.getRefundedFee(), operatorId);
        }

        RefundResult refundResult = new RefundResult();
        refundResult.setStatus(chargeHospitalSheet.getStatus());
        refundResult.setRefundFee(thisTimeRefundFee);
        refundResult.setChargePayTransactionId(thirdPartyRefundResultDto.getChargePayTransactionId());
        refundResult.setRefundedFee(chargeHospitalSheet.getRefundedPrice());
        refundResult.setPayStatus(thirdPartyRefundResultDto.getPayStatus());
        refundResult.setThirdPartyPayTaskId(thirdPartyRefundResultDto.getThirdPartyPayTaskId());
        refundResult.setChargeOweCombineTransactions(thirdPartyRefundResultDto.getChargeOweCombineTransactions());
        return refundResult;
    }

    /**
     * 红冲发票
     */
    private void destroyInvoice() {

        int invoiceStatus = chargeHospitalSheet.getInvoiceStatus();

        //退发票
        if (invoiceStatus == ChargeHospitalSheet.InvoiceStatus.INVOICE_SUCCESS && hospitalSheetProcessorProvider != null) {
            hospitalSheetProcessorProvider.getHospitalInvoiceProvider().destroyInvoice(chargeHospitalSheet, operatorId);
            return;
        }


        if (chargeHospitalSheet.getStatus() == ChargeHospitalSheet.Status.REFUNDED
                && (invoiceStatus == Constants.ChargeSheetInvoiceStatus.INVOICE_INVALID || invoiceStatus == Constants.ChargeSheetInvoiceStatus.INVOICE_REFUND)) {
            //如果不需要退发票，则将发票状态置为不可开状态
            chargeHospitalSheet.setInvoiceStatus(ChargeHospitalSheet.InvoiceStatus.INVOICE_WAITING);
        }
    }

    private void writePayTransaction(CombinedPayItem payItem, BigDecimal needPayFee, String operatorId) {
        int status;
        if (needPayFee.compareTo(BigDecimal.ZERO) > 0) {
            status = ChargeHospitalSheet.Status.PART_CHARGED;
        } else {
            status = ChargeHospitalSheet.Status.CHARGED;
            chargeHospitalSheet.setSettleStatus(ChargeHospitalSheet.SettleStatus.SETTLED);
        }

        chargeHospitalSheet.setStatus(status);
        chargeHospitalSheet.setReceivedPrice(MathUtils.wrapBigDecimalAdd(chargeHospitalSheet.getReceivedPrice(), payItem.getNetIncome()));

        //todo long-care 支付成功更新操作人，支付时间
        chargeHospitalSheet.setChargedBy(operatorId);
        chargeHospitalSheet.setChargedTime(Instant.now());
        if (chargeHospitalSheet.getFirstChargedTime() == null) {
            chargeHospitalSheet.setFirstChargedTime(Instant.now());
        }

        FillUtils.fillLastModifiedBy(chargeHospitalSheet, operatorId);
    }

    private void writeRefundTransaction(BigDecimal refundedFee, String operatorId) {

        if (refundedFee.compareTo(BigDecimal.ZERO) > 0) {
            return;
        }

        BigDecimal canRefundFee = this.canRefundFee.add(refundedFee);

        if (canRefundFee.compareTo(BigDecimal.ZERO) > 0) {
            chargeHospitalSheet.setStatus(ChargeHospitalSheet.Status.PART_REFUNDED);
        } else {
            chargeHospitalSheet.setStatus(ChargeHospitalSheet.Status.REFUNDED);
        }

        chargeHospitalSheet.setRefundedPrice(MathUtils.wrapBigDecimalAdd(chargeHospitalSheet.getRefundedPrice(), refundedFee));

        FillUtils.fillLastModifiedBy(chargeHospitalSheet, operatorId);
    }

    private String generatePayModeDisplayName(int payMode, int paySubMode) {

        if (payMode == Constants.ChargePayMode.ABC_PAY) {
            ChargeAction.PayActionInfo payActionInfo = new ChargeAction.PayActionInfo();
            ChargeUtils.buildAbcPaySubModeName(payActionInfo, paySubMode);
            return payActionInfo.getPayModeDisplayName();
        }


        if (hospitalSheetProcessorProvider == null || hospitalSheetProcessorProvider.getChargePayModeProvider() == null) {
            return "";
        }

        Map<Long, ChargePayModeConfigSimple> chargePayModeConfigSimpleMap = hospitalSheetProcessorProvider.getChargePayModeProvider().getChargePayModeConfigSimpleByChainId(chargeHospitalSheet.getChainId());

        if (MapUtils.isEmpty(chargePayModeConfigSimpleMap)) {
            return "";
        }

        return Optional.ofNullable(chargePayModeConfigSimpleMap.getOrDefault((long) payMode, null))
                .map(chargePayModeConfigSimple -> chargePayModeConfigSimple.getName())
                .orElse("");
    }

//    /**
//     * 记账退款
//     *
//     * @param payItem
//     * @return
//     */
//    private RefundResult recordRefund(CombinedPayItem payItem, boolean isPaidBack) {
//
//        payItem.setAmount(payItem.getAmount().negate());
//
//        writeRefundTransaction(payItem, null, null, isPaidBack, operatorId);
//
//        RefundResult refundResult = new RefundResult();
//        refundResult.setStatus(chargeHospitalSheet.getStatus());
//        refundResult.setRefundFee(payItem.getAmount());
//        refundResult.setChargePayTransactionId(null);
//        refundResult.setRefundedFee(chargeHospitalSheet.getRefundedPrice());
//        refundResult.setPayStatus(ChargeHospitalPayTransaction.PayStatus.SUCCESS);
//        refundResult.setThirdPartyPayTaskId(null);
//        refundResult.setThirdPartyPayTaskType(null);
//        return refundResult;
//    }
//
//    /**
//     * 第三方退款
//     *
//     * @param payItem
//     * @return
//     */
//    private RefundResult thirdPartyRefund(CombinedPayItem payItem, boolean isPaidBack) {
//
//        ThirdPartyRefundDto thirdPartyRefundDto = new ThirdPartyRefundDto();
//        thirdPartyRefundDto.setClinicId(chargeHospitalSheet.getClinicId());
//        thirdPartyRefundDto.setChainId(chargeHospitalSheet.getChainId());
//        thirdPartyRefundDto.setPatientId(chargeHospitalSheet.getPatientId());
//        thirdPartyRefundDto.setHospitalSheetId(chargeHospitalSheet.getId());
//        thirdPartyRefundDto.setAmount(payItem.getNetIncome());
//        thirdPartyRefundDto.setOperatorId(operatorId);
//        thirdPartyRefundDto.setPayMode(payItem.getPayMode());
//        thirdPartyRefundDto.setPaySubMode(payItem.getPaySubMode());
//        thirdPartyRefundDto.setHospitalTransactions(chargeHospitalSheet.getHospitalTransactions());
//        thirdPartyRefundDto.setTransactionIds(payItem.getTransactionIds());
//        thirdPartyRefundDto.setIsPaidBack(isPaidBack);
//
//
//        ThirdPartyRefundResultDto thirdPartyRefundResultDto = hospitalSheetProcessorProvider.getHospitalSheetPayProvider().refund(chargeHospitalSheet, payItem, operatorId);
//
//        if (Objects.isNull(thirdPartyRefundResultDto)) {
//            log.error("thirdPartyRefundResultDto is null");
//            throw new ServiceInternalException();
//        }
//
//        BigDecimal frontRefundFee = payItem.getAmount();
//        payItem.setAmount(thirdPartyRefundResultDto.getRefundedFee());
//        payItem.setPrincipalAmount(thirdPartyRefundResultDto.getRefundedPrincipal());
//        payItem.setPresentAmount(thirdPartyRefundResultDto.getRefundedPresent());
//
//        if (thirdPartyRefundResultDto.getPayStatus() == ChargeHospitalPayTransaction.PayStatus.SUCCESS) {
//            writeRefundTransaction(payItem, thirdPartyRefundResultDto.getPayInfo(), thirdPartyRefundResultDto.getAssociateThirdPayTaskId(), isPaidBack, operatorId);
//        }
//
//        RefundResult refundResult = new RefundResult();
//        refundResult.setStatus(chargeHospitalSheet.getStatus());
//        refundResult.setRefundFee(frontRefundFee.negate());
//        refundResult.setChargePayTransactionId(thirdPartyRefundResultDto.getChargePayTransactionId());
//        refundResult.setRefundedFee(chargeHospitalSheet.getRefundedPrice());
//        refundResult.setPayStatus(thirdPartyRefundResultDto.getPayStatus());
//        refundResult.setThirdPartyPayTaskId(thirdPartyRefundResultDto.getThirdPayTaskId());
//        refundResult.setThirdPartyPayTaskType(thirdPartyRefundResultDto.getThirdPayTaskId() != null ? 1 : null);
//        return refundResult;
//    }

    /**
     * 支付回调入账
     *
     * @param payItem
     */
    public void payCallback(CombinedPayItem payItem) {
        BigDecimal netIncome = payItem.getNetIncome();

        if (MathUtils.wrapBigDecimalCompare(netIncome, BigDecimal.ZERO) < 0) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "住院结算收费金额错误, netIncome: {}", netIncome);
            throw new ChargeServiceException(ChargeServiceError.CHARGE_HOSPITAL_PAID_AMOUNT_ERROR);
        }

        if (realReceivablePrice.compareTo(BigDecimal.ZERO) < 0) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "realReceivablePrice <= 0:" + realReceivablePrice);
            throw new ChargeServiceException(ChargeServiceError.CHARGE_NO_VALID_RECEIVABLE);
        }

        if (realReceivablePrice.compareTo(netIncome) < 0) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "收费金额大于了应收，realReceivablePrice: {}, netIncome: {}", realReceivablePrice, netIncome);
            throw new ChargeServiceException(ChargeServiceError.CHARGE_HOSPITAL_PAID_AMOUNT_ERROR);
        }

        BigDecimal needPayFee = realReceivablePrice.subtract(payItem.getNetIncome());

        writePayTransaction(payItem, needPayFee, operatorId);
    }

    /**
     * 退款回调入账
     *
     * @param thisTimeRefundFee
     */
    public void refundCallback(BigDecimal thisTimeRefundFee) {

        if (thisTimeRefundFee.compareTo(BigDecimal.ZERO) < 0) { //退费
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "本次退费金额小于0, thisTimeRefundFee: {}", thisTimeRefundFee);
            throw new ChargeServiceException(ChargeServiceError.CHARGE_HOSPITAL_REFUND_AMOUNT_ERROR);
        }

        if (MathUtils.wrapBigDecimalSubtract(canRefundFee, thisTimeRefundFee).compareTo(BigDecimal.ZERO) < 0) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "退费金额超过可退金额, canRefundFee: {}, thisTimeRefundFee: {}", canRefundFee, thisTimeRefundFee);
            throw new ChargeServiceException(ChargeServiceError.CHARGE_HOSPITAL_REFUND_OVERFLOW);
        }
        destroyInvoice();
        writeRefundTransaction(thisTimeRefundFee.negate(), operatorId);
    }
}
