package cn.abcyun.cis.charge.hospital.dto;

import cn.abcyun.bis.rpc.sdk.cis.model.patientorder.HospitalPatientOrderVO;
import cn.abcyun.bis.rpc.sdk.cis.model.shebao.QueryChargeSheetLongCareInfoRsp;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.cis.commons.rpc.crm.PatientInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

@Data
@Accessors(chain = true)
public class ChargeHospitalSheetPrintBaseView {

    @ApiModelProperty(value = "长护单id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "患者信息")
    private PatientInfo patient;

    @ApiModelProperty(value = "总金额")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "已收金额")
    private BigDecimal receivedPrice;       //实收

    @ApiModelProperty(value = "长护单住院信息")
    private HospitalPatientOrderVO hospitalPatientOrder;

    @ApiModelProperty(value = "最后一次收费的时间")
    private Instant lastChargedTime;

    @ApiModelProperty(value = "最后的收费人名称")
    private String lastChargedByName;

    @ApiModelProperty(value = "社保支付明细")
    private LongCarePaymentExtend shebaoPayment;

    @ApiModelProperty(value = "机构代码")
    private String hospitalCode;

    @JsonIgnore
    private List<QueryChargeSheetLongCareInfoRsp.GoodsItem> shebaoGoodsItems;

    @Data
    @Accessors(chain = true)
    @EqualsAndHashCode(callSuper = true)
    public static class LongCarePaymentExtend extends QueryChargeSheetLongCareInfoRsp.LongCarePayment {
        /**
         * 个人现金支付
         */
        private BigDecimal personalPaymentFee;

        public static LongCarePaymentExtend ofLongCarePaymentExtend(QueryChargeSheetLongCareInfoRsp.LongCarePayment longCarePayment, BigDecimal receivedPrice){
            if (longCarePayment == null) {
                return null;
            }
            LongCarePaymentExtend extend = new LongCarePaymentExtend();
            BeanUtils.copyProperties(longCarePayment, extend);
            extend.setPersonalPaymentFee(MathUtils.wrapBigDecimalSubtract(receivedPrice, longCarePayment.getFundTotalAmount()));
            return extend;
        }
    }
}
