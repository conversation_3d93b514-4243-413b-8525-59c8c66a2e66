package cn.abcyun.cis.charge.hospital.service;

import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Employee;
import cn.abcyun.bis.rpc.sdk.cis.model.dispensing.DispensingFormItem;
import cn.abcyun.bis.rpc.sdk.cis.model.dispensing.DispensingFormItemQuantityInfo;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsItem;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.QueryGoodsInPharmacyByIdsReq;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.QueryGoodsInPharmacyByIdsRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.patientorder.HospitalPatientOrderVO;
import cn.abcyun.bis.rpc.sdk.cis.model.shebao.LongCareChargeResultFormItemView;
import cn.abcyun.bis.rpc.sdk.cis.model.shebao.QueryLongCareOutpatientChargeSheetRsp;
import cn.abcyun.cis.charge.api.model.BaseSuccessRsp;
import cn.abcyun.cis.charge.base.ChargeHospitalSheetCanNotSettleException;
import cn.abcyun.cis.charge.base.ChargeServiceError;
import cn.abcyun.cis.charge.base.ChargeServiceException;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.base.pay.CombinedPayItem;
import cn.abcyun.cis.charge.combinorder.dto.CombineOrderBusinessPayCallbackReq;
import cn.abcyun.cis.charge.combinorder.dto.CombineOrderBusinessPayCallbackRsp;
import cn.abcyun.cis.charge.combinorder.dto.CombineOrderTransactionView;
import cn.abcyun.cis.charge.combinorder.dto.CombinePayOrderPayResult;
import cn.abcyun.cis.charge.controller.api.ChargeFormItemMerger;
import cn.abcyun.cis.charge.hospital.api.model.*;
import cn.abcyun.cis.charge.hospital.dto.ChargeHospitalItemNotValidView;
import cn.abcyun.cis.charge.hospital.dto.PayResult;
import cn.abcyun.cis.charge.hospital.dto.RefundResult;
import cn.abcyun.cis.charge.hospital.model.ChargeHospitalPayTransaction;
import cn.abcyun.cis.charge.hospital.model.ChargeHospitalSheet;
import cn.abcyun.cis.charge.hospital.processor.HospitalSheetProcessor;
import cn.abcyun.cis.charge.hospital.repository.ChargeHospitalPayTransactionRepository;
import cn.abcyun.cis.charge.hospital.repository.ChargeHospitalSheetRepository;
import cn.abcyun.cis.charge.hospital.utils.HospitalSheetUtil;
import cn.abcyun.cis.charge.mapper.ChargeMapper;
import cn.abcyun.cis.charge.model.ChargeFormItem;
import cn.abcyun.cis.charge.model.ChargeOweCombineTransaction;
import cn.abcyun.cis.charge.model.ChargeOweSheet;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.processor.SheetProcessor;
import cn.abcyun.cis.charge.repository.ChargeOweSheetRepository;
import cn.abcyun.cis.charge.repository.ChargeSheetRepository;
import cn.abcyun.cis.charge.service.ChargeOweSheetService;
import cn.abcyun.cis.charge.service.ChargeSheetService;
import cn.abcyun.cis.charge.service.EmployeeService;
import cn.abcyun.cis.charge.service.SheetProcessorService;
import cn.abcyun.cis.charge.service.dto.ChargeSheetExtend;
import cn.abcyun.cis.charge.service.dto.ChargeSheetForHospitalView;
import cn.abcyun.cis.charge.service.dto.DTOConverter;
import cn.abcyun.cis.charge.service.rpc.CisDispensingService;
import cn.abcyun.cis.charge.service.rpc.CisPatientOrderService;
import cn.abcyun.cis.charge.service.rpc.CisScGoodsService;
import cn.abcyun.cis.charge.service.rpc.CisShebaoService;
import cn.abcyun.cis.charge.util.ChargeUtils;
import cn.abcyun.cis.charge.util.FillUtils;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.model.ComposeType;
import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.cis.commons.util.ListUtils;
import cn.abcyun.cis.core.db.UseReadOnlyDB;
import cn.abcyun.cis.core.redis.RedisLock;
import cn.abcyun.cis.idgenerator.AbcIdGenerator;
import cn.abcyun.common.log.marker.AbcLogMarker;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ChargeHospitalService {

    @Autowired
    private ChargeHospitalSheetRepository chargeHospitalSheetRepository;
    @Autowired
    private HospitalSheetProcessorProvider hospitalSheetProcessorProvider;
    @Autowired
    private ChargeMapper chargeMapper;
    @Autowired
    private ChargeSheetService chargeSheetService;
    @Autowired
    private SheetProcessorService sheetProcessorService;
    @Autowired
    private ChargeSheetRepository chargeSheetRepository;
    @Autowired
    private CisScGoodsService cisScGoodsService;
    @Autowired
    private CisDispensingService cisDispensingService;
    @Autowired
    private CisPatientOrderService cisPatientOrderService;
    @Autowired
    private ChargeOweSheetService chargeOweSheetService;
    @Autowired
    private ChargeHospitalPayTransactionRepository chargeHospitalPayTransactionRepository;
    @Autowired
    private ChargeOweSheetRepository chargeOweSheetRepository;
    @Autowired
    private AbcIdGenerator abcIdGenerator;
    @Autowired
    private EmployeeService employeeService;
    @Autowired
    private CisShebaoService cisShebaoService;



    public ChargeHospitalSheet findByIdAndClinicId(Long id, String clinicId) {

        if (Objects.isNull(id) || StringUtils.isEmpty(clinicId)) {
            return null;
        }

        ChargeHospitalSheet chargeHospitalSheet = chargeHospitalSheetRepository.findByIdAndClinicIdAndIsDeleted(id, clinicId, 0);

        if (Objects.isNull(chargeHospitalSheet)) {
            return null;
        }

        return chargeHospitalSheet;

    }

    /**
     * 支付核心方法
     *
     * @param hospitalSheetId
     * @param payChargeHospitalSheetReq
     * @param clinicId
     * @param operatorId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @RedisLock(key = "'charge.hospitalSheet.paid:' + #hospitalSheetId", waitTime = 5)
    public PayChargeHospitalSheetRsp putChargeHospitalSheetPaid(Long hospitalSheetId, PayChargeHospitalSheetReq payChargeHospitalSheetReq, String clinicId, String operatorId) {

        ChargeHospitalSheet chargeHospitalSheet = findByIdAndClinicId(hospitalSheetId, clinicId);

        checkChargeHospitalSheet(hospitalSheetId, chargeHospitalSheet);

        HospitalSheetProcessor hospitalSheetProcessor = new HospitalSheetProcessor(chargeHospitalSheet, hospitalSheetProcessorProvider, operatorId);
        PayResult payResult = hospitalSheetProcessor.pay(payChargeHospitalSheetReq.getPayItem(), payChargeHospitalSheetReq.getReceivableFee());

        PayChargeHospitalSheetRsp rsp = new PayChargeHospitalSheetRsp();
        return rsp.setId(chargeHospitalSheet.getId())
                .setHospitalOrderId(chargeHospitalSheet.getHospitalOrderId())
                .setStatus(payResult.getStatus())
                .setNeedPay(payResult.getNeedPay())
                .setPayStatus(payResult.getPayStatus())
                .setThirdPartyPayTaskId(payResult.getThirdPartyPayTaskId())
                .setCombineOrderPayTransactionId(payResult.getCombineOrderPayTransactionId())
                .setPayStatus(payResult.getPayStatus());
    }

//    @Transactional(rollbackFor = Exception.class)
//    @RedisLock(key = "'charge.hospitalSheet.paidBack:' + #hospitalSheetId", waitTime = 5)
//    public RefundChargeHospitalSheetRsp putChargeHospitalSheetPaidBack(long hospitalSheetId, RefundChargeHospitalSheetReq refundChargeHospitalSheetReq, String clinicId, String employeeId) {
//
//        ChargeHospitalSheet chargeHospitalSheet = chargeHospitalSheetRepository.findByIdAndClinicIdAndIsDeleted(hospitalSheetId, clinicId, 0);
//
//        if (Objects.isNull(chargeHospitalSheet)) {
//            log.info("住院结算单不存在，hospitalSheetId: {}", hospitalSheetId);
//            throw new NotFoundException();
//        }
//
//        if (chargeHospitalSheet.getStatus() != ChargeHospitalSheet.Status.PART_CHARGED) {
//            log.info("chargeHospitalSheet状态不能进行退费, status: {}", chargeHospitalSheet.getStatus());
//            throw new ChargeServiceException(ChargeServiceError.CHARGE_HOSPITAL_SHEET_PAY_STATUS_ERROR);
//        }
//
//        HospitalSheetProcessor hospitalSheetProcessor = new HospitalSheetProcessor(chargeHospitalSheet, hospitalSheetProcessorProvider, employeeId);
//        RefundResult refundResult = hospitalSheetProcessor.refund(refundChargeHospitalSheetReq.getPayItem(), true);
//
//        //如果退费成功，修改收费单状态为欠费
////        if (refundResult.getPayStatus() == ChargeHospitalPayTransaction.PayStatus.SUCCESS) {
////            chargeSheetRepository.updateChargeSheetOwedStatusToOwed(chargeHospitalSheet.getClinicId(), chargeHospitalSheet.getId());
////        }
//
//        RefundChargeHospitalSheetRsp rsp = new RefundChargeHospitalSheetRsp();
//        return rsp.setId(chargeHospitalSheet.getId())
//                .setStatus(refundResult.getStatus())
//                .setRefundFee(refundResult.getRefundFee())
//                .setRefundedFee(refundResult.getRefundedFee())
//                .setChargePayTransactionId(refundResult.getChargePayTransactionId())
//                .setPayStatus(refundResult.getPayStatus())
//                .setThirdPartyPayTaskId(refundResult.getThirdPartyPayTaskId())
//                .setThirdPartyPayTaskType(refundResult.getThirdPartyPayTaskType());
//    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public BaseSuccessRsp checkChargeHospitalSheetCanPaid(long hospitalSheetId, String clinicId) {

        ChargeHospitalSheet chargeHospitalSheet = chargeHospitalSheetRepository.findByIdAndClinicIdAndIsDeleted(hospitalSheetId, clinicId, 0);

        checkChargeHospitalSheetCanPaidOrThrowException(chargeHospitalSheet);

        return new BaseSuccessRsp(BaseSuccessRsp.Code.SUCCESS, "校验通过");
    }

    public void checkChargeHospitalSheet(long hospitalSheetId, ChargeHospitalSheet chargeHospitalSheet) {
        if (Objects.isNull(chargeHospitalSheet)) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "住院结算单不存在，hospitalSheetId: {}", hospitalSheetId);
            throw new NotFoundException();
        }

        if (chargeHospitalSheet.getStatus() != ChargeHospitalSheet.Status.UNCHARGED && chargeHospitalSheet.getStatus() != ChargeHospitalSheet.Status.PART_CHARGED) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeHospitalSheet支付状态已经不是待支付或部分支付，不能进行付费, status: {}", chargeHospitalSheet.getStatus());
            throw new ChargeServiceException(ChargeServiceError.CHARGE_HOSPITAL_SHEET_PAY_STATUS_ERROR);
        }

        if (chargeHospitalSheet.getSettleStatus() == ChargeHospitalSheet.SettleStatus.WAIT_SETTLE) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "结算单还未发起结算，不能进行收费");
            throw new ChargeServiceException(ChargeServiceError.CHARGE_HOSPITAL_SHEET_NOT_APPLY_SETTLE);
        }

        if (chargeHospitalSheet.getSettleStatus() == ChargeHospitalSheet.SettleStatus.SETTLED) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "结算单已结算，不能进行收费");
            throw new ChargeServiceException(ChargeServiceError.CHARGE_HOSPITAL_SHEET_IS_SETTLED);
        }

        //校验结算单是否可以结算
        checkChargeHospitalSheetCanPaidOrThrowException(chargeHospitalSheet);
    }

    /**
     * 校验结算单是否可以结算
     *
     * @param chargeHospitalSheet
     */
    public void checkChargeHospitalSheetCanPaidOrThrowException(ChargeHospitalSheet chargeHospitalSheet) {

        if (Objects.isNull(chargeHospitalSheet)) {
            return;
        }

        if (chargeHospitalSheet.getStatus() != ChargeHospitalSheet.Status.UNCHARGED) {
            return;
        }

        List<ChargeSheet> chargeSheets = chargeSheetService.findAllByHospitalSheetId(chargeHospitalSheet.getChainId(), chargeHospitalSheet.getClinicId(), chargeHospitalSheet.getId());

        if (CollectionUtils.isEmpty(chargeSheets)) {
            return;
        }


        List<ChargeSheet> memoryChargeSheets = chargeSheets.stream().map(chargeSheet -> {
            ChargeSheet memoryChargeSheet = ChargeUtils.deepCopyChargeSheet(chargeSheet);
            ChargeFormItemMerger.mergeForChargeSheetForHospital(memoryChargeSheet);
            return memoryChargeSheet;
        }).collect(Collectors.toList());


        List<String> chargeSheetIds = memoryChargeSheets.stream().map(ChargeSheet::getId).distinct().collect(Collectors.toList());

        //找出所有需要发药的且已收费的收费项
        List<ChargeFormItem> chargedItems = memoryChargeSheets.stream()
                .filter(chargeSheet -> CollectionUtils.isNotEmpty(chargeSheet.getChargeForms()))
                .flatMap(chargeSheet -> chargeSheet.getChargeForms().stream())
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> CollectionUtils.isNotEmpty(chargeForm.getChargeFormItems()))
                .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                .filter(chargeFormItem -> chargeFormItem.getProductType() == Constants.ProductType.MEDICINE
                        || chargeFormItem.getProductType() == Constants.ProductType.MATERIAL
                        || chargeFormItem.getProductType() == Constants.ProductType.SALE_PRODUCT)
                .filter(item -> item.getIsDeleted() == 0)
                .filter(chargeFormItem -> chargeFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED)
                .filter(chargeFormItem -> MathUtils.wrapBigDecimalCompare(chargeFormItem.getUnitCount(), BigDecimal.ZERO) > 0)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(chargedItems)) {
            return;
        }

        List<DispensingFormItemQuantityInfo> dispensingFormItems = cisDispensingService.queryDispensingFormItemQuantityInfos(chargeHospitalSheet.getChainId(), chargeHospitalSheet.getClinicId(), chargeSheetIds);

        //未发药的收费项
        List<ChargeFormItem> unDispenseChargeFormItems = new ArrayList<>();

        //已退药但是未退费的收费项
        List<ChargeFormItem> notRefundChargeFormItems = new ArrayList<>();

        if (CollectionUtils.isEmpty(dispensingFormItems)) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "dispensingFormItems is null, but chargedItems is not null, chargedItems: {}", JsonUtils.dump(chargedItems));

            throw new ChargeHospitalSheetCanNotSettleException(chargedItems.stream()
                    .map(chargeFormItem -> ChargeHospitalItemNotValidView.ofChargeHospitalItemNotValidView(chargeFormItem, ChargeHospitalItemNotValidView.Status.UN_DISPENSED))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList())
            );
        }

        Map<String, DispensingFormItemQuantityInfo> dispensingFormItemsMap = dispensingFormItems.stream()
                .filter(dispensingFormItem -> StringUtils.isNotEmpty(dispensingFormItem.getSourceFormItemId()))
                .collect(Collectors.toMap(DispensingFormItemQuantityInfo::getSourceFormItemId, Function.identity(), (a, b) -> a));

        //过滤出未发药的收费项和已退费未退费的收费项
        chargedItems.forEach(chargeFormItem -> {

            DispensingFormItemQuantityInfo dispensingFormItem = dispensingFormItemsMap.getOrDefault(chargeFormItem.getId(), null);

            if (Objects.isNull(dispensingFormItem)) {
                unDispenseChargeFormItems.add(chargeFormItem);
                return;
            }

            if (dispensingFormItem.getStatus() == DispensingFormItem.Status.WAITING) {
                unDispenseChargeFormItems.add(chargeFormItem);
                return;
            }

            if (dispensingFormItem.getStatus() == DispensingFormItem.Status.UNDISPENSED || dispensingFormItem.getStatus() == DispensingFormItem.Status.CANCELED) {
                notRefundChargeFormItems.add(chargeFormItem);
                return;
            }

            if (dispensingFormItem.getStatus() == DispensingFormItem.Status.DISPENSED) {
                //没退费
                if (MathUtils.wrapBigDecimalOrZero(dispensingFormItem.getUndispensedDoseCount()).compareTo(BigDecimal.ZERO) == 0 && MathUtils.wrapBigDecimalOrZero(dispensingFormItem.getUndispensedUnitCount()).compareTo(BigDecimal.ZERO) == 0) {
                    return;
                }

                //部分退药的情况
                if (dispensingFormItem.getUndispenseType() == DispensingFormItem.UndispenseType.BY_UNIT) {
                    BigDecimal leftUnitCount = MathUtils.wrapBigDecimalSubtract(chargeFormItem.getUnitCount(), MathUtils.wrapBigDecimalSubtract(dispensingFormItem.getUnitCount(), dispensingFormItem.getUndispensedUnitCount()));

                    if (MathUtils.wrapBigDecimalCompare(leftUnitCount, BigDecimal.ZERO) > 0) {
                        chargeFormItem.setUnitCount(leftUnitCount);
                        notRefundChargeFormItems.add(chargeFormItem);
                        return;
                    }
                    return;
                } else if (dispensingFormItem.getUndispenseType() == DispensingFormItem.UndispenseType.BY_DOSE) {
                    BigDecimal leftDoseCount = MathUtils.wrapBigDecimalSubtract(chargeFormItem.getDoseCount(), MathUtils.wrapBigDecimalSubtract(dispensingFormItem.getDoseCount(), dispensingFormItem.getUndispensedDoseCount()));

                    if (MathUtils.wrapBigDecimalCompare(leftDoseCount, BigDecimal.ZERO) > 0) {
                        chargeFormItem.setDoseCount(leftDoseCount);
                        notRefundChargeFormItems.add(chargeFormItem);
                        return;
                    }
                    return;
                }
            }
        });

        List<ChargeHospitalItemNotValidView> chargeHospitalItemNotValidViews = new ArrayList<>();
        chargeHospitalItemNotValidViews.addAll(Optional.ofNullable(unDispenseChargeFormItems)
                .orElse(new ArrayList<>())
                .stream()
                .map(chargeFormItem -> ChargeHospitalItemNotValidView.ofChargeHospitalItemNotValidView(chargeFormItem, ChargeHospitalItemNotValidView.Status.WAITING_DISPENSE))
                .filter(Objects::nonNull)
                .collect(Collectors.toList()));

        chargeHospitalItemNotValidViews.addAll(Optional.ofNullable(notRefundChargeFormItems)
                .orElse(new ArrayList<>())
                .stream()
                .map(chargeFormItem -> ChargeHospitalItemNotValidView.ofChargeHospitalItemNotValidView(chargeFormItem, ChargeHospitalItemNotValidView.Status.UN_DISPENSED))
                .filter(Objects::nonNull)
                .collect(Collectors.toList()));

        if (CollectionUtils.isEmpty(chargeHospitalItemNotValidViews)) {
            return;
        }

        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeHospitalItemNotValidViews: {}", JsonUtils.dump(chargeHospitalItemNotValidViews));
        throw new ChargeHospitalSheetCanNotSettleException(chargeHospitalItemNotValidViews);
    }

    /**
     * 退费核心方法
     *
     * @param hospitalSheetId
     * @param refundChargeHospitalSheetReq
     * @param clinicId
     * @param employeeId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @RedisLock(key = "'charge.hospitalSheet.refund:'+ #hospitalSheetId", waitTime = 5)
    public RefundChargeHospitalSheetRsp putChargeHospitalSheetRefund(long hospitalSheetId, RefundChargeHospitalSheetReq refundChargeHospitalSheetReq, String clinicId, String employeeId) {

        ChargeHospitalSheet chargeHospitalSheet = findByIdAndClinicId(hospitalSheetId, clinicId);

        if (Objects.isNull(chargeHospitalSheet)) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "住院结算单不存在，hospitalSheetId: {}", hospitalSheetId);
            throw new NotFoundException();
        }

        if (chargeHospitalSheet.getStatus() != ChargeHospitalSheet.Status.CHARGED && chargeHospitalSheet.getStatus() != ChargeHospitalSheet.Status.PART_REFUNDED) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeHospitalSheet状态不能进行退费, status: {}", chargeHospitalSheet.getStatus());
            throw new ChargeServiceException(ChargeServiceError.CHARGE_HOSPITAL_SHEET_PAY_STATUS_ERROR);
        }

        HospitalSheetProcessor hospitalSheetProcessor = new HospitalSheetProcessor(chargeHospitalSheet, hospitalSheetProcessorProvider, employeeId);
        RefundResult refundResult = hospitalSheetProcessor.refund(refundChargeHospitalSheetReq.getPayItem());

        List<ChargeOweCombineTransaction> memoryChargeOweCombineTransactions = new ArrayList<>();
        memoryChargeOweCombineTransactions.addAll(chargeHospitalSheet.getChargeOweCombineTransactions()
                .stream()
                .map(transaction -> {
                    ChargeOweCombineTransaction memoryTransaction = new ChargeOweCombineTransaction();
                    BeanUtils.copyProperties(transaction, memoryTransaction, "transactionRecords");
                    return memoryTransaction;
                }).collect(Collectors.toList())
        );
        memoryChargeOweCombineTransactions.addAll(Optional.ofNullable(refundResult.getChargeOweCombineTransactions()).orElse(new ArrayList<>())
                .stream()
                .map(transaction -> {
                    ChargeOweCombineTransaction memoryTransaction = new ChargeOweCombineTransaction();
                    BeanUtils.copyProperties(transaction, memoryTransaction, "transactionRecords");
                    return memoryTransaction;
                }).collect(Collectors.toList())
        );

        RefundChargeHospitalSheetRsp rsp = new RefundChargeHospitalSheetRsp();
        return rsp.setId(chargeHospitalSheet.getId())
                .setStatus(refundResult.getStatus())
                .setRefundFee(refundResult.getRefundFee())
                .setRefundedFee(refundResult.getRefundedFee())
                .setChargePayTransactionId(refundResult.getChargePayTransactionId())
                .setPayStatus(refundResult.getPayStatus())
                .setThirdPartyPayTaskId(refundResult.getThirdPartyPayTaskId())
                .setThirdPartyPayTaskType(refundResult.getThirdPartyPayTaskType())
                .setPaymentSummaryInfos(HospitalSheetUtil.generatePaymentSummaryInfos(memoryChargeOweCombineTransactions));
    }

    @Transactional(rollbackFor = Exception.class)
    public PayChargeHospitalSheetRsp putChargeHospitalSheetRenew(long hospitalSheetId, String clinicId, String operatorId) {

        return putChargeHospitalSheetRenew(hospitalSheetId, clinicId, operatorId, ChargeHospitalSheet.SettleStatus.SETTLING);
    }

    public PayChargeHospitalSheetRsp putChargeHospitalSheetRenew(long hospitalSheetId, String clinicId, String operatorId, int settleStatus) {
        ChargeHospitalSheet chargeHospitalSheet = chargeHospitalSheetRepository.findByIdAndClinicIdAndIsDeleted(hospitalSheetId, clinicId, 0);

        if (Objects.isNull(chargeHospitalSheet)) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "住院结算单不存在，hospitalSheetId: {}", hospitalSheetId);
            throw new NotFoundException();
        }

        if (chargeHospitalSheet.getStatus() != ChargeHospitalSheet.Status.REFUNDED) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeHospitalSheet状态不是已退，不能重置为待收, status: {}", chargeHospitalSheet.getStatus());
            throw new ChargeServiceException(ChargeServiceError.CHARGE_HOSPITAL_SHEET_PAY_STATUS_ERROR);
        }

        HospitalSheetUtil.renewHospitalSheet(chargeHospitalSheet, operatorId, settleStatus);

        chargeSheetRepository.updateChargeSheetOwedStatusToOwed(chargeHospitalSheet.getClinicId(), chargeHospitalSheet.getId());
        renewChargeOweSheetForHospital(chargeHospitalSheet, operatorId);

        return new PayChargeHospitalSheetRsp()
                .setId(chargeHospitalSheet.getId())
                .setStatus(chargeHospitalSheet.getStatus())
                .setNeedPay(chargeHospitalSheet.getTotalPrice())
                .setReceivedFee(BigDecimal.ZERO)
                .setNetIncomeFee(BigDecimal.ZERO);
    }

    private void renewChargeOweSheetForHospital(ChargeHospitalSheet chargeHospitalSheet, String operatorId) {

        if (Objects.isNull(chargeHospitalSheet)) {
            return;
        }

        List<ChargeOweSheet> oldChargeOweSheets = chargeOweSheetRepository.findAllByChargeHospitalSheetIdAndClinicId(chargeHospitalSheet.getId(), chargeHospitalSheet.getClinicId());
        oldChargeOweSheets = new ArrayList<>(Optional.ofNullable(oldChargeOweSheets).orElse(new ArrayList<>())
                .stream()
                .filter(chargeOweSheet -> chargeOweSheet.getIsOldRecord() == 0)
                .collect(Collectors.toMap(ChargeOweSheet::getChargeSheetId, Function.identity(), (a, b) -> a))
                .values());

        if (CollectionUtils.isEmpty(oldChargeOweSheets)) {
            return;
        }

        //将老的欠费单标记为old
        chargeOweSheetRepository.markOweSheetAsOldRecordForHospital(
                oldChargeOweSheets.stream()
                        .map(ChargeOweSheet::getId)
                        .collect(Collectors.toList())
        );

        //新增收费单对应的欠费记录
        List<ChargeOweSheet> chargeOweSheets = oldChargeOweSheets
                .stream()
                .map(oldChargeOweSheet -> {
                    ChargeOweSheet chargeOweSheet = new ChargeOweSheet();
                    chargeOweSheet.setId(abcIdGenerator.getUIDLong())
                            .setChargeSheetId(oldChargeOweSheet.getChargeSheetId())
                            .setChainId(oldChargeOweSheet.getChainId())
                            .setClinicId(oldChargeOweSheet.getClinicId())
                            .setPatientId(oldChargeOweSheet.getPatientId())
                            .setReceivedPrice(BigDecimal.ZERO)
                            .setRefundedPrice(BigDecimal.ZERO)
                            .setTotalPrice(oldChargeOweSheet.getTotalPrice())
                            .setStatus(ChargeOweSheet.Status.UNCHARGED);
                    FillUtils.fillCreatedBy(chargeOweSheet, operatorId);
                    return chargeOweSheet;
                }).collect(Collectors.toList());

        chargeOweSheetRepository.saveAll(chargeOweSheets);

    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public HospitalSheetFrontView pageListChargeSheetsByHospitalSheetId(long hospitalSheetId, String clinicId, int offset, int limit) {

        ChargeHospitalSheet chargeHospitalSheet = chargeHospitalSheetRepository.findByIdAndClinicIdAndIsDeleted(hospitalSheetId, clinicId, 0);

        if (Objects.isNull(chargeHospitalSheet)) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "住院结算单不存在，hospitalSheetId: {}", hospitalSheetId);
            throw new NotFoundException();
        }

        HospitalSheetFrontView hospitalSheetFrontView = new HospitalSheetFrontView();
        BeanUtils.copyProperties(chargeHospitalSheet, hospitalSheetFrontView);
        hospitalSheetFrontView.setHospitalTransactions(HospitalSheetUtil.generateChargeHospitalTransactionViews(chargeHospitalSheet.getChargeOweCombineTransactions()));
        translateChargedByName(hospitalSheetFrontView);
        appendPageData(hospitalSheetId, clinicId, offset, limit, hospitalSheetFrontView);
        appendShebaoLongCareOutpatientChargeSheetItems(hospitalSheetFrontView);
        bindItemSelfPayRatio(hospitalSheetFrontView);
        hospitalSheetFrontView.setHospitalSheetSummary(HospitalSheetUtil.generateHospitalSheetSummary(chargeHospitalSheet, hospitalSheetFrontView.getLongCareOutpatientChargeSheet()));

        return hospitalSheetFrontView;
    }

    private void bindItemSelfPayRatio(HospitalSheetFrontView hospitalSheetFrontView) {

        if (hospitalSheetFrontView.getStatus() == ChargeHospitalSheet.Status.UNCHARGED) {
            return;
        }

        Map<String, BigDecimal> chargeFormItemIdConceitRatioMap = Optional.ofNullable(cisShebaoService.queryLongCarePaymentResultItems(hospitalSheetFrontView.getChainId(), hospitalSheetFrontView.getClinicId(), String.valueOf(hospitalSheetFrontView.getHospitalOrderId())))
                .orElse(new ArrayList<>())
                .stream()
                .filter(longCareChargeResultFormItemView -> org.apache.commons.lang3.StringUtils.isNotEmpty(longCareChargeResultFormItemView.getSourceFormItemId())
                        && Objects.nonNull(longCareChargeResultFormItemView.getSelfPayRatio()))
                .collect(Collectors.toMap(LongCareChargeResultFormItemView::getSourceFormItemId, LongCareChargeResultFormItemView::getSelfPayRatio, (a, b) -> a));

        hospitalSheetFrontView.getRows()
                .stream()
                .flatMap(chargeSheetForHospitalView -> Optional.ofNullable(chargeSheetForHospitalView.getChargeForms()).orElse(new ArrayList<>()).stream())
                .flatMap(chargeFormView -> Optional.ofNullable(chargeFormView.getChargeFormItems()).orElse(new ArrayList<>()).stream())
                .forEach(chargeFormItemView -> chargeFormItemView.setShebaoSelfPayRatio(chargeFormItemIdConceitRatioMap.get(chargeFormItemView.getId())));

    }

    private void appendShebaoLongCareOutpatientChargeSheetItems(HospitalSheetFrontView hospitalSheetFrontView) {

        if (Objects.isNull(hospitalSheetFrontView)) {
            return;
        }

//        只有第一页时才查询社保的外诊列表
//        if (hospitalSheetFrontView.getOffset() == null || hospitalSheetFrontView.getOffset() != 0) {
//            return;
//        }


        QueryLongCareOutpatientChargeSheetRsp queryLongCareOutpatientChargeSheetRsp = cisShebaoService.queryLongCareOutpatientChargeSheetItems(hospitalSheetFrontView.getChainId(), hospitalSheetFrontView.getClinicId(), String.valueOf(hospitalSheetFrontView.getHospitalOrderId()));

        if (queryLongCareOutpatientChargeSheetRsp != null)
            hospitalSheetFrontView.setLongCareOutpatientChargeSheet(queryLongCareOutpatientChargeSheetRsp);

    }

    private void translateChargedByName(HospitalSheetFrontView hospitalSheetFrontView) {
        Set<String> ids = new HashSet<>();
        ids.add(hospitalSheetFrontView.getChargedBy());
        ids.addAll(hospitalSheetFrontView.getHospitalTransactions().stream().map(item -> item.getChargedBy()).collect(Collectors.toList()));

        List<Employee> employeeList = employeeService.findEmployeeList(hospitalSheetFrontView.getChainId(), new ArrayList<>(ids));

        hospitalSheetFrontView.setChargedBy(employeeService.findNameInEmployeeList(employeeList, hospitalSheetFrontView.getChargedBy()));

        hospitalSheetFrontView.getHospitalTransactions().stream().forEach(item -> {
            item.setChargedBy(employeeService.findNameInEmployeeList(employeeList, item.getChargedBy()));
        });
    }

    private void appendPageData(long hospitalSheetId, String clinicId, int offset, int limit, HospitalSheetFrontView hospitalSheetFrontView) {
        hospitalSheetFrontView.setOffset(offset);
        hospitalSheetFrontView.setLimit(limit);
        hospitalSheetFrontView.setRows(Lists.newArrayList());
        hospitalSheetFrontView.setTotal(0);

        int totalCount = chargeMapper.countByHospitalSheetId(hospitalSheetId, clinicId, offset, limit);
        hospitalSheetFrontView.setTotal(totalCount);
        if (hospitalSheetFrontView.getTotal() <= 0) {
            return;
        }

        List<String> chargeSheetIds = chargeMapper.pageListChargeSheetIdsByHospitalSheetId(hospitalSheetId, hospitalSheetFrontView.getChainId(), clinicId, offset, limit);

        List<ChargeSheetForHospitalView> chargeSheetForHospitalViews = listChargeSheetForHospitalViewByChargeSheetIds(chargeSheetIds);
        hospitalSheetFrontView.setRows(chargeSheetForHospitalViews);
    }

    private List<ChargeSheetForHospitalView> listChargeSheetForHospitalViewByChargeSheetIds(List<String> chargeSheetIds) {

        if (CollectionUtils.isEmpty(chargeSheetIds)) {
            return Lists.newArrayList();
        }


        List<ChargeSheet> chargeSheets = chargeSheetService.findAllByIds(chargeSheetIds);

        if (CollectionUtils.isEmpty(chargeSheets)) {
            return new ArrayList<>();
        }

        List<ChargeSheetForHospitalView> chargeSheetForHospitalViews = chargeSheets.stream().map(chargeSheet -> {
            SheetProcessor sheetProcessor = new SheetProcessor(chargeSheet);
            sheetProcessor.setSheetProcessorInfoProvider(sheetProcessorService);
            sheetProcessor.build();
            return sheetProcessor.generateChargeSheetForHospitalView();
        }).collect(Collectors.toList());

        return chargeSheetForHospitalViews;
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public HospitalSheetSimpleView getHospitalSheetSimpleViewByHospitalOrderId(long hospitalOrderId) {

        ChargeHospitalSheet chargeHospitalSheet = chargeHospitalSheetRepository.findByHospitalOrderIdAndIsDeleted(hospitalOrderId, 0);

        if (Objects.isNull(chargeHospitalSheet)) {
            return null;
        }

        HospitalSheetSimpleView hospitalSheetSimpleView = new HospitalSheetSimpleView();
        BeanUtils.copyProperties(chargeHospitalSheet, hospitalSheetSimpleView);

        //查询外诊金额
        appendExternalTotalPrice(hospitalSheetSimpleView);

        return hospitalSheetSimpleView;
    }

    private void appendExternalTotalPrice(HospitalSheetSimpleView hospitalSheetSimpleView) {

        if (Objects.isNull(hospitalSheetSimpleView)) {
            return;
        }

        //查询外诊信息
        QueryLongCareOutpatientChargeSheetRsp rsp = cisShebaoService.queryLongCareOutpatientChargeSheetItems(hospitalSheetSimpleView.getChainId(), hospitalSheetSimpleView.getClinicId(), String.valueOf(hospitalSheetSimpleView.getHospitalOrderId()));

        if (Objects.isNull(rsp)) {
            return;
        }

        hospitalSheetSimpleView.setExternalTotalPrice(rsp.getMedSumFee());
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public HospitalSheetView getHospitalSheetViewByHospitalSheetId(long hospitalSheetId) {

        ChargeHospitalSheet chargeHospitalSheet = chargeHospitalSheetRepository.findByIdAndIsDeleted(hospitalSheetId, 0);

        return generateHospitalSheetViewContainProductInfo(chargeHospitalSheet);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public HospitalSheetView getHospitalSheetViewByHospitalOrderId(long hospitalOrderId) {

        ChargeHospitalSheet chargeHospitalSheet = chargeHospitalSheetRepository.findByHospitalOrderIdAndIsDeleted(hospitalOrderId, 0);

        return generateHospitalSheetViewContainProductInfo(chargeHospitalSheet);
    }

    public HospitalSheetView generateHospitalSheetViewContainProductInfo(ChargeHospitalSheet chargeHospitalSheet) {

        HospitalSheetView hospitalSheetView = generateHospitalSheetView(chargeHospitalSheet);

        Optional.ofNullable(hospitalSheetView)
                .ifPresent(this::bindChargeSheetProductInfo);
        return hospitalSheetView;
    }

    private HospitalSheetView generateHospitalSheetView(ChargeHospitalSheet chargeHospitalSheet) {

        if (Objects.isNull(chargeHospitalSheet)) {
            return null;
        }


        HospitalSheetView hospitalSheetView = new HospitalSheetView();
        BeanUtils.copyProperties(chargeHospitalSheet, hospitalSheetView);
        hospitalSheetView.setChargeSheets(new ArrayList<>());

        //查询住院等级信息
        HospitalPatientOrderVO hospitalPatientOrder = cisPatientOrderService.getHospitalPatientOrder(chargeHospitalSheet.getChainId(), chargeHospitalSheet.getClinicId(), String.valueOf(chargeHospitalSheet.getHospitalOrderId()));
        hospitalSheetView.setHospitalPatientOrder(hospitalPatientOrder);

        List<ChargeSheet> chargeSheets = chargeSheetService.findAllByHospitalSheetId(chargeHospitalSheet.getChainId(), chargeHospitalSheet.getClinicId(), chargeHospitalSheet.getId());

        if (CollectionUtils.isEmpty(chargeSheets)) {
            return hospitalSheetView;
        }

        List<ChargeSheetExtend> chargeSheetExtends = chargeSheets.stream().map(chargeSheet -> {
            ChargeSheet memoryChargeSheet = ChargeUtils.deepCopyChargeSheet(chargeSheet);
            ChargeSheetExtend chargeSheetExtend = DTOConverter.convertToChargeSheetExtend(memoryChargeSheet);
            ChargeFormItemMerger.mergeForChargeSheet(chargeSheetExtend);
            return chargeSheetExtend;
        }).collect(Collectors.toList());

        hospitalSheetView.setChargeSheets(chargeSheetExtends);

        return hospitalSheetView;
    }

    private void bindChargeSheetProductInfo(HospitalSheetView hospitalSheetView) {
        if (Objects.isNull(hospitalSheetView)) {
            return;
        }

        loadChargeSheetProductInfo(hospitalSheetView.getChargeSheets());
    }

    private void loadChargeSheetProductInfo(List<ChargeSheetExtend> chargeSheetExtends) {
        if (CollectionUtils.isEmpty(chargeSheetExtends)) {
            return;
        }


        chargeSheetExtends.stream()
                .filter(chargeSheetExtend -> CollectionUtils.isNotEmpty(chargeSheetExtend.getChargeForms()))
                .flatMap(chargeSheetExtend -> chargeSheetExtend.getChargeForms().stream())
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .forEach(chargeForm -> chargeForm.setUsageInfo(JsonUtils.loadAsJsonNode(chargeForm.getUsageInfoJson())));

        Set<String> productIds = new HashSet<>();

        chargeSheetExtends.stream()
                .filter(chargeSheetExtend -> CollectionUtils.isNotEmpty(chargeSheetExtend.getChargeForms()))
                .flatMap(chargeSheetExtend -> chargeSheetExtend.getChargeForms().stream())
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getChargeFormItems() != null)
                .flatMap(chargeForm -> chargeForm.getChargeFormItems().stream())
                .filter(Objects::nonNull)
                .filter(item -> item.getIsDeleted() == 0)
                .forEach(chargeFormItem -> {
                    chargeFormItem.setProductInfo(JsonUtils.loadAsJsonNode(chargeFormItem.getProductSnapshot()));
                    chargeFormItem.setUsageInfo(JsonUtils.loadAsJsonNode(chargeFormItem.getUsageInfoJson()));

                    if (chargeFormItem.getProductInfo() == null && !StringUtils.isEmpty(chargeFormItem.getProductId())) {
                        productIds.add(chargeFormItem.getProductId());
                    }

                });


        if (CollectionUtils.isEmpty(productIds)) {
            return;
        }

        List<QueryGoodsInPharmacyByIdsReq.QueryPharmacyGoodsReq> pharmacyGoodsReqs = chargeSheetExtends.stream()
                .filter(chargeSheetExtend -> CollectionUtils.isNotEmpty(chargeSheetExtend.getChargeForms()))
                .map(chargeSheetExtend -> chargeSheetExtend.getChargeForms().stream()
                        .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                        .filter(chargeForm -> chargeForm.getChargeFormItems() != null)
                        .filter(chargeForm -> chargeForm.getSourceFormType() != Constants.SourceFormType.AIR_PHARMACY)
                        .map(chargeForm -> {
                            QueryGoodsInPharmacyByIdsReq.QueryPharmacyGoodsReq goodsReq = new QueryGoodsInPharmacyByIdsReq.QueryPharmacyGoodsReq();
                            goodsReq.setPharmacyNo(chargeForm.getPharmacyNo());
                            Set<String> goodsIds = chargeForm.getChargeFormItems().stream()
                                    .filter(chargeFormItem -> chargeFormItem.getProductInfo() == null && !StringUtils.isEmpty(chargeFormItem.getProductId()))
                                    .filter(item -> item.getIsDeleted() == 0)
                                    .map(ChargeFormItem::getProductId)
                                    .collect(Collectors.toSet());
                            goodsReq.setGoodsIds(new ArrayList<>(goodsIds));
                            return goodsReq;
                        })
                        .filter(queryPharmacyGoodsReq -> CollectionUtils.isNotEmpty(queryPharmacyGoodsReq.getGoodsIds()))
                        .collect(Collectors.toMap(QueryGoodsInPharmacyByIdsReq.QueryPharmacyGoodsReq::getPharmacyNo, Function.identity(), (a, b) -> {
                            a.getGoodsIds().addAll(b.getGoodsIds());
                            a.setGoodsIds(a.getGoodsIds().stream().distinct().collect(Collectors.toList()));
                            return a;
                        })).values())
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(QueryGoodsInPharmacyByIdsReq.QueryPharmacyGoodsReq::getPharmacyNo, Function.identity(), (a, b) -> {
                    a.getGoodsIds().addAll(b.getGoodsIds());
                    a.setGoodsIds(a.getGoodsIds().stream().distinct().collect(Collectors.toList()));
                    return a;
                })).values().stream().collect(Collectors.toList());

        String chainId = chargeSheetExtends.get(0).getChainId();
        String clinicId = chargeSheetExtends.get(0).getClinicId();


        List<QueryGoodsInPharmacyByIdsRsp.QueryPharmacyGoodsRsp> queryPharmacyGoodsRsps = cisScGoodsService.queryGoodsInPharmacyByIds(clinicId, chainId, true, 1, pharmacyGoodsReqs);

        if (CollectionUtils.isEmpty(queryPharmacyGoodsRsps)) {
            return;
        }

        Map<Integer, QueryGoodsInPharmacyByIdsRsp.QueryPharmacyGoodsRsp> pharmacyGoodsRspMap = ListUtils.toMap(queryPharmacyGoodsRsps, QueryGoodsInPharmacyByIdsRsp.QueryPharmacyGoodsRsp::getPharmacyNo);

        chargeSheetExtends.stream()
                .filter(chargeSheetExtend -> CollectionUtils.isNotEmpty(chargeSheetExtend.getChargeForms()))
                .flatMap(chargeSheetExtend -> chargeSheetExtend.getChargeForms().stream())
                .filter(chargeForm -> chargeForm.getIsDeleted() == 0)
                .filter(chargeForm -> chargeForm.getChargeFormItems() != null)
                .filter(chargeForm -> chargeForm.getSourceFormType() != Constants.SourceFormType.AIR_PHARMACY)
                .forEach(chargeForm -> {
                    QueryGoodsInPharmacyByIdsRsp.QueryPharmacyGoodsRsp pharmacyGoodsRsp = pharmacyGoodsRspMap.getOrDefault(chargeForm.getPharmacyNo(), null);

                    if (pharmacyGoodsRsp == null || org.apache.commons.collections.CollectionUtils.isEmpty(pharmacyGoodsRsp.getList())) {
                        return;
                    }
                    Map<String, GoodsItem> goodsItemMap = ListUtils.toMap(pharmacyGoodsRsp.getList(), GoodsItem::getId);
                    chargeForm.getChargeFormItems().stream()
                            .filter(item -> item.getIsDeleted() == 0)
                            .filter(chargeFormItem -> !Constants.ProductType.SYSTEM_FEE_PRODUCT_TYPE.contains(chargeFormItem.getProductType()) && chargeFormItem.getComposeType() == ComposeType.NOT_COMPOSE)
                            .filter(chargeFormItem -> !StringUtils.isEmpty(chargeFormItem.getProductId()))
                            .forEach(chargeFormItem -> {
                                GoodsItem goodsItem = goodsItemMap.getOrDefault(chargeFormItem.getProductId(), null);
                                if (goodsItem != null) {
                                    chargeFormItem.setProductInfo(JsonUtils.dumpAsJsonNode(goodsItem));
                                }
                            });
                });

    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public HospitalSheetSimpleListRsp findAllHospitalSheetSimpleViewByHospitalOrderIds(HospitalSheetSimpleListReq req) {
        HospitalSheetSimpleListRsp rsp = new HospitalSheetSimpleListRsp();
        rsp.setHospitalSheets(new ArrayList<>());
        if (Objects.isNull(req) || CollectionUtils.isEmpty(req.getHospitalOrderIds())) {
            return rsp;
        }

        List<Long> hospitalOrderIds = req.getHospitalOrderIds().stream()
                .filter(NumberUtils::isDigits)
                .map(Long::parseLong)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(hospitalOrderIds)) {
            return rsp;
        }

        List<ChargeHospitalSheet> chargeHospitalSheets = chargeHospitalSheetRepository.findAllByClinicIdAndHospitalOrderIdInAndIsDeleted(req.getClinicId(), hospitalOrderIds, 0);

        return rsp.setHospitalSheets(Optional.ofNullable(chargeHospitalSheets)
                .orElse(new ArrayList<>())
                .stream()
                .map(chargeHospitalSheet -> {
                    HospitalSheetSimpleView hospitalSheetSimpleView = new HospitalSheetSimpleView();
                    BeanUtils.copyProperties(chargeHospitalSheet, hospitalSheetSimpleView);
                    return hospitalSheetSimpleView;
                }).collect(Collectors.toList()));
    }

    public CombineOrderBusinessPayCallbackRsp payCallback(CombineOrderBusinessPayCallbackReq req) {
        log.info("payCallback req: {}", JsonUtils.dump(req));
        req.checkParam();

        CombineOrderBusinessPayCallbackRsp rsp = new CombineOrderBusinessPayCallbackRsp();
        rsp.setCode(CombineOrderBusinessPayCallbackRsp.Code.FAIL);

        //查询住院结算单
        if (req.getBusinessId() == null) {
            throw new ParamRequiredException("businessId");
        }

        ChargeHospitalSheet chargeHospitalSheet = chargeHospitalSheetRepository.findByIdAndClinicIdAndIsDeleted(req.getBusinessId(), req.getClinicId(), 0);

        BigDecimal thisTimeReceivedFee = Optional.ofNullable(req.getCombineOrderTransaction()).map(CombineOrderTransactionView::getAmount).orElse(BigDecimal.ZERO);

        //校验是否可以入账
        if (!checkCallbackCanExecute(chargeHospitalSheet, thisTimeReceivedFee)) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "结算单不可入账");
            rsp.setMessage("入账金额大于结算单的可结算金额");
            return rsp;
        }

        //入账
        HospitalSheetProcessor hospitalSheetProcessor = new HospitalSheetProcessor(chargeHospitalSheet, hospitalSheetProcessorProvider, req.getOperatorId());
        CombineOrderTransactionView combineOrderTransaction = req.getCombineOrderTransaction();
        CombinedPayItem payItem = new CombinedPayItem();
        payItem.setPayMode(combineOrderTransaction.getPayMode());
        payItem.setPaySubMode(combineOrderTransaction.getPaySubMode());
        payItem.setAmount(combineOrderTransaction.getAmount());

        hospitalSheetProcessor.payCallback(payItem);

        chargeOweSheetService.oweSheetPayCallbackCore(req);

        rsp.setCode(CombineOrderBusinessPayCallbackRsp.Code.SUCCESS);
        return rsp;
    }

    /**
     * 校验是否可以入账
     *
     * @param chargeHospitalSheet
     * @return
     */
    public boolean checkCallbackCanExecute(ChargeHospitalSheet chargeHospitalSheet, BigDecimal thisTimeReceivedFee) {
        if (chargeHospitalSheet.getStatus() != ChargeHospitalSheet.Status.UNCHARGED && chargeHospitalSheet.getStatus() != ChargeHospitalSheet.Status.PART_CHARGED) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeHospitalSheet status 不是待收或部分收， status: {}", chargeHospitalSheet.getStatus());
            return false;
        }

        //判断金额是否足够入账
        BigDecimal realReceivablePrice = MathUtils.wrapBigDecimalSubtract(chargeHospitalSheet.getTotalPrice(), chargeHospitalSheet.getReceivedPrice()).add(MathUtils.wrapBigDecimalOrZero(chargeHospitalSheet.getRefundedPrice()));

        if (realReceivablePrice.compareTo(MathUtils.wrapBigDecimalOrZero(thisTimeReceivedFee)) < 0) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "应收小于本次入账金额，不能入账, realReceivablePrice: {}, thisTimeReceivedFee", realReceivablePrice, thisTimeReceivedFee);
            return false;
        }

        return true;
    }

    public CombineOrderBusinessPayCallbackRsp refundCallback(CombineOrderBusinessPayCallbackReq req) {
        log.info("refundCallback req: {}", JsonUtils.dump(req));
        req.checkParam();

        CombineOrderBusinessPayCallbackRsp rsp = new CombineOrderBusinessPayCallbackRsp();
        rsp.setCode(CombineOrderBusinessPayCallbackRsp.Code.FAIL);

        //查询住院结算单
        if (req.getBusinessId() == null) {
            throw new ParamRequiredException("businessId");
        }

        ChargeHospitalSheet chargeHospitalSheet = chargeHospitalSheetRepository.findByIdAndClinicIdAndIsDeleted(req.getBusinessId(), req.getClinicId(), 0);

        BigDecimal thisTimeRefundFee = Optional.ofNullable(req.getCombineOrderTransaction()).map(CombineOrderTransactionView::getAmount).orElse(BigDecimal.ZERO);

        //校验是否可以入账
        checkCanRefundExecuteOrThrowException(chargeHospitalSheet);

        HospitalSheetProcessor hospitalSheetProcessor = new HospitalSheetProcessor(chargeHospitalSheet, hospitalSheetProcessorProvider, req.getOperatorId());
        hospitalSheetProcessor.refundCallback(thisTimeRefundFee.abs());

        rsp = chargeOweSheetService.oweSheetRefundCallBackCore(req);

        if (rsp.getCode() == CombineOrderBusinessPayCallbackRsp.Code.FAIL) {
            return rsp;
        }

        rsp = updateChargeHospitalPayTransaction(req);

        return rsp;
    }

    private CombineOrderBusinessPayCallbackRsp updateChargeHospitalPayTransaction(CombineOrderBusinessPayCallbackReq req) {
        CombineOrderBusinessPayCallbackRsp rsp = new CombineOrderBusinessPayCallbackRsp();
        if (StringUtils.isEmpty(req.getBusinessPayTransactionId())) {
            return rsp;
        }

        ChargeHospitalPayTransaction chargeHospitalPayTransaction = chargeHospitalPayTransactionRepository.findByIdAndClinicIdAndIsDeleted(Long.parseLong(req.getBusinessPayTransactionId()), req.getClinicId(), 0);

        if (chargeHospitalPayTransaction == null) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeHospitalPayTransaction is null");
            throw new NotFoundException();
        }

        if (chargeHospitalPayTransaction.getPayStatus() == CombinePayOrderPayResult.PayStatus.SUCCESS) {
            return rsp;
        }

        if (chargeHospitalPayTransaction.getPayStatus() != CombinePayOrderPayResult.PayStatus.WAITING) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeHospitalPayTransaction status error, status: {}", chargeHospitalPayTransaction.getPayStatus());
            return rsp.setCode(CombineOrderBusinessPayCallbackRsp.Code.FAIL)
                    .setMessage("退款单状态错误");
        }

        chargeHospitalPayTransaction.setPayStatus(CombinePayOrderPayResult.PayStatus.SUCCESS);
        FillUtils.fillLastModifiedBy(chargeHospitalPayTransaction, req.getOperatorId());
        chargeHospitalPayTransactionRepository.save(chargeHospitalPayTransaction);
        return rsp;
    }

    private void checkCanRefundExecuteOrThrowException(ChargeHospitalSheet chargeHospitalSheet) {

        if (chargeHospitalSheet == null) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "chargeHospitalSheet is null");
            throw new NotFoundException();
        }

        if (chargeHospitalSheet.getStatus() != ChargeHospitalSheet.Status.CHARGED && chargeHospitalSheet.getStatus() != ChargeHospitalSheet.Status.PART_REFUNDED) {
            throw new ChargeServiceException(ChargeServiceError.CHARGE_HOSPITAL_SHEET_PAY_STATUS_ERROR);
        }
    }
}
