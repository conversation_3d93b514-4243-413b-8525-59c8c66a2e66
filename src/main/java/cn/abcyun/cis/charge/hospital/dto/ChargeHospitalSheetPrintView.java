package cn.abcyun.cis.charge.hospital.dto;

import cn.abcyun.cis.charge.service.dto.print.ChargeFormPrintView;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Objects;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class ChargeHospitalSheetPrintView extends ChargeHospitalSheetPrintBaseView {

    public ChargeHospitalSheetPrintView(ChargeHospitalSheetPrintBaseView chargeHospitalSheetPrintBaseView) {

        if (Objects.nonNull(chargeHospitalSheetPrintBaseView)) {
            BeanUtils.copyProperties(chargeHospitalSheetPrintBaseView, this);
        }

    }

    String doctorName;

    String nationalDoctorCode;

    private List<ChargeFormPrintView> chargeForms;

    @ApiModelProperty(value = "门诊类型")
    private String outpatientType = "普通门诊";

}
