package cn.abcyun.cis.charge.hospital.controller.rpc;

import cn.abcyun.cis.charge.hospital.api.model.HospitalSheetSimpleListReq;
import cn.abcyun.cis.charge.hospital.api.model.HospitalSheetSimpleListRsp;
import cn.abcyun.cis.charge.hospital.api.model.HospitalSheetSimpleView;
import cn.abcyun.cis.charge.hospital.api.model.HospitalSheetView;
import cn.abcyun.cis.charge.hospital.service.ChargeHospitalService;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.cis.core.aop.LogReqAndRsp;
import cn.abcyun.common.model.AbcServiceResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 住院结算单业务相关rpc接口
 */
@RestController
@RequestMapping("/rpc/charges/hospital/")
@Slf4j
public class RpcChargeHospitalSheetController {

    private final ChargeHospitalService chargeHospitalService;


    @Autowired
    public RpcChargeHospitalSheetController(ChargeHospitalService chargeHospitalService) {

        this.chargeHospitalService = chargeHospitalService;
    }


    @GetMapping("/hospital-order/{hospitalOrderId}/simple")
    @LogReqAndRsp
    public AbcServiceResponse<HospitalSheetSimpleView> getHospitalSheetSimpleViewByHospitalOrderId(@PathVariable("hospitalOrderId") String hospitalOrderId) {
        if (TextUtils.isEmpty(hospitalOrderId)) {
            throw new ParamRequiredException("id");
        }

        HospitalSheetSimpleView hospitalSheetSimpleView = null;

        if (!NumberUtils.isDigits(hospitalOrderId)) {
            return new AbcServiceResponse<>(null);
        }

        hospitalSheetSimpleView = chargeHospitalService.getHospitalSheetSimpleViewByHospitalOrderId(Long.parseLong(hospitalOrderId));
        return new AbcServiceResponse<>(hospitalSheetSimpleView);
    }

    @PostMapping("/hospital-order/list-simple")
    @LogReqAndRsp
    public AbcServiceResponse<HospitalSheetSimpleListRsp> findAllByHospitalOrderIds(@RequestBody HospitalSheetSimpleListReq req) {

        HospitalSheetSimpleListRsp rsp = chargeHospitalService.findAllHospitalSheetSimpleViewByHospitalOrderIds(req);
        return new AbcServiceResponse<>(rsp);
    }

    @GetMapping("/hospital-sheet/{hospitalSheetId}")
    @LogReqAndRsp
    public AbcServiceResponse<HospitalSheetView> getHospitalSheetViewByHospitalSheetId(@PathVariable("hospitalSheetId") String hospitalSheetId) {
        if (TextUtils.isEmpty(hospitalSheetId)) {
            throw new ParamRequiredException("hospitalSheetId");
        }

        if (!NumberUtils.isDigits(hospitalSheetId)) {
            throw new NotFoundException();
        }

        HospitalSheetView hospitalSheetView = chargeHospitalService.getHospitalSheetViewByHospitalSheetId(Long.parseLong(hospitalSheetId));

        return new AbcServiceResponse<>(hospitalSheetView);
    }

    @GetMapping("/hospital-order/{hospitalOrderId}")
    @LogReqAndRsp
    public AbcServiceResponse<HospitalSheetView> getHospitalSheetViewByHospitalOrderId(@PathVariable("hospitalOrderId") String hospitalOrderId) {
        if (TextUtils.isEmpty(hospitalOrderId)) {
            throw new ParamRequiredException("hospitalOrderId");
        }

        if (!NumberUtils.isDigits(hospitalOrderId)) {
            throw new NotFoundException();
        }

        HospitalSheetView hospitalSheetView = chargeHospitalService.getHospitalSheetViewByHospitalOrderId(Long.parseLong(hospitalOrderId));

        return new AbcServiceResponse<>(hospitalSheetView);
    }
}
