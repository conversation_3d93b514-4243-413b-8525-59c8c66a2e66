package cn.abcyun.cis.charge.hospital.api.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class ChargeHospitalPayTransactionPayStatusRsp {
    private int payStatus;
    private String message;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal needPayFee;

}
