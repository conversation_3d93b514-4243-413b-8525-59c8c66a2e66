package cn.abcyun.cis.charge.hospital.processor;

import cn.abcyun.cis.charge.api.model.owe.PayChargeOweSheetReq;
import cn.abcyun.cis.charge.api.model.owe.RefundChargeOweSheetReq;
import cn.abcyun.cis.charge.base.ChargeServiceError;
import cn.abcyun.cis.charge.base.ChargeServiceException;
import cn.abcyun.cis.charge.base.pay.CombinedPayItem;
import cn.abcyun.cis.charge.combinorder.dto.ChargeOweSheetRefundResult;
import cn.abcyun.cis.charge.combinorder.dto.CombinePayOrderPayResult;
import cn.abcyun.cis.charge.combinorder.model.ChargeCombineOrder;
import cn.abcyun.cis.charge.combinorder.model.ChargeCombineOrderTransaction;
import cn.abcyun.cis.charge.combinorder.service.CombineOrderService;
import cn.abcyun.cis.charge.hospital.dto.ThirdPartyRefundResultDto;
import cn.abcyun.cis.charge.hospital.model.ChargeHospitalPayTransaction;
import cn.abcyun.cis.charge.hospital.model.ChargeHospitalSheet;
import cn.abcyun.cis.charge.hospital.service.ChargeHospitalPayTransactionService;
import cn.abcyun.cis.charge.model.ChargeOweCombineTransaction;
import cn.abcyun.cis.charge.model.ChargeOweCombineTransactionRecord;
import cn.abcyun.cis.charge.model.ChargeOweSheet;
import cn.abcyun.cis.charge.processor.FlatPriceHelper;
import cn.abcyun.cis.charge.repository.ChargeOweSheetRepository;
import cn.abcyun.cis.charge.service.ChargeOweSheetService;
import cn.abcyun.cis.charge.util.AbcIdUtils;
import cn.abcyun.cis.charge.util.JsonUtils;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.exception.ServiceInternalException;
import cn.abcyun.common.log.marker.AbcLogMarker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.TreeSet;
import java.util.stream.Collectors;

@Service
@Slf4j
public class HospitalSheetPayProvider {

    private final CombineOrderService combineOrderService;
    private final ChargeOweSheetService chargeOweSheetService;
    private final ChargeOweSheetRepository chargeOweSheetRepository;
    private final ChargeHospitalPayTransactionService chargeHospitalPayTransactionService;

    @Autowired
    public HospitalSheetPayProvider(CombineOrderService combineOrderService,
                                    ChargeOweSheetService chargeOweSheetService,
                                    ChargeOweSheetRepository chargeOweSheetRepository,
                                    ChargeHospitalPayTransactionService chargeHospitalPayTransactionService) {

        this.combineOrderService = combineOrderService;
        this.chargeOweSheetService = chargeOweSheetService;
        this.chargeOweSheetRepository = chargeOweSheetRepository;
        this.chargeHospitalPayTransactionService = chargeHospitalPayTransactionService;
    }

    public CombinePayOrderPayResult pay(ChargeHospitalSheet chargeHospitalSheet, CombinedPayItem payItem, String operatorId) {

        if (Objects.isNull(chargeHospitalSheet)) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX,"chargeHospitalSheet is null");
            return null;
        }

        List<ChargeOweSheet> chargeOweSheets = chargeOweSheetRepository.findAllByChargeHospitalSheetIdAndClinicId(chargeHospitalSheet.getId(), chargeHospitalSheet.getClinicId());
        chargeOweSheets = Optional.ofNullable(chargeOweSheets).orElse(new ArrayList<>())
                .stream()
                .filter(chargeOweSheet -> chargeOweSheet.getStatus() == ChargeOweSheet.Status.UNCHARGED || chargeOweSheet.getStatus() == ChargeOweSheet.Status.PART_CHARGED)
                .filter(chargeOweSheet -> chargeOweSheet.getIsOldRecord() == 0)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(chargeOweSheets)) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX,"chargeOweSheets is null");
            throw new NotFoundException();
        }

        List<Long> oweSheetIds = chargeOweSheets.stream().map(ChargeOweSheet::getId).collect(Collectors.toList());

        //根据收费金额平摊
        List<FlatPriceHelper.FlatPriceCell> flatPriceCells = chargeOweSheets.stream()
                .map(chargeOweSheet -> {
                    FlatPriceHelper.FlatPriceCell flatPriceCell = new FlatPriceHelper.FlatPriceCell();
                    flatPriceCell.setId(chargeOweSheet.getId().toString());
                    flatPriceCell.setPriority(FlatPriceHelper.PRIORITY_HIGH);
                    flatPriceCell.setTotalPrice(chargeOweSheet.calculateReceivableFee());
                    return flatPriceCell;
                })
                .collect(Collectors.toList());


        FlatPriceHelper flatPriceHelper = new FlatPriceHelper(payItem.getAmount());
        flatPriceHelper.flat(flatPriceCells);

        PayChargeOweSheetReq payChargeOweSheetReq = new PayChargeOweSheetReq();

        CombinedPayItem combinedPayItem = new CombinedPayItem();
        BeanUtils.copyProperties(payItem, combinedPayItem);
        combinedPayItem.setChange(BigDecimal.ZERO);

        payChargeOweSheetReq.setPayItem(combinedPayItem)
                .setPatientId(chargeHospitalSheet.getPatientId())
                .setOweSheetItems(flatPriceCells.stream()
                        .map(cell -> new PayChargeOweSheetReq.CombineOweSheetItem().setOweSheetId(cell.getId())
                                .setAmount(cell.getFlatPrice())
                                .setReceivableFee(cell.getTotalPrice())
                        ).collect(Collectors.toList())
                );

        return chargeOweSheetService.payForChargeOweSheetCore(chargeOweSheets, oweSheetIds, payChargeOweSheetReq,
                chargeHospitalSheet.getChainId(), chargeHospitalSheet.getClinicId(), ChargeCombineOrder.Source.HOSPITAL_SHEET, chargeHospitalSheet.getId(), operatorId);
    }


    public ThirdPartyRefundResultDto refund(ChargeHospitalSheet chargeHospitalSheet, CombinedPayItem payItem, String operatorId) {

        List<String> transactionIds = payItem.getTransactionIds();

        if (CollectionUtils.isEmpty(transactionIds)) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX,"transactionIds is empty");
            throw new ParamRequiredException("transactionIds");
        }

        //查询欠费单数据
        List<ChargeOweSheet> chargeOweSheets = chargeOweSheetRepository.findAllByChargeHospitalSheetIdAndClinicId(chargeHospitalSheet.getId(), chargeHospitalSheet.getClinicId());
        chargeOweSheets = Optional.ofNullable(chargeOweSheets).orElse(new ArrayList<>())
                .stream()
                .filter(chargeOweSheet -> chargeOweSheet.getStatus() == ChargeOweSheet.Status.CHARGED || chargeOweSheet.getStatus() == ChargeOweSheet.Status.PART_REFUNDED)
                .filter(chargeOweSheet -> chargeOweSheet.getIsOldRecord() == 0)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(chargeOweSheets)) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX,"chargeOweSheets is null");
            throw new NotFoundException();
        }

        List<ChargeOweCombineTransaction> chargeOweCombineTransactions = chargeOweSheets.stream()
                .filter(chargeOweSheet -> CollectionUtils.isNotEmpty(chargeOweSheet.getTransactionRecords()))
                .flatMap(chargeOweSheet -> chargeOweSheet.getTransactionRecords().stream())
                .filter(chargeOweCombineTransactionRecord -> Objects.nonNull(chargeOweCombineTransactionRecord.getOweCombineTransaction()))
                .map(ChargeOweCombineTransactionRecord::getOweCombineTransaction)
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ChargeOweCombineTransaction::getId))),
                        ArrayList::new));

        List<ChargeOweCombineTransaction> needRefundOweCombineTransactions = chargeOweCombineTransactions.stream()
                .filter(chargeOweCombineTransaction -> chargeOweCombineTransaction.getType() == ChargeCombineOrderTransaction.Type.PAY)
                .filter(chargeOweCombineTransaction -> chargeOweCombineTransaction.getPayMode() == payItem.getPayMode())
                .filter(chargeOweCombineTransaction -> payItem.getTransactionIds().contains(Objects.toString(chargeOweCombineTransaction.getId(), "")))
                .collect(Collectors.toList());

        if (needRefundOweCombineTransactions.size() != payItem.getTransactionIds().size()) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX,"needRefundOweCombineTransactions.size != transactionIds.size, needRefundOweCombineTransactions.size: {}, transactionIds.size: {}", needRefundOweCombineTransactions.size(), payItem.getTransactionIds().size());

            throw new ChargeServiceException(ChargeServiceError.REFUND_RECEIVED_TRANSACTION_NOT_EXISTED);
        }

        BigDecimal canRefundTotalPrice = needRefundOweCombineTransactions.stream()
                .map(oweCombineTransaction -> MathUtils.wrapBigDecimalAdd(oweCombineTransaction.getAmount(), oweCombineTransaction.getRefundedAmount()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (canRefundTotalPrice.compareTo(payItem.getAmount().abs()) < 0) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX,"可退款金额小于本次的退费金额, canRefundTotalPrice: {}, thisTimeRefundPrice: {}", canRefundTotalPrice, payItem.getAmount().abs());
            throw new ChargeServiceException(ChargeServiceError.CHARGE_REFUND_OVERFLOW);
        }

        //判断是单个退费还是多个退费
        if(needRefundOweCombineTransactions.size() == 1) {
            String chargeHospitalPayTransactionId = AbcIdUtils.getUID();
            ChargeOweSheetRefundResult chargeOweSheetRefundResult = oweSheetRefund(chargeHospitalSheet, chargeOweSheets, needRefundOweCombineTransactions.get(0), chargeHospitalPayTransactionId, payItem, operatorId);

            if (chargeOweSheetRefundResult == null) {
                log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX,"chargeOweSheetRefundResult is null");
                throw new ServiceInternalException("退款失败");
            }

            int payStatus = chargeOweSheetRefundResult.getPayStatus();
            if (payStatus == CombinePayOrderPayResult.PayStatus.WAITING) {
                chargeHospitalPayTransactionService.saveHospitalPayTransaction(Long.parseLong(chargeHospitalPayTransactionId), chargeHospitalSheet.getChainId(), chargeHospitalSheet.getClinicId(),
                        chargeHospitalSheet.getId(), payItem.getPayMode(), payItem.getPaySubMode(), payItem.getAmount().abs().negate(),
                        payStatus, ChargeHospitalPayTransaction.PayType.REFUND, chargeOweSheetRefundResult.getCombineOrderPayTransactionId(), null, null, operatorId);
            }
            ThirdPartyRefundResultDto chargePayRefundResult = new ThirdPartyRefundResultDto();
            chargePayRefundResult.setPayStatus(chargeOweSheetRefundResult.getPayStatus());

            chargePayRefundResult.setChargePayTransactionId(chargeOweSheetRefundResult.getPayStatus() == CombinePayOrderPayResult.PayStatus.WAITING ? chargeHospitalPayTransactionId : null);
            chargePayRefundResult.setThirdPartyPayTaskId(chargeOweSheetRefundResult.getThirdPartyPayTaskId());
            if (payStatus == CombinePayOrderPayResult.PayStatus.SUCCESS) {
                chargePayRefundResult.setRefundedPrincipal(chargeOweSheetRefundResult.getCombineOrderTransaction().getAmount());
                chargePayRefundResult.setChargeOweCombineTransactions(Arrays.asList(chargeOweSheetRefundResult.getChargeOweCombineTransaction()));
            } else {
                chargePayRefundResult.setRefundedPrincipal(BigDecimal.ZERO);
                chargePayRefundResult.setChargeOweCombineTransactions(new ArrayList<>());
            }

            return chargePayRefundResult;
        } else {
            BigDecimal needRefundFee = payItem.getAmount().abs();
            List<ChargeOweSheetRefundResult> chargeOweSheetRefundResults = new ArrayList<>();
            for (ChargeOweCombineTransaction needRefundOweCombineTransaction : needRefundOweCombineTransactions) {
                BigDecimal combineTransactionNeedRefundFee = MathUtils.min(needRefundFee, MathUtils.wrapBigDecimalAdd(needRefundOweCombineTransaction.getAmount(), needRefundOweCombineTransaction.getRefundedAmount()));

                CombinedPayItem combinedPayItem = new CombinedPayItem();
                combinedPayItem.setPayMode(payItem.getPayMode());
                combinedPayItem.setPaySubMode(payItem.getPaySubMode());
                combinedPayItem.setAmount(combineTransactionNeedRefundFee);

                ChargeOweSheetRefundResult chargeOweSheetRefundResult = oweSheetRefund(chargeHospitalSheet, chargeOweSheets, needRefundOweCombineTransaction, null, combinedPayItem, operatorId);

                chargeOweSheetRefundResults.add(chargeOweSheetRefundResult);
                needRefundFee = needRefundFee.subtract(combineTransactionNeedRefundFee);
            }

            //多个退费只有记账才会出现，判断是否全部退费完成
            boolean isRefundSuccess = chargeOweSheetRefundResults.stream()
                    .allMatch(combinePayOrderPayResult -> combinePayOrderPayResult.getPayStatus() == CombinePayOrderPayResult.PayStatus.SUCCESS);

            if (!isRefundSuccess) {
                log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX,"退款失败， chargeOweSheetRefundResults: {}", JsonUtils.dump(chargeOweSheetRefundResults));
                throw new SecurityException("退款失败");
            }
            BigDecimal refundedPrice = chargeOweSheetRefundResults.stream()
                    .filter(combinePayOrderPayResult -> combinePayOrderPayResult.getCombineOrderTransaction() != null)
                    .map(combinePayOrderPayResult -> combinePayOrderPayResult.getCombineOrderTransaction().getAmount())
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);


            ThirdPartyRefundResultDto chargePayRefundResult = new ThirdPartyRefundResultDto();
            chargePayRefundResult.setPayStatus(CombinePayOrderPayResult.PayStatus.SUCCESS);

            chargePayRefundResult.setRefundedPrincipal(refundedPrice);
            chargePayRefundResult.setChargeOweCombineTransactions(chargeOweSheetRefundResults.stream().map(ChargeOweSheetRefundResult::getChargeOweCombineTransaction)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList()));

            return chargePayRefundResult;
        }
    }

    private ChargeOweSheetRefundResult oweSheetRefund(ChargeHospitalSheet chargeHospitalSheet, List<ChargeOweSheet> chargeOweSheets,
                                                      ChargeOweCombineTransaction needRefundOweCombineTransaction,
                                                      String chargeHospitalPayTransactionId,
                                                      CombinedPayItem payItem, String operatorId) {
        List<ChargeOweCombineTransactionRecord> oweCombineTransactionRecords = chargeOweSheets.stream()
                .filter(chargeOweSheet -> CollectionUtils.isNotEmpty(chargeOweSheet.getTransactionRecords()))
                .flatMap(chargeOweSheet -> chargeOweSheet.getTransactionRecords().stream())
                .filter(chargeOweCombineTransactionRecord -> Objects.equals(chargeOweCombineTransactionRecord.getOweCombineTransactionId(), needRefundOweCombineTransaction.getId()))
                .collect(Collectors.toList());

        RefundChargeOweSheetReq refundChargeOweSheetReq = new RefundChargeOweSheetReq();
        refundChargeOweSheetReq.setPayItem(payItem)
                .setOweSheetItems(oweCombineTransactionRecords.stream()
                        .map(transactionRecord -> new RefundChargeOweSheetReq.CombineOweSheetItem()
                                .setOweSheetId(transactionRecord.getOweSheetId())
                                .setPrice(MathUtils.wrapBigDecimalAdd(transactionRecord.getAmount(), transactionRecord.getRefundedAmount()))
                        )
                        .collect(Collectors.toList())
                )
                .setSource(ChargeCombineOrder.Source.HOSPITAL_SHEET)
                .setBusinessId(chargeHospitalSheet.getId())
                .setBusinessPayTransactionId(chargeHospitalPayTransactionId)
                .setPatientId(chargeHospitalSheet.getPatientId());

        return chargeOweSheetService.refundChargeOweSheetCore(chargeOweSheets, needRefundOweCombineTransaction,
                oweCombineTransactionRecords, refundChargeOweSheetReq,
                chargeHospitalSheet.getChainId(), chargeHospitalSheet.getClinicId(), operatorId);
    }


}
