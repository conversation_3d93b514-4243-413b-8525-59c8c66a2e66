package cn.abcyun.cis.charge.hospital.service;

import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.base.InnerPayModes;
import cn.abcyun.cis.charge.hospital.api.model.HospitalSheetPayCallbackReq;
import cn.abcyun.cis.charge.hospital.model.ChargeHospitalPayTransaction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class HospitalSheetAbcPayCallbackService extends HospitalSheetThirdPartyCallbackPayModeAbstractService {

    public HospitalSheetAbcPayCallbackService(HospitalSheetProcessorProvider hospitalSheetProcessorProvider) {
        super(hospitalSheetProcessorProvider);
    }

    @Override
    public String getPayModeKey() {
        return InnerPayModes.getPayModeByName(InnerPayModes.InnerPayModeName.ABC_PAY).getPayModeKey();
    }

    @Override
    public void resetPayModeAndPaySubMode(HospitalSheetPayCallbackReq payCallbackReq, ChargeHospitalPayTransaction payTransaction) {
        payCallbackReq.setPayMode(payTransaction.getPayMode());
        payCallbackReq.setPaySubMode(Constants.ABC_PAY_SUB_TYPE_MAP.getOrDefault(payCallbackReq.getPaySubMode(), 0));
    }
}
