package cn.abcyun.cis.charge.hospital.model;

import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.combinorder.model.ChargeCombineOrderTransaction;
import cn.abcyun.cis.charge.model.ChargeOweCombineTransaction;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.SQLInsert;
import org.hibernate.annotations.Where;

import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Data
@Entity
@Table(name = "v2_charge_hospital_sheet")
@Accessors(chain = true)
public class ChargeHospitalSheet implements Serializable {


	/**
	 * 主键id
	 */
	@Id
	private Long id;

	/**
	 * 连锁id
	 */
	private String chainId;

	/**
	 * 诊所id
	 */
	private String clinicId;

	/**
	 * 患者id
	 */
	private String patientId;

	/**
	 * 医院登记id
	 */
	private Long hospitalOrderId;

	/**
	 * 已收金额
	 */
	private BigDecimal receivedPrice;

	/**
	 * 已退金额
	 */
	private BigDecimal refundedPrice;

	/**
	 * 总金额
	 */
	private BigDecimal totalPrice;

	/**
	 * 结算状态：0：未发起结算，10：结算中，20：结算完成
	 */
	private int settleStatus;

	/**
	 * 支付状态：0：待收，10：部分收，20：已收，30：部分退费，40：已退费
	 */
	private int status;

	@OneToMany(cascade = CascadeType.ALL)
	@JoinColumn(name = "businessId")
	@Where(clause = "source = 1 and business_id is not null and is_deleted = 0")
	@SQLDelete(sql = "UPDATE v2_charge_owe_combine_transaction SET is_deleted = 1 WHERE business_id = ? AND id = ?")
	@SQLDeleteAll(sql = "UPDATE v2_charge_owe_combine_transaction SET is_deleted = 1 WHERE business_id = ?")
	@SQLInsert(sql = "UPDATE v2_charge_owe_combine_transaction SET business_id = ?, is_deleted = 0 WHERE id=?")
	private List<ChargeOweCombineTransaction> chargeOweCombineTransactions;

	private String chargedBy;

	private Instant chargedTime;

	private Instant firstChargedTime;

	/**
	 * 发票状态
	 * {@link InvoiceStatus}
	 */
	private int invoiceStatus = InvoiceStatus.INVOICE_WAITING;

	/**
	 * 删除状态：0：未删除，1：已删除
	 */
	private int isDeleted;

	/**
	 * 删除的毫秒值
	 */
	private long deleteMillis;

	/**
	 * 创建时间
	 */
	private Instant created;

	/**
	 * 创建人id
	 */
	private String createdBy;

	/**
	 * 最后修改时间
	 */
	private Instant lastModified;

	/**
	 * 最后修改时间
	 */
	private String lastModifiedBy;

	public static class SettleStatus {
		/**
		 * 待结算
		 */
		public static final int WAIT_SETTLE = 0;
		/**
		 * 结算中
		 */
		public static final int SETTLING = 10;
		/**
		 * 结算完成
		 */
		public static final int SETTLED = 20;
	}

	public static class Status {
		public static final int UNCHARGED = 0;
		public static final int PART_CHARGED = 10;
		public static final int CHARGED = 20;
		public static final int PART_REFUNDED = 30;
		public static final int REFUNDED = 40;
	}


	/**
	 * 长护单的发票状态
	 * 10：待开发票，20：开票中，30：已开票，40：开票失败，50：已冲红，60：已作废
	 */
	public static class InvoiceStatus {

		public static final int INVOICE_WAITING = 10;

		public static final int INVOICE_BEING = 20;

		public static final int INVOICE_SUCCESS = 30;

		public static final int INVOICE_FAIL = 40;

		public static final int INVOICE_INVALID = 50;

		public static final int INVOICE_REFUND = 60;

	}

	@JsonIgnore
	public BigDecimal getHealthCardPaymentFee() {
		if (chargeOweCombineTransactions == null) {
			return BigDecimal.ZERO;
		}
		return chargeOweCombineTransactions.stream()
				.filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.HEALTH_CARD)
				.map(ChargeOweCombineTransaction::getAmount)
				.filter(Objects::nonNull)
				.reduce(BigDecimal.ZERO, BigDecimal::add);
	}

	@JsonIgnore
	public String getLastChargedByEmployeeId() {
		//找到最后一次收费的employeeId
		return Optional.ofNullable(chargeOweCombineTransactions)
				.orElse(new ArrayList<>())
				.stream()
				.filter(chargeOweCombineTransaction -> chargeOweCombineTransaction.getType() == ChargeCombineOrderTransaction.Type.PAY)
				.max(Comparator.comparing(ChargeOweCombineTransaction::getCreated))
				.map(ChargeOweCombineTransaction::getCreatedBy)
				.orElse(null);
	}
}
