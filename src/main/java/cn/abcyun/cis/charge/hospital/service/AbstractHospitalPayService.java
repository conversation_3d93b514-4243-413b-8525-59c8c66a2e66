package cn.abcyun.cis.charge.hospital.service;

import cn.abcyun.cis.charge.hospital.dto.BaseThirdPartyDto;
import cn.abcyun.cis.charge.hospital.model.ChargeHospitalPayTransaction;
import cn.abcyun.cis.charge.hospital.repository.ChargeHospitalPayTransactionRepository;
import cn.abcyun.cis.charge.model.ThirdPartyPayInfo;
import cn.abcyun.cis.commons.util.FillUtils;
import cn.abcyun.cis.idgenerator.AbcIdGenerator;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

@Slf4j
public abstract class AbstractHospitalPayService {

    protected final AbcIdGenerator abcIdGenerator;
    protected final ChargeHospitalPayTransactionRepository chargeHospitalPayTransactionRepository;

    public AbstractHospitalPayService(AbcIdGenerator abcIdGenerator,
                                      ChargeHospitalPayTransactionRepository chargeHospitalPayTransactionRepository) {

        this.abcIdGenerator = abcIdGenerator;
        this.chargeHospitalPayTransactionRepository = chargeHospitalPayTransactionRepository;
    }

    protected ChargeHospitalPayTransaction saveHospitalPayTransaction(Long transactionId, BaseThirdPartyDto thirdPartyPayDto,
                                                                            int payStatus, int payType, String payTransactionId,
                                                                            ThirdPartyPayInfo thirdPartyPayInfo, JsonNode payReq) {

        ChargeHospitalPayTransaction hospitalPayTransaction = new ChargeHospitalPayTransaction();
        hospitalPayTransaction.setId(Objects.isNull(transactionId) ? abcIdGenerator.getUIDLong() : transactionId)
                .setChainId(thirdPartyPayDto.getChainId())
                .setClinicId(thirdPartyPayDto.getClinicId())
                .setHospitalSheetId(thirdPartyPayDto.getHospitalSheetId())
                .setPayTransactionId(payTransactionId)
                .setPayInfo(thirdPartyPayInfo)
                .setPayType(payType)
                .setPayStatus(payStatus)
                .setPayMode(thirdPartyPayDto.getPayMode())
                .setPaySubMode(thirdPartyPayDto.getPaySubMode())
                .setAmount(thirdPartyPayDto.getAmount())
                .setPayReq(payReq);

        FillUtils.fillCreatedBy(hospitalPayTransaction, thirdPartyPayDto.getOperatorId());


        chargeHospitalPayTransactionRepository.save(hospitalPayTransaction);
        return hospitalPayTransaction;
    }
}
