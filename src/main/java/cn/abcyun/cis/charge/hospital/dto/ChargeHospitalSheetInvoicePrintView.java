package cn.abcyun.cis.charge.hospital.dto;

import cn.abcyun.cis.charge.service.dto.print.MedicalBillPrintView;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ChargeHospitalSheetInvoicePrintView extends ChargeHospitalSheetPrintBaseView{

    public ChargeHospitalSheetInvoicePrintView(ChargeHospitalSheetPrintBaseView chargeHospitalSheetPrintBaseView) {
        if (Objects.nonNull(chargeHospitalSheetPrintBaseView)) {
            BeanUtils.copyProperties(chargeHospitalSheetPrintBaseView, this);
        }
    }

    @ApiModelProperty(value = "药品分类明细")
    private List<MedicalBillPrintView> medicalBills;

    @ApiModelProperty(value = "包含照护费的实收金额")
    private BigDecimal receivedPriceWithCareFee;

    public BigDecimal getReceivedPriceWithCareFee() {
        return Objects.nonNull(receivedPriceWithCareFee) ? receivedPriceWithCareFee : getReceivedPrice();
    }

    @JsonIgnore
    private int goodsNum;
}
