package cn.abcyun.cis.charge.model;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.time.Instant;

/**
 * v2_charge_rule_ladder_info
 * <AUTHOR>
@Data
@Entity
@Table(name = "v2_charge_rule_ladder_info")
public class ChargeRuleLadderInfo {
    /**
     * 快递费和加工费规则阶梯算法
     */
    @Id
    private String id;

    private String chainId;

    private String clinicId;

    /**
     * 规则类型：0：未知，1：快递费阶梯算法，2：加工费阶梯算法
     */
    private int ruleType;

    /**
     * 规则id
     */
    private String ruleId;

    /**
     * 类型：0：未知，1：处方剂数，2：药品重量，3：加工袋数
     */
    private int type;

    /**
     * 值的范围，例如：20剂以内
     */
    private BigDecimal unitCount;

    /**
     * 阶梯单位
     */
    private String unit;

    /**
     * 金额
     */
    private BigDecimal price;

    /**
     * 每增加额外的count
     */
    private BigDecimal additionalCount;

    /**
     * 每个额外的count增加的金额
     */
    private BigDecimal additionalPrice;

    /**
     * 加工费单位数量不足取整规则（0：向下取整；1：向上取整）
     */
    private int roundType;

    /**
     * 是否删除状态（0：正常；1：被删除）
     */
    private int isDeleted;

    private Instant created;

    private String createdBy;

    private Instant lastModified;

    private String lastModifiedBy;

    public static class RuleType{
        //快递费规则
        public static final int EXPRESS_DELIVERY = 1;

        //加工费规则
        public static final int PRECESS = 2;
    }


    public static class Type{
        //1：处方剂数，2：药品重量，3：加工袋数
        public static final int PRESCRIPTION_DOSAGE = 1;

        public static final int MEDICINE_WEIGHT = 2;

        public static final int PROCESS_BAG = 3;
    }

    public static class RoundType{
        // 向下取整
        public static final int ROUND_DOWN=0;
        // 向上取整
        public static final int ROUND_UP=1;
    }

}