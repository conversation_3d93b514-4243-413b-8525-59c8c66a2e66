package cn.abcyun.cis.charge.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Optional;


@Entity
@Table(name = "v2_charge_transaction_record")
@Data
public class ChargeTransactionRecord {
    @Id
    private String id;
    private String patientOrderId;
    private String chainId;
    private String clinicId;
    private String chargeSheetId;
    private String patientId;
    private String transactionId;
    private int chargeType;
    private String productId;
    private String chargeFormItemId;
    private int productType;
    private int productSubType;
    private int type;
    private int composeType;
    private String composeParentRecordId;

    private String productUnit;
    private BigDecimal productUnitCount;
    @Column(name = "[usage]")
    private String usage;
    private BigDecimal doseCount;

    /**
     * 从药店算费调整开始，这个字段就废弃了，不再维护
     */
    private BigDecimal totalPrice;
    private BigDecimal totalCostPrice;
    /**
     * 从药店算费调整开始，这个字段就废弃了，不再维护
     */
    private BigDecimal discountPrice;
    /**
     * goods的原始金额
     */
    private BigDecimal sourceTotalPrice;

    /**
     * 实收金额
     */
    private BigDecimal receivedPrice;

    @Column(name = "isDismounting")
    private int useDismounting;

    @Transient
    private ChargeTransactionRecordAdditional additional;

    @Transient
    List<ChargeTransactionRecordBatchInfo> batchInfos;

    /**
     * 场景类型：0：普通收退费，1：欠收，2：欠退
     * {@link SceneType}
     */
    private int sceneType;

    /**
     * 导入数据标记，0：非导入数据，非0都是导入数据
     * 对应收费单上的import_flag含义，但是导入进来的单据再次操作收退费，那这个单子的标记就不是导入标记了
     * 1：导入数据，2：导入的欠费单产生的明细数据，3：导入的收费单产生的明细数据
     * {@link ChargeSheet.ImportFlag}
     */
    private int importFlag;

    /**
     * 费用类型
     */
    private int feeComposeType;

    /**
     * 费用类型id
     */
    private Long feeTypeId;

    private int goodsFeeType;

    private int isOldRecord;

    private int isDeleted;

    private String createdBy;

    private Instant created;

    private String lastModifiedBy;

    private Instant lastModified;

    @Transient
    @JsonIgnore
    private boolean cold;

    public boolean checkGroupByItemIdIsNotEmpty() {

        if (type == RecordType.AIR_PHARMACY && additional != null && StringUtils.isNotEmpty(additional.getChargeFormId())) {
            return true;
        }
        return StringUtils.isNotEmpty(chargeFormItemId);
    }

    public String getGroupByItemId() {

        boolean itemIdIsNotEmpty = checkGroupByItemIdIsNotEmpty();

        if (!itemIdIsNotEmpty) {
            return "";
        }

        if (type == RecordType.AIR_PHARMACY) {
            return additional.getChargeFormId();
        }
        return chargeFormItemId;
    }

    public BigDecimal getDeductTotalPrice() {
        return Optional.ofNullable(additional)
                .map(ChargeTransactionRecordAdditional::getDeductTotalPrice)
                .orElse(BigDecimal.ZERO);
    }

    public BigDecimal getDeductTotalCostPrice() {
        return Optional.ofNullable(additional)
                .map(ChargeTransactionRecordAdditional::getDeductTotalCostPrice)
                .orElse(BigDecimal.ZERO);
    }

    public static class ChargeType {
        public static final int CHARGED = 1;
        public static final int PART_CHARGED = 0;
        public static final int REFUND = -1;
    }

    public static class RecordType {
        public static final int PRODUCT = 0;
        public static final int ADJUSTMENT_FEE = 1;
        public static final int MEMBER_CARD_RECHARGE= 2;
        public static final int ROUNDING_FEE= 3;
        public static final int AIR_PHARMACY = 4;
    }

    public static class SceneType {
        //0：普通收退费，1：欠收，2：欠退
        /**
         * 普通收退费
         */
        public static final int NORMAL = 0;

        /**
         * 欠收
         */
        public static final int OWE_PAY = 1;

        /**
         * 欠退
         */
        public static final int OWE_REFUND = 2;
    }
}
