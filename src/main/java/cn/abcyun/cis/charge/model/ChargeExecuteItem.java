package cn.abcyun.cis.charge.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.hibernate.annotations.SQLDelete;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.Instant;

@Entity
@SQLDelete(sql = "UPDATE v2_charge_execute_item SET is_deleted=1 WHERE id=?")
@Table(name = "v2_charge_execute_item")
@Data
public class ChargeExecuteItem {
    @Id
    private String id;
    @JsonIgnore
    private String patientOrderId;
    @JsonIgnore
    private String clinicId;
    @JsonIgnore
    private String chainId;
    @JsonIgnore
    private String chargeSheetId;
    @JsonIgnore
    private String chargeFormId;
    private String chargeFormItemId;
    private String productId;
    private int productType;
    private int productSubType;
    private String name;
    private BigDecimal unitCount;
    private BigDecimal executedCount;
    private String unit;

    @Transient
    private int executeStatus;

    @Transient
    private String executeStatusName;

    @JsonIgnore
    private int isDeleted;
    @JsonIgnore
    private String createdBy;
    @JsonIgnore
    private Instant created;
    @JsonIgnore
    private String lastModifiedBy;
    @JsonIgnore
    private Instant lastModified;

    @PrePersist
    @PreUpdate
    public void defaultFieldValues() {

        if (chainId == null) {
            chainId = "";
        }

        if (unitCount == null) {
            unitCount = BigDecimal.ZERO;
        }

        if (executedCount == null) {
            executedCount = BigDecimal.ZERO;
        }

        if (unit == null) {
            unit = "";
        }
    }

    public static class ExecuteStatus {
        public static final int NONE = 0;
        public static final int WAITING = 1;
        public static final int FINISHED = 2;
    }
}
