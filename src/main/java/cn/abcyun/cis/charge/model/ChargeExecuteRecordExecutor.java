package cn.abcyun.cis.charge.model;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.time.Instant;

/**
 * 执行记录执行人
 *
 * <AUTHOR>
 * @version ChargeExecuteRecordExecutor.java, 2020/7/30 下午2:42
 */
@Data
@Accessors(chain = true)
@Entity
@Table(name = "v2_charge_execute_record_executor")
public class ChargeExecuteRecordExecutor {
    /**
     * 主键id
     */
    @Id
    private Long       id;
    /**
     * 连锁id
     */
    private String     chainId;
    /**
     * 门店id
     */
    private String     clinicId;
    /**
     * 执行记录id
     */
    private String     executeRecordId;
    /**
     * 执行人id
     */
    private String     executorId;
    /**
     * 分成比例
     */
    private BigDecimal proportion;
    /**
     * 创建时间
     */
    private Instant    created;
    /**
     * 创建人
     */
    private String     createdBy;
    /**
     * 最后修改时间
     */
    private Instant    lastModified;
    /**
     * 最后修改人
     */
    private String     lastModifiedBy;
    /**
     * 收费单id
     */
    private String     chargeSheetId;
    /**
     * 收费单执行状态（0: 无执行项；1: 待执行；2: 已执行） {@link cn.abcyun.cis.charge.model.ChargeSheet.ExecuteStatus}
     */
    private Integer        chargeSheetExecuteStatus;
    /**
     * 执行记录状态（0:有效；1：撤销）  {@link ChargeExecuteRecord.ExecuteRecordStatus}
     */
    private Integer        executeRecordStatus;
}
