package cn.abcyun.cis.charge.model;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.Instant;

@Data
@Entity
@Table(name = "v2_charge_air_pharmacy_order_action")
public class ChargeAirPharmacyOrderAction {

    @Id
    private String id;

    private String payId;

    private String chainId;

    private String clinicId;

    private String paidBy;

    private String orderIds;

    private Instant created;

    private String createdBy;

}
