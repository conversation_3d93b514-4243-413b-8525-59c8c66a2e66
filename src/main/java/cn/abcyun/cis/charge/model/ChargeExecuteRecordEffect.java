package cn.abcyun.cis.charge.model;

import com.vladmihalcea.hibernate.type.json.JsonStringType;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * 执行记录效果
 *
 * <AUTHOR>
 * @version ChargeExecuteRecordEffect.java, 2020/11/30 下午3:37
 */
@Data
@Accessors(chain = true)
@Entity
@Table(name = "v2_charge_execute_record_effect")
@TypeDef(name = "json", typeClass = JsonStringType.class)
public class ChargeExecuteRecordEffect {
    @Id
    private Long                                      id;
    /**
     * 执行记录id
     */
    private String                                    executeRecordId;
    /**
     * 治疗方法
     */
    private String                                    treatmentMethod;
    /**
     * 治疗部位
     */
    private String                                    treatmentSite;
    /**
     * 治疗反应
     */
    private String                                    treatmentResponse;
    /**
     * 病因病机
     */
    private String                                    etiologyPathogenesis;
    /**
     * 治疗结果
     */
    private String                                    treatmentResult;
    /**
     * 执行时间(yyyy-MM-dd)
     */
    private LocalDate                                 executeDate;
    /**
     * 执行时间(LocalTime范围开始)
     */
    private LocalTime                                 executeTimeStart;
    /**
     * 执行时间(LocalTime范围结束)
     */
    private LocalTime                                 executeTimeEnd;
    /**
     * 附件
     */
    @Column(columnDefinition = "json")
    @Type(type = "json")
    private List<ChargeExecuteRecordEffectAttachment> attachments;
    /**
     * 是否删除
     */
    private Integer                                   isDeleted;
    /**
     * 创建时间
     */
    private Instant                                   created;
    /**
     * 创建人
     */
    private String                                    createdBy;
    /**
     * 最近修改时间
     */
    private Instant                                   lastModified;
    /**
     * 最近修改人
     */
    private String                                    lastModifiedBy;
}
