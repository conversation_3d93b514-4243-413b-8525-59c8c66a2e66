package cn.abcyun.cis.charge.model;

import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.combinorder.dto.ChargePayModeInfo;
import cn.abcyun.cis.charge.util.ChargePayModeUtils;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Type;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Objects;

@Data
@Entity
@Table(name = "v2_charge_owe_combine_transaction")
@Accessors(chain = true)
public class ChargeOweCombineTransaction implements Serializable {


	/**
	 * 主键id
	 */
	@Id
	private Long id;

	/**
	 * 连锁id
	 */
	private String chainId;

	/**
	 * 诊所id
	 */
	private String clinicId;

	/**
	 * 组合支付订单id
	 */
	private Long combineOrderId;

	/**
	 * 组合支付订单流水id
	 */
	private Long combineOrderTransactionId;

	/**
	 * 业务id
	 */
	private Long businessId;

	private int isPaidBack;

	/**
	 * 订单来源：0未知，1：住院结算单，2：欠费单
	 */
	private int source;

	/**
	 * 退款时关联的对应支付时的id
	 */
	private Long associateTransactionId;

	/**
	 * 第三方支付流水id
	 */
	private String thirdPartyPayTransactionId;

	/**
	 * 第三方支付卡id
	 */
	private String thirdPartyPayCardId;

	/**
	 * 第三方支付卡余额
	 */
	private BigDecimal thirdPartyPayCardBalance;

	/**
	 * 第三方支付信息
	 */
	@Type(type = "json")
	@Column(columnDefinition = "json")
	private ThirdPartyPayInfo thirdPartyPayInfo;

	/**
	 * 第三方支付订单号
	 */
	private String thirdPartyPayOrderId;

	/**
	 * 支付方式
	 */
	private int payMode;

	/**
	 * 支付方式子类型
	 */
	private int paySubMode;

	/**
	 * 支付方式名称 已作废，用payModeInfo代替
	 */
	@Deprecated
	private String payModeDisplayName;

	@org.hibernate.annotations.Type(type = "json")
	@Column(columnDefinition = "json")
	private ChargePayModeInfo payModeInfo;

	/**
	 * 类型：1：支付，2：退费
	 */
	private int type;

	/**
	 * 金额
	 */
	private BigDecimal amount;

	/**
	 * 退款金额(负值)
	 */
	private BigDecimal refundedAmount;

	/**
	 * 赠金支付金额
	 */
	private BigDecimal presentAmount;

	/**
	 * 本金支付金额
	 */
	private BigDecimal principalAmount;

	/**
	 * 交易备注
	 */
	private String chargeComment;

	@Transient
	private List<ChargeOweCombineTransactionRecord> transactionRecords;


	/**
	 * 删除状态：0：未删除，1：已删除
	 */
	private int isDeleted;

	/**
	 * 创建时间
	 */
	private Instant created;

	/**
	 * 创建人
	 */
	private String createdBy;

	/**
	 * 最后修改人
	 */
	private Instant lastModified;

	/**
	 * 最后修改时间
	 */
	private String lastModifiedBy;

	public String getPayModeDisplayName() {
		if (Objects.nonNull(payModeInfo)) {
			return ChargePayModeUtils.convertPayModeDisplayNameForTransaction(payModeInfo.getPayMode(), payModeInfo.getPaySubMode(), payModeInfo.getPayModeName(), payModeInfo.getPaySubModeName());
		}

		return payModeDisplayName;
	}

	public static String generateTransactionUniqueKey(ChargeOweCombineTransaction transaction) {

		if (transaction == null) {
			return "";
		}

		if (transaction.getPayMode() == Constants.ChargePayMode.PROMOTION_CARD) {
			return String.format("%d-%s", transaction.getPayMode(), transaction.getThirdPartyPayCardId());
		}
		if (transaction.getPayMode() == Constants.ChargePayMode.ABC_PAY) {
			if (transaction.getAmount().compareTo(BigDecimal.ZERO) >= 0) {
				return String.format("%d-%d-%s", transaction.getPayMode(), transaction.getPaySubMode(), transaction.getId());
			} else {
				return String.format("%d-%d-%s", transaction.getPayMode(), transaction.getPaySubMode(), transaction.getAssociateTransactionId());
			}
		}
		return String.valueOf(transaction.getPayMode());
	}
}
