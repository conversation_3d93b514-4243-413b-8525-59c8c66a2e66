package cn.abcyun.cis.charge.model;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.time.Instant;
import java.util.Date;
import java.util.Objects;

/**
 * 执行记录
 *
 * <AUTHOR>
 * @version ChargeExecuteRecord.java, 2020/7/30 下午1:59
 */
@Data
@Accessors(chain = true)
@Entity
@Table(name = "v2_charge_execute_record")
public class ChargeExecuteRecord {
    /**
     * 主键id
     */
    @Id
    private String  id;
    /**
     * 收费单id
     */
    private String  chargeSheetId;
    /**
     * 门店id
     */
    private String  clinicId;
    /**
     * 连锁id
     */
    private String  chainId;
    /**
     * 患者id
     */
    private String  patientId;
    /**
     * 本次执行总次数
     */
    private Integer count;
    /**
     * 执行状态（0：有效；1：撤销） {{@link ExecuteRecordStatus}}
     */
    private Integer status;
    /**
     * 创建时间
     */
    private Instant created;
    /**
     * 创建人
     */
    private String  createdBy;
    /**
     * 最后修改时间
     */
    private Instant lastModified;
    /**
     * 最后修改人
     */
    private String  lastModifiedBy;
    /**
     * 执行门店id
     */
    private String  executeClinicId;
    /**
     * 是否是导入；0：否；1：是
     */
    private Integer isImport;
    /**
     * 备注
     */
    private String  comment;
    /**
     * 执行效果是否对患者可见；0：否；1：是
     */
    private Integer effectVisibleForPatient;
    /**
     * 是否需要执行记录；0：否；1：是
     */
    private int     needExecuteEffect;
    /**
     * 执行地点
     */
    private String  homeCareAddress;
    /**
     * 执行开始时间
     */
    private String  homeCareStartTime;
    /**
     * 执行结束时间
     */
    private String  homeCareEndTime;
    /**
     * 是否需要上门服务
     */
    private int     needHomeCare;

    private Date executeTime;

    @PrePersist
    @PreUpdate
    public void beforeSave() {

        if (Objects.isNull(executeTime) && Objects.nonNull(created)) {
            executeTime = Date.from(created);
        }
    }

    public Date getExecuteTime() {
        return Objects.nonNull(executeTime) ? executeTime : Date.from(created);
    }

    public static class ExecuteRecordStatus {
        /**
         * 有效
         */
        public static final int EXECUTED = 0;
        /**
         * 撤销
         */
        public static final int CANCELED = 1;
    }
}
