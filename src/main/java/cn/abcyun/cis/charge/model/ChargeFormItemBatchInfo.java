package cn.abcyun.cis.charge.model;

import cn.abcyun.cis.charge.processor.limitprice.BaseLimitPriceInfo;
import cn.abcyun.cis.charge.service.dto.ChargeDiscountInfo;
import cn.abcyun.cis.charge.util.ChargeFormItemBatchInfoUtils;
import cn.abcyun.cis.charge.util.FillUtils;
import cn.abcyun.cis.charge.util.MathUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.vladmihalcea.hibernate.type.json.JsonStringType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;

import javax.persistence.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Entity
@Table(name = "v2_charge_form_item_batch_info")
@Data
@TypeDef(name = "json", typeClass = JsonStringType.class)
@AllArgsConstructor
@NoArgsConstructor
public class ChargeFormItemBatchInfo {


    @Id
    private String id;

    private String clinicId;

    private String chainId;

    private String patientOrderId;

    private String chargeSheetId;

    private String chargeFormId;

    private String chargeFormItemId;

    private String associateItemBatchInfoId; //退款项关联的chargeFormItemId

    /**
     * 批次成本价
     */
    private BigDecimal unitCostPrice;

    private BigDecimal totalCostPrice;


    /**
     * 总价格
     */
    private BigDecimal totalPrice;

    @Column(name = "batchSourceTotalPrice")
    private BigDecimal sourceTotalPrice;

    private BigDecimal sourceUnitPrice;

    /**
     * 批次的总数量
     */
    private BigDecimal unitCount;
    /**
     * 所有要区分大小单位的场景，都应该加这个变量解决误差问题
     * 总的扣库数量
     * 2024.11 清道夫，后台需要把这个变量吐出去，误差问题
     * 背景：3片/盒的药品，两个批次 1片 2片->如果goods只给出去 0.333盒 0.667盒 -->收费拿着这个0.667盒再来锁库
     * 0.667*3 == 2.001片会提示库存不足。所以后台需要把 0.667 对应的2片吐出去，锁库的时候带 0.667盒和2片上来
     *  == cutTotalPieceCount
     */
    @Transient
    @JsonIgnore
    private BigDecimal totalBatchPieceCount;

//    private BigDecimal doseCount;

    /**
     * 售价
     */
    private BigDecimal unitPrice;

    private BigDecimal refundUnitCount;

//    private BigDecimal refundDoseCount;

    /**
     * 已退总价
     */
    private BigDecimal refundTotalPrice;

    /**
     * 产品id
     */
    private String productId;

    /**
     * 该批次是否限价
     */
    private int isUseLimitPrice;

    private String stockId;

    private String batchId;
    private String batchNo;

    private BigDecimal receivablePrice;

    /**
     * 效期
     */
    private String expiryDate;

    /**
     * 营销折扣信息
     */
    @Type(type = "json")
    @Column(columnDefinition = "json")
    private ChargeDiscountInfo promotionInfo;


    @Type(type = "json")
    @Column(columnDefinition = "json")
    private BaseLimitPriceInfo.LimitInfo limitInfo;

    private BigDecimal expectedTotalPrice;

    @Type(type = "json")
    @Column(columnDefinition = "json")
    private List<BatchTraceableCode> traceableCodes;

    /**
     * 实收金额
     */
    private BigDecimal receivedPrice;

    @JsonIgnore
    @Transient
    private BigDecimal thisTimeReceivableFee = BigDecimal.ZERO;
//    /**
//     * 抵扣数量
//     */
//    private BigDecimal deductTotalCount;

//    /**
//     *  0:当前批次 10:收费批次 20:重新发药作废批次
//     */
//    private Integer dataType;

    /**
     * 是否为老数据
     */
    private int isOld;

    /**
     * 是否不为收费数据
     */
    private int isNotCharged;

    private int isDeleted;

    private String createdBy;

    private Instant created;

    private String lastModifiedBy;

    private Instant lastModified;

//    @Transient
//    private BigDecimal canRefundCount;

    @Transient
    private BigDecimal dispensedRefundUnitCount;

    @Transient
    private BigDecimal dispensedUnitCount;


    public BigDecimal getTotalCostPrice() {

        if (totalCostPrice != null) {
            return totalCostPrice;
        }

        return MathUtils.wrapBigDecimalMultiply(unitCostPrice, unitCount).setScale(4, RoundingMode.HALF_UP);
    }

    public void calculateTotalPriceHalfDown() {
        totalPrice = MathUtils.calculateTotalPrice(unitPrice, unitCount).setScale(2, RoundingMode.HALF_DOWN);
    }

    public void calculateSourceTotalPriceHalfDown() {
        sourceTotalPrice = MathUtils.calculateTotalPrice(unitPrice, unitCount).setScale(2, RoundingMode.HALF_DOWN);
    }

    public BigDecimal getCanRefundCount() {
        /**
         * 这可退的数量 为该批次的总数量减去已经退的数量   减去已经发药的数量  加上退药数量
         * 目前药房那边可以部分发药 但是只有全部发完才能退药
         * 所以当退药数量存在时（大于0）表示已经全部发药
         */
        return MathUtils.wrapBigDecimalSubtract(MathUtils.wrapBigDecimalSubtract(unitCount, refundUnitCount), MathUtils.max(BigDecimal.ZERO, MathUtils.wrapBigDecimalSubtract(dispensedUnitCount, dispensedRefundUnitCount)));
    }

    /**
     * 剩余可退的数量
     * @return
     */
    public BigDecimal getLeftCanRefundCount() {
        return MathUtils.wrapBigDecimalSubtract(unitCount, refundUnitCount);
    }

    public void addRefundUnitCount(BigDecimal refundUnitCount) {
        BigDecimal existedRefundUnitCount = this.refundUnitCount;
        this.refundUnitCount = MathUtils.wrapBigDecimalAdd(existedRefundUnitCount, refundUnitCount);
        if (MathUtils.wrapBigDecimalCompare(this.refundUnitCount, unitCount) > 0) {
            throw new IllegalStateException(String.format("退费数量超过了收费数量，检查代码，id: %s, chargedUnitCount: %s, existedRefundUnitCount: %s, refundUnitCount: %s", id, unitCount, existedRefundUnitCount, this.refundUnitCount));
        }
    }

    public void addRefundTotalPrice(BigDecimal totalPrice) {
        BigDecimal existedRefundTotalPrice = this.refundTotalPrice;
        this.refundTotalPrice = MathUtils.wrapBigDecimalAdd(existedRefundTotalPrice, totalPrice);
        if (MathUtils.wrapBigDecimalCompare(this.refundTotalPrice, this.totalPrice) > 0) {
            throw new IllegalStateException(String.format("退费总价超过了收费总价，检查代码，id: %s, totalPrice: %s, existedRefundTotalPrice: %s, refundTotalPrice: %s", id, this.totalPrice, existedRefundTotalPrice, this.refundTotalPrice));
        }
    }

    /**
     *
     * @return
     */
    public BigDecimal calculateReceivableFee() {
        //这里真实的步骤是：原价 + 单项优惠 + 单项议价 + 整单优惠 + 整单议价 + 限价金额
        BigDecimal receivable = getSourceTotalPrice()
                .add(calculateSinglePromotionPrice())
                .add(MathUtils.wrapBigDecimalOrZero(calculateUnitAdjustmentFee()))
                .add(calculatePackagePromotionPrice())
                .add(calculateListingDiscountFee())
                .add(MathUtils.wrapBigDecimalOrZero(calculateAdjustmentFee()));

        if (isUseLimitPrice == 1 && Objects.nonNull(limitInfo) && limitInfo.getExceedLimitPriceRule() == 0) {
            receivable = receivable.add(MathUtils.wrapBigDecimalOrZero(calculateLimitFee()));
        }
        this.receivablePrice = receivable;

        return receivable;

    }

    public BigDecimal calculateListingDiscountFee() {
        return Optional.ofNullable(promotionInfo)
                .map(ChargeDiscountInfo::getListingDiscountFee)
                .orElse(BigDecimal.ZERO);
    }

    /**
     * totalPrice计算
     *
     * @return
     */
    public BigDecimal calculateTotalPrice() {
        //这里真实的步骤是：totalPrice = 单项议价 + 整单议价（正值） + 挂网价限价 + 限价金额
        BigDecimal totalPrice = getSourceTotalPrice()
                .add(MathUtils.wrapBigDecimalOrZero(calculateUnitAdjustmentFee()))
                .add(calculateListingDiscountFee());

        if (isUseLimitPrice == 1 && Objects.nonNull(limitInfo) && limitInfo.getExceedLimitPriceRule() == 0) {
            totalPrice.add(MathUtils.wrapBigDecimalOrZero(calculateLimitFee()));
        }
        this.totalPrice = totalPrice;
        return totalPrice;

    }

    /**
     * totalPrice计算
     *
     * @return
     */
    public BigDecimal calculateTotalPriceV2() {
        //这里真实的步骤是：totalPrice = 单项议价 + 限价金额
        BigDecimal totalPrice = getSourceTotalPrice()
                .add(MathUtils.wrapBigDecimalOrZero(calculateSinglePromotionPrice()))
                .add(MathUtils.wrapBigDecimalOrZero(calculateUnitAdjustmentFee()))
                .add(calculateListingDiscountFee());

        if (isUseLimitPrice == 1 && Objects.nonNull(limitInfo) && limitInfo.getExceedLimitPriceRule() == 0) {
            totalPrice.add(MathUtils.wrapBigDecimalOrZero(calculateLimitFee()));
        }
        this.totalPrice = totalPrice;
        return totalPrice;

    }

    public BigDecimal calculateAdjustmentFee() {
        return Optional.ofNullable(promotionInfo)
                .map(ChargeDiscountInfo::getAdjustmentFee)
                .orElse(BigDecimal.ZERO);
    }

    public BigDecimal calculateUnitAdjustmentFee() {
        return Optional.ofNullable(promotionInfo)
                .map(ChargeDiscountInfo::getUnitAdjustmentFee)
                .orElse(BigDecimal.ZERO);
    }

    public BigDecimal calculatePackagePromotionPrice() {
        ChargeDiscountInfo promotionInfo = getPromotionInfo();
        if (promotionInfo == null) {
            return BigDecimal.ZERO;
        }

        return promotionInfo.calculatePackagePromotionPrice();
    }

    public BigDecimal calculateAfterUnitAdjustmentFeeTotalPrice() {
        return getSourceTotalPrice().add(calculateUnitAdjustmentFee());
    }

    public BigDecimal calculateLimitFee() {
        return Optional.ofNullable(promotionInfo)
                .map(ChargeDiscountInfo::getLimitFee)
                .orElse(BigDecimal.ZERO);
    }

    //计算单项优惠的值
    public BigDecimal calculateSinglePromotionPrice() {
        ChargeDiscountInfo promotionInfo = getPromotionInfo();
        if (promotionInfo == null) {
            return BigDecimal.ZERO;
        }

        return promotionInfo.calculateSinglePromotionPrice();
    }

    public void calculateTotalCostPrice() {
        totalCostPrice = MathUtils.wrapBigDecimalMultiply(unitCostPrice, unitCount);
    }

    @PrePersist
    @PreUpdate
    public void defaultFieldValues() {
        if (unitCostPrice == null) {
            unitCostPrice = BigDecimal.ZERO;
        }
        if (totalPrice == null) {
            totalPrice = BigDecimal.ZERO;
        }

        if (unitCount == null) {
            unitCount = BigDecimal.ZERO;
        }

        if (unitPrice == null) {
            unitPrice = BigDecimal.ZERO;
        }

        if (refundUnitCount == null) {
            refundUnitCount = BigDecimal.ZERO;
        }

        if (refundTotalPrice == null) {
            refundTotalPrice = BigDecimal.ZERO;
        }

        if (sourceUnitPrice == null) {
            sourceUnitPrice = BigDecimal.ZERO;
        }

    }

    public void addUnitAdjustmentFee(BigDecimal unitAdjustmentFee) {
        ChargeDiscountInfo promotionInfo = Optional.ofNullable(this.getPromotionInfo()).orElse(new ChargeDiscountInfo());
        promotionInfo.setUnitAdjustmentFee(unitAdjustmentFee);
        this.setPromotionInfo(promotionInfo);
    }

    /**
     * goods 会有 脏数据字符串 这里单独处理保护
     * @param expiryDate
     */
    public void setExpiryDate(String expiryDate) {
        this.expiryDate = ChargeFormItemBatchInfoUtils.dealBatchExpireDate(expiryDate);
    }

    public void deleteModel(String operatorId) {
        isDeleted = 1;
        FillUtils.fillLastModifiedBy(this, operatorId);
    }
}
