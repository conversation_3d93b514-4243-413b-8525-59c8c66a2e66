package cn.abcyun.cis.charge.model;

import cn.abcyun.cis.commons.model.CisPatientAge;
import cn.abcyun.cis.core.db.encrypt.JpaEncryptConverter;
import cn.abcyun.cis.core.db.encrypt.SensitiveEntity;
import com.vladmihalcea.hibernate.type.json.JsonStringType;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;

import javax.persistence.*;
import java.io.Serializable;
import java.time.Instant;

@Data
@Entity
@Table(name = "v2_charge_sheet_register_identity")
@Accessors(chain = true)
@TypeDef(name = "json", typeClass = JsonStringType.class)
@SensitiveEntity
public class ChargeSheetRegisterIdentity implements Serializable {

	@Id
	private Long id;

	/**
	 * 连锁id
	 */
	private String chainId;

	/**
	 * 诊所id
	 */
	private String clinicId;

	/**
	 * 姓名
	 */
	private String name;

	/**
	 * 生日，当实名登记时，此处为生日，age填空
	 */
	private String birthday;

	/**
	 * 年龄，当处理登记时，此处为登记时的年龄，birthday填空
	 */
	@Type(type = "json")
	@Column(columnDefinition = "json")
	private CisPatientAge age;

	/**
	 * 性别
	 */
	private String sex;

	/**
	 * 证件类型
	 */
	private String idCardType;

	/**
	 * 证件号
	 */
	@Convert(converter = JpaEncryptConverter.class)
	private String idCard;

	/**
	 * 手机号码
	 */
	@Convert(converter = JpaEncryptConverter.class)
	private String mobile;

	/**
	 * 手机号后4位
	 */
	private String mobileLast4;

	/**
	 * 当前版本
	 */
	private String version;

	/**
	 * 是否删除状态（0：正常；1：被删除）
	 */
	private Integer isDeleted;

	/**
	 * 最后修改人
	 */
	private String lastModifiedBy;

	/**
	 * 最后修改时间
	 */
	private Instant lastModified;

	/**
	 * 创建时间
	 */
	private Instant created;

	/**
	 * 创建人
	 */
	private String createdBy;
}
