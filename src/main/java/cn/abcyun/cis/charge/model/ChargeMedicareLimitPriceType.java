package cn.abcyun.cis.charge.model;

import cn.abcyun.cis.charge.base.Constants;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.io.Serializable;
import java.lang.reflect.Array;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * v2_charge_medicare_limit_price_type
 * <AUTHOR>
@Entity
@Table(name = "v2_charge_medicare_limit_price_type")
@Data
@Accessors(chain = true)
public class ChargeMedicareLimitPriceType {

    @Id
    private Long id;

    private String clinicId;

    private String chainId;

    /**
     * 开关：0：未开启，1：已开启
     */
    private Integer openSwitch;

    /**
     * 价格类别：1：最近进价，2：销售限价
     */
    private Integer priceType;

//    /**
//     * 限价比例：15， -15
//     */
//    private Integer limitRate;


    @Type(type = "json")
    @Column(columnDefinition = "json")
    private List<LimitDetail> limitDetail;

    /**
     * 类别： 1：西成药; 2：中药; 3：医用材料; 4：检查校验; 5：治疗项目，6：理疗项目
     */
    private Integer type;

    /**
     * 是否删除状态（0：正常；1：被删除）
     */
    private int isDeleted;

    @Column(name = "gmt_create")
    private Instant create;

    @Column(name = "gmt_modified")
    private Instant modified;

    /**
     * 最后的修改人
     */
    private String lastModifiedBy;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 定价超限价收费规则：0超过部分不收，1超过部分患者自费
     * {@link Constants.ExceedLimitPriceRule}
     */
    private int exceedLimitPriceRule;

    @Data
    public static class LimitDetail implements Serializable {
        private BigDecimal minPriceLimit;
        private BigDecimal maxPriceLimit;
        private Integer limitRate;

        /**
         * 计算限价比例转换为小数，比如：10%转换为1.1
         * @return
         */
        public BigDecimal calculateLimitRatePercent() {
            return new BigDecimal(100 + (Objects.isNull(limitRate) ? 0 : limitRate)).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        }
    }

    public static class priceType {
        public static final int NEAREST_COST_PRICE = 1;
        public static final int SALES_LIMIT_PRICE = 2;
        public static final int SHEBAO_PRICE = 3;
    }

    public static class LimitProductType {
        public static final int WESTERN_MEDICINE = 1;
        public static final int CHINESE_MEDICINE = 2;
        public static final int MATERIALS = 3;
        public static final int INSPECTION = 4;
        public static final int TREATMENT = 5;
        public static final int PHYSIOTHERAPY = 6;
        public static final int OTHER_FEE = 7;


        public static final List<Integer> HospitalProductType = Arrays.asList(WESTERN_MEDICINE, CHINESE_MEDICINE, MATERIALS, OTHER_FEE);
        public static final List<Integer> NotHospitalProductType = Arrays.asList(WESTERN_MEDICINE, CHINESE_MEDICINE, MATERIALS, INSPECTION, TREATMENT, PHYSIOTHERAPY);
    }
}