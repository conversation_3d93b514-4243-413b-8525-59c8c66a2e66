package cn.abcyun.cis.charge.model;

import cn.abcyun.bis.rpc.sdk.cis.model.common.BitFlagUtils;
import cn.abcyun.cis.charge.base.ChargeVersionConstants;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.service.dto.ChargeSheetRelationDto;
import cn.abcyun.cis.charge.util.FillUtils;
import cn.abcyun.cis.commons.rpc.charge.DiagnosisInfo;
import cn.abcyun.cis.commons.rpc.outpatient.ExtendDiagnosisInfo;
import cn.abcyun.cis.commons.rpc.patient.MemberInfo;
import cn.abcyun.cis.commons.util.ListUtils;
import cn.abcyun.cis.commons.util.MathUtils;
import cn.abcyun.cis.commons.util.TextUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;

@Data
@Entity
@Table(name = "v2_charge_sheet")
@AllArgsConstructor
@NoArgsConstructor
public class ChargeSheet {

    public ChargeSheet(String id, int transactionRecordHandleMode) {
        this.id = id;
        this.transactionRecordHandleMode = transactionRecordHandleMode;
    }

    @Id
    private String id;
    private String patientOrderId;
    private String patientId;
    private String chainId;
    private String clinicId;
    private String memberId;
    private String sellerId;
    /**
     * 销售员的部门id
     */
    private String sellerDepartmentId;
    private String doctorId;
    private String copywriterId;

    /**
     * 住院登记单id
     */
    private Long hospitalOrderId;

    /**
     * 住院结算单id
     */
    private Long hospitalSheetId;

    /**
     * 欠费状态：0：无欠费，10：欠费中
     * {@link OwedStatus}
     */
    private int owedStatus;

    @JsonIgnore
    @Column(name = "v1_id")
    private String v1Id;
    /**
     * 收费单状态
     *
     * @see cn.abcyun.cis.charge.base.Constants.ChargeSheetStatus
     */
    private int status;
    private int isDispensing;   //这个字段的含义是，是否收费的时候自动发药
    private int outpatientStatus;
    private BigDecimal outpatientAdjustmentFee; //门诊整单议价 //业务需求上来看门诊还能针对药品议价
    private BigDecimal draftAdjustmentFee; //收费整单议价
    private int executeStatus;
    private int importFlag;
    /**
     * 对于退费记录charge_transaction_record的处理方式，默认为0，表示使用新的退费记录方式，选择多少数量就记录多少数量，数量不会有小数，单独标识待退金额，1表示根据退的金额反算数量，数量会有小数
     */
    private int transactionRecordHandleMode;
    private Instant orderByDate;
    private Instant diagnosedDate;
    private Instant reserveDate;
    private int type;
    private String registrationChargeSheetId; //关联挂号收费单id
    /**
     * 收费单包含了哪些收费项
     * 使用位运算来存储
     * {@link Constants.ChargeSheetIncludeItemType}
     * example: 包含诊疗项目和输注和皮试 1|2|8 = 11，数据库中存储的值为11
     */
    private Integer includeItemType;

    //可支付的截止时间
    private Instant expireTime;

    @JsonIgnore
    private BigDecimal totalFee;            //总费用
    @JsonIgnore
    private BigDecimal additionalFee;       //附加费用【扣除】【负数】
    @JsonIgnore
    private BigDecimal discountFee;         //折扣【扣除】【负数】

    private BigDecimal refundFee;           //已退

    private BigDecimal receivableFee;       //应收

    private BigDecimal receivedFee;         //已收

    //    @OneToOne(cascade = {CascadeType.ALL})
//    @Where(clause = "is_deleted = 0")
//    @SQLDelete(sql = "UPDATE v2_charge_sheet_additional SET is_deleted=1 WHERE id=?")
//    @SQLDeleteAll(sql = "UPDATE v2_charge_sheet_additional SET is_deleted=1 WHERE id=?")
//    @PrimaryKeyJoinColumn
    @Transient
    private ChargeSheetAdditional additional;

    //    @OneToMany(cascade = CascadeType.ALL)
//    @JoinColumn(name = "chargeSheetId")
//    @Where(clause = "is_deleted = 0")
//    @SQLDelete(sql = "UPDATE v2_charge_form SET is_deleted=1 WHERE charge_sheet_id=? AND id=?")
//    @SQLDeleteAll(sql = "UPDATE v2_charge_form SET is_deleted=1 WHERE charge_sheet_id=?")
//    @SQLInsert(sql = "UPDATE v2_charge_form SET charge_sheet_id=?, is_deleted=0 WHERE id=?")
    @Transient
    private List<ChargeForm> chargeForms;

    @JsonIgnore
//    @OneToMany(cascade = CascadeType.ALL)
//    @JoinColumn(name = "chargeSheetId")
//    @Where(clause = "is_deleted = 0")
//    @SQLDelete(sql = "UPDATE v2_charge_additional_fee SET is_deleted=1 WHERE charge_sheet_id=? AND id=?")
//    @SQLDeleteAll(sql = "UPDATE v2_charge_additional_fee SET is_deleted=1 WHERE charge_sheet_id=?")
//    @SQLInsert(sql = "UPDATE v2_charge_additional_fee SET charge_sheet_id=?, is_deleted=0 WHERE id=?")
    @Transient
    private List<ChargeAdditionalFee> additionalFees;

    //    @JsonIgnore
//    @OneToMany(cascade = CascadeType.ALL)
//    @JoinColumn(name = "chargeSheetId")
//    @Where(clause = "is_deleted = 0")
//    @SQLDelete(sql = "UPDATE v2_charge_transaction SET is_deleted=1 WHERE charge_sheet_id=? AND id=?")
//    @SQLDeleteAll(sql = "UPDATE v2_charge_transaction SET is_deleted=1 WHERE charge_sheet_id=?")
//    @SQLInsert(sql = "UPDATE v2_charge_transaction SET charge_sheet_id=?, is_deleted=0 WHERE id=?")
    @Transient
    private List<ChargeTransaction> chargeTransactions;

    //    @OneToMany(cascade = CascadeType.ALL)
//    @JoinColumn(name = "chargeSheetId")
//    @Where(clause = "is_deleted = 0")
//    @SQLDelete(sql = "UPDATE v2_charge_action SET is_deleted=1 WHERE charge_sheet_id=? AND id=?")
//    @SQLDeleteAll(sql = "UPDATE v2_charge_action SET is_deleted=1 WHERE charge_sheet_id=?")
//    @SQLInsert(sql = "UPDATE v2_charge_action SET charge_sheet_id=?, is_deleted=0 WHERE id=?")
    @Transient
    private List<ChargeAction> chargeActions;

    //    @OneToMany(cascade = CascadeType.ALL)
//    @JoinColumn(name = "chargeSheetId")
//    @Where(clause = "is_deleted = 0")
//    @SQLDelete(sql = "UPDATE v2_charge_sheet_attachment SET is_deleted=1 WHERE charge_sheet_id=? AND id=?")
//    @SQLDeleteAll(sql = "UPDATE v2_charge_sheet_attachment SET is_deleted=1 WHERE charge_sheet_id=?")
//    @SQLInsert(sql = "UPDATE v2_charge_sheet_attachment SET charge_sheet_id=?, is_deleted=0 WHERE id=?")
    @Transient
    private List<ChargeSheetAttachment> attachments;


    @JsonIgnore
    @Column(name = "memberDiscountInfo")
    private String memberDiscountInfoJson;

    @Transient
    private JsonNode memberDiscountInfo;

    @JsonIgnore
    @Column(name = "promotionInfo")
    private String promotionInfoJson;

    @Transient
    private SheetPromotionInfo promotionInfo;


    //    @OneToMany(cascade = CascadeType.ALL)
//    @JoinColumn(name = "chargeSheetId")
//    @SQLDelete(sql = "delete from v2_charge_coupon_promotion_info WHERE charge_sheet_id=? AND id=?")
//    @SQLDeleteAll(sql = "delete from v2_charge_coupon_promotion_info WHERE charge_sheet_id=?")
//    @SQLInsert(sql = "UPDATE v2_charge_coupon_promotion_info SET charge_sheet_id=? WHERE id=?")
    @Transient
    private List<ChargeCouponPromotionInfo> couponPromotionInfos;

    //    @OneToMany(cascade = CascadeType.ALL)
//    @JoinColumn(name = "chargeSheetId")
//    @SQLDelete(sql = "delete from v2_charge_gift_promotion_info WHERE charge_sheet_id=? AND id=?")
//    @SQLDeleteAll(sql = "delete from v2_charge_gift_promotion_info WHERE charge_sheet_id=?")
//    @SQLInsert(sql = "UPDATE v2_charge_gift_promotion_info SET charge_sheet_id=? WHERE id=?")
    @Transient
    private List<ChargeGiftRulePromotionInfo> giftRulePromotionInfos;

    //    @OneToMany(cascade = CascadeType.ALL)
//    @JoinColumn(name = "chargeSheetId")
//    @SQLDelete(sql = "delete from v2_charge_patient_card_promotion_info WHERE charge_sheet_id=? AND id=?")
//    @SQLDeleteAll(sql = "delete from v2_charge_patient_card_promotion_info WHERE charge_sheet_id=?")
//    @SQLInsert(sql = "UPDATE v2_charge_patient_card_promotion_info SET charge_sheet_id=? WHERE id=?")
    @Transient
    private List<ChargePatientCardPromotionInfo> patientCardPromotionInfos;

    //    @OneToMany(cascade = CascadeType.ALL)
//    @JoinColumn(name = "chargeSheetId")
//    @SQLDelete(sql = "delete from v2_charge_verify_info WHERE charge_sheet_id=? AND id=?")
//    @SQLDeleteAll(sql = "delete from v2_charge_verify_info WHERE charge_sheet_id=?")
//    @SQLInsert(sql = "UPDATE v2_charge_verify_info SET charge_sheet_id=? WHERE id=?")
    @Transient
    private List<ChargeVerifyInfo> chargeVerifyInfos;


    /**
     * 积分抵现金规则
     */
    @Transient
    private ChargePatientPointsPromotionInfo patientPointsPromotionInfo;

    /**
     * 积分抵商品规则
     */
    @Transient
    private ChargePatientPointsDeductProductPromotionInfo patientPointsDeductProductPromotionInfo;

    @Transient
    private List<ChargeChangePayModeRecord> changePayModeRecords;

    @JsonIgnore
    @Column(name = "memberInfo")
    private String memberInfoJson;

    @Transient
    private MemberInfo memberInfo;

    private String chargedBy;

    private Instant chargedTime;

    private Instant firstChargedTime;

    @JsonIgnore
    @Version
    private int dataVersion;

    /**
     * 原始单据的数据版本号
     */
    private Integer sourceDataVersion;

    private int isDraft;

    private int isOnline;

    private int deliveryType;

    private int sendToPatientStatus;

    private int checkStatus;

    private int isDecoction;

    private int isClosed;

    @Transient
    private ChargeDeliveryInfo deliveryInfo;

    /**
     * 数据库中的虚拟药房的快递信息，用于特殊情况，在患者端未改地址时，需要比较是否为pc端为患者选择的快递信息，如果是，则不需要去计算快递费用了，直接用数据库的数据
     */
    @Transient
    private ChargeDeliveryInfo dbDeliveryInfo;

    @JsonIgnore
    private int isDeleted;
    @JsonIgnore
    private String createdBy;

    private Instant created;
    @JsonIgnore
    private String lastModifiedBy;

    private Instant lastModified;

    @JsonIgnore
    @Transient
    private Integer roundingType;

    /**
     * 零头处理的值
     */
    private BigDecimal oddFee;

    /**
     * 是否需要查询异常，数据库中默认值为0，表示没有异常
     * 使用位运算来存储
     * {@link Constants.ChargeSheetQueryExceptionType}
     * example: 需要查询社保和微信支付异常和abc支付异常  1|2|4 = 7，数据库中存储的值为7
     */
    private int queryExceptionType;

    /**
     * 销售单号
     */
    private String sellNo;

    /**
     * 来源单据id
     */
    private String sourceId;

    /**
     * 关联数据标记，位存储。有始终都要查的表，v2_charge_form，v2_charge_form_item，v2_charge_sheet_additional表始终都查，其他表按需查
     * 0x0001：v2_charge_form_item_batch_info
     * 0x0002：v2_charge_sheet_process_info
     * 0x0004：v2_charge_additional_fee
     * 0x0008：v2_charge_sheet_attachment
     * 0x0010：v2_charge_coupon_promotion_info
     * 0x0020：v2_charge_gift_promotion_info
     * 0x0040：v2_charge_patient_card_promotion_info
     * 0x0080：v2_charge_verify_info
     * 0x0100：v2_charge_patient_points_promotion_info
     * 0x0200：v2_charge_patient_points_deduct_promotion_info
     * {@link DataFlag}
     */
    private Integer dataFlag;

    /**
     * 草稿类型，null历史,1表示
     */
    @Transient
    private int draftType;

    @Transient
    private ChargeSheetRelationDto deletedDataCollector;

    public ChargeSheetRelationDto getDeletedDataCollector() {
        if (deletedDataCollector == null) {
            deletedDataCollector = new ChargeSheetRelationDto();
        }
        return deletedDataCollector;
    }

    public void addDeletedData(ChargeSheetRelationDto toAddDeletedData) {
        deletedDataCollector = ChargeSheetRelationDto.merge(deletedDataCollector, toAddDeletedData);
    }

    @PrePersist
    @PreUpdate
    public void defaultFieldValues() {
        if (chainId == null) {
            chainId = "";
        }

        if (createdBy == null || created == null) {
            createdBy = lastModifiedBy;
            created = lastModified;
        }

        if (orderByDate == null) {
            orderByDate = Instant.now();
        }


        if (sellerId == null) {
            sellerId = "";
        }

        if (memberId == null) {
            memberId = "";
        }

        if (doctorId == null) {
            doctorId = "";
        }

        if (totalFee == null) {
            totalFee = BigDecimal.ZERO;
        }

        if (additionalFee == null) {
            additionalFee = BigDecimal.ZERO;
        }

        if (discountFee == null) {
            discountFee = BigDecimal.ZERO;
        }

        if (refundFee == null) {
            refundFee = BigDecimal.ZERO;
        }

        if (receivableFee == null) {
            receivableFee = BigDecimal.ZERO;
        }

        if (receivedFee == null) {
            receivedFee = BigDecimal.ZERO;
        }

        if (draftAdjustmentFee == null) {
            draftAdjustmentFee = BigDecimal.ZERO;
        }

        if (TextUtils.isEmpty(memberDiscountInfoJson)) {
            memberDiscountInfoJson = null;
        }

        if (TextUtils.isEmpty(promotionInfoJson)) {
            promotionInfoJson = null;
        }

//        if (TextUtils.isEmpty(couponPromotionInfoJson)) {
//            couponPromotionInfoJson = null;
//        }
//
//        if (TextUtils.isEmpty(giftRulePromotionInfoJson)) {
//            giftRulePromotionInfoJson = null;
//        }

        if (TextUtils.isEmpty(memberDiscountInfoJson)) {
            memberDiscountInfoJson = null;
        }

        if (copywriterId == null) {
            copywriterId = "";
        }

        if (oddFee == null) {
            oddFee = BigDecimal.ZERO;
        }

        if (outpatientAdjustmentFee == null) {
            outpatientAdjustmentFee = BigDecimal.ZERO;
        }
    }

    @JsonIgnore
    public int getClonePrescriptionType() {
        if (additional == null) {
            return ChargeSheetAdditional.ClonePrescriptionType.NONE;
        }
        return additional.getClonePrescriptionType();
    }

    @JsonIgnore
    public int getLockStatus() {
        if (additional == null) {
            return Constants.ChargeSheetLockStatusV2.NONE;
        }

        if (additional.getLockStatusV2() > 0) {
            return additional.getLockStatusV2();
        }

        return additional.getLockStatusV2();
    }



    @JsonIgnore
    public String getCloneChargeSheetId() {
        if (additional == null) {
            return null;
        }
        return additional.getCloneChargeSheetId();
    }

    @JsonIgnore
    public int getSelfPayStatus() {
        if (additional == null) {
            return Constants.ChargeSheetSelfPayStatus.DISABLE_SELF_PAY;
        }
        return additional.getSelfPayStatus();
    }

    @JsonIgnore
    public Instant getAutoUnlockTime() {
        if (additional == null) {
            return null;
        }
        return additional.getAutoUnlockTime();
    }


    public int getChargeVersion() {
        return additional != null ? additional.getChargeVersion() : ChargeVersionConstants.V0;
    }

    public String getDiagnosis() {
        return additional != null ? additional.getDiagnosis() : null;
    }

    @JsonIgnore
    public List<DiagnosisInfo> getDiagnosisInfos() {
        return additional != null ? additional.getDiagnosisInfos() : null;
    }

    @JsonIgnore
    public List<ExtendDiagnosisInfo> getExtendDiagnosisInfos() {
        return additional != null ? additional.getExtendDiagnosisInfos() : null;
    }

    public String getChiefComplaint() {
        return additional != null ? additional.getChiefComplaint() : null;
    }

    @JsonIgnore
    public String getChargeSheetDoctorId() {
        return StringUtils.isNotEmpty(doctorId) ? doctorId : Optional.ofNullable(additional).map(a -> a.getTranscribeDoctorId()).orElse(null);
    }

    @JsonIgnore
    public int getUseMemberFlag() {
        return Optional.ofNullable(additional).map(ChargeSheetAdditional::getUseMemberFlag).orElse(0);
    }

    public boolean isLocking() {
        return getLockStatus() > 0;
    }

    public boolean isLockingForPayOrRefund() {
        return isLockingForPay() || isLockingForRefund();
    }

    /**
     * 是否在支付中锁单
     * @return
     */
    public boolean isLockingForPay() {
        return Constants.ChargeSheetLockStatusV2.isLockingForPay(getLockStatus());
    }

    /**
     * 是否在退费中锁单
     * @return
     */
    public boolean isLockingForRefund() {
        return Constants.ChargeSheetLockStatusV2.isLockingForPay(getLockStatus());
    }
    @JsonIgnore
    public String getQueryGoodsDepartmentId() {
        //科室id顺序，先用医生科室，再用销售人科室，还没有就为null
        return Optional.ofNullable(getAdditional())
                .filter(additional -> org.apache.commons.lang.StringUtils.isNotEmpty(additional.getDepartmentId()))
                .map(ChargeSheetAdditional::getDepartmentId)
                .orElse(getSellerDepartmentId());
    }

    public void specialFirstChargedTime(Instant specialChargeTime) {

        if (Objects.isNull(specialChargeTime)) {
            specialChargeTime = Instant.now();
        }

        if (Objects.isNull(this.firstChargedTime)) {
            this.firstChargedTime = specialChargeTime;
        } else {

            if (specialChargeTime.isBefore(this.firstChargedTime)) {
                this.firstChargedTime = specialChargeTime;
            }
        }

    }

    public void specialChargedTime(Instant specialChargeTime) {

        if (Objects.isNull(specialChargeTime)) {
            specialChargeTime = Instant.now();
        }

        if (Objects.isNull(this.chargedTime)) {
            this.chargedTime = specialChargeTime;
        } else {
            if (specialChargeTime.isAfter(this.chargedTime)) {
                this.chargedTime = specialChargeTime;
            }
        }

    }

    public BigDecimal calculateNetIncomeFeeForShebaoPrint() {
        if (status == Constants.ChargeSheetStatus.UNCHARGED || status == Constants.ChargeSheetStatus.PART_CHARGED) {
            return MathUtils.wrapBigDecimalOrZero(receivableFee);
        }
        return MathUtils.wrapBigDecimalAdd(receivedFee, refundFee);
    }

    public BigDecimal calculateNeedPay() {
        BigDecimal needPay = BigDecimal.ZERO;
        if (status == Constants.ChargeSheetStatus.UNCHARGED) {
            needPay = receivableFee;
        } else if (status == Constants.ChargeSheetStatus.PART_CHARGED) {
            needPay = MathUtils.wrapBigDecimalSubtract(receivableFee, receivedFee);
        }
        return MathUtils.max(needPay, BigDecimal.ZERO);
    }

    /**
     * 实收金额 = 已收 + 已退(负数)
     *
     * @return
     */
    @JsonIgnore
    public BigDecimal getRealReceivedFee() {
        return MathUtils.wrapBigDecimalAdd(receivedFee, refundFee);
    }

    public void deleteModel(String operatorId) {
        isDeleted = 1;
        FillUtils.fillLastModifiedBy(this, operatorId);

        ListUtils.alwaysList(chargeForms).forEach(m -> m.deleteModel(operatorId));
        ListUtils.alwaysList(additionalFees).forEach(m -> m.deleteModel(operatorId));
        ListUtils.alwaysList(attachments).forEach(m -> m.deleteModel(operatorId));
        ListUtils.alwaysList(couponPromotionInfos).forEach(m -> m.deleteModel(operatorId));
        ListUtils.alwaysList(giftRulePromotionInfos).forEach(m -> m.deleteModel(operatorId));
        ListUtils.alwaysList(patientCardPromotionInfos).forEach(m -> m.deleteModel(operatorId));
        ListUtils.alwaysList(chargeVerifyInfos).forEach(m -> m.deleteModel(operatorId));

        Optional.ofNullable(additional).ifPresent(a -> a.deleteModel(operatorId));
        Optional.ofNullable(patientPointsPromotionInfo).ifPresent(m -> m.deleteModel(operatorId));
        Optional.ofNullable(patientPointsDeductProductPromotionInfo).ifPresent(m -> m.deleteModel(operatorId));
        Optional.ofNullable(deliveryInfo).ifPresent(m -> m.deleteModel(operatorId));
        Optional.ofNullable(dbDeliveryInfo).ifPresent(m -> m.deleteModel(operatorId));
    }

    /**
     * 是否为药店批量提单产生的零售收费单
     */
    @JsonIgnore
    public boolean isBatchExtractDirectChargeSheet() {
        return BitFlagUtils.checkFlagOn(Optional.ofNullable(getAdditional()).map(ChargeSheetAdditional::getRetailType).orElse(0), ChargeSheetAdditional.RetailType.RETAIL_BATCH_EXTRACT);
    }

    public static class OutpatientStatus {
        public static final int UNKNOWN = 0;
        public static final int WAITING = 1;
        public static final int DIAGNOSED = 2;
        public static final int DRAFT = 3;
    }

    public static class ExecuteStatus {
        public static final int NONE = 0;
        public static final int WAITING = 1;
        public static final int FINISHED = 2;
        public static final int REFUNDED = 3;
    }

    /**
     * 注意：增加类型时，确认ES同步以及DSL定义是否需要同步增加
     */
    public static class Type {
        public static final int UNKNOWN = 0;
        /**
         * 挂号收费单
         */
        public static final int REGISTRATION = 1;
        /**
         * 门诊收费单
         */
        public static final int OUTPATIENT = 2;
        /**
         * 零售开单
         */
        public static final int DIRECT_SALE = 3;
        /**
         * 会员卡充值收费单
         */
        public static final int MEMBER_RECHARGE = 5;
        /**
         * 执行站开单
         */
        public static final int THERAPY = 6;
        /**
         * 在线咨询收费单
         */
        public static final int ONLINE_CONSULTATION = 7;

        /**
         * 自助续方收费单
         */
        public static final int CLONE_PRESCRIPTION = 8;

        /**
         * 家庭医生签约收费单
         */
        public static final int FAMILY_DOCTOR_SIGN = 9;

        /**
         * 营销卡项开卡收费单
         */
        public static final int PROMOTION_CARD_OPEN = 10;

        /**
         * 营销卡项充值收费单
         */
        public static final int PROMOTION_CARD_RECHARGE = 11;

        /**
         * 检查站开单
         */
        public static final int EXAMINATION_INSPECTION = 12;

        /**
         * 收费处直接开门诊收费单
         */
        public static final int DIRECT_OUTPATIENT_ADDITIONAL = 13;

        /**
         * 外购处方
         */
        public static final int OUTSOURCE_PRESCRIPTION = 14;

        /**
         * 咨询计划收费单
         */
        public static final int MEDICAL_PLAN = 15;

        /**
         * 患者档案处直接开单
         */
        public static final int PATIENT_ARCHIVE_DIRECT = 16;

        /**
         * 合作诊所处方订单
         */
        public static final int COOPERATION_ORDER = 17;

        // !!!注意：增加类型时，确认ES同步以及DSL定义是否需要同步增加


        /**
         * 收费处可以直接删除收费项的收费单类型
         *
         * @return
         */
        public static List<Integer> deleteUnselectedChargeFormItemTypes() {
            return Arrays.asList(DIRECT_SALE, THERAPY, CLONE_PRESCRIPTION, MEDICAL_PLAN, PATIENT_ARCHIVE_DIRECT, COOPERATION_ORDER);
        }

        public static List<Integer> needInformBusinessType() {
            return Arrays.asList(MEMBER_RECHARGE, PROMOTION_CARD_OPEN, PROMOTION_CARD_RECHARGE);
        }

        public static List<Integer> notNeedCalculateSystemAdjustmentFee() {
            return Arrays.asList(REGISTRATION, ONLINE_CONSULTATION, MEMBER_RECHARGE, FAMILY_DOCTOR_SIGN, PROMOTION_CARD_OPEN, PROMOTION_CARD_RECHARGE);
        }

        /**
         * 允许欠费支付的收费单类型
         *
         * @return
         */
        public static List<Integer> supportOwePayTypes() {
            return Arrays.asList(OUTPATIENT, DIRECT_SALE, THERAPY, CLONE_PRESCRIPTION, FAMILY_DOCTOR_SIGN, EXAMINATION_INSPECTION, DIRECT_OUTPATIENT_ADDITIONAL, MEDICAL_PLAN, PATIENT_ARCHIVE_DIRECT, COOPERATION_ORDER);
        }

        /**
         * 已收费后微诊所常驻收费单类型
         *
         * @return
         */
        public static List<Integer> paidSheetWeClinicAlwaysShowTypes() {
            return Arrays.asList(OUTPATIENT, DIRECT_SALE, THERAPY, EXAMINATION_INSPECTION, DIRECT_OUTPATIENT_ADDITIONAL, MEDICAL_PLAN, PATIENT_ARCHIVE_DIRECT, COOPERATION_ORDER);
        }

        /**
         * 需要再todo上展示的收费单类型
         *
         * @return
         */
        public static List<Integer> chargeTodoTypes() {
            return Arrays.asList(REGISTRATION, OUTPATIENT, DIRECT_SALE, THERAPY, CLONE_PRESCRIPTION, FAMILY_DOCTOR_SIGN, EXAMINATION_INSPECTION, DIRECT_OUTPATIENT_ADDITIONAL, MEDICAL_PLAN, PATIENT_ARCHIVE_DIRECT, COOPERATION_ORDER);
        }

        public static List<Integer> canUpdatePatientOrderTypes() {
            return Arrays.asList(DIRECT_SALE, COOPERATION_ORDER);
        }

        /**
         * 需要再todo上展示的收费单类型
         *
         * @return
         */
        public static List<Integer> enableInvoiceTypes() {
            return Arrays.asList(REGISTRATION, OUTPATIENT, DIRECT_SALE, THERAPY, ONLINE_CONSULTATION, CLONE_PRESCRIPTION, FAMILY_DOCTOR_SIGN, EXAMINATION_INSPECTION, DIRECT_OUTPATIENT_ADDITIONAL, PATIENT_ARCHIVE_DIRECT);
        }

        public static List<Integer> notNeedLockGoodsTypes() {
            return Arrays.asList(REGISTRATION, MEMBER_RECHARGE, ONLINE_CONSULTATION, FAMILY_DOCTOR_SIGN, PROMOTION_CARD_OPEN, PROMOTION_CARD_RECHARGE);
        }

        /**
         * 允许社保支付更新诊断信息的收费单类型list
         *
         * @return
         */
        public static List<Integer> supportUpdateDiagnosisForShebaoPayTypes() {
            return Arrays.asList(DIRECT_SALE, THERAPY, CLONE_PRESCRIPTION, EXAMINATION_INSPECTION, PATIENT_ARCHIVE_DIRECT);
        }


        public static List<Integer> directChargeSheetTypes() {
            return Arrays.asList(DIRECT_SALE, THERAPY, CLONE_PRESCRIPTION, EXAMINATION_INSPECTION, DIRECT_OUTPATIENT_ADDITIONAL, PATIENT_ARCHIVE_DIRECT);
        }

        public static List<Integer> salesOrderTypes() {
            return Arrays.asList(DIRECT_SALE, OUTSOURCE_PRESCRIPTION, COOPERATION_ORDER);
        }

        public final static List<Integer> SUPPORT_ORIGINAL_REFUND_PRODUCT_TYPES = Arrays.asList(MEMBER_RECHARGE, PROMOTION_CARD_OPEN, PROMOTION_CARD_RECHARGE);
    }

    public static class SourceType {
        public static final int UNKNOWN = 0;
        /**
         * 门诊
         */
        public static final int OUTPATIENT = 1;

        /**
         * 零售
         */
        public static final int RETAIL = 2;

        /**
         * 续方
         */
        public static final int RENEWAL = 3;

        /**
         * 咨询
         */
        public static final int CONSULTATION = 4;

        /**
         * 其他
         */
        public static final int OTHER = 5;

        private final static Map<Integer, List<Integer>> SOURCE_TYPES_MAP = new HashMap<Integer, List<Integer>>() {{
            put(OUTPATIENT, Arrays.asList(Type.OUTPATIENT));
            put(RETAIL, Arrays.asList(Type.DIRECT_SALE, Type.THERAPY, Type.EXAMINATION_INSPECTION, Type.DIRECT_OUTPATIENT_ADDITIONAL, Type.PATIENT_ARCHIVE_DIRECT));
            put(RENEWAL, Arrays.asList(Type.CLONE_PRESCRIPTION));
            put(CONSULTATION, Arrays.asList(Type.ONLINE_CONSULTATION, Type.MEDICAL_PLAN));
            put(OTHER, Arrays.asList(Type.FAMILY_DOCTOR_SIGN, Type.OUTSOURCE_PRESCRIPTION, Type.COOPERATION_ORDER));
        }};

        public static final List<Integer> ALL_SOURCE_TYPS = Arrays.asList(OUTPATIENT, RETAIL, RENEWAL, CONSULTATION, OTHER);

        public static List<Integer> getTypesMap(List<Integer> sourceTypeList) {
            if (CollectionUtils.isEmpty(sourceTypeList)) {
                return null;
            }
            Set<Integer> types = new HashSet<>();
            for (Integer sourceType : sourceTypeList) {
                if (!SOURCE_TYPES_MAP.containsKey(sourceType)) {
                    continue;
                }
                types.addAll(SOURCE_TYPES_MAP.get(sourceType));
            }
            return new ArrayList<>(types);
        }

        public static Integer getSourceType(int type) {
            for (Map.Entry<Integer, List<Integer>> entry : SOURCE_TYPES_MAP.entrySet()) {
                if (entry.getValue().contains(type)) {
                    return entry.getKey();
                }
            }
            return OTHER;
        }
    }

    public static class CheckStatus {
        public static final int NOT_NEED_CHECK = 0;
        public static final int NEED_PATIENT_CHECK = 1;//有两个含义：需要病人确认&不能展示在收费台
        public static final int NEED_CLINIC_CHECK = 2;
    }

    public static class DeliveryType {
        public static final int NONE = 0;
        public static final int DELIVERY_TO_HOME = 1;
    }

    public static class DeliveryDetailType {
        /**
         * 未选择
         */
        public static final int NONE = 0;

        /**
         * 快递
         */
        public static final int EXPRESS = 1;

        /**
         * 自取
         */
        public static final int SELF_PICKUP = 2;
    }

    public static class OwedStatus {

        /**
         * 无欠费
         */
        public static final int NO_OWED = 0;

        /**
         * 欠费中
         */
        public static final int OWING = 10;

    }

    public static class ImportFlag {
        /**
         * 老的导入数据
         */
        public static final int OLD_IMPORT = 1;

        /**
         * 导入欠费单
         */
        public static final int IMPORT_OWE_SHEET = 2;

        /**
         * 导入收费单在QL上展示，但是不可修改
         */
        public static final int IMPORT_SHEET_SHOW_IN_QL = 3;
    }

    /**
     * 关联数据的bit位，最多63个，int的最大值为2^31 - 1
     */
    public static class DataFlag {
        public static final int HAS_ITEM_BATCH_INFO = 0x0001;
        public static final int HAS_PROCESS_INFO = 0x0002;
        public static final int HAS_ADDITIONAL_FEE = 0x0004;
        public static final int HAS_ATTACHMENT = 0x0008;
        public static final int HAS_COUPON_PROMOTION_INFO = 0x0010;
        public static final int HAS_GIFT_RULE_PROMOTION_INFO = 0x0020;
        public static final int HAS_PATIENT_CARD_PROMOTION_INFO = 0x0040;
        public static final int HAS_CHARGE_VERIFY_INFO = 0x0080;
        public static final int HAS_PATIENT_POINTS_PROMOTION_INFO = 0x0100;
        public static final int HAS_PATIENT_POINTS_DEDUCT_PRODUCT_PROMOTION_INFO = 0x0200;

    }
}
