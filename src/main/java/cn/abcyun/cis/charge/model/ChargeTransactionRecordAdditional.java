package cn.abcyun.cis.charge.model;

import cn.abcyun.cis.charge.service.dto.ChargeDiscountInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.vladmihalcea.hibernate.type.json.JsonStringType;
import lombok.Data;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;


@Entity
@Table(name = "v2_charge_transaction_record_additional")
@Data
@TypeDef(name = "json", typeClass = JsonStringType.class)
public class ChargeTransactionRecordAdditional {
    @Id
    private String id;
    private String patientOrderId;
    private String chainId;
    private String clinicId;
    private String chargeSheetId;

    private String chargeFormId;

    /**
     * 空中药房订单id
     */
    private String bisOrderId;

    /**
     * 折扣明细
     */
    @Type(type = "json")
    @Column(columnDefinition = "json")
    private ChargeDiscountInfo discountInfo;

    @Type(type = "json")
    @Column(columnDefinition = "json")
    private List<RecordDeductInfo> deductInfo;

    /**
     * 抵扣总金额  负值
     */
    private BigDecimal deductTotalPrice;

    /**
     * 抵扣数量   正值
     */
    private BigDecimal deductCount;

    /**
     * 抵扣成本价  正值
     */
    private BigDecimal deductTotalCostPrice;

    private BigDecimal presentAmount;   //赠金支付金额

    private BigDecimal principalAmount; //本金支付金额

    private int pharmacyType;

    private int isDeleted;

    private String createdBy;

    private Instant created;

    private String lastModifiedBy;

    private Instant lastModified;

    @Transient
    @JsonIgnore
    private boolean cold;

    public static class ChargeType {
        public static final int CHARGED = 1;
        public static final int PART_CHARGED = 0;
        public static final int REFUND = -1;
    }

    public static class RecordType {
        public static final int PRODUCT = 0;
        public static final int ADJUSTMENT_FEE = 1;
        public static final int MEMBER_CARD_RECHARGE= 2;
        public static final int ROUNDING_FEE= 3;
    }

    public static class DeductType {
        public static final int UN_KNOWN = 0;
        /**
         * 卡项
         */
        public static final int PATIENT_CARD = 1;

        /**
         * 核销
         */
        public static final int VERIFY = 2;

    }

    @Data
    public static class RecordDeductInfo {

        private BigDecimal deductTotalPrice;

        private BigDecimal deductCount;

        private BigDecimal deductTotalCostPrice;

        public int type;
    }
}
