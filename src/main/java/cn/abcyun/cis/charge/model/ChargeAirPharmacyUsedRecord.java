package cn.abcyun.cis.charge.model;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.Instant;

@Data
@Entity
@Table(name = "v2_charge_air_pharmacy_used_record")
public class ChargeAirPharmacyUsedRecord {

    @Id
    private String id;

    private String chainId;

    private String clinicId;

    private String vendorId;

    private String usageScopeId;

    private int usedCount;

    private Instant created;

    private String createdBy;

    private String lastModifiedBy;

    private Instant lastModified;

}
