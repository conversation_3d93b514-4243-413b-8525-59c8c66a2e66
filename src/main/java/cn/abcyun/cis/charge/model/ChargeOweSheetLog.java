package cn.abcyun.cis.charge.model;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;

@Data
@Entity
@Table(name = "v2_charge_owe_sheet_log")
@Accessors(chain = true)
public class ChargeOweSheetLog implements Serializable {


	/**
	 * 主键id
	 */
	@Id
	private Long id;

	/**
	 * 连锁id
	 */
	private String chainId;

	/**
	 * 诊所id
	 */
	private String clinicId;

	/**
	 * 欠费单id
	 */
	private Long oweSheetId;

	/**
	 * 收费流水id
	 */
	private String chargeTransactionId;

	/**
	 * 收费流水id
	 */
	private String chargeSheetId;

	/**
	 * 金额
	 */
	private BigDecimal amount;

	/**
	 * 创建时间
	 */
	private Instant created;
	private String createdBy;
	private Instant lastModified;
	private String lastModifiedBy;
}
