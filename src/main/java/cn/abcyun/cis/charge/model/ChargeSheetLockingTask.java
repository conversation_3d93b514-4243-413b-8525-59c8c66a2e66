package cn.abcyun.cis.charge.model;

import cn.abcyun.bis.rpc.sdk.cis.model.goodslocking.GoodsLockingSheet;
import cn.abcyun.bis.rpc.sdk.cis.model.goodslocking.LockingServerGoodsLockingView;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.Instant;

@Entity
@Table(name = "v2_charge_sheet_locking_task")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChargeSheetLockingTask {

    @Id
    private String id;

    private String clinicId;

    private String chainId;

    private String chargeSheetId;

    private int status;

    @Type(type = "json")
    @Column(columnDefinition = "json")
    private GoodsLockingSheet batchInfo;

    private int isDeleted;

    private String createdBy;

    private Instant created;

    private String lastModifiedBy;

    private Instant lastModified;


    public static class status {
        public static final int LOCKED = 0;  //已加锁
        public static final int UNLOCKED = 1;  //已解锁
    }
}
