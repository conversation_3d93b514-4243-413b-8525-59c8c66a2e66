package cn.abcyun.cis.charge.model;

import cn.abcyun.cis.charge.base.Constants;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.Date;

/**
 * v2_charge_medicare_limit_price_product
 * <AUTHOR>
@Entity
@Table(name = "v2_charge_medicare_limit_price_product")
@Data
@Accessors(chain = true)
public class ChargeMedicareLimitPriceProduct {

    @Id
    private String id;

    private String clinicId;

    private String chainId;

    /**
     * 商品id
     */
    private String productId;

    /**
     * 1：药品/材料，2：检验/治疗/理疗项目
     */
    private Integer type;

    /**
     * 整售限价
     */
    private BigDecimal limitPackageUnitPrice;

    /**
     * 整售单位
     */
    private String packageUnit;

    /**
     * 拆零限价
     */
    private BigDecimal limitPieceUnitPrice;

    /**
     * 拆零单位
     */
    private String pieceUnit;

    /**
     * 是否删除状态（0：正常；1：被删除）
     */
    private int isDeleted;

    /**
     * 排序
     */
    private Integer sort;

    private Instant gmtCreate;

    private Instant gmtModified;

    private String lastModifiedBy;

    private String createdBy;

    /**
     * 定价超限价收费规则：0超过部分不收，1超过部分患者自费
     * {@link Constants.ExceedLimitPriceRule}
     */
    private int exceedLimitPriceRule;

}