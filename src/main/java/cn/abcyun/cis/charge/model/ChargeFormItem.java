package cn.abcyun.cis.charge.model;

import cn.abcyun.bis.rpc.sdk.cis.model.charge.GoodsFeeType;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsItem;
import cn.abcyun.cis.charge.base.ChargeVersionConstants;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.service.dto.ChargeDiscountInfo;
import cn.abcyun.cis.charge.service.dto.SinglePromotionView;
import cn.abcyun.cis.charge.util.*;
import cn.abcyun.cis.commons.model.ComposeType;
import cn.abcyun.cis.commons.util.TextUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.JsonNode;
import com.vladmihalcea.hibernate.type.json.JsonStringType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Entity
@Table(name = "v2_charge_form_item")
@Data
@TypeDef(name = "json", typeClass = JsonStringType.class)
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class ChargeFormItem {

    public ChargeFormItem(String id, int composeType, String composeParentFormItemId, BigDecimal receivedPrice, BigDecimal unitCount, BigDecimal doseCount) {
        this.id = id;
        this.composeType = composeType;
        this.composeParentFormItemId = composeParentFormItemId;
        this.receivedPrice = receivedPrice;
        this.unitCount = unitCount;
        this.doseCount = doseCount;
    }

    @Id
    private String id;

    private String clinicId;

    private String chainId;

    private String patientOrderId;

    private String chargeSheetId;

    private String chargeFormId;

    private String sourceFormItemId;

    private int status;

    private int payStatus;

    private int paySource;

    private String unit;

    private String name;

    private BigDecimal unitCostPrice;

    /**
     * 成本总价
     */
    private BigDecimal totalCostPrice;


    private BigDecimal unitCount;

    private BigDecimal doseCount;

    /**
     * 修改的剂量值，在中药form上有效
     */
    @Transient
    private BigDecimal expectedDoseCount;

    private BigDecimal unitPrice;

    private BigDecimal discountPrice; //discountPrice = adjustmentPrice + promotionPrice

    private BigDecimal totalPrice;  //总价不算折扣 totalPrice = unitPrice * unitCount * doseCount + fractionPrice 并不是永远使用这个公式，有特殊情况会强行设置totalPrice

    private BigDecimal expectedUnitPrice;

    /**
     * 原始单价
     */
    private BigDecimal sourceUnitPrice;

    /**
     * 原始金额
     */
    private BigDecimal sourceTotalPrice;

    /**
     * 单项应收
     */
    private BigDecimal receivablePrice;

    private BigDecimal fractionPrice;//TODO robinsli 这个是？？

    private BigDecimal expectedTotalPrice;

    @Transient
    private BigDecimal frontEndUnitPrice;

    @Transient
    private BigDecimal frontEndTotalPrice;

    /**
     * 抵扣总次数
     */
    private BigDecimal deductTotalCount = BigDecimal.ZERO;

    /**
     * 核销次数
     */
    private BigDecimal verifyTotalCount = BigDecimal.ZERO;

    private BigDecimal adjustmentPrice = BigDecimal.ZERO; //议价

    /**
     * 金额比例(单价比例和金额比例都是这个字段)
     */
    private BigDecimal totalPriceRatio;

    /**
     * 期望的金额比例（单价比例和金额比例都是这个字段）
     */
    private BigDecimal expectedTotalPriceRatio;

    /**
     * 单项金额议价值 = 折扣前金额 - sourceTotalPrice
     * 折扣前金额 = MathUtils.wrapBigDecimalAdd(MathUtils.calculateTotalPrice(unitPrice, unitCount, doseCount, 2), fractionPrice)
     * sourceTotalPrice = sourceUnitPrice * unitCount * doseCount
     */
    private BigDecimal unitAdjustmentFee;

    private BigDecimal promotionPrice = BigDecimal.ZERO; //优惠金额

    /**
     * 已收金额
     */
    private BigDecimal receivedPrice = BigDecimal.ZERO;

    /**
     * 门诊原单价
     */
    private BigDecimal doctorSourceUnitPrice;
    /**
     * 门诊原总价
     */
    private BigDecimal doctorSourceTotalPrice;

    /**
     * 本次应收金额
     */
    @JsonIgnore
    @Transient
    private BigDecimal thisTimeReceivableFee = BigDecimal.ZERO;

    @Column(name = "v2_status")
    private int v2Status = Constants.ChargeFormItemStatus.NONE;

    private BigDecimal refundUnitCount = BigDecimal.ZERO;

    private BigDecimal refundDoseCount;

    private BigDecimal refundDiscountPrice = BigDecimal.ZERO;

    private BigDecimal refundTotalPrice = BigDecimal.ZERO;

    @Column(name = "isDismounting")
    private int useDismounting;

    private int productType;

    private int productSubType;

    private int isAirPharmacy;

    private String associateFormItemId; //退款项关联的chargeFormItemId

    private String productId;

    private Integer groupId;

    private int sort;

    private int composeType = 0;

    private String composeParentFormItemId;

    /**
     * 是否触发限价
     */
    private int isUseLimitPrice;

    /**
     * item的类型
     * {@link Constants.SourceItemType}
     */
    private int sourceItemType;

    @Transient
    private String keyId;

    /**
     * 为赠品时，需要这个字段来找到是由哪个item赠送的，因为前段新增item时不会传id，只能通过keyId来建立关系，所以用这个字段来找
     */
    @Transient
    private String sourceFormItemKeyId;

    @Transient
    @JsonIgnore
    private String sourceComposeParentFormItemId;

    @JsonIgnore
    @Column(name = "usageInfo")
    private String usageInfoJson;

    @Transient
    private JsonNode usageInfo;

    @JsonIgnore
    @Column(name = "promotionInfo")
    private String promotionInfoJson;

    @Transient
    private ChargeDiscountInfo promotionInfo;

    @Deprecated
    @Column(name = "couponPromotionInfo")
    private String couponPromotionInfoJson;

    @Deprecated
    @Transient
    private ItemCouponPromotionInfo couponPromotionInfo;

    @Deprecated
    @Column(name = "giftRulePromotionInfo")
    private String giftRulePromotionInfoJson;

    @Deprecated
    @Transient
    private ItemGiftRulePromotionInfo giftRulePromotionInfo;


    @JsonIgnore
    private String productSnapshot;

    //是否为赠品
    /**
     * {@link Constants.ChargeFormItemGiftType}
     */
    private int isGift;

    /**
     * 药房类型
     */
    private int pharmacyType;

    /**
     * 套餐母项的unitCount
     */
    @Transient
    private BigDecimal composeParentUnitCount;

    /**
     * 药房号：默认为0
     */
    private int pharmacyNo;

    /**
     * 费用类型
     */
    private int feeComposeType;

    /**
     * 费用类型id
     */
    private Long feeTypeId;

    /**
     * {@link cn.abcyun.bis.rpc.sdk.cis.model.charge.GoodsFeeType}
     */
    private int goodsFeeType;

    @Type(type = "json")
    @Column(columnDefinition = "json")
    private ChargeFormItemAdditional additional;

    private String goodsTypeId;

    /**
     * 是否期望指定批次
     */
    private int isExpectedBatch;

    /**
     * 是否固定数据，算费临时使用，是一个内存属性，不落地
     */
    @Transient
    private int isFixedData;

    //    @OneToMany(cascade = CascadeType.ALL)
//    @JoinColumn(name = "chargeFormItemId")
//    @Where(clause = "is_deleted=0")
//    @SQLDelete(sql = "UPDATE v2_charge_form_item_batch_info SET is_deleted=1 WHERE charge_form_item_id=? AND id=?")
//    @SQLDeleteAll(sql = "UPDATE v2_charge_form_item_batch_info SET is_deleted=1 WHERE charge_form_item_id=?")
//    @SQLInsert(sql = "UPDATE v2_charge_form_item_batch_info SET charge_form_item_id=?, is_deleted=0 WHERE id=?")
    @Transient
    private List<ChargeFormItemBatchInfo> chargeFormItemBatchInfos;

    /**
     * 批次锁库id
     */
    private String lockId;

    @JsonIgnore
    private int isDeleted;
    @JsonIgnore
    private String createdBy;
    //    @JsonIgnore
    private Instant created;
    @JsonIgnore
    private String lastModifiedBy;
    @JsonIgnore
    private Instant lastModified;

    // 业务附加字段，不存入数据库
    @Transient
    private JsonNode productInfo;

    @Transient
    private BigDecimal executedUnitCount;

    @Transient
    private int needExecutive;

    @Transient
    @JsonIgnore
    private int isRefundByDose;

    @Transient
    private BigDecimal containAdjustmentUnitPrice;

    @Transient
    private BigDecimal containAdjustmentTotalPrice;

    @Transient
    private BigDecimal containAdjustmentFractionPrice;

    @Transient
    private List<ChargeFormItem> composeChildren;

    @Version
    private int version;

    /**
     * 批量提单时的原始chargeFormItemId列表
     */
    @Transient
    private List<String> originalChargeFormItemIds;

    public ChargeDiscountInfo getPromotionInfo() {
        if (promotionInfo != null) {
            return promotionInfo;
        }

        if (StringUtils.isEmpty(promotionInfoJson)) {
            return null;
        }
        return JsonUtils.readValue(promotionInfoJson, ChargeDiscountInfo.class);
    }

    public boolean isExpectedPriceItem() {
        return (expectedUnitPrice != null && MathUtils.wrapBigDecimalCompare(expectedUnitPrice, sourceUnitPrice) != 0)
                || (expectedTotalPrice != null && MathUtils.wrapBigDecimalCompare(expectedTotalPrice, getSourceTotalPrice()) != 0)
                || (expectedTotalPriceRatio != null && MathUtils.wrapBigDecimalCompare(expectedTotalPriceRatio, BigDecimal.ONE) != 0);
    }

    public void clearExpectedPrice() {
        expectedUnitPrice = null;
        expectedTotalPrice = null;
        expectedTotalPriceRatio = null;
    }

    public BigDecimal getUnitCount() {
        return MathUtils.wrapBigDecimal(unitCount, BigDecimal.ZERO);
    }

    public BigDecimal getDoseCount() {
        return MathUtils.wrapBigDecimal(doseCount, BigDecimal.ONE);
    }

    public BigDecimal getUnitPrice() {
        return MathUtils.wrapBigDecimal(unitPrice, BigDecimal.ZERO);
    }

    public BigDecimal getDiscountPrice() {
        return MathUtils.wrapBigDecimal(discountPrice, BigDecimal.ZERO);
    }

    public BigDecimal getTotalPrice() {
        return MathUtils.wrapBigDecimal(totalPrice, BigDecimal.ZERO);
    }

    public BigDecimal getUnitCostPrice() {
        return MathUtils.wrapBigDecimal(unitCostPrice, BigDecimal.ZERO);
    }

    public BigDecimal getTotalCostPrice() {

        if (totalCostPrice != null) {
            return totalCostPrice;
        }

        return MathUtils.calculateTotalPrice(unitCostPrice, unitCount, doseCount, 4);
    }

    public BigDecimal getSourceTotalPrice() {

        if (sourceTotalPrice == null) {
            return MathUtils.calculateTotalPrice(sourceUnitPrice, unitCount, doseCount, 2);
        }

        return sourceTotalPrice;
    }

    // 议价加价算在总价上
    public BigDecimal calculateTotalPrice() {
        totalPrice = MathUtils.wrapBigDecimalAdd(MathUtils.calculateTotalPrice(unitPrice, unitCount, doseCount, 2), fractionPrice);
        if (MathUtils.wrapBigDecimalCompare(adjustmentPrice, BigDecimal.ZERO) > 0) {
            totalPrice = MathUtils.wrapBigDecimalAdd(totalPrice, adjustmentPrice);
        }
        return totalPrice;
    }

    public BigDecimal calculateTotalPriceV2() {
        totalPrice = MathUtils.wrapBigDecimalAdd(MathUtils.calculateTotalPrice(unitPrice, unitCount, doseCount, 2), fractionPrice);
        return totalPrice;
    }

    // 议价减价算在折扣上
    public BigDecimal calculateDiscountPrice() {
        discountPrice = MathUtils.wrapBigDecimalAdd(promotionPrice, MathUtils.wrapBigDecimalCompare(adjustmentPrice, BigDecimal.ZERO) < 0 ? adjustmentPrice : BigDecimal.ZERO);
        return discountPrice;
    }

    public void calculateContainAdjustmentPrice() {
        containAdjustmentTotalPrice = MathUtils.wrapBigDecimalAdd(MathUtils.calculateTotalPrice(unitPrice, unitCount, doseCount, 2), fractionPrice).add(MathUtils.wrapBigDecimalOrZero(adjustmentPrice));
        containAdjustmentUnitPrice = containAdjustmentTotalPrice.divide(MathUtils.calculateTotalCount(unitCount, doseCount), 2, RoundingMode.FLOOR);
        BigDecimal fractionPrice = containAdjustmentTotalPrice.subtract(containAdjustmentUnitPrice.multiply(MathUtils.calculateTotalCount(unitCount, doseCount)).setScale(2, RoundingMode.FLOOR));
        containAdjustmentFractionPrice = MathUtils.max(fractionPrice, BigDecimal.ZERO);
    }

    public BigDecimal calculateDiscountedPrice() {
        return calculateTotalPrice().add(calculateDiscountPrice());
    }

    public BigDecimal calculateDiscountedPrice(int chargeVersion) {
        if (chargeVersion == ChargeVersionConstants.V1) {
            return calculateDiscountedPriceV2();
        }
        return calculateDiscountedPrice();
    }

    public BigDecimal calculateDiscountedPriceV2() {
        //这里真实的步骤是：原价 + 单项优惠 + 单项议价 + 整单优惠 + 整单议价
        //实际存储: promotionPrice = 单项优惠 + 整单优惠
        //所以实际算法可以改为： 原价 + promotionPrice + 单项议价 + 整单议价
        return getSourceTotalPrice()
                .add(promotionPrice)
                .add(MathUtils.wrapBigDecimalOrZero(getUnitAdjustmentFee()))
                .add(MathUtils.wrapBigDecimalOrZero(adjustmentPrice));
    }

    /**
     * 计算整单优惠的值
     *
     * @return
     */
    public BigDecimal calculatePackagePromotionPrice() {
        ChargeDiscountInfo promotionInfo = getPromotionInfo();
        if (promotionInfo == null) {
            return BigDecimal.ZERO;
        }

        return promotionInfo.calculatePackagePromotionPrice();
    }

    //计算单项优惠的值
    public BigDecimal calculateSinglePromotionPrice() {
        ChargeDiscountInfo promotionInfo = getPromotionInfo();
        if (promotionInfo == null) {
            return BigDecimal.ZERO;
        }

        return promotionInfo.calculateSinglePromotionPrice();
    }


    public BigDecimal calculateDisplayDiscountedUnitPrice() {
        BigDecimal discountedTotalPrice = calculateDiscountedPrice();

        if (discountedTotalPrice.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal totalCount = MathUtils.calculateTotalCount(unitCount, doseCount);

        if (totalCount.compareTo(BigDecimal.ZERO) <= 0) {
            return discountedTotalPrice;
        }

        return discountedTotalPrice.divide(totalCount, 2, RoundingMode.DOWN);
    }

    public BigDecimal calculateDisplayDiscountedFractionPrice() {
        BigDecimal discountedUnitPrice = calculateDisplayDiscountedUnitPrice();
        BigDecimal totalCount = MathUtils.calculateTotalCount(unitCount, doseCount);
        return MathUtils.wrapBigDecimalSubtract(calculateDiscountedPrice(), MathUtils.wrapBigDecimalMultiply(discountedUnitPrice, totalCount));
    }

    public BigDecimal calculateRefundDiscountedPrice(BigDecimal deductPromotionPrice) {
        totalPrice = MathUtils.wrapBigDecimalAdd(MathUtils.calculateTotalPrice(unitPrice, MathUtils.wrapBigDecimalSubtract(unitCount, getDeductAndVerifyTotalCount()), doseCount, 2), fractionPrice);
        if (MathUtils.wrapBigDecimalCompare(adjustmentPrice, BigDecimal.ZERO) > 0) {
            totalPrice = MathUtils.wrapBigDecimalAdd(totalPrice, adjustmentPrice);
        }
        totalPrice = totalPrice.add(deductPromotionPrice.negate());
        return totalPrice.add(calculateDiscountPrice());
    }

    public boolean isCanDispensing() {
//        //费用项不需要扣库存，不需要发药
//        if (goodsFeeType == GoodsFeeType.FEE_CHILD) {
//            return false;
//        }
//
//        //处理系统药品的逻辑，无库存的药品现在也可以收费了，如果是系统药品，就不判断有没有发药的逻辑，系统药品的判断就是 productId为空的药品
//        return (productType == Constants.ProductType.MEDICINE || productType == Constants.ProductType.MATERIAL || productType == Constants.ProductType.SALE_PRODUCT || productType == Constants.ProductType.EYE) && !StringUtils.isEmpty(productId);
        return ChargeFormItemUtils.isCanDispensing(goodsFeeType, productType, productId);
    }

    @PrePersist
    @PreUpdate
    public void defaultFieldValues() {
        if (sourceFormItemId == null) {
            sourceFormItemId = "";
        }

        if (name == null) {
            name = "";
        }

        if (unitCostPrice == null) {
            unitCostPrice = BigDecimal.ZERO;
        }

        if (totalCostPrice == null) {
            calculateTotalCostPrice();
        }

        if (unitPrice == null) {
            unitPrice = BigDecimal.ZERO;
        }

        if (unitCount == null) {
            unitCount = BigDecimal.ZERO;
        }
        if (doseCount == null) {
            doseCount = BigDecimal.ONE;
        }

        if (discountPrice == null) {
            discountPrice = BigDecimal.ZERO;
        }

        if (totalPrice == null) {
            totalPrice = BigDecimal.ZERO;
        }

        if (createdBy == null || created == null) {
            createdBy = lastModifiedBy;
            created = lastModified;
        }

        usageInfoJson = UsageInfoUtil.simplifyUsageInfo(usageInfoJson);

        if (unit == null) {
            unit = "";
        }

        if (TextUtils.isEmpty(promotionInfoJson)) {
            promotionInfoJson = null;
        }

        if (TextUtils.isEmpty(couponPromotionInfoJson)) {
            couponPromotionInfoJson = null;
        }

        if (TextUtils.isEmpty(giftRulePromotionInfoJson)) {
            giftRulePromotionInfoJson = null;
        }

        if (refundDiscountPrice == null) {
            refundDiscountPrice = BigDecimal.ZERO;
        }

        if (refundTotalPrice == null) {
            refundTotalPrice = BigDecimal.ZERO;
        }

        if (refundUnitCount == null) {
            refundUnitCount = BigDecimal.ZERO;
        }

        if (adjustmentPrice == null) {
            adjustmentPrice = BigDecimal.ZERO;
        }

        if (promotionPrice == null) {
            promotionPrice = BigDecimal.ZERO;
        }

        if (fractionPrice == null) {
            fractionPrice = BigDecimal.ZERO;
        }

        if (receivedPrice == null) {
            receivedPrice = BigDecimal.ZERO;
        }

        //保障中药入库一定拆零
        if (productType == Constants.ProductType.MEDICINE && productSubType == Constants.ProductType.SubType.MEDICINE_CHINESE) {
            useDismounting = 1;
        }

        //系统费用原来没有productId，保障入库时把系统费用的productId补上
        if (StringUtils.isEmpty(productId)) {

            if (productType == Constants.ProductType.REGISTRATION) {
                productId = Constants.SystemProductId.REGISTRATION_PRODUCT_ID;
            }

            if (productType == Constants.ProductType.EXPRESS_DELIVERY) {
                productId = Constants.SystemProductId.EXPRESS_DELIVERY_PRODUCT_ID;
            }

            if (productType == Constants.ProductType.PROCESS) {
                productId = Constants.SystemProductId.PROCESS_PRODUCT_ID;
            }

            if (productType == Constants.ProductType.ONLINE_CONSULTATION) {
                productId = Constants.SystemProductId.ONLINE_CONSULTATION_PRODUCT_ID;
            }
        }

        if (productId == null) {
            productId = "";
        }

        if (needLatestProductSnapshot()) {
            productSnapshot = null;
        }
    }

    public boolean needLatestProductSnapshot() {
        return !Constants.ProductType.SYSTEM_FEE_PRODUCT_TYPE.contains(productType)
                && composeType == ComposeType.NOT_COMPOSE
                && !isGoodsCombine()
                && goodsFeeType == GoodsFeeType.FEE_OWN;
    }

    /**
     * goods是否为组套
     *
     * @return
     */
    private boolean isGoodsCombine() {
        if (getProductType() != Constants.ProductType.EXAMINATION) {
            return false;
        }

        GoodsItem goodsItem = JsonUtils.readValue(productSnapshot, GoodsItem.class);

        if (goodsItem == null) {
            return false;
        }

        if (goodsItem.getCombineType() == GoodsConst.GoodsCombine.COMBINE) {
            return true;
        }

        return false;
    }

    @JsonIgnore
    public BigDecimal getDeductPromotionPrice() {
        return Optional.ofNullable(getPromotionInfo())
                .map(ChargeDiscountInfo::getDeductPromotionPrice)
                .orElse(BigDecimal.ZERO);
    }

    @JsonIgnore
    public BigDecimal getAllDeductPromotionPrice() {
        return Optional.ofNullable(getPromotionInfo())
                .map(ChargeDiscountInfo::getAllDeductPrice)
                .orElse(BigDecimal.ZERO);
    }

    public BigDecimal calculateTotalCostPrice() {
        totalCostPrice = MathUtils.calculateTotalPrice(unitCostPrice, unitCount, doseCount).setScale(4, RoundingMode.HALF_UP);
        return totalCostPrice;
    }

    @JsonIgnore
    public boolean isAllDeducted() {
        return MathUtils.wrapBigDecimalCompare(getDeductAndVerifyTotalCount(), MathUtils.calculateTotalCount(getUnitCount(), getDoseCount())) == 0;
    }

    //修正期望批次信息
    public void amendExpectedBatch() {

        if (!Objects.equals(1, isExpectedBatch)) {
            chargeFormItemBatchInfos = new ArrayList<>();
            return;
        }

        if (org.apache.commons.collections.CollectionUtils.isEmpty(chargeFormItemBatchInfos)) {
            isExpectedBatch = 0;
            return;
        }


        chargeFormItemBatchInfos = chargeFormItemBatchInfos.stream()
                .filter(chargeFormItemBatchInfo -> chargeFormItemBatchInfo.getIsDeleted() == 0)
                .filter(batchInfoReq -> MathUtils.wrapBigDecimalCompare(batchInfoReq.getUnitCount(), BigDecimal.ZERO) > 0)
                .collect(Collectors.toList());

        if (org.apache.commons.collections.CollectionUtils.isEmpty(chargeFormItemBatchInfos)) {
            isExpectedBatch = 0;
            return;
        }

        BigDecimal batchTotalCount = chargeFormItemBatchInfos.stream()
                .map(ChargeFormItemBatchInfo::getUnitCount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (MathUtils.wrapBigDecimalCompare(MathUtils.calculateTotalCount(unitCount, doseCount)
                .add(MathUtils.wrapBigDecimalOrZero(deductTotalCount)
                        .add(MathUtils.wrapBigDecimalOrZero(verifyTotalCount))
                ), batchTotalCount) != 0) {
            isExpectedBatch = 0;
            chargeFormItemBatchInfos = new ArrayList<>();
        }

    }

    public void deleteModel(String operatorId) {
        isDeleted = 1;
        FillUtils.fillLastModifiedBy(this, operatorId);

        Optional.ofNullable(chargeFormItemBatchInfos)
                .orElse(new ArrayList<>())
                .forEach(batchInfo -> batchInfo.deleteModel(operatorId));
    }

    public BigDecimal calculateListingDiscountFee() {
        return Optional.ofNullable(getPromotionInfo())
                .map(ChargeDiscountInfo::getListingDiscountFee)
                .orElse(BigDecimal.ZERO);
    }

    /**
     * 更新promotionInfo时需要顺带更新promotionInfoJson，因为最后落地的是promotionInfoJson
     *
     * @param updateSupplier
     */
    public void updatePromotionInfo(Supplier<ChargeDiscountInfo> updateSupplier) {
        Assert.notNull(updateSupplier, "updateSupplier can not be null");
        ChargeDiscountInfo chargeDiscountInfo = updateSupplier.get();
        setPromotionInfo(chargeDiscountInfo);
        setPromotionInfoJson(JsonUtils.dump(chargeDiscountInfo));
    }

    public static class PayStatus {
        public static final int UNPAID = 0;
        public static final int PARTED_PAID = 1;
        public static final int PAID = 2;
    }

    /**
     * 复写转化Json对象函数，外围有写场景读了db字段没有初始化Json
     */
    public JsonNode getUsageInfo() {
        if (this.usageInfo == null && !StringUtils.isEmpty(this.usageInfoJson)) {
            this.usageInfo = JsonUtils.loadAsJsonNode(this.usageInfoJson);
        }
        return this.usageInfo;
    }

    public JsonNode getProductInfo() {
        if (this.productInfo == null && !StringUtils.isEmpty(this.productSnapshot)) {
            this.productInfo = JsonUtils.loadAsJsonNode(this.productSnapshot);
        }
        return this.productInfo;
    }

    /**
     * 是否为顶层的item，比如直接开一个费用项，直接开一个费用母项，直接开一个套餐
     */
    public boolean isTopItem() {
        return ChargeFormItemUtils.isTopItem(getComposeType(), getGoodsFeeType());
    }

    public boolean isParentItem() {
        return ChargeFormItemUtils.isParentItem(getComposeType(), getGoodsFeeType());
    }

    public boolean isChildItem() {
        return ChargeFormItemUtils.isChildItem(getComposeType(), getGoodsFeeType());
    }

    /**
     * 限购活动信息
     *
     * @param statusList {@link SinglePromotionView.Status}
     */
    @Transient
    public ItemSinglePromotion getSellLimitPromotion(List<Integer> statusList) {
        if (getSourceItemType() != Constants.SourceItemType.NORMAL) {
            return null;
        }
        if (getIsGift() != Constants.ChargeFormItemGiftType.PROMOTION_GIFT) {
            return null;
        }
        if (getUseDismounting() != 0) {
            return null;
        }
        if (getAdditional() == null) {
            return null;
        }
        if (CollectionUtils.isEmpty(getAdditional().getSinglePromotions())) {
            return null;
        }

        if (!isTopItem()) {
            return null;
        }

        return getAdditional().getSinglePromotions().stream()
                .filter(p -> p.getChecked() && p.getLeftSaleCount() != null && p.getParticipationDiscountCount() != null)
                .filter(p -> statusList.contains(p.getStatus()))
                .findFirst()
                .orElse(null);
    }

    public boolean needUpdateBatch() {
        return Objects.equals(1, getIsExpectedBatch()) || Objects.equals(1, getIsFixedData());
    }


    public BigDecimal getDeductAndVerifyTotalCount() {
        return MathUtils.wrapBigDecimalAdd(getDeductTotalCount(), getVerifyTotalCount());
    }

    @Transient
    @JsonIgnore
    private boolean cold;

    public boolean hasEnoughStockPharmacy(BigDecimal stockPackageCount, BigDecimal stockPieceCount, BigDecimal pieceNum) {
        BigDecimal _totalCount = MathUtils.calculateTotalCount(this.getUnitCount(), this.getDoseCount());

        if (Objects.isNull(this.getProductInfo())) {
            return false;
        }

        if (this.getUseDismounting() == 1) {
            BigDecimal _stockPackage = MathUtils.wrapBigDecimalOrZero(stockPackageCount);
            BigDecimal _stockPiece = MathUtils.wrapBigDecimalOrZero(stockPieceCount);
            BigDecimal _stockCount = _stockPackage.multiply(MathUtils.wrapBigDecimalOrZero(pieceNum)).add(_stockPiece);

            return _stockCount.compareTo(_totalCount) >= 0;
        } else {

            BigDecimal _stockPackage = MathUtils.wrapBigDecimalOrZero(stockPackageCount);

            return _stockPackage.compareTo(_totalCount) >= 0;
        }
    }

}
