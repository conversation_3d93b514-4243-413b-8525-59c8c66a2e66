package cn.abcyun.cis.charge.model;

import cn.abcyun.cis.charge.util.FillUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import javax.persistence.*;
import java.time.Instant;

/**
 * v2_charge_sheet_attachment
 *
 * <AUTHOR>
@Data
@Entity
@Table(name = "v2_charge_sheet_attachment")
public class ChargeSheetAttachment {
    /**
     * 收费单附件id
     */
    @Id
    private String id;

    /**
     * 收费单id
     */
    @JsonIgnore
    private String chargeSheetId;


    /**
     * 连锁id
     */
    @JsonIgnore
    private String chainId;

    /**
     * 诊所id
     */
    @JsonIgnore
    private String clinicId;

    /**
     * 门诊附件id
     */
    @JsonIgnore
    private String sourceId;

    /**
     * 附件url
     */
    private String url;

    /**
     * 文件名
     */
    private String filename;

    /**
     * 排序
     */
    private int sort;

    /**
     * 图片宽度（像素）
     */
    private Integer imageWidth;

    /**
     * 图片高度（像素）
     */
    private Integer imageHeight;

    /**
     * 是否删除状态（0：正常；1：被删除）
     */
    @JsonIgnore
    private int isDeleted;

    /**
     * 创建时间
     */
    @JsonIgnore
    private Instant created;

    /**
     * 创建人
     */
    @JsonIgnore
    private String createdBy;

    /**
     * 最后修改人
     */
    @JsonIgnore
    private String lastModifiedBy;

    /**
     * 最后修改时间
     */
    @JsonIgnore
    private Instant lastModified;

    @PrePersist
    @PreUpdate
    public void defaultFieldValues() {
        if (filename == null) {
            filename = "";
        }
    }

    public void deleteModel(String operatorId) {
        isDeleted = 1;
        FillUtils.fillLastModifiedBy(this, operatorId);
    }
}