package cn.abcyun.cis.charge.model;

import cn.abcyun.cis.charge.util.FillUtils;
import cn.abcyun.cis.charge.util.MathUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.Instant;

@Entity
@Table(name = "v2_charge_additional_fee")
public class ChargeAdditionalFee {
    @Id
    private String id;

    private String patientOrderId;

    private String chargeSheetId;

    private String patientId;

    private String chainId;

    private String clinicId;

    private int type;

    private BigDecimal amount;

    private int isRefund;

    @JsonIgnore
    private int isDeleted;
    @JsonIgnore
    private String createdBy;
    @JsonIgnore
    private Instant created;
    @JsonIgnore
    private String lastModifiedBy;
    @JsonIgnore
    private Instant lastModified;

    @PrePersist
    @PreUpdate
    public void defaultFieldValues() {
        if (chainId == null) {
            chainId = "";
        }

        if (createdBy == null || created == null) {
            createdBy = lastModifiedBy;
            created = lastModified;
        }
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPatientOrderId() {
        return patientOrderId;
    }

    public void setPatientOrderId(String patientOrderId) {
        this.patientOrderId = patientOrderId;
    }

    public String getChargeSheetId() {
        return chargeSheetId;
    }

    public void setChargeSheetId(String chargeSheetId) {
        this.chargeSheetId = chargeSheetId;
    }

    public String getPatientId() {
        return patientId;
    }

    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }

    public String getChainId() {
        return chainId;
    }

    public void setChainId(String chainId) {
        this.chainId = chainId;
    }

    public String getClinicId() {
        return clinicId;
    }

    public void setClinicId(String clinicId) {
        this.clinicId = clinicId;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public BigDecimal getAmount() {
        return MathUtils.wrapBigDecimal(amount, BigDecimal.ZERO);
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public int getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(int isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreated() {
        return created;
    }

    public void setCreated(Instant created) {
        this.created = created;
    }

    public String getLastModifiedBy() {
        return lastModifiedBy;
    }

    public void setLastModifiedBy(String lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    public Instant getLastModified() {
        return lastModified;
    }

    public void setLastModified(Instant lastModified) {
        this.lastModified = lastModified;
    }

    public int getIsRefund() {
        return isRefund;
    }

    public void setIsRefund(int isRefund) {
        this.isRefund = isRefund;
    }

    public void deleteModel(String operatorId) {
        isDeleted = 1;
        FillUtils.fillLastModifiedBy(this, operatorId);
    }

    public static class AdditionalFeeType {
        public static final int NONE = 0;
        public static final int ADJUSTMENT_FEE = 1;
        public static final int CHARGE_ROUNDING_FEE = 2;
        public static final int OWED_REFUND_FEE = 3;
        public static final int ADJUSTMENT_DISCOUNT_FEE = 4;
        public static final int ADJUSTMENT_ADD_FEE = 5;

    }
}
