package cn.abcyun.cis.charge.model;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.vladmihalcea.hibernate.type.json.JsonStringType;
import lombok.Data;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.Instant;

@Data
@Entity
@Table(name = "v2_charge_refund_form_item")
@TypeDef(name = "json", typeClass = JsonStringType.class)
public class ChargeRefundFormItem {
    @Id
    private String id;
    private String chargeFormItemId;
    private String patientOrderId;
    private String chainId;
    private String clinicId;
    private String refundSheetId;

    private String name;
    private int status;
    private String unit;
    private BigDecimal unitPrice;
    private BigDecimal unitCount;
    private BigDecimal doseCount;
    private BigDecimal discountPrice;
    private BigDecimal totalPrice;
    /**
     * 抵扣总次数
     */
    private BigDecimal deductTotalCount;

    @Type(type = "json")
    @Column(columnDefinition = "json")
    private ChargeRefundFormItemBatchInfo refundFormItemBatchInfo;

    @JsonIgnore
    private int isDeleted;
    @JsonIgnore
    private String createdBy;
    @JsonIgnore
    private Instant created;
    @JsonIgnore
    private String lastModifiedBy;
    @JsonIgnore
    private Instant lastModified;

    @PrePersist
    @PreUpdate
    public void defaultFieldValues() {
        if (chainId == null) {
            chainId = "";
        }

        if (createdBy == null || created == null) {
            createdBy = lastModifiedBy;
            created = lastModified;
        }

        if (discountPrice == null) {
            discountPrice = BigDecimal.ZERO;
        }

        if (doseCount == null) {
            doseCount = BigDecimal.ONE;
        }
    }

    public static class Status {
        public static final int NONE = 0;
        public static final int REFUNDING = 1;
        public static final int REFUNDED = 2;
    }
}
