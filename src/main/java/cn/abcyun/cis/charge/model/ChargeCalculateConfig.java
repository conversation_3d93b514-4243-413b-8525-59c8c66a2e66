package cn.abcyun.cis.charge.model;

import lombok.Data;
import org.hibernate.annotations.Type;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.Instant;
import java.util.List;

@Entity
@Table(name = "v2_charge_calculate_config")
@Data
public class ChargeCalculateConfig {

    @Id
    private String id;

    private String clinicId;

    private String chainId;

    private int roundingType;

    private int bargainSwitch;

    private int singleBargainSwitch;

    private int doctorBargainSwitch;

    private int doctorSingleBargainSwitch;

    @Deprecated
    private int autoSendOrderInfoSwitch;

    private int doctorRegisteredBargainSwitch = 1;

    /**
     * 皮试尚未通过的收费单不允许收费
     */
    private int chargeNeedAstPassSwitch;

    private int nurseBargainSwitch;

    /**
     * 欠费收费开关
     */
    private int oweSheetSwitch;

    private int inspectBargainSwitch;

    private int isDeleted;

    private Instant created;

    private String createdBy;

    private String lastModifiedBy;

    private Instant lastModified;

    private int reservationRegisteredBargainSwitch = 1;

    /**
     * 使用会员/卡项优惠后不允许使用医保收费 0=关闭 1=开启
     */
    private int usedDiscountNotAllowShebaoSwitch;

    /**
     * 卡项开通使用会员卡：0不允许 1允许
     */
    private int openPromotionCardUseMemberSwitch;


    /**
     * 卡项充值使用会员卡：0不允许 1允许
     */
    private int rechargePromotionCardUseMemberSwitch;

    /**
     * 凑整抹零指定支付方式，0：全部支付方式 1：指定支付方式
     */
    private int oddFeeDealType;

    /**
     * 凑整抹零指定支付方式列表
     */
    @Type(type = "json")
    @Column(columnDefinition = "json")
    private List<Long> oddFeeDealPayModes;

    public static class RefundRestriction {
        /**
         * 发药
         */
        public static final int DISPENSE = 0;
        /**
         * 审核
         */
        public static final int AUDIT = 1;
        /**
         * 调配
         */
        public static final int COMPOUND = 2;
    }
}
