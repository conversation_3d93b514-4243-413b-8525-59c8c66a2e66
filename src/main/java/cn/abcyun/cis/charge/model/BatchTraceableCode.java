package cn.abcyun.cis.charge.model;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.TraceableCode;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class BatchTraceableCode implements Serializable {

    /**
     * 号
     */
    private String no;
    /**
     * 追溯码类型 null /0 普通码 10 无码
     * */
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private Integer type;


    public static BatchTraceableCode of(TraceableCode traceableCode) {
        return new BatchTraceableCode()
                .setNo(traceableCode.getNo())
                .setType(traceableCode.getType());
    }

}
