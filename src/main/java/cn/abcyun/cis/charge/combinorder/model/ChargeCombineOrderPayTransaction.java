package cn.abcyun.cis.charge.combinorder.model;

import cn.abcyun.cis.charge.combinorder.dto.ThirdPartyPayDto;
import cn.abcyun.cis.charge.combinorder.dto.ThirdPartyPayItem;
import cn.abcyun.cis.charge.model.ThirdPartyPayInfo;
import cn.abcyun.cis.charge.util.HandleUtils;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.JsonNode;
import com.vladmihalcea.hibernate.type.json.JsonStringType;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.collections.CollectionUtils;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Objects;

@Data
@Entity
@Table(name = "v2_charge_combine_order_pay_transaction")
@Accessors(chain = true)
@TypeDef(name = "json", typeClass = JsonStringType.class)
public class ChargeCombineOrderPayTransaction implements Serializable {

	/**
	 * 主键id
	 */
	@Id
	private Long id;

	/**
	 * 连锁id
	 */
	private String chainId;

	/**
	 * 诊所id
	 */
	private String clinicId;

	/**
	 * 组合订单id
	 */
	private Long orderId;

	/**
	 * 退款关联的支付id
	 */
	private String associatePayTransactionId;

	/**
	 * 支付流水id
	 */
	private String payTransactionId;

	/**
	 * 三方支付系统流水id
	 */
	@Type(type = "json")
	@Column(columnDefinition = "json")
	private ThirdPartyPayInfo payInfo;

	/**
	 * 支付类型（1：付款，2：退款）
	 */
	private int payType;

	/**
	 * 支付状态（1：支付中；2：支付成功；3：支付失败；4：支付取消）
	 * {@link cn.abcyun.cis.charge.combinorder.dto.CombinePayOrderPayResult.PayStatus}
	 */
	private int payStatus;

	/**
	 * 支付方式
	 */
	private int payMode;

	/**
	 * 支付方式子类型
	 */
	private int paySubMode;

	/**
	 * 金额
	 */
	private BigDecimal amount;

	private String businessPayTransactionId;

	/**
	 * 订单子项明细
	 */
	@Type(type = "json")
	@Column(columnDefinition = "json")
	private OrderItemInfo orderItemInfo;

	/**
	 * 第三方支付请求参数
	 */
	@Type(type = "json")
	@Column(columnDefinition = "json")
	private JsonNode payReq;

	private String failMessage;

	/**
	 * 是否删除：0：未删除，1：已删除
	 */
	private int isDeleted;

	/**
	 * 创建时间
	 */
	private Instant created;

	/**
	 * 创建人
	 */
	private String createdBy;

	/**
	 * 最后修改时间
	 */
	private Instant lastModified;

	/**
	 * 最后修改人
	 */
	private String lastModifiedBy;


	public static class PayType {
		public static final int NONE = 0;
		public static final int PAY = 1;
		public static final int REFUND = 2;
	}

	@Data
	@Accessors(chain = true)
	@JsonIgnoreProperties(ignoreUnknown = true)
	public static class OrderItemInfo implements Serializable{

		/**
		 * 业务单号id（可能为空）
		 */
		private Long businessId;

		private List<ThirdPartyPayItem> items;

		/**
		 * 业务补充信息
		 */
		private JsonNode businessExtra;

		public static OrderItemInfo ofOrderItemInfo(ThirdPartyPayDto thirdPartyPayDto) {

			if (Objects.isNull(thirdPartyPayDto)) {
				return null;
			}

			return HandleUtils.isTrueOrFalseReturn(CollectionUtils.isNotEmpty(thirdPartyPayDto.getItems()))
					.handleAndReturn(() -> new ChargeCombineOrderPayTransaction.OrderItemInfo()
									.setItems(thirdPartyPayDto.getItems())
									.setBusinessId(thirdPartyPayDto.getBusinessId())
									.setBusinessExtra(thirdPartyPayDto.getExtra()),
							() -> null);
		}
	}

}
