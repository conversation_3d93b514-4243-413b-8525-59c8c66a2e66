package cn.abcyun.cis.charge.combinorder.dto;

import cn.abcyun.cis.charge.model.ThirdPartyPayInfo;
import cn.abcyun.cis.charge.util.MathUtils;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ThirdPartyRefundResultDto {
    private int payStatus;
    private String thirdPartyPayTaskId;
    private BigDecimal refundedPrincipal = BigDecimal.ZERO; //退费的本金 负数
    private BigDecimal refundedPresent = BigDecimal.ZERO; //退费的赠金 负数
    private ThirdPartyPayInfo payInfo;
    private String combineOrderPayTransactionId;
    private String associateThirdPayTaskId; //退款时关联的thirdPayTaskId

    //退费的总费用
    public BigDecimal getRefundedFee() {
        return MathUtils.wrapBigDecimalAdd(refundedPresent, refundedPrincipal);
    }
}
