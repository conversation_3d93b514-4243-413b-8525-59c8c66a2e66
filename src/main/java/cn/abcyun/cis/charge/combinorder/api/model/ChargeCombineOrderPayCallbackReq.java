package cn.abcyun.cis.charge.combinorder.api.model;

import cn.abcyun.cis.commons.rpc.pay.PayExtraInfo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class ChargeCombineOrderPayCallbackReq {

    private String taskId;
    private String requestTransactionId;
    private int payStatus;
    private BigDecimal receivedFee;
    private PayExtraInfo extraInfo;
    private String operatorId;
    /**
     * 支付子方式：目前只有社保使用：1；省医保，2：市医保，3：异地医保
     */
    private int paySubMode;

    /**
     * 应收
     */
    private BigDecimal receivableFee;

    private int payMode;

    private String message;

}
