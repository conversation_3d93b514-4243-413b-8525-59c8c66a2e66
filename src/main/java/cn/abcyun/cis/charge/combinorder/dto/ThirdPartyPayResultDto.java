package cn.abcyun.cis.charge.combinorder.dto;

import cn.abcyun.cis.charge.model.ThirdPartyPayInfo;
import cn.abcyun.cis.charge.util.MathUtils;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.Instant;

@Data
@Accessors(chain = true)
public class ThirdPartyPayResultDto {
    private Instant created;
    private int payStatus;
    private String thirdPartyPayTaskId;
    private BigDecimal receivedPrincipal = BigDecimal.ZERO; //收到的本金
    private BigDecimal receivedPresent = BigDecimal.ZERO; //收到的赠金
    private ThirdPartyPayInfo payInfo;
    private String combineOrderPayTransactionId;
    private int payMode;
    private int paySubMode;

    //收到的总费用
    public BigDecimal getReceivedFee() {
        return MathUtils.wrapBigDecimalAdd(receivedPresent, receivedPrincipal);
    }
}
