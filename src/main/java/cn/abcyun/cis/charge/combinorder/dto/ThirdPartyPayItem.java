package cn.abcyun.cis.charge.combinorder.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ThirdPartyPayItem implements Serializable {

    @JsonSerialize(using= ToStringSerializer.class)
    private Long id;

    private String businessId;

    private BigDecimal price;

    private String patientOrderId;
}
