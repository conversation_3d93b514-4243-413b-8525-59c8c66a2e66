package cn.abcyun.cis.charge.combinorder.dto;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class BaseThirdPartyDto {

    private String clinicId;

    private String chainId;

    private String patientId;

    private Long orderId;

    private String memberId;
    /**
     * 正值
     */
    private BigDecimal amount;

    private String operatorId;

    private int payMode;

    private int paySubMode;

    /**
     * 业务来源
     * {@link cn.abcyun.cis.charge.combinorder.model.ChargeCombineOrder.Source}
     */
    private int source;

    private Long businessId;

    private JsonNode extra;

    private List<ThirdPartyPayItem> items;

}
