package cn.abcyun.cis.charge.combinorder.dto;

import cn.abcyun.cis.charge.combinorder.model.ChargeCombineOrderTransactionRecord;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.Objects;

@Data
@Accessors(chain = true)
public class CombineOrderTransactionRecordView {

    /**
     * 主键id
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long id;

    /**
     * 订单id
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long orderId;

    /**
     * 订单流水id
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long orderTransactionId;

    /**
     * 订单子项id
     */
    @JsonSerialize(using= ToStringSerializer.class)
    private Long orderItemId;

    /**
     * 业务子项id
     */
    private String businessId;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 赠金支付金额
     */
    private BigDecimal presentAmount;

    /**
     * 本金支付金额
     */
    private BigDecimal principalAmount;


    public static CombineOrderTransactionRecordView ofCombineOrderTransactionRecordView(ChargeCombineOrderTransactionRecord record) {

        if (Objects.isNull(record)) {
            return null;
        }

        CombineOrderTransactionRecordView recordView = new CombineOrderTransactionRecordView();
        BeanUtils.copyProperties(record, recordView);
        return recordView;
    }
}
