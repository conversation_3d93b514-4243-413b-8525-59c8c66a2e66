package cn.abcyun.cis.charge.combinorder.service;

import cn.abcyun.cis.charge.base.InnerPayModes;
import cn.abcyun.cis.charge.combinorder.api.model.ChargeCombineOrderPayCallbackReq;
import cn.abcyun.cis.charge.combinorder.model.ChargeCombineOrderPayTransaction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CombineOrderShebaoPayCallbackService extends CombineOrderThirdPartyCallbackPayModeAbstractService{

    public CombineOrderShebaoPayCallbackService(CombineOrderPayService combineOrderPayService) {
        super(combineOrderPayService);
    }

    @Override
    public String getPayModeKey() {
        return InnerPayModes.getPayModeByName(InnerPayModes.InnerPayModeName.SHEBAO_PAY).getPayModeKey();
    }

    @Override
    public void resetPayModeAndPaySubMode(ChargeCombineOrderPayCallbackReq payCallbackReq, ChargeCombineOrderPayTransaction payTransaction) {
        payCallbackReq.setPayMode(payTransaction.getPayMode());
    }
}
