package cn.abcyun.cis.charge.combinorder.service;

import cn.abcyun.cis.charge.base.ChargeServiceError;
import cn.abcyun.cis.charge.base.ChargeServiceException;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.base.InnerPayModes;
import cn.abcyun.cis.charge.combinorder.dto.*;
import cn.abcyun.cis.charge.combinorder.interfaces.ICombineOrderThirdPartyPayService;
import cn.abcyun.cis.charge.combinorder.model.ChargeCombineOrder;
import cn.abcyun.cis.charge.combinorder.model.ChargeCombineOrderPayTransaction;
import cn.abcyun.cis.charge.combinorder.model.ChargeCombineOrderTransaction;
import cn.abcyun.cis.charge.combinorder.repository.ChargeCombineOrderPayTransactionRepository;
import cn.abcyun.cis.charge.hospital.model.ChargeHospitalPayTransaction;
import cn.abcyun.cis.charge.model.ThirdPartyPayInfo;
import cn.abcyun.cis.charge.rpc.client.ShebaoClient;
import cn.abcyun.cis.charge.service.PatientService;
import cn.abcyun.cis.charge.util.AbcIdUtils;
import cn.abcyun.cis.charge.util.HandleUtils;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.cis.commons.CisServiceResponseBody;
import cn.abcyun.cis.commons.exception.FeignRuntimeException;
import cn.abcyun.cis.commons.exception.ParamNotValidException;
import cn.abcyun.cis.commons.exception.ServiceInternalException;
import cn.abcyun.cis.commons.rpc.crm.PatientInfo;
import cn.abcyun.cis.commons.rpc.pay.PayExtraInfo;
import cn.abcyun.cis.commons.rpc.pay.shebao.*;
import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.cis.idgenerator.AbcIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;

@Service
@Slf4j
public class CombineOrderShebaoPayService extends AbstractCombineOrderPayService implements ICombineOrderThirdPartyPayService {

    private final PatientService patientService;
    private final ShebaoClient shebaoClient;

    @Autowired
    public CombineOrderShebaoPayService(AbcIdGenerator abcIdGenerator,
                                        ChargeCombineOrderPayTransactionRepository combineOrderPayTransactionRepository,
                                        PatientService patientService,
                                        ShebaoClient shebaoClient) {
        super(abcIdGenerator, combineOrderPayTransactionRepository);
        this.patientService = patientService;
        this.shebaoClient = shebaoClient;
    }

    @Override
    public String getPayModeKey() {
        return InnerPayModes.getPayModeByName(InnerPayModes.InnerPayModeName.SHEBAO_PAY).getPayModeKey();
    }

    @Override
    public boolean checkCanPay(ThirdPartyPayDto thirdPartyPayDto) {
        //目前只有长护支持社保
        return thirdPartyPayDto.getSource() == ChargeCombineOrder.Source.HOSPITAL_SHEET
                || thirdPartyPayDto.getSource() == ChargeCombineOrder.Source.OWE_SHEET;
    }

    @Override
    public ThirdPartyPayResultDto pay(ThirdPartyPayDto thirdPartyPayDto) {
        if (thirdPartyPayDto == null) {
            return null;
        }

        PatientInfo patientInfo = patientService.findPatientInfoById(thirdPartyPayDto.getChainId(), thirdPartyPayDto.getClinicId(), thirdPartyPayDto.getPatientId(), false, false);

        PayForShebaoReq payForShebaoReq = new PayForShebaoReq();
        payForShebaoReq.setChargeSheetId(Objects.toString(thirdPartyPayDto.getBusinessId(), ""));
        payForShebaoReq.setClinicId(thirdPartyPayDto.getClinicId());
        payForShebaoReq.setChainId(thirdPartyPayDto.getChainId());
        payForShebaoReq.setOperatorId(thirdPartyPayDto.getOperatorId());
        payForShebaoReq.setPatientName(patientInfo != null ? patientInfo.getName() : null);
        payForShebaoReq.setReceivableFee(thirdPartyPayDto.getAmount());
        payForShebaoReq.setChargeSheetType(translateCombineOrderSource(thirdPartyPayDto.getSource()));
        //如果是单个收费单还款，传一下patientOrderId给社保服务
        if (thirdPartyPayDto.getItems() != null && thirdPartyPayDto.getItems().size() == 1) {
            payForShebaoReq.setPatientOrderId(thirdPartyPayDto.getItems().get(0).getPatientOrderId());
        }

        String combineOrderPayTransactionId = AbcIdUtils.getUIDLong().toString();
        payForShebaoReq.setRequestTransactionId(combineOrderPayTransactionId);

        PayForShebaoRsp payForShebaoRsp = null;
        try {
            long startRequestTime = System.currentTimeMillis();
            CisServiceResponseBody<PayForShebaoRsp> rspBody = shebaoClient.pay(payForShebaoReq);
            if (rspBody != null) {
                payForShebaoRsp = rspBody.getData();
                log.info("payForShebao cost time:{}ms, rsp:{}", (System.currentTimeMillis() - startRequestTime), JsonUtils.dump(payForShebaoRsp));
            }
        } catch (FeignRuntimeException e) {
            log.error("payForShebao feign error", e);
            throw e;
        } catch (Exception e) {
            log.error("payForShebao error", e);
            throw new ServiceInternalException(500, "payForShebao error");
        }

        if (payForShebaoRsp == null) {
            throw new ServiceInternalException(500, "payForShebao error");
        }

        if (payForShebaoRsp.getPayStatus() == CombinePayOrderPayResult.PayStatus.SUCCESS) {
            thirdPartyPayDto.setAmount(payForShebaoRsp.getReceivedFee());
        }

        PayExtraInfo payExtraInfo = payForShebaoRsp.getExtraInfo();
        ThirdPartyPayInfo thirdPartyPayInfo = null;
        if (payExtraInfo != null) {
            thirdPartyPayInfo = new ThirdPartyPayInfo();
            thirdPartyPayInfo.setCardId(payExtraInfo.getCardId());
            thirdPartyPayInfo.setIdCardNum(payExtraInfo.getIdCardNum());
            thirdPartyPayInfo.setCardBalance(payExtraInfo.getCardBalance());
            thirdPartyPayInfo.setCardOwner(payExtraInfo.getCardOwner());
            thirdPartyPayInfo.setTransactionId(payForShebaoRsp.getTaskId());
        }

        ChargeCombineOrderPayTransaction combineOrderPayTransaction = savePayTransaction(Long.parseLong(combineOrderPayTransactionId), thirdPartyPayDto, payForShebaoRsp.getPayStatus(),
                ChargeCombineOrderPayTransaction.PayType.PAY, payForShebaoRsp.getTaskId(), thirdPartyPayInfo,  JsonUtils.dumpAsJsonNode(payForShebaoReq), ChargeCombineOrderPayTransaction.OrderItemInfo.ofOrderItemInfo(thirdPartyPayDto), null);

        ThirdPartyPayResultDto payResult = new ThirdPartyPayResultDto();
        payResult.setCombineOrderPayTransactionId(combineOrderPayTransactionId)
                .setPayStatus(combineOrderPayTransaction.getPayStatus())
                .setThirdPartyPayTaskId(combineOrderPayTransaction.getPayTransactionId())
                .setPaySubMode(combineOrderPayTransaction.getPaySubMode());
        if (combineOrderPayTransaction.getPayStatus() == CombinePayOrderPayResult.PayStatus.SUCCESS) {
            payResult.setReceivedPrincipal(MathUtils.wrapBigDecimalOrZero(thirdPartyPayDto.getAmount()));
        } else {
            payResult.setReceivedPresent(BigDecimal.ZERO);
        }
        payResult.setPayInfo(combineOrderPayTransaction.getPayInfo());
        payResult.setPayMode(thirdPartyPayDto.getPayMode());
        return payResult;
    }

    @Override
    public ThirdPartyRefundResultDto refund(ThirdPartyRefundDto thirdPartyRefundDto) {
        if (thirdPartyRefundDto == null) {
            return null;
        }

        log.info("refundForShebao {}", JsonUtils.dump(thirdPartyRefundDto));

        ChargeCombineOrderTransaction payChargeTransaction = null;
        if (thirdPartyRefundDto.getCombineOrderTransactions() != null) {
            payChargeTransaction = thirdPartyRefundDto.getCombineOrderTransactions()
                    .stream()
                    .sorted((a, b) -> -ObjectUtils.compare(a.getCreated(), b.getCreated()))
                    .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.HEALTH_CARD)
                    .filter(chargeTransaction -> MathUtils.wrapBigDecimalCompare(chargeTransaction.getAmount(), MathUtils.wrapBigDecimalOrZero(thirdPartyRefundDto.getAmount())) == 0)
                    .filter(chargeTransaction -> chargeTransaction.getIsDeleted() == 0)
                    .filter(chargeTransaction -> Objects.isNull(chargeTransaction.getAssociateTransactionId()))
                    .findFirst().orElse(null);
        }

        if (payChargeTransaction == null) {
            log.info("医保卡退费金额与收费金额不一致");
            throw new ChargeServiceException(ChargeServiceError.REFUND_RECEIVED_HEALTH_CARD_NOT_MATCH.getCode(), String.format(ChargeServiceError.REFUND_RECEIVED_HEALTH_CARD_NOT_MATCH.getMessage(), "医保卡"));
        }

        RefundForShebaoReq refundForShebaoReq = new RefundForShebaoReq();
        refundForShebaoReq.setChargeSheetId(Objects.toString(thirdPartyRefundDto.getBusinessId(), ""));
        refundForShebaoReq.setClinicId(thirdPartyRefundDto.getClinicId());
        refundForShebaoReq.setChainId(thirdPartyRefundDto.getChainId());
        refundForShebaoReq.setOperatorId(thirdPartyRefundDto.getOperatorId());
        refundForShebaoReq.setReceivableFee(thirdPartyRefundDto.getAmount().negate());

        int chargeSheetType = 0;
        if (thirdPartyRefundDto.getSource() == ChargeCombineOrder.Source.HOSPITAL_SHEET) {
            chargeSheetType = ShebaoChargeSheetType.LONG_CARE;
        }
        refundForShebaoReq.setChargeSheetType(chargeSheetType);

        String combineOrderPayTransactionId = AbcIdUtils.getUIDLong().toString();
        refundForShebaoReq.setRequestTransactionId(combineOrderPayTransactionId);
        refundForShebaoReq.setPaidTaskId(payChargeTransaction.getThirdPartyPayTransactionId());
        RefundForShebaoRsp refundForShebaoRsp = null;
        try {
            long startRequestTime = System.currentTimeMillis();
            CisServiceResponseBody<RefundForShebaoRsp> rspBody = shebaoClient.refund(refundForShebaoReq);
            if (rspBody != null) {
                refundForShebaoRsp = rspBody.getData();
                log.info("refundForShebao cost time:{}ms, rsp:{}", (System.currentTimeMillis() - startRequestTime), JsonUtils.dump(refundForShebaoRsp));
            }
        } catch (FeignRuntimeException e) {
            log.error("refundForShebao feign error", e);
            throw e;
        } catch (Exception e) {
            log.error("refundForShebao error", e);
            throw new ServiceInternalException(500, "refundForShebao error");
        }

        if (refundForShebaoRsp == null) {
            throw new ServiceInternalException(500, "refundForShebao error");
        }

        if (refundForShebaoRsp.getPayStatus() == CombinePayOrderPayResult.PayStatus.SUCCESS) {
            thirdPartyRefundDto.setAmount(refundForShebaoRsp.getReceivedFee());
        }

        PayExtraInfo payExtraInfo = refundForShebaoRsp.getExtraInfo();
        ThirdPartyPayInfo thirdPartyPayInfo = null;
        if (payExtraInfo != null) {
            thirdPartyPayInfo = new ThirdPartyPayInfo();
            thirdPartyPayInfo.setCardId(payExtraInfo.getCardId());
            thirdPartyPayInfo.setIdCardNum(payExtraInfo.getIdCardNum());
            thirdPartyPayInfo.setCardBalance(payExtraInfo.getCardBalance());
            thirdPartyPayInfo.setCardOwner(payExtraInfo.getCardOwner());
            thirdPartyPayInfo.setTransactionId(refundForShebaoRsp.getTaskId());
        }

        ChargeCombineOrderPayTransaction.OrderItemInfo orderItemInfo = HandleUtils.isTrueOrFalseReturn(CollectionUtils.isNotEmpty(thirdPartyRefundDto.getItems()))
                .handleAndReturn(() -> new ChargeCombineOrderPayTransaction.OrderItemInfo()
                                .setItems(thirdPartyRefundDto.getItems())
                                .setBusinessId(thirdPartyRefundDto.getBusinessId()),
                        () -> null);

        savePayTransaction(Long.parseLong(combineOrderPayTransactionId), thirdPartyRefundDto, refundForShebaoRsp.getPayStatus(), ChargeCombineOrderTransaction.Type.REFUND, refundForShebaoRsp.getTaskId(), thirdPartyPayInfo, JsonUtils.dumpAsJsonNode(refundForShebaoReq), orderItemInfo, thirdPartyRefundDto.getBusinessPayTransactionId());


        ThirdPartyRefundResultDto chargePayRefundResult = new ThirdPartyRefundResultDto();
        chargePayRefundResult.setPayStatus(refundForShebaoRsp.getPayStatus());

        chargePayRefundResult.setCombineOrderPayTransactionId(combineOrderPayTransactionId);
        chargePayRefundResult.setThirdPartyPayTaskId(refundForShebaoRsp.getTaskId());
        chargePayRefundResult.setRefundedPrincipal(refundForShebaoRsp.getPayStatus() == ChargeHospitalPayTransaction.PayStatus.SUCCESS ? MathUtils.wrapBigDecimalNegateOrZero(refundForShebaoRsp.getReceivedFee()) : BigDecimal.ZERO);
        chargePayRefundResult.setPayInfo(thirdPartyPayInfo);
        chargePayRefundResult.setAssociateThirdPayTaskId(payChargeTransaction.getThirdPartyPayTransactionId());
        return chargePayRefundResult;
    }


    private int translateCombineOrderSource(int source) {
        Integer chargeSheetType = null;

        if (source == ChargeCombineOrder.Source.HOSPITAL_SHEET) {
            chargeSheetType = ShebaoChargeSheetType.LONG_CARE;
        } else if (source == ChargeCombineOrder.Source.OWE_SHEET) {
            chargeSheetType = ShebaoChargeSheetType.OWE_PAYMENT;
        }

        if (Objects.isNull(chargeSheetType)) {
            log.info("source未知, source: {}", source);
            throw new ParamNotValidException("订单不支持社保支付");
        }

        return chargeSheetType.intValue();
    }
}
