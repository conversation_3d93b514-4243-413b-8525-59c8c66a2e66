package cn.abcyun.cis.charge.combinorder.service;

import cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientMemberCardPayReq;
import cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientMemberCardPayRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientMemberCardRefundReq;
import cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientMemberCardRefundRsp;
import cn.abcyun.bis.rpc.sdk.mp.model.chargecenter.ChargeCenterConstants;
import cn.abcyun.cis.charge.base.ChargeServiceError;
import cn.abcyun.cis.charge.base.ChargeServiceException;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.base.InnerPayModes;
import cn.abcyun.cis.charge.combinorder.dto.*;
import cn.abcyun.cis.charge.combinorder.interfaces.ICombineOrderThirdPartyPayService;
import cn.abcyun.cis.charge.combinorder.model.ChargeCombineOrder;
import cn.abcyun.cis.charge.combinorder.model.ChargeCombineOrderPayTransaction;
import cn.abcyun.cis.charge.combinorder.model.ChargeCombineOrderTransaction;
import cn.abcyun.cis.charge.combinorder.repository.ChargeCombineOrderPayTransactionRepository;
import cn.abcyun.cis.charge.model.ThirdPartyPayInfo;
import cn.abcyun.cis.charge.service.PatientService;
import cn.abcyun.cis.charge.service.rpc.CisCrmService;
import cn.abcyun.cis.charge.util.AbcIdUtils;
import cn.abcyun.cis.charge.util.HandleUtils;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.cis.commons.exception.ParamNotValidException;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.exception.ServiceInternalException;
import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.cis.idgenerator.AbcIdGenerator;
import cn.abcyun.common.log.marker.AbcLogMarker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CombineOrderMemberCardPayService extends AbstractCombineOrderPayService implements ICombineOrderThirdPartyPayService {

    private final CisCrmService cisCrmService;
    private final PatientService patientService;

    @Autowired
    public CombineOrderMemberCardPayService(
            AbcIdGenerator abcIdGenerator,
            ChargeCombineOrderPayTransactionRepository combineOrderPayTransactionRepository,
            CisCrmService cisCrmService,
            PatientService patientService) {
        super(abcIdGenerator, combineOrderPayTransactionRepository);
        this.cisCrmService = cisCrmService;
        this.patientService = patientService;
    }

    @Override
    public String getPayModeKey() {
        return InnerPayModes.getPayModeByName(InnerPayModes.InnerPayModeName.MEMBER_CARD).getPayModeKey();
    }

    @Override
    public boolean checkCanPay(ThirdPartyPayDto thirdPartyPayDto) {
        return true;
    }

    @Override
    public ThirdPartyPayResultDto pay(ThirdPartyPayDto thirdPartyPayDto) {
        String combineOrderPayTransactionId = AbcIdUtils.getUIDLong().toString();
        PatientMemberCardPayRsp memberCardPayRsp = null;

        PatientMemberCardPayReq req = new PatientMemberCardPayReq();
        req.setMemberId(thirdPartyPayDto.getMemberId());
        req.setCash(thirdPartyPayDto.getAmount());
        req.setBusinessType(translateCombineOrderSource(thirdPartyPayDto.getSource()));
        req.setBusinessId(combineOrderPayTransactionId);
        req.setPassword(thirdPartyPayDto.getMemberCardPassword());
        req.setTransactionPatientId(thirdPartyPayDto.getPatientId());
        req.setClinicId(thirdPartyPayDto.getClinicId());
        req.setChainId(thirdPartyPayDto.getChainId());
        req.setOperatorId(thirdPartyPayDto.getOperatorId());
        req.setSourceType(ChargeCenterConstants.BusinessType.CIS_CHARGE_OWE_SHEET);
        try {
            memberCardPayRsp = cisCrmService.memberCardPay(req);
        }catch (Exception e) {
            log.error("memberCardPay error");
            throw e;
        }

        if (memberCardPayRsp == null) {
            throw new ServiceInternalException(500, "payForShebao error");
        }

        ThirdPartyPayInfo thirdPartyPayInfo = new ThirdPartyPayInfo();
        thirdPartyPayInfo.setTransactionId(memberCardPayRsp.getTransactionId());
        thirdPartyPayInfo.setCardBalance(MathUtils.wrapBigDecimalAdd(memberCardPayRsp.getPresentBalance(), memberCardPayRsp.getPrincipalBalance()));
        thirdPartyPayInfo.setCardId(req.getMemberId());

        ChargeCombineOrderPayTransaction combineOrderPayTransaction = savePayTransaction(Long.parseLong(combineOrderPayTransactionId), thirdPartyPayDto, CombinePayOrderPayResult.PayStatus.SUCCESS,
                ChargeCombineOrderPayTransaction.PayType.PAY, memberCardPayRsp.getTransactionId(), thirdPartyPayInfo, JsonUtils.dumpAsJsonNode(req), ChargeCombineOrderPayTransaction.OrderItemInfo.ofOrderItemInfo(thirdPartyPayDto), null);


        ThirdPartyPayResultDto payResult = new ThirdPartyPayResultDto();
        payResult.setCombineOrderPayTransactionId(combineOrderPayTransactionId)
                .setPayStatus(combineOrderPayTransaction.getPayStatus())
                .setThirdPartyPayTaskId(combineOrderPayTransaction.getPayTransactionId())
                .setPaySubMode(combineOrderPayTransaction.getPaySubMode());
        if (combineOrderPayTransaction.getPayStatus() == CombinePayOrderPayResult.PayStatus.SUCCESS) {
            payResult.setReceivedPrincipal(memberCardPayRsp.getPrincipal());
            payResult.setReceivedPresent(memberCardPayRsp.getPresent());
        }
        payResult.setPayInfo(combineOrderPayTransaction.getPayInfo());
        payResult.setPayMode(thirdPartyPayDto.getPayMode());
        return payResult;
    }


    private int translateCombineOrderSource(int source) {
        Integer businessType = 0;
        if (source == ChargeCombineOrder.Source.OWE_SHEET) {
            businessType = PatientMemberCardPayReq.BusinessType.CHARGE_ARREARS;
        }

        if (Objects.isNull(businessType)) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "该source还不支持会员卡支付, source: {}", source);
            throw new ParamNotValidException("暂不支持会员卡支付");
        }
        return businessType.intValue();
    }

    @Override
    public ThirdPartyRefundResultDto refund(ThirdPartyRefundDto thirdPartyRefundDto) {
        String transactionId = AbcIdUtils.getUID();

        if (StringUtils.isEmpty(thirdPartyRefundDto.getMemberId())) {
            throw new ParamRequiredException("memberId");
        }

        // 退会员支付金额是否匹配
        BigDecimal memberPaidAmount = getMemberPaidAmount(thirdPartyRefundDto.getCombineOrderTransactions());
        if (memberPaidAmount.compareTo(thirdPartyRefundDto.getAmount().abs()) < 0) {
            throw new ChargeServiceException(ChargeServiceError.CHARGE_REFUND_MEMBER_PAID_FEE_NOT_MATCH);
        }

        List<String> memberCardPayChargeTransactionIds = Optional.ofNullable(thirdPartyRefundDto.getCombineOrderTransactions())
                .orElse(new ArrayList<>())
                .stream()
                .filter(chargeCombineOrderTransaction -> chargeCombineOrderTransaction.getIsDeleted() == 0
                        && chargeCombineOrderTransaction.getPayMode() == Constants.ChargePayMode.MEMBER_CARD
                        && MathUtils.wrapBigDecimalCompare(chargeCombineOrderTransaction.getAmount(), BigDecimal.ZERO) > 0
                        && StringUtils.isNotEmpty(chargeCombineOrderTransaction.getThirdPartyPayTransactionId())
                )
                .map(chargeCombineOrderTransaction -> chargeCombineOrderTransaction.getThirdPartyPayTransactionId())
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(memberCardPayChargeTransactionIds)) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "memberCardPayChargeTransactionIds is null");
            throw new ChargeServiceException(ChargeServiceError.CHARGE_REFUND_MEMBER_PAID_FEE_NOT_MATCH);
        }

        PatientMemberCardRefundReq memberCardRefundReq = new PatientMemberCardRefundReq();
        memberCardRefundReq.setCash(thirdPartyRefundDto.getAmount().abs());
        memberCardRefundReq.setChainId(thirdPartyRefundDto.getChainId());
        memberCardRefundReq.setClinicId(thirdPartyRefundDto.getClinicId());
        memberCardRefundReq.setMemberId(thirdPartyRefundDto.getMemberId());
        memberCardRefundReq.setBusinessType(PatientMemberCardPayReq.BusinessType.CHARGE_ARREARS);
        memberCardRefundReq.setOperatorId(thirdPartyRefundDto.getOperatorId());
        memberCardRefundReq.setBusinessId(transactionId);
        memberCardRefundReq.setTransactionIds(memberCardPayChargeTransactionIds);
        memberCardRefundReq.setSourceType(ChargeCenterConstants.BusinessType.CIS_CHARGE_OWE_SHEET);

        PatientMemberCardRefundRsp memberCardRefundRsp = null;
        try {
            memberCardRefundRsp = cisCrmService.memberCardRefund(memberCardRefundReq);
        } catch (Exception e) {
            log.error("memberCardRefund error", e);
            throw e;
        }

        if (Objects.isNull(memberCardRefundRsp)) {
            throw new ServiceInternalException("memberCardRefund error");
        }

        ThirdPartyPayInfo thirdPartyPayInfo = new ThirdPartyPayInfo();
        thirdPartyPayInfo.setTransactionId(memberCardRefundRsp.getTransactionId());
        thirdPartyPayInfo.setCardBalance(MathUtils.wrapBigDecimalAdd(memberCardRefundRsp.getPresentBalance(), memberCardRefundRsp.getPrincipalBalance()));


        ChargeCombineOrderPayTransaction.OrderItemInfo orderItemInfo = HandleUtils.isTrueOrFalseReturn(CollectionUtils.isNotEmpty(thirdPartyRefundDto.getItems()))
                .handleAndReturn(() -> new ChargeCombineOrderPayTransaction.OrderItemInfo()
                                .setItems(thirdPartyRefundDto.getItems())
                                .setBusinessId(thirdPartyRefundDto.getBusinessId()),
                        () -> null);

        ChargeCombineOrderPayTransaction combineOrderPayTransaction = savePayTransaction(Long.parseLong(transactionId), thirdPartyRefundDto, CombinePayOrderPayResult.PayStatus.SUCCESS,
                ChargeCombineOrderPayTransaction.PayType.REFUND, memberCardRefundRsp.getTransactionId(), thirdPartyPayInfo, JsonUtils.dumpAsJsonNode(memberCardRefundReq), orderItemInfo, thirdPartyRefundDto.getBusinessPayTransactionId());

        ThirdPartyRefundResultDto chargePayRefundResult = new ThirdPartyRefundResultDto();
        chargePayRefundResult.setPayStatus(combineOrderPayTransaction.getPayStatus());
        chargePayRefundResult.setCombineOrderPayTransactionId(transactionId);
        chargePayRefundResult.setThirdPartyPayTaskId(memberCardRefundRsp.getTransactionId());
        if (combineOrderPayTransaction.getPayStatus() == CombinePayOrderPayResult.PayStatus.SUCCESS) {
            chargePayRefundResult.setRefundedPrincipal(MathUtils.wrapBigDecimalNegateOrZero(memberCardRefundRsp.getPrincipal()));
            chargePayRefundResult.setRefundedPresent(MathUtils.wrapBigDecimalNegateOrZero(memberCardRefundRsp.getPresent()));
        }
        chargePayRefundResult.setPayInfo(thirdPartyPayInfo);
        return chargePayRefundResult;
    }

    private BigDecimal getMemberPaidAmount(List<ChargeCombineOrderTransaction> combineOrderTransactions) {
        if (combineOrderTransactions == null) {
            return BigDecimal.ZERO;
        }

        return combineOrderTransactions
                .stream()
                .filter(chargeTransaction -> chargeTransaction.getPayMode() == Constants.ChargePayMode.MEMBER_CARD)
                .map(ChargeCombineOrderTransaction::getAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}
