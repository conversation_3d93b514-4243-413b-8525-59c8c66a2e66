package cn.abcyun.cis.charge.combinorder.model;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;

@Data
@Entity
@Table(name = "v2_charge_combine_order_transaction_record")
@Accessors(chain = true)
public class ChargeCombineOrderTransactionRecord implements Serializable {

	/**
	 * 主键id
	 */
	@Id
	private Long id;

	/**
	 * 连锁id
	 */
	private String chainId;

	/**
	 * 诊所id
	 */
	private String clinicId;

	/**
	 * 订单id
	 */
	private Long orderId;

	/**
	 * 订单流水id
	 */
	private Long orderTransactionId;

	/**
	 * 订单子项id
	 */
	private Long orderItemId;

	/**
	 * 金额
	 */
	private BigDecimal amount;

	/**
	 * 赠金支付金额
	 */
	@Transient
	private BigDecimal presentAmount;

	/**
	 * 本金支付金额
	 */
	@Transient
	private BigDecimal principalAmount;

	/**
	 * 是否删除：0：未删除，1：已删除
	 */
	private int isDeleted;

	/**
	 * 创建时间
	 */
	private Instant created;

	/**
	 * 创建人
	 */
	private String createdBy;

	/**
	 * 最后修改时间
	 */
	private Instant lastModified;

	/**
	 * 最后修改人
	 */
	private String lastModifiedBy;
}
