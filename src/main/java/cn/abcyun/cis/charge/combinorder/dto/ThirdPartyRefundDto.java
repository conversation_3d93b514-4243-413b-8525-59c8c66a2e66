package cn.abcyun.cis.charge.combinorder.dto;

import cn.abcyun.cis.charge.combinorder.model.ChargeCombineOrderTransaction;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ThirdPartyRefundDto extends BaseThirdPartyDto {

    private List<ChargeCombineOrderTransaction> combineOrderTransactions;

    private String businessPayTransactionId;
    /**
     * 被退费的transactionIds
     */
    private List<String> transactionIds;
}
