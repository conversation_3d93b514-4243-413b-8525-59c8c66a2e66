package cn.abcyun.cis.charge.combinorder.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class CombineRefundOrderItem extends CombinePayOrderItem {

    @NotEmpty(message = "combineOrderItemId不能为空")
    private Long combineOrderItemId;
}
