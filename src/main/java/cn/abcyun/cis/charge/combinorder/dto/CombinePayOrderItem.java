package cn.abcyun.cis.charge.combinorder.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class CombinePayOrderItem {

    @NotEmpty(message = "businessId不能为空")
    private String businessId;

    @NotNull(message = "price不能为空")
    @DecimalMin(value = "0", message = "price不能小于{value}")
    private BigDecimal price;


    private String patientOrderId;

}
