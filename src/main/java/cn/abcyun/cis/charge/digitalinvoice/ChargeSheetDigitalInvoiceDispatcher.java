package cn.abcyun.cis.charge.digitalinvoice;

import cn.abcyun.bis.rpc.sdk.cis.model.charge.GoodsFeeType;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ;
import cn.abcyun.cis.charge.api.model.DigitalInvoiceBasePrintView;
import cn.abcyun.cis.charge.api.model.MedicalDigitalInvoicePrintView;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.model.ChargeFormItem;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.model.ChargeSheetInvoiceContext;
import cn.abcyun.cis.charge.model.InvoiceChargeFormItem;
import cn.abcyun.cis.charge.service.OrganProductService;
import cn.abcyun.cis.charge.service.dto.InvoiceListDetail;
import cn.abcyun.cis.charge.util.ChargeUtils;
import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.cis.commons.util.MathUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ChargeSheetDigitalInvoiceDispatcher<R> {

    protected Boolean logDebugEnable;

    @Value("${abc.env}")
    protected void setEnv(String env) {
        logDebugEnable = "local".equals(env) || "dev".equals(env) || "test".equals(env);
    }

    private Map<Integer, ChargeSheetDigitalInvoiceService> chargeSheetDigitalInvoiceServiceMap;

    @Autowired
    public ChargeSheetDigitalInvoiceDispatcher(List<ChargeSheetDigitalInvoiceService> chargeSheetDigitalInvoiceServices) {
        initChargeSheetDigitalInvoiceServiceMap(chargeSheetDigitalInvoiceServices);
    }

    private void initChargeSheetDigitalInvoiceServiceMap(List<ChargeSheetDigitalInvoiceService> chargeSheetDigitalInvoiceServices) {

        if (CollectionUtils.isEmpty(chargeSheetDigitalInvoiceServices)) {
            chargeSheetDigitalInvoiceServiceMap = new HashMap<>();
        }
        chargeSheetDigitalInvoiceServiceMap = chargeSheetDigitalInvoiceServices.stream()
                .collect(Collectors.toMap(ChargeSheetDigitalInvoiceService::getSupplierId, Function.identity()));
    }

    public R generateDigitalInvoiceReq(ChargeSheet chargeSheet, int invoiceSupplierId, String operatorId, ChargeSheetInvoiceContext chargeSheetInvoiceContext) {
        ChargeSheetDigitalInvoiceService chargeSheetDigitalInvoiceService = chargeSheetDigitalInvoiceServiceMap.getOrDefault(invoiceSupplierId, null);
        if (Objects.isNull(chargeSheetDigitalInvoiceService)) {
            return null;
        }
        R r = chargeSheetDigitalInvoiceService.generateDigitalInvoiceReq(chargeSheet, operatorId, chargeSheetInvoiceContext);
        if (logDebugEnable) {
            log.info("ChargeSheetDigitalInvoiceDispatcher generateDigitalInvoiceReq chargeSheetId: {}, invoiceSupplierId: {}, digitalInvoiceReq: {}",
                    chargeSheet.getId(), invoiceSupplierId, JsonUtils.dump(r));
        }
        return r;
    }

    public DigitalInvoiceBasePrintView generateDigitalInvoicePrintView(ChargeSheet chargeSheet, int invoiceSupplierId, String operatorId, ChargeSheetInvoiceContext invoiceContext) {
        ChargeSheetDigitalInvoiceService chargeSheetDigitalInvoiceService = chargeSheetDigitalInvoiceServiceMap.getOrDefault(invoiceSupplierId, null);
        if (Objects.isNull(chargeSheetDigitalInvoiceService)) {
            return null;
        }
        DigitalInvoiceBasePrintView digitalInvoicePrintView = chargeSheetDigitalInvoiceService.generateDigitalInvoicePrintView(chargeSheet, operatorId, invoiceContext);
        if (logDebugEnable) {
            log.info("ChargeSheetDigitalInvoiceDispatcher generateDigitalInvoicePrintView chargeSheetId: {}, invoiceSupplierId: {}, digitalInvoicePrintView: {}",
                    chargeSheet.getId(), invoiceSupplierId, JsonUtils.dump(digitalInvoicePrintView));
        }
        if (digitalInvoicePrintView != null) {
            BigDecimal invoiceFee = MathUtils.wrapBigDecimalAdd(chargeSheet.getReceivedFee(), chargeSheet.getRefundFee());
            if (!CollectionUtils.isEmpty(invoiceContext.getInvoiceChargeFormItems())) {
                invoiceFee = invoiceContext.getInvoiceChargeFormItems().stream()
                        .map(InvoiceChargeFormItem::getInvoiceInfo)
                        .map(InvoiceChargeFormItem.InvoiceInfo::getDiscountedTotalPrice)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            digitalInvoicePrintView.setInvoiceFee(invoiceFee);
        }
        return digitalInvoicePrintView;
    }
}
