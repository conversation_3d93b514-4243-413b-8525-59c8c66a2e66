package cn.abcyun.cis.charge.digitalinvoice;

import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsItem;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsSystemType;
import cn.abcyun.bis.rpc.sdk.cis.model.invoice.CreateInvoiceReq;
import cn.abcyun.bis.rpc.sdk.cis.model.invoice.InvoiceConst;
import cn.abcyun.bis.rpc.sdk.cis.model.pay.shebao.ShebaoInvoiceRemarkRsp;
import cn.abcyun.cis.charge.api.model.DigitalInvoicePrintPreview;
import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.model.*;
import cn.abcyun.cis.charge.processor.ChargeSheetFeeProtocol;
import cn.abcyun.cis.charge.service.*;
import cn.abcyun.cis.charge.service.dto.ChargeTransactionView;
import cn.abcyun.cis.charge.service.dto.InvoiceDetailItemExtend;
import cn.abcyun.cis.charge.util.ChargeUtils;
import cn.abcyun.cis.charge.util.DateUtils;
import cn.abcyun.cis.charge.util.MathUtils;
import cn.abcyun.cis.charge.util.PrintUtils;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.rpc.crm.PatientInfo;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrder;
import cn.abcyun.cis.commons.rpc.registration.RegistrationFormItem;
import cn.abcyun.cis.commons.rpc.shebao.QueryChargeSheetShebaoInfoRsp;
import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.cis.commons.util.TextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ChargeSheetIsvInvoiceService extends ChargeSheetDigitalInvoiceService {

    private final OrganProductService organProductService;

    private final PatientService patientService;

    private final PatientOrderService patientOrderService;

    public ChargeSheetIsvInvoiceService(EmployeeService employeeService,
                                        OrganProductService organProductService,
                                        ShebaoService shebaoService,
                                        PatientService patientService,
                                        ChargeOweSheetService chargeOweSheetService,
                                        PatientOrderService patientOrderService) {
        super(employeeService, shebaoService, null, chargeOweSheetService);
        this.organProductService = organProductService;
        this.patientService = patientService;
        this.patientOrderService = patientOrderService;
    }


    @Override
    int getSupplierId() {
        return InvoiceConst.SupplierId.ABC_ISV;
    }

    /**
     * 发票预览返回的数据信息
     */
    @Override
    public DigitalInvoicePrintPreview generateDigitalInvoicePrintView(ChargeSheet chargeSheet, String operatorId, ChargeSheetInvoiceContext chargeSheetInvoiceContext) {
        CreateInvoiceReq.DigitalInvoice digitalInvoice = this.generateDigitalInvoiceReq(chargeSheet, operatorId, chargeSheetInvoiceContext);
        DigitalInvoicePrintPreview view = new DigitalInvoicePrintPreview();
        view.setPayee(digitalInvoice.getPayee());
        view.setInvoiceOpener(digitalInvoice.getClerk());
        view.setPatientId(digitalInvoice.getPatientId());
        view.setPatientName(digitalInvoice.getPatientName());
        view.setOutpatientDate(digitalInvoice.getInvoiceDate());
        view.setChargeDate(digitalInvoice.getInvoiceDate());
        view.setInvoiceRemark(StringUtils.isBlank(digitalInvoice.getRemark()) ? digitalInvoice.getSimpleRemark() : digitalInvoice.getRemark());
        List<CreateInvoiceReq.InvoiceDetailItem> detailItems = digitalInvoice.getDetailItems();
        if (CollectionUtils.isNotEmpty(detailItems)) {
            view.setItems(detailItems);
        }
        return view;
    }


    @Override
    public CreateInvoiceReq.DigitalInvoice generateDigitalInvoiceReq(ChargeSheet chargeSheet, String operatorId, ChargeSheetInvoiceContext chargeSheetInvoiceContext) {
        CreateInvoiceReq.DigitalInvoice digitalInvoice = baseGenerateDigitalInvoice(chargeSheet.getChainId(), operatorId);
        if (Objects.isNull(digitalInvoice)) {
            return null;
        }
        Organ organ = organProductService.findOrganById(chargeSheet.getClinicId());
        if (Objects.isNull(organ)) {
            throw new NotFoundException();
        }
        ChargeSheet memoryChargeSheet = ChargeUtils.deepCopyChargeSheet(chargeSheet);
        // 查询患者信息
        PatientInfo patientInfo = patientService.findPatientInfoById(memoryChargeSheet.getChainId(), memoryChargeSheet.getClinicId(), memoryChargeSheet.getPatientId(), false, false);
        Optional.ofNullable(patientInfo).ifPresent(patient -> {
            digitalInvoice.setPatientId(patient.getId());
            digitalInvoice.setPatientName(patient.getName());
        });

        List<ChargeOweSheet> chargeOweSheetList = chargeOweSheetService.findChargeOweSheetsByChargeSheetId(memoryChargeSheet.getClinicId(), memoryChargeSheet.getId());
        List<ChargeTransactionView> transactionViewList = ChargeSheetFeeProtocol.generateChargeTransactionViews(memoryChargeSheet, chargeOweSheetList, true, true, null);
        String shebaoChargeSheetId = super.getShebaoChargeSheetId(chargeOweSheetList, transactionViewList, memoryChargeSheet.getId());
        QueryChargeSheetShebaoInfoRsp shebaoInfoRsp = this.queryChargeSheetShebaoInfo(shebaoChargeSheetId, memoryChargeSheet);
        digitalInvoice.setDetailItems(this.generateInvoiceDetailItems(organ, shebaoInfoRsp.getGoodsItems(), chargeSheetInvoiceContext));

        PatientOrder patientOrder = patientOrderService.findPatientOrder(memoryChargeSheet.getPatientOrderId(), PatientOrderService.getAgeLockTime(memoryChargeSheet));
        String caseNumber = String.format("%08d", patientOrder.getNo());
        String medicalInstitution = Objects.isNull(organ.getCategory()) ? "机构" : organ.getCategory();
        String outpatientDateStr = DateUtils.convertInstantToString(Optional.ofNullable(memoryChargeSheet.getDiagnosedDate()).orElse(memoryChargeSheet.getChargedTime()), DateUtils.WECHAT_PAY_SUCCESS_TIME_FORMAT);
        digitalInvoice.setRemark(this.generateRemark(organ.getHisType(), caseNumber, outpatientDateStr, medicalInstitution, memoryChargeSheet.getRealReceivedFee(), shebaoInfoRsp, transactionViewList));
        return digitalInvoice;
    }

    public List<CreateInvoiceReq.InvoiceDetailItem> generateInvoiceDetailItems(Organ organ, List<QueryChargeSheetShebaoInfoRsp.GoodsItem> goodsItems, ChargeSheetInvoiceContext chargeSheetInvoiceContext) {
        boolean isNeedMedicalFeeGrade = TextUtils.equals(organ.getAddressCityId(), Constants.RegionId.HANGZHOU) || TextUtils.equals(organ.getAddressCityId(), Constants.RegionId.NANJING);
        Map<String, QueryChargeSheetShebaoInfoRsp.GoodsItem> goodsItemMap = new HashMap<>();
        //如果是杭州,查询药品的医保等级
        if (isNeedMedicalFeeGrade) {
            goodsItemMap = PrintUtils.convertShebaoGoodsItemMap(Optional.ofNullable(goodsItems).orElse(new ArrayList<>()));
        }
        Map<String, QueryChargeSheetShebaoInfoRsp.GoodsItem> finalGoodsItemMap = goodsItemMap;
        return chargeSheetInvoiceContext.getInvoiceChargeFormItems()
                .stream()
                .map(chargeFormItem -> generateInvoiceDetailItems(chargeFormItem, isNeedMedicalFeeGrade, finalGoodsItemMap, chargeSheetInvoiceContext.getGoodsTypeKeyToGoodsTypeMap()))
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private static List<CreateInvoiceReq.InvoiceDetailItem> generateInvoiceDetailItems(
            InvoiceChargeFormItem invoiceChargeFormItem,
            boolean isNeedMedicalFeeGrade,
            Map<String, QueryChargeSheetShebaoInfoRsp.GoodsItem> goodsItemMap,
            Map<String, GoodsSystemType> goodsTypeKeyToGoodsTypeMap) {

        List<CreateInvoiceReq.InvoiceDetailItem> list = new ArrayList<>();
        InvoiceChargeFormItem.InvoiceInfo invoiceInfo = invoiceChargeFormItem.getInvoiceInfo();
        if (invoiceInfo == null || MathUtils.wrapBigDecimalCompare(invoiceInfo.getCount(), BigDecimal.ZERO) <= 0 || MathUtils.wrapBigDecimalCompare(invoiceInfo.getDiscountedTotalPrice(), BigDecimal.ZERO) <= 0) {
            return list;
        }
        ChargeFormItem chargeFormItem = invoiceChargeFormItem.getChargeFormItem();
        int goodsType = chargeFormItem.getProductType();
        int goodsSubType = chargeFormItem.getProductSubType();
        String goodsCmSpec = null;
        Integer goodsTypeId = null;
        GoodsItem goodsItem = Optional.ofNullable(chargeFormItem.getProductInfo()).map(productInfo -> JsonUtils.readValue(productInfo, GoodsItem.class)).orElse(null);
        if (goodsItem != null) {
            goodsCmSpec = goodsItem.getCMSpec();
            goodsTypeId = goodsItem.getTypeId();
        }
        Long feeTypeId = ChargeUtils.getFeeTypeIdOrDefaultByGoodsTypeId(goodsType, goodsSubType, goodsCmSpec, goodsTypeId, chargeFormItem.getFeeTypeId(), goodsTypeKeyToGoodsTypeMap);
        String productName = goodsType == Constants.ProductType.REGISTRATION ? "挂号费" : chargeFormItem.getName();
        if (isNeedMedicalFeeGrade && StringUtils.isNotEmpty(chargeFormItem.getProductId())) {
            QueryChargeSheetShebaoInfoRsp.GoodsItem shebaoGoodsItem = Optional.ofNullable(goodsItemMap.getOrDefault(chargeFormItem.getId(), null))
                    .orElse(goodsItemMap.getOrDefault(chargeFormItem.getProductId(), null));
            String productNamePrefix = Optional.ofNullable(shebaoGoodsItem)
                    .map(QueryChargeSheetShebaoInfoRsp.GoodsItem::getMedicalFeeGrade)
                    .map(medicalFeeGradle -> {
                        if (medicalFeeGradle == 1) {
                            return String.format("[%s]", Constants.MedicalFeeGradeName.JIA);
                        }
                        if (medicalFeeGradle == 2) {
                            return String.format("[%s]", Constants.MedicalFeeGradeName.YI);
                        }
                        if (medicalFeeGradle == 3) {
                            return String.format("[%s]", Constants.MedicalFeeGradeName.BING);
                        }
                        return "";
                    }).orElse("");
            productName = productNamePrefix + productName;
        }
        if (!isNeedMedicalFeeGrade) {
            list.add(generateInvoiceDetailItem(productName, invoiceInfo.getUnitPrice(), invoiceInfo.getCount(),
                    Optional.ofNullable(chargeFormItem.getUnit()).orElse("次"),
                    goodsType,
                    goodsSubType,
                    goodsCmSpec,
                    feeTypeId));
            return list;
        }
        if (!(goodsType == Constants.ProductType.EXAMINATION && chargeFormItem.getComposeType() == GoodsConst.GoodsCombine.COMBINE)) {
            list.add(generateInvoiceDetailItem(productName, invoiceInfo.getUnitPrice(), invoiceInfo.getCount(),
                    Optional.ofNullable(chargeFormItem.getUnit()).orElse("次"),
                    goodsType,
                    goodsSubType,
                    goodsCmSpec,
                    feeTypeId));
            return list;
        }

        GoodsItem combineGoodsItem = JsonUtils.readValue(chargeFormItem.getProductSnapshot(), GoodsItem.class);
        List<GoodsItem> combineGoodsItemChildren = Optional.ofNullable(combineGoodsItem).map(GoodsItem::getChildren).orElse(null);

        // 对上社保编码或者子项为空或者没有goods信息,都返回组合母项
        if (combineGoodsItem == null || StringUtils.isNotEmpty(combineGoodsItem.getShebaoCode()) || CollectionUtils.isEmpty(combineGoodsItemChildren)) {
            list.add(generateInvoiceDetailItem(productName, invoiceInfo.getUnitPrice(), invoiceInfo.getCount(),
                    Optional.ofNullable(chargeFormItem.getUnit()).orElse("次"),
                    goodsType,
                    goodsSubType,
                    goodsCmSpec,
                    feeTypeId));
            return list;
        }
        list.addAll(generateInvoiceDetailItemExtendsAndFlatPrice(chargeFormItem, combineGoodsItemChildren, invoiceInfo.getUnitPrice(), goodsTypeKeyToGoodsTypeMap));
        return list;
    }

    public static List<InvoiceDetailItemExtend> generateInvoiceDetailItemExtendsAndFlatPrice(ChargeFormItem chargeFormItem, List<GoodsItem> combineGoodsItemChildren, BigDecimal combineUnitPrice, Map<String, GoodsSystemType> goodsTypeKeyToGoodsTypeMap) {
        if (CollectionUtils.isEmpty(combineGoodsItemChildren)) {
            return new ArrayList<>();
        }
        BigDecimal totalCount = MathUtils.calculateTotalCount(chargeFormItem.getUnitCount()
                        .subtract(MathUtils.wrapBigDecimalOrZero(chargeFormItem.getRefundUnitCount())),
                chargeFormItem.getDoseCount());
        List<InvoiceDetailItemExtend> invoiceDetailItems = generateInvoiceDetailItemExtends(combineGoodsItemChildren, totalCount, goodsTypeKeyToGoodsTypeMap);
        // 判断是否所有子项的金额都为0
        boolean isNotAllZero = invoiceDetailItems.stream().anyMatch(invoiceDetailItem -> MathUtils.wrapBigDecimalCompare(invoiceDetailItem.getUnitPrice(), BigDecimal.ZERO) > 0);

        if (isNotAllZero) {
            List<InvoiceDetailItemExtend> noZeroInvoiceDetailItems = invoiceDetailItems.stream()
                    .filter(invoiceDetailItem -> MathUtils.wrapBigDecimalCompare(invoiceDetailItem.getUnitPrice(), BigDecimal.ZERO) > 0)
                    .collect(Collectors.toList());
            BigDecimal totalPrice = noZeroInvoiceDetailItems.stream().map(InvoiceDetailItemExtend::getUnitPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal rate = combineUnitPrice.divide(totalPrice, 4, RoundingMode.DOWN);
            BigDecimal leftPrice = MathUtils.wrapBigDecimalSubtract(combineUnitPrice, totalPrice);
            if (MathUtils.wrapBigDecimalCompare(leftPrice, BigDecimal.ZERO) != 0) {
                BigDecimal leftItemUnitPrice = combineUnitPrice;
                for (InvoiceDetailItemExtend invoiceDetailItem : noZeroInvoiceDetailItems) {
                    BigDecimal itemUnitPrice = invoiceDetailItem.getUnitPrice().multiply(rate).setScale(2, RoundingMode.DOWN);
                    invoiceDetailItem.setUnitPrice(itemUnitPrice);
                    leftItemUnitPrice = MathUtils.wrapBigDecimalSubtract(leftItemUnitPrice, itemUnitPrice);
                }
                //leftItemUnitPrice 还有值，说明有零头，再把零头从第一个摊下去
                if (leftItemUnitPrice.compareTo(BigDecimal.ZERO) != 0) {
                    InvoiceDetailItemExtend firstItem = noZeroInvoiceDetailItems.get(0);
                    firstItem.setUnitPrice(MathUtils.wrapBigDecimalAdd(firstItem.getUnitPrice(), leftItemUnitPrice));
                }
            }
        } else {
            BigDecimal size = new BigDecimal(invoiceDetailItems.size());
            //均摊
            BigDecimal averagePrice = combineUnitPrice.divide(size, 4, RoundingMode.DOWN);
            BigDecimal oddPrice = combineUnitPrice.subtract(averagePrice.multiply(size));
            invoiceDetailItems.forEach(invoiceDetailItemExtend -> invoiceDetailItemExtend.setUnitPrice(averagePrice));
            if (MathUtils.wrapBigDecimalCompare(oddPrice, BigDecimal.ZERO) != 0) {
                invoiceDetailItems.get(0).setUnitPrice(MathUtils.wrapBigDecimalAdd(averagePrice, oddPrice));
            }
        }
        return invoiceDetailItems;
    }

    public static List<InvoiceDetailItemExtend> generateInvoiceDetailItemExtends(List<GoodsItem> combineGoodsItemChildren, BigDecimal totalCount, Map<String, GoodsSystemType> goodsTypeKeyToGoodsTypeMap) {
        return Optional.ofNullable(combineGoodsItemChildren).orElse(new ArrayList<>()).stream()
                .map(goodsItem -> {
                    CreateInvoiceReq.InvoiceDetailItem invoiceDetailItem = generateInvoiceDetailItem(goodsItem.getName(),
                            MathUtils.wrapBigDecimalOrZero(goodsItem.getComposePackagePrice()),
                            MathUtils.wrapBigDecimalMultiply(goodsItem.getComposePackageCount(), MathUtils.wrapBigDecimalOrZero(totalCount)),
                            Optional.ofNullable(goodsItem.getPackageUnit()).orElse("次"),
                            goodsItem.getType(),
                            goodsItem.getSubType(),
                            goodsItem.getCMSpec(),
                            ChargeUtils.getFeeTypeIdOrDefaultByGoodsTypeId(goodsItem.getType(), goodsItem.getSubType(), goodsItem.getCMSpec(), goodsItem.getTypeId(), goodsItem.getFeeTypeId(), goodsTypeKeyToGoodsTypeMap)
                    );
                    InvoiceDetailItemExtend invoiceDetailItemExtend = new InvoiceDetailItemExtend();
                    BeanUtils.copyProperties(invoiceDetailItem, invoiceDetailItemExtend);
                    invoiceDetailItemExtend.setGoodsItem(goodsItem);
                    return invoiceDetailItemExtend;
                }).collect(Collectors.toList());
    }

    public static List<InvoiceDetailItemExtend> generateRefundInvoiceDetailItemExtendsAndFlatPrice(BigDecimal totalCount, List<GoodsItem> combineGoodsItemChildren, BigDecimal combineUnitPrice, Map<String, GoodsSystemType> goodsTypeKeyToGoodsTypeMap) {
        //将unitPrice取反，退款按负值
        List<InvoiceDetailItemExtend> invoiceDetailItems = generateInvoiceDetailItemExtends(combineGoodsItemChildren, totalCount, goodsTypeKeyToGoodsTypeMap).stream()
                .peek(invoiceDetailItem -> invoiceDetailItem.setUnitPrice(invoiceDetailItem.getUnitPrice().negate()))
                .collect(Collectors.toList());

        //判断是否所有子项的金额都为0
        boolean isNotAllZero = invoiceDetailItems.stream().anyMatch(invoiceDetailItem -> MathUtils.wrapBigDecimalCompare(invoiceDetailItem.getUnitPrice(), BigDecimal.ZERO) < 0);

        if (isNotAllZero) {
            List<InvoiceDetailItemExtend> noZeroInvoiceDetailItems = invoiceDetailItems.stream()
                    .filter(invoiceDetailItem -> MathUtils.wrapBigDecimalCompare(invoiceDetailItem.getUnitPrice(), BigDecimal.ZERO) < 0)
                    .collect(Collectors.toList());

            BigDecimal totalPrice = noZeroInvoiceDetailItems.stream().map(InvoiceDetailItemExtend::getUnitPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal leftItemRefundUnitPrice = combineUnitPrice;
            for (InvoiceDetailItemExtend invoiceDetailItem : noZeroInvoiceDetailItems) {
                BigDecimal rate = invoiceDetailItem.getUnitPrice().divide(totalPrice, 4, RoundingMode.DOWN);
                BigDecimal itemRefundUnitPrice = combineUnitPrice.multiply(rate).setScale(2, RoundingMode.DOWN);
                invoiceDetailItem.setUnitPrice(itemRefundUnitPrice);
                leftItemRefundUnitPrice = MathUtils.wrapBigDecimalSubtract(leftItemRefundUnitPrice, itemRefundUnitPrice);
            }

            //leftItemUnitPrice 还有值，说明有零头，再把零头从第一个摊下去
            if (leftItemRefundUnitPrice.compareTo(BigDecimal.ZERO) != 0) {
                InvoiceDetailItemExtend firstItem = noZeroInvoiceDetailItems.get(0);
                firstItem.setUnitPrice(MathUtils.wrapBigDecimalAdd(firstItem.getUnitPrice(), leftItemRefundUnitPrice));
            }
        } else {
            BigDecimal size = new BigDecimal(invoiceDetailItems.size());
            //均摊
            BigDecimal averagePrice = combineUnitPrice.divide(size, 4, RoundingMode.DOWN);
            BigDecimal oddPrice = combineUnitPrice.subtract(averagePrice.multiply(size));
            invoiceDetailItems.forEach(invoiceDetailItemExtend -> invoiceDetailItemExtend.setUnitPrice(averagePrice));
            if (MathUtils.wrapBigDecimalCompare(oddPrice, BigDecimal.ZERO) != 0) {
                invoiceDetailItems.get(0).setUnitPrice(MathUtils.wrapBigDecimalAdd(averagePrice, oddPrice));
            }
        }
        return invoiceDetailItems;
    }

    private static CreateInvoiceReq.InvoiceDetailItem generateInvoiceDetailItem(String name, BigDecimal unitPrice, BigDecimal num, String unit, int goodsType, int goodsSubType, String goodsCmSpec, Long feeTypeId) {
        CreateInvoiceReq.InvoiceDetailItem invoiceDetailItem = new CreateInvoiceReq.InvoiceDetailItem();
        invoiceDetailItem.setGoodsName(name);
        invoiceDetailItem.setUnitPrice(unitPrice);
        invoiceDetailItem.setNum(num);
        invoiceDetailItem.setUnit(unit);
        invoiceDetailItem.setGoodsType(goodsType);
        invoiceDetailItem.setGoodsSubType(goodsSubType);
        invoiceDetailItem.setGoodsCmSpec(goodsCmSpec);
        invoiceDetailItem.setFeeTypeId(feeTypeId);
        return invoiceDetailItem;
    }

    public String generateRemark(int hisType, String caseNumber, String outpatientDate, String medicalInstitution, BigDecimal realReceivedFee, QueryChargeSheetShebaoInfoRsp shebaoInfoRsp, List<ChargeTransactionView> transactionViewList) {
        QueryChargeSheetShebaoInfoRsp.ShebaoPayment shebaoPayment = Optional.ofNullable(shebaoInfoRsp)
                .map(QueryChargeSheetShebaoInfoRsp::getShebaoPayment)
                .orElse(null);
        return super.generateRemark(hisType, caseNumber, outpatientDate, medicalInstitution, realReceivedFee, shebaoPayment, transactionViewList, ";");
    }

}
