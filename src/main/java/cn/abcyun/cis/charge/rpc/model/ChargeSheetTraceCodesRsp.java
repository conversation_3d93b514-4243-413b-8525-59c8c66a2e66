package cn.abcyun.cis.charge.rpc.model;

import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.commons.util.MathUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Objects;

/**
 * 收费单追溯码信息 响应
 */
@Data
@Accessors(chain = true)
public class ChargeSheetTraceCodesRsp {
    @ApiModelProperty("收费单id")
    private String id;
    @ApiModelProperty("状态 cn.abcyun.bis.rpc.sdk.cis.model.charge.ChargeConstants.ChargeSheetStatus")
    private int status;
    @ApiModelProperty("chargeForm集合")
    private List<ChargeFormTraceCodesRsp> chargeForms;
    @ApiModelProperty("已收")
    private BigDecimal receivedFee;
    @ApiModelProperty("已退")
    private BigDecimal refundFee;
    @ApiModelProperty("净收入")
    private BigDecimal netIncomeFee;
    @ApiModelProperty("第一次收费时间")
    private Instant firstChargedTime;
    @ApiModelProperty("收费完成时间")
    private Instant chargedTime;

    public static ChargeSheetTraceCodesRsp fromChargeSheet(ChargeSheet chargeSheet) {
        if (Objects.isNull(chargeSheet)){
            return null;
        }
        return new ChargeSheetTraceCodesRsp()
                .setId(chargeSheet.getId())
                .setStatus(chargeSheet.getStatus())
                .setReceivedFee(chargeSheet.getReceivedFee())
                .setRefundFee(chargeSheet.getRefundFee())
                .setNetIncomeFee(MathUtils.wrapBigDecimalSubtract(chargeSheet.getReceivedFee(), chargeSheet.getRefundFee()))
                .setFirstChargedTime(chargeSheet.getFirstChargedTime())
                .setChargedTime(chargeSheet.getChargedTime());
    }
}
