package cn.abcyun.cis.charge.rpc.client;

import cn.abcyun.bis.rpc.sdk.config.CisFeignForwardHeaderConfiguration;
import cn.abcyun.cis.commons.CisServiceResponseBody;
import cn.abcyun.cis.commons.rpc.registration.RegistrationSheet;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

@FeignClient(name = "abc-cis-registration-service", url = "http://abc-cis-registration-service", configuration = CisFeignForwardHeaderConfiguration.class)
public interface RegistrationClient {
    @GetMapping(value = "/rpc/registrations/patientorders/{patientOrderId}", produces = MediaType.APPLICATION_JSON_VALUE)
    CisServiceResponseBody<RegistrationSheet> findRegistrationSheetByPatientOrder(@PathVariable("patientOrderId") String patientOrderId);
}
