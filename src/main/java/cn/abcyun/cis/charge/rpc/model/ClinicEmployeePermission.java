package cn.abcyun.cis.charge.rpc.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 诊所成员的权限
 *
 * <AUTHOR>
 * @version ClinicEmployeePermission.java, 2020/11/26 下午3:32
 */
@Data
@Accessors(chain = true)
public class ClinicEmployeePermission {
    /**
     * 模块id集合
     */
    private List<Integer> moduleIds;
    /**
     * 角色集合
     */
    private List<Integer> roles;
    /**
     * 是否管理员
     */
    private int           isAdmin;
}
