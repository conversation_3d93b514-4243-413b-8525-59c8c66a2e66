package cn.abcyun.cis.charge.rpc.model;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsItem;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.TraceableCode;
import cn.abcyun.cis.charge.model.ChargeFormItem;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Data
@Accessors(chain = true)
public class ChargeFormItemTraceCodesRsp {
    @ApiModelProperty("charge form item id")
    private String id;
    @ApiModelProperty("状态 cn.abcyun.bis.rpc.sdk.cis.model.charge.ChargeConstants.ChargeFormItemStatus")
    private int status;
    @ApiModelProperty("单位")
    private String unit;
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("单位数量")
    private BigDecimal unitCount;
    @ApiModelProperty("剂数")
    private BigDecimal doseCount;
    @ApiModelProperty("product Type")
    private int productType;
    @ApiModelProperty("product subType")
    private int productSubType;
    @ApiModelProperty("商品信息")
    private GoodsItem productInfo;
    @ApiModelProperty("追溯码集合")
    private List<TraceableCode> traceableCodeList;

    public static ChargeFormItemTraceCodesRsp fromChargeFormItemAndAdditional(ChargeFormItem chargeFormItem,
                                                                              List<TraceableCode> traceableCodeList) {
        if (Objects.isNull(chargeFormItem)) {
            return null;
        }
        return new ChargeFormItemTraceCodesRsp()
                .setId(chargeFormItem.getId())
                .setStatus(chargeFormItem.getStatus())
                .setUnit(chargeFormItem.getUnit())
                .setName(chargeFormItem.getName())
                .setUnitCount(chargeFormItem.getUnitCount())
                .setDoseCount(chargeFormItem.getDoseCount())
                .setProductType(chargeFormItem.getProductType())
                .setProductSubType(chargeFormItem.getProductSubType())
                .setTraceableCodeList(traceableCodeList);
    }
}
