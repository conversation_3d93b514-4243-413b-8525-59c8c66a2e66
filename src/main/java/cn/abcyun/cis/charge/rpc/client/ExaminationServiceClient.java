package cn.abcyun.cis.charge.rpc.client;

import cn.abcyun.bis.rpc.sdk.config.CisFeignForwardHeaderConfiguration;
import cn.abcyun.cis.commons.CisServiceResponseBody;
import cn.abcyun.cis.commons.rpc.examination.QueryExaminationStatusRsp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "old-abc-cis-examination-service", url = "http://abc-cis-examination-service", configuration = CisFeignForwardHeaderConfiguration.class)
public interface ExaminationServiceClient {
    @GetMapping(value = "/rpc/examinations/examinationSheetsStatus", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    CisServiceResponseBody<QueryExaminationStatusRsp> queryExaminationStatus(@RequestParam("clinicId") String clinicId, @RequestParam("patientOrderId") String patientOrderId);
}
