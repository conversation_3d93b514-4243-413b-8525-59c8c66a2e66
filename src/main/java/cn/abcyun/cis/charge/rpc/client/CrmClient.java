package cn.abcyun.cis.charge.rpc.client;

import cn.abcyun.bis.rpc.sdk.config.CisFeignForwardHeaderConfiguration;
import cn.abcyun.cis.commons.CisServiceResponseBody;
import cn.abcyun.cis.commons.rpc.crm.CisPatientDeliveryInfo;
import cn.abcyun.cis.commons.rpc.crm.GetPatientBasicInfosReq;
import cn.abcyun.cis.commons.rpc.crm.PatientDeliveryInfoView;
import cn.abcyun.cis.commons.rpc.crm.PatientInfo;
import cn.abcyun.cis.commons.rpc.crm.PatientMemberRechargeReq;
import cn.abcyun.cis.commons.rpc.crm.PatientMemberRechargeRsp;
import cn.abcyun.cis.commons.rpc.patient.MemberCardPayReq;
import cn.abcyun.cis.commons.rpc.patient.MemberCardPayRsp;
import cn.abcyun.cis.commons.rpc.patient.PatientInfoListRsp;
import cn.abcyun.common.model.AbcServiceResponseBody;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "old-abc-cis-crm-service", url = "http://abc-cis-crm-service", configuration = CisFeignForwardHeaderConfiguration.class)
public interface CrmClient {

    @GetMapping(value = "/rpc/crm/patients/basic/{patientId}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    AbcServiceResponseBody<PatientInfo> findPatientInfoByPatientId(@PathVariable("patientId") String patientId, @RequestParam("needMember") boolean needMember, @RequestParam("needPointsInfo") boolean needPointsInfo,
                                                                   @RequestParam(value = "wx") boolean needWxInfo);


    @PutMapping(value = "/rpc/crm/patients/member/balance/order/{chargeSheetId}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    AbcServiceResponseBody<PatientMemberRechargeRsp> postCrmMemberRechargePaySuccess(@PathVariable("chargeSheetId") String chargeSheetId, PatientMemberRechargeReq patientMemberRechargeReq);

    @GetMapping(value = "/rpc/crm/patients/deliveryInfo", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    CisServiceResponseBody<PatientDeliveryInfoView> getPatientDeliveryInfoList(@RequestParam("patientIds") List<String> patientIds, @RequestParam("chainId") String chainId);

    @PostMapping(value = "/rpc/crm/patients/basic", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    AbcServiceResponseBody<PatientInfoListRsp> getPatientInfoList(@RequestBody GetPatientBasicInfosReq getPatientBasicInfosReq);

    @GetMapping(value = "/rpc/crm/patients/{patientId}/deliveryInfo/{deliveryInfoId}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    CisServiceResponseBody<CisPatientDeliveryInfo> getDeliveryInfoByPatientIdAndDeliveryInfoId(@PathVariable("patientId") String patientId, @PathVariable("deliveryInfoId") String deliveryInfoId, @RequestParam("chainId") String chainId);


    @PostMapping(value = "/rpc/crm/patients/{patientId}/deliveryInfo", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    CisServiceResponseBody<CisPatientDeliveryInfo> createDeliveryInfo(@PathVariable("patientId") String patientId, @RequestBody CisPatientDeliveryInfo deliveryInfo);

    @PostMapping(value = "/rpc/crm/patients/member/balance/pay", produces = MediaType.APPLICATION_JSON_VALUE)
    CisServiceResponseBody<MemberCardPayRsp> memberCardPay(MemberCardPayReq memberCardPayReq);

    //通过手机号查询患者信息
    @GetMapping(value = "/rpc/crm/patients/batch/basic/query", produces = MediaType.APPLICATION_JSON_VALUE)
    CisServiceResponseBody<PatientInfoListRsp> searchPatientInfoByMobile(@RequestParam("chainId") String chainId,@RequestParam("mobile") String mobile);



}
