package cn.abcyun.cis.charge.facade;

import cn.abcyun.cis.charge.api.model.PayCallbackContainPayModeReq;
import cn.abcyun.cis.charge.combinorder.dto.CombineOrderBusinessPayCallbackReq;
import cn.abcyun.cis.charge.combinorder.dto.CombineOrderBusinessPayCallbackRsp;
import cn.abcyun.cis.charge.model.ChargePayTransaction;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.repository.ChargePayTransactionRepository;
import cn.abcyun.cis.charge.service.ChargeOweSheetService;
import cn.abcyun.cis.charge.service.ChargeService;
import cn.abcyun.cis.charge.service.ChargeSheetService;
import cn.abcyun.cis.charge.util.FillUtils;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.rpc.pay.PayStatus;
import cn.abcyun.cis.commons.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ChargeOweSheetFacade {

    private final ChargeOweSheetService chargeOweSheetService;
    private final ChargeSheetService chargeSheetService;
    private final ChargeService chargeService;
    private final ChargePayTransactionRepository chargePayTransactionRepository;

    @Autowired
    public ChargeOweSheetFacade(ChargeOweSheetService chargeOweSheetService,
                                ChargeSheetService chargeSheetService,
                                ChargeService chargeService,
                                ChargePayTransactionRepository chargePayTransactionRepository) {

        this.chargeOweSheetService = chargeOweSheetService;
        this.chargeSheetService = chargeSheetService;
        this.chargeService = chargeService;
        this.chargePayTransactionRepository = chargePayTransactionRepository;
    }

    /**
     * 第三方支付回调处理
     */
    public CombineOrderBusinessPayCallbackRsp payCallback(CombineOrderBusinessPayCallbackReq req) {
        log.info("payCallback req: {}", JsonUtils.dump(req));
        req.checkParam();
        chargeOweSheetService.oweSheetPayCallbackCore(req);
        CombineOrderBusinessPayCallbackRsp rsp = new CombineOrderBusinessPayCallbackRsp();
        rsp.setCode(CombineOrderBusinessPayCallbackRsp.Code.SUCCESS);
        return rsp;
    }

    /**
     * 第三方退款回调处理
     */
    public CombineOrderBusinessPayCallbackRsp refundCallback(CombineOrderBusinessPayCallbackReq req) {
        log.info("refundCallback req: {}", JsonUtils.dump(req));
        req.checkParam();

        CombineOrderBusinessPayCallbackRsp rsp = chargeOweSheetService.oweSheetRefundCallBackCore(req);

        if (rsp.getCode() == CombineOrderBusinessPayCallbackRsp.Code.FAIL) {
            return rsp;
        }

        //修改chargePayTransaction的payStatus
        rsp = chargeSheetRefundCallback(req);

        if (rsp.getCode() == CombineOrderBusinessPayCallbackRsp.Code.FAIL) {
            return rsp;
        }

        rsp.setCode(CombineOrderBusinessPayCallbackRsp.Code.SUCCESS);
        return rsp;
    }

    private CombineOrderBusinessPayCallbackRsp chargeSheetRefundCallback(CombineOrderBusinessPayCallbackReq req) {
        CombineOrderBusinessPayCallbackRsp rsp = new CombineOrderBusinessPayCallbackRsp();
        if (StringUtils.isEmpty(req.getBusinessPayTransactionId())) {
            return rsp;
        }

        ChargePayTransaction chargePayTransaction = chargePayTransactionRepository.findByIdAndClinicIdAndIsDeleted(req.getBusinessPayTransactionId(), req.getClinicId(), 0);

        if (chargePayTransaction == null) {
            log.info("chargePayTransaction is null");
            throw new NotFoundException();
        }

        if (chargePayTransaction.getPayStatus() == PayStatus.SUCCESS) {
            return rsp;
        }

        if (chargePayTransaction.getPayStatus() != PayStatus.WAITING) {
            log.info("chargePayTransaction status error, status: {}", chargePayTransaction.getPayStatus());
            return rsp.setCode(CombineOrderBusinessPayCallbackRsp.Code.FAIL)
                    .setMessage("退款单状态错误");
        }

        ChargeSheet chargeSheet = chargeSheetService.findById(chargePayTransaction.getChargeSheetId());
        PayCallbackContainPayModeReq payCallbackReq = new PayCallbackContainPayModeReq();
        payCallbackReq.setPayMode(req.getPayMode());
        payCallbackReq.setOperatorId(req.getOperatorId());
        payCallbackReq.setReceivedFee(req.getCombineOrderTransaction().getAmount());

        chargeService.refundCallback(chargeSheet, payCallbackReq, chargePayTransaction);

        chargePayTransaction.setPayStatus(PayStatus.SUCCESS);
        FillUtils.fillLastModifiedBy(chargePayTransaction, req.getOperatorId());
        chargePayTransactionRepository.save(chargePayTransaction);

        return rsp;
    }

}
