package cn.abcyun.cis.charge.facade;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsItem;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsSheBaoMatchedCodesInfo;
import cn.abcyun.bis.rpc.sdk.cis.model.shebao.RpcGetShebaoMatchedCodesReq;
import cn.abcyun.cis.charge.api.model.RpcCalculateShebaoLimitPriceReq;
import cn.abcyun.cis.charge.api.model.RpcCalculateShebaoLimitPriceRsp;
import cn.abcyun.cis.charge.model.ChargeMedicareLimitPriceProduct;
import cn.abcyun.cis.charge.model.ChargeMedicareLimitPriceType;
import cn.abcyun.cis.charge.processor.limitprice.CalculateLimitPriceItem;
import cn.abcyun.cis.charge.processor.limitprice.LimitPriceProcessor;
import cn.abcyun.cis.charge.service.ChargeMedicareLimitPriceService;
import cn.abcyun.cis.charge.service.ShebaoService;
import cn.abcyun.cis.charge.util.JsonUtils;
import cn.abcyun.cis.charge.util.ListUtils;
import cn.abcyun.common.log.marker.AbcLogMarker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ChargeMedicareLimitPriceFacade {

    private final ChargeMedicareLimitPriceService chargeMedicareLimitPriceService;
    private final ShebaoService shebaoService;

    @Autowired
    public ChargeMedicareLimitPriceFacade(ChargeMedicareLimitPriceService chargeMedicareLimitPriceService,
                                          ShebaoService shebaoService) {
        this.chargeMedicareLimitPriceService = chargeMedicareLimitPriceService;
        this.shebaoService = shebaoService;
    }


    @Transactional(readOnly = true)
    public RpcCalculateShebaoLimitPriceRsp calculateShebaoLimitPrice(RpcCalculateShebaoLimitPriceReq req) {
        String chainId = req.getChainId();
        String clinicId = req.getClinicId();
        RpcCalculateShebaoLimitPriceRsp rsp = new RpcCalculateShebaoLimitPriceRsp();
        rsp.setChainId(chainId)
                .setClinicId(clinicId)
                .setItems(new ArrayList<>());
        if (CollectionUtils.isEmpty(req.getItems())) {
            return rsp;
        }

        //构造需要计算限价的item
        List<CalculateLimitPriceItem> calculateLimitPriceItems = req.getItems()
                .stream()
                .map(this::convertToCalculateLimitPriceItem)
                .collect(Collectors.toList());
        calculate(calculateLimitPriceItems);

        return rsp.setItems(calculateLimitPriceItems);
    }

    private CalculateLimitPriceItem convertToCalculateLimitPriceItem(RpcCalculateShebaoLimitPriceReq.CalculateShebaoLimitPriceItemReq itemReq) {

        if (itemReq == null) {
            return null;
        }

        CalculateLimitPriceItem calculateLimitPriceItem = new CalculateLimitPriceItem();
        BeanUtils.copyProperties(itemReq, calculateLimitPriceItem, "composeChildren", "batchInfos");

        if (CollectionUtils.isNotEmpty(itemReq.getComposeChildren())) {
            calculateLimitPriceItem.setComposeChildren(itemReq.getComposeChildren()
                    .stream()
                    .map(this::convertToCalculateLimitPriceItem)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList())
            );
        }

        if (CollectionUtils.isNotEmpty(itemReq.getBatchInfos())) {
            calculateLimitPriceItem.setBatchInfos(itemReq.getBatchInfos()
                    .stream()
                    .map(this::convertToCalculateLimitPriceBatchInfo)
                    .collect(Collectors.toList())
            );
        }

        return calculateLimitPriceItem;
    }

    private CalculateLimitPriceItem.CalculateLimitPriceItemBatchInfo convertToCalculateLimitPriceBatchInfo(RpcCalculateShebaoLimitPriceReq.CalculateShebaoLimitPriceItemBatchInfoReq batchInfoReq) {

        if (Objects.isNull(batchInfoReq)) {
            return null;
        }

        CalculateLimitPriceItem.CalculateLimitPriceItemBatchInfo batchInfo = new CalculateLimitPriceItem.CalculateLimitPriceItemBatchInfo();
        BeanUtils.copyProperties(batchInfoReq, batchInfo);

        return batchInfo;
    }

    private void calculate(List<CalculateLimitPriceItem> calculateLimitPriceItems) {

        if (CollectionUtils.isEmpty(calculateLimitPriceItems)) {
            return;
        }

        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "before limit calculateLimitPriceItems: {}", JsonUtils.dump(calculateLimitPriceItems));

        //匹配限价
        LimitPriceProcessor limitPriceProcessor = new LimitPriceProcessor();
        limitPriceProcessor.process(calculateLimitPriceItems);

        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "after limit calculateLimitPriceItems: {}", JsonUtils.dump(calculateLimitPriceItems));

    }

    private List<RpcGetShebaoMatchedCodesReq.GetSmartMatchedReqItem> convertShebaoSmartMatchedReqItems(Collection<GoodsItem> goodsItems) {

        return goodsItems
                .stream()
                .map(goodsItem -> {
                    RpcGetShebaoMatchedCodesReq.GetSmartMatchedReqItem smartMatchedReqItem = new RpcGetShebaoMatchedCodesReq.GetSmartMatchedReqItem();
                    smartMatchedReqItem.setGoodsId(goodsItem.getId());
                    smartMatchedReqItem.setGoodsType(goodsItem.getType());
                    smartMatchedReqItem.setGoodsTypeId(goodsItem.getTypeId());
                    smartMatchedReqItem.setName(goodsItem.getName());
                    smartMatchedReqItem.setDisplayName(goodsItem.getDisplayName());
                    smartMatchedReqItem.setSubType(goodsItem.getSubType());
                    smartMatchedReqItem.setMedicineCadn(goodsItem.getMedicineCadn());
                    smartMatchedReqItem.setMedicineNmpn(goodsItem.getMedicineNmpn());
                    smartMatchedReqItem.setMedicineDosageNum(goodsItem.getMedicineDosageNum());
                    smartMatchedReqItem.setMedicineDosageUnit(goodsItem.getMedicineDosageUnit());
                    smartMatchedReqItem.setMedicineDosageForm(goodsItem.getMedicineDosageForm());
                    smartMatchedReqItem.setPieceNum(goodsItem.getPieceNum());
                    smartMatchedReqItem.setPieceUnit(goodsItem.getPieceUnit());
                    smartMatchedReqItem.setPackageUnit(goodsItem.getPackageUnit());
                    smartMatchedReqItem.setCMSpec(goodsItem.getCMSpec());
                    smartMatchedReqItem.setStandardName(goodsItem.getStandardName());
                    smartMatchedReqItem.setManufacturer(goodsItem.getManufacturer());
                    smartMatchedReqItem.setManufacturerFull(goodsItem.getManufacturerFull());
                    smartMatchedReqItem.setExtendSpec(goodsItem.getExtendSpec());
                    return smartMatchedReqItem;
                }).collect(Collectors.toList());

    }

}
