package cn.abcyun.cis.charge.facade;

import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ;
import cn.abcyun.bis.rpc.sdk.cis.model.copharmacy.CoPharmacyOutpatientSheet;
import cn.abcyun.bis.rpc.sdk.cis.model.outpatient.OutpatientSheetShebaoSimpleInfo;
import cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientInfo;
import cn.abcyun.cis.charge.amqp.model.ChargeCoPharmacyOrderMessage;
import cn.abcyun.cis.charge.amqp.model.MessageConvertor;
import cn.abcyun.cis.charge.api.model.BasicCreateChargeSheetRsp;
import cn.abcyun.cis.charge.api.model.OutpatientSheetCalculateRsp;
import cn.abcyun.cis.charge.base.*;
import cn.abcyun.cis.charge.controller.StatusNameTranslator;
import cn.abcyun.cis.charge.factory.ChargeSheetFactory;
import cn.abcyun.cis.charge.model.ChargeCooperationOrder;
import cn.abcyun.cis.charge.model.ChargeFormItem;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.processor.SheetProcessorDispatcher;
import cn.abcyun.cis.charge.service.*;
import cn.abcyun.cis.charge.service.dto.BusinessSheetCalculateChargeSheetDto;
import cn.abcyun.cis.charge.service.rpc.CisCoPharmacyService;
import cn.abcyun.cis.charge.service.rpc.CisCrmService;
import cn.abcyun.cis.charge.service.rpc.CisGoodsLockingService;
import cn.abcyun.cis.charge.service.rpc.CisScClinicService;
import cn.abcyun.cis.charge.util.*;
import cn.abcyun.cis.commons.amqp.message.ChargeSheetMessage;
import cn.abcyun.cis.commons.amqp.message.PatientOrderMessage;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrder;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrderReq;
import cn.abcyun.cis.core.redis.RedisLock;
import cn.abcyun.common.log.marker.AbcLogMarker;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ChargeCooperationOrderFacade {

    private final ChargeCooperationOrderService chargeCooperationOrderService;
    private final PatientOrderService patientOrderService;
    private final CisCrmService cisCrmService;
    private final ChargeSheetService chargeSheetService;
    private final ChargeService chargeService;
    private final SheetProcessorService sheetProcessorService;
    private final CisScClinicService scClinicService;
    private final ChargeCalculateService chargeCalculateService;
    private final CisGoodsLockingService goodsLockingService;
    private final TobMessageService tobMessageService;
    private final CisCoPharmacyService coPharmacyService;

    /**
     * 提单
     *
     * @param id
     * @param clinicId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @RedisLock(key = "'charge.cooperationOrder.extract:' + #id", waitTime = 5)
    public BasicCreateChargeSheetRsp extractAndCreateChargeSheetById(String id, String clinicId, int hisType, String operatorId) {

        ChargeCooperationOrder chargeCooperationOrder = chargeCooperationOrderService.findById(id, clinicId);

        if (Objects.isNull(chargeCooperationOrder)) {
            log.error("chargeCooperationOrder is null, id: {}, clinicId: {}", id, clinicId);
            throw new ChargeServiceException(ChargeServiceError.COOPERATION_ORDER_IS_DELETED);
        }

        if (!ChargeCooperationOrder.Status.unchargedStatuses().contains(chargeCooperationOrder.getStatus())) {
            throw new ChargeServiceException(ChargeServiceError.COOPERATION_ORDER_STATUS_ERROR);
        }

        return unchargedCooperationOrderExtra(chargeCooperationOrder, clinicId, hisType, operatorId);

    }

    private BasicCreateChargeSheetRsp unchargedCooperationOrderExtra(ChargeCooperationOrder chargeCooperationOrder, String clinicId, int hisType, String operatorId) {

        Pair<Boolean, ChargeSheet> chargeSheetPair = findOrCreateChargeSheet(chargeCooperationOrder.getRelateChargeSheetId(),
                chargeCooperationOrder.getChainId(),
                clinicId,
                chargeCooperationOrder.getSourcePatientInfo(),
                hisType,
                operatorId);

        return extractOrderCore(chargeCooperationOrder, chargeSheetPair, hisType, operatorId);
    }


    /**
     * 提单核心方法
     *
     * @param chargeCooperationOrder
     * @param chargeSheetPair<isUpdate, ChargeSheet> <是否为更新收费单, 收费单对象>
     * @param hisType
     * @param operatorId
     * @return
     */
    private BasicCreateChargeSheetRsp extractOrderCore(ChargeCooperationOrder chargeCooperationOrder, Pair<Boolean, ChargeSheet> chargeSheetPair, int hisType, String operatorId) {

        boolean isUpdate = chargeSheetPair.getLeft();
        ChargeSheet chargeSheet = chargeSheetPair.getRight();

        if (chargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED) {
            log.error("收费单已收费，不能进行提单, chargeSheetId: {}", chargeSheet.getId());
            throw new ChargeServiceException(ChargeServiceError.CHARGE_SHEET_CHARGED);
        }

        ChargeSheetFactory.insertOrUpdateChargeFormsForChargeCooperationOrder(chargeSheet, chargeCooperationOrder, hisType, operatorId);

        int msgType;
        if (!isUpdate) {
            if (CollectionUtils.isEmpty(chargeSheet.getChargeForms())) {
                throw new ChargeServiceException(ChargeServiceError.COOPERATION_ORDER_EXTRACT_SHEET_FORMS_IS_EMPTY);
            }

            msgType = ChargeSheetMessage.MSG_TYPE_CREATED;
        } else {
            FillUtils.fillLastModifiedBy(chargeSheet, operatorId);
            msgType = ChargeSheetMessage.MSG_TYPE_UPDATE;
        }
        chargeService.notifyChargeSheetStatusChanged(chargeSheet, operatorId, PatientOrderMessage.MSG_TYPE_CHARGE_CREATED);

        chargeService.doAfterChargeSheetChange(chargeSheet, Lists.newArrayList(), Lists.newArrayList(), Maps.newHashMap(), msgType, null, null, operatorId);
        tobMessageService.pushChargeTodoMessage(chargeSheet.getChainId(), chargeSheet.getClinicId(), chargeSheet.getType());

        SheetProcessorDispatcher.ISheetProcessor sheetProcessor = SheetProcessorDispatcher.newSheetProcessor(chargeSheet);
        sheetProcessor.setSheetProcessorInfoProvider(sheetProcessorService);
        sheetProcessor.setOperatorId(operatorId);
        sheetProcessor.build();

        sheetProcessor.calculateSheetFee(null, null, new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), null, Constants.ChargeSource.BACKGROUND, false, false);
        chargeSheet = sheetProcessor.generateToSaveChargeSheet();


        List<String> appointChargeFormItemIds = sheetProcessor.getStockAvailableItems()
                .stream()
                .map(ChargeFormItem::getId)
                .collect(Collectors.toList());

        goodsLockingService.goodsLockAndSaveBatchInfo(chargeSheet,
                chargeSheet.getIsDeleted() == 1 ? GoodsLockScene.RELEASE_LOCK : GoodsLockScene.ONLY_LOCK_BILLING,
                true,
                appointChargeFormItemIds,
                false,
                operatorId);
        chargeSheetService.save(chargeSheet, !isUpdate ? null : sheetProcessor.getDeletedDataCollector());

        BasicCreateChargeSheetRsp rsp = new BasicCreateChargeSheetRsp();
        rsp.setId(chargeSheet.getId());
        rsp.setPatientOrderId(chargeSheet.getPatientOrderId());
        rsp.setStatus(chargeSheet.getStatus());
        rsp.setStatusName(StatusNameTranslator.translateChargeSheetStatus(0, chargeSheet.getStatus()));
        return rsp;
    }

    /**
     * 提单
     *
     * @param id
     * @param clinicId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @RedisLock(key = "'charge.cooperationOrder.extract:' + #id", waitTime = 5)
    public BasicCreateChargeSheetRsp reExtractAndCreateChargeSheetById(String id, String clinicId, int hisType, String operatorId) {

        ChargeCooperationOrder chargeCooperationOrder = chargeCooperationOrderService.findById(id, clinicId);

        if (Objects.isNull(chargeCooperationOrder)) {
            log.error("chargeCooperationOrder is null, id: {}, clinicId: {}", id, clinicId);
            throw new ChargeServiceException(ChargeServiceError.COOPERATION_ORDER_IS_DELETED);
        }

        if (chargeCooperationOrder.getStatus() != ChargeCooperationOrder.Status.REFUNDED && chargeCooperationOrder.getStatus() != ChargeCooperationOrder.Status.CLOSED) {
            log.error("chargeCooperationOrder status error. status: {}", chargeCooperationOrder.getStatus());
            throw new ChargeServiceException(ChargeServiceError.COOPERATION_ORDER_STATUS_ERROR);
        }

        //关闭重新提单
        if (chargeCooperationOrder.getStatus() == ChargeCooperationOrder.Status.CLOSED) {
            ChargeCooperationOrderUtils.reset(chargeCooperationOrder);
            return unchargedCooperationOrderExtra(chargeCooperationOrder, clinicId, hisType, operatorId);
        }

        ChargeCooperationOrderUtils.reset(chargeCooperationOrder);
        //退费重收重新提单
        ChargeSheet lastRefundedChargeSheet = chargeSheetService.findAllByPatientOrderId(chargeCooperationOrder.getRelatePatientOrderId(), ChargeSheet.Type.COOPERATION_ORDER)
                .stream()
                .filter(c -> c.getStatus() == Constants.ChargeSheetStatus.REFUNDED)
                .max(Comparator.comparing(ChargeSheet::getCreated))
                .orElseThrow(() -> {
                    log.error("findOrCreateChargeSheet 重新提单时未找到已退的收费单。");
                    return new ChargeServiceException(ChargeServiceError.COOPERATION_ORDER_STATUS_ERROR);
                });

        ChargeSheet chargeSheet = createChargeSheetForCooperationOrder(chargeCooperationOrder.getChainId(),
                clinicId,
                lastRefundedChargeSheet.getPatientOrderId(),
                lastRefundedChargeSheet.getPatientId(),
                hisType,
                operatorId);

        return extractOrderCore(chargeCooperationOrder, Pair.of(false, chargeSheet), hisType, operatorId);
    }


    /**
     * 查询或新增收费单
     *
     * @param relateChargeSheetId
     * @param chainId
     * @param clinicId
     * @param sourcePatientInfo
     * @param hisType
     * @param operatorId
     * @return Pair<Boolean, ChargeSheet> 参数为<是否为更新收费单， 收费单对象>
     */
    public Pair<Boolean, ChargeSheet> findOrCreateChargeSheet(String relateChargeSheetId,
                                                              String chainId,
                                                              String clinicId,
                                                              PatientInfo sourcePatientInfo,
                                                              int hisType,
                                                              String operatorId) {
        boolean isUpdate = true;
        ChargeSheet chargeSheet = null;

        if (StringUtils.isNotEmpty(relateChargeSheetId)) {
            chargeSheet = chargeSheetService.findByIdAndClinicId(relateChargeSheetId, clinicId);
        }

        if (Objects.nonNull(chargeSheet)) {
            return Pair.of(isUpdate, chargeSheet);
        }

        //查询患者是否存在，如果存在，用存在的患者信息生成patientOrder
        //这个是本门店的patient信息
        PatientInfo currentClinicPatientInfo = queryCurrentClinicPatientInfoFormSourcePatient(chainId, sourcePatientInfo);

        isUpdate = false;
        PatientOrder patientOrder = patientOrderService.createPatientOrder(PatientOrderReq.SourceClientType.PC_WEB,
                ChargeUtils.convertCisPatientInfoFromPatientInfo(currentClinicPatientInfo),
                null,
                clinicId,
                chainId,
                operatorId,
                PatientOrderReq.Source.MEMBER_CARD_RECHARGE,
                null,
                null,
                null);
        if (patientOrder == null) {
            log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "insertOrUpdatePatientOrder... 创建就诊单失败");
            throw new ChargeServiceException(ChargeServiceError.CREATE_PATIENT_ORDER_FAILED);
        }

        String patientId = Optional.ofNullable(currentClinicPatientInfo).map(PatientInfo::getId).orElse(Constants.ANONYMOUS_PATIENT_ID);
        chargeSheet = createChargeSheetForCooperationOrder(chainId, clinicId, patientOrder.getId(), patientId, hisType, operatorId);
        return Pair.of(isUpdate, chargeSheet);
    }

    private PatientInfo queryCurrentClinicPatientInfoFormSourcePatient(String chainId, PatientInfo sourcePatientInfo) {
        String patientName = null, patientMobile = null;
        if (Objects.nonNull(sourcePatientInfo)) {
            patientName = sourcePatientInfo.getName();
            patientMobile = sourcePatientInfo.getMobile();
        }

        //这个是本门店的patient信息
        return cisCrmService.queryPatientsByNameAndMobile(chainId, patientName, patientMobile);
    }

    /**
     * @param chainId
     * @param clinicId
     * @param patientOrderId
     * @param patientId
     * @param hisType
     * @param operatorId
     * @return
     */
    public ChargeSheet createChargeSheetForCooperationOrder(String chainId,
                                                            String clinicId,
                                                            String patientOrderId,
                                                            String patientId,
                                                            int hisType,
                                                            String operatorId) {
        ChargeSheet chargeSheet = new ChargeSheet();

        chargeSheet.setId(AbcIdUtils.getUUID());
        chargeSheet.setChainId(chainId);
        chargeSheet.setClinicId(clinicId);
        chargeSheet.setPatientOrderId(patientOrderId);
        chargeSheet.setPatientId(patientId);
        chargeSheet.setStatus(Constants.ChargeSheetStatus.UNCHARGED);
        chargeSheet.setType(ChargeSheet.Type.COOPERATION_ORDER);
        chargeSheet.setChargeForms(new ArrayList<>());
        chargeSheet.setTotalFee(BigDecimal.ZERO);
        chargeSheet.setDiscountFee(BigDecimal.ZERO);
        chargeSheet.setAdditionalFee(BigDecimal.ZERO);
        chargeSheet.setRefundFee(BigDecimal.ZERO);
        chargeSheet.setReceivableFee(BigDecimal.ZERO);
        chargeSheet.setReceivedFee(BigDecimal.ZERO);
        ChargeUtils.insertChargeSheetAdditionalIfNeed(chargeSheet, operatorId);
        chargeSheet.getAdditional().setChargeVersion(ChargeVersionConstants.convertChargeVersion(hisType));
        FillUtils.fillCreatedBy(chargeSheet, operatorId);
        return chargeSheet;
    }

    @Transactional(readOnly = true)
    public OutpatientSheetCalculateRsp calculateCoPharmacyOutpatientSheet(CoPharmacyOutpatientSheet req) {

        ChargeCoPharmacyOrderMessage chargeCoPharmacyOrderMessage = MessageConvertor.convertToChargeCoPharmacyOrderMessage(req);

        if (Objects.isNull(chargeCoPharmacyOrderMessage)) {
            return null;
        }

        List<ChargeCooperationOrder> allChargeCooperationOrders = chargeCooperationOrderService.queryOrGenerateChargeCooperationOrdersForCoPharmacyOutpatientSheet(chargeCoPharmacyOrderMessage);

        //查询已存在的chargeSheet
        List<String> chargeSheetIds = allChargeCooperationOrders.stream()
                .map(ChargeCooperationOrder::getRelateChargeSheetId)
                .filter(StringUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());

        Map<String, ChargeSheet> chargeSheetIdMap = Optional.ofNullable(chargeSheetService.findAllByIds(chargeSheetIds)).orElse(new ArrayList<>())
                .stream().collect(Collectors.toMap(ChargeSheet::getId, Function.identity(), (a, b) -> a));

        //查询患者信息
        PatientInfo currentClinicPatientInfo = queryCurrentClinicPatientInfoFormSourcePatient(chargeCoPharmacyOrderMessage.getChainId(), chargeCoPharmacyOrderMessage.getSourcePatientInfo());

        int hisType = Optional.ofNullable(scClinicService.getOrgan(chargeCoPharmacyOrderMessage.getClinicId())).map(Organ::getHisType).orElse(Organ.HisType.CIS_HIS_TYPE_PHARMACY);

        //将order转成chargeSheet
        List<ChargeSheet> availableChargeSheets = allChargeCooperationOrders.stream()
                .map(chargeCooperationOrder -> {
                    ChargeSheet relateChargeSheet = null;
                    if (StringUtils.isNotEmpty(chargeCooperationOrder.getRelateChargeSheetId())) {
                        relateChargeSheet = chargeSheetIdMap.get(chargeCooperationOrder.getRelateChargeSheetId());
                    }
                    return convertToChargeSheet(chargeCooperationOrder, relateChargeSheet, currentClinicPatientInfo, hisType, Constants.DEFAULT_OPERATORID);
                }).collect(Collectors.toList());

        BusinessSheetCalculateChargeSheetDto businessSheetCalculateChargeSheetDto = chargeCalculateService.businessSheetCalculateChargeSheet(availableChargeSheets,
                chargeCoPharmacyOrderMessage.getClinicId(),
                null,
                false,
                false);

        log.info(AbcLogMarker.MARKER_MESSAGE_NO_INDEX, "businessSheetCalculateChargeSheetDto: {}", JsonUtils.dump(businessSheetCalculateChargeSheetDto));

        //将businessSheetCalculateChargeSheetDto中

        OutpatientSheetCalculateRsp rsp = new OutpatientSheetCalculateRsp();

        rsp.setOutpatientForms(new ArrayList<>());

        // sheet status
        if (allChargeCooperationOrders.stream().anyMatch(chargeCooperationOrder -> chargeCooperationOrder.getStatus() == ChargeCooperationOrder.Status.UNCHARGED || chargeCooperationOrder.getStatus() == ChargeCooperationOrder.Status.PART_CHARGED)) {
            rsp.setStatus(Constants.ChargeSheetStatus.UNCHARGED);
        } else if (allChargeCooperationOrders.stream().anyMatch(chargeCooperationOrder -> chargeCooperationOrder.getStatus() == ChargeCooperationOrder.Status.CHARGED || chargeCooperationOrder.getStatus() == ChargeCooperationOrder.Status.PART_REFUNDED)) {
            rsp.setStatus(Constants.ChargeSheetStatus.CHARGED);
        } else {
            rsp.setStatus(Constants.ChargeSheetStatus.REFUNDED);
        }

        Map<String, BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeFormItem> cooperationOrderItemIdChargeFormItemMap = businessSheetCalculateChargeSheetDto.getChargeSheets()
                .stream()
                .filter(businessCalculateChargeSheet -> businessCalculateChargeSheet.getChargeForms() != null)
                .flatMap(businessCalculateChargeSheet -> businessCalculateChargeSheet.getChargeForms().stream())
                .filter(businessCalculateChargeForm -> businessCalculateChargeForm.getChargeFormItems() != null)
                .flatMap(businessCalculateChargeForm -> businessCalculateChargeForm.getChargeFormItems().stream())
                .filter(businessCalculateChargeFormItem -> StringUtils.isNotEmpty(businessCalculateChargeFormItem.getSourceFormItemId()))
                .collect(Collectors.toMap(BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeFormItem::getSourceFormItemId, Function.identity(), (a, b) -> a));
        Map<String, Integer> chargeSheetIdChargeSheetStatusMap = businessSheetCalculateChargeSheetDto.getChargeSheets()
                .stream()
                .collect(Collectors.toMap(BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeSheet::getId,
                        BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeSheet::getStatus,
                        (a, b) -> a)
                );


        //补充门诊form和收费处直接加的form
        rsp.setOutpatientForms(allChargeCooperationOrders.stream()
                .map(chargeCooperationOrder -> {
                    OutpatientSheetCalculateRsp.OutpatientForm outpatientForm = new OutpatientSheetCalculateRsp.OutpatientForm();
                    outpatientForm.setId(chargeCooperationOrder.getSourceFormId());
                    outpatientForm.setSourceFormType(chargeCooperationOrder.getSourceFormType());
                    outpatientForm.setChargeFormId(chargeCooperationOrder.getId());
                    outpatientForm.setFormItems(Optional.ofNullable(chargeCooperationOrder.getOrderItems())
                            .orElse(new ArrayList<>())
                            .stream()
                            .map(chargeCooperationOrderItem -> {
                                BusinessSheetCalculateChargeSheetDto.BusinessCalculateChargeFormItem businessCalculateChargeFormItem = cooperationOrderItemIdChargeFormItemMap.get(chargeCooperationOrderItem.getId());
                                OutpatientSheetCalculateRsp.OutpatientFormItem outpatientFormItem = null;
                                //如果没有，直接标记为退单
                                if (businessCalculateChargeFormItem == null) {
                                    outpatientFormItem = new OutpatientSheetCalculateRsp.OutpatientFormItem();
                                    BeanUtils.copyProperties(chargeCooperationOrderItem, outpatientFormItem);
                                    outpatientFormItem.setStatus(Constants.ChargeFormItemStatus.UNSELECTED);
                                    outpatientFormItem.setChargeFormItemId(null);
                                    outpatientFormItem.setReceivedPrice(BigDecimal.ZERO);
                                    //退单的情况
                                    outpatientFormItem.setReceivedPrice(BigDecimal.ZERO);
                                    outpatientFormItem.setReceivableFee(BigDecimal.ZERO);
                                    outpatientFormItem.setUnselectedFee(MathUtils.wrapBigDecimalNegateOrZero(outpatientFormItem.getTotalPrice()));
                                    outpatientFormItem.setNeedPayFee(BigDecimal.ZERO);
                                    outpatientFormItem.setPrintTotalPrice(BigDecimal.ZERO);
                                } else {
                                    outpatientFormItem = businessCalculateChargeFormItem.convertToOutpatientFormItem();
                                }

                                outpatientFormItem.setId(chargeCooperationOrderItem.getSourceItemId());
                                return outpatientFormItem;
                            }).collect(Collectors.toList())
                    );
                    outpatientForm.buildAllPrice();
                    int formStatus;
                    int chargeSheetStatus = chargeSheetIdChargeSheetStatusMap.getOrDefault(chargeCooperationOrder.getRelateChargeSheetId(), Constants.ChargeSheetStatus.UNCHARGED);
                    if (chargeSheetStatus != Constants.ChargeSheetStatus.UNCHARGED) {
                        formStatus = Constants.ChargeFormStatus.CHARGED;
                        if (CollectionUtils.isNotEmpty(outpatientForm.getFormItems())) {
                            if (outpatientForm.getFormItems().stream().anyMatch(outpatientFormItem -> outpatientFormItem.getStatus() == Constants.ChargeFormItemStatus.CHARGED)) {
                                formStatus = Constants.ChargeFormStatus.CHARGED;
                            } else if (outpatientForm.getFormItems().stream().allMatch(outpatientFormItem -> outpatientFormItem.getStatus() == Constants.ChargeFormItemStatus.REFUNDED)) {
                                formStatus = Constants.ChargeFormStatus.REFUNDED;
                            }
                        }
                    } else {
                        formStatus = Constants.ChargeFormStatus.UNCHARGED;
                    }

                    outpatientForm.setStatus(formStatus);
                    outpatientForm.buildAllPrice();
                    return outpatientForm;
                })
                .collect(Collectors.toList())
        );

        rsp.buildAllPrice();

        return rsp;
    }

    private ChargeSheet convertToChargeSheet(ChargeCooperationOrder chargeCooperationOrder, ChargeSheet relateChargeSheet, PatientInfo currentClinicPatientInfo, int hisType, String operatorId) {

        if (Objects.nonNull(relateChargeSheet) && relateChargeSheet.getStatus() != Constants.ChargeSheetStatus.UNCHARGED) {
            return relateChargeSheet;
        }

        if (Objects.isNull(relateChargeSheet)) {
            relateChargeSheet = createChargeSheetForCooperationOrder(chargeCooperationOrder.getChainId(),
                    chargeCooperationOrder.getClinicId(),
                    null,
                    Optional.ofNullable(currentClinicPatientInfo).map(PatientInfo::getId).orElse(Constants.ANONYMOUS_PATIENT_ID),
                    hisType,
                    operatorId);
        }

        //未收费时，走update逻辑
        ChargeSheetFactory.insertOrUpdateChargeFormsForChargeCooperationOrder(relateChargeSheet, chargeCooperationOrder, hisType, operatorId);

        return relateChargeSheet;
    }

    public int findUnchargedCount(String chainId, String clinicId) {
        Date now = new Date();
        Date startTime = cn.abcyun.cis.commons.util.DateUtils.getStartTime(now);
        Date endTime = cn.abcyun.cis.commons.util.DateUtils.getEndTime(now);

        return chargeCooperationOrderService.findUnchargedCount(chainId, clinicId, startTime.toInstant(), endTime.toInstant());
    }

    @Transactional(readOnly = true)
    public OutpatientSheetShebaoSimpleInfo queryCooperationOrderShebaoSimpleInfo(String chargeSheetId, String clinicId) {

        ChargeCooperationOrder chargeCooperationOrder = chargeCooperationOrderService.findByChargeSheetId(chargeSheetId, clinicId, false);

        if (Objects.isNull(chargeCooperationOrder)) {
            throw new NotFoundException();
        }

        return coPharmacyService.getOutpatientSheetShebaoSimpleInfo(chargeCooperationOrder.getSourceSheetId(), chargeCooperationOrder.getSourceChainId());
    }
}
