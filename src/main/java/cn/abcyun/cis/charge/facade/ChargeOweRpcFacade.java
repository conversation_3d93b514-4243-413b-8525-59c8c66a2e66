package cn.abcyun.cis.charge.facade;

import cn.abcyun.cis.charge.base.Constants;
import cn.abcyun.cis.charge.model.ChargeFormItem;
import cn.abcyun.cis.charge.model.ChargeOweSheet;
import cn.abcyun.cis.charge.model.ChargeSheet;
import cn.abcyun.cis.charge.processor.SheetProcessor;
import cn.abcyun.cis.charge.repository.ChargeOweSheetRepository;
import cn.abcyun.cis.charge.service.ChargeService;
import cn.abcyun.cis.charge.service.ChargeSheetService;
import cn.abcyun.cis.charge.service.OutpatientService;
import cn.abcyun.cis.charge.service.PatientOrderService;
import cn.abcyun.cis.charge.service.dto.ChargeFormItemView;
import cn.abcyun.cis.charge.service.dto.ChargeSheetView;
import cn.abcyun.cis.charge.util.ChargeUtils;
import cn.abcyun.cis.charge.util.ListUtils;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.rpc.outpatient.MedicalRecord;
import cn.abcyun.cis.commons.rpc.patientorder.PatientOrder;
import cn.abcyun.cis.commons.util.TextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Service
@Slf4j
public class ChargeOweRpcFacade {

    private final ChargeOweSheetRepository chargeOweSheetRepository;
    private final ChargeService chargeService;
    private final ChargeSheetService chargeSheetService;
    private final OutpatientService outpatientService;
    private final PatientOrderService patientOrderService;

    @Autowired
    public ChargeOweRpcFacade(ChargeOweSheetRepository chargeOweSheetRepository, ChargeService chargeService,
                              ChargeSheetService chargeSheetService,
                              OutpatientService outpatientService,
                              PatientOrderService patientOrderService) {

        this.chargeOweSheetRepository = chargeOweSheetRepository;
        this.chargeService = chargeService;
        this.chargeSheetService = chargeSheetService;
        this.outpatientService = outpatientService;
        this.patientOrderService = patientOrderService;
    }


    @Transactional(readOnly = true)
    public ChargeSheetView getChargeSheetViewByChargeOweSheetId(String clinicId, String chargeOweSheetId) {

        if (StringUtils.isAnyEmpty(clinicId, chargeOweSheetId)) {
            throw new ParamRequiredException("clinicId or chargeOweSheetId");
        }

        ChargeOweSheet chargeOweSheet = chargeOweSheetRepository.findByIdAndClinicIdAndIsDeleted(Long.parseLong(chargeOweSheetId), clinicId, 0);

        if (Objects.isNull(chargeOweSheet)) {
            log.info("chargeOweSheet is null");
            return null;
        }


        ChargeSheetView chargeSheetView = findChargeSheetById(chargeOweSheet.getChargeSheetId(), Constants.ChargeSource.CHARGE, true);

        if (chargeSheetView == null) {
            return null;
        }

        chargeSheetView.setCanUnLockChargeSheet(ChargeUtils.isChargerLockStatus(chargeSheetView.getLockStatus()));

        return chargeSheetView;
    }

    public ChargeSheetView findChargeSheetById(String chargeSheetId, int source, boolean notQuerySheBaoInfo) {
        if (TextUtils.isEmpty(chargeSheetId)) {
            return null;
        }

        ChargeSheet chargeSheet = chargeSheetService.findById(chargeSheetId);

        if (chargeSheet == null) {
            return null;
        }

        MedicalRecord medicalRecord = outpatientService.findMedicalRecord(chargeSheet.getPatientOrderId());

        PatientOrder patientOrder = patientOrderService.findPatientOrder(chargeSheet.getPatientOrderId(), PatientOrderService.getAgeLockTime(chargeSheet));
        ChargeSheet registrationChargeSheet = null;
        if (!TextUtils.isEmpty(chargeSheet.getRegistrationChargeSheetId())) {
            registrationChargeSheet = chargeSheetService.findById(chargeSheet.getRegistrationChargeSheetId());
            if (registrationChargeSheet != null && registrationChargeSheet.getStatus() == Constants.ChargeSheetStatus.UNCHARGED) {
                ChargeService.moveRegistrationChargeSheetToOutpatientChargeSheet(registrationChargeSheet, chargeSheet);
            }
        }


        SheetProcessor sheetProcessor = new SheetProcessor(chargeSheet);
        sheetProcessor.setSheetProcessorInfoProvider(chargeService);
        sheetProcessor.setMedicalRecord(medicalRecord);
        sheetProcessor.setPatientOrder(patientOrder);
        sheetProcessor.build();

        ChargeSheetView chargeSheetView = sheetProcessor.generateSheetDetail(false, false, source, notQuerySheBaoInfo);

        //对收费单的金额进行处理
        Map<String, ChargeFormItem> chargeFormItemMap = ListUtils.toMap(ChargeUtils.getChargeSheetItems(chargeSheet), ChargeFormItem::getId);

        Optional.ofNullable(chargeSheetView)
                .map(ChargeSheetView::getChargeForms)
                .orElse(new ArrayList<>())
                .stream()
                .filter(chargeFormView -> CollectionUtils.isNotEmpty(chargeFormView.getChargeFormItems()))
                .flatMap(chargeFormView -> chargeFormView.getChargeFormItems().stream())
                .filter(chargeFormItemView -> chargeFormItemView.getStatus() == Constants.ChargeFormItemStatus.CHARGED)
                .forEach(chargeFormItemView -> updateChargeFormItemAndChildrenReceivableTotalFee(chargeFormItemMap, chargeFormItemView));


        return chargeSheetView;

    }

    private void updateChargeFormItemAndChildrenReceivableTotalFee(Map<String, ChargeFormItem> chargeFormItemMap, ChargeFormItemView chargeFormItemView) {
        if (CollectionUtils.isNotEmpty(chargeFormItemView.getComposeChildren())) {
            chargeFormItemView.getComposeChildren()
                            .forEach(child -> updateChargeFormItemAndChildrenReceivableTotalFee(chargeFormItemMap, child));
        }

        ChargeFormItem chargeFormItem = chargeFormItemMap.getOrDefault(chargeFormItemView.getId(), null);

        if (Objects.isNull(chargeFormItem)) {
            return;
        }

        chargeFormItemView.setReceivableTotalFee(chargeFormItem.getReceivedPrice());
        chargeFormItemView.setSheBaoReceivableTotalFee(chargeFormItem.getReceivedPrice());

        if (CollectionUtils.isNotEmpty(chargeFormItemView.getChargeFormItemBatchInfos())) {
            chargeFormItemView.getChargeFormItemBatchInfos()
                    .forEach(chargeFormItemBatchInfoView -> {
                        chargeFormItemBatchInfoView.setSheBaoReceivableTotalFee(chargeFormItemBatchInfoView.getReceivedPrice());
                        chargeFormItemBatchInfoView.setSheBaoReceivableTotalFee(chargeFormItemBatchInfoView.getReceivedPrice());

                    });
        }

    }
}
