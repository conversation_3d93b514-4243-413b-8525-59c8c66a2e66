package cn.abcyun.cis.charge.mapper;

import cn.abcyun.cis.charge.api.model.*;
import cn.abcyun.cis.charge.api.model.openapi.OpenApiPatientChargeSheetView;
import cn.abcyun.cis.charge.api.model.openapi.OpenApiQueryPatientChargeSheetReq;
import cn.abcyun.cis.charge.api.model.orderlist.ChargeOrderView;
import cn.abcyun.cis.charge.api.model.orderlist.DeviceSelfPayChargeOrderView;
import cn.abcyun.cis.charge.model.ChargeFormItem;
import cn.abcyun.cis.charge.rpc.model.EmployeeChargeStatInfo;
import cn.abcyun.cis.charge.service.dto.*;
import cn.abcyun.cis.commons.rpc.charge.ChargeStatus;
import cn.abcyun.cis.commons.rpc.charge.WeClinicMyExecuteSheetListView;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.stream.Stream;

@Repository
public interface ChargeMapper {

    List<ChargeSheetAbstract> findChargeSheetAbstractList(
            @Param("offset") int offset,
            @Param("limit") int limit,
            @Param("withUndiagnosed") int withUndiagnosed,
            @Param("clinicId") String clinicId,
            @Param("filterChargeStatus") String filterChargeStatus,
            @Param("tab") Integer tab,
            @Param("typeList") List<Integer> typeList,
            @Param("createdBegin") Instant createdBegin,
            @Param("createdEnd") Instant createdEnd,
            @Param("invoiceStatusFlag") Integer invoiceStatusFlag,
            @Param("sendToPatientStatus") Integer sendToPatientStatus,
            @Param("invoiceStatues") List<Integer> invoiceStatues);

    ChargeSheetAbstractListRsp findChargeSheetAbstractListCount(
            @Param("withUndiagnosed") int withUndiagnosed,
            @Param("clinicId") String clinicId,
            @Param("filterChargeStatus") String filterChargeStatus,
            @Param("tab") Integer tab,
            @Param("typeList") List<Integer> typeList,
            @Param("createdBegin") Instant createdBegin,
            @Param("createdEnd") Instant createdEnd,
            @Param("invoiceStatusFlag") Integer invoiceStatusFlag,
            @Param("sendToPatientStatus") Integer sendToPatientStatus,
            @Param("invoiceStatues") List<Integer> invoiceStatues);

    List<ChargeSheetAbstract> findChargeSheetAbstractListForNurse(
            @Param("offset") int offset,
            @Param("limit") int limit,
            @Param("clinicId") String clinicId,
            @Param("filterExecuteStatus") String filterExecuteStatus,
            @Param("createdBegin") Instant createdBegin,
            @Param("createdEnd") Instant createdEnd,
            @Param("ownerIds") List<String> ownerIds,
            @Param("onlyShowContainsExecute") boolean onlyShowContainsExecute,
            @Param("includeItemType") Integer includeItemType,
            @Param("onlyExecuteAfterPaid") boolean onlyExecuteAfterPaid,
            @Param("onlyShowAfterPaid") boolean onlyShowAfterPaid,
            @Param("typeList") List<Integer> typeList,
            @Param("needFilterExecutor") boolean needFilterExecutor,
            @Param("filterExecutorId") String filterExecutorId);

    int findChargeSheetAbstractListCountForNurse(
            @Param("clinicId") String clinicId,
            @Param("filterExecuteStatus") String filterExecuteStatus,
            @Param("createdBegin") Instant createdBegin,
            @Param("createdEnd") Instant createdEnd,
            @Param("ownerIds") List<String> ownerIds,
            @Param("onlyShowContainsExecute") boolean onlyShowContainsExecute,
            @Param("includeItemType") Integer includeItemType,
            @Param("onlyShowAfterPaid") boolean onlyShowAfterPaid,
            @Param("typeList") List<Integer> typeList,
            @Param("needFilterExecutor") boolean needFilterExecutor,
            @Param("filterExecutorId") String filterExecutorId);

    ChargeSheetAbstractListRsp.ListSummary findChargeSheetAbstractListSummary(
            @Param("withUndiagnosed") int withUndiagnosed,
            @Param("clinicId") String clinicId,
            @Param("createdBegin") Instant createdBegin,
            @Param("createdEnd") Instant createdEnd
    );

    int findChargeSheetDraftCount(@Param("clinicId") String clinicId);

    int findChargeSheetOwedCount(@Param("clinicId") String clinicId);

    List<PatientChargeSheetAbstract> findPatientChargeSheetAbstractList(
            @Param("offset") int offset,
            @Param("limit") int limit,
            @Param("chainId") String chainId,
            @Param("clinicIds") List<String> clinicIds,
            @Param("patientId") String patientId,
            @Param("sellerId") String sellerId,
            @Param("chargedDateBegin") Instant chargedDateBegin,
            @Param("chargedDateEnd") Instant chargedDateEnd,
            @Param("isPatientCount") Integer isPatientCount
    );

    int findPatientChargeSheetAbstractListCount(
            @Param("chainId") String chainId,
            List<String> clinicIds, @Param("patientId") String patientId,
            @Param("sellerId") String sellerId,
            @Param("chargedDateBegin") Instant chargedDateBegin,
            @Param("chargedDateEnd") Instant chargedDateEnd,
            @Param("isPatientCount") Integer isPatientCount
    );

    BigDecimal findPatientChargeSheetTotalReceivedFee(
            @Param("chainId") String chainId,
            @Param("patientId") String patientId,
            @Param("sellerId") String sellerId,
            @Param("chargedDateBegin") Instant chargedDateBegin,
            @Param("chargedDateEnd") Instant chargedDateEnd
    );

    List<ChargeSheetAbstract> findChargeSheetAbstractListByIdsAndChainIdAndClinicId(
            @Param("ids") List<String> ids,
            @Param("chainId") String chainId,
            @Param("clinicId") String clinicId,
            @Param("withUndiagnosed") int withUndiagnosed
    );

    ChargeSheetAbstractListRsp findChargeSheetAbstractSummaryByIdsAndChainIdAndClinicId(
            @Param("ids") List<String> ids,
            @Param("chainId") String chainId,
            @Param("clinicId") String clinicId,
            @Param("withUndiagnosed") int withUndiagnosed
    );

    List<ChargeSheetAbstract> findChargeSheetAbstractListForNurseByIdsAndChainIdAndClinicId(
            @Param("ids") List<String> ids,
            @Param("chainId") String chainId,
            @Param("clinicId") String clinicId,
            @Param("withUndiagnosed") int withUndiagnosed,
            @Param("onlyExecuteAfterPaid") boolean onlyExecuteAfterPaid
    );

    List<ChargeSheetAbstract> findChargeSheetAbstractListByPatientOrderIdAndChainId(
            @Param("patientOrderId") String patientOrderId,
            @Param("chainId") String chainId,
            @Param("withUndiagnosed") int withUndiagnosed
    );

    List<ChargeStatus> findChargeSheetStatusBatch(@Param("patientOrderIds") List<String> patientOrderIds);

    List<DashboardStatus> findChargeDashboardStatusBatch(@Param("patientOrderIds") List<String> patientOrderIds);

    long findChargedCount(@Param("clinicId") String clinicId,
                          @Param("beginDate") Date beginDate,
                          @Param("endDate") Date endDate);

    int findUnchargedCount(@Param("clinicId") String clinicId,
                           @Param("withUndiagnosed") int withUndiagnosed,
                           @Param("beginDate") Date beginDate,
                           @Param("endDate") Date endDate);

    BigDecimal findChargedAmount(@Param("clinicId") String clinicId,
                                 @Param("beginDate") Date beginDate,
                                 @Param("endDate") Date endDate);


    BigDecimal findChargedOutpatientAmount(@Param("clinicId") String clinicId,
                                           @Param("doctorId") String doctorId,
                                           @Param("beginDate") Date beginDate,
                                           @Param("endDate") Date endDate);


    EmployeeChargeStatInfo findEmployeeChargeStatInfo(@Param("clinicId") String clinicId,
                                                      @Param("employeeId") String employeeId,
                                                      @Param("beginDate") Date beginDate,
                                                      @Param("endDate") Date endDate);

    List<String> findNeedUpgradeAdjustmentToDiscountChargeSheetIds(@Param("clinicId") String clinicId, @Param("chainId") String chainId);


    long findDoctorPatientDiagnosedChargedCount(@Param("chainId") String chainId, @Param("doctorId") String doctorId, @Param("patientId") String patientId);

    List<DoctorPatientDiagnosedChargedInfoRsp.PatientDiagnosedChargedInfo> findDoctorPatientDiagnosedChargedCountByPatientIds(@Param("chainId") String chainId, @Param("doctorId") String doctorId, @Param("patientIds") List<String> patientIds);

    //查快递公司快递浩分出来，之前是5表关联
    List<ChargeOrderViewDeliveyInfo> getDeliveryNoCompany(@Param("chargeSheetIds") List<String> chargeSheetIds, @Param("chainId") String chainId);

    //所有状态
    List<ChargeOrderView> getChargeOrderListByPatientIds(@Param("patientIds") List<String> patientIds, @Param("chainId") String chainId, @Param("offset") Integer offset, @Param("limit") Integer limit);

    int countChargeOrderListByPatientIds(@Param("patientIds") List<String> patientIds, @Param("chainId") String chainId);

    //待支付状态
    List<ChargeOrderView> getPayingChargeOrderListByPatientIds(@Param("patientIds") List<String> patientIds, @Param("chainId") String chainId, @Param("offset") Integer offset, @Param("limit") Integer limit);

    int countPayingChargeOrderListByPatientIds(@Param("patientIds") List<String> patientIds, @Param("chainId") String chainId);

    //待发药状态
    List<ChargeOrderView> getDispensingChargeOrderListByPatientIds(@Param("patientIds") List<String> patientIds, @Param("chainId") String chainId, @Param("offset") Integer offset, @Param("limit") Integer limit);

    int countDispensingChargeOrderListByPatientIds(@Param("patientIds") List<String> patientIds, @Param("chainId") String chainId);

    //待收药状态
    List<ChargeOrderView> getDeliveryChargeOrderListByPatientIds(@Param("patientIds") List<String> patientIds, @Param("chainId") String chainId, @Param("offset") Integer offset, @Param("limit") Integer limit, @Param("expiretime") Integer expiretime);

    int countDeliveryChargeOrderListByPatientIds(@Param("patientIds") List<String> patientIds, @Param("chainId") String chainId, @Param("expiretime") Integer expiretime);

    //已完成状态
    List<ChargeOrderView> getFinishedChargeOrderListByPatientIds(@Param("patientIds") List<String> patientIds, @Param("chainId") String chainId, @Param("offset") Integer offset, @Param("limit") Integer limit, @Param("expiretime") Integer expiretime);

    int countFinishedChargeOrderListByPatientIds(@Param("patientIds") List<String> patientIds, @Param("chainId") String chainId, @Param("expiretime") Integer expiretime);


    //自助取号机
    List<DeviceSelfPayChargeOrderView> getDeviceSelfPayChargeOrderListByPatientIds(@Param("patientIds") List<String> patientIds, @Param("chainId") String chainId, @Param("clinicId") String clinicId, @Param("offset") Integer offset, @Param("limit") Integer limit, @Param("searchBegin") Instant searchBegin);

    int countDeviceSelfPayChargeOrderListByPatientIds(@Param("patientIds") List<String> patientIds, @Param("chainId") String chainId, @Param("clinicId") String clinicId, @Param("searchBegin") Instant searchBegin);

    List<WeClinicChargeHistoryListRsp.WeClinicChargeHistoryItem> getChargeHistoryForWeClinic(
            @Param("chainId") String chainId,
            @Param("patientIds") List<String> patientIds,
            @Param("offset") Integer offset,
            @Param("limit") Integer limit);

    int getChargeHistoryForWeClinicCount(
            @Param("chainId") String chainId,
            @Param("patientIds") List<String> patientIds,
            @Param("offset") Integer offset,
            @Param("limit") Integer limit);

    /**
     * 根据连锁id、患者id集合、执行状态集合、状态集合、删除标识统计包含执行项的收费单
     *
     * @param chainId         连锁id
     * @param clinicId
     * @param patientIds      患者id集合
     * @param executeStatuses 执行状态集合
     * @param statuses        状态集合
     * @param isDeleted       删除标识
     * @param beginTime       开始日期
     * @param endTime         结束日期
     * @return 统计结果
     */
    int countExecutiveSheet(@Param("chainId") String chainId,
                            @Param("clinicId") String clinicId,
                            @Param("patientIds") List<String> patientIds,
                            @Param("executeStatuses") List<Integer> executeStatuses,
                            @Param("statuses") List<Integer> statuses,
                            @Param("isDeleted") int isDeleted,
                            @Param("beginTime") LocalDateTime beginTime,
                            @Param("endTime") LocalDateTime endTime);

    /**
     * 根据连锁id、患者id集合、执行状态集合、状态集合、删除标识分页查询包含执行项的收费单
     *
     * @param chainId         连锁id
     * @param clinicId
     * @param patientIds      患者id集合
     * @param executeStatuses 执行状态集合
     * @param statuses        状态集合
     * @param isDeleted       删除标识
     * @param offset          分页查询下标
     * @param limit           分页条数
     * @param beginTime       开始时间
     * @param endTime         结束时间
     * @return 分页查询结果
     */
    List<WeClinicMyExecuteSheetListView> pageListExecutiveSheet(@Param("chainId") String chainId,
                                                                @Param("clinicId") String clinicId,
                                                                @Param("patientIds") List<String> patientIds,
                                                                @Param("executeStatuses") List<Integer> executeStatuses,
                                                                @Param("statuses") List<Integer> statuses,
                                                                @Param("isDeleted") int isDeleted,
                                                                @Param("offset") int offset,
                                                                @Param("limit") int limit,
                                                                @Param("beginTime") LocalDateTime beginTime,
                                                                @Param("endTime") LocalDateTime endTime);

    /**
     * @param clinicId
     * @param beginDate
     * @param endDate
     * @return
     */
    List<ChargeSheetSimpleListRsp.ChargeSheetSimple> queryPaidChargeSheetsByDate(@Param("clinicId") String clinicId, @Param("beginDate") Instant beginDate, @Param("endDate") Instant endDate);

    List<ChargeSheetSimpleListRsp.ChargeSheetSimple> listChargeSheetSimpleByIds(@Param("chainId") String chainId, @Param("chargeSheetIds") List<String> chargeSheetIds);

    cn.abcyun.bis.rpc.sdk.cis.model.charge.ChargeSheetSimpleListRsp.ChargeSheetSimple findChargeSheetSimpleById(@Param("chargeSheetId") String chargeSheetId);

    List<QueryChargeSheetExceptionStatusListRsp.ChargeSheetExceptionStatusInfo> findAllContainExceptionChargeSheetsByPatientOrderIds(@Param("chainId") String chainId, @Param("clinicId") String clinicId, @Param("patientOrderIds") List<String> patientOrderIds);

    BigDecimal findPatientOrderReceivedFee(@Param("clinicId") String clinicId, @Param("patientOrderId") String patientOrderId);

    List<QueryPatientOrderNotificationsRsp.PatientOrderReceivedFeeView> findPatientOrdersReceivedFee(@Param("clinicId") String clinicId, @Param("patientOrderIds") List<String> patientOrderIds);

    List<String> findChargeSheetIdsByHospitalSheetId(@Param("chainId") String chainId, @Param("clinicId") String clinicId, @Param("hospitalSheetId") String hospitalSheetId);

    List<String> pageListChargeSheetIdsByHospitalSheetId(@Param("hospitalSheetId") long hospitalSheetId, @Param("chainId") String chainId, @Param("clinicId") String clinicId, @Param("offset") int offset, @Param("limit") int limit);

    int countByHospitalSheetId(long hospitalSheetId, String clinicId, int offset, int limit);

    List<ChargeSheetWithPatientOrderStatus> findChargeSheetsByPatientOrderId(@Param("patientOrderIds") List<String> patientOrderIds, @Param("type") int type);

    List<ChargeSheetAbstract> listChargeSheetAbstractListByDate(@Param("clinicId") String clinicId,
                                                                @Param("beginCreated") Instant beginCreated,
                                                                @Param("endCreated") Instant endCreated);

    List<ChargeSheetAbstract> listChargeSheetAbstractListByIds(@Param("chainId") String chainId,
                                                               @Param("ids") List<String> ids);

    List<ChargeSheetAbstract> listOwingChargeSheetByPatientId(@Param("patientId") String patientId, @Param("clinicId") String clinicId);

    ChargeSheetSimpleListRsp.ChargeSheetSimple findLatestUnchargedChargeSheetByPatientIds(@Param("chainId") String chainId,
                                                                                          @Param("clinicId") String clinicId,
                                                                                          @Param("patientIds") List<String> patientIds,
                                                                                          @Param("startTime") Date startTime,
                                                                                          @Param("endTime") Date endTime);

    List<ChargeSheetAbstract> findChargeSheetAbstractListByPatientId(@Param("patientId") String patientId,
                                                                     @Param("chainId") String chainId,
                                                                     @Param("sellerId") String sellerId,
                                                                     @Param("createdDateBegin") Instant createdDateBegin,
                                                                     @Param("createdDateEnd") Instant createdDateEnd);

    List<QueryChargeSheetByPatientOrderIdsRsp.ChargeSheet> listChargeSheetSimpleByPatientOrderIds(@Param("chainId") String chainId, @Param("patientOrderIds") List<String> patientOrderIds);

    void updateChargeFormRemark(@Param("chainId") String chainId,
                                @Param("clinicId") String clinicId,
                                @Param("chargeSheetId") String chargeSheetId,
                                @Param("id") String itemId,
                                @Param("remark") String remark,
                                @Param("operatorId") String operatorId);

    List<ChargeSheetSimpleListRsp.ChargeSheetSimple> pageLatestChargeSheetList(int offset, int limit, Date beginDate, Date endDate, String clinicId);

    int countLatestChargeSheet(Date beginDate, Date endDate, String clinicId);


    List<OpenApiPatientChargeSheetView> queryPatientChargeSheetForOpenApi(OpenApiQueryPatientChargeSheetReq req);

    int countPatientChargeSheetForOpenApi(OpenApiQueryPatientChargeSheetReq req);

    /**
     * 获取最后一次收费单信息
     */
    LastChargeSheetSimpleRsp.ChargeSheetSimple getLatestChargeSheet(@Param("chainId") String chainId,
                                                                    @Param("clinicId") String clinicId,
                                                                    @Param("patientId") String patientId);

    /**
     * 查询社保卡支付
     */
    List<String> queryChargeTransactionHealthCard(@Param("clinicId") String clinicId,
                                                  @Param("chargeSheetIdList") List<String> chargeSheetIdList,
                                                  @Param("payMode") int payMode,
                                                  @Param("isPaidback") int isPaidback,
                                                  @Param("isDeleted") int isDeleted);
}
