package cn.abcyun.cis.charge.mapper;

import cn.abcyun.cis.charge.model.ChargeAirPharmacyUsedRecord;
import cn.abcyun.cis.commons.amqp.message.charge.AirPharmacyAddVendorUsedCountMessage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ChargeAirPharmacyUsedRecordMapper {

    List<ChargeAirPharmacyUsedRecord> findAllByChainIdAndClinicIdAndVendor(@Param("chainId") String chainId, @Param("clinicId") String clinicId, @Param("vendors") List<AirPharmacyAddVendorUsedCountMessage.AirPharmacyAddVendorUsedCount> vendors);


}
