package cn.abcyun.cis.charge.mapper;

import cn.abcyun.cis.charge.service.dto.SalesOrderAbstract;
import cn.abcyun.cis.charge.service.dto.SalesOrderSummary;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024-01-27 20:28
 * @Description
 */

@Repository
public interface ChargeSalesOrderMapper {
    List<SalesOrderAbstract> findChargeSheetSalesOrderAbstractListByIdsAndChainIdAndClinicId(
            @Param("ids") List<String> ids,
            @Param("chainId") String chainId,
            @Param("clinicId") String clinicId);

    List<SalesOrderAbstract> findChargeSheetSalesOrderAbstractListByChainIdAndClinicId(
            @Param("chainId") String chainId,
            @Param("clinicId") String clinicId,
            @Param("statusList") List<Integer> statusList,
            @Param("filterFlag") Integer filterFlag,
            @Param("beginTime") Instant beginTime,
            @Param("endTime") Instant endTime,
            @Param("type") Integer type,
            @Param("coSourceClinicId") String coSourceClinicId,
            @Param("chargeSheetId") String chargeSheetId,
            @Param("offset") Integer offset,
            @Param("limit") Integer limit);

    SalesOrderSummary findChargeSheetSalesOrderSummary(@Param("chainId") String chainId,
                                                       @Param("clinicId") String clinicId,
                                                       @Param("statusList") List<Integer> statusList,
                                                       @Param("filterFlag") Integer filterFlag,
                                                       @Param("beginTime") Instant beginTime,
                                                       @Param("endTime") Instant endTime,
                                                       @Param("type") Integer type,
                                                       @Param("coSourceClinicId") String coSourceClinicId,
                                                       @Param("chargeSheetId") String chargeSheetId);
}
