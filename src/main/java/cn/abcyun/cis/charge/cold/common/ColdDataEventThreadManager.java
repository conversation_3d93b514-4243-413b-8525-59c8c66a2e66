package cn.abcyun.cis.charge.cold.common;

import org.springframework.context.ApplicationEventPublisher;
import org.springframework.core.NamedThreadLocal;
import org.springframework.transaction.support.TransactionSynchronization;

import java.util.function.Consumer;

public class ColdDataEventThreadManager {

    public static final ThreadLocal<ColdDataEvent> currentThreadColdDataEvent = new NamedThreadLocal<>("currentThreadColdDataEvent");
    public static final ThreadLocal<TransactionSynchronization> currentThreadColdDataEventPublish = new NamedThreadLocal<>("currentThreadColdDataEventPublish");

    public static void clear() {
        currentThreadColdDataEvent.remove();
        currentThreadColdDataEventPublish.remove();
    }

    public static TransactionSynchronization getCurrentThreadColdDataEventPublisher(ApplicationEventPublisher applicationEventPublisher, Consumer<ColdDataEvent> coldDataEventConsumer) {
        TransactionSynchronization coldDataEventPublisher = currentThreadColdDataEventPublish.get();

        if (coldDataEventPublisher == null) {
            //创建coldDataEvent
            synchronized (Thread.currentThread()) {
                TransactionSynchronization existedColdDataEventPublisher = currentThreadColdDataEventPublish.get();
                if(existedColdDataEventPublisher == null) {
                    coldDataEventPublisher = new TransactionSynchronization() {
                        @Override
                        public void afterCommit() {
                            ColdDataEvent coldDataEvent = getColdDataEvent();
                            coldDataEventConsumer.accept(coldDataEvent);
                            applicationEventPublisher.publishEvent(coldDataEvent);
                            ColdDataEventThreadManager.clear();
                        }

                        @Override
                        public void afterCompletion(int status) {
                            if (status != STATUS_COMMITTED) {
                                ColdDataEventThreadManager.clear(); // 事务回滚时清理
                            }
                        }
                    };
                    currentThreadColdDataEventPublish.set(coldDataEventPublisher);
                } else {
                    coldDataEventPublisher = existedColdDataEventPublisher;
                }
            }
        }

        return coldDataEventPublisher;
    }

    /**
     * 单例获取coldDataEvent
     * @return
     */
    public static ColdDataEvent getColdDataEvent() {
        ColdDataEvent coldDataEvent = currentThreadColdDataEvent.get();

        if (coldDataEvent == null) {
            //创建coldDataEvent
            synchronized (Thread.currentThread()) {
                ColdDataEvent existedColdDataEvent = currentThreadColdDataEvent.get();
                if(existedColdDataEvent == null) {
                    coldDataEvent = new ColdDataEvent();
                    currentThreadColdDataEvent.set(coldDataEvent);
                } else {
                    coldDataEvent = existedColdDataEvent;
                }
            }
        }
        return coldDataEvent;
    }

}
