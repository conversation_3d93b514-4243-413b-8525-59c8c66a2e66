package cn.abcyun.cis.charge.cold.common;

import cn.abcyun.cis.charge.model.ChargeFormItem;
import cn.abcyun.cis.charge.model.ChargeTransactionRecord;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class ColdDataEvent {
    private List<ChargeFormItem> chargeFormItems;

    private List<ChargeTransactionRecord> chargeTransactionRecords;
}
