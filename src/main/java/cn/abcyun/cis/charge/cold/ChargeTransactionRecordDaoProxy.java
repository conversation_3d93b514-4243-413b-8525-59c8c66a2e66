package cn.abcyun.cis.charge.cold;

import cn.abcyun.cis.charge.cold.common.ColdDataEventThreadManager;
import cn.abcyun.cis.charge.model.ChargeTransactionRecord;
import cn.abcyun.cis.charge.model.ChargeTransactionRecordAdditional;
import cn.abcyun.cis.charge.model.ChargeTransactionRecordBatchInfo;
import cn.abcyun.cis.charge.repository.ChargeTransactionRecordAdditionalRepository;
import cn.abcyun.cis.charge.repository.ChargeTransactionRecordBatchInfoRepository;
import cn.abcyun.cis.charge.repository.ChargeTransactionRecordRepository;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 说明: 对于merge result为true的方法，第一个参数需要是list
 */
@Service
public class ChargeTransactionRecordDaoProxy {
    private final ChargeTransactionRecordRepository chargeTransactionRecordRepository;
    private final ChargeTransactionRecordAdditionalRepository chargeTransactionRecordAdditionalRepository;
    private final ChargeTransactionRecordBatchInfoRepository chargeTransactionRecordBatchInfoRepository;
    private final ApplicationEventPublisher applicationEventPublisher;

    public ChargeTransactionRecordDaoProxy(ChargeTransactionRecordRepository chargeTransactionRecordRepository,
                                           ChargeTransactionRecordAdditionalRepository chargeTransactionRecordAdditionalRepository,
                                           ChargeTransactionRecordBatchInfoRepository chargeTransactionRecordBatchInfoRepository,
                                           ApplicationEventPublisher applicationEventPublisher) {
        this.chargeTransactionRecordRepository = chargeTransactionRecordRepository;
        this.chargeTransactionRecordAdditionalRepository = chargeTransactionRecordAdditionalRepository;
        this.chargeTransactionRecordBatchInfoRepository = chargeTransactionRecordBatchInfoRepository;
        this.applicationEventPublisher = applicationEventPublisher;
    }

    public void saveAll(List<ChargeTransactionRecord> chargeTransactionRecords) {
        if (CollectionUtils.isEmpty(chargeTransactionRecords)) {
            return;
        }

        chargeTransactionRecordRepository.saveAll(chargeTransactionRecords);

        saveAdditionals(chargeTransactionRecords);
        saveBatchInfos(chargeTransactionRecords);

        //发布事务提交后事件
        TransactionSynchronizationManager.registerSynchronization(ColdDataEventThreadManager.getCurrentThreadColdDataEventPublisher(applicationEventPublisher, coldDataEvent -> coldDataEvent.setChargeTransactionRecords(chargeTransactionRecords)));
    }

    private void saveBatchInfos(List<ChargeTransactionRecord> chargeTransactionRecords) {

        if (CollectionUtils.isEmpty(chargeTransactionRecords)) {
            return;
        }

        List<ChargeTransactionRecordBatchInfo> recordBatchInfos = chargeTransactionRecords.stream()
                .filter(chargeTransactionRecord -> CollectionUtils.isNotEmpty(chargeTransactionRecord.getBatchInfos()))
                .flatMap(chargeTransactionRecord -> chargeTransactionRecord.getBatchInfos().stream())
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(recordBatchInfos)) {
            return;
        }

        chargeTransactionRecordBatchInfoRepository.saveAll(recordBatchInfos);
    }

    private void saveAdditionals(List<ChargeTransactionRecord> chargeTransactionRecords) {

        List<ChargeTransactionRecordAdditional> additionals = chargeTransactionRecords.stream()
                .map(ChargeTransactionRecord::getAdditional)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(additionals)) {
            return;
        }

        chargeTransactionRecordAdditionalRepository.saveAll(additionals);

    }


}
