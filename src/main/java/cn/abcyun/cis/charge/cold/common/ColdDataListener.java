package cn.abcyun.cis.charge.cold.common;

import cn.abcyun.cis.charge.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.concurrent.ThreadPoolExecutor;

@Component
@Slf4j
public class ColdDataListener {
    @Value("${abc.coldDbSyncEnabled:true}")
    private boolean coldDbSyncEnabled;
    private final ColdDataService coldService;

    public ColdDataListener(ColdDataService coldService) {
        this.coldService = coldService;
    }

    @Async("coldDataAsyncExecutor")
    @EventListener
    public void saveColdData(ColdDataEvent coldDataEvent) {

        log.info("coldDbSyncEnabled: {},saveColdData: {}", coldDbSyncEnabled, JsonUtils.dump(coldDataEvent));

        if (!coldDbSyncEnabled) {
            return;
        }
        if (coldDataEvent == null) {
            return;
        }

        coldService.saveColdData(coldDataEvent);
    }

    @Bean("coldDataAsyncExecutor")
    public ThreadPoolTaskExecutor coldDataAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(500);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardPolicy());
        executor.setThreadNamePrefix("coldDataAsyncExecutor-");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(5);
        executor.initialize();
        return executor;
    }
}
