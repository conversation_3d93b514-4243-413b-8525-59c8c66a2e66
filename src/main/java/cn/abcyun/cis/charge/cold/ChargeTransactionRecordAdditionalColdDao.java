package cn.abcyun.cis.charge.cold;

import cn.abcyun.cis.charge.cold.common.GenericEntityService;
import cn.abcyun.cis.charge.model.ChargeTransactionRecordAdditional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ChargeTransactionRecordAdditionalColdDao {

    @Autowired
    private GenericEntityService genericEntityService;

    public void saveColdData(List<ChargeTransactionRecordAdditional> additionals) {
        genericEntityService.saveEntities(additionals, ChargeTransactionRecordAdditional.class);
    }
}
