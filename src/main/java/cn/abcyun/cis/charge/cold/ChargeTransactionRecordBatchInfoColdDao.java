package cn.abcyun.cis.charge.cold;

import cn.abcyun.cis.charge.cold.common.GenericEntityService;
import cn.abcyun.cis.charge.model.ChargeTransactionRecordBatchInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ChargeTransactionRecordBatchInfoColdDao {

    @Autowired
    private GenericEntityService genericEntityService;

    public void saveColdData(List<ChargeTransactionRecordBatchInfo> recordBatchInfos) {
        genericEntityService.saveEntities(recordBatchInfos, ChargeTransactionRecordBatchInfo.class);
    }
}
