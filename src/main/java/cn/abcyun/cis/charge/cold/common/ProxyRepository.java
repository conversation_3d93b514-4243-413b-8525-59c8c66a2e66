package cn.abcyun.cis.charge.cold.common;

import cn.abcyun.cis.charge.cold.BusinessIndexIdConvert;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Order(Ordered.HIGHEST_PRECEDENCE)
public @interface ProxyRepository {
    /**
     * 是否对冷热结果进行merge操作
     */
    boolean mergeResult() default false;

    BusinessIndexIdConvert convertIndexIdClass();
    /**
     * index argument position
     */
    int indexArgPosition() default 0;
}
