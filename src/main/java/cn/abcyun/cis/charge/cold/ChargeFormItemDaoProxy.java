package cn.abcyun.cis.charge.cold;

import cn.abcyun.cis.charge.cold.common.ColdDataEventThreadManager;
import cn.abcyun.cis.charge.cold.common.ColdDataService;
import cn.abcyun.cis.charge.mapper.ChargeFormItemMapper;
import cn.abcyun.cis.charge.model.ChargeFormItem;
import cn.abcyun.cis.charge.repository.ChargeFormItemRepository;
import cn.abcyun.cis.charge.service.dto.ChargeFormItemCountDto;
import cn.abcyun.cis.charge.service.dto.ChargeSheetIdSourceTotalPriceDto;
import cn.abcyun.cis.charge.service.dto.RegistrationChargeView;
import cn.abcyun.cis.core.cold.ProxyDao;
import cn.abcyun.cis.core.db.UseDataSource;
import org.apache.commons.collections.CollectionUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 说明: 对于merge result为true的方法，第一个参数需要是list
 */
@Service
public class ChargeFormItemDaoProxy implements BusinessIndexIdConvert {
    private final ChargeFormItemRepository mChargeFormItemRepository;
    private final ChargeFormItemMapper chargeFormItemMapper;
    private final ApplicationEventPublisher applicationEventPublisher;
    private final ColdDataService coldDataService;

    public ChargeFormItemDaoProxy(ChargeFormItemRepository mChargeFormItemRepository,
                                  ChargeFormItemMapper chargeFormItemMapper,
                                  ApplicationEventPublisher applicationEventPublisher, ColdDataService coldDataService) {
        this.mChargeFormItemRepository = mChargeFormItemRepository;
        this.chargeFormItemMapper = chargeFormItemMapper;
        this.applicationEventPublisher = applicationEventPublisher;
        this.coldDataService = coldDataService;
    }

    @ProxyDao
    public ChargeFormItem findByChainIdAndIdAndIsDeleted(String chainId, String chargeFormItemId, int isDeleted) {
        return mChargeFormItemRepository.findByChainIdAndIdAndIsDeleted(chainId, chargeFormItemId, isDeleted);
    }

    @ProxyDao
    public List<ChargeFormItem> findByChargeSheetId(String chargeSheetId) {
        return mChargeFormItemRepository.findByChargeSheetId(chargeSheetId);
    }

    @ProxyDao
    public List<ChargeFormItem> findAllByClinicIdAndChargeSheetIdAndIdInAndIsDeleted(String clinicId, String chargeSheetId, Collection<String> chargeFormItemIds, int isDeleted) {
        return mChargeFormItemRepository.findAllByClinicIdAndChargeSheetIdAndIdInAndIsDeleted(clinicId, chargeSheetId, chargeFormItemIds, isDeleted);
    }

    @ProxyDao(mergeResult = true)
    public List<ChargeFormItem> findByChargeFormIdInAndIsDeleted(List<String> formIds, int isDeleted) {
        if (CollectionUtils.isEmpty(formIds)) {
            return new ArrayList<>();
        }
        return mChargeFormItemRepository.findByChargeFormIdInAndIsDeleted(formIds, isDeleted);
    }

    @ProxyDao(mergeResult = true)
    public List<ChargeFormItem> findByChargeSheetIdIn(List<String> chargeSheetIds) {
        return mChargeFormItemRepository.findByChargeSheetIdIn(chargeSheetIds);
    }

    @ProxyDao(mergeResult = true)
    public List<ChargeFormItem> findByChargeSheetIdInAndIsDeleted(List<String> chargeSheetIds, int isDeleted) {
        return mChargeFormItemRepository.findByChargeSheetIdInAndIsDeleted(chargeSheetIds, isDeleted);
    }

    @ProxyDao(mergeResult = true)
    public List<ChargeFormItem> findByIdInAndChainIdAndIsDeleted(List<String> chargeFormItemIds, String chainId, int isDeleted) {
        return mChargeFormItemRepository.findByIdInAndChainIdAndIsDeleted(chargeFormItemIds, chainId, isDeleted);
    }

    @UseDataSource("cold")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<String> findChargeSheetIdByPatientId(List<String> requestChargeSheetIds, int isDeleted, int offset, int limit) {
        return mChargeFormItemRepository.findChargeSheetIdByPatientId(requestChargeSheetIds, isDeleted, offset, limit);
    }

    @UseDataSource("cold")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public int countChargeSheetIdByPatientId(List<String> chargeSheetIds, int isDeleted) {
        return mChargeFormItemRepository.countChargeSheetIdByPatientId(chargeSheetIds, isDeleted);
    }

    @ProxyDao
    public ChargeFormItem findFirstChargeFormItemsByPatientOrderIdAndProductType(String patientOrderId, int productType, int isDeleted) {
        return mChargeFormItemRepository.findFirstChargeFormItemByPatientOrderIdAndProductTypeAndIsDeleted(patientOrderId, productType, isDeleted);
    }

    @ProxyDao(mergeResult = true)
    public List<ChargeFormItemCountDto> getChargeFormItemCountDtoBySheetIds(List<String> chargeSheetIds, String chainId) {
        return chargeFormItemMapper.getChargeFormItemCountDtoBySheetIds(chargeSheetIds, chainId);
    }

    @ProxyDao(mergeResult = true)
    public List<RegistrationChargeView> queryRegistrationChargeInfoBatchByPatientOrderIds(List<String> patientOrderIds, String chainId) {
        return chargeFormItemMapper.queryRegistrationChargeInfoBatchByPatientOrderIds(patientOrderIds);
    }

    @ProxyDao(mergeResult = true)
    public List<ChargeSheetIdSourceTotalPriceDto> findChargeSheetSourceTotalPriceBySheetIds(List<String> chargeSheetIds, String chainId) {
        return chargeFormItemMapper.findChargeSheetSourceTotalPriceBySheetIds(chargeSheetIds, chainId);
    }

    public void updateChargeFormItemRemark(String chainId, String clinicId, String chargeSheetId, String businessId, String remark, String operatorId) {
        chargeFormItemMapper.updateChargeFormItemRemark(chainId, clinicId, chargeSheetId, businessId, remark, operatorId);
    }

    public void updateChargeFormItemSpecialRequirement(String chainId, String clinicId, String chargeSheetId, String businessId, String remark, String operatorId) {
        chargeFormItemMapper.updateChargeFormItemSpecialRequirement(chainId, clinicId, chargeSheetId, businessId, remark, operatorId);
    }

//    public void updateProductSnapshotById(String chargeSheetId, Map<String, String> chargeFormItemIdProductSnapshotMap, String operatorId) {
//        if (MapUtils.isEmpty(chargeFormItemIdProductSnapshotMap)) {
//            return;
//        }
//
//        //回写
//        List<ChargeFormItem> formItems = mChargeFormItemRepository.findByChargeSheetId(chargeSheetId);
//        boolean isCold = formItems.stream().anyMatch(ChargeFormItem::isCold);
//        if (isCold) {
//            mChargeFormItemRepository.saveAll(formItems);
//        }
//
//        //异步更新冷库
//        //@ChenLei 关键地方 updateProductSnapshotById 原因是因为并发修改formItem
//        chargeFormItemIdProductSnapshotMap.forEach((id, productSnapshot) -> {
//            mChargeFormItemRepository.updateProductSnapshotById(id, productSnapshot, operatorId);
//        });
//    }

    public void saveAll(List<ChargeFormItem> updateFormItems) {
        if (CollectionUtils.isEmpty(updateFormItems)) {
            return;
        }

        // 回写保存chargeSheet下的formItem
        List<String> coldChargeSheetIds = updateFormItems.stream().filter(ChargeFormItem::isCold).map(ChargeFormItem::getChargeSheetId).distinct().collect(Collectors.toList());
        List<ChargeFormItem> savedFormItems;
        if (CollectionUtils.isNotEmpty(coldChargeSheetIds)) {
            savedFormItems = coldDataService.findByChargeSheetIdIn(coldChargeSheetIds);
            for (ChargeFormItem updateFormItem : updateFormItems) {
                savedFormItems.removeIf(item -> updateFormItem.getId().equals(item.getId()));
            }
            savedFormItems.addAll(updateFormItems);
        } else {
            savedFormItems = updateFormItems;
        }

        saveFormItems(savedFormItems);
    }

    public void save(ChargeFormItem chargeFormItem) {
        if (chargeFormItem == null) {
            return;
        }
        List<ChargeFormItem> savedFormItems;
        if (chargeFormItem.isCold()) {
            savedFormItems = mChargeFormItemRepository.findByChargeSheetId(chargeFormItem.getChargeSheetId());
            savedFormItems.removeIf(item -> item.getId().equals(chargeFormItem.getId()));
            savedFormItems.add(chargeFormItem);
        } else {
            savedFormItems = Collections.singletonList(chargeFormItem);
        }
        saveFormItems(savedFormItems);
    }

    public void saveInSingleSheet(List<ChargeFormItem> chargeFormItems, String chargeSheetId) {
        if (CollectionUtils.isEmpty(chargeFormItems)) {
            return;
        }

        List<ChargeFormItem> savedFormItems;
        // 回写保存chargeSheet下的formItem
        boolean isCold = chargeFormItems.stream().anyMatch(ChargeFormItem::isCold);
        if (isCold) {
            savedFormItems = coldDataService.findByChargeSheetId(chargeSheetId);
            for (ChargeFormItem updateFormItem : chargeFormItems) {
                savedFormItems.removeIf(item -> updateFormItem.getId().equals(item.getId()));
            }
            savedFormItems.addAll(chargeFormItems);
        } else {
            savedFormItems = chargeFormItems;
        }

        saveFormItems(savedFormItems);
    }


    private void saveFormItems(List<ChargeFormItem> formItems) {
        if (CollectionUtils.isEmpty(formItems)) {
            return;
        }

        //复制对象，保证两次save是一个对象
//        List<ChargeFormItem> cloneFormItems = formItems.stream().map(chargeFormItem -> {
//            ChargeFormItem cloneFormItem = new ChargeFormItem();
//            BeanUtils.copyProperties(chargeFormItem, cloneFormItem);
//            return cloneFormItem;
//        }).collect(Collectors.toList());
        mChargeFormItemRepository.saveAll(formItems);

        TransactionSynchronizationManager.registerSynchronization(ColdDataEventThreadManager.getCurrentThreadColdDataEventPublisher(applicationEventPublisher, coldDataEvent -> coldDataEvent.setChargeFormItems(formItems)));

//        applicationEventPublisher.publishEvent(new ColdDataEvent().setChargeFormItems(formItems));
    }

    @Override
    public String convert(ProceedingJoinPoint joinPoint, Object item) {
        if (item instanceof ChargeFormItem) {
            if (joinPoint.getSignature().getName().contains("ChargeFormIdIn")) {
                return ((ChargeFormItem) item).getChargeFormId();
            } else if (joinPoint.getSignature().getName().contains("ChargeSheetIdIn")) {
                return ((ChargeFormItem) item).getChargeSheetId();
            } else if (joinPoint.getSignature().getName().contains("IdIn")) {
                return ((ChargeFormItem) item).getId();
            }
        } else if (item instanceof ChargeFormItemCountDto) {
            if (joinPoint.getSignature().getName().contains("getChargeFormItemCountDtoBySheetIds")) {
                return ((ChargeFormItemCountDto) item).getChargeSheetId();
            }
        } else if (item instanceof RegistrationChargeView) {
            if (joinPoint.getSignature().getName().contains("queryRegistrationChargeInfoBatchByPatientOrderIds")) {
                return ((RegistrationChargeView) item).getPatientOrderId();
            }
        } else if (item instanceof ChargeSheetIdSourceTotalPriceDto) {
            if (joinPoint.getSignature().getName().contains("findChargeSheetSourceTotalPriceBySheetIds")) {
                return ((ChargeSheetIdSourceTotalPriceDto) item).getChargeSheetId();
            }
        }
        return null;
    }
}
