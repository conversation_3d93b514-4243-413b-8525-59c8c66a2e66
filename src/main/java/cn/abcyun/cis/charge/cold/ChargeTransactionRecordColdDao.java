package cn.abcyun.cis.charge.cold;

import cn.abcyun.cis.charge.cold.common.GenericEntityService;
import cn.abcyun.cis.charge.model.ChargeTransactionRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ChargeTransactionRecordColdDao {

    @Autowired
    private GenericEntityService genericEntityService;

    public void saveColdData(List<ChargeTransactionRecord> chargeTransactionRecords) {
        genericEntityService.saveEntities(chargeTransactionRecords, ChargeTransactionRecord.class);
    }
}
