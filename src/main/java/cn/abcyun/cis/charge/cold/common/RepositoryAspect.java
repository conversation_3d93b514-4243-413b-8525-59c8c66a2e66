package cn.abcyun.cis.charge.cold.common;

import cn.abcyun.cis.charge.model.ChargeFormItem;
import cn.abcyun.cis.charge.service.dto.ChargeFormItemCountDto;
import cn.abcyun.cis.charge.service.dto.ChargeSheetIdSourceTotalPriceDto;
import cn.abcyun.cis.charge.service.dto.RegistrationChargeView;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Aspect
@Component
@Slf4j
public class RepositoryAspect {
    private final ColdDataService coldDataService;

    public RepositoryAspect(ColdDataService coldDataService) {
        this.coldDataService = coldDataService;
    }

    // 前置通知：在调用 UserRepository 中的方法之前执行
    @Around("@annotation(proxyRepository)")
    public Object around(ProceedingJoinPoint joinPoint, ProxyRepository proxyRepository) {
        log.debug("method execution: {}", joinPoint.getSignature().getName());
        boolean mergeResult = proxyRepository.mergeResult();
        int indexArgPosition = proxyRepository.indexArgPosition();

        //获取注解的

        try {
            Object result = joinPoint.proceed();
            if (mergeResult) {
                return mergeResult(joinPoint, result, indexArgPosition);
            } else if (result == null) {
                result = coldDataService.getColdDataByPoint(joinPoint);
            } else if (result instanceof List) {
                List resultList = (List) result;
                if (resultList.isEmpty()) {
                    result = coldDataService.getColdDataByPoint(joinPoint);
                }
            }
            return result;
        } catch (Throwable e) {
            log.error("get cold data error", e);
            throw new RuntimeException(e);
        }
    }

    @SuppressWarnings("unchecked")
    private Object mergeResult(ProceedingJoinPoint joinPoint, Object result, int indexArgPosition) {
        Object[] args = joinPoint.getArgs();
        if (args == null || args.length == 0) {
            return null;
        }

        if (indexArgPosition >= args.length) {
            log.error("indexArgPosition is out of range");
            throw new RuntimeException("indexArgPosition is out of range");
        }

        Object arg = args[indexArgPosition];
        if (!(arg instanceof List) || !(result instanceof List)) {
            return null;
        }

        //通过joinPoint找到对应的类，找到对应的convertBusinessIndexId方法


        //可以通过反射找出来indexId. 这里就先不反射了。
        List<String> indexIds = (List<String>) arg;
        List resultList = (List) result;
        Set<String> hotIndexIds = (Set<String>) resultList.stream()
                .map(item -> getBusinessIndexId(joinPoint, item))
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        if (hotIndexIds.size() == indexIds.size()) {
            return resultList;
        }

        //找出来coldIndexIds
        List<String> coldIndexIds = new ArrayList<>();
        for (String indexId : indexIds) {
            if (!hotIndexIds.contains(indexId)) {
                coldIndexIds.add(indexId);
            }
        }

        //继续查询coldIndexIds
        if (CollectionUtils.isNotEmpty(coldIndexIds)) {
            args[indexArgPosition] = coldIndexIds;
            Object coldData = coldDataService.getColdDataByPoint(joinPoint, args);
            if (coldData instanceof List) {
                resultList.addAll((List<Object>) coldData);
            } else {
                log.error("Cold data is not a list {}", coldData);
            }
        }
        return resultList;
    }


    public String getBusinessIndexId(ProceedingJoinPoint joinPoint, Object item) {
        if (item instanceof ChargeFormItem) {
            if (joinPoint.getSignature().getName().contains("ChargeFormIdIn")) {
                return ((ChargeFormItem) item).getChargeFormId();
            } else if (joinPoint.getSignature().getName().contains("ChargeSheetIdIn")) {
                return ((ChargeFormItem) item).getChargeSheetId();
            } else if (joinPoint.getSignature().getName().contains("IdIn")) {
                return ((ChargeFormItem) item).getId();
            }
        } else if (item instanceof ChargeFormItemCountDto) {
            if (joinPoint.getSignature().getName().contains("getChargeFormItemCountDtoBySheetIds")) {
                return ((ChargeFormItemCountDto) item).getChargeSheetId();
            }
        } else if (item instanceof RegistrationChargeView) {
            if (joinPoint.getSignature().getName().contains("queryRegistrationChargeInfoBatchByPatientOrderIds")) {
                return ((RegistrationChargeView) item).getPatientOrderId();
            }
        } else if (item instanceof ChargeSheetIdSourceTotalPriceDto) {
            if (joinPoint.getSignature().getName().contains("findChargeSheetSourceTotalPriceBySheetIds")) {
                return ((ChargeSheetIdSourceTotalPriceDto) item).getChargeSheetId();
            }
        }
        return null;
    }
}