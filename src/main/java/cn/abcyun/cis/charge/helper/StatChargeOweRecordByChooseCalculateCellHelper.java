package cn.abcyun.cis.charge.helper;

import cn.abcyun.cis.charge.model.ChargeOweCombineTransactionRecordDetail;
import cn.abcyun.cis.charge.processor.*;
import cn.abcyun.cis.charge.processor.stat.StatRecordByChooseCalculateHelper;
import cn.abcyun.cis.charge.service.dto.ChargeDiscountInfo;
import cn.abcyun.cis.charge.util.JsonUtils;
import cn.abcyun.cis.charge.util.MathUtils;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.function.BinaryOperator;

@Data
@Accessors(chain = true)
public class StatChargeOweRecordByChooseCalculateCellHelper {

    public static void calculateCell(BigDecimal totalOwePrice, BigDecimal transactionRecordPrice, List<StatChargeOweRecordByChooseCalculateCell> cells, int payType, boolean isFirstPay) {
        BigDecimal leftToRecordPrice = transactionRecordPrice;

        //判断是否收完
        if (payType == StatChargeOweRecordProcessor.PayType.PAID) {
            for (StatChargeOweRecordByChooseCalculateCell cell : cells) {
                cell.setToRecordDiscountedPrice(cell.getLeftToRecordDiscountedPrice())
                        .setToRecordTotalCostPrice(cell.getLeftToRecordTotalCostPrice())
                        .setToRecordDiscountPrice(cell.getLeftToRecordDiscountPrice())
                        .setToRecordPromotionDiscountInfo(ChargeTransactionRecordDiscountInfoHelper.generateToRecordChargeTransactionRecordDiscountInfoForLastRecord(cell.getLimitedRecordDiscountInfo(), cell.getRecordedDiscountInfo()));
                if (isFirstPay) {
                    cell.setToRecordDeductTotalPrice(cell.getLimitedDeductTotalPrice())
                            .setToRecordDeductCount(cell.getLimitedDeductCount())
                            .setToRecordDeductedUnitAdjustmentFee(cell.getLimitedDeductedUnitAdjustmentFee())
                            .setToRecordDeductTotalCostPrice(cell.getLimitedDeductTotalCostPrice())
                            .setToRecordDeductDiscountInfos(cell.getLimitedDeductDiscountInfo())
                            .setToRecordVerifyDiscountInfos(cell.getLimitedVerifyDeductDiscountInfo())
                            .setDeductInfo(cell.getLimitedRecordDeductInfoInfo());


                }
            }
        } else {
            BinaryOperator<BigDecimal> calculatePriceFunction = (limitPrice, price) -> MathUtils.min(limitPrice, price.multiply(transactionRecordPrice).divide(totalOwePrice, 2, RoundingMode.DOWN));
            for (StatChargeOweRecordByChooseCalculateCell cell : cells) {
                BigDecimal limitedDiscountedPrice = cell.getLimitedDiscountedPrice();
                cell.setToRecordDiscountedPrice(MathUtils.min(leftToRecordPrice, calculatePriceFunction.apply(cell.getLeftToRecordDiscountedPrice(), limitedDiscountedPrice)))
                        .setToRecordTotalCostPrice(MathUtils.min(cell.getLeftToRecordTotalCostPrice(), cell.getLimitedTotalCostPrice().subtract(cell.getLimitedDeductTotalCostPrice()).multiply(transactionRecordPrice).divide(totalOwePrice, 4, RoundingMode.DOWN)))
                        .setToRecordDiscountPrice(calculatePriceFunction.apply(cell.getLeftToRecordDiscountPrice().abs(), (cell.getLimitedDiscountPrice().subtract(cell.getLimitedDeductTotalPrice().negate())).abs())
                                .negate()
                        );
                if (MathUtils.wrapBigDecimalCompare(cell.getToRecordDiscountedPrice(), BigDecimal.ZERO) == 0) {
                    if (MathUtils.wrapBigDecimalCompare(cell.getToRecordDiscountPrice(), BigDecimal.ZERO) != 0) {
                        cell.setToRecordPromotionDiscountInfo(ChargeTransactionRecordDiscountInfoHelper.generateToRecordChargeTransactionRecordDiscountInfo(cell.getLimitedRecordDiscountInfo(),
                                cell.getToRecordDiscountPrice(),
                                cell.getToRecordDiscountPrice(),
                                cell.getLimitedDiscountPrice().subtract(cell.getLimitedDeductTotalPrice()),
                                false)
                        );
                    }
                } else {
                    cell.setToRecordPromotionDiscountInfo(ChargeTransactionRecordDiscountInfoHelper.generateToRecordChargeTransactionRecordDiscountInfo(cell.getLimitedRecordDiscountInfo(),
                            cell.getToRecordDiscountPrice(),
                            cell.getToRecordDiscountedPrice(),
                            cell.getLimitedDiscountedPrice(),
                            false));
                }

                if (isFirstPay) {
                    cell.setToRecordDeductTotalPrice(cell.getLeftToRecordDeductTotalPrice())
                            .setToRecordDeductCount(cell.getLeftToRecordDeductCount())
                            .setToRecordDeductTotalCostPrice(cell.getLeftToRecordDeductTotalCostPrice())
                            .setToRecordVerifyDiscountInfos(cell.getLimitedVerifyDeductDiscountInfo())
                            .setToRecordDeductDiscountInfos(cell.getLimitedDeductDiscountInfo())
                            .setDeductInfo(cell.getLimitedRecordDeductInfoInfo());
                }

                leftToRecordPrice = leftToRecordPrice.subtract(cell.getToRecordDiscountedPrice());
            }

            //精度问题处理
            if (leftToRecordPrice.compareTo(BigDecimal.ZERO) > 0) {
                for (StatChargeOweRecordByChooseCalculateCell cell : cells) {

                    if (leftToRecordPrice.compareTo(BigDecimal.ZERO) <= 0) {
                        break;
                    }

                    BigDecimal fractionPrice = MathUtils.min(leftToRecordPrice, cell.getLeftToRecordDiscountedPrice().subtract(cell.getToRecordDiscountedPrice()));
                    cell.setToRecordDiscountedPrice(cell.getToRecordDiscountedPrice().add(fractionPrice));
                    leftToRecordPrice = leftToRecordPrice.subtract(fractionPrice);
                }
            }
        }
    }


    public static void calculateParentCell(List<StatChargeOweRecordByChooseCalculateCell> parentCells, int payType, boolean needDealClinicShebaoCodeMatchCostPrice) {

        if (CollectionUtils.isEmpty(parentCells)) {
            return;
        }

        for (StatChargeOweRecordByChooseCalculateCell parentCell : parentCells) {

            BigDecimal composeTotalPrice = BigDecimal.ZERO;
            BigDecimal composeTotalCostPrice = BigDecimal.ZERO;
            BigDecimal composeDiscountPrice = BigDecimal.ZERO;
            BigDecimal composeDeductedPrice = BigDecimal.ZERO;

            List<ChargeDiscountInfo> childrenDiscountInfos = new ArrayList<>();

            for (ChargeOweCombineTransactionRecordDetail child : parentCell.getThisTimeRecordDetails()) {

                composeTotalPrice = MathUtils.wrapBigDecimalAdd(composeTotalPrice, child.getTotalPrice());

                composeTotalCostPrice = MathUtils.wrapBigDecimalAdd(composeTotalCostPrice, child.getTotalCostPrice());
                //折扣金额
                composeDiscountPrice = MathUtils.wrapBigDecimalAdd(composeDiscountPrice, child.getDiscountPrice());
                //抵扣金额
                composeDeductedPrice = MathUtils.wrapBigDecimalAdd(composeDeductedPrice, child.getDeductTotalPrice());

                Optional.ofNullable(child.getDiscountInfo())
                        .ifPresent(discountInfo -> childrenDiscountInfos.add(JsonUtils.readValue(JsonUtils.dump(discountInfo), ChargeDiscountInfo.class)));
            }

            parentCell.setToRecordDiscountedPrice(MathUtils.wrapBigDecimalAdd(composeTotalPrice, composeDiscountPrice));

            //套餐的折扣价
            parentCell.setToRecordDiscountPrice(composeDiscountPrice);

            parentCell.setToRecordDeductCount(composeDeductedPrice);

            //诊所管家中治疗理疗检查检验其他费用有多个对码时，由于使用了医嘱费用项的结构，但是又没有遵循子项的成本和 = 母项的成本，这种母项数据需要单独对成本金额处理
            if (StatRecordByChooseCalculateHelper.canDealClinicShebaoCodeMatchCostPriceItem(needDealClinicShebaoCodeMatchCostPrice, parentCell.getProductType())) {
                composeTotalCostPrice = calculateShebaoCodeMatchCostPrice(parentCell, payType);
            }

            //套餐的成本价
            parentCell.setToRecordTotalCostPrice(composeTotalCostPrice);

            parentCell.setToRecordPromotionDiscountInfo(ChargeTransactionRecordDiscountInfoHelper.mergeChargeTransactionRecordDiscountInfo(childrenDiscountInfos));

        }

    }

    private static BigDecimal calculateShebaoCodeMatchCostPrice(StatChargeOweRecordByChooseCalculateCell parentCell, int payType) {
        BigDecimal toRecordTotalPrice = BigDecimal.ZERO;
        if (payType == StatChargeOweRecordProcessor.PayType.PARTED_PAID) {
            if (MathUtils.wrapBigDecimalCompare(parentCell.getLimitedDiscountedPrice(), BigDecimal.ZERO) > 0) {
                toRecordTotalPrice = parentCell.getLimitedTotalCostPrice().multiply(parentCell.getToRecordDiscountedPrice()).divide(parentCell.getLimitedDiscountedPrice(), 2, RoundingMode.DOWN);
            }
        } else if (payType == StatRecordProcessor.PayType.PAID) {
            toRecordTotalPrice = parentCell.getLimitedTotalCostPrice().subtract(parentCell.getRecordedTotalCostPrice());
        }

        return toRecordTotalPrice;
    }
}
