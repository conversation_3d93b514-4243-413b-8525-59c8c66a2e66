package cn.abcyun.cis.charge.helper;

import cn.abcyun.cis.charge.service.dto.ChargeDiscountInfo;
import cn.abcyun.cis.charge.util.JsonUtils;
import cn.abcyun.cis.charge.util.ListUtils;
import cn.abcyun.cis.charge.util.MathUtils;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Slf4j
public class ChargeTransactionRecordDiscountInfoHelper {

    public static ChargeDiscountInfo negateChargeTransactionRecordDiscountInfo(ChargeDiscountInfo discountInfo) {
        if (Objects.isNull(discountInfo)) {
            return null;
        }

        discountInfo.setAdjustmentFee(MathUtils.wrapBigDecimalNegateOrZero(discountInfo.getAdjustmentFee()))
                .setPatientPointPromotionFee(MathUtils.wrapBigDecimalNegateOrZero(discountInfo.getPatientPointPromotionFee()))
                .setUnitAdjustmentFee(MathUtils.wrapBigDecimalNegateOrZero(discountInfo.getUnitAdjustmentFee()))
                .setDeductedUnitAdjustmentFee(MathUtils.wrapBigDecimalNegateOrZero(discountInfo.getDeductedUnitAdjustmentFee()))
                .setUnitAdjustmentFeeIgnoreDeduct(MathUtils.wrapBigDecimalNegateOrZero(discountInfo.getUnitAdjustmentFeeIgnoreDeduct()))
                .setLimitFee(MathUtils.wrapBigDecimalNegateOrZero(discountInfo.getLimitFee()));

        discountInfo.getDeductDiscountInfos()
                .forEach(deductDiscountInfo -> {
                    deductDiscountInfo.setDiscountPrice(MathUtils.wrapBigDecimalNegateOrZero(deductDiscountInfo.getDiscountPrice()));
                    deductDiscountInfo.setDeductedCount(MathUtils.wrapBigDecimalNegateOrZero(deductDiscountInfo.getDeductedCount()));
                });
        discountInfo.getDiscountPromotionInfos()
                .forEach(discountPromotionInfo -> discountPromotionInfo.setDiscountPrice(MathUtils.wrapBigDecimalNegateOrZero(discountPromotionInfo.getDiscountPrice())));
        discountInfo.getGiftRulePromotionInfos()
                .forEach(giftRulePromotionInfo -> giftRulePromotionInfo.setDiscountPrice(MathUtils.wrapBigDecimalNegateOrZero(giftRulePromotionInfo.getDiscountPrice())));
        discountInfo.getCouponInfos()
                .forEach(couponInfo -> couponInfo.setDiscountPrice(MathUtils.wrapBigDecimalNegateOrZero(couponInfo.getDiscountPrice())));

        return discountInfo;
    }

    public static ChargeDiscountInfo mergeChargeTransactionRecordDiscountInfo(List<ChargeDiscountInfo> recordedChargeTransactionRecordDiscountInfos) {
        if (CollectionUtils.isEmpty(recordedChargeTransactionRecordDiscountInfos)) {
            return null;
        }

        Map<Integer, ChargeDiscountInfo> chargeTransactionRecordDiscountInfoMap = recordedChargeTransactionRecordDiscountInfos.stream()
                .collect(Collectors.toMap(chargeTransactionRecordDiscountInfo -> 1,
                        chargeTransactionRecordDiscountInfo -> JsonUtils.readValue(JsonUtils.dump(chargeTransactionRecordDiscountInfo), ChargeDiscountInfo.class),
                        (a, b) -> {
                            a.setAdjustmentFee(MathUtils.wrapBigDecimalAdd(a.getAdjustmentFee(), b.getAdjustmentFee()));
                            a.setUnitAdjustmentFee(MathUtils.wrapBigDecimalAdd(a.getUnitAdjustmentFee(), b.getUnitAdjustmentFee()));
                            a.setDeductedUnitAdjustmentFee(MathUtils.wrapBigDecimalAdd(a.getDeductedUnitAdjustmentFee(), b.getDeductedUnitAdjustmentFee()));
                            a.setVerifyUnitAdjustmentFee(MathUtils.wrapBigDecimalAdd(a.getVerifyUnitAdjustmentFee(), b.getVerifyUnitAdjustmentFee()));
                            a.setUnitAdjustmentFeeIgnoreDeduct(MathUtils.wrapBigDecimalAdd(a.getUnitAdjustmentFeeIgnoreDeduct(), b.getUnitAdjustmentFeeIgnoreDeduct()));
                            a.setPatientPointPromotionFee(MathUtils.wrapBigDecimalAdd(a.getPatientPointPromotionFee(), b.getPatientPointPromotionFee()));
                            a.addDeductDiscountInfos(b.getDeductDiscountInfos());
                            a.addVerifyDeductInfos(b.getVerifyDeductInfos());
                            a.addDiscountPromotionInfos(b.getDiscountPromotionInfos());
                            a.addGiftRulePromotionInfos(b.getGiftRulePromotionInfos());
                            a.addCouponInfos(b.getCouponInfos());
                            a.setLimitFee(MathUtils.wrapBigDecimalAdd(a.getLimitFee(), b.getLimitFee()));
                            a.addVerifyDeductInfos(b.getVerifyDeductInfos());
                            a.setListingDiscountFee(MathUtils.wrapBigDecimalAdd(a.getListingDiscountFee(), b.getListingDiscountFee()));
                            return a;
                        }));

        ChargeDiscountInfo discountInfo = chargeTransactionRecordDiscountInfoMap.getOrDefault(1, null);

        if (discountInfo == null) {
            return null;
        }

        discountInfo.setDeductDiscountInfos(discountInfo.getDeductDiscountInfos().stream()
                .collect(Collectors.toMap(ChargeDiscountInfo.DeductDiscountInfo::getId, Function.identity(), (a, b) -> {
                    a.setDiscountPrice(MathUtils.wrapBigDecimalAdd(a.getDiscountPrice(), b.getDiscountPrice()));
                    a.setDeductedCount(MathUtils.wrapBigDecimalAdd(a.getDeductedCount(), b.getDeductedCount()));
                    return a;
                }))
                .values()
                .stream()
                .collect(Collectors.toList())
        );

        discountInfo.setVerifyDeductInfos(discountInfo.getVerifyDeductInfos().stream()
                .collect(Collectors.toMap(ChargeDiscountInfo.VerifyDeductInfo::getId, Function.identity(), (a, b) -> {
                    a.setDiscountPrice(MathUtils.wrapBigDecimalAdd(a.getDiscountPrice(), b.getDiscountPrice()));
                    a.setDeductedCount(MathUtils.wrapBigDecimalAdd(a.getDeductedCount(), b.getDeductedCount()));
                    return a;
                }))
                .values()
                .stream()
                .collect(Collectors.toList())
        );

        discountInfo.setDiscountPromotionInfos(discountInfo.getDiscountPromotionInfos().stream()
                .collect(Collectors.toMap(ChargeDiscountInfo.PromotionInfo::getId, Function.identity(), (a, b) -> {
                    a.setDiscountPrice(MathUtils.wrapBigDecimalAdd(a.getDiscountPrice(), b.getDiscountPrice()));
                    return a;
                }))
                .values()
                .stream()
                .collect(Collectors.toList())
        );

        discountInfo.setGiftRulePromotionInfos(discountInfo.getGiftRulePromotionInfos().stream()
                .collect(Collectors.toMap(ChargeDiscountInfo.PromotionInfo::getId, Function.identity(), (a, b) -> {
                    a.setDiscountPrice(MathUtils.wrapBigDecimalAdd(a.getDiscountPrice(), b.getDiscountPrice()));
                    return a;
                }))
                .values()
                .stream()
                .collect(Collectors.toList())
        );

        discountInfo.setCouponInfos(discountInfo.getCouponInfos().stream()
                .collect(Collectors.toMap(ChargeDiscountInfo.CouponInfo::getId, Function.identity(), (a, b) -> {
                    a.setDiscountPrice(MathUtils.wrapBigDecimalAdd(a.getDiscountPrice(), b.getDiscountPrice()));
                    return a;
                }))
                .values()
                .stream()
                .collect(Collectors.toList())
        );

        return discountInfo;
    }

    /**
     * 为最后一次记录创建折扣对象
     *
     * @param itemPromotionInfo
     * @param historyChargeTransactionRecordDiscountInfo
     * @return
     */
    public static ChargeDiscountInfo generateToRecordChargeTransactionRecordDiscountInfoForLastRecord(ChargeDiscountInfo itemPromotionInfo, ChargeDiscountInfo historyChargeTransactionRecordDiscountInfo) {


        if (itemPromotionInfo == null) {
            return null;
        }
        ChargeDiscountInfo discountInfo = new ChargeDiscountInfo();
        if (historyChargeTransactionRecordDiscountInfo == null) {
            discountInfo.setAdjustmentFee(itemPromotionInfo.getAdjustmentFee())
//                    .setDeductDiscountInfos(itemPromotionInfo.getDeductDiscountInfos())
                    .setUnitAdjustmentFeeIgnoreDeduct(itemPromotionInfo.getUnitAdjustmentFeeIgnoreDeduct())
                    .setUnitAdjustmentFee(itemPromotionInfo.getUnitAdjustmentFee())
                    .setDeductedUnitAdjustmentFee(itemPromotionInfo.getDeductedUnitAdjustmentFee())
                    .setDiscountPromotionInfos(itemPromotionInfo.getDiscountPromotionInfos())
                    .setGiftRulePromotionInfos(itemPromotionInfo.getGiftRulePromotionInfos())
                    .setCouponInfos(Optional.ofNullable(itemPromotionInfo.getCouponInfos()).orElse(new ArrayList<>())
                            .stream()
                            .map(couponInfo -> {
                                ChargeDiscountInfo.CouponInfo thisTimeResult = new ChargeDiscountInfo.CouponInfo();
                                thisTimeResult.setId(couponInfo.getId());
                                thisTimeResult.setName(couponInfo.getName());
                                thisTimeResult.setDiscountPrice(couponInfo.getDiscountPrice());
                                thisTimeResult.setType(couponInfo.getType());
                                return thisTimeResult;
                            }).collect(Collectors.toList())
                    )
                    .setPatientPointPromotionFee(itemPromotionInfo.getPatientPointPromotionFee())
                    .setLimitFee(itemPromotionInfo.getLimitFee())
                    .setListingDiscountFee(itemPromotionInfo.getListingDiscountFee());
            return discountInfo;
        }

        AtomicReference<BigDecimal> historyTotalAdjustmentFee = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> historyUnitAdjustmentFeeIgnoreDeduct = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> historyUnitAdjustmentFee = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> historyDeductedUnitAdjustmentFee = new AtomicReference<>(BigDecimal.ZERO);
        Map<String, ChargeDiscountInfo.DeductDiscountInfo> historyDeductDiscountInfoMap = new HashMap<>();
        Map<String, ChargeDiscountInfo.PromotionInfo> historyDiscountPromotionInfoMap = new HashMap<>();
        Map<String, ChargeDiscountInfo.PromotionInfo> historyGiftRulePromotionInfoMap = new HashMap<>();
        Map<String, ChargeDiscountInfo.PromotionInfo> historyCouponInfoMap = new HashMap<>();
        AtomicReference<BigDecimal> historyTotalPatientPointPromotionFee = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> historyTotalListingDiscountFee = new AtomicReference<>(BigDecimal.ZERO);

        //议价
        historyTotalAdjustmentFee.set(MathUtils.wrapBigDecimalAdd(historyTotalAdjustmentFee.get(), historyChargeTransactionRecordDiscountInfo.getAdjustmentFee()));
        //单项议价
        historyUnitAdjustmentFeeIgnoreDeduct.set(MathUtils.wrapBigDecimalAdd(historyUnitAdjustmentFeeIgnoreDeduct.get(), historyChargeTransactionRecordDiscountInfo.getUnitAdjustmentFeeIgnoreDeduct()));
        historyUnitAdjustmentFee.set(MathUtils.wrapBigDecimalAdd(historyUnitAdjustmentFee.get(), historyChargeTransactionRecordDiscountInfo.getUnitAdjustmentFee()));
        historyDeductedUnitAdjustmentFee.set(MathUtils.wrapBigDecimalAdd(historyDeductedUnitAdjustmentFee.get(), historyChargeTransactionRecordDiscountInfo.getDeductedUnitAdjustmentFee()));
        //挂网价限价
        historyTotalListingDiscountFee.set(MathUtils.wrapBigDecimalAdd(historyTotalListingDiscountFee.get(), historyChargeTransactionRecordDiscountInfo.getListingDiscountFee()));

        //抵扣
//        historyChargeTransactionRecordDiscountInfo.getDeductDiscountInfos().forEach(historyDeductDiscountInfo -> {
//            ChargeDiscountInfo.DeductDiscountInfo deductDiscountInfo = historyDeductDiscountInfoMap.computeIfAbsent(historyDeductDiscountInfo.getId(), key -> {
//                ChargeDiscountInfo.DeductDiscountInfo deductDiscountInfoFlag = new ChargeDiscountInfo.DeductDiscountInfo();
//                deductDiscountInfoFlag.setId(historyDeductDiscountInfo.getId());
//                deductDiscountInfoFlag.setType(historyDeductDiscountInfo.getType());
//                deductDiscountInfoFlag.setPresentId(historyDeductDiscountInfo.getPresentId());
//                return deductDiscountInfoFlag;
//            });
//
//            deductDiscountInfo.setDiscountPrice(MathUtils.wrapBigDecimalAdd(deductDiscountInfo.getDiscountPrice(), historyDeductDiscountInfo.getDiscountPrice()));
//            deductDiscountInfo.setDeductedCount(MathUtils.wrapBigDecimalAdd(deductDiscountInfo.getDeductedCount(), historyDeductDiscountInfo.getDeductedCount()));
//        });

        //折扣
        historyChargeTransactionRecordDiscountInfo.getDiscountPromotionInfos().forEach(historyDiscountPromotionInfo -> {
            ChargeDiscountInfo.PromotionInfo promotionInfo = historyDiscountPromotionInfoMap.computeIfAbsent(historyDiscountPromotionInfo.getId(), key -> {
                ChargeDiscountInfo.PromotionInfo promotionInfoFlag = new ChargeDiscountInfo.PromotionInfo();
                promotionInfoFlag.setId(historyDiscountPromotionInfo.getId());
                promotionInfoFlag.setName(historyDiscountPromotionInfo.getName());
                promotionInfoFlag.setType(historyDiscountPromotionInfo.getType());
                return promotionInfoFlag;
            });

            promotionInfo.setDiscountPrice(MathUtils.wrapBigDecimalAdd(promotionInfo.getDiscountPrice(), historyDiscountPromotionInfo.getDiscountPrice()));
        });

        //满减满赠
        historyChargeTransactionRecordDiscountInfo.getGiftRulePromotionInfos().forEach(historyGiftRulePromotionInfo -> {
            ChargeDiscountInfo.PromotionInfo promotionInfo = historyGiftRulePromotionInfoMap.computeIfAbsent(historyGiftRulePromotionInfo.getId(), key -> {
                ChargeDiscountInfo.PromotionInfo promotionInfoFlag = new ChargeDiscountInfo.PromotionInfo();
                promotionInfoFlag.setId(historyGiftRulePromotionInfo.getId());
                promotionInfoFlag.setName(historyGiftRulePromotionInfo.getName());
                promotionInfoFlag.setType(historyGiftRulePromotionInfo.getType());
                return promotionInfoFlag;
            });
            promotionInfo.setDiscountPrice(MathUtils.wrapBigDecimalAdd(promotionInfo.getDiscountPrice(), historyGiftRulePromotionInfo.getDiscountPrice()));
        });

        //优惠券
        historyChargeTransactionRecordDiscountInfo.getCouponInfos().forEach(historyCouponInfo -> {
            ChargeDiscountInfo.PromotionInfo promotionInfo = historyCouponInfoMap.computeIfAbsent(historyCouponInfo.getId(), key -> {
                ChargeDiscountInfo.CouponInfo couponInfoFlag = new ChargeDiscountInfo.CouponInfo();
                couponInfoFlag.setId(historyCouponInfo.getId());
                couponInfoFlag.setName(historyCouponInfo.getName());
                couponInfoFlag.setType(historyCouponInfo.getType());
                return couponInfoFlag;
            });
            promotionInfo.setDiscountPrice(MathUtils.wrapBigDecimalAdd(promotionInfo.getDiscountPrice(), historyCouponInfo.getDiscountPrice()));
        });

        //积分
        historyTotalPatientPointPromotionFee.set(MathUtils.wrapBigDecimalAdd(historyTotalPatientPointPromotionFee.get(), historyChargeTransactionRecordDiscountInfo.getPatientPointPromotionFee()));


        //处理剩余的折扣或议价等
        if (itemPromotionInfo.getAdjustmentFee() != null) {
            if (MathUtils.wrapBigDecimalCompare(itemPromotionInfo.getAdjustmentFee(), BigDecimal.ZERO) > 0) {
                discountInfo.setAdjustmentFee(MathUtils.max(BigDecimal.ZERO, MathUtils.wrapBigDecimalSubtract(itemPromotionInfo.getAdjustmentFee(), historyTotalAdjustmentFee.get())));
            } else {
                discountInfo.setAdjustmentFee(MathUtils.min(BigDecimal.ZERO, MathUtils.wrapBigDecimalSubtract(itemPromotionInfo.getAdjustmentFee(), historyTotalAdjustmentFee.get())));
            }
        }

        discountInfo.setUnitAdjustmentFeeIgnoreDeduct(MathUtils.wrapBigDecimalSubtract(itemPromotionInfo.getUnitAdjustmentFeeIgnoreDeduct(), historyUnitAdjustmentFeeIgnoreDeduct.get()));
        discountInfo.setUnitAdjustmentFee(MathUtils.wrapBigDecimalSubtract(itemPromotionInfo.getUnitAdjustmentFee(), historyUnitAdjustmentFee.get()));
        discountInfo.setDeductedUnitAdjustmentFee(MathUtils.wrapBigDecimalSubtract(itemPromotionInfo.getDeductedUnitAdjustmentFee(), historyDeductedUnitAdjustmentFee.get()));
        discountInfo.setLimitFee(MathUtils.wrapBigDecimalSubtract(itemPromotionInfo.getLimitFee(), historyChargeTransactionRecordDiscountInfo.getLimitFee()));

//        if (CollectionUtils.isNotEmpty(itemPromotionInfo.getDeductDiscountInfos())) {
//            discountInfo.setDeductDiscountInfos(itemPromotionInfo.getDeductDiscountInfos()
//                    .stream()
//                    .map(deductDiscountInfo -> {
//                        ChargeDiscountInfo.DeductDiscountInfo historyDeductDiscountInfo = historyDeductDiscountInfoMap.getOrDefault(deductDiscountInfo.getId(), null);
//
//                        ChargeDiscountInfo.DeductDiscountInfo thisTimeResult = new ChargeDiscountInfo.DeductDiscountInfo();
//                        BeanUtils.copyProperties(deductDiscountInfo, thisTimeResult);
//
//                        if (historyDeductDiscountInfo == null) {
//                            return deductDiscountInfo;
//                        }
//                        thisTimeResult.setDeductedCount(MathUtils.max(BigDecimal.ZERO, MathUtils.wrapBigDecimalSubtract(deductDiscountInfo.getDeductedCount(), historyDeductDiscountInfo.getDeductedCount())));
//                        thisTimeResult.setDiscountPrice(MathUtils.min(BigDecimal.ZERO, MathUtils.wrapBigDecimalSubtract(deductDiscountInfo.getDiscountPrice(), historyDeductDiscountInfo.getDiscountPrice())));
//                        return thisTimeResult;
//                    })
//                    .collect(Collectors.toList())
//            );
//        }

        if (CollectionUtils.isNotEmpty(itemPromotionInfo.getDiscountPromotionInfos())) {
            discountInfo.setDiscountPromotionInfos(itemPromotionInfo.getDiscountPromotionInfos()
                    .stream()
                    .map(promotionInfo -> {
                        ChargeDiscountInfo.PromotionInfo thisTimeResult = new ChargeDiscountInfo.PromotionInfo();
                        BeanUtils.copyProperties(promotionInfo, thisTimeResult);
                        ChargeDiscountInfo.PromotionInfo historyPromotionInfo = historyDiscountPromotionInfoMap.getOrDefault(promotionInfo.getId(), null);

                        if (historyPromotionInfo == null) {
                            return thisTimeResult;
                        }

                        thisTimeResult.setDiscountPrice(MathUtils.min(BigDecimal.ZERO, MathUtils.wrapBigDecimalSubtract(promotionInfo.getDiscountPrice(), historyPromotionInfo.getDiscountPrice())));
                        return thisTimeResult;
                    }).collect(Collectors.toList())
            );
        }

        if (CollectionUtils.isNotEmpty(itemPromotionInfo.getGiftRulePromotionInfos())) {
            discountInfo.setGiftRulePromotionInfos(itemPromotionInfo.getGiftRulePromotionInfos()
                    .stream()
                    .map(promotionInfo -> {
                        ChargeDiscountInfo.PromotionInfo thisTimeResult = new ChargeDiscountInfo.PromotionInfo();
                        BeanUtils.copyProperties(promotionInfo, thisTimeResult);
                        ChargeDiscountInfo.PromotionInfo historyPromotionInfo = historyGiftRulePromotionInfoMap.getOrDefault(promotionInfo.getId(), null);

                        if (historyPromotionInfo == null) {
                            return thisTimeResult;
                        }

                        thisTimeResult.setDiscountPrice(MathUtils.min(BigDecimal.ZERO, MathUtils.wrapBigDecimalSubtract(promotionInfo.getDiscountPrice(), historyPromotionInfo.getDiscountPrice())));
                        return thisTimeResult;
                    }).collect(Collectors.toList())
            );
        }

        if (CollectionUtils.isNotEmpty(itemPromotionInfo.getCouponInfos())) {
            discountInfo.setCouponInfos(itemPromotionInfo.getCouponInfos()
                    .stream()
                    .map(couponInfo -> {
                        ChargeDiscountInfo.CouponInfo thisTimeResult = new ChargeDiscountInfo.CouponInfo();
                        BeanUtils.copyProperties(couponInfo, thisTimeResult, "couponIds");

                        ChargeDiscountInfo.PromotionInfo historyCouponInfo = historyCouponInfoMap.getOrDefault(couponInfo.getId(), null);

                        if (historyCouponInfo == null) {
                            return thisTimeResult;
                        }

                        thisTimeResult.setDiscountPrice(MathUtils.min(BigDecimal.ZERO, MathUtils.wrapBigDecimalSubtract(couponInfo.getDiscountPrice(), historyCouponInfo.getDiscountPrice())));
                        return thisTimeResult;
                    }).collect(Collectors.toList())
            );
        }

        if (itemPromotionInfo.getPatientPointPromotionFee() != null) {
            discountInfo.setPatientPointPromotionFee(MathUtils.min(BigDecimal.ZERO, MathUtils.wrapBigDecimalSubtract(itemPromotionInfo.getPatientPointPromotionFee(), historyTotalPatientPointPromotionFee.get())));
        }

        if (itemPromotionInfo.getListingDiscountFee() != null) {
            discountInfo.setListingDiscountFee(MathUtils.min(BigDecimal.ZERO, MathUtils.wrapBigDecimalSubtract(itemPromotionInfo.getListingDiscountFee(), historyTotalListingDiscountFee.get())));
        }

        return discountInfo;
    }

    public static ChargeDiscountInfo generateToRecordChargeTransactionRecordDiscountInfo(ChargeDiscountInfo itemPromotionInfo, BigDecimal toRecordDiscountPrice, BigDecimal rateUnit, BigDecimal rateTotal, boolean containUnitAdjustmentFee) {

        if (itemPromotionInfo == null) {
            return null;
        }

        if (Objects.isNull(rateUnit) || Objects.isNull(rateTotal)) {
            throw new IllegalArgumentException("rateUnit or rateTotal cannot be null");
        }

        if (rateTotal.compareTo(BigDecimal.ZERO) == 0) {
            throw new IllegalArgumentException("rateTotal不能为0");
        }

//        if (MathUtils.wrapBigDecimalCompare(toRecordDiscountPrice, BigDecimal.ZERO) == 0) {
//            return null;
//        }

        Function<BigDecimal, BigDecimal> calculateFunction = (source) -> calculateByRateAndSetScaleTwoDown(source, rateUnit, rateTotal);

        ChargeDiscountInfo discountInfo = new ChargeDiscountInfo();

        if (itemPromotionInfo.getAdjustmentFee() != null) {
            discountInfo.setAdjustmentFee(calculateFunction.apply(itemPromotionInfo.getAdjustmentFee()));
        }
        if (itemPromotionInfo.getUnitAdjustmentFeeIgnoreDeduct() != null) {
            discountInfo.setUnitAdjustmentFeeIgnoreDeduct(calculateFunction.apply(itemPromotionInfo.getUnitAdjustmentFeeIgnoreDeduct()));
        }

        if (itemPromotionInfo.getLimitFee() != null) {
            discountInfo.setLimitFee(calculateFunction.apply(itemPromotionInfo.getLimitFee()));
        }

        if (itemPromotionInfo.getListingDiscountFee() != null) {
            discountInfo.setListingDiscountFee(calculateFunction.apply(itemPromotionInfo.getListingDiscountFee()));
        }


        if (CollectionUtils.isNotEmpty(itemPromotionInfo.getDiscountPromotionInfos())) {
            discountInfo.setDiscountPromotionInfos(itemPromotionInfo.getDiscountPromotionInfos().stream()
                    .map(promotionInfo -> {
                        ChargeDiscountInfo.PromotionInfo thisTimeResult = new ChargeDiscountInfo.PromotionInfo();
                        BeanUtils.copyProperties(promotionInfo, thisTimeResult);
                        thisTimeResult.setDiscountPrice(calculateFunction.apply(promotionInfo.getDiscountPrice()));
                        return thisTimeResult;
                    }).collect(Collectors.toList())
            );
        }

        if (CollectionUtils.isNotEmpty(itemPromotionInfo.getGiftRulePromotionInfos())) {
            discountInfo.setGiftRulePromotionInfos(itemPromotionInfo.getGiftRulePromotionInfos().stream()
                    .map(promotionInfo -> {
                        ChargeDiscountInfo.PromotionInfo thisTimeResult = new ChargeDiscountInfo.PromotionInfo();
                        BeanUtils.copyProperties(promotionInfo, thisTimeResult);
                        thisTimeResult.setDiscountPrice(calculateFunction.apply(promotionInfo.getDiscountPrice()));
                        return thisTimeResult;
                    }).collect(Collectors.toList())
            );
        }

        if (CollectionUtils.isNotEmpty(itemPromotionInfo.getCouponInfos())) {
            discountInfo.setCouponInfos(itemPromotionInfo.getCouponInfos().stream()
                    .map(couponInfo -> {
                        ChargeDiscountInfo.CouponInfo thisTimeResult = new ChargeDiscountInfo.CouponInfo();
                        BeanUtils.copyProperties(couponInfo, thisTimeResult, "couponIds");
                        thisTimeResult.setDiscountPrice(calculateFunction.apply(couponInfo.getDiscountPrice()));
                        return thisTimeResult;
                    }).collect(Collectors.toList())
            );
        }

        if (itemPromotionInfo.getPatientPointPromotionFee() != null) {

            discountInfo.setPatientPointPromotionFee(calculateFunction.apply(itemPromotionInfo.getPatientPointPromotionFee()));
        }

        BigDecimal totalDiscountPrice = discountInfo.getTotalDiscountPrice(containUnitAdjustmentFee);
        //比较本次摊出来的折扣和toRecordDiscountPrice是否一致，如果不一致，要处理零头的问题
        if (MathUtils.wrapBigDecimalCompare(totalDiscountPrice, toRecordDiscountPrice) > 0) {

            BigDecimal leftToRecordDiscountPrice = MathUtils.wrapBigDecimalSubtract(toRecordDiscountPrice, totalDiscountPrice);

            dealLeftToRecordDiscountPrice(discountInfo, itemPromotionInfo, leftToRecordDiscountPrice);

        }

        return discountInfo;
    }

    private static BigDecimal dealLeftToRecordDiscountPrice(ChargeDiscountInfo discountInfo, ChargeDiscountInfo itemPromotionInfo, BigDecimal leftToRecordDiscountPrice) {

        //从议价开始算，只有议价减价才处理
        if (MathUtils.wrapBigDecimalCompare(discountInfo.getAdjustmentFee(), BigDecimal.ZERO) <= 0) {
            BigDecimal leftAdjustmentFee = MathUtils.max(leftToRecordDiscountPrice, MathUtils.wrapBigDecimalSubtract(itemPromotionInfo.getAdjustmentFee(), discountInfo.getAdjustmentFee()));
            discountInfo.setAdjustmentFee(MathUtils.wrapBigDecimalAdd(discountInfo.getAdjustmentFee(), leftAdjustmentFee));

            leftToRecordDiscountPrice = leftToRecordDiscountPrice.subtract(leftAdjustmentFee);
        }


        if (MathUtils.wrapBigDecimalCompare(leftToRecordDiscountPrice, BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

//        if (CollectionUtils.isNotEmpty(discountInfo.getDeductDiscountInfos())) {
//            Map<String, ChargeDiscountInfo.DeductDiscountInfo> deductDiscountInfoMap = ListUtils.toMap(itemPromotionInfo.getDeductDiscountInfos(), ChargeDiscountInfo.DeductDiscountInfo::getId);
//            for (ChargeDiscountInfo.DeductDiscountInfo promotionInfo : discountInfo.getDeductDiscountInfos()) {
//                ChargeDiscountInfo.DeductDiscountInfo originalPromotionInfo = deductDiscountInfoMap.getOrDefault(promotionInfo.getId(), null);
//
//                if (originalPromotionInfo == null) {
//                    continue;
//                }
//                BigDecimal leftDiscountPrice = MathUtils.max(leftToRecordDiscountPrice, MathUtils.wrapBigDecimalSubtract(originalPromotionInfo.getDiscountPrice(), promotionInfo.getDiscountPrice()));
//
//                promotionInfo.setDiscountPrice(MathUtils.wrapBigDecimalAdd(promotionInfo.getDiscountPrice(), leftDiscountPrice));
//
//                leftToRecordDiscountPrice = leftToRecordDiscountPrice.subtract(leftDiscountPrice);
//
//                if (MathUtils.wrapBigDecimalCompare(leftToRecordDiscountPrice, BigDecimal.ZERO) == 0) {
//                    break;
//                }
//            }
//        }
//
//        if (MathUtils.wrapBigDecimalCompare(leftToRecordDiscountPrice, BigDecimal.ZERO) == 0) {
//            return BigDecimal.ZERO;
//        }

        if (CollectionUtils.isNotEmpty(discountInfo.getDiscountPromotionInfos())) {
            Map<String, ChargeDiscountInfo.PromotionInfo> discountPromotionInfoMap = ListUtils.toMap(itemPromotionInfo.getDiscountPromotionInfos(), ChargeDiscountInfo.PromotionInfo::getId);
            for (ChargeDiscountInfo.PromotionInfo promotionInfo : discountInfo.getDiscountPromotionInfos()) {
                ChargeDiscountInfo.PromotionInfo originalPromotionInfo = discountPromotionInfoMap.getOrDefault(promotionInfo.getId(), null);

                if (originalPromotionInfo == null) {
                    continue;
                }
                BigDecimal leftDiscountPrice = MathUtils.max(leftToRecordDiscountPrice, MathUtils.wrapBigDecimalSubtract(originalPromotionInfo.getDiscountPrice(), promotionInfo.getDiscountPrice()));

                promotionInfo.setDiscountPrice(MathUtils.wrapBigDecimalAdd(promotionInfo.getDiscountPrice(), leftDiscountPrice));

                leftToRecordDiscountPrice = leftToRecordDiscountPrice.subtract(leftDiscountPrice);

                if (MathUtils.wrapBigDecimalCompare(leftToRecordDiscountPrice, BigDecimal.ZERO) == 0) {
                    break;
                }
            }
        }

        if (MathUtils.wrapBigDecimalCompare(leftToRecordDiscountPrice, BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        if (CollectionUtils.isNotEmpty(discountInfo.getGiftRulePromotionInfos())) {
            Map<String, ChargeDiscountInfo.PromotionInfo> giftRulePromotionInfoMap = ListUtils.toMap(itemPromotionInfo.getGiftRulePromotionInfos(), ChargeDiscountInfo.PromotionInfo::getId);
            for (ChargeDiscountInfo.PromotionInfo promotionInfo : discountInfo.getGiftRulePromotionInfos()) {
                ChargeDiscountInfo.PromotionInfo originalPromotionInfo = giftRulePromotionInfoMap.getOrDefault(promotionInfo.getId(), null);

                if (originalPromotionInfo == null) {
                    continue;
                }
                BigDecimal leftDiscountPrice = MathUtils.max(leftToRecordDiscountPrice, MathUtils.wrapBigDecimalSubtract(originalPromotionInfo.getDiscountPrice(), promotionInfo.getDiscountPrice()));

                promotionInfo.setDiscountPrice(MathUtils.wrapBigDecimalAdd(promotionInfo.getDiscountPrice(), leftDiscountPrice));

                leftToRecordDiscountPrice = leftToRecordDiscountPrice.subtract(leftDiscountPrice);

                if (MathUtils.wrapBigDecimalCompare(leftToRecordDiscountPrice, BigDecimal.ZERO) == 0) {
                    break;
                }
            }
        }

        if (MathUtils.wrapBigDecimalCompare(leftToRecordDiscountPrice, BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        if (CollectionUtils.isNotEmpty(discountInfo.getCouponInfos())) {
            Map<String, ChargeDiscountInfo.CouponInfo> couponInfoMap = ListUtils.toMap(itemPromotionInfo.getCouponInfos(), ChargeDiscountInfo.CouponInfo::getId);
            for (ChargeDiscountInfo.PromotionInfo promotionInfo : discountInfo.getCouponInfos()) {
                ChargeDiscountInfo.CouponInfo originalPromotionInfo = couponInfoMap.getOrDefault(promotionInfo.getId(), null);

                if (originalPromotionInfo == null) {
                    continue;
                }
                BigDecimal leftDiscountPrice = MathUtils.max(leftToRecordDiscountPrice, MathUtils.wrapBigDecimalSubtract(originalPromotionInfo.getDiscountPrice(), promotionInfo.getDiscountPrice()));

                promotionInfo.setDiscountPrice(MathUtils.wrapBigDecimalAdd(promotionInfo.getDiscountPrice(), leftDiscountPrice));

                leftToRecordDiscountPrice = leftToRecordDiscountPrice.subtract(leftDiscountPrice);

                if (MathUtils.wrapBigDecimalCompare(leftToRecordDiscountPrice, BigDecimal.ZERO) == 0) {
                    break;
                }
            }
        }

        if (MathUtils.wrapBigDecimalCompare(leftToRecordDiscountPrice, BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal leftPatientPointPromotionFee = MathUtils.max(leftToRecordDiscountPrice, MathUtils.wrapBigDecimalSubtract(itemPromotionInfo.getPatientPointPromotionFee(), discountInfo.getPatientPointPromotionFee()));

        discountInfo.setPatientPointPromotionFee(MathUtils.wrapBigDecimalAdd(discountInfo.getPatientPointPromotionFee(), leftPatientPointPromotionFee));
        leftToRecordDiscountPrice = leftToRecordDiscountPrice.subtract(leftPatientPointPromotionFee);

        return leftToRecordDiscountPrice;
    }


    private static BigDecimal calculateByRateAndSetScaleTwoDown(BigDecimal source, BigDecimal rateUnit, BigDecimal rateTotal) {
        return MathUtils.wrapBigDecimalOrZero(source).multiply(rateUnit).divide(rateTotal, 2, RoundingMode.DOWN);
    }

    private static BigDecimal calculateByRateAndSetScaleTwoUp(BigDecimal source, BigDecimal rateUnit, BigDecimal rateTotal) {
        return MathUtils.wrapBigDecimalOrZero(source).multiply(rateUnit).divide(rateTotal, 2, RoundingMode.UP);
    }

    public static ChargeDiscountInfo mergeLeftPromotionInfo(ChargeDiscountInfo sourcePromotionInfo, ChargeDiscountInfo recordedPromotionInfo) {

        if (sourcePromotionInfo == null || recordedPromotionInfo == null) {
            return sourcePromotionInfo;
        }

        ChargeDiscountInfo leftPromotionInfo = new ChargeDiscountInfo();
        leftPromotionInfo.setAdjustmentFee(MathUtils.wrapBigDecimalSubtract(sourcePromotionInfo.getAdjustmentFee(), recordedPromotionInfo.getAdjustmentFee()));
        leftPromotionInfo.setUnitAdjustmentFee(MathUtils.wrapBigDecimalSubtract(sourcePromotionInfo.getUnitAdjustmentFee(), recordedPromotionInfo.getUnitAdjustmentFee()));
        leftPromotionInfo.setDeductedUnitAdjustmentFee(MathUtils.wrapBigDecimalSubtract(sourcePromotionInfo.getDeductedUnitAdjustmentFee(), recordedPromotionInfo.getDeductedUnitAdjustmentFee()));
        leftPromotionInfo.setUnitAdjustmentFeeIgnoreDeduct(MathUtils.wrapBigDecimalSubtract(sourcePromotionInfo.getUnitAdjustmentFeeIgnoreDeduct(), recordedPromotionInfo.getUnitAdjustmentFeeIgnoreDeduct()));
        leftPromotionInfo.setPatientPointPromotionFee(MathUtils.wrapBigDecimalSubtract(sourcePromotionInfo.getPatientPointPromotionFee(), recordedPromotionInfo.getPatientPointPromotionFee()));
        leftPromotionInfo.setLimitFee(MathUtils.wrapBigDecimalSubtract(sourcePromotionInfo.getLimitFee(), recordedPromotionInfo.getLimitFee()));
        leftPromotionInfo.setListingDiscountFee(MathUtils.wrapBigDecimalSubtract(sourcePromotionInfo.getListingDiscountFee(), recordedPromotionInfo.getListingDiscountFee()));

        Map<String, BigDecimal> promotionIdDiscountPromtionMap = recordedPromotionInfo.getDiscountPromotionInfos()
                .stream()
                .collect(Collectors.toMap(ChargeDiscountInfo.PromotionInfo::getId, ChargeDiscountInfo.PromotionInfo::getDiscountPrice, MathUtils::wrapBigDecimalAdd));
        leftPromotionInfo.setDiscountPromotionInfos(sourcePromotionInfo.getDiscountPromotionInfos().stream()
                .map(source -> {
                    ChargeDiscountInfo.PromotionInfo left = new ChargeDiscountInfo.PromotionInfo();
                    BeanUtils.copyProperties(source, left);
                    left.setDiscountPrice(MathUtils.wrapBigDecimalSubtract(source.getDiscountPrice(), promotionIdDiscountPromtionMap.getOrDefault(source.getId(), BigDecimal.ZERO)));
                    return left;
                }).collect(Collectors.toList())
        );


        Map<String, BigDecimal> promotionIdGiftRulePromtionMap = recordedPromotionInfo.getGiftRulePromotionInfos()
                .stream()
                .collect(Collectors.toMap(ChargeDiscountInfo.PromotionInfo::getId, ChargeDiscountInfo.PromotionInfo::getDiscountPrice, MathUtils::wrapBigDecimalAdd));
        leftPromotionInfo.setGiftRulePromotionInfos(sourcePromotionInfo.getGiftRulePromotionInfos().stream()
                .map(source -> {
                    ChargeDiscountInfo.PromotionInfo left = new ChargeDiscountInfo.PromotionInfo();
                    BeanUtils.copyProperties(source, left);
                    left.setDiscountPrice(MathUtils.wrapBigDecimalSubtract(source.getDiscountPrice(), promotionIdGiftRulePromtionMap.getOrDefault(source.getId(), BigDecimal.ZERO)));
                    return left;
                }).collect(Collectors.toList())
        );

        Map<String, BigDecimal> promotionIdCouponPromtionMap = recordedPromotionInfo.getCouponInfos()
                .stream()
                .collect(Collectors.toMap(ChargeDiscountInfo.PromotionInfo::getId, ChargeDiscountInfo.PromotionInfo::getDiscountPrice, MathUtils::wrapBigDecimalAdd));
        leftPromotionInfo.setCouponInfos(sourcePromotionInfo.getCouponInfos().stream()
                .map(source -> {
                    ChargeDiscountInfo.CouponInfo left = new ChargeDiscountInfo.CouponInfo();
                    BeanUtils.copyProperties(source, left);
                    left.setDiscountPrice(MathUtils.wrapBigDecimalSubtract(source.getDiscountPrice(), promotionIdCouponPromtionMap.getOrDefault(source.getId(), BigDecimal.ZERO)));
                    return left;
                }).collect(Collectors.toList())
        );

        return leftPromotionInfo;

    }


    @Data
    @Accessors(chain = true)
    public static class FlatBatchInfoPromotionInfoResult {

        private ChargeDiscountInfo batchPromotionInfo;

        private BigDecimal sourceTotalPrice;

    }

    public static void main(String[] args) {
        ChargeDiscountInfo upperLimitPromotionInfo = JsonUtils.readValue("{\"unitAdjustmentFee\":33.25,\"unitAdjustmentFeeIgnoreDeduct\":33.25,\"discountPromotionInfos\":[{\"parentId\":\"3802310590485151744\",\"id\":\"3802310590485151746\",\"name\":\"12\",\"discountPrice\":-14.26,\"parentType\":1,\"type\":1,\"subType\":2,\"discountWay\":0,\"ruleType\":0}],\"giftRulePromotionInfos\":[{\"id\":\"3801834940573679617\",\"name\":\"西药满减\",\"discountPrice\":-15.83,\"parentType\":0,\"type\":2}]}", ChargeDiscountInfo.class);
        ChargeDiscountInfo batchDiscountInfo = JsonUtils.readValue("{\"unitAdjustmentFee\":42.00,\"discountPromotionInfos\":[{\"parentId\":\"3802310590485151744\",\"id\":\"3802310590485151746\",\"name\":\"12\",\"discountPrice\":-18.00,\"parentType\":1,\"type\":1,\"subType\":2,\"discountWay\":0,\"ruleType\":0}],\"giftRulePromotionInfos\":[{\"id\":\"3801834940573679617\",\"name\":\"西药满减\",\"discountPrice\":-20.00,\"parentType\":0,\"type\":2}]}", ChargeDiscountInfo.class);
        ChargeDiscountInfo batchLeftDiscountInfo = JsonUtils.readValue("{\"unitAdjustmentFee\":42.00,\"discountPromotionInfos\":[{\"parentId\":\"3802310590485151744\",\"id\":\"3802310590485151746\",\"name\":\"12\",\"discountPrice\":-18.00,\"parentType\":1,\"type\":1,\"subType\":2,\"discountWay\":0,\"ruleType\":0}],\"giftRulePromotionInfos\":[{\"id\":\"3801834940573679617\",\"name\":\"西药满减\",\"discountPrice\":-20.00,\"parentType\":0,\"type\":2}]}", ChargeDiscountInfo.class);
        BigDecimal currentPrice = new BigDecimal("19");
        BigDecimal totalPrice = new BigDecimal("24");
        boolean calLimitFee = false;
        FlatBatchInfoPromotionInfoResult flatBatchInfoPromotionInfoResult = flatBatchInfoPromotionInfo(upperLimitPromotionInfo, batchDiscountInfo, batchLeftDiscountInfo, currentPrice, totalPrice, calLimitFee);

        upperLimitPromotionInfo = ChargeTransactionRecordDiscountInfoHelper.mergeLeftPromotionInfo(upperLimitPromotionInfo, flatBatchInfoPromotionInfoResult.getBatchPromotionInfo());

        BigDecimal fractionPrice = ChargeTransactionRecordDiscountInfoHelper.addFractionPromotionInfo(batchLeftDiscountInfo, flatBatchInfoPromotionInfoResult.getBatchPromotionInfo(), upperLimitPromotionInfo, flatBatchInfoPromotionInfoResult.getSourceTotalPrice());

        System.out.println(JsonUtils.dump(upperLimitPromotionInfo));
        System.out.println(fractionPrice);
    }

    /**
     * @param upperLimitDiscountInfo 本次入账的营销上限详细信息
     * @param batchDiscountInfo      批次总的营销详细信息
     * @param batchLeftDiscountInfo  批次剩余可入账的营销详细信息
     * @param currentPrice           批次本次入账的实收金额
     * @param totalPrice             批次总的应收金额
     *                               实收 = 原价 + 单项优惠 + 单项议价 + 整单优惠 + 整单议价
     *                               在平摊反算原价时要反着来，通过实收得到原价，且每计算一个值，原价都不能小于0，且顺序不能变
     *                               实收 - 整单议价 - 整单优惠 - 单项议价- 单项优惠 = 原价
     * @return
     */
    public static FlatBatchInfoPromotionInfoResult flatBatchInfoPromotionInfo(ChargeDiscountInfo upperLimitDiscountInfo,
                                                                              ChargeDiscountInfo batchDiscountInfo,
                                                                              ChargeDiscountInfo batchLeftDiscountInfo,
                                                                              BigDecimal currentPrice,
                                                                              BigDecimal totalPrice,
                                                                              boolean calLimitFee) {
        /**
         * 目标的原价，这个值不能小于0
         */
        final AtomicReference<BigDecimal> purposeSourceTotalPrice = new AtomicReference<>(currentPrice);

        FlatBatchInfoPromotionInfoResult flatBatchInfoPromotionInfoResult = new FlatBatchInfoPromotionInfoResult();

        if (upperLimitDiscountInfo == null || batchDiscountInfo == null || batchLeftDiscountInfo == null) {
            return flatBatchInfoPromotionInfoResult.setSourceTotalPrice(purposeSourceTotalPrice.get());
        }

        if (MathUtils.wrapBigDecimalCompare(totalPrice, BigDecimal.ZERO) == 0) {
            return flatBatchInfoPromotionInfoResult.setSourceTotalPrice(purposeSourceTotalPrice.get());
        }


        Function<BigDecimal, BigDecimal> calculateRatePriceFunc = (source) -> calculateByRateAndSetScaleTwoUp(source, currentPrice, totalPrice);

        ChargeDiscountInfo result = new ChargeDiscountInfo();

        //1、减整单议价
        result.setAdjustmentFee(calculateBatchInfoPriceCore(purposeSourceTotalPrice.get(),
                calculateRatePriceFunc,
                upperLimitDiscountInfo::getAdjustmentFee,
                batchLeftDiscountInfo::getAdjustmentFee,
                batchDiscountInfo::getAdjustmentFee));
        purposeSourceTotalPrice.set(MathUtils.wrapBigDecimalSubtract(purposeSourceTotalPrice.get(), result.getAdjustmentFee()));

        //2、减整单优惠（积分，优惠券，满减满赠）
        //2.1、积分
        result.setPatientPointPromotionFee(calculateBatchInfoPriceCore(purposeSourceTotalPrice.get(),
                calculateRatePriceFunc,
                upperLimitDiscountInfo::getPatientPointPromotionFee,
                batchLeftDiscountInfo::getPatientPointPromotionFee,
                batchDiscountInfo::getPatientPointPromotionFee));

        purposeSourceTotalPrice.set(MathUtils.wrapBigDecimalSubtract(purposeSourceTotalPrice.get(), result.getPatientPointPromotionFee()));

        //2.2、优惠券
        Map<String, BigDecimal> upperLimitCouponPromotionIdMap = upperLimitDiscountInfo.getCouponInfos()
                .stream()
                .collect(Collectors.toMap(ChargeDiscountInfo.PromotionInfo::getId, ChargeDiscountInfo.PromotionInfo::getDiscountPrice, MathUtils::wrapBigDecimalAdd));
        Map<String, BigDecimal> batchLeftCouponPromotionIdMap = batchLeftDiscountInfo.getCouponInfos()
                .stream()
                .collect(Collectors.toMap(ChargeDiscountInfo.PromotionInfo::getId, ChargeDiscountInfo.PromotionInfo::getDiscountPrice, MathUtils::wrapBigDecimalAdd));
        Map<String, BigDecimal> batchCouponPromotionIdMap = batchDiscountInfo.getCouponInfos()
                .stream()
                .collect(Collectors.toMap(ChargeDiscountInfo.PromotionInfo::getId, ChargeDiscountInfo.PromotionInfo::getDiscountPrice, MathUtils::wrapBigDecimalAdd));

        result.setCouponInfos(batchDiscountInfo.getCouponInfos().stream()
                .map(batchPromotionInfo -> {
                    ChargeDiscountInfo.CouponInfo resultPromotionInfo = new ChargeDiscountInfo.CouponInfo();
                    BeanUtils.copyProperties(batchPromotionInfo, resultPromotionInfo);
                    BigDecimal discountPriceResult = calculateBatchInfoPriceCore(purposeSourceTotalPrice.get(),
                            calculateRatePriceFunc,
                            () -> upperLimitCouponPromotionIdMap.get(batchPromotionInfo.getId()),
                            () -> batchLeftCouponPromotionIdMap.get(batchPromotionInfo.getId()),
                            () -> batchCouponPromotionIdMap.get(batchPromotionInfo.getId())
                    );
                    purposeSourceTotalPrice.set(MathUtils.wrapBigDecimalSubtract(purposeSourceTotalPrice.get(), discountPriceResult));
                    resultPromotionInfo.setDiscountPrice(discountPriceResult);
                    return resultPromotionInfo;
                }).collect(Collectors.toList())
        );

        //2.3、满减满赠
        Map<String, BigDecimal> upperLimitGiftRulePromotionIdMap = upperLimitDiscountInfo.getGiftRulePromotionInfos()
                .stream()
                .collect(Collectors.toMap(ChargeDiscountInfo.PromotionInfo::getId, ChargeDiscountInfo.PromotionInfo::getDiscountPrice, MathUtils::wrapBigDecimalAdd));
        Map<String, BigDecimal> batchLeftGiftRulePromotionIdMap = batchLeftDiscountInfo.getGiftRulePromotionInfos()
                .stream()
                .collect(Collectors.toMap(ChargeDiscountInfo.PromotionInfo::getId, ChargeDiscountInfo.PromotionInfo::getDiscountPrice, MathUtils::wrapBigDecimalAdd));
        Map<String, BigDecimal> batchGiftRulePromotionIdMap = batchDiscountInfo.getGiftRulePromotionInfos()
                .stream()
                .collect(Collectors.toMap(ChargeDiscountInfo.PromotionInfo::getId, ChargeDiscountInfo.PromotionInfo::getDiscountPrice, MathUtils::wrapBigDecimalAdd));

        result.setGiftRulePromotionInfos(batchDiscountInfo.getGiftRulePromotionInfos().stream()
                .map(batchPromotionInfo -> {
                    ChargeDiscountInfo.PromotionInfo resultPromotionInfo = new ChargeDiscountInfo.PromotionInfo();
                    BeanUtils.copyProperties(batchPromotionInfo, resultPromotionInfo);
                    BigDecimal discountPriceResult = calculateBatchInfoPriceCore(purposeSourceTotalPrice.get(),
                            calculateRatePriceFunc,
                            () -> upperLimitGiftRulePromotionIdMap.get(batchPromotionInfo.getId()),
                            () -> batchLeftGiftRulePromotionIdMap.get(batchPromotionInfo.getId()),
                            () -> batchGiftRulePromotionIdMap.get(batchPromotionInfo.getId())
                    );
                    purposeSourceTotalPrice.set(MathUtils.wrapBigDecimalSubtract(purposeSourceTotalPrice.get(), discountPriceResult));
                    resultPromotionInfo.setDiscountPrice(discountPriceResult);
                    return resultPromotionInfo;
                }).collect(Collectors.toList())
        );

        //3、减单项议价
        //批次目前没考虑卡项抵扣抵扣，积分抵扣在discountInfos里面，不用单独特殊处理
        result.setDeductedUnitAdjustmentFee(BigDecimal.ZERO);
        result.setUnitAdjustmentFee(calculateBatchInfoPriceCore(purposeSourceTotalPrice.get(),
                calculateRatePriceFunc,
                upperLimitDiscountInfo::getUnitAdjustmentFee,
                batchLeftDiscountInfo::getUnitAdjustmentFee,
                batchDiscountInfo::getUnitAdjustmentFee));
        purposeSourceTotalPrice.set(MathUtils.wrapBigDecimalSubtract(purposeSourceTotalPrice.get(), result.getUnitAdjustmentFee()));

        result.setUnitAdjustmentFeeIgnoreDeduct(result.getUnitAdjustmentFee());

        //4、减单项优惠
        Map<String, BigDecimal> upperLimitDiscountPromotionIdMap = upperLimitDiscountInfo.getDiscountPromotionInfos()
                .stream()
                .collect(Collectors.toMap(ChargeDiscountInfo.PromotionInfo::getId, ChargeDiscountInfo.PromotionInfo::getDiscountPrice, MathUtils::wrapBigDecimalAdd));
        Map<String, BigDecimal> batchLeftDiscountPromotionIdMap = batchLeftDiscountInfo.getDiscountPromotionInfos()
                .stream()
                .collect(Collectors.toMap(ChargeDiscountInfo.PromotionInfo::getId, ChargeDiscountInfo.PromotionInfo::getDiscountPrice, MathUtils::wrapBigDecimalAdd));
        Map<String, BigDecimal> batchDiscountPromotionIdMap = batchDiscountInfo.getDiscountPromotionInfos()
                .stream()
                .collect(Collectors.toMap(ChargeDiscountInfo.PromotionInfo::getId, ChargeDiscountInfo.PromotionInfo::getDiscountPrice, MathUtils::wrapBigDecimalAdd));

        result.setDiscountPromotionInfos(batchDiscountInfo.getDiscountPromotionInfos().stream()
                .map(batchPromotionInfo -> {
                    ChargeDiscountInfo.PromotionInfo resultPromotionInfo = new ChargeDiscountInfo.PromotionInfo();
                    BeanUtils.copyProperties(batchPromotionInfo, resultPromotionInfo);
                    BigDecimal discountPriceResult = calculateBatchInfoPriceCore(purposeSourceTotalPrice.get(),
                            calculateRatePriceFunc,
                            () -> upperLimitDiscountPromotionIdMap.get(batchPromotionInfo.getId()),
                            () -> batchLeftDiscountPromotionIdMap.get(batchPromotionInfo.getId()),
                            () -> batchDiscountPromotionIdMap.get(batchPromotionInfo.getId())
                    );
                    purposeSourceTotalPrice.set(MathUtils.wrapBigDecimalSubtract(purposeSourceTotalPrice.get(), discountPriceResult));
                    resultPromotionInfo.setDiscountPrice(discountPriceResult);
                    return resultPromotionInfo;
                }).collect(Collectors.toList())
        );

        if (calLimitFee) {
            result.setLimitFee(calculateBatchInfoPriceCore(purposeSourceTotalPrice.get(),
                    calculateRatePriceFunc,
                    upperLimitDiscountInfo::getLimitFee,
                    batchLeftDiscountInfo::getLimitFee,
                    batchDiscountInfo::getLimitFee));
            purposeSourceTotalPrice.set(MathUtils.wrapBigDecimalSubtract(purposeSourceTotalPrice.get(), result.getLimitFee()));
        }

        //挂网价限价
        result.setListingDiscountFee(calculateBatchInfoPriceCore(purposeSourceTotalPrice.get(),
                calculateRatePriceFunc,
                upperLimitDiscountInfo::getListingDiscountFee,
                batchLeftDiscountInfo::getListingDiscountFee,
                batchDiscountInfo::getListingDiscountFee));

        purposeSourceTotalPrice.set(MathUtils.wrapBigDecimalSubtract(purposeSourceTotalPrice.get(), result.getListingDiscountFee()));

        //判断平摊过后的原价是否小于0
        if (purposeSourceTotalPrice.get().compareTo(BigDecimal.ZERO) < 0) {
            log.error("purposeSourceTotalPrice不能小于0，平摊批次的优惠失败");
            throw new IllegalArgumentException("平摊批次的优惠失败");
        }

        return flatBatchInfoPromotionInfoResult.setBatchPromotionInfo(result)
                .setSourceTotalPrice(purposeSourceTotalPrice.get());
    }

    /**
     * @param purposeSourceTotalPrice
     * @param calculateRatePriceFunc
     * @param upperLimitPriceSupplier
     * @param batchLeftPriceSupplier
     * @param batchPriceSupplier
     * @return
     */
    private static BigDecimal calculateBatchInfoPriceCore(BigDecimal purposeSourceTotalPrice,
                                                          Function<BigDecimal, BigDecimal> calculateRatePriceFunc,
                                                          Supplier<BigDecimal> upperLimitPriceSupplier,
                                                          Supplier<BigDecimal> batchLeftPriceSupplier,
                                                          Supplier<BigDecimal> batchPriceSupplier) {

        BigDecimal upperLimitPrice = MathUtils.minAbs(upperLimitPriceSupplier.get(), batchLeftPriceSupplier.get());

        BigDecimal resultPrice = MathUtils.minAbs(calculateRatePriceFunc.apply(batchPriceSupplier.get()), upperLimitPrice);

        /**
         * 例如：在第一次计算时，purposeSourceTotalPrice就等于应收，假如等于5，议价平摊出来为6，目标原价为了不小于0，这里需要将差值减去
         * 目标原价 = purposeSourceTotalPrice - resultPrice,此时算出来目标原价等于-1，说明议价被平摊多了，多了1元，所以
         * resultPrice = 6 + (5 - 6)，也就是6 + (-1) = 5
         * 最终议价本次的值就是5
         */
        resultPrice = resultPrice.add(MathUtils.min(purposeSourceTotalPrice.subtract(resultPrice), BigDecimal.ZERO));

        return resultPrice;
    }

    @FunctionalInterface
    public interface CalculateAddFractionPriceFunc {
        BigDecimal apply(BigDecimal upperLimitPrice, BigDecimal existedPrice, BigDecimal fractionPrice);

        /**
         * 默认算法
         */
        CalculateAddFractionPriceFunc DEFAULT_FUNC = (upperLimitPrice, existedPrice, fractionPrice) -> {

            if (MathUtils.compareZero(upperLimitPrice) == 0) {
                return BigDecimal.ZERO;
            }

            //是否为正值
            boolean isPositive = MathUtils.compareZero(upperLimitPrice) > 0;

            BigDecimal addUpperLimit = MathUtils.wrapBigDecimalSubtract(upperLimitPrice, existedPrice);

            //为正值时，两种情况
            if (isPositive) {
                if (fractionPrice.compareTo(BigDecimal.ZERO) > 0) {
                    //说明子项的金额加起来小于母项
                    return MathUtils.min(addUpperLimit, fractionPrice);
                } else {
                    //说明子项的金额加起来大于了母项，摊多了，那么就需要把子项的金额减一点下来
                    return MathUtils.min(existedPrice.abs(), fractionPrice.abs()).negate();
                }
            } else {
                if (fractionPrice.compareTo(BigDecimal.ZERO) > 0) {
                    //说明子项的负值金额摊多了，要把子项的金额减下来，比如母项是-2，子项是-2.1，那么子项需要变成-2
                    return MathUtils.min(existedPrice.abs(), fractionPrice);
                } else {
                    //说明子项的金额加起来小于母项（负值维度）
                    return MathUtils.max(addUpperLimit, fractionPrice);
                }
            }
        };

        static BigDecimal addFractionPrice(Supplier<BigDecimal> upperLimitSupplier,
                                     Supplier<BigDecimal> existedSupplier,
                                     Supplier<BigDecimal> fractionSupplier,
                                     Consumer<BigDecimal> existedAddFractionPriceConsumer,
                                     Consumer<BigDecimal> fractionSubtractPriceConsumer,
                                     BigDecimal sourceTotalPrice) {

            BigDecimal addFractionPrice = CalculateAddFractionPriceFunc.DEFAULT_FUNC.apply(
                    upperLimitSupplier.get(),
                    existedSupplier.get(),
                    fractionSupplier.get());

            if (MathUtils.compareZero(addFractionPrice) == 0) {
                return BigDecimal.ZERO;
            }

            //保障原价不能小于0
            if (addFractionPrice.compareTo(BigDecimal.ZERO) > 0) {
                addFractionPrice = MathUtils.min(sourceTotalPrice, addFractionPrice);
            }

            existedAddFractionPriceConsumer.accept(MathUtils.wrapBigDecimalAdd(existedSupplier.get(), addFractionPrice));
            fractionSubtractPriceConsumer.accept(MathUtils.wrapBigDecimalSubtract(fractionSupplier.get(), addFractionPrice));
            return addFractionPrice;
        }
    }

    /**
     * 增加剩余的零头promotion
     *
     * @param upperLimitDiscountInfo 上限
     * @param existedDiscountInfo    已存在的
     * @param fractionPromotionInfo  零头
     * @return
     */
    public static BigDecimal addFractionPromotionInfo(ChargeDiscountInfo upperLimitDiscountInfo,
                                                      ChargeDiscountInfo existedDiscountInfo,
                                                      ChargeDiscountInfo fractionPromotionInfo,
                                                      BigDecimal sourceTotalPrice) {
        AtomicReference<BigDecimal> addFractionPriceAtomic = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> sourceTotalPriceAtomic = new AtomicReference<>(sourceTotalPrice);

        if (Objects.isNull(upperLimitDiscountInfo) || Objects.isNull(existedDiscountInfo) || Objects.isNull(fractionPromotionInfo)) {
            log.info("upperLimitDiscountInfo or existedDiscountInfo or fractionPromotionInfo is null, upperLimitDiscountInfo: {}, existedDiscountInfo: {}, fractionPromotionInfo: {}", JsonUtils.dump(upperLimitDiscountInfo), JsonUtils.dump(existedDiscountInfo), JsonUtils.dump(fractionPromotionInfo));
            return BigDecimal.ZERO;
        }

        //1、减整单议价
        if (MathUtils.compareZero(fractionPromotionInfo.getAdjustmentFee()) != 0) {
            BigDecimal fractionPrice = CalculateAddFractionPriceFunc.addFractionPrice(upperLimitDiscountInfo::getAdjustmentFee,
                    existedDiscountInfo::getAdjustmentFee,
                    fractionPromotionInfo::getAdjustmentFee,
                    existedDiscountInfo::setAdjustmentFee,
                    fractionPromotionInfo::setAdjustmentFee,
                    sourceTotalPriceAtomic.get());
            sourceTotalPriceAtomic.set(sourceTotalPriceAtomic.get().subtract(fractionPrice));
            addFractionPriceAtomic.set(addFractionPriceAtomic.get().add(fractionPrice));
        }

        //2、减整单优惠（积分，优惠券，满减满赠）
        //2.1、积分
        if (MathUtils.compareZero(fractionPromotionInfo.getPatientPointPromotionFee()) != 0) {
            BigDecimal fractionPrice = CalculateAddFractionPriceFunc.addFractionPrice(upperLimitDiscountInfo::getPatientPointPromotionFee,
                    existedDiscountInfo::getPatientPointPromotionFee,
                    fractionPromotionInfo::getPatientPointPromotionFee,
                    existedDiscountInfo::setPatientPointPromotionFee,
                    fractionPromotionInfo::setPatientPointPromotionFee,
                    sourceTotalPriceAtomic.get());
            sourceTotalPriceAtomic.set(sourceTotalPriceAtomic.get().subtract(fractionPrice));
            addFractionPriceAtomic.set(addFractionPriceAtomic.get().add(fractionPrice));
        }


        //2.2、优惠券
        Map<String, ChargeDiscountInfo.CouponInfo> upperLimitCouponPromotionIdMap = upperLimitDiscountInfo.getCouponInfos()
                .stream()
                .collect(Collectors.toMap(ChargeDiscountInfo.PromotionInfo::getId, Function.identity(), (a, b) -> {
                    a.setDiscountPrice(MathUtils.wrapBigDecimalAdd(a.getDiscountPrice(), b.getDiscountPrice()));
                    return a;
                }));
        Map<String, ChargeDiscountInfo.CouponInfo> fractionCouponPromotionIdMap = fractionPromotionInfo.getCouponInfos()
                .stream()
                .collect(Collectors.toMap(ChargeDiscountInfo.PromotionInfo::getId, Function.identity(), (a, b) -> {
                    a.setDiscountPrice(MathUtils.wrapBigDecimalAdd(a.getDiscountPrice(), b.getDiscountPrice()));
                    return a;
                }));

        existedDiscountInfo.getCouponInfos()
                .forEach(existedCouponPromotion -> {
                    ChargeDiscountInfo.CouponInfo upperLimitCouponInfo = upperLimitCouponPromotionIdMap.get(existedCouponPromotion.getId());
                    ChargeDiscountInfo.CouponInfo fractionCouponInfo = fractionCouponPromotionIdMap.get(existedCouponPromotion.getId());

                    if (Objects.isNull(fractionCouponInfo) || MathUtils.compareZero(fractionCouponInfo.getDiscountPrice()) == 0) {
                        return;
                    }

                    if (Objects.isNull(upperLimitCouponInfo) || MathUtils.compareZero(upperLimitCouponInfo.getDiscountPrice()) == 0) {
                        return;
                    }

                    BigDecimal fractionPrice = CalculateAddFractionPriceFunc.addFractionPrice(upperLimitCouponInfo::getDiscountPrice,
                            existedCouponPromotion::getDiscountPrice,
                            fractionCouponInfo::getDiscountPrice,
                            existedCouponPromotion::setDiscountPrice,
                            fractionCouponInfo::setDiscountPrice,
                            sourceTotalPriceAtomic.get());
                    sourceTotalPriceAtomic.set(sourceTotalPriceAtomic.get().subtract(fractionPrice));
                    addFractionPriceAtomic.set(addFractionPriceAtomic.get().add(fractionPrice));
                });

        //2.3、满减满赠
        Map<String, ChargeDiscountInfo.PromotionInfo> upperLimitGiftRulePromotionIdMap = upperLimitDiscountInfo.getGiftRulePromotionInfos()
                .stream()
                .collect(Collectors.toMap(ChargeDiscountInfo.PromotionInfo::getId, Function.identity(), (a, b) -> {
                    a.setDiscountPrice(MathUtils.wrapBigDecimalAdd(a.getDiscountPrice(), b.getDiscountPrice()));
                    return a;
                }));
        Map<String, ChargeDiscountInfo.PromotionInfo> fractionGiftRulePromotionIdMap = fractionPromotionInfo.getGiftRulePromotionInfos()
                .stream()
                .collect(Collectors.toMap(ChargeDiscountInfo.PromotionInfo::getId, Function.identity(), (a, b) -> {
                    a.setDiscountPrice(MathUtils.wrapBigDecimalAdd(a.getDiscountPrice(), b.getDiscountPrice()));
                    return a;
                }));

        existedDiscountInfo.getGiftRulePromotionInfos()
                .forEach(existedGiftRulePromotion -> {
                    ChargeDiscountInfo.PromotionInfo fractionGiftRulePromotion = fractionGiftRulePromotionIdMap.get(existedGiftRulePromotion.getId());
                    ChargeDiscountInfo.PromotionInfo upperLimitGiftRulePromotion = upperLimitGiftRulePromotionIdMap.get(existedGiftRulePromotion.getId());

                    if (Objects.isNull(fractionGiftRulePromotion) || MathUtils.compareZero(fractionGiftRulePromotion.getDiscountPrice()) == 0) {
                        return;
                    }

                    if (Objects.isNull(upperLimitGiftRulePromotion) || MathUtils.compareZero(upperLimitGiftRulePromotion.getDiscountPrice()) == 0) {
                        return;
                    }

                    BigDecimal fractionPrice = CalculateAddFractionPriceFunc.addFractionPrice(upperLimitGiftRulePromotion::getDiscountPrice,
                            existedGiftRulePromotion::getDiscountPrice,
                            fractionGiftRulePromotion::getDiscountPrice,
                            existedGiftRulePromotion::setDiscountPrice,
                            fractionGiftRulePromotion::setDiscountPrice,
                            sourceTotalPriceAtomic.get());
                    sourceTotalPriceAtomic.set(sourceTotalPriceAtomic.get().subtract(fractionPrice));
                    addFractionPriceAtomic.set(addFractionPriceAtomic.get().add(fractionPrice));

                });


        //3、减单项议价
        //批次目前没考虑卡项抵扣抵扣，积分抵扣在discountInfos里面，不用单独特殊处理
        if (MathUtils.compareZero(fractionPromotionInfo.getUnitAdjustmentFee()) != 0) {
            BigDecimal fractionPrice = CalculateAddFractionPriceFunc.addFractionPrice(upperLimitDiscountInfo::getUnitAdjustmentFee,
                    existedDiscountInfo::getUnitAdjustmentFee,
                    fractionPromotionInfo::getUnitAdjustmentFee,
                    result -> {
                        existedDiscountInfo.setUnitAdjustmentFee(result);
                        existedDiscountInfo.setUnitAdjustmentFeeIgnoreDeduct(result);
                    },
                    fractionPromotionInfo::setUnitAdjustmentFee,
                    sourceTotalPriceAtomic.get());
            sourceTotalPriceAtomic.set(sourceTotalPriceAtomic.get().subtract(fractionPrice));
            addFractionPriceAtomic.set(addFractionPriceAtomic.get().add(fractionPrice));
        }

        //4、减单项优惠
        Map<String, ChargeDiscountInfo.PromotionInfo> upperLimitDiscountPromotionIdMap = upperLimitDiscountInfo.getDiscountPromotionInfos()
                .stream()
                .collect(Collectors.toMap(ChargeDiscountInfo.PromotionInfo::getId, Function.identity(), (a, b) -> {
                    a.setDiscountPrice(MathUtils.wrapBigDecimalAdd(a.getDiscountPrice(), b.getDiscountPrice()));
                    return a;
                }));
        Map<String, ChargeDiscountInfo.PromotionInfo> fractionDiscountPromotionIdMap = fractionPromotionInfo.getDiscountPromotionInfos()
                .stream()
                .collect(Collectors.toMap(ChargeDiscountInfo.PromotionInfo::getId, Function.identity(), (a, b) -> {
                    a.setDiscountPrice(MathUtils.wrapBigDecimalAdd(a.getDiscountPrice(), b.getDiscountPrice()));
                    return a;
                }));

        existedDiscountInfo.getDiscountPromotionInfos()
                .forEach(existedDiscountPromotion -> {
                    ChargeDiscountInfo.PromotionInfo fractionDiscountPromotion = fractionDiscountPromotionIdMap.get(existedDiscountPromotion.getId());
                    ChargeDiscountInfo.PromotionInfo upperLimitDiscountPromotion = upperLimitDiscountPromotionIdMap.get(existedDiscountPromotion.getId());

                    if (Objects.isNull(fractionDiscountPromotion) || MathUtils.compareZero(fractionDiscountPromotion.getDiscountPrice()) == 0) {
                        return;
                    }

                    if (Objects.isNull(upperLimitDiscountPromotion) || MathUtils.compareZero(upperLimitDiscountPromotion.getDiscountPrice()) == 0) {
                        return;
                    }

                    BigDecimal fractionPrice = CalculateAddFractionPriceFunc.addFractionPrice(upperLimitDiscountPromotion::getDiscountPrice,
                            existedDiscountPromotion::getDiscountPrice,
                            fractionDiscountPromotion::getDiscountPrice,
                            existedDiscountPromotion::setDiscountPrice,
                            fractionDiscountPromotion::setDiscountPrice,
                            sourceTotalPriceAtomic.get());
                    sourceTotalPriceAtomic.set(sourceTotalPriceAtomic.get().subtract(fractionPrice));
                    addFractionPriceAtomic.set(addFractionPriceAtomic.get().add(fractionPrice));
                });

        return addFractionPriceAtomic.get();
    }
}
