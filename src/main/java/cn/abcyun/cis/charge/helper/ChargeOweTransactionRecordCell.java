package cn.abcyun.cis.charge.helper;

import cn.abcyun.cis.charge.model.ChargeOweCombineTransactionRecordDetail;
import cn.abcyun.cis.charge.util.MathUtils;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.List;

public class ChargeOweTransactionRecordCell implements PresentPrincipalAmountFlatTool.TransactionRecordCell{

    private final ChargeOweCombineTransactionRecordDetail chargeOweCombineTransactionRecordDetail;

    public ChargeOweTransactionRecordCell(ChargeOweCombineTransactionRecordDetail chargeOweCombineTransactionRecordDetail){
        Assert.notNull(chargeOweCombineTransactionRecordDetail, "ChargeOweCombineTransactionRecordDetail can not be null");
        this.chargeOweCombineTransactionRecordDetail = chargeOweCombineTransactionRecordDetail;
    }

    @Override
    public String getId() {
        return chargeOweCombineTransactionRecordDetail.getId();
    }

    @Override
    public BigDecimal getTotalPrice() {
        return MathUtils.wrapBigDecimalAdd(chargeOweCombineTransactionRecordDetail.getTotalPrice(), chargeOweCombineTransactionRecordDetail.getDiscountPrice());
    }

    @Override
    public int getComposeType() {
        return chargeOweCombineTransactionRecordDetail.getComposeType();
    }

    @Override
    public int getGoodsFeeType() {
        return chargeOweCombineTransactionRecordDetail.getGoodsFeeType();
    }

    @Override
    public String getParentTransactionRecordId() {
        return chargeOweCombineTransactionRecordDetail.getComposeParentTransactionRecordDetailId();
    }

    @Override
    public BigDecimal getPrincipalAmount() {
        return chargeOweCombineTransactionRecordDetail.getPrincipalAmount();
    }

    @Override
    public BigDecimal getPresentAmount() {
        return chargeOweCombineTransactionRecordDetail.getPresentAmount();
    }

    @Override
    public void setPrincipalAmount(BigDecimal principalAmount) {
        chargeOweCombineTransactionRecordDetail.setPrincipalAmount(principalAmount);
    }

    @Override
    public void setPresentAmount(BigDecimal presentAmount) {
        chargeOweCombineTransactionRecordDetail.setPresentAmount(presentAmount);
    }

    @Override
    public List<PresentPrincipalAmountFlatTool.TransactionRecordCell> getChildren() {
        return null;
    }
}
