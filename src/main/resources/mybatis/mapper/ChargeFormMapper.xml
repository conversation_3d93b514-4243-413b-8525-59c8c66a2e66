<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.abcyun.cis.charge.mapper.ChargeFormMapper">

    <select id="filterChargeSheetIdsBySheetIdsAndSourceFormType" resultType="java.lang.String">
        select distinct charge_sheet_id from v2_charge_form where charge_sheet_id in
        <foreach collection="chargeSheetIds" open="(" close=")" separator="," item="chargeSheetId">
            #{chargeSheetId}
        </foreach>
        and clinic_id = #{clinicId}
        and is_deleted = 0 and source_form_type in
        <foreach collection="sourceFormTypes" open="(" close=")" separator="," item="sourceFormType">
            #{sourceFormType}
        </foreach>
        <if test="specification != null and specification != ''">
            and specification = #{specification}
        </if>
    </select>
</mapper>