<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.abcyun.cis.charge.hospital.mapper.ChargeHospitalMapper">


    <select id="findNeedRefreshHospitalSheetIds" resultType="java.lang.Long">
        select distinct a.hospital_sheet_id from v2_charge_sheet a left join v2_charge_owe_sheet b on a.id = b.charge_sheet_id
        where b.id is null and a.hospital_sheet_id is not null and a.chain_id in
        <foreach collection="chainIds" item="chainId" open="(" separator="," close=")">
            #{chainId}
        </foreach>
        <if test="hospitalSheetIds != null and hospitalSheetIds.size() > 0">
            and a.hospital_sheet_id in
            <foreach collection="hospitalSheetIds" item="hospitalSheetId" open="(" separator="," close=")">
                #{hospitalSheetId}
            </foreach>
        </if>
    </select>
</mapper>