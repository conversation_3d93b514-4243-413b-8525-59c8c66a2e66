<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.abcyun.cis.charge.mapper.ChargeAirPharmacyUsedRecordMapper">

    <select id="findAllByChainIdAndClinicIdAndVendor"
            resultType="cn.abcyun.cis.charge.model.ChargeAirPharmacyUsedRecord">

        select * from v2_charge_air_pharmacy_used_record where chain_id = #{chainId}
        and clinic_id = #{clinicId}
        and (
        <foreach collection="vendors" separator="or" item="item">
            ( vendor_id = #{item.vendorId} and usage_scope_id = #{item.usageScopeId} )
        </foreach>
        )
    </select>
</mapper>