<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.abcyun.cis.charge.mapper.ChargeSalesOrderMapper">
    <resultMap id="salesOrderAbstract" type="cn.abcyun.cis.charge.service.dto.SalesOrderAbstract">
        <id column="id" property="id"/>
        <result column="patientOrderId" property="patientOrderId"/>
        <result column="clinicId" property="clinicId"/>
        <result column="chainId" property="chainId"/>
        <result column="sellNo" property="sellNo"/>
        <result column="receivableFee" property="receivableFee"/>
        <result column="receivedFee" property="receivedFee"/>
        <result column="refundFee" property="refundFee"/>
        <result column="patientId" property="patientId"/>
        <result column="sellerId" property="sellerId"/>
        <result column="chargedBy" property="chargedBy"/>
        <result column="status" property="status"/>
        <result column="created" property="created"/>
        <result column="isClosed" property="isClosed"/>
        <result column="type" property="type"/>
        <result column="coSourceClinicId" property="coSourceClinicId"/>
        <result column="coSourceClinicName" property="coSourceClinicName"/>
        <result column="additionalExtendedInfo" property="additionalExtendedInfo"
                typeHandler="cn.abcyun.cis.charge.mybatis.handler.ChargeSheetAdditionalExtendInfoTypeHandler"
                javaType="cn.abcyun.cis.charge.model.ChargeSheetAdditionalExtendInfo" jdbcType="VARCHAR"/>
        <result column="orderByDate" property="orderByDate" />
    </resultMap>

    <sql id="salesOrderAbstractColumn">
        a.id               AS id,
        a.patient_order_id as patientOrderId,
        a.clinic_id        AS clinicId,
        a.chain_id         AS chainId,
        a.sell_no          AS sellNo,
        a.receivable_fee   AS receivableFee,
        a.received_fee     AS receivedFee,
        a.refund_fee       AS refundFee,
        a.patient_id       AS patientId,
        a.seller_id        AS sellerId,
        a.charged_by       AS chargedBy,
        a.status           AS status,
        a.is_closed        AS isClosed,
        a.created          AS created,
        a.order_by_date    AS orderByDate,
        a.type             AS type,
        ad.extended_info   as additionalExtendedInfo,
        c.source_clinic_id    coSourceClinicId,
        c.source_clinic_name  coSourceClinicName,
        CASE
            WHEN a.status = 1 THEN a.created -- 待收费按开单时间正序
            WHEN a.status = 0 and a.is_closed != 1 THEN a.created -- 待收费按开单时间正序
            ELSE TIMESTAMP('9999-12-31 23:59:59') - a.order_by_date -- 其他状态按开单时间倒序
            END
                           AS sortPriority
    </sql>

    <select id="findChargeSheetSalesOrderAbstractListByIdsAndChainIdAndClinicId"
            resultMap="salesOrderAbstract">
        SELECT
        <include refid="salesOrderAbstractColumn"/>
        FROM v2_charge_sheet AS a
                 LEFT JOIN
             v2_charge_sheet_additional ad ON ad.id = a.id
                 LEFT JOIN v2_charge_cooperation_order c on a.id = c.relate_charge_sheet_id and a.clinic_id = c.clinic_id and c.is_deleted = 0
        WHERE a.is_deleted = 0
          AND a.chain_id = #{chainId}
        <if test="clinicId != null and clinicId != ''">
            AND a.clinic_id = #{clinicId}
        </if>
        AND
        a.id IN
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by a.id
        ORDER BY sortPriority
    </select>

    <sql id="salesOrderAbstractListWhere">
        a.is_deleted = 0
            AND
        a.chain_id = #{chainId}
            AND
        a.type in (3, 14, 17)
            AND
        a.is_draft = 0
        <if test="clinicId != null and clinicId != ''">
            AND a.clinic_id = #{clinicId}
        </if>
        <if test="chargeSheetId != null and chargeSheetId != ''">
            AND a.id = #{chargeSheetId}
        </if>
        <if test="type != null">
            AND a.type = #{type}
        </if>
        <if test="coSourceClinicId != null and coSourceClinicId != ''">
            and c.source_clinic_id = #{coSourceClinicId}
        </if>
        <choose>
            <when test="statusList != null and statusList.size() > 0">
                AND (
                a.status IN
                <foreach collection="statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
                )
            </when>
        </choose>
        <if test="filterFlag == 1">
            AND (a.status = 0 AND a.is_closed = 1)
        </if>
        <if test="filterFlag == 2">
            AND a.is_closed = 0
        </if>
        <if test="beginTime != null">
            AND a.order_by_date &gt; #{beginTime}
        </if>
        <if test="endTime != null">
            AND a.order_by_date &lt; #{endTime}
        </if>
    </sql>

    <select id="findChargeSheetSalesOrderAbstractListByChainIdAndClinicId"
            resultMap="salesOrderAbstract">
        SELECT
        <include refid="salesOrderAbstractColumn"/>
        FROM v2_charge_sheet AS a
                 LEFT JOIN v2_charge_sheet_additional ad ON ad.id = a.id
                 LEFT JOIN v2_charge_cooperation_order c on a.type = 17 and a.source_id is not null and a.source_id = c.id and a.clinic_id = c.clinic_id
        WHERE
        <include refid="salesOrderAbstractListWhere">
        </include>
        group by a.id
        ORDER BY sortPriority
        <if test="offset != null and offset gt -1 and limit != null and limit gt -1">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <!--    <select id="findChargeSheetSalesOrderSummary" resultType="cn.abcyun.cis.charge.service.dto.SalesOrderSummary">
            SELECT
                COUNT(*) AS totalCount,
                SUM(CASE WHEN status='0' THEN 1 ELSE 0 END) AS unchargedCount,
                SUM(CASE WHEN status='1' THEN 1 ELSE 0 END) AS partChargedCount,
                SUM(CASE WHEN status='2' THEN 1 ELSE 0 END) AS chargedCount,
                SUM(CASE WHEN status='3' THEN 1 ELSE 0 END) AS partRefundedCount,
                SUM(CASE WHEN status='4' THEN 1 ELSE 0 END) AS refundedCount,
                SUM(received_fee) AS receivedFeeTotal,
                SUM(refund_fee) AS refundFeeTotal
            FROM v2_charge_sheet AS a
            WHERE
                <include refid="salesOrderAbstractListWhere"></include>
        </select>-->

    <select id="findChargeSheetSalesOrderSummary" resultType="cn.abcyun.cis.charge.service.dto.SalesOrderSummary">
        SELECT COUNT(distinct a.id)             AS totalCount,
               SUM(IFNULL(a.received_fee, 0))   AS receivedFeeTotal,
               SUM(IFNULL(a.receivable_fee, 0)) as receivableFeeTotal,
               SUM(IFNULL(a.refund_fee, 0))     AS refundFeeTotal
        FROM
        v2_charge_sheet AS a
        <if test="coSourceClinicId != null and coSourceClinicId != ''">
            LEFT JOIN v2_charge_cooperation_order c on a.type = 17 and a.source_id is not null and a.source_id = c.id and a.clinic_id = c.clinic_id
        </if>
        WHERE
        <include refid="salesOrderAbstractListWhere">
        </include>
    </select>
</mapper>