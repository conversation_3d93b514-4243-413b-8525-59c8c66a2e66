<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.abcyun.cis.charge.mapper.ChargeMapper">

    <resultMap id="chargeSheetAbstract" type="cn.abcyun.cis.charge.service.dto.ChargeSheetAbstract">
        <id column="id" property="id"/>
        <result column="patientOrderId" property="patientOrderId"/>
        <result column="created" property="created"/>
        <result column="clinicId" property="clinicId"/>
        <result column="chainId" property="chainId"/>
        <result column="doctorName" property="doctorName"/>
        <result column="status" property="status"/>
        <result column="type" property="type"/>
        <result column="outpatientStatus" property="outpatientStatus"/>
        <result column="executeStatus" property="executeStatus"/>
        <result column="receivableFee" property="receivableFee"/>
        <result column="isDraft" property="isDraft"/>
        <result column="isOnline" property="isOnline"/>
        <result column="sendToPatientStatus" property="sendToPatientStatus"/>
        <association property="patient" javaType="cn.abcyun.cis.commons.model.CisPatientInfo">
            <result column="patientId" property="id"/>
            <result column="patientName" property="name"/>
            <result column="patientSex" property="sex"/>
            <result column="patientMobile" property="mobile"/>
            <result column="patientBirthday" property="age"
                    typeHandler="cn.abcyun.cis.charge.mybatis.handler.CisPatientAgeFromBirthdayHandler"
                    javaType="cn.abcyun.cis.commons.model.CisPatientAge" jdbcType="VARCHAR"/>
            <result column="isMember" property="isMember"/>
        </association>
    </resultMap>

    <sql id="chargeDisplayType">
        2, 3, 6, 8, 9, 12, 13, 14, 15, 16, 17
    </sql>

    <sql id="nurseChargeDisplayType">
        3, 8, 12, 13, 14, 15, 16
    </sql>

    <sql id="chargeSheetAbstractListForNurseWhereSql">
        a.is_deleted = 0 AND a.clinic_id = #{clinicId} and a.check_status &lt;&gt; 1
        AND (
                (
                    (
                        (a.type in (<include refid="nurseChargeDisplayType"></include>) AND a.execute_status &lt;&gt; 0)
                        OR (a.type = 2 AND a.outpatient_status IN (0,2) )
                    )
                    <choose>
                        <when test="includeItemType == null">
                            <if test="onlyShowContainsExecute">
                                AND a.execute_status != 0
                            </if>
                        </when>
                        <otherwise>
                            AND if(a.include_item_type is null, if(#{onlyShowContainsExecute}, a.execute_status != 0, false), a.include_item_type &amp; #{includeItemType})
                        </otherwise>
                    </choose>
                    <if test="onlyShowAfterPaid">
                        AND a.status in (2,3,4)
                    </if>
                )
                OR a.type = 6
        )
        <choose>
            <when test="filterExecuteStatus == 'waiting'">
                AND a.execute_status = 1 AND a.status &lt; 4
            </when>
            <when test="filterExecuteStatus == 'executed'">
                AND a.execute_status = 2 AND a.status &lt; 4
            </when>
        </choose>
        <if test="(ownerIds != null and ownerIds.size > 0) or (needFilterExecutor and filterExecutorId != null and filterExecutorId !='')">
            AND (
                <if test="ownerIds != null and ownerIds.size > 0">
                    a.seller_id in
                    <foreach collection="ownerIds" item="ownerId" open="(" separator="," close=")">
                        #{ownerId}
                    </foreach>
                    or a.doctor_id in
                    <foreach collection="ownerIds" item="ownerId" open="(" separator="," close=")">
                        #{ownerId}
                    </foreach>
                </if>
                <if test="needFilterExecutor and filterExecutorId != null and filterExecutorId !=''">
                    <if test="ownerIds != null and ownerIds.size > 0">
                        or
                    </if>
                     b.id is not null
                </if>
            )
        </if>
        <if test="createdBegin != null">
            AND a.order_by_date &gt; #{createdBegin}
        </if>
        <if test="createdEnd != null">
            AND a.order_by_date &lt; #{createdEnd}
        </if>
        <if test="typeList != null and typeList.size() > 0">
            AND a.type in
            <foreach collection="typeList" item="type" open="(" separator=" , " close=")">
                #{type}
            </foreach>
        </if>
    </sql>

    <sql id="importFlags">
        0, 2, 3
    </sql>

    <sql id="queryPatientChargeSheetWhere">
        chain_id = #{chainId}
            and clinic_id = #{clinicId}
            and patient_id = #{patientId}
            and is_deleted = 0
            and is_closed = 0
            and created between #{beginTime} and #{endTime}
        <if test="statuses != null and statuses.size() != 0">
            and status in
            <foreach collection="statuses" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="chargeSheetTypes != null and chargeSheetTypes.size() != 0">
            and type in
            <foreach collection="chargeSheetTypes" item="chargeSheetType" open="(" separator="," close=")">
                #{chargeSheetType}
            </foreach>
            and (type != 2 or outpatient_status = 2)
                and is_online = 0
        </if>
    </sql>

    <sql id="chargeSheetSimpleParamSql">
        a.id,
        a.patient_order_id as patientOrderId,
        a.order_by_date as orderByDate,
        a.chain_id as chainId,
        a.clinic_id as clinicId,
        if(a.doctor_id is null or a.doctor_id = '', b.transcribe_doctor_id, a.doctor_id) as doctorId,
        b.department_id as departmentId,
        a.patient_id as patientId,
        a.receivable_fee as receivableFee,
        a.status,
        a.execute_status as executeStatus,
        a.outpatient_status as outpatientStatus,
        a.is_online as isOnline,
        a.diagnosed_date as diagnosedDate,
        a.received_fee as receivedFee,
        a.refund_fee as refundFee,
        a.charged_time as chargedTime,
        a.type,
        a.charged_by as chargedBy,
        a.seller_id as sellerId,
        a.sell_no as sellNo,
        a.created,
        a.owed_status as owedStatus,
        a.is_draft as isDraft
    </sql>

    <select id="findChargeSheetAbstractList" resultType="cn.abcyun.cis.charge.service.dto.ChargeSheetAbstract">

        SELECT
        a.id AS id,
        a.patient_order_id AS patientOrderId,
        a.status AS status,
        a.type AS type,
        a.outpatient_status AS outpatientStatus,
        a.execute_status AS executeStatus,
        a.is_draft AS isDraft,
        a.receivable_fee AS receivableFee,
        a.is_online as isOnline,
        a.patient_id AS patientId,
        a.clinic_id AS clinicId,
        a.chain_id AS chainId,
        a.doctor_id as doctorId,
        a.send_to_patient_status AS sendToPatientStatus,
        a.is_closed as isClosed,
        a.order_by_date AS created,
        a.query_exception_type AS queryExceptionType,
        a.owed_status AS owedStatus,
        b.invoice_status_flag AS invoiceStatusFlag,
        case
        when a.is_closed = 1 then 4
        when a.is_draft = 1 then 0
        when a.status = 0 and a.outpatient_status IN (0, 2) then 1
        when a.status = 1 then 1
        when a.status = 0 and a.outpatient_status = 1 then 2
        when a.status = 2 or a.status = 3 then 3
        when a.status = 5 then 5
        else 6
        end as nOrderByStatus,
        if((a.status = 0 or a.status = 1) AND a.is_draft = 0, -unix_timestamp(a.order_by_date),
        unix_timestamp(a.order_by_date)) as nOrderByDate,
        b.extended_info as extendedInfo,
        a.seller_id,
        b.remarks
        FROM v2_charge_sheet AS a
            left join v2_charge_sheet_additional b on (a.id = b.id)
        WHERE a.is_deleted = 0 AND a.clinic_id = #{clinicId} and a.check_status &lt;&gt; 1
            AND (a.type IN (<include refid="chargeDisplayType"></include>) OR (a.type = 7 AND a.status &gt; 1))
            AND a.import_flag in (<include refid="importFlags"></include>) AND a.outpatient_status IN (0 <if
                test="withUndiagnosed != null and withUndiagnosed == 1">,1</if>, 2)
            <if test="withUndiagnosed != null and withUndiagnosed == 1">
                AND (a.reserve_date is null OR a.reserve_date &lt; (CURDATE() + INTERVAL 1 DAY))
            </if>
            <choose>
                <when test="tab != null and (tab == 1 or tab == 2)">
                    AND a.is_draft = 0 AND a.owed_status = 0
                </when>
                <when test="tab != null and tab == 3">
                    AND a.is_draft = 1 AND a.status = 0 AND a.is_closed = 0
                </when>
                <when test="tab != null and (tab == 4)">
                    AND a.is_draft = 0 AND a.owed_status = 10 AND a.is_closed = 0
                </when>
                <otherwise>
                    <choose>
                        <when test="filterChargeStatus == 'draft'">
                            AND a.status = 0 AND a.is_draft = 1 AND a.is_closed = 0
                        </when>
                        <when test="filterChargeStatus == 'uncharged'">
                            AND a.status &lt; 2 AND a.is_draft = 0 AND a.is_closed = 0
                        </when>
                        <when test="filterChargeStatus == 'charged'">
                            AND a.status &gt;= 2 AND a.status &lt; 4
                        </when>
                        <when test="filterChargeStatus == 'chargedRefunded'">
                            AND a.status &gt;= 2
                        </when>
                        <when test="filterChargeStatus == 'refunded'">
                            AND a.status = 4
                        </when>
                        <otherwise>
                            AND a.is_draft = 0
                        </otherwise>
                    </choose>
                </otherwise>
            </choose>

        <choose>
            <when test="sendToPatientStatus != null and sendToPatientStatus == 0">
                AND a.status = 0 and a.send_to_patient_status = 0
            </when>
            <when test="sendToPatientStatus != null and sendToPatientStatus == 1">
                AND a.status = 0 and a.send_to_patient_status = 1
            </when>
            <otherwise>
            </otherwise>
        </choose>

        <if test="typeList != null and typeList.size() > 0">
            AND a.type in
            <foreach collection="typeList" item="type" open="(" separator=" , " close=")">
                #{type}
            </foreach>
        </if>
        <if test="createdBegin != null">
            AND a.order_by_date &gt; #{createdBegin}
        </if>
        <if test="createdEnd != null">
            AND a.order_by_date &lt; #{createdEnd}
        </if>
        <if test="invoiceStatusFlag != null">
            AND ((b.invoice_status_flag IS NOT NULL AND b.invoice_status_flag &amp; #{invoiceStatusFlag} > 0)
                <if test="invoiceStatues != null and invoiceStatues.size() > 0">
                OR (b.invoice_status_flag IS NULL
                    AND b.invoice_status in
                    <foreach collection="invoiceStatues" item="invoiceStatus" open="(" separator=" , " close=")">
                        #{invoiceStatus}
                    </foreach>
                    )
                </if>)
        </if>
        ORDER BY nOrderByStatus ASC, nOrderByDate DESC
        <if test="offset != null and offset gt -1 and limit != null and limit gt -1">
            LIMIT #{offset}, #{limit}
        </if>

    </select>

    <select id="findChargeSheetAbstractListCount" resultType="cn.abcyun.cis.charge.api.model.ChargeSheetAbstractListRsp">
        SELECT
        COUNT(a.id) as totalCount,
        sum(a.receivable_fee) as draftReceivableTotalFee
        FROM v2_charge_sheet AS a
        <if test="invoiceStatusFlag != null">
            left join v2_charge_sheet_additional b on (a.id = b.id)
        </if>
        WHERE a.is_deleted = 0 AND a.clinic_id = #{clinicId} and a.check_status &lt;&gt; 1
            AND (a.type IN (<include refid="chargeDisplayType"></include>) OR (a.type = 7 AND a.status &gt; 1))
            AND a.import_flag in (<include refid="importFlags"></include>) AND a.outpatient_status IN (0 <if
                test="withUndiagnosed != null and withUndiagnosed == 1">, 1</if>, 2)
            <if test="withUndiagnosed != null and withUndiagnosed == 1">
                AND (a.reserve_date is null OR a.reserve_date &lt; (CURDATE() + INTERVAL 1 DAY))
            </if>
            <choose>
                <when test="tab != null and (tab == 1 or tab == 2)">
                    AND a.is_draft = 0 AND a.owed_status = 0
                </when>
                <when test="tab != null and tab == 3">
                    AND a.is_draft = 1 AND a.status = 0 AND a.is_closed = 0
                </when>
                <when test="tab != null and (tab == 4)">
                    AND a.is_draft = 0 AND a.owed_status = 10 AND a.is_closed = 0
                </when>
                <otherwise>
                    <choose>
                        <when test="filterChargeStatus == 'draft'">
                            AND a.status = 0 AND a.is_draft = 1 AND a.is_closed = 0
                        </when>
                        <when test="filterChargeStatus == 'uncharged'">
                            AND a.status &lt; 2 AND a.is_draft = 0 AND a.is_closed = 0
                        </when>
                        <when test="filterChargeStatus == 'charged'">
                            AND a.status &gt;= 2 AND a.status &lt; 4
                        </when>
                        <when test="filterChargeStatus == 'chargedRefunded'">
                            AND a.status &gt;= 2
                        </when>
                        <when test="filterChargeStatus == 'refunded'">
                            AND a.status = 4
                        </when>
                        <otherwise>
                            AND a.is_draft = 0
                        </otherwise>
                    </choose>
                </otherwise>
            </choose>
        <choose>
            <when test="sendToPatientStatus != null and sendToPatientStatus == 0">
                AND a.status = 0 and a.send_to_patient_status = 0
            </when>
            <when test="sendToPatientStatus != null and sendToPatientStatus == 1">
                AND a.status = 0 and a.send_to_patient_status = 0
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test="typeList != null and typeList.size() > 0">
            AND a.type in
            <foreach collection="typeList" item="type" open="(" separator=" , " close=")">
                #{type}
            </foreach>
        </if>
        <if test="createdBegin != null">
            AND a.order_by_date &gt; #{createdBegin}
        </if>
        <if test="createdEnd != null">
            AND a.order_by_date &lt; #{createdEnd}
        </if>
        <if test="invoiceStatusFlag != null">
            AND ((b.invoice_status_flag IS NOT NULL AND (b.invoice_status_flag &amp; #{invoiceStatusFlag}) > 0)
            <if test="invoiceStatues != null and invoiceStatues.size() > 0">
                OR (b.invoice_status_flag IS NULL
                AND b.invoice_status in
                <foreach collection="invoiceStatues" item="invoiceStatus" open="(" separator=" , " close=")">
                    #{invoiceStatus}
                </foreach>
                )
            </if>)
        </if>
    </select>

    <select id="findChargeSheetAbstractListForNurse" resultType="cn.abcyun.cis.charge.service.dto.ChargeSheetAbstract">
        SELECT distinct
        a.id AS id,
        a.patient_order_id AS patientOrderId,
        a.status AS status,
        a.type AS type,
        a.outpatient_status AS outpatientStatus,
        a.execute_status AS executeStatus,
        a.is_draft AS isDraft,
        a.receivable_fee AS receivableFee,
        a.is_online as isOnline,
        a.patient_id AS patientId,
        a.clinic_id AS clinicId,
        a.chain_id AS chainId,
        a.doctor_id as doctorId,
        a.send_to_patient_status AS sendToPatientStatus,
        a.is_closed as isClosed,
        a.order_by_date AS created,
        a.query_exception_type AS queryExceptionType,
        a.owed_status AS owedStatus,
        <choose>
            <when test="onlyExecuteAfterPaid">
                IF(a.execute_status = 1 and a.is_closed = 0 and (a.status = 2 or a.status = 3), 0, 1) as nOrderByStatus,
                IF(a.execute_status = 1 and a.is_closed = 0 and (a.status = 2 or a.status = 3), -unix_timestamp(a.order_by_date), unix_timestamp(a.order_by_date)) as nOrderByDate
            </when>
            <otherwise>
                IF(a.execute_status = 1 and a.is_closed = 0, 0, 1) as nOrderByStatus,
                IF(a.execute_status = 1 and a.is_closed = 0, -unix_timestamp(a.order_by_date), unix_timestamp(a.order_by_date)) as nOrderByDate
            </otherwise>
        </choose>
        FROM v2_charge_sheet AS a
                <if test="needFilterExecutor and filterExecutorId != null and filterExecutorId !=''">
                    left join v2_charge_execute_record_executor b
                        on a.chain_id = b.chain_id and a.id = b.charge_sheet_id and b.execute_record_status = 0 and b.executor_id = #{filterExecutorId}
                </if>
        WHERE
            <include refid="chargeSheetAbstractListForNurseWhereSql"></include>
        ORDER BY nOrderByStatus ASC, nOrderByDate DESC
        <if test="offset != null and offset gt -1 and limit != null and limit gt -1">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="findChargeSheetAbstractListCountForNurse" resultType="int">
        SELECT
        COUNT(distinct a.id)
        FROM v2_charge_sheet AS a
            <if test="needFilterExecutor and filterExecutorId != null and filterExecutorId !=''">
                left join v2_charge_execute_record_executor b
                on a.chain_id = b.chain_id and a.id = b.charge_sheet_id and b.execute_record_status = 0 and b.executor_id = #{filterExecutorId}
            </if>
        WHERE <include refid="chargeSheetAbstractListForNurseWhereSql"></include>
    </select>

    <select id="findChargeSheetAbstractListSummary"
            resultType="cn.abcyun.cis.charge.api.model.ChargeSheetAbstractListRsp$ListSummary">
        SELECT
        count(a.id) AS allCount,
        IFNULL(SUM(if(a.status &lt; 2, 1, 0)), 0) as unchargedCount,
        IFNULL(SUM(if(a.is_online = 0, 1, 0)), 0) as normalCount,
        IFNULL(SUM(if(a.is_online = 1, 1, 0)), 0) as onlineCount
        FROM v2_charge_sheet AS a
        WHERE a.is_deleted = 0 AND a.clinic_id = #{clinicId} and a.check_status &lt;&gt; 1 AND a.is_draft = 0 and
        a.is_closed = 0
        AND (a.type IN (<include refid="chargeDisplayType"></include>) OR (a.type = 7 AND a.status &gt; 1))
        AND a.import_flag in (<include refid="importFlags"></include>) AND a.outpatient_status IN (0 <if test="withUndiagnosed != null and withUndiagnosed == 1">
        , 1</if>, 2)
        <if test="withUndiagnosed != null and withUndiagnosed == 1">
            AND (a.reserve_date is null OR a.reserve_date &lt; (CURDATE() + INTERVAL 1 DAY))
        </if>
        <if test="createdBegin != null">
            AND a.order_by_date &gt; #{createdBegin}
        </if>
        <if test="createdEnd != null">
            AND a.order_by_date &lt; #{createdEnd}
        </if>
    </select>

    <select id="findChargeSheetDraftCount" resultType="int">
        SELECT
        count(a.id) as draftCount
        FROM v2_charge_sheet AS a
        WHERE a.is_deleted = 0 AND a.clinic_id = #{clinicId} and a.check_status &lt;&gt; 1 AND a.status = 0 AND
        a.is_draft = 1 ANd a.is_closed = 0 AND a.type IN (<include refid="chargeDisplayType"></include>) AND
        a.import_flag in (<include refid="importFlags"></include>)
    </select>

    <select id="findChargeSheetOwedCount" resultType="java.lang.Integer">
        SELECT count(id) as owedCount
        FROM v2_charge_sheet AS a
        WHERE a.is_deleted = 0
        AND a.clinic_id = #{clinicId}
        AND a.is_closed = 0
        and a.owed_status = 10 and a.is_draft = 0 and a.type IN (<include refid="chargeDisplayType"></include>) AND a.import_flag in (<include refid="importFlags"></include>)
    </select>

    <select id="findChargeSheetAbstractListByIdsAndChainIdAndClinicId"
            resultType="cn.abcyun.cis.charge.service.dto.ChargeSheetAbstract">
        SELECT
        a.id AS id,
        a.patient_order_id AS patientOrderId,
        a.order_by_date AS created,
        a.status AS status,
        a.type AS type,
        a.outpatient_status AS outpatientStatus,
        a.execute_status AS executeStatus,
        a.is_draft AS isDraft,
        a.receivable_fee AS receivableFee,
        a.is_online as isOnline,
        a.patient_id AS patientId,
        a.clinic_id AS clinicId,
        a.chain_id AS chainId,
        a.doctor_id as doctorId,
        a.send_to_patient_status AS sendToPatientStatus,
        a.is_closed as isClosed,
        a.owed_status as owedStatus,
        a.charged_time as chargedTime,
        b.invoice_status_flag AS invoiceStatusFlag,
        a.diagnosed_date as diagnosedDate,
        b.extended_info AS extendedInfo,
        a.seller_id,
        b.remarks
        FROM v2_charge_sheet AS a
            left join v2_charge_sheet_additional b on (a.id = b.id)
        WHERE <include refid="findChargeSheetAbstractListByIdsAndChainIdAndClinicIdWhereSql"></include>

    </select>

    <sql id="findChargeSheetAbstractListByIdsAndChainIdAndClinicIdWhereSql">
        a.is_deleted = 0
        AND a.chain_id = #{chainId}
        <if test="clinicId != null and clinicId != ''">
            AND a.clinic_id = #{clinicId}
        </if>
        AND a.outpatient_status IN (0 <if test="withUndiagnosed != null and withUndiagnosed == 1">, 1</if>, 2)
        AND (a.type IN (<include refid="chargeDisplayType"></include>) OR (a.type = 7 AND a.status &gt; 1)) and
        a.check_status &lt;&gt; 1
        <if test="withUndiagnosed != null and withUndiagnosed == 1">
            AND (a.reserve_date is null OR a.reserve_date &lt; (CURDATE() + INTERVAL 1 DAY))
        </if>
        AND a.id IN
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </sql>

    <select id="findChargeSheetAbstractSummaryByIdsAndChainIdAndClinicId"
            resultType="cn.abcyun.cis.charge.api.model.ChargeSheetAbstractListRsp">
        SELECT
        sum(a.receivable_fee) as draftReceivableTotalFee
        FROM v2_charge_sheet AS a
        left join v2_charge_sheet_additional b on (a.id = b.id)
        WHERE <include refid="findChargeSheetAbstractListByIdsAndChainIdAndClinicIdWhereSql"></include>
    </select>

    <select id="findChargeSheetAbstractListForNurseByIdsAndChainIdAndClinicId" resultType="cn.abcyun.cis.charge.service.dto.ChargeSheetAbstract">
        SELECT
        a.id AS id,
        a.patient_order_id AS patientOrderId,
        a.order_by_date AS created,
        a.status AS status,
        a.type AS type,
        a.outpatient_status AS outpatientStatus,
        a.execute_status AS executeStatus,
        a.is_draft AS isDraft,
        a.receivable_fee AS receivableFee,
        a.is_online as isOnline,
        a.patient_id AS patientId,
        a.clinic_id AS clinicId,
        a.chain_id AS chainId,
        a.doctor_id as doctorId,
        a.send_to_patient_status AS sendToPatientStatus,
        a.is_closed as isClosed,
        a.owed_status as owedStatus,
        a.charged_time as chargedTime,
        b.invoice_status_flag AS invoiceStatusFlag,
        b.extended_info AS extendedInfo,
        <choose>
            <when test="onlyExecuteAfterPaid">
                IF(a.execute_status = 1 and a.is_closed = 0 and (a.status = 2 or a.status = 3), 0, 1) as nOrderByStatus,
                IF(a.execute_status = 1 and a.is_closed = 0 and (a.status = 2 or a.status = 3), -unix_timestamp(a.order_by_date), unix_timestamp(a.order_by_date)) as nOrderByDate
            </when>
            <otherwise>
                IF(a.execute_status = 1 and a.is_closed = 0, 0, 1) as nOrderByStatus,
                IF(a.execute_status = 1 and a.is_closed = 0, -unix_timestamp(a.order_by_date), unix_timestamp(a.order_by_date)) as nOrderByDate
            </otherwise>
        </choose>
        FROM v2_charge_sheet AS a
        left join v2_charge_sheet_additional b on (a.id = b.id)
        WHERE a.is_deleted = 0
        AND a.chain_id = #{chainId}
        <if test="clinicId != null and clinicId != ''">
            AND a.clinic_id = #{clinicId}
        </if>
        AND a.outpatient_status IN (0 <if test="withUndiagnosed != null and withUndiagnosed == 1">, 1</if>, 2)
        AND (a.type IN (<include refid="chargeDisplayType"></include>) OR (a.type = 7 AND a.status &gt; 1)) and
        a.check_status &lt;&gt; 1
        <if test="withUndiagnosed != null and withUndiagnosed == 1">
            AND (a.reserve_date is null OR a.reserve_date &lt; (CURDATE() + INTERVAL 1 DAY))
        </if>
        AND a.id IN
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        ORDER BY nOrderByStatus ASC, nOrderByDate DESC
    </select>


    <select id="findChargeSheetReceivedFee">
        SELECT
          IFNULL(SUM(amount), 0)
        FROM v2_charge_transaction
        WHERE charge_sheet_id = #{chargeSheetId} AND is_deleted = 0
    </select>


    <select id="findChargeSheetStatusBatch" resultType="cn.abcyun.cis.commons.rpc.charge.ChargeStatus">
        SELECT
        id,
        patient_order_id AS patientOrderId,
        status AS status
        FROM
        v2_charge_sheet
        WHERE is_deleted = 0 AND patient_order_id IN
        <foreach collection="patientOrderIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

    <select id="findChargedCount" resultType="long">
        SELECT
        COUNT(DISTINCT(a.patient_order_id)) AS charged_count
        FROM v2_charge_transaction AS a
        WHERE a.is_deleted = 0 AND a.pay_source = 0 AND a.clinic_id = #{clinicId}
        <if test="beginDate != null">
            AND a.created &gt; #{beginDate}
        </if>
        <if test="endDate != null">
            AND a.created &lt; #{endDate}
        </if>
    </select>


    <select id="findUnchargedCount" resultType="int">
        SELECT
        COUNT(DISTINCT(a.id)) AS uncharged_count
        FROM v2_charge_sheet AS a
        WHERE a.is_deleted = 0 AND (a.status = 0 OR a.status = 1)
        AND a.type in (<include refid="chargeDisplayType"></include>)
        AND a.clinic_id = #{clinicId} AND a.outpatient_status IN (0 <if
            test="withUndiagnosed != null and withUndiagnosed == 1">, 1</if>, 2)
        AND a.import_flag in (<include refid="importFlags"></include>)
        AND a.check_status != 1
        AND a.is_draft = 0
        AND a.is_closed = 0
        <if test="beginDate != null">
            AND a.order_by_date &gt; #{beginDate}
        </if>
        <if test="endDate != null">
            AND a.order_by_date &lt; #{endDate}
        </if>
    </select>

    <select id="findChargedAmount" resultType="java.math.BigDecimal">
        SELECT
        IFNULL(SUM(a.amount), 0) AS total_amount
        FROM v2_charge_transaction AS a
        WHERE a.is_deleted = 0 AND a.pay_source = 0 AND a.clinic_id = #{clinicId}
        <if test="beginDate != null">
            AND a.created &gt; #{beginDate}
        </if>
        <if test="endDate != null">
            AND a.created &lt; #{endDate}
        </if>
    </select>

    <select id="findChargedOutpatientAmount" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(a.amount), 0) AS total_amount
        FROM v2_charge_transaction AS a
        INNER JOIN v2_charge_sheet AS c ON c.id = a.charge_sheet_id AND c.clinic_id = #{clinicId}
        WHERE a.is_deleted = 0 AND a.clinic_id = #{clinicId} AND c.is_deleted = 0 AND a.pay_source IN (0, 1) AND c.type
        IN (1, 2, 7) AND c.import_flag in (<include refid="importFlags"></include>)
        <if test="doctorId != null and doctorId != ''">
            AND c.doctor_id = #{doctorId}
        </if>
        <if test="beginDate != null">
            AND a.created &gt; #{beginDate}
        </if>
        <if test="endDate != null">
            AND a.created &lt; #{endDate}
        </if>
    </select>

    <select id="findNeedUpgradeAdjustmentToDiscountChargeSheetIds" resultType="java.lang.String">
        select id from v2_charge_sheet
        where
        is_deleted = 0
        and status IN (1,2,3)
        and id IN
        (
        select charge_sheet_id from v2_charge_additional_fee where amount &lt; 0 and is_deleted = 0 and type = 1
        <if test="clinicId != null">
            and clinic_id = #{clinicId}
        </if>
        <if test="chainId != null">
            and chain_id = #{chainId}
        </if>
        )
    </select>

    <select id="findChargeDashboardStatusBatch" resultType="cn.abcyun.cis.charge.api.model.DashboardStatus">
        select
        a.patient_order_id as patientOrderId,
        if(a.chargeStatus &lt; 2, 1, if(a.chargeStatus = 4, 0, 2)) as chargeStatus,
        if(b.executedCount is null, 0, if(b.executedCount = 0, 1, 2)) as treatmentStatus,
        if(c.executedCount is null, 0, if(c.executedCount = 0, 1, 2)) as physiotherapyStatus
        from
        (
        select
        patient_order_id,
        min(status) as chargeStatus
        from v2_charge_sheet
        where is_deleted = 0 and type &lt;&gt; 1 and outpatient_status &lt;&gt; 1 and is_closed = 0 and patient_order_id
        in
        <foreach collection="patientOrderIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        group by patient_order_id
        ) as a
        left join (
        select
        ei.patient_order_id as patient_order_id,
        ifnull(sum(ei.executed_count), 0) as executedCount
        from v2_charge_execute_item as ei
        where ei.is_deleted = 0 and ei.product_sub_type = 1 and ei.product_type = 4 and
        ei.patient_order_id in
        <foreach collection="patientOrderIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        group by ei.patient_order_id
        )
        as b on a.patient_order_id = b.patient_order_id
        left join (
        select
        ei.patient_order_id as patient_order_id,
        ifnull(sum(ei.executed_count),0) as executedCount
        from v2_charge_execute_item as ei
        where ei.is_deleted = 0 and ei.product_sub_type = 2 and ei.product_type = 4 and
        ei.patient_order_id in
        <foreach collection="patientOrderIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        group by ei.patient_order_id
        )
        as c on a.patient_order_id = c.patient_order_id
    </select>

    <select id="findEmployeeChargeStatInfo" resultType="cn.abcyun.cis.charge.rpc.model.EmployeeChargeStatInfo">
        select
        round(ifnull(sum(ct.amount), 0), 2) as totalAmount,
        count(distinct (ct.charge_sheet_id)) as sheetCount
        from v2_charge_transaction as ct
        where ct.is_deleted = 0 and ct.clinic_id = #{clinicId} and ct.created_by = #{employeeId}
        <if test="beginDate != null">
            and ct.created &gt; #{beginDate}
        </if>
        <if test="endDate != null">
            and ct.created &lt; #{endDate}
        </if>
    </select>

    <sql id="findPatientChargeSheetAbstractListWhereSql">
        cs.is_deleted = 0 and cs.is_closed = 0
        <choose>
            <when test="isPatientCount != null and isPatientCount == 1">
                and (cs.type not in (5,10,11) and cs.status &gt; 1)
            </when>
            <otherwise>
                and ((cs.type not in (5,10,11) and cs.status &gt; 1) or cs.type = 16)
            </otherwise>
        </choose>
        and cs.patient_id=#{patientId} and cs.chain_id=#{chainId}
        <if test="clinicIds != null and clinicIds.size() != 0">
            and cs.clinic_id in
            <foreach collection="clinicIds" open="(" close=")" separator="," item="clinicId">
                #{clinicId}
            </foreach>
        </if>
        <if test="sellerId != null and sellerId != ''">
            and ((cs.type in (2, 13) and cs.doctor_id = #{sellerId}) or (cs.type not in (2, 13) and cs.seller_id = #{sellerId}))
        </if>
        <if test="chargedDateBegin != null">
            AND cs.charged_time &gt; #{chargedDateBegin}
        </if>
        <if test="chargedDateEnd != null">
            AND cs.charged_time &lt; #{chargedDateEnd}
        </if>
    </sql>

    <select id="findPatientChargeSheetAbstractList"
            resultType="cn.abcyun.cis.charge.api.model.PatientChargeSheetAbstract">
        select
            cs.id as id,
            cs.charged_time as chargedTime,
            cs.clinic_id,
            cs.status as status,
            cs.receivable_fee as receivableFee,
            cs.charged_by,
            if (cs.type in (2, 13), doctor_id, seller_id) as sellerId,
            if (cs.type in (2, 13), diagnosed_date, null) as diagnosedDate,
            cs.owed_status owedStatus,
            v2csa.abstract_info as abstractInfo
        from v2_charge_sheet as cs
            left join v2_charge_sheet_additional v2csa on cs.id = v2csa.id
            where <include refid="findPatientChargeSheetAbstractListWhereSql"/>
            order by cs.order_by_date desc
        <if test="offset != null and offset gt -1 and limit != null and limit gt -1">
            limit #{offset}, #{limit}
        </if>
    </select>

    <select id="findPatientChargeSheetAbstractListCount" resultType="int">
        select
            count(cs.id) as listCount
        from v2_charge_sheet as cs
        where <include refid="findPatientChargeSheetAbstractListWhereSql"/>
    </select>

    <select id="findPatientChargeSheetTotalReceivedFee" resultType="java.math.BigDecimal">
        select
            ifnull(sum(amount), 0) as receivedFee
        from v2_charge_transaction as ct
        inner join v2_charge_sheet as cs on cs.id = ct.charge_sheet_id and cs.patient_id=#{patientId} and cs.chain_id=#{chainId}
        where ct.is_deleted = 0 and ct.is_paidback = 0 and ct.patient_id=#{patientId} and ct.chain_id=#{chainId}
              and cs.is_deleted = 0 and cs.type not in (5,10,11) and cs.status &gt; 1
        <if test="sellerId != null and sellerId != ''">
            and ((cs.type = 2 and cs.doctor_id = #{sellerId}) or (cs.type != 2 and cs.seller_id = #{sellerId}))
        </if>
        <if test="chargedDateBegin != null">
            AND cs.charged_time &gt; #{chargedDateBegin}
        </if>
        <if test="chargedDateEnd != null">
            AND cs.charged_time &lt; #{chargedDateEnd}
        </if>
    </select>

    <select id="findDoctorPatientDiagnosedChargedCount" resultType="long">
        select
            count(id)
        from v2_charge_sheet
        where is_deleted = 0 and type = 2 and doctor_id = #{doctorId} and patient_id = #{patientId} and chain_id = #{chainId} and status &gt; 1
    </select>

    <select id="findDoctorPatientDiagnosedChargedCountByPatientIds" resultType="cn.abcyun.cis.charge.api.model.DoctorPatientDiagnosedChargedInfoRsp$PatientDiagnosedChargedInfo">
        select
            patient_id as patientId,
            count(id) as chargedCount
        from v2_charge_sheet
        where is_deleted = 0 and type = 2 and doctor_id = #{doctorId} and patient_id in
        <foreach collection="patientIds" open="(" close=")" separator="," item="patientId">
            #{patientId}
        </foreach>
        and chain_id = #{chainId} and status &gt; 1
        group by patient_id
    </select>

    <sql id="chargeOrderViewResult">
        a.id as chargeSheetId,
        a.patient_id as patientId,
        a.clinic_id as clinicId,
        a.created,
        a.diagnosed_date as diagnosedDate,
        a.receivable_fee as receivableFee,
        a.received_fee as receivedFee,
        a.type as type,
        a.status as status,
        a.check_status as checkStatus,
        a.delivery_type as deliveryType,
        a.send_to_patient_status as  sendToPatientStatus,
        f.abstract_info as abstractInfo,
        f.dispensing_status as dispensingStatus,
        f.self_pay_status as selfPayStatus,
        f.air_dispensing_status as airDispensingStatus,
        f.lock_status as lockStatus,
        a.last_modified as lastModified
    </sql>
    <sql id="chargeOrderViewWhere">
        a.patient_id in
        <foreach collection="patientIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        and a.chain_id = #{chainId}
        and a.is_deleted = 0
        and a.is_closed = 0
        order by a.created desc
        <if test="offset != null and offset gt -1 and limit != null and limit gt -1">
            limit #{offset}, #{limit}
        </if>
    </sql>

    <sql id="chargeOrderViewWhereNoLimit">
        a.patient_id in
        <foreach collection="patientIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        and a.chain_id = #{chainId}
        and a.is_deleted = 0
        and a.is_closed = 0
    </sql>

    <select id="getDeliveryNoCompany" resultType="cn.abcyun.cis.charge.api.model.ChargeOrderViewDeliveyInfo">
        select
        d.charge_sheet_id as chargeSheetId,
        d.delivery_order_no as deliveryOrderNo,
        e.name as deliveryCompanyName
        from v2_charge_delivery d
        left join v2_charge_delivery_company e on d.delivery_company_id = e.id and e.chain_id = #{chainId}
        where d.chain_id = #{chainId} and
        d.is_deleted = 0 and
        d.charge_sheet_id in
        <foreach collection="chargeSheetIds" open="(" close=")" separator="," item="sheetid">
            #{sheetid}
        </foreach>
    </select>

    <sql id = "getChargeOrderListByPatientIdsWhereSql">
        (
            ((a.send_to_patient_status = 1 and (a.type &lt;&gt; 2 or (a.type = 2 and a.outpatient_status = 2)) and a.check_status &lt;&gt; 1) or (a.status = 0 and a.check_status = 2))
                or a.type = 8
        )
    </sql>

    <!--    总的列表SQL    -->
    <select id="getChargeOrderListByPatientIds" resultType="cn.abcyun.cis.charge.api.model.orderlist.ChargeOrderView">
        select
        <include refid="chargeOrderViewResult"></include>
        from v2_charge_sheet a
        left join v2_charge_sheet_additional f on a.id = f.id
        where <include refid="getChargeOrderListByPatientIdsWhereSql"></include>
            and
        <include refid="chargeOrderViewWhere"></include>
    </select>
    <select id="countChargeOrderListByPatientIds" resultType="java.lang.Integer">
        select
        count(a.id)
        from v2_charge_sheet a
        where <include refid="getChargeOrderListByPatientIdsWhereSql"></include>
            and
        <include refid="chargeOrderViewWhereNoLimit"></include>
    </select>

    <!--    待付款中的列表sql列表    -->
    <select id="getPayingChargeOrderListByPatientIds"
            resultType="cn.abcyun.cis.charge.api.model.orderlist.ChargeOrderView">
        select
        <include refid="chargeOrderViewResult"></include>
        from v2_charge_sheet a
        left join v2_charge_sheet_additional f on a.id = f.id
        where (a.send_to_patient_status = 1 and (a.type &lt;&gt; 2 or (a.type = 2 and a.outpatient_status = 2))) and
        ((a.status = 1 and a.received_fee &lt; a.receivable_fee ) or a.status = 0
        )
        and a.check_status = 0 and
        <include refid="chargeOrderViewWhere"></include>
    </select>
    <select id="countPayingChargeOrderListByPatientIds" resultType="java.lang.Integer">
        select
        count(a.id)
        from v2_charge_sheet a
        left join v2_charge_sheet_additional f on a.id = f.id
        where (a.send_to_patient_status = 1 and (a.type &lt;&gt; 2 or (a.type = 2 and a.outpatient_status = 2))) and
        ((a.status = 1 and a.received_fee &lt; a.receivable_fee ) or a.status = 0
        )
        and a.check_status = 0 and
        <include refid="chargeOrderViewWhereNoLimit"></include>
    </select>

    <!--    待发药列表sql列表    -->
    <select id="getDispensingChargeOrderListByPatientIds"
            resultType="cn.abcyun.cis.charge.api.model.orderlist.ChargeOrderView">
        select
        <include refid="chargeOrderViewResult"></include>
        from v2_charge_sheet a
        left join v2_charge_sheet_additional f on a.id = f.id
        where a.send_to_patient_status = 1 and a.status = 2 and ( f.dispensing_status = 0 or ( f.air_dispensing_status
        &lt; 100 and f.air_dispensing_status &gt; -1 ) ) and
        <include refid="chargeOrderViewWhere"></include>
    </select>
    <select id="countDispensingChargeOrderListByPatientIds" resultType="java.lang.Integer">
        select
        count(a.id)
        from v2_charge_sheet a
        left join v2_charge_sheet_additional f on a.id = f.id
        where a.send_to_patient_status = 1 and a.status = 2 and ( f.dispensing_status = 0 or ( f.air_dispensing_status
        &lt; 100 and f.air_dispensing_status &gt; -1 ) ) and
        <include refid="chargeOrderViewWhereNoLimit"></include>
    </select>


    <!--    待收药列表sql列表    -->
    <select id="getDeliveryChargeOrderListByPatientIds"
            resultType="cn.abcyun.cis.charge.api.model.orderlist.ChargeOrderView">
        select
        <include refid="chargeOrderViewResult"></include>
        from v2_charge_sheet a
        left join v2_charge_sheet_additional f on a.id = f.id
        where a.send_to_patient_status = 1 and a.status = 2 and ( ( a.delivery_type = 1 and f.dispensing_status = 1 ) or
        ( f.air_dispensing_status = 100 ) ) and TIMESTAMPDIFF(second,a.last_modified,CURRENT_TIMESTAMP()) &lt;
        #{expiretime} and
        <include refid="chargeOrderViewWhere"></include>
    </select>
    <select id="countDeliveryChargeOrderListByPatientIds" resultType="java.lang.Integer">
        select
        count(a.id)
        from v2_charge_sheet a
        left join v2_charge_sheet_additional f on a.id = f.id
        where a.send_to_patient_status = 1 and a.status = 2 and ( ( a.delivery_type = 1 and f.dispensing_status = 1 ) or
        ( f.air_dispensing_status = 100 ) ) and TIMESTAMPDIFF(second,a.last_modified,CURRENT_TIMESTAMP()) &lt;
        #{expiretime} and
        <include refid="chargeOrderViewWhereNoLimit"></include>
    </select>


    <!--    已完成列表sql列表    -->
    <select id="getFinishedChargeOrderListByPatientIds"
            resultType="cn.abcyun.cis.charge.api.model.orderlist.ChargeOrderView">
        select
        <include refid="chargeOrderViewResult"></include>
        from v2_charge_sheet a
        left join v2_charge_sheet_additional f on a.id = f.id
        where a.send_to_patient_status = 1 and a.status = 2
        and
        ( ((
        ( a.delivery_type = 1 and f.dispensing_status = 1 and f.air_dispensing_status = 100 ) or
        ( a.delivery_type = 1 and f.dispensing_status = 1 and f.air_dispensing_status = -1 ) or
        ( a.delivery_type = 0 and f.air_dispensing_status = 100 ) )
        and TIMESTAMPDIFF(second,a.last_modified,CURRENT_TIMESTAMP()) &gt; #{expiretime} )
        or (a.delivery_type = 0 and (f.dispensing_status = 1 or f.dispensing_status = -1) and f.air_dispensing_status =
        -1) )
        and
        <include refid="chargeOrderViewWhere"></include>
    </select>
    <select id="countFinishedChargeOrderListByPatientIds" resultType="java.lang.Integer">
        select
        count(a.id)
        from v2_charge_sheet a
        left join v2_charge_sheet_additional f on a.id = f.id
        where a.send_to_patient_status = 1 and a.status = 2
        and
        ( ((
        ( a.delivery_type = 1 and f.dispensing_status = 1 and f.air_dispensing_status = 100 ) or
        ( a.delivery_type = 1 and f.dispensing_status = 1 and f.air_dispensing_status = -1 ) or
        ( a.delivery_type = 0 and f.air_dispensing_status = 100 ) )
        and TIMESTAMPDIFF(second,a.last_modified,CURRENT_TIMESTAMP()) &gt; #{expiretime} )
        or (a.delivery_type = 0 and (f.dispensing_status = 1 or f.dispensing_status = -1) and f.air_dispensing_status =
        -1) )
        and
        <include refid="chargeOrderViewWhereNoLimit"></include>
    </select>


    <!--    自助取号机支付列表获取    -->
    <select id="getDeviceSelfPayChargeOrderListByPatientIds"
            resultType="cn.abcyun.cis.charge.api.model.orderlist.DeviceSelfPayChargeOrderView">
        select
        a.id as chargeSheetId,
        a.patient_id patientId,
        a.created,
        a.receivable_fee as receivableFee,
        a.received_fee as receivedFee,
        a.status as status,
        a.check_status as checkStatus,
        a.delivery_type as deliveryType,
        a.send_to_patient_status as sendToPatientStatus,
        f.abstract_info as abstractInfo,
        f.dispensing_status as dispensingStatus,
        f.self_pay_status as selfPayStatus,
        f.air_dispensing_status as airDispensingStatus,
        f.lock_status as lockStatus,
        a.last_modified as lastModified,
        a.patient_order_id as patientOrderId,
        a.is_closed as isClosed
        from v2_charge_sheet a
        left join v2_charge_sheet_additional f on a.id = f.id
        where a.chain_id = #{chainId}
        and a.clinic_id = #{clinicId}
        and a.check_status = 0
        and a.is_closed = 0
        and a.patient_id in
        <foreach collection="patientIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        <if test="searchBegin != null">
            AND a.last_modified &gt; #{searchBegin}
        </if>
        and a.is_deleted = 0
        and (
        (a.type in (3, 8, 17) and a.send_to_patient_status = 1)
        or (a.type = 2 and a.outpatient_status = 2)
        or a.type = 6
        )
        GROUP BY a.id
        order by a.status asc , a.created desc
        <if test="offset != null and offset gt -1 and limit != null and limit gt -1">
            limit #{offset}, #{limit}
        </if>
    </select>
    <select id="countDeviceSelfPayChargeOrderListByPatientIds" resultType="java.lang.Integer">
        select
        count(a.id)
        from v2_charge_sheet a
        left join v2_charge_sheet_additional f on a.id = f.id
        where a.chain_id = #{chainId}
        and a.clinic_id = #{clinicId}
        and a.check_status = 0
        and a.is_closed = 0
        and (
        (a.type in (3, 8) and a.send_to_patient_status = 1)
        or (a.type = 2 and a.outpatient_status = 2)
        or a.type = 6
        )
        and a.patient_id in
        <foreach collection="patientIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        <if test="searchBegin != null">
            AND a.last_modified &gt; #{searchBegin}
        </if>
        and a.is_deleted = 0
    </select>

    <select id="getChargeHistoryForWeClinic"
            resultType="cn.abcyun.cis.charge.api.model.WeClinicChargeHistoryListRsp$WeClinicChargeHistoryItem">
        select
        a.id as id,
        ifnull(a.charged_time, a.created) as chargedTime,
        a.patient_order_id as patientOrderId,
        a.type as type,
        b.abstract_info as abstractInfo,
        b.diagnosis as diagnosis,
        b.chief_complaint as chiefComplaint,
        a.patient_id patientId,
        a.clinic_id clinicId,
        a.doctor_id doctorId,
        a.diagnosed_date as diagnosedDate
        from v2_charge_sheet as a
        inner join v2_charge_sheet_additional as b on a.id = b.id and b.chain_id = #{chainId}
        where a.is_deleted = 0 and a.chain_id = #{chainId} and a.status in (1, 2, 3) and b.is_can_be_clone = 1 and
        b.is_deleted = 0 and a.patient_id in
        <foreach collection="patientIds" item="patientId" open="(" close=")" separator=",">
            #{patientId}
        </foreach>
        order by chargedTime desc
        limit #{offset}, #{limit}
    </select>

    <select id="getChargeHistoryForWeClinicCount" resultType="java.lang.Integer">
        select
        count(a.id)
        from v2_charge_sheet as a
        inner join v2_charge_sheet_additional as b on a.id = b.id and b.chain_id = #{chainId}
        where a.is_deleted = 0 and a.chain_id = #{chainId} and a.status in (1, 2, 3) and b.is_can_be_clone = 1 and
        b.is_deleted = 0 and a.patient_id in
        <foreach collection="patientIds" item="patientId" open="(" close=")" separator=",">
            #{patientId}
        </foreach>
    </select>

    <select id="countExecutiveSheet" resultType="java.lang.Integer">
        select
        count(distinct cs.id)
        from v2_charge_execute_item ei
        inner join v2_charge_sheet cs on ei.charge_sheet_id = cs.id
        where ei.chain_id = #{chainId}
        <if test="clinicId != null and clinicId != ''">
            AND ei.clinic_id = #{clinicId}
        </if>
        and cs.is_closed = 0
        and cs.patient_id in
        <foreach collection="patientIds" open="(" separator="," close=")" item="patientId">
            #{patientId}
        </foreach>
        and cs.execute_status in
        <foreach collection="executeStatuses" open="(" separator="," close=")" item="executeStatus">
            #{executeStatus}
        </foreach>
        and cs.status in
        <foreach collection="statuses" open="(" separator="," close=")" item="status">
            #{status}
        </foreach>
        <if test="beginTime != null">
            and cs.order_by_date &gt;= #{beginTime}
        </if>
        <if test="endTime != null">
            and cs.order_by_date &lt;= #{endTime}
        </if>
        and ei.product_type = 4
        and cs.is_deleted = #{isDeleted}
        and ei.is_deleted = #{isDeleted}
    </select>
    <select id="pageListExecutiveSheet" resultType="cn.abcyun.cis.commons.rpc.charge.WeClinicMyExecuteSheetListView">
        select distinct
        cs.id as chargeSheetId,
        cs.patient_id as patientId,
        cs.created as created,
        cs.execute_status as executeStatus,
        cs.status as status,
        cs.seller_id as sellerId
        from v2_charge_execute_item ei
        inner join v2_charge_sheet cs on ei.charge_sheet_id = cs.id
        where ei.chain_id = #{chainId}
        <if test="clinicId != null and clinicId != ''">
            and cs.clinic_id = #{clinicId}
        </if>
        and cs.is_closed = 0
        and cs.patient_id in
        <foreach collection="patientIds" open="(" separator="," close=")" item="patientId">
            #{patientId}
        </foreach>
        and cs.execute_status in
        <foreach collection="executeStatuses" open="(" separator="," close=")" item="executeStatus">
            #{executeStatus}
        </foreach>
        and cs.status in
        <foreach collection="statuses" open="(" separator="," close=")" item="status">
            #{status}
        </foreach>
        <if test="beginTime != null">
            and cs.order_by_date &gt;= #{beginTime}
        </if>
        <if test="endTime != null">
            and cs.order_by_date &lt;= #{endTime}
        </if>
        and ei.product_type = 4
        and cs.is_deleted = #{isDeleted}
        and ei.is_deleted = #{isDeleted}
        order by cs.order_by_date desc
        limit #{offset},#{limit}
    </select>
    <select id="queryPaidChargeSheetsByDate"
            resultType="cn.abcyun.cis.charge.api.model.ChargeSheetSimpleListRsp$ChargeSheetSimple">
        select
        <include refid="chargeSheetSimpleParamSql"></include>
        from v2_charge_sheet a
        left join v2_charge_sheet_additional b on a.id = b.id and a.chain_id = b.chain_id
        where a.status = 2
        and a.is_deleted = 0
        and a.type not in (1, 5, 7)
        <if test="clinicId != null">
            and a.clinic_id = #{clinicId}
        </if>
        <if test="beginDate != null">
            AND a.order_by_date &gt; #{beginDate}
        </if>
        <if test="endDate != null">
            AND a.order_by_date &lt; #{endDate}
        </if>
        order by a.order_by_date desc
    </select>
    <select id="listChargeSheetSimpleByIds"
            resultType="cn.abcyun.cis.charge.api.model.ChargeSheetSimpleListRsp$ChargeSheetSimple">
        select
        <include refid="chargeSheetSimpleParamSql"></include>
        from v2_charge_sheet a
        left join v2_charge_sheet_additional b on a.id = b.id and a.chain_id = b.chain_id
        where a.is_deleted = 0
          and a.chain_id = #{chainId}
          and a.id in
        <foreach collection="chargeSheetIds" open="(" separator="," close=")" item="chargeSheetId">
            #{chargeSheetId}
        </foreach>
    </select>
    <select id="findChargeSheetSimpleById"
            resultType="cn.abcyun.bis.rpc.sdk.cis.model.charge.ChargeSheetSimpleListRsp$ChargeSheetSimple">
        select
        <include refid="chargeSheetSimpleParamSql"></include>
        from v2_charge_sheet a
        left join v2_charge_sheet_additional b on a.id = b.id and a.chain_id = b.chain_id
        where a.id = #{chargeSheetId} and a.is_deleted = 0
    </select>
    <select id="findAllContainExceptionChargeSheetsByPatientOrderIds"
            resultType="cn.abcyun.cis.charge.api.model.QueryChargeSheetExceptionStatusListRsp$ChargeSheetExceptionStatusInfo">
        select a.patient_order_id as patientOrderId,
        a.id as chargeSheetId,
        a.query_exception_type as queryExceptionType
        from v2_charge_sheet a
        where a.is_deleted = 0
        and a.type = 1
        and a.status in (0, 1, 2)
        and a.query_exception_type > 0
        and a.clinic_id = #{clinicId} and a.chain_id = #{chainId}
        and a.patient_order_id in
        <foreach collection="patientOrderIds" open="(" separator="," close=")" item="patientOrderId">
            #{patientOrderId}
        </foreach>
    </select>
    <select id="findPatientOrderReceivedFee" resultType="java.math.BigDecimal">
      select sum(a.amount)
        from v2_charge_transaction a
        inner join v2_charge_sheet b on a.charge_sheet_id = b.id and b.is_deleted = 0
        where a.patient_order_id = #{patientOrderId}
                  and a.clinic_id = #{clinicId}
          and b.status in (2,3)
          and a.is_deleted = 0
          and a.is_paidback = 0
    </select>
    <select id="findPatientOrdersReceivedFee"
            resultType="cn.abcyun.cis.charge.api.model.QueryPatientOrderNotificationsRsp$PatientOrderReceivedFeeView">
        select a.patient_order_id patientOrderId,
        sum(a.amount) receivedFee
        from v2_charge_transaction a
        inner join v2_charge_sheet b on a.charge_sheet_id = b.id and b.is_deleted = 0
        where a.patient_order_id in
        <foreach collection="patientOrderIds" open="(" separator="," close=")" item="patientOrderId">
            #{patientOrderId}
        </foreach>
        and b.status in (2, 3)
        and a.is_deleted = 0
        and a.is_paidback = 0
        group by a.patient_order_id
    </select>
    <select id="findChargeSheetIdsByHospitalSheetId" resultType="java.lang.String">
        select id from v2_charge_sheet
        where is_deleted = 0 and chain_id = #{chainId}
        and clinic_id = #{clinicId}
        and hospital_sheet_id = #{hospitalSheetId}
    </select>
    <select id="pageListChargeSheetIdsByHospitalSheetId" resultType="java.lang.String">
        select id
        from v2_charge_sheet
        where hospital_sheet_id = #{hospitalSheetId}
          and chain_id = #{chainId}
          and clinic_id = #{clinicId}
          and is_deleted = 0
          and status != 0
          and type = 2
        order by created desc
        limit #{offset}, #{limit}
    </select>
    <select id="countByHospitalSheetId" resultType="java.lang.Integer">
        select count(id)
        from v2_charge_sheet
        where hospital_sheet_id = #{hospitalSheetId}
          and clinic_id = #{clinicId}
          and is_deleted = 0
          and status != 0
          and type = 2
    </select>

    <select id="findChargeSheetsByPatientOrderId"
            resultType="cn.abcyun.cis.charge.service.dto.ChargeSheetWithPatientOrderStatus">
        select
        id,
        patient_order_id as patientOrderId,
        type,
        status
        from v2_charge_sheet where patient_order_id in
        <foreach collection="patientOrderIds" open="(" separator="," close=")" item="patientOrderId">
            #{patientOrderId}
        </foreach>
        and type = #{type} and is_deleted = 0
    </select>

    <select id="listChargeSheetAbstractListByDate"
            resultType="cn.abcyun.cis.charge.service.dto.ChargeSheetAbstract">
        SELECT
        a.id AS id,
        a.patient_order_id AS patientOrderId,
        a.status AS status,
        a.type AS type,
        a.outpatient_status AS outpatientStatus,
        a.execute_status AS executeStatus,
        a.is_draft AS isDraft,
        a.receivable_fee AS receivableFee,
        a.is_online as isOnline,
        a.patient_id AS patientId,
        a.clinic_id AS clinicId,
        a.chain_id AS chainId,
        a.doctor_id as doctorId,
        a.send_to_patient_status AS sendToPatientStatus,
        a.is_closed as isClosed,
        a.created AS created,
        a.owed_status AS owedStatus
        FROM v2_charge_sheet AS a
        WHERE a.is_deleted = 0 AND a.clinic_id = #{clinicId} and a.check_status &lt;&gt; 1
        AND (a.type IN (<include refid="chargeDisplayType"></include>) OR (a.type = 7 AND a.status &gt; 1))
        AND a.import_flag in (<include refid="importFlags"></include>)
        <if test="beginCreated != null">
            AND a.created &gt; #{beginCreated}
        </if>
        <if test="endCreated != null">
            AND a.created &lt; #{endCreated}
        </if>
        ORDER BY a.created desc
    </select>

    <select id="listChargeSheetAbstractListByIds" resultType="cn.abcyun.cis.charge.service.dto.ChargeSheetAbstract">
        SELECT
        a.id AS id,
        a.patient_order_id AS patientOrderId,
        a.status AS status,
        a.type AS type,
        a.outpatient_status AS outpatientStatus,
        a.execute_status AS executeStatus,
        a.is_draft AS isDraft,
        a.receivable_fee AS receivableFee,
        a.is_online as isOnline,
        a.patient_id AS patientId,
        a.clinic_id AS clinicId,
        a.chain_id AS chainId,
        a.doctor_id as doctorId,
        a.send_to_patient_status AS sendToPatientStatus,
        a.is_closed as isClosed,
        a.created AS created,
        a.owed_status AS owedStatus
        FROM v2_charge_sheet AS a
        WHERE a.is_deleted = 0
          AND a.chain_id = #{chainId}
          AND a.id in
          <foreach collection="ids" open="(" separator="," close=")" item="id">
              #{id}
          </foreach>
    </select>
    <select id="listOwingChargeSheetByPatientId"
            resultType="cn.abcyun.cis.charge.service.dto.ChargeSheetAbstract">
        SELECT
            a.id AS id,
            a.patient_order_id AS patientOrderId,
            a.status AS status,
            a.type AS type,
            a.outpatient_status AS outpatientStatus,
            a.execute_status AS executeStatus,
            a.is_draft AS isDraft,
            a.receivable_fee AS receivableFee,
            a.is_online as isOnline,
            a.patient_id AS patientId,
            a.clinic_id AS clinicId,
            a.chain_id AS chainId,
            a.doctor_id as doctorId,
            a.send_to_patient_status AS sendToPatientStatus,
            a.is_closed as isClosed,
            a.created AS created,
            a.owed_status AS owedStatus
        FROM v2_charge_sheet AS a
        WHERE a.is_deleted = 0 AND a.clinic_id = #{clinicId} and a.owed_status = 10 and a.patient_id = #{patientId}
    </select>

    <select id="findLatestUnchargedChargeSheetByPatientIds"
            resultType="cn.abcyun.cis.charge.api.model.ChargeSheetSimpleListRsp$ChargeSheetSimple">
        select
            id,
            patient_order_id as patientOrderId,
            order_by_date as orderByDate,
            chain_id as chainId,
            clinic_id as clinicId,
            doctor_id as doctorId,
            patient_id as patientId,
            receivable_fee as receivableFee,
            status,
            execute_status as executeStatus,
            outpatient_status as outpatientStatus,
            is_online as isOnline,
            type,
            charged_by as chargedBy
        from v2_charge_sheet
        where is_deleted = 0 and status = 0 and chain_id = #{chainId}
        <if test="clinicId != null and clinicId !=''">
            and clinic_id = #{clinicId}
        </if>
        and patient_id in
        <foreach collection="patientIds" open="(" separator="," close=")" item="patientId">
            #{patientId}
        </foreach>
        and type IN (<include refid="chargeDisplayType"></include>)
        and outpatient_status IN (0, 2)
        and is_closed = 0
        and check_status &lt;&gt; 1
        and patient_id != '00000000000000000000000000000000'
        and order_by_date &gt;= #{startTime}
        and order_by_date &lt;= #{endTime}
        order by order_by_date desc limit 1
    </select>
    <select id="findChargeSheetAbstractListByPatientId"
            resultType="cn.abcyun.cis.charge.service.dto.ChargeSheetAbstract">
        SELECT
            a.id AS id,
            a.patient_order_id AS patientOrderId,
            a.status AS status,
            a.type AS type,
            a.outpatient_status AS outpatientStatus,
            a.execute_status AS executeStatus,
            a.is_draft AS isDraft,
            a.receivable_fee AS receivableFee,
            a.is_online as isOnline,
            a.patient_id AS patientId,
            a.clinic_id AS clinicId,
            a.chain_id AS chainId,
            a.doctor_id as doctorId,
            a.send_to_patient_status AS sendToPatientStatus,
            a.is_closed as isClosed,
            a.order_by_date AS created,
            a.query_exception_type AS queryExceptionType,
            a.owed_status AS owedStatus,
            a.diagnosed_date as diagnosedDate
        FROM v2_charge_sheet AS a
        where a.is_deleted = 0 and a.is_closed = 0 and a.patient_id = #{patientId}
        and !(a.type = 3 and a.is_draft = 1)
        <if test="sellerId != null and sellerId != ''">
            and ((a.type in (2, 13) and a.doctor_id = #{sellerId}) or (a.type != 2 and a.type != 13 and a.seller_id = #{sellerId}))
        </if>
        <if test="createdDateBegin != null">
            AND a.created &gt; #{createdDateBegin}
        </if>
        <if test="createdDateEnd != null">
            AND a.created &lt; #{createdDateEnd}
        </if>
    </select>
    <select id="findChargeSheetAbstractListByPatientOrderIdAndChainId"
            resultType="cn.abcyun.cis.charge.service.dto.ChargeSheetAbstract">
        SELECT
        a.id AS id,
        a.patient_order_id AS patientOrderId,
        a.order_by_date AS created,
        a.status AS status,
        a.type AS type,
        a.outpatient_status AS outpatientStatus,
        a.execute_status AS executeStatus,
        a.is_draft AS isDraft,
        a.receivable_fee AS receivableFee,
        a.is_online as isOnline,
        a.patient_id AS patientId,
        a.clinic_id AS clinicId,
        a.chain_id AS chainId,
        a.doctor_id as doctorId,
        a.seller_id as sellerId,
        a.send_to_patient_status AS sendToPatientStatus,
        a.is_closed as isClosed,
        a.owed_status as owedStatus
        FROM v2_charge_sheet AS a
        WHERE a.is_deleted = 0
        AND a.chain_id = #{chainId}
        AND a.outpatient_status IN (0 <if test="withUndiagnosed != null and withUndiagnosed == 1">, 1</if>, 2)
        AND (a.type IN (<include refid="chargeDisplayType"></include>) OR (a.type = 7 AND a.status &gt; 1)) and
        a.check_status &lt;&gt; 1
        <if test="withUndiagnosed != null and withUndiagnosed == 1">
            AND (a.reserve_date is null OR a.reserve_date &lt; (CURDATE() + INTERVAL 1 DAY))
        </if>
        AND a.patient_order_id = #{patientOrderId}
    </select>
    <select id="listChargeSheetSimpleByPatientOrderIds"
            resultType="cn.abcyun.cis.charge.api.model.QueryChargeSheetByPatientOrderIdsRsp$ChargeSheet">
        select a.id,
        a.patient_order_id as patientOrderId,
        a.chain_id as chainId,
        a.clinic_id as clinicId,
        a.total_fee as totalFee,
        a.receivable_fee as receivableFee,
        a.received_fee as receivedFee,
        a.refund_fee as refundFee,
        a.type,
        a.status
        from v2_charge_sheet a
        where a.is_deleted = 0 and a.chain_id = #{chainId}
        and a.patient_order_id in
        <foreach collection="patientOrderIds" open="(" separator="," close=")" item="patientOrderId">
            #{patientOrderId}
        </foreach>
    </select>

    <update id="updateChargeFormRemark">
        update v2_charge_form
        set usage_info = json_set(usage_info, '$.requirement', #{remark}),
            last_modified    = now(),
            last_modified_by = #{operatorId}
        where id = #{id}
          and charge_sheet_id = #{chargeSheetId}
          and chain_id = #{chainId}
          and clinic_id = #{clinicId}
    </update>

    <select id="listRegistrationChargeSheetByPatientOrderIds"
            resultType="cn.abcyun.cis.charge.model.ChargeSheet">
        select
            a.id
        from v2_charge_sheet a
        where a.is_deleted = 0 and a.chain_id = #{chainId} and a.patient_order_id in
        <foreach collection="patientOrderIds" open="(" separator="," close=")" item="patientOrderId">
            #{patientOrderId}
        </foreach>
    </select>

    <select id="listChargeSheetAdditionalByIds" resultType="cn.abcyun.cis.charge.model.ChargeSheetAdditional">
        select
            a.id
        from v2_charge_sheet a
        where a.is_deleted = 0 and a.chain_id = #{chainId} and a.patient_order_id in
        <foreach collection="patientOrderIds" open="(" separator="," close=")" item="patientOrderId">
            #{patientOrderId}
        </foreach>
    </select>

    <select id="pageLatestChargeSheetList"
            resultType="cn.abcyun.cis.charge.api.model.ChargeSheetSimpleListRsp$ChargeSheetSimple">
        SELECT
        a.id AS id,
        a.patient_order_id AS patientOrderId,
        a.order_by_date as orderByDate,
        a.chain_id AS chainId,
        a.clinic_id AS clinicId,
        a.doctor_id as doctorId,
        a.patient_id AS patientId,
        a.receivable_fee AS receivableFee,
        a.status AS status,
        a.execute_status AS executeStatus,
        a.outpatient_status AS outpatientStatus,
        a.is_online as isOnline,
        a.type AS type,
        a.charged_by as chargedBy
        FROM v2_charge_sheet AS a
        WHERE a.is_deleted = 0 AND a.clinic_id = #{clinicId}
        <if test="beginDate != null">
            AND a.last_modified &gt; #{beginDate}
        </if>
        <if test="endDate != null">
            AND a.last_modified &lt; #{endDate}
        </if>
        order by a.last_modified desc
        <if test="offset != null and offset gt -1 and limit != null and limit gt -1">
            LIMIT #{offset}, #{limit}
        </if>

    </select>
    <select id="countLatestChargeSheet" resultType="int">
        SELECT
        count(a.id)
        FROM v2_charge_sheet AS a
        WHERE a.is_deleted = 0 AND a.clinic_id = #{clinicId}
        <if test="beginDate != null">
            AND a.last_modified &gt; #{beginDate}
        </if>
        <if test="endDate != null">
            AND a.last_modified &lt; #{endDate}
        </if>
        order by a.last_modified desc
    </select>

    <select id="queryPatientChargeSheetForOpenApi" resultType="cn.abcyun.cis.charge.api.model.openapi.OpenApiPatientChargeSheetView">
        select id,
               patient_order_id,
               doctor_id,
               status,
               owed_status,
               seller_id,
               type,
               created,
               receivable_fee,
               charged_by,
               diagnosed_date
        from v2_charge_sheet
        <where>
            <include refid="queryPatientChargeSheetWhere"/>
            order by created desc
            limit #{offset}, #{limit}
        </where>
    </select>

    <select id="countPatientChargeSheetForOpenApi" resultType="int">
        select count(id)
        from v2_charge_sheet
        <where>
            <include refid="queryPatientChargeSheetWhere"/>
        </where>
    </select>

    <select id="getLatestChargeSheet"
            resultType="cn.abcyun.cis.charge.api.model.LastChargeSheetSimpleRsp$ChargeSheetSimple">
        SELECT
        a.id AS id,
        a.patient_order_id AS patientOrderId,
        a.order_by_date as orderByDate,
        a.chain_id AS chainId,
        a.clinic_id AS clinicId,
        a.doctor_id as doctorId,
        a.patient_id AS patientId,
        a.receivable_fee AS receivableFee,
        a.status AS status,
        a.execute_status AS executeStatus,
        a.outpatient_status AS outpatientStatus,
        a.is_online as isOnline,
        a.type AS type,
        a.charged_by as chargedBy,
        a.charged_time as chargedTime
        FROM v2_charge_sheet AS a
        WHERE a.is_deleted = 0 AND a.chain_id = #{chainId}  and a.status in (1, 2)
        AND a.patient_id = #{patientId}

        <if test="clinicId != null">
            AND a.clinic_id = #{clinicId}
        </if>
        order by a.charged_time desc
        limit 0 , 1
    </select>

    <select id="queryChargeTransactionHealthCard" resultType="java.lang.String">
        select
            charge_sheet_id t
        from v2_charge_transaction t
        where t.clinic_id = #{clinicId}
          and t.pay_mode = #{payMode}
          and t.is_paidback = #{isPaidback}
          and t.is_deleted = #{isDeleted}
          and t.charge_sheet_id in
            <foreach collection="chargeSheetIdList" item="chargeSheetId" separator="," open="(" close=")">
                #{chargeSheetId}
            </foreach>
    </select>
</mapper>